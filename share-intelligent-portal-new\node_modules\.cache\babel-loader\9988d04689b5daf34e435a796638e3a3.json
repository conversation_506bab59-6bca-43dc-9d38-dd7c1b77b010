{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\profile\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\profile\\index.vue", "mtime": 1750311963073}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_login", "_home", "_user", "name", "components", "UserMenu", "data", "appliList", "url", "link", "router", "defaultAvatar", "demandSupply", "userinfo", "supplyList", "demandList", "dockingList", "created", "getInfo", "getSupplyList", "getDemandList", "getDockingList", "methods", "_this", "then", "res", "code", "member", "window", "sessionStorage", "setItem", "JSON", "stringify", "handleClick", "val", "handleLink", "open", "$router", "push", "path", "$notify", "info", "title", "_this2", "params", "pageNum", "pageSize", "queryType", "mySupply", "rows", "_this3", "<PERSON><PERSON><PERSON><PERSON>", "_this4", "go<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "goToNotice", "goTodockingRecords"], "sources": ["src/views/system/user/profile/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <div class=\"user-info-card\">\r\n              <!-- 上部分 -->\r\n              <div class=\"user-info-card-top\">\r\n                <!-- 左侧图片 -->\r\n                <div class=\"imgStyle\">\r\n                  <img style=\"width: 100%; height: 100%\" :src=\"userinfo.avatar ? userinfo.avatar : defaultAvatar\"\r\n                    alt=\"\" />\r\n                </div>\r\n                <!-- 右侧内容 -->\r\n                <div class=\"user-info-card-top-right\">\r\n                  <div class=\"nameStyle\">{{ userinfo.memberRealName || '暂无' }}</div>\r\n                  <div class=\"phoneStyle\" style=\"margin-top: 17px; margin-bottom: 11px\">\r\n                    联系电话：{{ userinfo.memberPhone || '暂无' }}\r\n                  </div>\r\n                  <!-- <div class=\"phoneStyle\">所属企业：立即加入></div> -->\r\n                </div>\r\n              </div>\r\n              <!-- 下部分 -->\r\n              <div class=\"user-info-card-bottom\">\r\n                <!-- 顶部导航 -->\r\n                <div class=\"navStyle\">\r\n                  <div class=\"navStyle-left\">\r\n                    <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/business_icon.png\" alt=\"\" />\r\n                    <div class=\"text\">企业应用</div>\r\n                  </div>\r\n                  <!-- <div class=\"navStyle-right\">查看更多 >></div> -->\r\n                </div>\r\n                <!-- 底部内容 -->\r\n                <div class=\"businessAppli\">\r\n                  <div class=\"appliItem\" v-for=\"(item, index) in appliList\" :key=\"index\" @click=\"handleLink(item)\">\r\n                    <div class=\"appliImg\">\r\n                      <img style=\"width: 100%; height: 100%\" :src=\"item.url\" alt=\"\" />\r\n                    </div>\r\n                    <div class=\"appliName\">{{ item.name }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"demandSupply\">\r\n              <!-- 顶部导航 -->\r\n              <div class=\"navStyle\">\r\n                <div class=\"navStyle-left\" @click=\"handleClick('1')\" :style=\"demandSupply == '1'\r\n                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'\r\n                  : 'border-bottom: none;cursor: pointer;'\r\n                  \">\r\n                  <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/demand_icon.png\" alt=\"\" />\r\n                  <div class=\"text\">我的需求</div>\r\n                </div>\r\n                <div class=\"navStyle-left\" @click=\"handleClick('2')\" style=\"margin-left: 24px\" :style=\"demandSupply == '2'\r\n                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'\r\n                  : 'border-bottom: none;cursor: pointer;'\r\n                  \">\r\n                  <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/supply_icon.png\" alt=\"\" />\r\n                  <div class=\"text\">我的供给</div>\r\n                </div>\r\n                <div class=\"navStyle-right\" @click=\"goTosupplyDemand\">查看更多 >></div>\r\n              </div>\r\n              <el-table :data=\"demandList\" v-if=\"demandSupply == '1'\">\r\n                <el-table-column prop=\"title\" label=\"需求标题\" />\r\n                <el-table-column prop=\"description\" label=\"需求描述\" />\r\n              </el-table>\r\n              <el-table :data=\"supplyList\" v-if=\"demandSupply == '2'\">\r\n                <el-table-column prop=\"title\" label=\"供给标题\" />\r\n                <el-table-column prop=\"description\" label=\"供给描述\" />\r\n              </el-table>\r\n              <!-- 暂无数据 -->\r\n              <!-- <div>\r\n                <div class=\"noDataStyle\">\r\n                  <img style=\"width: 100%; height: 100%\" src=\"../../../../assets/user/noData.png\" alt=\"\" />\r\n                </div>\r\n                <div class=\"noDataText\">暂无内容</div>\r\n              </div> -->\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"meNotification\">\r\n              <!-- 顶部导航 -->\r\n              <div class=\"navStyle\">\r\n                <div class=\"navStyle-left\">\r\n                  <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/message_icon.png\" alt=\"\" />\r\n                  <div class=\"text\">消息通知</div>\r\n                </div>\r\n                <div class=\"navStyle-right\" @click=\"goToNotice\">查看更多 >></div>\r\n              </div>\r\n              <!-- 暂无数据 -->\r\n              <div>\r\n                <div class=\"noDataStyle\">\r\n                  <img style=\"width: 100%; height: 100%\" src=\"../../../../assets/user/noData.png\" alt=\"\" />\r\n                </div>\r\n                <div class=\"noDataText\">暂无内容</div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"dockingRecords\">\r\n              <!-- 顶部导航 -->\r\n              <div class=\"navStyle\">\r\n                <div class=\"navStyle-left\">\r\n                  <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/record_icon.png\" alt=\"\" />\r\n                  <div class=\"text\">对接记录</div>\r\n                </div>\r\n                <div class=\"navStyle-right\" @click=\"goTodockingRecords\">查看更多 >></div>\r\n              </div>\r\n              <!-- 暂无数据 -->\r\n              <el-table :data=\"dockingList\" v-if=\"dockingList.length > 0\">\r\n                <el-table-column align=\"center\" prop=\"title\" label=\"资源名称\">\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" prop=\"intentionContent\" label=\"申请内容\">\r\n                </el-table-column>\r\n              </el-table>\r\n              <div v-if=\"dockingList.length == 0\">\r\n                <div class=\"noDataStyle\">\r\n                  <img style=\"width: 100%; height: 100%\" src=\"../../../../assets/user/noData.png\" alt=\"\" />\r\n                </div>\r\n                <div class=\"noDataText\">暂无内容</div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { mySupply, myDemand } from \"@/api/home\";\r\nimport { dockingList } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      appliList: [\r\n        {\r\n          url: require(\"../../../../assets/user/appli1.png\"),\r\n          name: \"复材商城\",\r\n          link: 'http://**************:1001/'\r\n        },\r\n        {\r\n          url: require(\"../../../../assets/user/appli2.png\"),\r\n          name: \"仓储物流\",\r\n        },\r\n        {\r\n          url: require(\"../../../../assets/user/appli3.png\"),\r\n          name: \"金融服务\",\r\n        },\r\n        {\r\n          url: require(\"../../../../assets/user/appli4.png\"),\r\n          name: \"设备管理\",\r\n          router: '/user/equipmentManagement'\r\n        },\r\n      ],\r\n      defaultAvatar: require('@/assets/images/avatar.png'),\r\n      demandSupply: \"1\",\r\n      userinfo: {},\r\n      supplyList: [],\r\n      demandList: [],\r\n      dockingList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getInfo();\r\n    this.getSupplyList();\r\n    this.getDemandList();\r\n    this.getDockingList();\r\n  },\r\n  methods: {\r\n    // 获取用户信息\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (res.code == 200) {\r\n          this.userinfo = res.member;\r\n          window.sessionStorage.setItem(\"userinfo\", JSON.stringify(res.member));\r\n        }\r\n      });\r\n    },\r\n    handleClick(val) {\r\n      this.demandSupply = val;\r\n    },\r\n    handleLink(val) {\r\n      if (val.link) {\r\n        window.open(val.link)\r\n      } else if (val.router) {\r\n        this.$router.push({ path: val.router });\r\n      }\r\n      else {\r\n        this.$notify.info({\r\n          title: '敬请期待',\r\n        });\r\n      }\r\n    },\r\n    getSupplyList() {\r\n      let params = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        queryType: \"my\",\r\n      };\r\n      mySupply(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.supplyList = res.rows;\r\n        }\r\n      });\r\n    },\r\n    getDemandList() {\r\n      let params = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        queryType: \"my\",\r\n      };\r\n      myDemand(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.demandList = res.rows;\r\n        }\r\n      });\r\n    },\r\n    getDockingList() {\r\n      dockingList({\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        queryType: 'my',\r\n      }).then((res) => {\r\n        this.dockingList = res.rows;\r\n      });\r\n    },\r\n    goTosupplyDemand() {\r\n      this.$router.push({ path: '/user/supplyDemand' })\r\n    },\r\n    goToNotice() {\r\n      // this.$router.push({path:'/user/notice'})\r\n    },\r\n    goTodockingRecords() {\r\n      this.$router.push({ path: '/user/dockingRecords' })\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 1080px;\r\n}\r\n\r\n.user-info-card {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  height: 450px;\r\n  border-radius: 4px;\r\n\r\n  .user-info-card-top {\r\n    width: 100%;\r\n    height: 140px;\r\n    background: url(\"../../../../assets/user/homePageBanner.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n    padding: 32px 30px 36px 30px;\r\n    display: flex;\r\n\r\n    .imgStyle {\r\n      width: 60px;\r\n      height: 60px;\r\n    }\r\n\r\n    .user-info-card-top-right {\r\n      margin-left: 20px;\r\n\r\n      .nameStyle {\r\n        height: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        font-size: 18px;\r\n        color: #ffffff;\r\n      }\r\n\r\n      .phoneStyle {\r\n        height: 15px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .user-info-card-bottom {\r\n    margin-top: 20px;\r\n\r\n    .businessAppli {\r\n      margin-top: 31px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .appliItem {\r\n        width: 70px;\r\n        margin-left: 41px;\r\n        text-align: center;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .appliItem:nth-child(1) {\r\n        margin-left: 0;\r\n      }\r\n\r\n      .appliImg {\r\n        width: 70px;\r\n        height: 75px;\r\n      }\r\n\r\n      .appliName {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #222222;\r\n        margin-top: 11px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.demandSupply {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  height: 450px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.meNotification {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  height: 450px;\r\n  border-radius: 4px;\r\n  margin-top: 24px;\r\n}\r\n\r\n.dockingRecords {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  height: 450px;\r\n  border-radius: 4px;\r\n  margin-top: 24px;\r\n}\r\n\r\n.navStyle {\r\n  height: 50px;\r\n  border-bottom: 1px solid #ccc;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .navStyle-left {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 50px;\r\n    border-bottom: 2px solid #21c9b8;\r\n  }\r\n\r\n  .text {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #21c9b8;\r\n    margin-left: 10px;\r\n  }\r\n\r\n  .navStyle-right {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #333333;\r\n    margin-left: auto;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.noDataStyle {\r\n  width: 259px;\r\n  height: 259px;\r\n  margin-top: 63px;\r\n  margin-left: calc((100% - 259px) / 2);\r\n}\r\n\r\n.noDataText {\r\n  width: 100%;\r\n  text-align: center;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 400;\r\n  font-size: 16px;\r\n  color: #999999;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAuIA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,GACA;QACAC,GAAA,EAAAT,OAAA;QACAI,IAAA;QACAM,IAAA;MACA,GACA;QACAD,GAAA,EAAAT,OAAA;QACAI,IAAA;MACA,GACA;QACAK,GAAA,EAAAT,OAAA;QACAI,IAAA;MACA,GACA;QACAK,GAAA,EAAAT,OAAA;QACAI,IAAA;QACAO,MAAA;MACA,EACA;MACAC,aAAA,EAAAZ,OAAA;MACAa,YAAA;MACAC,QAAA;MACAC,UAAA;MACAC,UAAA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,aAAA;IACA,KAAAC,aAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACAJ,OAAA,WAAAA,QAAA;MAAA,IAAAK,KAAA;MACA,IAAAL,cAAA,IAAAM,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAH,KAAA,CAAAV,QAAA,GAAAY,GAAA,CAAAE,MAAA;UACAC,MAAA,CAAAC,cAAA,CAAAC,OAAA,aAAAC,IAAA,CAAAC,SAAA,CAAAP,GAAA,CAAAE,MAAA;QACA;MACA;IACA;IACAM,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAAtB,YAAA,GAAAsB,GAAA;IACA;IACAC,UAAA,WAAAA,WAAAD,GAAA;MACA,IAAAA,GAAA,CAAAzB,IAAA;QACAmB,MAAA,CAAAQ,IAAA,CAAAF,GAAA,CAAAzB,IAAA;MACA,WAAAyB,GAAA,CAAAxB,MAAA;QACA,KAAA2B,OAAA,CAAAC,IAAA;UAAAC,IAAA,EAAAL,GAAA,CAAAxB;QAAA;MACA,OACA;QACA,KAAA8B,OAAA,CAAAC,IAAA;UACAC,KAAA;QACA;MACA;IACA;IACAvB,aAAA,WAAAA,cAAA;MAAA,IAAAwB,MAAA;MACA,IAAAC,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACA,IAAAC,cAAA,EAAAJ,MAAA,EAAApB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAiB,MAAA,CAAA7B,UAAA,GAAAW,GAAA,CAAAwB,IAAA;QACA;MACA;IACA;IACA7B,aAAA,WAAAA,cAAA;MAAA,IAAA8B,MAAA;MACA,IAAAN,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACA,IAAAI,cAAA,EAAAP,MAAA,EAAApB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAwB,MAAA,CAAAnC,UAAA,GAAAU,GAAA,CAAAwB,IAAA;QACA;MACA;IACA;IACA5B,cAAA,WAAAA,eAAA;MAAA,IAAA+B,MAAA;MACA,IAAApC,iBAAA;QACA6B,OAAA;QACAC,QAAA;QACAC,SAAA;MACA,GAAAvB,IAAA,WAAAC,GAAA;QACA2B,MAAA,CAAApC,WAAA,GAAAS,GAAA,CAAAwB,IAAA;MACA;IACA;IACAI,gBAAA,WAAAA,iBAAA;MACA,KAAAhB,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACAe,UAAA,WAAAA,WAAA;MACA;IAAA,CACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,KAAAlB,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}