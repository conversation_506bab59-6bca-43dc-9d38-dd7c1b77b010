<template>
  <div class="policy-page">
    <div class="policy-page-header">
      <div class="banner">
        <img src="../../assets/policy/banner.png" alt="政策大厅" />
      </div>
      <div class="body">
        <div class="enterprise-list-title-box">
          <div class="enterprise-list-divider"></div>
          <div class="enterprise-list-title">链政策</div>
          <div class="enterprise-list-divider"></div>
        </div>
        <!-- <header-tag title="链政策" /> -->
        <div class="search-box">
          <el-form ref="form" class="search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.title"
                placeholder="请输入搜索内容"
                class="search-input"
              >
                <el-button slot="append" class="search-btn" @click="search"
                  >搜索
                </el-button>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="card-container policy-page-body">
      <div class="card">
        <router-link to="/policymode" class="card-left">
          <el-image
            fit="contain"
            class="notice-icon"
            :src="require('../../assets/policy/zixun.png')"
          />
          <div class="imageBtn">查看更多</div>
        </router-link>
        <div v-loading="noticeLoading" class="card-right notice-content">
          <template v-if="notices.length > 0">
            <router-link
              class="notice-item"
              v-for="item in notices"
              :key="item.id"
              :to="`/policyDetail?id=${item.id}`"
            >
              <div class="notice-item-content">
                <div class="title">{{ item.title }}</div>
                <div class="footer">
                  <div class="company">{{ item.company }}</div>
                  <div class="date">{{ item.updateTime }}</div>
                  <!-- <div class="date">发文日期：{{ item.updateTime }}</div> -->
                </div>
              </div>
              <div class="notice-item-btn">查看详情</div>
            </router-link>
          </template>
          <template v-else>
            <el-empty />
          </template>
        </div>
      </div>
      <div>
        <div class="card-left">
          <el-image
            fit="contain"
            class="draw-icon"
            :src="require('../../assets/policy/huaxiang.png')"
          />
        </div>
        <div class="card-right policy-content">
          <el-row :gutter="10">
            <el-col :span="6" v-for="item in policyItems" :key="item.key">
              <div class="policy-card">
                <el-checkbox-group v-model="checkedArr">
                  <el-checkbox
                    class="checkbox-policy"
                    v-for="code in item.children"
                    :label="code.code"
                    :key="code.code"
                    >{{ code.text }}</el-checkbox
                  >
                </el-checkbox-group>
              </div>
            </el-col>
          </el-row>
          <div class="policy-content-footer">
            <div @click="onCancel" class="btn-cancel">取消</div>
            <div @click="onConfirm" class="btn-confirm">确定</div>
          </div>
        </div>
      </div>
      <div class="card">
        <router-link to="/policyDeclare" class="card-left">
          <el-image
            fit="fill"
            class="apply-icon"
            :src="require('../../assets/policy/shenbao.png')"
          />
          <div class="imageBtn">在线查看</div>
        </router-link>
        <div class="card-right apply-content">
          <div class="apply-card">
            <router-link to="/policyDeclare" class="apply-card-header">
              <div class="left">
                <div class="tag" />
                <div class="title">科创平台</div>
              </div>
              <div class="right">更多>></div>
            </router-link>
            <div class="apply-card-body">
              <template v-if="letItems.length > 0">
                <router-link
                  class="item"
                  :to="`/policyDeclareDetail?id=${item.id}`"
                  v-for="item in letItems"
                  :key="item.id"
                >
                  <div class="item-tag" />
                  <div class="item-text">{{ item.title }}</div>
                </router-link>
              </template>
              <template v-else>
                <el-empty />
              </template>
            </div>
          </div>
          <div class="apply-card">
            <router-link to="/policyDeclare" class="apply-card-header">
              <div class="left">
                <div class="tag" />
                <div class="title">人才政策</div>
              </div>
              <div class="right">更多>></div>
            </router-link>
            <div class="apply-card-body">
              <template v-if="letItems.length > 0">
                <router-link
                  class="item"
                  :to="`/policyDeclareDetail?id=${item.id}`"
                  v-for="item in rightItems"
                  :key="item.id"
                >
                  <div class="item-tag" />
                  <div class="item-text">{{ item.title }}</div>
                </router-link>
              </template>
              <template v-else>
                <el-empty />
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { forEachObjIndexed, map } from "ramda";
// import HeaderTag from "@/views/components/home/<USER>";
import { getListByText } from "@/api/notice";
import { listPolicyByType, listByTypePolicy } from "@/api/zhm";
// import { policymode } from "../components/home/<USER>";

export default {
  name: "policyPage",
  components: {
    // HeaderTag,
    // policymode,
  },
  data() {
    return {
      noticeLoading: false,
      form: {
        title: undefined,
      },
      notices: [],
      letItems: [],
      rightItems: [],
      checkedArr: [],
      policyItems: [],
    };
  },
  created() {
    this.getNoticeData();
    this.getTypePolicy();
    this.getLeftItems();
    this.getRightItems();
  },
  watch: {
    $route(to, from) {
      // to表示要跳转的路由，from表示从哪儿跳的路由   to.path
      this.$router.go(0);
    },
  },
  mounted() {
    let specialLoc = this.$route.query.specialLoc;
    if (specialLoc) {
      let inter = setInterval(() => {
        let target = this.$refs[specialLoc];
        if (target) {
          clearInterval(inter);
          target.scrollIntoView();
        }
      }, 100);
    }
  },
  methods: {
    getNoticeData() {
      this.noticeLoading = true;
      getListByText({
        typeTop: 2,
        pageNum: 1,
        pageSize: 4,
      })
        .then((res) => {
          const { code, rows } = res;
          if (code === 200) {
            this.notices = rows;
          }
        })
        .finally(() => (this.noticeLoading = false));
    },
    getLeftItems() {
      listPolicyByType(100).then((res) => {
        const { code, rows } = res;
        if (code === 200) {
          this.letItems = rows;
        }
      });
    },
    getRightItems() {
      listPolicyByType(101).then((res) => {
        const { code, rows } = res;
        if (code === 200) {
          this.rightItems = rows;
        }
      });
    },
    getTypePolicy() {
      listByTypePolicy().then((res) => {
        const { code, data } = res;
        if (code === 200) {
          const items = [];
          forEachObjIndexed((value, key) => {
            items.push({
              key,
              children: map(
                (item) => ({
                  id: item.id,
                  code: item.labelCode,
                  text: item.labelText,
                }),
                value
              ),
            });
          }, data);
          this.policyItems = items;
        }
      });
    },
    search() {
      const { title } = this.form;
      if (title) {
        this.$router.push(`/notice?keyword=${title}`);
      }
    },
    onCancel() {
      this.checkedArr = [];
    },
    onConfirm() {
      if (this.checkedArr.length === 0) {
        this.$message.info("请选择画像");
        return;
      }
      this.$router.push({
        name: "policyDeclare",
        params: { code: this.checkedArr },
      });
      console.log(this.checkedArr);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";

.policy-page {
  background-color: #f4f5f9;
  &-header {
    background-color: #ffffff;
    .banner {
      width: 100%;
      height: 50vh;
      background-color: #f5f5f5;
      img {
        width: 100%;
        height: 100%;
        object-fit: fill;
      }
    }

    .body {
      padding: 20px 0;
    }
    .search-box {
      // padding-top: 40px;
      .search-form {
        text-align: center;

        .search-input {
          width: 792px;
          height: 54px;

          .search-btn {
            width: 100px;
          }

          ::v-deep.el-input__inner {
            height: 54px;
            background: #fff;
            border-radius: 27px 0 0 27px;
            border: 1px solid #d9d9d9;
            font-size: 16px;
            line-height: 24px;
            padding-left: 30px;
          }

          ::v-deep.el-input-group__append {
            border: 1px solid #21c9b8;
            border-radius: 0 100px 100px 0;
            background: #21c9b8;
            font-size: 16px;
            color: #fff;
            line-height: 24px;
          }
        }
      }
    }
  }
  &-body {
    padding: 40px 0;
    .card {
      display: flex;
      flex-direction: row;
      align-items: center;
      flex-shrink: 0;
      width: 100%;
      margin-bottom: 40px;
      &-left {
        // width: 292px;
        position: relative;
        flex-shrink: 0;
        .notice-icon {
          width: 100%;
          // height: 632px;
        }
        .draw-icon {
          width: 100%;
          height: 100%;
          // height: 526px;
        }
        .apply-icon {
          width: 100%;
          height: 304px;
        }
        .imageBtn {
          position: absolute;
          left: calc((100% - 200px) / 2);
          bottom: 20px;
          width: 200px;
          height: 44px;
          line-height: 44px;
          background: #ffffff;
          border-radius: 2px;
          text-align: center;
          cursor: pointer;
          color: #21c9b8;
        }
      }
      &-right {
        flex: 1;
        &.notice-content {
          // height: 632px;
          padding-left: 24px;
          padding-top: 20px;
          overflow-y: auto;
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
          .notice-item {
            // display: flex;
            // flex-shrink: 0;
            // flex-direction: row;
            // align-items: center;
            width: 49%;
            height: 200px;
            background-color: #ffffff;
            padding: 20px 24px;
            margin-bottom: 24px;
            &-content {
              // display: flex;
              // flex-direction: column;
              // flex: 1;
              // justify-content: space-between;
              // height: 100%;
              padding-right: 24px;
              .title {
                @include multiEllipsis(1);
                font-size: 24px;
                font-weight: 500;
                color: #323233;
                line-height: 32px;
              }
              .footer {
                display: flex;
                flex-direction: row;
                align-items: center;
                margin-top: 50px;
                margin-bottom: 20px;
                .company {
                  font-size: 14px;
                  font-weight: 400;
                  color: #999999;
                  line-height: 14px;
                  margin-right: 40px;
                }
                .date {
                  font-size: 14px;
                  font-weight: 400;
                  color: #999999;
                  line-height: 14px;
                }
              }
            }
            &-btn {
              @include flexCenter;
              width: 128px;
              height: 40px;
              background: #21c9b8;
              border-radius: 4px;
              font-size: 16px;
              font-weight: 500;
              color: #ffffff;
              line-height: 16px;
            }
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
        &.policy-content {
          display: flex;
          flex-direction: column;
          flex-shrink: 0;
          justify-content: space-between;
          height: 526px;
          padding: 24px;
          background-color: #ffffff;
          margin: 24px 0 60px 0;
          .policy-card {
            height: 202px;
            background: #f4f5f9;
            border-radius: 8px;
            overflow-y: auto;
            overflow-x: hidden;
            margin-bottom: 10px;
            padding: 10px 0;
            .checkbox-policy {
              padding: 6px 26px;
              ::v-deep.el-checkbox__label {
                @include ellipsis;
                display: inline-block;
                max-width: 130px;
                font-size: 12px;
                font-weight: 400;
                color: #262626;
                line-height: 12px;
              }
            }
          }
          .policy-content-footer {
            @include flexCenter;
            .btn-cancel {
              @include flexCenter;
              width: 160px;
              height: 40px;
              border-radius: 4px;
              border: 1px solid #d9d9d9;
              font-size: 16px;
              font-weight: 500;
              color: #333333;
              line-height: 16px;
              margin-right: 25px;
              cursor: pointer;
            }
            .btn-confirm {
              @include flexCenter;
              width: 160px;
              height: 40px;
              background: #21c9b8;
              border-radius: 4px;
              font-size: 16px;
              font-weight: 500;
              color: #ffffff;
              line-height: 16px;
              cursor: pointer;
            }
          }
        }
        &.apply-content {
          height: 304px;
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding-left: 24px;
          .apply-card {
            width: 426px;
            height: 304px;
            background: #ffffff;
            &-header {
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: space-between;
              height: 66px;
              background-color: #ffffff;
              padding: 0 24px;
              border-bottom: 1px solid #e8e8e8;
              .left {
                display: flex;
                flex-direction: row;
                align-items: center;
                .tag {
                  width: 4px;
                  height: 20px;
                  background: #21c9b8;
                  margin-right: 8px;
                }
                .title {
                  font-size: 18px;
                  font-weight: 500;
                  color: #333333;
                  line-height: 18px;
                }
              }
              .right {
                font-size: 18px;
                font-weight: 400;
                color: #21c9b8;
                line-height: 18px;
              }
            }
            &-body {
              padding: 12px 0;
              .item {
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 12px 24px;
                &-tag {
                  width: 6px;
                  height: 6px;
                  background: #21c9b8;
                }
                &-text {
                  @include ellipsis;
                  flex: 1;
                  font-size: 16px;
                  font-weight: 400;
                  color: #666666;
                  line-height: 16px;
                  padding-left: 14px;
                }
              }
              .el-empty {
                padding: 0 !important;
              }
            }
          }
        }
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
.enterprise-list-title-box {
  width: 336px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 0 40px;
  .enterprise-list-title {
    font-size: 40px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333;
    line-height: 40px;
    padding: 0 40px;
  }
  .enterprise-list-divider {
    width: 48px;
    height: 4px;
    background: #21c9b8;
  }
}
</style>
