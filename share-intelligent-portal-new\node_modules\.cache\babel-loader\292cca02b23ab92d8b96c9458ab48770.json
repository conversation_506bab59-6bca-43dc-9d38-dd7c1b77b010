{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\areaTags.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\areaTags.vue", "mtime": 1750311962955}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ramda", "require", "_data", "name", "data", "dynamicTags", "inputVisible", "inputValue", "watch", "value", "handler", "val", "Array", "isArray", "split", "deep", "immediate", "created", "init", "methods", "_this", "getDicts", "then", "res", "_res$data", "length", "map", "item", "dict<PERSON><PERSON>l", "$emit", "handleClose", "tag", "_this2", "filter", "$nextTick", "formatValue", "showInput", "_this3", "_", "$refs", "saveTagInput", "input", "focus", "handleInputConfirm", "_this4", "includes", "concat", "_toConsumableArray2", "default", "$message", "warning"], "sources": ["src/views/form/components/areaTags.vue"], "sourcesContent": ["<template>\r\n  <div class=\"application-area\">\r\n    <div class=\"action\">\r\n      <el-input\r\n        class=\"input-new-tag\"\r\n        v-if=\"inputVisible\"\r\n        v-model.trim=\"inputValue\"\r\n        ref=\"saveTagInput\"\r\n        size=\"small\"\r\n        @keyup.enter.native=\"handleInputConfirm\"\r\n        @blur=\"handleInputConfirm\"\r\n      >\r\n      </el-input>\r\n      <div v-else class=\"button-new-tag\" @click=\"showInput\">\r\n        <i class=\"el-icon-plus\"></i>\r\n      </div>\r\n    </div>\r\n    <el-tag\r\n      v-for=\"tag in dynamicTags\"\r\n      :key=\"`tag${tag}`\"\r\n      closable\r\n      :disable-transitions=\"false\"\r\n      @close=\"handleClose(tag)\">\r\n      {{tag}}\r\n    </el-tag>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { includes } from 'ramda'\r\nimport { getDicts } from '@/api/system/dict/data'\r\n\r\nexport default {\r\n  name: 'ApplicationAreaDicts',\r\n  data() {\r\n    return {\r\n      dynamicTags: [],\r\n      inputVisible: false,\r\n      inputValue: ''\r\n    };\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          this.dynamicTags =  Array.isArray(val) ? val : this.value.split(',');\r\n        } else {\r\n          this.dynamicTags = [];\r\n          return [];\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      getDicts('application_area').then(res => {\r\n        const { data = [] } = res;\r\n        if (data && data.length > 0) {\r\n          this.dynamicTags = data.map(item => item.dictLabel)\r\n          this.$emit(\"input\", this.dynamicTags);\r\n        }\r\n      })\r\n    },\r\n    handleClose(tag) {\r\n      this.dynamicTags = this.dynamicTags.filter(item => item !== tag);\r\n      this.$nextTick(() => {\r\n        this.$emit(\"input\", this.formatValue(this.dynamicTags));\r\n      })\r\n    },\r\n\r\n    showInput() {\r\n      this.inputVisible = true;\r\n      this.$nextTick(_ => {\r\n        this.$refs.saveTagInput.$refs.input.focus();\r\n      });\r\n    },\r\n\r\n    handleInputConfirm() {\r\n      let inputValue = this.inputValue;\r\n      if (inputValue && !this.dynamicTags.includes(inputValue)) {\r\n        this.dynamicTags = [inputValue, ...this.dynamicTags];\r\n        this.$nextTick(() => {\r\n          this.$emit(\"input\", this.dynamicTags);\r\n        })\r\n      } else {\r\n        this.$message.warning(\"应用领域已存在\");\r\n      }\r\n      this.inputVisible = false;\r\n      this.inputValue = '';\r\n\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n.application-area {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  flex-shrink: 0;\r\n  flex-wrap: wrap;\r\n  .el-tag {\r\n    margin: 6px;\r\n    &:last-child {\r\n      margin-right: 0;\r\n    }\r\n  }\r\n  .button-new-tag {\r\n    @include flexCenter;\r\n    width: 24px;\r\n    height: 24px;\r\n    border: 1px dotted #999999;\r\n    border-radius: 100%;\r\n    margin-right: 6px;\r\n    cursor: pointer;\r\n    i {\r\n      color: #999999;\r\n      font-size: 16px;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n  .input-new-tag {\r\n    width: 90px;\r\n    margin-right: 6px;\r\n    vertical-align: bottom;\r\n  }\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AA6BA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,YAAA;MACAC,UAAA;IACA;EACA;EACAC,KAAA;IACAC,KAAA;MACAC,OAAA,WAAAA,QAAAC,GAAA;QACA,IAAAA,GAAA;UACA,KAAAN,WAAA,GAAAO,KAAA,CAAAC,OAAA,CAAAF,GAAA,IAAAA,GAAA,QAAAF,KAAA,CAAAK,KAAA;QACA;UACA,KAAAT,WAAA;UACA;QACA;MACA;MACAU,IAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,IAAA,WAAAA,KAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,cAAA,sBAAAC,IAAA,WAAAC,GAAA;QACA,IAAAC,SAAA,GAAAD,GAAA,CAAAnB,IAAA;UAAAA,IAAA,GAAAoB,SAAA,mBAAAA,SAAA;QACA,IAAApB,IAAA,IAAAA,IAAA,CAAAqB,MAAA;UACAL,KAAA,CAAAf,WAAA,GAAAD,IAAA,CAAAsB,GAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAC,SAAA;UAAA;UACAR,KAAA,CAAAS,KAAA,UAAAT,KAAA,CAAAf,WAAA;QACA;MACA;IACA;IACAyB,WAAA,WAAAA,YAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA3B,WAAA,QAAAA,WAAA,CAAA4B,MAAA,WAAAN,IAAA;QAAA,OAAAA,IAAA,KAAAI,GAAA;MAAA;MACA,KAAAG,SAAA;QACAF,MAAA,CAAAH,KAAA,UAAAG,MAAA,CAAAG,WAAA,CAAAH,MAAA,CAAA3B,WAAA;MACA;IACA;IAEA+B,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAA/B,YAAA;MACA,KAAA4B,SAAA,WAAAI,CAAA;QACAD,MAAA,CAAAE,KAAA,CAAAC,YAAA,CAAAD,KAAA,CAAAE,KAAA,CAAAC,KAAA;MACA;IACA;IAEAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,IAAArC,UAAA,QAAAA,UAAA;MACA,IAAAA,UAAA,UAAAF,WAAA,CAAAwC,QAAA,CAAAtC,UAAA;QACA,KAAAF,WAAA,IAAAE,UAAA,EAAAuC,MAAA,KAAAC,mBAAA,CAAAC,OAAA,OAAA3C,WAAA;QACA,KAAA6B,SAAA;UACAU,MAAA,CAAAf,KAAA,UAAAe,MAAA,CAAAvC,WAAA;QACA;MACA;QACA,KAAA4C,QAAA,CAAAC,OAAA;MACA;MACA,KAAA5C,YAAA;MACA,KAAAC,UAAA;IAEA;EACA;AACA", "ignoreList": []}]}