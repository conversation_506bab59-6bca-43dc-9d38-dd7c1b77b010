<template>
  <!-- 解决方案 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="方案类型" prop="solutionTypeId">
        <el-input v-model="queryParams.solutionTypeId" placeholder="请输入方案类型" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="方案名称" prop="solutionName">
        <el-input v-model="queryParams.solutionName" placeholder="请输入方案名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['portalconsole:solution:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['portalconsole:solution:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['portalconsole:solution:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['portalconsole:solution:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="solutionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" align="center" prop="solutionId" />
      <el-table-column label="方案名称" align="center" prop="solutionName" />
      <!-- <el-table-column label="方案类型" align="center" prop="solutionTypeId" /> -->
      <!-- <el-table-column label="banner图片" align="center" prop="solutionBanner" /> -->
      <el-table-column label="方案简介" align="center" prop="solutionIntroduction" />
      <el-table-column label="方案概述" align="center" prop="solutionOverview" />
      <!-- <el-table-column label="方案概述" align="center" prop="solutionImg" /> -->
      <el-table-column label="状态" align="center" prop="solutionStatus" >
        <template slot-scope="scope">
          <div v-if="scope.row.solutionStatus==='0'">上架</div>
          <div v-else-if="scope.row.solutionStatus==='1'">下架</div>
          <div v-else></div>
        </template>
      </el-table-column>
      
      <!-- <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:solution:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:solution:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改解决方案对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :span="24">
          <el-col :span="8">
            <el-form-item label="方案类型" prop="solutionType">
              <!-- <el-input v-model="form.solutionTypeId" placeholder="请输入方案类型" /> -->
              <el-cascader :options="solutionTypeList" 
              v-model="solutionType" 
              @change="handleChange" 
               clearable
               :show-all-levels="false"
               :props="propsCascader"
              >
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态" prop="solutionStatus">
              <el-select v-model="form.solutionStatus" placeholder="请选择状态">
                <el-option v-for="dict in dict.type.sys_jjfa_status" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否推荐" prop="recommend">
              <el-select v-model="form.recommend" placeholder="请选择是否推荐" style="width: 100%">
                <el-option v-for="dict in dict.type.sys_jjfa_recommend" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="方案名称" prop="solutionName">
          <el-input v-model="form.solutionName" placeholder="请输入方案名称" maxlength="25" show-word-limit />
        </el-form-item>
        <el-form-item label="banner图片" prop="solutionBanner">
          <!-- <el-input v-model="form.solutionBanner" placeholder="请输入banner图片" /> -->
          <el-upload class="avatar-uploader" action="" :show-file-list="false" :http-request="uploadFun1"
            :before-upload="beforeAvatarUpload1">
            <el-image v-if="form.solutionBanner" :src="form.solutionBanner" class="avatar">
              <div slot="error" class="image-slot">
                <span>暂无图片</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">请上传 大小不超过 5MB 格式为 png/jpg/jpeg 的文件</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="方案简介" prop="solutionIntroduction">
          <el-input v-model="form.solutionIntroduction" type="textarea" placeholder="请输入方案简介" :rows="5" maxlength="35"
            show-word-limit />
        </el-form-item>
        <el-form-item label="方案概述" prop="solutionOverview">
          <el-input v-model="form.solutionOverview" type="textarea" placeholder="请输入方案概述" :rows="5" maxlength="350"
            show-word-limit />
        </el-form-item>
        <el-form-item label="上传图片" prop="solutionImg">
          <el-upload class="avatar-uploader" action="" :show-file-list="false" :http-request="uploadFun2"
            :before-upload="beforeAvatarUpload2">
            <el-image v-if="form.solutionImg" :src="form.solutionImg" class="avatar">
              <div slot="error" class="image-slot">
                <span>暂无图片</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">请上传 大小不超过 5MB 格式为 png/jpg/jpeg 的文件</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="行业痛点" prop="solutionPainList">
          <el-table :data="this.form.solutionPainList" min-height="100" border style="width: 100%">
            <el-table-column prop="solutionPainName" label="痛点名称">
              <template slot-scope="scope">
                <el-input size="mini" v-model="scope.row.solutionPainName" placeholder="请输入" clearable></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="solutionPainContent" label="痛点内容">
              <template slot-scope="scope">
                <span class="inputSize2">
                  <el-input size="mini" v-model="scope.row.solutionPainContent" placeholder="请输入" maxlength="40" show-word-limit clearable></el-input>
                </span>
              </template>
            </el-table-column>

            <el-table-column fixed="right" label="操作" width="120">
              <template slot-scope="scope">
                <el-button @click.native.prevent="addPainSpotRow(scope.row)" type="text" size="small">
                  新增
                </el-button>
                <el-button @click.native.prevent="deletePainSpotRow(scope.row, scope.$index)" type="text" size="small">
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="方案优势" prop="solutionAdvantageList">
          <el-table :data="form.solutionAdvantageList" min-height="300" border style="width: 100%">
            <el-table-column prop="solutionAdvantageName" label="优势名称">
              <template slot-scope="scope">
                <span class="inputSize2">
                  <el-input size="mini" v-model="scope.row.solutionAdvantageName" maxlength="15" show-word-limit placeholder="请输入"></el-input>
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="solutionAdvantageType" label="优势类型" class="inputSize">
              <template slot-scope="scope">
                <span class="inputSize2">
                  <el-input size="mini" v-model="scope.row.solutionAdvantageType" maxlength="15" show-word-limit placeholder="请输入"></el-input>
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="solutionAdvantageContent" label="优势内容">
              <template slot-scope="scope">
                <span class="inputSize">
                  <el-input size="mini" v-model="scope.row.solutionAdvantageContent" maxlength="160" show-word-limit placeholder="请输入"></el-input>
                </span>
              </template>
            </el-table-column>

            <el-table-column label="上传图片" prop="solutionAdvantageImage">
              <template slot-scope="scope">
                <div class="uploadImg">
                  <el-upload class="avatar-uploader" action="" :show-file-list="false" :http-request="(response)=>uploadFun3(response,scope.$index)"
                    :before-upload="beforeAvatarUpload3">
                    <el-image v-if="scope.row.solutionAdvantageImage" :src="scope.row.solutionAdvantageImage" class="avatar">
                      <div slot="error" class="image-slot">
                        <span>暂无图片</span>
                      </div>
                    </el-image>
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    <div slot="tip" class="el-upload__tip">请上传 大小不超过 5MB 格式为 png/jpg/jpeg 的文件</div>
                  </el-upload>
                </div>
              </template>
            </el-table-column>

            <el-table-column fixed="right" label="操作" width="120">
              <template slot-scope="scope">
                <el-button @click.native.prevent="addAdvantageContentRow(scope.row)" type="text" size="small">
                  新增
                </el-button>
                <el-button @click.native.prevent="deleteAdvantageContentRow(scope.row, scope.$index)" type="text"
                  size="small">
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="实施案例" prop="solutionCaseList">
          <el-table :data="this.form.solutionCaseList" min-height="100" border style="width: 100%">
            <el-table-column prop="solutionCaseName" label="案例名称">
              <template slot-scope="scope">
                <el-input size="mini" v-model="scope.row.solutionCaseName" placeholder="请输入"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="solutionCaseIntroduction" label="案例简述">
              <template slot-scope="scope">
                <el-input size="mini" v-model="scope.row.solutionCaseIntroduction" placeholder="请输入"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="solutionCaseContent" label="案例内容">
              <template slot-scope="scope">
                <span class="inputSize">
                  <el-input size="mini" v-model="scope.row.solutionCaseContent" maxlength="360" show-word-limit placeholder="请输入"></el-input>
                </span>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="120">
              <template slot-scope="scope">
                <el-button @click.native.prevent="addSolutionCasesListRow(scope.row)" type="text" size="small">
                  新增
                </el-button>
                <el-button @click.native.prevent="deleteSolutionCasesListRow(scope.row, scope.$index)" type="text"
                  size="small">
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <!-- <el-form-item label="方案概述" prop="solutionImg">
          <el-input v-model="form.solutionImg" type="textarea" placeholder="请输入内容" />
        </el-form-item> 
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>-->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSolution, getSolution, delSolution, addSolution, updateSolution } from "@/api/portalconsole/solution";
import { listSolutionType } from "@/api/portalconsole/solutionType";
import { comUpload } from "@/api/portalconsole/uploadApi";
export default {
  name: "SolutionList",
  dicts: ['sys_jjfa_status', 'sys_jjfa_recommend'],

  data() {
    let validType = (rule, value, callback) => {
      // 直接用value 获取不到选中的值
      // 所以直接 用HTML中 v-model 绑定的值来判断 是否有值
      if (this.solutionType.length == 0) {
        callback(new Error('请选择方案类型'))
      } else {
        callback()
      }
    }
    let validType1 = (rule, value, callback) => {
      // 直接用value 获取不到选中的值
      // 所以直接 用HTML中 v-model 绑定的值来判断 是否有值
      if (!this.form.solutionBanner) {
        callback(new Error('请上传banner图片'))
      } else {
        callback()
      }
    }
    let validType2 = (rule, value, callback) => {
      // 直接用value 获取不到选中的值
      // 所以直接 用HTML中 v-model 绑定的值来判断 是否有值
      if (!this.form.solutionImg) {
        callback(new Error('请上传图片'))
      } else {
        callback()
      }
    }
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 解决方案表格数据
      solutionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        solutionTypeId: null,
        solutionName: null,
        solutionBanner: null,
        solutionIntroduction: null,
        solutionOverview: null,
        solutionImg: null,
        solutionStatus: null,
      },
      // 表单参数
      form: {
        solutionStatus: '',//状态
        solutionName: '',//方案名称
        solutionIntroduction: '',//方案简介
        solutionOverview: '',//方案概述
        recommend: '',//是否推荐
        solutionTypeId: '',//方案类型
        // fileList:[],
        // bannerImageList:[],
        //行业痛点
        solutionPainList: [
          {
            solutionPainName: "",
            solutionPainContent: "",
          }
        ],
        // 方案优势
        solutionAdvantageList: [
          {
            solutionAdvantageName: '',
            solutionAdvantageType: '',
            solutionAdvantageContent: '',
            solutionAdvantageImage:''
          }
        ],
        //案例实施
        solutionCaseList: [
          {
            solutionCaseName: "",
            solutionCaseIntroduction: "",
            solutionCaseContent: "",
          }
        ],
        solutionImg: null,
      },
      // 表单校验
      rules: {
        solutionType:[
          { required: true,  validator: validType, tirgger: 'blur' },
          { type: 'array', message: '请选择方案类型' }
        ],
        solutionName: [
          { required: true, message: "方案名称不能为空", trigger: "blur" }
        ],
        solutionBanner:[
          { required: true,  validator: validType1, tirgger: 'blur' },
          {  message: '请上传banner图片' }
        ],
        solutionStatus: [
          { required: true, message: "请选择状态", trigger: 'change' }
        ],
        solutionIntroduction: [
          { required: true, message: "方案简介不能为空", trigger: "blur" }
        ],
        solutionOverview: [
          { required: true, message: "方案概述不能为空", trigger: "blur" }
        ],
        solutionImg:[
          { required: true,  validator: validType2, tirgger: 'blur' },
          {  message: '请上传图片' }
        ],
      },
      options: [],
      //方案类型
      solutionType: [],
      solutionTypeList: [],
      propsCascader:{ value: 'value',label: 'label',children: 'children'}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询解决方案列表 */
    getList() {
      this.loading = true;
      listSolution(this.queryParams).then(response => {
        this.solutionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        solutionStatus: '',//状态
        solutionName: '',//方案名称
        solutionIntroduction: '',//方案简介
        solutionOverview: '',//方案概述
        recommend: '',//是否推荐
        solutionTypeId: '',//方案类型
        // fileList:[],
        // bannerImageList:[],
        //行业痛点
        solutionPainList: [
          {
            solutionPainName: "",
            solutionPainContent: "",
          }
        ],
        //方案优势
        solutionAdvantageList: [
          {
            solutionAdvantageName: '',
            solutionAdvantageType: '',
            solutionAdvantageContent:'',
            solutionAdvantageImage:''
          }
        ],
        //案例实施
        solutionCaseList: [
          {
            solutionCaseName: "",
            solutionCaseIntroduction: "",
            solutionCaseContent: "",
          }
        ],
      },
      this.solutionType = [],
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.solutionId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.getListSolution()
      this.title = "添加方案";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const solutionId = row.solutionId || this.ids
      this.getListSolution()
      getSolution(solutionId).then(response => {
        this.form = response.data;
        setTimeout(() => {
        // 在这里执行你想要延迟执行的函数或代码
        if (response.data.solutionTypeId) {
            this.solutionType=this.getParentsById(this.solutionTypeList,response.data.solutionTypeId)
            console.log("pid",this.solutionType)
          }
          this.open = true;
          this.title = "修改解决方案";
      }, 1000); // 设置延迟时间，这里是1秒（1000毫秒）
        
      });
    },
    /*
    * el-cascader递归获取父级id
    * @param  list 数据列表
    * @param  id 后端返回的id
    * propsCascader 是el-cascader props属性
    **/
    getParentsById(list, id) {
      for (let i in list) {
        if (list[i][this.propsCascader.value || 'value'] == id) {
          return [list[i][this.propsCascader.value || 'value']]
        }
        if (list[i].children) {
          let node = this.getParentsById(list[i].children, id)
          if (node !== undefined) {
            // 追加父节点
            node.unshift(list[i][this.propsCascader.value || 'value'])
            return node
          }
        }
      }
    },
    
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(this.solutionType != null && this.solutionType.length > 0) {
            this.form.solutionTypeId = this.solutionType.slice(-1)[0]
          }
          if (this.form.solutionId != null) {
            updateSolution(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSolution(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const solutionIds = row.solutionId || this.ids;
      this.$modal.confirm('是否确认删除解决方案编号为"' + solutionIds + '"的数据项？').then(function () {
        return delSolution(solutionIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/solution/export', {
        ...this.queryParams
      }, `solution_${new Date().getTime()}.xlsx`)
    },
    //行业痛点添加行/删除行
    addPainSpotRow() {
      this.form.solutionPainList.push({
        edit: true,
      });
    },
    deletePainSpotRow(row, index) {
      if (index != 0) {
        this.form.solutionPainList.splice(index, 1);
      }
    },
    //方案优势
    addAdvantageContentRow() {
      this.form.solutionAdvantageList.push({
        edit: true,
        solutionAdvantageImage:''
      });
    },
    deleteAdvantageContentRow(row, index) {
      if (index != 0) {
        this.form.solutionAdvantageList.splice(index, 1);
      }
    },
    //实施案例
    addSolutionCasesListRow() {
      this.form.solutionCaseList.push({
        edit: true,
      });
    },
    deleteSolutionCasesListRow(row, index) {
      if (index != 0) {
        this.form.solutionCaseList.splice(index, 1);
      }
    },
    // banner图片上传
    uploadFun1(params) {
      const file = params.file;
      let form = new FormData();
      form.append("file", file); // 文件对象
      comUpload(form).then(res => {
        let data = res.data;
        this.$set(this.form, 'solutionBanner', data.fileFullPath)  // 图片全路径
        // this.$set(this.form,'imageUrl',data.fileId) // 图片Id
      })
    },
    beforeAvatarUpload1(file) {
      const isJPG = file.type === 'image/jpeg' || 'image/png' || 'image/jpg';
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error('上传图片只能是 jpg、jpeg、png 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    //上传图片
    uploadFun2(params) {
      const file = params.file;
      let form = new FormData();
      form.append("file", file); // 文件对象
      comUpload(form).then(res => {
        let data = res.data;
        this.$set(this.form, 'solutionImg', data.fileFullPath)  // 图片全路径
        // this.$set(this.form,'imageUrl',data.fileId) // 图片Id
      })
    },
    beforeAvatarUpload2(file) {
      const isJPG = file.type === 'image/jpeg' || 'image/png' || 'image/jpg';
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error('上传图片只能是 jpg、jpeg、png 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    //方案优势上传图片
    uploadFun3(params,index) {
      console.log("index",index)
      const file = params.file;
      let form = new FormData();
      form.append("file", file); // 文件对象
      comUpload(form).then(res => {
        let data = res.data;
        this.form.solutionAdvantageList[index].solutionAdvantageImage=data.fileFullPath
        console.log("this.form.solutionAdvantageList",this.form.solutionAdvantageList)
      })
    },
    beforeAvatarUpload3(file) {
      const isJPG = file.type === 'image/jpeg' || 'image/png' || 'image/jpg';
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error('上传图片只能是 jpg、jpeg、png 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    /** 查询解决方案类型列表 */
    getListSolution() {
      let that = this
      let queryParams = {
        pageNum: 1,
        pageSize: 100,
      }
      listSolutionType(queryParams).then(response => {
        console.log("response.rows",response.rows)
        let arrList = response.rows
        let data = []
        arrList.forEach(item => {
          data.push({
            value: item.solutionTypeId,
            parentId: item.parentId,
            label: item.solutionTypeName
          })
        });
        that.solutionTypeList = that.tranListToTreeData(data, null)
        console.log("that.solutionTypeList", that.solutionTypeList)
      });
    },
    //把list整理成树形
    tranListToTreeData(list, parentId = null) {
      const arr = [];
      list.forEach((item) => {
        if (item.parentId === parentId) {
          // 递归调用
          const children = this.tranListToTreeData(list, item.value)
          if (children.length) {
            item.children = children;
          }
          arr.push(item);
        }
      });
      console.log("arr", arr)
      return arr;
    },
    handleChange(value) {
      console.log(value);
      //this.$set(this.form, 'solutionTypeId', value)
    },
  }
};
</script>
<style  scoped>
>>>.el-table .el-dropdown-link {
  cursor: pointer;
  color: #409eff;
  margin-left: 5px;
}

.avatar-uploader>>>.el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
</style>
