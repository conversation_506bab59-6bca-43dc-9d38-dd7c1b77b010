<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="企业名称" prop="companyName">
        <el-input v-model="queryParams.companyName" placeholder="请输入企业名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="行业" prop="industry">
        <el-input v-model="queryParams.industry" placeholder="请输入行业" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="社会信用代码" prop="socialCreditCode">
        <el-input
          v-model="queryParams.socialCreditCode"
          placeholder="请输入社会信用代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="产品与服务" prop="registeredCapital">
        <el-input
          v-model="queryParams.registeredCapital"
          placeholder="请输入产品与服务"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input v-model="queryParams.contactPhone" placeholder="请输入联系电话" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="平台类型" prop="factoryType">
        <el-select v-model="queryParams.factoryType" placeholder="请选择平台类型" clearable>
          <el-option v-for="item in dict.type.entrepreneurship_type" :key="item.key" :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="技术人员姓名" prop="technicianName">
        <el-input
          v-model="queryParams.technicianName"
          placeholder="请输入技术人员姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="生产设备" prop="productionEquipment">
        <el-input
          v-model="queryParams.productionEquipment"
          placeholder="请输入生产设备"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="规格型号" prop="specification">
        <el-input
          v-model="queryParams.specification"
          placeholder="请输入规格型号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:settledFactory:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:settledFactory:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:settledFactory:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['system:settledFactory:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="settledFactoryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="企业名称" align="center" prop="companyName" />
      <el-table-column label="行业" align="center" prop="industry" />
      <el-table-column label="公司地址" align="center" prop="companyAddress" />
      <el-table-column label="社会信用代码" align="center" prop="socialCreditCode" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" />
      <el-table-column label="审核状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.settledStatus == 1 ? "已审核" : "待审核" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:settledFactory:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:settledFactory:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改入驻工厂对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="企业名称" prop="companyName">
          <el-input v-model="form.companyName" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="行业" prop="industry">
          <el-input v-model="form.industry" placeholder="请输入行业" />
        </el-form-item>
        <el-form-item label="公司地址" prop="companyAddress">
          <el-input v-model="form.companyAddress" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="社会信用代码" prop="socialCreditCode">
          <el-input v-model="form.socialCreditCode" placeholder="请输入社会信用代码" />
        </el-form-item>
        <el-form-item label="经营范围" prop="businessScope">
          <el-input v-model="form.businessScope" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <!-- <el-form-item label="注册资本" prop="registeredCapital">
        <el-input v-model="form.registeredCapital" placeholder="请输入注册资本" />
      </el-form-item> -->
        <el-form-item label="产品与服务" prop="registeredCapital">
          <el-input v-model="form.registeredCapital" placeholder="请输入产品与服务" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="公司简介" prop="qrCode">
          <el-input v-model="form.qrCode" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="资质证件" prop="qualificationFiles">
          <el-input v-model="form.qualificationFiles" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="技术人员姓名" prop="technicianName">
          <el-input v-model="form.technicianName" placeholder="请输入技术人员姓名" />
        </el-form-item>
        <el-form-item label="技术实力" prop="technicalCapability">
          <el-input v-model="form.technicalCapability" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="生产设备" prop="productionEquipment">
          <el-input v-model="form.productionEquipment" placeholder="请输入生产设备" />
        </el-form-item>
        <el-form-item label="规格型号" prop="specification">
          <el-input v-model="form.specification" placeholder="请输入规格型号" />
        </el-form-item>
        <el-form-item label="审核状态" prop="settledStatus">
          <el-select v-model="form.settledStatus" placeholder="请选择" clearable style="width: 100%">
            <el-option v-for="dict in auditStatusList" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="平台类型" prop="factoryType">
          <el-select v-model="form.factoryType" placeholder="请选择平台类型">
            <el-option v-for="item in dict.type.entrepreneurship_type" :key="item.key" :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSettledFactory,
  getSettledFactory,
  delSettledFactory,
  addSettledFactory,
  updateSettledFactory,
} from "@/api/system/settledFactory";

export default {
  name: "SettledFactory",
  dicts: ["entrepreneurship_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 入驻工厂表格数据
      settledFactoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyName: null,
        industry: null,
        companyAddress: null,
        socialCreditCode: null,
        businessScope: null,
        registeredCapital: null,
        contactPhone: null,
        qrCode: null,
        qualificationFiles: null,
        technicianName: null,
        technicalType: null,
        technicalCapability: null,
        productionEquipment: null,
        specification: null,
        factoryType: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        companyName: [
          { required: true, message: "企业名称不能为空", trigger: "blur" },
        ],
        industry: [
          { required: true, message: "行业不能为空", trigger: "blur" },
        ],
        companyAddress: [
          { required: true, message: "公司地址不能为空", trigger: "blur" },
        ],
        socialCreditCode: [
          { required: true, message: "社会信用代码不能为空", trigger: "blur" },
        ],
        contactPhone: [
          { required: true, message: "联系电话不能为空", trigger: "blur" },
        ],
      },
      auditStatusList: [
        {
          dictValue: "0",
          dictLabel: "待审核",
        },
        {
          dictValue: "1",
          dictLabel: "已审核",
        },
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询入驻工厂列表 */
    getList() {
      this.loading = true;
      listSettledFactory(this.queryParams).then((response) => {
        this.settledFactoryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        companyName: null,
        industry: null,
        companyAddress: null,
        socialCreditCode: null,
        businessScope: null,
        registeredCapital: null,
        contactPhone: null,
        qrCode: null,
        qualificationFiles: null,
        technicianName: null,
        technicalType: null,
        technicalCapability: null,
        productionEquipment: null,
        specification: null,
        createTime: null,
        updateTime: null,
        settledStatus: null,
        factoryType: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加入驻工厂";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getSettledFactory(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改入驻工厂";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateSettledFactory(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSettledFactory(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除入驻工厂编号为"' + ids + '"的数据项？')
        .then(function () {
          return delSettledFactory(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/settledFactory/export",
        {
          ...this.queryParams,
        },
        `settledFactory_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
