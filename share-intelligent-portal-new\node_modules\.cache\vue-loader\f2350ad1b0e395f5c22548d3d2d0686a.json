{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\supplyDemand\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\supplyDemand\\index.vue", "mtime": 1750311963085}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IGRlbWFuZCBmcm9tICIuL2NvbXBvbmVudHMvZGVtYW5kLnZ1ZSI7DQppbXBvcnQgc3VwcGx5IGZyb20gIi4vY29tcG9uZW50cy9zdXBwbHkudnVlIjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlVzZXIiLA0KICBjb21wb25lbnRzOiB7IFVzZXJNZW51LCBkZW1hbmQsIHN1cHBseSB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBkZW1hbmRTdXBwbHk6ICIxIiwNCiAgICAgIHR5cGVMaXN0OiBbXSwNCiAgICAgIHN0YXR1c0xpc3Q6IFtdLA0KICAgICAgZm9ybTogew0KICAgICAgICBvcmRlclN0YXR1czogIiIsDQogICAgICB9LA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7fSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUNsaWNrKHZhbCkgew0KICAgICAgdGhpcy5kZW1hbmRTdXBwbHkgPSB2YWw7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/supplyDemand", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div>\r\n          <!-- 顶部导航 -->\r\n          <div class=\"navStyle\">\r\n            <div\r\n              class=\"navStyle-left\"\r\n              @click=\"handleClick('1')\"\r\n              :style=\"\r\n                demandSupply == '1'\r\n                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'\r\n                  : 'border-bottom: none;cursor: pointer;'\r\n              \"\r\n            >\r\n              <div class=\"text\">我的需求</div>\r\n            </div>\r\n            <div\r\n              class=\"navStyle-left\"\r\n              @click=\"handleClick('2')\"\r\n              style=\"margin-left: 24px\"\r\n              :style=\"\r\n                demandSupply == '2'\r\n                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'\r\n                  : 'border-bottom: none;cursor: pointer;'\r\n              \"\r\n            >\r\n              <div class=\"text\">我的供给</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"typeStyle\">\r\n            <div class=\"typeLine\"></div>\r\n            <div class=\"typeText\">\r\n              我的{{ demandSupply == \"1\" ? \"需求\" : \"供给\" }}\r\n            </div>\r\n          </div>\r\n          <div style=\"margin-top: 20px\">\r\n            <demand v-if=\"demandSupply == '1'\"></demand>\r\n            <supply v-if=\"demandSupply == '2'\"></supply>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport demand from \"./components/demand.vue\";\r\nimport supply from \"./components/supply.vue\";\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu, demand, supply },\r\n  data() {\r\n    return {\r\n      demandSupply: \"1\",\r\n      typeList: [],\r\n      statusList: [],\r\n      form: {\r\n        orderStatus: \"\",\r\n      },\r\n    };\r\n  },\r\n  created() {},\r\n  methods: {\r\n    handleClick(val) {\r\n      this.demandSupply = val;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 1080px;\r\n}\r\n.navStyle {\r\n  height: 50px;\r\n  border-bottom: 1px solid #ccc;\r\n  display: flex;\r\n  align-items: center;\r\n  .navStyle-left {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 50px;\r\n    border-bottom: 2px solid #21c9b8;\r\n  }\r\n  .text {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #21c9b8;\r\n  }\r\n  .navStyle-right {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #333333;\r\n    margin-left: auto;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.typeStyle {\r\n  width: 100%;\r\n  height: 50px;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  margin-top: 20px;\r\n  .typeLine {\r\n    width: 2px;\r\n    height: 20px;\r\n    background: #10af9f;\r\n  }\r\n  .typeText {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #333333;\r\n    margin-left: 17px;\r\n  }\r\n}\r\n</style>\r\n"]}]}