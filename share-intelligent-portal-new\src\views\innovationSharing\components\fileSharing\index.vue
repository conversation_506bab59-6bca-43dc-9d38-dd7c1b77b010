<template>
  <div class="content">
    <div class="content_banner">文件共享</div>
    <div class="card-container card_content">
      <el-table v-loading="loading" :data="fileList" key="table2">
        <el-table-column label="文件名称" align="center" prop="fileName" width="280" :show-overflow-tooltip="true" />
        <el-table-column label="文件类别" align="fileType" prop="category" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.file_category" :value="scope.row.category" />
          </template>
        </el-table-column>
        <el-table-column label="文件介绍" align="center" prop="description" width="600" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <div class="btnStyle" @click="jumpIntention(scope.row)">
              申请查看
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pageStyle">
        <el-pagination v-if="fileList && fileList.length > 0" background layout="prev, pager, next"
          class="activity-pagination" :page-size="pageSize" :current-page="pageNum" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { fileSharingListData } from "@/api/innovationSharing";

export default {
  dicts: ['file_category', 'file_sub_category'],
  data() {
    return {
      pageNum: 1,
      pageSize: 20,
      total: 0,
      loading: false,
      fileList: [
        {
          id: 1,
          fileName: "1111",
        },
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      fileSharingListData(params).then((response) => {
        if (response.code == 200) {
          this.fileList = response.rows;
          this.total = response.total;
          this.loading = false;
        } else {
          this.$message.error(response.msg);
          this.loading = false;
        }
      });
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    interested(id) {
      this.$router.push("/fileInterested?id=" + id);
    },
    jumpIntention(item) {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => { });
        return;
      } else {
        this.$router.push(`/fileInterested?demandName=${item.fileName}&updateTime=${item.updateTime}&intentionType=10&fieldName=文件库&intentionId=${item.id}`);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  padding-bottom: 60px;
  background-color: #f2f2f2;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../../assets/release/banner.png");
  background-size: 100% 100%;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
  text-align: center;
  line-height: 300px;
}

.card_content {
  // height: 980px;
  background: #ffffff;
  border-radius: 2px;
  margin-top: 30px;
  padding: 40px;

  .btnStyle {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #21c9b8;
    cursor: pointer;
  }

  .pageStyle {
    margin-top: 60px;
    width: 100%;
    text-align: center;
  }
}
</style>
