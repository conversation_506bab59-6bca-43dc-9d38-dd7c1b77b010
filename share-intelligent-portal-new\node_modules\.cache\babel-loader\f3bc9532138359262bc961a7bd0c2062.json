{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\AppMain.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\AppMain.vue", "mtime": 1750311962841}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9pbmRleCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9JZnJhbWVUb2dnbGUvaW5kZXgiKSk7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnQXBwTWFpbicsCiAgY29tcG9uZW50czogewogICAgaWZyYW1lVG9nZ2xlOiBfaW5kZXguZGVmYXVsdAogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGNhY2hlZFZpZXdzOiBmdW5jdGlvbiBjYWNoZWRWaWV3cygpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnRhZ3NWaWV3LmNhY2hlZFZpZXdzOwogICAgfSwKICAgIGtleTogZnVuY3Rpb24ga2V5KCkgewogICAgICByZXR1cm4gdGhpcy4kcm91dGUucGF0aDsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "name", "components", "iframe<PERSON>oggle", "computed", "cachedViews", "$store", "state", "tagsView", "key", "$route", "path"], "sources": ["src/layout/components/AppMain.vue"], "sourcesContent": ["<template>\r\n  <section class=\"app-main el-scrollbar\">\r\n    <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <keep-alive :include=\"cachedViews\">\r\n        <router-view v-if=\"!$route.meta.link\" :key=\"key\" />\r\n      </keep-alive>\r\n    </transition>\r\n    <iframe-toggle />\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport iframeToggle from \"./IframeToggle/index\"\r\n\r\nexport default {\r\n  name: 'AppMain',\r\n  components: { iframeToggle },\r\n  computed: {\r\n    cachedViews() {\r\n      return this.$store.state.tagsView.cachedViews\r\n    },\r\n    key() {\r\n      return this.$route.path\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-main {\r\n  /* 50= navbar  50  */\r\n  min-height: calc(100vh - 80px);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow-x: hidden;\r\n}\r\n\r\n.fixed-header + .app-main {\r\n  padding-top: 80px;\r\n}\r\n\r\n.hasTagsView {\r\n  .app-main {\r\n    /* 84 = navbar + tags-view = 50 + 34 */\r\n    min-height: calc(100vh - 84px);\r\n  }\r\n\r\n  .fixed-header + .app-main {\r\n    padding-top: 84px;\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n// fix css style bug in open el-dialog\r\n.el-popup-parent--hidden {\r\n  .fixed-header {\r\n    padding-right: 17px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAYA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IAAAC,YAAA,EAAAA;EAAA;EACAC,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,WAAA;IACA;IACAI,GAAA,WAAAA,IAAA;MACA,YAAAC,MAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}