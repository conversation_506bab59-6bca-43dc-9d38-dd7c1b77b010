{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\innovationSharing\\components\\fileSharing\\interested.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\innovationSharing\\components\\fileSharing\\interested.vue", "mtime": 1750311962960}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["interested.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "interested.vue", "sourceRoot": "src/views/innovationSharing/components/fileSharing", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">我有意向</div>\r\n      <div style=\"height: 33px; margin-top: 21px\">I have intentions</div>\r\n    </div>\r\n    <div class=\"card-container card-content\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <div class=\"imgStyle\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../../assets/device/ceshi.png\" alt=\"\" />\r\n        </div>\r\n        <div class=\"title\">{{ form.title }}</div>\r\n        <div style=\"display: flex; align-items: center; margin-top: 15px\">\r\n          <div class=\"publishTimeStyle\">发布时间：{{ updateTime }}</div>\r\n          <!-- <div class=\"detailStyle\" @click=\"goDetail\">查看详情 >></div> -->\r\n        </div>\r\n      </div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <span class=\"resourceType\">资源类型：</span>\r\n          <span class=\"resourceValue\">文件共享</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <span class=\"resourceType\">资源名称：</span>\r\n          <span class=\"resourceValue\">{{ form.title }}</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <span class=\"resourceType\">归属单位：</span>\r\n          <span class=\"resourceValue\">青岛创意科技有限公司</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"申请期限\" prop=\"term\">\r\n              <el-radio-group v-model=\"form.term\">\r\n                <el-radio v-for=\"dict in appliDeadlineList\" :key=\"dict.value\" :label=\"dict.value\" :value=\"dict.value\">{{\r\n                  dict.label }}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item label=\"意向企业：\" prop=\"companyName\">\r\n              <el-input disabled v-model=\"form.companyName\" placeholder=\"自动带出\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人：\" prop=\"linkMan\">\r\n              <el-input disabled v-model=\"form.linkMan\" placeholder=\"请先维护联系人\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话：\" prop=\"linkTel\">\r\n              <el-input disabled v-model=\"form.linkTel\" placeholder=\"请先维护联系方式\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button style=\"width: 100%; height: 50px\" type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n        <div class=\"promptStyle\">温馨提示</div>\r\n        <div class=\"desc\" style=\"margin-top: 20px\">\r\n          1、我们会在最快的时间和您取得联系（工作时间周一至周五8:00-18:00）\r\n        </div>\r\n        <div class=\"desc\" style=\"margin-top: 13px\">\r\n          2、紧急问题请拨打：15512688882\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { update } from 'ramda';\r\nimport { submitIntention } from \"@/api/home\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        title: \"\",\r\n      },\r\n      updateTime: \"2025-03-17\",\r\n      appliDeadlineList: [\r\n        {\r\n          label: \"15天\",\r\n          value: \"1\",\r\n        },\r\n        {\r\n          label: \"30天\",\r\n          value: \"2\",\r\n        },\r\n        {\r\n          label: \"45天\",\r\n          value: \"3\",\r\n        },\r\n        {\r\n          label: \"60天\",\r\n          value: \"4\",\r\n        },\r\n        {\r\n          label: \"90天\",\r\n          value: \"5\",\r\n        },\r\n      ],\r\n      rules:{\r\n        term:[\r\n          { required: true, message: '请选择申请期限', trigger: 'change' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n    if (userinfo) {\r\n      this.form.companyName = userinfo.memberCompanyName;\r\n      this.form.linkMan = userinfo.companyRealName;\r\n      this.form.linkTel = userinfo.memberPhone;\r\n    }\r\n    if (this.$route.query.demandName && this.$route.query.demandName != 'null') {\r\n      this.form.title = this.$route.query.demandName\r\n    }\r\n    if (this.$route.query.intentionType) {\r\n      this.form.intentionType = parseInt(this.$route.query.intentionType)\r\n    }\r\n    if (this.$route.query.fieldName) {\r\n      this.form.fieldName = this.$route.query.fieldName\r\n    }\r\n    if (this.$route.query.intentionId) {\r\n      this.form.intentionId = this.$route.query.intentionId\r\n    }\r\n    if (this.$route.query.updateTime && this.$route.query.updateTime != 'null' && this.$route.query.updateTime != 'undefined') {\r\n      this.updateTime = this.$route.query.updateTime\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          submitIntention(this.form).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$message.success(\"提交成功\")\r\n              this.cancel()\r\n            } else {\r\n              this.$message.error(res.msg)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    goDetail() {\r\n      this.$router.push(\"/innovationSharing\");\r\n    },\r\n    cancel(){\r\n      this.$router.go(-1)\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: rgb(242, 242, 242);\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n}\r\n\r\n.card-content {\r\n  display: flex;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: -70px;\r\n  padding: 60px 59px 62px 60px;\r\n\r\n  .card_left {\r\n    .imgStyle {\r\n      width: 330px;\r\n      height: 230px;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #333333;\r\n      margin-top: 23px;\r\n    }\r\n\r\n    .publishTimeStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n    }\r\n\r\n    .detailStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #21c9b8;\r\n      margin-left: auto;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .card_right {\r\n    margin-left: 40px;\r\n    width: 100%;\r\n\r\n    .resourceType {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .resourceValue {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #666666;\r\n    }\r\n\r\n    .footer-submit {\r\n      margin-top: 40px;\r\n    }\r\n\r\n    .promptStyle {\r\n      margin-top: 30px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .desc {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}