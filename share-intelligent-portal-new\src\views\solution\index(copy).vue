<template>
  <div class="activity-container">
    <div class="activity-banner">
      <img src="../../assets/solution/solution.png" alt="" />
      <div class="bannerTitle">解决方案</div>
      <div class="bannerDesc">
        沉淀众多优秀解决方案，提供适用于不同行业、领域的数字化转型服务方案
      </div>
    </div>
    <div>
      <div class="activity-title-content">
        <!-- <div class="activity-title-box">
          <div class="activity-divider"></div>
          <div class="activity-title">解决方案</div>
          <div class="activity-divider"></div>
        </div> -->
        <div class="activity-search-box">
          <el-form ref="form" class="activity-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.keywords"
                placeholder="请输入搜索内容"
                class="activity-search-input"
              >
                <el-button
                  slot="append"
                  class="activity-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="solutionContent">
        <div class="solutionLeft">
          <div
            class="itemType"
            :class="flag === '全部' ? 'itemTypeHover' : ''"
            @click="getItemData({ typeName: '全部' })"
          >
            全部（{{ allAmounts }}）
          </div>
          <div
            class="itemType"
            :class="flag === item.typeName ? 'itemTypeHover' : ''"
            v-for="item in solutionTypeList"
            :key="item.id"
            @click="getItemData(item)"
          >
            {{ item.typeName }}（{{ item.value }}）
          </div>
        </div>
        <div class="solutionRight">
          <div style="display: flex; flex-wrap: wrap" v-loading="loading">
            <div
              class="itemContent"
              v-for="item in solutionListData"
              :key="item.id"
              @click="goDetail(item.id)"
            >
              <!-- <div class="content_left">
              <img src="" alt="">
            </div> -->
              <div class="content_right">
                <div class="title">{{ item.name }}</div>
                <div class="desc">
                  {{ item.introduction }}
                </div>
              </div>
            </div>
          </div>
          <div class="activity-page-end">
            <!-- <el-button class="activity-page-btn" @click="goHome"
              >首页</el-button
            > -->
            <el-pagination
              v-if="solutionListData && solutionListData.length > 0"
              background
              layout="prev, pager, next"
              class="activity-pagination"
              :page-size="pageSize"
              :current-page="pageNum"
              :total="total"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { getActivityList } from "@/api/purchaseSales";
import { solutionType, solutionTypeName, solutionList } from "@/api/solution";
import { getDicts } from "@/api/system/dict/data";
import { caseList } from "@/api/classicCase";

export default {
  data() {
    return {
      fit: "cover",
      loading: false,
      form: {
        keywords: "", //搜索内容
      },
      formInfo: {
        caseType: "", // 案例类型
      },
      caseTypeList: [],
      data: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      allAmounts: 0,
      solutionTypeList: [
        {
          value: "0",
          label: "全部",
        },
        {
          value: "1",
          label: "节能减排",
        },
        {
          value: "2",
          label: "低碳认证",
        },
        {
          value: "3",
          label: "数据核算",
        },
        {
          value: "4",
          label: "中和服务",
        },
        {
          value: "5",
          label: "星碳培训",
        },
        {
          value: "6",
          label: "绿色会议",
        },
        {
          value: "7",
          label: "数据建模",
        },
        {
          value: "8",
          label: "资产管理",
        },
      ],
      flag: "全部",
      solutionListData: [],
    };
  },
  created() {
    // this.initData();
    // this.getDictsList("activity_type", "activityTypeList");
    // this.search();
  },
  methods: {
    getList(id) {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        grounding: 0,
        parentId: 1,
        searchStr: this.form.keywords,
        typeId: id,
      };
      solutionList(params).then((res) => {
        if (res.code === 200) {
          console.log(res.rows, "-------------");
          this.solutionListData = res.rows;
          if (!id) {
            this.allAmounts = res.total;
          }
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    changeRadio() {
      this.onSearch();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    onSearch() {
      this.pageNum = 1;
      this.getList();
    },
    goHome() {
      this.$router.push({ path: "/index" });
    },
    initData() {
      let params = {
        type: "industry",
      };
      solutionType(params).then((res) => {
        if (res.code === 200) {
          this.getTypeName(res.data);
        }
      });
      // getDicts("case_industry").then((res) => {
      //   const { code, data = [] } = res;
      //   if (code === 200) {
      //     this.caseTypeList = data;
      //     this.getCaseList();
      //   }
      // });
    },
    getTypeName(list) {
      let params = {
        parentId: 1,
      };
      solutionTypeName(params).then((res) => {
        if (res.code === 200) {
          for (var i = 0; i < res.rows.length; i++) {
            for (var k = 0; k < res.rows.length; k++) {
              if (res.rows[i].typeName == list[k].key) {
                res.rows[i].value = list[k].value;
              }
            }
          }
          this.solutionTypeList = res.rows;
          this.getList();
        }
      });
    },
    getItemData(item) {
      this.flag = item.typeName;
      this.getList(item.id);
    },
    goDetail(id) {
      let routeData = this.$router.resolve({
        path: "/solutionDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
  },
};
</script>

<style lang="scss" scoped>
.activity-container {
  width: 100%;
  background: #ffffff;
  .activity-banner {
    width: 100%;
    height: 500px;
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .bannerTitle {
      position: absolute;
      top: 161px;
      left: 24%;
      font-size: 50px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
    }
    .bannerDesc {
      position: absolute;
      top: 249px;
      left: 24%;
      font-size: 24px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }
  }
  .activity-title-content {
    width: 100%;
    background-color: #fff;
    // padding-bottom: 18px;
    .activity-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .activity-title {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .activity-divider {
        width: 48px;
        height: 4px;
        background: #21c9b8;
      }
    }
    .activity-search-box {
      margin-top: 40px;
      .activity-search-form {
        text-align: center;
        .activity-search-input {
          width: 792px;
          height: 54px;
          .activity-search-btn {
            width: 100px;
          }
        }
      }
    }
  }
  .none-class {
    text-align: center;
    padding: 8% 0;
    background: #fff;
    margin-top: 25px;
    .text {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 14px;
    }
  }
  .solutionContent {
    width: 1200px;
    background-color: rgb(252, 252, 252);
    margin: 40px auto;
    display: flex;
    .solutionLeft {
      width: 172px;
      height: 100%;
      overflow-y: auto;
      .itemType {
        width: 100%;
        height: 24px;
        margin-top: 42px;
        padding-left: 30px;
        cursor: pointer;
      }
      .itemTypeHover {
        border-left: 4px solid #21c9b8;
        color: #21c9b8;
      }
      .itemType:nth-child(1) {
        margin-top: 0;
      }
    }
    .solutionRight {
      width: 1000px;
      margin-left: 20px;
      // display: flex;
      // flex-wrap: wrap;
      margin: 0 auto;
      .itemContent {
        width: 490px;
        height: 190px;
        padding: 30px 42px 30px 26px;
        display: flex;
        background: #ffffff;
        margin-left: 20px;
        margin-top: 20px;
        cursor: pointer;
      }
      .itemContent:hover {
        box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);
        border-radius: 2px;
      }
      .itemContent:nth-child(2n + 1) {
        margin-left: 0;
      }
    }
    // .content_left {
    //   width: 130px;
    //   height: 130px;
    //   background: #ccc;
    // }
    .content_right {
      width: 402px;
      height: 130px;
      margin-left: 20px;
      .title {
        font-size: 20px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #222222;
        margin-top: 20px;
      }
      .desc {
        margin-top: 24px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #65676a;
      }
    }
  }
  .activity-page-end {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    padding: 24px 0 60px;
    .activity-page-btn {
      width: 82px;
      height: 32px;
      background: #fff;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #333;
      line-height: 10px;
    }
  }
}
</style>

<style lang="scss">
.activity-container {
  .activity-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #21c9b8;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #fff;
      line-height: 24px;
    }
  }
  .activity-search-line {
    .el-form-item__label {
      width: 88px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #999;
      padding-right: 32px;
      text-align: left;
    }
    .activity-search-radio {
      width: 1050px;
      margin-top: 11px;
      .el-radio-button {
        padding-bottom: 20px;
        .el-radio-button__inner {
          border: none;
          padding: 0 32px 0 0;
          background: none;
          &:hover {
            color: #21c9b8;
          }
        }
        &.is-active {
          .el-radio-button__inner {
            color: #21c9b8;
            background: none;
          }
        }
        .el-radio-button__orig-radio:checked {
          & + .el-radio-button__inner {
            box-shadow: unset;
          }
        }
      }
    }
  }
  .activity-page-end {
    .activity-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
}
</style>
