{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\application\\publish.vue?vue&type=style&index=0&id=a91d947c&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\application\\publish.vue", "mtime": 1750311963042}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogI2Y0ZjVmOTsNCn0NCi5jb250YWluZXIgew0KICB3aWR0aDogMTAwJTsNCiAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgcGFkZGluZzogNDBweDsNCn0NCi5jb250ZW50IHsNCiAgd2lkdGg6IDEwMCU7DQogIC8vIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIC5zdGVwc1N0eWxlIHsNCiAgICBwYWRkaW5nOiAwIDEzJTsNCiAgfQ0KICAuY3VycmVudENvbnRlbnQgew0KICAgIG1hcmdpbi10b3A6IDMwcHg7DQogICAgcGFkZGluZy1sZWZ0OiA1JTsNCiAgICBwYWRkaW5nLXJpZ2h0OiA0MCU7DQogIH0NCiAgLnRpdGxlIHsNCiAgICBmb250LXNpemU6IDIycHg7DQogICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICBtYXJnaW4tYm90dG9tOiAzMHB4Ow0KICAgIG1hcmdpbi1sZWZ0OiAyMHB4Ow0KICB9DQogIC5zdWJtaXRTdHlsZSB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHJpZ2h0Ow0KICB9DQogIC5idXR0b25TdHlsZSB7DQogICAgd2lkdGg6IDEwMHB4Ow0KICAgIHBhZGRpbmc6IDEwcHg7DQogICAgYmFja2dyb3VuZDogIzIxYzliODsNCiAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["publish.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAs2BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "publish.vue", "sourceRoot": "src/views/system/user/application", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"container\">\r\n          <div class=\"content\">\r\n            <!-- 步骤条 -->\r\n            <div class=\"stepsStyle\">\r\n              <el-steps :active=\"active\">\r\n                <el-step title=\"创建应用\" description=\"去创建\"></el-step>\r\n                <el-step title=\"配置开发管理\" description=\"地址配置\"></el-step>\r\n                <!-- <el-step title=\"应用测试\" description=\"查看调试文档\"></el-step> -->\r\n                <el-step title=\"上架应用\" description=\"去上架\"></el-step>\r\n              </el-steps>\r\n            </div>\r\n            <div class=\"currentContent\">\r\n              <div v-show=\"active == 0\">\r\n                <div class=\"title\">基本信息</div>\r\n                <el-form\r\n                  :model=\"ruleForm\"\r\n                  :rules=\"rules\"\r\n                  ref=\"appliForm\"\r\n                  label-width=\"100px\"\r\n                  class=\"demo-ruleForm\"\r\n                >\r\n                  <el-form-item label=\"应用名称\" prop=\"appName\">\r\n                    <el-input v-model=\"ruleForm.appName\"></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用类型\" prop=\"appCategory\">\r\n                    <el-select\r\n                      v-model=\"ruleForm.appCategory\"\r\n                      placeholder=\"请选择应用类型\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item, index) in appliTypeData\"\r\n                        :key=\"index\"\r\n                        :label=\"item.dictLabel\"\r\n                        :value=\"item.dictLabel\"\r\n                      ></el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用服务端\" prop=\"applicaServer\">\r\n                    <el-radio-group v-model=\"ruleForm.applicaServer\">\r\n                      <el-radio label=\"0\">APP端</el-radio>\r\n                      <el-radio label=\"1\">web端</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"交付方式\" prop=\"delivery\">\r\n                    <el-radio-group v-model=\"ruleForm.delivery\">\r\n                      <el-radio label=\"0\">Saas服务</el-radio>\r\n                      <el-radio label=\"1\">本地部署</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用简介\" prop=\"briefInto\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.briefInto\"\r\n                      type=\"textarea\"\r\n                      :rows=\"3\"\r\n                      resize=\"none\"\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用详情\" prop=\"content\">\r\n                    <editor v-model=\"ruleForm.content\" :min-height=\"192\" />\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用封面\" prop=\"appLogo\">\r\n                    <el-upload\r\n                      :limit=\"1\"\r\n                      list-type=\"picture-card\"\r\n                      :headers=\"headers\"\r\n                      :action=\"uploadUrl\"\r\n                      :file-list=\"personalCardList\"\r\n                      :accept=\"accept\"\r\n                      :before-upload=\"handleBeforeUpload\"\r\n                      :on-preview=\"handlePersonalCardPreview\"\r\n                      :on-remove=\"handleRemove\"\r\n                      :on-success=\"handlePersonalCardSuccess\"\r\n                      :on-exceed=\"handelExceed\"\r\n                    >\r\n                      <i class=\"el-icon-plus\"></i>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用提供\" prop=\"supply\">\r\n                    <el-input v-model=\"ruleForm.supply\"></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"联系人\" prop=\"linkman\">\r\n                    <el-input v-model=\"ruleForm.linkman\"></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"联系方式\" prop=\"phone\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.phone\"\r\n                      @input=\"\r\n                        ruleForm.phone = ruleForm.phone.replace(/[^0-9.]/g, '')\r\n                      \"\r\n                      maxlength=\"11\"\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                </el-form>\r\n              </div>\r\n              <div v-show=\"active == 1\">\r\n                <div class=\"title\">开发管理配置</div>\r\n                <el-form\r\n                  :model=\"ruleForm\"\r\n                  :rules=\"rules\"\r\n                  ref=\"appliForm1\"\r\n                  label-width=\"160px\"\r\n                  class=\"demo-ruleForm\"\r\n                >\r\n                  <el-form-item label=\"服务器出口IP\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.serverIp\"\r\n                      @input=\"\r\n                        ruleForm.serverIp = ruleForm.serverIp.replace(\r\n                          /[^0-9.]/g,\r\n                          ''\r\n                        )\r\n                      \"\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"网页端(Web)应用地址\" prop=\"webUrl\">\r\n                    <el-input v-model=\"ruleForm.webUrl\"></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item\r\n                    label=\"网页端(Web)体验地址\"\r\n                    prop=\"webexperienceUrl\"\r\n                  >\r\n                    <el-input v-model=\"ruleForm.webexperienceUrl\"></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item\r\n                    label=\"健康检查服务端地址\"\r\n                    prop=\"serverexamineUrl\"\r\n                  >\r\n                    <el-input v-model=\"ruleForm.serverexamineUrl\"></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"开发联系人\" prop=\"developmentPeople\">\r\n                    <el-input v-model=\"ruleForm.developmentPeople\"></el-input>\r\n                  </el-form-item>\r\n                  <!-- <el-form-item label=\"获取测试token\">\r\n                    <div>点此获取</div>\r\n                  </el-form-item> -->\r\n                </el-form>\r\n              </div>\r\n              <div v-show=\"active == 2\">\r\n                <div class=\"title\">商品规格信息</div>\r\n                <div>\r\n                  <el-form\r\n                    :model=\"ruleForm\"\r\n                    :rules=\"rules\"\r\n                    ref=\"appliForm2\"\r\n                    label-width=\"180px\"\r\n                    class=\"demo-ruleForm\"\r\n                  >\r\n                    <el-form-item label=\"规格\" prop=\"spec\">\r\n                      <el-input v-model=\"ruleForm.spec\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"使用用户数\" prop=\"userNumber\">\r\n                      <el-input\r\n                        v-model=\"ruleForm.userNumber\"\r\n                        @input=\"\r\n                          ruleForm.userNumber = ruleForm.userNumber.replace(\r\n                            /[^0-9.]/g,\r\n                            ''\r\n                          )\r\n                        \"\r\n                      ></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"有效时间\" prop=\"validTime\">\r\n                      <el-date-picker\r\n                        v-model=\"ruleForm.validTime\"\r\n                        type=\"datetime\"\r\n                        placeholder=\"请选择有效时间\"\r\n                      >\r\n                      </el-date-picker>\r\n                    </el-form-item>\r\n                    <div style=\"margin-top: 20px\">\r\n                      <div class=\"title\">商品价格信息</div>\r\n                      <div style=\"margin-top: 20px\">\r\n                        <el-form-item label=\"订货编码\" prop=\"orderCode\">\r\n                          <el-input\r\n                            v-model=\"ruleForm.orderCode\"\r\n                            @input=\"\r\n                              ruleForm.orderCode = ruleForm.orderCode.replace(\r\n                                /[^\\w\\.\\/]/g,\r\n                                ''\r\n                              )\r\n                            \"\r\n                          ></el-input>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"商品原价（元）\" prop=\"price\">\r\n                          <el-input\r\n                            v-model=\"ruleForm.price\"\r\n                            @input=\"\r\n                              ruleForm.price = ruleForm.price.replace(\r\n                                /[^0-9.]/g,\r\n                                ''\r\n                              )\r\n                            \"\r\n                          ></el-input>\r\n                        </el-form-item>\r\n                        <el-form-item\r\n                          label=\"商品促销价（元）\"\r\n                          prop=\"promotionPrice\"\r\n                        >\r\n                          <el-input\r\n                            v-model=\"ruleForm.promotionPrice\"\r\n                            @input=\"\r\n                              ruleForm.promotionPrice =\r\n                                ruleForm.promotionPrice.replace(/[^0-9.]/g, '')\r\n                            \"\r\n                          ></el-input>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"商品分佣比例（%）\">\r\n                          <el-input\r\n                            v-model=\"ruleForm.commissionRatio\"\r\n                            @input=\"\r\n                              ruleForm.commissionRatio =\r\n                                ruleForm.commissionRatio.replace(/[^0-9.]/g, '')\r\n                            \"\r\n                          ></el-input>\r\n                        </el-form-item>\r\n                        <!-- <div class=\"buttonStyle\" @click=\"addPrice\">新增价格</div>\r\n                          <div style=\"margin-top: 20px\">\r\n                            <el-table\r\n                              :data=\"tableData\"\r\n                              style=\"width: 100%; mni-height: 200px\"\r\n                            >\r\n                              <el-table-column prop=\"date\" label=\"规格\">\r\n                              </el-table-column>\r\n                              <el-table-column prop=\"name\" label=\"订货编码\">\r\n                              </el-table-column>\r\n                              <el-table-column prop=\"name\" label=\"商品原价（元）\">\r\n                              </el-table-column>\r\n                              <el-table-column prop=\"name\" label=\"商品促销价（元）\">\r\n                              </el-table-column>\r\n                              <el-table-column prop=\"name\" label=\"商品分佣比例（%）\">\r\n                              </el-table-column>\r\n                              <el-table-column label=\"操作\">\r\n                                <template>\r\n                                  <div>删除</div>\r\n                                </template>\r\n                              </el-table-column>\r\n                            </el-table>\r\n                          </div> -->\r\n                      </div>\r\n                    </div>\r\n                    <div style=\"margin-top: 20px\">\r\n                      <div class=\"title\">商品参数介绍</div>\r\n                      <div style=\"margin-top: 20px\">\r\n                        <el-form-item\r\n                          label=\"服务咨询电话\"\r\n                          prop=\"consultingTelephone\"\r\n                        >\r\n                          <el-input\r\n                            v-model=\"ruleForm.consultingTelephone\"\r\n                            @input=\"\r\n                              ruleForm.consultingTelephone =\r\n                                ruleForm.consultingTelephone.replace(\r\n                                  /[^0-9.]/g,\r\n                                  ''\r\n                                )\r\n                            \"\r\n                          ></el-input>\r\n                        </el-form-item>\r\n                        <el-form-item\r\n                          label=\"产品运营联系人手机号\"\r\n                          prop=\"operationTelephone\"\r\n                        >\r\n                          <el-input\r\n                            v-model=\"ruleForm.operationTelephone\"\r\n                            @input=\"\r\n                              ruleForm.operationTelephone =\r\n                                ruleForm.operationTelephone.replace(\r\n                                  /[^0-9.]/g,\r\n                                  ''\r\n                                )\r\n                            \"\r\n                          ></el-input>\r\n                        </el-form-item>\r\n                        <!-- <el-table\r\n                      :data=\"shopData\"\r\n                      style=\"width: 100%; mni-height: 200px\"\r\n                    >\r\n                      <el-table-column label=\"名称\">\r\n                        <template slot-scope=\"scoped\">\r\n                          <div v-if=\"scoped.row.type == 1\">\r\n                            {{ scoped.row.name }}\r\n                          </div>\r\n                          <div v-if=\"scoped.row.type == 2\">\r\n                            <el-input\r\n                              v-model=\"scoped.row.name\"\r\n                              placeholder=\"请输入\"\r\n                            ></el-input>\r\n                          </div>\r\n                        </template>\r\n                      </el-table-column>\r\n                      <el-table-column label=\"内容\">\r\n                        <template slot-scope=\"scoped\">\r\n                          <el-input\r\n                            v-model=\"scoped.row.content\"\r\n                            placeholder=\"请输入\"\r\n                          ></el-input>\r\n                        </template>\r\n                      </el-table-column>\r\n                      <el-table-column label=\"操作\">\r\n                        <template slot-scope=\"scoped\">\r\n                          <div v-if=\"scoped.row.type == 1\">-</div>\r\n                          <div\r\n                            style=\"color: #21C9B8; cursor: pointer\"\r\n                            v-if=\"scoped.row.type == 2\"\r\n                            @click=\"delShopData(scoped.row.id)\"\r\n                          >\r\n                            删除\r\n                          </div>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                    <div\r\n                      style=\"color: #21C9B8; margin-top: 20px; cursor: pointer\"\r\n                      @click=\"addShopData\"\r\n                    >\r\n                      新增参数\r\n                    </div> -->\r\n                      </div>\r\n                    </div>\r\n                  </el-form>\r\n                  <!-- <div class=\"buttonStyle\" @click=\"addSpecification\">\r\n                    新增规格\r\n                  </div>\r\n                  <div style=\"margin-top: 20px\">\r\n                    <el-table\r\n                      :data=\"tableData\"\r\n                      style=\"width: 100%; mni-height: 200px\"\r\n                    >\r\n                      <el-table-column prop=\"date\" label=\"规格\" width=\"180\">\r\n                      </el-table-column>\r\n                      <el-table-column prop=\"name\" label=\"规格信息\" width=\"180\">\r\n                      </el-table-column>\r\n                      <el-table-column label=\"操作\">\r\n                        <template>\r\n                          <div>删除</div>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"submitStyle\">\r\n              <div v-show=\"active !== 0\" class=\"buttonStyle\" @click=\"prevStep\">\r\n                上一步\r\n              </div>\r\n              <div v-show=\"active !== 2\" class=\"buttonStyle\" @click=\"nextStep\">\r\n                下一步\r\n              </div>\r\n              <div\r\n                v-show=\"active === 2\"\r\n                class=\"buttonStyle\"\r\n                @click=\"submitData\"\r\n              >\r\n                提交\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      title=\"新增规格\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      :before-close=\"handleClose\"\r\n    >\r\n      <div>\r\n        <el-form\r\n          :model=\"ruleForm\"\r\n          :rules=\"rules\"\r\n          ref=\"ruleForm\"\r\n          label-width=\"80px\"\r\n          class=\"demo-ruleForm\"\r\n        >\r\n          <el-form-item label=\"规格\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"规格信息\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"dialogVisible = false\"\r\n          >确 定</el-button\r\n        >\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title=\"新增价格\"\r\n      :visible.sync=\"dialogpriceVisible\"\r\n      width=\"30%\"\r\n      :before-close=\"handlepriceClose\"\r\n    >\r\n      <div>\r\n        <el-form\r\n          :model=\"ruleForm\"\r\n          :rules=\"rules\"\r\n          ref=\"ruleForm\"\r\n          label-width=\"150px\"\r\n          class=\"demo-ruleForm\"\r\n        >\r\n          <el-form-item label=\"规格\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"订货编码\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"商品原价（元）\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"商品促销价（元）\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"商品分佣比例（%）\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogpriceVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"dialogpriceVisible = false\"\r\n          >确 定</el-button\r\n        >\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import { list, delOperlog, cleanOperlog } from \"@/api/system/operlog\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { appliAdd, appliEdit, appliDetail } from \"@/api/appliMarket\";\r\nimport store from \"@/store\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      active: 0,\r\n      ruleForm: {\r\n        appName: \"\",\r\n        appCategory: \"\",\r\n        applicaServer: \"\",\r\n        delivery: \"\",\r\n        briefInto: \"\",\r\n        content: \"\",\r\n        appLogo: \"\",\r\n        supply: \"\",\r\n        linkman: \"\",\r\n        phone: \"\",\r\n        serverIp: \"\",\r\n        webUrl: \"\",\r\n        webexperienceUrl: \"\",\r\n        serverexamineUrl: \"\",\r\n        developmentPeople: \"\",\r\n        spec: \"\",\r\n        userNumber: \"\",\r\n        validTime: \"\",\r\n        orderCode: \"\",\r\n        price: \"\",\r\n        promotionPrice: \"\",\r\n        commissionRatio: \"\",\r\n      },\r\n      rules: {\r\n        appName: [\r\n          { required: true, message: \"请输入应用名称\", trigger: \"blur\" },\r\n        ],\r\n        appCategory: [\r\n          { required: true, message: \"请选择应用类型\", trigger: \"change\" },\r\n        ],\r\n        applicaServer: [\r\n          { required: true, message: \"请选择应用服务端\", trigger: \"change\" },\r\n        ],\r\n        delivery: [\r\n          { required: true, message: \"请选择交付方式\", trigger: \"change\" },\r\n        ],\r\n        briefInto: [\r\n          { required: true, message: \"请输入应用简介\", trigger: \"blur\" },\r\n        ],\r\n        content: [\r\n          { required: true, message: \"请输入应用详情\", trigger: \"blur\" },\r\n        ],\r\n        appLogo: [\r\n          { required: true, message: \"请上传应用封面图片\", trigger: \"change\" },\r\n        ],\r\n        supply: [\r\n          { required: true, message: \"请输入应用提供\", trigger: \"blur\" },\r\n        ],\r\n        linkman: [{ required: true, message: \"请输入联系人\", trigger: \"blur\" }],\r\n        phone: [{ required: true, message: \"请输入联系方式\", trigger: \"blur\" }],\r\n        webUrl: [\r\n          {\r\n            required: true,\r\n            message: \"请输入网页端(Web)应用地址\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        webexperienceUrl: [\r\n          {\r\n            required: true,\r\n            message: \"请输入网页端(Web)体验地址\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        serverexamineUrl: [\r\n          {\r\n            required: true,\r\n            message: \"请输入健康检查服务端地址\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        developmentPeople: [\r\n          {\r\n            required: true,\r\n            message: \"请输入开发联系人\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n\r\n        spec: [\r\n          {\r\n            required: true,\r\n            message: \"请输入规格\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        userNumber: [\r\n          {\r\n            required: true,\r\n            message: \"请输入使用用户数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        validTime: [\r\n          {\r\n            required: true,\r\n            message: \"请选择有效时间\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n        orderCode: [\r\n          {\r\n            required: true,\r\n            message: \"请输入订货编码\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            required: true,\r\n            message: \"请输入商品原价\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        promotionPrice: [\r\n          {\r\n            required: true,\r\n            message: \"请输入商品促销价\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        consultingTelephone: [\r\n          {\r\n            required: true,\r\n            message: \"请输入服务咨询电话\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        operationTelephone: [\r\n          {\r\n            required: true,\r\n            message: \"请输入产品运营联系人手机号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        // date1: [\r\n        //   {\r\n        //     type: \"date\",\r\n        //     required: true,\r\n        //     message: \"请选择日期\",\r\n        //     trigger: \"change\",\r\n        //   },\r\n        // ],\r\n        // date2: [\r\n        //   {\r\n        //     type: \"date\",\r\n        //     required: true,\r\n        //     message: \"请选择时间\",\r\n        //     trigger: \"change\",\r\n        //   },\r\n        // ],\r\n      },\r\n      personalCardList: [],\r\n      tableData: [\r\n        {\r\n          date: \"2016-05-02\",\r\n          name: \"王小虎\",\r\n          address: \"上海市普陀区金沙江路 1518 弄\",\r\n        },\r\n        {\r\n          date: \"2016-05-04\",\r\n          name: \"王小虎\",\r\n          address: \"上海市普陀区金沙江路 1517 弄\",\r\n        },\r\n        {\r\n          date: \"2016-05-01\",\r\n          name: \"王小虎\",\r\n          address: \"上海市普陀区金沙江路 1519 弄\",\r\n        },\r\n        {\r\n          date: \"2016-05-03\",\r\n          name: \"王小虎\",\r\n          address: \"上海市普陀区金沙江路 1516 弄\",\r\n        },\r\n      ],\r\n      shopData: [\r\n        {\r\n          id: 1,\r\n          name: \"服务咨询电话\",\r\n          content: \"\",\r\n          type: 1,\r\n        },\r\n        {\r\n          id: 2,\r\n          name: \"产品运营联系人手机号\",\r\n          content: \"\",\r\n          type: 1,\r\n        },\r\n      ],\r\n      dialogVisible: false,\r\n      dialogpriceVisible: false,\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      appliTypeData: [\r\n        {\r\n          dictValue: \"1\",\r\n          dictLabel: \"研发设计\",\r\n        },\r\n        {\r\n          dictValue: \"2\",\r\n          dictLabel: \"生产制造\",\r\n        },\r\n        {\r\n          dictValue: \"3\",\r\n          dictLabel: \"运营管理\",\r\n        },\r\n        {\r\n          dictValue: \"4\",\r\n          dictLabel: \"质量管控\",\r\n        },\r\n        {\r\n          dictValue: \"5\",\r\n          dictLabel: \"仓储物流\",\r\n        },\r\n        {\r\n          dictValue: \"6\",\r\n          dictLabel: \"安全生产\",\r\n        },\r\n        {\r\n          dictValue: \"7\",\r\n          dictLabel: \"节能减排\",\r\n        },\r\n        {\r\n          dictValue: \"8\",\r\n          dictLabel: \"运维服务\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    getDetail() {\r\n      let id = this.$route.query.id;\r\n      if (id) {\r\n        let params = {\r\n          id,\r\n          userId: store.getters.userId,\r\n        };\r\n        appliDetail(params).then((res) => {\r\n          if (res.code === 200) {\r\n            this.ruleForm = res.data;\r\n            this.personalCardList = [{ url: this.ruleForm.appLogo }];\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 操作日志类型字典翻译\r\n    typeFormat(row, column) {\r\n      return this.selectDictLabel(\r\n        this.dict.type.sys_oper_type,\r\n        row.businessType\r\n      );\r\n    },\r\n    /** 详细按钮操作 */\r\n    handleView(row) {\r\n      this.open = true;\r\n      this.form = row;\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const operIds = row.operId || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除日志编号为\"' + operIds + '\"的数据项？')\r\n        .then(function () {\r\n          return delOperlog(operIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    prevStep() {\r\n      this.active--;\r\n    },\r\n    nextStep() {\r\n      if (this.active == 0) {\r\n        this.$refs.appliForm.validate((valid) => {\r\n          if (valid) {\r\n            console.log(this.active, \"执行了吗1----------\");\r\n            this.active++;\r\n            // this.$nextTick(() => {\r\n            //   this.$refs.appliForm1.clearValidate(); // 只清除清除验证\r\n            // });\r\n          } else {\r\n            console.log(\"error submit!!\");\r\n            return false;\r\n          }\r\n        });\r\n      } else if (this.active == 1) {\r\n        this.$refs.appliForm1.validate((valid) => {\r\n          if (valid) {\r\n            console.log(this.active, \"执行了吗2----------\");\r\n            this.active++;\r\n          } else {\r\n            console.log(\"error submit!!\");\r\n            return false;\r\n          }\r\n        });\r\n      }\r\n    },\r\n    addSpecification() {\r\n      this.dialogVisible = true;\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false;\r\n    },\r\n    addPrice() {\r\n      this.dialogpriceVisible = true;\r\n    },\r\n    handlepriceClose() {\r\n      this.dialogpriceVisible = false;\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePersonalCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.personalCardList = [];\r\n      this.ruleForm.appLogo = \"\";\r\n    },\r\n    handlePersonalCardSuccess(res, file, fileList) {\r\n      if (!this.personalCardList) {\r\n        this.personalCardList = [];\r\n        this.ruleForm.appLogo = \"\";\r\n      }\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.personalCardList.push(res.data);\r\n        this.ruleForm.appLogo = res.data.url;\r\n      }\r\n    },\r\n    handelExceed(file, fileList) {\r\n      this.$message.warning(\"超出图片个数限制\");\r\n    },\r\n    addShopData() {\r\n      let id = this.shopData.length + 1;\r\n      let data = {\r\n        id,\r\n        name: \"\",\r\n        content: \"\",\r\n        type: 2,\r\n      };\r\n      this.shopData.push(data);\r\n    },\r\n    delShopData(id) {\r\n      this.shopData.forEach((item, index) => {\r\n        if (item.id === id) {\r\n          this.shopData.splice(index, 1);\r\n        }\r\n      });\r\n    },\r\n    submitData() {\r\n      this.$refs.appliForm2.validate((valid) => {\r\n        if (valid) {\r\n          if (this.$route.query.id) {\r\n            appliEdit(this.ruleForm).then((res) => {\r\n              if (res.code === 200) {\r\n                this.$message.success(\"操作成功!\");\r\n                this.$router.push({\r\n                  path: \"/user/application\",\r\n                });\r\n              }\r\n            });\r\n          } else {\r\n            appliAdd(this.ruleForm).then((res) => {\r\n              if (res.code === 200) {\r\n                this.$message.success(\"操作成功!\");\r\n                this.$router.push({\r\n                  path: \"/user/application\",\r\n                });\r\n              }\r\n            });\r\n          }\r\n        } else {\r\n          console.log(\"error submit!!\");\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.container {\r\n  width: 100%;\r\n  background: #ffffff;\r\n  padding: 40px;\r\n}\r\n.content {\r\n  width: 100%;\r\n  // background: #ffffff;\r\n  .stepsStyle {\r\n    padding: 0 13%;\r\n  }\r\n  .currentContent {\r\n    margin-top: 30px;\r\n    padding-left: 5%;\r\n    padding-right: 40%;\r\n  }\r\n  .title {\r\n    font-size: 22px;\r\n    font-weight: 600;\r\n    margin-bottom: 30px;\r\n    margin-left: 20px;\r\n  }\r\n  .submitStyle {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: right;\r\n  }\r\n  .buttonStyle {\r\n    width: 100px;\r\n    padding: 10px;\r\n    background: #21c9b8;\r\n    color: #ffffff;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    border-radius: 4px;\r\n    margin-right: 20px;\r\n  }\r\n}\r\n</style>\r\n"]}]}