{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\aboutUs\\components\\dynamicInfoDetail.vue?vue&type=style&index=0&id=f34736ba&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\aboutUs\\components\\dynamicInfoDetail.vue", "mtime": 1750311962914}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["dynamicInfoDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "dynamicInfoDetail.vue", "sourceRoot": "src/views/aboutUs/components", "sourcesContent": ["<template>\r\n  <div class=\"main\" v-loading=\"loading\">\r\n    <div class=\"main_l\">\r\n      <div class=\"zhuanjia_title\">{{ detail.newsInformationName || \"\" }}</div>\r\n      <div class=\"laiyuan\">\r\n        {{ detail.newsInformationDate }} 作者：{{\r\n          detail.newsInformationAuthor ? detail.newsInformationAuthor : \"\"\r\n        }}\r\n      </div>\r\n      <div class=\"news_c\" v-html=\"detail.newsInformationContent\"></div>\r\n    </div>\r\n    <div class=\"main_r\">\r\n      <div class=\"ad\">\r\n        <div style=\"color: white\">更多新闻动态</div>\r\n      </div>\r\n      <!-- <div class=\"ad_exr\">\r\n        <ul class=\"news_exr_list2\">\r\n          <li v-for=\"(item, index) in cmsList\" :key=\"index\">\r\n            <a\r\n              :href=\"'newsInformationDetail.html?id=' + item.newsInformationId\"\r\n            >\r\n              <div class=\"news_exr_l\">\r\n                <img :src=\"item.newsInformationImg\" />\r\n              </div>\r\n              <div class=\"news_exr_r\">\r\n                <p>{{ item.newsInformationName || \"\" }}</p>\r\n                <div class=\"time\">{{ item.newsInformationDate }}</div>\r\n              </div>\r\n            </a>\r\n          </li>\r\n        </ul>\r\n      </div> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { infoDetailData, infoData } from \"@/api/home\";\r\n\r\nexport default {\r\n  name: \"news\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      detail: {},\r\n      newsList: [],\r\n      newsList2: [],\r\n      newsList3: [],\r\n      newsList4: [],\r\n      newsList5: [],\r\n      newsList6: [],\r\n      newsList7: [],\r\n      newsList8: [],\r\n      newsList9: [],\r\n      newsList10: [],\r\n      id: null,\r\n      cmsList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    getData() {\r\n      let params = {\r\n        newsInformationId: this.id,\r\n      };\r\n      infoDetailData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.detail = res.data;\r\n          this.detail.newsInformationContent = decodeURIComponent(\r\n            this.detail.newsInformationContent\r\n          );\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.main {\r\n  width: 1200px;\r\n  display: flex;\r\n  flex-flow: row wrap;\r\n  justify-content: space-between;\r\n  margin-top: 0px;\r\n  margin-right: auto;\r\n  margin-bottom: 0;\r\n  margin-left: auto;\r\n  font-size: 14px;\r\n  padding-top: 40px;\r\n  padding-right: 0px;\r\n  padding-bottom: 40px;\r\n  padding-left: 0px;\r\n}\r\n.main_l {\r\n  width: 855px;\r\n}\r\n\r\n.main_r {\r\n  width: 320px;\r\n}\r\n.zhuanjia_title {\r\n  font-size: 30px;\r\n  color: #000000;\r\n  line-height: 50px;\r\n}\r\n\r\n.news_c {\r\n  padding-top: 20px;\r\n  padding-bottom: 20px;\r\n  width: 100%;\r\n\r\n  ::v-deep .ql-align-center{\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.ad {\r\n  height: 86px;\r\n  background-size: cover;\r\n  border-radius: 4px;\r\n  line-height: 86px;\r\n  font-size: 20px;\r\n  text-align: center;\r\n  background-color: #21c9b8;\r\n  cursor: pointer;\r\n}\r\n\r\n.ad_exr {\r\n  padding-left: 20px;\r\n  padding-top: 20px;\r\n  background-color: #fff;\r\n}\r\n\r\nul.news_exr_list2 {\r\n  margin: 0px;\r\n  padding-top: 10px;\r\n  padding-right: 0px;\r\n  padding-bottom: 0px;\r\n  padding-left: 0px;\r\n}\r\n\r\nul.news_exr_list2 li {\r\n  padding: 12px;\r\n  box-shadow: 0 2px 5px #eee;\r\n  border-radius: 5px;\r\n  margin-top: 15px;\r\n  margin-right: 0px;\r\n  margin-bottom: 0px;\r\n  margin-left: 0px;\r\n  border-top-width: 1px;\r\n  border-right-width: 1px;\r\n  border-bottom-width: 0px;\r\n  border-left-width: 1px;\r\n  border-top-style: solid;\r\n  border-right-style: solid;\r\n  border-bottom-style: solid;\r\n  border-left-style: solid;\r\n  border-top-color: #eee;\r\n  border-right-color: #eee;\r\n  border-bottom-color: #eee;\r\n  border-left-color: #eee;\r\n}\r\n\r\nul.news_exr_list2 li a {\r\n  display: flex;\r\n  width: 100%;\r\n  flex-flow: row wrap;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n"]}]}