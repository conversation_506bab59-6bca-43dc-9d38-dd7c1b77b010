<template>
  <div class="scene-container">
    <div class="scene-banner">
      <img
        src="https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/1676017632639145.webp"
        alt="场景发布"
      />
    </div>
    <div v-loading="loading">
      <div class="scene-title-content">
        <div class="scene-title-box">
          <div class="scene-divider"></div>
          <div class="scene-title">场景发布</div>
          <div class="scene-divider"></div>
        </div>
        <div class="scene-search-box">
          <el-form ref="form" class="scene-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.title"
                placeholder="请输入搜索内容"
                class="scene-search-input"
              >
                <el-button
                  slot="append"
                  class="scene-search-btn"
                  @click="search"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="scene-info-content">
        <template v-if="items.length > 0">
          <div
            v-for="(item, index) in items"
            :key="index"
            class="scene-list-item"
            @click="goNoticDetail(item.id)"
          >
            <div class="list-item-content">
              <!-- <el-image class="list-item-img" fit="fill" :alt="item.title" :src="item.src" /> -->
              <div class="list-item-info">
                <div class="list-item-title">
                  {{ item.title }}
                </div>
                <div class="list-item-text">
                  {{ item.content }}
                </div>
                <div class="list-item-time">{{ item.updateTime }}</div>
              </div>
            </div>
          </div>
        </template>
        <template v-else>
          <el-empty />
        </template>
        <div class="scene-page-end">
          <el-button class="scene-page-btn" @click="goHome">首页</el-button>
          <el-pagination
            v-if="items && items.length > 0"
            background
            layout="prev, pager, next"
            class="scene-pagination"
            :page-size="pageSize"
            :current-page="pageNum"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { head, map } from "ramda";
import { getListByText } from "@/api/scene";

export default {
  name: "ScenePage",
  data() {
    return {
      loading: false,
      form: {
        title: undefined, //搜索内容
      },
      items: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  created() {
    this.search();
  },
  methods: {
    search() {
      this.loading = true;
      getListByText({
        ...this.form,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      })
        .then((res) => {
          this.loading = false;
          let { rows, total } = res || [];
          console.log("rows", rows);
          this.items = map(
            ({
              id,
              title,
              simpleContent,
              updateTime,
              coverPictureList = [],
            }) => {
              const image = head(coverPictureList || []) || {};
              return {
                id,
                title,
                content: simpleContent,
                updateTime,
                src: image.url,
              };
            },
            rows
          );
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.pageNum = 1;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.search();
    },
    // 跳转到详情页面
    goNoticDetail(id) {
      let routeData = this.$router.resolve({
        path: "/scenarioDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到首页
    goHome() {
      this.$router.push({ path: "/index" });
    },
  },
};
</script>

<style lang="scss" scoped>
.scene-container {
  width: 100%;
  background: #f4f5f9;
  .scene-banner {
    width: 100%;
    height: 540px;
    img {
      width: 100%;
      height: 540px;
      object-fit: fill;
    }
  }
  .scene-title-content {
    width: 100%;
    background-color: #fff;
    padding-bottom: 18px;
    .scene-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .scene-title {
        font-size: 40px;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .scene-divider {
        width: 48px;
        height: 4px;
        background: #21c9b8;
      }
    }
    .scene-search-box {
      .scene-search-form {
        text-align: center;
        .scene-search-input {
          width: 792px;
          height: 54px;
          .scene-search-btn {
            width: 100px;
          }
        }
      }
    }
  }
  .scene-info-content {
    width: 1200px;
    margin: 40px auto 0;
    .scene-list-item {
      width: 100%;
      background: #fff;
      border-radius: 12px;
      margin-top: 24px;
      .list-item-content {
        display: flex;
        padding: 24px 32px;
        cursor: pointer;
        .list-item-img {
          width: 298px;
          height: 212px;
          border-radius: 7px;
          overflow: hidden;
        }
        .list-item-info {
          padding-left: 24px;
          .list-item-title {
            width: 806px;
            height: 24px;
            text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
            white-space: nowrap; /*让文字不换行*/
            overflow: hidden; /*超出要隐藏*/
            font-size: 24px;
            font-weight: 500;
            color: #323233;
            line-height: 24px;
            margin: 8px 0 24px;
          }
          .list-item-text {
            width: 806px;
            height: 90px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
            text-overflow: ellipsis;
            font-size: 16px;
            color: #666;
            line-height: 30px;
          }
          .list-item-time {
            color: #999;
            line-height: 14px;
            margin-top: 52px;
          }
        }
        &:hover {
          .list-item-title {
            color: #21c9b8;
          }
        }
      }
    }
    .scene-page-end {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      padding: 24px 0 60px;
      .scene-page-btn {
        width: 82px;
        height: 32px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        color: #333;
        line-height: 10px;
      }
    }
  }
}
</style>

<style lang="scss">
.scene-container {
  .scene-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #21c9b8;
      font-size: 16px;
      color: #fff;
      line-height: 24px;
    }
  }
  .scene-search-line {
    .el-form-item__label {
      width: 88px;
      font-weight: 500;
      color: #999;
      padding-right: 32px;
      text-align: left;
    }
    .scene-search-radio {
      width: 1050px;
      margin-top: 11px;
      .el-radio-button {
        padding-bottom: 20px;
        .el-radio-button__inner {
          border: none;
          padding: 0 32px 0 0;
          background: none;
          &:hover {
            color: #21c9b8;
          }
        }
        &.is-active {
          .el-radio-button__inner {
            color: #21c9b8;
            background: none;
          }
        }
        .el-radio-button__orig-radio:checked {
          & + .el-radio-button__inner {
            box-shadow: unset;
          }
        }
      }
    }
  }
  .scene-page-end {
    .scene-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
}
</style>
