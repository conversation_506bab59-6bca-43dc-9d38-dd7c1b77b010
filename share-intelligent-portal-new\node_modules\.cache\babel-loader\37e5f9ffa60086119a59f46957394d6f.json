{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\solution\\index.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\solution\\index.js", "mtime": 1750311961340}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0U29sdXRpb25MaXN0ID0gZ2V0U29sdXRpb25MaXN0OwpleHBvcnRzLmdldFNvbHV0aW9uVHlwZUxpc3QgPSBnZXRTb2x1dGlvblR5cGVMaXN0OwpleHBvcnRzLnNvbHV0aW9uRGV0YWlsID0gc29sdXRpb25EZXRhaWw7CmV4cG9ydHMuc29sdXRpb25EaWN0cyA9IHNvbHV0aW9uRGljdHM7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDop6PlhrPmlrnmoYgt5YiX6KGoCmZ1bmN0aW9uIGdldFNvbHV0aW9uTGlzdChwYXJhbXMpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogIi9wb3J0YWx3ZWIvc29sdXRpb24vbGlzdERlc2siLAogICAgbWV0aG9kOiAiZ2V0IiwKICAgIHBhcmFtczogcGFyYW1zCiAgfSk7Cn0KLy8g6I635Y+W6Kej5Yaz5pa55qGI5LqM57qn6I+c5Y2V5YiX6KGoCmZ1bmN0aW9uIGdldFNvbHV0aW9uVHlwZUxpc3QocGFyYW1zKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICIvcG9ydGFsd2ViL3NvbHV0aW9uVHlwZS9saXN0U29sdXRpb24iLAogICAgbWV0aG9kOiAiZ2V0IiwKICAgIHBhcmFtczogcGFyYW1zCiAgfSk7Cn0KCi8vIOino+WGs+aWueahiOivpuaDhQpmdW5jdGlvbiBzb2x1dGlvbkRldGFpbChwYXJhbXMpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogIi9wb3J0YWx3ZWIvc29sdXRpb24vZGV0YWlsRGVzayIsCiAgICBtZXRob2Q6ICJnZXQiLAogICAgcGFyYW1zOiBwYXJhbXMKICB9KTsKfQovLyDojrflj5bop6PlhrPmlrnmoYhkaWN0CmZ1bmN0aW9uIHNvbHV0aW9uRGljdHMocGFyYW1zKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICJzeXN0ZW0vZGljdC9kYXRhL3R5cGUveHBfcmVxdWlyZV9jYXRlZ29yeSIsCiAgICBtZXRob2Q6ICJnZXQiLAogICAgcGFyYW1zOiBwYXJhbXMKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getSolutionList", "params", "request", "url", "method", "getSolutionTypeList", "solutionDetail", "solutionDicts"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/api/solution/index.js"], "sourcesContent": ["import request from \"@/utils/request\";\r\n\r\n\r\n// 解决方案-列表\r\nexport function getSolutionList(params) {\r\n  return request({\r\n    url: `/portalweb/solution/listDesk`,\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n// 获取解决方案二级菜单列表\r\nexport function getSolutionTypeList(params) {\r\n  return request({\r\n    url: `/portalweb/solutionType/listSolution`,\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 解决方案详情\r\nexport function solutionDetail(params) {\r\n  return request({\r\n    url: `/portalweb/solution/detailDesk`,\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n// 获取解决方案dict\r\nexport function solutionDicts(params) {\r\n  return request({\r\n    url: `system/dict/data/type/xp_require_category`,\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAGA;AACO,SAASC,eAAeA,CAACC,MAAM,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,gCAAgC;IACnCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AACA;AACO,SAASI,mBAAmBA,CAACJ,MAAM,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,wCAAwC;IAC3CC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACL,MAAM,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,kCAAkC;IACrCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AACA;AACO,SAASM,aAAaA,CAACN,MAAM,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,6CAA6C;IAChDC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}