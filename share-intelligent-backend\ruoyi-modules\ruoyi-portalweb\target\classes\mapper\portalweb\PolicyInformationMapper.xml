<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.PolicyInformationMapper">

    <resultMap type="PolicyInformation" id="PolicyInformationResult">
        <result property="policyInformationId" column="policy_information_id"/>
        <result property="policyInformationUnit" column="policy_information_unit"/>
        <result property="policyInformationType" column="policy_information_type"/>
        <result property="policyInformationLevel" column="policy_information_level"/>
        <result property="policyInformationTitle" column="policy_information_title"/>
        <result property="policyInformationIntroduction" column="policy_information_introduction"/>
        <result property="policyInformationImg" column="policy_information_img"/>
        <result property="policyInformationContent" column="policy_information_content"/>
        <result property="policyInformationFrequency" column="policy_information_frequency"/>
        <result property="policyInformationDate" column="policy_information_date"/>
        <result property="top" column="top"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectPolicyInformationVo">
        select policy_information_id, policy_information_unit, policy_information_type, policy_information_level,
        policy_information_title, policy_information_introduction, policy_information_img, policy_information_content,
        policy_information_frequency, policy_information_date, del_flag, top, create_by, create_time, update_by,
        update_time, remark from policy_information
    </sql>

    <select id="selectPolicyInformationList" parameterType="PolicyInformationVO" resultMap="PolicyInformationResult">
        <include refid="selectPolicyInformationVo"/>
        <where>
            <if test="policyInformationUnit != null ">and policy_information_unit = #{policyInformationUnit}</if>
            <if test="policyInformationType != null  and policyInformationType != ''">and policy_information_type =
                #{policyInformationType}
            </if>
            <if test="policyInformationLevel != null  and policyInformationLevel != ''">and policy_information_level =
                #{policyInformationLevel}
            </if>
            <if test="policyInformationTitle != null  and policyInformationTitle != ''">and policy_information_title like
                concat('%', #{policyInformationTitle} ,'%')
            </if>
            <if test="policyInformationIntroduction != null  and policyInformationIntroduction != ''">and
                policy_information_introduction like concat('%', #{policyInformationIntroduction} ,'%')
            </if>
            <if test="policyInformationImg != null  and policyInformationImg != ''">and policy_information_img =
                #{policyInformationImg}
            </if>
            <if test="policyInformationContent != null  and policyInformationContent != ''">and
                policy_information_content = #{policyInformationContent}
            </if>
            <if test="policyInformationFrequency != null ">and policy_information_frequency =
                #{policyInformationFrequency}
            </if>
            <if test="policyInformationDate != null ">and policy_information_date = #{policyInformationDate}</if>
            <if test="top != null  and top != ''">and top = #{top}</if>
            <if test="keyword != null  and keyword != ''">and (policy_information_title like
                concat('%', #{keyword} ,'%') or policy_information_content like concat('%', #{keyword} ,'%')  or policy_information_introduction like concat('%', #{keyword} ,'%'))
            </if>
        </where>
    </select>

    <select id="selectPolicyInformationByPolicyInformationId" parameterType="Long" resultMap="PolicyInformationResult">
        <include refid="selectPolicyInformationVo"/>
        where policy_information_id = #{policyInformationId}
    </select>

    <insert id="insertPolicyInformation" parameterType="PolicyInformation" useGeneratedKeys="true"
            keyProperty="policyInformationId">
        insert into policy_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="policyInformationUnit != null">policy_information_unit,</if>
            <if test="policyInformationType != null">policy_information_type,</if>
            <if test="policyInformationLevel != null">policy_information_level,</if>
            <if test="policyInformationTitle != null">policy_information_title,</if>
            <if test="policyInformationIntroduction != null">policy_information_introduction,</if>
            <if test="policyInformationImg != null">policy_information_img,</if>
            <if test="policyInformationContent != null">policy_information_content,</if>
            <if test="policyInformationFrequency != null">policy_information_frequency,</if>
            <if test="policyInformationDate != null">policy_information_date,</if>
            <if test="top != null">top,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="policyInformationUnit != null">#{policyInformationUnit},</if>
            <if test="policyInformationType != null">#{policyInformationType},</if>
            <if test="policyInformationLevel != null">#{policyInformationLevel},</if>
            <if test="policyInformationTitle != null">#{policyInformationTitle},</if>
            <if test="policyInformationIntroduction != null">#{policyInformationIntroduction},</if>
            <if test="policyInformationImg != null">#{policyInformationImg},</if>
            <if test="policyInformationContent != null">#{policyInformationContent},</if>
            <if test="policyInformationFrequency != null">#{policyInformationFrequency},</if>
            <if test="policyInformationDate != null">#{policyInformationDate},</if>
            <if test="top != null">#{top},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updatePolicyInformation" parameterType="PolicyInformation">
        update policy_information
        <trim prefix="SET" suffixOverrides=",">
            <if test="policyInformationUnit != null">policy_information_unit = #{policyInformationUnit},</if>
            <if test="policyInformationType != null">policy_information_type = #{policyInformationType},</if>
            <if test="policyInformationLevel != null">policy_information_level = #{policyInformationLevel},</if>
            <if test="policyInformationTitle != null">policy_information_title = #{policyInformationTitle},</if>
            <if test="policyInformationIntroduction != null">policy_information_introduction =
                #{policyInformationIntroduction},
            </if>
            <if test="policyInformationImg != null">policy_information_img = #{policyInformationImg},</if>
            <if test="policyInformationContent != null">policy_information_content = #{policyInformationContent},</if>
            <if test="policyInformationFrequency != null">policy_information_frequency =
                #{policyInformationFrequency},
            </if>
            <if test="policyInformationDate != null">policy_information_date = #{policyInformationDate},</if>
            <if test="top != null">top = #{top},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where policy_information_id = #{policyInformationId}
    </update>

    <delete id="deletePolicyInformationByPolicyInformationId" parameterType="Long">
        delete from policy_information where policy_information_id = #{policyInformationId}
    </delete>

    <delete id="deletePolicyInformationByPolicyInformationIds" parameterType="String">
        delete from policy_information where policy_information_id in
        <foreach item="policyInformationId" collection="array" open="(" separator="," close=")">
            #{policyInformationId}
        </foreach>
    </delete>
</mapper>