{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Settings\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Settings\\index.vue", "mtime": 1750311962847}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Settings", "sourcesContent": ["<template>\r\n  <el-drawer size=\"280px\" :visible=\"visible\" :with-header=\"false\" :append-to-body=\"true\" :show-close=\"false\">\r\n    <div class=\"drawer-container\">\r\n      <div>\r\n        <div class=\"setting-drawer-content\">\r\n          <div class=\"setting-drawer-title\">\r\n            <h3 class=\"drawer-title\">主题风格设置</h3>\r\n          </div>\r\n          <div class=\"setting-drawer-block-checbox\">\r\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-dark')\">\r\n              <img src=\"@/assets/images/dark.svg\" alt=\"dark\">\r\n              <div v-if=\"sideTheme === 'theme-dark'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                  <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\">\r\n                    <path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\r\n                  </svg>\r\n                </i>\r\n              </div>\r\n            </div>\r\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-light')\">\r\n              <img src=\"@/assets/images/light.svg\" alt=\"light\">\r\n              <div v-if=\"sideTheme === 'theme-light'\" class=\"setting-drawer-block-checbox-selectIcon\" style=\"display: block;\">\r\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                  <svg viewBox=\"64 64 896 896\" data-icon=\"check\" width=\"1em\" height=\"1em\" :fill=\"theme\" aria-hidden=\"true\" focusable=\"false\" class=\"\">\r\n                    <path d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\r\n                  </svg>\r\n                </i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n\r\n        <el-divider/>\r\n\r\n        <h3 class=\"drawer-title\">系统布局配置</h3>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启 TopNav</span>\r\n          <el-switch v-model=\"topNav\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启 Tags-Views</span>\r\n          <el-switch v-model=\"tagsView\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>固定 Header</span>\r\n          <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>显示 Logo</span>\r\n          <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>动态标题</span>\r\n          <el-switch v-model=\"dynamicTitle\" class=\"drawer-switch\" />\r\n        </div>\r\n\r\n        <el-divider/>\r\n\r\n        <el-button size=\"small\" type=\"primary\" plain icon=\"el-icon-document-add\" @click=\"saveSetting\">保存配置</el-button>\r\n        <el-button size=\"small\" plain icon=\"el-icon-refresh\" @click=\"resetSetting\">重置配置</el-button>\r\n      </div>\r\n    </div>\r\n  </el-drawer>\r\n</template>\r\n\r\n<script>\r\nimport ThemePicker from '@/components/ThemePicker'\r\n\r\nexport default {\r\n  components: { ThemePicker },\r\n  data() {\r\n    return {\r\n      theme: this.$store.state.settings.theme,\r\n      sideTheme: this.$store.state.settings.sideTheme\r\n    };\r\n  },\r\n  computed: {\r\n    visible: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      }\r\n    },\r\n    fixedHeader: {\r\n      get() {\r\n        return this.$store.state.settings.fixedHeader\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'fixedHeader',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'topNav',\r\n          value: val\r\n        })\r\n        if (!val) {\r\n          this.$store.dispatch('app/toggleSideBarHide', false);\r\n          this.$store.commit(\"SET_SIDEBAR_ROUTERS\", this.$store.state.permission.defaultRoutes);\r\n        }\r\n      }\r\n    },\r\n    tagsView: {\r\n      get() {\r\n        return this.$store.state.settings.tagsView\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'tagsView',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    sidebarLogo: {\r\n      get() {\r\n        return this.$store.state.settings.sidebarLogo\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'sidebarLogo',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    dynamicTitle: {\r\n      get() {\r\n        return this.$store.state.settings.dynamicTitle\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'dynamicTitle',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n  },\r\n  methods: {\r\n    themeChange(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'theme',\r\n        value: val\r\n      })\r\n      this.theme = val;\r\n    },\r\n    handleTheme(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'sideTheme',\r\n        value: val\r\n      })\r\n      this.sideTheme = val;\r\n    },\r\n    saveSetting() {\r\n      this.$modal.loading(\"正在保存到本地，请稍候...\");\r\n      this.$cache.local.set(\r\n        \"layout-setting\",\r\n        `{\r\n            \"topNav\":${this.topNav},\r\n            \"tagsView\":${this.tagsView},\r\n            \"fixedHeader\":${this.fixedHeader},\r\n            \"sidebarLogo\":${this.sidebarLogo},\r\n            \"dynamicTitle\":${this.dynamicTitle},\r\n            \"sideTheme\":\"${this.sideTheme}\",\r\n            \"theme\":\"${this.theme}\"\r\n          }`\r\n      );\r\n      setTimeout(this.$modal.closeLoading(), 1000)\r\n    },\r\n    resetSetting() {\r\n      this.$modal.loading(\"正在清除设置缓存并刷新，请稍候...\");\r\n      this.$cache.local.remove(\"layout-setting\")\r\n      setTimeout(\"window.location.reload()\", 1000)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .setting-drawer-content {\r\n    .setting-drawer-title {\r\n      margin-bottom: 12px;\r\n      color: rgba(0, 0, 0, .85);\r\n      font-size: 14px;\r\n      line-height: 22px;\r\n      font-weight: bold;\r\n    }\r\n\r\n    .setting-drawer-block-checbox {\r\n      display: flex;\r\n      justify-content: flex-start;\r\n      align-items: center;\r\n      margin-top: 10px;\r\n      margin-bottom: 20px;\r\n\r\n      .setting-drawer-block-checbox-item {\r\n        position: relative;\r\n        margin-right: 16px;\r\n        border-radius: 2px;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          width: 48px;\r\n          height: 48px;\r\n        }\r\n\r\n        .setting-drawer-block-checbox-selectIcon {\r\n          position: absolute;\r\n          top: 0;\r\n          right: 0;\r\n          width: 100%;\r\n          height: 100%;\r\n          padding-top: 15px;\r\n          padding-left: 24px;\r\n          color: #1890ff;\r\n          font-weight: 700;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .drawer-container {\r\n    padding: 20px;\r\n    font-size: 14px;\r\n    line-height: 1.5;\r\n    word-wrap: break-word;\r\n\r\n    .drawer-title {\r\n      margin-bottom: 12px;\r\n      color: rgba(0, 0, 0, .85);\r\n      font-size: 14px;\r\n      line-height: 22px;\r\n    }\r\n\r\n    .drawer-item {\r\n      color: rgba(0, 0, 0, .65);\r\n      font-size: 14px;\r\n      padding: 12px 0;\r\n    }\r\n\r\n    .drawer-switch {\r\n      float: right\r\n    }\r\n  }\r\n</style>\r\n"]}]}