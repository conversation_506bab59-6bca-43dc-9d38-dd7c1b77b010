<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysUserFriendMapper">
    <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.SysUserFriend">
        <!--@mbg.generated-->
        <!--@Table sys_user_friend-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="friend_user_name" jdbcType="VARCHAR" property="friendUserName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="friend_nick_name" jdbcType="VARCHAR" property="friendNickName"/>
        <result column="friend_avatar" jdbcType="VARCHAR" property="friendAvatar"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, user_name, friend_user_name, create_time, delete_flag, update_time, remark, friend_nick_name,
        friend_avatar
    </sql>

    <select id="selectFriendList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_user_friend
        where delete_flag = 1
          and user_name = #{userName}
        order by id desc
    </select>

    <update id="deleteFriend">
        UPDATE
                sys_user_friend
        SET delete_flag = 0,
            update_time = now()
        where  delete_flag = 1
          and user_name = #{userName}
        and friend_user_name = #{friendUseName}
    </update>

    <update id="updateFriend" parameterType="com.ruoyi.system.domain.SysUserFriend">
        update sys_user_friend
        set update_time = #{updateTime,jdbcType=TIMESTAMP},
            remark      = #{remark,jdbcType=VARCHAR}
            where delete_flag = 1
            and user_name = #{userName}
        and friend_user_name = #{friendUserName}
    </update>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.system.domain.SysUserFriend" useGeneratedKeys="true">
        insert into sys_user_friend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userName != null">
                user_name,
            </if>
            <if test="friendUserName != null">
                friend_user_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="friendNickName != null">
                friend_nick_name,
            </if>
            <if test="friendAvatar != null">
                friend_avatar,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="friendUserName != null">
                #{friendUserName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=BOOLEAN},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="friendNickName != null">
                #{friendNickName,jdbcType=VARCHAR},
            </if>
            <if test="friendAvatar != null">
                #{friendAvatar,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="selectFriendByFriendName" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_user_friend
        where delete_flag = 1
        and user_name = #{userName}
        and friend_user_name = #{friendUserName}
    </select>
</mapper>