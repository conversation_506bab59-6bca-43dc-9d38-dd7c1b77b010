package com.ruoyi.common.security.sso;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Map;

/**
 * SSO回调控制器
 * 
 * <AUTHOR>
 */
// @Controller  // 禁用此控制器，使用ruoyi-auth中的SSOClientController
@RequestMapping("/sso")
public class SSOCallbackController {
    
    private static final Logger log = LoggerFactory.getLogger(SSOCallbackController.class);
    
    @Autowired
    private SSOClient ssoClient;
    
    // SSO用户信息在Session中的键名
    private static final String SSO_USER_SESSION_KEY = "SSO_USER_INFO";
    private static final String SSO_TOKEN_SESSION_KEY = "SSO_ACCESS_TOKEN";
    
    /**
     * SSO登录回调处理
     */
    @GetMapping("/callback")
    public void callback(@RequestParam(required = false) String code,
                        @RequestParam(required = false) String state,
                        @RequestParam(required = false) String error,
                        HttpServletRequest request,
                        HttpServletResponse response) throws IOException {
        
        if (StringUtils.isNotEmpty(error)) {
            log.error("SSO登录失败: {}", error);
            response.sendRedirect("/login?error=" + error);
            return;
        }
        
        if (StringUtils.isEmpty(code)) {
            log.error("SSO回调缺少授权码");
            response.sendRedirect("/login?error=missing_code");
            return;
        }
        
        try {
            // 使用授权码换取访问令牌
            Map<String, Object> tokenResult = ssoClient.exchangeToken(code);
            if (tokenResult == null) {
                log.error("授权码换取令牌失败");
                response.sendRedirect("/login?error=token_exchange_failed");
                return;
            }
            
            String accessToken = (String) tokenResult.get("access_token");
            if (StringUtils.isEmpty(accessToken)) {
                log.error("获取访问令牌失败");
                response.sendRedirect("/login?error=invalid_token");
                return;
            }
            
            // 获取用户信息
            Map<String, Object> userInfo = ssoClient.getUserInfo(accessToken);
            if (userInfo == null) {
                log.error("获取用户信息失败");
                response.sendRedirect("/login?error=userinfo_failed");
                return;
            }
            
            // 创建SSO用户信息对象
            SSOUserInfo ssoUserInfo = new SSOUserInfo(userInfo);
            ssoUserInfo.setAccessToken(accessToken);
            
            // 计算令牌过期时间
            Integer expiresIn = (Integer) tokenResult.get("expires_in");
            if (expiresIn != null) {
                ssoUserInfo.setTokenExpireTime(System.currentTimeMillis() + expiresIn * 1000L);
            }
            
            // 存储到Session
            HttpSession session = request.getSession();
            session.setAttribute(SSO_USER_SESSION_KEY, ssoUserInfo);
            session.setAttribute(SSO_TOKEN_SESSION_KEY, accessToken);
            
            log.info("SSO登录成功: {}", ssoUserInfo.getUsername());
            
            // 重定向到原始请求页面或首页
            String redirectUrl = StringUtils.isNotEmpty(state) ? state : "/index";
            response.sendRedirect(redirectUrl);
            
        } catch (Exception e) {
            log.error("SSO回调处理异常", e);
            response.sendRedirect("/login?error=callback_error");
        }
    }
    
    /**
     * SSO登录入口
     */
    @GetMapping("/login")
    public void login(@RequestParam(required = false) String redirect,
                     HttpServletResponse response) throws IOException {
        
        String loginUrl = ssoClient.getLoginUrl(redirect);
        response.sendRedirect(loginUrl);
    }
    
    /**
     * SSO登出
     */
    @PostMapping("/logout")
    @ResponseBody
    public R<String> logout(HttpServletRequest request) {
        
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                String accessToken = (String) session.getAttribute(SSO_TOKEN_SESSION_KEY);
                
                // 调用SSO服务登出
                if (StringUtils.isNotEmpty(accessToken)) {
                    ssoClient.logout(accessToken);
                }
                
                // 清除本地Session
                session.removeAttribute(SSO_USER_SESSION_KEY);
                session.removeAttribute(SSO_TOKEN_SESSION_KEY);
                session.invalidate();
            }
            
            log.info("SSO登出成功");
            return R.ok("登出成功");
            
        } catch (Exception e) {
            log.error("SSO登出异常", e);
            return R.fail("登出失败");
        }
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/userinfo")
    @ResponseBody
    public R<SSOUserInfo> getUserInfo(HttpServletRequest request) {
        
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                SSOUserInfo userInfo = (SSOUserInfo) session.getAttribute(SSO_USER_SESSION_KEY);
                if (userInfo != null) {
                    // 不返回敏感信息
                    SSOUserInfo safeUserInfo = new SSOUserInfo();
                    safeUserInfo.setUserId(userInfo.getUserId());
                    safeUserInfo.setUsername(userInfo.getUsername());
                    safeUserInfo.setClientId(userInfo.getClientId());
                    safeUserInfo.setLoginTime(userInfo.getLoginTime());
                    safeUserInfo.setRoles(userInfo.getRoles());
                    safeUserInfo.setPermissions(userInfo.getPermissions());
                    safeUserInfo.setHasPermission(userInfo.getHasPermission());
                    
                    return R.ok(safeUserInfo);
                }
            }
            
            return R.fail("用户未登录");
            
        } catch (Exception e) {
            log.error("获取用户信息异常", e);
            return R.fail("获取用户信息失败");
        }
    }
    
    /**
     * 检查SSO服务状态
     */
    @GetMapping("/status")
    @ResponseBody
    public R<String> checkStatus() {
        
        try {
            boolean status = ssoClient.checkSSOStatus();
            if (status) {
                return R.ok("SSO服务正常");
            } else {
                return R.fail("SSO服务不可用");
            }
            
        } catch (Exception e) {
            log.error("检查SSO服务状态异常", e);
            return R.fail("检查SSO服务状态失败");
        }
    }
    
    /**
     * 刷新用户信息
     */
    @PostMapping("/refresh")
    @ResponseBody
    public R<SSOUserInfo> refreshUserInfo(HttpServletRequest request) {
        
        try {
            HttpSession session = request.getSession(false);
            if (session != null) {
                String accessToken = (String) session.getAttribute(SSO_TOKEN_SESSION_KEY);
                
                if (StringUtils.isNotEmpty(accessToken)) {
                    // 重新获取用户信息
                    Map<String, Object> userInfo = ssoClient.getUserInfo(accessToken);
                    if (userInfo != null) {
                        SSOUserInfo ssoUserInfo = new SSOUserInfo(userInfo);
                        ssoUserInfo.setAccessToken(accessToken);
                        
                        // 更新Session
                        session.setAttribute(SSO_USER_SESSION_KEY, ssoUserInfo);
                        
                        log.info("刷新用户信息成功: {}", ssoUserInfo.getUsername());
                        
                        // 返回安全的用户信息
                        SSOUserInfo safeUserInfo = new SSOUserInfo();
                        safeUserInfo.setUserId(ssoUserInfo.getUserId());
                        safeUserInfo.setUsername(ssoUserInfo.getUsername());
                        safeUserInfo.setClientId(ssoUserInfo.getClientId());
                        safeUserInfo.setLoginTime(ssoUserInfo.getLoginTime());
                        safeUserInfo.setRoles(ssoUserInfo.getRoles());
                        safeUserInfo.setPermissions(ssoUserInfo.getPermissions());
                        safeUserInfo.setHasPermission(ssoUserInfo.getHasPermission());
                        
                        return R.ok(safeUserInfo);
                    }
                }
            }
            
            return R.fail("刷新用户信息失败");
            
        } catch (Exception e) {
            log.error("刷新用户信息异常", e);
            return R.fail("刷新用户信息失败");
        }
    }
}
