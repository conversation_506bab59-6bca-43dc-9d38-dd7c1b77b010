<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-02-20 13:53:26
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24">
        <div class="company-info-container">
          <div class="enterprise-detail-content">
            <div class="enterprise-title">{{ data.name }}</div>
            <div class="enterprise-title-carousel">
              <el-carousel
                class="carousel-content"
                arrow="always"
                @change="carouselChange"
                :interval="5000"
              >
                <el-carousel-item
                  v-for="(item, index) in form.companyPictureList"
                  :key="index"
                  class="carousel-item-content"
                >
                  <div class="carousel-item-box">
                    <img :src="item.url" alt="" />
                  </div>
                </el-carousel-item>
              </el-carousel>

              <div class="action-container">
                <el-upload
                  multiple
                  :limit="5"
                  :show-file-list="false"
                  :headers="headers"
                  :action="uploadUrl"
                  :file-list="form.companyPictureList"
                  :accept="accept"
                  :before-upload="handleBeforeUpload"
                  :on-preview="handlePictureCardPreview"
                  :on-success="handleSuccess"
                >
                  <img
                    class="button-icon"
                    src="@/assets/user/company_upload.png"
                    alt=""
                  />
                </el-upload>
                <a @click="deleteCompanyPhoto">
                  <img
                    class="button-icon ml_30"
                    src="@/assets/user/company_delete.png"
                    alt=""
                  />
                </a>
              </div>
            </div>

            <div class="enterprise-title-tag">
              <div
                v-for="(item, index) in form.industrialChainValueList"
                :key="index"
                class="title-tag-item"
              >
                {{ item }}
              </div>
            </div>
            <div class="form-class"></div>
            <el-form
              ref="form"
              :model="form"
              :rules="rules"
              label-width="120px"
            >
              <el-form-item label="所属行业：" prop="industry">
                <el-select
                  v-model="form.industry"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="dict in dict.type.company_industry"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="所在地区：" prop="contactsName">
                    <el-cascader
                      v-model="locationRess"
                      class="user-mgt-edit-select"
                      :options="sourceAddress"
                      :props="{
                        label: 'name',
                        value: 'code',
                        checkStrictly: true,
                      }"
                      @change="handleAddress"
                    >
                    </el-cascader>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="24">
                  <el-form-item label="详细地址：" prop="detailAddress">
                    <el-input
                      v-model="form.detailAddress"
                      placeholder="请输入详细地址"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="主营业务：" prop="mainBusiness">
                    <el-input
                      v-model="form.mainBusiness"
                      type="textarea"
                      :maxlength="500"
                      rows="5"
                      show-word-limit
                      placeholder="请输入主营业务"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="企业介绍：" prop="introduce">
                    <el-input
                      v-model="form.introduce"
                      type="textarea"
                      :maxlength="500"
                      rows="5"
                      show-word-limit
                      placeholder="请输入企业介绍"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="上传文件：" prop="companyMaterialList">
                <el-upload
                  :file-list="form.companyMaterialList"
                  :headers="headers"
                  :action="uploadUrl"
                  accept=".pdf, .docx, .xls"
                  :on-remove="handleompanyFileRemove"
                  :on-success="handlecompanyFileSuccess"
                  :limit="10"
                >
                  <el-button
                    class="apathy-upload-btn"
                    size="small"
                    icon="el-icon-upload2"
                    >上传文件
                  </el-button>
                  <span slot="tip" class="el-upload__tip">
                    仅限doc、pdf、xls格式，且不超过10M
                  </span>
                </el-upload>
              </el-form-item>
            </el-form>
            <div class="button-container">
              <el-button type="danger" @click="submitForm()">保存</el-button>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import UserMenu from "../components/userMenu.vue";
import {
  getCompanyDetailByBussinessNo,
  editCompany,
} from "@/api/system/company";
import store from "@/store";
import { address4 } from "@/assets/address4";
import { get_address_label4, get_address_values4 } from "@/utils/index";
import { getToken } from "@/utils/auth";

export default {
  name: "CompanyInfo",
  dicts: ["company_industry"],

  data() {
    return {
      data: {},
      locationRess: "",
      carouselIndex: 0,
      user: store.getters.user,
      headers: { Authorization: "Bearer " + getToken() },
      sourceAddress: address4, // 省市区街道源
      form: {
        companyPictureList: [],
      },
      rules: {},
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/upload", //上传地址
      accept: ".jpg, .jpeg, .png, .bmp",
    };
  },
  components: { UserMenu },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      getCompanyDetailByBussinessNo({
        bussinessNo: this.user.bussinessNo,
      }).then((res) => {
        if (res.code == 200) {
          this.form = res.data;
          //初始化地区联动
          let province = res.data.province;
          let city = res.data.city;
          let region = res.data.region;
          let values = get_address_values4([province, city, region]);
          this.locationRess = values;
        }
      });
    },
    submitForm() {
      editCompany({
        ...this.form,
      }).then((res) => {
        if (res.code == 200) {
          this.$modal.msgSuccess("保存成功");
        }
      });
    },
    // 产品照片上传之前的钩子
    handleBeforeUpload(file) {
      let { name, type, size } = file;
      let typeList = this.accept
        .split(",")
        .map((item) => item.trim().toLowerCase().substr(1));
      let dotIndex = name.lastIndexOf(".");
      // 文件类型校验
      if (dotIndex === -1) {
        this.$message.error("请上传正确格式的文件");
        return false;
      } else {
        let suffix = name.substring(dotIndex + 1);
        if (typeList.indexOf(suffix.toLowerCase()) === -1) {
          this.$message.error("请上传正确格式的文件");
          return false;
        }
      }
      // 文件上传大小限制
      if (size > 1048576 * 20) {
        this.$message.error("文件大小不能超过20M！");
        return false;
      }
    },
    // 点击产品照片
    handlePictureCardPreview(file) {
      this.imageUrl = file.url;
      this.imgVisible = true;
    },
    handleSuccess(response, file, fileList) {
      if (!this.form.companyPictureList) {
        this.form.companyPictureList = [];
      }
      this.form.companyPictureList.push(response.data);
      console.log(this.form.companyPictureList);
    },
    carouselChange(now, prev) {
      this.carouselIndex = now;
    },
    // 删除产品照片
    deleteCompanyPhoto() {
      this.form.companyPictureList.splice(this.carouselIndex, 1);
    },
    handlecompanyFileSuccess(res, file, fileList) {
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        if (!this.form.companyMaterialList) {
          this.form.companyMaterialList = [];
        }
        this.form.companyMaterialList.push(res.data);
      }
    },
    handleompanyFileRemove(file, fileList) {
      this.companyForm.companyCardList = res.data;
    },
    /* 选择省市区街道*/
    handleAddress(val) {
      let data = get_address_label4(val);
      this.form.province = data[0];
      this.form.provinceCode = val[0];
      this.form.city = data[1];
      this.form.cityCode = val[1];
      this.form.region = data[2];
      this.form.regionCode = val[2];
    },
  },
};
</script>
<style lang="scss">
.app-container {
  background: #f4f5f9;
  .company-info-container {
    .enterprise-detail-content {
      width: 1200px;
      margin: 0 auto;
      padding: 60px 116px 58px;
      background: #fff;
      .el-cascader--medium {
        font-size: 14px;
        width: 100%;
        line-height: 36px;
      }
      .enterprise-title {
        font-size: 32px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333;
        line-height: 32px;
        text-align: center;
        padding-bottom: 44px;
      }
      .enterprise-title-carousel {
        width: 720px;
        height: 360px;
        position: relative;
        margin: 0 auto;
        .action-container {
          position: absolute;
          right: 80px;
          bottom: 10px;
          display: flex;
          .button-icon {
            width: 44px;
            height: 44px;
          }
          .ml_30 {
            margin-left: 20px;
          }
        }
        .carousel-content {
          width: 100%;
          height: 360px;
          .carousel-item-content {
            width: 100%;
            height: 100%;
            .carousel-item-box {
              margin: 0 auto;
              width: 600px;
              height: 100%;
              img {
                width: 100%;
                height: 100%;
              }
            }
          }
        }
        .carousel-default-img {
          width: 600px;
          height: 100%;
          margin: 0 auto;
          display: block;
        }
      }
      .enterprise-title-tag {
        display: flex;
        flex-wrap: wrap;
        width: 600px;
        margin: 0 auto 21px;
        .title-tag-item {
          background: rgba(197, 37, 33, 0.1);
          border-radius: 4px;
          padding: 6px 12px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #21c9b8;
          line-height: 12px;
          margin: 16px 16px 0 0;
        }
      }

      .enterprise-introduction-box {
        width: 960px;
        padding-top: 61px;
        .enterprise-introduction-info {
          padding-top: 40px;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #333;
          line-height: 28px;
        }
      }
      .enterprise-introduction-title {
        display: flex;
        align-items: center;
        .introduction-line {
          width: 4px;
          height: 20px;
          background: #21c9b8;
        }
        .introduction-title {
          font-size: 24px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333;
          line-height: 24px;
          padding-left: 8px;
        }
      }
    }
    .el-select {
      display: inline-block;
      position: relative;
      width: 100%;
    }
    .form-class {
      .el-input--medium .el-input__inner {
        height: 36px;
        width: 400px;
        line-height: 36px;
      }
    }
    .button-container {
      width: 100%;
      margin-top: 50px;
      text-align: center;
      .el-button--danger {
        background: #21c9b8;
        color: #fff;
        border-color: #21c9b8;
      }
    }
  }
}
</style>
