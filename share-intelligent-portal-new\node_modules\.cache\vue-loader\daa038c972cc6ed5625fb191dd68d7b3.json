{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyInfo\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyInfo\\index.vue", "mtime": 1750311963052}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsNCiAgZ2V0Q29tcGFueURldGFpbEJ5QnVzc2luZXNzTm8sDQogIGVkaXRDb21wYW55LA0KfSBmcm9tICJAL2FwaS9zeXN0ZW0vY29tcGFueSI7DQppbXBvcnQgc3RvcmUgZnJvbSAiQC9zdG9yZSI7DQppbXBvcnQgeyBhZGRyZXNzNCB9IGZyb20gIkAvYXNzZXRzL2FkZHJlc3M0IjsNCmltcG9ydCB7IGdldF9hZGRyZXNzX2xhYmVsNCwgZ2V0X2FkZHJlc3NfdmFsdWVzNCB9IGZyb20gIkAvdXRpbHMvaW5kZXgiOw0KaW1wb3J0IHsgZ2V0VG9rZW4gfSBmcm9tICJAL3V0aWxzL2F1dGgiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJDb21wYW55SW5mbyIsDQogIGRpY3RzOiBbImNvbXBhbnlfaW5kdXN0cnkiXSwNCg0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBkYXRhOiB7fSwNCiAgICAgIGxvY2F0aW9uUmVzczogIiIsDQogICAgICBjYXJvdXNlbEluZGV4OiAwLA0KICAgICAgdXNlcjogc3RvcmUuZ2V0dGVycy51c2VyLA0KICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpIH0sDQogICAgICBzb3VyY2VBZGRyZXNzOiBhZGRyZXNzNCwgLy8g55yB5biC5Yy66KGX6YGT5rqQDQogICAgICBmb3JtOiB7DQogICAgICAgIGNvbXBhbnlQaWN0dXJlTGlzdDogW10sDQogICAgICB9LA0KICAgICAgcnVsZXM6IHt9LA0KICAgICAgdXBsb2FkVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9maWxlL3VwbG9hZCIsIC8v5LiK5Lyg5Zyw5Z2ADQogICAgICBhY2NlcHQ6ICIuanBnLCAuanBlZywgLnBuZywgLmJtcCIsDQogICAgfTsNCiAgfSwNCiAgY29tcG9uZW50czogeyBVc2VyTWVudSB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0RGV0YWlsKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXREZXRhaWwoKSB7DQogICAgICBnZXRDb21wYW55RGV0YWlsQnlCdXNzaW5lc3NObyh7DQogICAgICAgIGJ1c3NpbmVzc05vOiB0aGlzLnVzZXIuYnVzc2luZXNzTm8sDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZm9ybSA9IHJlcy5kYXRhOw0KICAgICAgICAgIC8v5Yid5aeL5YyW5Zyw5Yy66IGU5YqoDQogICAgICAgICAgbGV0IHByb3ZpbmNlID0gcmVzLmRhdGEucHJvdmluY2U7DQogICAgICAgICAgbGV0IGNpdHkgPSByZXMuZGF0YS5jaXR5Ow0KICAgICAgICAgIGxldCByZWdpb24gPSByZXMuZGF0YS5yZWdpb247DQogICAgICAgICAgbGV0IHZhbHVlcyA9IGdldF9hZGRyZXNzX3ZhbHVlczQoW3Byb3ZpbmNlLCBjaXR5LCByZWdpb25dKTsNCiAgICAgICAgICB0aGlzLmxvY2F0aW9uUmVzcyA9IHZhbHVlczsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgZWRpdENvbXBhbnkoew0KICAgICAgICAuLi50aGlzLmZvcm0sDQogICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/neWtmOaIkOWKnyIpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOS6p+WTgeeFp+eJh+S4iuS8oOS5i+WJjeeahOmSqeWtkA0KICAgIGhhbmRsZUJlZm9yZVVwbG9hZChmaWxlKSB7DQogICAgICBsZXQgeyBuYW1lLCB0eXBlLCBzaXplIH0gPSBmaWxlOw0KICAgICAgbGV0IHR5cGVMaXN0ID0gdGhpcy5hY2NlcHQNCiAgICAgICAgLnNwbGl0KCIsIikNCiAgICAgICAgLm1hcCgoaXRlbSkgPT4gaXRlbS50cmltKCkudG9Mb3dlckNhc2UoKS5zdWJzdHIoMSkpOw0KICAgICAgbGV0IGRvdEluZGV4ID0gbmFtZS5sYXN0SW5kZXhPZigiLiIpOw0KICAgICAgLy8g5paH5Lu257G75Z6L5qCh6aqMDQogICAgICBpZiAoZG90SW5kZXggPT09IC0xKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivt+S4iuS8oOato+ehruagvOW8j+eahOaWh+S7tiIpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgc3VmZml4ID0gbmFtZS5zdWJzdHJpbmcoZG90SW5kZXggKyAxKTsNCiAgICAgICAgaWYgKHR5cGVMaXN0LmluZGV4T2Yoc3VmZml4LnRvTG93ZXJDYXNlKCkpID09PSAtMSkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivt+S4iuS8oOato+ehruagvOW8j+eahOaWh+S7tiIpOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLy8g5paH5Lu25LiK5Lyg5aSn5bCP6ZmQ5Yi2DQogICAgICBpZiAoc2l6ZSA+IDEwNDg1NzYgKiAyMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmlofku7blpKflsI/kuI3og73otoXov4cyME3vvIEiKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g54K55Ye75Lqn5ZOB54Wn54mHDQogICAgaGFuZGxlUGljdHVyZUNhcmRQcmV2aWV3KGZpbGUpIHsNCiAgICAgIHRoaXMuaW1hZ2VVcmwgPSBmaWxlLnVybDsNCiAgICAgIHRoaXMuaW1nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICBoYW5kbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgaWYgKCF0aGlzLmZvcm0uY29tcGFueVBpY3R1cmVMaXN0KSB7DQogICAgICAgIHRoaXMuZm9ybS5jb21wYW55UGljdHVyZUxpc3QgPSBbXTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybS5jb21wYW55UGljdHVyZUxpc3QucHVzaChyZXNwb25zZS5kYXRhKTsNCiAgICAgIGNvbnNvbGUubG9nKHRoaXMuZm9ybS5jb21wYW55UGljdHVyZUxpc3QpOw0KICAgIH0sDQogICAgY2Fyb3VzZWxDaGFuZ2Uobm93LCBwcmV2KSB7DQogICAgICB0aGlzLmNhcm91c2VsSW5kZXggPSBub3c7DQogICAgfSwNCiAgICAvLyDliKDpmaTkuqflk4HnhafniYcNCiAgICBkZWxldGVDb21wYW55UGhvdG8oKSB7DQogICAgICB0aGlzLmZvcm0uY29tcGFueVBpY3R1cmVMaXN0LnNwbGljZSh0aGlzLmNhcm91c2VsSW5kZXgsIDEpOw0KICAgIH0sDQogICAgaGFuZGxlY29tcGFueUZpbGVTdWNjZXNzKHJlcywgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIC8v5q2k5aSE5YaZ5LiK5Lygb3Nz5oiQ5Yqf5LmL5ZCO55qE6YC76L6RDQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIGlmICghdGhpcy5mb3JtLmNvbXBhbnlNYXRlcmlhbExpc3QpIHsNCiAgICAgICAgICB0aGlzLmZvcm0uY29tcGFueU1hdGVyaWFsTGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICAgIHRoaXMuZm9ybS5jb21wYW55TWF0ZXJpYWxMaXN0LnB1c2gocmVzLmRhdGEpOw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlb21wYW55RmlsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5jb21wYW55Rm9ybS5jb21wYW55Q2FyZExpc3QgPSByZXMuZGF0YTsNCiAgICB9LA0KICAgIC8qIOmAieaLqeecgeW4guWMuuihl+mBkyovDQogICAgaGFuZGxlQWRkcmVzcyh2YWwpIHsNCiAgICAgIGxldCBkYXRhID0gZ2V0X2FkZHJlc3NfbGFiZWw0KHZhbCk7DQogICAgICB0aGlzLmZvcm0ucHJvdmluY2UgPSBkYXRhWzBdOw0KICAgICAgdGhpcy5mb3JtLnByb3ZpbmNlQ29kZSA9IHZhbFswXTsNCiAgICAgIHRoaXMuZm9ybS5jaXR5ID0gZGF0YVsxXTsNCiAgICAgIHRoaXMuZm9ybS5jaXR5Q29kZSA9IHZhbFsxXTsNCiAgICAgIHRoaXMuZm9ybS5yZWdpb24gPSBkYXRhWzJdOw0KICAgICAgdGhpcy5mb3JtLnJlZ2lvbkNvZGUgPSB2YWxbMl07DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/companyInfo", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-20 13:53:26\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"company-info-container\">\r\n          <div class=\"enterprise-detail-content\">\r\n            <div class=\"enterprise-title\">{{ data.name }}</div>\r\n            <div class=\"enterprise-title-carousel\">\r\n              <el-carousel\r\n                class=\"carousel-content\"\r\n                arrow=\"always\"\r\n                @change=\"carouselChange\"\r\n                :interval=\"5000\"\r\n              >\r\n                <el-carousel-item\r\n                  v-for=\"(item, index) in form.companyPictureList\"\r\n                  :key=\"index\"\r\n                  class=\"carousel-item-content\"\r\n                >\r\n                  <div class=\"carousel-item-box\">\r\n                    <img :src=\"item.url\" alt=\"\" />\r\n                  </div>\r\n                </el-carousel-item>\r\n              </el-carousel>\r\n\r\n              <div class=\"action-container\">\r\n                <el-upload\r\n                  multiple\r\n                  :limit=\"5\"\r\n                  :show-file-list=\"false\"\r\n                  :headers=\"headers\"\r\n                  :action=\"uploadUrl\"\r\n                  :file-list=\"form.companyPictureList\"\r\n                  :accept=\"accept\"\r\n                  :before-upload=\"handleBeforeUpload\"\r\n                  :on-preview=\"handlePictureCardPreview\"\r\n                  :on-success=\"handleSuccess\"\r\n                >\r\n                  <img\r\n                    class=\"button-icon\"\r\n                    src=\"@/assets/user/company_upload.png\"\r\n                    alt=\"\"\r\n                  />\r\n                </el-upload>\r\n                <a @click=\"deleteCompanyPhoto\">\r\n                  <img\r\n                    class=\"button-icon ml_30\"\r\n                    src=\"@/assets/user/company_delete.png\"\r\n                    alt=\"\"\r\n                  />\r\n                </a>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"enterprise-title-tag\">\r\n              <div\r\n                v-for=\"(item, index) in form.industrialChainValueList\"\r\n                :key=\"index\"\r\n                class=\"title-tag-item\"\r\n              >\r\n                {{ item }}\r\n              </div>\r\n            </div>\r\n            <div class=\"form-class\"></div>\r\n            <el-form\r\n              ref=\"form\"\r\n              :model=\"form\"\r\n              :rules=\"rules\"\r\n              label-width=\"120px\"\r\n            >\r\n              <el-form-item label=\"所属行业：\" prop=\"industry\">\r\n                <el-select\r\n                  v-model=\"form.industry\"\r\n                  placeholder=\"请选择\"\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.company_industry\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"所在地区：\" prop=\"contactsName\">\r\n                    <el-cascader\r\n                      v-model=\"locationRess\"\r\n                      class=\"user-mgt-edit-select\"\r\n                      :options=\"sourceAddress\"\r\n                      :props=\"{\r\n                        label: 'name',\r\n                        value: 'code',\r\n                        checkStrictly: true,\r\n                      }\"\r\n                      @change=\"handleAddress\"\r\n                    >\r\n                    </el-cascader>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"详细地址：\" prop=\"detailAddress\">\r\n                    <el-input\r\n                      v-model=\"form.detailAddress\"\r\n                      placeholder=\"请输入详细地址\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"主营业务：\" prop=\"mainBusiness\">\r\n                    <el-input\r\n                      v-model=\"form.mainBusiness\"\r\n                      type=\"textarea\"\r\n                      :maxlength=\"500\"\r\n                      rows=\"5\"\r\n                      show-word-limit\r\n                      placeholder=\"请输入主营业务\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"企业介绍：\" prop=\"introduce\">\r\n                    <el-input\r\n                      v-model=\"form.introduce\"\r\n                      type=\"textarea\"\r\n                      :maxlength=\"500\"\r\n                      rows=\"5\"\r\n                      show-word-limit\r\n                      placeholder=\"请输入企业介绍\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-form-item label=\"上传文件：\" prop=\"companyMaterialList\">\r\n                <el-upload\r\n                  :file-list=\"form.companyMaterialList\"\r\n                  :headers=\"headers\"\r\n                  :action=\"uploadUrl\"\r\n                  accept=\".pdf, .docx, .xls\"\r\n                  :on-remove=\"handleompanyFileRemove\"\r\n                  :on-success=\"handlecompanyFileSuccess\"\r\n                  :limit=\"10\"\r\n                >\r\n                  <el-button\r\n                    class=\"apathy-upload-btn\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-upload2\"\r\n                    >上传文件\r\n                  </el-button>\r\n                  <span slot=\"tip\" class=\"el-upload__tip\">\r\n                    仅限doc、pdf、xls格式，且不超过10M\r\n                  </span>\r\n                </el-upload>\r\n              </el-form-item>\r\n            </el-form>\r\n            <div class=\"button-container\">\r\n              <el-button type=\"danger\" @click=\"submitForm()\">保存</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport {\r\n  getCompanyDetailByBussinessNo,\r\n  editCompany,\r\n} from \"@/api/system/company\";\r\nimport store from \"@/store\";\r\nimport { address4 } from \"@/assets/address4\";\r\nimport { get_address_label4, get_address_values4 } from \"@/utils/index\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"CompanyInfo\",\r\n  dicts: [\"company_industry\"],\r\n\r\n  data() {\r\n    return {\r\n      data: {},\r\n      locationRess: \"\",\r\n      carouselIndex: 0,\r\n      user: store.getters.user,\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      sourceAddress: address4, // 省市区街道源\r\n      form: {\r\n        companyPictureList: [],\r\n      },\r\n      rules: {},\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n    };\r\n  },\r\n  components: { UserMenu },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    getDetail() {\r\n      getCompanyDetailByBussinessNo({\r\n        bussinessNo: this.user.bussinessNo,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.form = res.data;\r\n          //初始化地区联动\r\n          let province = res.data.province;\r\n          let city = res.data.city;\r\n          let region = res.data.region;\r\n          let values = get_address_values4([province, city, region]);\r\n          this.locationRess = values;\r\n        }\r\n      });\r\n    },\r\n    submitForm() {\r\n      editCompany({\r\n        ...this.form,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.$modal.msgSuccess(\"保存成功\");\r\n        }\r\n      });\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePictureCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    handleSuccess(response, file, fileList) {\r\n      if (!this.form.companyPictureList) {\r\n        this.form.companyPictureList = [];\r\n      }\r\n      this.form.companyPictureList.push(response.data);\r\n      console.log(this.form.companyPictureList);\r\n    },\r\n    carouselChange(now, prev) {\r\n      this.carouselIndex = now;\r\n    },\r\n    // 删除产品照片\r\n    deleteCompanyPhoto() {\r\n      this.form.companyPictureList.splice(this.carouselIndex, 1);\r\n    },\r\n    handlecompanyFileSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        if (!this.form.companyMaterialList) {\r\n          this.form.companyMaterialList = [];\r\n        }\r\n        this.form.companyMaterialList.push(res.data);\r\n      }\r\n    },\r\n    handleompanyFileRemove(file, fileList) {\r\n      this.companyForm.companyCardList = res.data;\r\n    },\r\n    /* 选择省市区街道*/\r\n    handleAddress(val) {\r\n      let data = get_address_label4(val);\r\n      this.form.province = data[0];\r\n      this.form.provinceCode = val[0];\r\n      this.form.city = data[1];\r\n      this.form.cityCode = val[1];\r\n      this.form.region = data[2];\r\n      this.form.regionCode = val[2];\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .company-info-container {\r\n    .enterprise-detail-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      padding: 60px 116px 58px;\r\n      background: #fff;\r\n      .el-cascader--medium {\r\n        font-size: 14px;\r\n        width: 100%;\r\n        line-height: 36px;\r\n      }\r\n      .enterprise-title {\r\n        font-size: 32px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        text-align: center;\r\n        padding-bottom: 44px;\r\n      }\r\n      .enterprise-title-carousel {\r\n        width: 720px;\r\n        height: 360px;\r\n        position: relative;\r\n        margin: 0 auto;\r\n        .action-container {\r\n          position: absolute;\r\n          right: 80px;\r\n          bottom: 10px;\r\n          display: flex;\r\n          .button-icon {\r\n            width: 44px;\r\n            height: 44px;\r\n          }\r\n          .ml_30 {\r\n            margin-left: 20px;\r\n          }\r\n        }\r\n        .carousel-content {\r\n          width: 100%;\r\n          height: 360px;\r\n          .carousel-item-content {\r\n            width: 100%;\r\n            height: 100%;\r\n            .carousel-item-box {\r\n              margin: 0 auto;\r\n              width: 600px;\r\n              height: 100%;\r\n              img {\r\n                width: 100%;\r\n                height: 100%;\r\n              }\r\n            }\r\n          }\r\n        }\r\n        .carousel-default-img {\r\n          width: 600px;\r\n          height: 100%;\r\n          margin: 0 auto;\r\n          display: block;\r\n        }\r\n      }\r\n      .enterprise-title-tag {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        width: 600px;\r\n        margin: 0 auto 21px;\r\n        .title-tag-item {\r\n          background: rgba(197, 37, 33, 0.1);\r\n          border-radius: 4px;\r\n          padding: 6px 12px;\r\n          font-size: 12px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #21c9b8;\r\n          line-height: 12px;\r\n          margin: 16px 16px 0 0;\r\n        }\r\n      }\r\n\r\n      .enterprise-introduction-box {\r\n        width: 960px;\r\n        padding-top: 61px;\r\n        .enterprise-introduction-info {\r\n          padding-top: 40px;\r\n          font-size: 16px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 28px;\r\n        }\r\n      }\r\n      .enterprise-introduction-title {\r\n        display: flex;\r\n        align-items: center;\r\n        .introduction-line {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .introduction-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n    }\r\n    .el-select {\r\n      display: inline-block;\r\n      position: relative;\r\n      width: 100%;\r\n    }\r\n    .form-class {\r\n      .el-input--medium .el-input__inner {\r\n        height: 36px;\r\n        width: 400px;\r\n        line-height: 36px;\r\n      }\r\n    }\r\n    .button-container {\r\n      width: 100%;\r\n      margin-top: 50px;\r\n      text-align: center;\r\n      .el-button--danger {\r\n        background: #21c9b8;\r\n        color: #fff;\r\n        border-color: #21c9b8;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}