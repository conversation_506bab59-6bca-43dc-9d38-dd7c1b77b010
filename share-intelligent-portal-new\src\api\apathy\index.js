/*
 * @Author: jhy
 * @Date: 2023-01-30 17:58:37
 * @LastEditors: jhy
 * @LastEditTime: 2023-02-02 13:45:35
 */
import request from "@/utils/request";

// -提交/草稿
export function submitInsentience(data) {
  return request({
    url: "/system/insentience-cash/submit",
    method: "post",
    data,
  });
}

// -根据当前登录用户获取相关联的企业信息
export function getCompanyInfoByLoginInfo(params) {
  return request({
    url: "/system/company/mag/getCompanyInfoByLoginInfo",
    method: "get",
    params,
  });
}
