import request from '@/utils/request'

// 查询人才入驻流程列表
export function listTalentSettleProcess(query) {
  return request({
    url: '/system/talentSettleProcess/list',
    method: 'get',
    params: query
  })
}

// 查询人才入驻流程详细
export function getTalentSettleProcess(id) {
  return request({
    url: '/system/talentSettleProcess/' + id,
    method: 'get'
  })
}

// 新增人才入驻流程
export function addTalentSettleProcess(data) {
  return request({
    url: '/system/talentSettleProcess',
    method: 'post',
    data: data
  })
}

// 修改人才入驻流程
export function updateTalentSettleProcess(data) {
  return request({
    url: '/system/talentSettleProcess',
    method: 'put',
    data: data
  })
}

// 删除人才入驻流程
export function delTalentSettleProcess(id) {
  return request({
    url: '/system/talentSettleProcess/' + id,
    method: 'delete'
  })
}
