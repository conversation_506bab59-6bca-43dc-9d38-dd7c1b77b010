package com.ruoyi.portalweb.api.domain;

import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 关联企业信息对象 company_related
 * 
 * <AUTHOR>
 * @date 2024-06-20
 */
public class CompanyRelated extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 关联公司ID */
    private Long companyRelatedId;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 公司邮箱 */
    @Excel(name = "公司邮箱")
    private String companyEmail;

    /** 营业执照 */
    @Excel(name = "营业执照")
    private String businessLicenseImageUrl;

    /** logo图片 */
    @Excel(name = "logo图片")
    private String logoImageUrl;

    /** 社会统一信用代码 */
    @Excel(name = "社会统一信用代码")
    private String socialUnityCreditCode;

    /** 服务行业 */
    @Excel(name = "服务行业")
    private String serviceIndustry;

    /** 企业规模 */
    @Excel(name = "企业规模")
    private String companySize;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String address;

    /** 注册资金 */
    @Excel(name = "注册资金")
    private String registeredCapital;

    /** 企业简介 */
    @Excel(name = "企业简介")
    private String intrduction;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 公司法人 */
    @Excel(name = "公司法人")
    private String companyLegal;

    /** 成立日期 */
    @Excel(name = "成立日期")
    private String establishTime;

    /** 企业状态 */
    @Excel(name = "企业状态")
    private String status;

    /** 注册号 */
    @Excel(name = "注册号")
    private String regNumber;

    /** 组织机构代码 */
    @Excel(name = "组织机构代码")
    private String orgNumber;

    /** 企业类型 */
    @Excel(name = "企业类型")
    private String companyOrgType;

    /** 曾用名 */
    @Excel(name = "曾用名")
    private String historyNameList;

    /** 实际注册资金 */
    @Excel(name = "实际注册资金")
    private String actualCapital;

    /** 查看次数 */
    @Excel(name = "查看次数")
    private Long viewCount;

    /** 企业标签 */
    @Excel(name = "企业标签")
    private String label;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Long getViewCount() {return viewCount;}

    public void setViewCount(Long viewCount) {this.viewCount = viewCount;}


    public String getLogoImageUrl() {return logoImageUrl;}

    public void setLogoImageUrl(String logoImageUrl) {this.logoImageUrl = logoImageUrl;}

    public void setCompanyRelatedId(Long companyRelatedId)
    {
        this.companyRelatedId = companyRelatedId;
    }

    public Long getCompanyRelatedId() 
    {
        return companyRelatedId;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setCompanyEmail(String companyEmail) 
    {
        this.companyEmail = companyEmail;
    }

    public String getCompanyEmail() 
    {
        return companyEmail;
    }
    public void setBusinessLicenseImageUrl(String businessLicenseImageUrl) 
    {
        this.businessLicenseImageUrl = businessLicenseImageUrl;
    }

    public String getBusinessLicenseImageUrl() 
    {
        return businessLicenseImageUrl;
    }
    public void setSocialUnityCreditCode(String socialUnityCreditCode) 
    {
        this.socialUnityCreditCode = socialUnityCreditCode;
    }

    public String getSocialUnityCreditCode() 
    {
        return socialUnityCreditCode;
    }
    public void setServiceIndustry(String serviceIndustry) 
    {
        this.serviceIndustry = serviceIndustry;
    }

    public String getServiceIndustry() 
    {
        return serviceIndustry;
    }
    public void setCompanySize(String companySize) 
    {
        this.companySize = companySize;
    }

    public String getCompanySize() 
    {
        return companySize;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setRegisteredCapital(String registeredCapital) 
    {
        this.registeredCapital = registeredCapital;
    }

    public String getRegisteredCapital() 
    {
        return registeredCapital;
    }
    public void setIntrduction(String intrduction) 
    {
        this.intrduction = intrduction;
    }

    public String getIntrduction() 
    {
        return intrduction;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }
    public void setCompanyLegal(String companyLegal) 
    {
        this.companyLegal = companyLegal;
    }

    public String getCompanyLegal() 
    {
        return companyLegal;
    }
    public void setEstablishTime(String establishTime) 
    {
        this.establishTime = establishTime;
    }

    public String getEstablishTime() 
    {
        return establishTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setRegNumber(String regNumber) 
    {
        this.regNumber = regNumber;
    }

    public String getRegNumber() 
    {
        return regNumber;
    }
    public void setOrgNumber(String orgNumber) 
    {
        this.orgNumber = orgNumber;
    }

    public String getOrgNumber() 
    {
        return orgNumber;
    }
    public void setCompanyOrgType(String companyOrgType) 
    {
        this.companyOrgType = companyOrgType;
    }

    public String getCompanyOrgType() 
    {
        return companyOrgType;
    }
    public void setHistoryNameList(String historyNameList) 
    {
        this.historyNameList = historyNameList;
    }

    public String getHistoryNameList() 
    {
        return historyNameList;
    }
    public void setActualCapital(String actualCapital) 
    {
        this.actualCapital = actualCapital;
    }

    public String getActualCapital() 
    {
        return actualCapital;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("companyRelatedId", getCompanyRelatedId())
            .append("companyName", getCompanyName())
            .append("companyEmail", getCompanyEmail())
            .append("businessLicenseImageUrl", getBusinessLicenseImageUrl())
            .append("socialUnityCreditCode", getSocialUnityCreditCode())
            .append("serviceIndustry", getServiceIndustry())
            .append("companySize", getCompanySize())
            .append("phone", getPhone())
            .append("address", getAddress())
            .append("registeredCapital", getRegisteredCapital())
            .append("intrduction", getIntrduction())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("delFlag", getDelFlag())
            .append("companyLegal", getCompanyLegal())
            .append("establishTime", getEstablishTime())
            .append("status", getStatus())
            .append("regNumber", getRegNumber())
            .append("orgNumber", getOrgNumber())
            .append("companyOrgType", getCompanyOrgType())
            .append("historyNameList", getHistoryNameList())
            .append("actualCapital", getActualCapital())
            .append("viewCount", getViewCount())
            .append("label", getLabel())
            .toString();
    }
}
