import request from '@/utils/request'

// 查询解决方案列表
export function listSolution(query) {
  return request({
    url: '/portalconsole/solution/list',
    method: 'get',
    params: query
  })
}

// 查询解决方案详细
export function getSolution(solutionId) {
  return request({
    url: '/portalconsole/solution/detail/' + solutionId,
    method: 'get'
  })

  //http://localhost:8080/portalconsole/solution/detail/2
}



// 新增解决方案
export function addSolution(data) {
  return request({
    url: '/portalconsole/solution',
    method: 'post',
    data: data
  })
}

// 修改解决方案
export function updateSolution(data) {
  return request({
    url: '/portalconsole/solution',
    method: 'put',
    data: data
  })
}

// 删除解决方案
export function delSolution(solutionId) {
  return request({
    url: '/portalconsole/solution/' + solutionId,
    method: 'delete'
  })
}
