<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.DemandMapper">

    <resultMap type="com.ruoyi.portalconsole.domain.vo.DemandVO" id="DemandResult">
        <result property="id" column="id"/>
        <result property="companyName" column="company_name"/>
        <result property="contact" column="contact"/>
        <result property="phone" column="phone"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="description" column="description"/>
        <result property="applicationArea" column="application_area"/>
        <result property="imageUrl" column="image_url"/>
<!--        <result property="bidType" column="bid_type"/>-->
<!--        <result property="targetCompany" column="target_company"/>-->
<!--        <result property="bidStartTime" column="bid_start_time"/>-->
<!--        <result property="bidEndTime" column="bid_end_time"/>-->
<!--        <result property="bidDeadline" column="bid_deadline"/>-->
<!--        <result property="attachment" column="attachment"/>-->
<!--        <result property="publisher" column="publisher"/>-->
        <result property="auditStatus" column="audit_status"/>
        <result property="visible" column="visible"/>
        <result property="recommend" column="recommend"/>
        <result property="processStatus" column="process_status"/>
        <result property="onShow" column="on_show"/>
<!--        <result property="prospectiveCorp" column="prospective_corp"/>-->
<!--        <result property="autoPublish" column="auto_publish"/>-->
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberId" column="member_id"/>
        <result property="viewCount" column="view_count"/>


        <result property="applicationAreaName" column="application_area_name"/>
    </resultMap>

    <sql id="selectDemandVo">
        select id, company_name, contact, phone, title, type, description, application_area, image_url, view_count,
        audit_status, visible, recommend, process_status, on_show, create_by, member_id,
        create_time, update_by, update_time, remark, d2.application_area_name from demand
    </sql>

    <select id="selectDemandList" parameterType="Demand" resultMap="DemandResult">
        <include refid="selectDemandVo"/>
        LEFT JOIN  (
            SELECT d1.id as did, GROUP_CONCAT(af.application_field_name) as application_area_name FROM demand d1 LEFT JOIN application_field af ON FIND_IN_SET (af.application_field_id, d1.application_area)
            GROUP BY d1.id
        ) d2 ON d2.did = demand.id

        <where>
            <if test="companyName != null  and companyName != ''">and company_name like concat('%', #{companyName},
                '%')
            </if>
            <if test="contact != null  and contact != ''">and contact = #{contact}</if>
            <if test="phone != null  and phone != ''">and phone = #{phone}</if>
            <if test="title != null  and title != ''">and title like concat('%', #{title}, '%')</if>
            <if test="type != null  and type != ''">and type like concat('%', #{type},'%')</if>
            <if test="applicationArea != null  and applicationArea != ''">and application_area like concat('%',
                #{applicationArea}, '%')
            </if>
<!--            <if test="bidType != null  and bidType != ''">and bid_type = #{bidType}</if>-->
<!--            <if test="bidStartTime != null ">and bid_start_time = #{bidStartTime}</if>-->
<!--            <if test="bidEndTime != null ">and bid_end_time = #{bidEndTime}</if>-->
<!--            <if test="bidDeadline != null ">and bid_deadline = #{bidDeadline}</if>-->
            <if test="publisher != null  and publisher != ''">and publisher = #{publisher}</if>
            <if test="auditStatus != null ">and audit_status = #{auditStatus}</if>
            <if test="processStatus != null ">and process_status = #{processStatus}</if>
            <if test="onShow != null ">and on_show = #{onShow}</if>
            <if test="autoPublish != null ">and auto_publish = #{autoPublish}</if>
        </where>
    </select>

    <select id="selectDemandById" parameterType="Long" resultMap="DemandResult">
        <include refid="selectDemandVo"/>
        LEFT JOIN  (
        SELECT d1.id as did, GROUP_CONCAT(af.application_field_name) as application_area_name FROM demand d1 LEFT JOIN application_field af ON FIND_IN_SET (af.application_field_id, d1.application_area)
        GROUP BY d1.id
        ) d2 ON d2.did = demand.id
        where id = #{id}
    </select>

    <insert id="insertDemand" parameterType="Demand" useGeneratedKeys="true" keyProperty="id">
        insert into demand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="contact != null and contact != ''">contact,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="type != null">type,</if>
            <if test="description != null">description,</if>
            <if test="applicationArea != null">application_area,</if>
            <if test="imageUrl != null">image_url,</if>
<!--            <if test="bidType != null">bid_type,</if>-->
<!--            <if test="targetCompany != null">target_company,</if>-->
<!--            <if test="bidStartTime != null">bid_start_time,</if>-->
<!--            <if test="bidEndTime != null">bid_end_time,</if>-->
<!--            <if test="bidDeadline != null">bid_deadline,</if>-->
<!--            <if test="attachment != null">attachment,</if>-->
<!--            <if test="publisher != null">publisher,</if>-->
            <if test="auditStatus != null">audit_status,</if>
            <if test="visible != null and visible != ''">visible,</if>
            <if test="recommend != null">recommend,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="onShow != null">on_show,</if>
<!--            <if test="prospectiveCorp != null">prospective_corp,</if>-->
<!--            <if test="autoPublish != null">auto_publish,</if>-->
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="type != null">#{type},</if>
            <if test="description != null">#{description},</if>
            <if test="applicationArea != null">#{applicationArea},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
<!--            <if test="bidType != null">#{bidType},</if>-->
<!--            <if test="targetCompany != null">#{targetCompany},</if>-->
<!--            <if test="bidStartTime != null">#{bidStartTime},</if>-->
<!--            <if test="bidEndTime != null">#{bidEndTime},</if>-->
<!--            <if test="bidDeadline != null">#{bidDeadline},</if>-->
<!--            <if test="attachment != null">#{attachment},</if>-->
<!--            <if test="publisher != null">#{publisher},</if>-->
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="visible != null and visible != ''">#{visible},</if>
            <if test="recommend != null">#{recommend},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="onShow != null">#{onShow},</if>
<!--            <if test="prospectiveCorp != null">#{prospectiveCorp},</if>-->
<!--            <if test="autoPublish != null">#{autoPublish},</if>-->
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDemand" parameterType="Demand">
        update demand
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="type != null">type = #{type},</if>
            <if test="description != null">description = #{description},</if>
            <if test="applicationArea != null">application_area = #{applicationArea},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
<!--            <if test="bidType != null">bid_type = #{bidType},</if>-->
<!--            <if test="targetCompany != null">target_company = #{targetCompany},</if>-->
<!--            <if test="bidStartTime != null">bid_start_time = #{bidStartTime},</if>-->
<!--            <if test="bidEndTime != null">bid_end_time = #{bidEndTime},</if>-->
<!--            <if test="bidDeadline != null">bid_deadline = #{bidDeadline},</if>-->
<!--            <if test="attachment != null">attachment = #{attachment},</if>-->
<!--            <if test="publisher != null">publisher = #{publisher},</if>-->
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="visible != null and visible != ''">visible = #{visible},</if>
            <if test="recommend != null">recommend = #{recommend},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="onShow != null">on_show = #{onShow},</if>
<!--            <if test="prospectiveCorp != null">prospective_corp = #{prospectiveCorp},</if>-->
<!--            <if test="autoPublish != null">auto_publish = #{autoPublish},</if>-->
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDemandById" parameterType="Long">
        delete from demand where id = #{id}
    </delete>

    <delete id="deleteDemandByIds" parameterType="String">
        delete from demand where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>