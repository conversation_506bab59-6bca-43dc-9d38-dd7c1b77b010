<template>
    <el-dialog title="设置需求方" :visible.sync="chargemodal" width="80%" append-to-body>
      <el-form ref="form" :inline="true" :model="customerParams" label-width="80px">
        <el-form-item label="手机号" prop="userId">
          <el-input v-model="customerParams.userId" clearable placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="customerParams.name" clearable placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button type="primary" @click="customerSearch">搜索</el-button>
          <el-button @click="delCustomerSearch">清空</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="chargeloading" :data="customerList">
        <el-table-column label="姓名" align="center" prop="name" />
        <el-table-column label="手机号码" align="center" prop="userId" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="small" type="primary" @click="setCustomer(scope.row)">设为需求方</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="customerTotal > 0" :total="customerTotal" :page.sync="customerParams.page"
        :limit.sync="customerParams.size" @pagination="getCustomer" />
    </el-dialog>
</template>
<script>
export default {
    name: "setupDialog",
    props: {

    },
    data() {
      return {
        chargeloading: false,
        chargemodal:false,
        customerParams: {
          page: 1,
          size: 10,
          name: null,
          userId: null,
        },
        customerTotal: 0,
        customerList: [],
      };
    },
    created() {
      
    },
    methods: {
     /**
       * 显示弹框
       */
       async show() {
        this.chargemodal = true; // 切换显示
        this.chargeloading = true;
      },
      getCustomer() {
        // getCustomer(this.customerParams).then((res) => {
        //   this.customerList = res.data.rows;
        //   this.customerTotal = res.data.total;
        //   this.chargeloading = false;
        // });
      },
      // 客服搜索
      customerSearch() {
        this.customerParams.page = 1;
        this.customerParams.size = 10;
        this.getCustomer();
      },
      //清空
      delCustomerSearch() {
        this.customerParams = {
          page: 1,
          size: 10,
          name: null,
          userId: null,
        };
        this.getCustomer();
      },
      // 确认设置客服
      setCustomer(row) {
        // chargeRequire({
        //   "id": this.requireId,
        //   "charger": row.userId,
        //   "ncharger": row.name
        // }).then((response) => {
        //   if (response.code === 200) {
        //     this.msgSuccess("修改成功");
        //     this.chargemodal = false;
        //     this.getList()
        //   }
        // });
      },
    }
  };
</script>
<style>
</style>