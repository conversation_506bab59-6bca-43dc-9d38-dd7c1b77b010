{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\scene\\index.vue?vue&type=style&index=1&id=5fdb2436&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\scene\\index.vue", "mtime": 1750311963004}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnNjZW5lLWNvbnRhaW5lciB7DQogIC5zY2VuZS1zZWFyY2gtaW5wdXQgew0KICAgIC5lbC1pbnB1dF9faW5uZXIgew0KICAgICAgaGVpZ2h0OiA1NHB4Ow0KICAgICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDI3cHggMCAwIDI3cHg7DQogICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgICBwYWRkaW5nLWxlZnQ6IDMwcHg7DQogICAgfQ0KICAgIC5lbC1pbnB1dC1ncm91cF9fYXBwZW5kIHsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDBweCAxMDBweCAxMDBweCAwcHg7DQogICAgICBiYWNrZ3JvdW5kOiAjMjFjOWI4Ow0KICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgY29sb3I6ICNmZmY7DQogICAgICBsaW5lLWhlaWdodDogMjRweDsNCiAgICB9DQogIH0NCiAgLnNjZW5lLXNlYXJjaC1saW5lIHsNCiAgICAuZWwtZm9ybS1pdGVtX19sYWJlbCB7DQogICAgICB3aWR0aDogODhweDsNCiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICBjb2xvcjogIzk5OTsNCiAgICAgIHBhZGRpbmctcmlnaHQ6IDMycHg7DQogICAgICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICAgIH0NCiAgICAuc2NlbmUtc2VhcmNoLXJhZGlvIHsNCiAgICAgIHdpZHRoOiAxMDUwcHg7DQogICAgICBtYXJnaW4tdG9wOiAxMXB4Ow0KICAgICAgLmVsLXJhZGlvLWJ1dHRvbiB7DQogICAgICAgIHBhZGRpbmctYm90dG9tOiAyMHB4Ow0KICAgICAgICAuZWwtcmFkaW8tYnV0dG9uX19pbm5lciB7DQogICAgICAgICAgYm9yZGVyOiBub25lOw0KICAgICAgICAgIHBhZGRpbmc6IDAgMzJweCAwIDA7DQogICAgICAgICAgYmFja2dyb3VuZDogbm9uZTsNCiAgICAgICAgICAmOmhvdmVyIHsNCiAgICAgICAgICAgIGNvbG9yOiAjMjFjOWI4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAmLmlzLWFjdGl2ZSB7DQogICAgICAgICAgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgew0KICAgICAgICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiBub25lOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAuZWwtcmFkaW8tYnV0dG9uX19vcmlnLXJhZGlvOmNoZWNrZWQgew0KICAgICAgICAgICYgKyAuZWwtcmFkaW8tYnV0dG9uX19pbm5lciB7DQogICAgICAgICAgICBib3gtc2hhZG93OiB1bnNldDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLnNjZW5lLXBhZ2UtZW5kIHsNCiAgICAuc2NlbmUtcGFnaW5hdGlvbiB7DQogICAgICAuYnRuLXByZXYsDQogICAgICAuYnRuLW5leHQsDQogICAgICAuYnRuLXF1aWNrcHJldiB7DQogICAgICAgIHdpZHRoOiAzMnB4Ow0KICAgICAgICBoZWlnaHQ6IDMycHg7DQogICAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICB9DQogICAgICAmLmlzLWJhY2tncm91bmQgew0KICAgICAgICAuZWwtcGFnZXIgew0KICAgICAgICAgIC5udW1iZXIgew0KICAgICAgICAgICAgd2lkdGg6IDMycHg7DQogICAgICAgICAgICBoZWlnaHQ6IDMycHg7DQogICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICAgICAgICAgICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAzMnB4Ow0KICAgICAgICAgICAgJi5hY3RpdmUgew0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjMjFjOWI4Ow0KICAgICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjMjFjOWI4Ow0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkSA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/scene", "sourcesContent": ["<template>\r\n  <div class=\"scene-container\">\r\n    <div class=\"scene-banner\">\r\n      <img\r\n        src=\"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/1676017632639145.webp\"\r\n        alt=\"场景发布\"\r\n      />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"scene-title-content\">\r\n        <div class=\"scene-title-box\">\r\n          <div class=\"scene-divider\"></div>\r\n          <div class=\"scene-title\">场景发布</div>\r\n          <div class=\"scene-divider\"></div>\r\n        </div>\r\n        <div class=\"scene-search-box\">\r\n          <el-form ref=\"form\" class=\"scene-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.title\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"scene-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"scene-search-btn\"\r\n                  @click=\"search\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"scene-info-content\">\r\n        <template v-if=\"items.length > 0\">\r\n          <div\r\n            v-for=\"(item, index) in items\"\r\n            :key=\"index\"\r\n            class=\"scene-list-item\"\r\n            @click=\"goNoticDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <!-- <el-image class=\"list-item-img\" fit=\"fill\" :alt=\"item.title\" :src=\"item.src\" /> -->\r\n              <div class=\"list-item-info\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.title }}\r\n                </div>\r\n                <div class=\"list-item-text\">\r\n                  {{ item.content }}\r\n                </div>\r\n                <div class=\"list-item-time\">{{ item.updateTime }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-else>\r\n          <el-empty />\r\n        </template>\r\n        <div class=\"scene-page-end\">\r\n          <el-button class=\"scene-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"items && items.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"scene-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { head, map } from \"ramda\";\r\nimport { getListByText } from \"@/api/scene\";\r\n\r\nexport default {\r\n  name: \"ScenePage\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        title: undefined, //搜索内容\r\n      },\r\n      items: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getListByText({\r\n        ...this.form,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          console.log(\"rows\", rows);\r\n          this.items = map(\r\n            ({\r\n              id,\r\n              title,\r\n              simpleContent,\r\n              updateTime,\r\n              coverPictureList = [],\r\n            }) => {\r\n              const image = head(coverPictureList || []) || {};\r\n              return {\r\n                id,\r\n                title,\r\n                content: simpleContent,\r\n                updateTime,\r\n                src: image.url,\r\n              };\r\n            },\r\n            rows\r\n          );\r\n          this.total = total;\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.pageNum = 1;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    // 跳转到详情页面\r\n    goNoticDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/scenarioDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.scene-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .scene-banner {\r\n    width: 100%;\r\n    height: 540px;\r\n    img {\r\n      width: 100%;\r\n      height: 540px;\r\n      object-fit: fill;\r\n    }\r\n  }\r\n  .scene-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    padding-bottom: 18px;\r\n    .scene-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .scene-title {\r\n        font-size: 40px;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .scene-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .scene-search-box {\r\n      .scene-search-form {\r\n        text-align: center;\r\n        .scene-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .scene-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .scene-info-content {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    .scene-list-item {\r\n      width: 100%;\r\n      background: #fff;\r\n      border-radius: 12px;\r\n      margin-top: 24px;\r\n      .list-item-content {\r\n        display: flex;\r\n        padding: 24px 32px;\r\n        cursor: pointer;\r\n        .list-item-img {\r\n          width: 298px;\r\n          height: 212px;\r\n          border-radius: 7px;\r\n          overflow: hidden;\r\n        }\r\n        .list-item-info {\r\n          padding-left: 24px;\r\n          .list-item-title {\r\n            width: 806px;\r\n            height: 24px;\r\n            text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n            white-space: nowrap; /*让文字不换行*/\r\n            overflow: hidden; /*超出要隐藏*/\r\n            font-size: 24px;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            line-height: 24px;\r\n            margin: 8px 0 24px;\r\n          }\r\n          .list-item-text {\r\n            width: 806px;\r\n            height: 90px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 3;\r\n            text-overflow: ellipsis;\r\n            font-size: 16px;\r\n            color: #666;\r\n            line-height: 30px;\r\n          }\r\n          .list-item-time {\r\n            color: #999;\r\n            line-height: 14px;\r\n            margin-top: 52px;\r\n          }\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .scene-page-end {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin: 0 auto;\r\n      padding: 24px 0 60px;\r\n      .scene-page-btn {\r\n        width: 82px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        color: #333;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.scene-container {\r\n  .scene-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .scene-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .scene-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .scene-page-end {\r\n    .scene-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}