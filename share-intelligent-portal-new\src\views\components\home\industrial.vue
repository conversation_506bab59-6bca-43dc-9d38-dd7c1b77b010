<template>
  <div
    class="card-container2 wow animate__animated animate__fadeInUp industrial"
    data-wow-duration="1s"
  >
    <h1 class="flexFooterTitle">品牌实力</h1>
    <div class="flex">
      <div class="flexl">
        <img src="@/assets/aboutUs/763.png" alt="" />
        <div style="height: 20px"></div>
        <!-- <p>
            <span class="number">763</span>
            <span class="unit">台套</span>
          </p> -->
        <p class="flexlTitle">复材产业集群设备共享</p>
      </div>
      <div class="flexr">
        <div
          class="flexr_item"
          :class="{ flexr_itemA: activeIndex === 0 }"
          @mouseenter="handleMouseEnter(0)"
          @mouseleave="handleMouseLeave"
        >
          <div>
            <span class="flexrA">187</span>
            <span class="flexrB">家</span>
          </div>

          <div style="height: 12px"></div>
          <div class="flexrB">企业注册</div>
        </div>
        <div
          class="flexr_item"
          :class="{ flexr_itemB: activeIndex === 1 }"
          @mouseenter="handleMouseEnter(1)"
          @mouseleave="handleMouseLeave"
        >
          <div>
            <span class="flexrA">12</span>
            <span class="flexrB">种</span>
          </div>
          <div style="height: 12px"></div>
          <div class="flexrB">工业APP</div>
        </div>
        <div
          class="flexr_item"
          :class="{ flexr_itemC: activeIndex === 2 }"
          @mouseenter="handleMouseEnter(2)"
          @mouseleave="handleMouseLeave"
        >
          <div>
            <span class="flexrA">736</span>
            <span class="flexrB">次</span>
          </div>
          <div style="height: 12px"></div>
          <div class="flexrC">2025年累计服务需求</div>
        </div>
        <div
          class="flexr_item"
          :class="{ flexr_itemD: activeIndex === 3 }"
          @mouseenter="handleMouseEnter(3)"
          @mouseleave="handleMouseLeave"
        >
          <div>
            <span class="flexrA">13.5</span>
            <span class="flexrB">万平</span>
          </div>
          <div style="height: 12px"></div>
          <div class="flexrB">共享面积</div>
        </div>
        <div
          class="flexr_item"
          :class="{ flexr_itemE: activeIndex === 4 }"
          @mouseenter="handleMouseEnter(4)"
          @mouseleave="handleMouseLeave"
        >
          <div>
            <span class="flexrA">3.6</span>
            <span class="flexrB">亿</span>
          </div>
          <div style="height: 12px"></div>
          <div class="flexrB">集采授信总额</div>
        </div>
      </div>
    </div>
    <!-- <div class="enterpriseTitle">
      <div>产业服务</div>
      <div class="allEnterprise" @click="goSolution">查看全部>></div>
    </div>
    <div class="content">
      <div
        stopPropagation
        :class="index === flag ? 'contentItemHover' : 'contentItem'"
        v-for="(item, index) in data"
        :key="index"
        @click="goDetail(index)"
        @mouseenter="mouseOver(index)"
      >
        <div v-if="index != flag">
          <div class="titleIcon" v-if="index == 0">
            <img
              class="iconStyle"
              src="@/assets/images/home/<USER>"
              alt=""
            />
          </div>
          <div class="titleIcon" v-if="index == 1">
            <img
              class="iconStyle"
              src="@/assets/images/home/<USER>"
              alt=""
            />
          </div>
          <div class="titleIcon" v-if="index == 2">
            <img
              class="iconStyle"
              src="@/assets/images/home/<USER>"
              alt=""
            />
          </div>
          <div class="titleIcon" v-if="index == 3">
            <img
              class="iconStyle"
              src="@/assets/images/home/<USER>"
              alt=""
            />
          </div>
          <div class="title">{{ item.typeName }}</div>
        </div>
        <div v-else>
          <div class="typeName">{{ item.typeName }}</div>
          <div class="desc">
            {{ item.text }}
          </div>
        </div>
        <div class="typeName" v-show="index == flag">
          {{ item.name }}
        </div>
        <div class="desc" v-show="index === flag">
          <div style="font-size: 14px">{{ item.overview }}</div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
// import { getCompanyHomeList } from "@/api/purchaseSales";
import { solutionHomeList } from "@/api/solution";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      activeIndex: 0,
      loading: false,
      data: [
        {
          id: 1,
          typeName: "制造共享",
          overview: "**************************",
          text: "易复材共享智造工业互联网平台整合复材集群即有的制造链条资源，实现设备、车间、订单的灵活共享以及工序的高效外协，构建起“按需调用、弹性分配”的分布式制造网络，有效盘活了集群企业中闲置的设备设施，避免了资源的浪费，大幅度降低了需求企业在固定资产投资方面的负担。",
        },
        {
          id: 2,
          typeName: "服务共享",
          overview: "**************************",
          text: "易复材共享智造工业互联网平台联合第三方服务机构，打造“制造+服务”融合生态，覆盖企业全生命周期需求。提供人才培育、技能鉴定、用工咨询、创业支持、检验检测等服务。目前已开展人才培训、技能鉴定、人才“蓄水池”、设备资源共享等平台功能，助力企业从注册到经营的全流程高效运转。",
        },
        {
          id: 3,
          typeName: "创新共享",
          overview: "**************************",
          text: "易复材共享智造工业互联网平台依托研究院及创新资源，通过众筹科研、揭榜挂帅、校企合作等方式推动产学研深度融合。重点解决企业技术难题，加速专利成果转化与行业标准制定。推动复合材料产业向高端化、绿色化方向延伸，构建技术攻关与成果转化的可持续创新生态。",
        },
        {
          id: 4,
          typeName: "集采共享",
          overview: "**************************",
          text: "易复材共享智造工业互联网平台聚焦供应链协同优化，采用“集采+仓储+物流+金融”一体化模式。通过集中采购提升议价能力，提供原料价格趋势分析与多品类集采服务；搭建现代化“云仓库”解决中小企业仓储物流成本高、周期长的问题；联合建设银行推出“建行e链”供应链金融系统，为企业提供快速授信支持。",
        },
      ],
      pageNum: 1,
      total: 0,
      flag: 0,
    };
  },
  created() {
    // this.getList();
  },
  methods: {
    handleMouseEnter(index) {
      this.activeIndex = index;
    },
    handleMouseLeave() {
      // 鼠标离开后恢复默认(第一个保持黑色)
      this.activeIndex = 0;
    },
    getList() {
      solutionHomeList().then((res) => {
        if (res.code === 200) {
          this.data = res.data;
        }
      });
    },
    // 跳转到详情页面
    goDetail(index) {
      if (index == 0) {
        this.$router.push("/manufacturingSharing");
      } else if (index == 1) {
        this.$router.push("/serviceSharing");
      } else if (index == 2) {
        this.$router.push("/innovationSharing");
      } else if (index == 3) {
        window.open("http://**************:1001/");
      }
      // this.$router.push({
      //   path: "/solutionDetail",
      //   query: {
      //     id,
      //   },
      // });
    },
    goSolution() {
      let routeData = this.$router.resolve({
        path: "/solution",
      });
      window.open(routeData.href, "_blank");
    },
    // 移入
    mouseOver(index) {
      this.flag = index;
    },
  },
};
</script>

<style lang="scss" scoped>
.industrial {
  background: url("../../../assets/aboutUs/aboutUsFooter.png") no-repeat center
    center;
  background-size: 100% 100%;
  height: 804px;
  .flexFooterTitle {
    font-size: 34px;
    padding: 90px 0 60px;
    margin: 0;
    text-align: center;
    color: #000000;
  }
  .flex {
    width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    .flexr {
      width: 50%;
      flex: 1;
      display: flex;
      justify-content: space-between; /* 默认第一个元素为黑色背景 */
      .flexrA {
        font-size: 50px;
      }
      .flexrB {
        font-size: 20px;
      }
      .flexr_itemA {
        background-image: url("../../../assets/aboutUs/flexr_itemA.png") !important;
        background-size: 100% 100%;
        width: 25% !important;
      }
      .flexr_itemB {
        background-image: url("../../../assets/aboutUs/flexr_itemB.png") !important;
        background-size: 100% 100%;
        width: 25% !important;
      }
      .flexr_itemC {
        background-image: url("../../../assets/aboutUs/flexr_itemC.png") !important;
        background-size: 100% 100%;
        width: 25% !important;
      }
      .flexr_itemD {
        background-image: url("../../../assets/aboutUs/flexr_itemD.png") !important;
        background-size: 100% 100%;
        width: 25% !important;
      }
      .flexr_itemE {
        background-image: url("../../../assets/aboutUs/flexr_itemE.png") !important;
        background-size: 100% 100%;
        width: 25% !important;
      }
      .flexr_item {
        color: #ffffff;
        transition: all 0.3s ease-in;
        cursor: pointer;
        // width: 33.2%;
        width: 20%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(10, 147, 133, 0.6);
        // &:hover {
        //   width: 34% !important;
        //   background: rgba(10, 147, 133, 0.6) !important;
        // }
      }
    }
    .flexl {
      width: 420px;
      height: 500px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      // align-items: center;
      background: rgba(255, 255, 255, 0.6);
      padding: 70px;
      img {
        width: 242px;
      }
      .flexlTitle {
        font-weight: 900;
        font-size: 20px;
      }
    }
  }
}

.card-container2 {
  /*   // max-width: 1920px;
  width: 100%;
  // height: 630px;
margin-left: auto;
  margin-right: auto;
  background-image: url("../../../assets/images/home/<USER>");
  background-size: 100% 100%;
  padding-top: 68px;
  padding-bottom: 66px; */
}
.enterpriseTitle {
  width: 100%;
  text-align: center;
  margin-bottom: 59px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 38px;
  color: #222222;
  // position: relative;
  // .allEnterprise {
  //   position: absolute;
  //   top: 50px;
  //   right: 0;
  //   font-size: 16px;
  //   font-family: Source Han Sans CN;
  //   font-weight: 500;
  //   color: #21C9B8;
  //   line-height: 26px;
  //   cursor: pointer;
  // }
}
.content {
  display: flex;
  justify-content: space-between;
  width: 1200px;
  height: 400px;
  margin: 0 auto;
  .contentItem {
    width: 200px;
    height: 100%;
    text-align: center;
    cursor: pointer;
    background: #ffffff;
    box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);
    border-radius: 4px;
    margin: 0 4.5px;
    transition-duration: 0.4s;
    transition-timing-function: ease;
    transition-delay: 0s;
    transition-property: width;
    img {
      width: 100%;
      height: 230px;
    }
    .title {
      font-size: 20px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      margin-top: 20px;
      text-align: center;
    }
    .titleIcon {
      width: 46px;
      height: 46px;
      margin: 132px auto 13px;
      // background: #ffffff;
      // border-radius: 2px;
      // margin-left: 30px;
      .iconStyle {
        width: 100%;
        height: 100%;
      }
    }
  }
  .contentItem:nth-child(1) {
    background-image: url("../../../assets/images/home/<USER>");
    background-size: 100% 100%;
    margin-left: 0;
  }
  .contentItem:nth-child(2) {
    background-image: url("../../../assets/images/home/<USER>");
    background-size: 100% 100%;
  }
  .contentItem:nth-child(3) {
    background-image: url("../../../assets/images/home/<USER>");
    background-size: 100% 100%;
  }
  .contentItem:nth-child(4) {
    background-image: url("../../../assets/images/home/<USER>");
    background-size: 100% 100%;
  }
  .contentItemHover {
    width: 560px;
    height: 100%;
    transition-duration: 0.4s;
    transition-timing-function: ease;
    transition-delay: 0s;
    transition-property: width;
    // background-image: url("../../../assets/images/home/<USER>");
    background-size: 100% 100%;
    overflow: hidden;
    margin: 0 4.5px;
    cursor: pointer;
    .title {
      font-size: 20px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
      margin: 30px 0 10px 30px;
    }
    .typeName {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 26px;
      color: #ffffff;
      margin: 59px 0 30px 50px;
    }
    .desc {
      width: 449px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      margin: 0 60px 0 51px;
      line-height: 30px;
      // overflow: hidden;
      // display: -webkit-box;
      // -webkit-box-orient: vertical;
      // -webkit-line-clamp: 6;
      // text-overflow: ellipsis;
      // word-wrap: break-word;
    }
  }
  .contentItemHover:nth-child(1) {
    background-image: url("../../../assets/images/home/<USER>");
    background-size: 100% 100%;
  }
  .contentItemHover:nth-child(2) {
    background-image: url("../../../assets/images/home/<USER>");
    background-size: 100% 100%;
  }
  .contentItemHover:nth-child(3) {
    background-image: url("../../../assets/images/home/<USER>");
    background-size: 100% 100%;
  }
  .contentItemHover:nth-child(4) {
    background-image: url("../../../assets/images/home/<USER>");
    background-size: 100% 100%;
  }
}
</style>
