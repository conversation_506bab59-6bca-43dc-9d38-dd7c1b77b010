{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\userCenter\\index.vue?vue&type=style&index=0&id=ca4b6cfe&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\userCenter\\index.vue", "mtime": 1750311963090}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgSA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "index.vue", "sourceRoot": "src/views/system/user/userCenter", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"main-content\">\r\n          <el-row :gutter=\"30\">\r\n            <el-col :span=\"5\" style=\"opacity: 0;\">marginleft</el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form :model=\"userData\" :rules=\"rules\" ref=\"userData\" label-width=\"80px\">\r\n                <el-form-item label=\"头像\" prop=\"avatar\">\r\n                  <ImageUpload v-model=\"avatarList\" :limit=\"1\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"真实姓名\" prop=\"memberRealName\">\r\n                  <el-input v-model=\"userData.memberRealName\" placeholder=\"请输入真实姓名\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" prop=\"memberPhone\">\r\n                  <el-input v-model=\"userData.memberPhone\" placeholder=\"请输入联系方式\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"微信号\" prop=\"memberWechat\">\r\n                  <el-input v-model=\"userData.memberWechat\" placeholder=\"请输入微信号\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"所在行业\" prop=\"solutionTypeId\">\r\n                  <el-select v-model=\"userData.solutionTypeId\" placeholder=\"请选择\" class=\"form-select\">\r\n                    <el-option v-for=\"item in solutionTypeList\" :key=\"item.solutionTypeId\"\r\n                      :label=\"item.solutionTypeName\" :value=\"item.solutionTypeId\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"职位\" prop=\"memberPost\">\r\n                  <el-select v-model=\"userData.memberPost\" placeholder=\"请选择\" class=\"form-select\">\r\n                    <el-option v-for=\"item in memberPostList\" :key=\"item.dictValue\" :label=\"item.dictLabel\"\r\n                      :value=\"item.dictValue\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"企业名称\" prop=\"memberCompanyName\">\r\n                  <el-autocomplete v-model=\"userData.memberCompanyName\" placeholder=\"请输入您公司的完整名称\"\r\n                    :fetch-suggestions=\"querySearchTianYanCha\" @select=\"selectAutoDataTianYanCha\"\r\n                    style=\"width: 84%\"></el-autocomplete>\r\n                </el-form-item>\r\n                <el-form-item label=\"企业地址\" prop=\"memberCompanyArea\">\r\n                  <el-cascader clearable label=\"title\" value=\"id\" :options=\"areaList\"\r\n                    v-model=\"userData.memberCompanyArea\" :props=\"props\" placeholder=\"请选择地区\"\r\n                    class=\"form-select\"></el-cascader>\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"企业规模\" prop=\"companyScale\">\r\n                  <el-select v-model=\"userData.companyScale\" placeholder=\"请选择\" class=\"form-select\">\r\n                    <el-option v-for=\"item in companyScaleList\" :key=\"item.dictValue\" :label=\"item.dictLabel\"\r\n                      :value=\"item.dictValue\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-form>\r\n              <div class=\"btn-box\">\r\n                <el-button class=\"btn\" type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <!-- <div class=\"card\" v-if=\"!talentInfo\">\r\n                <div class=\"card-title\">人才库</div>\r\n                <div class=\"card-content\">您暂未入驻!</div>\r\n                <div class=\"card-content\">入驻后可获取需求企业对接</div>\r\n                <div class=\"btn-card\">\r\n                  <el-button class=\"btn\" type=\"primary\" @click=\"gotoTalentJoinNow('')\">立即入驻</el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"card\" v-if=\"talentInfo\">\r\n                <div class=\"card-title\">人才库</div>\r\n                <div class=\"card-content success\">您已经入驻!</div>\r\n                <div class=\"card-content\">点击下方按钮可预览，</div>\r\n                <div class=\"card-content\">如修改内容需重新审核。</div>\r\n                <div class=\"btn-card\">\r\n                  <el-button class=\"btn\" type=\"primary\" @click=\"gotoTalentJoinNow(talentInfo)\">查看信息</el-button>\r\n                </div>\r\n              </div> -->\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { industryList, getAreaData, userInfoSave } from \"@/api/system/user\";\r\nimport { searchCompany, getCompanyCodeByName } from \"@/api/system/company\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { getInfo } from \"@/api/login\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      userData: {},\r\n      settled: false,\r\n      locationhost: '',\r\n      showLogin: false,\r\n      userinfo: {},\r\n      token: '',\r\n      userType: '',\r\n      id: '',\r\n      detail: {},\r\n      setting: false,\r\n      cooperationData: [],\r\n      demandData: [],\r\n      showSuccess: false,\r\n      jbzldata: true,\r\n      qyzhdata: false,\r\n      imgLen: 0,\r\n      showPop1: false,\r\n      showPop2: false,\r\n      areaList: [],\r\n      props: {\r\n        label: 'title',\r\n        value: 'id',\r\n        children: 'children',\r\n        multiple: false\r\n      },\r\n      companyScaleList: [], // 企业规模\r\n      memberPostList: [], // 职位\r\n      solutionTypeList: [], // 行业\r\n      rules: {\r\n        memberPhone: [\r\n          { pattern: /^1[3456789]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n      },\r\n      talentInfo: {},\r\n      avatarList: '',\r\n    };\r\n  },\r\n  created() {\r\n    // if (window.sessionStorage.getItem('userinfo') != 'null' && window.sessionStorage.getItem('userinfo') != 'undefined' && window.sessionStorage.getItem('userinfo')) {\r\n    //   this.userinfo = JSON.parse(window.sessionStorage.getItem('userinfo'))\r\n    //   let info = this.userinfo\r\n    //   info.solutionTypeId = info.solutionTypeId ? info.solutionTypeId.toString() : ''\r\n    //   info.memberCompanyArea = info.memberCompanyArea ? info.memberCompanyArea.split(',') : []\r\n    //   this.userData = info\r\n    //   this.avatarList = this.userData.avatar ? [this.userData.avatar] : []\r\n    // } else {\r\n    // }\r\n    this.getInfo()\r\n    this.getSolutionType()\r\n    this.getMemberPost()\r\n    this.getCity()\r\n    this.getCompanyScale()\r\n  },\r\n  methods: {\r\n    // 获取用户信息\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (res.code == 200) {\r\n          this.userData = res.member || {};\r\n          this.userData.solutionTypeId = this.userData.solutionTypeId ? this.userData.solutionTypeId.toString() : ''\r\n          this.userData.memberCompanyArea = this.userData.memberCompanyArea ? this.userData.memberCompanyArea.split(',') : []\r\n          this.avatarList = this.userData.avatar ? this.userData.avatar : ''\r\n          this.talentInfo = res.talentInfo || {};\r\n        }\r\n      });\r\n    },\r\n    submitForm() {\r\n      this.$refs['userData'].validate((valid) => {\r\n        if (valid) {\r\n          let params = {\r\n            ...this.userData,\r\n            memberCompanyArea: this.userData.memberCompanyArea ? this.userData.memberCompanyArea.join(',') : '',\r\n            avatar: this.avatarList ? this.avatarList : ''\r\n          }\r\n          userInfoSave(params).then(res => {\r\n            if (res.code && res.code == 200) {\r\n              this.$message({\r\n                showClose: true,\r\n                message: '保存成功',\r\n                type: 'success'\r\n              });\r\n              window.sessionStorage.setItem('userinfo', JSON.stringify(res.member))\r\n              window.sessionStorage.setItem('userName', res.member.memberPhone);\r\n              window.sessionStorage.setItem('userId', res.member.memberId);\r\n              window.history.go(-1)\r\n            } else {\r\n              this.$message({\r\n                showClose: true,\r\n                message: '保存失败',\r\n                type: 'error'\r\n              });\r\n            }\r\n          });\r\n        }\r\n      });\r\n\r\n\r\n    },\r\n    // 行业\r\n    getSolutionType() {\r\n      industryList({}).then(res => {\r\n        if (res.code == 200) {\r\n          this.solutionTypeList = res.rows\r\n        }\r\n      })\r\n    },\r\n    // 职位\r\n    getMemberPost() {\r\n      let params = { dictType: \"member_post\" };\r\n      listData(params).then((response) => {\r\n        if (response.code == 200) {\r\n          this.memberPostList = response.rows;\r\n        }\r\n      });\r\n    },\r\n    // 企业名称\r\n    querySearchTianYanCha(queryString, cb) {\r\n      if (queryString) {\r\n        searchCompany({ keywords: queryString }).then(res => {\r\n          let data = res.rows;\r\n          let List = [];\r\n          data.forEach(function (val, index) {\r\n            List.push({\r\n              id: index,\r\n              value: val\r\n            })\r\n          })\r\n          if (data.length > 0) {\r\n            cb(List);\r\n          } else {\r\n            cb([{\r\n              id: '',\r\n              value: '暂无数据'\r\n            }]);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 企业名称选择\r\n    selectAutoDataTianYanCha(row) {\r\n      getCompanyCodeByName({ keywords: row.value }).then(res => {\r\n        if (res.code == 200) {\r\n          let data = res.data;\r\n          this.$set(this.userData, 'socialUnityCreditCode', data.taxNo)\r\n        }\r\n      });\r\n    },\r\n    // 企业地址\r\n    getCity() {\r\n      let cityList = window.sessionStorage.getItem('cityList');\r\n      if (!cityList) {\r\n        getAreaData().then(res => {\r\n          if (res.code == 200) {\r\n            let areaList = res.rows\r\n            areaList.forEach(item => {\r\n              item.children.forEach(item1 => {\r\n                item1.children.forEach(item2 => {\r\n                  delete item2.children\r\n                })\r\n              })\r\n            })\r\n            window.sessionStorage.setItem('cityList', JSON.stringify(areaList));\r\n            this.areaList = areaList;\r\n          }\r\n        })\r\n      } else {\r\n        this.areaList = JSON.parse(JSON.parse(JSON.stringify(cityList)))\r\n      }\r\n      // console.log( this.areaList)\r\n    },\r\n    // 职位\r\n    getCompanyScale() {\r\n      let params = { dictType: \"company_scale\" };\r\n      listData(params).then((response) => {\r\n        if (response.code == 200) {\r\n          this.companyScaleList = response.rows;\r\n        }\r\n      });\r\n    },\r\n    gotoTalentJoinNow(info) {\r\n      if (info) {\r\n        this.$router.push({ path: '/user/talentDetail', query: { id: info.id } })\r\n      } else {\r\n        this.$router.push({ path: '/user/talentJoinNow' })\r\n      }\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.main-content {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  padding-bottom: 100px;\r\n\r\n  .btn-box {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin-top: 40px;\r\n\r\n    .btn {\r\n      width: 200px;\r\n      height: 50px;\r\n    }\r\n  }\r\n\r\n  .card {\r\n    margin-top: 170px;\r\n    padding: 10px;\r\n    box-sizing: border-box;\r\n    width: 280px;\r\n    height: 240px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    background: url(\"../../../../assets/userCenter/card_bg.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n\r\n\r\n    .card-title {\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      margin-bottom: 10px;\r\n      color: #030A1A;\r\n    }\r\n\r\n    .card-content {\r\n      font-size: 14px;\r\n      color: #666666;\r\n      line-height: 30px;\r\n    }\r\n\r\n    .success {\r\n      color: #21C9B8;\r\n    }\r\n\r\n    .btn-card {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin-top: 0px;\r\n\r\n      .btn {\r\n        width: 200px;\r\n        height: 50px;\r\n      }\r\n    }\r\n  }\r\n\r\n}\r\n</style>\r\n"]}]}