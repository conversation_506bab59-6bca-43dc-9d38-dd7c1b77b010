{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyDemand\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyDemand\\detail\\index.vue", "mtime": 1750311963050}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_demand", "_oss", "_auth", "_store", "_zhm", "_cryptoJs", "id", "secret<PERSON>ey", "_default", "exports", "default", "name", "dicts", "components", "UserMenu", "data", "isDetail", "title", "imageUrl", "actionUrl", "uploadUrl", "headers", "Authorization", "getToken", "process", "env", "VUE_APP_BASE_API", "accept", "isCreate", "imgVisible", "user", "tel", "store", "getters", "companyName", "bussinessNo", "phonenumber", "keywords", "applicationsInput", "info", "form", "accountLicenceList", "rules", "demandTitle", "required", "message", "trigger", "demandType", "applicationArea", "displayRestrictions", "summary", "contactsName", "contactsMobile", "created", "$route", "query", "type", "goCreate", "getDetail", "methods", "initForm", "scenePicture", "scenePictureList", "auditStatus", "displayStatus", "publisherName", "publisherMobile", "undefined", "_this", "getDemandDetail", "then", "response", "key", "CryptoJS", "enc", "Utf8", "parse", "decrypt", "AES", "mode", "ECB", "padding", "pad", "Pkcs7", "JSON", "stringify", "total", "goBack", "$router", "go", "getUrl", "str", "list", "length", "url", "handleSummaryClose", "tag", "splice", "indexOf", "handleBeforeUpload", "file", "size", "typeList", "split", "map", "item", "trim", "toLowerCase", "substr", "dotIndex", "lastIndexOf", "$message", "error", "suffix", "substring", "handlePictureCardPreview", "handleRemove", "fileList", "handleSuccess", "code", "push", "changeMode", "toString", "businessNo", "handleFilePreview", "window", "open", "displayRestrictionChanged", "res", "_this2", "dict", "display_restrictions", "for<PERSON>ach", "label", "value", "submitForm", "_this3", "$refs", "validate", "valid", "join", "createDemand", "_objectSpread2", "isSubmit", "$modal", "msgSuccess", "edit<PERSON><PERSON><PERSON>", "toZiyuan", "path", "handleKeywordList", "_this4", "keywordList", "msg", "warning", "handleApplicationRemove", "application", "handleApplicationSuccess", "applicationName", "handleAccountRemove", "accountLicence", "handleAccountSuccess", "accountLicenceName"], "sources": ["src/views/system/user/companyDemand/detail/index.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-11 15:20:15\r\n * @LastEditTime: 2023-02-28 08:48:59\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-11 15:18:41\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"company-demand-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">{{ this.title }}</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                基本信息\r\n                <el-button\r\n                  plain\r\n                  type=\"primary\"\r\n                  style=\"position: absolute; right: 0\"\r\n                  @click=\"toZiyuan\"\r\n                  >查看平台匹配资源</el-button\r\n                >\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 需求标题 </template>\r\n                  {{ info.demandTitle }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 需求描述 </template>\r\n                  {{ info.summary }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 应用领域</template>\r\n                  {{ info.applicationArea }}\r\n                </el-descriptions-item>\r\n\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 需求封面 </template>\r\n                  <el-image\r\n                    style=\"width: 90px; height: 64px\"\r\n                    :src=\"getUrl(info.scenePicture)\"\r\n                  ></el-image>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <div class=\"header-small mt_40\">\r\n                <div class=\"red-tag\"></div>\r\n                联系信息\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 公司名称 </template>\r\n                  {{ info.companyName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系人 </template>\r\n                  {{ info.contactsName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系电话</template>\r\n                  {{ info.contactsMobile }}\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <el-image\r\n                class=\"status_approving\"\r\n                v-if=\"info.status == '1'\"\r\n                style=\"width: 120px; height: 102px\"\r\n                :src=\"require('@/assets/user/status_approving.png')\"\r\n              ></el-image>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"goBack\">返回</el-button>\r\n                <el-button type=\"danger\" @click=\"changeMode\">编辑</el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"edit-page\" v-else>\r\n              <el-form\r\n                ref=\"form\"\r\n                :model=\"form\"\r\n                :rules=\"rules\"\r\n                label-position=\"top\"\r\n              >\r\n                <el-form-item label=\"需求标题\" prop=\"demandTitle\">\r\n                  <el-input v-model=\"form.demandTitle\" placeholder=\"请输入\" />\r\n                </el-form-item>\r\n                <el-form-item prop=\"demandType\">\r\n                  <div class=\"label-item\" slot=\"label\">\r\n                    <span>需求类型</span>\r\n                    <span class=\"extra\"\r\n                      >（可按需求产品+应用行业+应用领域进行描述）</span\r\n                    >\r\n                  </div>\r\n                  <el-checkbox-group\r\n                    v-model=\"form.demandType\"\r\n                    placeholder=\"请选择\"\r\n                    clearable\r\n                  >\r\n                    <el-checkbox\r\n                      v-for=\"dict in dict.type.demand_type\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.value\"\r\n                      :value=\"dict.value\"\r\n                      >{{ dict.label }}</el-checkbox\r\n                    >\r\n                  </el-checkbox-group>\r\n                </el-form-item>\r\n                <el-form-item label=\"需求描述\" prop=\"summary\">\r\n                  <el-input\r\n                    v-model=\"form.summary\"\r\n                    type=\"textarea\"\r\n                    :rows=\"2\"\r\n                    :maxlength=\"500\"\r\n                    placeholder=\"请输入\"\r\n                  />\r\n                  <div class=\"extra-content\">\r\n                    <div class=\"extra-content-header\">\r\n                      <el-button\r\n                        @click=\"handleKeywordList\"\r\n                        size=\"small\"\r\n                        type=\"primary\"\r\n                        >生成关键词</el-button\r\n                      >\r\n                      <span class=\"tip\">生成关键词有利于实现精准匹配哦！</span>\r\n                    </div>\r\n                    <div\r\n                      v-if=\"form.keywords && form.keywords.length > 0\"\r\n                      class=\"extra-content-body\"\r\n                    >\r\n                      <el-tag\r\n                        :key=\"`${tag}_${index}`\"\r\n                        v-for=\"(tag, index) in form.keywords\"\r\n                        closable\r\n                        size=\"small\"\r\n                        disable-transitions\r\n                        @close=\"handleSummaryClose(tag)\"\r\n                      >\r\n                        {{ tag }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n                <el-form-item label=\"应用领域\" prop=\"applicationArea\">\r\n                  <el-select\r\n                    v-model=\"form.applicationArea\"\r\n                    filterable\r\n                    multiple\r\n                    allow-create\r\n                    style=\"width: 100%\"\r\n                    placeholder=\"请选择\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in dict.type.application_area\"\r\n                      :key=\"item.value\"\r\n                      :label=\"item.label\"\r\n                      :value=\"item.label\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                  <!-- <el-tag\r\n                    v-for=\"tag in form.applicationAreaList\"\r\n                    closable\r\n                    class=\"add-demand-tag\"\r\n                    :key=\"tag\"\r\n                    :disable-transitions=\"false\"\r\n                    @close=\"handleClose(tag)\"\r\n                  >\r\n                    {{ tag }}\r\n                  </el-tag>\r\n                  <el-input v-model=\"applicationsInput\" :maxlength=\"255\">\r\n                  </el-input>\r\n                  <el-button\r\n                    size=\"small\"\r\n                    icon=\"el-icon-plus\"\r\n                    class=\"add-demand-btn-tag\"\r\n                    @click=\"handleInputConfirm\"\r\n                    >新增</el-button\r\n                  > -->\r\n                </el-form-item>\r\n                <el-form-item label=\"产品图片\">\r\n                  <el-upload\r\n                    list-type=\"picture-card\"\r\n                    :headers=\"headers\"\r\n                    :action=\"uploadUrl\"\r\n                    :file-list=\"form.scenePictureList\"\r\n                    :accept=\"accept\"\r\n                    :before-upload=\"handleBeforeUpload\"\r\n                    :on-preview=\"handlePictureCardPreview\"\r\n                    :on-remove=\"handleRemove\"\r\n                    :on-success=\"handleSuccess\"\r\n                  >\r\n                    <i class=\"el-icon-plus\"></i>\r\n                  </el-upload>\r\n                  <el-dialog\r\n                    append-to-body\r\n                    :visible.sync=\"imgVisible\"\r\n                    :close-on-click-modal=\"false\"\r\n                  >\r\n                    <img v-if=\"imageUrl\" width=\"100%\" :src=\"imageUrl\" alt=\"\" />\r\n                  </el-dialog>\r\n                </el-form-item>\r\n                <el-form-item label=\"展示限制\" prop=\"displayRestrictions\">\r\n                  <el-select\r\n                    v-model=\"form.displayRestrictions\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100%\"\r\n                    clearable\r\n                  >\r\n                    <el-option\r\n                      v-for=\"dict in dict.type.display_restrictions\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.label\"\r\n                      :value=\"dict.value\"\r\n                    />\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n                      <el-input\r\n                        disabled\r\n                        v-model=\"form.companyName\"\r\n                        placeholder=\"请输入公司名称\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"联系人\" prop=\"contactsName\">\r\n                      <el-input\r\n                        disabled\r\n                        v-model=\"form.contactsName\"\r\n                        placeholder=\"请输入联系人\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"联系电话\" prop=\"contactsMobile\">\r\n                      <el-input\r\n                        disabled\r\n                        v-model=\"form.contactsMobile\"\r\n                        placeholder=\"请输入联系电话\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"changeMode\">返回</el-button>\r\n                <!-- <el-button type=\"error\" @click=\"changeMode(0)\"\r\n                  >暂存草稿</el-button\r\n                > -->\r\n                <el-button type=\"danger\" @click=\"submitForm(1)\"\r\n                  >提交审核</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport { getDemandDetail, createDemand, editDemand } from \"@/api/system/demand\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport store from \"@/store\";\r\nimport { demandAdd, keywordList } from \"@/api/zhm\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\r\n    \"demand_type\",\r\n    \"affiliated_unit\",\r\n    \"capital_source\",\r\n    \"affiliated_street\",\r\n    \"display_restrictions\",\r\n    \"application_area\",\r\n  ],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      title: \"需求详情\",\r\n      imageUrl: \"\",\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      isCreate: false,\r\n      imgVisible: false,\r\n      user: {\r\n        tel: store.getters.tel,\r\n        name: store.getters.name,\r\n        companyName: store.getters.companyName,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        phonenumber: store.getters.phonenumber,\r\n      },\r\n      keywords: [],\r\n      applicationsInput: \"\",\r\n      info: {},\r\n\r\n      form: {},\r\n      accountLicenceList: [],\r\n\r\n      // 表单校验\r\n      rules: {\r\n        demandTitle: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" },\r\n        ],\r\n        demandType: [\r\n          { required: true, message: \"请选择需求类型\", trigger: \"change\" },\r\n        ],\r\n        applicationArea: [\r\n          { required: true, message: \"请选择应用领域\", trigger: \"change\" },\r\n        ],\r\n        displayRestrictions: [\r\n          { required: true, message: \"请选择展示限制\", trigger: \"change\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"需求描述不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.isCreate = this.$route.query.type == 1;\r\n    if (this.isCreate) {\r\n      this.goCreate();\r\n    } else {\r\n      this.getDetail();\r\n    }\r\n  },\r\n  methods: {\r\n    initForm() {\r\n      this.form = {\r\n        demandType: [],\r\n        applicationArea: [],\r\n        scenePicture: [],\r\n        // applicationAreaList: [],\r\n        scenePictureList: [],\r\n        keywords: [],\r\n        auditStatus: \"1\",\r\n        displayStatus: \"2\",\r\n        publisherName: this.user.name,\r\n        publisherMobile: this.user.tel,\r\n        // 展示限制\r\n        displayRestrictions: undefined,\r\n      };\r\n    },\r\n    getDetail() {\r\n      let id = this.$route.query.id;\r\n      getDemandDetail(id).then((response) => {\r\n        let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n        let decrypt = CryptoJS.AES.decrypt(response, key, {\r\n          mode: CryptoJS.mode.ECB,\r\n          padding: CryptoJS.pad.Pkcs7,\r\n        });\r\n        response = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    getUrl(str) {\r\n      if (str && str != null) {\r\n        var list = JSON.parse(str);\r\n        if (list && list.length > 0) {\r\n          return list[0].url;\r\n        }\r\n      }\r\n\r\n      return null;\r\n    },\r\n    // // 应用领域新增\r\n    // handleInputConfirm() {\r\n    //   let val = this.applicationsInput;\r\n    //   if (val) {\r\n    //     this.form.applicationAreaList.push(val);\r\n    //   }\r\n    //   this.applicationsInput = \"\";\r\n    // },\r\n    // // 应用领域移除\r\n    // handleClose(tag) {\r\n    //   this.form.applicationAreaList.splice(\r\n    //     this.form.applicationAreaList.indexOf(tag),\r\n    //     1\r\n    //   );\r\n    // },\r\n    handleSummaryClose(tag) {\r\n      this.form.keywords.splice(this.form.keywords.indexOf(tag), 1);\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePictureCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.form.scenePictureList = fileList;\r\n    },\r\n    handleSuccess(response, file) {\r\n      if (response.code == 200) {\r\n        if (this.form.scenePictureList == null) {\r\n          this.form.scenePictureList = [];\r\n        }\r\n        this.form.scenePictureList.push(response.data);\r\n      }\r\n    },\r\n    changeMode() {\r\n      if (this.isCreate) {\r\n        this.goBack();\r\n        return;\r\n      }\r\n      if (this.isDetail) {\r\n        this.title = \"编辑需求\";\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n        if (this.info.applicationArea) {\r\n          this.form.applicationArea = this.info.applicationArea.split(\",\");\r\n        } else {\r\n          this.form.applicationArea = [];\r\n        }\r\n        if (this.info.displayRestrictions) {\r\n          this.form.displayRestrictions =\r\n            this.info.displayRestrictions.toString();\r\n        }\r\n        if (this.info.keywords) {\r\n          this.form.keywords = this.info.keywords.split(\",\");\r\n        }\r\n        if (this.info.demandType) {\r\n          this.form.demandType = this.info.demandType.split(\",\");\r\n        }\r\n        if (this.info.scenePicture && this.info.scenePicture != \"null\") {\r\n          this.form.scenePictureList = JSON.parse(this.info.scenePicture);\r\n        } else {\r\n          this.form.scenePictureList = [];\r\n        }\r\n      } else {\r\n        this.isDetail = true;\r\n        this.title = \"需求详情\";\r\n        this.initForm();\r\n        this.getDetail();\r\n      }\r\n    },\r\n    goCreate() {\r\n      this.title = \"新增需求\";\r\n      this.isDetail = false;\r\n      this.initForm();\r\n      this.form.companyName = this.user.companyName;\r\n      this.form.contactsName = this.user.name;\r\n      this.form.contactsMobile = this.user.phonenumber;\r\n      this.form.publisherName = this.user.name;\r\n      this.form.publisherMobile = this.user.phonenumber;\r\n      this.form.businessNo = this.user.bussinessNo;\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    displayRestrictionChanged(res) {\r\n      this.dict.type.display_restrictions.forEach((item) => {\r\n        if (item.label == res) {\r\n          this.form.displayRestrictions = item.value;\r\n        }\r\n      });\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          // if (\r\n          //   this.form.applicationAreaList &&\r\n          //   this.form.applicationAreaList.length > 0\r\n          // ) {\r\n          //   this.form.applicationArea = this.form.applicationAreaList.join(\",\");\r\n          // } else {\r\n          //   this.form.applicationArea = \"\";\r\n          // }\r\n          if (this.form.applicationArea.length > 0) {\r\n            this.form.applicationArea = this.form.applicationArea.join();\r\n          }\r\n          this.form.scenePicture = JSON.stringify(this.form.scenePictureList);\r\n          this.form.businessNo = this.user.bussinessNo;\r\n          if (this.form.keywords && this.form.keywords.length > 0) {\r\n            this.form.keywords = this.form.keywords.join(\",\");\r\n          } else {\r\n            this.form.keywords = \"\";\r\n          }\r\n          if (this.form.demandType.length > 0) {\r\n            this.form.demandType = this.form.demandType.join();\r\n          }\r\n          if (this.isCreate) {\r\n            createDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          } else {\r\n            this.form.auditStatus = 1;\r\n            editDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    toZiyuan() {\r\n      this.$router.push({\r\n        path: \"/user/companyApplyDetail1\",\r\n        query: { key: JSON.stringify(this.info) },\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      const { summary } = this.form;\r\n      if (summary) {\r\n        keywordList(summary).then((res) => {\r\n          const { code, data, msg } = res;\r\n          if (code === 200) {\r\n            this.form.keywords = data;\r\n          } else {\r\n            this.$message.error(msg);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning(\"请输入需求描述\");\r\n      }\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.application = res.data.url;\r\n        this.form.applicationName = res.data.name;\r\n      }\r\n    },\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .company-demand-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 36px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .mt_40 {\r\n          margin-top: 40px;\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 90%;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-textarea__inner {\r\n          width: 90%;\r\n        }\r\n        .add-demand-tag {\r\n          margin-right: 10px;\r\n          height: 32px;\r\n          line-height: 32px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n          color: #333;\r\n        }\r\n        .el-button:hover,\r\n        .el-button:focus {\r\n          border-color: #d9d9d9;\r\n          background-color: #fff;\r\n        }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8 !important;\r\n          border-color: #21c9b8 !important;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .el-input__suffix {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 11%;\r\n}\r\n.el-checkbox {\r\n  font-size: 14px;\r\n  font-weight: 400;\r\n  color: #262626;\r\n  line-height: 18px;\r\n  margin-right: 28px;\r\n}\r\n.edit-page {\r\n  padding-left: 50px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AA8RA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,IAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,IAAA,GAAAL,OAAA;AACA,IAAAM,SAAA,GAAAP,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARA,IAAAO,EAAA;AASA,IAAAC,SAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,KAAA,GACA,eACA,mBACA,kBACA,qBACA,wBACA,mBACA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,KAAA;MACAC,QAAA;MACAC,SAAA,MAAAC,cAAA;MACAC,OAAA;QAAAC,aAAA,kBAAAC,cAAA;MAAA;MACAH,SAAA,EAAAI,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,MAAA;MACAC,QAAA;MACAC,UAAA;MACAC,IAAA;QACAC,GAAA,EAAAC,cAAA,CAAAC,OAAA,CAAAF,GAAA;QACApB,IAAA,EAAAqB,cAAA,CAAAC,OAAA,CAAAtB,IAAA;QACAuB,WAAA,EAAAF,cAAA,CAAAC,OAAA,CAAAC,WAAA;QACAC,WAAA,EAAAH,cAAA,CAAAC,OAAA,CAAAE,WAAA;QACAC,WAAA,EAAAJ,cAAA,CAAAC,OAAA,CAAAG;MACA;MACAC,QAAA;MACAC,iBAAA;MACAC,IAAA;MAEAC,IAAA;MACAC,kBAAA;MAEA;MACAC,KAAA;QACAC,WAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,UAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,eAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,mBAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,OAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,YAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,WAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAM,cAAA,GACA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAO,OAAA,WAAAA,QAAA;IACA,KAAAzB,QAAA,QAAA0B,MAAA,CAAAC,KAAA,CAAAC,IAAA;IACA,SAAA5B,QAAA;MACA,KAAA6B,QAAA;IACA;MACA,KAAAC,SAAA;IACA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAApB,IAAA;QACAO,UAAA;QACAC,eAAA;QACAa,YAAA;QACA;QACAC,gBAAA;QACAzB,QAAA;QACA0B,WAAA;QACAC,aAAA;QACAC,aAAA,OAAAnC,IAAA,CAAAnB,IAAA;QACAuD,eAAA,OAAApC,IAAA,CAAAC,GAAA;QACA;QACAkB,mBAAA,EAAAkB;MACA;IACA;IACAT,SAAA,WAAAA,UAAA;MAAA,IAAAU,KAAA;MACA,IAAA9D,EAAA,QAAAgD,MAAA,CAAAC,KAAA,CAAAjD,EAAA;MACA,IAAA+D,uBAAA,EAAA/D,EAAA,EAAAgE,IAAA,WAAAC,QAAA;QACA,IAAAC,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAArE,SAAA;QACA,IAAAsE,OAAA,GAAAJ,iBAAA,CAAAK,GAAA,CAAAD,OAAA,CAAAN,QAAA,EAAAC,GAAA;UACAO,IAAA,EAAAN,iBAAA,CAAAM,IAAA,CAAAC,GAAA;UACAC,OAAA,EAAAR,iBAAA,CAAAS,GAAA,CAAAC;QACA;QACAZ,QAAA,GAAAa,IAAA,CAAAR,KAAA,CAAAH,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAU,SAAA,CAAAR,OAAA;QACAT,KAAA,CAAA7B,IAAA,GAAAgC,QAAA,CAAAxD,IAAA;QACAqD,KAAA,CAAAkB,KAAA,GAAAf,QAAA,CAAAe,KAAA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,MAAA,WAAAA,OAAAC,GAAA;MACA,IAAAA,GAAA,IAAAA,GAAA;QACA,IAAAC,IAAA,GAAAR,IAAA,CAAAR,KAAA,CAAAe,GAAA;QACA,IAAAC,IAAA,IAAAA,IAAA,CAAAC,MAAA;UACA,OAAAD,IAAA,IAAAE,GAAA;QACA;MACA;MAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,GAAA;MACA,KAAAxD,IAAA,CAAAH,QAAA,CAAA4D,MAAA,MAAAzD,IAAA,CAAAH,QAAA,CAAA6D,OAAA,CAAAF,GAAA;IACA;IACA;IACAG,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAzF,IAAA,GAAAyF,IAAA,CAAAzF,IAAA;QAAA6C,IAAA,GAAA4C,IAAA,CAAA5C,IAAA;QAAA6C,IAAA,GAAAD,IAAA,CAAAC,IAAA;MACA,IAAAC,QAAA,QAAA3E,MAAA,CACA4E,KAAA,MACAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA,GAAAC,WAAA,GAAAC,MAAA;MAAA;MACA,IAAAC,QAAA,GAAAlG,IAAA,CAAAmG,WAAA;MACA;MACA,IAAAD,QAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;QACA,IAAAC,MAAA,GAAAtG,IAAA,CAAAuG,SAAA,CAAAL,QAAA;QACA,IAAAP,QAAA,CAAAJ,OAAA,CAAAe,MAAA,CAAAN,WAAA;UACA,KAAAI,QAAA,CAAAC,KAAA;UACA;QACA;MACA;MACA;MACA,IAAAX,IAAA;QACA,KAAAU,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACA;IACAG,wBAAA,WAAAA,yBAAAf,IAAA;MACA,KAAAlF,QAAA,GAAAkF,IAAA,CAAAN,GAAA;MACA,KAAAjE,UAAA;IACA;IACA;IACAuF,YAAA,WAAAA,aAAAhB,IAAA,EAAAiB,QAAA;MACA,KAAA7E,IAAA,CAAAsB,gBAAA,GAAAuD,QAAA;IACA;IACAC,aAAA,WAAAA,cAAA/C,QAAA,EAAA6B,IAAA;MACA,IAAA7B,QAAA,CAAAgD,IAAA;QACA,SAAA/E,IAAA,CAAAsB,gBAAA;UACA,KAAAtB,IAAA,CAAAsB,gBAAA;QACA;QACA,KAAAtB,IAAA,CAAAsB,gBAAA,CAAA0D,IAAA,CAAAjD,QAAA,CAAAxD,IAAA;MACA;IACA;IACA0G,UAAA,WAAAA,WAAA;MACA,SAAA7F,QAAA;QACA,KAAA2D,MAAA;QACA;MACA;MACA,SAAAvE,QAAA;QACA,KAAAC,KAAA;QACA,KAAAD,QAAA;QACA,KAAAwB,IAAA,QAAAD,IAAA;QACA,SAAAA,IAAA,CAAAS,eAAA;UACA,KAAAR,IAAA,CAAAQ,eAAA,QAAAT,IAAA,CAAAS,eAAA,CAAAuD,KAAA;QACA;UACA,KAAA/D,IAAA,CAAAQ,eAAA;QACA;QACA,SAAAT,IAAA,CAAAU,mBAAA;UACA,KAAAT,IAAA,CAAAS,mBAAA,GACA,KAAAV,IAAA,CAAAU,mBAAA,CAAAyE,QAAA;QACA;QACA,SAAAnF,IAAA,CAAAF,QAAA;UACA,KAAAG,IAAA,CAAAH,QAAA,QAAAE,IAAA,CAAAF,QAAA,CAAAkE,KAAA;QACA;QACA,SAAAhE,IAAA,CAAAQ,UAAA;UACA,KAAAP,IAAA,CAAAO,UAAA,QAAAR,IAAA,CAAAQ,UAAA,CAAAwD,KAAA;QACA;QACA,SAAAhE,IAAA,CAAAsB,YAAA,SAAAtB,IAAA,CAAAsB,YAAA;UACA,KAAArB,IAAA,CAAAsB,gBAAA,GAAAsB,IAAA,CAAAR,KAAA,MAAArC,IAAA,CAAAsB,YAAA;QACA;UACA,KAAArB,IAAA,CAAAsB,gBAAA;QACA;MACA;QACA,KAAA9C,QAAA;QACA,KAAAC,KAAA;QACA,KAAA2C,QAAA;QACA,KAAAF,SAAA;MACA;IACA;IACAD,QAAA,WAAAA,SAAA;MACA,KAAAxC,KAAA;MACA,KAAAD,QAAA;MACA,KAAA4C,QAAA;MACA,KAAApB,IAAA,CAAAN,WAAA,QAAAJ,IAAA,CAAAI,WAAA;MACA,KAAAM,IAAA,CAAAW,YAAA,QAAArB,IAAA,CAAAnB,IAAA;MACA,KAAA6B,IAAA,CAAAY,cAAA,QAAAtB,IAAA,CAAAM,WAAA;MACA,KAAAI,IAAA,CAAAyB,aAAA,QAAAnC,IAAA,CAAAnB,IAAA;MACA,KAAA6B,IAAA,CAAA0B,eAAA,QAAApC,IAAA,CAAAM,WAAA;MACA,KAAAI,IAAA,CAAAmF,UAAA,QAAA7F,IAAA,CAAAK,WAAA;IACA;IACAyF,iBAAA,WAAAA,kBAAAxB,IAAA;MACAyB,MAAA,CAAAC,IAAA,CAAA1B,IAAA;IACA;IACA2B,yBAAA,WAAAA,0BAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,IAAA,CAAA1E,IAAA,CAAA2E,oBAAA,CAAAC,OAAA,WAAA3B,IAAA;QACA,IAAAA,IAAA,CAAA4B,KAAA,IAAAL,GAAA;UACAC,MAAA,CAAAzF,IAAA,CAAAS,mBAAA,GAAAwD,IAAA,CAAA6B,KAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA/E,IAAA;MAAA,IAAAgF,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IAAAH,MAAA,CAAAhG,IAAA,CAAAQ,eAAA,CAAA6C,MAAA;YACA2C,MAAA,CAAAhG,IAAA,CAAAQ,eAAA,GAAAwF,MAAA,CAAAhG,IAAA,CAAAQ,eAAA,CAAA4F,IAAA;UACA;UACAJ,MAAA,CAAAhG,IAAA,CAAAqB,YAAA,GAAAuB,IAAA,CAAAC,SAAA,CAAAmD,MAAA,CAAAhG,IAAA,CAAAsB,gBAAA;UACA0E,MAAA,CAAAhG,IAAA,CAAAmF,UAAA,GAAAa,MAAA,CAAA1G,IAAA,CAAAK,WAAA;UACA,IAAAqG,MAAA,CAAAhG,IAAA,CAAAH,QAAA,IAAAmG,MAAA,CAAAhG,IAAA,CAAAH,QAAA,CAAAwD,MAAA;YACA2C,MAAA,CAAAhG,IAAA,CAAAH,QAAA,GAAAmG,MAAA,CAAAhG,IAAA,CAAAH,QAAA,CAAAuG,IAAA;UACA;YACAJ,MAAA,CAAAhG,IAAA,CAAAH,QAAA;UACA;UACA,IAAAmG,MAAA,CAAAhG,IAAA,CAAAO,UAAA,CAAA8C,MAAA;YACA2C,MAAA,CAAAhG,IAAA,CAAAO,UAAA,GAAAyF,MAAA,CAAAhG,IAAA,CAAAO,UAAA,CAAA6F,IAAA;UACA;UACA,IAAAJ,MAAA,CAAA5G,QAAA;YACA,IAAAiH,oBAAA,MAAAC,cAAA,CAAApI,OAAA,MAAAoI,cAAA,CAAApI,OAAA,MAAA8H,MAAA,CAAAhG,IAAA;cAAAuG,QAAA,EAAAvF;YAAA,IAAAc,IAAA,WAAAC,QAAA;cACAiE,MAAA,CAAAQ,MAAA,CAAAC,UAAA;cACAT,MAAA,CAAAf,UAAA;YACA;UACA;YACAe,MAAA,CAAAhG,IAAA,CAAAuB,WAAA;YACA,IAAAmF,kBAAA,MAAAJ,cAAA,CAAApI,OAAA,MAAAoI,cAAA,CAAApI,OAAA,MAAA8H,MAAA,CAAAhG,IAAA;cAAAuG,QAAA,EAAAvF;YAAA,IAAAc,IAAA,WAAAC,QAAA;cACAiE,MAAA,CAAAQ,MAAA,CAAAC,UAAA;cACAT,MAAA,CAAAf,UAAA;YACA;UACA;QACA;MACA;IACA;IACA0B,QAAA,WAAAA,SAAA;MACA,KAAA3D,OAAA,CAAAgC,IAAA;QACA4B,IAAA;QACA7F,KAAA;UAAAiB,GAAA,EAAAY,IAAA,CAAAC,SAAA,MAAA9C,IAAA;QAAA;MACA;IACA;IACA8G,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAApG,OAAA,QAAAV,IAAA,CAAAU,OAAA;MACA,IAAAA,OAAA;QACA,IAAAqG,gBAAA,EAAArG,OAAA,EAAAoB,IAAA,WAAA0D,GAAA;UACA,IAAAT,IAAA,GAAAS,GAAA,CAAAT,IAAA;YAAAxG,IAAA,GAAAiH,GAAA,CAAAjH,IAAA;YAAAyI,GAAA,GAAAxB,GAAA,CAAAwB,GAAA;UACA,IAAAjC,IAAA;YACA+B,MAAA,CAAA9G,IAAA,CAAAH,QAAA,GAAAtB,IAAA;UACA;YACAuI,MAAA,CAAAvC,QAAA,CAAAC,KAAA,CAAAwC,GAAA;UACA;QACA;MACA;QACA,KAAAzC,QAAA,CAAA0C,OAAA;MACA;IACA;IACAC,uBAAA,WAAAA,wBAAAtD,IAAA,EAAAiB,QAAA;MACA,KAAA7E,IAAA,CAAAmH,WAAA;IACA;IACAC,wBAAA,WAAAA,yBAAA5B,GAAA,EAAA5B,IAAA,EAAAiB,QAAA;MACA;MACA,IAAAW,GAAA,CAAAT,IAAA;QACA,KAAA/E,IAAA,CAAAmH,WAAA,GAAA3B,GAAA,CAAAjH,IAAA,CAAA+E,GAAA;QACA,KAAAtD,IAAA,CAAAqH,eAAA,GAAA7B,GAAA,CAAAjH,IAAA,CAAAJ,IAAA;MACA;IACA;IACAmJ,mBAAA,WAAAA,oBAAA1D,IAAA,EAAAiB,QAAA;MACA,KAAA7E,IAAA,CAAAuH,cAAA;IACA;IACAC,oBAAA,WAAAA,qBAAAhC,GAAA,EAAA5B,IAAA,EAAAiB,QAAA;MACA;MACA,IAAAW,GAAA,CAAAT,IAAA;QACA,KAAA/E,IAAA,CAAAuH,cAAA,GAAA/B,GAAA,CAAAjH,IAAA,CAAA+E,GAAA;QACA,KAAAtD,IAAA,CAAAyH,kBAAA,GAAAjC,GAAA,CAAAjH,IAAA,CAAAJ,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}