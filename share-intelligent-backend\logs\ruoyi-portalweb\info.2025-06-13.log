17:11:28.143 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
17:11:28.272 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:11:28.801 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:11:28.801 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:11:33.804 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
17:11:39.978 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9212"]
17:11:39.984 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:11:39.984 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
17:11:40.437 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:11:42.741 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
17:11:42.748 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
17:11:42.748 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:11:49.852 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getVerifier,69] - 获取签名验证器
17:11:53.166 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-13T09:11:53.166Z
17:11:53.188 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayClient,91] - 获取httpClient
17:11:53.457 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-13T09:11:53.457Z
17:11:54.866 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:11:58.355 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayNoSignClient,114] - == getWxPayNoSignClient END ==
17:12:02.929 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9212"]
17:12:03.036 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
17:12:03.037 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
17:12:03.280 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-portalweb ************:9212 register finished
17:12:06.390 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStarted,61] - Started RuoYiPortalwebApplication in 40.282 seconds (JVM running for 41.956)
17:12:06.452 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb.yml, group=DEFAULT_GROUP
17:12:06.453 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb-prod.yml, group=DEFAULT_GROUP
17:12:06.454 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb, group=DEFAULT_GROUP
17:12:07.645 [RMI TCP Connection(15)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:11:53.182 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-13T10:11:53.182Z
18:11:53.500 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-13T10:11:53.500Z
