{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\newsCenter.vue?vue&type=style&index=0&id=66d67d4e&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\newsCenter.vue", "mtime": 1750311962934}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["newsCenter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "newsCenter.vue", "sourceRoot": "src/views/components/home", "sourcesContent": ["<template>\r\n  <div\r\n    class=\"card-container wow animate__animated animate__fadeInUp\"\r\n    data-wow-duration=\"1s\"\r\n  >\r\n    <div class=\"enterpriseTitle\">\r\n      <div>新闻中心</div>\r\n      <div class=\"allEnterprise\" @click=\"goNewsCenter\">查看全部>></div>\r\n    </div>\r\n    <div class=\"content\" v-loading=\"loading\">\r\n      <div class=\"content_left\">\r\n        <div\r\n          class=\"platDynamics\"\r\n          :class=\"flag === '1' ? 'platDyHover' : ''\"\r\n          @click=\"getFlag('1')\"\r\n        >\r\n          <div class=\"platImg\">\r\n            <img\r\n              v-show=\"flag !== '1'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-show=\"flag === '1'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div class=\"platTitle\">平台动态</div>\r\n        </div>\r\n        <div\r\n          class=\"platDynamics\"\r\n          :class=\"flag === '2' ? 'platDyHover' : ''\"\r\n          @click=\"getFlag('2')\"\r\n        >\r\n          <div class=\"platImg\">\r\n            <img\r\n              v-show=\"flag !== '2'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-show=\"flag === '2'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div class=\"platTitle\">行业动态</div>\r\n        </div>\r\n        <div\r\n          class=\"platDynamics\"\r\n          :class=\"flag === '3' ? 'platDyHover' : ''\"\r\n          @click=\"getFlag('3')\"\r\n        >\r\n          <div class=\"platImg\">\r\n            <img\r\n              v-show=\"flag !== '3'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-show=\"flag === '3'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div class=\"platTitle\">政策法规</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content_middle\">\r\n        <div class=\"newsImg\">\r\n          <img src=\"../../../assets/images/home/<USER>\" alt=\"\" />\r\n        </div>\r\n        <div\r\n          class=\"newsContent\"\r\n          v-if=\"newsFirstData.updateTime\"\r\n          @click=\"goDetail(newsFirstData.id)\"\r\n        >\r\n          <div class=\"news_left\">\r\n            <div class=\"news_year\">\r\n              {{ newsFirstData.updateTime.slice(0, 7) }}\r\n            </div>\r\n            <div class=\"news_day\">\r\n              {{ newsFirstData.updateTime.slice(8, 10) }}\r\n            </div>\r\n          </div>\r\n          <div class=\"news_right\">\r\n            <div class=\"title\">{{ newsFirstData.title }}</div>\r\n            <div class=\"desc\">\r\n              {{ newsFirstData.brief }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content_right\">\r\n        <div\r\n          class=\"newsContent\"\r\n          v-for=\"item in newsRightList\"\r\n          :key=\"item.id\"\r\n          @click=\"goDetail(item.id)\"\r\n        >\r\n          <div class=\"news_left\">\r\n            <div class=\"news_year\">{{ item.updateTime.slice(0, 7) }}</div>\r\n            <div class=\"news_day\">{{ item.updateTime.slice(8, 10) }}</div>\r\n          </div>\r\n          <div class=\"news_right\">\r\n            <div class=\"title\">{{ item.title }}</div>\r\n            <div class=\"desc\">\r\n              {{ item.brief }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { newsList } from \"@/api/newsCenter\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 4,\r\n      total: 0,\r\n      flag: \"1\",\r\n      newsFirstData: {},\r\n      newsRightList: [],\r\n    };\r\n  },\r\n  created() {\r\n    // this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        kind: \"0\",\r\n        typeTop: this.flag,\r\n      };\r\n      newsList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.loading = false;\r\n          this.newsFirstData = res.rows[0];\r\n          this.newsRightList = res.rows.slice(1, 4);\r\n        }\r\n      });\r\n      // newsType().then((res) => {\r\n      //   if (res.code === 200) {\r\n      //     console.log(res, \"-----------------\");\r\n      //   }\r\n      // });\r\n      // getDicts(\"case_industry\").then((res) => {\r\n      //   const { code, data = [] } = res;\r\n      //   if (code === 200) {\r\n      //     this.caseTypeList = data;\r\n      //     this.getCaseList();\r\n      //   }\r\n      // });\r\n    },\r\n    // 跳转到详情页面\r\n    goDetail(id) {\r\n      this.$router.push({\r\n        path: \"/newsDetail\",\r\n        query: {\r\n          id,\r\n        },\r\n      });\r\n    },\r\n    goNewsCenter() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/newsCenter\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    getFlag(value) {\r\n      this.flag = value;\r\n      this.initData();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.enterpriseTitle {\r\n  width: 100%;\r\n  font-size: 36px;\r\n  text-align: center;\r\n  margin: 60px 0;\r\n  position: relative;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  color: #000000;\r\n  .allEnterprise {\r\n    position: absolute;\r\n    top: 8 px;\r\n    right: 0;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    line-height: 26px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.content {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 330px;\r\n  margin-bottom: 86px;\r\n  .content_left {\r\n    width: 300px;\r\n    height: 100%;\r\n    .platDynamics {\r\n      width: 100%;\r\n      height: 96px;\r\n      background-color: rgb(229, 247, 243);\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin-top: 21px;\r\n      cursor: pointer;\r\n      .platImg {\r\n        width: 42px;\r\n        height: 40px;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .platTitle {\r\n        font-size: 20px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #21c9b8;\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n    .platDynamics:nth-child(1) {\r\n      margin-top: 0;\r\n    }\r\n    .platDyHover {\r\n      background: #21c9b8;\r\n      .platTitle {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n  .content_middle {\r\n    width: 460px;\r\n    height: 100%;\r\n    margin: 0 20px 0 30px;\r\n  }\r\n  .content_right {\r\n    width: 390px;\r\n    height: 100%;\r\n  }\r\n  .newsImg {\r\n    width: 100%;\r\n    height: 220px;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .newsContent {\r\n    width: 100%;\r\n    height: 110px;\r\n    background: #f9f9f9;\r\n    padding: 22px 26px;\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    .news_left {\r\n      width: 100px;\r\n      font-size: 16px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #666666;\r\n      margin-right: 20px;\r\n      text-align: center;\r\n      .news_year {\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #666666;\r\n      }\r\n      .news_day {\r\n        font-size: 30px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #222222;\r\n        line-height: 26px;\r\n        margin-top: 10px;\r\n      }\r\n    }\r\n    .news_right {\r\n      width: calc(100% - 100px);\r\n      .title {\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #222222;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 1;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n      .desc {\r\n        margin-top: 13px;\r\n        font-size: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #666666;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n    }\r\n  }\r\n  .newsContent:hover {\r\n    background: rgb(235, 252, 240);\r\n    .title {\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}