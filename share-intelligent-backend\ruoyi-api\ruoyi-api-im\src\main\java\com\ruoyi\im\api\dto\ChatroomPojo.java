package com.ruoyi.im.api.dto;/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api.dto
 * @ClassName: ImChatroomMsgVO
 * @Author: ${maguo<PERSON>}
 * @Description:
 * @Date: 2022/3/23 17:21
 * @Version: 1.0
 */

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * @program: ruoyi
 *
 * @description:
 *
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 *
 * @create: 2022-03-23 17:21
 **/
@Data
public class ChatroomPojo {

    private String chatroomId;//聊天室ID

    private Integer number;

    private String chatroomName;//聊天室名称

    private String module;//模块

    private JSONObject msg;

    private String msgtype;

    private String create_time;
}
