{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\IframeToggle\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\IframeToggle\\index.vue", "mtime": 1750311962844}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9pbmRleCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vSW5uZXJMaW5rL2luZGV4IikpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgY29tcG9uZW50czogewogICAgSW5uZXJMaW5rOiBfaW5kZXguZGVmYXVsdAogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGlmcmFtZVZpZXdzOiBmdW5jdGlvbiBpZnJhbWVWaWV3cygpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnRhZ3NWaWV3LmlmcmFtZVZpZXdzOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "components", "InnerLink", "computed", "iframeViews", "$store", "state", "tagsView"], "sources": ["src/layout/components/IframeToggle/index.vue"], "sourcesContent": ["<template>\r\n  <transition-group name=\"fade-transform\" mode=\"out-in\">\r\n    <inner-link\r\n      v-for=\"(item, index) in iframeViews\"\r\n      :key=\"item.path\"\r\n      :iframeId=\"'iframe' + index\"\r\n      v-show=\"$route.path === item.path\"\r\n      :src=\"item.meta.link\"\r\n    ></inner-link>\r\n  </transition-group>\r\n</template>\r\n\r\n<script>\r\nimport InnerLink from \"../InnerLink/index\"\r\n\r\nexport default {\r\n  components: { InnerLink },\r\n  computed: {\r\n    iframeViews() {\r\n      return this.$store.state.tagsView.iframeViews\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;AAaA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;iCAEA;EACAC,UAAA;IAAAC,SAAA,EAAAA;EAAA;EACAC,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,WAAA;IACA;EACA;AACA", "ignoreList": []}]}