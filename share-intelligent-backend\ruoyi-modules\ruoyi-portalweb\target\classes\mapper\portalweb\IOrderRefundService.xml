<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.BuMemberOnlineRefundMapper">
    
    <resultMap type="BuMemberOnlineRefund" id="BuMemberOnlineRefundResult">
        <result property="id"    column="id"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="memberId"    column="member_id"    />
        <result property="refundOrderNo"    column="refund_order_no"    />
        <result property="payOrderNo"    column="pay_order_no"    />
        <result property="onlinePayStyle"    column="online_pay_style"    />
        <result property="money"    column="money"    />
        <result property="appStoreOrderNo"    column="app_store_order_no"    />
        <result property="refundStatus"    column="refund_status"    />
        <result property="successTime"    column="success_time"    />
        <result property="refundRecvAccout"    column="refund_recv_accout"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDept"    column="create_dept"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="isDeleted"    column="is_deleted"    />
        <result property="version"    column="version"    />
    </resultMap>

    <sql id="selectBuMemberOnlineRefundVo">
        select id, tenant_id, customer_id, member_id, refund_order_no, pay_order_no, online_pay_style, money, app_store_order_no, refund_status, success_time, refund_recv_accout, remark, create_by, create_dept, create_time, update_by, update_time, status, is_deleted, version from bu_member_online_refund
    </sql>

    <select id="selectBuMemberOnlineRefundList" parameterType="BuMemberOnlineRefund" resultMap="BuMemberOnlineRefundResult">
        <include refid="selectBuMemberOnlineRefundVo"/>
        <where>  
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
            <if test="customerId != null "> and customer_id = #{customerId}</if>
            <if test="memberId != null "> and member_id = #{memberId}</if>
            <if test="refundOrderNo != null  and refundOrderNo != ''"> and refund_order_no = #{refundOrderNo}</if>
            <if test="payOrderNo != null  and payOrderNo != ''"> and pay_order_no = #{payOrderNo}</if>
            <if test="onlinePayStyle != null  and onlinePayStyle != ''"> and online_pay_style = #{onlinePayStyle}</if>
            <if test="money != null "> and money = #{money}</if>
            <if test="appStoreOrderNo != null  and appStoreOrderNo != ''"> and app_store_order_no = #{appStoreOrderNo}</if>
            <if test="refundStatus != null  and refundStatus != ''"> and refund_status = #{refundStatus}</if>
            <if test="successTime != null  and successTime != ''"> and success_time = #{successTime}</if>
            <if test="refundRecvAccout != null  and refundRecvAccout != ''"> and refund_recv_accout = #{refundRecvAccout}</if>
            <if test="createDept != null "> and create_dept = #{createDept}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
            <if test="version != null "> and version = #{version}</if>
        </where>
    </select>
    
    <select id="selectBuMemberOnlineRefundById" parameterType="Long" resultMap="BuMemberOnlineRefundResult">
        <include refid="selectBuMemberOnlineRefundVo"/>
        where id = #{id}
    </select>
    <select id="selectBuMemberOnlineRefundByRefundOrderNo" parameterType="String" resultMap="BuMemberOnlineRefundResult">
        <include refid="selectBuMemberOnlineRefundVo"/>
        where refund_order_no = #{refundOrderNo}
    </select>

    <insert id="insertBuMemberOnlineRefund" parameterType="BuMemberOnlineRefund">
        insert into bu_member_online_refund
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="tenantId != null and tenantId != ''">tenant_id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="memberId != null">member_id,</if>
            <if test="refundOrderNo != null">refund_order_no,</if>
            <if test="payOrderNo != null and payOrderNo != ''">pay_order_no,</if>
            <if test="onlinePayStyle != null and onlinePayStyle != ''">online_pay_style,</if>
            <if test="money != null">money,</if>
            <if test="appStoreOrderNo != null and appStoreOrderNo != ''">app_store_order_no,</if>
            <if test="refundStatus != null">refund_status,</if>
            <if test="successTime != null">success_time,</if>
            <if test="refundRecvAccout != null">refund_recv_accout,</if>
            <if test="remark != null">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDept != null">create_dept,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="isDeleted != null">is_deleted,</if>
            <if test="version != null">version,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="tenantId != null and tenantId != ''">#{tenantId},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="refundOrderNo != null">#{refundOrderNo},</if>
            <if test="payOrderNo != null and payOrderNo != ''">#{payOrderNo},</if>
            <if test="onlinePayStyle != null and onlinePayStyle != ''">#{onlinePayStyle},</if>
            <if test="money != null">#{money},</if>
            <if test="appStoreOrderNo != null and appStoreOrderNo != ''">#{appStoreOrderNo},</if>
            <if test="refundStatus != null">#{refundStatus},</if>
            <if test="successTime != null">#{successTime},</if>
            <if test="refundRecvAccout != null">#{refundRecvAccout},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDept != null">#{createDept},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
            <if test="version != null">#{version},</if>
         </trim>
    </insert>

    <update id="updateBuMemberOnlineRefund" parameterType="BuMemberOnlineRefund">
        update bu_member_online_refund
        <trim prefix="SET" suffixOverrides=",">
            <if test="tenantId != null and tenantId != ''">tenant_id = #{tenantId},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="refundOrderNo != null">refund_order_no = #{refundOrderNo},</if>
            <if test="payOrderNo != null and payOrderNo != ''">pay_order_no = #{payOrderNo},</if>
            <if test="onlinePayStyle != null and onlinePayStyle != ''">online_pay_style = #{onlinePayStyle},</if>
            <if test="money != null">money = #{money},</if>
            <if test="appStoreOrderNo != null and appStoreOrderNo != ''">app_store_order_no = #{appStoreOrderNo},</if>
            <if test="refundStatus != null">refund_status = #{refundStatus},</if>
            <if test="successTime != null">success_time = #{successTime},</if>
            <if test="refundRecvAccout != null">refund_recv_accout = #{refundRecvAccout},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDept != null">create_dept = #{createDept},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="version != null">version = #{version},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateBuMemberOnlineRefundByRefundOrderNo" parameterType="BuMemberOnlineRefund">
        update bu_member_online_refund
        <trim prefix="SET" suffixOverrides=",">
            <if test="tenantId != null and tenantId != ''">tenant_id = #{tenantId},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="onlinePayStyle != null and onlinePayStyle != ''">online_pay_style = #{onlinePayStyle},</if>
            <if test="appStoreOrderNo != null and appStoreOrderNo != ''">app_store_order_no = #{appStoreOrderNo},</if>
            <if test="refundStatus != null">refund_status = #{refundStatus},</if>
            <if test="successTime != null">success_time = #{successTime},</if>
            <if test="refundRecvAccout != null">refund_recv_accout = #{refundRecvAccout},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDept != null">create_dept = #{createDept},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
            <if test="version != null">version = #{version},</if>
        </trim>
        where refund_order_no = #{refundOrderNo}
    </update>

    <delete id="deleteBuMemberOnlineRefundById" parameterType="Long">
        delete from bu_member_online_refund where id = #{id}
    </delete>

    <delete id="deleteBuMemberOnlineRefundByIds" parameterType="String">
        delete from bu_member_online_refund where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>