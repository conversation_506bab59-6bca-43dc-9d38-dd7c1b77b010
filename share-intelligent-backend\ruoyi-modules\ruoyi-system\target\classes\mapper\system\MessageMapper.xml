<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MessageMapper">

    <resultMap id="MessageResult" type="com.ruoyi.system.api.domain.Message">
        <result property="messageId" column="message_id"/>
        <result property="messageTitle" column="message_title"/>
        <result property="messageBody" column="message_body"/>
        <result property="messageTime" column="message_time"/>
        <result property="messageStatus" column="message_status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberId" column="member_id"/>
    </resultMap>

    <insert id="insertMessages"  useGeneratedKeys="true" keyProperty="messageId">
        insert into message
          ( message_title,message_body,message_time,message_status,del_flag,create_by,create_time,update_by,update_time,remark,member_id )
        values
        <foreach collection="list" item="msg" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
            <choose>
                <when test="msg.messageTitle">#{msg.messageTitle},</when>
                <otherwise>null,</otherwise>
            </choose>
            <choose>
                <when test="msg.messageBody">#{msg.messageBody},</when>
                <otherwise>null,</otherwise>
            </choose>
            <choose>
                <when test="msg.messageTime">#{msg.messageTime},</when>
                <otherwise>null,</otherwise>
            </choose>
            <choose>
                <when test="msg.messageStatus">#{msg.messageStatus},</when>
                <otherwise>null,</otherwise>
            </choose>
            <choose>
                <when test="msg.delFlag">#{msg.delFlag},</when>
                <otherwise>null,</otherwise>
            </choose>
            <choose>
                <when test="msg.createBy">#{msg.createBy},</when>
                <otherwise>null,</otherwise>
            </choose>
            <choose>
                <when test="msg.createTime">#{msg.createTime},</when>
                <otherwise>null,</otherwise>
            </choose>
            <choose>
                <when test="msg.updateBy">#{msg.updateBy},</when>
                <otherwise>null,</otherwise>
            </choose>
            <choose>
                <when test="msg.updateTime">#{msg.updateTime},</when>
                <otherwise>null,</otherwise>
            </choose>
            <choose>
                <when test="msg.remark">#{msg.remark},</when>
                <otherwise>null,</otherwise>
            </choose>
            <choose>
                <when test="msg.memberId">#{msg.memberId},</when>
                <otherwise>null</otherwise>
            </choose>
            </trim>
        </foreach>
    </insert>


</mapper>