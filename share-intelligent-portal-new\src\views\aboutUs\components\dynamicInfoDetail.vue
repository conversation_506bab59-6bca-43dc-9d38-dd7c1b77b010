<template>
  <div class="main" v-loading="loading">
    <div class="main_l">
      <div class="zhuanjia_title">{{ detail.newsInformationName || "" }}</div>
      <div class="laiyuan">
        {{ detail.newsInformationDate }} 作者：{{
          detail.newsInformationAuthor ? detail.newsInformationAuthor : ""
        }}
      </div>
      <div class="news_c" v-html="detail.newsInformationContent"></div>
    </div>
    <div class="main_r">
      <div class="ad">
        <div style="color: white">更多新闻动态</div>
      </div>
      <!-- <div class="ad_exr">
        <ul class="news_exr_list2">
          <li v-for="(item, index) in cmsList" :key="index">
            <a
              :href="'newsInformationDetail.html?id=' + item.newsInformationId"
            >
              <div class="news_exr_l">
                <img :src="item.newsInformationImg" />
              </div>
              <div class="news_exr_r">
                <p>{{ item.newsInformationName || "" }}</p>
                <div class="time">{{ item.newsInformationDate }}</div>
              </div>
            </a>
          </li>
        </ul>
      </div> -->
    </div>
  </div>
</template>
<script>
import { infoDetailData, infoData } from "@/api/home";

export default {
  name: "news",
  data() {
    return {
      loading: false,
      detail: {},
      newsList: [],
      newsList2: [],
      newsList3: [],
      newsList4: [],
      newsList5: [],
      newsList6: [],
      newsList7: [],
      newsList8: [],
      newsList9: [],
      newsList10: [],
      id: null,
      cmsList: [],
    };
  },
  created() {
    this.id = this.$route.query.id;
    this.getData();
  },
  methods: {
    getData() {
      let params = {
        newsInformationId: this.id,
      };
      infoDetailData(params).then((res) => {
        if (res.code === 200) {
          this.detail = res.data;
          this.detail.newsInformationContent = decodeURIComponent(
            this.detail.newsInformationContent
          );
          this.loading = false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.main {
  width: 1200px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
  margin-top: 0px;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  font-size: 14px;
  padding-top: 40px;
  padding-right: 0px;
  padding-bottom: 40px;
  padding-left: 0px;
}
.main_l {
  width: 855px;
}

.main_r {
  width: 320px;
}
.zhuanjia_title {
  font-size: 30px;
  color: #000000;
  line-height: 50px;
}

.news_c {
  padding-top: 20px;
  padding-bottom: 20px;
  width: 100%;

  ::v-deep .ql-align-center{
    text-align: center;
  }
}

.ad {
  height: 86px;
  background-size: cover;
  border-radius: 4px;
  line-height: 86px;
  font-size: 20px;
  text-align: center;
  background-color: #21c9b8;
  cursor: pointer;
}

.ad_exr {
  padding-left: 20px;
  padding-top: 20px;
  background-color: #fff;
}

ul.news_exr_list2 {
  margin: 0px;
  padding-top: 10px;
  padding-right: 0px;
  padding-bottom: 0px;
  padding-left: 0px;
}

ul.news_exr_list2 li {
  padding: 12px;
  box-shadow: 0 2px 5px #eee;
  border-radius: 5px;
  margin-top: 15px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 0px;
  border-top-width: 1px;
  border-right-width: 1px;
  border-bottom-width: 0px;
  border-left-width: 1px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #eee;
  border-right-color: #eee;
  border-bottom-color: #eee;
  border-left-color: #eee;
}

ul.news_exr_list2 li a {
  display: flex;
  width: 100%;
  flex-flow: row wrap;
  justify-content: space-between;
}
</style>
