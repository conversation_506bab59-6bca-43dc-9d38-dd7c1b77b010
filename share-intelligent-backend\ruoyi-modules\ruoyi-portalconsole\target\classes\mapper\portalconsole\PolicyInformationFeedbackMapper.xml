<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.PolicyInformationFeedbackMapper">
    
    <resultMap type="PolicyInformationFeedbackVO" id="PolicyInformationFeedbackResult">
        <result property="policyInformationFeedbackId"    column="policy_information_feedback_id"    />
        <result property="policyInformationId"    column="policy_information_id"    />
        <result property="content"    column="content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />

        <result property="policyInformationTitle"    column="policy_information_title"    />
    </resultMap>

    <sql id="selectPolicyInformationFeedbackVo">
        select policy_information_feedback.policy_information_feedback_id, policy_information_feedback.policy_information_id, policy_information_feedback.content, policy_information_feedback.create_by, policy_information_feedback.create_time, policy_information_feedback.update_by, policy_information_feedback.update_time, policy_information_feedback.remark,
               policy_information.policy_information_title from policy_information_feedback
       LEFT JOIN policy_information  ON policy_information.policy_information_id = policy_information_feedback.policy_information_id
    </sql>

    <select id="selectPolicyInformationFeedbackList" parameterType="PolicyInformationFeedback" resultMap="PolicyInformationFeedbackResult">
        <include refid="selectPolicyInformationFeedbackVo"/>

        <where>  
            <if test="policyInformationId != null "> and policy_information_id = #{policyInformationId}</if>
            <if test="content != null  and content != ''"> and content like concat('%', #{content}, '%')</if>
        </where>
    </select>
    
    <select id="selectPolicyInformationFeedbackByPolicyInformationFeedbackId" parameterType="Long" resultMap="PolicyInformationFeedbackResult">
        <include refid="selectPolicyInformationFeedbackVo"/>
        where policy_information_feedback_id = #{policyInformationFeedbackId}
    </select>
        
    <insert id="insertPolicyInformationFeedback" parameterType="PolicyInformationFeedback">
        insert into policy_information_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="policyInformationFeedbackId != null">policy_information_feedback_id,</if>
            <if test="policyInformationId != null">policy_information_id,</if>
            <if test="content != null">content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="policyInformationFeedbackId != null">#{policyInformationFeedbackId},</if>
            <if test="policyInformationId != null">#{policyInformationId},</if>
            <if test="content != null">#{content},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePolicyInformationFeedback" parameterType="PolicyInformationFeedback">
        update policy_information_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="policyInformationId != null">policy_information_id = #{policyInformationId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where policy_information_feedback_id = #{policyInformationFeedbackId}
    </update>

    <delete id="deletePolicyInformationFeedbackByPolicyInformationFeedbackId" parameterType="Long">
        delete from policy_information_feedback where policy_information_feedback_id = #{policyInformationFeedbackId}
    </delete>

    <delete id="deletePolicyInformationFeedbackByPolicyInformationFeedbackIds" parameterType="String">
        delete from policy_information_feedback where policy_information_feedback_id in 
        <foreach item="policyInformationFeedbackId" collection="array" open="(" separator="," close=")">
            #{policyInformationFeedbackId}
        </foreach>
    </delete>
</mapper>