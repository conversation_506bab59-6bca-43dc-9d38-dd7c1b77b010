<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
        <div class="top">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">用工信息</div>
            <div class="buttonStyle" @click="toPublish">发布用工信息</div>
          </div>
        </div>
        <el-form class="queryForm" :model="queryParams" ref="queryForm" size="small" :inline="true">
          <el-form-item label="岗位名称" prop="positionName">
            <el-input v-model="queryParams.positionName" placeholder="请输入岗位名称" clearable
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="cards">
          <div class="card" v-for="item in employmentList" :key="item.id" @click="goDetail(item.id)">
            <el-descriptions :column="1" :labelStyle="labelStyle" :contentStyle="contentStyle">
              <el-descriptions-item label="岗位名称">{{
                item.positionName
                }}</el-descriptions-item>
              <el-descriptions-item label="岗位要求">
                {{ item.requirements }}
              </el-descriptions-item>
              <el-descriptions-item label="时间">{{
                item.createTime
                }}</el-descriptions-item>
              <el-descriptions-item label="状态">{{
                item.status == "0" ? "正常" : "停用"
                }}</el-descriptions-item>
            </el-descriptions>
            <!-- <div class="btn">
              <el-button type="primary" plain size="mini">修改</el-button>
              <el-button type="primary" size="mini">详情</el-button>
            </div> -->
          </div>
          <!-- 分页 -->
          <div class="pageStyle">
            <el-pagination v-if="employmentList && employmentList.length > 0" background layout="prev, pager, next"
              class="activity-pagination" :page-size="pageSize" :current-page="pageNum" :total="total"
              @size-change="handleSizeChange" @current-change="handleCurrentChange">
            </el-pagination>
          </div>
        </div>
        <el-empty v-if="employmentList.length == 0"></el-empty>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import UserMenu from "../components/userMenu.vue";
import { employUserListData } from "@/api/serviceSharing";

export default {
  name: "User",
  components: { UserMenu },
  data() {
    return {
      pageNum: 1,
      pageSize: 8,
      total: 0,
      employmentList: [],
      queryParams: {
        positionName: "",
      },
      labelStyle: {
        fontWeight: 400,
        fontSize: "14px",
        color: "#999999",
        width: "60px",
        justifyContent: "flex-end",
      },
      contentStyle: {
        fontWeight: 400,
        fontSize: "14px",
        color: "#333333",
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 列表接口
    getList() {
      this.loading = true;
      let userinfo = JSON.parse(window.sessionStorage.getItem("userinfo"));
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        positionName: this.queryParams.positionName,
        createBy: userinfo.memberPhone
      };
      employUserListData(params).then((res) => {
        if (res.code === 200) {
          this.employmentList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        categoryId: undefined,
        status: undefined,
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    toPublish() {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => { });
        return;
      } else {
        this.$router.push("/publishEmInformation");
      }
    },
    goDetail(id) {
      this.$router.push("/employmentInfoDetail?id=" + id);
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));
  height: 1000px;
}

.top {
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  // margin-top: 20px;
  display: flex;
  justify-content: space-between;

  .content_title {
    display: flex;
    align-items: center;
    width: 100%;

    .icon {
      width: 4px;
      height: 20px;
      background: #21c9b8;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #030a1a;
      margin-left: 10px;
    }

    .buttonStyle {
      padding: 10px 20px;
      background: #21c9b8;
      color: #fff;
      text-align: center;
      cursor: pointer;
      border-radius: 10px;
      margin-left: auto;
    }
  }
}

.queryForm {
  padding: 20px;
}

.cards {
  display: flex;
  flex-wrap: wrap;

  .card {
    width: 24%;
    // height: 200px;
    background: #fff;
    padding: 20px;
    margin-right: 10px;
    margin-bottom: 10px;
    border-radius: 10px;
    box-sizing: border-box;
    position: relative;
    cursor: pointer;

    .btn {
      position: absolute;
      right: 20px;
      bottom: 20px;
    }
  }

  .pageStyle {
    width: 100%;
    margin-top: 61px;
    display: flex;
    justify-content: center;
  }
}
</style>
