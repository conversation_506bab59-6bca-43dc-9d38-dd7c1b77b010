<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.IndustryTypeMapper">
    
    <resultMap type="IndustryType" id="IndustryTypeResult">
        <result property="industryTypeId"    column="industry_type_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="industryTypeName"    column="industry_type_name"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectIndustryTypeVo">
        select industry_type_id, parent_id, industry_type_name, del_flag, create_by, create_time, update_by, update_time, remark from industry_type
    </sql>

    <select id="selectIndustryTypeList" parameterType="IndustryType" resultMap="IndustryTypeResult">
        <include refid="selectIndustryTypeVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="industryTypeName != null  and industryTypeName != ''"> and industry_type_name like concat('%', #{industryTypeName}, '%')</if>
        </where>
    </select>
    
    <select id="selectIndustryTypeByIndustryTypeId" parameterType="Long" resultMap="IndustryTypeResult">
        <include refid="selectIndustryTypeVo"/>
        where industry_type_id = #{industryTypeId}
    </select>
        
    <insert id="insertIndustryType" parameterType="IndustryType" useGeneratedKeys="true" keyProperty="industryTypeId">
        insert into industry_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="industryTypeName != null and industryTypeName != ''">industry_type_name,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="industryTypeName != null and industryTypeName != ''">#{industryTypeName},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateIndustryType" parameterType="IndustryType">
        update industry_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="industryTypeName != null and industryTypeName != ''">industry_type_name = #{industryTypeName},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where industry_type_id = #{industryTypeId}
    </update>

    <delete id="deleteIndustryTypeByIndustryTypeId" parameterType="Long">
        delete from industry_type where industry_type_id = #{industryTypeId}
    </delete>

    <delete id="deleteIndustryTypeByIndustryTypeIds" parameterType="String">
        delete from industry_type where industry_type_id in 
        <foreach item="industryTypeId" collection="array" open="(" separator="," close=")">
            #{industryTypeId}
        </foreach>
    </delete>
</mapper>