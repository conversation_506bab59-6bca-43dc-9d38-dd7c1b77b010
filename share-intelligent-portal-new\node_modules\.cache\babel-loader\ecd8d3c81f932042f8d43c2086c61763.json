{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\interested.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\interested.vue", "mtime": 1750311963023}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_home", "require", "data", "form", "title", "updateTime", "applicationAreaName", "rules", "created", "initData", "$route", "query", "demandName", "intentionType", "parseInt", "fieldName", "intentionId", "methods", "onSubmit", "_this", "submitIntention", "then", "res", "code", "$message", "success", "cancel", "error", "msg", "goDetail", "_this2", "userInfo", "JSON", "parse", "sessionStorage", "getItem", "linkMan", "memberRealName", "linkTel", "memberPhone", "companyName", "memberCompanyName", "$confirm", "confirmButtonText", "cancelButtonText", "type", "cancelButtonClass", "confirmButtonClass", "$router", "push", "catch", "go"], "sources": ["src/views/supplyDemandDocking/components/interested.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">我有意向</div>\r\n      <div style=\"height: 33px; margin-top: 21px\">I have intentions</div>\r\n    </div>\r\n    <div class=\"card-container card-content\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <div class=\"imgStyle\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../assets/device/ceshi.png\" alt=\"\" />\r\n        </div>\r\n        <div class=\"title\">{{ form.title || '' }}</div>\r\n        <div style=\"display: flex; align-items: center; margin-top: 15px\">\r\n          <div class=\"publishTimeStyle\">发布时间：{{ updateTime }}</div>\r\n          <!-- <div class=\"detailStyle\" @click=\"goDetail\">查看详情 >></div> -->\r\n        </div>\r\n      </div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <span class=\"resourceType\">资源类型：</span>\r\n          <span class=\"resourceValue\">{{ form.fieldName || '' }}</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <span class=\"resourceType\">资源名称：</span>\r\n          <span class=\"resourceValue\">{{ form.title || '' }}</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"意向描述\">\r\n              <el-input v-model=\"form.intentionContent\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\"\r\n                show-word-limit placeholder=\"\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人：\" prop=\"linkMan\">\r\n              <el-input disabled v-model=\"form.linkMan\" placeholder=\"请先维护联系人\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话：\" prop=\"linkTel\">\r\n              <el-input disabled v-model=\"form.linkTel\" placeholder=\"请先维护联系方式\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button style=\"width: 100%; height: 50px\" type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n        <div class=\"promptStyle\">温馨提示</div>\r\n        <div class=\"desc\" style=\"margin-top: 20px\">\r\n          1、我们会在最快的时间和您取得联系（工作时间周一至周五8:00-18:00）\r\n        </div>\r\n        <div class=\"desc\" style=\"margin-top: 13px\">\r\n          2、紧急问题请拨打：15512688882\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { submitIntention } from \"@/api/home\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        title: \"\",\r\n      },\r\n      updateTime: \"暂无\",\r\n      applicationAreaName: \"\",\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n    if (this.$route.query.demandName && this.$route.query.demandName != 'null') {\r\n      this.form.title = this.$route.query.demandName\r\n    }\r\n    if (this.$route.query.intentionType) {\r\n      this.form.intentionType = parseInt(this.$route.query.intentionType)\r\n    }\r\n    if (this.$route.query.fieldName) {\r\n      this.form.fieldName = this.$route.query.fieldName\r\n    }\r\n    if (this.$route.query.intentionId) {\r\n      this.form.intentionId = this.$route.query.intentionId\r\n    }\r\n    if (this.$route.query.updateTime && this.$route.query.updateTime != 'null') {\r\n      this.updateTime = this.$route.query.updateTime\r\n    }\r\n    if (this.$route.query.applicationAreaName && this.$route.query.applicationAreaName != 'null') {\r\n      this.applicationAreaName = this.$route.query.applicationAreaName\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit() {\r\n      submitIntention(this.form).then((res) => {\r\n        if (res.code == 200) {\r\n          this.$message.success(\"提交成功\")\r\n          this.cancel()\r\n        } else {\r\n          this.$message.error(res.msg)\r\n        }\r\n      })\r\n    },\r\n    goDetail() {\r\n      // this.$router.push(\"/productOrderDetail\");\r\n    },\r\n    // 判断是否关联企业\r\n    initData() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (userInfo && userInfo != 'null') {\r\n        this.form.linkMan = userInfo.memberRealName;\r\n        this.form.linkTel = userInfo.memberPhone;\r\n        this.form.companyName = userInfo.memberCompanyName;\r\n      }\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      }\r\n    },\r\n    cancel() {\r\n      this.$router.go(-1)\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: rgb(242, 242, 242);\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n}\r\n\r\n.card-content {\r\n  display: flex;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: -70px;\r\n  padding: 60px 59px 62px 60px;\r\n\r\n  .card_left {\r\n    .imgStyle {\r\n      width: 330px;\r\n      height: 230px;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #333333;\r\n      margin-top: 23px;\r\n    }\r\n\r\n    .publishTimeStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n    }\r\n\r\n    .detailStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #21c9b8;\r\n      margin-left: auto;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .card_right {\r\n    margin-left: 40px;\r\n    width: 100%;\r\n\r\n    .resourceType {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .resourceValue {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #666666;\r\n    }\r\n\r\n    .footer-submit {\r\n      margin-top: 40px;\r\n    }\r\n\r\n    .promptStyle {\r\n      margin-top: 30px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .desc {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAyDA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,KAAA;MACA;MACAC,UAAA;MACAC,mBAAA;MACAC,KAAA,GACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,UAAA,SAAAF,MAAA,CAAAC,KAAA,CAAAC,UAAA;MACA,KAAAT,IAAA,CAAAC,KAAA,QAAAM,MAAA,CAAAC,KAAA,CAAAC,UAAA;IACA;IACA,SAAAF,MAAA,CAAAC,KAAA,CAAAE,aAAA;MACA,KAAAV,IAAA,CAAAU,aAAA,GAAAC,QAAA,MAAAJ,MAAA,CAAAC,KAAA,CAAAE,aAAA;IACA;IACA,SAAAH,MAAA,CAAAC,KAAA,CAAAI,SAAA;MACA,KAAAZ,IAAA,CAAAY,SAAA,QAAAL,MAAA,CAAAC,KAAA,CAAAI,SAAA;IACA;IACA,SAAAL,MAAA,CAAAC,KAAA,CAAAK,WAAA;MACA,KAAAb,IAAA,CAAAa,WAAA,QAAAN,MAAA,CAAAC,KAAA,CAAAK,WAAA;IACA;IACA,SAAAN,MAAA,CAAAC,KAAA,CAAAN,UAAA,SAAAK,MAAA,CAAAC,KAAA,CAAAN,UAAA;MACA,KAAAA,UAAA,QAAAK,MAAA,CAAAC,KAAA,CAAAN,UAAA;IACA;IACA,SAAAK,MAAA,CAAAC,KAAA,CAAAL,mBAAA,SAAAI,MAAA,CAAAC,KAAA,CAAAL,mBAAA;MACA,KAAAA,mBAAA,QAAAI,MAAA,CAAAC,KAAA,CAAAL,mBAAA;IACA;EACA;EACAW,OAAA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,qBAAA,OAAAjB,IAAA,EAAAkB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAK,QAAA,CAAAC,OAAA;UACAN,KAAA,CAAAO,MAAA;QACA;UACAP,KAAA,CAAAK,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA;IAAA,CACA;IACA;IACApB,QAAA,WAAAA,SAAA;MAAA,IAAAqB,MAAA;MACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,cAAA,CAAAC,OAAA;MACA,IAAAJ,QAAA,IAAAA,QAAA;QACA,KAAA5B,IAAA,CAAAiC,OAAA,GAAAL,QAAA,CAAAM,cAAA;QACA,KAAAlC,IAAA,CAAAmC,OAAA,GAAAP,QAAA,CAAAQ,WAAA;QACA,KAAApC,IAAA,CAAAqC,WAAA,GAAAT,QAAA,CAAAU,iBAAA;MACA;MACA,MAAAV,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAU,iBAAA;QACA,KAAAC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;UACAC,iBAAA;UACAC,kBAAA;QACA,GACA1B,IAAA;UACAS,MAAA,CAAAkB,OAAA,CAAAC,IAAA;QACA,GACAC,KAAA;QACA;MACA;IACA;IACAxB,MAAA,WAAAA,OAAA;MACA,KAAAsB,OAAA,CAAAG,EAAA;IACA;EACA;AACA", "ignoreList": []}]}