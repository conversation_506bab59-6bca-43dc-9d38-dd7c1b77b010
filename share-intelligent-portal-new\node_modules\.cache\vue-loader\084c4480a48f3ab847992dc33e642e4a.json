{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\TopNav\\index.vue?vue&type=style&index=0&id=35f3a2c1&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\TopNav\\index.vue", "mtime": 1750311962827}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5pcy1hY3RpdmUgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmICFpbXBvcnRhbnQ7DQp9DQoNCi5oZWFkX3RpdGxlX2xpbmUgew0KICAudGl0bGVMaW5lIHsNCiAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQoNCiAgICAmOjphZnRlciB7DQogICAgICBjb250ZW50OiAiIjsNCiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgIGJvdHRvbTogMDsNCiAgICAgIHdpZHRoOiAwJTsNCiAgICAgIHdpZHRoOiAwJTsNCiAgICAgIGhlaWdodDogMnB4Ow0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzM3YzliODsNCiAgICAgIHRyYW5zaXRpb246IGFsbCAwLjM1cyBlYXNlLWluOw0KICAgICAgbGVmdDogMDsNCiAgICAgIHotaW5kZXg6IDE7DQogICAgfQ0KICB9DQoNCiAgJjpob3ZlciB7DQogICAgLnRpdGxlTGluZTo6YWZ0ZXIgew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgfQ0KICB9DQp9DQoNCi5uYXZfc3BhbiB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAmOjpiZWZvcmUgew0KICAgIGNvbnRlbnQ6ICIiOw0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICBsZWZ0OiA1MCU7DQogICAgYm90dG9tOiAtM3B4Ow0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGhlaWdodDogNHB4Ow0KICAgIGJhY2tncm91bmQtY29sb3I6ICMzN2M5Yjg7DQogICAgdHJhbnNmb3JtLW9yaWdpbjogY2VudGVyOw0KICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIDApIHNjYWxlWCgwKTsNCiAgICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4zcyBlYXNlLWluOw0KICB9DQoNCiAgJjpob3ZlciB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZiAhaW1wb3J0YW50Ow0KDQogICAgJjo6YmVmb3JlIHsNCiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlKC01MCUsIDApIHNjYWxlWCgxKTsNCiAgICB9DQogIH0NCn0NCg0KLnRvcG1lbnUtY29udGFpbmVyLmVsLW1lbnUtLWhvcml6b250YWw+LmVsLW1lbnUtaXRlbSB7DQogIGhlaWdodDogODBweCAhaW1wb3J0YW50Ow0KICBsaW5lLWhlaWdodDogODBweCAhaW1wb3J0YW50Ow0KICBjb2xvcjogIzMzMzMzMyAhaW1wb3J0YW50Ow0KICBmb250LXNpemU6IDE2cHggIWltcG9ydGFudDsNCiAgZm9udC13ZWlnaHQ6IDUwMCAhaW1wb3J0YW50Ow0KICBwYWRkaW5nOiAwIDEwcHggIWltcG9ydGFudDsNCiAgbWFyZ2luOiAwIDEwcHggIWltcG9ydGFudDsNCn0NCg0KLnRvcG1lbnUtY29udGFpbmVyLmVsLW1lbnUtLWhvcml6b250YWw+LmVsLW1lbnUtaXRlbTpob3ZlciB7DQogIGNvbG9yOiAjeyJ2YXIoLS10aGVtZSkifSAhaW1wb3J0YW50Ow0KfQ0KDQoudG9wbWVudS1jb250YWluZXIuZWwtbWVudS0taG9yaXpvbnRhbD4uZWwtbWVudS1pdGVtLmlzLWFjdGl2ZSwNCi5lbC1tZW51LS1ob3Jpem9udGFsPi5lbC1zdWJtZW51LmlzLWFjdGl2ZSAuZWwtc3VibWVudV9fdGl0bGUgew0KICBjb2xvcjogIzIxYzliOCAhaW1wb3J0YW50Ow0KICBib3JkZXItYm90dG9tOiA0cHggc29saWQgIzIxYzliOCAhaW1wb3J0YW50Ow0KICAvLyBjb2xvcjogI2ZmZiAhaW1wb3J0YW50Ow0KICAvLyBiYWNrZ3JvdW5kLWNvbG9yOiAjeyJ2YXIoLS10aGVtZSkifSAhaW1wb3J0YW50Ow0KICAvLyBib3JkZXItYm90dG9tOiBub25lICFpbXBvcnRhbnQ7DQp9DQoNCi8qIHN1Ym1lbnUgaXRlbSAqLw0KLnRvcG1lbnUtY29udGFpbmVyLmVsLW1lbnUtLWhvcml6b250YWw+LmVsLXN1Ym1lbnUgLmVsLXN1Ym1lbnVfX3RpdGxlIHsNCiAgaGVpZ2h0OiA4MHB4ICFpbXBvcnRhbnQ7DQogIGxpbmUtaGVpZ2h0OiA4MHB4ICFpbXBvcnRhbnQ7DQogIGNvbG9yOiAjMzMzMzMzICFpbXBvcnRhbnQ7DQogIHBhZGRpbmc6IDAgNXB4ICFpbXBvcnRhbnQ7DQogIG1hcmdpbjogMCAxMHB4ICFpbXBvcnRhbnQ7DQogIGZvbnQtc2l6ZTogMTZweCAhaW1wb3J0YW50Ow0KICBmb250LXdlaWdodDogNTAwICFpbXBvcnRhbnQ7DQp9DQoNCi50b3BtZW51LWNvbnRhaW5lci5lbC1tZW51LS1ob3Jpem9udGFsPi5lbC1tZW51LWl0ZW06bnRoLWNoaWxkKDYpOmhvdmVyfi5zdXBwbHlEZW1hbmREb2NraW5nIHsNCiAgZGlzcGxheTogYmxvY2s7DQp9DQoNCi50b3BtZW51LWNvbnRhaW5lci5lbC1tZW51LS1ob3Jpem9udGFsPi5lbC1tZW51LWl0ZW06bnRoLWNoaWxkKDMpOmhvdmVyfi5tYW51ZmFjdHVyaW5nU2hhcmUgew0KICBkaXNwbGF5OiBibG9jazsNCn0NCg0KLnRvcG1lbnUtY29udGFpbmVyLmVsLW1lbnUtLWhvcml6b250YWw+LmVsLW1lbnUtaXRlbTpudGgtY2hpbGQoNCk6aG92ZXJ+LnNlcnZpY2VTaGFyZSB7DQogIGRpc3BsYXk6IGJsb2NrOw0KfQ0KDQoudG9wbWVudS1jb250YWluZXIuZWwtbWVudS0taG9yaXpvbnRhbD4uZWwtbWVudS1pdGVtOm50aC1jaGlsZCg1KTpob3Zlcn4uaW5ub3ZhdGlvblNoYXJlIHsNCiAgZGlzcGxheTogYmxvY2s7DQp9DQoNCi50b3BtZW51LWNvbnRhaW5lci5lbC1tZW51LS1ob3Jpem9udGFsPi5lbC1tZW51LWl0ZW06bnRoLWNoaWxkKDcpOmhvdmVyfi5hYm91dFVzIHsNCiAgZGlzcGxheTogYmxvY2s7DQp9DQoNCi5zdXBwbHlEZW1hbmREb2NraW5nLA0KLm1hbnVmYWN0dXJpbmdTaGFyZSwNCi5zZXJ2aWNlU2hhcmUsDQouaW5ub3ZhdGlvblNoYXJlLA0KLmFib3V0VXMgew0KICBwb3NpdGlvbjogZml4ZWQ7DQogIGRpc3BsYXk6IG5vbmU7DQogIGxlZnQ6IDAgIWltcG9ydGFudDsNCiAgdG9wOiA4MHB4ICFpbXBvcnRhbnQ7DQogIHdpZHRoOiAxMDB2dzsNCiAgLy8gaGVpZ2h0OiAyNDZweDsNCiAgbWFyZ2luLXRvcDogMDsNCiAgcGFkZGluZzogMDsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCg0KICAuc3ViLXB1cmNoYXNlLWNvbnRlbnQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgZmxleC13cmFwOiB3cmFwOw0KICAgIC8vIG1hcmdpbi1sZWZ0OiAyOC45NnZ3Ow0KICAgIC8vIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgIHBhZGRpbmc6IDUwcHg7DQoNCiAgICAuY29udGVudF9pdGVtIHsNCiAgICAgIHdpZHRoOiAxNSU7DQogICAgICBtYXJnaW4tbGVmdDogNSU7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQoNCiAgICAgIC50aXRsZSB7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7DQogICAgICAgIGZvbnQtc2l6ZTogMjBweDsNCiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlLWluOw0KICAgICAgfQ0KDQogICAgICAudGl0bGVMaW5lIHsNCiAgICAgICAgaGVpZ2h0OiAycHg7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjOTk5Ow0KICAgICAgICBtYXJnaW46IDIwcHggMDsNCiAgICAgIH0NCg0KICAgICAgLmRlc2Mgew0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIGNvbG9yOiAjOTk5Ow0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5jb250ZW50X2l0ZW06aG92ZXIgLnRpdGxlIHsNCiAgICAgIGNvbG9yOiAjMjFjOWI4Ow0KICAgICAgLy8gdHJhbnNpdGlvbjogYWxsIDAuNnMgZWFzZS1pbi1vdXQ7DQogICAgfQ0KDQogICAgLmNvbnRlbnRfaXRlbTpob3ZlciAudGl0bGVMaW5lIHsNCiAgICAgIC8vIGJhY2tncm91bmQtY29sb3I6ICMyMWM5Yjg7DQogICAgfQ0KDQogICAgLmNvbnRlbnRfaXRlbTpudGgtY2hpbGQoNW4gKyAxKSB7DQogICAgICBtYXJnaW4tbGVmdDogMDsNCiAgICB9DQoNCiAgICAuY29udGVudF9pdGVtOm50aC1jaGlsZChuICsgNikgew0KICAgICAgbWFyZ2luLXRvcDogNTBweDsNCiAgICB9DQoNCiAgICAvLyAuc3ViLXB1cmNoYXNlLWxlZnQgew0KICAgIC8vICAgbWFyZ2luOiA0cHggODBweCAwIDA7DQogICAgLy8gICB3aWR0aDogNDAwcHg7DQogICAgLy8gICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1SZWd1bGFyLCBQaW5nRmFuZyBTQzsNCiAgICAvLyAgIC5zdWItbGVmdC1pbWcgew0KICAgIC8vICAgICB3aWR0aDogNjRweDsNCiAgICAvLyAgICAgaGVpZ2h0OiA2NHB4Ow0KICAgIC8vICAgICBtYXJnaW46IDAgYXV0bzsNCiAgICAvLyAgICAgaW1nIHsNCiAgICAvLyAgICAgICB3aWR0aDogMTAwJTsNCiAgICAvLyAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgLy8gICAgIH0NCiAgICAvLyAgIH0NCiAgICAvLyAgIC5zdWItbGVmdC10aXRsZSB7DQogICAgLy8gICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgLy8gICAgIGNvbG9yOiAjMzMzOw0KICAgIC8vICAgICBsaW5lLWhlaWdodDogMjZweDsNCiAgICAvLyAgICAgcGFkZGluZzogMThweCAwIDhweCAwOw0KICAgIC8vICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgLy8gICB9DQogICAgLy8gICAuc3ViLWxlZnQtaW5mbyB7DQogICAgLy8gICAgIGNvbG9yOiAjNjY2Ow0KICAgIC8vICAgICBsaW5lLWhlaWdodDogMjZweDsNCiAgICAvLyAgICAgd2hpdGUtc3BhY2U6IG5vcm1hbDsNCiAgICAvLyAgIH0NCiAgICAvLyB9DQogICAgLy8gLnN1Yi1wdXJjaGFzZS1yaWdodCB7DQogICAgLy8gICBkaXNwbGF5OiBmbGV4Ow0KICAgIC8vICAgLnN1Yi1yaWdodC1lYWNoIHsNCiAgICAvLyAgICAgLnN1Yi1yaWdodC1pdGVtIHsNCiAgICAvLyAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgLy8gICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtUmVndWxhciwgUGluZ0ZhbmcgU0M7DQogICAgLy8gICAgICAgLnN1Yi1yaWdodC10aXRsZSB7DQogICAgLy8gICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIC8vICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAvLyAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAvLyAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgLy8gICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAvLyAgICAgICAgIGxpbmUtaGVpZ2h0OiAxNnB4Ow0KICAgIC8vICAgICAgIH0NCiAgICAvLyAgICAgICAuc3ViLXJpZ2h0LWFycm93IHsNCiAgICAvLyAgICAgICAgIGRpc3BsYXk6IG5vbmU7DQogICAgLy8gICAgICAgfQ0KICAgIC8vICAgICAgIC5zdWItcmlnaHQtaW5mbyB7DQogICAgLy8gICAgICAgICBjb2xvcjogIzk5OTk5OWIzOw0KICAgIC8vICAgICAgICAgbGluZS1oZWlnaHQ6IDE0cHg7DQogICAgLy8gICAgICAgICBwYWRkaW5nLXRvcDogMTJweDsNCiAgICAvLyAgICAgICB9DQogICAgLy8gICAgICAgJjpob3ZlciB7DQogICAgLy8gICAgICAgICAuc3ViLXJpZ2h0LXRpdGxlIHsNCiAgICAvLyAgICAgICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgLy8gICAgICAgICB9DQogICAgLy8gICAgICAgICAuc3ViLXJpZ2h0LWFycm93IHsNCiAgICAvLyAgICAgICAgICAgZGlzcGxheTogYmxvY2s7DQogICAgLy8gICAgICAgICB9DQogICAgLy8gICAgICAgfQ0KICAgIC8vICAgICAgICYgKyAuc3ViLXJpZ2h0LWl0ZW0gew0KICAgIC8vICAgICAgICAgcGFkZGluZy10b3A6IDMycHg7DQogICAgLy8gICAgICAgfQ0KICAgIC8vICAgICB9DQogICAgLy8gICAgICYgKyAuc3ViLXJpZ2h0LWVhY2ggew0KICAgIC8vICAgICAgIG1hcmdpbi1sZWZ0OiA4MHB4Ow0KICAgIC8vICAgICB9DQogICAgLy8gICB9DQogICAgLy8gfQ0KICB9DQoNCiAgJjpob3ZlciB7DQogICAgZGlzcGxheTogYmxvY2s7DQogIH0NCn0NCg0KLnNob3ctY29udGVudCB7DQogIC5zdWItcHVyY2hhc2UtcG9wcGVyIHsNCiAgICBkaXNwbGF5OiBub25lOw0KICB9DQp9DQoNCi5lbC1tZW51LS1ob3Jpem9udGFsPi5lbC1zdWJtZW51IHsNCiAgJjpob3ZlciB7DQogICAgLnN1Yi1wdXJjaGFzZS10aXRsZSB7DQogICAgICBjb2xvcjogIzIxYzliOCAhaW1wb3J0YW50Ow0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+YA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/TopNav", "sourcesContent": ["<template>\r\n  <el-menu :default-active=\"activeMenu\" menu-trigger=\"hover\" mode=\"horizontal\" background-color=\"transparent\"\r\n    @select=\"handleSelect\">\r\n    <template v-for=\"(item, index) in menus\">\r\n      <el-menu-item :style=\"{ '--theme': '#45c9b8' }\" :index=\"item.path\" class=\"nav_span\" :key=\"index\">\r\n        {{ item.meta.title }}\r\n      </el-menu-item>\r\n    </template>\r\n    <!-- 弹窗---- -->\r\n    <!-- 供需对接 -->\r\n    <div class=\"supplyDemandDocking\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in supplyDemandDocking\" :key=\"index\"\r\n          @click=\"goSupplyDemandDocking(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 制造共享 -->\r\n    <div class=\"manufacturingShare\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in manufacturingSharing\" :key=\"index\"\r\n          @click=\"goManufacturingSharing(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 服务共享 -->\r\n    <div class=\"serviceShare\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in serviceSharing\" :key=\"index\"\r\n          @click=\"goServiceSharing(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 创新共享 -->\r\n    <div class=\"innovationShare\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in innovationSharing\" :key=\"index\"\r\n          @click=\"goInnovationSharing(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 关于我们 -->\r\n    <div class=\"aboutUs\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in aboutUsList\" :key=\"index\"\r\n          @click=\"goAboutUs(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 弹窗 -->\r\n\r\n    <!-- <div class=\"sub-purchase-popper\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"sub-purchase-left\">\r\n          <div class=\"sub-left-img\">\r\n            <img src=\"../../assets/purchaseSales/purchaseNav.png\" alt=\"\" />\r\n          </div>\r\n          <div class=\"sub-left-title\">采销互联</div>\r\n          <div class=\"sub-left-info\">\r\n            利用平台实现区域互采互销，支持产业链上下游企业间的供需对接，切实推进本地企业产品互采互用，实现区域内企业互利共赢，共同发展。\r\n          </div>\r\n        </div>\r\n        <div class=\"sub-purchase-right\">\r\n          <div class=\"sub-right-each\">\r\n            <div class=\"sub-right-item\" @click=\"goDemandHall\">\r\n              <div class=\"sub-right-title\">\r\n                需求大厅\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">\r\n                汇集企业发展的瓶颈和难题，谁有能力谁来揭榜\r\n              </div>\r\n            </div>\r\n            <div class=\"sub-right-item\" @click=\"goSupply\">\r\n              <div class=\"sub-right-title\">\r\n                供给大厅\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">\r\n                整合产业链资源，工业信息互联供需精准对接\r\n              </div>\r\n            </div>\r\n            <div class=\"sub-right-item\" @click=\"goCompany\">\r\n              <div class=\"sub-right-title\">\r\n                企业名录\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">\r\n                企业黄页大全，产业链上下游企业精准筛选\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"sub-right-each\">\r\n            <div class=\"sub-right-item\" @click=\"goExpertLibrary\">\r\n              <div class=\"sub-right-title\">\r\n                专家智库\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">\r\n                开放科研院所、行业专家资源、解决企业卡脖子难题\r\n              </div>\r\n            </div>\r\n            <div class=\"sub-right-item\" @click=\"goActivity\">\r\n              <div class=\"sub-right-title\">\r\n                活动广场\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">参与最新、最全的线上、线下活动</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div> -->\r\n    <!-- <div class=\"sub-purchase-left\">\r\n          <div class=\"sub-left-img\">\r\n            <img src=\"../../assets/policyDeclare/policyNav.png\" alt=\"\" />\r\n          </div>\r\n          <div class=\"sub-left-title\">政策大厅</div>\r\n          <div class=\"sub-left-info\" style=\"text-align: center\">\r\n            中央到镇街五级政府政策，一键查询\r\n          </div>\r\n        </div>\r\n        <div class=\"sub-purchase-right\">\r\n          <div class=\"sub-right-each\">\r\n            <div class=\"sub-right-item\" @click=\"goPolicyInformation\">\r\n              <div class=\"sub-right-title\">\r\n                政策资讯\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">最新政策 在线查看</div>\r\n            </div>\r\n            <div class=\"sub-right-item\">\r\n              <div class=\"sub-right-title\" @click=\"add\">\r\n                政策画像\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">权威便捷、高效查询</div>\r\n            </div>\r\n            <div class=\"sub-right-item\" @click=\"goPolicyApply\">\r\n              <div class=\"sub-right-title\">\r\n                政策申报\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">精准查询 申报无忧</div>\r\n            </div>\r\n          </div>\r\n        </div> -->\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { find, propEq, replace, filter, map } from \"ramda\";\r\n\r\nimport { constantRoutes } from \"@/router\";\r\nexport default {\r\n  data() {\r\n    const mainMenu = find(propEq(\"name\", \"main\"), constantRoutes) || {};\r\n    const menus = filter((item) => !item.hidden, mainMenu.children || []);\r\n    const $menus = map((item) => item.path, menus);\r\n    const $path = replace(\"/\", \"\", this.$route.path);\r\n    return {\r\n      activeMenu: $menus.includes($path) ? $path : \"index\",\r\n      menus,\r\n      paths: $menus,\r\n      // 顶部栏初始数\r\n      visibleNumber: 6,\r\n      // 当前激活菜单的 index\r\n      currentIndex: undefined,\r\n      mobile: \"\",\r\n      key: \"QmRlODJTVGhkNg==\",\r\n      type: \"cG9ydHJhaXQ=\",\r\n      base64EncodeChars:\r\n        \"ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",\r\n      text: {},\r\n      wwk: {},\r\n      cashType: \"cG9saWN5Y2FzaA==\",\r\n      supplyDemandDocking: [\r\n        {\r\n          title: \"需求大厅\",\r\n          desc: \"企业发布需求\",\r\n        },\r\n        {\r\n          title: \"供给大厅\",\r\n          desc: \"企业发布供给\",\r\n        },\r\n        {\r\n          title: \"解决方案\",\r\n          desc: \"提供行业问题解决策略与案例\",\r\n        },\r\n        {\r\n          title: \"应用商店\",\r\n          desc: \"汇聚工业工具与应用的下载平台\",\r\n        },\r\n        {\r\n          title: \"复材展厅\",\r\n          desc: \"展示复合材料产品与技术的平台\",\r\n        },\r\n      ],\r\n      manufacturingSharing: [\r\n        {\r\n          title: \"设备共享\",\r\n          desc: \"线上提供模具、工业设备租赁，企业在线申请\",\r\n        },\r\n        {\r\n          title: \"车间共享\",\r\n          desc: \"提供车间租赁，企业在线申请。\",\r\n        },\r\n        {\r\n          title: \"订单共享\",\r\n          desc: \"企业发布生产订单协同需求，工厂接单响应生产交付。\",\r\n        },\r\n      ],\r\n      serviceSharing: [\r\n        {\r\n          title: \"人才服务\",\r\n          desc: \"企业招聘、个人简历或能力信息，人才供需对接。\",\r\n        },\r\n        {\r\n          title: \"企业用工\",\r\n          desc: \"劳务用工信息汇集，海量高薪职位等你来选。\",\r\n        },\r\n        {\r\n          title: \"检验检测\",\r\n          desc: \"专业第三方检测机构提供检验检测服务，检测项目一键申请。\",\r\n        },\r\n        {\r\n          title: \"实验室共享\",\r\n          desc: \"实验室资源共享，低成本实现创新。\",\r\n        }\r\n      ],\r\n      innovationSharing: [\r\n        {\r\n          title: \"创业孵化\",\r\n          desc: \"提供创业基地给初创企业，了解加入共享创新园区。\",\r\n        },\r\n        {\r\n          title: \"文件共享\",\r\n          desc: \"专利、标准、商标等知识库信息开放。\",\r\n        },\r\n        {\r\n          title: \"众筹科研\",\r\n          desc: \"汇聚科研众智众筹资金，搭建成果孵化、资源共享的创新协作平台\",\r\n        },\r\n      ],\r\n      aboutUsList: [\r\n        {\r\n          title: \"平台介绍\",\r\n          desc: \"以“共享智造”赋能特色产业集群，实现资源利用的最大化。\",\r\n        },\r\n        {\r\n          title: \"动态资讯\",\r\n          desc: \"提供最新的新闻资讯、让您迅掌握产业动态。\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  watch: {\r\n    $route(route) {\r\n      const { path } = route;\r\n      const $path = replace(\"/\", \"\", path);\r\n      if (this.paths.includes($path)) {\r\n        if ($path !== this.activeMenu) {\r\n          this.activeMenu = $path;\r\n        }\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    },\r\n  },\r\n\r\n  methods: {\r\n    goSupplyDemandDocking(index) {\r\n      if (index == 2) {\r\n        this.$router.push({\r\n          path: \"/solution\",\r\n        })\r\n      } else if (index == 3) {\r\n        this.$router.push({\r\n          path: \"/appStore\",\r\n        })\r\n      } else if (index == 4) {\r\n        this.$router.push({\r\n          path: \"/compositeExhibitionHall\",\r\n        })\r\n      } else {\r\n        this.$router.push(\"/supplyDemandDocking?index=\" + index);\r\n      }\r\n    },\r\n    goManufacturingSharing(index) {\r\n      this.$router.push(\"/manufacturingSharing?index=\" + index);\r\n    },\r\n    goServiceSharing(index) {\r\n      this.$router.push(\"/serviceSharing?index=\" + index);\r\n    },\r\n\r\n    goInnovationSharing(index) {\r\n      this.$router.push(\"/innovationSharing?index=\" + index);\r\n    },\r\n\r\n    goAboutUs(index) {\r\n      this.$router.push(\"/aboutUs?index=\" + index);\r\n    },\r\n\r\n    // add() {\r\n    //   if (JSON.parse(localStorage.getItem(\"sessionObj\"))) {\r\n    //     this.text = JSON.parse(localStorage.getItem(\"sessionObj\"));\r\n    //     this.wwk = JSON.parse(this.text.data);\r\n    //     this.mobile = this.wwk.username;\r\n    //     this.mobile = this.$Base64.encode(this.mobile);\r\n    //     // this.type = this.$Base64.encode(this.type);\r\n    //     window.open(\r\n    //       `https://cyqyfw.com/index/user/login?key=${this.key}&type=${this.type}&mobile=${this.mobile}`\r\n    //     );\r\n    //   } else {\r\n    //     // window.open('https://120.221.94.235/index/policy/portrait.html')\r\n    //     // window.open('https://cyqyfw.com ')\r\n    //     window.open(\"https://cyqyfw.com/index/policy/portrait.html\");\r\n    //   }\r\n    // },\r\n    // senselessCashing() {\r\n    //   if (JSON.parse(localStorage.getItem(\"sessionObj\"))) {\r\n    //     this.text = JSON.parse(localStorage.getItem(\"sessionObj\"));\r\n    //     this.wwk = JSON.parse(this.text.data);\r\n    //     this.mobile = this.wwk.username;\r\n    //     this.mobile = this.$Base64.encode(this.mobile);\r\n    //     window.open(\r\n    //       `https://cyqyfw.com/index/user/login?key=${this.key}&type=${this.cashType}&mobile=${this.mobile}`\r\n    //     );\r\n    //   } else {\r\n    //     // window.open(\"https://120.221.94.235\");\r\n    //     window.open(\"https://cyqyfw.com \");\r\n    //   }\r\n    // },\r\n    handleSelect(index) {\r\n      console.log(index, \"----------\");\r\n      if (index && index !== \"shopping\") {\r\n        this.$router.push(`/${index}`);\r\n      } else {\r\n        window.open(\"http://61.240.145.100:1001/\");\r\n      }\r\n      //  else {\r\n      //   this.senselessCashing();\r\n      // }\r\n    },\r\n    // // 跳转到需求大厅\r\n    // goDemandHall() {\r\n    //   this.$router.push(\"/demandHall\");\r\n    // },\r\n    // // 跳转到资源大厅\r\n    // goSupply() {\r\n    //   this.$router.push(\"/resourceHall\");\r\n    // },\r\n    // // 跳转到企业名录\r\n    // goCompany() {\r\n    //   this.$router.push(\"/enterpriseList\");\r\n    // },\r\n    // // 跳转到专家智库\r\n    // goExpertLibrary() {\r\n    //   this.$router.push(\"/expertLibrary\");\r\n    // },\r\n    // // 跳转到活动广场\r\n    // goActivity() {\r\n    //   this.$router.push(\"/activitySquare\");\r\n    // },\r\n    // // 跳转政策资讯\r\n    // goPolicyInformation() {\r\n    //   this.$router.push(\"/policy?specialLoc=policyInfo\");\r\n    // },\r\n    // // 跳转政策画像\r\n    // goPolicyPortrait() {\r\n    //   this.$router.push(\"/policy?specialLoc=policyProtrait\");\r\n    // },\r\n    // // 跳转政策申报\r\n    // goPolicyApply() {\r\n    //   this.$router.push(\"/policy?specialLoc=applyPolicy\");\r\n    // },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.is-active {\r\n  background-color: #fff !important;\r\n}\r\n\r\n.head_title_line {\r\n  .titleLine {\r\n    position: relative;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      bottom: 0;\r\n      width: 0%;\r\n      width: 0%;\r\n      height: 2px;\r\n      background-color: #37c9b8;\r\n      transition: all 0.35s ease-in;\r\n      left: 0;\r\n      z-index: 1;\r\n    }\r\n  }\r\n\r\n  &:hover {\r\n    .titleLine::after {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.nav_span {\r\n  position: relative;\r\n\r\n  &::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    left: 50%;\r\n    bottom: -3px;\r\n    width: 100%;\r\n    height: 4px;\r\n    background-color: #37c9b8;\r\n    transform-origin: center;\r\n    transform: translate(-50%, 0) scaleX(0);\r\n    transition: transform 0.3s ease-in;\r\n  }\r\n\r\n  &:hover {\r\n    background-color: #fff !important;\r\n\r\n    &::before {\r\n      transform: translate(-50%, 0) scaleX(1);\r\n    }\r\n  }\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item {\r\n  height: 80px !important;\r\n  line-height: 80px !important;\r\n  color: #333333 !important;\r\n  font-size: 16px !important;\r\n  font-weight: 500 !important;\r\n  padding: 0 10px !important;\r\n  margin: 0 10px !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:hover {\r\n  color: #{\"var(--theme)\"} !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item.is-active,\r\n.el-menu--horizontal>.el-submenu.is-active .el-submenu__title {\r\n  color: #21c9b8 !important;\r\n  border-bottom: 4px solid #21c9b8 !important;\r\n  // color: #fff !important;\r\n  // background-color: #{\"var(--theme)\"} !important;\r\n  // border-bottom: none !important;\r\n}\r\n\r\n/* submenu item */\r\n.topmenu-container.el-menu--horizontal>.el-submenu .el-submenu__title {\r\n  height: 80px !important;\r\n  line-height: 80px !important;\r\n  color: #333333 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n  font-size: 16px !important;\r\n  font-weight: 500 !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(6):hover~.supplyDemandDocking {\r\n  display: block;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(3):hover~.manufacturingShare {\r\n  display: block;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(4):hover~.serviceShare {\r\n  display: block;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(5):hover~.innovationShare {\r\n  display: block;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(7):hover~.aboutUs {\r\n  display: block;\r\n}\r\n\r\n.supplyDemandDocking,\r\n.manufacturingShare,\r\n.serviceShare,\r\n.innovationShare,\r\n.aboutUs {\r\n  position: fixed;\r\n  display: none;\r\n  left: 0 !important;\r\n  top: 80px !important;\r\n  width: 100vw;\r\n  // height: 246px;\r\n  margin-top: 0;\r\n  padding: 0;\r\n  background-color: #fff;\r\n\r\n  .sub-purchase-content {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    // margin-left: 28.96vw;\r\n    // justify-content: center;\r\n    padding: 50px;\r\n\r\n    .content_item {\r\n      width: 15%;\r\n      margin-left: 5%;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        font-weight: 700;\r\n        font-size: 20px;\r\n        position: relative;\r\n        transition: all 0.2s ease-in;\r\n      }\r\n\r\n      .titleLine {\r\n        height: 2px;\r\n        width: 100%;\r\n        background-color: #999;\r\n        margin: 20px 0;\r\n      }\r\n\r\n      .desc {\r\n        font-size: 14px;\r\n        color: #999;\r\n      }\r\n    }\r\n\r\n    .content_item:hover .title {\r\n      color: #21c9b8;\r\n      // transition: all 0.6s ease-in-out;\r\n    }\r\n\r\n    .content_item:hover .titleLine {\r\n      // background-color: #21c9b8;\r\n    }\r\n\r\n    .content_item:nth-child(5n + 1) {\r\n      margin-left: 0;\r\n    }\r\n\r\n    .content_item:nth-child(n + 6) {\r\n      margin-top: 50px;\r\n    }\r\n\r\n    // .sub-purchase-left {\r\n    //   margin: 4px 80px 0 0;\r\n    //   width: 400px;\r\n    //   font-family: PingFangSC-Regular, PingFang SC;\r\n    //   .sub-left-img {\r\n    //     width: 64px;\r\n    //     height: 64px;\r\n    //     margin: 0 auto;\r\n    //     img {\r\n    //       width: 100%;\r\n    //       height: 100%;\r\n    //     }\r\n    //   }\r\n    //   .sub-left-title {\r\n    //     font-weight: 500;\r\n    //     color: #333;\r\n    //     line-height: 26px;\r\n    //     padding: 18px 0 8px 0;\r\n    //     text-align: center;\r\n    //   }\r\n    //   .sub-left-info {\r\n    //     color: #666;\r\n    //     line-height: 26px;\r\n    //     white-space: normal;\r\n    //   }\r\n    // }\r\n    // .sub-purchase-right {\r\n    //   display: flex;\r\n    //   .sub-right-each {\r\n    //     .sub-right-item {\r\n    //       cursor: pointer;\r\n    //       font-family: PingFangSC-Regular, PingFang SC;\r\n    //       .sub-right-title {\r\n    //         display: flex;\r\n    //         align-items: center;\r\n    //         font-size: 16px;\r\n    //         font-weight: 500;\r\n    //         color: #333;\r\n    //         line-height: 16px;\r\n    //       }\r\n    //       .sub-right-arrow {\r\n    //         display: none;\r\n    //       }\r\n    //       .sub-right-info {\r\n    //         color: #999999b3;\r\n    //         line-height: 14px;\r\n    //         padding-top: 12px;\r\n    //       }\r\n    //       &:hover {\r\n    //         .sub-right-title {\r\n    //           color: #21c9b8;\r\n    //         }\r\n    //         .sub-right-arrow {\r\n    //           display: block;\r\n    //         }\r\n    //       }\r\n    //       & + .sub-right-item {\r\n    //         padding-top: 32px;\r\n    //       }\r\n    //     }\r\n    //     & + .sub-right-each {\r\n    //       margin-left: 80px;\r\n    //     }\r\n    //   }\r\n    // }\r\n  }\r\n\r\n  &:hover {\r\n    display: block;\r\n  }\r\n}\r\n\r\n.show-content {\r\n  .sub-purchase-popper {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.el-menu--horizontal>.el-submenu {\r\n  &:hover {\r\n    .sub-purchase-title {\r\n      color: #21c9b8 !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}