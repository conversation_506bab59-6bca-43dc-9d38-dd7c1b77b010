/**
 * SSO单点登录前端集成工具
 * 
 * <AUTHOR>
 */

import axios from 'axios'
import { getToken, setToken, removeToken } from '@/utils/auth'

// SSO配置
const SSO_CONFIG = {
  // 主系统地址
  MAIN_SYSTEM_URL: process.env.VUE_APP_MAIN_SYSTEM_URL || 'http://localhost:9200',
  // 当前系统标识
  CURRENT_SYSTEM: 'market',
  // SSO相关接口
  SSO_LOGIN_URL: '/sso/login',
  SSO_LOGOUT_URL: '/sso/logout',
  SSO_STATUS_URL: '/sso/status',
  SSO_LOGIN_URL_API: '/sso/loginUrl',
  SSO_REDIRECT_URL: '/sso/redirect-to-main'
}

/**
 * 处理SSO登录
 * 检查URL参数中是否包含SSO Token
 */
export function handleSSOLogin() {
  const urlParams = new URLSearchParams(window.location.search)
  const token = urlParams.get('token')
  
  if (token) {
    // 如果URL中包含token，说明是SSO登录回调
    setToken(token)
    
    // 清除URL中的token参数
    const url = new URL(window.location)
    url.searchParams.delete('token')
    window.history.replaceState({}, document.title, url.pathname + url.search)
    
    return true
  }
  
  return false
}

/**
 * 检查SSO登录状态
 * 
 * @returns {Promise<Object>} SSO状态信息
 */
export function checkSSOStatus() {
  return new Promise((resolve, reject) => {
    axios.get(SSO_CONFIG.SSO_STATUS_URL)
      .then(response => {
        if (response.data && response.data.code === 0) {
          resolve(response.data.data)
        } else {
          reject(new Error('检查SSO状态失败'))
        }
      })
      .catch(error => {
        reject(error)
      })
  })
}

/**
 * 跳转到主系统登录
 * 
 * @param {string} redirectUrl 登录成功后的跳转地址
 */
export function redirectToMainSystemLogin(redirectUrl) {
  // 获取主系统登录地址
  const params = redirectUrl ? { redirect: redirectUrl } : {}
  
  axios.get(SSO_CONFIG.SSO_LOGIN_URL_API, { params })
    .then(response => {
      if (response.data && response.data.code === 0) {
        const loginUrl = response.data.data.loginUrl
        window.location.href = loginUrl
      } else {
        // 如果获取登录地址失败，直接跳转
        const fallbackUrl = `${SSO_CONFIG.MAIN_SYSTEM_URL}${SSO_CONFIG.SSO_LOGIN_URL}?target=${SSO_CONFIG.CURRENT_SYSTEM}`
        if (redirectUrl) {
          fallbackUrl += `&redirect=${encodeURIComponent(redirectUrl)}`
        }
        window.location.href = fallbackUrl
      }
    })
    .catch(error => {
      console.error('获取主系统登录地址失败:', error)
      // 降级处理：直接跳转到主系统
      const fallbackUrl = `${SSO_CONFIG.MAIN_SYSTEM_URL}${SSO_CONFIG.SSO_LOGIN_URL}?target=${SSO_CONFIG.CURRENT_SYSTEM}`
      if (redirectUrl) {
        fallbackUrl += `&redirect=${encodeURIComponent(redirectUrl)}`
      }
      window.location.href = fallbackUrl
    })
}

/**
 * SSO登出
 * 
 * @returns {Promise<boolean>} 登出结果
 */
export function ssoLogout() {
  return new Promise((resolve, reject) => {
    axios.post(SSO_CONFIG.SSO_LOGOUT_URL)
      .then(response => {
        if (response.data && response.data.code === 0) {
          // 清除本地token
          removeToken()
          resolve(true)
        } else {
          reject(new Error('SSO登出失败'))
        }
      })
      .catch(error => {
        reject(error)
      })
  })
}

/**
 * 检查是否需要SSO登录
 * 如果用户未登录且主系统已登录，则自动跳转到SSO登录
 * 
 * @returns {Promise<boolean>} 是否需要SSO登录
 */
export function checkNeedSSOLogin() {
  return new Promise((resolve) => {
    const currentToken = getToken()
    
    if (currentToken) {
      // 已有本地token，不需要SSO登录
      resolve(false)
      return
    }
    
    // 检查SSO状态
    checkSSOStatus()
      .then(status => {
        if (status.mainSystemLogin && !status.isLogin) {
          // 主系统已登录但本系统未登录，需要SSO登录
          resolve(true)
        } else {
          resolve(false)
        }
      })
      .catch(() => {
        resolve(false)
      })
  })
}

/**
 * 自动SSO登录检查
 * 在应用启动时调用，检查是否需要自动进行SSO登录
 */
export function autoSSOLoginCheck() {
  // 首先处理SSO登录回调
  if (handleSSOLogin()) {
    return Promise.resolve(true)
  }
  
  // 检查是否需要SSO登录
  return checkNeedSSOLogin().then(needSSO => {
    if (needSSO) {
      const currentUrl = window.location.href
      redirectToMainSystemLogin(currentUrl)
      return true
    }
    return false
  })
}

/**
 * SSO Vue插件
 */
export default {
  install(Vue) {
    Vue.prototype.$sso = {
      handleLogin: handleSSOLogin,
      checkStatus: checkSSOStatus,
      redirectToMain: redirectToMainSystemLogin,
      logout: ssoLogout,
      checkNeedLogin: checkNeedSSOLogin,
      autoCheck: autoSSOLoginCheck
    }
  }
}

// 导出配置供外部使用
export { SSO_CONFIG }
