{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\demandHall\\index.vue?vue&type=style&index=0&id=9fc74ad2&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\demandHall\\index.vue", "mtime": 1750311962985}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/purchaseSales/component/demandHall", "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-02-01 17:22:11\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-12-09 18:52:38\r\n-->\r\n<template>\r\n  <div class=\"demand-hall-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"purchase-banner\">\r\n      <img src=\"../../../../assets/demandHall/demandHallBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"demand-hall-title-content\">\r\n        <div class=\"demand-hall-title-box\">\r\n          <div class=\"demand-hall-divider\"></div>\r\n          <div class=\"demand-hall-title\">商机需求</div>\r\n          <div class=\"demand-hall-divider\"></div>\r\n        </div>\r\n        <div class=\"demand-hall-search-box\">\r\n          <el-form ref=\"form\" class=\"demand-hall-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.name\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"demand-hall-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"demand-hall-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"demand-hall-card\">\r\n        <div class=\"demand-hall-info-content\">\r\n          <div class=\"demand-hall-search-type-box\">\r\n            <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n              <div class=\"demand-hall-search-line\">\r\n                <el-form-item\r\n                  label=\"需求类型\"\r\n                  class=\"demand-hall-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.demandType\"\r\n                    class=\"demand-hall-search-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in demandList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"demand-hall-list-item\"\r\n            @click=\"goDemandDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img\r\n                  v-if=\"item.scenePicture && item.scenePicture.length > 0\"\r\n                  alt=\"\"\r\n                  :src=\"item.scenePicture[0].url\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/demandDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"list-item-info\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.demandTitle }}\r\n                </div>\r\n                <div class=\"list-item-text\">\r\n                  <div class=\"list-item-label\">应用领域：</div>\r\n                  <div class=\"list-item-tag-box\">\r\n                    <div\r\n                      v-for=\"(val, num) in item.applicationArea\"\r\n                      :key=\"num\"\r\n                      class=\"lilst-item-tag\"\r\n                    >\r\n                      {{ val }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"demand-hall-page-end\">\r\n            <el-button class=\"demand-hall-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            >\r\n            <el-pagination\r\n              v-if=\"data && data.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"demand-hall-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @size-change=\"handleSizeChange\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { gatewayDemendListTen } from \"@/api/purchaseSales\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        name: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        demandType: \"\", //需求类型\r\n      },\r\n      demandList: [], //资讯类型列表\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getDictsList(\"demand_type\", \"demandList\");\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      gatewayDemendListTen({\r\n        ...this.form,\r\n        ...this.formInfo,\r\n        displayStatus: 1,\r\n        auditStatus: 2,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          console.log(res);\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          console.log(res);\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.data.forEach((item) => {\r\n            item.scenePicture = item.scenePicture\r\n              ? JSON.parse(item.scenePicture)\r\n              : [];\r\n            item.applicationArea = item.applicationArea\r\n              ? item.applicationArea.split(\",\")\r\n              : \"\";\r\n          });\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n    // 跳转到详情页面\r\n    goDemandDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/demandHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.demand-hall-container {\r\n  width: 100%;\r\n  .purchase-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .demand-hall-title-content {\r\n    width: 100%;\r\n    padding-bottom: 18px;\r\n    .demand-hall-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .demand-hall-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .demand-hall-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .demand-hall-search-box {\r\n      .demand-hall-search-form {\r\n        text-align: center;\r\n        .demand-hall-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .demand-hall-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .demand-hall-card {\r\n    background: #f4f5f9;\r\n    padding-top: 40px;\r\n    .demand-hall-info-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      .demand-hall-search-type-box {\r\n        background: #fff;\r\n        margin-bottom: 17px;\r\n        .demand-hall-search-line {\r\n          padding: 14px 24px 4px;\r\n          .demand-hall-search-line-item {\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n      }\r\n      .demand-hall-list-item {\r\n        width: 100%;\r\n        background: #fff;\r\n        border-radius: 12px;\r\n        .list-item-content {\r\n          display: flex;\r\n          padding: 24px;\r\n          cursor: pointer;\r\n          .list-item-img {\r\n            width: 180px;\r\n            height: 128px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n              border-radius: 7px;\r\n            }\r\n          }\r\n          .list-item-info {\r\n            padding-left: 24px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            .list-item-title {\r\n              width: 922px;\r\n              height: 24px;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              font-size: 20px;\r\n              font-weight: 500;\r\n              color: #323233;\r\n              line-height: 20px;\r\n              margin-bottom: 26px;\r\n              word-wrap: break-word;\r\n            }\r\n            .list-item-text {\r\n              display: flex;\r\n              align-items: top;\r\n              .list-item-label {\r\n                color: #323233;\r\n                line-height: 14px;\r\n                margin-top: 14px;\r\n              }\r\n              .list-item-tag-box {\r\n                display: flex;\r\n                width: 852px;\r\n                flex-wrap: wrap;\r\n                .lilst-item-tag {\r\n                  padding: 0 12px;\r\n                  max-width: 840px;\r\n                  background: #21c9b8 1a;\r\n                  border-radius: 4px;\r\n                  font-size: 12px;\r\n                  color: #21c9b8;\r\n                  line-height: 24px;\r\n                  text-align: center;\r\n                  margin-right: 16px;\r\n                  margin-top: 10px;\r\n                  word-wrap: break-word;\r\n                  text-align: left;\r\n                }\r\n              }\r\n            }\r\n          }\r\n          &:hover {\r\n            .list-item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        & + .demand-hall-list-item {\r\n          margin-top: 24px;\r\n        }\r\n      }\r\n      .demand-hall-page-end {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        padding: 24px 0 60px;\r\n        .demand-hall-page-btn {\r\n          width: 82px;\r\n          height: 32px;\r\n          background: #fff;\r\n          border-radius: 4px;\r\n          border: 1px solid #d9d9d9;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.demand-hall-container {\r\n  .demand-hall-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .demand-hall-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .demand-hall-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .demand-hall-page-end {\r\n    .demand-hall-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}