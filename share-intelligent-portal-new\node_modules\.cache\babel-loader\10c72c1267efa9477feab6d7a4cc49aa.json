{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\innovationSharing\\components\\fileSharing\\interested.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\innovationSharing\\components\\fileSharing\\interested.vue", "mtime": 1750311962960}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ramda", "require", "_home", "data", "form", "title", "updateTime", "appliDeadlineList", "label", "value", "rules", "term", "required", "message", "trigger", "created", "userinfo", "JSON", "parse", "sessionStorage", "getItem", "companyName", "memberCompanyName", "linkMan", "companyRealName", "linkTel", "memberPhone", "$route", "query", "demandName", "intentionType", "parseInt", "fieldName", "intentionId", "methods", "onSubmit", "_this", "$refs", "validate", "valid", "submitIntention", "then", "res", "code", "$message", "success", "cancel", "error", "msg", "goDetail", "$router", "push", "go"], "sources": ["src/views/innovationSharing/components/fileSharing/interested.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">我有意向</div>\r\n      <div style=\"height: 33px; margin-top: 21px\">I have intentions</div>\r\n    </div>\r\n    <div class=\"card-container card-content\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <div class=\"imgStyle\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../../assets/device/ceshi.png\" alt=\"\" />\r\n        </div>\r\n        <div class=\"title\">{{ form.title }}</div>\r\n        <div style=\"display: flex; align-items: center; margin-top: 15px\">\r\n          <div class=\"publishTimeStyle\">发布时间：{{ updateTime }}</div>\r\n          <!-- <div class=\"detailStyle\" @click=\"goDetail\">查看详情 >></div> -->\r\n        </div>\r\n      </div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <span class=\"resourceType\">资源类型：</span>\r\n          <span class=\"resourceValue\">文件共享</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <span class=\"resourceType\">资源名称：</span>\r\n          <span class=\"resourceValue\">{{ form.title }}</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <span class=\"resourceType\">归属单位：</span>\r\n          <span class=\"resourceValue\">青岛创意科技有限公司</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"申请期限\" prop=\"term\">\r\n              <el-radio-group v-model=\"form.term\">\r\n                <el-radio v-for=\"dict in appliDeadlineList\" :key=\"dict.value\" :label=\"dict.value\" :value=\"dict.value\">{{\r\n                  dict.label }}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item label=\"意向企业：\" prop=\"companyName\">\r\n              <el-input disabled v-model=\"form.companyName\" placeholder=\"自动带出\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人：\" prop=\"linkMan\">\r\n              <el-input disabled v-model=\"form.linkMan\" placeholder=\"请先维护联系人\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话：\" prop=\"linkTel\">\r\n              <el-input disabled v-model=\"form.linkTel\" placeholder=\"请先维护联系方式\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button style=\"width: 100%; height: 50px\" type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n        <div class=\"promptStyle\">温馨提示</div>\r\n        <div class=\"desc\" style=\"margin-top: 20px\">\r\n          1、我们会在最快的时间和您取得联系（工作时间周一至周五8:00-18:00）\r\n        </div>\r\n        <div class=\"desc\" style=\"margin-top: 13px\">\r\n          2、紧急问题请拨打：15512688882\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { update } from 'ramda';\r\nimport { submitIntention } from \"@/api/home\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        title: \"\",\r\n      },\r\n      updateTime: \"2025-03-17\",\r\n      appliDeadlineList: [\r\n        {\r\n          label: \"15天\",\r\n          value: \"1\",\r\n        },\r\n        {\r\n          label: \"30天\",\r\n          value: \"2\",\r\n        },\r\n        {\r\n          label: \"45天\",\r\n          value: \"3\",\r\n        },\r\n        {\r\n          label: \"60天\",\r\n          value: \"4\",\r\n        },\r\n        {\r\n          label: \"90天\",\r\n          value: \"5\",\r\n        },\r\n      ],\r\n      rules:{\r\n        term:[\r\n          { required: true, message: '请选择申请期限', trigger: 'change' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n    if (userinfo) {\r\n      this.form.companyName = userinfo.memberCompanyName;\r\n      this.form.linkMan = userinfo.companyRealName;\r\n      this.form.linkTel = userinfo.memberPhone;\r\n    }\r\n    if (this.$route.query.demandName && this.$route.query.demandName != 'null') {\r\n      this.form.title = this.$route.query.demandName\r\n    }\r\n    if (this.$route.query.intentionType) {\r\n      this.form.intentionType = parseInt(this.$route.query.intentionType)\r\n    }\r\n    if (this.$route.query.fieldName) {\r\n      this.form.fieldName = this.$route.query.fieldName\r\n    }\r\n    if (this.$route.query.intentionId) {\r\n      this.form.intentionId = this.$route.query.intentionId\r\n    }\r\n    if (this.$route.query.updateTime && this.$route.query.updateTime != 'null' && this.$route.query.updateTime != 'undefined') {\r\n      this.updateTime = this.$route.query.updateTime\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          submitIntention(this.form).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$message.success(\"提交成功\")\r\n              this.cancel()\r\n            } else {\r\n              this.$message.error(res.msg)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    goDetail() {\r\n      this.$router.push(\"/innovationSharing\");\r\n    },\r\n    cancel(){\r\n      this.$router.go(-1)\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: rgb(242, 242, 242);\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n}\r\n\r\n.card-content {\r\n  display: flex;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: -70px;\r\n  padding: 60px 59px 62px 60px;\r\n\r\n  .card_left {\r\n    .imgStyle {\r\n      width: 330px;\r\n      height: 230px;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #333333;\r\n      margin-top: 23px;\r\n    }\r\n\r\n    .publishTimeStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n    }\r\n\r\n    .detailStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #21c9b8;\r\n      margin-left: auto;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .card_right {\r\n    margin-left: 40px;\r\n    width: 100%;\r\n\r\n    .resourceType {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .resourceValue {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #666666;\r\n    }\r\n\r\n    .footer-submit {\r\n      margin-top: 40px;\r\n    }\r\n\r\n    .promptStyle {\r\n      margin-top: 30px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .desc {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAkEA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,KAAA;MACA;MACAC,UAAA;MACAC,iBAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAC,KAAA;QACAC,IAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,cAAA,CAAAC,OAAA;IACA,IAAAJ,QAAA;MACA,KAAAZ,IAAA,CAAAiB,WAAA,GAAAL,QAAA,CAAAM,iBAAA;MACA,KAAAlB,IAAA,CAAAmB,OAAA,GAAAP,QAAA,CAAAQ,eAAA;MACA,KAAApB,IAAA,CAAAqB,OAAA,GAAAT,QAAA,CAAAU,WAAA;IACA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,UAAA,SAAAF,MAAA,CAAAC,KAAA,CAAAC,UAAA;MACA,KAAAzB,IAAA,CAAAC,KAAA,QAAAsB,MAAA,CAAAC,KAAA,CAAAC,UAAA;IACA;IACA,SAAAF,MAAA,CAAAC,KAAA,CAAAE,aAAA;MACA,KAAA1B,IAAA,CAAA0B,aAAA,GAAAC,QAAA,MAAAJ,MAAA,CAAAC,KAAA,CAAAE,aAAA;IACA;IACA,SAAAH,MAAA,CAAAC,KAAA,CAAAI,SAAA;MACA,KAAA5B,IAAA,CAAA4B,SAAA,QAAAL,MAAA,CAAAC,KAAA,CAAAI,SAAA;IACA;IACA,SAAAL,MAAA,CAAAC,KAAA,CAAAK,WAAA;MACA,KAAA7B,IAAA,CAAA6B,WAAA,QAAAN,MAAA,CAAAC,KAAA,CAAAK,WAAA;IACA;IACA,SAAAN,MAAA,CAAAC,KAAA,CAAAtB,UAAA,SAAAqB,MAAA,CAAAC,KAAA,CAAAtB,UAAA,mBAAAqB,MAAA,CAAAC,KAAA,CAAAtB,UAAA;MACA,KAAAA,UAAA,QAAAqB,MAAA,CAAAC,KAAA,CAAAtB,UAAA;IACA;EACA;EACA4B,OAAA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,qBAAA,EAAAJ,KAAA,CAAAhC,IAAA,EAAAqC,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACAP,KAAA,CAAAQ,QAAA,CAAAC,OAAA;cACAT,KAAA,CAAAU,MAAA;YACA;cACAV,KAAA,CAAAQ,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;YACA;UACA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACAL,MAAA,WAAAA,OAAA;MACA,KAAAI,OAAA,CAAAE,EAAA;IACA;EACA;AACA", "ignoreList": []}]}