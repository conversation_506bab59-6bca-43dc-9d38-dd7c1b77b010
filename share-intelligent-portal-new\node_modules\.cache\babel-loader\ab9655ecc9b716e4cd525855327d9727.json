{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\TopNav\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\TopNav\\index.vue", "mtime": 1750311962827}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ramda", "require", "_router", "data", "mainMenu", "find", "propEq", "constantRoutes", "menus", "filter", "item", "hidden", "children", "$menus", "map", "path", "$path", "replace", "$route", "activeMenu", "includes", "paths", "visibleNumber", "currentIndex", "undefined", "mobile", "key", "type", "base64EncodeChars", "text", "wwk", "cashType", "supplyDemandDocking", "title", "desc", "manufacturingSharing", "serviceSharing", "innovationSharing", "aboutUsList", "watch", "route", "computed", "theme", "$store", "state", "settings", "methods", "goSupplyDemandDocking", "index", "$router", "push", "goManufacturingSharing", "goServiceSharing", "goInnovationSharing", "goAboutUs", "handleSelect", "console", "log", "concat", "window", "open"], "sources": ["src/components/TopNav/index.vue"], "sourcesContent": ["<template>\r\n  <el-menu :default-active=\"activeMenu\" menu-trigger=\"hover\" mode=\"horizontal\" background-color=\"transparent\"\r\n    @select=\"handleSelect\">\r\n    <template v-for=\"(item, index) in menus\">\r\n      <el-menu-item :style=\"{ '--theme': '#45c9b8' }\" :index=\"item.path\" class=\"nav_span\" :key=\"index\">\r\n        {{ item.meta.title }}\r\n      </el-menu-item>\r\n    </template>\r\n    <!-- 弹窗---- -->\r\n    <!-- 供需对接 -->\r\n    <div class=\"supplyDemandDocking\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in supplyDemandDocking\" :key=\"index\"\r\n          @click=\"goSupplyDemandDocking(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 制造共享 -->\r\n    <div class=\"manufacturingShare\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in manufacturingSharing\" :key=\"index\"\r\n          @click=\"goManufacturingSharing(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 服务共享 -->\r\n    <div class=\"serviceShare\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in serviceSharing\" :key=\"index\"\r\n          @click=\"goServiceSharing(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 创新共享 -->\r\n    <div class=\"innovationShare\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in innovationSharing\" :key=\"index\"\r\n          @click=\"goInnovationSharing(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 关于我们 -->\r\n    <div class=\"aboutUs\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in aboutUsList\" :key=\"index\"\r\n          @click=\"goAboutUs(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 弹窗 -->\r\n\r\n    <!-- <div class=\"sub-purchase-popper\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"sub-purchase-left\">\r\n          <div class=\"sub-left-img\">\r\n            <img src=\"../../assets/purchaseSales/purchaseNav.png\" alt=\"\" />\r\n          </div>\r\n          <div class=\"sub-left-title\">采销互联</div>\r\n          <div class=\"sub-left-info\">\r\n            利用平台实现区域互采互销，支持产业链上下游企业间的供需对接，切实推进本地企业产品互采互用，实现区域内企业互利共赢，共同发展。\r\n          </div>\r\n        </div>\r\n        <div class=\"sub-purchase-right\">\r\n          <div class=\"sub-right-each\">\r\n            <div class=\"sub-right-item\" @click=\"goDemandHall\">\r\n              <div class=\"sub-right-title\">\r\n                需求大厅\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">\r\n                汇集企业发展的瓶颈和难题，谁有能力谁来揭榜\r\n              </div>\r\n            </div>\r\n            <div class=\"sub-right-item\" @click=\"goSupply\">\r\n              <div class=\"sub-right-title\">\r\n                供给大厅\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">\r\n                整合产业链资源，工业信息互联供需精准对接\r\n              </div>\r\n            </div>\r\n            <div class=\"sub-right-item\" @click=\"goCompany\">\r\n              <div class=\"sub-right-title\">\r\n                企业名录\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">\r\n                企业黄页大全，产业链上下游企业精准筛选\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"sub-right-each\">\r\n            <div class=\"sub-right-item\" @click=\"goExpertLibrary\">\r\n              <div class=\"sub-right-title\">\r\n                专家智库\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">\r\n                开放科研院所、行业专家资源、解决企业卡脖子难题\r\n              </div>\r\n            </div>\r\n            <div class=\"sub-right-item\" @click=\"goActivity\">\r\n              <div class=\"sub-right-title\">\r\n                活动广场\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">参与最新、最全的线上、线下活动</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div> -->\r\n    <!-- <div class=\"sub-purchase-left\">\r\n          <div class=\"sub-left-img\">\r\n            <img src=\"../../assets/policyDeclare/policyNav.png\" alt=\"\" />\r\n          </div>\r\n          <div class=\"sub-left-title\">政策大厅</div>\r\n          <div class=\"sub-left-info\" style=\"text-align: center\">\r\n            中央到镇街五级政府政策，一键查询\r\n          </div>\r\n        </div>\r\n        <div class=\"sub-purchase-right\">\r\n          <div class=\"sub-right-each\">\r\n            <div class=\"sub-right-item\" @click=\"goPolicyInformation\">\r\n              <div class=\"sub-right-title\">\r\n                政策资讯\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">最新政策 在线查看</div>\r\n            </div>\r\n            <div class=\"sub-right-item\">\r\n              <div class=\"sub-right-title\" @click=\"add\">\r\n                政策画像\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">权威便捷、高效查询</div>\r\n            </div>\r\n            <div class=\"sub-right-item\" @click=\"goPolicyApply\">\r\n              <div class=\"sub-right-title\">\r\n                政策申报\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">精准查询 申报无忧</div>\r\n            </div>\r\n          </div>\r\n        </div> -->\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { find, propEq, replace, filter, map } from \"ramda\";\r\n\r\nimport { constantRoutes } from \"@/router\";\r\nexport default {\r\n  data() {\r\n    const mainMenu = find(propEq(\"name\", \"main\"), constantRoutes) || {};\r\n    const menus = filter((item) => !item.hidden, mainMenu.children || []);\r\n    const $menus = map((item) => item.path, menus);\r\n    const $path = replace(\"/\", \"\", this.$route.path);\r\n    return {\r\n      activeMenu: $menus.includes($path) ? $path : \"index\",\r\n      menus,\r\n      paths: $menus,\r\n      // 顶部栏初始数\r\n      visibleNumber: 6,\r\n      // 当前激活菜单的 index\r\n      currentIndex: undefined,\r\n      mobile: \"\",\r\n      key: \"QmRlODJTVGhkNg==\",\r\n      type: \"cG9ydHJhaXQ=\",\r\n      base64EncodeChars:\r\n        \"ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",\r\n      text: {},\r\n      wwk: {},\r\n      cashType: \"cG9saWN5Y2FzaA==\",\r\n      supplyDemandDocking: [\r\n        {\r\n          title: \"需求大厅\",\r\n          desc: \"企业发布需求\",\r\n        },\r\n        {\r\n          title: \"供给大厅\",\r\n          desc: \"企业发布供给\",\r\n        },\r\n        {\r\n          title: \"解决方案\",\r\n          desc: \"提供行业问题解决策略与案例\",\r\n        },\r\n        {\r\n          title: \"应用商店\",\r\n          desc: \"汇聚工业工具与应用的下载平台\",\r\n        },\r\n        {\r\n          title: \"复材展厅\",\r\n          desc: \"展示复合材料产品与技术的平台\",\r\n        },\r\n      ],\r\n      manufacturingSharing: [\r\n        {\r\n          title: \"设备共享\",\r\n          desc: \"线上提供模具、工业设备租赁，企业在线申请\",\r\n        },\r\n        {\r\n          title: \"车间共享\",\r\n          desc: \"提供车间租赁，企业在线申请。\",\r\n        },\r\n        {\r\n          title: \"订单共享\",\r\n          desc: \"企业发布生产订单协同需求，工厂接单响应生产交付。\",\r\n        },\r\n      ],\r\n      serviceSharing: [\r\n        {\r\n          title: \"人才服务\",\r\n          desc: \"企业招聘、个人简历或能力信息，人才供需对接。\",\r\n        },\r\n        {\r\n          title: \"企业用工\",\r\n          desc: \"劳务用工信息汇集，海量高薪职位等你来选。\",\r\n        },\r\n        {\r\n          title: \"检验检测\",\r\n          desc: \"专业第三方检测机构提供检验检测服务，检测项目一键申请。\",\r\n        },\r\n        {\r\n          title: \"实验室共享\",\r\n          desc: \"实验室资源共享，低成本实现创新。\",\r\n        }\r\n      ],\r\n      innovationSharing: [\r\n        {\r\n          title: \"创业孵化\",\r\n          desc: \"提供创业基地给初创企业，了解加入共享创新园区。\",\r\n        },\r\n        {\r\n          title: \"文件共享\",\r\n          desc: \"专利、标准、商标等知识库信息开放。\",\r\n        },\r\n        {\r\n          title: \"众筹科研\",\r\n          desc: \"汇聚科研众智众筹资金，搭建成果孵化、资源共享的创新协作平台\",\r\n        },\r\n      ],\r\n      aboutUsList: [\r\n        {\r\n          title: \"平台介绍\",\r\n          desc: \"以“共享智造”赋能特色产业集群，实现资源利用的最大化。\",\r\n        },\r\n        {\r\n          title: \"动态资讯\",\r\n          desc: \"提供最新的新闻资讯、让您迅掌握产业动态。\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  watch: {\r\n    $route(route) {\r\n      const { path } = route;\r\n      const $path = replace(\"/\", \"\", path);\r\n      if (this.paths.includes($path)) {\r\n        if ($path !== this.activeMenu) {\r\n          this.activeMenu = $path;\r\n        }\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    },\r\n  },\r\n\r\n  methods: {\r\n    goSupplyDemandDocking(index) {\r\n      if (index == 2) {\r\n        this.$router.push({\r\n          path: \"/solution\",\r\n        })\r\n      } else if (index == 3) {\r\n        this.$router.push({\r\n          path: \"/appStore\",\r\n        })\r\n      } else if (index == 4) {\r\n        this.$router.push({\r\n          path: \"/compositeExhibitionHall\",\r\n        })\r\n      } else {\r\n        this.$router.push(\"/supplyDemandDocking?index=\" + index);\r\n      }\r\n    },\r\n    goManufacturingSharing(index) {\r\n      this.$router.push(\"/manufacturingSharing?index=\" + index);\r\n    },\r\n    goServiceSharing(index) {\r\n      this.$router.push(\"/serviceSharing?index=\" + index);\r\n    },\r\n\r\n    goInnovationSharing(index) {\r\n      this.$router.push(\"/innovationSharing?index=\" + index);\r\n    },\r\n\r\n    goAboutUs(index) {\r\n      this.$router.push(\"/aboutUs?index=\" + index);\r\n    },\r\n\r\n    // add() {\r\n    //   if (JSON.parse(localStorage.getItem(\"sessionObj\"))) {\r\n    //     this.text = JSON.parse(localStorage.getItem(\"sessionObj\"));\r\n    //     this.wwk = JSON.parse(this.text.data);\r\n    //     this.mobile = this.wwk.username;\r\n    //     this.mobile = this.$Base64.encode(this.mobile);\r\n    //     // this.type = this.$Base64.encode(this.type);\r\n    //     window.open(\r\n    //       `https://cyqyfw.com/index/user/login?key=${this.key}&type=${this.type}&mobile=${this.mobile}`\r\n    //     );\r\n    //   } else {\r\n    //     // window.open('https://120.221.94.235/index/policy/portrait.html')\r\n    //     // window.open('https://cyqyfw.com ')\r\n    //     window.open(\"https://cyqyfw.com/index/policy/portrait.html\");\r\n    //   }\r\n    // },\r\n    // senselessCashing() {\r\n    //   if (JSON.parse(localStorage.getItem(\"sessionObj\"))) {\r\n    //     this.text = JSON.parse(localStorage.getItem(\"sessionObj\"));\r\n    //     this.wwk = JSON.parse(this.text.data);\r\n    //     this.mobile = this.wwk.username;\r\n    //     this.mobile = this.$Base64.encode(this.mobile);\r\n    //     window.open(\r\n    //       `https://cyqyfw.com/index/user/login?key=${this.key}&type=${this.cashType}&mobile=${this.mobile}`\r\n    //     );\r\n    //   } else {\r\n    //     // window.open(\"https://120.221.94.235\");\r\n    //     window.open(\"https://cyqyfw.com \");\r\n    //   }\r\n    // },\r\n    handleSelect(index) {\r\n      console.log(index, \"----------\");\r\n      if (index && index !== \"shopping\") {\r\n        this.$router.push(`/${index}`);\r\n      } else {\r\n        window.open(\"http://61.240.145.100:1001/\");\r\n      }\r\n      //  else {\r\n      //   this.senselessCashing();\r\n      // }\r\n    },\r\n    // // 跳转到需求大厅\r\n    // goDemandHall() {\r\n    //   this.$router.push(\"/demandHall\");\r\n    // },\r\n    // // 跳转到资源大厅\r\n    // goSupply() {\r\n    //   this.$router.push(\"/resourceHall\");\r\n    // },\r\n    // // 跳转到企业名录\r\n    // goCompany() {\r\n    //   this.$router.push(\"/enterpriseList\");\r\n    // },\r\n    // // 跳转到专家智库\r\n    // goExpertLibrary() {\r\n    //   this.$router.push(\"/expertLibrary\");\r\n    // },\r\n    // // 跳转到活动广场\r\n    // goActivity() {\r\n    //   this.$router.push(\"/activitySquare\");\r\n    // },\r\n    // // 跳转政策资讯\r\n    // goPolicyInformation() {\r\n    //   this.$router.push(\"/policy?specialLoc=policyInfo\");\r\n    // },\r\n    // // 跳转政策画像\r\n    // goPolicyPortrait() {\r\n    //   this.$router.push(\"/policy?specialLoc=policyProtrait\");\r\n    // },\r\n    // // 跳转政策申报\r\n    // goPolicyApply() {\r\n    //   this.$router.push(\"/policy?specialLoc=applyPolicy\");\r\n    // },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.is-active {\r\n  background-color: #fff !important;\r\n}\r\n\r\n.head_title_line {\r\n  .titleLine {\r\n    position: relative;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      bottom: 0;\r\n      width: 0%;\r\n      width: 0%;\r\n      height: 2px;\r\n      background-color: #37c9b8;\r\n      transition: all 0.35s ease-in;\r\n      left: 0;\r\n      z-index: 1;\r\n    }\r\n  }\r\n\r\n  &:hover {\r\n    .titleLine::after {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.nav_span {\r\n  position: relative;\r\n\r\n  &::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    left: 50%;\r\n    bottom: -3px;\r\n    width: 100%;\r\n    height: 4px;\r\n    background-color: #37c9b8;\r\n    transform-origin: center;\r\n    transform: translate(-50%, 0) scaleX(0);\r\n    transition: transform 0.3s ease-in;\r\n  }\r\n\r\n  &:hover {\r\n    background-color: #fff !important;\r\n\r\n    &::before {\r\n      transform: translate(-50%, 0) scaleX(1);\r\n    }\r\n  }\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item {\r\n  height: 80px !important;\r\n  line-height: 80px !important;\r\n  color: #333333 !important;\r\n  font-size: 16px !important;\r\n  font-weight: 500 !important;\r\n  padding: 0 10px !important;\r\n  margin: 0 10px !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:hover {\r\n  color: #{\"var(--theme)\"} !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item.is-active,\r\n.el-menu--horizontal>.el-submenu.is-active .el-submenu__title {\r\n  color: #21c9b8 !important;\r\n  border-bottom: 4px solid #21c9b8 !important;\r\n  // color: #fff !important;\r\n  // background-color: #{\"var(--theme)\"} !important;\r\n  // border-bottom: none !important;\r\n}\r\n\r\n/* submenu item */\r\n.topmenu-container.el-menu--horizontal>.el-submenu .el-submenu__title {\r\n  height: 80px !important;\r\n  line-height: 80px !important;\r\n  color: #333333 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n  font-size: 16px !important;\r\n  font-weight: 500 !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(6):hover~.supplyDemandDocking {\r\n  display: block;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(3):hover~.manufacturingShare {\r\n  display: block;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(4):hover~.serviceShare {\r\n  display: block;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(5):hover~.innovationShare {\r\n  display: block;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(7):hover~.aboutUs {\r\n  display: block;\r\n}\r\n\r\n.supplyDemandDocking,\r\n.manufacturingShare,\r\n.serviceShare,\r\n.innovationShare,\r\n.aboutUs {\r\n  position: fixed;\r\n  display: none;\r\n  left: 0 !important;\r\n  top: 80px !important;\r\n  width: 100vw;\r\n  // height: 246px;\r\n  margin-top: 0;\r\n  padding: 0;\r\n  background-color: #fff;\r\n\r\n  .sub-purchase-content {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    // margin-left: 28.96vw;\r\n    // justify-content: center;\r\n    padding: 50px;\r\n\r\n    .content_item {\r\n      width: 15%;\r\n      margin-left: 5%;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        font-weight: 700;\r\n        font-size: 20px;\r\n        position: relative;\r\n        transition: all 0.2s ease-in;\r\n      }\r\n\r\n      .titleLine {\r\n        height: 2px;\r\n        width: 100%;\r\n        background-color: #999;\r\n        margin: 20px 0;\r\n      }\r\n\r\n      .desc {\r\n        font-size: 14px;\r\n        color: #999;\r\n      }\r\n    }\r\n\r\n    .content_item:hover .title {\r\n      color: #21c9b8;\r\n      // transition: all 0.6s ease-in-out;\r\n    }\r\n\r\n    .content_item:hover .titleLine {\r\n      // background-color: #21c9b8;\r\n    }\r\n\r\n    .content_item:nth-child(5n + 1) {\r\n      margin-left: 0;\r\n    }\r\n\r\n    .content_item:nth-child(n + 6) {\r\n      margin-top: 50px;\r\n    }\r\n\r\n    // .sub-purchase-left {\r\n    //   margin: 4px 80px 0 0;\r\n    //   width: 400px;\r\n    //   font-family: PingFangSC-Regular, PingFang SC;\r\n    //   .sub-left-img {\r\n    //     width: 64px;\r\n    //     height: 64px;\r\n    //     margin: 0 auto;\r\n    //     img {\r\n    //       width: 100%;\r\n    //       height: 100%;\r\n    //     }\r\n    //   }\r\n    //   .sub-left-title {\r\n    //     font-weight: 500;\r\n    //     color: #333;\r\n    //     line-height: 26px;\r\n    //     padding: 18px 0 8px 0;\r\n    //     text-align: center;\r\n    //   }\r\n    //   .sub-left-info {\r\n    //     color: #666;\r\n    //     line-height: 26px;\r\n    //     white-space: normal;\r\n    //   }\r\n    // }\r\n    // .sub-purchase-right {\r\n    //   display: flex;\r\n    //   .sub-right-each {\r\n    //     .sub-right-item {\r\n    //       cursor: pointer;\r\n    //       font-family: PingFangSC-Regular, PingFang SC;\r\n    //       .sub-right-title {\r\n    //         display: flex;\r\n    //         align-items: center;\r\n    //         font-size: 16px;\r\n    //         font-weight: 500;\r\n    //         color: #333;\r\n    //         line-height: 16px;\r\n    //       }\r\n    //       .sub-right-arrow {\r\n    //         display: none;\r\n    //       }\r\n    //       .sub-right-info {\r\n    //         color: #999999b3;\r\n    //         line-height: 14px;\r\n    //         padding-top: 12px;\r\n    //       }\r\n    //       &:hover {\r\n    //         .sub-right-title {\r\n    //           color: #21c9b8;\r\n    //         }\r\n    //         .sub-right-arrow {\r\n    //           display: block;\r\n    //         }\r\n    //       }\r\n    //       & + .sub-right-item {\r\n    //         padding-top: 32px;\r\n    //       }\r\n    //     }\r\n    //     & + .sub-right-each {\r\n    //       margin-left: 80px;\r\n    //     }\r\n    //   }\r\n    // }\r\n  }\r\n\r\n  &:hover {\r\n    display: block;\r\n  }\r\n}\r\n\r\n.show-content {\r\n  .sub-purchase-popper {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.el-menu--horizontal>.el-submenu {\r\n  &:hover {\r\n    .sub-purchase-title {\r\n      color: #21c9b8 !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAsKA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAE,IAAA,WAAAA,KAAA;IACA,IAAAC,QAAA,OAAAC,WAAA,MAAAC,aAAA,mBAAAC,sBAAA;IACA,IAAAC,KAAA,OAAAC,aAAA,YAAAC,IAAA;MAAA,QAAAA,IAAA,CAAAC,MAAA;IAAA,GAAAP,QAAA,CAAAQ,QAAA;IACA,IAAAC,MAAA,OAAAC,UAAA,YAAAJ,IAAA;MAAA,OAAAA,IAAA,CAAAK,IAAA;IAAA,GAAAP,KAAA;IACA,IAAAQ,KAAA,OAAAC,cAAA,gBAAAC,MAAA,CAAAH,IAAA;IACA;MACAI,UAAA,EAAAN,MAAA,CAAAO,QAAA,CAAAJ,KAAA,IAAAA,KAAA;MACAR,KAAA,EAAAA,KAAA;MACAa,KAAA,EAAAR,MAAA;MACA;MACAS,aAAA;MACA;MACAC,YAAA,EAAAC,SAAA;MACAC,MAAA;MACAC,GAAA;MACAC,IAAA;MACAC,iBAAA,EACA;MACAC,IAAA;MACAC,GAAA;MACAC,QAAA;MACAC,mBAAA,GACA;QACAC,KAAA;QACAC,IAAA;MACA,GACA;QACAD,KAAA;QACAC,IAAA;MACA,GACA;QACAD,KAAA;QACAC,IAAA;MACA,GACA;QACAD,KAAA;QACAC,IAAA;MACA,GACA;QACAD,KAAA;QACAC,IAAA;MACA,EACA;MACAC,oBAAA,GACA;QACAF,KAAA;QACAC,IAAA;MACA,GACA;QACAD,KAAA;QACAC,IAAA;MACA,GACA;QACAD,KAAA;QACAC,IAAA;MACA,EACA;MACAE,cAAA,GACA;QACAH,KAAA;QACAC,IAAA;MACA,GACA;QACAD,KAAA;QACAC,IAAA;MACA,GACA;QACAD,KAAA;QACAC,IAAA;MACA,GACA;QACAD,KAAA;QACAC,IAAA;MACA,EACA;MACAG,iBAAA,GACA;QACAJ,KAAA;QACAC,IAAA;MACA,GACA;QACAD,KAAA;QACAC,IAAA;MACA,GACA;QACAD,KAAA;QACAC,IAAA;MACA,EACA;MACAI,WAAA,GACA;QACAL,KAAA;QACAC,IAAA;MACA,GACA;QACAD,KAAA;QACAC,IAAA;MACA;IAEA;EACA;EACAK,KAAA;IACArB,MAAA,WAAAA,OAAAsB,KAAA;MACA,IAAAzB,IAAA,GAAAyB,KAAA,CAAAzB,IAAA;MACA,IAAAC,KAAA,OAAAC,cAAA,WAAAF,IAAA;MACA,SAAAM,KAAA,CAAAD,QAAA,CAAAJ,KAAA;QACA,IAAAA,KAAA,UAAAG,UAAA;UACA,KAAAA,UAAA,GAAAH,KAAA;QACA;MACA;IACA;EACA;EACAyB,QAAA;IACAC,KAAA,WAAAA,MAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,KAAA;IACA;EACA;EAEAI,OAAA;IACAC,qBAAA,WAAAA,sBAAAC,KAAA;MACA,IAAAA,KAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;UACAnC,IAAA;QACA;MACA,WAAAiC,KAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;UACAnC,IAAA;QACA;MACA,WAAAiC,KAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;UACAnC,IAAA;QACA;MACA;QACA,KAAAkC,OAAA,CAAAC,IAAA,iCAAAF,KAAA;MACA;IACA;IACAG,sBAAA,WAAAA,uBAAAH,KAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,kCAAAF,KAAA;IACA;IACAI,gBAAA,WAAAA,iBAAAJ,KAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,4BAAAF,KAAA;IACA;IAEAK,mBAAA,WAAAA,oBAAAL,KAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,+BAAAF,KAAA;IACA;IAEAM,SAAA,WAAAA,UAAAN,KAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,qBAAAF,KAAA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAO,YAAA,WAAAA,aAAAP,KAAA;MACAQ,OAAA,CAAAC,GAAA,CAAAT,KAAA;MACA,IAAAA,KAAA,IAAAA,KAAA;QACA,KAAAC,OAAA,CAAAC,IAAA,KAAAQ,MAAA,CAAAV,KAAA;MACA;QACAW,MAAA,CAAAC,IAAA;MACA;MACA;MACA;MACA;IACA,EACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;AACA", "ignoreList": []}]}