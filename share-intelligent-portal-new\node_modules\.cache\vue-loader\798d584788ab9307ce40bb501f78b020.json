{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\aboutUs\\components\\dynamicInfoDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\aboutUs\\components\\dynamicInfoDetail.vue", "mtime": 1750311962914}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBpbmZvRGV0YWlsRGF0YSwgaW5mb0RhdGEgfSBmcm9tICJAL2FwaS9ob21lIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAibmV3cyIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgZGV0YWlsOiB7fSwNCiAgICAgIG5ld3NMaXN0OiBbXSwNCiAgICAgIG5ld3NMaXN0MjogW10sDQogICAgICBuZXdzTGlzdDM6IFtdLA0KICAgICAgbmV3c0xpc3Q0OiBbXSwNCiAgICAgIG5ld3NMaXN0NTogW10sDQogICAgICBuZXdzTGlzdDY6IFtdLA0KICAgICAgbmV3c0xpc3Q3OiBbXSwNCiAgICAgIG5ld3NMaXN0ODogW10sDQogICAgICBuZXdzTGlzdDk6IFtdLA0KICAgICAgbmV3c0xpc3QxMDogW10sDQogICAgICBpZDogbnVsbCwNCiAgICAgIGNtc0xpc3Q6IFtdLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5pZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkOw0KICAgIHRoaXMuZ2V0RGF0YSgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0RGF0YSgpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgIG5ld3NJbmZvcm1hdGlvbklkOiB0aGlzLmlkLA0KICAgICAgfTsNCiAgICAgIGluZm9EZXRhaWxEYXRhKHBhcmFtcykudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5kZXRhaWwgPSByZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLmRldGFpbC5uZXdzSW5mb3JtYXRpb25Db250ZW50ID0gZGVjb2RlVVJJQ29tcG9uZW50KA0KICAgICAgICAgICAgdGhpcy5kZXRhaWwubmV3c0luZm9ybWF0aW9uQ29udGVudA0KICAgICAgICAgICk7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["dynamicInfoDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "dynamicInfoDetail.vue", "sourceRoot": "src/views/aboutUs/components", "sourcesContent": ["<template>\r\n  <div class=\"main\" v-loading=\"loading\">\r\n    <div class=\"main_l\">\r\n      <div class=\"zhuanjia_title\">{{ detail.newsInformationName || \"\" }}</div>\r\n      <div class=\"laiyuan\">\r\n        {{ detail.newsInformationDate }} 作者：{{\r\n          detail.newsInformationAuthor ? detail.newsInformationAuthor : \"\"\r\n        }}\r\n      </div>\r\n      <div class=\"news_c\" v-html=\"detail.newsInformationContent\"></div>\r\n    </div>\r\n    <div class=\"main_r\">\r\n      <div class=\"ad\">\r\n        <div style=\"color: white\">更多新闻动态</div>\r\n      </div>\r\n      <!-- <div class=\"ad_exr\">\r\n        <ul class=\"news_exr_list2\">\r\n          <li v-for=\"(item, index) in cmsList\" :key=\"index\">\r\n            <a\r\n              :href=\"'newsInformationDetail.html?id=' + item.newsInformationId\"\r\n            >\r\n              <div class=\"news_exr_l\">\r\n                <img :src=\"item.newsInformationImg\" />\r\n              </div>\r\n              <div class=\"news_exr_r\">\r\n                <p>{{ item.newsInformationName || \"\" }}</p>\r\n                <div class=\"time\">{{ item.newsInformationDate }}</div>\r\n              </div>\r\n            </a>\r\n          </li>\r\n        </ul>\r\n      </div> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { infoDetailData, infoData } from \"@/api/home\";\r\n\r\nexport default {\r\n  name: \"news\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      detail: {},\r\n      newsList: [],\r\n      newsList2: [],\r\n      newsList3: [],\r\n      newsList4: [],\r\n      newsList5: [],\r\n      newsList6: [],\r\n      newsList7: [],\r\n      newsList8: [],\r\n      newsList9: [],\r\n      newsList10: [],\r\n      id: null,\r\n      cmsList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    getData() {\r\n      let params = {\r\n        newsInformationId: this.id,\r\n      };\r\n      infoDetailData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.detail = res.data;\r\n          this.detail.newsInformationContent = decodeURIComponent(\r\n            this.detail.newsInformationContent\r\n          );\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.main {\r\n  width: 1200px;\r\n  display: flex;\r\n  flex-flow: row wrap;\r\n  justify-content: space-between;\r\n  margin-top: 0px;\r\n  margin-right: auto;\r\n  margin-bottom: 0;\r\n  margin-left: auto;\r\n  font-size: 14px;\r\n  padding-top: 40px;\r\n  padding-right: 0px;\r\n  padding-bottom: 40px;\r\n  padding-left: 0px;\r\n}\r\n.main_l {\r\n  width: 855px;\r\n}\r\n\r\n.main_r {\r\n  width: 320px;\r\n}\r\n.zhuanjia_title {\r\n  font-size: 30px;\r\n  color: #000000;\r\n  line-height: 50px;\r\n}\r\n\r\n.news_c {\r\n  padding-top: 20px;\r\n  padding-bottom: 20px;\r\n  width: 100%;\r\n\r\n  ::v-deep .ql-align-center{\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.ad {\r\n  height: 86px;\r\n  background-size: cover;\r\n  border-radius: 4px;\r\n  line-height: 86px;\r\n  font-size: 20px;\r\n  text-align: center;\r\n  background-color: #21c9b8;\r\n  cursor: pointer;\r\n}\r\n\r\n.ad_exr {\r\n  padding-left: 20px;\r\n  padding-top: 20px;\r\n  background-color: #fff;\r\n}\r\n\r\nul.news_exr_list2 {\r\n  margin: 0px;\r\n  padding-top: 10px;\r\n  padding-right: 0px;\r\n  padding-bottom: 0px;\r\n  padding-left: 0px;\r\n}\r\n\r\nul.news_exr_list2 li {\r\n  padding: 12px;\r\n  box-shadow: 0 2px 5px #eee;\r\n  border-radius: 5px;\r\n  margin-top: 15px;\r\n  margin-right: 0px;\r\n  margin-bottom: 0px;\r\n  margin-left: 0px;\r\n  border-top-width: 1px;\r\n  border-right-width: 1px;\r\n  border-bottom-width: 0px;\r\n  border-left-width: 1px;\r\n  border-top-style: solid;\r\n  border-right-style: solid;\r\n  border-bottom-style: solid;\r\n  border-left-style: solid;\r\n  border-top-color: #eee;\r\n  border-right-color: #eee;\r\n  border-bottom-color: #eee;\r\n  border-left-color: #eee;\r\n}\r\n\r\nul.news_exr_list2 li a {\r\n  display: flex;\r\n  width: 100%;\r\n  flex-flow: row wrap;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n"]}]}