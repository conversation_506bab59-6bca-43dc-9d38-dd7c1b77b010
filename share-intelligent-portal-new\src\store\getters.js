/*
 * @Author: zhc
 * @Date: 2023-02-02 11:35:59
 * @LastEditTime: 2023-02-20 18:13:32
 * @Description:
 * @LastEditors: zhc
 */
const getters = {
  sidebar: (state) => state.app.sidebar,
  size: (state) => state.app.size,
  device: (state) => state.app.device,
  dict: (state) => state.dict.dict,
  visitedViews: (state) => state.tagsView.visitedViews,
  cachedViews: (state) => state.tagsView.cachedViews,
  user: (state) => state.user,
  token: (state) => state.user.token,
  avatar: (state) => state.user.avatar,
  userId: (state) => state.user.userId,
  bussinessNo: (state) => state.user.bussinessNo,
  companyName: (state) => state.user.companyName,
  phonenumber: (state) => state.user.phonenumber,
  name: (state) => state.user.name,
  introduction: (state) => state.user.introduction,
  roles: (state) => state.user.roles,
  permissions: (state) => state.user.permissions,
  permission_routes: (state) => state.permission.routes,
  topbarRouters: (state) => state.permission.topbarRouters,
  defaultRoutes: (state) => state.permission.defaultRoutes,
  sidebarRouters: (state) => state.permission.sidebarRouters,
};
export default getters;
