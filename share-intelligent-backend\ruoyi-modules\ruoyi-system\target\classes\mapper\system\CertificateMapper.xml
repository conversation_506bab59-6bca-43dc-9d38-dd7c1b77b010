<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.CertificateMapper">
    
    <resultMap type="com.ruoyi.system.domain.Certificate" id="CertificateResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="idCard"    column="id_card"    />
        <result property="positionName"    column="position_name"    />
        <result property="positionLevel"    column="position_level"    />
        <result property="company"    column="company"    />
        <result property="certificateName"    column="certificate_name"    />
        <result property="certificateNo"    column="certificate_no"    />
        <result property="certificateType"    column="certificate_type"    />
        <result property="issueDate"    column="issue_date"    />
        <result property="validUntil"    column="valid_until"    />
        <result property="status"    column="status"    />
        <result property="certificateImage"    column="certificate_image"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCertificateVo">
        select id, name, id_card, position_name, position_level, company, certificate_name, certificate_no, certificate_type, issue_date, valid_until, status, certificate_image, create_time, update_time from certificate
    </sql>

    <select id="selectCertificateList" parameterType="com.ruoyi.system.domain.Certificate" resultMap="CertificateResult">
        <include refid="selectCertificateVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="positionName != null  and positionName != ''"> and position_name like concat('%', #{positionName}, '%')</if>
            <if test="positionLevel != null  and positionLevel != ''"> and position_level = #{positionLevel}</if>
            <if test="company != null  and company != ''"> and company like concat('%', #{company}, '%')</if>
            <if test="certificateName != null  and certificateName != ''"> and certificate_name like concat('%', #{certificateName}, '%')</if>
            <if test="certificateNo != null  and certificateNo != ''"> and certificate_no = #{certificateNo}</if>
            <if test="certificateType != null  and certificateType != ''"> and certificate_type = #{certificateType}</if>
            <if test="issueDate != null  and issueDate != ''"> and issue_date = #{issueDate}</if>
            <if test="validUntil != null  and validUntil != ''"> and valid_until = #{validUntil}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectCertificateById" parameterType="Long" resultMap="CertificateResult">
        <include refid="selectCertificateVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCertificate" parameterType="com.ruoyi.system.domain.Certificate">
        insert into certificate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="idCard != null">id_card,</if>
            <if test="positionName != null">position_name,</if>
            <if test="positionLevel != null">position_level,</if>
            <if test="company != null">company,</if>
            <if test="certificateName != null">certificate_name,</if>
            <if test="certificateNo != null">certificate_no,</if>
            <if test="certificateType != null">certificate_type,</if>
            <if test="issueDate != null">issue_date,</if>
            <if test="validUntil != null">valid_until,</if>
            <if test="status != null">status,</if>
            <if test="certificateImage != null">certificate_image,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="positionName != null">#{positionName},</if>
            <if test="positionLevel != null">#{positionLevel},</if>
            <if test="company != null">#{company},</if>
            <if test="certificateName != null">#{certificateName},</if>
            <if test="certificateNo != null">#{certificateNo},</if>
            <if test="certificateType != null">#{certificateType},</if>
            <if test="issueDate != null">#{issueDate},</if>
            <if test="validUntil != null">#{validUntil},</if>
            <if test="status != null">#{status},</if>
            <if test="certificateImage != null">#{certificateImage},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCertificate" parameterType="com.ruoyi.system.domain.Certificate">
        update certificate
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="positionName != null">position_name = #{positionName},</if>
            <if test="positionLevel != null">position_level = #{positionLevel},</if>
            <if test="company != null">company = #{company},</if>
            <if test="certificateName != null">certificate_name = #{certificateName},</if>
            <if test="certificateNo != null">certificate_no = #{certificateNo},</if>
            <if test="certificateType != null">certificate_type = #{certificateType},</if>
            <if test="issueDate != null">issue_date = #{issueDate},</if>
            <if test="validUntil != null">valid_until = #{validUntil},</if>
            <if test="status != null">status = #{status},</if>
            <if test="certificateImage != null">certificate_image = #{certificateImage},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCertificateById" parameterType="Long">
        delete from certificate where id = #{id}
    </delete>

    <delete id="deleteCertificateByIds" parameterType="String">
        delete from certificate where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 