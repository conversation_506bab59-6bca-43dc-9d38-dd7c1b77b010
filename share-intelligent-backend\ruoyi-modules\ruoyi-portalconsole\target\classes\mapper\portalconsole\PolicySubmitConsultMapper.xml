<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.PolicySubmitConsultMapper">
    
    <resultMap type="PolicySubmitConsult" id="PolicySubmitConsultResult">
        <result property="policySubmitConsultId"    column="policy_submit_consult_id"    />
        <result property="policySubmitTitle"    column="policy_submit_title"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="content"    column="content"    />
        <result property="contact"    column="contact"    />
        <result property="phone"    column="phone"    />
    </resultMap>

    <sql id="selectPolicySubmitConsultVo">
        select policy_submit_consult_id, policy_submit_title, create_by, create_time, update_by, update_time, remark, content, contact, phone from policy_submit_consult
    </sql>

    <select id="selectPolicySubmitConsultList" parameterType="PolicySubmitConsult" resultMap="PolicySubmitConsultResult">
        <include refid="selectPolicySubmitConsultVo"/>
        <where>  
            <if test="policySubmitTitle != null  and policySubmitTitle != ''"> and policy_submit_title like concat('%', #{policySubmitTitle}, '%')</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="contact != null  and contact != ''"> and contact = #{contact}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
        </where>
    </select>
    
    <select id="selectPolicySubmitConsultByPolicySubmitConsultId" parameterType="Long" resultMap="PolicySubmitConsultResult">
        <include refid="selectPolicySubmitConsultVo"/>
        where policy_submit_consult_id = #{policySubmitConsultId}
    </select>
        
    <insert id="insertPolicySubmitConsult" parameterType="PolicySubmitConsult" useGeneratedKeys="true" keyProperty="policySubmitConsultId">
        insert into policy_submit_consult
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="policySubmitTitle != null">policy_submit_title,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="content != null">content,</if>
            <if test="contact != null">contact,</if>
            <if test="phone != null">phone,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="policySubmitTitle != null">#{policySubmitTitle},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="content != null">#{content},</if>
            <if test="contact != null">#{contact},</if>
            <if test="phone != null">#{phone},</if>
         </trim>
    </insert>

    <update id="updatePolicySubmitConsult" parameterType="PolicySubmitConsult">
        update policy_submit_consult
        <trim prefix="SET" suffixOverrides=",">
            <if test="policySubmitTitle != null">policy_submit_title = #{policySubmitTitle},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="content != null">content = #{content},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="phone != null">phone = #{phone},</if>
        </trim>
        where policy_submit_consult_id = #{policySubmitConsultId}
    </update>

    <delete id="deletePolicySubmitConsultByPolicySubmitConsultId" parameterType="Long">
        delete from policy_submit_consult where policy_submit_consult_id = #{policySubmitConsultId}
    </delete>

    <delete id="deletePolicySubmitConsultByPolicySubmitConsultIds" parameterType="String">
        delete from policy_submit_consult where policy_submit_consult_id in 
        <foreach item="policySubmitConsultId" collection="array" open="(" separator="," close=")">
            #{policySubmitConsultId}
        </foreach>
    </delete>
</mapper>