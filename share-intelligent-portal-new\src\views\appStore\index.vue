<template>
  <div class="content">
    <div class="content_banner">
      <div style="height: 37px">应用商店</div>
      <div style="height: 33px; margin-top: 41px;font-size: 18px;">针对场景痛点，提供成熟完善的数字化应用产品</div>
      <div class="activity-title-content">
        <div class="activity-search-box">
          <el-form ref="form" class="activity-search-form">
            <el-form-item>
              <el-input v-model="keywords" placeholder="请输入搜索内容" class="activity-search-input">
                <el-button slot="append" class="activity-search-btn" @click="onSearch">搜索</el-button>
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="appliType">
      <div class="everyType" v-for="(item, index) in appliTypeData" :key="item.dictValue"
        @click="getappliData(item.dictValue)">
        <div class="everyImg">
          <img :src="appliTypeImgList[index].url" alt="" />
        </div>
        <div class="everyTitle">{{ item.dictLabel }}</div>
        <div class="everyIcon" v-show="flag === item.dictValue"></div>
      </div>
    </div>
    <!-- 底部内容 -->
    <div v-loading="loading">
      <div class="content_bottom" v-if="demandList && demandList.length > 0">
        <div class="demandContent">
          <div class="app-list">
            <div v-for="(item, index) in demandList" :key="index" class="detailDemand tr2">
              <span class="bottom"></span>
              <span class="right"></span>
              <span class="top"></span>
              <span class="left"></span>
              <div @click="goDetail(item.appStoreId)"  style="height: 260px; ">
                <div class="detailTitle textOverflow1">{{ item.appStoreName }}</div>
                <div class="desc textOverflow2">{{ item.appStoreIntroduction }}</div>
                <div class="demandTopRightflex" v-if="item.appLabel" style="margin-top: 30px">
                  <div   class="alink">{{ item.appLabel }}</div>
                </div>
                <div class="demandTopRightflex" style="color: #21c9b8;position: absolute;bottom: 53px;left: 20px;">
                  {{ item.appStorePrice }} 元
                </div>
                <div class="btn1">
                  <div   style="color: #21c9b8;">立即获取
                    <i class="el-icon-right"
                      style="width: 24px;height: 24px;float: right;margin-top: 13px;font-size: 24px;"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="none-class" v-else>
        <el-image style="width: 160px; height: 160px" :src="require('@/assets/user/none.png')"></el-image>
        <div class="text">暂无数据</div>
      </div>
      <!-- 分页 -->
      <div class="pageStyle">
        <el-pagination v-if="demandList && demandList.length > 0" background layout="prev, pager, next"
          class="activity-pagination" :page-size="pageSize" :current-page="pageNum" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { getAppStoreList } from "@/api/appStore";
import "@/assets/styles/index.css";

// :href="`applicationInfo.html?id=${item.appStoreId}`"
export default {
  name: "demandHall",
  data() {
    return {
      pageNum: 1,
      pageSize: 10,
      total: 0,
      keywords: "",
      form: {},
      flag: "",
      appliTypeData: [],
      appliTypeImgList: [
        {
          url: require("@/assets/appliMarket/type1.png"),
        },
        {
          url: require("@/assets/appliMarket/type2.png"),
        },
        {
          url: require("@/assets/appliMarket/type3.png"),
        },
        {
          url: require("@/assets/appliMarket/type4.png"),
        },
        {
          url: require("@/assets/appliMarket/type5.png"),
        },
        {
          url: require("@/assets/appliMarket/type6.png"),
        },
        {
          url: require("@/assets/appliMarket/type7.png"),
        },
        {
          url: require("@/assets/appliMarket/type8.png"),
        },
        {
          url: require("@/assets/appliMarket/type9.png"),
        },
      ],
      demandList: [],
    };
  },
  created() {
    this.getDictList();
    this.getList();
  },
  methods: {
    /** 查询字典数据列表 */
    getDictList() {
      let params = { dictType: "app_store_type" };
      listData(params).then((response) => {
        this.appliTypeData = response.rows;
        this.appliTypeData.unshift({ dictValue: "", dictLabel: "全部" });
      });
    },
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        appStoreType: this.flag,
        appStoreName: this.keywords,
      };
      getAppStoreList(params).then((res) => {
        if (res.code === 200) {
          this.demandList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    onSearch() {
      this.getList();
    },
    getappliData(value) {
      console.log(value)
      this.flag = value;
      this.pageNum = 1;
      this.getList();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    goDetail(id) {
      this.$router.push("/appStoreInfo?id=" + id);
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  padding-bottom: 60px;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  padding-top: 71px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
}

.activity-title-content {
  width: 100%;

  // background-color: #fff;
  .activity-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;

    .activity-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }

    .activity-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }

  .activity-search-box {
    margin-top: 40px;

    .activity-search-form {
      text-align: center;

      .activity-search-input {
        width: 792px;
        height: 54px;

        .activity-search-btn {
          width: 100px;
        }
      }
    }
  }
}

.appliType {
  width: 1200px;
  margin: 40px auto 0;
  display: flex;
  justify-content: space-between;

  .everyType {
    width: 102px;
    // height: 160px;
    text-align: center;
    cursor: pointer;

    .everyImg {
      width: 63px;
      height: 78px;
      margin-left: calc((100% - 63px) / 2);

      img {
        width: 100%;
        height: 100%;
      }
    }

    .everyTitle {
      font-size: 18px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #979797;
      margin-top: 10px;
    }

    .everyIcon {
      width: 63px;
      height: 4px;
      background: #21c9b8;
      margin-top: 10px;
      margin-left: calc((100% - 63px) / 2);
    }
  }
}

.content_bottom {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.pageStyle {
  margin-top: 60px;
  width: 100%;
  text-align: center;
}
</style>

<style lang="scss">
.activity-search-input {
  .el-input__inner {
    height: 54px;
    background: #fff;
    border-radius: 27px 0 0 27px;
    border: 1px solid #d9d9d9;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    line-height: 24px;
    padding-left: 30px;
  }

  .el-input-group__append {
    border-radius: 0px 100px 100px 0px;
    background: #21c9b8;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    color: #fff;
    line-height: 24px;
  }
}

.none-class {
  background-color: #fff;
  text-align: center;
  padding: 8% 0;

  .text {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
  }
}
</style>


<style lang="scss" scoped>
.swiper-pagination-bullet {
  background: #fff;
}

.swiper-wrapper {
  position: relative;
}

.swiper-container {
  width: 100%;
}

.swiper-container2 {
  width: 100%;
  overflow: hidden;
}


/* NEW */
.activity-container {
  width: 100%;
  background: #ffffff;

  .activity-banner {
    width: 100%;
    height: 500px;
    position: relative;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.activity-search-box {
  width: 800px;
  margin: 0 auto 0;

  .activity-search-form {
    text-align: center;

    .activity-search-input {
      width: 792px;
      height: 54px;

      .activity-search-btn {
        width: 100px;
      }
    }
  }
}

#inputBox {
  float: left;
  width: 678px;

  background: #fff;
  border-radius: 2px 0 0 2px;

  font-size: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 24px;
  padding-left: 30px;
  height: 48px;
  border: transparent;
}

.btn {
  float: right;
  line-height: 50px;
  box-sizing: border-box;
  height: 50px;
  width: 90px;
  background: #21c9b8;
  font-size: 16px;
  color: #fff;
  border: 1px solid #d9d9d9;
}

.btn:hover {
  color: #fff;
}


.detailDemand {
  width: 285px;
  height: 280px;
  float: left;
  margin-right: 20px;
  margin-bottom: 20px;
  position: relative;
  box-shadow: 0px 4px 8px 3px rgba(38, 74, 116, .2);
  cursor: pointer;
  position: relative;
  span {
      position: absolute;
      z-index: 1;
      background-color: #37c9b8;
      transition: transform 0.5s ease;
    }
    .bottom,
    .top {
      height: 2px;
      left: -1px;
      right: -1px;
      transform: scaleX(0);
    }
    .left,
    .right {
      width: 2px;
      top: -1px;
      bottom: -1px;
      transform: scaleY(0);
    }
    .bottom {
      bottom: -1px;
      transform-origin: bottom right;
    }
    .right {
      right: -1px;
      transform-origin: top right;
    }
    .top {
      top: -1px;
      transform-origin: top left;
    }
    .left {
      left: -1px;
      transform-origin: bottom left;
    }
}

.detailDemand:nth-child(4n) {
  margin-right: 0;
}

.detailDemand:hover {
  box-shadow: -3px 3px 20px 0px rgba(132, 212, 178, 0.6);
  .top {
      transform-origin: top right;
      transform: scaleX(1);
    }
    .left {
      transform-origin: top left;
      transform: scaleY(1);
    }
    .bottom {
      transform-origin: bottom left;
      transform: scaleX(1);
    }
    .right {
      transform-origin: bottom right;
      transform: scaleY(1);
    }
}

.alink {
  width: 90px;
  height: 36px;
  border: 1px solid #21c9b8;
  border-radius: 21px;
  font-size: 16px;
  font-weight: 500;
  color: #21c9b8;
  text-align: center;
  line-height: 36px;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  word-wrap: break-word;
}

.app-list {
  width: 1240px;
  margin: 0 auto;
  overflow: hidden;
  padding: 20px;
}

.detailDemand a:hover .detailTitle {
  color: #21c9b8;
}

.detailTitle {
  height: 30px;
  color: rgba(51, 51, 51, 1);
  font-size: 18px;
  margin-bottom: 10px;
}

.textOverflow2 {
  height: 65px;
  line-height: 22px;
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.detailDemand .btn1 {
  position: absolute;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 50px;
  line-height: 50px;
  background-color: rgba(247, 248, 250, 1);
  font-size: 14px;
  color: #21c9b8;
  padding: 0px 15px;
  display: none;
}

.detailDemand:hover .btn1 {
  display: block;
}
</style>