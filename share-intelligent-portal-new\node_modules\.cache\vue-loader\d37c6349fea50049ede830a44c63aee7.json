{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Navbar.vue?vue&type=style&index=0&id=d16d6306&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Navbar.vue", "mtime": 1750311962846}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2MA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Navbar.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- <div>您好，欢迎来到复合材料共享智造工业互联网平台!</div> -->\r\n    <div class=\"navbar\">\r\n      <div class=\"left-menu\" @click=\"goIndex\">\r\n        <div>\r\n          <img :src=\"logoImg\" class=\"navbar-logo\" />\r\n        </div>\r\n        <div class=\"platTitle\">易复材共享智造<br />工业互联网平台</div>\r\n      </div>\r\n      <div\r\n        class=\"navbar-body\"\r\n        style=\"display: flex; justify-content: space-around\"\r\n      >\r\n        <top-nav id=\"topmenu-container\" class=\"topmenu-container\" />\r\n        <!-- <div style=\"display: flex; text-align: right\">\r\n        <div class=\"chengyang\">\r\n          <div style=\"margin-top: 10px\">\r\n            <img\r\n              style=\"width: 50px; height: 50px\"\r\n              src=\"../../assets/images/chengyang.jpg\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div style=\"font-size: 12px; color: #ccc\">扫码进入小程序</div>\r\n          <div class=\"chengyangBlock\">\r\n            <img src=\"../../assets/images/chengyang.jpg\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"yunduanyanfa\">\r\n          <div style=\"margin-top: 10px\">\r\n            <img\r\n              style=\"width: 50px; height: 50px\"\r\n              src=\"../../assets/images/yunduanyanfa.jpg\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div style=\"font-size: 12px; color: #ccc\">扫码进入企业端</div>\r\n          <div class=\"yunduanyanfaBlock\">\r\n            <img src=\"../../assets/images/yunduanyanfa.jpg\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n      </div>\r\n      <div class=\"right-menu\">\r\n        <!-- <div class=\"suppot\" @click=\"ifale = !ifale\" v-if=\"token\">技术支持</div> -->\r\n        <!-- v-if=\"token\" -->\r\n        <template v-if=\"token\">\r\n          <el-dropdown\r\n            class=\"avatar-container right-menu-item hover-effect\"\r\n            trigger=\"click\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <span class=\"name\">{{ name }}</span>\r\n              <el-image\r\n                class=\"userAvatar\"\r\n                icon=\"el-icon-user-solid\"\r\n                :size=\"36\"\r\n                :src=\"avatar ? avatar : require('@/assets/images/avatar.png')\"\r\n              ></el-image>\r\n              <i class=\"el-icon-arrow-down\" />\r\n            </div>\r\n\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <router-link to=\"/user/profile\">\r\n                <el-dropdown-item>个人中心</el-dropdown-item>\r\n              </router-link>\r\n              <el-dropdown-item divided @click.native=\"logout\">\r\n                <span>退出登录</span>\r\n              </el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n          <!-- <div class=\"cont\" v-show=\"ifale\">\r\n          <p class=\"sum\">13843272866</p>\r\n          <p class=\"sum\">17685863516</p>\r\n          <p class=\"question\">技术问题请拨打</p>\r\n        </div> -->\r\n        </template>\r\n\r\n        <template v-else>\r\n          <div class=\"login-container\">\r\n            <el-button size=\"small\" type=\"primary\" @click=\"login()\" class=\"dl\"\r\n              >登录</el-button\r\n            >\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from \"vuex\";\r\nimport logoImg from \"@/assets/images/home/<USER>\";\r\nimport Breadcrumb from \"@/components/Breadcrumb\";\r\nimport TopNav from \"@/components/TopNav\";\r\nimport Hamburger from \"@/components/Hamburger\";\r\nimport Screenfull from \"@/components/Screenfull\";\r\nimport SizeSelect from \"@/components/SizeSelect\";\r\nimport Search from \"@/components/HeaderSearch\";\r\nimport RuoYiGit from \"@/components/RuoYi/Git\";\r\nimport RuoYiDoc from \"@/components/RuoYi/Doc\";\r\nimport { getTicket, removeTicket } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  components: {\r\n    Breadcrumb,\r\n    TopNav,\r\n    Hamburger,\r\n    Screenfull,\r\n    SizeSelect,\r\n    Search,\r\n    RuoYiGit,\r\n    RuoYiDoc,\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\", \"sidebar\", \"avatar\", \"name\", \"device\"]),\r\n    setting: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings;\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch(\"settings/changeSetting\", {\r\n          key: \"showSettings\",\r\n          value: val,\r\n        });\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      logoImg,\r\n      mobile: \"\",\r\n      key: \"QmRlODJTVGhkNg==\",\r\n      type: \"cG9saWN5Y2FzaA==\",\r\n      url: \"\",\r\n      text: {},\r\n      wwk: {},\r\n      // visible: false,\r\n      ifale: false,\r\n      base64EncodeChars:\r\n        \"ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n    if (userinfo) {\r\n      this.$store.commit(\"SET_NAME\", userinfo.memberRealName);\r\n      this.$store.commit(\"SET_AVATAR\", userinfo.avatar);\r\n    }\r\n  },\r\n  methods: {\r\n    login() {\r\n      this.$router.push(\"/login\");\r\n    },\r\n    add() {\r\n      if (JSON.parse(localStorage.getItem(\"sessionObj\"))) {\r\n        this.text = JSON.parse(localStorage.getItem(\"sessionObj\"));\r\n        this.wwk = JSON.parse(this.text.data);\r\n        this.mobile = this.wwk.username;\r\n        this.mobile = this.$Base64.encode(this.mobile);\r\n        window.open(\r\n          `https://cyqyfw.com/index/user/login?key=${this.key}&type=${this.type}&mobile=${this.mobile}`\r\n        );\r\n      } else {\r\n        // window.open(\"https://120.221.94.235\");\r\n        window.open(\"https://cyqyfw.com \");\r\n      }\r\n    },\r\n    toggleSideBar() {\r\n      this.$store.dispatch(\"app/toggleSideBar\");\r\n    },\r\n    async logout() {\r\n      this.$confirm(\"确定注销并退出系统吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            if (getTicket()) {\r\n              window.location.href =\r\n                \"https://qyzhfw.chengyang.gov.cn/sso/logout?redirectUrl=https://qyfw.chengyang.gov.cn/index\";\r\n              removeTicket();\r\n            } else {\r\n              location.href = \"/index\";\r\n              localStorage.removeItem(\"sessionObj\");\r\n            }\r\n          });\r\n          sessionStorage.removeItem(\"userinfo\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    goIndex() {\r\n      this.$router.push({\r\n        path: \"/index\",\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cont {\r\n  width: 180px;\r\n  height: 140px;\r\n  text-align: center;\r\n  background-color: #fff;\r\n  border: 1px solid #bbb;\r\n  position: fixed;\r\n  top: 60px;\r\n  right: 80px;\r\n\r\n  .sum {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .question {\r\n    color: rgba(0, 21, 41, 0.678);\r\n  }\r\n}\r\n\r\n.navbar {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  height: 80px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: #fff;\r\n  box-shadow: 0 1px 4px 0 rgba(0, 21, 41, 0.12);\r\n  background: rgb(197, 241, 236);\r\n  background: linear-gradient(\r\n    180deg,\r\n    rgba(197, 241, 236, 1) 34%,\r\n    rgba(245, 255, 254, 1) 99%\r\n  );\r\n  &-body {\r\n    min-width: 1030px;\r\n    // min-width: 1200px;\r\n    // min-width: 66.6%;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n\r\n    .wgdx {\r\n      position: fixed;\r\n      right: 46.5%;\r\n      top: 27.5px;\r\n    }\r\n\r\n    .wgdx:hover {\r\n      color: #c52622;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .errLog-container {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .left-menu {\r\n    display: flex;\r\n    justify-content: center;\r\n    // width: 200px;\r\n    align-items: center;\r\n    margin-left: 20px;\r\n    cursor: pointer;\r\n\r\n    .platTitle {\r\n      width: 128px;\r\n      height: 36px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      font-size: 16px;\r\n      color: #111111;\r\n      line-height: 20px;\r\n      margin-left: 10px;\r\n    }\r\n\r\n    .navbar-logo {\r\n      width: 63px;\r\n      height: 40px;\r\n      object-fit: contain;\r\n    }\r\n  }\r\n\r\n  .right-menu {\r\n    width: 220px;\r\n    height: 100%;\r\n    padding-right: 32px;\r\n    display: flex;\r\n    justify-content: right;\r\n\r\n    .suppot {\r\n      width: 80px;\r\n      height: 40px;\r\n      margin-top: 28px;\r\n      color: #21c9b8;\r\n      border: none;\r\n      background-color: #fff;\r\n    }\r\n\r\n    .suppot:hover {\r\n      cursor: pointer;\r\n    }\r\n\r\n    // .wgdx {\r\n    //   position: fixed;\r\n    //   right: 46.5%;\r\n    //   top: 27.5px;\r\n    // }\r\n    // .wgdx:hover {\r\n    //   color: #c52622;\r\n    //   cursor: pointer;\r\n    // }\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n\r\n    .right-menu-item {\r\n      display: inline-block;\r\n      padding: 0 8px;\r\n      height: 100%;\r\n      font-size: 18px;\r\n      color: #5a5e66;\r\n      vertical-align: text-bottom;\r\n\r\n      &.hover-effect {\r\n        cursor: pointer;\r\n        transition: background 0.3s;\r\n\r\n        &:hover {\r\n          background: rgba(0, 0, 0, 0.025);\r\n        }\r\n      }\r\n    }\r\n\r\n    .avatar-container {\r\n      display: flex;\r\n      align-items: center;\r\n      flex-direction: row;\r\n      position: relative;\r\n\r\n      .avatar-wrapper {\r\n        display: flex;\r\n        align-items: center;\r\n        flex-direction: row;\r\n\r\n        .userAvatar {\r\n          width: 36px;\r\n          height: 36px;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        .name {\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 14px;\r\n          margin-right: 12px;\r\n        }\r\n\r\n        .el-icon-arrow-down {\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          font-weight: bold;\r\n          margin-left: 8px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .login-container {\r\n      .technology {\r\n        color: #21c9b8;\r\n        border: none;\r\n        background-color: #fff;\r\n        width: 120px;\r\n      }\r\n\r\n      .technology:hover {\r\n        cursor: pointer;\r\n      }\r\n\r\n      display: flex;\r\n      // width: 240px;\r\n      height: 80px;\r\n      line-height: 80px;\r\n      align-items: center;\r\n      position: relative;\r\n    }\r\n  }\r\n}\r\n\r\n.chengyang {\r\n  width: 100px;\r\n  height: 83px;\r\n  text-align: center;\r\n}\r\n\r\n.yunduanyanfa {\r\n  width: 100px;\r\n  height: 83px;\r\n  text-align: center;\r\n  margin-left: 20px;\r\n}\r\n\r\n.chengyangBlock {\r\n  position: fixed;\r\n  top: 80px;\r\n  right: calc((100% - 1200px) / 2);\r\n  margin-right: 5%;\r\n  display: none;\r\n}\r\n\r\n.chengyang:hover {\r\n  .chengyangBlock {\r\n    display: block;\r\n  }\r\n}\r\n\r\n.yunduanyanfaBlock {\r\n  position: fixed;\r\n  top: 80px;\r\n  right: calc((100% - 1200px) / 2);\r\n  margin-right: 1%;\r\n  display: none;\r\n}\r\n\r\n.yunduanyanfa:hover {\r\n  .yunduanyanfaBlock {\r\n    display: block;\r\n  }\r\n}\r\n</style>\r\n"]}]}