<template>
  <div>
    <talentPool v-if="currentIndex == 0"></talentPool>
    <employmentInfo v-if="currentIndex == 1"></employmentInfo>
    <detectingSharing v-if="currentIndex == 2"></detectingSharing>
    <laboratorySharing v-if="currentIndex == 3"></laboratorySharing>
    <certificateQuery v-if="currentIndex == 4"></certificateQuery>
  </div>
</template>
<script>
import talentPool from "./components/talentPool/index.vue";
import employmentInfo from "./components/employmentInfo/index.vue";
import detectingSharing from "./components/detectingSharing/index.vue";
import laboratorySharing from "./components/laboratorySharing/index.vue";
import certificateQuery from "./components/certificateQuery/index.vue";
export default {
  name: "serviceSharing",
  components: {
    talentPool,
    employmentInfo,
    detectingSharing,
    laboratorySharing,
    certificateQuery,
  },
  data() {
    return {
      currentIndex: 0,
    };
  },
  watch: {
    "$route.query.index": {
      handler(val) {
        this.currentIndex = val || 0;
      },
      deep: true,
    },
  },

  methods: {},
  created() {
    this.currentIndex = this.$route.query.index || 0;
  },
};
</script>
<style></style>
