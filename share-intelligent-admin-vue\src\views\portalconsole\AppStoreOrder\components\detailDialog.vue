<template>
    <!-- 订单详情 -->
    <el-dialog :title="title" :visible.sync="open" width="70%"  >
        <el-form ref="form" :model="form" label-width="120px">
            <!-- <el-row :span="24" :gutter="10">
              <el-col :span="10">
                <h3 >待发货</h3>
                <p>平台将于2023-08-04日前发货，感谢您的支持!如您对订单有疑问，可联系客服4008-939-365</p>
              </el-col>
              <el-col :span="7">
                <p>订单金额:<strong>0.01</strong></p>
              </el-col>
              <el-col :span="7">
                <p style="color: rgb(0, 176, 133);">前往试用</p>
              </el-col>
            </el-row> -->
            <el-row>
              <h3>基本信息</h3>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="订单编号:" prop="">
                        {{ this.form.appStoreOrderNo }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="下单时间:" prop="">
                        {{ this.form.orderTime }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="销售公司:" prop="">
                        {{ this.form.saleCompanyName }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="购买公司:" prop="">
                      {{ this.form.buyCompanyName }}
                    </el-form-item>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="12">
                    <el-form-item label="价格:" prop="">
                      {{ this.form.appStorePrice }}
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="备注:" prop="">
                      {{ this.form.remark }}
                    </el-form-item>
                </el-col>
            </el-row>
            <!-- <el-row>
              <h1>商品规格信息</h1>
            </el-row>
            <el-table :data="tableData1" style="width: 100%">
              <el-table-column prop="" label="规格" width="180">
              </el-table-column>
              <el-table-column prop="name" label="使用用户数" width="180">
              </el-table-column>
              <el-table-column prop="address" label="有效时间">
              </el-table-column>
            </el-table>
            <el-row>
              <h1>商品价格信息</h1>
            </el-row>
            <el-table :data="tableData1" style="width: 100%">
              <el-table-column prop="" label="订货编码" width="180">
              </el-table-column>
              <el-table-column prop="" label="商品原价（元）" width="180">
              </el-table-column>
              <el-table-column prop="" label="商品分佣比例（%）">
              </el-table-column>
            </el-table>
            <el-row>
              <h1>商品参数信息</h1>
            </el-row>
            <el-row>
              <el-form-item label="服务咨询电话:">
                <p>898998908</p>
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item label="产品运营联系人手机号:">
                <p>89899890877777</p>
              </el-form-item>
            </el-row> -->
          </el-form>
  
    </el-dialog>
  </template>
  <script>
  import {formatDate} from '@/utils/time'

  export default {
    name: "detailDialog",
    props: {
  
    },
    data() {
      return {
        title:'应用详情',
        activeName: 'first',
        open: false,
        tableData1:[],
        form:{},
      };
    },
    created() {
  
    },
    methods: {
      /**
        * 显示弹框
        */
      async show(form) {
        this.form = form
        this.form.orderTime=formatDate(this.form.orderTime)
        this.open = true; // 切换显示
      },
      handleClick(tab, event) {
        console.log(tab, event);
      },
  
  
    }
  };
  </script>
  <style>
  h3{
    color: black;
  }
</style>