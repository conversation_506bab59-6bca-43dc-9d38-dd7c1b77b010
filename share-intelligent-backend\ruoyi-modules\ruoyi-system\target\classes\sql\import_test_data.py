#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试数据导入脚本
作者: AI助手
日期: 2025-03-08
"""

import pymysql
import sys
import os
import time

# 设置控制台编码
if sys.platform == 'win32':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)

def main():
    # 数据库连接配置
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': 'root',
        'db': 'ry-cloud',
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }

    # SQL文件路径
    sql_file_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'test_data.sql')

    try:
        # 读取SQL文件内容
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()

        # 连接数据库
        print("正在连接数据库...")
        conn = pymysql.connect(**config)
        cursor = conn.cursor()
        
        # 分割SQL语句
        sql_commands = sql_content.split(';')
        
        # 执行SQL命令
        print("开始执行SQL脚本...")
        for command in sql_commands:
            command = command.strip()
            if command:
                print(f"执行: {command[:60]}...")  # 只打印前60个字符
                cursor.execute(command)
                print(f"影响的行数: {cursor.rowcount}")
                time.sleep(0.1)  # 稍微暂停一下，避免执行过快
        
        # 提交事务
        conn.commit()
        print("SQL脚本执行完成！")
        
    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
            print("数据库连接已关闭")

if __name__ == "__main__":
    main()
