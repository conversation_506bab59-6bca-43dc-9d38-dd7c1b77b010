{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\supplyDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\supplyDetail.vue", "mtime": 1750311963023}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_home", "require", "name", "data", "loading", "pageSize", "total", "pageNum", "title", "tableData", "imageList", "detailData", "id", "created", "$route", "query", "getData", "methods", "_this", "params", "supplyDetailData", "then", "res", "code", "getImage", "imageUrl", "alFileDetailVOs", "length", "fileFullPath", "jumpIntention", "_this2", "userInfo", "JSON", "parse", "sessionStorage", "getItem", "memberCompanyName", "$confirm", "confirmButtonText", "cancelButtonText", "type", "cancelButtonClass", "confirmButtonClass", "$router", "push", "catch", "concat", "updateTime", "applicationAreaName"], "sources": ["src/views/supplyDemandDocking/components/supplyDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"demanddetailbg\" v-loading=\"loading\">\r\n    <div class=\"card-container\">\r\n      <div class=\"demandDetaitop\" style=\"height: 280px\">\r\n        <div class=\"carouselDemand\" style=\"height: 200px\">\r\n          <img :src=\"getImage()\" style=\"width: 85%; height: 100%\" />\r\n          <!-- <el-carousel height=\"200px\">\r\n              <el-carousel-item v-for=\"item in imageList\" :key=\"item\">\r\n                <img :src=\"item\" style=\"width: 100%; height: 100%\" />\r\n              </el-carousel-item>\r\n            </el-carousel> -->\r\n        </div>\r\n        <div class=\"demandTopRight\" style=\"height: 200px\">\r\n          <div class=\"demandTopRighttitle\">\r\n            <el-tooltip\r\n              class=\"item\"\r\n              effect=\"dark\"\r\n              :content=\"title\"\r\n              placement=\"top\"\r\n            >\r\n              <span> {{ title }}</span>\r\n            </el-tooltip>\r\n          </div>\r\n\r\n          <div class=\"demandTopRightflex\">\r\n            <div class=\"detailrightTitle\">发布时间：</div>\r\n            <div class=\"detailrightContent\">{{ detailData.createTime }}</div>\r\n          </div>\r\n          <div class=\"demandTopRightflex\">\r\n            <div class=\"detailrightTitle\">供给方：</div>\r\n            <div class=\"detailrightContent\">{{ detailData.organization }}</div>\r\n          </div>\r\n          <!-- <div class=\"demandTopRightflex\">\r\n            <div class=\"detailrightTitle\">应用领域：</div>\r\n            <div class=\"detailrightContent\">\r\n              {{ detailData.pplicationAreaName }}\r\n            </div>\r\n          </div> -->\r\n          <div class=\"demandTopRightflex\">\r\n            <div class=\"detailrightTitle\">技术类别：</div>\r\n            <div class=\"detailrightContent\">\r\n              {{ detailData.technologyCategoryName }}\r\n            </div>\r\n          </div>\r\n          <div class=\"demandTopRightflex\">\r\n            <div class=\"detailrightTitle\">产品阶段：</div>\r\n            <div class=\"detailrightContent\">\r\n              {{ detailData.supplyProcessName }}\r\n            </div>\r\n          </div>\r\n          <div class=\"demandTopRightflex\">\r\n            <div class=\"intentionBtn\" @click=\"jumpIntention\">我要对接</div>\r\n            <!-- <a>\r\n                <div class=\"intentionBtn\" @click=\"jumpIntention()\">\r\n                  <img\r\n                    src=\"../images/new/intentionIcon.png\"\r\n                    style=\"margin-right: 10px; width: 15px; height: 15px\"\r\n                  />我有意向\r\n                </div>\r\n              </a> -->\r\n            <!-- <div class=\"onlineBtn\" @click=\"opChat\" v-if=\"showUser\">\r\n                <img\r\n                  src=\"../images/new/onlineIcon.png\"\r\n                  style=\"margin-right: 10px\"\r\n                />在线沟通\r\n              </div> -->\r\n            <!-- <div\r\n                class=\"onlineBtn collectBtn\"\r\n                @click=\"handleCollect(id, isLike, likeDelID)\"\r\n              >\r\n                <span\r\n                  v-if=\"isLike == '1'\"\r\n                  style=\"margin-right: 10px\"\r\n                  class=\"iconfont\"\r\n                  >&#xe8c6;</span\r\n                >\r\n                <span v-else style=\"margin-right: 10px\" class=\"iconfont\"\r\n                  >&#xe8b9;</span\r\n                >\r\n                {{ isLike == \"1\" ? \"取消收藏\" : \"立即收藏\" }}\r\n              </div> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"demandDetailbottom\">\r\n        <div style=\"width: 100%; height: 10px\">\r\n          <div class=\"demandDetailTitle\">供给描述</div>\r\n        </div>\r\n        <div class=\"demandDetailContent\" v-html=\"detailData.description\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { supplyDetailData } from \"@/api/home\";\r\nexport default {\r\n  name: \"demandDetail\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageSize: 10,\r\n      total: 0,\r\n      pageNum: 1,\r\n      title: \"\",\r\n      tableData: [],\r\n      imageList: [],\r\n      detailData: {},\r\n      id: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    getData() {\r\n      this.loading = true;\r\n      let params = { id: this.id };\r\n      supplyDetailData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.detailData = res.data;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    getImage() {\r\n      if (this.detailData.imageUrl) {\r\n        return this.detailData.imageUrl;\r\n      } else if (\r\n        this.detailData.alFileDetailVOs &&\r\n        this.detailData.alFileDetailVOs.length > 0\r\n      ) {\r\n        return this.detailData.alFileDetailVOs[0].fileFullPath;\r\n      } else {\r\n        return require(\"@/assets/demand/xqimgdefault.png\");\r\n      }\r\n    },\r\n    jumpIntention() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => {});\r\n        return;\r\n      } else {\r\n        this.$router.push(`/demandInterested?demandName=${this.detailData.title}&updateTime=${this.detailData.updateTime}&applicationAreaName=${this.detailData.applicationAreaName}&intentionType=2&fieldName=供给大厅&intentionId=${this.detailData.id}`);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.demanddetailbg {\r\n  background: #f7f8fa;\r\n  padding: 20px 0;\r\n}\r\n.demandDetaitop {\r\n  background: white;\r\n  height: 348px;\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  justify-content: flex-start;\r\n}\r\n.carouselDemand {\r\n  width: 334px;\r\n  height: 268px;\r\n  margin: 40px;\r\n}\r\n.demandDetailbottom {\r\n  background: white;\r\n  min-height: 500px;\r\n  padding: 20px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.demandTopRight {\r\n  height: 268px;\r\n  margin: 40px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.demandTopRighttitle {\r\n  width: 500px;\r\n  color: rgba(51, 51, 51, 1);\r\n  font-size: 24px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.demandTopRightflex {\r\n  display: flex;\r\n  line-height: 24px;\r\n}\r\n\r\n.detailrightTitle {\r\n  color: rgba(153, 153, 153, 1);\r\n  font-size: 14px;\r\n}\r\n\r\n.detailrightContent {\r\n  color: rgba(51, 51, 51, 1);\r\n  font-size: 14px;\r\n}\r\n\r\n.intentionBtn {\r\n  width: 110px;\r\n  height: 40px;\r\n  line-height: 40px;\r\n  border-radius: 4px;\r\n  background-color: #21c9b8;\r\n  color: rgba(255, 255, 255, 1);\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n  cursor: pointer;\r\n}\r\n\r\n.onlineBtn {\r\n  width: 110px;\r\n  height: 40px;\r\n  line-height: 40px;\r\n  border-radius: 4px;\r\n  background-color: rgba(247, 154, 71, 0.2);\r\n  color: rgba(247, 154, 71, 1);\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.collectBtn {\r\n  margin-left: 20px;\r\n  border-radius: 4px;\r\n  background-color: #ffffff;\r\n  border: 1px solid #f79a47;\r\n  color: #f79a47;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.demandDetailTitle {\r\n  // width: 8%;\r\n  border-left: 3px solid #21c9b8;\r\n  height: 21px;\r\n  color: rgba(16, 16, 16, 1);\r\n  font-size: 20px;\r\n  line-height: 21px;\r\n  padding-left: 10px;\r\n  float: left;\r\n}\r\n\r\n.demandDetailContent {\r\n  padding: 10px;\r\n  color: rgba(102, 102, 102, 1);\r\n  font-size: 16px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.textOverflow {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n.textOverflow1 {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 1;\r\n  -webkit-box-orient: vertical;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AA8FA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,OAAA;MACAC,KAAA;MACAC,SAAA;MACAC,SAAA;MACAC,UAAA;MACAC,EAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,EAAA,QAAAE,MAAA,CAAAC,KAAA,CAAAH,EAAA;IACA,KAAAI,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAd,OAAA;MACA,IAAAe,MAAA;QAAAP,EAAA,OAAAA;MAAA;MACA,IAAAQ,sBAAA,EAAAD,MAAA,EAAAE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAL,KAAA,CAAAP,UAAA,GAAAW,GAAA,CAAAnB,IAAA;UACAe,KAAA,CAAAd,OAAA;QACA;MACA;IACA;IACAoB,QAAA,WAAAA,SAAA;MACA,SAAAb,UAAA,CAAAc,QAAA;QACA,YAAAd,UAAA,CAAAc,QAAA;MACA,WACA,KAAAd,UAAA,CAAAe,eAAA,IACA,KAAAf,UAAA,CAAAe,eAAA,CAAAC,MAAA,MACA;QACA,YAAAhB,UAAA,CAAAe,eAAA,IAAAE,YAAA;MACA;QACA,OAAA3B,OAAA;MACA;IACA;IACA4B,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,cAAA,CAAAC,OAAA;MACA,MAAAJ,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAK,iBAAA;QACA,KAAAC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;UACAC,iBAAA;UACAC,kBAAA;QACA,GACArB,IAAA;UACAS,MAAA,CAAAa,OAAA,CAAAC,IAAA;QACA,GACAC,KAAA;QACA;MACA;QACA,KAAAF,OAAA,CAAAC,IAAA,iCAAAE,MAAA,MAAAnC,UAAA,CAAAH,KAAA,kBAAAsC,MAAA,MAAAnC,UAAA,CAAAoC,UAAA,2BAAAD,MAAA,MAAAnC,UAAA,CAAAqC,mBAAA,sEAAAF,MAAA,MAAAnC,UAAA,CAAAC,EAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}