{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\supplyDetail.vue?vue&type=style&index=0&id=6bf94fd6&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\supplyDetail.vue", "mtime": 1750311963023}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["supplyDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "supplyDetail.vue", "sourceRoot": "src/views/supplyDemandDocking/components", "sourcesContent": ["<template>\r\n  <div class=\"demanddetailbg\" v-loading=\"loading\">\r\n    <div class=\"card-container\">\r\n      <div class=\"demandDetaitop\" style=\"height: 280px\">\r\n        <div class=\"carouselDemand\" style=\"height: 200px\">\r\n          <img :src=\"getImage()\" style=\"width: 85%; height: 100%\" />\r\n          <!-- <el-carousel height=\"200px\">\r\n              <el-carousel-item v-for=\"item in imageList\" :key=\"item\">\r\n                <img :src=\"item\" style=\"width: 100%; height: 100%\" />\r\n              </el-carousel-item>\r\n            </el-carousel> -->\r\n        </div>\r\n        <div class=\"demandTopRight\" style=\"height: 200px\">\r\n          <div class=\"demandTopRighttitle\">\r\n            <el-tooltip\r\n              class=\"item\"\r\n              effect=\"dark\"\r\n              :content=\"title\"\r\n              placement=\"top\"\r\n            >\r\n              <span> {{ title }}</span>\r\n            </el-tooltip>\r\n          </div>\r\n\r\n          <div class=\"demandTopRightflex\">\r\n            <div class=\"detailrightTitle\">发布时间：</div>\r\n            <div class=\"detailrightContent\">{{ detailData.createTime }}</div>\r\n          </div>\r\n          <div class=\"demandTopRightflex\">\r\n            <div class=\"detailrightTitle\">供给方：</div>\r\n            <div class=\"detailrightContent\">{{ detailData.organization }}</div>\r\n          </div>\r\n          <!-- <div class=\"demandTopRightflex\">\r\n            <div class=\"detailrightTitle\">应用领域：</div>\r\n            <div class=\"detailrightContent\">\r\n              {{ detailData.pplicationAreaName }}\r\n            </div>\r\n          </div> -->\r\n          <div class=\"demandTopRightflex\">\r\n            <div class=\"detailrightTitle\">技术类别：</div>\r\n            <div class=\"detailrightContent\">\r\n              {{ detailData.technologyCategoryName }}\r\n            </div>\r\n          </div>\r\n          <div class=\"demandTopRightflex\">\r\n            <div class=\"detailrightTitle\">产品阶段：</div>\r\n            <div class=\"detailrightContent\">\r\n              {{ detailData.supplyProcessName }}\r\n            </div>\r\n          </div>\r\n          <div class=\"demandTopRightflex\">\r\n            <div class=\"intentionBtn\" @click=\"jumpIntention\">我要对接</div>\r\n            <!-- <a>\r\n                <div class=\"intentionBtn\" @click=\"jumpIntention()\">\r\n                  <img\r\n                    src=\"../images/new/intentionIcon.png\"\r\n                    style=\"margin-right: 10px; width: 15px; height: 15px\"\r\n                  />我有意向\r\n                </div>\r\n              </a> -->\r\n            <!-- <div class=\"onlineBtn\" @click=\"opChat\" v-if=\"showUser\">\r\n                <img\r\n                  src=\"../images/new/onlineIcon.png\"\r\n                  style=\"margin-right: 10px\"\r\n                />在线沟通\r\n              </div> -->\r\n            <!-- <div\r\n                class=\"onlineBtn collectBtn\"\r\n                @click=\"handleCollect(id, isLike, likeDelID)\"\r\n              >\r\n                <span\r\n                  v-if=\"isLike == '1'\"\r\n                  style=\"margin-right: 10px\"\r\n                  class=\"iconfont\"\r\n                  >&#xe8c6;</span\r\n                >\r\n                <span v-else style=\"margin-right: 10px\" class=\"iconfont\"\r\n                  >&#xe8b9;</span\r\n                >\r\n                {{ isLike == \"1\" ? \"取消收藏\" : \"立即收藏\" }}\r\n              </div> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"demandDetailbottom\">\r\n        <div style=\"width: 100%; height: 10px\">\r\n          <div class=\"demandDetailTitle\">供给描述</div>\r\n        </div>\r\n        <div class=\"demandDetailContent\" v-html=\"detailData.description\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { supplyDetailData } from \"@/api/home\";\r\nexport default {\r\n  name: \"demandDetail\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageSize: 10,\r\n      total: 0,\r\n      pageNum: 1,\r\n      title: \"\",\r\n      tableData: [],\r\n      imageList: [],\r\n      detailData: {},\r\n      id: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    getData() {\r\n      this.loading = true;\r\n      let params = { id: this.id };\r\n      supplyDetailData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.detailData = res.data;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    getImage() {\r\n      if (this.detailData.imageUrl) {\r\n        return this.detailData.imageUrl;\r\n      } else if (\r\n        this.detailData.alFileDetailVOs &&\r\n        this.detailData.alFileDetailVOs.length > 0\r\n      ) {\r\n        return this.detailData.alFileDetailVOs[0].fileFullPath;\r\n      } else {\r\n        return require(\"@/assets/demand/xqimgdefault.png\");\r\n      }\r\n    },\r\n    jumpIntention() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => {});\r\n        return;\r\n      } else {\r\n        this.$router.push(`/demandInterested?demandName=${this.detailData.title}&updateTime=${this.detailData.updateTime}&applicationAreaName=${this.detailData.applicationAreaName}&intentionType=2&fieldName=供给大厅&intentionId=${this.detailData.id}`);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.demanddetailbg {\r\n  background: #f7f8fa;\r\n  padding: 20px 0;\r\n}\r\n.demandDetaitop {\r\n  background: white;\r\n  height: 348px;\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  flex-wrap: nowrap;\r\n  justify-content: flex-start;\r\n}\r\n.carouselDemand {\r\n  width: 334px;\r\n  height: 268px;\r\n  margin: 40px;\r\n}\r\n.demandDetailbottom {\r\n  background: white;\r\n  min-height: 500px;\r\n  padding: 20px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.demandTopRight {\r\n  height: 268px;\r\n  margin: 40px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  flex-direction: column;\r\n  justify-content: space-between;\r\n}\r\n\r\n.demandTopRighttitle {\r\n  width: 500px;\r\n  color: rgba(51, 51, 51, 1);\r\n  font-size: 24px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.demandTopRightflex {\r\n  display: flex;\r\n  line-height: 24px;\r\n}\r\n\r\n.detailrightTitle {\r\n  color: rgba(153, 153, 153, 1);\r\n  font-size: 14px;\r\n}\r\n\r\n.detailrightContent {\r\n  color: rgba(51, 51, 51, 1);\r\n  font-size: 14px;\r\n}\r\n\r\n.intentionBtn {\r\n  width: 110px;\r\n  height: 40px;\r\n  line-height: 40px;\r\n  border-radius: 4px;\r\n  background-color: #21c9b8;\r\n  color: rgba(255, 255, 255, 1);\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 20px;\r\n  cursor: pointer;\r\n}\r\n\r\n.onlineBtn {\r\n  width: 110px;\r\n  height: 40px;\r\n  line-height: 40px;\r\n  border-radius: 4px;\r\n  background-color: rgba(247, 154, 71, 0.2);\r\n  color: rgba(247, 154, 71, 1);\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n}\r\n\r\n.collectBtn {\r\n  margin-left: 20px;\r\n  border-radius: 4px;\r\n  background-color: #ffffff;\r\n  border: 1px solid #f79a47;\r\n  color: #f79a47;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.demandDetailTitle {\r\n  // width: 8%;\r\n  border-left: 3px solid #21c9b8;\r\n  height: 21px;\r\n  color: rgba(16, 16, 16, 1);\r\n  font-size: 20px;\r\n  line-height: 21px;\r\n  padding-left: 10px;\r\n  float: left;\r\n}\r\n\r\n.demandDetailContent {\r\n  padding: 10px;\r\n  color: rgba(102, 102, 102, 1);\r\n  font-size: 16px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.textOverflow {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n}\r\n\r\n.textOverflow1 {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 1;\r\n  -webkit-box-orient: vertical;\r\n}\r\n</style>\r\n"]}]}