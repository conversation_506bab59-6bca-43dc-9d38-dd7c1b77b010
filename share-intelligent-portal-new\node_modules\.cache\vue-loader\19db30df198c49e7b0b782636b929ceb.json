{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\activitySquare\\detail.vue?vue&type=template&id=43fb02f4&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\activitySquare\\detail.vue", "mtime": 1750311962983}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}