{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\addDemandAndSupply.vue?vue&type=style&index=0&id=5532178a&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\addDemandAndSupply.vue", "mtime": 1750311962953}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICJ+QC9hc3NldHMvc3R5bGVzL21peGluLnNjc3MiOw0KLmRlbWFuZC1zdXBwbHktcGFnZSB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmNGY1Zjk7DQogIHBhZGRpbmctYm90dG9tOiA4MHB4Ow0KICAmLWhlYWRlciB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCiAgICAuYmFubmVyIHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgaGVpZ2h0OiA1NDBweDsNCiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7DQogICAgICBpbWcgew0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgaGVpZ2h0OiA1NDBweDsNCiAgICAgICAgb2JqZWN0LWZpdDogZmlsbDsNCiAgICAgIH0NCiAgICB9DQogICAgLmJvZHkgew0KICAgICAgcGFkZGluZzogNjBweCAwOw0KICAgIH0NCiAgfQ0KICAmLXRpdGxlIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgcGFkZGluZzogNjBweCAwIDQwcHggMDsNCiAgICAudG9nZ2xlIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgIGZsZXgtc2hyaW5rOiAwOw0KICAgICAgd2lkdGg6IDMyMHB4Ow0KICAgICAgaGVpZ2h0OiA2MHB4Ow0KICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgICAgIGJveC1zaGFkb3c6IDAgNHB4IDE2cHggMCByZ2JhKDM4LCA3NCwgMTE2LCAwLjEpOw0KICAgICAgYm9yZGVyLXJhZGl1czogMzBweDsNCiAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgICYtaXRlbSB7DQogICAgICAgIEBpbmNsdWRlIGZsZXhDZW50ZXI7DQogICAgICAgIHdpZHRoOiAxNjBweDsNCiAgICAgICAgaGVpZ2h0OiA2MHB4Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiAzMHB4Ow0KICAgICAgICBmb250LXNpemU6IDIwcHg7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgIGNvbG9yOiAjMjFjOWI4Ow0KICAgICAgICBsaW5lLWhlaWdodDogMjBweDsNCiAgICAgICAgdHJhbnNpdGlvbjogY29sb3IsIGJhY2tncm91bmQtY29sb3IgMC40NW1zIGVhc2U7DQogICAgICAgICYuYWN0aXZlIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjFjOWI4Ow0KICAgICAgICAgIGNvbG9yOiAjZmZmZmZmOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQogIC5kZW1hbmQtc3VwcGx5LWZvcm0gew0KICAgIEBpbmNsdWRlIGZsZXhDZW50ZXI7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCiAgICBwYWRkaW5nOiA4MHB4IDA7DQogICAgbWFyZ2luLWJvdHRvbTogODBweDsNCiAgICAudGFiLWNhcmQgew0KICAgICAgZGlzcGxheTogbm9uZTsNCiAgICAgIG1pbi1oZWlnaHQ6IDEwODBweDsNCiAgICAgIHRyYW5zaXRpb246IGRpc3BsYXkgMC40NW1zIGVhc2U7DQogICAgICAmLmFjdGl2ZSB7DQogICAgICAgIGRpc3BsYXk6IGJsb2NrOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["addDemandAndSupply.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addDemandAndSupply.vue", "sourceRoot": "src/views/form", "sourcesContent": ["<template>\r\n  <div class=\"demand-supply-page\">\r\n    <div class=\"demand-supply-page-header\">\r\n      <div class=\"banner\">\r\n        <img\r\n          src=\"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230211/1676086235866518.webp\"\r\n          alt=\"发布需求/发布资源\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <div class=\"demand-supply-page-title\">\r\n      <div class=\"toggle\">\r\n        <div\r\n          @click=\"onChangeTab('demand')\"\r\n          :class=\"{ active: tab === 'demand' }\"\r\n          class=\"toggle-item\"\r\n        >\r\n          发布需求\r\n        </div>\r\n        <div\r\n          @click=\"onChangeTab('supply')\"\r\n          :class=\"{ active: tab === 'supply' }\"\r\n          class=\"toggle-item\"\r\n        >\r\n          发布资源\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container demand-supply-form\">\r\n      <div :class=\"{ active: tab === 'demand' }\" class=\"tab-card\">\r\n        <demand-form />\r\n      </div>\r\n      <div :class=\"{ active: tab === 'supply' }\" class=\"tab-card\">\r\n        <supply-form />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport DemandForm from \"./components/demandForm.vue\";\r\nimport SupplyForm from \"./components/supplyForm.vue\";\r\nexport default {\r\n  name: \"addDemandAndSupply\",\r\n  components: {\r\n    DemandForm,\r\n    SupplyForm,\r\n  },\r\n  data() {\r\n    return {\r\n      tab: \"demand\",\r\n    };\r\n  },\r\n  created() {\r\n    this.onInit();\r\n  },\r\n  methods: {\r\n    onInit() {\r\n      const type = this.$route.query.type;\r\n      if (type) {\r\n        this.onChangeTab(type);\r\n      }\r\n    },\r\n    onChangeTab(key) {\r\n      if (this.tab !== key) {\r\n        this.tab = key;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n.demand-supply-page {\r\n  background-color: #f4f5f9;\r\n  padding-bottom: 80px;\r\n  &-header {\r\n    background-color: #ffffff;\r\n    .banner {\r\n      width: 100%;\r\n      height: 540px;\r\n      background-color: #f5f5f5;\r\n      img {\r\n        width: 100%;\r\n        height: 540px;\r\n        object-fit: fill;\r\n      }\r\n    }\r\n    .body {\r\n      padding: 60px 0;\r\n    }\r\n  }\r\n  &-title {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    padding: 60px 0 40px 0;\r\n    .toggle {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      flex-shrink: 0;\r\n      width: 320px;\r\n      height: 60px;\r\n      background: #ffffff;\r\n      box-shadow: 0 4px 16px 0 rgba(38, 74, 116, 0.1);\r\n      border-radius: 30px;\r\n      cursor: pointer;\r\n      &-item {\r\n        @include flexCenter;\r\n        width: 160px;\r\n        height: 60px;\r\n        border-radius: 30px;\r\n        font-size: 20px;\r\n        font-weight: 500;\r\n        color: #21c9b8;\r\n        line-height: 20px;\r\n        transition: color, background-color 0.45ms ease;\r\n        &.active {\r\n          background-color: #21c9b8;\r\n          color: #ffffff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .demand-supply-form {\r\n    @include flexCenter;\r\n    background-color: #ffffff;\r\n    padding: 80px 0;\r\n    margin-bottom: 80px;\r\n    .tab-card {\r\n      display: none;\r\n      min-height: 1080px;\r\n      transition: display 0.45ms ease;\r\n      &.active {\r\n        display: block;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}