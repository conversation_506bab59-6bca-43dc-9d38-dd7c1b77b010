{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\Crontab\\min.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\Crontab\\min.vue", "mtime": 1750311962792}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "radioValue", "cycle01", "cycle02", "average01", "average02", "checkboxList", "checkNum", "$options", "propsData", "check", "name", "props", "methods", "radioChange", "$emit", "cycleTotal", "averageTotal", "checkboxString", "cycleChange", "averageChange", "checkboxChange", "watch", "computed", "str", "join"], "sources": ["src/components/Crontab/min.vue"], "sourcesContent": ["<template>\r\n\t<el-form size=\"small\">\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"1\">\r\n\t\t\t\t分钟，允许的通配符[, - * /]\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"2\">\r\n\t\t\t\t周期从\r\n\t\t\t\t<el-input-number v-model='cycle01' :min=\"0\" :max=\"58\" /> -\r\n\t\t\t\t<el-input-number v-model='cycle02' :min=\"cycle01 ? cycle01 + 1 : 1\" :max=\"59\" /> 分钟\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"3\">\r\n\t\t\t\t从\r\n\t\t\t\t<el-input-number v-model='average01' :min=\"0\" :max=\"58\" /> 分钟开始，每\r\n\t\t\t\t<el-input-number v-model='average02' :min=\"1\" :max=\"59 - average01 || 0\" /> 分钟执行一次\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\r\n\t\t<el-form-item>\r\n\t\t\t<el-radio v-model='radioValue' :label=\"4\">\r\n\t\t\t\t指定\r\n\t\t\t\t<el-select clearable v-model=\"checkboxList\" placeholder=\"可多选\" multiple style=\"width:100%\">\r\n\t\t\t\t\t<el-option v-for=\"item in 60\" :key=\"item\" :value=\"item-1\">{{item-1}}</el-option>\r\n\t\t\t\t</el-select>\r\n\t\t\t</el-radio>\r\n\t\t</el-form-item>\r\n\t</el-form>\r\n\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tradioValue: 1,\r\n\t\t\tcycle01: 1,\r\n\t\t\tcycle02: 2,\r\n\t\t\taverage01: 0,\r\n\t\t\taverage02: 1,\r\n\t\t\tcheckboxList: [],\r\n\t\t\tcheckNum: this.$options.propsData.check\r\n\t\t}\r\n\t},\r\n\tname: 'crontab-min',\r\n\tprops: ['check', 'cron'],\r\n\tmethods: {\r\n\t\t// 单选按钮值变化时\r\n\t\tradioChange() {\r\n\t\t\tswitch (this.radioValue) {\r\n\t\t\t\tcase 1:\r\n\t\t\t\t\tthis.$emit('update', 'min', '*', 'min');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 2:\r\n\t\t\t\t\tthis.$emit('update', 'min', this.cycleTotal, 'min');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 3:\r\n\t\t\t\t\tthis.$emit('update', 'min', this.averageTotal, 'min');\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase 4:\r\n\t\t\t\t\tthis.$emit('update', 'min', this.checkboxString, 'min');\r\n\t\t\t\t\tbreak;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 周期两个值变化时\r\n\t\tcycleChange() {\r\n\t\t\tif (this.radioValue == '2') {\r\n\t\t\t\tthis.$emit('update', 'min', this.cycleTotal, 'min');\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 平均两个值变化时\r\n\t\taverageChange() {\r\n\t\t\tif (this.radioValue == '3') {\r\n\t\t\t\tthis.$emit('update', 'min', this.averageTotal, 'min');\r\n\t\t\t}\r\n\t\t},\r\n\t\t// checkbox值变化时\r\n\t\tcheckboxChange() {\r\n\t\t\tif (this.radioValue == '4') {\r\n\t\t\t\tthis.$emit('update', 'min', this.checkboxString, 'min');\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t},\r\n\twatch: {\r\n\t\t'radioValue': 'radioChange',\r\n\t\t'cycleTotal': 'cycleChange',\r\n\t\t'averageTotal': 'averageChange',\r\n\t\t'checkboxString': 'checkboxChange',\r\n\t},\r\n\tcomputed: {\r\n\t\t// 计算两个周期值\r\n\t\tcycleTotal: function () {\r\n\t\t\tconst cycle01 = this.checkNum(this.cycle01, 0, 58)\r\n\t\t\tconst cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : 1, 59)\r\n\t\t\treturn cycle01 + '-' + cycle02;\r\n\t\t},\r\n\t\t// 计算平均用到的值\r\n\t\taverageTotal: function () {\r\n\t\t\tconst average01 = this.checkNum(this.average01, 0, 58)\r\n\t\t\tconst average02 = this.checkNum(this.average02, 1, 59 - average01 || 0)\r\n\t\t\treturn average01 + '/' + average02;\r\n\t\t},\r\n\t\t// 计算勾选的checkbox值合集\r\n\t\tcheckboxString: function () {\r\n\t\t\tlet str = this.checkboxList.join();\r\n\t\t\treturn str == '' ? '*' : str;\r\n\t\t}\r\n\t}\r\n}\r\n</script>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAqCA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,OAAA;MACAC,OAAA;MACAC,SAAA;MACAC,SAAA;MACAC,YAAA;MACAC,QAAA,OAAAC,QAAA,CAAAC,SAAA,CAAAC;IACA;EACA;EACAC,IAAA;EACAC,KAAA;EACAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,aAAAb,UAAA;QACA;UACA,KAAAc,KAAA;UACA;QACA;UACA,KAAAA,KAAA,uBAAAC,UAAA;UACA;QACA;UACA,KAAAD,KAAA,uBAAAE,YAAA;UACA;QACA;UACA,KAAAF,KAAA,uBAAAG,cAAA;UACA;MACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,SAAAlB,UAAA;QACA,KAAAc,KAAA,uBAAAC,UAAA;MACA;IACA;IACA;IACAI,aAAA,WAAAA,cAAA;MACA,SAAAnB,UAAA;QACA,KAAAc,KAAA,uBAAAE,YAAA;MACA;IACA;IACA;IACAI,cAAA,WAAAA,eAAA;MACA,SAAApB,UAAA;QACA,KAAAc,KAAA,uBAAAG,cAAA;MACA;IACA;EAEA;EACAI,KAAA;IACA;IACA;IACA;IACA;EACA;EACAC,QAAA;IACA;IACAP,UAAA,WAAAA,WAAA;MACA,IAAAd,OAAA,QAAAK,QAAA,MAAAL,OAAA;MACA,IAAAC,OAAA,QAAAI,QAAA,MAAAJ,OAAA,EAAAD,OAAA,GAAAA,OAAA;MACA,OAAAA,OAAA,SAAAC,OAAA;IACA;IACA;IACAc,YAAA,WAAAA,aAAA;MACA,IAAAb,SAAA,QAAAG,QAAA,MAAAH,SAAA;MACA,IAAAC,SAAA,QAAAE,QAAA,MAAAF,SAAA,UAAAD,SAAA;MACA,OAAAA,SAAA,SAAAC,SAAA;IACA;IACA;IACAa,cAAA,WAAAA,eAAA;MACA,IAAAM,GAAA,QAAAlB,YAAA,CAAAmB,IAAA;MACA,OAAAD,GAAA,eAAAA,GAAA;IACA;EACA;AACA", "ignoreList": []}]}