{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\supplyDemand\\index.vue?vue&type=style&index=0&id=138fdf9b&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\supplyDemand\\index.vue", "mtime": 1750311963085}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCByZ2IoMjI1LCAyNDcsIDI0MCksIHJnYigyNDQsIDI1MiwgMjUwKSk7DQogIGhlaWdodDogMTA4MHB4Ow0KfQ0KLm5hdlN0eWxlIHsNCiAgaGVpZ2h0OiA1MHB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2NjYzsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgLm5hdlN0eWxlLWxlZnQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBoZWlnaHQ6IDUwcHg7DQogICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICMyMWM5Yjg7DQogIH0NCiAgLnRleHQgew0KICAgIGZvbnQtZmFtaWx5OiBTb3VyY2UgSGFuIFNhbnMgQ047DQogICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICBmb250LXNpemU6IDE0cHg7DQogICAgY29sb3I6ICMyMWM5Yjg7DQogIH0NCiAgLm5hdlN0eWxlLXJpZ2h0IHsNCiAgICBmb250LWZhbWlseTogU291cmNlIEhhbiBTYW5zIENOOw0KICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgIG1hcmdpbi1sZWZ0OiBhdXRvOw0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgfQ0KfQ0KLnR5cGVTdHlsZSB7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDUwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIG1hcmdpbi10b3A6IDIwcHg7DQogIC50eXBlTGluZSB7DQogICAgd2lkdGg6IDJweDsNCiAgICBoZWlnaHQ6IDIwcHg7DQogICAgYmFja2dyb3VuZDogIzEwYWY5ZjsNCiAgfQ0KICAudHlwZVRleHQgew0KICAgIGZvbnQtZmFtaWx5OiBTb3VyY2UgSGFuIFNhbnMgQ047DQogICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICBmb250LXNpemU6IDE0cHg7DQogICAgY29sb3I6ICMzMzMzMzM7DQogICAgbWFyZ2luLWxlZnQ6IDE3cHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/supplyDemand", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div>\r\n          <!-- 顶部导航 -->\r\n          <div class=\"navStyle\">\r\n            <div\r\n              class=\"navStyle-left\"\r\n              @click=\"handleClick('1')\"\r\n              :style=\"\r\n                demandSupply == '1'\r\n                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'\r\n                  : 'border-bottom: none;cursor: pointer;'\r\n              \"\r\n            >\r\n              <div class=\"text\">我的需求</div>\r\n            </div>\r\n            <div\r\n              class=\"navStyle-left\"\r\n              @click=\"handleClick('2')\"\r\n              style=\"margin-left: 24px\"\r\n              :style=\"\r\n                demandSupply == '2'\r\n                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'\r\n                  : 'border-bottom: none;cursor: pointer;'\r\n              \"\r\n            >\r\n              <div class=\"text\">我的供给</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"typeStyle\">\r\n            <div class=\"typeLine\"></div>\r\n            <div class=\"typeText\">\r\n              我的{{ demandSupply == \"1\" ? \"需求\" : \"供给\" }}\r\n            </div>\r\n          </div>\r\n          <div style=\"margin-top: 20px\">\r\n            <demand v-if=\"demandSupply == '1'\"></demand>\r\n            <supply v-if=\"demandSupply == '2'\"></supply>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport demand from \"./components/demand.vue\";\r\nimport supply from \"./components/supply.vue\";\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu, demand, supply },\r\n  data() {\r\n    return {\r\n      demandSupply: \"1\",\r\n      typeList: [],\r\n      statusList: [],\r\n      form: {\r\n        orderStatus: \"\",\r\n      },\r\n    };\r\n  },\r\n  created() {},\r\n  methods: {\r\n    handleClick(val) {\r\n      this.demandSupply = val;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 1080px;\r\n}\r\n.navStyle {\r\n  height: 50px;\r\n  border-bottom: 1px solid #ccc;\r\n  display: flex;\r\n  align-items: center;\r\n  .navStyle-left {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 50px;\r\n    border-bottom: 2px solid #21c9b8;\r\n  }\r\n  .text {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #21c9b8;\r\n  }\r\n  .navStyle-right {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #333333;\r\n    margin-left: auto;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.typeStyle {\r\n  width: 100%;\r\n  height: 50px;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  margin-top: 20px;\r\n  .typeLine {\r\n    width: 2px;\r\n    height: 20px;\r\n    background: #10af9f;\r\n  }\r\n  .typeText {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #333333;\r\n    margin-left: 17px;\r\n  }\r\n}\r\n</style>\r\n"]}]}