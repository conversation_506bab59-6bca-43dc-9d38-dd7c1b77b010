{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\spCertification\\index.vue?vue&type=style&index=0&id=0cd2ccdc&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\spCertification\\index.vue", "mtime": 1750311963083}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQodG8gcmlnaHQsIHJnYigyMjUsIDI0NywgMjQwKSwgcmdiKDI0NCwgMjUyLCAyNTApKTsNCiAgaGVpZ2h0OiAxMDB2aDsNCn0NCg0KLm1haW4tY29udGVudCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIHBhZGRpbmc6IDIwcHg7DQogIHBhZGRpbmctYm90dG9tOiAxMDBweDsNCg0KDQogIC50aXRsZSB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQoNCg0KICAgIC50aXRsZS10ZXh0IHsNCiAgICAgIGZvbnQtc2l6ZTogMzJweDsNCiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgbGluZS1oZWlnaHQ6IDQwcHg7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQoNCiAgICAgIC50aXRsZS1idG4gew0KICAgICAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAudGl0bGUtZGVzYyB7DQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICBjb2xvcjogIzk5OTsNCiAgICB9DQogIH0NCg0KICAuYnRuLWJveCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIG1hcmdpbi10b3A6IDQwcHg7DQogICAgd2lkdGg6IDEwMCU7DQoNCiAgICAuYnRuIHsNCiAgICAgIHdpZHRoOiAzMDBweDsNCiAgICAgIGhlaWdodDogNTBweDsNCiAgICB9DQogIH0NCg0KDQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgLA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;AAGA", "file": "index.vue", "sourceRoot": "src/views/system/user/spCertification", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"main-content\">\r\n          <div class=\"title\">\r\n            <div class=\"title-text\">\r\n              <span>企业认证</span>\r\n              <el-button class=\"title-btn\" plain :type=\"colorClass[form.companyStatus]\">{{\r\n                companyNameObj[form.companyStatus] }}</el-button>\r\n            </div>\r\n            <div class=\"title-desc\">请核对您的个人信息。若审核通过后再进行修改，需要再次进行认证。</div>\r\n          </div>\r\n          <el-row :gutter=\"30\">\r\n            <el-col :span=\"4\" style=\"opacity: 0;\">marginleft</el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form :model=\"form\" :rules=\"rules\" ref=\"form\" label-width=\"80px\" label-position=\"top\">\r\n                <el-form-item label=\"真实姓名\" prop=\"companyRealName\">\r\n                  <el-input v-model=\"form.companyRealName\" placeholder=\"请输入真实姓名\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" prop=\"phone\">\r\n                  <el-input v-model=\"form.phone\" placeholder=\"请输入联系方式\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"企业名称\" prop=\"companyName\">\r\n                  <!-- <el-input v-model=\"form.companyName\" placeholder=\"请输入企业名称\" /> -->\r\n                  <el-select v-model=\"form.companyName\" filterable remote reserve-keyword placeholder=\"请输入您公司的完整名称\"\r\n                    :remote-method=\"remoteMethod\" :loading=\"loading\" @change=\"changeData\" style=\"display: block;\">\r\n                    <el-option v-for=\"item in options\" :key=\"item\" :label=\"item\" :value=\"item\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"邮箱\" prop=\"companyEmail\">\r\n                  <el-input v-model=\"form.companyEmail\" placeholder=\"请输入邮箱\" />\r\n                </el-form-item>\r\n              </el-form>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form :model=\"form\" label-width=\"80px\" label-position=\"top\">\r\n                <el-form-item label=\"上传授权书\" prop=\"companyEmpower\">\r\n                  <ImageUpload v-model=\"form.companyEmpower\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"上传附件\" prop=\"alFileDetailVOs\">\r\n                  <FileUpload v-model=\"form.alFileDetailVOs\" />\r\n                </el-form-item>\r\n              </el-form>\r\n            </el-col>\r\n          </el-row>\r\n          <div class=\"btn-box\">\r\n            <el-button class=\"btn\" type=\"primary\" @click=\"submitForm\">认证</el-button>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport ImageUpload from \"@/components/ImageUpload\";\r\nimport FileUpload from \"@/components/FileUpload\";\r\nimport { setCompanyAuth, searchCompany, getCompanyCodeByName, getMyCompanyInfo } from \"@/api/system/company\";\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      form: {\r\n        companyRealName: \"\",\r\n        phone: \"\",\r\n        companyName: \"\",\r\n        companyEmail: \"\",\r\n        companyEmpower: \"\",\r\n        alFileDetailVOs: [],\r\n        socialUnityCreditCode: \"\",\r\n        companyStatus: 0,\r\n        companyStatusName: \"未认证\",\r\n      },\r\n      rules: {\r\n        companyRealName: [\r\n          { required: true, message: \"请输入姓名\", trigger: \"blur\" },\r\n        ],\r\n        phone: [\r\n          { required: true, message: \"请输入手机号\", trigger: \"blur\" },\r\n          { pattern: /^1[3456789]\\d{9}$/, message: \"请输入正确的手机号\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"请输入企业名称\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      settled: true,\r\n      loading: false,\r\n      options: [],\r\n      list: [],\r\n      companyNameObj: {\r\n        \"0\": '未认证',\r\n        \"1\": '认证中',\r\n        \"2\": '认证驳回',\r\n        \"3\": '已认证',\r\n      },\r\n      colorClass: {\r\n        \"0\": 'info', // 未认证\r\n        \"1\": 'warning', // 认证中\r\n        \"2\": 'danger', // 认证驳回\r\n        \"3\": 'success', // 未认证\r\n      },\r\n\r\n    };\r\n  },\r\n  created() {\r\n    this.getCompany()\r\n  },\r\n  methods: {\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          setCompanyAuth(this.form).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"提交成功\");\r\n            } else {\r\n              this.$message.error(res.msg);\r\n            }\r\n          });\r\n        }\r\n      })\r\n\r\n    },\r\n    // 远程搜索\r\n    getList(row) {\r\n      searchCompany({ keywords: row }).then(res => {\r\n        this.options = res.rows\r\n      })\r\n    },\r\n    // 远程搜索\r\n    remoteMethod(query) {\r\n      if (query !== '') {\r\n        this.loading = true;\r\n        setTimeout(() => {\r\n          this.loading = false;\r\n          this.getList(query)\r\n          this.options = this.list.filter(item => {\r\n            return item;\r\n          });\r\n        }, 200);\r\n      } else {\r\n        this.options = [];\r\n      }\r\n    },\r\n    // 根据公司名称查询社会信用代码\r\n    changeData(val) {\r\n      console.log(\"val\", val)\r\n      getCompanyCodeByName({ keywords: val }).then(res => {\r\n        let data = res.data;\r\n        this.$set(this.form, 'socialUnityCreditCode', data.taxNo)\r\n      })\r\n    },\r\n    // 获取我的公司信息\r\n    getCompany() {\r\n      getMyCompanyInfo().then(res => {\r\n        let data = res.data;\r\n        if (data) {\r\n          if (data && data.alFileDetailVOs) {\r\n            data.alFileDetailVOs.forEach(item => {\r\n              item.url = item.fileFullPath\r\n              item.name = item.fileName\r\n            })\r\n          }\r\n          this.form = data\r\n        }\r\n      })\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.main-content {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  padding-bottom: 100px;\r\n\r\n\r\n  .title {\r\n    width: 100%;\r\n    text-align: center;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    margin-bottom: 20px;\r\n\r\n\r\n    .title-text {\r\n      font-size: 32px;\r\n      font-weight: bold;\r\n      line-height: 40px;\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 10px;\r\n\r\n      .title-btn {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .title-desc {\r\n      font-size: 14px;\r\n      color: #999;\r\n    }\r\n  }\r\n\r\n  .btn-box {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin-top: 40px;\r\n    width: 100%;\r\n\r\n    .btn {\r\n      width: 300px;\r\n      height: 50px;\r\n    }\r\n  }\r\n\r\n\r\n}\r\n</style>\r\n"]}]}