{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\areaTags.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\areaTags.vue", "mtime": 1750311962955}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["areaTags.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "areaTags.vue", "sourceRoot": "src/views/form/components", "sourcesContent": ["<template>\r\n  <div class=\"application-area\">\r\n    <div class=\"action\">\r\n      <el-input\r\n        class=\"input-new-tag\"\r\n        v-if=\"inputVisible\"\r\n        v-model.trim=\"inputValue\"\r\n        ref=\"saveTagInput\"\r\n        size=\"small\"\r\n        @keyup.enter.native=\"handleInputConfirm\"\r\n        @blur=\"handleInputConfirm\"\r\n      >\r\n      </el-input>\r\n      <div v-else class=\"button-new-tag\" @click=\"showInput\">\r\n        <i class=\"el-icon-plus\"></i>\r\n      </div>\r\n    </div>\r\n    <el-tag\r\n      v-for=\"tag in dynamicTags\"\r\n      :key=\"`tag${tag}`\"\r\n      closable\r\n      :disable-transitions=\"false\"\r\n      @close=\"handleClose(tag)\">\r\n      {{tag}}\r\n    </el-tag>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { includes } from 'ramda'\r\nimport { getDicts } from '@/api/system/dict/data'\r\n\r\nexport default {\r\n  name: 'ApplicationAreaDicts',\r\n  data() {\r\n    return {\r\n      dynamicTags: [],\r\n      inputVisible: false,\r\n      inputValue: ''\r\n    };\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          this.dynamicTags =  Array.isArray(val) ? val : this.value.split(',');\r\n        } else {\r\n          this.dynamicTags = [];\r\n          return [];\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      getDicts('application_area').then(res => {\r\n        const { data = [] } = res;\r\n        if (data && data.length > 0) {\r\n          this.dynamicTags = data.map(item => item.dictLabel)\r\n          this.$emit(\"input\", this.dynamicTags);\r\n        }\r\n      })\r\n    },\r\n    handleClose(tag) {\r\n      this.dynamicTags = this.dynamicTags.filter(item => item !== tag);\r\n      this.$nextTick(() => {\r\n        this.$emit(\"input\", this.formatValue(this.dynamicTags));\r\n      })\r\n    },\r\n\r\n    showInput() {\r\n      this.inputVisible = true;\r\n      this.$nextTick(_ => {\r\n        this.$refs.saveTagInput.$refs.input.focus();\r\n      });\r\n    },\r\n\r\n    handleInputConfirm() {\r\n      let inputValue = this.inputValue;\r\n      if (inputValue && !this.dynamicTags.includes(inputValue)) {\r\n        this.dynamicTags = [inputValue, ...this.dynamicTags];\r\n        this.$nextTick(() => {\r\n          this.$emit(\"input\", this.dynamicTags);\r\n        })\r\n      } else {\r\n        this.$message.warning(\"应用领域已存在\");\r\n      }\r\n      this.inputVisible = false;\r\n      this.inputValue = '';\r\n\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n.application-area {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  flex-shrink: 0;\r\n  flex-wrap: wrap;\r\n  .el-tag {\r\n    margin: 6px;\r\n    &:last-child {\r\n      margin-right: 0;\r\n    }\r\n  }\r\n  .button-new-tag {\r\n    @include flexCenter;\r\n    width: 24px;\r\n    height: 24px;\r\n    border: 1px dotted #999999;\r\n    border-radius: 100%;\r\n    margin-right: 6px;\r\n    cursor: pointer;\r\n    i {\r\n      color: #999999;\r\n      font-size: 16px;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n  .input-new-tag {\r\n    width: 90px;\r\n    margin-right: 6px;\r\n    vertical-align: bottom;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}