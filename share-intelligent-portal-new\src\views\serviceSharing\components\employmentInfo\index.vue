<template>
  <div class="content">
    <div class="content_banner">企业用工</div>
    <div class="card-container card_top">
      <div class="card_top_item">
        <div class="largeCategory">薪资范围：</div>
        <div class="smallCategory" :class="salaryRange === item.dictValue ? 'smallCategoryActive' : ''"
          v-for="(item, index) in salaryRangeList" :key="index" @click="switchSalaryRange(item.dictValue)">
          {{ item.dictLabel }}
        </div>
      </div>
      <div class="card_top_itemLine"></div>
      <div class="card_top_item">
        <div class="largeCategory">用工地点：</div>
        <div class="smallCategory" :class="workPlace === item.dictValue ? 'smallCategoryActive' : ''"
          v-for="(item, index) in locationList" :key="index" @click="switchWorkPlace(item.dictValue)">
          {{ item.dictLabel }}
        </div>
      </div>
      <div class="card_top_itemLine"></div>
      <div class="card_top_item">
        <div class="largeCategory">岗位分类：</div>
        <div class="smallCategory" :class="jobCategory === item.dictValue ? 'smallCategoryActive' : ''"
          v-for="(item, index) in jobCategoryList" :key="index" @click="switchJobCategory(item.dictValue)">
          {{ item.dictLabel }}
        </div>
      </div>
      <div class="buttonStyle">
        <div class="imgStyle" @click="initList">
          <img style="width: 100%; height: 100%" src="../../../../assets/serviceSharing/reset.png" alt="" />
        </div>
        <div class="buttonText" @click="resetData">重置筛选</div>
      </div>
    </div>
    <div class="card-container card_bottom">
      <div class="content_bottom" v-loading="loading" v-if="employmentList && employmentList.length > 0">
        <div class="card_bottom_item tr2" v-for="(item, index) in employmentList" :key="index"
          @click="goDetail(item.id)">
          <span class="bottom"></span>
          <span class="right"></span>
          <span class="top"></span>
          <span class="left"></span>
          <div class="content_title">
            <div class="title">{{ item.positionName }}</div>
            <div class="price" v-if="salaryRangeList.length > 0 && item.salaryRange">
              {{
                salaryRangeList.filter(
                  (item1) => item1.dictValue == item.salaryRange
                )[0].dictLabel
              }}
            </div>
          </div>
          <div class="desc">{{ item.requirements }}</div>
          <div class="content_address">
            <div class="imgStyle">
              <img style="width: 100%; height: 100%" src="../../../../assets/serviceSharing/address_icon.png" alt="" />
            </div>
            <div class="address" v-if="locationList.length > 0 && item.location">
              {{
                locationList.filter(
                  (item1) => item1.dictValue == item.location
                )[0].dictLabel
              }}
            </div>
          </div>
        </div>
      </div>
      <div class="none-class" v-else>
        <el-image style="width: 160px; height: 160px" :src="require('@/assets/user/none.png')" :fit="fit"></el-image>
        <div class="text">暂无数据</div>
      </div>
      <!-- 分页 -->
      <div class="pageStyle">
        <el-pagination v-if="employmentList && employmentList.length > 0" background layout="prev, pager, next"
          class="activity-pagination" :page-size="pageSize" :current-page="pageNum" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { employListData } from "@/api/serviceSharing";

export default {
  name: "serviceSharing",
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 9,
      total: 0,
      employmentList: [
        {
          name: "事业部总经理",
          price: "20000元以上",
          desc: "按照公司的规划，进行人员招聘、管理达成公司要求的各个事项。",
          address: "枣强市",
        },
        {
          name: "事业部总经理",
          price: "20000元以上",
          desc: "按照公司的规划，进行人员招聘、管理达成公司要求的各个事项。",
          address: "枣强市",
        },
        {
          name: "事业部总经理",
          price: "20000元以上",
          desc: "按照公司的规划，进行人员招聘、管理达成公司要求的各个事项。",
          address: "枣强市",
        },
        {
          name: "事业部总经理",
          price: "20000元以上",
          desc: "按照公司的规划，进行人员招聘、管理达成公司要求的各个事项。",
          address: "枣强市",
        },
        {
          name: "事业部总经理",
          price: "20000元以上",
          desc: "按照公司的规划，进行人员招聘、管理达成公司要求的各个事项。",
          address: "枣强市",
        },
        {
          name: "事业部总经理",
          price: "20000元以上",
          desc: "按照公司的规划，进行人员招聘、管理达成公司要求的各个事项。",
          address: "枣强市",
        },
        {
          name: "事业部总经理",
          price: "20000元以上",
          desc: "按照公司的规划，进行人员招聘、管理达成公司要求的各个事项。",
          address: "枣强市",
        },
        {
          name: "事业部总经理",
          price: "20000元以上",
          desc: "按照公司的规划，进行人员招聘、管理达成公司要求的各个事项。",
          address: "枣强市",
        },
        {
          name: "事业部总经理",
          price: "20000元以上",
          desc: "按照公司的规划，进行人员招聘、管理达成公司要求的各个事项。",
          address: "枣强市",
        },
      ],
      salaryRange: "",
      workPlace: "",
      jobCategory: "",
      salaryRangeList: [], // 薪资范围
      locationList: [], // 用工地点
      jobCategoryList: [], // 岗位类别
      fit: "cover",
    };
  },
  created() {
    this.getSalaryRange();
    this.getLocation();
    this.getJobCategory();
    this.getList();
  },
  methods: {
    switchSalaryRange(index) {
      this.salaryRange = index;
      this.getList();
    },
    switchWorkPlace(index) {
      this.workPlace = index;
      this.getList();
    },
    switchJobCategory(index) {
      this.jobCategory = index;
      this.getList();
    },
    // 薪资范围字典
    getSalaryRange() {
      let params = { dictType: "salary_range" };
      listData(params).then((response) => {
        this.salaryRangeList = response.rows;
        this.salaryRangeList.unshift({
          dictValue: "",
          dictLabel: "全部",
        });
      });
    },
    // 用工地点字典
    getLocation() {
      let params = { dictType: "location" };
      listData(params).then((response) => {
        this.locationList = response.rows;
        this.locationList.unshift({
          dictValue: "",
          dictLabel: "全部",
        });
      });
    },
    getJobCategory() {
      let params = { dictType: "job_category" };
      listData(params).then((response) => {
        this.jobCategoryList = response.rows;
        this.jobCategoryList.unshift({
          dictValue: "",
          dictLabel: "全部",
        });
      });
      // this.jobCategoryList =[
      //   {
      //     dictValue: "",
      //     dictLabel: "全部",
      //   },
      //   {
      //     dictValue: "1",
      //     dictLabel: "技术类",
      //   },
      //   {
      //     dictValue: "2",
      //     dictLabel: "销售类",
      //   },
      //   {
      //     dictValue: "3",
      //     dictLabel: "管理类",
      //   },
      // ]
    },
    // 列表接口
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        salaryRange: this.salaryRange,
        location: this.workPlace,
        jobCategory: this.jobCategory,
      };
      employListData(params).then((res) => {
        if (res.code === 200) {
          this.employmentList = res.rows;
          this.loading = false;
          this.total = res.total;
        }
      });
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    goDetail(id) {
      this.$router.push("/employmentInfoDetail?id=" + id);
    },
    initList() {
      this.getList();
    },
    resetData() {
      this.salaryRange = "";
      this.workPlace = "";
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  padding-bottom: 60px;
  background-color: #f2f2f2;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../../assets/release/banner.png");
  background-size: 100% 100%;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
  text-align: center;
  line-height: 300px;
}

.card_top {
  background: #ffffff;
  border-radius: 2px;
  margin-top: 30px;
  padding: 58px 60px 32px 62px;

  .card_top_item {
    display: flex;
    align-items: center;
    margin-top: 20px;

    .largeCategory {
      width: 90px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #222222;
      margin-right: 28px;
    }

    .smallCategory {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      padding: 12px 24px;
      cursor: pointer;
    }

    .smallCategoryActive {
      background: #e0f7f5;
      border-radius: 2px;
      color: #21c9b8;
    }
  }

  .card_top_item:nth-child(1) {
    margin-top: 0;
  }

  .card_top_itemLine {
    width: 100%;
    height: 1px;
    background: #eeeeee;
    margin-top: 20px;
  }

  .buttonStyle {
    margin-top: 9px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .imgStyle {
      width: 19px;
      height: 16px;
      cursor: pointer;
    }

    .buttonText {
      margin-left: 10px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #21c9b8;
      cursor: pointer;
    }
  }
}

.card_bottom {
  height: 770px;
  background: #ffffff;
  border-radius: 2px;
  margin-top: 30px;
  padding: 40px 60px 62px 60px;

  .content_bottom {
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .card_bottom_item {
      width: 346px;
      height: 180px;
      background: #ffffff;
      box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      border: 2px solid #ffffff;
      margin-left: 20px;
      padding: 28px 26px 24px 27px;
      cursor: pointer;

      span {
        position: absolute;
        z-index: 1;
        background-color: #37c9b8;
        transition: transform 0.5s ease;
      }

      .bottom,
      .top {
        height: 2px;
        left: -1px;
        right: -1px;
        transform: scaleX(0);
      }

      .left,
      .right {
        width: 2px;
        top: -1px;
        bottom: -1px;
        transform: scaleY(0);
      }

      .bottom {
        bottom: -1px;
        transform-origin: bottom right;
      }

      .right {
        right: -1px;
        transform-origin: top right;
      }

      .top {
        top: -1px;
        transform-origin: top left;
      }

      .left {
        left: -1px;
        transform-origin: bottom left;
      }

      .content_title {
        width: 100%;
        display: flex;
      }

      .title {
        height: 18px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #000000;
      }

      .price {
        height: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #21c9b8;
        margin-left: auto;
      }

      .desc {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #7e7e7e;
        line-height: 20px;
        margin-top: 19px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        word-wrap: break-word;
      }

      .content_address {
        display: flex;
        align-items: center;
        margin-top: 37px;
      }

      .imgStyle {
        width: 14px;
        height: 18px;
      }

      .address {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
        margin-left: 9px;
      }
    }

    .card_bottom_item:nth-child(3n + 1) {
      margin-left: 0;
    }

    .card_bottom_item:nth-child(n + 4) {
      margin-top: 20px;
    }

    .card_bottom_item:hover {
      box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);
      scale: 1.01;

      .top {
        transform-origin: top right;
        transform: scaleX(1);
      }

      .left {
        transform-origin: top left;
        transform: scaleY(1);
      }

      .bottom {
        transform-origin: bottom left;
        transform: scaleX(1);
      }

      .right {
        transform-origin: bottom right;
        transform: scaleY(1);
      }
    }
  }

  .pageStyle {
    margin-top: 60px;
    width: 100%;
    text-align: center;
  }
}

.none-class {
  text-align: center;
  padding: 8% 0;

  .text {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
  }
}
</style>
