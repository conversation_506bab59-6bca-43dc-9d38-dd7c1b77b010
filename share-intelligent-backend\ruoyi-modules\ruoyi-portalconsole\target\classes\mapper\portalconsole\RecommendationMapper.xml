<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.RecommendationMapper">
    
    <resultMap type="Recommendation" id="RecommendationResult">
        <result property="id"    column="id"    />
        <result property="content"    column="content"    />
        <result property="createTime"    column="create_time"    />
        <result property="readStatus"    column="read_status"    />
    </resultMap>

    <sql id="selectRecommendationVo">
        select id, content, create_time, read_status from recommendation
    </sql>

    <select id="selectRecommendationList" parameterType="Recommendation" resultMap="RecommendationResult">
        <include refid="selectRecommendationVo"/>
        <where>  
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="readStatus != null  and readStatus != ''"> and read_status = #{readStatus}</if>
        </where>
    </select>
    
    <select id="selectRecommendationById" parameterType="Long" resultMap="RecommendationResult">
        <include refid="selectRecommendationVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertRecommendation" parameterType="Recommendation" useGeneratedKeys="true" keyProperty="id">
        insert into recommendation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="readStatus != null">read_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="readStatus != null">#{readStatus},</if>
         </trim>
    </insert>

    <update id="updateRecommendation" parameterType="Recommendation">
        update recommendation
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="readStatus != null">read_status = #{readStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRecommendationById" parameterType="Long">
        delete from recommendation where id = #{id}
    </delete>

    <delete id="deleteRecommendationByIds" parameterType="String">
        delete from recommendation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>