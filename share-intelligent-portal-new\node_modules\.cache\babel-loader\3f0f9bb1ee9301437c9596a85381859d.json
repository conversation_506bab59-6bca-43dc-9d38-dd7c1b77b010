{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\notice\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\notice\\index.vue", "mtime": 1750311963067}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_info", "name", "components", "UserMenu", "data", "activeName", "records", "systemParams", "pageNum", "pageSize", "total", "fit", "dialogVisible", "created", "getSystemList", "methods", "goDetail", "id", "$router", "push", "handleClick", "tab", "event", "$options", "propsData", "getResponseList", "deleteInfo", "row", "type", "_this", "$confirm", "then", "_", "ids", "toString", "response", "$message", "message", "catch", "_this2", "listInfo", "_objectSpread2", "default", "rows", "getInfoDetail", "_this3", "revocationApply", "_revocationApply", "_x", "apply", "arguments", "_this4", "systemPageChange", "res"], "sources": ["src/views/system/user/notice/index.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-21 16:37:16\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"notice-record-page\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"response-message\">\r\n            <div\r\n              class=\"response-message-item\"\r\n              v-for=\"item in records\"\r\n              :key=\"item.id\"\r\n            >\r\n              <el-image\r\n                class=\"iamge\"\r\n                style=\"width: 36px; height: 36px\"\r\n                :src=\"\r\n                  require(item.readStatus === 0\r\n                    ? '@/assets/user/notice_2.png'\r\n                    : '@/assets/user/notice_1.png')\r\n                \"\r\n              ></el-image>\r\n              <a class=\"item-content\" @click=\"getInfoDetail(item.id)\">\r\n                <div class=\"title\">{{ item.title || \"系统消息\" }}</div>\r\n                <div class=\"item-content-bottom\">\r\n                  <div class=\"content\">\r\n                    {{ item.remark || item.describeInfo || \"--\" }}\r\n                  </div>\r\n\r\n                  <div class=\"date\">{{ item.createTime || \"--\" }}</div>\r\n                </div>\r\n              </a>\r\n              <a @click=\"revocationApply(item.id)\" v-if=\"item.backStatus == 1\">\r\n                <el-image\r\n                  class=\"re-icon\"\r\n                  style=\"width: 18px; height: 20px\"\r\n                  :src=\"require('@/assets/user/revocation.png')\"\r\n                ></el-image>\r\n              </a>\r\n\r\n              <a @click=\"deleteInfo(item, 2)\">\r\n                <el-image\r\n                  class=\"delete-icon\"\r\n                  style=\"width: 18px; height: 20px\"\r\n                  :src=\"require('@/assets/user/delete.png')\"\r\n                ></el-image>\r\n              </a>\r\n            </div>\r\n          </div>\r\n          <el-pagination\r\n            v-show=\"total > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            :page-size=\"5\"\r\n            :current-page.sync=\"systemParams.pageNum\"\r\n            @current-change=\"systemPageChange\"\r\n            :total=\"total\"\r\n          >\r\n          </el-pagination>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listInfo, deleteInfo, getInfoDetail } from \"@/api/system/info\";\r\nexport default {\r\n  name: \"Notice\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      activeName: \"first\",\r\n      records: [],\r\n      systemParams: {\r\n        pageNum: 1,\r\n        pageSize: 5,\r\n      },\r\n      total: 0,\r\n      fit: \"cover\",\r\n      dialogVisible: false,\r\n    };\r\n  },\r\n  created() {\r\n    this.getSystemList();\r\n  },\r\n  methods: {\r\n    goDetail(id) {\r\n      this.$router.push(\"/user/noticeDetail?id=\" + id);\r\n    },\r\n    handleClick(tab, event) {\r\n      if (tab.$options.propsData.name == \"first\") {\r\n        this.getResponseList();\r\n      } else {\r\n        this.getSystemList();\r\n      }\r\n    },\r\n\r\n    deleteInfo(row, type) {\r\n      this.$confirm(\"是否确认删除该消息？\", { type: \"error\" })\r\n        .then((_) => {\r\n          deleteInfo({ ids: row.id.toString() }).then((response) => {\r\n            this.$message({\r\n              message: \"操作成功\",\r\n              type: \"success\",\r\n            });\r\n          });\r\n          if (type == 1) {\r\n            this.getResponseList();\r\n          } else {\r\n            this.getSystemList();\r\n          }\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n    getSystemList() {\r\n      listInfo({ ...this.systemParams, type: 2 }).then((response) => {\r\n        this.records = response.rows;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    getInfoDetail(id) {\r\n      getInfoDetail(id).then((response) => {\r\n        this.getSystemList();\r\n      });\r\n    },\r\n    revocationApply(id) {\r\n      revocationApply({ id: id }).then((response) => {\r\n        this.$message({\r\n          message: \"操作成功\",\r\n          type: \"success\",\r\n        });\r\n        this.getSystemList();\r\n      });\r\n    },\r\n    systemPageChange(res) {\r\n      this.systemParams.pageNum = res;\r\n      this.getSystemList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .notice-record-page {\r\n    .response-message {\r\n      width: 100%;\r\n      height: 600px;\r\n      background-color: #fff;\r\n      .none-class {\r\n        text-align: center;\r\n        padding: 10% 0;\r\n        .text {\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #999999;\r\n          line-height: 14px;\r\n        }\r\n      }\r\n      .response-message-item {\r\n        width: 100%;\r\n        height: 100px;\r\n        vertical-align: middle;\r\n        padding: 22px 22px;\r\n        display: flex;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        position: relative;\r\n\r\n        .iamge {\r\n          margin: auto 0;\r\n        }\r\n        .item-content {\r\n          vertical-align: middle;\r\n          margin-left: 14px;\r\n          .title {\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            line-height: 20px;\r\n          }\r\n          .item-content-bottom {\r\n            width: 100%;\r\n            display: flex;\r\n            line-height: 45px;\r\n            font-size: 14px;\r\n            font-weight: 400;\r\n\r\n            .content {\r\n              width: 680px;\r\n              height: 30px;\r\n              color: #666666;\r\n              overflow: hidden;\r\n              -webkit-line-clamp: 1;\r\n              text-overflow: ellipsis;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n            }\r\n            .to-detail {\r\n              width: 150px;\r\n              text-align: center;\r\n              color: #21c9b8;\r\n            }\r\n            .date {\r\n              width: 180px;\r\n              text-align: center;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #666666;\r\n            }\r\n          }\r\n        }\r\n        .delete-icon {\r\n          right: 30px;\r\n          top: 40px;\r\n          margin: 0 auto;\r\n          position: absolute;\r\n        }\r\n        .re-icon {\r\n          right: 80px;\r\n          top: 40px;\r\n          margin: 0 auto;\r\n          position: absolute;\r\n        }\r\n      }\r\n    }\r\n\r\n    .el-pagination {\r\n      width: 100%;\r\n      margin-top: 20px;\r\n      text-align: center;\r\n    }\r\n    .el-pagination.is-background .el-pager li {\r\n      background-color: #fff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled).active {\r\n      background-color: #21c9b8;\r\n      color: #ffffff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled):hover {\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AA0EA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAE,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,OAAA;MACAC,YAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,KAAA;MACAC,GAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAAC,EAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,4BAAAF,EAAA;IACA;IACAG,WAAA,WAAAA,YAAAC,GAAA,EAAAC,KAAA;MACA,IAAAD,GAAA,CAAAE,QAAA,CAAAC,SAAA,CAAAvB,IAAA;QACA,KAAAwB,eAAA;MACA;QACA,KAAAX,aAAA;MACA;IACA;IAEAY,UAAA,WAAAA,WAAAC,GAAA,EAAAC,IAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,QAAA;QAAAF,IAAA;MAAA,GACAG,IAAA,WAAAC,CAAA;QACA,IAAAN,gBAAA;UAAAO,GAAA,EAAAN,GAAA,CAAAV,EAAA,CAAAiB,QAAA;QAAA,GAAAH,IAAA,WAAAI,QAAA;UACAN,KAAA,CAAAO,QAAA;YACAC,OAAA;YACAT,IAAA;UACA;QACA;QACA,IAAAA,IAAA;UACAC,KAAA,CAAAJ,eAAA;QACA;UACAI,KAAA,CAAAf,aAAA;QACA;MACA,GACAwB,KAAA,WAAAN,CAAA;IACA;IACAlB,aAAA,WAAAA,cAAA;MAAA,IAAAyB,MAAA;MACA,IAAAC,cAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,WAAAnC,YAAA;QAAAqB,IAAA;MAAA,IAAAG,IAAA,WAAAI,QAAA;QACAI,MAAA,CAAAjC,OAAA,GAAA6B,QAAA,CAAAQ,IAAA;QACAJ,MAAA,CAAA7B,KAAA,GAAAyB,QAAA,CAAAzB,KAAA;MACA;IACA;IACAkC,aAAA,WAAAA,cAAA3B,EAAA;MAAA,IAAA4B,MAAA;MACA,IAAAD,mBAAA,EAAA3B,EAAA,EAAAc,IAAA,WAAAI,QAAA;QACAU,MAAA,CAAA/B,aAAA;MACA;IACA;IACAgC,eAAA,YAAAC,gBAAA;MAAA,SAAAD,gBAAAE,EAAA;QAAA,OAAAD,gBAAA,CAAAE,KAAA,OAAAC,SAAA;MAAA;MAAAJ,eAAA,CAAAZ,QAAA;QAAA,OAAAa,gBAAA,CAAAb,QAAA;MAAA;MAAA,OAAAY,eAAA;IAAA,YAAA7B,EAAA;MAAA,IAAAkC,MAAA;MACAL,eAAA;QAAA7B,EAAA,EAAAA;MAAA,GAAAc,IAAA,WAAAI,QAAA;QACAgB,MAAA,CAAAf,QAAA;UACAC,OAAA;UACAT,IAAA;QACA;QACAuB,MAAA,CAAArC,aAAA;MACA;IACA;IACAsC,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAA9C,YAAA,CAAAC,OAAA,GAAA6C,GAAA;MACA,KAAAvC,aAAA;IACA;EACA;AACA", "ignoreList": []}]}