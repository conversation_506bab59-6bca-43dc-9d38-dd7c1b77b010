package com.ruoyi.common.core.utils;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Random;

/**
 * 短信发送工具类
 */
public class SmsSendUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(SmsSendUtils.class);

    // 产品名称:云通信短信API产品,开发者无需替换
    private static final String PRODUCT = "Dysmsapi";
    // 产品域名,开发者无需替换
    private static final String DOMAIN = "dysmsapi.aliyuncs.com";

    private static final String ACCESS_KEY_ID = "LTAI5t7ybVczhgsZ9Am1nVh1";           // 玺品提供的id
    private static final String ACCESS_KEY_SECRET = "******************************"; // 玺品提供秘钥，后期可以做到配置文件

    /**
     *
     * @param signName   签名
     * @param telephone  手机号
     * @param templateCode  阿里云模板编码
     * @param templateParam 阿里云模板参数
     * @return
     */
    public static SendSmsResponse sendSms(String signName, String telephone, String templateCode, String templateParam) {

        // 可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");

        // 初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", ACCESS_KEY_ID, ACCESS_KEY_SECRET);
        try {
            DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", PRODUCT, DOMAIN);
        } catch (ClientException e) {
            e.printStackTrace();
        }
        IAcsClient acsClient = new DefaultAcsClient(profile);

        // 组装请求对象-具体描述见控制台-文档部分内容
        SendSmsRequest request = new SendSmsRequest();
        // 必填:待发送手机号
        request.setPhoneNumbers(telephone);
        // 必填:短信签名-可在短信控制台中找到
        request.setSignName(signName); // 签名
        // 必填:短信模板-可在短信控制台中找到
        request.setTemplateCode(templateCode);  // 验证码模板
        // 可选:模板中的变量替换JSON串,如模板内容为"亲爱的用户,您的验证码为${code}"时,此处的值为
        request.setTemplateParam(templateParam);
        // 选填-上行短信扩展码(无特殊需求用户请忽略此字段)
        // request.setSmsUpExtendCode("90997");
        // 可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
        request.setOutId("yourOutId");
        // hint 此处可能会抛出异常，注意catch
        SendSmsResponse sendSmsResponse = null;
        try {
            sendSmsResponse = acsClient.getAcsResponse(request);
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                //记录日志
                LOGGER.info("短信发送成功！signName: {}, telephone: {}, templateCode: {}, templateParam: {}", signName, telephone, templateCode, templateParam);
            } else {
                // 记录日志
                LOGGER.info("短信发送失败！ {}。 signName: {}, telephone: {}, templateCode: {}, templateParam: {}", sendSmsResponse.getMessage(), signName, telephone, templateCode, templateParam);
            }
        } catch (ClientException e) {
            e.printStackTrace();
            LOGGER.error(e.getMessage(), e);
        }

        return sendSmsResponse;
    }

    /**
     * 生成随机数，去掉0和1
     *
     * @param length 长度
     * @return
     */
    public static String getRandomNum(int length) {
        char[] codeSeq = {'2', '3', '4', '5', '6', '7', '8', '9'};
        Random random = new Random();
        StringBuilder s = new StringBuilder();
        for (int i = 0; i < length; i++) {
            String r = String.valueOf(codeSeq[random.nextInt(codeSeq.length)]);
            s.append(r);
        }
        return s.toString();
    }

}
