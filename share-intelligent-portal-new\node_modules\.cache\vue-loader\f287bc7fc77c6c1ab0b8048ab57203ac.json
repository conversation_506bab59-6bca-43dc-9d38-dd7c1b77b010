{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\thinkTank.vue?vue&type=style&index=0&id=455164bb&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\thinkTank.vue", "mtime": 1750311962937}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["thinkTank.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "thinkTank.vue", "sourceRoot": "src/views/components/home", "sourcesContent": ["<template>\r\n  <div\r\n    class=\"cardBg wow animate__animated animate__fadeInUp\"\r\n    data-wow-duration=\"1s\"\r\n  >\r\n    <div class=\"card-container\">\r\n      <div class=\"enterpriseTitle\">\r\n        <div>专家智库</div>\r\n        <div class=\"allEnterprise\" @click=\"gothinkTank\">查看全部>></div>\r\n      </div>\r\n      <div class=\"expert-library-list\">\r\n        <div\r\n          v-for=\"(item, index) in data\"\r\n          :key=\"index\"\r\n          class=\"list-item-content\"\r\n          @click=\"goExpertLibrary(item.id)\"\r\n        >\r\n          <div class=\"list-item-box\">\r\n            <div class=\"item-headline\">\r\n              <div class=\"item-title\">\r\n                {{ item.expertName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"expert-library-label\">\r\n              <div\r\n                v-for=\"(val, index1) in item.techniqueTypeName\"\r\n                :key=\"index1\"\r\n                class=\"library-label-item\"\r\n              >\r\n                <span v-if=\"index1 < 2\" class=\"expert-library-type\">{{\r\n                  `#${val}`\r\n                }}</span>\r\n                <span v-else>…</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"expert-library-box\">\r\n              {{ item.synopsis }}\r\n            </div>\r\n          </div>\r\n          <div class=\"list-item-img\">\r\n            <img v-if=\"item.headPortrait\" :src=\"item.headPortrait\" alt=\"\" />\r\n            <img\r\n              v-else\r\n              src=\"../../../assets/expertLibrary/defaultImg.png\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getExpertList } from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    // this.searchExpert();\r\n  },\r\n  methods: {\r\n    gothinkTank() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/thinkTank\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    searchExpert() {\r\n      this.loading = true;\r\n      getExpertList({\r\n        pageSize: 4,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows.slice(0, 4);\r\n          this.data.forEach((item) => {\r\n            item.techniqueTypeName = item.techniqueTypeName\r\n              ? item.techniqueTypeName.split(\",\")\r\n              : [];\r\n          });\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 跳转到专家详情页面\r\n    goExpertLibrary(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/expertDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cardBg {\r\n  width: 100%;\r\n  height: 740px;\r\n  background-image: url(\"../../../assets/images/home/<USER>\");\r\n  background-size: 100% 100%;\r\n}\r\n.enterpriseTitle {\r\n  width: 100%;\r\n  font-size: 36px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  color: #000000;\r\n  text-align: center;\r\n  margin: 60px 0 20px 0;\r\n  padding-top: 70px;\r\n  position: relative;\r\n  .allEnterprise {\r\n    position: absolute;\r\n    top: 8 px;\r\n    right: 0;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    line-height: 26px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n  height: 280px;\r\n  .contentItem {\r\n    width: 23%;\r\n    height: 100%;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    background: #ffffff;\r\n    box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);\r\n    border-radius: 4px;\r\n    img {\r\n      width: 100%;\r\n      height: 230px;\r\n    }\r\n  }\r\n}\r\n.expert-library-list {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  width: 100%;\r\n  .list-item-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    width: 578px;\r\n    background: #fff;\r\n    margin-top: 31px;\r\n    padding: 28px 32px;\r\n    min-height: 240px;\r\n    .list-item-box {\r\n      flex: 1;\r\n      .item-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        .item-title {\r\n          width: 280px;\r\n          font-size: 32px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 32px;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          word-wrap: break-word;\r\n        }\r\n      }\r\n      .expert-library-label {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        margin: 0 0 16px;\r\n        .library-label-item {\r\n          max-width: 350px;\r\n          padding: 6px 12px;\r\n          background: #f4f5f9;\r\n          border-radius: 4px;\r\n          font-size: 12px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #666;\r\n          line-height: 12px;\r\n          margin: 24px 16px 0 0;\r\n          .expert-library-type {\r\n            word-wrap: break-word;\r\n          }\r\n        }\r\n      }\r\n      .expert-library-box {\r\n        width: 370px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #666;\r\n        line-height: 32px;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n    }\r\n    .list-item-img {\r\n      width: 120px;\r\n      height: 168px;\r\n      margin-left: 24px;\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n    &:hover {\r\n      cursor: pointer;\r\n    }\r\n  }\r\n  .list-item-content:hover {\r\n    box-shadow: 0px 2px 20px 0px rgba(13, 230, 96, 0.3);\r\n  }\r\n}\r\n</style>\r\n"]}]}