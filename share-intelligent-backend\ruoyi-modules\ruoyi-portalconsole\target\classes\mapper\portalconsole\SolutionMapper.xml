<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.SolutionMapper">
    
    <resultMap type="SolutionVO" id="SolutionResult" >
        <result property="solutionId"    column="solution_id"    />
        <result property="solutionTypeId"    column="solution_type_id"    />
        <result property="solutionTypeName"    column="solution_type_name"    />
        <result property="solutionName"    column="solution_name"    />
        <result property="solutionBanner"    column="solution_banner"    />
        <result property="solutionIntroduction"    column="solution_introduction"    />
        <result property="solutionOverview"    column="solution_overview"    />
        <result property="solutionImg"    column="solution_img"    />
        <result property="solutionStatus"    column="solution_status"    />
        <result property="recommend"    column="recommend"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSolutionVo">
        select solution_id, solution.solution_type_id, solution_name, solution_banner, solution_introduction, solution_overview, solution_img, solution_status, recommend, solution.del_flag,  solution.create_by,  solution.create_time,  solution.update_by,  solution.update_time,  solution.remark, solution_type.solution_type_name from solution
        left join solution_type on solution.solution_type_id = solution_type.solution_type_id
    </sql>

    <select id="selectSolutionList" parameterType="Solution" resultMap="SolutionResult">
        <include refid="selectSolutionVo"/>
        <where>  
            <if test="solutionTypeId != null "> and solution_type_id = #{solutionTypeId}</if>
            <if test="solutionName != null  and solutionName != ''"> and solution_name like concat('%', #{solutionName}, '%')</if>
            <if test="solutionBanner != null  and solutionBanner != ''"> and solution_banner = #{solutionBanner}</if>
            <if test="solutionIntroduction != null  and solutionIntroduction != ''"> and solution_introduction = #{solutionIntroduction}</if>
            <if test="solutionOverview != null  and solutionOverview != ''"> and solution_overview = #{solutionOverview}</if>
            <if test="recommend != null  and recommend != ''"> and recommend = #{recommend}</if>
            <if test="solutionImg != null  and solutionImg != ''"> and solution_img = #{solutionImg}</if>
            <if test="solutionStatus != null  and solutionStatus != ''"> and solution_status = #{solutionStatus}</if>
        </where>
    </select>
    
    <select id="selectSolutionBySolutionId" parameterType="Long" resultMap="SolutionResult">
        <include refid="selectSolutionVo"/>
        where solution_id = #{solutionId}
    </select>
        
    <insert id="insertSolution" parameterType="Solution" useGeneratedKeys="true" keyProperty="solutionId">
        insert into solution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="solutionTypeId != null">solution_type_id,</if>
            <if test="solutionName != null">solution_name,</if>
            <if test="solutionBanner != null">solution_banner,</if>
            <if test="solutionIntroduction != null">solution_introduction,</if>
            <if test="solutionOverview != null">solution_overview,</if>
            <if test="solutionImg != null">solution_img,</if>
            <if test="solutionStatus != null">solution_status,</if>
            <if test="recommend != null">recommend,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="solutionTypeId != null">#{solutionTypeId},</if>
            <if test="solutionName != null">#{solutionName},</if>
            <if test="solutionBanner != null">#{solutionBanner},</if>
            <if test="solutionIntroduction != null">#{solutionIntroduction},</if>
            <if test="solutionOverview != null">#{solutionOverview},</if>
            <if test="solutionImg != null">#{solutionImg},</if>
            <if test="solutionStatus != null">#{solutionStatus},</if>
            <if test="recommend != null">#{recommend},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSolution" parameterType="Solution">
        update solution
        <trim prefix="SET" suffixOverrides=",">
            <if test="solutionTypeId != null">solution_type_id = #{solutionTypeId},</if>
            <if test="solutionName != null">solution_name = #{solutionName},</if>
            <if test="solutionBanner != null">solution_banner = #{solutionBanner},</if>
            <if test="solutionIntroduction != null">solution_introduction = #{solutionIntroduction},</if>
            <if test="solutionOverview != null">solution_overview = #{solutionOverview},</if>
            <if test="solutionImg != null">solution_img = #{solutionImg},</if>
            <if test="solutionStatus != null">solution_status = #{solutionStatus},</if>
            <if test="recommend != null">recommend = #{recommend},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where solution_id = #{solutionId}
    </update>

    <delete id="deleteSolutionBySolutionId" parameterType="Long">
        delete from solution where solution_id = #{solutionId}
    </delete>

    <delete id="deleteSolutionBySolutionIds" parameterType="String">
        delete from solution where solution_id in 
        <foreach item="solutionId" collection="array" open="(" separator="," close=")">
            #{solutionId}
        </foreach>
    </delete>
</mapper>