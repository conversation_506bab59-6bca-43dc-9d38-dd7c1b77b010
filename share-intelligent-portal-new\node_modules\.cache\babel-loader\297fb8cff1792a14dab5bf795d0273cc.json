{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\purchaseSales\\index.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\purchaseSales\\index.js", "mtime": 1750311961332}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getDemandList", "params", "request", "url", "method", "getSupplyList", "gatewayDemendListTen", "gatewayDemandListShow", "getDemandDetail", "id", "getResourceHallList", "gatewaySupplyListShow", "getSupplyDetail", "getCheckSubmit", "getCompanyHomeList", "getCompanyListLb", "getCompanyDetail", "getExpertList", "getExpertListFour", "getExpertDetail", "getActivityList", "getActivityDetail", "addActivityEnroll", "data", "insList", "insDetail", "concat", "laboratoryList", "laboratoryDetail"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/api/purchaseSales/index.js"], "sourcesContent": ["/*\r\n * @Author: jhy\r\n * @Date: 2023-01-30 17:58:37\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-12-11 14:09:59\r\n */\r\nimport request from \"@/utils/request\";\r\n\r\n// 采销互联--需求\r\nexport function getDemandList(params) {\r\n  return request({\r\n    url: \"/system/demand/secret/list\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 采销互联--资源\r\nexport function getSupplyList(params) {\r\n  return request({\r\n    url: \"/system/supply/secret/list\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n//  商机需求\r\nexport function gatewayDemendListTen(params) {\r\n  return request({\r\n    // url: \"/system/demand/secret/gatewayList\",\r\n    url: \"/system/demand/gatewayListTen\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n//  采销互联--找需求\r\nexport function gatewayDemandListShow(params) {\r\n  return request({\r\n    url: \"/system/demand/gatewayListShow\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 需求详情\r\nexport function getDemandDetail(id) {\r\n  return request({\r\n    url: \"/system/demand/secret/\" + id,\r\n    method: \"get\",\r\n  });\r\n}\r\n\r\n// 资源大厅\r\nexport function getResourceHallList(params) {\r\n  return request({\r\n    // url: \"/system/supply/secret/gatewayList\",\r\n    url: \"/system/supply/gatewayListTen\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 采销互联--找资源\r\nexport function gatewaySupplyListShow(params) {\r\n  return request({\r\n    url: \"/system/supply/gatewayListShow\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 资源详情\r\nexport function getSupplyDetail(id) {\r\n  return request({\r\n    url: \"/system/supply/secret/\" + id,\r\n    method: \"get\",\r\n  });\r\n}\r\n\r\n// 是否对此资源提交过意向\r\nexport function getCheckSubmit(params) {\r\n  return request({\r\n    url: \"/system/interactRecord/check-submit\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 采销互联--推荐企业名录\r\nexport function getCompanyHomeList(params) {\r\n  return request({\r\n    // url: \"/system/company/mag/getCompanylist_home\",\r\n    url: \"/system/company/mag/secret/getCompanylist_cxhl_list\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 采销互联--推荐企业列表\r\nexport function getCompanyListLb(params) {\r\n  return request({\r\n    url: \"/system/company/mag/secret/getCompanylist_cxhl_lb\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 企业详情\r\nexport function getCompanyDetail(params) {\r\n  return request({\r\n    url: \"/system/company/mag/detailShow\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 采销互联--专家智库列表\r\nexport function getExpertList(params) {\r\n  return request({\r\n    url: \"/system/expert/listTen\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 采销互联--专家智库\r\nexport function getExpertListFour(params) {\r\n  return request({\r\n    url: \"/system/expert/listFour\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 专家详情\r\nexport function getExpertDetail(params) {\r\n  return request({\r\n    // url: \"/system/expert/detail\",\r\n    url: \"/system/expert/detailShow\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 采销互联--活动广场列表\r\nexport function getActivityList(params) {\r\n  return request({\r\n    url: \"/system/activity-portal/list\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 活动详情\r\nexport function getActivityDetail(params) {\r\n  return request({\r\n    url: \"/system/activity-portal/detail\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n\r\n// 活动详情--立即报名\r\nexport function addActivityEnroll(data) {\r\n  return request({\r\n    url: \"/system/activity-enroll/add\",\r\n    method: \"post\",\r\n    data,\r\n  });\r\n}\r\n\r\n// 设备列表\r\nexport function insList(data) {\r\n  return request({\r\n    url: \"/system/web/tyInstrument/list\",\r\n    method: \"get\",\r\n    params: data,\r\n  });\r\n}\r\n\r\n// 设备详情\r\nexport function insDetail(id) {\r\n  return request({\r\n    url: `/system/web/tyInstrument/${id}`,\r\n    method: \"get\",\r\n  });\r\n}\r\n\r\n// 实验室列表\r\nexport function laboratoryList(data) {\r\n  return request({\r\n    url: \"/system/web/wq_lab/list\",\r\n    method: \"get\",\r\n    params: data,\r\n  });\r\n}\r\n\r\n// 实验室详情\r\nexport function laboratoryDetail(id) {\r\n  return request({\r\n    url: `/system/web/wq_lab/${id}`,\r\n    method: \"get\",\r\n  });\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AANA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACO,SAASC,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACJ,MAAM,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,oBAAoBA,CAACL,MAAM,EAAE;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACb;IACAC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,qBAAqBA,CAACN,MAAM,EAAE;EAC5C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,eAAeA,CAACC,EAAE,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGM,EAAE;IAClCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,mBAAmBA,CAACT,MAAM,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACb;IACAC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,qBAAqBA,CAACV,MAAM,EAAE;EAC5C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,eAAeA,CAACH,EAAE,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGM,EAAE;IAClCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,cAAcA,CAACZ,MAAM,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASa,kBAAkBA,CAACb,MAAM,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACb;IACAC,GAAG,EAAE,qDAAqD;IAC1DC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,gBAAgBA,CAACd,MAAM,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mDAAmD;IACxDC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,gBAAgBA,CAACf,MAAM,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASgB,aAAaA,CAAChB,MAAM,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASiB,iBAAiBA,CAACjB,MAAM,EAAE;EACxC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASkB,eAAeA,CAAClB,MAAM,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACb;IACAC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASmB,eAAeA,CAACnB,MAAM,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASoB,iBAAiBA,CAACpB,MAAM,EAAE;EACxC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASqB,iBAAiBA,CAACC,IAAI,EAAE;EACtC,OAAO,IAAArB,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdmB,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,OAAOA,CAACD,IAAI,EAAE;EAC5B,OAAO,IAAArB,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEsB;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAAChB,EAAE,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,8BAAAuB,MAAA,CAA8BjB,EAAE,CAAE;IACrCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASuB,cAAcA,CAACJ,IAAI,EAAE;EACnC,OAAO,IAAArB,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEsB;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,gBAAgBA,CAACnB,EAAE,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,wBAAAuB,MAAA,CAAwBjB,EAAE,CAAE;IAC/BL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ", "ignoreList": []}]}