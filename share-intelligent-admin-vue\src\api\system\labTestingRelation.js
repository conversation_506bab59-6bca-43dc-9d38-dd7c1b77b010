import request from '@/utils/request'

// 查询实验室检测项目关联列表
export function listLabTestingRelation(query) {
  return request({
    url: '/system/labTestingRelation/list',
    method: 'get',
    params: query
  })
}

// 查询实验室检测项目关联详细
export function getLabTestingRelation(id) {
  return request({
    url: '/system/labTestingRelation/' + id,
    method: 'get'
  })
}

// 新增实验室检测项目关联
export function addLabTestingRelation(data) {
  return request({
    url: '/system/labTestingRelation',
    method: 'post',
    data: data
  })
}

// 修改实验室检测项目关联
export function updateLabTestingRelation(data) {
  return request({
    url: '/system/labTestingRelation',
    method: 'put',
    data: data
  })
}

// 删除实验室检测项目关联
export function delLabTestingRelation(id) {
  return request({
    url: '/system/labTestingRelation/' + id,
    method: 'delete'
  })
}
