{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\enterpriseList\\detail.vue?vue&type=template&id=47302afe&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\enterpriseList\\detail.vue", "mtime": 1750311962986}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}