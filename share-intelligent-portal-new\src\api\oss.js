/*
 * @Author: zhc
 * @Date: 2023-02-07 17:43:14
 * @LastEditTime: 2023-02-12 12:04:00
 * @Description:
 * @LastEditors: zhc
 */
/*
 * @Author: zhc
 * @Date: 2023-01-31 11:13:23
 * @LastEditTime: 2023-01-31 11:13:23
 * @Description:
 * @LastEditors: zhc
 */
import request from "@/utils/request";

// 查询云储存列表
export function listOss(query) {
  return request({
    url: "/xipin/oss/list",
    method: "get",
    params: query,
  });
}

// 查询云储存详细
export function getOss(id) {
  return request({
    url: "/xipin/oss/" + id,
    method: "get",
  });
}

// 新增云储存
export function addOss(data) {
  return request({
    url: "/xipin/oss",
    method: "post",
    data: data,
  });
}
// 新增云储存
export function uploadFile(data) {
  return request({
    url: "/file/upload",
    method: "post",
    data: data,
  });
}

// 修改云储存
export function updateOss(data) {
  return request({
    url: "/xipin/oss",
    method: "put",
    data: data,
  });
}

// 删除云储存
export function delOss(id) {
  return request({
    url: "/system/oss/delete/" + id,
    method: "delete",
  });
}

// 修改云储存
export function upload(data) {
  return request({
    url: "/xipin/oss",
    method: "post",
    data: data,
  });
}

// 修改云储存
export function uploadUrl() {
  return process.env.VUE_APP_BASE_API + "/file/upload";
}
