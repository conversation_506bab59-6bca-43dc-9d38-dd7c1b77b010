{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\newsCenter\\index.vue?vue&type=template&id=6a8080b6&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\newsCenter\\index.vue", "mtime": 1750311962978}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}