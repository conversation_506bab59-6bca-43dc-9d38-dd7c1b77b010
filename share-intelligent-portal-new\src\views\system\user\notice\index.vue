<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-02-21 16:37:16
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div class="app-container">
    <div class="notice-record-page">
      <el-row :gutter="20">
        <el-col :span="2.5" :xs="24">
          <user-menu />
        </el-col>
        <el-col :span="20" :xs="24">
          <div class="response-message">
            <div
              class="response-message-item"
              v-for="item in records"
              :key="item.id"
            >
              <el-image
                class="iamge"
                style="width: 36px; height: 36px"
                :src="
                  require(item.readStatus === 0
                    ? '@/assets/user/notice_2.png'
                    : '@/assets/user/notice_1.png')
                "
              ></el-image>
              <a class="item-content" @click="getInfoDetail(item.id)">
                <div class="title">{{ item.title || "系统消息" }}</div>
                <div class="item-content-bottom">
                  <div class="content">
                    {{ item.remark || item.describeInfo || "--" }}
                  </div>

                  <div class="date">{{ item.createTime || "--" }}</div>
                </div>
              </a>
              <a @click="revocationApply(item.id)" v-if="item.backStatus == 1">
                <el-image
                  class="re-icon"
                  style="width: 18px; height: 20px"
                  :src="require('@/assets/user/revocation.png')"
                ></el-image>
              </a>

              <a @click="deleteInfo(item, 2)">
                <el-image
                  class="delete-icon"
                  style="width: 18px; height: 20px"
                  :src="require('@/assets/user/delete.png')"
                ></el-image>
              </a>
            </div>
          </div>
          <el-pagination
            v-show="total > 0"
            background
            layout="prev, pager, next"
            :page-size="5"
            :current-page.sync="systemParams.pageNum"
            @current-change="systemPageChange"
            :total="total"
          >
          </el-pagination>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import UserMenu from "../components/userMenu.vue";
import { listInfo, deleteInfo, getInfoDetail } from "@/api/system/info";
export default {
  name: "Notice",
  components: { UserMenu },
  data() {
    return {
      activeName: "first",
      records: [],
      systemParams: {
        pageNum: 1,
        pageSize: 5,
      },
      total: 0,
      fit: "cover",
      dialogVisible: false,
    };
  },
  created() {
    this.getSystemList();
  },
  methods: {
    goDetail(id) {
      this.$router.push("/user/noticeDetail?id=" + id);
    },
    handleClick(tab, event) {
      if (tab.$options.propsData.name == "first") {
        this.getResponseList();
      } else {
        this.getSystemList();
      }
    },

    deleteInfo(row, type) {
      this.$confirm("是否确认删除该消息？", { type: "error" })
        .then((_) => {
          deleteInfo({ ids: row.id.toString() }).then((response) => {
            this.$message({
              message: "操作成功",
              type: "success",
            });
          });
          if (type == 1) {
            this.getResponseList();
          } else {
            this.getSystemList();
          }
        })
        .catch((_) => {});
    },
    getSystemList() {
      listInfo({ ...this.systemParams, type: 2 }).then((response) => {
        this.records = response.rows;
        this.total = response.total;
      });
    },
    getInfoDetail(id) {
      getInfoDetail(id).then((response) => {
        this.getSystemList();
      });
    },
    revocationApply(id) {
      revocationApply({ id: id }).then((response) => {
        this.$message({
          message: "操作成功",
          type: "success",
        });
        this.getSystemList();
      });
    },
    systemPageChange(res) {
      this.systemParams.pageNum = res;
      this.getSystemList();
    },
  },
};
</script>

<style lang="scss">
.app-container {
  background: #f4f5f9;
  .notice-record-page {
    .response-message {
      width: 100%;
      height: 600px;
      background-color: #fff;
      .none-class {
        text-align: center;
        padding: 10% 0;
        .text {
          font-size: 14px;
          font-weight: 400;
          color: #999999;
          line-height: 14px;
        }
      }
      .response-message-item {
        width: 100%;
        height: 100px;
        vertical-align: middle;
        padding: 22px 22px;
        display: flex;
        border-bottom: 1px solid #e8e8e8;
        position: relative;

        .iamge {
          margin: auto 0;
        }
        .item-content {
          vertical-align: middle;
          margin-left: 14px;
          .title {
            font-size: 16px;
            font-weight: 500;
            color: #333333;
            line-height: 20px;
          }
          .item-content-bottom {
            width: 100%;
            display: flex;
            line-height: 45px;
            font-size: 14px;
            font-weight: 400;

            .content {
              width: 680px;
              height: 30px;
              color: #666666;
              overflow: hidden;
              -webkit-line-clamp: 1;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
            }
            .to-detail {
              width: 150px;
              text-align: center;
              color: #21c9b8;
            }
            .date {
              width: 180px;
              text-align: center;
              font-family: PingFangSC-Regular, PingFang SC;
              color: #666666;
            }
          }
        }
        .delete-icon {
          right: 30px;
          top: 40px;
          margin: 0 auto;
          position: absolute;
        }
        .re-icon {
          right: 80px;
          top: 40px;
          margin: 0 auto;
          position: absolute;
        }
      }
    }

    .el-pagination {
      width: 100%;
      margin-top: 20px;
      text-align: center;
    }
    .el-pagination.is-background .el-pager li {
      background-color: #fff;
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active {
      background-color: #21c9b8;
      color: #ffffff;
    }
    .el-pagination.is-background .el-pager li:not(.disabled):hover {
      color: #21c9b8;
    }
  }
}
</style>
