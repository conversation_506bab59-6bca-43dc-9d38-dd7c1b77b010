{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\aboutUs\\index.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\aboutUs\\index.js", "mtime": 1750311961290}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0TmV3c0luZm9ybWF0aW9uTGlzdCA9IGdldE5ld3NJbmZvcm1hdGlvbkxpc3Q7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDojrflj5bliqjmgIHotYTorq/liJfooagKZnVuY3Rpb24gZ2V0TmV3c0luZm9ybWF0aW9uTGlzdChwYXJhbXMpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogIi9wb3J0YWx3ZWIvTmV3c0luZm9ybWF0aW9uL2xpc3REZXNrIiwKICAgIG1ldGhvZDogImdldCIsCiAgICBwYXJhbXM6IHBhcmFtcwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getNewsInformationList", "params", "request", "url", "method"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/api/aboutUs/index.js"], "sourcesContent": ["import request from \"@/utils/request\";\r\n\r\n// 获取动态资讯列表\r\nexport function getNewsInformationList(params) {\r\n  return request({\r\n    url: \"/portalweb/NewsInformation/listDesk\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}"], "mappings": ";;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,sBAAsBA,CAACC,MAAM,EAAE;EAC7C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}