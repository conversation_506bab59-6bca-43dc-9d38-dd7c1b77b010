import request from '@/utils/request'

// 查询政策意见反馈列表
export function listPolicyInformationFeedback(query) {
  return request({
    url: '/portalconsole/PolicyInformationFeedback/list',
    method: 'get',
    params: query
  })
}

// 查询政策意见反馈详细
export function getPolicyInformationFeedback(policyInformationFeedbackId) {
  return request({
    url: '/portalconsole/PolicyInformationFeedback/' + policyInformationFeedbackId,
    method: 'get'
  })
}

// 新增政策意见反馈
export function addPolicyInformationFeedback(data) {
  return request({
    url: '/portalconsole/PolicyInformationFeedback',
    method: 'post',
    data: data
  })
}

// 修改政策意见反馈
export function updatePolicyInformationFeedback(data) {
  return request({
    url: '/portalconsole/PolicyInformationFeedback',
    method: 'put',
    data: data
  })
}

// 删除政策意见反馈
export function delPolicyInformationFeedback(policyInformationFeedbackId) {
  return request({
    url: '/portalconsole/PolicyInformationFeedback/' + policyInformationFeedbackId,
    method: 'delete'
  })
}
