<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="身份证号" prop="idCard">
        <el-input v-model="queryParams.idCard" placeholder="请输入身份证号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="学历" prop="education">
        <el-input v-model="queryParams.education" placeholder="请输入学历" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="联系方式" prop="contact">
        <el-input v-model="queryParams.contact" placeholder="请输入联系方式" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="鉴定工种1" prop="certificationType1">
        <el-input v-model="queryParams.certificationType1" placeholder="请输入鉴定工种1" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="鉴定技能等级" prop="skillLevel">
        <el-input v-model="queryParams.skillLevel" placeholder="请输入鉴定技能等级" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="鉴定工种2" prop="certificationType2">
        <el-input v-model="queryParams.certificationType2" placeholder="请输入鉴定工种2" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:talentCertificationApplication:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:talentCertificationApplication:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:talentCertificationApplication:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['system:talentCertificationApplication:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="talentCertificationApplicationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="身份证号" align="center" prop="idCard" />
      <el-table-column label="学历" align="center" prop="education" />
      <el-table-column label="所在单位" align="center" prop="certificationType2" />
      <el-table-column label="联系方式" align="center" prop="contact" />
      <el-table-column label="鉴定工种" align="center" prop="certificationType1" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.certification_type" :value="scope.row.certificationType1" />
        </template>
      </el-table-column>
      <el-table-column label="鉴定技能等级" align="center" prop="skillLevel" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.skill_level" :value="scope.row.skillLevel" />
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="status" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.talent_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:talentCertificationApplication:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:talentCertificationApplication:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改衡水市职业技能鉴定中心申请对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="身份证号" prop="idCard">
          <el-input v-model="form.idCard" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="学历" prop="education">
          <el-select v-model="form.education" placeholder="请选择学历">
            <el-option v-for="dict in dict.type.education" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系方式" prop="contact">
          <el-input v-model="form.contact" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="所在单位" prop="certificationType2">
          <el-input v-model="form.certificationType2" placeholder="请输入所在单位" />
        </el-form-item>
        <el-form-item label="鉴定工种" prop="certificationType1">
          <el-select v-model="form.certificationType1" placeholder="请选择鉴定工种">
            <el-option v-for="dict in dict.type.certification_type" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="鉴定技能等级" prop="skillLevel">
          <el-select v-model="form.skillLevel" placeholder="请选择鉴定技能等级">
            <el-option v-for="dict in dict.type.skill_level" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态" prop="status">
          <el-select v-model="form.status" placeholder="请审核">
            <el-option v-for="dict in dict.type.talent_status" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTalentCertificationApplication, getTalentCertificationApplication, delTalentCertificationApplication, addTalentCertificationApplication, updateTalentCertificationApplication } from "@/api/system/talentCertificationApplication";

export default {
  name: "TalentCertificationApplication",
  dicts: ['education', 'certification_type', 'skill_level','talent_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 衡水市职业技能鉴定中心申请表格数据
      talentCertificationApplicationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        idCard: null,
        education: null,
        contact: null,
        certificationType1: null,
        skillLevel: null,
        certificationType2: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "姓名不能为空", trigger: "blur" }
        ],
        // idCard: [
        //   { required: true, message: "身份证号不能为空", trigger: "blur" }
        // ],
        // contact: [
        //   { required: true, message: "联系方式不能为空", trigger: "blur" }
        // ],
        certificationType1: [
          { required: true, message: "鉴定工种不能为空", trigger: "change" }
        ],
        // skillLevel: [
        //   { required: true, message: "鉴定技能等级不能为空", trigger: "change" }
        // ],
        // status:[
        //   { required: true, message: "审核状态不能为空", trigger: "change" }
        // ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询衡水市职业技能鉴定中心申请列表 */
    getList() {
      this.loading = true;
      listTalentCertificationApplication(this.queryParams).then(response => {
        this.talentCertificationApplicationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        idCard: null,
        education: null,
        contact: null,
        certificationType1: null,
        skillLevel: null,
        certificationType2: null,
        createTime: null,
        updateTime: null,
        status: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加衡水市职业技能鉴定中心申请";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getTalentCertificationApplication(id).then(response => {
        this.form = response.data;
        this.form.status = this.form.status.toString()
        this.open = true;
        this.title = "修改衡水市职业技能鉴定中心申请";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateTalentCertificationApplication(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTalentCertificationApplication(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除衡水市职业技能鉴定中心申请编号为"' + ids + '"的数据项？').then(function () {
        return delTalentCertificationApplication(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/talentCertificationApplication/export', {
        ...this.queryParams
      }, `talentCertificationApplication_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
