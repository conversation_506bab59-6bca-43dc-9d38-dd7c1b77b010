import request from '@/utils/request'

// 查询工厂业绩情况列表
export function listPerformance(query) {
  return request({
    url: '/system/performance/list',
    method: 'get',
    params: query
  })
}

// 查询工厂业绩情况详细
export function getPerformance(id) {
  return request({
    url: '/system/performance/' + id,
    method: 'get'
  })
}

// 新增工厂业绩情况
export function addPerformance(data) {
  return request({
    url: '/system/performance',
    method: 'post',
    data: data
  })
}

// 修改工厂业绩情况
export function updatePerformance(data) {
  return request({
    url: '/system/performance',
    method: 'put',
    data: data
  })
}

// 删除工厂业绩情况
export function delPerformance(id) {
  return request({
    url: '/system/performance/' + id,
    method: 'delete'
  })
}
