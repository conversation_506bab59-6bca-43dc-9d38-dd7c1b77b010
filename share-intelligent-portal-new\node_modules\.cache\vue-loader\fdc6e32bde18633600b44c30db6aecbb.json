{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\activitySquare\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\activitySquare\\index.vue", "mtime": 1750311962983}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRBY3Rpdml0eUxpc3QgfSBmcm9tICJAL2FwaS9wdXJjaGFzZVNhbGVzIjsNCmltcG9ydCB7IGdldERpY3RzIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2RpY3QvZGF0YSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBmb3JtOiB7DQogICAgICAgIGtleXdvcmRzOiAiIiwgLy/mkJzntKLlhoXlrrkNCiAgICAgIH0sDQogICAgICBmb3JtSW5mbzogew0KICAgICAgICBhY3Rpdml0eVR5cGU6ICIiLCAvL+a0u+WKqOexu+Weiw0KICAgICAgfSwNCiAgICAgIGFjdGl2aXR5VHlwZUxpc3Q6IFtdLCAvL+a0u+WKqOexu+Wei+WIl+ihqA0KICAgICAgZGF0YTogW10sDQogICAgICBwYWdlTnVtOiAxLA0KICAgICAgcGFnZVNpemU6IDksDQogICAgICB0b3RhbDogMCwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0RGljdHNMaXN0KCJhY3Rpdml0eV90eXBlIiwgImFjdGl2aXR5VHlwZUxpc3QiKTsNCiAgICB0aGlzLnNlYXJjaCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgc2VhcmNoKCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGdldEFjdGl2aXR5TGlzdCh7DQogICAgICAgIC4uLnRoaXMuZm9ybSwNCiAgICAgICAgLi4udGhpcy5mb3JtSW5mbywNCiAgICAgICAgcGFnZU51bTogdGhpcy5wYWdlTnVtLA0KICAgICAgICBwYWdlU2l6ZTogdGhpcy5wYWdlU2l6ZSwNCiAgICAgIH0pDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICBsZXQgeyByb3dzLCB0b3RhbCB9ID0gcmVzIHx8IFtdOw0KICAgICAgICAgIHRoaXMuZGF0YSA9IHJvd3M7DQogICAgICAgICAgdGhpcy50b3RhbCA9IHRvdGFsOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4gew0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWtl+WFuA0KICAgIGdldERpY3RzTGlzdChjb2RlLCBwcm9wZXJ0eU5hbWUpIHsNCiAgICAgIGdldERpY3RzKGNvZGUpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzW3Byb3BlcnR5TmFtZV0gPSByZXMuZGF0YSB8fCBbXTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgY2hhbmdlUmFkaW8oKSB7DQogICAgICB0aGlzLm9uU2VhcmNoKCk7DQogICAgfSwNCiAgICBoYW5kbGVTaXplQ2hhbmdlKHBhZ2VTaXplKSB7DQogICAgICB0aGlzLnBhZ2VTaXplID0gcGFnZVNpemU7DQogICAgICB0aGlzLm9uU2VhcmNoKCk7DQogICAgfSwNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKHBhZ2VOdW0pIHsNCiAgICAgIHRoaXMucGFnZU51bSA9IHBhZ2VOdW07DQogICAgICB0aGlzLnNlYXJjaCgpOw0KICAgIH0sDQogICAgb25TZWFyY2goKSB7DQogICAgICB0aGlzLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5zZWFyY2goKTsNCiAgICB9LA0KICAgIC8vIOi3s+i9rOWIsOacgOaWsOa0u+WKqOmhtemdog0KICAgIGdvQWN0aXZpdHlEZXRhaWwoaWQpIHsNCiAgICAgIGxldCByb3V0ZURhdGEgPSB0aGlzLiRyb3V0ZXIucmVzb2x2ZSh7DQogICAgICAgIHBhdGg6ICIvYWN0aXZpdHlEZXRhaWwiLA0KICAgICAgICBxdWVyeTogeyBpZCB9LA0KICAgICAgfSk7DQogICAgICB3aW5kb3cub3Blbihyb3V0ZURhdGEuaHJlZiwgIl9ibGFuayIpOw0KICAgIH0sDQogICAgZ29Ib21lKCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiL2luZGV4IiB9KTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyHA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/purchaseSales/component/activitySquare", "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-01-30 11:29:06\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-02-13 11:28:34\r\n-->\r\n<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img\r\n        src=\"../../../../assets/activitySquare/activitySquareBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-title-box\">\r\n          <div class=\"activity-divider\"></div>\r\n          <div class=\"activity-title\">链活动</div>\r\n          <div class=\"activity-divider\"></div>\r\n        </div>\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.keywords\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"activity-info-content\">\r\n        <div class=\"activity-search-type-box\">\r\n          <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n            <div class=\"activity-search-line\">\r\n              <el-form-item label=\"活动类型\" class=\"activity-search-line-item\">\r\n                <el-radio-group\r\n                  v-model=\"formInfo.activityType\"\r\n                  class=\"activity-search-radio\"\r\n                  @input=\"changeRadio\"\r\n                >\r\n                  <el-radio-button label=\"\">全部</el-radio-button>\r\n                  <el-radio-button\r\n                    v-for=\"(item, index) in activityTypeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.dictValue\"\r\n                    >{{ item.dictLabel }}</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n        <div class=\"content\">\r\n          <div\r\n            class=\"contentItem\"\r\n            v-for=\"item in data\"\r\n            :key=\"item.id\"\r\n            @click=\"goActivityDetail(item.id)\"\r\n          >\r\n            <div>\r\n              <img :src=\"item.activityPicture\" alt=\"\" />\r\n            </div>\r\n            <div class=\"contentTitle\">{{ item.activityName }}</div>\r\n          </div>\r\n        </div>\r\n        <!-- <div\r\n          v-for=\"(item, index) in data\"\r\n          :key=\"index\"\r\n          class=\"activity-list-item\"\r\n          @click=\"goActivityDetail(item.id)\"\r\n        >\r\n          <div class=\"list-item-content\">\r\n            <div class=\"list-item-img\">\r\n              <img\r\n                v-if=\"item.activityPicture\"\r\n                alt=\"\"\r\n                :src=\"item.activityPicture\"\r\n              />\r\n            </div>\r\n            <div class=\"list-item-info\">\r\n              <div class=\"list-item-title\">\r\n                {{ item.activityName }}\r\n              </div>\r\n              <div class=\"list-item-text\">\r\n                {{ item.activityOverview }}\r\n              </div>\r\n              <div class=\"list-item-time\">{{ item.createTimeStr }}</div>\r\n            </div>\r\n          </div>\r\n        </div> -->\r\n        <div class=\"activity-page-end\">\r\n          <el-button class=\"activity-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"data && data.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getActivityList } from \"@/api/purchaseSales\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        activityType: \"\", //活动类型\r\n      },\r\n      activityTypeList: [], //活动类型列表\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 9,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getDictsList(\"activity_type\", \"activityTypeList\");\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getActivityList({\r\n        ...this.form,\r\n        ...this.formInfo,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n    // 跳转到最新活动页面\r\n    goActivityDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/activityDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-info-content {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    .activity-search-type-box {\r\n      background: #fff;\r\n      margin-bottom: -7px;\r\n      .activity-search-line {\r\n        padding: 14px 24px 4px;\r\n        .activity-search-line-item {\r\n          margin-bottom: 0;\r\n        }\r\n        & + .activity-search-line {\r\n          border-top: 1px solid #f5f5f5;\r\n        }\r\n      }\r\n    }\r\n    .activity-list-item {\r\n      width: 100%;\r\n      background: #fff;\r\n      border-radius: 12px;\r\n      margin-top: 24px;\r\n      .list-item-content {\r\n        display: flex;\r\n        padding: 24px 32px;\r\n        cursor: pointer;\r\n        .list-item-img {\r\n          width: 230px;\r\n          height: 164px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n            border-radius: 5px;\r\n          }\r\n        }\r\n        .list-item-info {\r\n          padding-left: 24px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          .list-item-title {\r\n            width: 806px;\r\n            height: 24px;\r\n            text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n            white-space: nowrap; /*让文字不换行*/\r\n            overflow: hidden; /*超出要隐藏*/\r\n            font-size: 24px;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            line-height: 24px;\r\n            margin: 8px 0 24px;\r\n            word-wrap: break-word;\r\n          }\r\n          .list-item-text {\r\n            width: 806px;\r\n            height: 60px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            text-overflow: ellipsis;\r\n            font-size: 16px;\r\n            color: #666;\r\n            line-height: 30px;\r\n            word-wrap: break-word;\r\n          }\r\n          .list-item-time {\r\n            color: #999;\r\n            line-height: 14px;\r\n            margin-top: 24px;\r\n          }\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .activity-page-end {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin: 0 auto;\r\n      padding: 24px 0 60px;\r\n      .activity-page-btn {\r\n        width: 82px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n  .content {\r\n    display: flex;\r\n    // justify-content: space-between;\r\n    flex-wrap: wrap;\r\n    width: 100%;\r\n    height: 100%;\r\n    margin-top: 20px;\r\n    padding-bottom: 30px;\r\n    .contentItem {\r\n      width: 32%;\r\n      height: 340px;\r\n      text-align: center;\r\n      padding: 20px 30px;\r\n      background: #fff;\r\n      margin: 20px 0 0 23px;\r\n      cursor: pointer;\r\n      img {\r\n        width: 100%;\r\n        height: 230px;\r\n      }\r\n      .contentTitle {\r\n        margin-top: 15px;\r\n        font-size: 18px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #222222;\r\n      }\r\n    }\r\n    .contentItem:nth-child(3n + 1) {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}