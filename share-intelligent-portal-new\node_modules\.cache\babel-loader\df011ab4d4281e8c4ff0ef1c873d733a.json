{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1750311962855}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_path", "_interopRequireDefault", "require", "_validate", "_Item", "_Link", "_FixiOSBug", "name", "components", "<PERSON><PERSON>", "AppLink", "mixins", "FixiOSBug", "props", "item", "type", "Object", "required", "isNest", "Boolean", "default", "basePath", "String", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "methods", "hasOneShowingChild", "_this", "children", "arguments", "length", "undefined", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "hidden", "_objectSpread2", "path", "noShowingChildren", "<PERSON><PERSON><PERSON>", "routePath", "routeQuery", "isExternal", "query", "JSON", "parse", "resolve"], "sources": ["src/layout/components/Sidebar/SidebarItem.vue"], "sourcesContent": ["<template>\r\n  <div v-if=\"!item.hidden\">\r\n    <template v-if=\"hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow\">\r\n      <app-link v-if=\"onlyOneChild.meta\" :to=\"resolvePath(onlyOneChild.path, onlyOneChild.query)\">\r\n        <el-menu-item :index=\"resolvePath(onlyOneChild.path)\" :class=\"{'submenu-title-noDropdown':!isNest}\">\r\n          <item :icon=\"onlyOneChild.meta.icon||(item.meta&&item.meta.icon)\" :title=\"onlyOneChild.meta.title\" />\r\n        </el-menu-item>\r\n      </app-link>\r\n    </template>\r\n\r\n    <el-submenu v-else ref=\"subMenu\" :index=\"resolvePath(item.path)\" popper-append-to-body>\r\n      <template slot=\"title\">\r\n        <item v-if=\"item.meta\" :icon=\"item.meta && item.meta.icon\" :title=\"item.meta.title\" />\r\n      </template>\r\n      <sidebar-item\r\n        v-for=\"child in item.children\"\r\n        :key=\"child.path\"\r\n        :is-nest=\"true\"\r\n        :item=\"child\"\r\n        :base-path=\"resolvePath(child.path)\"\r\n        class=\"nest-menu\"\r\n      />\r\n    </el-submenu>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport path from 'path'\r\nimport { isExternal } from '@/utils/validate'\r\nimport Item from './Item'\r\nimport AppLink from './Link'\r\nimport FixiOSBug from './FixiOSBug'\r\n\r\nexport default {\r\n  name: 'SidebarItem',\r\n  components: { Item, AppLink },\r\n  mixins: [FixiOSBug],\r\n  props: {\r\n    // route object\r\n    item: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    isNest: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    basePath: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    this.onlyOneChild = null\r\n    return {}\r\n  },\r\n  methods: {\r\n    hasOneShowingChild(children = [], parent) {\r\n      if (!children) {\r\n        children = [];\r\n      }\r\n      const showingChildren = children.filter(item => {\r\n        if (item.hidden) {\r\n          return false\r\n        } else {\r\n          // Temp set(will be used if only has one showing child)\r\n          this.onlyOneChild = item\r\n          return true\r\n        }\r\n      })\r\n\r\n      // When there is only one child router, the child router is displayed by default\r\n      if (showingChildren.length === 1) {\r\n        return true\r\n      }\r\n\r\n      // Show parent if there are no child router to display\r\n      if (showingChildren.length === 0) {\r\n        this.onlyOneChild = { ... parent, path: '', noShowingChildren: true }\r\n        return true\r\n      }\r\n\r\n      return false\r\n    },\r\n    resolvePath(routePath, routeQuery) {\r\n      if (isExternal(routePath)) {\r\n        return routePath\r\n      }\r\n      if (isExternal(this.basePath)) {\r\n        return this.basePath\r\n      }\r\n      if (routeQuery) {\r\n        let query = JSON.parse(routeQuery);\r\n        return { path: path.resolve(this.basePath, routePath), query: query }\r\n      }\r\n      return path.resolve(this.basePath, routePath)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;AA2BA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,UAAA,GAAAL,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,UAAA;IAAAC,IAAA,EAAAA,aAAA;IAAAC,OAAA,EAAAA;EAAA;EACAC,MAAA,GAAAC,kBAAA;EACAC,KAAA;IACA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,OAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAN,IAAA,EAAAO,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA,KAAAC,YAAA;IACA;EACA;EACAC,OAAA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,KAAA;MAAA,IAAAC,QAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MAAA,IAAAG,MAAA,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MACA,KAAAH,QAAA;QACAA,QAAA;MACA;MACA,IAAAK,eAAA,GAAAL,QAAA,CAAAM,MAAA,WAAApB,IAAA;QACA,IAAAA,IAAA,CAAAqB,MAAA;UACA;QACA;UACA;UACAR,KAAA,CAAAH,YAAA,GAAAV,IAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAmB,eAAA,CAAAH,MAAA;QACA;MACA;;MAEA;MACA,IAAAG,eAAA,CAAAH,MAAA;QACA,KAAAN,YAAA,OAAAY,cAAA,CAAAhB,OAAA,MAAAgB,cAAA,CAAAhB,OAAA,MAAAY,MAAA;UAAAK,IAAA;UAAAC,iBAAA;QAAA;QACA;MACA;MAEA;IACA;IACAC,WAAA,WAAAA,YAAAC,SAAA,EAAAC,UAAA;MACA,QAAAC,oBAAA,EAAAF,SAAA;QACA,OAAAA,SAAA;MACA;MACA,QAAAE,oBAAA,OAAArB,QAAA;QACA,YAAAA,QAAA;MACA;MACA,IAAAoB,UAAA;QACA,IAAAE,KAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAJ,UAAA;QACA;UAAAJ,IAAA,EAAAA,aAAA,CAAAS,OAAA,MAAAzB,QAAA,EAAAmB,SAAA;UAAAG,KAAA,EAAAA;QAAA;MACA;MACA,OAAAN,aAAA,CAAAS,OAAA,MAAAzB,QAAA,EAAAmB,SAAA;IACA;EACA;AACA", "ignoreList": []}]}