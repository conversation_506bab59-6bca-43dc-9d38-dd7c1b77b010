{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\profile\\index(copy).vue?vue&type=style&index=0&id=5606e2ec&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\profile\\index(copy).vue", "mtime": 1750311963072}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQ6ICNmNGY1Zjk7DQogIC51c2VyLXR5cGUtY2FyZCB7DQogICAgYmFja2dyb3VuZDogcmdiYSgzMywgNzcsIDE5NywgMC4xKTsNCiAgICBtYXJnaW4tbGVmdDogMjBweDsNCiAgICBtYXJnaW4tdG9wOiAxNHB4Ow0KICAgIGNvbG9yOiAjMjE0ZGM1Ow0KICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICBwYWRkaW5nOiA0cHggMTJweCA0cHggMTJweDsNCiAgfQ0KICAudXNlci1pbmZvLWNhcmQgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogICAgcGFkZGluZzogMzBweDsNCiAgICBoZWlnaHQ6IDI0MHB4Ow0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAubGVmdC1pbmZvLWJveCB7DQogICAgICB3aWR0aDogMzAlOw0KICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgLnVzZXItbmFtZSB7DQogICAgICAgIGZvbnQtc2l6ZTogMjhweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgLy8gbWFyZ2luLWxlZnQ6IDIwcHg7DQogICAgICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgICAgICBsaW5lLWhlaWdodDogNTBweDsNCiAgICAgIH0NCiAgICAgIC50YWctZ3JvdXAgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgICAgLmNlcnRpZmljYXRpb24gew0KICAgICAgICAgIHdpZHRoOiAxMTBweDsNCiAgICAgICAgICBoZWlnaHQ6IDQwcHg7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDQwcHg7DQogICAgICAgICAgYmFja2dyb3VuZDogcmdiKDIyOSwgMjQ3LCAyNDMpOw0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgZm9udC1mYW1pbHk6IFNvdXJjZSBIYW4gU2FucyBDTjsNCiAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgIGNvbG9yOiAjMjFjOWI4Ow0KICAgICAgICB9DQogICAgICAgIC5sYWJlbC1jb250YWluZXIgew0KICAgICAgICAgIHBhZGRpbmc6IDRweCA2cHg7DQogICAgICAgICAgbWFyZ2luLXRvcDogNnB4Ow0KICAgICAgICAgIG1hcmdpbi1yaWdodDogNnB4Ow0KICAgICAgICAgIGhlaWdodDogMjRweDsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjBmMWY0Ow0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICBjb2xvcjogIzhhOGM5NDsNCiAgICAgICAgICBsaW5lLWhlaWdodDogMTJweDsNCiAgICAgICAgICAuZWwtaW1hZ2Ugew0KICAgICAgICAgICAgbWFyZ2luOiAycHggNHB4IDAgMDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLm9yYW5nZSB7DQogICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDE5MSwgNjksIDAuMik7DQogICAgICAgICAgY29sb3I6ICNmZjhhMjc7DQogICAgICAgIH0NCiAgICAgICAgLnJlZCB7DQogICAgICAgICAgYmFja2dyb3VuZDogcmdiYSgxOTcsIDM3LCAzMywgMC4xKTsNCiAgICAgICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgICAuZGl2aWRlciB7DQogICAgICB3aWR0aDogMXB4Ow0KICAgICAgaGVpZ2h0OiA4MHB4Ow0KICAgICAgbWFyZ2luOiBhdXRvIDA7DQogICAgICBiYWNrZ3JvdW5kOiAjZThlOGU4Ow0KICAgIH0NCiAgICAucmlnaHQtaW5mby1ib3ggew0KICAgICAgd2lkdGg6IDUwJTsNCiAgICAgIHBhZGRpbmctbGVmdDogNDBweDsNCiAgICAgIHBhZGRpbmctdG9wOiAxMnB4Ow0KICAgICAgaGVpZ2h0OiAxMDBweDsNCiAgICAgIC5waG9uZS1jbGFzcyB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIG1hcmdpbi10b3A6IDE0cHg7DQogICAgICB9DQogICAgICAucGhvbmUtbnVtYmVyIHsNCiAgICAgICAgbWFyZ2luLWxlZnQ6IDEycHg7DQogICAgICB9DQogICAgICAuZ3JleSB7DQogICAgICAgIGNvbG9yOiAjOTk5OTk5Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICAubm90aWZ5LWNhcmQgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogICAgcGFkZGluZzogMzBweDsNCiAgICBoZWlnaHQ6IDI0MHB4Ow0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAuZHJpdmVyIHsNCiAgICAgIHdpZHRoOiA2NSU7DQogICAgICBoZWlnaHQ6IDFweDsNCiAgICAgIGJhY2tncm91bmQ6ICNmN2Y4Zjk7DQogICAgICBtYXJnaW4tdG9wOiAxNXB4Ow0KICAgIH0NCiAgICAubm90aWZ5X2NvbnRlbnQgew0KICAgICAgbWFyZ2luLXRvcDogMTVweDsNCiAgICAgIC5ldmVyeUl0ZW0gew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICBtYXJnaW4tdG9wOiAyMnB4Ow0KICAgICAgICAuaXRlbV9pY29uIHsNCiAgICAgICAgICB3aWR0aDogMTBweDsNCiAgICAgICAgICBoZWlnaHQ6IDEwcHg7DQogICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzIxYzliODsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgIC5pY29uX3NtYWxsIHsNCiAgICAgICAgICAgIHdpZHRoOiA0cHg7DQogICAgICAgICAgICBoZWlnaHQ6IDRweDsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC5kZXNjIHsNCiAgICAgICAgICB3aWR0aDogODAlOw0KICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgIGZvbnQtZmFtaWx5OiBNaWNyb3NvZnQgWWFIZWk7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgICAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgICAgICAgfQ0KICAgICAgICAuaXRlbV90aW1lIHsNCiAgICAgICAgICB3aWR0aDogMjAlOw0KICAgICAgICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICAgICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgICBmb250LWZhbWlseTogTWljcm9zb2Z0IFlhSGVpOw0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICAgICAgY29sb3I6ICM5OTk5OTk7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC5ldmVyeUl0ZW06bnRoLWNoaWxkKDEpIHsNCiAgICAgICAgbWFyZ2luLXRvcDogMDsNCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLnN1YnNjcmliZS1jYXJkIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICAgIHBhZGRpbmc6IDMwcHg7DQogICAgaGVpZ2h0OiA0MTBweDsNCiAgICBtYXJnaW4tdG9wOiAyMXB4Ow0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAuc3Vic2NyaWJlX2NvbnRlbnQgew0KICAgICAgbWFyZ2luLXRvcDogMjBweDsNCiAgICAgIC5ldmVyeUl0ZW0gew0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgbWFyZ2luLXRvcDogMzBweDsNCiAgICAgICAgLml0ZW1faW1nIHsNCiAgICAgICAgICB3aWR0aDogMTclOw0KICAgICAgICAgIGhlaWdodDogMTIwcHg7DQogICAgICAgICAgaW1nIHsNCiAgICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAuaXRlbV9kZXNjIHsNCiAgICAgICAgICBtYXJnaW4tbGVmdDogMjBweDsNCiAgICAgICAgICB3aWR0aDogNTglOw0KICAgICAgICAgIC50aXRsZSB7DQogICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICBmb250LWZhbWlseTogU291cmNlIEhhbiBTYW5zIENOOw0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgICAgICAgICAgbWFyZ2luLXRvcDogMTFweDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLml0ZW1fYW1vdW50cyB7DQogICAgICAgICAgd2lkdGg6IDIwJTsNCiAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC5ldmVyeUl0ZW06bnRoLWNoaWxkKDEpIHsNCiAgICAgICAgbWFyZ2luLXRvcDogMDsNCiAgICAgIH0NCiAgICAgIC5kcml2ZXIgew0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgaGVpZ2h0OiAxcHg7DQogICAgICAgIGJhY2tncm91bmQ6ICNmN2Y4Zjk7DQogICAgICAgIG1hcmdpbjogMzBweCAwOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICAub3JkZXJtYW5hZ2UtY2FyZCB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgICBwYWRkaW5nOiAzMHB4Ow0KICAgIGhlaWdodDogNDEwcHg7DQogICAgbWFyZ2luLXRvcDogMjFweDsNCiAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgLm9yZGVybWFuYWdlX2NvbnRlbnQgew0KICAgICAgLmV2ZXJ5SXRlbSB7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2Y3ZjhmOTsNCiAgICAgICAgcGFkZGluZzogMTBweCAwOw0KICAgICAgICAuaXRlbV9jb21wYW55IHsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgZm9udC1mYW1pbHk6IE1pY3Jvc29mdCBZYUhlaTsNCiAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgIGNvbG9yOiAjNjY2NjY2Ow0KICAgICAgICB9DQogICAgICAgIC5pdGVtX2NvbXBhbnk6bnRoLWNoaWxkKDEpIHsNCiAgICAgICAgICBtYXJnaW4tdG9wOiAwOw0KICAgICAgICB9DQogICAgICAgIC5pdGVtX2NvbnRlbnQgew0KICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgbWFyZ2luLXRvcDogMTBweDsNCiAgICAgICAgICAuaXRlbV9pbWcgew0KICAgICAgICAgICAgd2lkdGg6IDExJTsNCiAgICAgICAgICAgIGhlaWdodDogNzBweDsNCiAgICAgICAgICAgIGltZyB7DQogICAgICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIC5pdGVtX21pZGRsZSB7DQogICAgICAgICAgICB3aWR0aDogNzAlOw0KICAgICAgICAgICAgbWFyZ2luLWxlZnQ6IDIwcHg7DQogICAgICAgICAgICAudGl0bGUgew0KICAgICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICAgIGZvbnQtZmFtaWx5OiBTb3VyY2UgSGFuIFNhbnMgQ047DQogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgICAgICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgICAgICAgICAgICBtYXJnaW46IDEwcHggMDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIC5kZXNjIHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgICBmb250LWZhbWlseTogTWljcm9zb2Z0IFlhSGVpOw0KICAgICAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgICAgICBjb2xvcjogIzk5OTk5OTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgLml0ZW1fcmlnaHQgew0KICAgICAgICAgICAgd2lkdGg6IDMwJTsNCiAgICAgICAgICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICAgICAgICAgICAgLnBheVR5cGUgew0KICAgICAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgICAgIGZvbnQtZmFtaWx5OiBNaWNyb3NvZnQgWWFIZWk7DQogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICAgICAgICAgIGNvbG9yOiAjZTEwYzAyOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLmV2ZXJ5SXRlbTpudGgtY2hpbGQoMykgew0KICAgICAgICBib3JkZXItYm90dG9tOiBub25lOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICAuZGF0YVBhbmVsLWNhcmQgew0KICAgIGhlaWdodDogMjkwcHg7DQogICAgcGFkZGluZzogMzBweDsNCiAgICBtYXJnaW4tdG9wOiAyMXB4Ow0KICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgIC5kYXRhUGFuZWxfY29udGVudCB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAuZGF0YVBhbmVsX2xlZnQgew0KICAgICAgICB3aWR0aDogNDUlOw0KICAgICAgICAuZGF0YVBhbmVsSXRlbSB7DQogICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgaGVpZ2h0OiA1NnB4Ow0KICAgICAgICAgIGJhY2tncm91bmQ6IHJnYigyNDUsIDI1MiwgMjUwKTsNCiAgICAgICAgICBsaW5lLWhlaWdodDogNTZweDsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIG1hcmdpbi10b3A6IDE4cHg7DQogICAgICAgICAgLml0ZW1UeXBlIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgICAgIGZvbnQtZmFtaWx5OiBNaWNyb3NvZnQgWWFIZWk7DQogICAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICAgICAgICBtYXJnaW4tbGVmdDogNSU7DQogICAgICAgICAgfQ0KICAgICAgICAgIC50eXBlTnVtIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMzBweDsNCiAgICAgICAgICAgIGZvbnQtZmFtaWx5OiBTaW1IZWk7DQogICAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgICAgICAgICBtYXJnaW4tbGVmdDogOSU7DQogICAgICAgICAgfQ0KICAgICAgICAgIC5hZGRTdHlsZSB7DQogICAgICAgICAgICBtYXJnaW4tbGVmdDogOSU7DQogICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgd2lkdGg6IDQ1JTsNCiAgICAgICAgICAgIC5hZGRUZXh0IHsNCiAgICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgICAgICBmb250LWZhbWlseTogTWljcm9zb2Z0IFlhSGVpOw0KICAgICAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgICAgICBjb2xvcjogIzk5OTk5OTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC5kYXRhUGFuZWxfcmlnaHQgew0KICAgICAgICB3aWR0aDogNTUlOw0KICAgICAgICBwYWRkaW5nLWxlZnQ6IDEwcHg7DQogICAgICB9DQogICAgfQ0KICB9DQogIC5hcHBsaWNhdGlvbi1jYXJkIHsNCiAgICBoZWlnaHQ6IDI5MHB4Ow0KICAgIHBhZGRpbmc6IDMwcHg7DQogICAgbWFyZ2luLXRvcDogMjFweDsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmZmZmOw0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAuYXBwbGljYXRpb24tY29udGVudCB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZmxleC13cmFwOiB3cmFwOw0KICAgICAgbWFyZ2luLXRvcDogMjBweDsNCiAgICAgIC5ldmVyeUl0ZW0gew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICB3aWR0aDogMzIlOw0KICAgICAgICBtYXJnaW4tbGVmdDogMiU7DQogICAgICAgIC5pdGVtX2ltZyB7DQogICAgICAgICAgd2lkdGg6IDQ4JTsNCiAgICAgICAgICBoZWlnaHQ6IDk1cHg7DQogICAgICAgICAgaW1nIHsNCiAgICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAudGl0bGUgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgICBmb250LWZhbWlseTogU291cmNlIEhhbiBTYW5zIENOOw0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICAgICAgbWFyZ2luLWxlZnQ6IDklOw0KICAgICAgICAgIG1hcmdpbi10b3A6IDI3cHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC5ldmVyeUl0ZW06bnRoLWNoaWxkKG4gKyA0KSB7DQogICAgICAgIG1hcmdpbi10b3A6IDIwcHg7DQogICAgICB9DQogICAgICAuZXZlcnlJdGVtOm50aC1jaGlsZCgzbiArIDEpIHsNCiAgICAgICAgbWFyZ2luLWxlZnQ6IDA7DQogICAgICB9DQogICAgfQ0KICB9DQogIC52aWV3U3R5bGUgew0KICAgIHdpZHRoOiBjYWxjKDEwMCUgLSA5MHB4KTsNCiAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICBmb250LXNpemU6IDE2cHg7DQogICAgZm9udC1mYW1pbHk6IFNvdXJjZSBIYW4gU2FucyBDTjsNCiAgICBmb250LXdlaWdodDogNTAwOw0KICAgIGNvbG9yOiAjMjFjOWI4Ow0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgfQ0KICAuZGVtYW5kLWNhcmQgew0KICAgIG1hcmdpbi10b3A6IDE4cHg7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgICBoZWlnaHQ6IDM0MHB4Ow0KICAgIHotaW5kZXg6IC0xOw0KICAgIC5kZW1hbmQtaGVhZGVyIHsNCiAgICAgIGxpbmUtaGVpZ2h0OiA2MHB4Ow0KICAgICAgcGFkZGluZzogMCAyMHB4Ow0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGU4ZTg7DQoNCiAgICAgIC5oZWFkZXIgew0KICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLVNlbWlib2xkLCBQaW5nRmFuZyBTQzsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICB9DQogICAgICAubW9yZSB7DQogICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtUmVndWxhciwgUGluZ0ZhbmcgU0M7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICAgIGNvbG9yOiAjMjFjOWI4Ow0KICAgICAgfQ0KICAgIH0NCiAgICAuZGVtbWFuZC1jYXJvdXNlbCB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGhlaWdodDogMjYwcHg7DQogICAgICBwYWRkaW5nOiAxMnB4IDIwcHg7DQogICAgICAuZGVtYW5kLW1lc3NhZ2UtaXRlbSB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIHBhZGRpbmc6IDE2cHggMCAxNnB4IDA7DQogICAgICAgIC50aXRsZSB7DQogICAgICAgICAgd2lkdGg6IDY1JTsNCiAgICAgICAgICBoZWlnaHQ6IDUwcHg7DQogICAgICAgICAgbWFyZ2luLWxlZnQ6IDEycHg7DQogICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAyNnB4Ow0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgICAgICAtd2Via2l0LWxpbmUtY2xhbXA6IDI7DQogICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogICAgICAgICAgZGlzcGxheTogLXdlYmtpdC1ib3g7DQogICAgICAgICAgLXdlYmtpdC1ib3gtb3JpZW50OiB2ZXJ0aWNhbDsNCiAgICAgICAgfQ0KICAgICAgICAuc3RhdHVzLWNhcmQgew0KICAgICAgICAgIGJhY2tncm91bmQ6IHJnYmEoMjEsIDE4OCwgMTMyLCAwLjE1KTsNCiAgICAgICAgICBjb2xvcjogIzE1YmM4NDsNCiAgICAgICAgICBtYXJnaW46IGF1dG87DQogICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgICBwYWRkaW5nOiAwIDEycHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgICBoZWlnaHQ6IDMwcHg7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDMwcHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLnNtYWxsLWNhcmQgew0KICAgIG1hcmdpbi10b3A6IDE4cHg7DQogICAgaGVpZ2h0OiAzMDBweDsNCiAgICBwYWRkaW5nLWJvdHRvbTogMTJweDsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICAgIC5zbWFsbC1jYXJkLWhlYWRlciB7DQogICAgICBsaW5lLWhlaWdodDogNjBweDsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIHBhZGRpbmc6IDAgMjBweDsNCiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZThlOGU4Ow0KICAgICAgLmhlYWRlciB7DQogICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtU2VtaWJvbGQsIFBpbmdGYW5nIFNDOw0KICAgICAgICBmb250LXdlaWdodDogNjAwOw0KICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgIH0NCiAgICAgIC5tb3JlIHsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1SZWd1bGFyLCBQaW5nRmFuZyBTQzsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgICB9DQogICAgfQ0KICAgIC5tZXNzYWdlLWNhcm91c2VsIHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgaGVpZ2h0OiAyNjBweDsNCiAgICAgIC5tZXNzYWdlLWl0ZW0gew0KICAgICAgICBwYWRkaW5nOiAxOHB4IDAgMThweCAwOw0KICAgICAgICBtYXJnaW46IDRweCAyMHB4Ow0KICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U4ZThlODsNCiAgICAgICAgLnRpdGxlIHsNCiAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgICAgICBsaW5lLWhlaWdodDogMjRweDsNCiAgICAgICAgfQ0KICAgICAgICAuY29udGVudCB7DQogICAgICAgICAgbWFyZ2luLXRvcDogOHB4Ow0KICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgIGNvbG9yOiAjNjY2NjY2Ow0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAyM3B4Ow0KICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgICAgLXdlYmtpdC1saW5lLWNsYW1wOiAyOw0KICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICAgICAgICAgIGRpc3BsYXk6IC13ZWJraXQtYm94Ow0KICAgICAgICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogICAgLmFwcGx5LWNhcm91c2VsIHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgaGVpZ2h0OiAyMzBweDsNCiAgICAgIC5hcHBseS1pdGVtIHsNCiAgICAgICAgcGFkZGluZzogMThweCAwIDEwcHggMDsNCiAgICAgICAgbWFyZ2luOiAwIDIwcHg7DQogICAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZThlOGU4Ow0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAuaXRlbS1sZWZ0IHsNCiAgICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgICAudGl0bGUgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgICAgICAgICAgLy8gd2lkdGg6IDMwMHB4Ow0KICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDE2cHg7DQogICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICAgICAgLXdlYmtpdC1saW5lLWNsYW1wOiAxOw0KICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogICAgICAgICAgICBkaXNwbGF5OiAtd2Via2l0LWJveDsNCiAgICAgICAgICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7DQogICAgICAgICAgfQ0KICAgICAgICAgIC5jb250ZW50IHsNCiAgICAgICAgICAgIG1hcmdpbi10b3A6IDhweDsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICAgICAgICBjb2xvcjogIzY2NjY2NjsNCiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAyMHB4Ow0KICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgICAgICAgICAgIC13ZWJraXQtbGluZS1jbGFtcDogMTsNCiAgICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICAgICAgICAgICAgZGlzcGxheTogLXdlYmtpdC1ib3g7DQogICAgICAgICAgICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAuaXRlbS1yaWdodCB7DQogICAgICAgICAgcGFkZGluZzogMTBweCAwOw0KICAgICAgICAgIC5idXR0b24gew0KICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgICAgICAgd2lkdGg6IDcycHg7DQogICAgICAgICAgICBwYWRkaW5nOiA2cHggMDsNCiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMyMTRkYzU7DQogICAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgICAgY29sb3I6ICMyMTRkYzU7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMTJweDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogICAgLmNvbnZlcnNhdGlvbi1ib3ggew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgICBoZWlnaHQ6IDIzMHB4Ow0KICAgICAgLmNvbnZlcnNhaW9uLWl0ZW0gew0KICAgICAgICBwYWRkaW5nOiAxMnB4IDAgMTBweCAwOw0KICAgICAgICBtYXJnaW46IDAgMjBweDsNCiAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGU4ZTg7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICYtYXZhdGFyIHsNCiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDE2cHg7DQogICAgICAgICAgLmVsLWF2YXRhciB7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgJi1jZW50ZXIgew0KICAgICAgICAgIHdpZHRoOiAzMDBweDsNCiAgICAgICAgICAmLWNvbnRlbnQgew0KICAgICAgICAgICAgbWFyZ2luLXRvcDogMnB4Ow0KICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgICAgIGNvbG9yOiAjNjY2NjY2Ow0KICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDIwcHg7DQogICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICAgICAgLXdlYmtpdC1saW5lLWNsYW1wOiAxOw0KICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogICAgICAgICAgICBkaXNwbGF5OiAtd2Via2l0LWJveDsNCiAgICAgICAgICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgJi1yaWdodCB7DQogICAgICAgICAgcGFkZGluZzogMTBweCAwOw0KICAgICAgICAgIC5idXR0b24gew0KICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgICAgICAgd2lkdGg6IDcycHg7DQogICAgICAgICAgICBwYWRkaW5nOiA2cHggMDsNCiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMyMTRkYzU7DQogICAgICAgICAgICBmb250LXNpemU6IDEycHg7DQogICAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgICAgY29sb3I6ICMyMTRkYzU7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMTJweDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLm5vbmUtY2xhc3Mgew0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICBtYXJnaW46IDglIDA7DQogICAgLnRleHQgew0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgIGNvbG9yOiAjOTk5OTk5Ow0KICAgICAgbGluZS1oZWlnaHQ6IDE0cHg7DQogICAgfQ0KICB9DQogIC5jb2RlLWJveCB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgIC5jb2RlLWl0ZW0gew0KICAgICAgcGFkZGluZzogMTZweCAwOw0KICAgICAgLnRpdGxlIHsNCiAgICAgICAgbWFyZ2luLXRvcDogMTBweDsNCiAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDEycHg7DQogICAgICB9DQogICAgICAuaGludCB7DQogICAgICAgIG1hcmdpbi10b3A6IDEwcHg7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDZweDsNCiAgICAgICAgZm9udC1zaXplOiAxMHB4Ow0KICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICBjb2xvcjogIzk5OTk5OTsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDEwcHg7DQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index(copy).vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6wBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index(copy).vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <div class=\"user-info-card\">\r\n              <div style=\"display: flex; align-items: center\">\r\n                <el-image\r\n                  style=\"width: 18px; height: 18px\"\r\n                  :src=\"require('@/assets/user/infoIcon.png')\"\r\n                ></el-image>\r\n                <div style=\"margin-left: 10px\">我的信息</div>\r\n              </div>\r\n              <div style=\"display: flex; margin-top: 34px\">\r\n                <el-image\r\n                  style=\"width: 100px; height: 100px; border-radius: 50%\"\r\n                  :fit=\"fit\"\r\n                  :src=\"user.avatar\"\r\n                ></el-image>\r\n                <div class=\"left-info-box\">\r\n                  <div class=\"user-name\">{{ user.realName || \"--\" }}</div>\r\n                  <div class=\"tag-group\">\r\n                    <div\r\n                      v-if=\"this.companyStatus == '0'\"\r\n                      class=\"label-container\"\r\n                    >\r\n                      <el-image\r\n                        style=\"width: 12px; height: 12px\"\r\n                        :src=\"require('@/assets/user/authentication.png')\"\r\n                      ></el-image>\r\n                      <span>未认证</span>\r\n                    </div>\r\n                    <div v-else class=\"certification\">服务商认证</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"divider\"></div>\r\n                <div class=\"right-info-box\">\r\n                  <div class=\"phone-class\">\r\n                    <el-image\r\n                      class=\"iamge\"\r\n                      style=\"width: 14px; height: 20px; margin-left: 4px\"\r\n                      :src=\"require('@/assets/user/icon_phone.png')\"\r\n                    ></el-image>\r\n                    <div class=\"phone-number\">{{ user.phonenumber }}</div>\r\n                  </div>\r\n                  <div class=\"phone-class\">\r\n                    <el-image\r\n                      class=\"iamge\"\r\n                      style=\"width: 22px; height: 20px\"\r\n                      :src=\"require('@/assets/user/icon_company.png')\"\r\n                    ></el-image>\r\n                    <div class=\"phone-number\" v-if=\"user.bussinessNo\">\r\n                      {{ user.companyName }}\r\n                    </div>\r\n                    <div class=\"phone-number grey\" v-else>\r\n                      您还未关联企业，<a\r\n                        @click=\"jumpToApprove\"\r\n                        style=\"color: #21c9b8\"\r\n                        >请先关联</a\r\n                      >\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"notify-card\">\r\n              <div style=\"display: flex; align-items: center\">\r\n                <el-image\r\n                  style=\"width: 14px; height: 20px\"\r\n                  :src=\"require('@/assets/user/notifyIcon.png')\"\r\n                ></el-image>\r\n                <div style=\"margin-left: 10px\">消息通知</div>\r\n                <div class=\"viewStyle\" @click=\"viewMessage\">查看更多>></div>\r\n              </div>\r\n              <div class=\"driver\"></div>\r\n              <div class=\"notify_content\">\r\n                <div\r\n                  class=\"everyItem\"\r\n                  v-for=\"item in notifyList\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div class=\"item_icon\">\r\n                    <div class=\"icon_small\"></div>\r\n                  </div>\r\n                  <div class=\"desc\">\r\n                    {{ item.remark || item.describeInfo || \"--\" }}\r\n                  </div>\r\n                  <div class=\"item_time\">\r\n                    {{ item.createTime.slice(0, 10) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <div class=\"subscribe-card\">\r\n              <div style=\"display: flex; align-items: center\">\r\n                <el-image\r\n                  style=\"width: 14px; height: 20px\"\r\n                  :src=\"require('@/assets/user/notifyIcon.png')\"\r\n                ></el-image>\r\n                <div style=\"margin-left: 10px\">我的订阅</div>\r\n                <div class=\"viewStyle\" @click=\"viewSubscribe\">查看更多>></div>\r\n              </div>\r\n              <div class=\"subscribe_content\">\r\n                <div\r\n                  class=\"everyItem\"\r\n                  v-for=\"item in subscribeList\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div style=\"display: flex\">\r\n                    <div class=\"item_img\">\r\n                      <img :src=\"item.appLogo\" alt=\"\" />\r\n                      <!-- <img :src=\"require('@/assets/user/wait.png')\" alt=\"\" /> -->\r\n                    </div>\r\n                    <div class=\"item_desc\">\r\n                      <div class=\"title\">{{ item.remark }}</div>\r\n                      <div style=\"font-size: 14px; margin-top: 10px\">\r\n                        <span style=\"color: #999999\">规格:</span>\r\n                        <span style=\"margin-left: 5px\">{{\r\n                          item.specs == \"1\" ? \"基础版\" : \"高级版\"\r\n                        }}</span>\r\n                      </div>\r\n                      <div style=\"font-size: 14px; margin-top: 10px\">\r\n                        <span style=\"color: #999999\">可用时长:</span>\r\n                        <span style=\"margin-left: 5px\">{{\r\n                          item.validTime == \"1\" ? \"一年\" : \"永久\"\r\n                        }}</span>\r\n                      </div>\r\n                      <div style=\"font-size: 14px; margin-top: 10px\">\r\n                        <span style=\"color: #999999\">可用人数:</span>\r\n                        <span style=\"margin-left: 5px\">不限</span>\r\n                        <!-- <span style=\"margin-left: 5px\">{{ item.number }}</span> -->\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"item_amounts\">\r\n                      <div\r\n                        style=\"\r\n                          color: #999999;\r\n                          font-size: 14px;\r\n                          margin-top: 34px;\r\n                        \"\r\n                      >\r\n                        订单金额\r\n                      </div>\r\n                      <div\r\n                        style=\"\r\n                          margin-top: 21px;\r\n                          color: #e10c02;\r\n                          font-weight: 400;\r\n                        \"\r\n                      >\r\n                        ￥{{ item.price }}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"driver\" v-show=\"item.id == 1\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"ordermanage-card\">\r\n              <div style=\"display: flex; align-items: center\">\r\n                <el-image\r\n                  style=\"width: 16px; height: 19px\"\r\n                  :src=\"require('@/assets/user/order.png')\"\r\n                ></el-image>\r\n                <div style=\"margin-left: 10px\">订单管理</div>\r\n                <div class=\"viewStyle\">查看更多>></div>\r\n              </div>\r\n              <div class=\"ordermanage_content\">\r\n                <div\r\n                  class=\"everyItem\"\r\n                  v-for=\"item in ordermanageList\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div class=\"item_company\">\r\n                    {{ item.supply }}\r\n                  </div>\r\n                  <div class=\"item_content\">\r\n                    <div class=\"item_img\">\r\n                      <img :src=\"item.appLogo\" alt=\"\" />\r\n                      <!-- <img :src=\"require('@/assets/user/wait.png')\" alt=\"\" /> -->\r\n                    </div>\r\n                    <div class=\"item_middle\">\r\n                      <div class=\"title\">{{ item.appName }}</div>\r\n                      <!-- <div class=\"desc\">{{ item.desc }}</div> -->\r\n                    </div>\r\n                    <div class=\"item_right\">\r\n                      <div\r\n                        style=\"\r\n                          font-size: 14px;\r\n                          font-family: Microsoft YaHei;\r\n                          font-weight: 400;\r\n                          color: #666666;\r\n                          margin: 10px 0;\r\n                        \"\r\n                      >\r\n                        ￥{{ item.price }}\r\n                      </div>\r\n                      <div\r\n                        class=\"payType\"\r\n                        :style=\"\r\n                          item.status == '待支付'\r\n                            ? 'color:#E10C02'\r\n                            : item.status == '待发货'\r\n                            ? 'color:#FBAC14'\r\n                            : 'color:#19C582'\r\n                        \"\r\n                      >\r\n                        {{ getStatus(item.orderStatus) }}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <div class=\"dataPanel-card\">\r\n              <div style=\"display: flex; align-items: center\">\r\n                <el-image\r\n                  style=\"width: 18px; height: 20px\"\r\n                  :src=\"require('@/assets/user/dataPanel.png')\"\r\n                ></el-image>\r\n                <div style=\"margin-left: 10px\">数据面板</div>\r\n              </div>\r\n              <div class=\"dataPanel_content\">\r\n                <div class=\"dataPanel_left\">\r\n                  <div class=\"dataPanelItem\">\r\n                    <div class=\"itemType\">应用</div>\r\n                    <div class=\"typeNum\">{{ appOrderData.appNumber }}</div>\r\n                    <div class=\"addStyle\">\r\n                      <div class=\"addText\">今日新增</div>\r\n                      <div style=\"color: #f46768; margin-left: 10%\">\r\n                        {{ appOrderData.todayAppNumber }}\r\n                      </div>\r\n                      <div style=\"margin-left: 10%\">\r\n                        <img\r\n                          :src=\"require('@/assets/user/add.png')\"\r\n                          alt=\"\"\r\n                          style=\"width: 12px; height: 16px\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"dataPanelItem\">\r\n                    <div class=\"itemType\">订单</div>\r\n                    <div class=\"typeNum\">{{ appOrderData.orderNumber }}</div>\r\n                    <div class=\"addStyle\">\r\n                      <div class=\"addText\">今日新增</div>\r\n                      <div style=\"color: #f46768; margin-left: 10%\">\r\n                        {{ appOrderData.todayOrderNumber }}\r\n                      </div>\r\n                      <div style=\"margin-left: 10%\">\r\n                        <img\r\n                          :src=\"require('@/assets/user/add.png')\"\r\n                          alt=\"\"\r\n                          style=\"width: 12px; height: 16px\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"dataPanelItem\">\r\n                    <div class=\"itemType\">开票</div>\r\n                    <div class=\"typeNum\">{{ appOrderData.invoiceNumber }}</div>\r\n                    <div class=\"addStyle\">\r\n                      <div class=\"addText\">今日新增</div>\r\n                      <div style=\"color: #f46768; margin-left: 10%\">\r\n                        {{ appOrderData.todayInvoiceNumber }}\r\n                      </div>\r\n                      <div style=\"margin-left: 10%\">\r\n                        <img\r\n                          :src=\"require('@/assets/user/add.png')\"\r\n                          alt=\"\"\r\n                          style=\"width: 12px; height: 16px\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"dataPanel_right\">\r\n                  <div style=\"display: flex; margin-top: 18px\">\r\n                    <div>\r\n                      年份:\r\n                      <el-date-picker\r\n                        v-model=\"valueYear\"\r\n                        type=\"year\"\r\n                        placeholder=\"选择年\"\r\n                        style=\"width: 70%\"\r\n                        format=\"yyyy\"\r\n                        value-format=\"yyyy\"\r\n                        @change=\"getSubData\"\r\n                      >\r\n                      </el-date-picker>\r\n                    </div>\r\n                    <div>\r\n                      月份:\r\n                      <el-date-picker\r\n                        v-model=\"valueMonth\"\r\n                        type=\"month\"\r\n                        placeholder=\"选择月\"\r\n                        style=\"width: 70%\"\r\n                        format=\"MM\"\r\n                        value-format=\"MM\"\r\n                        @change=\"getSubData\"\r\n                        @focus=\"timeEditable\"\r\n                      >\r\n                      </el-date-picker>\r\n                    </div>\r\n                  </div>\r\n                  <div id=\"dataPanel\" style=\"width: 100%; height: 200px\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"application-card\">\r\n              <div style=\"display: flex; align-items: center\">\r\n                <el-image\r\n                  style=\"width: 16px; height: 19px\"\r\n                  :src=\"require('@/assets/user/application.png')\"\r\n                ></el-image>\r\n                <div style=\"margin-left: 10px\">应用管理</div>\r\n                <div class=\"viewStyle\">查看更多>></div>\r\n              </div>\r\n              <div class=\"application-content\">\r\n                <div\r\n                  class=\"everyItem\"\r\n                  v-for=\"item in applicationList\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div class=\"item_img\">\r\n                    <!-- <img :src=\"require('@/assets/user/wait.png')\" alt=\"\" /> -->\r\n                    <img :src=\"item.appLogo\" alt=\"\" />\r\n                  </div>\r\n                  <div class=\"title\">{{ item.appName }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <!-- <el-col :span=\"3\" :xs=\"24\">\r\n        <div class=\"code-box\">\r\n          <div class=\"code-item\">\r\n            <el-image\r\n              style=\"width: 100px; height: 100px\"\r\n              :src=\"require('@/assets/user/more.png')\"\r\n              :fit=\"fit\"\r\n            ></el-image>\r\n            <div class=\"title\">门户端</div>\r\n            <div class=\"hint\">政策服务/申报/数据掌握</div>\r\n          </div>\r\n          <div class=\"code-item\">\r\n            <el-image\r\n              style=\"width: 100px; height: 100px\"\r\n              :src=\"require('@/assets/user/company_mini.png')\"\r\n              :fit=\"fit\"\r\n            ></el-image>\r\n            <div class=\"title\">企业端-云端研发</div>\r\n            <div class=\"hint\">研发/采购/销售/政策/服务</div>\r\n          </div>\r\n          <div class=\"code-item\">\r\n            <el-image\r\n              style=\"width: 100px; height: 100px\"\r\n              :src=\"require('@/assets/user/resource_mini.png')\"\r\n              :fit=\"fit\"\r\n            ></el-image>\r\n            <div class=\"title\">资源端</div>\r\n            <div class=\"hint\">供应商/服务商/专家</div>\r\n          </div>\r\n          <div class=\"code-item\">\r\n            <el-image\r\n              style=\"width: 100px; height: 100px\"\r\n              :src=\"require('@/assets/user/gov_mini.png')\"\r\n              :fit=\"fit\"\r\n            ></el-image>\r\n            <div class=\"title\">政府端</div>\r\n            <div class=\"hint\">政策服务/申报/数据掌握</div>\r\n          </div>\r\n        </div>\r\n      </el-col> -->\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport store from \"@/store\";\r\nimport userAvatar from \"./userAvatar\";\r\nimport userInfo from \"./userInfo\";\r\nimport resetPwd from \"./resetPwd\";\r\n// import { listInfo } from \"@/api/system/info\";\r\nimport { getAbutmentList } from \"@/api/system/abutment\";\r\n\r\nimport {\r\n  getUserInfo,\r\n  checkAuthStatus,\r\n  sublist,\r\n  appOrderNum,\r\n  subStatistics,\r\n} from \"@/api/system/user\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport * as RongIMLib from \"@rongcloud/imlib-next\";\r\nimport { getUserIMToken } from \"@/api/system/user\";\r\nimport { getUserListByIds } from \"@/api/im.js\";\r\nimport * as echarts from \"echarts\";\r\nimport { listInfo } from \"@/api/system/info\";\r\nimport { appliList } from \"@/api/appliMarket\";\r\nimport { orderList } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"Profile\",\r\n  components: { userAvatar, userInfo, resetPwd, UserMenu },\r\n  data() {\r\n    return {\r\n      user: {\r\n        name: store.getters.name,\r\n        avatar: store.getters.avatar,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        id: store.getters.userId,\r\n      },\r\n      roleGroup: {},\r\n      userInfo: {},\r\n      postGroup: {},\r\n      fit: \"cover\",\r\n      responseList: [],\r\n      responseList: [],\r\n      abutmentList: [],\r\n      conversationList: [],\r\n      companyStatus: \"\",\r\n      activeTab: \"userinfo\",\r\n      url: \"https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg\",\r\n      notifyList: [],\r\n      subscribeList: [],\r\n      ordermanageList: [],\r\n      valueYear: \"\",\r\n      valueMonth: \"\",\r\n      applicationList: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      orderStatusList: [\r\n        {\r\n          dictValue: 1,\r\n          dictLabel: \"待支付\",\r\n        },\r\n        {\r\n          dictValue: 2,\r\n          dictLabel: \"待发货\",\r\n        },\r\n        {\r\n          dictValue: 3,\r\n          dictLabel: \"支付失败\",\r\n        },\r\n        {\r\n          dictValue: 4,\r\n          dictLabel: \"已发货\",\r\n        },\r\n        {\r\n          dictValue: 5,\r\n          dictLabel: \"已成交\",\r\n        },\r\n        {\r\n          dictValue: 6,\r\n          dictLabel: \"待续费\",\r\n        },\r\n        {\r\n          dictValue: 7,\r\n          dictLabel: \"已关闭\",\r\n        },\r\n        {\r\n          dictValue: 8,\r\n          dictLabel: \"支付中\",\r\n        },\r\n        {\r\n          dictValue: 9,\r\n          dictLabel: \"已取消\",\r\n        },\r\n      ],\r\n      appOrderData: {},\r\n      subStatisticsData: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getMessage();\r\n    this.getSublist();\r\n    this.getAppliList();\r\n    this.getOrderList();\r\n    this.getAppOrderNum();\r\n    this.getSubStatistics();\r\n    this.getUser();\r\n    // this.getResponseList();\r\n    // this.getAbutmentList();\r\n    // this.getImMessage();\r\n  },\r\n  // mounted() {\r\n  //   this.getInitEcharts();\r\n  // },\r\n  methods: {\r\n    getMessage() {\r\n      let data = {\r\n        pageNum: 1,\r\n        pageSize: 4,\r\n        type: 2,\r\n      };\r\n      listInfo(data).then((response) => {\r\n        this.notifyList = response.rows.slice(0, 4);\r\n        console.log(this.notifyList, \"----------\");\r\n      });\r\n    },\r\n    getSublist() {\r\n      sublist().then((res) => {\r\n        if (res.code === 200) {\r\n          this.subscribeList = res.rows.slice(0, 2);\r\n        }\r\n      });\r\n    },\r\n    getAppliList() {\r\n      let params = {\r\n        createBy: this.$store.state.user.userId,\r\n        pageNum: this.queryParams.pageNum,\r\n        pageSize: 6,\r\n      };\r\n      appliList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.applicationList = res.rows.slice(0, 6);\r\n        }\r\n      });\r\n    },\r\n    getOrderList() {\r\n      let params = {\r\n        createBy: this.$store.state.user.userId,\r\n        pageNum: this.queryParams.pageNum,\r\n        pageSize: 3,\r\n      };\r\n      orderList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.ordermanageList = res.rows;\r\n          console.log(this.ordermanageList, \"----------------\");\r\n        }\r\n      });\r\n    },\r\n    getAppOrderNum() {\r\n      appOrderNum().then((res) => {\r\n        if (res.code === 200) {\r\n          this.appOrderData = res.data;\r\n        }\r\n      });\r\n    },\r\n    getSubStatistics() {\r\n      let params = {\r\n        year: this.valueYear,\r\n        month: this.valueMonth,\r\n      };\r\n      console.log(params, \"------------\");\r\n      subStatistics(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.subStatisticsData = res.data;\r\n          this.getInitEcharts();\r\n          console.log(res, \"------------\");\r\n        }\r\n      });\r\n    },\r\n    getImMessage() {\r\n      if (this.user.id) {\r\n        getUserIMToken({ userId: this.user.id }).then((res) => {\r\n          console.log(res);\r\n          if (res.code === 200 && res.data.code === 200) {\r\n            window.token = res.data.token;\r\n            RongIMLib.connect(token).then((res) => {\r\n              console.info(\"连接结果打印1：\", res);\r\n              const startTime = 0;\r\n              const count = 10;\r\n              const order = 0;\r\n\r\n              RongIMLib.getConversationList({\r\n                count: count,\r\n                startTime: startTime,\r\n                order: order,\r\n              }).then((res) => {\r\n                if (res.code === 0) {\r\n                  this.conversationList = res.data;\r\n                  let ids = [];\r\n                  this.conversationList.forEach((item) => {\r\n                    ids.push(item.targetId);\r\n                  });\r\n                  this.getUserListByIds(ids);\r\n                } else {\r\n                  console.log(res.code, res.msg);\r\n                }\r\n              });\r\n            });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    getUserListByIds(ids) {\r\n      getUserListByIds(ids).then((res) => {\r\n        if (res.code == 200) {\r\n          let list = [];\r\n          this.conversationList.forEach((item) => {\r\n            let index = res.data.findIndex(\r\n              (element) => element.id == item.targetId\r\n            );\r\n            if (index != -1) {\r\n              this.conversationList[index].realName = res.data[index].realName;\r\n              this.conversationList[index].userPortrait =\r\n                res.data[index].userPortrait;\r\n              // list.push({ ...item, ...res.data[index] });\r\n            }\r\n          });\r\n          // this.conversationList = list;\r\n          this.conversationList = this.sliceArray(this.conversationList, 3);\r\n        }\r\n      });\r\n    },\r\n    getUser() {\r\n      getUserInfo(this.user.id).then((response) => {\r\n        this.user = response.data;\r\n        this.roleGroup = response.roleGroup;\r\n        this.postGroup = response.postGroup;\r\n      });\r\n      checkAuthStatus().then((response) => {\r\n        this.companyStatus = response.data.companyStatus;\r\n      });\r\n    },\r\n    goIM(info) {\r\n      let routeData;\r\n      if (info) {\r\n        routeData = this.$router.resolve({\r\n          path: \"/user/im\",\r\n          query: {\r\n            userId: info.targetId,\r\n          },\r\n        });\r\n      } else {\r\n        routeData = this.$router.resolve({\r\n          path: \"/user/im\",\r\n        });\r\n      }\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    getResponseList() {\r\n      listInfo({ pageNum: 1, pageSize: 10 }).then((response) => {\r\n        this.responseList = this.sliceArray(response.rows, 2);\r\n      });\r\n    },\r\n    getAbutmentList() {\r\n      getAbutmentList({ pageNum: 1, pageSize: 9 }).then((response) => {\r\n        this.abutmentList = this.sliceArray(response.rows, 3);\r\n      });\r\n    },\r\n    getApplyStatusName(key) {\r\n      switch (key) {\r\n        case 1:\r\n          return \"审核中\";\r\n        case 2:\r\n          return \"审核通过\";\r\n        default:\r\n        case 3:\r\n          return \"审核驳回\";\r\n      }\r\n    },\r\n    jumpToApprove() {\r\n      this.$router.push(`/user/userInfo?relevanceCompany=1`);\r\n    },\r\n    getUrl(str) {\r\n      if (!str) {\r\n        return \"\";\r\n      }\r\n\r\n      var list = JSON.parse(str);\r\n      if (list.length > 0) {\r\n        return list[0].url;\r\n      }\r\n    },\r\n\r\n    sliceArray(array, number) {\r\n      var result = [];\r\n      for (var x = 0; x < Math.ceil(array.length / number); x++) {\r\n        var start = x * number;\r\n        var end = start + number;\r\n        result.push(array.slice(start, end));\r\n      }\r\n      console.log(result);\r\n      return result;\r\n    },\r\n    jumpToDemand() {\r\n      this.$router.push(\"/user/companyDemand\");\r\n    },\r\n    jumpToApply() {\r\n      this.$router.push(\"/user/companyApply\");\r\n    },\r\n    jumpToMessage() {\r\n      this.$router.push(\"/user/notice\");\r\n    },\r\n    jumpToAbutment() {\r\n      this.$router.push(\"/user/abutmentRecord\");\r\n    },\r\n    getInitEcharts() {\r\n      // 基于准备好的dom，初始化echarts实例\r\n      var myChart = echarts.init(document.getElementById(\"dataPanel\"));\r\n      // 绘制图表\r\n      myChart.setOption({\r\n        tooltip: {\r\n          trigger: \"item\",\r\n        },\r\n        legend: {\r\n          orient: \"vertical\",\r\n          right: 0,\r\n          top: 40,\r\n          // top: \"5%\",\r\n          // left: \"center\",\r\n        },\r\n        series: [\r\n          {\r\n            type: \"pie\",\r\n            radius: [\"40%\", \"70%\"],\r\n            avoidLabelOverlap: false,\r\n            center: [\"30%\", \"50%\"],\r\n            label: {\r\n              show: false,\r\n              position: \"center\",\r\n            },\r\n            labelLine: {\r\n              show: false,\r\n            },\r\n            data: this.subStatisticsData,\r\n          },\r\n        ],\r\n      });\r\n    },\r\n    viewMessage() {\r\n      this.$router.push({\r\n        path: \"/user/notice\",\r\n      });\r\n    },\r\n    viewSubscribe() {\r\n      this.$router.push({\r\n        path: \"/user/mySubscriptions\",\r\n      });\r\n    },\r\n    getStatus(status) {\r\n      let orderStatus;\r\n      this.orderStatusList.forEach((item) => {\r\n        if (item.dictValue == status) {\r\n          orderStatus = item.dictLabel;\r\n        }\r\n      });\r\n      return orderStatus;\r\n    },\r\n    getSubData() {\r\n      this.getSubStatistics();\r\n    },\r\n    timeEditable() {\r\n      this.$nextTick(() => {\r\n        let els = document.querySelectorAll(\r\n          \".el-date-picker__header--bordered\"\r\n        );\r\n        for (var i = 0; i <= els.length - 1; i++) {\r\n          els[1].style.display = \"none\";\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .user-type-card {\r\n    background: rgba(33, 77, 197, 0.1);\r\n    margin-left: 20px;\r\n    margin-top: 14px;\r\n    color: #214dc5;\r\n    font-size: 12px;\r\n    border-radius: 4px;\r\n    font-weight: 400;\r\n    padding: 4px 12px 4px 12px;\r\n  }\r\n  .user-info-card {\r\n    background-color: #fff;\r\n    padding: 30px;\r\n    height: 240px;\r\n    border-radius: 4px;\r\n    .left-info-box {\r\n      width: 30%;\r\n      text-align: center;\r\n      .user-name {\r\n        font-size: 28px;\r\n        font-weight: 500;\r\n        // margin-left: 20px;\r\n        color: #333333;\r\n        line-height: 50px;\r\n      }\r\n      .tag-group {\r\n        display: flex;\r\n        justify-content: center;\r\n        .certification {\r\n          width: 110px;\r\n          height: 40px;\r\n          line-height: 40px;\r\n          background: rgb(229, 247, 243);\r\n          border-radius: 4px;\r\n          font-size: 16px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #21c9b8;\r\n        }\r\n        .label-container {\r\n          padding: 4px 6px;\r\n          margin-top: 6px;\r\n          margin-right: 6px;\r\n          height: 24px;\r\n          background: #f0f1f4;\r\n          border-radius: 4px;\r\n          font-size: 12px;\r\n          font-weight: 500;\r\n          color: #8a8c94;\r\n          line-height: 12px;\r\n          .el-image {\r\n            margin: 2px 4px 0 0;\r\n          }\r\n        }\r\n        .orange {\r\n          background: rgba(255, 191, 69, 0.2);\r\n          color: #ff8a27;\r\n        }\r\n        .red {\r\n          background: rgba(197, 37, 33, 0.1);\r\n          color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n    .divider {\r\n      width: 1px;\r\n      height: 80px;\r\n      margin: auto 0;\r\n      background: #e8e8e8;\r\n    }\r\n    .right-info-box {\r\n      width: 50%;\r\n      padding-left: 40px;\r\n      padding-top: 12px;\r\n      height: 100px;\r\n      .phone-class {\r\n        display: flex;\r\n        margin-top: 14px;\r\n      }\r\n      .phone-number {\r\n        margin-left: 12px;\r\n      }\r\n      .grey {\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n  .notify-card {\r\n    background-color: #fff;\r\n    padding: 30px;\r\n    height: 240px;\r\n    border-radius: 4px;\r\n    .driver {\r\n      width: 65%;\r\n      height: 1px;\r\n      background: #f7f8f9;\r\n      margin-top: 15px;\r\n    }\r\n    .notify_content {\r\n      margin-top: 15px;\r\n      .everyItem {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-top: 22px;\r\n        .item_icon {\r\n          width: 10px;\r\n          height: 10px;\r\n          border: 1px solid #21c9b8;\r\n          border-radius: 50%;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          .icon_small {\r\n            width: 4px;\r\n            height: 4px;\r\n            background: #21c9b8;\r\n            border-radius: 50%;\r\n          }\r\n        }\r\n        .desc {\r\n          width: 80%;\r\n          overflow: hidden;\r\n          font-size: 14px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          margin-left: 10px;\r\n        }\r\n        .item_time {\r\n          width: 20%;\r\n          text-align: right;\r\n          font-size: 14px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          color: #999999;\r\n        }\r\n      }\r\n      .everyItem:nth-child(1) {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n  }\r\n  .subscribe-card {\r\n    background-color: #fff;\r\n    padding: 30px;\r\n    height: 410px;\r\n    margin-top: 21px;\r\n    border-radius: 4px;\r\n    .subscribe_content {\r\n      margin-top: 20px;\r\n      .everyItem {\r\n        width: 100%;\r\n        margin-top: 30px;\r\n        .item_img {\r\n          width: 17%;\r\n          height: 120px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .item_desc {\r\n          margin-left: 20px;\r\n          width: 58%;\r\n          .title {\r\n            font-size: 16px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            margin-top: 11px;\r\n          }\r\n        }\r\n        .item_amounts {\r\n          width: 20%;\r\n          text-align: center;\r\n        }\r\n      }\r\n      .everyItem:nth-child(1) {\r\n        margin-top: 0;\r\n      }\r\n      .driver {\r\n        width: 100%;\r\n        height: 1px;\r\n        background: #f7f8f9;\r\n        margin: 30px 0;\r\n      }\r\n    }\r\n  }\r\n  .ordermanage-card {\r\n    background-color: #fff;\r\n    padding: 30px;\r\n    height: 410px;\r\n    margin-top: 21px;\r\n    border-radius: 4px;\r\n    .ordermanage_content {\r\n      .everyItem {\r\n        width: 100%;\r\n        border-bottom: 1px solid #f7f8f9;\r\n        padding: 10px 0;\r\n        .item_company {\r\n          font-size: 14px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          color: #666666;\r\n        }\r\n        .item_company:nth-child(1) {\r\n          margin-top: 0;\r\n        }\r\n        .item_content {\r\n          display: flex;\r\n          margin-top: 10px;\r\n          .item_img {\r\n            width: 11%;\r\n            height: 70px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          .item_middle {\r\n            width: 70%;\r\n            margin-left: 20px;\r\n            .title {\r\n              font-size: 16px;\r\n              font-family: Source Han Sans CN;\r\n              font-weight: 500;\r\n              color: #333333;\r\n              margin: 10px 0;\r\n            }\r\n            .desc {\r\n              font-size: 14px;\r\n              font-family: Microsoft YaHei;\r\n              font-weight: 400;\r\n              color: #999999;\r\n            }\r\n          }\r\n          .item_right {\r\n            width: 30%;\r\n            text-align: right;\r\n            .payType {\r\n              font-size: 14px;\r\n              font-family: Microsoft YaHei;\r\n              font-weight: 400;\r\n              color: #e10c02;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .everyItem:nth-child(3) {\r\n        border-bottom: none;\r\n      }\r\n    }\r\n  }\r\n  .dataPanel-card {\r\n    height: 290px;\r\n    padding: 30px;\r\n    margin-top: 21px;\r\n    background: #ffffff;\r\n    border-radius: 4px;\r\n    .dataPanel_content {\r\n      width: 100%;\r\n      display: flex;\r\n      .dataPanel_left {\r\n        width: 45%;\r\n        .dataPanelItem {\r\n          width: 100%;\r\n          height: 56px;\r\n          background: rgb(245, 252, 250);\r\n          line-height: 56px;\r\n          display: flex;\r\n          margin-top: 18px;\r\n          .itemType {\r\n            font-size: 16px;\r\n            font-family: Microsoft YaHei;\r\n            font-weight: 400;\r\n            color: #333333;\r\n            margin-left: 5%;\r\n          }\r\n          .typeNum {\r\n            font-size: 30px;\r\n            font-family: SimHei;\r\n            font-weight: 400;\r\n            color: #21c9b8;\r\n            margin-left: 9%;\r\n          }\r\n          .addStyle {\r\n            margin-left: 9%;\r\n            display: flex;\r\n            width: 45%;\r\n            .addText {\r\n              font-size: 16px;\r\n              font-family: Microsoft YaHei;\r\n              font-weight: 400;\r\n              color: #999999;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .dataPanel_right {\r\n        width: 55%;\r\n        padding-left: 10px;\r\n      }\r\n    }\r\n  }\r\n  .application-card {\r\n    height: 290px;\r\n    padding: 30px;\r\n    margin-top: 21px;\r\n    background: #ffffff;\r\n    border-radius: 4px;\r\n    .application-content {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      margin-top: 20px;\r\n      .everyItem {\r\n        display: flex;\r\n        width: 32%;\r\n        margin-left: 2%;\r\n        .item_img {\r\n          width: 48%;\r\n          height: 95px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .title {\r\n          font-size: 16px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          margin-left: 9%;\r\n          margin-top: 27px;\r\n        }\r\n      }\r\n      .everyItem:nth-child(n + 4) {\r\n        margin-top: 20px;\r\n      }\r\n      .everyItem:nth-child(3n + 1) {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n  .viewStyle {\r\n    width: calc(100% - 90px);\r\n    text-align: right;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    cursor: pointer;\r\n  }\r\n  .demand-card {\r\n    margin-top: 18px;\r\n    background-color: #fff;\r\n    height: 340px;\r\n    z-index: -1;\r\n    .demand-header {\r\n      line-height: 60px;\r\n      padding: 0 20px;\r\n      width: 100%;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      border-bottom: 1px solid #e8e8e8;\r\n\r\n      .header {\r\n        font-size: 18px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333333;\r\n      }\r\n      .more {\r\n        font-size: 14px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        font-weight: 400;\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    .demmand-carousel {\r\n      width: 100%;\r\n      height: 260px;\r\n      padding: 12px 20px;\r\n      .demand-message-item {\r\n        display: flex;\r\n        padding: 16px 0 16px 0;\r\n        .title {\r\n          width: 65%;\r\n          height: 50px;\r\n          margin-left: 12px;\r\n          font-size: 16px;\r\n          line-height: 26px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          overflow: hidden;\r\n          -webkit-line-clamp: 2;\r\n          text-overflow: ellipsis;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n        }\r\n        .status-card {\r\n          background: rgba(21, 188, 132, 0.15);\r\n          color: #15bc84;\r\n          margin: auto;\r\n          font-size: 12px;\r\n          border-radius: 4px;\r\n          padding: 0 12px;\r\n          font-weight: 400;\r\n          height: 30px;\r\n          line-height: 30px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .small-card {\r\n    margin-top: 18px;\r\n    height: 300px;\r\n    padding-bottom: 12px;\r\n    background-color: #fff;\r\n    .small-card-header {\r\n      line-height: 60px;\r\n      width: 100%;\r\n      display: flex;\r\n      padding: 0 20px;\r\n      justify-content: space-between;\r\n      border-bottom: 1px solid #e8e8e8;\r\n      .header {\r\n        font-size: 18px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333333;\r\n      }\r\n      .more {\r\n        font-size: 14px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        font-weight: 400;\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    .message-carousel {\r\n      width: 100%;\r\n      height: 260px;\r\n      .message-item {\r\n        padding: 18px 0 18px 0;\r\n        margin: 4px 20px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        .title {\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n        }\r\n        .content {\r\n          margin-top: 8px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #666666;\r\n          line-height: 23px;\r\n          overflow: hidden;\r\n          -webkit-line-clamp: 2;\r\n          text-overflow: ellipsis;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n        }\r\n      }\r\n    }\r\n    .apply-carousel {\r\n      width: 100%;\r\n      height: 230px;\r\n      .apply-item {\r\n        padding: 18px 0 10px 0;\r\n        margin: 0 20px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        display: flex;\r\n        .item-left {\r\n          width: 100%;\r\n          .title {\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            // width: 300px;\r\n            line-height: 16px;\r\n            overflow: hidden;\r\n            -webkit-line-clamp: 1;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n          }\r\n          .content {\r\n            margin-top: 8px;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #666666;\r\n            line-height: 20px;\r\n            overflow: hidden;\r\n            -webkit-line-clamp: 1;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n          }\r\n        }\r\n        .item-right {\r\n          padding: 10px 0;\r\n          .button {\r\n            text-align: center;\r\n            width: 72px;\r\n            padding: 6px 0;\r\n            border-radius: 4px;\r\n            border: 1px solid #214dc5;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #214dc5;\r\n            line-height: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .conversation-box {\r\n      width: 100%;\r\n      height: 230px;\r\n      .conversaion-item {\r\n        padding: 12px 0 10px 0;\r\n        margin: 0 20px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        display: flex;\r\n        &-avatar {\r\n          margin-right: 16px;\r\n          .el-avatar {\r\n            background: transparent;\r\n          }\r\n        }\r\n        &-center {\r\n          width: 300px;\r\n          &-content {\r\n            margin-top: 2px;\r\n            font-size: 14px;\r\n            font-weight: 400;\r\n            color: #666666;\r\n            line-height: 20px;\r\n            overflow: hidden;\r\n            -webkit-line-clamp: 1;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n          }\r\n        }\r\n\r\n        &-right {\r\n          padding: 10px 0;\r\n          .button {\r\n            text-align: center;\r\n            width: 72px;\r\n            padding: 6px 0;\r\n            border-radius: 4px;\r\n            border: 1px solid #214dc5;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #214dc5;\r\n            line-height: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    margin: 8% 0;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .code-box {\r\n    background-color: #fff;\r\n    border-radius: 8px;\r\n    text-align: center;\r\n    .code-item {\r\n      padding: 16px 0;\r\n      .title {\r\n        margin-top: 10px;\r\n        font-size: 12px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        line-height: 12px;\r\n      }\r\n      .hint {\r\n        margin-top: 10px;\r\n        margin-bottom: 6px;\r\n        font-size: 10px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}