{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\classicCase.vue?vue&type=style&index=0&id=4362fde1&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\classicCase.vue", "mtime": 1750311962928}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["classicCase.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "classicCase.vue", "sourceRoot": "src/views/components/home", "sourcesContent": ["<template>\r\n  <div\r\n    class=\"content wow animate__animated animate__fadeInUp\"\r\n    data-wow-duration=\"1s\"\r\n  >\r\n    <div class=\"img_right\">\r\n      <img src=\"../../../assets/images/home/<USER>\" alt=\"\" />\r\n    </div>\r\n    <div class=\"card-container\">\r\n      <div class=\"caseTitle\">\r\n        <div>典型案例</div>\r\n        <div class=\"allCase\" @click=\"goclassicCase\">查看全部>></div>\r\n      </div>\r\n      <div style=\"margin: 40px 0\">\r\n        <div style=\"display: flex; justify-content: center\">\r\n          <div\r\n            v-for=\"item in tabs\"\r\n            :key=\"item.dictValue\"\r\n            class=\"caseName\"\r\n            :class=\"activeName == item.dictValue ? 'caseNameHover' : ''\"\r\n            @click=\"getCaseType(item.dictValue)\"\r\n          >\r\n            {{ item.dictLabel }}\r\n          </div>\r\n        </div>\r\n        <!-- <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\" stretch>\r\n          <el-tab-pane\r\n            v-for=\"item in tabs\"\r\n            :key=\"item.dictValue\"\r\n            :label=\"item.dictLabel\"\r\n            :name=\"item.dictValue\"\r\n          ></el-tab-pane>\r\n        </el-tabs> -->\r\n      </div>\r\n      <div v-if=\"list && list.length > 0\">\r\n        <el-carousel\r\n          v-loading=\"loading\"\r\n          class=\"body\"\r\n          height=\"360px\"\r\n          :autoplay=\"false\"\r\n        >\r\n          <el-carousel-item\r\n            class=\"body-item\"\r\n            v-for=\"item in list\"\r\n            :key=\"item.id\"\r\n          >\r\n            <div class=\"card\" @click=\"goCaseDetail(item.id)\">\r\n              <el-image\r\n                v-if=\"item.coverPicUrl\"\r\n                class=\"card-img\"\r\n                :src=\"item.coverPicUrl\"\r\n                fit=\"fill\"\r\n              />\r\n              <div class=\"card-content\">\r\n                <div class=\"title\">{{ item.name }}</div>\r\n                <div class=\"desc\">\r\n                  <div>{{ item.introduction }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n      <div class=\"none-class\" v-else>\r\n        <el-image\r\n          style=\"width: 160px; height: 160px\"\r\n          :src=\"require('@/assets/user/none.png')\"\r\n          :fit=\"fit\"\r\n        ></el-image>\r\n        <div class=\"text\">暂无数据</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { caseList } from \"@/api/classicCase\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      activeName: \"0\",\r\n      loading: false,\r\n      tabs: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        industry: \"\",\r\n      },\r\n      list: [],\r\n    };\r\n  },\r\n  created() {\r\n    // this.initData();\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.$wow.init();\r\n    });\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      this.getCaseList();\r\n    },\r\n    goclassicCase() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/classicCase\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    initData() {\r\n      getDicts(\"case_industry\").then((res) => {\r\n        const { code, data = [] } = res;\r\n        if (code === 200) {\r\n          this.tabs = data;\r\n          this.tabs.unshift({\r\n            dictLabel: \"全部\",\r\n            dictValue: \"0\",\r\n          });\r\n          this.getCaseList();\r\n        }\r\n      });\r\n    },\r\n    getCaseList() {\r\n      this.loading = true;\r\n      this.queryParams.industry = this.activeName == \"0\" ? \"\" : this.activeName;\r\n      caseList(this.queryParams).then((response) => {\r\n        this.list = response.rows;\r\n        // this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    goCaseDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/caseDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    getCaseType(value) {\r\n      this.activeName = value;\r\n      this.queryParams.industry = this.activeName == \"0\" ? \"\" : this.activeName;\r\n      this.getCaseList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.caseTitle {\r\n  width: 100%;\r\n  font-size: 36px;\r\n  text-align: center;\r\n  margin-top: 84px;\r\n  position: relative;\r\n  // padding-top: 64px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  color: #000000;\r\n  .allCase {\r\n    position: absolute;\r\n    top: 50px;\r\n    right: 0;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    line-height: 26px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n::v-deep .el-tabs__item {\r\n  font-size: 16px;\r\n}\r\n::v-deep .el-tabs__nav-scroll {\r\n  width: 67%;\r\n  margin: 0 auto;\r\n}\r\n::v-deep .el-tabs__nav-wrap::after {\r\n  display: none;\r\n}\r\n.body {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 360px;\r\n  background: #ffffff;\r\n  box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.1);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  ::v-deep.el-carousel__arrow {\r\n    i {\r\n      color: #777777;\r\n    }\r\n    &:hover {\r\n      background-color: #21c9b8;\r\n      i {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n  ::v-deep.el-carousel__indicators--horizontal {\r\n    bottom: 30px;\r\n    .el-carousel__indicator {\r\n      .el-carousel__button {\r\n        width: 4px;\r\n        height: 4px;\r\n        background: #d8d8d8;\r\n        border-radius: 3px;\r\n      }\r\n      &.is-active {\r\n        .el-carousel__button {\r\n          width: 24px;\r\n          height: 4px;\r\n          background: #21c9b8;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &-item {\r\n    display: flex;\r\n    width: 100%;\r\n    flex-shrink: 0;\r\n    flex-direction: row;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n  .card {\r\n    display: flex;\r\n    flex-direction: row;\r\n    flex-shrink: 0;\r\n    height: 290px;\r\n    cursor: pointer;\r\n    &-img {\r\n      width: 406px;\r\n      height: 290px;\r\n    }\r\n    &-content {\r\n      width: 500px;\r\n      height: 100%;\r\n      padding-left: 50px;\r\n      padding-right: 50px;\r\n      padding-top: 50px;\r\n      background-image: url(\"../../../assets/images/home/<USER>\");\r\n      background-size: 100% 100%;\r\n      .title {\r\n        font-size: 22px;\r\n        font-weight: 500;\r\n        color: #ffffff;\r\n        line-height: 24px;\r\n        margin-bottom: 20px;\r\n        font-family: Source Han Sans CN;\r\n      }\r\n      .desc {\r\n        min-height: 78px;\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #ffffff;\r\n        line-height: 26px;\r\n        margin-bottom: 17px;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 6;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n      .info {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        margin-bottom: 32px;\r\n        &-tag {\r\n          background: rgba(197, 37, 33, 0.1);\r\n          border-radius: 4px;\r\n          padding: 10px 12px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #21c9b8;\r\n          line-height: 12px;\r\n          margin-right: 18px;\r\n        }\r\n        &-day {\r\n          display: flex;\r\n          flex-direction: row;\r\n          align-items: center;\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 18px;\r\n          .count {\r\n            font-size: 24px;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            line-height: 24px;\r\n            padding: 0 8px;\r\n          }\r\n        }\r\n      }\r\n      .view-btn {\r\n        // @include flexCenter;\r\n        width: 120px;\r\n        height: 40px;\r\n        background: #21c9b8;\r\n        border-radius: 4px;\r\n        font-size: 16px;\r\n        font-weight: 500;\r\n        color: #ffffff;\r\n        line-height: 16px;\r\n      }\r\n    }\r\n  }\r\n}\r\n.none-class {\r\n  text-align: center;\r\n  padding: 8% 0;\r\n  .text {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #999999;\r\n    line-height: 14px;\r\n  }\r\n}\r\n.content {\r\n  width: 100%;\r\n  // height: 730px;\r\n  // background: url(\"../../../assets/images/home/<USER>\") no-repeat;\r\n  // background-size: 100% 100%;\r\n  // animation: fadeInUp; /* referring directly to the animation's @keyframe declaration */\r\n  // animation-duration: 2s; /* don't forget to set a duration! */\r\n  position: relative;\r\n  .img_right {\r\n    position: absolute;\r\n    top: 110px;\r\n    right: 0;\r\n    width: calc((100% - 1200px) / 2);\r\n    height: 450px;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n}\r\n.caseName {\r\n  width: 110px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  margin-left: 15px;\r\n  text-align: center;\r\n  line-height: 40px;\r\n  cursor: pointer;\r\n}\r\n.caseNameHover {\r\n  background: #21c9b8;\r\n  color: #ffffff;\r\n}\r\n.caseName:nth-child(1) {\r\n  margin-left: 0;\r\n}\r\n</style>\r\n"]}]}