import request from "@/utils/request";

// 应用市场-类型
export function appliType() {
  return request({
    url: "/system/dict/data/type/xipin_app_category",
    method: "get",
  });
}

// 应用市场-列表
export function appliList(params) {
  return request({
    url: "/uuc/store/list",
    method: "get",
    params,
  });
}

// 应用市场-详情
export function appliDetail(params) {
  return request({
    url: "/uuc/store/get",
    method: "get",
    params,
  });
}

// 应用-下单
export function appliOrder(data) {
  return request({
    url: "/uuc/scm/order",
    method: "post",
    data,
  });
}

// 应用-收藏
export function appliCollect(data) {
  return request({
    url: "/uuc/store/subscribe",
    method: "post",
    data,
  });
}

// 应用-取消收藏
export function appliCancelCollect(data) {
  return request({
    url: "/uuc/store/unsubscribe",
    method: "post",
    data,
  });
}

// 收藏列表
export function collectList(data) {
  return request({
    url: "/uuc/store/subscribelist",
    method: "get",
    data,
  });
}

// 应用-下单支付
export function orderPayment(data) {
  return request({
    url: "/uuc/weixin/pay/native",
    method: "post",
    data,
  });
}

// 应用-发布新增应用
export function appliAdd(data) {
  return request({
    url: "/uuc/store",
    method: "post",
    data,
  });
}

// 应用-编辑应用
export function appliEdit(data) {
  return request({
    url: "/uuc/store",
    method: "put",
    data,
  });
}

// 应用-删除
export function delAppli(id) {
  return request({
    url: `/uuc/store/${id}`,
    method: "delete",
  });
}

// 应用-订单状态个数
export function orderStatusNum() {
  return request({
    url: "/uuc/scm/order/selectAppOrderStatusNum",
    method: "get",
  });
}

// 应用-上下架
export function appliGroundOff(data) {
  return request({
    url: "/uuc/store/updateAppState",
    method: "post",
    data,
  });
}
