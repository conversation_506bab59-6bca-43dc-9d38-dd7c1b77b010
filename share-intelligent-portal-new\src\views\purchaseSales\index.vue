<!--
 * @Author: jhy
 * @Date: 2023-02-01 08:50:01
 * @LastEditors: JHY
 * @LastEditTime: 2023-12-11 14:05:18
-->
<template>
  <div class="purchase-sales-container">
    <!-- banner图 -->
    <div class="purchase-banner">
      <img src="../../assets/purchaseSales/purchaseSalesBanner.png" alt="" />
    </div>
    <!-- 互采互销 -->
    <div class="purchase-sales-content">
      <div class="purchase-sales-title-box">
        <div class="purchase-sales-divider"></div>
        <div class="purchase-sales-title">互采互销</div>
        <div class="purchase-sales-divider"></div>
      </div>
      <div class="purchase-sales-box">
        <div class="purchase-sales-search-content">
          <div class="purchase-sales-search-top">
            <el-button
              :class="{
                'purchase-sale-top-btn': !showDemand,
                'purchase-sale-top-active-btn': showDemand,
              }"
              @click="searchContent('1')"
              >找需求<img
                v-if="showDemand"
                src="../../assets/purchaseSales/arrow.png"
                alt=""
                class="search-top-img"
            /></el-button>
            <el-button
              type="text"
              class="purchase-sale-bottom-btn"
              @click="viewMoreDemand"
              >查看全部需求 >>
            </el-button>
            <el-button
              :class="{
                'purchase-sale-top-btn': !showResources,
                'purchase-sale-top-active-btn': showResources,
              }"
              @click="searchContent('2')"
              >找资源<img
                v-if="showResources"
                src="../../assets/purchaseSales/arrow.png"
                alt=""
                class="search-top-img"
            /></el-button>
            <el-button
              type="text"
              class="purchase-sale-bottom-btn"
              @click="viewMoreSupply"
              >查看全部资源 >>
            </el-button>
          </div>
        </div>
        <div class="purchase-sales-info-content">
          <div
            class="scene-search-box"
            @click="searchContent('2')"
            v-if="showResources"
          >
            <el-form ref="form" class="scene-search-form" :model="name">
              <el-form-item>
                <el-input
                  v-model="name"
                  placeholder="请输入搜索内容"
                  class="scene-search-input"
                >
                  <el-button
                    slot="append"
                    class="scene-search-btn"
                    @click="search"
                    >找资源</el-button
                  >
                </el-input>
              </el-form-item>
            </el-form>
          </div>
          <div
            class="scene-search-box"
            @click="searchContent('1')"
            v-if="showDemand"
          >
            <el-form ref="form" class="scene-search-form" :model="name">
              <el-form-item>
                <el-input
                  v-model="name"
                  placeholder="请输入搜索内容"
                  class="scene-search-input"
                >
                  <el-button
                    slot="append"
                    class="scene-search-btn"
                    @click="search"
                    >找需求</el-button
                  >
                </el-input>
              </el-form-item>
            </el-form>
          </div>
          <el-tabs
            v-show="showDemand"
            v-model="demandType"
            class="purchase-sales-info-tab"
            @tab-click="tabDemand"
          >
            <el-tab-pane name="1">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/innovation.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">创新研发</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="2">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img src="../../assets/purchaseSales/material.png" alt="" />
                  </div>
                  <div class="item-title">物料采购</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="3">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/intelligence.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">智能制造</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="4">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/digitization.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">数字化管理</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="5">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img src="../../assets/purchaseSales/software.png" alt="" />
                  </div>
                  <div class="item-title">软件服务</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="6">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/supplyChain.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">供应链金融</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="7">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/operationPublicity.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">运营宣传</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="8">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img src="../../assets/purchaseSales/other.png" alt="" />
                  </div>
                  <div class="item-title">其他</div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
          <el-tabs
            v-show="showResources"
            v-model="technologyType"
            class="purchase-sales-info-tab"
            @tab-click="tabTechnology"
          >
            <el-tab-pane name="国产化替代">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/domesticSubstitution.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">国产化替代</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="机器替人">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/robotSubstitutes.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">机器替人</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="管理提升">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/managementPromotion.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">管理提升</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="质量提升">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/qualityImprovement.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">质量提升</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="灭菌消杀">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/sterilization.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">灭菌消杀</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="供应链金融">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/supplyChain.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">供应链金融</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="新材料">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/newMaterial.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">新材料</div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane name="绿色星碳">
              <div slot="label">
                <div class="info-type-item">
                  <div class="item-img">
                    <img
                      src="../../assets/purchaseSales/greenDoubleCarbon.png"
                      alt=""
                    />
                  </div>
                  <div class="item-title">绿色星碳</div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
          <div v-loading="purchaseLoading" class="purchase-sales-info-box">
            <div
              v-for="(item, index) in data"
              :key="index"
              class="purchase-sales-item"
              @click="goMoreDetail(item.id)"
            >
              <div v-if="showDemand" class="item-img">
                <img
                  v-if="item.scenePicture && item.scenePicture.length > 0"
                  :src="item.scenePicture[0].url"
                  alt=""
                />
                <img
                  v-else
                  src="../../assets/purchaseSales/demandDefault.png"
                  alt=""
                />
              </div>
              <div v-else class="item-img">
                <img
                  v-if="item.productPhoto && item.productPhoto.length > 0"
                  :src="item.productPhoto[0].url"
                  alt=""
                />
                <img
                  v-else
                  src="../../assets/purchaseSales/resourceDefault.png"
                  alt=""
                />
              </div>
              <div class="item-content">
                <div class="item-content-title">
                  <span v-if="showDemand">{{ item.demandTitle }}</span>
                  <span v-else-if="showResources">{{ item.supplyName }}</span>
                </div>
                <div class="item-content-detail">
                  <span>{{ item.companyName }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="tab-page-end">
        <!-- <span class="demonstration">完整功能</span> -->
        <el-pagination
          class="company-tab-pagination"
          @current-change="handleCurrentChange"
          :current-page="pageNum"
          :page-size="pageSize"
          layout=" prev, pager, next "
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <!-- 推荐企业 -->
    <div class="recommend-company">
      <div class="purchase-sales-title-box purchase-sales-title-style">
        <div class="purchase-sales-divider"></div>
        <div class="purchase-sales-title">推荐企业</div>
        <div class="purchase-sales-divider"></div>
      </div>
      <div class="recommend-company-content">
        <div class="recommend-company-more">
          <el-button type="text" @click="goMoreCompany">查看全部>></el-button>
        </div>
        <div class="recommend-company-carousel">
          <el-carousel
            class="recommend-company-carousel-content"
            :interval="5000"
          >
            <el-carousel-item
              v-for="(item, index) in companyData"
              :key="index"
              class="item-company-box"
            >
              <div
                v-for="(val, number) in item"
                :key="number"
                class="item-company-content"
              >
                <div class="item-company-detail">
                  <div class="item-company-img">
                    <img
                      v-if="
                        val.companyPictureList &&
                        val.companyPictureList.length > 0
                      "
                      :src="val.companyPictureList[0].url"
                      alt=""
                    />
                    <img
                      v-else
                      src="../../assets/purchaseSales/companyDefault.png"
                      alt=""
                    />
                  </div>
                  <div class="item-company-info">
                    <div class="item-company-title">{{ val.name }}</div>
                    <div class="item-company-intro">{{ val.introduce }}</div>
                    <div class="item-company-btn">
                      <el-button type="text" @click="viewCompanyDetail(val)"
                        >查看详情
                        <img
                          src="../../assets/purchaseSales/arrow.png"
                          alt=""
                          class="item-company-img"
                        />
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </div>
    <!-- 专家智库 -->
    <div class="expert-library-container">
      <div class="expert-library">
        <div class="purchase-sales-title-box purchase-sales-title-style">
          <div class="purchase-sales-divider"></div>
          <div class="purchase-sales-title">专家智库</div>
          <div class="purchase-sales-divider"></div>
        </div>
        <div class="recommend-company-more">
          <el-button type="text" @click="goExpertLibrary">查看全部>></el-button>
        </div>
        <div class="expert-library-content">
          <div
            v-for="(item, index) in expertData"
            :key="index"
            class="expert-library-item"
            @click="goExpertLibraryDetail(item.id)"
          >
            <div class="expert-library-list">
              <div class="expert-library-title">
                <div class="title-name">{{ item.expertName }}</div>
              </div>
              <div class="expert-library-label">
                <div
                  v-for="(val, index1) in item.techniqueTypeName"
                  :key="index1"
                  class="library-label-item"
                >
                  <span v-if="index1 < 2" class="library-label-type">
                    {{ `#${val}` }}
                  </span>
                  <span v-else>…</span>
                </div>
              </div>
              <div class="expert-library-box">
                {{ item.synopsis }}
              </div>
            </div>
            <div class="expert-library-avatar">
              <img v-if="item.headPortrait" :src="item.headPortrait" alt="" />
              <img
                v-else
                src="../../assets/expertLibrary/defaultImg.png"
                alt=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 活动广场 -->
    <div class="activity-square">
      <div class="purchase-sales-title-box purchase-sales-title-style">
        <div class="purchase-sales-divider"></div>
        <div class="purchase-sales-title">活动广场</div>
        <div class="purchase-sales-divider"></div>
      </div>
      <div class="recommend-company-more activity-more">
        <el-button type="text" @click="goActivitySquare">查看全部>></el-button>
      </div>
      <div class="activity-square-content">
        <div
          v-for="(item, index) in activityData"
          :key="index"
          class="activity-square-item"
          @click="goActivityDetail(item.id)"
        >
          <div class="activity-square-img">
            <img
              v-if="item.activityPicture"
              :src="item.activityPicture"
              alt=""
            />
          </div>
          <div class="activity-square-box">
            <div class="activity-square-item-title">
              {{ item.activityName }}
            </div>
            <div class="activity-square-item-info">
              {{ item.activityOverview }}
            </div>
            <div class="activity-square-item-time">
              {{ item.createTimeStr }}
            </div>
          </div>
          <div class="activity-square-label">{{ item.activityTypeName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  gatewayDemandListShow,
  gatewaySupplyListShow,
  getCompanyListLb,
  getExpertListFour,
  getActivityList,
} from "@/api/purchaseSales";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      showDemand: true, //找需求按钮箭头是否展示
      showResources: false, //找资源按钮箭头是否展示
      demandBtn: false,
      purchaseLoading: false,
      data: [],
      companyData: [], //推荐企业列表
      expertData: [], //专家智库列表
      activityData: [], //活动广场列表
      demandType: "1",
      technologyType: "国产化替代",
      pageNum: 1,
      pageSize: 6,
      total: 0,
      name: "",
    };
  },
  created() {
    this.getDemandList();
    this.getCompanyListLb();
    this.getExpertListFour();
    this.getActivityList();
  },
  methods: {
    // 找需求、找资源按钮显示样式
    searchContent(flag) {
      if (flag === "1") {
        this.showDemand = true;
        this.showResources = false;
        this.demandType = "1";
        this.getDemandList();
      } else {
        this.showDemand = false;
        this.showResources = true;
        this.technologyType = "国产化替代";
        this.getSupplyList();
      }
    },
    // 跳转到商机需求列表
    viewMoreDemand() {
      let routeData = this.$router.resolve({
        path: "/demandHall",
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到资源大厅
    viewMoreSupply() {
      let routeData = this.$router.resolve({
        path: "/resourceHall",
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到需求详情/资源页面
    goMoreDetail(id) {
      let path = "";
      this.showDemand
        ? (path = "/demandHallDetail")
        : (path = "/resourceHallDetail");
      let routeData = this.$router.resolve({
        path: path,
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    // 找需求
    getDemandList() {
      this.purchaseLoading = true;
      gatewayDemandListShow({
        demandType: this.demandType,
        city: "青岛市",
        region: "城阳区",
        displayStatus: 1,
        auditStatus: 2,
        pageNum: this.pageNum,
        // pageSize: this.pageSize,
        name: this.name,
      })
        .then((res) => {
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
          this.purchaseLoading = false;
          let { rows } = res || [];
          this.data = rows;
          this.total = res.total;
          this.data.forEach((item) => {
            if (item.scenePicture) {
              item.scenePicture = JSON.parse(item.scenePicture);
            }
          });
        })
        .catch(() => {
          this.purchaseLoading = false;
          this.data = [];
        });
    },
    // 找资源
    getSupplyList() {
      // this.purchaseLoading = true;
      gatewaySupplyListShow({
        technologyType: this.technologyType,
        city: "青岛市",
        region: "城阳区",
        auditStatus: 2,
        displayStatus: 1,
        pageNum: 1,
        // pageSize: 6,
        name: this.name,
      })
        .then((res) => {
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
          this.total = res.total;
          this.purchaseLoading = false;
          let { rows } = res || [];
          this.data = rows;
          this.data.forEach((item) => {
            item.productPhoto = JSON.parse(item.productPhoto);
          });
        })
        .catch(() => {
          // this.purchaseLoading = false;
          this.data = [];
        });
    },
    // 找需求tab切换
    tabDemand() {
      this.getDemandList();
    },
    // 找资源tab切换
    tabTechnology() {
      this.getSupplyList();
    },
    // 推荐企业列表
    getCompanyListLb() {
      getCompanyListLb({ recommendStatus: 1 }).then((res) => {
        let key = CryptoJS.enc.Utf8.parse(secretKey);
        let decrypt = CryptoJS.AES.decrypt(res, key, {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7,
        });
        res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
        let data = res.rows || [];
        this.companyData = [];
        for (var i = 0; i < data.length; i += 3) {
          this.companyData.push(data.slice(i, i + 3));
        }
      });
    },
    // 跳转到企业名录页面
    goMoreCompany() {
      let routeData = this.$router.resolve({
        path: "/enterpriseList",
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到企业详情页面
    viewCompanyDetail(val) {
      let routeData = this.$router.resolve({
        path: "/enterpriseDetail",
        query: { id: val.id, businessNo: val.businessNo },
      });
      window.open(routeData.href, "_blank");
    },
    // 专家智库列表
    getExpertListFour() {
      getExpertListFour().then((res) => {
        let key = CryptoJS.enc.Utf8.parse(secretKey);
        let decrypt = CryptoJS.AES.decrypt(res, key, {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7,
        });
        res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
        let { rows } = res || [];
        this.expertData = rows.slice(0, 4);
        this.expertData.forEach((item) => {
          item.techniqueTypeName = item.techniqueTypeName.split(",");
        });
      });
    },
    // 活动广场
    getActivityList() {
      getActivityList().then((res) => {
        let { rows } = res || [];
        this.activityData = rows.slice(0, 6);
      });
    },
    // 跳转到专家智库页面
    goExpertLibrary() {
      let routeData = this.$router.resolve({
        path: "/expertLibrary",
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到专家详情页面
    goExpertLibraryDetail(id) {
      let routeData = this.$router.resolve({
        path: "/expertDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到活动广场
    goActivitySquare() {
      let routeData = this.$router.resolve({
        path: "/activitySquare",
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到最新活动页面
    goActivityDetail(id) {
      let routeData = this.$router.resolve({
        path: "/activityDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },

    handleCurrentChange(pageNum) {
      // console.log(`当前页: ${val}`);
      this.pageNum = pageNum;
      this.getDemandList();
    },
  },
};
</script>

<style lang="scss" scoped>
// 分页
.block {
  width: 600px;
  margin: 0 auto;
}

.purchase-sales-container {
  width: 100%;
  background: #fff;
  .purchase-banner {
    width: 100%;
    height: 50vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .purchase-sales-content {
    background: #f4f5f9;
    .purchase-sales-box {
      width: 1216px;
      margin: 0 auto;
      display: flex;
      padding-bottom: 60px;
      .purchase-sales-search-content {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 40px 24px 40px 0;
        .purchase-sales-search-top {
          display: flex;
          flex-direction: column;
          .purchase-sale-top-btn {
            width: 172px;
            height: 60px;
            border-radius: 30px;
            border: 1px solid #21c9b8;
            font-size: 20px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #21c9b8;
            line-height: 20px;
            margin-left: 0;
            background: transparent;
          }
          .purchase-sale-top-active-btn {
            width: 172px;
            height: 60px;
            border-radius: 30px;
            border: 1px solid #21c9b8;
            font-size: 20px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #fff;
            line-height: 20px;
            margin-left: 0;
            background: #21c9b8;
          }
          .search-top-img {
            width: 20px;
            height: 20px;
            margin-left: 12px;
            margin-bottom: -3px;
          }
        }

        .purchase-sale-bottom-btn {
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #21c9b8;
          line-height: 18px;
          margin-left: 0;
          margin-bottom: 60px;

          .search-bottom-img {
            width: 18px;
            height: 18px;
            margin-bottom: -3px;
          }
        }
      }
      .purchase-sales-info-content {
        flex: 1;
        width: 996px;
        background: #fff;
        .purchase-sales-info-tab {
          padding: 24px 53px 1px 61px;
        }
        .info-type-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 80px;
          text-align: center;
          .item-img {
            width: 60px;
            height: 79px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .item-title {
            font-size: 16px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333;
            line-height: 16px;
            padding-top: 16px;
          }
          &:hover {
            .item-title {
              color: #21c9b8;
            }
          }
        }
        .purchase-sales-info-box {
          display: flex;
          flex-wrap: wrap;
          padding: 0 24px 24px;
          min-height: 628px;
          .purchase-sales-item {
            width: 300px;
            height: 278px;
            background: #f8f9fb;
            border-radius: 6px;
            margin: 24px 24px 0 0;
            cursor: pointer;
            .item-img {
              width: 100%;
              height: 160px;
              img {
                width: 100%;
                height: 100%;
              }
            }
            .item-content {
              padding: 16px 22px 24px 23px;
              font-family: PingFangSC-Regular, PingFang SC;
              .item-content-title {
                width: 255px;
                height: 52px;
                font-size: 18px;
                font-weight: 500;
                color: #333;
                line-height: 26px;
                overflow: hidden;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                text-overflow: ellipsis;
                margin-bottom: 12px;
                word-wrap: break-word;
              }
              .item-content-detail {
                width: 255px;
                font-weight: 400;
                color: #666;
                line-height: 14px;
                text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
                white-space: nowrap; /*让文字不换行*/
                overflow: hidden; /*超出要隐藏*/
                word-wrap: break-word;
              }
            }
          }
        }
      }
    }
  }
  .recommend-company {
    height: 881px;
    background-image: url("../../assets/purchaseSales/recommendCompanyBanner.png");
    background-size: 100% 881px;
    .recommend-company-content {
      padding: 0 4.79vw;
      .recommend-company-carousel {
        margin-top: 32px;
        .item-company-box {
          display: flex;
          .item-company-content {
            margin-right: 2vw;
            .item-company-detail {
              position: relative;
              .item-company-img {
                width: 28.75vw;
                height: 350px;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .item-company-info {
                position: absolute;
                top: 160px;
                left: 1.7vw;
                width: 25.3vw;
                height: 280px;
                background: #fff;
                box-shadow: 0px 10px 40px 0px #264a741a;
                font-family: PingFangSC-Regular, PingFang SC;
                .item-company-title {
                  max-width: 25.2vw;
                  height: 24px;
                  margin: 32px 32px 24px;
                  font-size: 24px;
                  font-weight: 500;
                  color: #333;
                  line-height: 24px;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  overflow: hidden;
                  word-wrap: break-word;
                }
                .item-company-intro {
                  width: 25.2vw;
                  height: 96px;
                  font-size: 16px;
                  color: #666;
                  line-height: 32px;
                  padding: 0 32px 24px;
                  overflow: hidden;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 3;
                  text-overflow: ellipsis;
                  word-wrap: break-word;
                }
                .item-company-btn {
                  margin: 24px 1.66vw 0 0;
                  text-align: right;
                  .el-button {
                    width: 8.95vw;
                    height: 48px;
                    background: #21c9b8;
                    border-radius: 30px;
                    font-size: 16px;
                    font-family: PingFangSC-Medium, PingFang SC;
                    font-weight: 500;
                    color: #fff;
                    line-height: 16px;
                    .item-company-img {
                      width: 20px;
                      height: 20px;
                      margin-left: 8px;
                      margin-bottom: -5px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .expert-library-container {
    background: #f4f5f9;
    .expert-library {
      width: 1200px;
      margin: 0 auto;
      padding-bottom: 100px;
      .expert-library-content {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .expert-library-item {
          display: flex;
          justify-content: space-between;
          width: 578px;
          background: #fff;
          margin-top: 34px;
          padding: 28px 32px;
          min-height: 224px;
          .expert-library-list {
            flex: 1;
            .expert-library-title {
              display: flex;
              justify-content: space-between;
              align-items: center;
              .title-name {
                width: 270px;
                font-size: 32px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #333;
                line-height: 32px;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                word-wrap: break-word;
              }
            }
          }
          .expert-library-label {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 16px;
            .library-label-item {
              max-width: 350px;
              padding: 6px 12px;
              background: #f4f5f9;
              border-radius: 4px;
              font-size: 12px;
              font-family: PingFangSC-Regular, PingFang SC;
              color: #666;
              line-height: 12px;
              margin: 24px 16px 0 0;
              .library-label-type {
                word-wrap: break-word;
              }
            }
          }
          .expert-library-box {
            width: 370px;
            font-size: 16px;
            font-family: PingFangSC-Regular, PingFang SC;
            color: #666;
            line-height: 32px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            word-wrap: break-word;
          }
          .expert-library-avatar {
            width: 120px;
            height: 168px;
            margin-left: 24px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          &:hover {
            cursor: pointer;
          }
        }
      }
    }
  }
  .activity-square {
    width: 1200px;
    margin: 0 auto;
    padding-bottom: 100px;
    .activity-more {
      margin-bottom: 32px;
    }
    .activity-square-content {
      display: flex;
      flex-wrap: wrap;
      .activity-square-item {
        position: relative;
        width: 382px;
        background: #fff;
        margin: 24px 14px 0 0;
        box-shadow: 0px 10px 40px 0px #264a741a;
        .activity-square-img {
          width: 100%;
          height: 160px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .activity-square-box {
          padding: 16px 32px 24px 24px;
          font-family: PingFangSC-Regular, PingFang SC;
          .activity-square-item-title {
            width: 326px;
            height: 52px;
            font-size: 18px;
            font-weight: 500;
            color: #333;
            line-height: 26px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            word-wrap: break-word;
          }
          .activity-square-item-info {
            width: 334px;
            height: 44px;
            color: #666;
            line-height: 22px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            margin: 10px 0;
            word-wrap: break-word;
          }
          .activity-square-item-time {
            color: #666;
            line-height: 22px;
          }
        }
        .activity-square-label {
          position: absolute;
          top: 0;
          left: 0;
          padding: 0 4px;
          max-width: 382px;
          max-height: 160px;
          background: rgba(0, 0, 0, 0.5);
          font-family: PingFangSC-Regular, PingFang SC;
          color: #fff;
          line-height: 26px;
          text-align: center;
          word-wrap: break-word;
        }
        &:hover {
          cursor: pointer;
        }
      }
    }
  }
  .purchase-sales-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0;
    .purchase-sales-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }
    .purchase-sales-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }
  .purchase-sales-title-style {
    padding-top: 80px;
  }
  .recommend-company-more {
    text-align: right;
    .el-button {
      font-size: 18px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #21c9b8;
      line-height: 18px;
      padding: 0;
    }
  }
}
</style>

<style lang="scss">
.scene-search-box {
  margin-top: 60px;
  .scene-search-form {
    text-align: center;
    .scene-search-input {
      width: 892px;
      height: 54px;
      .scene-search-btn {
        width: 100px;
      }
    }
  }
}
.purchase-sales-container {
  .purchase-sales-info-tab {
    .el-tabs__nav {
      height: 114px;
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
    .el-tabs__nav-wrap::after {
      background-color: transparent;
    }
    .el-tabs__active-bar {
      background-color: transparent;
    }
    .el-tabs__item.is-active {
      .item-title {
        color: #21c9b8 !important;
      }
    }
  }
  .recommend-company {
    .recommend-company-carousel-content {
      height: 546px;
      .el-carousel__container {
        height: 100%;
      }
      .el-carousel__indicator {
        cursor: pointer;
        &.is-active {
          .el-carousel__button {
            background: #21c9b8;
          }
        }
      }
      .el-carousel__button {
        width: 3.125vw;
        height: 6px;
        background: #c1c1c180;
        & + .el-carousel__button {
          margin-left: 16px;
        }
      }
    }
  }
  .tab-page-end {
    .company-tab-pagination {
      width: 200px;
      margin: 0 auto;
      padding-bottom: 20px;
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #ffffff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        margin: 0 6px;
        color: #333;
      }
      .el-pager {
        .number {
          width: 32px;
          height: 32px;
          border: 1px solid #d9d9d9;
          background: #ffffff;
          border-radius: 4px;
          line-height: 32px;
          margin: 0 6px;
          &.active {
            background: #21c9b8;
            border: 1px solid #21c9b8;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.scene-search-input {
  .el-input__inner {
    height: 54px;
    background: #fff;
    border-radius: 27px 0 0 27px;
    border: 1px solid #d9d9d9;
    font-size: 16px;
    line-height: 24px;
    padding-left: 30px;
  }
  .el-input-group__append {
    border-radius: 0px 100px 100px 0px;
    background: #21c9b8;
    font-size: 16px;
    color: #fff;
    line-height: 24px;
  }
}
.scene-search-line {
  .el-form-item__label {
    width: 88px;
    font-weight: 500;
    color: #999;
    padding-right: 32px;
    text-align: left;
  }
  .scene-search-radio {
    width: 1050px;
    margin-top: 11px;
    .el-radio-button {
      padding-bottom: 20px;
      .el-radio-button__inner {
        border: none;
        padding: 0 32px 0 0;
        background: none;
        &:hover {
          color: #21c9b8;
        }
      }
      &.is-active {
        .el-radio-button__inner {
          color: #21c9b8;
          background: none;
        }
      }
      .el-radio-button__orig-radio:checked {
        & + .el-radio-button__inner {
          box-shadow: unset;
        }
      }
    }
  }
}
.scene-page-end {
  .scene-pagination {
    .btn-prev,
    .btn-next,
    .btn-quickprev {
      width: 32px;
      height: 32px;
      background: #fff;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      color: #333;
    }
    &.is-background {
      .el-pager {
        .number {
          width: 32px;
          height: 32px;
          border: 1px solid #d9d9d9;
          background: #fff;
          border-radius: 4px;
          line-height: 32px;
          &.active {
            background: #21c9b8;
            border: 1px solid #21c9b8;
          }
        }
      }
    }
  }
}
</style>
