<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.EcologyMapper">
    
    <resultMap id="EcologyResult" type="com.ruoyi.portalconsole.domain.vo.EcologyVO">
        <result property="ecologyId"    column="ecology_id"    />
        <result property="ecologyCategoryId"    column="ecology_category_id"    />
        <result property="companyId"    column="company_id"    />
        <result property="ecologyIdeas"    column="ecology_ideas"    />
        <result property="ecologyContact"    column="ecology_contact"    />
        <result property="ecologyPhone"    column="ecology_phone"    />
        <result property="companyName"    column="company_name"    />
        <result property="ecologyCategoryName"    column="ecology_category_name"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectEcologyVo">
        select ecology_id, ecology_category.ecology_category_id as ecology_category_id, company.company_id as company_id, ecology_ideas, ecology_contact, ecology_phone, ecology.create_by, ecology.create_time, ecology.update_by, ecology.update_time, ecology.remark, company.company_name as company_name, ecology_category.ecology_category_name as ecology_category_name from ecology
         left join  company on company.company_id = ecology.company_id left join ecology_category on ecology_category.ecology_category_id = ecology.ecology_category_id
    </sql>

    <select id="selectEcologyList" parameterType="Ecology" resultMap="EcologyResult">
        <include refid="selectEcologyVo"/>

        <where>  
            <if test="ecologyCategoryId != null "> and ecology_category_id = #{ecologyCategoryId}</if>
            <if test="companyId != null  and companyId != ''"> and company_id = #{companyId}</if>
            <if test="ecologyIdeas != null  and ecologyIdeas != ''"> and ecology_ideas = #{ecologyIdeas}</if>
            <if test="ecologyContact != null  and ecologyContact != ''"> and ecology_contact = #{ecologyContact}</if>
            <if test="ecologyPhone != null  and ecologyPhone != ''"> and ecology_phone = #{ecologyPhone}</if>
        </where>
    </select>
    
    <select id="selectEcologyByEcologyId" parameterType="Long" resultMap="EcologyResult">
        <include refid="selectEcologyVo"/>
        where ecology_id = #{ecologyId}
    </select>
        
    <insert id="insertEcology" parameterType="Ecology" useGeneratedKeys="true" keyProperty="ecologyId">
        insert into ecology
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ecologyCategoryId != null">ecology_category_id,</if>
            <if test="companyId != null">company_id,</if>
            <if test="ecologyIdeas != null">ecology_ideas,</if>
            <if test="ecologyContact != null">ecology_contact,</if>
            <if test="ecologyPhone != null">ecology_phone,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ecologyCategoryId != null">#{ecologyCategoryId},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="ecologyIdeas != null">#{ecologyIdeas},</if>
            <if test="ecologyContact != null">#{ecologyContact},</if>
            <if test="ecologyPhone != null">#{ecologyPhone},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateEcology" parameterType="Ecology">
        update ecology
        <trim prefix="SET" suffixOverrides=",">
            <if test="ecologyCategoryId != null">ecology_category_id = #{ecologyCategoryId},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="ecologyIdeas != null">ecology_ideas = #{ecologyIdeas},</if>
            <if test="ecologyContact != null">ecology_contact = #{ecologyContact},</if>
            <if test="ecologyPhone != null">ecology_phone = #{ecologyPhone},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where ecology_id = #{ecologyId}
    </update>

    <delete id="deleteEcologyByEcologyId" parameterType="Long">
        delete from ecology where ecology_id = #{ecologyId}
    </delete>

    <delete id="deleteEcologyByEcologyIds" parameterType="String">
        delete from ecology where ecology_id in 
        <foreach item="ecologyId" collection="array" open="(" separator="," close=")">
            #{ecologyId}
        </foreach>
    </delete>
</mapper>