{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\scene\\detail.vue?vue&type=style&index=1&id=6ae34364&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\scene\\detail.vue", "mtime": 1750311963002}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnNjZW5lLWRldGFpbC1jb250YWluZXIgew0KICAuc2NlbmUtaW5mby1jb250ZW50IHsNCiAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICAgIGxpbmUtaGVpZ2h0OiAyOHB4Ow0KICAgIGNvbG9yOiAjMzMzOw0KICAgIGltZyB7DQogICAgICBtYXgtd2lkdGg6IDEwMCU7DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/scene", "sourcesContent": ["<template>\r\n  <div class=\"scene-detail-container\">\r\n    <div class=\"scene-detail-banner\">\r\n      <img\r\n        src=\"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/167601983615484.webp\"\r\n        alt=\"场景详情\"\r\n      />\r\n    </div>\r\n    <div class=\"scene-detail-title-box\">\r\n      <div class=\"scene-divider\"></div>\r\n      <div class=\"scene-detail-title\">场景详情</div>\r\n      <div class=\"scene-divider\"></div>\r\n    </div>\r\n    <div class=\"scene-detail-content\">\r\n      <template v-if=\"data.id\">\r\n        <div class=\"scene-detail-box\">\r\n          <div class=\"scene-info-title\">\r\n            {{ data.title }}\r\n          </div>\r\n          <div class=\"scene-info-time\">{{ data.updateTime }}</div>\r\n          <div class=\"scene-info-divider\"></div>\r\n          <div class=\"scene-info-box\">\r\n            <div\r\n              v-html=\"data.content\"\r\n              class=\"scene-info-content ql-editor\"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <template v-else>\r\n        <el-empty />\r\n      </template>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getInfoDetail } from \"@/api/scene\";\r\n\r\nexport default {\r\n  name: \"sceneDetailPage\",\r\n  data() {\r\n    return {\r\n      data: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      let id = this.$route.query.id;\r\n      this.loading = true;\r\n      getInfoDetail({ id: id })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.scene-detail-container {\r\n  width: 100%;\r\n  padding: 0 0 100px;\r\n  background: #f4f5f9;\r\n  .scene-detail-banner {\r\n    width: 100%;\r\n    height: 280px;\r\n    img {\r\n      width: 100%;\r\n      height: 280px;\r\n      object-fit: fill;\r\n    }\r\n  }\r\n  .scene-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .scene-detail-title {\r\n      font-size: 40px;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .scene-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .scene-detail-content {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n    .scene-detail-box {\r\n      padding: 60px 116px 100px;\r\n      .scene-info-title {\r\n        width: 960px;\r\n        font-size: 32px;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n        white-space: nowrap; /*让文字不换行*/\r\n        overflow: hidden; /*超出要隐藏*/\r\n      }\r\n      .scene-info-time {\r\n        font-size: 12px;\r\n        color: #999;\r\n        line-height: 12px;\r\n        padding-top: 40px;\r\n      }\r\n      .scene-info-divider {\r\n        width: 100%;\r\n        height: 1px;\r\n        background: #e8e8e8;\r\n        margin-top: 10px;\r\n      }\r\n      .scene-info-box {\r\n        padding-top: 40px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.scene-detail-container {\r\n  .scene-info-content {\r\n    word-break: break-all;\r\n    font-size: 16px;\r\n    line-height: 28px;\r\n    color: #333;\r\n    img {\r\n      max-width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}