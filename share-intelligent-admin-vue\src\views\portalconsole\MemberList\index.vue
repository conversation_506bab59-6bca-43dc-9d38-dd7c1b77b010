<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="手机号" prop="memberPhone">
        <el-input
          v-model="queryParams.memberPhone"
          placeholder="请输入手机号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="密码" prop="memberPassword">
        <el-input
          v-model="queryParams.memberPassword"
          placeholder="请输入密码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最后登录时间" prop="lastLoginTime">
        <el-date-picker clearable
          v-model="queryParams.lastLoginTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择最后登录时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="真实姓名" prop="memberRealName">
        <el-input
          v-model="queryParams.memberRealName"
          placeholder="请输入真实姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="微信号" prop="memberWechat">
        <el-input
          v-model="queryParams.memberWechat"
          placeholder="请输入微信号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="方案类型ID" prop="solutionTypeId">
        <el-input
          v-model="queryParams.solutionTypeId"
          placeholder="请输入方案类型ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="职位" prop="memberPost">
        <el-select v-model="queryParams.memberPost" placeholder="请选择职位">
            <el-option v-for="dict in dict.type.member_post" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
      </el-form-item>
      <el-form-item label="所属公司名称" prop="memberCompanyName">
        <el-input
          v-model="queryParams.memberCompanyName"
          placeholder="请输入所属公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="所属公司省市区编码" prop="memberCompanyArea">
        <el-input
          v-model="queryParams.memberCompanyArea"
          placeholder="请输入所属公司省市区编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="所属公司地址" prop="memberCompanyAddr">
        <el-input
          v-model="queryParams.memberCompanyAddr"
          placeholder="请输入所属公司地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="认证公司id" prop="memberCompanyId">
        <el-input
          v-model="queryParams.memberCompanyId"
          placeholder="请输入认证公司id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:Member:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:Member:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:Member:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:Member:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="MemberList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="会员ID" align="center" prop="memberId" />
      <el-table-column label="手机号" align="center" prop="memberPhone" />
      <el-table-column label="密码" align="center" prop="memberPassword" />
      <el-table-column label="最后登录时间" align="center" prop="lastLoginTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.lastLoginTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="真实姓名" align="center" prop="memberRealName" />
      <el-table-column label="微信号" align="center" prop="memberWechat" />
      <!-- <el-table-column label="方案类型ID" align="center" prop="solutionTypeId" /> -->
      <el-table-column label="职位" align="center" prop="" >
        <template slot-scope="scope">
          <div v-if="scope.row.memberPost==='1'">董事</div>
          <div v-else-if="scope.row.memberPost==='2'">经理</div>
          <div v-else-if="scope.row.memberPost==='3'">人事</div>
        </template>
      </el-table-column>
      <el-table-column label="所属公司名称" align="center" prop="memberCompanyName" />
      <!-- <el-table-column label="所属公司省市区编码" align="center" prop="memberCompanyArea" /> -->
      <el-table-column label="所属公司地址" align="center" prop="memberCompanyAddr" />
      <el-table-column label="会员状态" align="center" prop="">
        <template slot-scope="scope">
          <div v-if="scope.row.memberStatus==='0'">正常</div>
          <div v-else-if="scope.row.memberStatus==='1'">停用</div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="认证公司id" align="center" prop="memberCompanyId" /> -->
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:Member:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:Member:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改会员对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-form-item label="手机号" prop="memberPhone">
          <el-input v-model="form.memberPhone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="密码" prop="memberPassword">
          <el-input v-model="form.memberPassword" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="最后登录时间" prop="lastLoginTime">
          <el-date-picker clearable
            v-model="form.lastLoginTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择最后登录时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="真实姓名" prop="memberRealName">
          <el-input v-model="form.memberRealName" placeholder="请输入真实姓名" />
        </el-form-item>
        <el-form-item label="微信号" prop="memberWechat">
          <el-input v-model="form.memberWechat" placeholder="请输入微信号" />
        </el-form-item>
        <!-- <el-form-item label="方案类型ID" prop="solutionTypeId">
          <el-input v-model="form.solutionTypeId" placeholder="请输入方案类型ID" />
        </el-form-item> -->
        <el-form-item label="职位" prop="memberPost">
          <el-select v-model="form.memberPost" placeholder="请选择职位">
            <el-option v-for="dict in dict.type.member_post" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属公司名称" prop="memberCompanyName">
          <el-input v-model="form.memberCompanyName" placeholder="请输入所属公司名称" />
        </el-form-item>
        <el-form-item label="所属公司省市区编码" prop="memberCompanyArea">
          <el-input v-model="form.memberCompanyArea" placeholder="请输入所属公司省市区编码" />
        </el-form-item>
        <el-form-item label="所属公司地址" prop="memberCompanyAddr">
          <el-input v-model="form.memberCompanyAddr" placeholder="请输入所属公司地址" />
        </el-form-item>
        <el-form-item label="会员状态" prop="">
          <el-select v-model="form.memberStatus" placeholder="请选择会员状态">
            <el-option v-for="dict in dict.type.member_status" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        
        <!-- <el-form-item label="认证公司id" prop="memberCompanyId">
          <el-input v-model="form.memberCompanyId" placeholder="请输入认证公司id" />
        </el-form-item> -->
        <!-- <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item> -->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMember, getMember, delMember, addMember, updateMember } from "@/api/portalconsole/Member";

export default {
  name: "MemberList",
  dicts: ['member_post','member_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 会员表格数据
      MemberList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        memberPhone: null,
        memberPassword: null,
        lastLoginTime: null,
        memberRealName: null,
        memberWechat: null,
        solutionTypeId: null,
        memberPost: null,
        memberCompanyName: null,
        memberCompanyArea: null,
        memberCompanyAddr: null,
        memberStatus: null,
        memberCompanyId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        solutionTypeId: [
          { required: true, message: "方案类型ID不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询会员列表 */
    getList() {
      this.loading = true;
      listMember(this.queryParams).then(response => {
        this.MemberList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        memberId: null,
        memberPhone: null,
        memberPassword: null,
        lastLoginTime: null,
        memberRealName: null,
        memberWechat: null,
        solutionTypeId: null,
        memberPost: null,
        memberCompanyName: null,
        memberCompanyArea: null,
        memberCompanyAddr: null,
        memberStatus: null,
        memberCompanyId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.memberId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加会员";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const memberId = row.memberId || this.ids
      getMember(memberId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改会员";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.memberId != null) {
            updateMember(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMember(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const memberIds = row.memberId || this.ids;
      this.$modal.confirm('是否确认删除会员编号为"' + memberIds + '"的数据项？').then(function() {
        return delMember(memberIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/Member/export', {
        ...this.queryParams
      }, `Member_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
