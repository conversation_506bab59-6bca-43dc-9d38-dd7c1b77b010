{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\activitySquare\\detail.vue?vue&type=style&index=0&id=43fb02f4&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\activitySquare\\detail.vue", "mtime": 1750311962983}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/purchaseSales/component/activitySquare", "sourcesContent": ["<template>\r\n  <div class=\"activity-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"activity-detail-banner\">\r\n      <img\r\n        src=\"../../../../assets/activitySquare/activityDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"activity-detail-title-box\">\r\n      <div class=\"activity-detail-divider\"></div>\r\n      <div class=\"activity-detail-title\">活动详情</div>\r\n      <div class=\"activity-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"activity-detail-card\">\r\n      <div class=\"activity-detail-content\">\r\n        <div class=\"activity-detail-headline\">\r\n          <div class=\"headline-title\">\r\n            {{ data.activityName }}\r\n          </div>\r\n          <div class=\"headline-time\">\r\n            {{ getTime() }}\r\n          </div>\r\n        </div>\r\n        <!-- 活动详情标题 -->\r\n        <div class=\"activity-detail-caption\" v-if=\"data.activityContent\">\r\n          <div class=\"caption-line\"></div>\r\n          <div class=\"caption-title\">活动详情</div>\r\n        </div>\r\n        <!-- 活动详情 -->\r\n        <div class=\"activity-detail-img\">\r\n          <img v-if=\"data.activityContent\" :src=\"data.activityContent\" alt=\"\" />\r\n        </div>\r\n        <!-- 相关专家标题 -->\r\n        <div\r\n          class=\"activity-detail-caption\"\r\n          v-if=\"data.expertList && data.expertList.length > 0\"\r\n        >\r\n          <div class=\"caption-line\"></div>\r\n          <div class=\"caption-title\">相关专家</div>\r\n        </div>\r\n        <!-- 相关专家 -->\r\n        <div class=\"activity-detai-list\">\r\n          <div\r\n            v-for=\"(item, index) in data.expertList\"\r\n            :key=\"index\"\r\n            class=\"list-item-content\"\r\n            @click=\"goExpertLibraryDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-box\">\r\n              <div class=\"item-headline\">\r\n                <div class=\"item-title\">\r\n                  {{ item.expertName }}\r\n                </div>\r\n              </div>\r\n              <div class=\"activity-detai-label\">\r\n                <div\r\n                  v-for=\"(val, index1) in item.techniqueTypeName\"\r\n                  :key=\"index1\"\r\n                  class=\"activity-label-item\"\r\n                >\r\n                  <span v-if=\"index1 < 2\" class=\"activity-label-type\">{{\r\n                    `#${val}`\r\n                  }}</span>\r\n                  <span v-else>…</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"activity-detai-box\">\r\n                {{ item.synopsis }}\r\n              </div>\r\n            </div>\r\n            <div class=\"list-item-img\">\r\n              <img v-if=\"item.headPortrait\" :src=\"item.headPortrait\" alt=\"\" />\r\n              <img\r\n                v-else\r\n                src=\"../../../../assets/expertLibrary/defaultImg.png\"\r\n                alt=\"\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 相关需求标题 -->\r\n        <div\r\n          class=\"activity-detail-caption\"\r\n          v-if=\"data.demandList && data.demandList.length > 0\"\r\n        >\r\n          <div class=\"caption-line\"></div>\r\n          <div class=\"caption-title\">相关需求</div>\r\n        </div>\r\n        <!-- 相关需求内容 -->\r\n        <div class=\"activity-demand-info\">\r\n          <div\r\n            v-for=\"(item, index) in data.demandList\"\r\n            :key=\"index\"\r\n            class=\"activity-demand-item\"\r\n            @click=\"goDemandDetail(item.id)\"\r\n          >\r\n            <div class=\"activity-item-img\">\r\n              <img\r\n                v-if=\"item.scenePicture && item.scenePicture.length > 0\"\r\n                :src=\"item.scenePicture[0].url\"\r\n                alt=\"\"\r\n              />\r\n              <img\r\n                v-else\r\n                src=\"../../../../assets/purchaseSales/demandDefault.png\"\r\n                alt=\"\"\r\n              />\r\n            </div>\r\n            <div class=\"activity-item-content\">\r\n              <div class=\"activity-item-title\">\r\n                {{ item.demandTitle }}\r\n              </div>\r\n              <div class=\"activity-item-content-tag\">\r\n                <div\r\n                  v-for=\"(val, num) in item.applicationArea\"\r\n                  :key=\"num\"\r\n                  class=\"activity-item-tag\"\r\n                >\r\n                  {{ val }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 相关供给标题 -->\r\n        <div\r\n          class=\"activity-detail-caption related-supply-title\"\r\n          v-if=\"data.supplyList && data.supplyList.length > 0\"\r\n        >\r\n          <div class=\"caption-line\"></div>\r\n          <div class=\"caption-title\">相关供给</div>\r\n        </div>\r\n        <!-- 相关供给内容 -->\r\n        <div class=\"activity-demand-info\">\r\n          <div\r\n            v-for=\"(item, index) in data.supplyList\"\r\n            :key=\"index\"\r\n            class=\"activity-demand-item\"\r\n            @click=\"goResourceDetail(item.id)\"\r\n          >\r\n            <div class=\"activity-item-img\">\r\n              <img\r\n                v-if=\"item.productPhoto && item.productPhoto.length > 0\"\r\n                :src=\"item.productPhoto[0].url\"\r\n                alt=\"\"\r\n              />\r\n              <img\r\n                v-else\r\n                src=\"../../../../assets/purchaseSales/resourceDefault.png\"\r\n                alt=\"\"\r\n              />\r\n            </div>\r\n            <div class=\"activity-item-content\">\r\n              <div class=\"activity-item-title\">\r\n                {{ item.supplyName }}\r\n              </div>\r\n              <div class=\"activity-item-content-tag\">\r\n                <div\r\n                  v-for=\"(val, num) in item.applicationArea\"\r\n                  :key=\"num\"\r\n                  class=\"activity-item-tag\"\r\n                >\r\n                  {{ val }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 立即报名按钮 -->\r\n        <div class=\"activity-area-btn\">\r\n          <el-button\r\n            v-if=\"data.isEnroll === 1\"\r\n            disabled\r\n            type=\"info\"\r\n            class=\"activity-disabled-btn\"\r\n            >您已报名</el-button\r\n          >\r\n          <el-button v-else class=\"activity-sign-up\" @click=\"signUp\"\r\n            >立即报名</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getActivityDetail, addActivityEnroll } from \"@/api/purchaseSales\";\r\nimport { mapGetters } from \"vuex\";\r\nimport { getInfo } from \"@/api/login\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.token ? this.getInfo() : this.getActivityDetail();\r\n    },\r\n    // 详情接口\r\n    getActivityDetail(id) {\r\n      this.loading = true;\r\n      let userId = id ? id : null;\r\n      getActivityDetail({ id: this.$route.query.id, userId: userId })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          if (this.data.expertList && this.data.expertList.length > 0) {\r\n            this.data.expertList.forEach((item) => {\r\n              item.techniqueTypeName = item.techniqueTypeName.split(\",\");\r\n            });\r\n          }\r\n          if (this.data.demandList && this.data.demandList.length > 0) {\r\n            this.data.demandList.forEach((item) => {\r\n              item.scenePicture = JSON.parse(item.scenePicture);\r\n              item.applicationArea = item.applicationArea.split(\",\");\r\n            });\r\n          }\r\n          if (this.data.supplyList && this.data.supplyList.length > 0) {\r\n            this.data.supplyList.forEach((item) => {\r\n              item.productPhoto = JSON.parse(item.productPhoto);\r\n              item.applicationArea = item.applicationArea.split(\",\");\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        let id = res.user.userId || null;\r\n        this.getActivityDetail(id);\r\n      });\r\n    },\r\n    // 时间展示\r\n    getTime() {\r\n      let info = \"--\";\r\n      if (this.data.startTime && this.data.endTime) {\r\n        info = `${this.data.startTime}至${this.data.endTime}`;\r\n      } else if (this.data.startTime || this.data.endTime) {\r\n        info = this.data.startTime || this.data.endTime;\r\n      }\r\n      return info;\r\n    },\r\n    // 跳转到专家详情页面\r\n    goExpertLibraryDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/expertDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到需求详情页面\r\n    goDemandDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/demandHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到资源详情页面\r\n    goResourceDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/resourceHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 立即报名\r\n    signUp() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm(\r\n        `您参加的是${this.data.activityName},请确认。报名成功后，平台客服会与您对接`,\r\n        \"提示\",\r\n        {\r\n          confirmButtonText: \"我要参加\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }\r\n      ).then(() => {\r\n        this.loading = true;\r\n        addActivityEnroll({ activityId: this.$route.query.id })\r\n          .then(() => {\r\n            this.loading = false;\r\n            this.$message.success(\"报名成功\");\r\n            this.init();\r\n          })\r\n          .catch(() => {\r\n            this.loading = false;\r\n          });\r\n      });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-detail-container {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n  background: #f4f5f9;\r\n  .activity-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .activity-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .activity-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .activity-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .activity-detail-card {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n    .activity-detail-content {\r\n      padding: 52px 116px 60px;\r\n      .activity-detail-headline {\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        padding-bottom: 32px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        margin-bottom: 40px;\r\n        .headline-title {\r\n          font-size: 32px;\r\n          font-weight: 600;\r\n          color: #333;\r\n          line-height: 48px;\r\n          word-wrap: break-word;\r\n        }\r\n        .headline-time {\r\n          color: #333;\r\n          line-height: 14px;\r\n          padding-top: 16px;\r\n        }\r\n      }\r\n      .activity-detail-caption {\r\n        display: flex;\r\n        align-items: center;\r\n        .caption-line {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .caption-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n      .related-supply-title {\r\n        margin-top: 60px;\r\n      }\r\n      .activity-demand-info {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        .activity-demand-item {\r\n          width: 222px;\r\n          margin: 40px 18px 0 0;\r\n          background: #f8f9fb;\r\n          .activity-item-img {\r\n            width: 100%;\r\n            height: 160px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          .activity-item-content {\r\n            padding: 16px 16px 14px;\r\n            .activity-item-title {\r\n              width: 190px;\r\n              height: 52px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Medium, PingFang SC;\r\n              font-weight: 500;\r\n              color: #333;\r\n              line-height: 26px;\r\n              overflow: hidden;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n              -webkit-line-clamp: 2;\r\n              text-overflow: ellipsis;\r\n            }\r\n            .activity-item-content-tag {\r\n              display: flex;\r\n              flex-wrap: wrap;\r\n              .activity-item-tag {\r\n                max-width: 190px;\r\n                padding: 6px 12px;\r\n                font-size: 12px;\r\n                font-family: PingFangSC-Regular, PingFang SC;\r\n                color: #214dc5;\r\n                line-height: 12px;\r\n                background: rgba(33, 77, 197, 0.1);\r\n                border-radius: 4px;\r\n                margin: 12px 12px 0 0;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n          }\r\n          &:hover {\r\n            cursor: pointer;\r\n            .activity-item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .activity-detail-img {\r\n        width: 960px;\r\n        // height: 1160px;\r\n        height: 100%;\r\n        margin: 40px 0 60px;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .activity-detai-list {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        flex-wrap: wrap;\r\n        width: 100%;\r\n        margin-bottom: 60px;\r\n        .list-item-content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          width: 462px;\r\n          background: #fff;\r\n          box-shadow: 0px 8px 32px 0px rgba(38, 74, 116, 0.1);\r\n          margin-top: 40px;\r\n          padding: 22px 25px 23px 26px;\r\n          min-height: 179px;\r\n          .list-item-box {\r\n            flex: 1;\r\n            .item-headline {\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              .item-title {\r\n                width: 200px;\r\n                font-size: 26px;\r\n                font-family: PingFangSC-Medium, PingFang SC;\r\n                font-weight: 500;\r\n                color: #333;\r\n                line-height: 26px;\r\n                text-overflow: ellipsis;\r\n                white-space: nowrap;\r\n                overflow: hidden;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n            .activity-detai-label {\r\n              display: flex;\r\n              flex-wrap: wrap;\r\n              margin: 0 0 15px;\r\n              .activity-label-item {\r\n                max-width: 280px;\r\n                padding: 5px 12px;\r\n                background: #f4f5f9;\r\n                border-radius: 4px;\r\n                font-size: 10px;\r\n                font-family: PingFangSC-Regular, PingFang SC;\r\n                color: #666;\r\n                line-height: 10px;\r\n                margin: 19px 12px 0 0;\r\n                .activity-label-type {\r\n                  word-wrap: break-word;\r\n                }\r\n              }\r\n            }\r\n            .activity-detai-box {\r\n              width: 296px;\r\n              font-size: 12px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #666;\r\n              line-height: 24px;\r\n              overflow: hidden;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n              -webkit-line-clamp: 2;\r\n              text-overflow: ellipsis;\r\n              word-wrap: break-word;\r\n            }\r\n          }\r\n          .list-item-img {\r\n            width: 96px;\r\n            height: 134px;\r\n            margin-left: 19px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          &:hover {\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n      .activity-area-btn {\r\n        text-align: center;\r\n        margin-top: 60px;\r\n        .activity-sign-up {\r\n          width: 400px;\r\n          height: 50px;\r\n          background: #21c9b8;\r\n          border-radius: 4px;\r\n          font-size: 20px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #fff;\r\n          line-height: 20px;\r\n        }\r\n      }\r\n      .activity-disabled-btn {\r\n        width: 400px;\r\n        height: 50px;\r\n        border-radius: 4px;\r\n        font-size: 20px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        line-height: 20px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}