{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\orderManage\\index.vue?vue&type=style&index=0&id=6b518408&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\orderManage\\index.vue", "mtime": 1750311963069}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kOiAjZjRmNWY5Ow0KfQ0KLmNvbnRlbnQgew0KICB3aWR0aDogMTAwJTsNCiAgcGFkZGluZzogNDBweDsNCiAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgLy8gaGVpZ2h0OiA4MDBweDsNCiAgLy8gYmFja2dyb3VuZDogcmdiKDI0MiwgMjQ4LCAyNTUpOw0KICAuY29udGVudF90eXBlIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIHdpZHRoOiAxMDAlOw0KICAgIG1hcmdpbi1ib3R0b206IDMwcHg7DQogICAgLnRpdGxlIHsNCiAgICAgIHdpZHRoOiAxMDBweDsNCiAgICAgIHBhZGRpbmctbGVmdDogMjBweDsNCiAgICAgIGhlaWdodDogMzBweDsNCiAgICAgIGxpbmUtaGVpZ2h0OiAzMHB4Ow0KICAgICAgLy8gYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjMjFDOUI4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICB9DQogICAgLy8gLnJpZ2h0X2NvbnRlbnQgew0KICAgIC8vICAgd2lkdGg6IGNhbGMoMTAwJSAtIDEwMHB4KTsNCiAgICAvLyAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICAgIC8vIH0NCiAgfQ0KICAudGFiU3R5bGUgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgLy8gYmFja2dyb3VuZDogcmdiKDI0MywgMjQ4LCAyNTQpOw0KICAgIC5idXR0b25TdHlsZSB7DQogICAgICB3aWR0aDogMTAwcHg7DQogICAgICBwYWRkaW5nOiAxMHB4Ow0KICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICBib3JkZXI6IDFweCBzb2xpZCAjMjFjOWI4Ow0KICAgIH0NCiAgICAuYnV0dG9uU3R5bGU6bnRoLWNoaWxkKG4gKyAyKSB7DQogICAgICBib3JkZXItbGVmdDogbm9uZTsNCiAgICB9DQogICAgLmJ1dHRvbkhvdmVyIHsNCiAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICB9DQogIH0NCiAgLnRhYmxlU3R5bGUgew0KICAgIC5ldmVyeUl0ZW0gew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgICBoZWlnaHQ6IDIwMHB4Ow0KICAgICAgYm94LXNoYWRvdzogMHB4IDRweCAyMHB4IDBweCByZ2JhKDAsIDAsIDAsIDAuMDYpOw0KICAgICAgbWFyZ2luLXRvcDogMjBweDsNCiAgICAgIHBhZGRpbmc6IDIwcHg7DQogICAgICAvLyBiYWNrZ3JvdW5kOiAjZmZmZmZmOw0KICAgICAgLm9yZGVyTnVtVGltZSB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICB9DQogICAgICAuZHJpdmVyIHsNCiAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgIGhlaWdodDogMXB4Ow0KICAgICAgICBiYWNrZ3JvdW5kOiAjY2NjOw0KICAgICAgICBtYXJnaW46IDE1cHggMDsNCiAgICAgIH0NCiAgICAgIC5pdGVtX2NvbnRlbnQgew0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgLy8gaGVpZ2h0OiAxMDAlOw0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAuaXRlbV9pbWcgew0KICAgICAgICAgIHdpZHRoOiAxNCU7DQogICAgICAgICAgaGVpZ2h0OiAxMTBweDsNCiAgICAgICAgICBpbWcgew0KICAgICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC5pdGVtX2Rlc2Mgew0KICAgICAgICAgIG1hcmdpbi1sZWZ0OiAyMHB4Ow0KICAgICAgICAgIHdpZHRoOiAyNSU7DQogICAgICAgICAgLnRpdGxlIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgICAgIGZvbnQtZmFtaWx5OiBTb3VyY2UgSGFuIFNhbnMgQ047DQogICAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC5pdGVtX2Ftb3VudHMgew0KICAgICAgICAgIHdpZHRoOiAxMCU7DQogICAgICAgICAgdGV4dC1hbGlnbjogcmlnaHQ7DQogICAgICAgIH0NCiAgICAgICAgLmRyaXZlclZlcnRpY2FsIHsNCiAgICAgICAgICB3aWR0aDogMXB4Ow0KICAgICAgICAgIGhlaWdodDogMTEwcHg7DQogICAgICAgICAgYmFja2dyb3VuZDogI2NjYzsNCiAgICAgICAgICBtYXJnaW46IDAgOCU7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyjBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/orderManage", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\">\r\n          <div class=\"content_type\">\r\n            <div class=\"title\">订单管理</div>\r\n          </div>\r\n          <div class=\"tabStyle\">\r\n            <!-- <div\r\n              v-for=\"item in orderTypeList\"\r\n              :key=\"item.value\"\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == item.value ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(item.value)\"\r\n            >\r\n              {{ item.label }}\r\n            </div> -->\r\n            <!-- <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 2 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(2)\"\r\n            >\r\n              待付款\r\n            </div>\r\n            <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 3 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(3)\"\r\n            >\r\n              待发货\r\n            </div>\r\n            <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 4 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(4)\"\r\n            >\r\n              待收货\r\n            </div>\r\n            <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 5 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(5)\"\r\n            >\r\n              已完成\r\n            </div> -->\r\n          </div>\r\n          <div style=\"margin-top: 20px\">\r\n            <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"订单编号\" prop=\"nickName\">\r\n                    <el-input v-model=\"form.id\" placeholder=\"请输入\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <!-- <el-col :span=\"6\">\r\n                  <el-form-item label=\"手机号\" prop=\"userName\">\r\n                    <el-input v-model=\"form.phone\" placeholder=\"请输入\" />\r\n                  </el-form-item>\r\n                </el-col> -->\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"应用名称\" prop=\"userName\">\r\n                    <el-input v-model=\"form.appName\" placeholder=\"请输入\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"订货编码\" prop=\"userName\">\r\n                    <el-input v-model=\"form.orderCode\" placeholder=\"请输入\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"订单状态\" prop=\"userName\">\r\n                    <el-select\r\n                      v-model=\"form.orderStatus\"\r\n                      placeholder=\"请选择\"\r\n                      clearable\r\n                      style=\"width: 100%\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"dict in orderStatusList\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictLabel\"\r\n                        :value=\"dict.dictValue\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"订单生成时间\" prop=\"userName\">\r\n                    <el-date-picker\r\n                      v-model=\"createTime\"\r\n                      type=\"daterange\"\r\n                      range-separator=\"至\"\r\n                      start-placeholder=\"开始日期\"\r\n                      end-placeholder=\"结束日期\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                    >\r\n                    </el-date-picker>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item>\r\n                    <el-button\r\n                      type=\"primary\"\r\n                      icon=\"el-icon-search\"\r\n                      size=\"mini\"\r\n                      @click=\"handleQuery\"\r\n                      >查询</el-button\r\n                    >\r\n                    <el-button\r\n                      icon=\"el-icon-refresh\"\r\n                      size=\"mini\"\r\n                      @click=\"resetQuery\"\r\n                      >重置</el-button\r\n                    >\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n          </div>\r\n          <div class=\"tableStyle\" v-loading=\"loading\">\r\n            <div class=\"everyItem\" v-for=\"item in subscribeList\" :key=\"item.id\">\r\n              <div class=\"orderNumTime\">\r\n                <div>订单编号: {{ item.id }}</div>\r\n                <div style=\"margin-left: 5%\">\r\n                  下单时间: {{ item.createTime }}\r\n                </div>\r\n                <div style=\"margin-left: 5%\">下单人: {{ item.nickName }}</div>\r\n                <div style=\"margin-left: 5%\">企业名称: {{ item.supply }}</div>\r\n              </div>\r\n              <div class=\"driver\"></div>\r\n              <div class=\"item_content\">\r\n                <div class=\"item_img\">\r\n                  <img :src=\"item.appLogo\" alt=\"\" />\r\n                </div>\r\n                <div class=\"item_desc\">\r\n                  <div class=\"title\">{{ item.remark }}</div>\r\n                  <!-- <div style=\"font-size: 14px; margin-top: 10px\">\r\n                    <span style=\"color: #999999\">规格:</span>\r\n                    <span style=\"margin-left: 5px\">{{ item.spec }}</span>\r\n                  </div> -->\r\n                  <div style=\"font-size: 14px; margin-top: 10px\">\r\n                    <span style=\"color: #999999\">可用时长:</span>\r\n                    <span style=\"margin-left: 5px\">{{\r\n                      item.validTime == \"1\" ? \"一年\" : \"永久\"\r\n                    }}</span>\r\n                  </div>\r\n                  <div style=\"font-size: 14px; margin-top: 10px\">\r\n                    <span style=\"color: #999999\">可用人数:</span>\r\n                    <span style=\"margin-left: 5px\">{{ item.userNumber }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item_amounts\">\r\n                  <div style=\"color: #999999; font-size: 14px\">订单金额</div>\r\n                  <div style=\"margin-top: 10px; font-weight: 400\">\r\n                    ￥{{ item.price }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"driverVertical\"></div>\r\n                <div>\r\n                  <div>{{ getStatus(item.orderStatus) }}</div>\r\n                  <!-- <div\r\n                    style=\"margin-top: 10px; color: #21C9B8; cursor: pointer\"\r\n                    @click=\"gotoDetail(item.id)\"\r\n                  >\r\n                    订单详情\r\n                  </div> -->\r\n                </div>\r\n                <div style=\"margin: 0 7%\">\r\n                  <div\r\n                    style=\"color: #21c9b8; cursor: pointer\"\r\n                    @click=\"gotoDetail(item.id)\"\r\n                  >\r\n                    订单详情\r\n                  </div>\r\n                  <!-- 待发货 -->\r\n                  <div v-if=\"item.orderStatus == 2\">\r\n                    <div\r\n                      @click=\"goShip(item.id)\"\r\n                      style=\"margin-top: 10px; color: #21c9b8; cursor: pointer\"\r\n                    >\r\n                      去发货\r\n                    </div>\r\n                  </div>\r\n                  <!-- 已发货 -->\r\n                  <div v-if=\"item.orderStatus !== 2 && item.orderStatus !== 9\">\r\n                    <div\r\n                      @click=\"invoicing(item.id)\"\r\n                      style=\"margin-top: 10px; color: #21c9b8; cursor: pointer\"\r\n                    >\r\n                      {{ item.makeinvoice == 0 ? \"开具发票\" : \"重新开票\" }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div style=\"text-align: center; margin-top: 45px\">\r\n              <el-pagination\r\n                v-show=\"total > 0\"\r\n                background\r\n                layout=\"prev, pager, next\"\r\n                :page-size=\"5\"\r\n                :current-page.sync=\"queryParams.pageNum\"\r\n                @current-change=\"handleCurrentChange\"\r\n                :total=\"total\"\r\n              >\r\n              </el-pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      title=\"开票信息\"\r\n      :visible.sync=\"invoiceVisible\"\r\n      width=\"750px\"\r\n      append-to-body\r\n    >\r\n      <el-form\r\n        ref=\"ruleForm\"\r\n        :model=\"ruleForm\"\r\n        :rules=\"rules\"\r\n        label-width=\"80px\"\r\n      >\r\n        <el-form-item label=\"发票类型:\">\r\n          {{ invoiceData.invoiceType == 1 ? \"专票\" : \"普票\" }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司名称:\">\r\n          {{ invoiceData.companyName }}\r\n        </el-form-item>\r\n        <el-form-item label=\"税号:\">\r\n          {{ invoiceData.dutyParagraph }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司地址:\">\r\n          {{ invoiceData.address }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司电话:\">\r\n          {{ invoiceData.phone }}\r\n        </el-form-item>\r\n        <el-form-item label=\"开户银行:\">\r\n          {{ invoiceData.openAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"银行账号:\">\r\n          {{ invoiceData.bankAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱地址:\">\r\n          {{ invoiceData.email }}\r\n        </el-form-item>\r\n        <el-form-item label=\"上传发票\" prop=\"companyCardList\">\r\n          <el-upload\r\n            :headers=\"headers\"\r\n            :action=\"actionUrl\"\r\n            accept=\".pdf,.jpg,.png\"\r\n            :file-list=\"ruleForm.companyCardList\"\r\n            :before-upload=\"handleBeforeUpload\"\r\n            :on-remove=\"handleApplicationRemove\"\r\n            :on-success=\"handleApplicationSuccess\"\r\n            :on-exceed=\"handleExceedLicence\"\r\n            :on-preview=\"handlePreview\"\r\n            :limit=\"1\"\r\n          >\r\n            <div>\r\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload2\"\r\n                >上传文件</el-button\r\n              >\r\n              <span style=\"margin-left: 10px\">可上传pdf,jpg,png格式</span>\r\n            </div>\r\n          </el-upload>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">发送发票</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  orderList,\r\n  modifyStatus,\r\n  invoiceList,\r\n  sendInvoice,\r\n} from \"@/api/system/user\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    var validIsEmptyArr = (s, value, callback) => {\r\n      if (!Array.isArray(value) || value.length === 0) {\r\n        callback(new Error(\"请上传文件\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    return {\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      actionUrl: uploadUrl(),\r\n      form: {\r\n        id: \"\",\r\n        phone: \"\",\r\n        appName: \"\",\r\n        orderCode: \"\",\r\n        orderStatus: \"\",\r\n      },\r\n      createTime: [],\r\n      subscribeList: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      flag: 1,\r\n      typeList: [\r\n        {\r\n          dictValue: 1,\r\n          dictLabel: \"工业应用\",\r\n        },\r\n        {\r\n          dictValue: 2,\r\n          dictLabel: \"工业模型\",\r\n        },\r\n      ],\r\n      orderStatusList: [\r\n        {\r\n          dictValue: 1,\r\n          dictLabel: \"待支付\",\r\n        },\r\n        {\r\n          dictValue: 2,\r\n          dictLabel: \"待发货\",\r\n        },\r\n        {\r\n          dictValue: 3,\r\n          dictLabel: \"支付失败\",\r\n        },\r\n        {\r\n          dictValue: 4,\r\n          dictLabel: \"已发货\",\r\n        },\r\n        {\r\n          dictValue: 5,\r\n          dictLabel: \"已成交\",\r\n        },\r\n        {\r\n          dictValue: 6,\r\n          dictLabel: \"待续费\",\r\n        },\r\n        {\r\n          dictValue: 7,\r\n          dictLabel: \"已关闭\",\r\n        },\r\n        {\r\n          dictValue: 8,\r\n          dictLabel: \"支付中\",\r\n        },\r\n        {\r\n          dictValue: 9,\r\n          dictLabel: \"已取消\",\r\n        },\r\n      ],\r\n      orderTypeList: [\r\n        {\r\n          value: \"0\",\r\n          label: \"全部\",\r\n        },\r\n        {\r\n          value: \"1\",\r\n          label: \"待付款\",\r\n        },\r\n        {\r\n          value: \"2\",\r\n          label: \"待发货\",\r\n        },\r\n        {\r\n          value: \"3\",\r\n          label: \"待收货\",\r\n        },\r\n        {\r\n          value: \"4\",\r\n          label: \"已完成\",\r\n        },\r\n      ],\r\n      flag: \"0\",\r\n      loading: false,\r\n      invoiceVisible: false,\r\n      invoiceData: {},\r\n      ruleForm: {\r\n        companyCardList: [],\r\n      },\r\n      rules: {\r\n        companyCardList: [\r\n          { required: true, validator: validIsEmptyArr, trigger: \"change\" },\r\n        ],\r\n      },\r\n      currentId: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        createBy: this.$store.state.user.userId,\r\n        pageNum: this.queryParams.pageNum,\r\n        pageSize: 5,\r\n        ...this.form,\r\n        startTime: this.createTime.length > 0 ? this.createTime[0] : \"\",\r\n        endTime: this.createTime.length > 0 ? this.createTime[1] : \"\",\r\n      };\r\n      orderList(params).then((response) => {\r\n        this.subscribeList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.form = {\r\n        id: \"\",\r\n        phone: \"\",\r\n        appName: \"\",\r\n        orderCode: \"\",\r\n        orderStatus: \"\",\r\n      };\r\n      this.createTime = [];\r\n      this.getList();\r\n    },\r\n    getFlag(value) {\r\n      this.flag = value;\r\n    },\r\n    gotoDetail(id) {\r\n      this.$router.push({\r\n        path: \"/user/orderManageDetail\",\r\n        query: {\r\n          id,\r\n        },\r\n      });\r\n    },\r\n    getStatus(status) {\r\n      let orderStatus;\r\n      this.orderStatusList.forEach((item) => {\r\n        if (item.dictValue == status) {\r\n          orderStatus = item.dictLabel;\r\n        }\r\n      });\r\n      return orderStatus;\r\n    },\r\n    goShip(id) {\r\n      this.$confirm(\"货品发货后无法撤销，确认发货吗？\", \"发货确认\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          let data = {\r\n            id,\r\n            orderStatus: 4,\r\n          };\r\n          modifyStatus(data).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    invoicing(id) {\r\n      this.currentId = id;\r\n      invoiceList().then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceData = res.data;\r\n          this.invoiceVisible = true;\r\n          this.ruleForm.companyCardList = [];\r\n        }\r\n      });\r\n    },\r\n    cancelDialog() {\r\n      this.invoiceVisible = false;\r\n    },\r\n    submitForm() {\r\n      this.$refs.ruleForm.validate((valid) => {\r\n        if (valid) {\r\n          let data = {\r\n            companyCardList: this.ruleForm.companyCardList,\r\n            orderId: this.currentId,\r\n          };\r\n          sendInvoice(data).then((res) => {\r\n            if (res.code === 200) {\r\n              this.invoiceVisible = false;\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        } else {\r\n          console.log(\"error submit!!\");\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.ruleForm.companyCardList = [];\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      if (res.code == 200) {\r\n        this.ruleForm.companyCardList.push({\r\n          name: res.data.name,\r\n          url: res.data.url,\r\n          type: res.data.type,\r\n          suffix: res.data.suffix,\r\n        });\r\n      }\r\n    },\r\n    handleExceedLicence(files, fileList) {\r\n      let num = files.length + fileList.length;\r\n      if (num >= 1) {\r\n        this.$message.error(\"上传数量超过上限\");\r\n        return false;\r\n      }\r\n    },\r\n    handlePreview(file) {\r\n      window.open(file.url);\r\n    },\r\n    // 文件上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      // let typeList = this.accept\r\n      //   .split(\",\")\r\n      //   .map((item) => item.trim().toLowerCase().substr(1));\r\n      // let dotIndex = name.lastIndexOf(\".\");\r\n      // // 文件类型校验\r\n      // if (dotIndex === -1) {\r\n      //   this.$message.error(\"请上传正确格式的文件\");\r\n      //   return false;\r\n      // } else {\r\n      //   let suffix = name.substring(dotIndex + 1);\r\n      //   if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n      //     this.$message.error(\"请上传正确格式的文件\");\r\n      //     return false;\r\n      //   }\r\n      // }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 5) {\r\n        this.$message.error(\"文件大小不能超过5M！\");\r\n        return false;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  // height: 800px;\r\n  // background: rgb(242, 248, 255);\r\n  .content_type {\r\n    display: flex;\r\n    width: 100%;\r\n    margin-bottom: 30px;\r\n    .title {\r\n      width: 100px;\r\n      padding-left: 20px;\r\n      height: 30px;\r\n      line-height: 30px;\r\n      // border-left: 4px solid #21C9B8;\r\n      font-weight: 600;\r\n      font-size: 18px;\r\n    }\r\n    // .right_content {\r\n    //   width: calc(100% - 100px);\r\n    //   text-align: right;\r\n    // }\r\n  }\r\n  .tabStyle {\r\n    display: flex;\r\n    // background: rgb(243, 248, 254);\r\n    .buttonStyle {\r\n      width: 100px;\r\n      padding: 10px;\r\n      color: #21c9b8;\r\n      text-align: center;\r\n      cursor: pointer;\r\n      border: 1px solid #21c9b8;\r\n    }\r\n    .buttonStyle:nth-child(n + 2) {\r\n      border-left: none;\r\n    }\r\n    .buttonHover {\r\n      background: #21c9b8;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .tableStyle {\r\n    .everyItem {\r\n      width: 100%;\r\n      height: 200px;\r\n      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n      margin-top: 20px;\r\n      padding: 20px;\r\n      // background: #ffffff;\r\n      .orderNumTime {\r\n        display: flex;\r\n      }\r\n      .driver {\r\n        width: 100%;\r\n        height: 1px;\r\n        background: #ccc;\r\n        margin: 15px 0;\r\n      }\r\n      .item_content {\r\n        width: 100%;\r\n        // height: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        .item_img {\r\n          width: 14%;\r\n          height: 110px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .item_desc {\r\n          margin-left: 20px;\r\n          width: 25%;\r\n          .title {\r\n            font-size: 16px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            color: #333333;\r\n          }\r\n        }\r\n        .item_amounts {\r\n          width: 10%;\r\n          text-align: right;\r\n        }\r\n        .driverVertical {\r\n          width: 1px;\r\n          height: 110px;\r\n          background: #ccc;\r\n          margin: 0 8%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}