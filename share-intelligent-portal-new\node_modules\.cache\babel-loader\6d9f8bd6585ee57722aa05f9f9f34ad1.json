{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\addIntention.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\addIntention.vue", "mtime": 1750311962953}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_zhm", "require", "TYPES", "_default", "exports", "default", "name", "data", "user", "$store", "state", "loading", "form", "businessId", "undefined", "resourceType", "resourceTitle", "resourceDescribe", "<PERSON><PERSON><PERSON>", "contactPhone", "tel", "created", "_this$$route$query", "$route", "query", "id", "type", "title", "methods", "formatTitle", "key", "onSubmit", "_this", "interactRecordAdd", "then", "res", "code", "msg", "$message", "success", "error", "finally", "console", "log"], "sources": ["src/views/form/addIntention.vue"], "sourcesContent": ["<template>\r\n  <div class=\"intention-page\">\r\n    <div class=\"intention-page-header\">\r\n      <div class=\"banner\">\r\n        <img\r\n          src=\"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/1676034162343360.webp\"\r\n          alt=\"我有意向\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <div class=\"intention-page-title\">我有意向</div>\r\n    <div v-loading=\"loading\" class=\"card-container intention-form\">\r\n      <div class=\"form-content\">\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n          <el-form-item label=\"意向类型\">\r\n            {{ formatTitle(form.resourceType) }}\r\n          </el-form-item>\r\n          <el-form-item label=\"意向名称\">\r\n            {{form.resourceTitle || \"--\"}}\r\n          </el-form-item>\r\n          <el-form-item label=\"意向描述\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              v-model=\"form.resourceDescribe\"\r\n              maxlength=\"500\"\r\n              rows=\"6\"\r\n              show-word-limit\r\n              placeholder=\"请输入需求描述\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"联系人\">\r\n            <el-input disabled v-model=\"form.contactPerson\" placeholder=\"请输入联系人\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"联系方式\">\r\n            <el-input disabled v-model=\"form.contactPhone\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item class=\"footer-submit\">\r\n            <el-button type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>/**\r\n * businessId 业务ID\r\n * resourceType类型是下面:\r\n * 专家智库  resource_expet\r\n * 成果云服  resource_supply\r\n * 商机需求  resource_demand\r\n * resourceTitle: 资源名称\r\n *\r\n * **/\r\nimport {interactRecordAdd} from \"@/api/zhm\";\r\n\r\nconst TYPES = {\r\n  \"resource_expet\": \"专家智库\",\r\n  \"resource_supply\": \"成果云服\",\r\n  \"resource_demand\": \"商机需求\",\r\n};\r\n\r\nexport default {\r\n  name: \"addIntention\",\r\n  data() {\r\n    const { user } = this.$store.state;\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        businessId: undefined,\r\n        resourceType: undefined,\r\n        resourceTitle: undefined,\r\n        resourceDescribe: undefined,\r\n        contactPerson: user.name,\r\n        contactPhone: user.tel,\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    const { id, type, title } = this.$route.query;\r\n    if (id) {\r\n      this.form.businessId = id;\r\n      this.form.resourceType = type;\r\n      this.form.resourceTitle = title;\r\n    }\r\n  },\r\n  methods: {\r\n    formatTitle(key) {\r\n      return TYPES[key] || '--';\r\n    },\r\n    onSubmit() {\r\n      this.loading = true;\r\n      interactRecordAdd(this.form).then((res) => {\r\n        const { code, msg } = res;\r\n        if (code === 200) {\r\n          this.$message.success(\"提交成功\");\r\n          // TODO: 跳转到提交记录\r\n          // this.$router.push({\r\n          //   path: \"/addIntention\",\r\n          //   params: {\r\n          //     id: '对应当前资源的id',\r\n          //     type: '专家智库: resource_expet 成果云服  resource_supply 商机需求  resource_demand',\r\n          //     title: '资源名称'\r\n          //   },\r\n          // });\r\n        } else {\r\n          this.$message.error(msg || \"提交失败\");\r\n        }\r\n      }).finally(() => this.loading = false)\r\n      console.log('submit!', this.form);\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n.intention-page {\r\n  background-color: #F4F5F9;\r\n  padding-bottom: 80px;\r\n  &-header {\r\n    background-color: #FFFFFF;\r\n    .banner {\r\n      width: 100%;\r\n      height: 540px;\r\n      background-color: #f5f5f5;\r\n      img {\r\n        width: 100%;\r\n        height: 540px;\r\n        object-fit: fill;\r\n      }\r\n    }\r\n    .body {\r\n      padding: 60px 0;\r\n    }\r\n  }\r\n  &-title {\r\n    font-size: 40px;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    line-height: 40px;\r\n    text-align: center;\r\n    padding: 60px 0;\r\n  }\r\n  .intention-form {\r\n    @include flexCenter;\r\n    height: 664px;\r\n    background-color: #FFFFFF;\r\n    margin-bottom: 80px;\r\n    .form-content {\r\n      width: 750px;\r\n      .footer-submit {\r\n        text-align: center;\r\n        margin-top: 40px;\r\n        .el-button {\r\n          width: 400px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAsDA,IAAAA,IAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,IAAAC,KAAA;EACA;EACA;EACA;AACA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA,IAAAC,IAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,IAAA;IACA;MACAG,OAAA;MACAC,IAAA;QACAC,UAAA,EAAAC,SAAA;QACAC,YAAA,EAAAD,SAAA;QACAE,aAAA,EAAAF,SAAA;QACAG,gBAAA,EAAAH,SAAA;QACAI,aAAA,EAAAV,IAAA,CAAAF,IAAA;QACAa,YAAA,EAAAX,IAAA,CAAAY;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,kBAAA,QAAAC,MAAA,CAAAC,KAAA;MAAAC,EAAA,GAAAH,kBAAA,CAAAG,EAAA;MAAAC,IAAA,GAAAJ,kBAAA,CAAAI,IAAA;MAAAC,KAAA,GAAAL,kBAAA,CAAAK,KAAA;IACA,IAAAF,EAAA;MACA,KAAAb,IAAA,CAAAC,UAAA,GAAAY,EAAA;MACA,KAAAb,IAAA,CAAAG,YAAA,GAAAW,IAAA;MACA,KAAAd,IAAA,CAAAI,aAAA,GAAAW,KAAA;IACA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MACA,OAAA5B,KAAA,CAAA4B,GAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,KAAArB,OAAA;MACA,IAAAsB,sBAAA,OAAArB,IAAA,EAAAsB,IAAA,WAAAC,GAAA;QACA,IAAAC,IAAA,GAAAD,GAAA,CAAAC,IAAA;UAAAC,GAAA,GAAAF,GAAA,CAAAE,GAAA;QACA,IAAAD,IAAA;UACAJ,KAAA,CAAAM,QAAA,CAAAC,OAAA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;UACAP,KAAA,CAAAM,QAAA,CAAAE,KAAA,CAAAH,GAAA;QACA;MACA,GAAAI,OAAA;QAAA,OAAAT,KAAA,CAAArB,OAAA;MAAA;MACA+B,OAAA,CAAAC,GAAA,iBAAA/B,IAAA;IACA;EACA;AACA", "ignoreList": []}]}