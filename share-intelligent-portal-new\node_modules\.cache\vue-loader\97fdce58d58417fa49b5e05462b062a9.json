{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\enterpriseList\\detail.vue?vue&type=style&index=0&id=47302afe&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\enterpriseList\\detail.vue", "mtime": 1750311962986}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/purchaseSales/component/enterpriseList", "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-02-03 09:26:43\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-12-09 21:08:18\r\n-->\r\n<template>\r\n  <div class=\"enterprise-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"enterprise-detail-banner\">\r\n      <img\r\n        src=\"../../../../assets/enterprise/enterpriseDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"enterprise-detail-title-box\">\r\n      <div class=\"enterprise-detail-divider\"></div>\r\n      <div class=\"enterprise-detail-title\">机构详情</div>\r\n      <div class=\"enterprise-detail-divider\"></div>\r\n    </div>\r\n    <div class=\"enterprise-detail-content\">\r\n      <div class=\"enterprise-title\">{{ data.name }}</div>\r\n      <div class=\"enterprise-title-carousel\">\r\n        <el-carousel\r\n          v-if=\"data.companyPictureList && data.companyPictureList.length > 0\"\r\n          class=\"carousel-content\"\r\n          arrow=\"always\"\r\n          :interval=\"5000\"\r\n        >\r\n          <el-carousel-item\r\n            v-for=\"(item, index) in data.companyPictureList\"\r\n            :key=\"index\"\r\n            class=\"carousel-item-content\"\r\n          >\r\n            <div class=\"carousel-item-box\">\r\n              <img :src=\"item.url\" alt=\"\" />\r\n            </div>\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n        <img\r\n          v-else\r\n          src=\"../../../../assets/purchaseSales/companyDefault.png\"\r\n          alt=\"\"\r\n          class=\"carousel-default-img\"\r\n        />\r\n      </div>\r\n      <div class=\"enterprise-title-tag\">\r\n        <div\r\n          v-for=\"(item, index) in data.industrialChainValueList\"\r\n          :key=\"index\"\r\n          class=\"title-tag-item\"\r\n        >\r\n          {{ item }}\r\n        </div>\r\n      </div>\r\n      <div class=\"enterprise-title-address\">\r\n        <div class=\"address-content\">\r\n          <img\r\n            src=\"../../../../assets/enterprise/addressIcon.png\"\r\n            alt=\"\"\r\n            class=\"address-content-img\"\r\n          />\r\n          <div class=\"address-content-text\">\r\n            {{ data.address }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- 企业简介 -->\r\n      <div class=\"enterprise-introduction-box\" v-if=\"this.data.introduce != 0\">\r\n        <div class=\"enterprise-introduction-title\">\r\n          <div class=\"introduction-line\"></div>\r\n          <div class=\"introduction-title\">企业简介</div>\r\n        </div>\r\n        <div class=\"enterprise-introduction-info\">\r\n          {{ data.introduce }}\r\n        </div>\r\n      </div>\r\n      <!-- 企业需求 -->\r\n      <div class=\"enterprise-demand-content\" v-if=\"this.demandTotal != 0\">\r\n        <div class=\"enterprise-introduction-title\">\r\n          <div class=\"introduction-line\"></div>\r\n          <div class=\"introduction-title\">企业需求（{{ demandTotal }}）</div>\r\n        </div>\r\n        <div class=\"enterprise-demand-info\">\r\n          <div\r\n            v-for=\"(item, index) in demandData\"\r\n            :key=\"index\"\r\n            class=\"enterprise-demand-item\"\r\n          >\r\n            <router-link\r\n              :to=\"{ name: 'demandHallDetail', query: { id: item.id } }\"\r\n            >\r\n              <div class=\"item-img\">\r\n                <img\r\n                  v-if=\"item.scenePicture && item.scenePicture.length > 0\"\r\n                  :src=\"item.scenePicture[0].url\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/companyDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <!-- </router-link> -->\r\n              <div class=\"item-content\">\r\n                <div class=\"item-title\">\r\n                  {{ item.demandTitle }}\r\n                </div>\r\n                <div class=\"item-content-tag\">\r\n                  <div\r\n                    v-for=\"(val, num) in item.applicationArea\"\r\n                    :key=\"num\"\r\n                    class=\"item-tag\"\r\n                  >\r\n                    {{ val }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </router-link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- 企业供给 -->\r\n      <div class=\"enterprise-demand-content\" v-if=\"this.supplyTotal != 0\">\r\n        <div class=\"enterprise-introduction-title\">\r\n          <div class=\"introduction-line\"></div>\r\n          <div class=\"introduction-title\">企业供给（{{ supplyTotal }}）</div>\r\n        </div>\r\n        <div class=\"enterprise-demand-info\">\r\n          <div\r\n            v-for=\"(item, index) in supplyData\"\r\n            :key=\"index\"\r\n            class=\"enterprise-demand-item\"\r\n          >\r\n            <router-link\r\n              :to=\"{ name: 'resourceHallDetail', query: { id: item.id } }\"\r\n            >\r\n              <div class=\"item-img\">\r\n                <img\r\n                  v-if=\"item.productPhoto && item.productPhoto.length > 0\"\r\n                  :src=\"item.productPhoto[0].url\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/resourceDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <!-- </router-link> -->\r\n              <div class=\"item-content\">\r\n                <div class=\"item-title\">\r\n                  {{ item.supplyName }}\r\n                </div>\r\n                <div class=\"item-content-tag\">\r\n                  <div\r\n                    v-for=\"(val, num) in item.applicationArea\"\r\n                    :key=\"num\"\r\n                    class=\"item-tag\"\r\n                  >\r\n                    {{ val }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </router-link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- 企业资料 -->\r\n      <div\r\n        class=\"enterprise-data-content\"\r\n        v-if=\"this.data.companyMaterialList != 0\"\r\n      >\r\n        <div class=\"enterprise-introduction-title introduction-title-data\">\r\n          <div class=\"introduction-line\"></div>\r\n          <div class=\"introduction-title\">企业资料</div>\r\n        </div>\r\n        <div\r\n          v-for=\"(item, index) in data.companyMaterialList\"\r\n          :key=\"index\"\r\n          class=\"introduction-data-info\"\r\n        >\r\n          <div class=\"item-introduction\">\r\n            <div class=\"item-introduction-name\">\r\n              <div class=\"item-introduction-img\">\r\n                <img\r\n                  src=\"../../../../assets/purchaseSales/linkIcon.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"item-introduction-file\">\r\n                {{ item.name }}\r\n              </div>\r\n            </div>\r\n            <div class=\"item-introduction-btn\">\r\n              <el-button @click=\"viewData(item.url)\">立即查看</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"enterprise-detail-btn\">\r\n        <!-- <el-button icon=\"el-icon-chat-dot-round\">在线沟通</el-button> -->\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getCompanyDetail,\r\n  getDemandList,\r\n  getSupplyList,\r\n} from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      data: {},\r\n      status: true,\r\n      demandData: [],\r\n      demandTotal: 0,\r\n      supplyData: [],\r\n      supplyTotal: 0,\r\n      arr: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getCompanyDetail();\r\n    this.getDemandList();\r\n    this.getSupplyList();\r\n  },\r\n  methods: {\r\n    // 企业详情\r\n    getCompanyDetail() {\r\n      this.loading = true;\r\n      getCompanyDetail({ id: this.$route.query.id })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 企业需求\r\n    getDemandList() {\r\n      getDemandList({\r\n        businessNo: this.$route.query.businessNo,\r\n        displayStatus: 1,\r\n        auditStatus: 2,\r\n      }).then((res) => {\r\n        let { rows, total } = res || [];\r\n        this.demandData = rows;\r\n        this.demandData.forEach((item) => {\r\n          item.scenePicture = item.scenePicture\r\n            ? JSON.parse(item.scenePicture)\r\n            : [];\r\n          item.applicationArea = item.applicationArea\r\n            ? item.applicationArea.split(\",\")\r\n            : [];\r\n        });\r\n        this.demandTotal = total;\r\n      });\r\n    },\r\n    // btn11(){\r\n    //   this.$router.push({path:'/demandHallDetail',query:{id:this.demandData.id}})\r\n    // },\r\n    // 企业供给\r\n    getSupplyList() {\r\n      getSupplyList({\r\n        businessNo: this.$route.query.businessNo,\r\n        auditStatus: 2,\r\n        displayStatus: 1,\r\n      }).then((res) => {\r\n        let { rows, total } = res || [];\r\n        this.supplyData = rows;\r\n        this.supplyData.forEach((item) => {\r\n          item.productPhoto = item.productPhoto\r\n            ? JSON.parse(item.productPhoto)\r\n            : [];\r\n          item.applicationArea = item.applicationArea\r\n            ? item.applicationArea.split(\",\")\r\n            : [];\r\n        });\r\n        this.supplyTotal = total;\r\n      });\r\n    },\r\n    // 查看企业资料\r\n    viewData(url) {\r\n      window.open(url);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.enterprise-detail-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  padding-bottom: 60px;\r\n  .enterprise-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .enterprise-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .enterprise-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .enterprise-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .enterprise-detail-content {\r\n    width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 60px 116px 58px;\r\n    background: #fff;\r\n    .enterprise-title {\r\n      max-width: 960px;\r\n      font-size: 32px;\r\n      font-family: PingFangSC-Semibold, PingFang SC;\r\n      font-weight: 600;\r\n      color: #333;\r\n      line-height: 32px;\r\n      text-align: center;\r\n      padding-bottom: 44px;\r\n      word-break: break-all;\r\n    }\r\n    .enterprise-title-carousel {\r\n      width: 720px;\r\n      height: 360px;\r\n      margin: 0 auto;\r\n      .carousel-content {\r\n        width: 100%;\r\n        height: 360px;\r\n        .carousel-item-content {\r\n          width: 100%;\r\n          height: 100%;\r\n          .carousel-item-box {\r\n            margin: 0 auto;\r\n            width: 600px;\r\n            height: 100%;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .carousel-default-img {\r\n        width: 600px;\r\n        height: 100%;\r\n        margin: 0 auto;\r\n        display: block;\r\n      }\r\n    }\r\n    .enterprise-title-tag {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      width: 600px;\r\n      margin: 0 auto 21px;\r\n      .title-tag-item {\r\n        max-width: 660px;\r\n        background: rgba(197, 37, 33, 0.1);\r\n        border-radius: 4px;\r\n        padding: 6px 12px;\r\n        font-size: 12px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #21c9b8;\r\n        line-height: 12px;\r\n        margin: 16px 16px 0 0;\r\n        word-wrap: break-word;\r\n        text-align: left;\r\n      }\r\n    }\r\n    .enterprise-title-address {\r\n      width: 600px;\r\n      margin: 21px auto 0;\r\n      .address-content {\r\n        display: flex;\r\n        align-items: center;\r\n        .address-content-img {\r\n          width: 12px;\r\n          height: 14px;\r\n        }\r\n        .address-content-text {\r\n          max-width: 600px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #666;\r\n          line-height: 14px;\r\n          padding-left: 6px;\r\n          word-wrap: break-word;\r\n          text-align: left;\r\n        }\r\n      }\r\n    }\r\n    .enterprise-introduction-box {\r\n      width: 960px;\r\n      padding-top: 61px;\r\n      .enterprise-introduction-info {\r\n        width: 960px;\r\n        padding-top: 40px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n        word-wrap: break-word;\r\n        text-align: left;\r\n      }\r\n    }\r\n    .enterprise-introduction-title {\r\n      display: flex;\r\n      align-items: center;\r\n      .introduction-line {\r\n        width: 4px;\r\n        height: 20px;\r\n        background: #21c9b8;\r\n      }\r\n      .introduction-title {\r\n        font-size: 24px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 24px;\r\n        padding-left: 8px;\r\n      }\r\n    }\r\n    .introduction-title-data {\r\n      margin-bottom: 40px;\r\n    }\r\n    .enterprise-demand-content {\r\n      width: 960px;\r\n      padding-top: 60px;\r\n      .enterprise-demand-info {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        .enterprise-demand-item {\r\n          width: 222px;\r\n          margin: 40px 18px 0 0;\r\n          background: #f8f9fb;\r\n          .item-img {\r\n            width: 100%;\r\n            height: 160px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          .item-content {\r\n            padding: 16px 16px 14px;\r\n            .item-title {\r\n              width: 190px;\r\n              height: 52px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Medium, PingFang SC;\r\n              font-weight: 500;\r\n              color: #333;\r\n              line-height: 26px;\r\n              overflow: hidden;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n              -webkit-line-clamp: 2;\r\n              text-overflow: ellipsis;\r\n              word-wrap: break-word;\r\n            }\r\n            .item-content-tag {\r\n              display: flex;\r\n              flex-wrap: wrap;\r\n              .item-tag {\r\n                max-width: 190px;\r\n                padding: 12px;\r\n                font-size: 12px;\r\n                font-family: PingFangSC-Regular, PingFang SC;\r\n                color: #214dc5;\r\n                line-height: 12px;\r\n                background: rgba(33, 77, 197, 0.1);\r\n                border-radius: 4px;\r\n                margin: 12px 12px 0 0;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n          }\r\n          &:hover {\r\n            cursor: pointer;\r\n            .item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .enterprise-data-content {\r\n      width: 960px;\r\n      margin-top: 60px;\r\n    }\r\n    .introduction-data-info {\r\n      margin-top: 10px;\r\n      .item-introduction {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        padding: 13px 13px 16px;\r\n        background: #f7f8fa;\r\n        border-radius: 4px;\r\n        .item-introduction-name {\r\n          display: flex;\r\n          align-items: center;\r\n          width: 850px;\r\n          font-size: 14px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 14px;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          .item-introduction-img {\r\n            width: 26px;\r\n            height: 32px;\r\n            margin-right: 12px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          .item-introduction-file {\r\n            width: 800px;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n          }\r\n        }\r\n        .item-introduction-btn {\r\n          .el-button {\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            color: #2f76e0;\r\n            padding: 0px 8px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .enterprise-detail-btn {\r\n      margin-top: 60px;\r\n      text-align: center;\r\n      .el-button {\r\n        width: 163px;\r\n        height: 40px;\r\n        border-radius: 4px;\r\n        border: 1px solid #21c9b8;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #21c9b8;\r\n        line-height: 16px;\r\n        &:hover {\r\n          background-color: transparent;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.enterprise-detail-container {\r\n  .enterprise-title-carousel {\r\n    .el-carousel__container {\r\n      height: 100%;\r\n      .el-carousel__arrow {\r\n        height: 48px;\r\n        width: 48px;\r\n        background: #f4f5f9;\r\n        &:hover {\r\n          background: #21c9b8;\r\n        }\r\n      }\r\n      .el-icon-arrow-left,\r\n      .el-icon-arrow-right {\r\n        font-size: 20px;\r\n      }\r\n      .el-carousel__arrow--left {\r\n        left: 0;\r\n      }\r\n      .el-carousel__arrow--right {\r\n        right: 0;\r\n      }\r\n    }\r\n    .el-carousel__indicator {\r\n      .el-carousel__button {\r\n        width: 4px;\r\n        height: 4px;\r\n        background: rgba(0, 0, 0, 0.5);\r\n        border-radius: 3px;\r\n        &:hover {\r\n          width: 24px;\r\n          height: 4px;\r\n          background: #21c9b8;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n      .el-carousel__indicator--horizontal {\r\n        padding: 12px 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}