{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\newsCenter\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\newsCenter\\index.vue", "mtime": 1750311962978}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/newsCenter", "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/newsCenter/newsCenterBanner.png\" alt=\"\" />\r\n      <div class=\"bannerTitle\">新闻中心</div>\r\n      <div class=\"bannerDesc\">即时了解行业新闻动态,助力企业高质量迅速发展</div>\r\n    </div>\r\n    <div class=\"content\">\r\n      <div class=\"content_type\">\r\n        <div\r\n          class=\"everyType\"\r\n          v-for=\"item in typeList\"\r\n          :key=\"item.dictValue\"\r\n          @click=\"getItemData(item.dictValue)\"\r\n        >\r\n          <div\r\n            class=\"title\"\r\n            :style=\"item.dictValue == flag ? 'color:#21C9B8' : ''\"\r\n          >\r\n            {{ item.dictLabel }}\r\n          </div>\r\n          <div v-show=\"item.dictValue == flag\" class=\"icon\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content_item\" v-loading=\"loading\">\r\n        <div\r\n          class=\"everyItem\"\r\n          v-for=\"item in newsList\"\r\n          :key=\"item.id\"\r\n          @click=\"goDetail(item.id)\"\r\n        >\r\n          <div class=\"item_left\" v-if=\"item.updateTime\">\r\n            <div class=\"item_year\">{{ item.updateTime.slice(0, 4) }}</div>\r\n            <div class=\"icon_year\"></div>\r\n            <div class=\"item_month\">{{ item.updateTime.slice(5, 10) }}</div>\r\n          </div>\r\n          <div class=\"item_middle\">\r\n            <div class=\"title\">\r\n              {{ item.title }}\r\n            </div>\r\n            <div class=\"desc\">\r\n              {{ item.brief }}\r\n            </div>\r\n          </div>\r\n          <div class=\"item_right\">\r\n            <img :src=\"getPicUrl(item.picUrl)\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div></div>\r\n      <div class=\"activity-page-end\">\r\n        <el-button class=\"activity-page-btn\" @click=\"goHome\">首页</el-button>\r\n        <el-pagination\r\n          v-if=\"newsList && newsList.length > 0\"\r\n          background\r\n          layout=\"prev, pager, next\"\r\n          class=\"activity-pagination\"\r\n          :page-size=\"pageSize\"\r\n          :current-page=\"pageNum\"\r\n          :total=\"total\"\r\n          @current-change=\"handleCurrentChange\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import { getActivityList } from \"@/api/purchaseSales\";\r\nimport { newsType, newsList } from \"@/api/newsCenter\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { caseList } from \"@/api/classicCase\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      loading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        caseType: \"\", // 案例类型\r\n      },\r\n      caseTypeList: [],\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      solutionTypeList: [\r\n        {\r\n          value: \"0\",\r\n          label: \"全部\",\r\n        },\r\n        {\r\n          value: \"1\",\r\n          label: \"节能减排\",\r\n        },\r\n        {\r\n          value: \"2\",\r\n          label: \"低碳认证\",\r\n        },\r\n        {\r\n          value: \"3\",\r\n          label: \"数据核算\",\r\n        },\r\n        {\r\n          value: \"4\",\r\n          label: \"中和服务\",\r\n        },\r\n        {\r\n          value: \"5\",\r\n          label: \"星碳培训\",\r\n        },\r\n        {\r\n          value: \"6\",\r\n          label: \"绿色会议\",\r\n        },\r\n        {\r\n          value: \"7\",\r\n          label: \"数据建模\",\r\n        },\r\n        {\r\n          value: \"8\",\r\n          label: \"资产管理\",\r\n        },\r\n      ],\r\n      flag: \"1\",\r\n      typeList: [\r\n        {\r\n          dictValue: \"1\",\r\n          dictLabel: \"平台动态\",\r\n        },\r\n        {\r\n          dictValue: \"2\",\r\n          dictLabel: \"行业动态\",\r\n        },\r\n        {\r\n          dictValue: \"3\",\r\n          dictLabel: \"政策法规\",\r\n        },\r\n      ],\r\n      newsList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n    // this.getDictsList(\"activity_type\", \"activityTypeList\");\r\n    // this.search();\r\n  },\r\n  methods: {\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.initData();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.initData();\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    initData() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        kind: \"0\",\r\n        typeTop: this.flag,\r\n      };\r\n      newsList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res.rows, \"----------------\");\r\n          this.newsList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n      // newsType().then((res) => {\r\n      //   if (res.code === 200) {\r\n      //     console.log(res, \"-----------------\");\r\n      //   }\r\n      // });\r\n      // getDicts(\"case_industry\").then((res) => {\r\n      //   const { code, data = [] } = res;\r\n      //   if (code === 200) {\r\n      //     this.caseTypeList = data;\r\n      //     this.getCaseList();\r\n      //   }\r\n      // });\r\n    },\r\n    getItemData(value) {\r\n      this.flag = value;\r\n      this.initData();\r\n    },\r\n    goDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/newsDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    getPicUrl(picUrl) {\r\n      try {\r\n        let data = JSON.parse(picUrl);\r\n        return data[0].url;\r\n      } catch (error) {\r\n        return picUrl;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #ffffff;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 500px;\r\n    position: relative;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    .bannerTitle {\r\n      position: absolute;\r\n      top: 161px;\r\n      left: 24%;\r\n      font-size: 50px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n    }\r\n    .bannerDesc {\r\n      position: absolute;\r\n      top: 249px;\r\n      left: 24%;\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .content {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    .content_type {\r\n      display: flex;\r\n      width: 100%;\r\n      margin-bottom: 30px;\r\n      .everyType {\r\n        width: 110px;\r\n        text-align: center;\r\n        margin-left: 66px;\r\n        cursor: pointer;\r\n        .title {\r\n          font-size: 18px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #333333;\r\n        }\r\n        .icon {\r\n          width: 110px;\r\n          height: 4px;\r\n          background: #21c9b8;\r\n          margin-top: 30px;\r\n        }\r\n      }\r\n      .everyType:nth-child(1) {\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n    .content_item {\r\n      width: 100%;\r\n      .everyItem {\r\n        display: flex;\r\n        width: 100%;\r\n        height: 230px;\r\n        background: #ffffff;\r\n        box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n        padding: 45px 50px;\r\n        margin-top: 30px;\r\n        cursor: pointer;\r\n        .item_left {\r\n          margin-top: 43px;\r\n          width: 53px;\r\n          .item_year {\r\n            font-size: 24px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            color: #222222;\r\n          }\r\n          .icon_year {\r\n            width: 58px;\r\n            height: 2px;\r\n            background: #21c9b8;\r\n            margin-top: 2px;\r\n          }\r\n          .item_month {\r\n            font-size: 18px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            color: #222222;\r\n            margin-top: 5px;\r\n          }\r\n        }\r\n        .item_middle {\r\n          width: 710px;\r\n          margin-left: 40px;\r\n          .title {\r\n            font-size: 18px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            color: #222222;\r\n            margin-top: 31px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 1;\r\n            text-overflow: ellipsis;\r\n            word-wrap: break-word;\r\n          }\r\n          .desc {\r\n            font-size: 16px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            color: #65676a;\r\n            margin-top: 17px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            text-overflow: ellipsis;\r\n            word-wrap: break-word;\r\n          }\r\n        }\r\n        .item_right {\r\n          width: 200px;\r\n          height: 140px;\r\n          margin-left: 85px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n      .everyItem:hover {\r\n        box-shadow: 0px 4px 20px 0px rgba(58, 180, 118, 0.3);\r\n        .item_year {\r\n          color: #21c9b8;\r\n        }\r\n        .title {\r\n          color: #21c9b8;\r\n        }\r\n      }\r\n      .everyItem:nth-child(1) {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n    .activity-page-end {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin: 0 auto;\r\n      padding: 24px 0 60px;\r\n      .activity-page-btn {\r\n        width: 82px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    padding: 8% 0;\r\n    background: #fff;\r\n    margin-top: 25px;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}