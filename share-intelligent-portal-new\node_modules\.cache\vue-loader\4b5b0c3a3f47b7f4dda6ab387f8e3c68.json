{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\publishEquipment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\publishEquipment\\index.vue", "mtime": 1750311963078}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/publishEquipment", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row\r\n      :gutter=\"20\"\r\n      style=\"background: linear-gradient(to right, #e1f7f0, #f4fcfa)\"\r\n    >\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"formStyle\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"设备名称\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" placeholder=\"请输入设备名称\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"设备分类\" prop=\"category\">\r\n              <el-select\r\n                v-model=\"form.category\"\r\n                placeholder=\"请选择设备分类\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in deviceMenuList\"\r\n                  :key=\"dict.dictLabel\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"设备规格\" prop=\"specifications\">\r\n              <el-input\r\n                v-model=\"form.specifications\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"所属单位/位置\" prop=\"location\">\r\n              <el-input\r\n                v-model=\"form.location\"\r\n                placeholder=\"请输入所属单位/位置\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"设备用途描述\" prop=\"description\">\r\n              <el-input\r\n                v-model=\"form.description\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"设备图片\" prop=\"images\">\r\n              <ImageUpload v-model=\"form.images\" resultType=\"string\" :limit=\"1\" :multiple=\"false\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"租用模式\" prop=\"rentMode\">\r\n              <el-input v-model=\"form.rentMode\" placeholder=\"请输入租用模式\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"租用价格\" prop=\"rentPrice\">\r\n              <el-input v-model=\"form.rentPrice\" placeholder=\"请输入租用价格\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"压力(MPa)\" prop=\"pressure\">\r\n              <el-input v-model=\"form.pressure\" placeholder=\"请输入压力(MPa)\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"温度\" prop=\"temperature\">\r\n              <el-input v-model=\"form.temperature\" placeholder=\"请输入温度\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"尺寸\" prop=\"dimension\">\r\n              <el-input v-model=\"form.dimension\" placeholder=\"请输入尺寸\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"规格型号\" prop=\"modelNumber\">\r\n              <el-input\r\n                v-model=\"form.modelNumber\"\r\n                placeholder=\"请输入规格型号\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button type=\"primary\" @click=\"onSubmit\">{{\r\n                form.id ? \"保存\" : \"发布\"\r\n              }}</el-button>\r\n              <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\"\r\n                >取消</el-button\r\n              >\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport {\r\n  addDeviceInfo,\r\n  updateDeviceInfo,\r\n  deviceDetailData,\r\n} from \"@/api/manufacturingSharing\";\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      form: {\r\n        id: null,\r\n        name: null,\r\n        category: null,\r\n        specifications: null,\r\n        location: null,\r\n        description: null,\r\n        images: null,\r\n        technicalParams: null,\r\n        rentMode: null,\r\n        rentPrice: null,\r\n        createTime: null,\r\n        updateTime: null,\r\n        checkStatus: null,\r\n        createBy: null,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        name: [\r\n          { required: true, message: \"设备名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        category: [\r\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" },\r\n        ],\r\n        location: [\r\n          { required: true, message: \"所属单位/位置不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      deviceMenuList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getDicts();\r\n    if (this.$route.query.id) {\r\n      this.getDetail();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询字典数据列表 */\r\n    getDicts() {\r\n      let params = { dictType: \"device_share_type\" };\r\n      listData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.deviceMenuList = res.rows;\r\n        }\r\n      });\r\n    },\r\n    getDetail() {\r\n      deviceDetailData(this.$route.query.id).then((res) => {\r\n        if (res.code == 200) {\r\n          this.form = res.data;\r\n        }\r\n      });\r\n    },\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateDeviceInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.$router.go(-1);\r\n            });\r\n          } else {\r\n            this.form.checkStatus = 0;\r\n            let userinfo = JSON.parse(\r\n              window.sessionStorage.getItem(\"userinfo\")\r\n            );\r\n            this.form.createBy = userinfo.memberPhone;\r\n            addDeviceInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"发布成功,请等待审核\");\r\n              this.$router.go(-1);\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.formStyle {\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  .footer-submit {\r\n    text-align: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}