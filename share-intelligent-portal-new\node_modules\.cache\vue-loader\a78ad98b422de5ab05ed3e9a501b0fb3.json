{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\verificationCode\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\verificationCode\\index.vue", "mtime": 1750311962830}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/verificationCode", "sourcesContent": ["<template>\r\n  <div class=\"bc-verification-code\">\r\n    <el-input\r\n      ref=\"input\"\r\n      v-model=\"hValue\"\r\n      autocomplete=\"off\"\r\n      class=\"bc-verification-input\"\r\n      :size=\"size\"\r\n      :maxlength=\"maxlength\"\r\n      :placeholder=\"placeholder\"\r\n    >\r\n      <img\r\n        slot=\"prefix\"\r\n        src=\"../../assets/login/mailIcon.png\"\r\n        alt=\"\"\r\n        class=\"input-icon\"\r\n      />\r\n      <el-button\r\n        slot=\"suffix\"\r\n        type=\"text\"\r\n        class=\"sendCode\"\r\n        :size=\"size\"\r\n        :disabled=\"msgKey\"\r\n        :loading=\"sendLoading\"\r\n        @click=\"handleSendCode\"\r\n      >\r\n        {{ msgKey ? `${msgTime} S后重新发送` : \"发送验证码\" }}\r\n      </el-button>\r\n    </el-input>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCommonCode } from \"@/api/login\";\r\n\r\nexport default {\r\n  props: {\r\n    value: String,\r\n    mobile: {\r\n      type: [String, Object],\r\n      default: \"\",\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: \"请输入验证码\",\r\n    },\r\n    size: String,\r\n    beforeSendCode: Function,\r\n    sendParams: {\r\n      type: Array,\r\n      default() {\r\n        return [];\r\n      },\r\n    },\r\n    showIcon: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    maxlength: {\r\n      type: Number,\r\n      default: 6,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      msgText: \"\",\r\n      msgTime: \"\",\r\n      msgKey: false,\r\n      sendLoading: false,\r\n    };\r\n  },\r\n  computed: {\r\n    hValue: {\r\n      get() {\r\n        return this.value;\r\n      },\r\n      set(val) {\r\n        this.$emit(\"input\", val);\r\n      },\r\n    },\r\n    config() {\r\n      return {\r\n        MSGINIT: \"发送验证码\",\r\n        MSGSCUCCESS: \"秒后重发\",\r\n        MSGTIME: 60,\r\n      };\r\n    },\r\n  },\r\n  created() {\r\n    this.msgText = this.config.MSGINIT;\r\n    this.msgTime = this.config.MSGTIME;\r\n  },\r\n  methods: {\r\n    handleSendCode() {\r\n      this.hValue = \"\";\r\n      if (this.beforeSendCode) {\r\n        let result = this.beforeSendCode();\r\n        if (result === false) {\r\n          return;\r\n        }\r\n        if (result !== true) {\r\n          Promise.resolve(result).then(() => {\r\n            this.sendCode();\r\n          });\r\n        }\r\n      } else {\r\n        this.sendCode();\r\n      }\r\n    },\r\n    sendCode() {\r\n      if (!this.mobile.phone) {\r\n        this.$message.warning(\"请输入账号\");\r\n        return;\r\n      }\r\n      this.sendLoading = true;\r\n      this.msgText = \"发送中\";\r\n      let params = [];\r\n      if (this.mobile) {\r\n        params.push(this.mobile);\r\n      }\r\n      if (this.sendParams) {\r\n        params = params.concat(this.sendParams);\r\n      }\r\n      getCommonCode({ telphone: this.mobile.phone })\r\n        .then((res) => {\r\n          if (res.code !== 200) {\r\n            this.$message.error(res.msg);\r\n            this.sendLoading = false;\r\n            return;\r\n          }\r\n          this.$emit(\"after-send\", res.data || {});\r\n          this.sendLoading = false;\r\n          this.msgText = this.msgTime + this.config.MSGSCUCCESS;\r\n          this.msgKey = true;\r\n          this.$refs.input.focus();\r\n          const time = setInterval(() => {\r\n            this.msgTime--;\r\n            this.msgText = this.msgTime + this.config.MSGSCUCCESS;\r\n            if (this.msgTime === 0) {\r\n              this.msgTime = this.config.MSGTIME;\r\n              this.msgText = this.config.MSGINIT;\r\n              this.msgKey = false;\r\n              clearInterval(time);\r\n            }\r\n          }, 1000);\r\n        })\r\n        .catch(() => {\r\n          this.sendLoading = false;\r\n          this.msgText = this.config.MSGINIT;\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.bc-verification-code {\r\n  display: flex;\r\n  .bc-verification-input {\r\n    flex: 1;\r\n    .el-input__inner {\r\n      // width: 400px;\r\n      height: 40px;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 14px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 14px;\r\n      padding-left: 40px;\r\n    }\r\n    .input-icon {\r\n      width: 16px;\r\n      height: 16px;\r\n      margin: 12px;\r\n    }\r\n  }\r\n  .el-button {\r\n    margin-left: 10px;\r\n    height: 40px !important;\r\n    line-height: 40px;\r\n    padding: 0;\r\n    float: right;\r\n  }\r\n  .sendCode {\r\n    margin-right: 12px;\r\n    text-decoration: inherit;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    color: #21c9b8;\r\n    line-height: 16px;\r\n  }\r\n  .el-button.is-disabled {\r\n    color: #cfcfcf;\r\n  }\r\n}\r\n</style>\r\n"]}]}