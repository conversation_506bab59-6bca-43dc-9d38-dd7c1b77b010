{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\appliMarket\\index.vue?vue&type=style&index=1&id=318ef293&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\appliMarket\\index.vue", "mtime": 1750311962921}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFjdGl2aXR5LWNvbnRhaW5lciB7DQogIC5hY3Rpdml0eS1zZWFyY2gtaW5wdXQgew0KICAgIC5lbC1pbnB1dF9faW5uZXIgew0KICAgICAgaGVpZ2h0OiA1NHB4Ow0KICAgICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDI3cHggMCAwIDI3cHg7DQogICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtUmVndWxhciwgUGluZ0ZhbmcgU0M7DQogICAgICBsaW5lLWhlaWdodDogMjRweDsNCiAgICAgIHBhZGRpbmctbGVmdDogMzBweDsNCiAgICB9DQogICAgLmVsLWlucHV0LWdyb3VwX19hcHBlbmQgew0KICAgICAgYm9yZGVyLXJhZGl1czogMHB4IDEwMHB4IDEwMHB4IDBweDsNCiAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1SZWd1bGFyLCBQaW5nRmFuZyBTQzsNCiAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgfQ0KICB9DQogIC5hY3Rpdml0eS1zZWFyY2gtbGluZSB7DQogICAgLmVsLWZvcm0taXRlbV9fbGFiZWwgew0KICAgICAgd2lkdGg6IDg4cHg7DQogICAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1NZWRpdW0sIFBpbmdGYW5nIFNDOw0KICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgIGNvbG9yOiAjOTk5Ow0KICAgICAgcGFkZGluZy1yaWdodDogMzJweDsNCiAgICAgIHRleHQtYWxpZ246IGxlZnQ7DQogICAgfQ0KICAgIC5hY3Rpdml0eS1zZWFyY2gtcmFkaW8gew0KICAgICAgd2lkdGg6IDEwNTBweDsNCiAgICAgIG1hcmdpbi10b3A6IDExcHg7DQogICAgICAuZWwtcmFkaW8tYnV0dG9uIHsNCiAgICAgICAgcGFkZGluZy1ib3R0b206IDIwcHg7DQogICAgICAgIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsNCiAgICAgICAgICBib3JkZXI6IG5vbmU7DQogICAgICAgICAgcGFkZGluZzogMCAzMnB4IDAgMDsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiBub25lOw0KICAgICAgICAgICY6aG92ZXIgew0KICAgICAgICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgICYuaXMtYWN0aXZlIHsNCiAgICAgICAgICAuZWwtcmFkaW8tYnV0dG9uX19pbm5lciB7DQogICAgICAgICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6IG5vbmU7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC5lbC1yYWRpby1idXR0b25fX29yaWctcmFkaW86Y2hlY2tlZCB7DQogICAgICAgICAgJiArIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsNCiAgICAgICAgICAgIGJveC1zaGFkb3c6IHVuc2V0Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICAuYWN0aXZpdHktcGFnZS1lbmQgew0KICAgIC5hY3Rpdml0eS1wYWdpbmF0aW9uIHsNCiAgICAgIC5idG4tcHJldiwNCiAgICAgIC5idG4tbmV4dCwNCiAgICAgIC5idG4tcXVpY2twcmV2IHsNCiAgICAgICAgd2lkdGg6IDMycHg7DQogICAgICAgIGhlaWdodDogMzJweDsNCiAgICAgICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgIH0NCiAgICAgICYuaXMtYmFja2dyb3VuZCB7DQogICAgICAgIC5lbC1wYWdlciB7DQogICAgICAgICAgLm51bWJlciB7DQogICAgICAgICAgICB3aWR0aDogMzJweDsNCiAgICAgICAgICAgIGhlaWdodDogMzJweDsNCiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDMycHg7DQogICAgICAgICAgICAmLmFjdGl2ZSB7DQogICAgICAgICAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMyMWM5Yjg7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAggBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/appliMarket", "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/appliMarket/appliMarketBanner.png\" alt=\"\" />\r\n      <div class=\"bannerTitle\">应用市场</div>\r\n      <div class=\"bannerDesc\">\r\n        助力企业数字化低碳转型升级，提供成熟完善的数字化应用产品\r\n      </div>\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.keywords\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"appliType\">\r\n        <div\r\n          class=\"everyType\"\r\n          v-for=\"(item, index) in appliTypeData\"\r\n          :key=\"item.dictValue\"\r\n          @click=\"getappliData(item.dictLabel)\"\r\n        >\r\n          <div class=\"everyImg\">\r\n            <img :src=\"appliTypeImgList[index].url\" alt=\"\" />\r\n          </div>\r\n          <div class=\"everyTitle\">{{ item.dictLabel }}</div>\r\n          <div class=\"everyIcon\" v-show=\"flag === item.dictLabel\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"appliContent\">\r\n        <div style=\"display: flex; flex-wrap: wrap\">\r\n          <div\r\n            v-loading=\"loading\"\r\n            class=\"everyContent\"\r\n            v-for=\"item in appliDataList\"\r\n            :key=\"item.id\"\r\n            @click=\"goPurchaseapps(item.id)\"\r\n          >\r\n            <div class=\"title\">{{ item.appName }}</div>\r\n            <div class=\"desc\">\r\n              {{ item.briefInto }}\r\n            </div>\r\n            <div\r\n              class=\"tagStyle\"\r\n              v-if=\"item.appLabel && item.appLabel.length > 0\"\r\n            >\r\n              <div\r\n                class=\"everyTag\"\r\n                v-for=\"itemTag in item.appLabel.split(',')\"\r\n                :key=\"itemTag\"\r\n              >\r\n                {{ itemTag }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"activity-page-end\">\r\n          <el-button class=\"activity-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"appliDataList && appliDataList.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { appliType, appliList } from \"@/api/appliMarket\";\r\n// import { getDicts } from \"@/api/system/dict/data\";\r\n// import { caseList } from \"@/api/classicCase\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      loading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        caseType: \"\", // 案例类型\r\n      },\r\n      caseTypeList: [],\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 12,\r\n      total: 0,\r\n      flag: \"全部\",\r\n      appliTypeData: [\r\n        {\r\n          dictValue: \"0\",\r\n          dictLabel: \"全部\",\r\n        },\r\n        {\r\n          dictValue: \"1\",\r\n          dictLabel: \"研发设计\",\r\n        },\r\n        {\r\n          dictValue: \"2\",\r\n          dictLabel: \"生产制造\",\r\n        },\r\n        {\r\n          dictValue: \"3\",\r\n          dictLabel: \"运营管理\",\r\n        },\r\n        {\r\n          dictValue: \"4\",\r\n          dictLabel: \"质量管控\",\r\n        },\r\n        {\r\n          dictValue: \"5\",\r\n          dictLabel: \"仓储物流\",\r\n        },\r\n        {\r\n          dictValue: \"6\",\r\n          dictLabel: \"安全生产\",\r\n        },\r\n        {\r\n          dictValue: \"7\",\r\n          dictLabel: \"节能减排\",\r\n        },\r\n        {\r\n          dictValue: \"8\",\r\n          dictLabel: \"运维服务\",\r\n        },\r\n      ],\r\n      appliTypeImgList: [\r\n        {\r\n          url: require(\"../../assets/appliMarket/type1.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type2.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type3.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type4.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type5.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type6.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type7.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type8.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type9.png\"),\r\n        },\r\n      ],\r\n      appliDataList: [\r\n        {\r\n          id: 1,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 5,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 6,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 7,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 8,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      appliType().then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res.data, \"------------\");\r\n          this.getListData();\r\n        }\r\n      });\r\n    },\r\n    getListData() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        appName: this.form.keywords,\r\n        appCategory: this.flag == \"全部\" ? undefined : this.flag,\r\n        appState: 2,\r\n      };\r\n      appliList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res, \"7777777777777777777\");\r\n          this.appliDataList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getListData();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.getListData();\r\n    },\r\n    goCaseDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/caseDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    getappliData(value) {\r\n      this.flag = value;\r\n      this.getListData();\r\n    },\r\n    goPurchaseapps(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/purchaseapp\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #ffffff;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 500px;\r\n    position: relative;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    .bannerTitle {\r\n      position: absolute;\r\n      top: 161px;\r\n      left: 24%;\r\n      font-size: 50px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n    }\r\n    .bannerDesc {\r\n      position: absolute;\r\n      top: 249px;\r\n      left: 24%;\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    // padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      margin-top: 40px;\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .appliType {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    .everyType {\r\n      width: 102px;\r\n      // height: 160px;\r\n      text-align: center;\r\n      cursor: pointer;\r\n      .everyImg {\r\n        width: 63px;\r\n        height: 78px;\r\n        margin-left: calc((100% - 63px) / 2);\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .everyTitle {\r\n        font-size: 18px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #979797;\r\n        margin-top: 10px;\r\n      }\r\n      .everyIcon {\r\n        width: 63px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n        margin-top: 10px;\r\n        margin-left: calc((100% - 63px) / 2);\r\n      }\r\n    }\r\n  }\r\n  .appliContent {\r\n    width: 1200px;\r\n    // height: 500px;\r\n    margin: 0 auto;\r\n    .everyContent {\r\n      width: 280px;\r\n      height: 220px;\r\n      background: #ffffff;\r\n      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n      border-radius: 4px;\r\n      padding: 30px;\r\n      cursor: pointer;\r\n      margin-left: 24px;\r\n      margin-top: 20px;\r\n      .title {\r\n        font-size: 20px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #222222;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 1;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n      .desc {\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #65676a;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n        margin-top: 26px;\r\n      }\r\n      .tagStyle {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-top: 30px;\r\n        .everyTag {\r\n          width: 90px;\r\n          height: 42px;\r\n          border: 1px solid #21c9b8;\r\n          border-radius: 21px;\r\n          font-size: 16px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #21c9b8;\r\n          text-align: center;\r\n          line-height: 42px;\r\n          overflow: hidden;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n          -webkit-line-clamp: 1;\r\n          text-overflow: ellipsis;\r\n          word-wrap: break-word;\r\n        }\r\n      }\r\n    }\r\n    .everyContent:hover {\r\n      box-shadow: 0 4px 16px 0 rgba(38, 74, 116, 0.2);\r\n      .title {\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    .everyContent:nth-child(4n + 1) {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    padding: 8% 0;\r\n    background: #fff;\r\n    margin-top: 25px;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    padding: 24px 0 60px;\r\n    .activity-page-btn {\r\n      width: 82px;\r\n      height: 32px;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      border: 1px solid #d9d9d9;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #333;\r\n      line-height: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}