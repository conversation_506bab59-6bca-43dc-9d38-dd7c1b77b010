{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\noninductive\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\noninductive\\detail\\index.vue", "mtime": 1750311963064}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpsZXQgaWQgPSAwOw0KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsNCiAgZ2V0Tm9uaW5kdWN0aXZlRGV0YWlsLA0KICBlZGl0Tm9uaW5kdWN0aXZlLA0KfSBmcm9tICJAL2FwaS9zeXN0ZW0vbm9uaW5kdWN0aXZlIjsNCmltcG9ydCB7IGdldERpY3RzLCBnZXRTZWNvbmREaWN0cyB9IGZyb20gIkAvYXBpL3N5c3RlbS9kaWN0L2RhdGEuanMiOw0KaW1wb3J0IHsgdXBsb2FkVXJsIH0gZnJvbSAiQC9hcGkvb3NzIjsNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiTm90aWNlIiwNCiAgZGljdHM6IFsiYWZmaWxpYXRlZF91bml0IiwgImNhcGl0YWxfc291cmNlIiwgImFmZmlsaWF0ZWRfc3RyZWV0Il0sDQogIGNvbXBvbmVudHM6IHsgVXNlck1lbnUgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgaXNEZXRhaWw6IHRydWUsDQogICAgICBhY3Rpb25Vcmw6IHVwbG9hZFVybCgpLA0KICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpIH0sDQogICAgICBpbmZvOiB7fSwNCiAgICAgIGZvcm06IHt9LA0KICAgICAgYWNjb3VudExpY2VuY2VMaXN0OiBbXSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgYWZmaWxpYXRlZFVuaXRDb2RlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJgOWxnuWNleS9jeS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBjb21wYW55TmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlhazlj7jlkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29tcGFueUNyZWRpdENvZGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5LyB5Lia57uf5LiA5Luj56CB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvbnRhY3RzTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLogZTns7vkurrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY2FwaXRhbFNvdXJjZUNvZGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6LWE6YeR5p2l5rqQ5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGFmZmlsaWF0ZWRTdHJlZXRDb2RlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaJgOWxnuihl+mBk+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBiYW5rTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlvIDmiLfpk7booYzkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29udGFjdHNQaG9uZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLogZTns7vnlLXor53kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgaXRlbVR5cGVDb2RlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumAieaLqemhueebruS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBjb21wYW55QWRkcmVzczogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkvIHkuJrms6jlhozlnLDlnYDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgYmFua0NvZGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6ZO26KGM6LSm5Y+35LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGFwcGxpY2F0aW9uOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIueUs+ivt+S5puS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBhY2NvdW50TGljZW5jZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlLPor7fkuabkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgICBwcm9qZWN0UHJvcHM6IHsNCiAgICAgICAgbGF6eTogdHJ1ZSwNCiAgICAgICAgbGF6eUxvYWQobm9kZSwgcmVzb2x2ZSkgew0KICAgICAgICAgIGxldCByZXMgPSBbXTsNCiAgICAgICAgICBpZiAobm9kZS5sZXZlbCA9PSAwKSB7DQogICAgICAgICAgICBnZXREaWN0cygiaXRlbV90eXBlIikudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKChlbGVtZW50KSA9PiB7DQogICAgICAgICAgICAgICAgcmVzLnB1c2goew0KICAgICAgICAgICAgICAgICAgbGFiZWw6IGVsZW1lbnQuZGljdExhYmVsLA0KICAgICAgICAgICAgICAgICAgdmFsdWU6IGVsZW1lbnQuZGljdFZhbHVlLA0KICAgICAgICAgICAgICAgICAgZGljdENvZGU6IGVsZW1lbnQuZGljdENvZGUsDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgICBjb25zdCBub2RlcyA9IHJlczsNCiAgICAgICAgICAgICAgcmVzb2x2ZShub2Rlcyk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgZ2V0U2Vjb25kRGljdHMoeyBwYXJlbnRDb2RlOiBub2RlLmRhdGEuZGljdENvZGUgfSkudGhlbigNCiAgICAgICAgICAgICAgKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgcmVzcG9uc2UuZGF0YS5mb3JFYWNoKChlbGVtZW50KSA9PiB7DQogICAgICAgICAgICAgICAgICByZXMucHVzaCh7DQogICAgICAgICAgICAgICAgICAgIGxhYmVsOiBlbGVtZW50LmRpY3RMYWJlbCwNCiAgICAgICAgICAgICAgICAgICAgdmFsdWU6IGVsZW1lbnQuZGljdFZhbHVlLA0KICAgICAgICAgICAgICAgICAgICBsZWFmOiB0cnVlLA0KICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgY29uc3Qgbm9kZXMgPSByZXM7DQogICAgICAgICAgICAgICAgcmVzb2x2ZShub2Rlcyk7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICk7DQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIGNvbnN0IG5vZGVzID0gQXJyYXkuZnJvbSh7IGxlbmd0aDogbGV2ZWwgKyAxIH0pLm1hcCgoaXRlbSkgPT4gKHsNCiAgICAgICAgICAvLyAgIHZhbHVlOiArK2lkLA0KICAgICAgICAgIC8vICAgbGFiZWw6IGDpgInpobkke2lkfWAsDQogICAgICAgICAgLy8gICBsZWFmOiBsZXZlbCA+PSAyLA0KICAgICAgICAgIC8vIH0pKTsNCiAgICAgICAgICAvLyDpgJrov4fosIPnlKhyZXNvbHZl5bCG5a2Q6IqC54K55pWw5o2u6L+U5Zue77yM6YCa55+l57uE5Lu25pWw5o2u5Yqg6L295a6M5oiQDQogICAgICAgIH0sDQogICAgICB9LA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXREZXRhaWwoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGl0ZW1UeXBlQ2hhbmdlZCh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5pdGVtVHlwZUZpcnN0Q29kZSA9IHZhbFswXTsNCiAgICAgIHRoaXMuZm9ybS5pdGVtVHlwZVNlY29uZENvZGUgPSB2YWxbMV07DQogICAgfSwNCiAgICBnZXREZXRhaWwoKSB7DQogICAgICBsZXQgdXNlcklkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7DQogICAgICBnZXROb25pbmR1Y3RpdmVEZXRhaWwodXNlcklkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLmluZm8gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOeUs+ivt+S5puOAgeW8gOaIt+iuuOWPr+ivgemihOiniA0KICAgIGhhbmRsZVByZXZpZXcoZmlsZSkgew0KICAgICAgd2luZG93Lm9wZW4oZmlsZS51cmwpOw0KICAgIH0sDQogICAgLy8g5byA5oi36K645Y+v6K+B5LiK5Lyg5pWw6YeP6ZmQ5Yi2DQogICAgaGFuZGxlRXhjZWVkTGljZW5jZShmaWxlcywgZmlsZUxpc3QpIHsNCiAgICAgIGxldCBudW0gPSBmaWxlcy5sZW5ndGggKyBmaWxlTGlzdC5sZW5ndGg7DQogICAgICBpZiAobnVtID49IDEpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5LiK5Lyg5pWw6YeP6LaF6L+H5LiK6ZmQIik7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGdvQmFjaygpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7DQogICAgfSwNCg0KICAgIGNoYW5nZU1vZGUoKSB7DQogICAgICBpZiAodGhpcy5pc0RldGFpbCkgew0KICAgICAgICB0aGlzLmlzRGV0YWlsID0gZmFsc2U7DQogICAgICAgIHRoaXMuZm9ybSA9IHRoaXMuaW5mbzsNCiAgICAgICAgdGhpcy5mb3JtLml0ZW1UeXBlQ29kZSA9IFsNCiAgICAgICAgICB0aGlzLmluZm8uaXRlbVR5cGVGaXJzdENvZGUsDQogICAgICAgICAgdGhpcy5pbmZvLml0ZW1UeXBlU2Vjb25kQ29kZSwNCiAgICAgICAgXTsNCiAgICAgICAgdGhpcy5hY2NvdW50TGljZW5jZUxpc3QgPSB0aGlzLmluZm8uYWNjb3VudExpY2VuY2UNCiAgICAgICAgICA/IFsNCiAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgIG5hbWU6IHRoaXMuaW5mby5hY2NvdW50TGljZW5jZU5hbWUsDQogICAgICAgICAgICAgICAgdXJsOiB0aGlzLmluZm8uYWNjb3VudExpY2VuY2UsDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICBdDQogICAgICAgICAgOiBbXTsNCiAgICAgICAgdGhpcy5hcHBsaWNhdGlvbkxpc3QgPSB0aGlzLmluZm8uYXBwbGljYXRpb24NCiAgICAgICAgICA/IFsNCiAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgIG5hbWU6IHRoaXMuaW5mby5hcHBsaWNhdGlvbk5hbWUsDQogICAgICAgICAgICAgICAgdXJsOiB0aGlzLmluZm8uYXBwbGljYXRpb24sDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICBdDQogICAgICAgICAgOiBbXTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuaXNEZXRhaWwgPSB0cnVlOw0KICAgICAgICB0aGlzLmZvcm0gPSB7fTsNCiAgICAgICAgdGhpcy5nZXREZXRhaWwoKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUZpbGVQcmV2aWV3KGZpbGUpIHsNCiAgICAgIHdpbmRvdy5vcGVuKGZpbGUpOw0KICAgIH0sDQogICAgc3VibWl0Rm9ybSh0eXBlKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGVkaXROb25pbmR1Y3RpdmUoeyAuLi50aGlzLmZvcm0sIGlzU3VibWl0OiB0eXBlIH0pLnRoZW4oDQogICAgICAgICAgICAocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMuY2hhbmdlTW9kZSgpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlQXBwbGljYXRpb25SZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMuZm9ybS5hcHBsaWNhdGlvbiA9ICIiOw0KICAgIH0sDQogICAgaGFuZGxlQXBwbGljYXRpb25TdWNjZXNzKHJlcywgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIC8v5q2k5aSE5YaZ5LiK5Lygb3Nz5oiQ5Yqf5LmL5ZCO55qE6YC76L6RDQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIHRoaXMuZm9ybS5hcHBsaWNhdGlvbiA9IHJlcy5kYXRhLnVybDsNCiAgICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uTmFtZSA9IHJlcy5kYXRhLm5hbWU7DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVBY2NvdW50UmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLmZvcm0uYWNjb3VudExpY2VuY2UgPSAiIjsNCiAgICB9LA0KICAgIGhhbmRsZUFjY291bnRTdWNjZXNzKHJlcywgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIC8v5q2k5aSE5YaZ5LiK5Lygb3Nz5oiQ5Yqf5LmL5ZCO55qE6YC76L6RDQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIHRoaXMuZm9ybS5hY2NvdW50TGljZW5jZSA9IHJlcy5kYXRhLnVybDsNCiAgICAgICAgdGhpcy5mb3JtLmFjY291bnRMaWNlbmNlTmFtZSA9IHJlcy5kYXRhLm5hbWU7DQogICAgICB9DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0UA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/noninductive/detail", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-03-24 08:55:04\r\n * @Description:\r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"notice-record-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"18\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">兑现详情</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                基本信息\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 所属单位 </template>\r\n                  {{ info.affiliatedUnitName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 资金来源 </template>\r\n                  {{ info.capitalSourceName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 项目名称</template>\r\n                  {{\r\n                    info.itemTypeFirstName || \"\" + info.itemTypeSecondName || \"\"\r\n                  }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 公司名称</template>\r\n                  {{ info.companyName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 所属街道</template>\r\n                  {{ info.affiliatedStreetName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 企业注册地址</template>\r\n                  {{ info.companyAddress }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 企业统一代码 </template>\r\n                  {{ info.companyCreditCode }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 开户银行</template>\r\n                  {{ info.bankName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 银行账号</template>\r\n                  {{ info.bankCode }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系人</template>\r\n                  {{ info.contactsName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系电话 </template>\r\n                  {{ info.contactsPhone }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 申请书 </template>\r\n                  <div v-if=\"!info.application\">--</div>\r\n                  <a\r\n                    v-else\r\n                    class=\"file-class\"\r\n                    @click=\"handleFilePreview(info.application)\"\r\n                  >\r\n                    <el-image\r\n                      style=\"width: 14px; height: 17px\"\r\n                      :src=\"require('@/assets/user/file_pdf.png')\"\r\n                    ></el-image>\r\n                    {{ info.applicationName }}\r\n                    <div class=\"previwe-class\">立即查看</div>\r\n                  </a>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 开户许可证 </template>\r\n                  <div v-if=\"!info.accountLicence\">--</div>\r\n\r\n                  <a\r\n                    class=\"file-class\"\r\n                    v-else\r\n                    @click=\"handleFilePreview(info.accountLicence)\"\r\n                  >\r\n                    <el-image\r\n                      style=\"width: 14px; height: 17px\"\r\n                      :src=\"require('@/assets/user/file_pdf.png')\"\r\n                    ></el-image>\r\n                    {{ info.accountLicenceName }}\r\n                    <div class=\"previwe-class\">立即查看</div>\r\n                  </a>\r\n                  <!-- <el-upload\r\n                    v-if=\"info.accountLicence\"\r\n                    class=\"upload-demo\"\r\n                    :on-preview=\"handleFilePreview(info.accountLicence)\"\r\n                  >\r\n                  </el-upload> -->\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <el-image\r\n                class=\"status_approving\"\r\n                v-if=\"info.status === '1'\"\r\n                style=\"width: 120px; height: 102px\"\r\n                :src=\"require('@/assets/user/status_approving.png')\"\r\n              ></el-image>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"goBack\">返回</el-button>\r\n                <el-button\r\n                  v-if=\"info.status == '0'\"\r\n                  type=\"danger\"\r\n                  @click=\"changeMode\"\r\n                  >编辑</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n            <div class=\"edit-page\" v-else>\r\n              <el-form\r\n                ref=\"form\"\r\n                :model=\"form\"\r\n                :rules=\"rules\"\r\n                label-width=\"120px\"\r\n              >\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"所属单位\" prop=\"affiliatedUnitCode\">\r\n                      <el-select\r\n                        v-model=\"form.affiliatedUnitCode\"\r\n                        placeholder=\"请选择所属单位\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.affiliated_unit\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n                      <el-input\r\n                        v-model=\"form.companyName\"\r\n                        placeholder=\"公司名称\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"企业统一代码\" prop=\"companyCreditCode\">\r\n                      <el-input\r\n                        v-model=\"form.companyCreditCode\"\r\n                        placeholder=\"请输入企业统一代码\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"联系人\" prop=\"contactsName\">\r\n                      <el-input\r\n                        v-model=\"form.contactsName\"\r\n                        placeholder=\"请输入联系人\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"资金来源\" prop=\"capitalSourceCode\">\r\n                      <el-select\r\n                        v-model=\"form.capitalSourceCode\"\r\n                        placeholder=\"请选择资金来源\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.capital_source\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"所属街道\" prop=\"affiliatedStreetCode\">\r\n                      <el-select\r\n                        v-model=\"form.affiliatedStreetCode\"\r\n                        placeholder=\"请选择所属街道\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.affiliated_street\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"开户银行\" prop=\"bankName\">\r\n                      <el-input\r\n                        v-model=\"form.bankName\"\r\n                        placeholder=\"请选择开户银行\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"联系电话\" prop=\"contactsPhone\">\r\n                      <el-input\r\n                        v-model=\"form.contactsPhone\"\r\n                        placeholder=\"请选择联系电话\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"选择项目\" prop=\"itemTypeCode\">\r\n                      <el-cascader\r\n                        ref=\"test\"\r\n                        :props=\"projectProps\"\r\n                        v-model=\"form.itemTypeCode\"\r\n                        placeholder=\"请选择项目\"\r\n                        @change=\"itemTypeChanged\"\r\n                      ></el-cascader>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"企业注册地址\" prop=\"companyAddress\">\r\n                      <el-input\r\n                        v-model=\"form.companyAddress\"\r\n                        placeholder=\"请输入企业注册地址\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"银行账号\" prop=\"bankCode\">\r\n                      <el-input\r\n                        v-model=\"form.bankCode\"\r\n                        placeholder=\"输入请银行账号\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"申请书上传\" prop=\"application\">\r\n                      <el-upload\r\n                        :headers=\"headers\"\r\n                        :action=\"actionUrl\"\r\n                        accept=\".pdf\"\r\n                        :file-list=\"applicationList\"\r\n                        :on-remove=\"handleApplicationRemove\"\r\n                        :on-success=\"handleApplicationSuccess\"\r\n                        :on-exceed=\"handleExceedLicence\"\r\n                        :on-preview=\"handlePreview\"\r\n                        :limit=\"1\"\r\n                      >\r\n                        <div>\r\n                          <el-button\r\n                            size=\"small\"\r\n                            type=\"primary\"\r\n                            icon=\"el-icon-upload2\"\r\n                            >上传文件</el-button\r\n                          >\r\n                          <span class=\"tip\">仅限pdf格式</span>\r\n                        </div>\r\n                      </el-upload>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"开户许可证上传\" prop=\"accountLicence\">\r\n                      <el-upload\r\n                        :headers=\"headers\"\r\n                        :action=\"actionUrl\"\r\n                        accept=\".pdf\"\r\n                        :file-list=\"accountLicenceList\"\r\n                        :on-remove=\"handleAccountRemove\"\r\n                        :on-exceed=\"handleExceedLicence\"\r\n                        :on-success=\"handleAccountSuccess\"\r\n                        :on-preview=\"handlePreview\"\r\n                        :limit=\"1\"\r\n                      >\r\n                        <div>\r\n                          <el-button\r\n                            size=\"small\"\r\n                            type=\"danger\"\r\n                            icon=\"el-icon-upload2\"\r\n                            >上传文件</el-button\r\n                          >\r\n                          <span class=\"tip\">仅限pdf格式</span>\r\n                        </div>\r\n                      </el-upload>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"changeMode\">返回</el-button>\r\n                <el-button type=\"error\" @click=\"changeMode(0)\"\r\n                  >暂存草稿</el-button\r\n                >\r\n                <el-button type=\"danger\" @click=\"submitForm(1)\"\r\n                  >提交审核</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport {\r\n  getNoninductiveDetail,\r\n  editNoninductive,\r\n} from \"@/api/system/noninductive\";\r\nimport { getDicts, getSecondDicts } from \"@/api/system/dict/data.js\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\"affiliated_unit\", \"capital_source\", \"affiliated_street\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      info: {},\r\n      form: {},\r\n      accountLicenceList: [],\r\n      // 表单校验\r\n      rules: {\r\n        affiliatedUnitCode: [\r\n          { required: true, message: \"所属单位不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyCreditCode: [\r\n          { required: true, message: \"企业统一代码不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        capitalSourceCode: [\r\n          { required: true, message: \"资金来源不能为空\", trigger: \"blur\" },\r\n        ],\r\n        affiliatedStreetCode: [\r\n          { required: true, message: \"所属街道不能为空\", trigger: \"blur\" },\r\n        ],\r\n        bankName: [\r\n          { required: true, message: \"开户银行不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsPhone: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n        itemTypeCode: [\r\n          { required: true, message: \"选择项目不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyAddress: [\r\n          { required: true, message: \"企业注册地址不能为空\", trigger: \"blur\" },\r\n        ],\r\n        bankCode: [\r\n          { required: true, message: \"银行账号不能为空\", trigger: \"blur\" },\r\n        ],\r\n        application: [\r\n          { required: true, message: \"申请书不能为空\", trigger: \"blur\" },\r\n        ],\r\n        accountLicence: [\r\n          { required: true, message: \"申请书不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      projectProps: {\r\n        lazy: true,\r\n        lazyLoad(node, resolve) {\r\n          let res = [];\r\n          if (node.level == 0) {\r\n            getDicts(\"item_type\").then((response) => {\r\n              response.data.forEach((element) => {\r\n                res.push({\r\n                  label: element.dictLabel,\r\n                  value: element.dictValue,\r\n                  dictCode: element.dictCode,\r\n                });\r\n              });\r\n              const nodes = res;\r\n              resolve(nodes);\r\n            });\r\n          } else {\r\n            getSecondDicts({ parentCode: node.data.dictCode }).then(\r\n              (response) => {\r\n                response.data.forEach((element) => {\r\n                  res.push({\r\n                    label: element.dictLabel,\r\n                    value: element.dictValue,\r\n                    leaf: true,\r\n                  });\r\n                });\r\n                const nodes = res;\r\n                resolve(nodes);\r\n              }\r\n            );\r\n          }\r\n          // const nodes = Array.from({ length: level + 1 }).map((item) => ({\r\n          //   value: ++id,\r\n          //   label: `选项${id}`,\r\n          //   leaf: level >= 2,\r\n          // }));\r\n          // 通过调用resolve将子节点数据返回，通知组件数据加载完成\r\n        },\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    itemTypeChanged(val) {\r\n      this.form.itemTypeFirstCode = val[0];\r\n      this.form.itemTypeSecondCode = val[1];\r\n    },\r\n    getDetail() {\r\n      let userId = this.$route.query.id;\r\n      getNoninductiveDetail(userId).then((response) => {\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    // 申请书、开户许可证预览\r\n    handlePreview(file) {\r\n      window.open(file.url);\r\n    },\r\n    // 开户许可证上传数量限制\r\n    handleExceedLicence(files, fileList) {\r\n      let num = files.length + fileList.length;\r\n      if (num >= 1) {\r\n        this.$message.error(\"上传数量超过上限\");\r\n        return false;\r\n      }\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n\r\n    changeMode() {\r\n      if (this.isDetail) {\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n        this.form.itemTypeCode = [\r\n          this.info.itemTypeFirstCode,\r\n          this.info.itemTypeSecondCode,\r\n        ];\r\n        this.accountLicenceList = this.info.accountLicence\r\n          ? [\r\n              {\r\n                name: this.info.accountLicenceName,\r\n                url: this.info.accountLicence,\r\n              },\r\n            ]\r\n          : [];\r\n        this.applicationList = this.info.application\r\n          ? [\r\n              {\r\n                name: this.info.applicationName,\r\n                url: this.info.application,\r\n              },\r\n            ]\r\n          : [];\r\n      } else {\r\n        this.isDetail = true;\r\n        this.form = {};\r\n        this.getDetail();\r\n      }\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          editNoninductive({ ...this.form, isSubmit: type }).then(\r\n            (response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            }\r\n          );\r\n        }\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.application = res.data.url;\r\n        this.form.applicationName = res.data.name;\r\n      }\r\n    },\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .notice-record-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 20px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 300px;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n        }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}