{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\system\\company.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\system\\company.js", "mtime": 1750311961344}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getCompanyDetail", "params", "request", "url", "method", "getCompanyDetailByBussinessNo", "editCompany", "data", "setCompanyAuth", "searchCompany", "getCompanyCodeByName", "getMyCompanyInfo"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/api/system/company.js"], "sourcesContent": ["/*\r\n * @Author: zhc\r\n * @Date: 2023-02-12 11:18:27\r\n * @LastEditTime: 2023-12-09 20:50:14\r\n * @Description:\r\n * @LastEditors: JHY\r\n */\r\n/*\r\n * @Author: zhc\r\n * @Date: 2023-02-11 14:45:07\r\n * @LastEditTime: 2023-02-11 15:26:15\r\n * @Description:\r\n * @LastEditors: zhc\r\n */\r\n\r\nimport request from \"@/utils/request\";\r\n\r\n// 企业详情\r\nexport function getCompanyDetail(params) {\r\n  return request({\r\n    url: \"/system/company/mag/detailShow\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n// 个人中心-公司信息\r\nexport function getCompanyDetailByBussinessNo(params) {\r\n  return request({\r\n    url: \"/system/company/mag/detailByBussinessNo\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n// 修改企业信息\r\nexport function editCompany(params) {\r\n  return request({\r\n    url: \"/system/company/mag/edit\",\r\n    method: \"post\",\r\n    data: params,\r\n  });\r\n}\r\n\r\n// 认证企业设置\r\nexport function setCompanyAuth(params) {\r\n  return request({\r\n    url: \"/portalweb/Member/addCompany\",\r\n    method: \"post\",\r\n    data: params,\r\n  });\r\n}\r\n// 搜索企业\r\nexport function searchCompany(params) {\r\n  return request({\r\n    url: \"/portalweb/Company/searchByKeywords\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n// 根据公司名称查询社会信用代码\r\nexport function getCompanyCodeByName(params) {\r\n  return request({\r\n    url: \"/portalweb/Company/searchByCustomerName\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}\r\n// 获取我的企业信息\r\nexport function getMyCompanyInfo(params) {\r\n  return request({\r\n    url: \"/portalweb/Member/getMyCompany\",\r\n    method: \"get\",\r\n    params,\r\n  });\r\n}"], "mappings": ";;;;;;;;;;;;;AAeA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACO,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AACA;AACO,SAASI,6BAA6BA,CAACJ,MAAM,EAAE;EACpD,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AACA;AACO,SAASK,WAAWA,CAACL,MAAM,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEN;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,cAAcA,CAACP,MAAM,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdG,IAAI,EAAEN;EACR,CAAC,CAAC;AACJ;AACA;AACO,SAASQ,aAAaA,CAACR,MAAM,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AACA;AACO,SAASS,oBAAoBA,CAACT,MAAM,EAAE;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC;IAC9CC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ;AACA;AACO,SAASU,gBAAgBA,CAACV,MAAM,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAANA;EACF,CAAC,CAAC;AACJ", "ignoreList": []}]}