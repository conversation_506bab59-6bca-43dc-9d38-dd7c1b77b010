{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\resourceHall\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\resourceHall\\detail.vue", "mtime": 1750311962990}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8GA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/purchaseSales/component/resourceHall", "sourcesContent": ["<template>\r\n  <div class=\"resource-hall-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"resource-hall-detail-banner\">\r\n      <img\r\n        src=\"../../../../assets/resourceHall/resourceHallDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"resource-hall-detail-title-box\">\r\n      <div class=\"resource-hall-detail-divider\"></div>\r\n      <div class=\"resource-hall-detail-title\">资源详情</div>\r\n      <div class=\"resource-hall-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"resource-hall-detail-content\">\r\n      <div class=\"resource-hall-detail-box\">\r\n        <div class=\"resource-hall-detail-box-title\">\r\n          {{ data.supplyName }}\r\n        </div>\r\n        <div class=\"resource-hall-detail-headline\">\r\n          <div class=\"headline-content\">\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">技术类别：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.technologyType }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">应用领域：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.applicationArea }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">产品阶段：</div>\r\n              <div class=\"item-content\">{{ data.productStageName }}</div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">合作方式：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.cooperationModeName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">供给方：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.companyName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">联系人：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.contactsName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">联系方式：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.contactsMobile }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">发布时间：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.createTime }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-btn\">\r\n              <el-button\r\n                v-if=\"showBtn\"\r\n                class=\"headline-btn-style intention-btn\"\r\n                @click=\"goIntention\"\r\n                >我有意向</el-button\r\n              >\r\n              <el-button\r\n                @click=\"goChat\"\r\n                class=\"headline-btn-style communication-btn\"\r\n                icon=\"el-icon-chat-dot-round\"\r\n                >在线沟通</el-button\r\n              >\r\n            </div>\r\n          </div>\r\n          <div class=\"headline-img\">\r\n            <img\r\n              v-if=\"data.productPhoto && data.productPhoto.length > 0\"\r\n              :src=\"data.productPhoto[0].url\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-else\r\n              src=\"../../../../assets/resourceHall/resourceHallDetailBanner.png\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"resource-hall-detail-description\">\r\n          <div class=\"description-title-box\">\r\n            <div class=\"description-divider\"></div>\r\n            <div class=\"description-title\">资源描述</div>\r\n          </div>\r\n          <div class=\"description-content\">\r\n            <div v-html=\"data.summary\" class=\"description-text ql-editor\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getSupplyDetail, getCheckSubmit } from \"@/api/purchaseSales\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { getCompanyInfoByLoginInfo } from \"@/api/apathy\";\r\nimport { mapGetters } from \"vuex\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n      showBtn: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      getSupplyDetail(this.$route.query.id)\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          this.data.productPhoto = JSON.parse(this.data.productPhoto);\r\n          if (!this.token) {\r\n            this.showBtn = true;\r\n          } else {\r\n            this.getInfo();\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 判断此资源是不是自己发布的\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (this.data.createById === res.user.userId) {\r\n          this.showBtn = false;\r\n        } else {\r\n          this.showBtn = true;\r\n        }\r\n      });\r\n    },\r\n    goChat() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      let routeData = this.$router.resolve({\r\n        path: \"/user/im\",\r\n        query: {\r\n          userId: this.data.createImById,\r\n        },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳到我有意向页面\r\n    goIntention() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      // 是否加入企业\r\n      getCompanyInfoByLoginInfo()\r\n        .then((res) => {\r\n          if (res.data) {\r\n            // 是否对此资源提交过意向\r\n            getCheckSubmit({\r\n              id: this.$route.query.id,\r\n              resourceType: \"resource_supply\",\r\n            })\r\n              .then((res) => {\r\n                this.loading = false;\r\n                // true 提交过  false未提交过\r\n                if (res.data) {\r\n                  this.$message({\r\n                    type: \"warning\",\r\n                    message: \"已经提交过了哦！\",\r\n                  });\r\n                } else {\r\n                  let routeData = this.$router.resolve({\r\n                    path: \"/addIntention\",\r\n                    query: {\r\n                      id: this.$route.query.id,\r\n                      type: \"resource_supply\",\r\n                      title: this.data.supplyName,\r\n                    },\r\n                  });\r\n                  window.open(routeData.href, \"_blank\");\r\n                }\r\n              })\r\n              .catch(() => {\r\n                this.loading = false;\r\n              });\r\n          } else {\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"warning\",\r\n              message: \"必须加入企业才可提交我有意向\",\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.resource-hall-detail-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .resource-hall-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .resource-hall-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .resource-hall-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .resource-hall-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .resource-hall-detail-content {\r\n    background: #f4f5f9;\r\n    padding-bottom: 70px;\r\n    .resource-hall-detail-box {\r\n      width: 1200px;\r\n      background: #fff;\r\n      margin: 0 auto;\r\n      padding: 60px 60px 192px;\r\n      .resource-hall-detail-box-title {\r\n        width: 100%;\r\n        font-size: 32px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        word-wrap: break-word;\r\n      }\r\n      .resource-hall-detail-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-top: 40px;\r\n        padding-bottom: 40px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        .headline-content {\r\n          flex: 1;\r\n          .headline-content-item {\r\n            display: flex;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            line-height: 32px;\r\n            .item-title {\r\n              width: 80px;\r\n              color: #666;\r\n            }\r\n            .item-content {\r\n              flex: 1;\r\n              max-width: 560px;\r\n              color: #333;\r\n              word-wrap: break-word;\r\n            }\r\n          }\r\n          .headline-content-btn {\r\n            padding-top: 112px;\r\n            .headline-btn-style {\r\n              width: 100px;\r\n              height: 32px;\r\n              border-radius: 4px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              padding: 8px 11px;\r\n            }\r\n            .intention-btn {\r\n              background: #21c9b8;\r\n              color: #fff;\r\n            }\r\n            .communication-btn {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        .headline-img {\r\n          width: 400px;\r\n          height: 240px;\r\n          margin-left: 20px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .resource-hall-detail-description {\r\n      padding-top: 39px;\r\n      .description-title-box {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-bottom: 40px;\r\n        .description-divider {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .description-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n      .description-content {\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.resource-hall-detail-container {\r\n  .description-content {\r\n    .description-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}