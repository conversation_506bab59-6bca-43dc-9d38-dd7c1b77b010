{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\interested.vue?vue&type=style&index=0&id=4175256b&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\interested.vue", "mtime": 1750311962967}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["interested.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8IA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "interested.vue", "sourceRoot": "src/views/manufacturingSharing/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">我有意向</div>\r\n      <div style=\"height: 33px; margin-top: 21px\">I have intentions</div>\r\n    </div>\r\n    <div class=\"card-container card-content\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <div class=\"imgStyle\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../assets/device/ceshi.png\" alt=\"\" />\r\n        </div>\r\n        <div class=\"title\">{{ form.title }}</div>\r\n        <div style=\"display: flex; align-items: center; margin-top: 15px\">\r\n          <div class=\"publishTimeStyle\">发布时间：{{ updateTime }}</div>\r\n          <!-- <div class=\"detailStyle\" @click=\"goDetail\">查看详情 >></div> -->\r\n        </div>\r\n      </div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <span class=\"resourceType\">资源类型：</span>\r\n          <span class=\"resourceValue\">{{ form.fieldName }}</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <span class=\"resourceType\">资源名称：</span>\r\n          <span class=\"resourceValue\">{{ form.title }}</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"意向描述：\">\r\n              <el-input v-model=\"form.intentionContent\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\"\r\n                show-word-limit placeholder=\"请输入\" />\r\n            </el-form-item>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"完工日期\" prop=\"completionDate\" style=\"width: 80%\">\r\n                  <el-date-picker v-model=\"form.completionDate\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\"\r\n                    style=\"width: 100%\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"加工数量\" prop=\"quantity\" style=\"width: 80%\">\r\n                  <el-input v-model=\"form.quantity\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-form-item label=\"意向企业：\" prop=\"companyName\">\r\n              <el-input disabled v-model=\"form.companyName\" placeholder=\"自动带出\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人：\" prop=\"linkMan\">\r\n              <el-input disabled v-model=\"form.linkMan\" placeholder=\"请先维护联系人\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话：\" prop=\"linkTel\">\r\n              <el-input disabled v-model=\"form.linkTel\" placeholder=\"请先维护联系方式\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button style=\"width: 100%; height: 50px\" type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n        <div class=\"promptStyle\">温馨提示</div>\r\n        <div class=\"desc\" style=\"margin-top: 20px\">\r\n          1、我们会在最快的时间和您取得联系（工作时间周一至周五8:00-18:00）\r\n        </div>\r\n        <div class=\"desc\" style=\"margin-top: 13px\">\r\n          2、紧急问题请拨打：15512688882\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { update } from 'ramda';\r\nimport { submitIntention } from \"@/api/home\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        title: \"\",\r\n      },\r\n      updateTime: \"\",\r\n      rules: {\r\n        completionDate: [\r\n          { required: true, message: \"请选择完工日期\", trigger: \"change\" },\r\n        ],\r\n        quantity: [\r\n          { required: true, message: \"请输入加工数量\", trigger: \"blur\" },\r\n        ],\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n    if (userinfo) {\r\n      this.form.companyName = userinfo.memberCompanyName;\r\n      this.form.linkMan = userinfo.companyRealName;\r\n      this.form.linkTel = userinfo.memberPhone;\r\n    }\r\n    if (this.$route.query.demandName && this.$route.query.demandName != 'null') {\r\n      this.form.title = this.$route.query.demandName\r\n    }\r\n    if (this.$route.query.intentionType) {\r\n      this.form.intentionType = parseInt(this.$route.query.intentionType)\r\n    }\r\n    if (this.$route.query.fieldName) {\r\n      this.form.fieldName = this.$route.query.fieldName\r\n    }\r\n    if (this.$route.query.intentionId) {\r\n      this.form.intentionId = this.$route.query.intentionId\r\n    }\r\n    if (this.$route.query.updateTime && this.$route.query.updateTime != 'null') {\r\n      this.updateTime = this.$route.query.updateTime\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          this.form.sum = this.sum\r\n          submitIntention(this.form).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$message.success(\"提交成功\")\r\n              this.cancel()\r\n            } else {\r\n              this.$message.error(res.msg)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    goDetail() {\r\n      this.$router.push(\"/productOrderDetail\");\r\n    },\r\n    cancel(){\r\n      this.$router.go(-1)\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: rgb(242, 242, 242);\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n}\r\n\r\n.card-content {\r\n  display: flex;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: -70px;\r\n  padding: 60px 59px 62px 60px;\r\n\r\n  .card_left {\r\n    .imgStyle {\r\n      width: 330px;\r\n      height: 230px;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #333333;\r\n      margin-top: 23px;\r\n    }\r\n\r\n    .publishTimeStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n    }\r\n\r\n    .detailStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #21c9b8;\r\n      margin-left: auto;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .card_right {\r\n    margin-left: 40px;\r\n    width: 100%;\r\n\r\n    .resourceType {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .resourceValue {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #666666;\r\n    }\r\n\r\n    .footer-submit {\r\n      margin-top: 40px;\r\n    }\r\n\r\n    .promptStyle {\r\n      margin-top: 30px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .desc {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}