{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\Crontab\\index.vue?vue&type=style&index=0&id=2216c3ec&scoped=true&lang=css", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\Crontab\\index.vue", "mtime": 1750311962791}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnBvcF9idG4gew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG1hcmdpbi10b3A6IDIwcHg7DQp9DQoucG9wdXAtbWFpbiB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgbWFyZ2luOiAxMHB4IGF1dG87DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIGJvcmRlci1yYWRpdXM6IDVweDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBvdmVyZmxvdzogaGlkZGVuOw0KfQ0KLnBvcHVwLXRpdGxlIHsNCiAgb3ZlcmZsb3c6IGhpZGRlbjsNCiAgbGluZS1oZWlnaHQ6IDM0cHg7DQogIHBhZGRpbmctdG9wOiA2cHg7DQogIGJhY2tncm91bmQ6ICNmMmYyZjI7DQp9DQoucG9wdXAtcmVzdWx0IHsNCiAgYm94LXNpemluZzogYm9yZGVyLWJveDsNCiAgbGluZS1oZWlnaHQ6IDI0cHg7DQogIG1hcmdpbjogMjVweCBhdXRvOw0KICBwYWRkaW5nOiAxNXB4IDEwcHggMTBweDsNCiAgYm9yZGVyOiAxcHggc29saWQgI2NjYzsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KfQ0KLnBvcHVwLXJlc3VsdCAudGl0bGUgew0KICBwb3NpdGlvbjogYWJzb2x1dGU7DQogIHRvcDogLTI4cHg7DQogIGxlZnQ6IDUwJTsNCiAgd2lkdGg6IDE0MHB4Ow0KICBmb250LXNpemU6IDE0cHg7DQogIG1hcmdpbi1sZWZ0OiAtNzBweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBsaW5lLWhlaWdodDogMzBweDsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCn0NCi5wb3B1cC1yZXN1bHQgdGFibGUgew0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIHdpZHRoOiAxMDAlOw0KICBtYXJnaW46IDAgYXV0bzsNCn0NCi5wb3B1cC1yZXN1bHQgdGFibGUgc3BhbiB7DQogIGRpc3BsYXk6IGJsb2NrOw0KICB3aWR0aDogMTAwJTsNCiAgZm9udC1mYW1pbHk6IGFyaWFsOw0KICBsaW5lLWhlaWdodDogMzBweDsNCiAgaGVpZ2h0OiAzMHB4Ow0KICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICBvdmVyZmxvdzogaGlkZGVuOw0KICBib3JkZXI6IDFweCBzb2xpZCAjZThlOGU4Ow0KfQ0KLnBvcHVwLXJlc3VsdC1zY3JvbGwgew0KICBmb250LXNpemU6IDEycHg7DQogIGxpbmUtaGVpZ2h0OiAyNHB4Ow0KICBoZWlnaHQ6IDEwZW07DQogIG92ZXJmbG93LXk6IGF1dG87DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmXA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-tabs type=\"border-card\">\r\n      <el-tab-pane label=\"秒\" v-if=\"shouldHide('second')\">\r\n        <CrontabSecond\r\n          @update=\"updateCrontabValue\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          ref=\"cronsecond\"\r\n        />\r\n      </el-tab-pane>\r\n\r\n      <el-tab-pane label=\"分钟\" v-if=\"shouldHide('min')\">\r\n        <CrontabMin\r\n          @update=\"updateCrontabValue\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          ref=\"cronmin\"\r\n        />\r\n      </el-tab-pane>\r\n\r\n      <el-tab-pane label=\"小时\" v-if=\"shouldHide('hour')\">\r\n        <CrontabHour\r\n          @update=\"updateCrontabValue\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          ref=\"cronhour\"\r\n        />\r\n      </el-tab-pane>\r\n\r\n      <el-tab-pane label=\"日\" v-if=\"shouldHide('day')\">\r\n        <CrontabDay\r\n          @update=\"updateCrontabValue\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          ref=\"cronday\"\r\n        />\r\n      </el-tab-pane>\r\n\r\n      <el-tab-pane label=\"月\" v-if=\"shouldHide('month')\">\r\n        <CrontabMonth\r\n          @update=\"updateCrontabValue\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          ref=\"cronmonth\"\r\n        />\r\n      </el-tab-pane>\r\n\r\n      <el-tab-pane label=\"周\" v-if=\"shouldHide('week')\">\r\n        <CrontabWeek\r\n          @update=\"updateCrontabValue\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          ref=\"cronweek\"\r\n        />\r\n      </el-tab-pane>\r\n\r\n      <el-tab-pane label=\"年\" v-if=\"shouldHide('year')\">\r\n        <CrontabYear\r\n          @update=\"updateCrontabValue\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          ref=\"cronyear\"\r\n        />\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n\r\n    <div class=\"popup-main\">\r\n      <div class=\"popup-result\">\r\n        <p class=\"title\">时间表达式</p>\r\n        <table>\r\n          <thead>\r\n            <th v-for=\"item of tabTitles\" width=\"40\" :key=\"item\">{{item}}</th>\r\n            <th>Cron 表达式</th>\r\n          </thead>\r\n          <tbody>\r\n            <td>\r\n              <span>{{crontabValueObj.second}}</span>\r\n            </td>\r\n            <td>\r\n              <span>{{crontabValueObj.min}}</span>\r\n            </td>\r\n            <td>\r\n              <span>{{crontabValueObj.hour}}</span>\r\n            </td>\r\n            <td>\r\n              <span>{{crontabValueObj.day}}</span>\r\n            </td>\r\n            <td>\r\n              <span>{{crontabValueObj.month}}</span>\r\n            </td>\r\n            <td>\r\n              <span>{{crontabValueObj.week}}</span>\r\n            </td>\r\n            <td>\r\n              <span>{{crontabValueObj.year}}</span>\r\n            </td>\r\n            <td>\r\n              <span>{{crontabValueString}}</span>\r\n            </td>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <CrontabResult :ex=\"crontabValueString\"></CrontabResult>\r\n\r\n      <div class=\"pop_btn\">\r\n        <el-button size=\"small\" type=\"primary\" @click=\"submitFill\">确定</el-button>\r\n        <el-button size=\"small\" type=\"warning\" @click=\"clearCron\">重置</el-button>\r\n        <el-button size=\"small\" @click=\"hidePopup\">取消</el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CrontabSecond from \"./second.vue\";\r\nimport CrontabMin from \"./min.vue\";\r\nimport CrontabHour from \"./hour.vue\";\r\nimport CrontabDay from \"./day.vue\";\r\nimport CrontabMonth from \"./month.vue\";\r\nimport CrontabWeek from \"./week.vue\";\r\nimport CrontabYear from \"./year.vue\";\r\nimport CrontabResult from \"./result.vue\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      tabTitles: [\"秒\", \"分钟\", \"小时\", \"日\", \"月\", \"周\", \"年\"],\r\n      tabActive: 0,\r\n      myindex: 0,\r\n      crontabValueObj: {\r\n        second: \"*\",\r\n        min: \"*\",\r\n        hour: \"*\",\r\n        day: \"*\",\r\n        month: \"*\",\r\n        week: \"?\",\r\n        year: \"\",\r\n      },\r\n    };\r\n  },\r\n  name: \"vcrontab\",\r\n  props: [\"expression\", \"hideComponent\"],\r\n  methods: {\r\n    shouldHide(key) {\r\n      if (this.hideComponent && this.hideComponent.includes(key)) return false;\r\n      return true;\r\n    },\r\n    resolveExp() {\r\n      // 反解析 表达式\r\n      if (this.expression) {\r\n        let arr = this.expression.split(\" \");\r\n        if (arr.length >= 6) {\r\n          //6 位以上是合法表达式\r\n          let obj = {\r\n            second: arr[0],\r\n            min: arr[1],\r\n            hour: arr[2],\r\n            day: arr[3],\r\n            month: arr[4],\r\n            week: arr[5],\r\n            year: arr[6] ? arr[6] : \"\",\r\n          };\r\n          this.crontabValueObj = {\r\n            ...obj,\r\n          };\r\n          for (let i in obj) {\r\n            if (obj[i]) this.changeRadio(i, obj[i]);\r\n          }\r\n        }\r\n      } else {\r\n        // 没有传入的表达式 则还原\r\n        this.clearCron();\r\n      }\r\n    },\r\n    // tab切换值\r\n    tabCheck(index) {\r\n      this.tabActive = index;\r\n    },\r\n    // 由子组件触发，更改表达式组成的字段值\r\n    updateCrontabValue(name, value, from) {\r\n      \"updateCrontabValue\", name, value, from;\r\n      this.crontabValueObj[name] = value;\r\n      if (from && from !== name) {\r\n        console.log(`来自组件 ${from} 改变了 ${name} ${value}`);\r\n        this.changeRadio(name, value);\r\n      }\r\n    },\r\n    // 赋值到组件\r\n    changeRadio(name, value) {\r\n      let arr = [\"second\", \"min\", \"hour\", \"month\"],\r\n        refName = \"cron\" + name,\r\n        insValue;\r\n\r\n      if (!this.$refs[refName]) return;\r\n\r\n      if (arr.includes(name)) {\r\n        if (value === \"*\") {\r\n          insValue = 1;\r\n        } else if (value.indexOf(\"-\") > -1) {\r\n          let indexArr = value.split(\"-\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].cycle01 = 0)\r\n            : (this.$refs[refName].cycle01 = indexArr[0]);\r\n          this.$refs[refName].cycle02 = indexArr[1];\r\n          insValue = 2;\r\n        } else if (value.indexOf(\"/\") > -1) {\r\n          let indexArr = value.split(\"/\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].average01 = 0)\r\n            : (this.$refs[refName].average01 = indexArr[0]);\r\n          this.$refs[refName].average02 = indexArr[1];\r\n          insValue = 3;\r\n        } else {\r\n          insValue = 4;\r\n          this.$refs[refName].checkboxList = value.split(\",\");\r\n        }\r\n      } else if (name == \"day\") {\r\n        if (value === \"*\") {\r\n          insValue = 1;\r\n        } else if (value == \"?\") {\r\n          insValue = 2;\r\n        } else if (value.indexOf(\"-\") > -1) {\r\n          let indexArr = value.split(\"-\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].cycle01 = 0)\r\n            : (this.$refs[refName].cycle01 = indexArr[0]);\r\n          this.$refs[refName].cycle02 = indexArr[1];\r\n          insValue = 3;\r\n        } else if (value.indexOf(\"/\") > -1) {\r\n          let indexArr = value.split(\"/\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].average01 = 0)\r\n            : (this.$refs[refName].average01 = indexArr[0]);\r\n          this.$refs[refName].average02 = indexArr[1];\r\n          insValue = 4;\r\n        } else if (value.indexOf(\"W\") > -1) {\r\n          let indexArr = value.split(\"W\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].workday = 0)\r\n            : (this.$refs[refName].workday = indexArr[0]);\r\n          insValue = 5;\r\n        } else if (value === \"L\") {\r\n          insValue = 6;\r\n        } else {\r\n          this.$refs[refName].checkboxList = value.split(\",\");\r\n          insValue = 7;\r\n        }\r\n      } else if (name == \"week\") {\r\n        if (value === \"*\") {\r\n          insValue = 1;\r\n        } else if (value == \"?\") {\r\n          insValue = 2;\r\n        } else if (value.indexOf(\"-\") > -1) {\r\n          let indexArr = value.split(\"-\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].cycle01 = 0)\r\n            : (this.$refs[refName].cycle01 = indexArr[0]);\r\n          this.$refs[refName].cycle02 = indexArr[1];\r\n          insValue = 3;\r\n        } else if (value.indexOf(\"#\") > -1) {\r\n          let indexArr = value.split(\"#\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].average01 = 1)\r\n            : (this.$refs[refName].average01 = indexArr[0]);\r\n          this.$refs[refName].average02 = indexArr[1];\r\n          insValue = 4;\r\n        } else if (value.indexOf(\"L\") > -1) {\r\n          let indexArr = value.split(\"L\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].weekday = 1)\r\n            : (this.$refs[refName].weekday = indexArr[0]);\r\n          insValue = 5;\r\n        } else {\r\n          this.$refs[refName].checkboxList = value.split(\",\");\r\n          insValue = 6;\r\n        }\r\n      } else if (name == \"year\") {\r\n        if (value == \"\") {\r\n          insValue = 1;\r\n        } else if (value == \"*\") {\r\n          insValue = 2;\r\n        } else if (value.indexOf(\"-\") > -1) {\r\n          insValue = 3;\r\n        } else if (value.indexOf(\"/\") > -1) {\r\n          insValue = 4;\r\n        } else {\r\n          this.$refs[refName].checkboxList = value.split(\",\");\r\n          insValue = 5;\r\n        }\r\n      }\r\n      this.$refs[refName].radioValue = insValue;\r\n    },\r\n    // 表单选项的子组件校验数字格式（通过-props传递）\r\n    checkNumber(value, minLimit, maxLimit) {\r\n      // 检查必须为整数\r\n      value = Math.floor(value);\r\n      if (value < minLimit) {\r\n        value = minLimit;\r\n      } else if (value > maxLimit) {\r\n        value = maxLimit;\r\n      }\r\n      return value;\r\n    },\r\n    // 隐藏弹窗\r\n    hidePopup() {\r\n      this.$emit(\"hide\");\r\n    },\r\n    // 填充表达式\r\n    submitFill() {\r\n      this.$emit(\"fill\", this.crontabValueString);\r\n      this.hidePopup();\r\n    },\r\n    clearCron() {\r\n      // 还原选择项\r\n      (\"准备还原\");\r\n      this.crontabValueObj = {\r\n        second: \"*\",\r\n        min: \"*\",\r\n        hour: \"*\",\r\n        day: \"*\",\r\n        month: \"*\",\r\n        week: \"?\",\r\n        year: \"\",\r\n      };\r\n      for (let j in this.crontabValueObj) {\r\n        this.changeRadio(j, this.crontabValueObj[j]);\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    crontabValueString: function() {\r\n      let obj = this.crontabValueObj;\r\n      let str =\r\n        obj.second +\r\n        \" \" +\r\n        obj.min +\r\n        \" \" +\r\n        obj.hour +\r\n        \" \" +\r\n        obj.day +\r\n        \" \" +\r\n        obj.month +\r\n        \" \" +\r\n        obj.week +\r\n        (obj.year == \"\" ? \"\" : \" \" + obj.year);\r\n      return str;\r\n    },\r\n  },\r\n  components: {\r\n    CrontabSecond,\r\n    CrontabMin,\r\n    CrontabHour,\r\n    CrontabDay,\r\n    CrontabMonth,\r\n    CrontabWeek,\r\n    CrontabYear,\r\n    CrontabResult,\r\n  },\r\n  watch: {\r\n    expression: \"resolveExp\",\r\n    hideComponent(value) {\r\n      // 隐藏部分组件\r\n    },\r\n  },\r\n  mounted: function() {\r\n    this.resolveExp();\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.pop_btn {\r\n  text-align: center;\r\n  margin-top: 20px;\r\n}\r\n.popup-main {\r\n  position: relative;\r\n  margin: 10px auto;\r\n  background: #fff;\r\n  border-radius: 5px;\r\n  font-size: 12px;\r\n  overflow: hidden;\r\n}\r\n.popup-title {\r\n  overflow: hidden;\r\n  line-height: 34px;\r\n  padding-top: 6px;\r\n  background: #f2f2f2;\r\n}\r\n.popup-result {\r\n  box-sizing: border-box;\r\n  line-height: 24px;\r\n  margin: 25px auto;\r\n  padding: 15px 10px 10px;\r\n  border: 1px solid #ccc;\r\n  position: relative;\r\n}\r\n.popup-result .title {\r\n  position: absolute;\r\n  top: -28px;\r\n  left: 50%;\r\n  width: 140px;\r\n  font-size: 14px;\r\n  margin-left: -70px;\r\n  text-align: center;\r\n  line-height: 30px;\r\n  background: #fff;\r\n}\r\n.popup-result table {\r\n  text-align: center;\r\n  width: 100%;\r\n  margin: 0 auto;\r\n}\r\n.popup-result table span {\r\n  display: block;\r\n  width: 100%;\r\n  font-family: arial;\r\n  line-height: 30px;\r\n  height: 30px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  border: 1px solid #e8e8e8;\r\n}\r\n.popup-result-scroll {\r\n  font-size: 12px;\r\n  line-height: 24px;\r\n  height: 10em;\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n"]}]}