{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\resourceHall\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\resourceHall\\index.vue", "mtime": 1750311962990}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_purchaseSales", "require", "_data", "_cryptoJs", "_interopRequireDefault", "secret<PERSON>ey", "_default", "exports", "default", "data", "loading", "form", "name", "formInfo", "supplyType", "technologyType", "productStage", "cooperationMode", "supplyTypeList", "technologyTypeList", "productStageList", "cooperationModeList", "advanced", "pageNum", "pageSize", "total", "created", "getDictsList", "search", "methods", "_this", "getResourceHallList", "_objectSpread2", "auditStatus", "displayStatus", "then", "res", "console", "log", "key", "CryptoJS", "enc", "Utf8", "parse", "decrypt", "AES", "mode", "ECB", "padding", "pad", "Pkcs7", "JSON", "stringify", "_ref", "rows", "for<PERSON>ach", "item", "productPhoto", "applicationArea", "split", "catch", "code", "propertyName", "_this2", "getDicts", "changeRadio", "onSearch", "handleSizeChange", "handleCurrentChange", "toggleAdvanced", "goResourceDetail", "id", "routeData", "$router", "resolve", "path", "query", "window", "open", "href", "goHome", "push"], "sources": ["src/views/purchaseSales/component/resourceHall/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"resource-hall-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"resource-hall-banner\">\r\n      <img\r\n        src=\"../../../../assets/resourceHall/resourceHallBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"resource-hall-title-content\">\r\n        <div class=\"resource-hall-title-box\">\r\n          <div class=\"resource-hall-divider\"></div>\r\n          <div class=\"resource-hall-title\">资源大厅</div>\r\n          <div class=\"resource-hall-divider\"></div>\r\n        </div>\r\n        <div class=\"resource-hall-search-box\">\r\n          <el-form ref=\"form\" class=\"resource-hall-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.name\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"resource-hall-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"resource-hall-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"resource-hall-card\">\r\n        <div class=\"resource-hall-info-content\">\r\n          <div class=\"resource-hall-search-type-box\">\r\n            <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n              <div class=\"resource-hall-search-line\">\r\n                <el-form-item\r\n                  label=\"供给类型\"\r\n                  class=\"resource-hall-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.supplyType\"\r\n                    class=\"resource-hall-search-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in supplyTypeList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"resource-hall-search-more-line\">\r\n                <el-form-item\r\n                  label=\"技术类别\"\r\n                  class=\"resource-hall-search-more-line-item\"\r\n                  :class=\"{ advanced: !advanced }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.technologyType\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in technologyTypeList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictLabel\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"resource-hall-search-more-line-btn\"\r\n                  @click=\"toggleAdvanced\"\r\n                  >{{ advanced ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n              <div class=\"resource-hall-search-line\">\r\n                <el-form-item\r\n                  label=\"成果阶段\"\r\n                  class=\"resource-hall-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.productStage\"\r\n                    class=\"resource-hall-search-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in productStageList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"resource-hall-search-line\">\r\n                <el-form-item\r\n                  label=\"合作方式\"\r\n                  class=\"resource-hall-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.cooperationMode\"\r\n                    class=\"resource-hall-search-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in cooperationModeList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"resource-hall-list-item\"\r\n            @click=\"goResourceDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img\r\n                  v-if=\"item.productPhoto && item.productPhoto.length > 0\"\r\n                  :src=\"item.productPhoto[0].url\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/resourceDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"list-item-info\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.supplyName }}\r\n                </div>\r\n                <div class=\"list-item-text\">\r\n                  <div class=\"list-item-label\">应用领域：</div>\r\n                  <div class=\"list-item-tag-box\">\r\n                    <div\r\n                      v-for=\"(val, num) in item.applicationArea\"\r\n                      :key=\"num\"\r\n                      class=\"lilst-item-tag red-tag\"\r\n                    >\r\n                      {{ val }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"list-item-text\">\r\n                  <div class=\"list-item-label\">技术类别：</div>\r\n                  <div class=\"list-item-tag-box\">\r\n                    <div\r\n                      v-for=\"(val, num) in item.technologyType\"\r\n                      :key=\"num\"\r\n                      class=\"lilst-item-tag blue-tag\"\r\n                    >\r\n                      {{ val }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"resource-hall-page-end\">\r\n            <el-button class=\"resource-hall-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            >\r\n            <el-pagination\r\n              v-if=\"data && data.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"resource-hall-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @size-change=\"handleSizeChange\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getResourceHallList } from \"@/api/purchaseSales\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        name: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        supplyType: \"\", //供给类型\r\n        technologyType: \"\", //技术类别\r\n        productStage: \"\", //成果阶段\r\n        cooperationMode: \"\", //合作方式\r\n      },\r\n      supplyTypeList: [], //供给类型列表\r\n      technologyTypeList: [], //技术类别列表\r\n      productStageList: [], //成果阶段列表\r\n      cooperationModeList: [], //合作方式列表\r\n      advanced: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getDictsList(\"supply_type\", \"supplyTypeList\");\r\n    this.getDictsList(\"technology_type\", \"technologyTypeList\");\r\n    this.getDictsList(\"product_stage\", \"productStageList\");\r\n    this.getDictsList(\"cooperation_mode\", \"cooperationModeList\");\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getResourceHallList({\r\n        ...this.form,\r\n        ...this.formInfo,\r\n        auditStatus: 2,\r\n        displayStatus: 1,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          console.log(res);\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          console.log(res);\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.data.forEach((item) => {\r\n            item.productPhoto = item.productPhoto\r\n              ? JSON.parse(item.productPhoto)\r\n              : [];\r\n            item.applicationArea = item.applicationArea\r\n              ? item.applicationArea.split(\",\")\r\n              : \"\";\r\n            item.technologyType = item.technologyType\r\n              ? item.technologyType.split(\",\")\r\n              : \"\";\r\n          });\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n    // 技术类别显示更多\r\n    toggleAdvanced() {\r\n      this.advanced = !this.advanced;\r\n    },\r\n    // 跳转到资源详情页面\r\n    goResourceDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/resourceHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.resource-hall-container {\r\n  width: 100%;\r\n  .resource-hall-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .resource-hall-title-content {\r\n    width: 100%;\r\n    padding-bottom: 18px;\r\n    .resource-hall-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .resource-hall-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .resource-hall-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .resource-hall-search-box {\r\n      .resource-hall-search-form {\r\n        text-align: center;\r\n        .resource-hall-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .resource-hall-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .resource-hall-card {\r\n    background: #f4f5f9;\r\n    padding-top: 40px;\r\n    .resource-hall-info-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      .resource-hall-search-type-box {\r\n        background: #fff;\r\n        margin-bottom: 17px;\r\n        .resource-hall-search-line {\r\n          padding: 14px 24px 4px;\r\n          .resource-hall-search-line-item {\r\n            margin-bottom: 0;\r\n            .resource-hall-search-radio {\r\n              width: 1050px;\r\n              margin-top: 11px;\r\n            }\r\n          }\r\n          & + .resource-hall-search-line {\r\n            border-top: 1px solid #f5f5f5;\r\n          }\r\n        }\r\n        .resource-hall-search-more-line {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 14px 24px 4px;\r\n          border-top: 1px solid #f5f5f5;\r\n          border-bottom: 1px solid #f5f5f5;\r\n          .resource-hall-search-more-line-item {\r\n            flex: 1;\r\n            margin-bottom: 0;\r\n            display: flex;\r\n            &.advanced {\r\n              overflow: hidden;\r\n              height: 45px;\r\n            }\r\n            .more-radio {\r\n              margin-top: 11px;\r\n              flex: 1;\r\n            }\r\n          }\r\n          .resource-hall-search-more-line-btn {\r\n            display: inline-block;\r\n            width: 64px;\r\n            height: 24px;\r\n            background: #fff;\r\n            border-radius: 2px;\r\n            border: 1px solid #d9d9d9;\r\n            font-size: 12px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            color: #333;\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 0 16px;\r\n            margin-top: 5px;\r\n            &:hover {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .resource-hall-list-item {\r\n        width: 100%;\r\n        background: #fff;\r\n        border-radius: 12px;\r\n        .list-item-content {\r\n          display: flex;\r\n          padding: 24px;\r\n          cursor: pointer;\r\n          .list-item-img {\r\n            width: 180px;\r\n            height: 128px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n              border-radius: 4px;\r\n            }\r\n          }\r\n          .list-item-info {\r\n            padding-left: 24px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            .list-item-title {\r\n              width: 922px;\r\n              height: 20px;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              font-size: 20px;\r\n              font-weight: 500;\r\n              color: #323233;\r\n              line-height: 20px;\r\n              margin-bottom: 24px;\r\n              word-wrap: break-word;\r\n            }\r\n            .list-item-text {\r\n              display: flex;\r\n              align-items: top;\r\n              .list-item-label {\r\n                color: #323233;\r\n                line-height: 14px;\r\n                margin-top: 16px;\r\n              }\r\n              .list-item-tag-box {\r\n                display: flex;\r\n                width: 852px;\r\n                flex-wrap: wrap;\r\n                .lilst-item-tag {\r\n                  max-width: 840px;\r\n                  padding: 0px 12px;\r\n                  border-radius: 4px;\r\n                  font-size: 12px;\r\n                  line-height: 24px;\r\n                  text-align: center;\r\n                  margin-right: 16px;\r\n                  margin-top: 12px;\r\n                  word-wrap: break-word;\r\n                  text-align: left;\r\n                }\r\n                .red-tag {\r\n                  color: #21c9b8;\r\n                  background: #21c9b8 1a;\r\n                }\r\n                .blue-tag {\r\n                  background: #214dc51a;\r\n                  color: #214dc5;\r\n                }\r\n              }\r\n            }\r\n          }\r\n          &:hover {\r\n            .list-item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        & + .resource-hall-list-item {\r\n          margin-top: 24px;\r\n        }\r\n      }\r\n      .resource-hall-page-end {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        padding: 24px 0 60px;\r\n        .resource-hall-page-btn {\r\n          width: 82px;\r\n          height: 32px;\r\n          background: #fff;\r\n          border-radius: 4px;\r\n          border: 1px solid #d9d9d9;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.resource-hall-container {\r\n  .resource-hall-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .el-form-item__label {\r\n    width: 88px;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #999;\r\n    padding-right: 32px;\r\n    text-align: left;\r\n  }\r\n  .resource-hall-search-more-line {\r\n    .el-form-item__content {\r\n      width: 970px;\r\n    }\r\n  }\r\n  .el-radio-button {\r\n    padding-bottom: 20px;\r\n    .el-radio-button__inner {\r\n      border: none;\r\n      padding: 0 32px 0 0;\r\n      background: none;\r\n      &:hover {\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        color: #21c9b8;\r\n        background: none;\r\n      }\r\n    }\r\n    .el-radio-button__orig-radio:checked {\r\n      & + .el-radio-button__inner {\r\n        box-shadow: unset;\r\n      }\r\n    }\r\n  }\r\n  .resource-hall-page-end {\r\n    .resource-hall-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AA0MA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAC,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAI,SAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;QACAC,IAAA;MACA;MACAC,QAAA;QACAC,UAAA;QAAA;QACAC,cAAA;QAAA;QACAC,YAAA;QAAA;QACAC,eAAA;MACA;MACAC,cAAA;MAAA;MACAC,kBAAA;MAAA;MACAC,gBAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,QAAA;MACAb,IAAA;MACAc,OAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAA,YAAA;IACA,KAAAA,YAAA;IACA,KAAAA,YAAA;IACA,KAAAC,MAAA;EACA;EACAC,OAAA;IACAD,MAAA,WAAAA,OAAA;MAAA,IAAAE,KAAA;MACA,KAAApB,OAAA;MACA,IAAAqB,kCAAA,MAAAC,cAAA,CAAAxB,OAAA,MAAAwB,cAAA,CAAAxB,OAAA,MAAAwB,cAAA,CAAAxB,OAAA,MACA,KAAAG,IAAA,GACA,KAAAE,QAAA;QACAoB,WAAA;QACAC,aAAA;QACAX,OAAA,OAAAA;QACA;MAAA,EACA,EACAY,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAG,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAtC,SAAA;QACA,IAAAuC,OAAA,GAAAJ,iBAAA,CAAAK,GAAA,CAAAD,OAAA,CAAAR,GAAA,EAAAG,GAAA;UACAO,IAAA,EAAAN,iBAAA,CAAAM,IAAA,CAAAC,GAAA;UACAC,OAAA,EAAAR,iBAAA,CAAAS,GAAA,CAAAC;QACA;QACAd,GAAA,GAAAe,IAAA,CAAAR,KAAA,CAAAH,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAU,SAAA,CAAAR,OAAA;QACAP,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACAN,KAAA,CAAApB,OAAA;QACA,IAAA2C,IAAA,GAAAjB,GAAA;UAAAkB,IAAA,GAAAD,IAAA,CAAAC,IAAA;UAAA7B,KAAA,GAAA4B,IAAA,CAAA5B,KAAA;QACAK,KAAA,CAAArB,IAAA,GAAA6C,IAAA;QACAxB,KAAA,CAAArB,IAAA,CAAA8C,OAAA,WAAAC,IAAA;UACAA,IAAA,CAAAC,YAAA,GAAAD,IAAA,CAAAC,YAAA,GACAN,IAAA,CAAAR,KAAA,CAAAa,IAAA,CAAAC,YAAA,IACA;UACAD,IAAA,CAAAE,eAAA,GAAAF,IAAA,CAAAE,eAAA,GACAF,IAAA,CAAAE,eAAA,CAAAC,KAAA,QACA;UACAH,IAAA,CAAAzC,cAAA,GAAAyC,IAAA,CAAAzC,cAAA,GACAyC,IAAA,CAAAzC,cAAA,CAAA4C,KAAA,QACA;QACA;QACA7B,KAAA,CAAAL,KAAA,GAAAA,KAAA;MACA,GACAmC,KAAA;QACA9B,KAAA,CAAApB,OAAA;MACA;IACA;IACA;IACAiB,YAAA,WAAAA,aAAAkC,IAAA,EAAAC,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,cAAA,EAAAH,IAAA,EAAA1B,IAAA,WAAAC,GAAA;QACA2B,MAAA,CAAAD,YAAA,IAAA1B,GAAA,CAAA3B,IAAA;MACA;IACA;IACAwD,WAAA,WAAAA,YAAA;MACA,KAAAC,QAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA3C,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAA0C,QAAA;IACA;IACAE,mBAAA,WAAAA,oBAAA7C,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAK,MAAA;IACA;IACAsC,QAAA,WAAAA,SAAA;MACA,KAAA3C,OAAA;MACA,KAAAK,MAAA;IACA;IACA;IACAyC,cAAA,WAAAA,eAAA;MACA,KAAA/C,QAAA,SAAAA,QAAA;IACA;IACA;IACAgD,gBAAA,WAAAA,iBAAAC,EAAA;MACA,IAAAC,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACAC,KAAA;UAAAL,EAAA,EAAAA;QAAA;MACA;MACAM,MAAA,CAAAC,IAAA,CAAAN,SAAA,CAAAO,IAAA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAP,OAAA,CAAAQ,IAAA;QAAAN,IAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}