{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\senseTab.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\senseTab.vue", "mtime": 1750311962930}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ramda", "require", "_scene", "name", "data", "loading", "items", "pageNum", "pageSize", "total", "created", "getSenseData", "methods", "_this", "getListByText", "then", "res", "code", "_res$rows", "rows", "map", "_ref", "id", "title", "simpleContent", "updateTime", "_ref$coverPictureList", "coverPictureList", "image", "head", "content", "src", "url", "to", "finally", "handleSizeChange", "newSize", "handleCurrentChange", "newPage"], "sources": ["src/views/components/home/<USER>/senseTab.vue"], "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" class=\"tab-main\">\r\n    <template v-if=\"items.length > 0\">\r\n      <div v-for=\"item in items\" :key=\"item.id\">\r\n        <router-link\r\n          class=\"card\"\r\n          target=\"_blank\"\r\n          :to=\"`/scenarioDetail?id=${item.id}`\"\r\n        >\r\n          <div class=\"list-item-content\">\r\n            <div class=\"list-item-info\">\r\n              <div class=\"list-item-title\">\r\n                {{ item.title }}\r\n              </div>\r\n              <div class=\"list-item-text\">{{ item.content }}</div>\r\n              <div class=\"list-item-time\">{{ item.updateTime }}</div>\r\n            </div>\r\n          </div>\r\n        </router-link>\r\n      </div>\r\n    </template>\r\n    <template v-else>\r\n      <el-empty />\r\n    </template>\r\n    <div class=\"tab-page-end\">\r\n      <!-- <span class=\"demonstration\">完整功能</span> -->\r\n      <el-pagination\r\n        class=\"company-tab-pagination\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"pageNum\"\r\n        :page-sizes=\"[100, 200, 300, 400]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\" prev, pager, next \"\r\n        :total=\"total\"\r\n      >\r\n      </el-pagination>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { head, map } from \"ramda\";\r\nimport { getListByText } from \"@/api/scene\";\r\n\r\nexport default {\r\n  name: \"SenseTab\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      items: [],\r\n      pageNum: 1,\r\n      pageSize: 3,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getSenseData();\r\n  },\r\n  methods: {\r\n    getSenseData() {\r\n      this.loading = true;\r\n      getListByText({ pageNum: this.pageNum, pageSize: this.pageSize })\r\n        .then((res) => {\r\n          const { code, rows = [], total } = res;\r\n          if (code === 200) {\r\n            this.items = map(\r\n              ({\r\n                id,\r\n                title,\r\n                simpleContent,\r\n                updateTime,\r\n                coverPictureList = [],\r\n              }) => {\r\n                const image = head(coverPictureList || []) || {};\r\n                return {\r\n                  id,\r\n                  title,\r\n                  content: simpleContent,\r\n                  updateTime,\r\n                  src: image.url,\r\n                };\r\n              },\r\n              rows\r\n            );\r\n            this.to;\r\n            this.total = total;\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    handleSizeChange(newSize) {\r\n      this.pageSize = newSize;\r\n      this.getSenseData();\r\n    },\r\n    handleCurrentChange(newPage) {\r\n      this.pageNum = newPage;\r\n      this.getSenseData();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n\r\n.tab-main {\r\n  position: relative;\r\n  width: 100%;\r\n\r\n  .el-row--flex {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  ::v-deep .el-scrollbar__wrap {\r\n    overflow-x: hidden;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .card {\r\n    display: flex;\r\n    flex-direction: column;\r\n    width: 100%;\r\n    flex-shrink: 0;\r\n    min-height: 100px;\r\n    background: #ffffff;\r\n    box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.1);\r\n    margin-bottom: 24px;\r\n    .list-item-content {\r\n      display: flex;\r\n      padding: 24px 32px;\r\n      cursor: pointer;\r\n\r\n      .list-item-info {\r\n        padding-left: 24px;\r\n        .list-item-title {\r\n          width: 806px;\r\n          height: 24px;\r\n          text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n          white-space: nowrap; /*让文字不换行*/\r\n          overflow: hidden; /*超出要隐藏*/\r\n          font-size: 20px;\r\n          font-weight: 500;\r\n          color: #323233;\r\n          line-height: 24px;\r\n          margin: 8px 0 14px;\r\n        }\r\n        .list-item-text {\r\n          width: 806px;\r\n          height: 60px;\r\n          overflow: hidden;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n          -webkit-line-clamp: 2;\r\n          text-overflow: ellipsis;\r\n          font-size: 14px;\r\n          color: #666;\r\n          line-height: 30px;\r\n        }\r\n        .list-item-time {\r\n          color: #999;\r\n          line-height: 14px;\r\n          margin-top: 22px;\r\n        }\r\n      }\r\n      &:hover {\r\n        .list-item-title {\r\n          color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n    &-footer {\r\n      padding: 16px 24px;\r\n\r\n      .title {\r\n        @include multiEllipsis(2);\r\n        font-size: 18px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        line-height: 26px;\r\n        margin-bottom: 12px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.tab-main {\r\n  .tab-page-end {\r\n    .company-tab-pagination {\r\n      width: 280px;\r\n      margin-left: calc(45% - 200px);\r\n      // margin: 0 auto;\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #ffffff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        margin: 0 6px;\r\n        color: #333;\r\n      }\r\n      .el-pager {\r\n        .number {\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 1px solid #d9d9d9;\r\n          background: #ffffff;\r\n          border-radius: 4px;\r\n          line-height: 32px;\r\n          margin: 0 6px;\r\n          &.active {\r\n            background: #21c9b8;\r\n            border: 1px solid #21c9b8;\r\n            color: #fff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AA0CA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACAD,YAAA,WAAAA,aAAA;MAAA,IAAAE,KAAA;MACA,KAAAR,OAAA;MACA,IAAAS,oBAAA;QAAAP,OAAA,OAAAA,OAAA;QAAAC,QAAA,OAAAA;MAAA,GACAO,IAAA,WAAAC,GAAA;QACA,IAAAC,IAAA,GAAAD,GAAA,CAAAC,IAAA;UAAAC,SAAA,GAAAF,GAAA,CAAAG,IAAA;UAAAA,IAAA,GAAAD,SAAA,mBAAAA,SAAA;UAAAT,KAAA,GAAAO,GAAA,CAAAP,KAAA;QACA,IAAAQ,IAAA;UACAJ,KAAA,CAAAP,KAAA,OAAAc,UAAA,EACA,UAAAC,IAAA,EAMA;YAAA,IALAC,EAAA,GAAAD,IAAA,CAAAC,EAAA;cACAC,KAAA,GAAAF,IAAA,CAAAE,KAAA;cACAC,aAAA,GAAAH,IAAA,CAAAG,aAAA;cACAC,UAAA,GAAAJ,IAAA,CAAAI,UAAA;cAAAC,qBAAA,GAAAL,IAAA,CACAM,gBAAA;cAAAA,gBAAA,GAAAD,qBAAA,mBAAAA,qBAAA;YAEA,IAAAE,KAAA,OAAAC,WAAA,EAAAF,gBAAA;YACA;cACAL,EAAA,EAAAA,EAAA;cACAC,KAAA,EAAAA,KAAA;cACAO,OAAA,EAAAN,aAAA;cACAC,UAAA,EAAAA,UAAA;cACAM,GAAA,EAAAH,KAAA,CAAAI;YACA;UACA,GACAb,IACA;UACAN,KAAA,CAAAoB,EAAA;UACApB,KAAA,CAAAJ,KAAA,GAAAA,KAAA;QACA;MACA,GACAyB,OAAA;QACArB,KAAA,CAAAR,OAAA;MACA;IACA;IACA8B,gBAAA,WAAAA,iBAAAC,OAAA;MACA,KAAA5B,QAAA,GAAA4B,OAAA;MACA,KAAAzB,YAAA;IACA;IACA0B,mBAAA,WAAAA,oBAAAC,OAAA;MACA,KAAA/B,OAAA,GAAA+B,OAAA;MACA,KAAA3B,YAAA;IACA;EACA;AACA", "ignoreList": []}]}