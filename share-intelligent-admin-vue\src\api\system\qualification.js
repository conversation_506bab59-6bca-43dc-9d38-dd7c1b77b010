import request from '@/utils/request'

// 查询工厂资质证件列表
export function listQualification(query) {
  return request({
    url: '/system/qualification/list',
    method: 'get',
    params: query
  })
}

// 查询工厂资质证件详细
export function getQualification(id) {
  return request({
    url: '/system/qualification/' + id,
    method: 'get'
  })
}

// 新增工厂资质证件
export function addQualification(data) {
  return request({
    url: '/system/qualification',
    method: 'post',
    data: data
  })
}

// 修改工厂资质证件
export function updateQualification(data) {
  return request({
    url: '/system/qualification',
    method: 'put',
    data: data
  })
}

// 删除工厂资质证件
export function delQualification(id) {
  return request({
    url: '/system/qualification/' + id,
    method: 'delete'
  })
}
