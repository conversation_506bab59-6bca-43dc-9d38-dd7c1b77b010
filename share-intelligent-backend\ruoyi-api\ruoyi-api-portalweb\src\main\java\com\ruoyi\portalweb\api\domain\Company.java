package com.ruoyi.portalweb.api.domain;

import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

import io.swagger.annotations.ApiModelProperty;

/**
 * 企业信息对象 company
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
public class Company extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 会员ID */
    @ApiModelProperty(value = "会员ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long companyId;

    /** 公司名称 */
    @Excel(name = "公司名称")
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /** 公司邮箱 */
    @Excel(name = "公司邮箱")
    @ApiModelProperty(value = "公司邮箱")
    private String companyEmail;

    /** 授权书 */
    @Excel(name = "授权书")
    @ApiModelProperty(value = "授权书")
    private String companyEmpower;

    /** 状态，业务字典 */
    @Excel(name = "状态，业务字典")
    @ApiModelProperty(value = "状态，业务字典")
    private String companyStatus;
    
    /** 营业执照 */
    @Excel(name = "营业执照")
    @ApiModelProperty(value = "营业执照")
    private String businessLicenseImageUrl;
    
    
    /** 社会统一信用代码 */
    @Excel(name = "社会统一信用代码")
    @ApiModelProperty(value = "社会统一信用代码")
    private String socialUnityCreditCode;
    
    /** 服务行业 */
    @Excel(name = "服务行业")
    @ApiModelProperty(value = "服务行业")
    private String serviceIndustry;
    
    /** 提交时间 */
    @Excel(name = "提交时间")
    @ApiModelProperty(value = "提交时间")
    private Date submitTime;
    
    /** 企业规模 */
    @Excel(name = "企业规模")
    @ApiModelProperty(value = "企业规模")
    private String companySize;
    
    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String phone;
    
    /** 详细地址 */
    @Excel(name = "详细地址")
    @ApiModelProperty(value = "详细地址")
    private String address;
    
    /** 注册资金 */
    @Excel(name = "注册资金")
    @ApiModelProperty(value = "注册资金")
    private String registeredCapital;
    
    /** 企业简介 */
    @Excel(name = "企业简介")
    @ApiModelProperty(value = "企业简介")
    private String intrduction;


    /** 真实姓名 */
    @Excel(name = "真实姓名")
    @ApiModelProperty(value = "真实姓名")
    private String companyRealName;

    /** 删除标志（0代表存在 2代表删除） */
    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;

    public String getBusinessLicenseImageUrl() {
        return businessLicenseImageUrl;
    }

    public void setBusinessLicenseImageUrl(String businessLicenseImageUrl) {
        this.businessLicenseImageUrl = businessLicenseImageUrl;
    }

    public String getCompanyRealName() {
        return companyRealName;
    }

    public void setCompanyRealName(String companyRealName) {
        this.companyRealName = companyRealName;
    }

    public void setCompanyId(Long companyId)
    {
        this.companyId = companyId;
    }

    public Long getCompanyId() 
    {
        return companyId;
    }
    public void setCompanyName(String companyName) 
    {
        this.companyName = companyName;
    }

    public String getCompanyName() 
    {
        return companyName;
    }
    public void setCompanyEmail(String companyEmail) 
    {
        this.companyEmail = companyEmail;
    }

    public String getCompanyEmail() 
    {
        return companyEmail;
    }
    public void setCompanyEmpower(String companyEmpower) 
    {
        this.companyEmpower = companyEmpower;
    }

    public String getCompanyEmpower() 
    {
        return companyEmpower;
    }
    public void setCompanyStatus(String companyStatus) 
    {
        this.companyStatus = companyStatus;
    }

    public String getCompanyStatus() 
    {
        return companyStatus;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }
    
    public String getSocialUnityCreditCode() {
		return socialUnityCreditCode;
	}

	public void setSocialUnityCreditCode(String socialUnityCreditCode) {
		this.socialUnityCreditCode = socialUnityCreditCode;
	}

	public String getServiceIndustry() {
		return serviceIndustry;
	}

	public void setServiceIndustry(String serviceIndustry) {
		this.serviceIndustry = serviceIndustry;
	}

	public Date getSubmitTime() {
		return submitTime;
	}

	public void setSubmitTime(Date submitTime) {
		this.submitTime = submitTime;
	}

	public String getCompanySize() {
		return companySize;
	}

	public void setCompanySize(String companySize) {
		this.companySize = companySize;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getRegisteredCapital() {
		return registeredCapital;
	}

	public void setRegisteredCapital(String registeredCapital) {
		this.registeredCapital = registeredCapital;
	}

	public String getIntrduction() {
		return intrduction;
	}

	public void setIntrduction(String intrduction) {
		this.intrduction = intrduction;
	}

	@Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("companyId", getCompanyId())
            .append("companyName", getCompanyName())
            .append("companyEmail", getCompanyEmail())
            .append("companyEmpower", getCompanyEmpower())
            .append("companyStatus", getCompanyStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
