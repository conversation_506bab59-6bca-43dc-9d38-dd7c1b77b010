package com.ruoyi.auth.controller;

import com.ruoyi.auth.service.SSOService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * SSO认证中心控制器
 * 主系统作为认证中心，为从系统提供单点登录服务
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sso")
public class SSOController {

    @Autowired
    private SSOService ssoService;

    /**
     * 生成SSO Token并跳转到目标系统
     * 
     * @param targetSystem 目标系统标识 (如: market)
     * @param redirectUrl 跳转地址
     * @param response HTTP响应
     */
    @GetMapping("/login")
    public void ssoLogin(@RequestParam("target") String targetSystem,
                        @RequestParam(value = "redirect", required = false) String redirectUrl,
                        HttpServletResponse response) throws IOException {
        
        // 检查用户是否已登录
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (loginUser == null) {
            // 未登录，跳转到登录页面
            String loginUrl = "/login?redirect=" + 
                java.net.URLEncoder.encode(
                    "/sso/login?target=" + targetSystem + 
                    (redirectUrl != null ? "&redirect=" + redirectUrl : ""), 
                    "UTF-8"
                );
            response.sendRedirect(loginUrl);
            return;
        }

        // 生成SSO Token
        String ssoToken = ssoService.generateSSOToken(loginUser, targetSystem);
        
        // 构造跳转URL
        String targetUrl = ssoService.buildTargetUrl(targetSystem, ssoToken, redirectUrl);
        
        // 跳转到目标系统
        response.sendRedirect(targetUrl);
    }

    /**
     * 验证SSO Token
     * 从系统调用此接口验证Token的有效性
     * 
     * @param token SSO Token
     * @param system 系统标识
     * @return 用户信息
     */
    @PostMapping("/verify")
    public AjaxResult verifySSOToken(@RequestParam("token") String token,
                                   @RequestParam("system") String system) {
        try {
            Map<String, Object> userInfo = ssoService.verifySSOToken(token, system);
            if (userInfo != null) {
                return AjaxResult.success("Token验证成功", userInfo);
            } else {
                return AjaxResult.error("Token验证失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("Token验证异常: " + e.getMessage());
        }
    }

    /**
     * 获取用户详细信息
     * 
     * @param userId 用户ID
     * @return 用户详细信息
     */
    @GetMapping("/userinfo/{userId}")
    public AjaxResult getUserInfo(@PathVariable Long userId) {
        try {
            Map<String, Object> userInfo = ssoService.getUserDetailInfo(userId);
            return AjaxResult.success(userInfo);
        } catch (Exception e) {
            return AjaxResult.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * SSO登出
     * 
     * @param token 当前系统的token
     * @param system 系统标识
     * @return 结果
     */
    @PostMapping("/logout")
    public AjaxResult ssoLogout(@RequestParam("token") String token,
                              @RequestParam("system") String system) {
        try {
            ssoService.ssoLogout(token, system);
            return AjaxResult.success("登出成功");
        } catch (Exception e) {
            return AjaxResult.error("登出失败: " + e.getMessage());
        }
    }

    /**
     * 检查SSO状态
     * 
     * @param request HTTP请求
     * @return SSO状态信息
     */
    @GetMapping("/status")
    public AjaxResult checkSSOStatus(HttpServletRequest request) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Map<String, Object> status = new HashMap<>();
            
            if (loginUser != null) {
                status.put("isLogin", true);
                status.put("username", loginUser.getUsername());
                status.put("userId", loginUser.getUserid());
                status.put("deptName", loginUser.getUser().getDept() != null ? 
                          loginUser.getUser().getDept().getDeptName() : "");
            } else {
                status.put("isLogin", false);
            }
            
            return AjaxResult.success(status);
        } catch (Exception e) {
            return AjaxResult.error("检查SSO状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统配置信息
     * 
     * @param system 系统标识
     * @return 系统配置
     */
    @GetMapping("/config/{system}")
    public AjaxResult getSystemConfig(@PathVariable String system) {
        try {
            Map<String, Object> config = ssoService.getSystemConfig(system);
            return AjaxResult.success(config);
        } catch (Exception e) {
            return AjaxResult.error("获取系统配置失败: " + e.getMessage());
        }
    }

    /**
     * 同步用户信息到从系统
     * 
     * @param userId 用户ID
     * @param targetSystem 目标系统
     * @return 同步结果
     */
    @PostMapping("/sync-user")
    public AjaxResult syncUserToSystem(@RequestParam("userId") Long userId,
                                     @RequestParam("targetSystem") String targetSystem) {
        try {
            boolean result = ssoService.syncUserToSystem(userId, targetSystem);
            if (result) {
                return AjaxResult.success("用户信息同步成功");
            } else {
                return AjaxResult.error("用户信息同步失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("用户信息同步异常: " + e.getMessage());
        }
    }
}
