{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index(copy).vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index(copy).vue", "mtime": 1750311963019}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index(copy).vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index(copy).vue", "sourceRoot": "src/views/solution", "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/solution/solution.png\" alt=\"\" />\r\n      <div class=\"bannerTitle\">解决方案</div>\r\n      <div class=\"bannerDesc\">\r\n        沉淀众多优秀解决方案，提供适用于不同行业、领域的数字化转型服务方案\r\n      </div>\r\n    </div>\r\n    <div>\r\n      <div class=\"activity-title-content\">\r\n        <!-- <div class=\"activity-title-box\">\r\n          <div class=\"activity-divider\"></div>\r\n          <div class=\"activity-title\">解决方案</div>\r\n          <div class=\"activity-divider\"></div>\r\n        </div> -->\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.keywords\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"solutionContent\">\r\n        <div class=\"solutionLeft\">\r\n          <div\r\n            class=\"itemType\"\r\n            :class=\"flag === '全部' ? 'itemTypeHover' : ''\"\r\n            @click=\"getItemData({ typeName: '全部' })\"\r\n          >\r\n            全部（{{ allAmounts }}）\r\n          </div>\r\n          <div\r\n            class=\"itemType\"\r\n            :class=\"flag === item.typeName ? 'itemTypeHover' : ''\"\r\n            v-for=\"item in solutionTypeList\"\r\n            :key=\"item.id\"\r\n            @click=\"getItemData(item)\"\r\n          >\r\n            {{ item.typeName }}（{{ item.value }}）\r\n          </div>\r\n        </div>\r\n        <div class=\"solutionRight\">\r\n          <div style=\"display: flex; flex-wrap: wrap\" v-loading=\"loading\">\r\n            <div\r\n              class=\"itemContent\"\r\n              v-for=\"item in solutionListData\"\r\n              :key=\"item.id\"\r\n              @click=\"goDetail(item.id)\"\r\n            >\r\n              <!-- <div class=\"content_left\">\r\n              <img src=\"\" alt=\"\">\r\n            </div> -->\r\n              <div class=\"content_right\">\r\n                <div class=\"title\">{{ item.name }}</div>\r\n                <div class=\"desc\">\r\n                  {{ item.introduction }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"activity-page-end\">\r\n            <!-- <el-button class=\"activity-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            > -->\r\n            <el-pagination\r\n              v-if=\"solutionListData && solutionListData.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"activity-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import { getActivityList } from \"@/api/purchaseSales\";\r\nimport { solutionType, solutionTypeName, solutionList } from \"@/api/solution\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { caseList } from \"@/api/classicCase\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      loading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        caseType: \"\", // 案例类型\r\n      },\r\n      caseTypeList: [],\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      allAmounts: 0,\r\n      solutionTypeList: [\r\n        {\r\n          value: \"0\",\r\n          label: \"全部\",\r\n        },\r\n        {\r\n          value: \"1\",\r\n          label: \"节能减排\",\r\n        },\r\n        {\r\n          value: \"2\",\r\n          label: \"低碳认证\",\r\n        },\r\n        {\r\n          value: \"3\",\r\n          label: \"数据核算\",\r\n        },\r\n        {\r\n          value: \"4\",\r\n          label: \"中和服务\",\r\n        },\r\n        {\r\n          value: \"5\",\r\n          label: \"星碳培训\",\r\n        },\r\n        {\r\n          value: \"6\",\r\n          label: \"绿色会议\",\r\n        },\r\n        {\r\n          value: \"7\",\r\n          label: \"数据建模\",\r\n        },\r\n        {\r\n          value: \"8\",\r\n          label: \"资产管理\",\r\n        },\r\n      ],\r\n      flag: \"全部\",\r\n      solutionListData: [],\r\n    };\r\n  },\r\n  created() {\r\n    // this.initData();\r\n    // this.getDictsList(\"activity_type\", \"activityTypeList\");\r\n    // this.search();\r\n  },\r\n  methods: {\r\n    getList(id) {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        grounding: 0,\r\n        parentId: 1,\r\n        searchStr: this.form.keywords,\r\n        typeId: id,\r\n      };\r\n      solutionList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res.rows, \"-------------\");\r\n          this.solutionListData = res.rows;\r\n          if (!id) {\r\n            this.allAmounts = res.total;\r\n          }\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    initData() {\r\n      let params = {\r\n        type: \"industry\",\r\n      };\r\n      solutionType(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.getTypeName(res.data);\r\n        }\r\n      });\r\n      // getDicts(\"case_industry\").then((res) => {\r\n      //   const { code, data = [] } = res;\r\n      //   if (code === 200) {\r\n      //     this.caseTypeList = data;\r\n      //     this.getCaseList();\r\n      //   }\r\n      // });\r\n    },\r\n    getTypeName(list) {\r\n      let params = {\r\n        parentId: 1,\r\n      };\r\n      solutionTypeName(params).then((res) => {\r\n        if (res.code === 200) {\r\n          for (var i = 0; i < res.rows.length; i++) {\r\n            for (var k = 0; k < res.rows.length; k++) {\r\n              if (res.rows[i].typeName == list[k].key) {\r\n                res.rows[i].value = list[k].value;\r\n              }\r\n            }\r\n          }\r\n          this.solutionTypeList = res.rows;\r\n          this.getList();\r\n        }\r\n      });\r\n    },\r\n    getItemData(item) {\r\n      this.flag = item.typeName;\r\n      this.getList(item.id);\r\n    },\r\n    goDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/solutionDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #ffffff;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 500px;\r\n    position: relative;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    .bannerTitle {\r\n      position: absolute;\r\n      top: 161px;\r\n      left: 24%;\r\n      font-size: 50px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n    }\r\n    .bannerDesc {\r\n      position: absolute;\r\n      top: 249px;\r\n      left: 24%;\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    // padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      margin-top: 40px;\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    padding: 8% 0;\r\n    background: #fff;\r\n    margin-top: 25px;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .solutionContent {\r\n    width: 1200px;\r\n    background-color: rgb(252, 252, 252);\r\n    margin: 40px auto;\r\n    display: flex;\r\n    .solutionLeft {\r\n      width: 172px;\r\n      height: 100%;\r\n      overflow-y: auto;\r\n      .itemType {\r\n        width: 100%;\r\n        height: 24px;\r\n        margin-top: 42px;\r\n        padding-left: 30px;\r\n        cursor: pointer;\r\n      }\r\n      .itemTypeHover {\r\n        border-left: 4px solid #21c9b8;\r\n        color: #21c9b8;\r\n      }\r\n      .itemType:nth-child(1) {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n    .solutionRight {\r\n      width: 1000px;\r\n      margin-left: 20px;\r\n      // display: flex;\r\n      // flex-wrap: wrap;\r\n      margin: 0 auto;\r\n      .itemContent {\r\n        width: 490px;\r\n        height: 190px;\r\n        padding: 30px 42px 30px 26px;\r\n        display: flex;\r\n        background: #ffffff;\r\n        margin-left: 20px;\r\n        margin-top: 20px;\r\n        cursor: pointer;\r\n      }\r\n      .itemContent:hover {\r\n        box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);\r\n        border-radius: 2px;\r\n      }\r\n      .itemContent:nth-child(2n + 1) {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n    // .content_left {\r\n    //   width: 130px;\r\n    //   height: 130px;\r\n    //   background: #ccc;\r\n    // }\r\n    .content_right {\r\n      width: 402px;\r\n      height: 130px;\r\n      margin-left: 20px;\r\n      .title {\r\n        font-size: 20px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #222222;\r\n        margin-top: 20px;\r\n      }\r\n      .desc {\r\n        margin-top: 24px;\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #65676a;\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    padding: 24px 0 60px;\r\n    .activity-page-btn {\r\n      width: 82px;\r\n      height: 32px;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      border: 1px solid #d9d9d9;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #333;\r\n      line-height: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}