<template>
  <div class="activity-container">
    <div>
      <div style="background-color: #fff">
        <div class="info-top">
          <div style="width: 1128px; margin: 0px auto">
            <div
              style="width: 797px; display: inline-block; vertical-align: top"
            >
              <div style="margin-top: 60px; width: 100%">
                <span
                  style="
                    color: rgba(51, 51, 51, 1);
                    font-size: 42px;
                    line-height: 66px;
                  "
                  >{{ info.appName }}</span
                >
                <span
                  style="
                    line-height: 34px;
                    border-radius: 2px;
                    background-color: #21c9b8;
                    color: #fff;
                    font-size: 14px;
                    margin: 16px 30px;
                    padding: 0px 10px;
                  "
                  >{{ info.appLabel }}</span
                >
              </div>
              <div
                style="
                  color: #21c9b8;
                  font-size: 16px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                "
              >
                {{ info.briefInto }}
              </div>
              <div
                style="
                  line-height: 36px;
                  color: #21c9b8;
                  font-size: 20px;
                  margin: 20px 0;
                "
              >
                {{ info.price }}元起 / 年
              </div>
              <div style="margin-top: 30px">
                <span
                  class="btn"
                  style="background: #21c9b8; color: #fff"
                  @click="goumai"
                  >立即订阅</span
                >
                <span class="btn" @click="collect" v-if="info.issub == 0"
                  >立即收藏</span
                >
                <span class="btn" @click="cancelCollect" v-else>已收藏</span>
                <!--							<span class="btn" @click="getUrl()">跳转使用</span>-->
              </div>
            </div>

            <div class="right-info" v-show="info.supply !== ''">
              <div>
                <p style="padding-top: 30px">应用提供：{{ info.supply }}</p>
                <p>联系人：{{ info.linkman }}</p>
                <p>联系电话：{{ info.phone }}</p>
              </div>
            </div>
          </div>
        </div>
        <div style="width: 1226px; margin: 60px auto">
          <h3
            style="
              line-height: 30px;
              color: #333;
              font-size: 20px;
              font-weight: 400;
              padding-bottom: 20px;
            "
          >
            <span
              style="
                display: inline-block;
                vertical-align: top;
                height: 20px;
                width: 3px;
                background-color: rgba(247, 154, 71, 100);
                border-radius: 3px;
                margin: 5px 18px 5px 0px;
              "
            ></span>
            应用介绍
          </h3>
          <div class="appliDetail" v-html="info.content"></div>
        </div>
      </div>
    </div>
    <div class="tishi_bg" v-if="showGm">
      <div class="goumai_bg">
        <div class="goumai_t">
          <span
            ><a href="javascript:void(0)" @click="showGm = false"
              ><img src="../../assets/images/close2.png" /></a></span
          >获取企业研发平台
        </div>
        <div class="goumai_c">
          <div class="appLabel">
            <a href="#">{{ info.appLabel }}</a>
          </div>
          <div class="briefInto">
            <p>应用编号：{{ info.appCode }}</p>
            <p>应用提供：{{ info.supply }}</p>
            <!-- <p>联系电话：{{ detail.phone }}</p>
            <p>联系人：{{ detail.linkman }}</p> -->
          </div>
          <div class="goumai_cont">
            <!-- <p>
              选择规格:
              <span @click="getSpec('1')" style="cursor: pointer">
                <el-tag :type="specFlag == '1' ? 'success' : 'info'"
                  >基础版</el-tag
                ></span
              >
              <span
                @click="getSpec('2')"
                style="margin-left: 10px; cursor: pointer"
              >
                <el-tag :type="specFlag == '2' ? 'success' : 'info'"
                  >高级版</el-tag
                ></span
              >
            </p> -->
            <p>
              可用时长:
              <span @click="getDuration('1')" style="cursor: pointer">
                <el-tag :type="duFlag == '1' ? 'success' : 'info'">一年</el-tag>
              </span>
              <span
                @click="getDuration('2')"
                style="margin-left: 10px; cursor: pointer"
              >
                <el-tag :type="duFlag == '2' ? 'success' : 'info'">永久</el-tag>
              </span>
            </p>
            <p>可用人数: 不限</p>
          </div>
          <div class="goumai_total">
            <div class="goumai_total_l">
              <p>
                总价:<strong>￥{{ info.price }}</strong>
              </p>
            </div>
            <div class="goumai_total_r">
              <a href="javascript:void(0)" @click="showGm = false">取消</a
              ><a href="javascript:void(0)" @click="order">立即下单</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  appliDetail,
  appliOrder,
  appliCollect,
  appliCancelCollect,
} from "@/api/appliMarket";
import store from "@/store";

export default {
  name: "purchaseapp",
  data() {
    return {
      info: {},
      detail: [],
      showGm: false,
      team: "",
      specFlag: "1",
      duFlag: "1",
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      let id = this.$route.query.id;
      let params = {
        id,
        userId: store.getters.userId,
      };
      appliDetail(params).then((res) => {
        if (res.code === 200) {
          this.info = res.data;
        }
      });
    },
    goumai() {
      this.showGm = true;
      // if (!window.sessionStorage.getItem('token')) {
      // 	alert('请先登录')
      // 	let url;
      // 	var str = window.location.href;
      // 	var result = encodeURIComponent(str)
      // 	if (window.location.host == 'test.ningmengdou.com') {
      // 		// url = "https://ssotest.ningmengdou.com/single/login?returnUrl=" + result
      // 		url = "https://sso.ningmengdou.com/single/newLogin?returnUrl=" + result
      // 	} else if (window.location.host == 'www.ningmengdou.com') {
      // 		// url = "https://sso.ningmengdou.com/single/login?returnUrl=" + result
      // 		url = "https://sso.ningmengdou.com/single/newLogin?returnUrl=" + result
      // 	}
      // 	window.location.href = url
      // 	return
      // }
      // this.showGm = true
    },
    collect() {
      let data = {
        id: this.info.id,
        userId: store.getters.userId,
      };
      appliCollect(data).then((res) => {
        if (res.code === 200) {
          this.$message.success("操作成功!");
          this.getDetail();
        }
      });
    },
    cancelCollect() {
      let data = {
        id: this.info.id,
        userId: store.getters.userId,
      };
      appliCancelCollect(data).then((res) => {
        if (res.code === 200) {
          this.$message.success("操作成功!");
          this.getDetail();
        }
      });
    },
    getSpec(value) {
      console.log(value);
      this.specFlag = value;
    },
    order() {
      let data = {
        appId: this.info.id,
        price: this.info.price,
        totalAmount: this.info.price,
        spec: this.specFlag,
        validTime: this.duFlag,
        phone: this.info.phone,
        remark: this.info.appName,
      };
      appliOrder(data).then((res) => {
        if (res.code === 200) {
          this.showGm = false;
          this.$message.success("下单成功!");
          this.$router.push({
            path: "/payment",
            query: {
              id: res.data.id,
            },
          });
        }
      });
    },
    getDuration(value) {
      this.duFlag = value;
    },
  },
};
</script>

<style lang="scss" scoped>
.info-top {
  background-image: url("../../assets/images/appbanner.png");
  height: 360px;
  background-size: 100%;
}
.info-top span {
  display: inline-block;
  text-align: center;
  vertical-align: top;
}
.info-top .right-info {
  display: inline-block;
  vertical-align: top;
  width: 331px;
  height: 326px;
  background-image: url("../../assets/images/appbannersub.png");
  background-size: 100%;
  margin-top: 8px;
  float: right;
}
.info-top .right-info > div {
  width: 304px;
  height: 184px;
  margin-top: 80px;
  margin-left: 20px;
  opacity: 0.75;
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 1);
}
.info-top .right-info > div p {
  line-height: 36px;
  padding-left: 30px;
  padding-bottom: 8px;
  color: rgba(102, 102, 102, 1);
  font-size: 14px;
  text-align: left;
}
.info-top span.btn {
  cursor: pointer;
  width: 104px;
  height: 36px;
  line-height: 36px;
  border-radius: 4px;
  color: #21c9b8;
  font-size: 16px;
  border: 1px solid #21c9b8;
  margin-right: 20px;
}
.tishi_bg {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.6);
  z-index: 100;
  top: 0;
  left: 0;
}
.goumai_bg {
  width: 600px;
  position: relative;
  margin-top: 8%;
  margin-right: auto;
  margin-bottom: 0;
  margin-left: auto;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0px 0px 4px 0px rgb(51 51 51 / 20%);
  padding: 0px;
  overflow: hidden;
}
.goumai_t {
  background-color: #eee;
  color: #333;
  font-size: 18px;
  line-height: 50px;
  padding-right: 30px;
  padding-left: 30px;
}

.goumai_t span {
  float: right;
}

.goumai_t span img {
  width: 25px;
  margin-top: 12px;
}

.goumai_c {
  padding: 30px;
}
.appLabel a {
  color: #de791b;
}
.goumai_cont {
  border-top-width: 0px;
  border-right-width: 0px;
  border-bottom-width: 1px;
  border-left-width: 0px;
  border-top-style: solid;
  border-right-style: solid;
  border-bottom-style: solid;
  border-left-style: solid;
  border-top-color: #ccc;
  border-right-color: #ccc;
  border-bottom-color: #ccc;
  border-left-color: #ccc;
  padding-top: 10px;
  padding-bottom: 10px;
}

.goumai_cont p {
  padding-top: 5px;
  padding-bottom: 5px;
}
.goumai_total {
  padding-top: 15px;
  display: flex;
  flex-flow: row wrap;
  justify-content: space-between;
}

.goumai_total_l {
  width: 60%;
}

.goumai_total_l strong {
  color: #f00;
  font-size: 24px;
}

.goumai_total_r {
  width: 40%;
  text-align: right;
}

.goumai_total_r a {
  display: inline-block;
  line-height: 35px;
  text-align: center;
  border: 1px solid #ccc;
  width: 40%;
  margin-top: 0px;
  margin-right: 0px;
  margin-bottom: 0px;
  margin-left: 2%;
}

.goumai_total_r a:hover {
  color: #fff;
  background-color: #21c9b8;
  border: 1px solid #21c9b8;
}
::v-deep .appliDetail img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>
