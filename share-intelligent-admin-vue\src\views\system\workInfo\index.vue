<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="车间名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入车间名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属单位" prop="company">
        <el-input
          v-model="queryParams.company"
          placeholder="请输入所属单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车间面积" prop="area">
        <el-input
          v-model="queryParams.area"
          placeholder="请输入车间面积"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="参考价格" prop="price">
        <el-input
          v-model="queryParams.price"
          placeholder="请输入参考价格"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:workInfo:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:workInfo:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:workInfo:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:workInfo:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="workInfoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="车间ID" align="center" prop="id" />
      <el-table-column label="车间名称" align="center" prop="name" />
      <el-table-column label="车间类型" align="center">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.workshop_type"
            :value="scope.row.type"
          />
        </template>
      </el-table-column>
      <el-table-column label="所属单位" align="center" prop="company" />
      <el-table-column label="车间地址" align="center" prop="address" />
      <el-table-column label="审核状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.checkStatus == 1 ? "已审核" : "待审核" }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="车间面积" align="center" prop="area" />
      <el-table-column label="参考价格" align="center" prop="price" />
      <el-table-column label="车间概况" align="center" prop="description" />
      <el-table-column label="设备资源" align="center" prop="resources" />
      <el-table-column label="生产能力" align="center" prop="capability" />
      <el-table-column label="注意事项" align="center" prop="notes" />
      <el-table-column label="车间图片" align="center" prop="images" /> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:workInfo:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:workInfo:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改车间信息对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1200px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="车间名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入车间名称" />
        </el-form-item>
        <el-form-item label="车间类型" prop="type">
          <el-select
            v-model="form.type"
            placeholder="请选择车间类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in dict.type.workshop_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属单位" prop="company">
          <el-input v-model="form.company" placeholder="请输入所属单位" />
        </el-form-item>
        <el-form-item label="车间地址" prop="address">
          <el-input
            v-model="form.address"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="车间面积" prop="area">
          <el-input v-model="form.area" placeholder="请输入车间面积" />
        </el-form-item>
        <el-form-item label="参考价格" prop="price">
          <el-input v-model="form.price" placeholder="请输入参考价格" />
        </el-form-item>
        <el-form-item label="车间概况" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="设备资源" prop="resources">
          <el-input
            v-model="form.resources"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="生产能力" prop="capability">
          <el-input
            v-model="form.capability"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="注意事项" prop="notes">
          <el-input
            v-model="form.notes"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="车间图片" prop="images">
          <!-- <el-upload class="avatar-uploader" list-type="picture-card" action="" :show-file-list="false"
            :http-request="uploadFun" :before-upload="beforeAvatarUpload">
            <el-image v-if="form.images" :src="form.images" class="avatar">
              <div slot="error" class="image-slot">
                <span>暂无图片</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload> -->
          <ImageUpload v-model="form.images"></ImageUpload>
        </el-form-item>
        <el-form-item label="审核状态" prop="settledStatus">
          <el-select
            v-model="form.checkStatus"
            placeholder="请选择"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in auditStatusList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listWorkInfo,
  getWorkInfo,
  delWorkInfo,
  addWorkInfo,
  updateWorkInfo,
} from "@/api/system/workInfo";
import { comUpload } from "@/api/portalconsole/uploadApi";

export default {
  name: "WorkInfo",
  dicts: ["workshop_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 车间信息表格数据
      workInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        company: null,
        address: null,
        area: null,
        price: null,
        description: null,
        resources: null,
        capability: null,
        notes: null,
        images: null,
        type: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "车间名称不能为空", trigger: "blur" },
        ],
        company: [
          { required: true, message: "所属单位不能为空", trigger: "blur" },
        ],
        address: [
          { required: true, message: "车间地址不能为空", trigger: "blur" },
        ],
        type: [
          { required: true, message: "车间类型不能为空", trigger: "change" },
        ],
      },
      workMenuList: [
        {
          dictValue: "0",
          dictLabel: "生产车间",
        },
        {
          dictValue: "1",
          dictLabel: "中试车间",
        },
      ],
      auditStatusList: [
        {
          dictValue: 0,
          dictLabel: "待审核",
        },
        {
          dictValue: 1,
          dictLabel: "已审核",
        },
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询车间信息列表 */
    getList() {
      this.loading = true;
      listWorkInfo(this.queryParams).then((response) => {
        this.workInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        company: null,
        address: null,
        area: null,
        price: null,
        description: null,
        resources: null,
        capability: null,
        notes: null,
        images: null,
        type: null,
        createTime: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加车间信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getWorkInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改车间信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateWorkInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWorkInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除车间信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delWorkInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/workInfo/export",
        {
          ...this.queryParams,
        },
        `workInfo_${new Date().getTime()}.xlsx`
      );
    },
    // 图片上传
    uploadFun(params) {
      const file = params.file;
      let form = new FormData();
      form.append("file", file); // 文件对象
      comUpload(form).then((res) => {
        let data = res.data;
        this.form.images = data.fileFullPath;
      });
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg" || "image/png" || "image/jpg";
      const isLt2M = file.size / 1024 / 1024 < 50;
      if (!isJPG) {
        this.$message.error("上传图片只能是 jpg、jpeg、png 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 50MB!");
      }
      return isJPG && isLt2M;
    },
  },
};
</script>
