{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\supplyTab.vue?vue&type=style&index=0&id=561bfa04&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\supplyTab.vue", "mtime": 1750311962932}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["supplyTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "supplyTab.vue", "sourceRoot": "src/views/components/home/<USER>", "sourcesContent": ["<template>\r\n  <div class=\"supply-tab-container\">\r\n    <div v-loading=\"loading\" class=\"tab-main\">\r\n      <el-scrollbar noresize class=\"left\">\r\n        <div class=\"tab-content\">\r\n          <div\r\n            v-for=\"(item, index) in tabs\"\r\n            :key=\"index\"\r\n            :class=\"{ active: tabIndex === index }\"\r\n            class=\"tab-content-item\"\r\n            @click=\"onTabChange(index)\"\r\n          >\r\n            {{ item.dictLabel }}\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n      <el-row class=\"right\" :gutter=\"24\">\r\n        <template v-if=\"items.length > 0\">\r\n          <el-col :span=\"8\" v-for=\"item in items\" :key=\"item.id\">\r\n            <router-link\r\n              target=\"_blank\"\r\n              :to=\"`/resourceHallDetail?id=${item.id}`\"\r\n            >\r\n              <div class=\"card\">\r\n                <el-image\r\n                  class=\"card-img\"\r\n                  :src=\"item.url ? item.url : defaultUrl\"\r\n                  fit=\"fill\"\r\n                />\r\n                <div class=\"card-footer\">\r\n                  <div class=\"title\" :title=\"item.title\">{{ item.title }}</div>\r\n                  <div class=\"subtitle\">{{ item.company }}</div>\r\n                </div>\r\n              </div>\r\n            </router-link>\r\n          </el-col>\r\n        </template>\r\n        <template v-else>\r\n          <el-empty />\r\n        </template>\r\n      </el-row>\r\n    </div>\r\n    <div class=\"tab-page-end\">\r\n      <!-- <span class=\"demonstration\">完整功能</span> -->\r\n      <el-pagination\r\n        class=\"supply-tab-pagination\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"pageNum\"\r\n        :page-sizes=\"[100, 200, 300, 400]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\" prev, pager, next \"\r\n        :total=\"total\"\r\n      >\r\n      </el-pagination>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { head, map } from \"ramda\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { listSupply } from \"@/api/zhm/supply\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  name: \"SupplyTab\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tabs: [],\r\n      tabIndex: 0,\r\n      items: [],\r\n      pageNum: 1,\r\n      pageSize: 6,\r\n      total: 0,\r\n      defaultUrl: require(\"../../../../assets/resourceHall/resourceHallDetailBanner.png\"),\r\n    };\r\n  },\r\n  created() {\r\n    this.getSupplyData();\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      getDicts(\"supply_type\").then((res) => {\r\n        const { code, data = [] } = res;\r\n        if (code === 200) {\r\n          this.tabs = data;\r\n          this.tabs.unshift({\r\n            dictLabel: \"全部\",\r\n            dictValue: undefined,\r\n          });\r\n          const item = head(data);\r\n          this.getSupplyData(item.dictValue);\r\n        }\r\n      });\r\n    },\r\n    getSupplyData(type) {\r\n      this.loading = true;\r\n      listSupply({\r\n        supplyType: type,\r\n        displayStatus: 1,\r\n        auditStatus: 2,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          const { code, rows = [] } = res;\r\n          if (code === 200) {\r\n            this.items = map((item) => {\r\n              let url;\r\n              const images = JSON.parse(item.productPhoto) || [];\r\n              if (images.length > 0) {\r\n                url = head(images).url;\r\n              }\r\n              return {\r\n                id: item.id,\r\n                title: item.supplyName,\r\n                company: item.companyName,\r\n                url,\r\n              };\r\n            }, rows);\r\n          }\r\n          this.total = res.total;\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    onTabChange(index) {\r\n      if (index !== this.tabIndex) {\r\n        this.tabIndex = index;\r\n        this.pageNum = 1;\r\n        const item = this.tabs[index] || {};\r\n        this.getSupplyData(item.dictValue);\r\n      }\r\n    },\r\n    handleSizeChange(newSize) {\r\n      this.pageSize = newSize;\r\n      const item = this.tabs[this.tabIndex] || {};\r\n      this.getSupplyData(item.dictValue);\r\n    },\r\n    handleCurrentChange(newPage) {\r\n      this.pageNum = newPage;\r\n      const item = this.tabs[this.tabIndex] || {};\r\n      this.getSupplyData(item.dictValue);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n\r\n.supply-tab-container {\r\n  .tab-main {\r\n    position: relative;\r\n    display: flex;\r\n    flex-shrink: 0;\r\n    width: 100%;\r\n    flex-direction: row;\r\n    ::v-deep .el-scrollbar__wrap {\r\n      overflow-x: hidden;\r\n      overflow-y: auto;\r\n    }\r\n    .left {\r\n      width: 148px;\r\n      height: 580px;\r\n      background: #21c9b8;\r\n      .tab-content {\r\n        padding: 24px 0 24px 18px;\r\n        &-item {\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          flex-shrink: 0;\r\n          height: 40px;\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #ffffff;\r\n          line-height: 14px;\r\n          transition: background, color 0.25ms ease;\r\n          margin-bottom: 12px;\r\n          cursor: pointer;\r\n          &.active {\r\n            color: #21c9b8;\r\n            background: linear-gradient(270deg, #fbfdff 0%, #ffffff 100%);\r\n          }\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .right {\r\n      flex: 1;\r\n      padding-left: 36px;\r\n      .card {\r\n        width: 100%;\r\n        min-height: 318px;\r\n        background: #ffffff;\r\n        box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.1);\r\n        margin-bottom: 24px;\r\n        &-img {\r\n          width: 100%;\r\n          height: 200px;\r\n          background: #ffffff;\r\n        }\r\n        &-footer {\r\n          padding: 16px 24px;\r\n          .title {\r\n            // @include multiEllipsis(2);\r\n            @include ellipsis;\r\n            font-size: 18px;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            line-height: 26px;\r\n            margin-bottom: 12px;\r\n          }\r\n          .subtitle {\r\n            @include ellipsis;\r\n            font-size: 14px;\r\n            font-weight: 400;\r\n            color: #666666;\r\n            line-height: 14px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.supply-tab-container {\r\n  .tab-page-end {\r\n    .supply-tab-pagination {\r\n      width: 240px;\r\n      margin: 0 auto;\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #ffffff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        margin: 0 6px;\r\n        color: #333;\r\n      }\r\n      .el-pager {\r\n        .number {\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 1px solid #d9d9d9;\r\n          background: #ffffff;\r\n          border-radius: 4px;\r\n          line-height: 32px;\r\n          margin: 0 6px;\r\n          &.active {\r\n            background: #21c9b8;\r\n            border: 1px solid #21c9b8;\r\n            color: #fff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}