17:44:10.342 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:44:11.613 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0
17:44:11.699 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 46 ms to scan 1 urls, producing 3 keys and 6 values 
17:44:11.749 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
17:44:11.762 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 3 keys and 10 values 
17:44:11.950 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 185 ms to scan 235 urls, producing 0 keys and 0 values 
17:44:11.961 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
17:44:11.976 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
17:44:11.990 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
17:44:12.174 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 181 ms to scan 235 urls, producing 0 keys and 0 values 
17:44:12.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:44:12.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/180489140
17:44:12.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1512822728
17:44:12.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:44:12.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:44:12.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:44:14.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153454050_127.0.0.1_50078
17:44:14.469 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0] Notify connected event to listeners.
17:44:14.470 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:44:14.470 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/2077845538
17:44:14.660 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:44:23.264 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
17:44:23.740 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0
17:44:23.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] RpcClient init label, labels = {module=naming, source=sdk}
17:44:23.746 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:44:23.747 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:44:23.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:44:23.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:44:23.867 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153463752_127.0.0.1_50173
17:44:23.868 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Notify connected event to listeners.
17:44:23.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:44:23.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/2077845538
17:44:24.316 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
17:44:25.104 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0
17:44:25.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:44:25.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/180489140
17:44:25.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1512822728
17:44:25.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:44:25.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:44:25.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:44:25.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153465110_127.0.0.1_50177
17:44:25.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:44:25.227 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0] Notify connected event to listeners.
17:44:25.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/2077845538
17:44:25.951 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Receive server push request, request = NotifySubscriberRequest, requestId = 6
17:44:25.952 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Ack server push request, request = NotifySubscriberRequest, requestId = 6
17:44:26.232 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Receive server push request, request = NotifySubscriberRequest, requestId = 7
17:44:26.232 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Ack server push request, request = NotifySubscriberRequest, requestId = 7
17:44:26.496 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
17:44:26.534 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 17.121 seconds (JVM running for 18.594)
17:44:26.540 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
17:44:26.540 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
17:44:26.541 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
17:44:26.998 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Receive server push request, request = NotifySubscriberRequest, requestId = 8
17:44:27.001 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Ack server push request, request = NotifySubscriberRequest, requestId = 8
17:50:56.452 [nacos-grpc-client-executor-149] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Receive server push request, request = NotifySubscriberRequest, requestId = 13
17:50:56.452 [nacos-grpc-client-executor-149] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Ack server push request, request = NotifySubscriberRequest, requestId = 13
17:50:56.453 [nacos-grpc-client-executor-150] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Receive server push request, request = NotifySubscriberRequest, requestId = 15
17:50:56.453 [nacos-grpc-client-executor-150] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Ack server push request, request = NotifySubscriberRequest, requestId = 15
17:50:56.455 [nacos-grpc-client-executor-151] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Receive server push request, request = NotifySubscriberRequest, requestId = 14
17:50:56.455 [nacos-grpc-client-executor-151] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Ack server push request, request = NotifySubscriberRequest, requestId = 14
18:35:14.658 [lettuce-nioEventLoop-5-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:258)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:722)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:584)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:496)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
18:35:14.800 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /127.0.0.1:6379
18:35:14.884 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:35:14.852 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:35:14.886 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:35:19.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [585d62ba-fe7d-4b85-b646-f68e6cb18e2c_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
18:35:19.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c8f2fbe-3b07-49f5-bbcc-6387b93a67b0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
18:35:19.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d3d82f6-3a03-436b-afd3-85f02728eca1_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
