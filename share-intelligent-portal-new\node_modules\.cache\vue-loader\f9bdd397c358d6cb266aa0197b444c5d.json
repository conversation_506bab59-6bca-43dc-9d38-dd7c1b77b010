{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\productOrderDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\productOrderDetail.vue", "mtime": 1750311962969}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBvcmRlckRldGFpbERhdGEgfSBmcm9tICJAL2FwaS9tYW51ZmFjdHVyaW5nU2hhcmluZyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogImRldmljZURldGFpbCIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGRldGFpbHNEYXRhOiB7fSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuaWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsNCiAgICB0aGlzLmdldERldGFpbERhdGEoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldERldGFpbERhdGEoKSB7DQogICAgICBvcmRlckRldGFpbERhdGEodGhpcy5pZCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5kZXRhaWxzRGF0YSA9IHJlcy5kYXRhOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHRha2VPcmRlcihpZCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9yZWNlaXZlT3JkZXIiKTsgLy8g5LygaWQNCiAgICB9LA0KICAgIGp1bXBJbnRlbnRpb24oKSB7DQogICAgICBsZXQgdXNlckluZm8gPSBKU09OLnBhcnNlKHNlc3Npb25TdG9yYWdlLmdldEl0ZW0oInVzZXJpbmZvIikpOw0KICAgICAgaWYgKCF1c2VySW5mbz8ubWVtYmVyQ29tcGFueU5hbWUpIHsNCiAgICAgICAgdGhpcy4kY29uZmlybSgi5oKo5b2T5YmN5bCa5pyq5YWz6IGU5LyB5Lia77yM5piv5ZCm5YmN5b6A5pON5L2cPyIsICLmj5DnpLoiLCB7DQogICAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgICAgICBjYW5jZWxCdXR0b25DbGFzczogImNhbmNlbEJ1dHRvbkNsYXNzIiwNCiAgICAgICAgICBjb25maXJtQnV0dG9uQ2xhc3M6ICJjdXN0b21DbGFzcyIsDQogICAgICAgIH0pDQogICAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi91c2VyL3VzZXJDZW50ZXIiKTsNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5jYXRjaCgoKSA9PiB7IH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaChgL3JlY2VpdmVPcmRlcj9kZW1hbmROYW1lPSR7dGhpcy5kZXRhaWxzRGF0YS5kZW1hbmRDb21wYW55fSZ1cGRhdGVUaW1lPSR7dGhpcy5kZXRhaWxzRGF0YS51cGRhdGVUaW1lfSZpbnRlbnRpb25UeXBlPTEyJmZpZWxkTmFtZT3nlJ/miJDorqLljZUmaW50ZW50aW9uSWQ9JHt0aGlzLmRldGFpbHNEYXRhLmlkfWApOw0KICAgICAgfQ0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["productOrderDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmJA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "productOrderDetail.vue", "sourceRoot": "src/views/manufacturingSharing/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"card-container cardStyle\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <div class=\"card_left_bottom\">\r\n          <div class=\"title\">{{ detailsData.demandCompany }}</div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">联系人：</div>\r\n            <div class=\"optionValue\">{{ detailsData.contactPerson }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">联系电话：</div>\r\n            <div class=\"optionValue\">{{ detailsData.contactPhone }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">交货地址：</div>\r\n            <div class=\"optionValue\">{{ detailsData.deliveryAddress }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">需求截至时间：</div>\r\n            <div class=\"optionValue\">{{ detailsData.deadline }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">订单状态：</div>\r\n            <div class=\"optionValue\">\r\n              {{\r\n                detailsData.status == \"0\"\r\n                  ? \"未接单\"\r\n                  : detailsData.status == \"1\"\r\n                    ? \"进行中\"\r\n                    : \"已完成\"\r\n              }}\r\n            </div>\r\n          </div>\r\n          <div class=\"buttonStyle\" @click=\"jumpIntention\">我要接单</div>\r\n        </div>\r\n      </div>\r\n      <!-- 中间 -->\r\n      <div class=\"card_center_line\"></div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">需求物料</div>\r\n          </div>\r\n          <div style=\"margin-top: 22px\">\r\n            <el-table :data=\"detailsData.materials\" style=\"margin-top: 20px\">\r\n              <el-table-column label=\"物料名称\" align=\"center\" prop=\"name\">\r\n              </el-table-column>\r\n              <el-table-column label=\"规格型号\" align=\"center\" prop=\"modelNumber\">\r\n              </el-table-column>\r\n              <el-table-column label=\"数量\" align=\"center\" prop=\"quantity\">\r\n              </el-table-column>\r\n              <el-table-column label=\"单位\" align=\"center\" prop=\"unit\">\r\n              </el-table-column>\r\n              <el-table-column label=\"可承接量\" align=\"center\" prop=\"capacity\">\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">交付要求</div>\r\n          </div>\r\n          <div style=\"margin-top: 22px\">\r\n            <el-descriptions class=\"margin-top\" title=\"\" :column=\"2\" border>\r\n              <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 压力 </template>\r\n60Mpa\r\n</el-descriptions-item>\r\n<el-descriptions-item>\r\n  <template slot=\"label\"> 温度 </template>\r\n  18100000000\r\n</el-descriptions-item>\r\n<el-descriptions-item>\r\n  <template slot=\"label\"> 尺寸 </template>\r\n  3.5m\r\n</el-descriptions-item> -->\r\n              <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 规格型号 </template>\r\n                T-565487\r\n              </el-descriptions-item> -->\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 拦标价 </template>\r\n                <!-- {{ detailsData.deliveryAddress }} -->\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 是否允许接单 </template>\r\n                是\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 交付时间 </template>\r\n                <!-- 2025-12-05 -->\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 需求截至时间： </template>\r\n                {{ detailsData.deadline }}\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">基本信息</div>\r\n          </div>\r\n          <div style=\"margin-top: 22px\">\r\n            <el-descriptions class=\"margin-top\" title=\"\" :column=\"2\" border>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 需求企业 </template>\r\n                {{ detailsData.demandCompany }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 联系人 </template>\r\n                {{ detailsData.contactPerson }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 联系电话 </template>\r\n                {{ detailsData.contactPhone }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 交货地址 </template>\r\n                {{ detailsData.deliveryAddress }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item :span=\"2\">\r\n                <template slot=\"label\"> 交货要求 </template>\r\n                {{ detailsData.fileRequirement }}\r\n              </el-descriptions-item>\r\n              <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 开户行 </template>\r\n                {{ detailsData.bankName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 付款账号 </template>\r\n                {{ detailsData.paymentAccount }}\r\n              </el-descriptions-item> -->\r\n            </el-descriptions>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { orderDetailData } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"deviceDetail\",\r\n  data() {\r\n    return {\r\n      detailsData: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getDetailData();\r\n  },\r\n  methods: {\r\n    getDetailData() {\r\n      orderDetailData(this.id).then((res) => {\r\n        if (res.code === 200) {\r\n          this.detailsData = res.data;\r\n        }\r\n      });\r\n    },\r\n    takeOrder(id) {\r\n      this.$router.push(\"/receiveOrder\"); // 传id\r\n    },\r\n    jumpIntention() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(`/receiveOrder?demandName=${this.detailsData.demandCompany}&updateTime=${this.detailsData.updateTime}&intentionType=12&fieldName=生成订单&intentionId=${this.detailsData.id}`);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: #f2f2f2;\r\n  padding: 30px 0 61px 0;\r\n}\r\n\r\n.cardStyle {\r\n  height: 100%;\r\n  // height: 660px;\r\n  background-color: #ffffff;\r\n  padding: 60px 56px 54px 50px;\r\n  display: flex;\r\n}\r\n\r\n.card_left {\r\n  .card_left_bottom {\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      font-size: 20px;\r\n      color: #222222;\r\n      margin-bottom: 25px;\r\n    }\r\n\r\n    .everyOption {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 12px;\r\n\r\n      .optionName {\r\n        // height: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #999999;\r\n      }\r\n\r\n      .optionValue {\r\n        // height: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #333333;\r\n      }\r\n    }\r\n\r\n    .buttonStyle {\r\n      margin-top: 32px;\r\n      // margin-left: 55px;\r\n      width: 220px;\r\n      height: 50px;\r\n      background: #21c9b8;\r\n      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);\r\n      border-radius: 2px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      text-align: center;\r\n      line-height: 50px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.card_center_line {\r\n  width: 1px;\r\n  height: 660px;\r\n  background: #e1e1e1;\r\n  margin-left: 60px;\r\n  margin-right: 61px;\r\n}\r\n\r\n.card_right {\r\n  width: 100%;\r\n\r\n  // overflow-y: auto;\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}