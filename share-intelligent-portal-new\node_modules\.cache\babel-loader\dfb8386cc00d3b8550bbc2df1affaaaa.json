{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\dockingRecords\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\dockingRecords\\index.vue", "mtime": 1750311963055}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_data", "_user", "name", "components", "UserMenu", "data", "loading", "tableData", "category", "content", "company", "time", "status", "showLogin", "userinfo", "token", "cooperationList", "partnerList", "total", "pageNum", "pageSize", "activeName", "queryType", "intentionStatus", "queryParams", "companyName", "queryParams1", "intentionCompanyName", "created", "getList", "getDicts", "methods", "_this", "dockingList", "then", "res", "rows", "handleClick", "tab", "event", "handleQuery", "reset<PERSON><PERSON>y", "handleSizeChange", "handleCurrentChange", "_this2", "params", "dictType", "listData", "response", "code", "handleDelete", "row", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "deleteDocking", "id", "catch", "handleView", "console", "log", "$router", "push", "path", "query", "createTime", "intentionTypeName", "intentionContent", "completionDate", "quantity", "price", "rate", "shippingFee", "sum", "term", "title"], "sources": ["src/views/system/user/dockingRecords/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"main-content\">\r\n          <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n            <el-tab-pane label=\"我申请的\" name=\"0\">\r\n              <el-form\r\n                class=\"queryForm\"\r\n                :model=\"queryParams\"\r\n                ref=\"queryForm\"\r\n                size=\"small\"\r\n                :inline=\"true\"\r\n              >\r\n                <el-form-item label=\"企业名称\" prop=\"companyName\">\r\n                  <el-input\r\n                    v-model=\"queryParams.companyName\"\r\n                    placeholder=\"请输入企业名称\"\r\n                    clearable\r\n                  >\r\n                  </el-input>\r\n                </el-form-item>\r\n                <!-- <el-form-item label=\"状态\" prop=\"intentionStatus\">\r\n                  <el-select v-model=\"queryParams.intentionStatus\" placeholder=\"请选择\" clearable>\r\n                    <el-option v-for=\"dict in intentionStatus\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n                      :value=\"dict.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item> -->\r\n                <el-form-item>\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                  >\r\n                  <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                  >\r\n                </el-form-item>\r\n              </el-form>\r\n              <div class=\"table\">\r\n                <div style=\"width: 100%\">\r\n                  <el-table\r\n                    :data=\"cooperationList\"\r\n                    style=\"width: 100%\"\r\n                    :v-loading=\"loading\"\r\n                  >\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"intentionTypeName\"\r\n                      label=\"资源类型\"\r\n                      width=\"180\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"title\"\r\n                      label=\"资源名称\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"intentionContent\"\r\n                      label=\"申请内容\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"companyName\"\r\n                      label=\"申请公司\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"createTime\"\r\n                      label=\"申请时间\"\r\n                    >\r\n                    </el-table-column>\r\n                    <!-- <el-table-column align=\"center\" prop=\"intentionStatus\" label=\"状态\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '0'\" type=\"danger\">待回复</el-tag>\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '1'\" type=\"warning\">已申请</el-tag>\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '2'\" type=\"primary\">对接中</el-tag>\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '3'\" type=\"info\">已完结</el-tag>\r\n                      </template>\r\n                    </el-table-column> -->\r\n                    <!-- <el-table-column align=\"center\" label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button type=\"text\" size=\"small\" @click=\"handleView(scope.row)\">查看</el-button>\r\n                        <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n                      </template>\r\n                    </el-table-column> -->\r\n                  </el-table>\r\n                </div>\r\n                <!-- 分页 -->\r\n                <div class=\"pageStyle\">\r\n                  <el-pagination\r\n                    v-if=\"tableData && tableData.length > 0\"\r\n                    background\r\n                    layout=\"prev, pager, next\"\r\n                    class=\"activity-pagination\"\r\n                    :page-size=\"pageSize\"\r\n                    :current-page=\"pageNum\"\r\n                    :total=\"total\"\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                  >\r\n                  </el-pagination>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane label=\"被申请的\" name=\"1\">\r\n              <el-form\r\n                class=\"queryForm\"\r\n                :model=\"queryParams1\"\r\n                ref=\"queryForm\"\r\n                size=\"small\"\r\n                :inline=\"true\"\r\n              >\r\n                <el-form-item label=\"企业名称\" prop=\"companyName\">\r\n                  <el-input\r\n                    v-model=\"queryParams1.intentionCompanyName\"\r\n                    placeholder=\"请输入企业名称\"\r\n                    clearable\r\n                  >\r\n                  </el-input>\r\n                </el-form-item>\r\n                <!-- <el-form-item label=\"状态\" prop=\"intentionStatus\">\r\n                  <el-select v-model=\"queryParams1.intentionStatus\" placeholder=\"请选择\" clearable>\r\n                    <el-option v-for=\"dict in intentionStatus\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n                      :value=\"dict.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item> -->\r\n                <el-form-item>\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                  >\r\n                  <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                  >\r\n                </el-form-item>\r\n              </el-form>\r\n              <div class=\"table\">\r\n                <div style=\"width: 100%\">\r\n                  <el-table\r\n                    :data=\"cooperationList\"\r\n                    style=\"width: 100%\"\r\n                    :v-loading=\"loading\"\r\n                  >\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"intentionTypeName\"\r\n                      label=\"资源类型\"\r\n                      width=\"180\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"title\"\r\n                      label=\"资源名称\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"intentionContent\"\r\n                      label=\"申请内容\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"companyName\"\r\n                      label=\"供需发布公司\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"createTime\"\r\n                      label=\"申请时间\"\r\n                    >\r\n                    </el-table-column>\r\n                    <!-- <el-table-column align=\"center\" prop=\"intentionStatus\" label=\"状态\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '0'\" type=\"danger\">待回复</el-tag>\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '1'\" type=\"warning\">已申请</el-tag>\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '2'\" type=\"primary\">对接中</el-tag>\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '3'\" type=\"info\">已完结</el-tag>\r\n                      </template>\r\n                    </el-table-column> -->\r\n                    <el-table-column align=\"center\" label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button\r\n                          type=\"text\"\r\n                          size=\"small\"\r\n                          @click=\"handleView(scope.row)\"\r\n                          >查看</el-button\r\n                        >\r\n                        <el-button\r\n                          type=\"text\"\r\n                          size=\"small\"\r\n                          @click=\"handleDelete(scope.row)\"\r\n                          >删除</el-button\r\n                        >\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n                <!-- 分页 -->\r\n                <div class=\"pageStyle\">\r\n                  <el-pagination\r\n                    v-if=\"tableData && tableData.length > 0\"\r\n                    background\r\n                    layout=\"prev, pager, next\"\r\n                    class=\"activity-pagination\"\r\n                    :page-size=\"pageSize\"\r\n                    :current-page=\"pageNum\"\r\n                    :total=\"total\"\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                  >\r\n                  </el-pagination>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { dockingList } from \"@/api/system/user\";\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tableData: [\r\n        {\r\n          category: \"供给\",\r\n          name: \"玻璃钢格栅\",\r\n          content: \"申请内容\",\r\n          company: \"恒润集团有限公司\",\r\n          time: \"2025-12-05\",\r\n          status: \"待回复\",\r\n        },\r\n        {\r\n          category: \"供给\",\r\n          name: \"玻璃钢格栅\",\r\n          content: \"申请内容\",\r\n          company: \"恒润集团有限公司\",\r\n          time: \"2025-12-05\",\r\n          status: \"对接中\",\r\n        },\r\n        {\r\n          category: \"供给\",\r\n          name: \"玻璃钢格栅\",\r\n          content: \"申请内容\",\r\n          company: \"恒润集团有限公司\",\r\n          time: \"2025-12-05\",\r\n          status: \"已完结\",\r\n        },\r\n        {\r\n          category: \"供给\",\r\n          name: \"玻璃钢格栅\",\r\n          content: \"申请内容\",\r\n          company: \"恒润集团有限公司\",\r\n          time: \"2025-12-05\",\r\n          status: \"已申请\",\r\n        },\r\n      ],\r\n      showLogin: false,\r\n      userinfo: [],\r\n      token: \"\",\r\n      cooperationList: [],\r\n      partnerList: [],\r\n      total: 1,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      activeName: \"0\",\r\n      queryType: \"my\",\r\n      intentionStatus: [],\r\n      queryParams: {\r\n        companyName: \"\",\r\n        intentionStatus: \"\",\r\n      },\r\n      queryParams1: {\r\n        intentionCompanyName: \"\",\r\n        intentionStatus: \"\",\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getDicts();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      dockingList({\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        queryType: this.queryType,\r\n        companyName: this.queryParams.companyName,\r\n        intentionStatus: this.queryParams.intentionStatus,\r\n        intentionCompanyName: this.queryParams1.intentionCompanyName,\r\n      }).then((res) => {\r\n        this.cooperationList = res.rows;\r\n        this.total = res.total;\r\n      });\r\n    },\r\n    handleClick(tab, event) {\r\n      if (this.activeName === \"0\") {\r\n        this.queryType = \"my\";\r\n      } else {\r\n        this.queryType = \"\";\r\n      }\r\n      this.getList();\r\n    },\r\n    handleQuery() {\r\n      this.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams = {\r\n        companyName: \"\",\r\n        intentionStatus: \"\",\r\n      };\r\n      this.queryParams1 = {\r\n        intentionCompanyName: \"\",\r\n        intentionStatus: \"\",\r\n      };\r\n      this.getList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    getDicts() {\r\n      let params = { dictType: \"intention_status\" };\r\n      listData(params).then((response) => {\r\n        if (response.code == 200) {\r\n          this.intentionStatus = response.rows;\r\n        }\r\n      });\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm(\"是否确认删除该记录?\", \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteDocking(row.id);\r\n        })\r\n        .catch(function () {});\r\n      // let delurl = '/portalweb/IntentionApply/' + id\r\n      // YS.deleteJsonFetch(delurl, {}).then(res => {\r\n      //   alert('删除成功')\r\n      //   this.getCooperationList()\r\n      // });\r\n    },\r\n    handleView(row) {\r\n      console.log(row);\r\n      this.$router.push({\r\n        path: \"dockingRecordsDetail\",\r\n        query: {\r\n          id: row.id,\r\n          createTime: row.createTime ? row.createTime : \"\",\r\n          intentionTypeName: row.intentionTypeName ? row.intentionTypeName : \"\",\r\n          intentionContent: row.intentionContent ? row.intentionContent : \"\",\r\n          completionDate: row.completionDate ? row.completionDate : \"\",\r\n          quantity: row.quantity ? row.quantity : \"\",\r\n          price: row.price ? row.price : \"\",\r\n          rate: row.rate ? row.rate : \"\",\r\n          shippingFee: row.shippingFee ? row.shippingFee : \"\",\r\n          sum: row.sum ? row.sum : \"\",\r\n          term: row.term ? row.term : \"\",\r\n          title: row.title ? row.title : \"\",\r\n        },\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.main-content {\r\n  padding: 20px;\r\n\r\n  .table {\r\n    margin-top: 20px;\r\n    background: #fff;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    box-sizing: border-box;\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    justify-content: space-around;\r\n\r\n    .pageStyle {\r\n      width: 100%;\r\n      margin-top: 61px;\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAoPA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAG,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,SAAA,GACA;QACAC,QAAA;QACAN,IAAA;QACAO,OAAA;QACAC,OAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACAJ,QAAA;QACAN,IAAA;QACAO,OAAA;QACAC,OAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACAJ,QAAA;QACAN,IAAA;QACAO,OAAA;QACAC,OAAA;QACAC,IAAA;QACAC,MAAA;MACA,GACA;QACAJ,QAAA;QACAN,IAAA;QACAO,OAAA;QACAC,OAAA;QACAC,IAAA;QACAC,MAAA;MACA,EACA;MACAC,SAAA;MACAC,QAAA;MACAC,KAAA;MACAC,eAAA;MACAC,WAAA;MACAC,KAAA;MACAC,OAAA;MACAC,QAAA;MACAC,UAAA;MACAC,SAAA;MACAC,eAAA;MACAC,WAAA;QACAC,WAAA;QACAF,eAAA;MACA;MACAG,YAAA;QACAC,oBAAA;QACAJ,eAAA;MACA;IACA;EACA;EACAK,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,iBAAA;QACAd,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA,QAAA;QACAE,SAAA,OAAAA,SAAA;QACAG,WAAA,OAAAD,WAAA,CAAAC,WAAA;QACAF,eAAA,OAAAC,WAAA,CAAAD,eAAA;QACAI,oBAAA,OAAAD,YAAA,CAAAC;MACA,GAAAO,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAhB,eAAA,GAAAmB,GAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAd,KAAA,GAAAiB,GAAA,CAAAjB,KAAA;MACA;IACA;IACAmB,WAAA,WAAAA,YAAAC,GAAA,EAAAC,KAAA;MACA,SAAAlB,UAAA;QACA,KAAAC,SAAA;MACA;QACA,KAAAA,SAAA;MACA;MACA,KAAAO,OAAA;IACA;IACAW,WAAA,WAAAA,YAAA;MACA,KAAArB,OAAA;MACA,KAAAU,OAAA;IACA;IACAY,UAAA,WAAAA,WAAA;MACA,KAAAjB,WAAA;QACAC,WAAA;QACAF,eAAA;MACA;MACA,KAAAG,YAAA;QACAC,oBAAA;QACAJ,eAAA;MACA;MACA,KAAAM,OAAA;IACA;IACAa,gBAAA,WAAAA,iBAAAtB,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAS,OAAA;IACA;IACAc,mBAAA,WAAAA,oBAAAxB,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAU,OAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAc,MAAA;MACA,IAAAC,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAX,IAAA,WAAAc,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAL,MAAA,CAAArB,eAAA,GAAAyB,QAAA,CAAAZ,IAAA;QACA;MACA;IACA;IACAc,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACAtB,IAAA;QACAkB,MAAA,CAAAK,aAAA,CAAAN,GAAA,CAAAO,EAAA;MACA,GACAC,KAAA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAT,GAAA;MACAU,OAAA,CAAAC,GAAA,CAAAX,GAAA;MACA,KAAAY,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAC,KAAA;UACAR,EAAA,EAAAP,GAAA,CAAAO,EAAA;UACAS,UAAA,EAAAhB,GAAA,CAAAgB,UAAA,GAAAhB,GAAA,CAAAgB,UAAA;UACAC,iBAAA,EAAAjB,GAAA,CAAAiB,iBAAA,GAAAjB,GAAA,CAAAiB,iBAAA;UACAC,gBAAA,EAAAlB,GAAA,CAAAkB,gBAAA,GAAAlB,GAAA,CAAAkB,gBAAA;UACAC,cAAA,EAAAnB,GAAA,CAAAmB,cAAA,GAAAnB,GAAA,CAAAmB,cAAA;UACAC,QAAA,EAAApB,GAAA,CAAAoB,QAAA,GAAApB,GAAA,CAAAoB,QAAA;UACAC,KAAA,EAAArB,GAAA,CAAAqB,KAAA,GAAArB,GAAA,CAAAqB,KAAA;UACAC,IAAA,EAAAtB,GAAA,CAAAsB,IAAA,GAAAtB,GAAA,CAAAsB,IAAA;UACAC,WAAA,EAAAvB,GAAA,CAAAuB,WAAA,GAAAvB,GAAA,CAAAuB,WAAA;UACAC,GAAA,EAAAxB,GAAA,CAAAwB,GAAA,GAAAxB,GAAA,CAAAwB,GAAA;UACAC,IAAA,EAAAzB,GAAA,CAAAyB,IAAA,GAAAzB,GAAA,CAAAyB,IAAA;UACAC,KAAA,EAAA1B,GAAA,CAAA0B,KAAA,GAAA1B,GAAA,CAAA0B,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}