{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\abutmentRecord\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\abutmentRecord\\index.vue", "mtime": 1750311963038}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsgZ2V0QWJ1dG1lbnRMaXN0LCBvcGVyYXRlQWJ1dG1lbnQgfSBmcm9tICJAL2FwaS9zeXN0ZW0vYWJ1dG1lbnQiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJBYnV0bWVudFJlY29yZCIsDQogIGNvbXBvbmVudHM6IHsgVXNlck1lbnUgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgYWN0aXZlTmFtZTogImZpcnN0IiwNCiAgICAgIHJlY29yZHM6IFtdLA0KICAgICAgZml0OiAiY292ZXIiLA0KICAgICAgdGltZURpYzogWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLmjInml7bpl7Tmn6Xor6IiLA0KICAgICAgICAgIHZhbHVlOiAxLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLmjInlkI3np7Dmn6Xor6IiLA0KICAgICAgICAgIHZhbHVlOiAyLA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIHN0YXR1c0RpYzogWw0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLlt7LnlLPor7ciLA0KICAgICAgICAgIHZhbHVlOiAxLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLov5vooYzkuK0iLA0KICAgICAgICAgIHZhbHVlOiAyLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLlr7nmjqXlrozmiJAiLA0KICAgICAgICAgIHZhbHVlOiAzLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLlt7Lmi5Lnu50iLA0KICAgICAgICAgIHZhbHVlOiA0LA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbGFiZWw6ICLlhajpg6giLA0KICAgICAgICAgIHZhbHVlOiA1LA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiA0LA0KICAgICAgICBsaXN0VHlwZTogIjEiLA0KICAgICAgfSwNCg0KICAgICAgdG90YWw6IDAsDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldEFidXRtZW50TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaGFuZGxlQ2xpY2sodGFiLCBldmVudCkgew0KICAgICAgaWYgKHRoaXMuYWN0aXZlTmFtZSA9PT0gImZpcnN0Iikgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmxpc3RUeXBlID0gIjEiOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5saXN0VHlwZSA9ICIyIjsNCiAgICAgIH0NCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VTaXplID0gNDsNCg0KICAgICAgdGhpcy5nZXRBYnV0bWVudExpc3QoKTsNCiAgICB9LA0KDQogICAgc3VibWl0UGFnZUNoYW5nZShyZXMpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IHJlczsNCiAgICAgIHRoaXMuZ2V0QWJ1dG1lbnRMaXN0KCk7DQogICAgfSwNCiAgICBnZXRBYnV0bWVudExpc3QoKSB7DQogICAgICBnZXRBYnV0bWVudExpc3QoeyAuLi50aGlzLnF1ZXJ5UGFyYW1zIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMucmVjb3JkcyA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgb3BlcmF0ZUFidXRtZW50KGlkLCB0eXBlKSB7DQogICAgICBvcGVyYXRlQWJ1dG1lbnQoeyBpZDogaWQsIG9wZXJhdGVTdGF0dXM6IHR5cGUgfSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7DQogICAgICAgICAgdGhpcy5nZXRBYnV0bWVudExpc3QoKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzeXN0ZW1QYWdlQ2hhbmdlKHJlcykgew0KICAgICAgdGhpcy5hYnV0bXJudFBhcmFtcy5wYWdlTnVtID0gcmVzOw0KICAgICAgdGhpcy5nZXRTeXN0ZW1MaXN0KCk7DQogICAgfSwNCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuc3RhdHVzID0gdW5kZWZpbmVkOw0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5xdWVyeVR5cGUgPSB1bmRlZmluZWQ7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VTaXplID0gNDsNCiAgICAgIHRoaXMuZ2V0QWJ1dG1lbnRMaXN0KCk7DQogICAgfSwNCg0KICAgIGdldFN0YXR1c0NsYXNzKHN0YXR1cykgew0KICAgICAgc3dpdGNoIChzdGF0dXMpIHsNCiAgICAgICAgY2FzZSAxOg0KICAgICAgICAgIHJldHVybiAiYmx1ZSI7DQogICAgICAgIGNhc2UgMjoNCiAgICAgICAgICByZXR1cm4gIiBncmVlbiI7DQogICAgICAgIGNhc2UgMzoNCiAgICAgICAgICByZXR1cm4gImdyZXkiOw0KICAgICAgICBjYXNlIDQ6DQogICAgICAgICAgcmV0dXJuICJyZWQiOw0KICAgICAgfQ0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8OA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/abutmentRecord", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-20 10:41:34\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"abutmrnt-record-page\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div>\r\n            <el-tabs\r\n              class=\"abutmrnt-record-tab\"\r\n              v-model=\"activeName\"\r\n              @tab-click=\"handleClick\"\r\n            >\r\n              <el-tab-pane label=\"我提交的申请\" name=\"first\">\r\n                <el-form\r\n                  :model=\"queryParams\"\r\n                  ref=\"queryForm\"\r\n                  size=\"small\"\r\n                  :inline=\"true\"\r\n                >\r\n                  <el-form-item>\r\n                    <el-select\r\n                      v-model=\"queryParams.queryType\"\r\n                      placeholder=\"查询方式\"\r\n                      clearable\r\n                      style=\"width: 140px\"\r\n                      @change=\"getAbutmentList\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"dict in timeDic\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item>\r\n                    <el-select\r\n                      v-model=\"queryParams.status\"\r\n                      placeholder=\"状态\"\r\n                      clearable\r\n                      style=\"width: 140px\"\r\n                      @change=\"getAbutmentList\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"dict in statusDic\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item>\r\n                    <el-button size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n                  </el-form-item>\r\n                </el-form>\r\n                <div class=\"abutmrnt-message\">\r\n                  <div\r\n                    class=\"none-class\"\r\n                    v-if=\"!records || records.length == 0\"\r\n                  >\r\n                    <el-image\r\n                      style=\"width: 160px; height: 160px\"\r\n                      :src=\"require('@/assets/user/none.png')\"\r\n                      :fit=\"fit\"\r\n                    ></el-image>\r\n                    <div class=\"text\">暂无数据</div>\r\n                  </div>\r\n                  <div\r\n                    class=\"abutmrnt-message-item\"\r\n                    v-for=\"item in records\"\r\n                    v-else\r\n                    :key=\"item.id\"\r\n                  >\r\n                    <div class=\"item-content\">\r\n                      <div class=\"left\">\r\n                        <div class=\"title\">\r\n                          {{ item.resourceTitle }}\r\n                        </div>\r\n                        <div class=\"company-name\">\r\n                          {{ item.resourceCompanyName }}\r\n                        </div>\r\n                        <div class=\"tag\">{{ item.resourceTypeName }}</div>\r\n                      </div>\r\n                      <div class=\"right\">\r\n                        <div\r\n                          :class=\"['status-tag', getStatusClass(item.status)]\"\r\n                        >\r\n                          {{ item.statusName }}\r\n                        </div>\r\n                        <div class=\"date\">{{ item.createTimeStr }}</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <el-pagination\r\n                  v-show=\"total > 0\"\r\n                  background\r\n                  layout=\"prev, pager, next\"\r\n                  :page-size=\"4\"\r\n                  :current-page.sync=\"queryParams.pageNum\"\r\n                  @current-change=\"submitPageChange\"\r\n                  :total=\"total\"\r\n                >\r\n                </el-pagination\r\n              ></el-tab-pane>\r\n              <el-tab-pane label=\"我收到的申请\" name=\"second\">\r\n                <el-form\r\n                  :model=\"queryParams\"\r\n                  ref=\"queryForm\"\r\n                  size=\"small\"\r\n                  :inline=\"true\"\r\n                >\r\n                  <el-form-item>\r\n                    <el-select\r\n                      v-model=\"queryParams.queryType\"\r\n                      placeholder=\"查询方式\"\r\n                      clearable\r\n                      style=\"width: 140px\"\r\n                      @change=\"getAbutmentList\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"dict in timeDic\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item>\r\n                    <el-select\r\n                      v-model=\"queryParams.status\"\r\n                      placeholder=\"状态\"\r\n                      clearable\r\n                      style=\"width: 140px\"\r\n                      @change=\"getAbutmentList\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"dict in statusDic\"\r\n                        :key=\"dict.value\"\r\n                        :label=\"dict.label\"\r\n                        :value=\"dict.value\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item>\r\n                    <el-button size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n                  </el-form-item>\r\n                </el-form>\r\n                <div class=\"abutmrnt-message\">\r\n                  <div\r\n                    class=\"none-class\"\r\n                    v-if=\"!records || records.length == 0\"\r\n                  >\r\n                    <el-image\r\n                      style=\"width: 160px; height: 160px\"\r\n                      :src=\"require('@/assets/user/none.png')\"\r\n                      :fit=\"fit\"\r\n                    ></el-image>\r\n                    <div class=\"text\">暂无数据</div>\r\n                  </div>\r\n                  <div\r\n                    class=\"abutmrnt-message-item\"\r\n                    v-for=\"item in records\"\r\n                    :key=\"item.id\"\r\n                  >\r\n                    <div class=\"item-content\">\r\n                      <div class=\"left\">\r\n                        <div class=\"title\">\r\n                          {{ item.resourceTitle }}\r\n                        </div>\r\n                        <div class=\"company-name\">\r\n                          {{ item.resourceCompanyName }}\r\n                        </div>\r\n                        <div class=\"tag\">{{ item.resourceTypeName }}</div>\r\n                      </div>\r\n\r\n                      <div class=\"right right_200\" v-if=\"item.showOperate == 1\">\r\n                        <div class=\"tags\">\r\n                          <a\r\n                            class=\"status-tag blue_white\"\r\n                            @click=\"operateAbutment(item.id, 1)\"\r\n                            >接受</a\r\n                          >\r\n                          <a\r\n                            class=\"status-tag red ml_20\"\r\n                            @click=\"operateAbutment(item.id, 4)\"\r\n                            >忽略</a\r\n                          >\r\n                        </div>\r\n                      </div>\r\n                      <div class=\"right\">\r\n                        <div\r\n                          :class=\"['status-tag', getStatusClass(item.status)]\"\r\n                          v-if=\"item.showOperate != 1\"\r\n                        >\r\n                          {{ item.statusName }}\r\n                        </div>\r\n                        <div class=\"date\">{{ item.createTimeStr }}</div>\r\n                      </div>\r\n                    </div>\r\n                    <div\r\n                      class=\"unread-tag\"\r\n                      v-if=\"item.receiveReadStatus == 0\"\r\n                    ></div>\r\n                    <div class=\"unread-text\" v-if=\"item.receiveReadStatus == 0\">\r\n                      未读\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <el-pagination\r\n                  v-show=\"total > 0\"\r\n                  background\r\n                  layout=\"prev, pager, next\"\r\n                  :page-size=\"4\"\r\n                  :current-page.sync=\"queryParams.pageNum\"\r\n                  @current-change=\"submitPageChange\"\r\n                  :total=\"total\"\r\n                >\r\n                </el-pagination\r\n              ></el-tab-pane>\r\n            </el-tabs>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { getAbutmentList, operateAbutment } from \"@/api/system/abutment\";\r\n\r\nexport default {\r\n  name: \"AbutmentRecord\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      activeName: \"first\",\r\n      records: [],\r\n      fit: \"cover\",\r\n      timeDic: [\r\n        {\r\n          label: \"按时间查询\",\r\n          value: 1,\r\n        },\r\n        {\r\n          label: \"按名称查询\",\r\n          value: 2,\r\n        },\r\n      ],\r\n      statusDic: [\r\n        {\r\n          label: \"已申请\",\r\n          value: 1,\r\n        },\r\n        {\r\n          label: \"进行中\",\r\n          value: 2,\r\n        },\r\n        {\r\n          label: \"对接完成\",\r\n          value: 3,\r\n        },\r\n        {\r\n          label: \"已拒绝\",\r\n          value: 4,\r\n        },\r\n        {\r\n          label: \"全部\",\r\n          value: 5,\r\n        },\r\n      ],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 4,\r\n        listType: \"1\",\r\n      },\r\n\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getAbutmentList();\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      if (this.activeName === \"first\") {\r\n        this.queryParams.listType = \"1\";\r\n      } else {\r\n        this.queryParams.listType = \"2\";\r\n      }\r\n      this.queryParams.pageNum = 1;\r\n      this.queryParams.pageSize = 4;\r\n\r\n      this.getAbutmentList();\r\n    },\r\n\r\n    submitPageChange(res) {\r\n      this.queryParams.pageNum = res;\r\n      this.getAbutmentList();\r\n    },\r\n    getAbutmentList() {\r\n      getAbutmentList({ ...this.queryParams }).then((response) => {\r\n        this.records = response.rows;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    operateAbutment(id, type) {\r\n      operateAbutment({ id: id, operateStatus: type }).then((response) => {\r\n        if (response.code == 200) {\r\n          this.$modal.msgSuccess(\"操作成功\");\r\n          this.getAbutmentList();\r\n        }\r\n      });\r\n    },\r\n    systemPageChange(res) {\r\n      this.abutmrntParams.pageNum = res;\r\n      this.getSystemList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.queryParams.status = undefined;\r\n      this.queryParams.queryType = undefined;\r\n      this.queryParams.pageSize = 4;\r\n      this.getAbutmentList();\r\n    },\r\n\r\n    getStatusClass(status) {\r\n      switch (status) {\r\n        case 1:\r\n          return \"blue\";\r\n        case 2:\r\n          return \" green\";\r\n        case 3:\r\n          return \"grey\";\r\n        case 4:\r\n          return \"red\";\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .abutmrnt-record-page {\r\n    .none-class {\r\n      text-align: center;\r\n      padding: 10% 0;\r\n      .text {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n        line-height: 14px;\r\n      }\r\n    }\r\n    .abutmrnt-record-tab {\r\n      .el-tabs__nav {\r\n        width: 100%;\r\n        height: 60px;\r\n        padding: 0 43%;\r\n        display: flex;\r\n        // justify-content: space-between;\r\n      }\r\n      .el-tabs__nav-wrap::after {\r\n        background-color: transparent;\r\n      }\r\n      .el-tabs__active-bar {\r\n        background-color: transparent;\r\n      }\r\n      .el-tabs__item {\r\n        padding: 0 20px !important;\r\n        background-color: #fff;\r\n        border-radius: 20px;\r\n        box-shadow: 0px 4px 16px 0px rgba(38, 74, 116, 0.1);\r\n      }\r\n      .el-tabs__item.is-active {\r\n        background-color: #21c9b8 !important;\r\n        color: #fff;\r\n      }\r\n\r\n      .el-tabs__item#tab-first {\r\n        padding-right: 40px !important;\r\n      }\r\n      .el-tabs__item#tab-second {\r\n        padding-left: 40px !important;\r\n        margin-left: -30px;\r\n        z-index: -1;\r\n      }\r\n      .el-tabs__item.is-active#tab-first {\r\n        padding-right: 15px !important;\r\n      }\r\n      .el-tabs__item.is-active#tab-second {\r\n        padding-left: 20px !important;\r\n        margin-left: -30px;\r\n        z-index: 999;\r\n      }\r\n    }\r\n    .el-button {\r\n      background: #21c9b8;\r\n      color: #fff;\r\n      border-color: transparent;\r\n    }\r\n    .abutmrnt-message {\r\n      width: 100%;\r\n      height: 600px;\r\n      .abutmrnt-message-item {\r\n        width: 100%;\r\n        vertical-align: middle;\r\n        padding: 22px 22px;\r\n        margin-bottom: 20px;\r\n        background-color: #fff;\r\n\r\n        display: flex;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        position: relative;\r\n\r\n        .iamge {\r\n          margin: auto 0;\r\n        }\r\n        .item-content {\r\n          margin-left: 10px;\r\n          display: flex;\r\n          width: 100%;\r\n          justify-content: space-between;\r\n          .left {\r\n            width: 900px;\r\n            .title {\r\n              font-size: 16px;\r\n              font-weight: 500;\r\n              width: 900px;\r\n              color: #333333;\r\n              line-height: 20px;\r\n              overflow: hidden;\r\n              -webkit-line-clamp: 1;\r\n              text-overflow: ellipsis;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n            }\r\n            .company-name {\r\n              font-size: 14px;\r\n              font-weight: 400;\r\n              color: #666666;\r\n              line-height: 50px;\r\n              height: 50px;\r\n            }\r\n            .tag {\r\n              width: 72px;\r\n              height: 24px;\r\n              border-radius: 4px;\r\n              border: 1px solid #214dc5;\r\n              text-align: center;\r\n              font-size: 12px;\r\n              font-weight: 400;\r\n              color: #214dc5;\r\n              line-height: 24px;\r\n            }\r\n          }\r\n          .right {\r\n            width: 100px;\r\n            height: 100%;\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: space-between;\r\n            .status-tag {\r\n              width: 74px;\r\n              height: 30px;\r\n              border-radius: 4px;\r\n              text-align: center;\r\n              font-weight: 500;\r\n              font-size: 14px;\r\n              line-height: 30px;\r\n            }\r\n            .blue {\r\n              background: rgba(33, 77, 197, 0.15);\r\n              color: #0044ff;\r\n              font-size: 14px;\r\n              font-weight: 500;\r\n              color: #0044ff;\r\n            }\r\n            .blue_white {\r\n              background: #305ae8;\r\n              color: #fff;\r\n            }\r\n            .green {\r\n              background: rgba(21, 188, 132, 0.15);\r\n              font-size: 14px;\r\n              font-weight: 500;\r\n              color: #15bc84;\r\n            }\r\n            .red {\r\n              background: rgba(255, 77, 77, 0.15);\r\n              color: #fff;\r\n              font-size: 14px;\r\n              font-weight: 500;\r\n              color: #ff4d4d;\r\n            }\r\n            .grey {\r\n              background: #d2d2d2;\r\n              font-size: 14px;\r\n              font-weight: 500;\r\n              color: #b7b7b7;\r\n            }\r\n            .date {\r\n              font-size: 14px;\r\n              font-weight: 400;\r\n              color: #666666;\r\n              line-height: 30px;\r\n            }\r\n          }\r\n          .tags {\r\n            display: flex;\r\n            justify-content: flex-end;\r\n          }\r\n          .ml_20 {\r\n            margin-left: 20px;\r\n          }\r\n          .right_200 {\r\n            width: 200px;\r\n            text-align: right;\r\n          }\r\n        }\r\n        .unread-tag {\r\n          position: absolute;\r\n          top: -40px;\r\n          left: -50px;\r\n          width: 0;\r\n          height: 0;\r\n          border: 40px solid #ff5151;\r\n          border-bottom-color: transparent;\r\n          border-top-color: transparent;\r\n          border-left-color: transparent;\r\n          transform: rotateZ(45deg);\r\n        }\r\n        .unread-text {\r\n          position: absolute;\r\n          top: 10px;\r\n          left: 2px;\r\n          transform: rotateZ(-45deg);\r\n          font-size: 12px;\r\n          font-weight: 500;\r\n          color: #ffffff;\r\n          line-height: 12px;\r\n        }\r\n        .delete-icon {\r\n          right: 30px;\r\n          top: 40px;\r\n          margin: 0 auto;\r\n          position: absolute;\r\n        }\r\n        .re-icon {\r\n          right: 80px;\r\n          top: 40px;\r\n          margin: 0 auto;\r\n          position: absolute;\r\n        }\r\n      }\r\n    }\r\n    .el-pagination {\r\n      width: 100%;\r\n      margin-top: 20px;\r\n      text-align: center;\r\n    }\r\n    .el-pagination.is-background .el-pager li {\r\n      background-color: #fff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled).active {\r\n      background-color: #21c9b8;\r\n      color: #ffffff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled):hover {\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}