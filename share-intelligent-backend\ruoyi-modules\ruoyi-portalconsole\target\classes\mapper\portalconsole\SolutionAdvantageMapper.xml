<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.SolutionAdvantageMapper">
    
    <resultMap type="SolutionAdvantage" id="SolutionAdvantageResult">
        <result property="solutionAdvantageId"    column="solution_advantage_id"    />
        <result property="solutionId"    column="solution_id"    />
        <result property="solutionAdvantageName"    column="solution_advantage_name"    />
        <result property="solutionAdvantageType"    column="solution_advantage_type"    />
        <result property="solutionAdvantageContent"    column="solution_advantage_content"    />
        <result property="solutionAdvantageImage"    column="solution_advantage_image"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSolutionAdvantageVo">
        select solution_advantage_id, solution_id, solution_advantage_name, solution_advantage_type, solution_advantage_content, solution_advantage_image, del_flag, create_by, create_time, update_by, update_time, remark from solution_advantage
    </sql>

    <select id="selectSolutionAdvantageList" parameterType="SolutionAdvantage" resultMap="SolutionAdvantageResult">
        <include refid="selectSolutionAdvantageVo"/>
        <where>  
            <if test="solutionId != null "> and solution_id = #{solutionId}</if>
            <if test="solutionAdvantageName != null  and solutionAdvantageName != ''"> and solution_advantage_name like concat('%', #{solutionAdvantageName}, '%')</if>
            <if test="solutionAdvantageType != null  and solutionAdvantageType != ''"> and solution_advantage_type = #{solutionAdvantageType}</if>
            <if test="solutionAdvantageContent != null  and solutionAdvantageContent != ''"> and solution_advantage_content = #{solutionAdvantageContent}</if>
        </where>
    </select>
    
    <select id="selectSolutionAdvantageBySolutionAdvantageId" parameterType="Long" resultMap="SolutionAdvantageResult">
        <include refid="selectSolutionAdvantageVo"/>
        where solution_advantage_id = #{solutionAdvantageId}
    </select>
        
    <insert id="insertSolutionAdvantage" parameterType="SolutionAdvantage" useGeneratedKeys="true" keyProperty="solutionAdvantageId">
        insert into solution_advantage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="solutionId != null">solution_id,</if>
            <if test="solutionAdvantageName != null">solution_advantage_name,</if>
            <if test="solutionAdvantageType != null">solution_advantage_type,</if>
            <if test="solutionAdvantageContent != null">solution_advantage_content,</if>
            <if test="solutionAdvantageImage != null">solution_advantage_image,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="solutionId != null">#{solutionId},</if>
            <if test="solutionAdvantageName != null">#{solutionAdvantageName},</if>
            <if test="solutionAdvantageType != null">#{solutionAdvantageType},</if>
            <if test="solutionAdvantageContent != null">#{solutionAdvantageContent},</if>
            <if test="solutionAdvantageImage != null">#{solutionAdvantageImage},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>
    <insert id="insertSolutionAdvantageList"
            parameterType="java.util.List">
        insert into solution_advantage (solution_id, solution_advantage_name, solution_advantage_type, solution_advantage_content, solution_advantage_image, create_by, update_by, remark) values
       <foreach collection="list" item="item" separator=",">
           (#{item.solutionId},#{item.solutionAdvantageName},#{item.solutionAdvantageType},#{item.solutionAdvantageContent}, #{item.solutionAdvantageImage},#{item.createBy},#{item.updateBy},#{item.remark})
       </foreach>
    </insert>

    <update id="updateSolutionAdvantage" parameterType="SolutionAdvantage">
        update solution_advantage
        <trim prefix="SET" suffixOverrides=",">
            <if test="solutionId != null">solution_id = #{solutionId},</if>
            <if test="solutionAdvantageName != null">solution_advantage_name = #{solutionAdvantageName},</if>
            <if test="solutionAdvantageType != null">solution_advantage_type = #{solutionAdvantageType},</if>
            <if test="solutionAdvantageContent != null">solution_advantage_content = #{solutionAdvantageContent},</if>
            <if test="solutionAdvantageImage != null">solution_advantage_image = #{solutionAdvantageImage},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where solution_advantage_id = #{solutionAdvantageId}
    </update>

    <delete id="deleteSolutionAdvantageBySolutionAdvantageId" parameterType="Long">
        delete from solution_advantage where solution_advantage_id = #{solutionAdvantageId}
    </delete>

    <delete id="deleteSolutionAdvantageBySolutionAdvantageIds" parameterType="String">
        delete from solution_advantage where solution_advantage_id in 
        <foreach item="solutionAdvantageId" collection="array" open="(" separator="," close=")">
            #{solutionAdvantageId}
        </foreach>
    </delete>
    <delete id="deleteSolutionAdvantageBySolutionId" parameterType="java.lang.Long">
        delete from solution_advantage where solution_id = #{solutionId}
    </delete>
    <delete id="deleteSolutionAdvantageBySolutionIds" >
        delete from solution_advantage where solution_id in
        <foreach collection="array" item="solutionId" open="(" separator="," close=")">
            #{solutionId}
        </foreach>
    </delete>
</mapper>