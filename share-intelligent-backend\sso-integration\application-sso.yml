# SSO单点登录配置 - 主系统（Backend）
sso:
  # SSO服务器配置
  server:
    # SSO服务器地址
    url: http://localhost:9100
    # 登录接口
    login-url: /sso/login
    # Token换取接口
    token-url: /sso/token
    # Token验证接口
    validate-url: /sso/validate
    # 用户信息接口
    userinfo-url: /sso/userinfo
    # 登出接口
    logout-url: /sso/logout
    # 状态检查接口
    status-url: /sso/status
  
  # 客户端配置
  client:
    # 客户端ID
    id: backend
    # 客户端密钥
    secret: "backend_2024#RuoYi@Share$Key!8888"
    # 回调地址
    callback-url: http://localhost:9200/sso/callback
    # 系统名称
    name: 复合材料共享智造平台
  
  # Token配置
  token:
    # Token过期时间（秒）
    expire-time: 28800
    # Token刷新时间（秒）
    refresh-time: 1800
  
  # 缓存配置
  cache:
    # 缓存前缀
    prefix: sso:backend:
    # 缓存过期时间（秒）
    expire-time: 28800

# RestTemplate配置
rest-template:
  # 连接超时时间（毫秒）
  connect-timeout: 5000
  # 读取超时时间（毫秒）
  read-timeout: 10000

# 权限映射配置
permission-mapping:
  # 默认角色映射
  default-roles:
    admin: ["admin", "system_manager"]
    user: ["user"]
  
  # 默认权限映射
  default-permissions:
    admin:
      - "system:user:list"
      - "system:user:add"
      - "system:user:edit"
      - "system:user:remove"
      - "system:role:list"
      - "system:role:add"
      - "system:role:edit"
      - "system:role:remove"
      - "system:menu:list"
      - "system:menu:add"
      - "system:menu:edit"
      - "system:menu:remove"
      - "system:dept:list"
      - "system:dept:add"
      - "system:dept:edit"
      - "system:dept:remove"
    user:
      - "system:user:list"
      - "system:user:view"
