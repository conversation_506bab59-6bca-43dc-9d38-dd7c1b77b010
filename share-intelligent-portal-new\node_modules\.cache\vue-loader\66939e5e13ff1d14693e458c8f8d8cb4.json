{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\reApplication\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\reApplication\\index.vue", "mtime": 1750311963081}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsgc2VhcmNoQ29tcGFueSwgZ2V0Q29tcGFueUNvZGVCeU5hbWUgfSBmcm9tICJAL2FwaS9zeXN0ZW0vY29tcGFueSI7DQppbXBvcnQgeyBlbnRlcmluZ0ZhY3RvcnlBZGQgfSBmcm9tICJAL2FwaS9tYW51ZmFjdHVyaW5nU2hhcmluZyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlVzZXIiLA0KICBjb21wb25lbnRzOiB7IFVzZXJNZW51IH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGZvcm06IHsNCiAgICAgICAgY29tcGFueU5hbWU6ICIiLCAvLyDkvIHkuJrlkI3np7ANCiAgICAgICAgc29jaWFsQ3JlZGl0Q29kZTogIiIsIC8vIOekvuS8muS/oeeUqOS7o+eggQ0KICAgICAgICByZWdpc3RlcmVkQ2FwaXRhbDogIiIsIC8vIOazqOWGjOi1hOacrA0KICAgICAgICBjb250YWN0UGhvbmU6ICIiLCAvLyDogZTns7vnlLXor50NCiAgICAgICAgaW5kdXN0cnk6ICIiLCAvLyDooYzkuJoNCiAgICAgICAgY29tcGFueUFkZHJlc3M6ICIiLCAvLyDlnLDlnYANCiAgICAgICAgYnVzaW5lc3NTY29wZTogIiIsIC8vIOe7j+iQpeiMg+WbtA0KICAgICAgICBwZXJzb25uZWxMaXN0OiBbXSwgLy8g5Lq65ZGY6IO95YqbDQogICAgICAgIHF1YWxpZmljYXRpb25MaXN0OiBbXSwgLy8g6LWE6LSo6K+B5Lu2DQogICAgICAgIGVxdWlwbWVudExpc3Q6IFtdLCAvLyDorr7lpIfkv6Hmga/liJfooagNCiAgICAgICAgc2V0dGxlZFN0YXR1czogIjAiLCAvLyDpu5jorqTkvKDlvoXlrqHmoLgNCiAgICAgIH0sDQogICAgICBydWxlczogew0KICAgICAgICBjb21wYW55TmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXkvIHkuJrlkI3np7AiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsgfSwNCiAgbWV0aG9kczogew0KICAgIG9uU3VibWl0KCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBlbnRlcmluZ0ZhY3RvcnlBZGQodGhpcy5mb3JtKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgb25DYW5jZWwoKSB7IH0sDQogICAgYWRkUGVyc29ubmVsTGlzdCgpIHsNCiAgICAgIHRoaXMuZm9ybS5wZXJzb25uZWxMaXN0LnB1c2goew0KICAgICAgICB0ZWNobmljaWFuTmFtZTogIiIsDQogICAgICAgIHRlY2huaWNhbFR5cGU6ICIiLA0KICAgICAgfSk7DQogICAgfSwNCiAgICBhZGRRdWFsaWZpY2F0aW9uTGlzdCgpIHsNCiAgICAgIHRoaXMuZm9ybS5xdWFsaWZpY2F0aW9uTGlzdC5wdXNoKHsNCiAgICAgICAgcXVhbGlmaWNhdGlvbk5hbWU6ICIiLA0KICAgICAgICBhdHRhY2htZW50OiAiIiwNCiAgICAgIH0pOw0KICAgIH0sDQogICAgYWRkRXF1aXBtZW50TGlzdCgpIHsNCiAgICAgIHRoaXMuZm9ybS5lcXVpcG1lbnRMaXN0LnB1c2goew0KICAgICAgICBlcXVpcG1lbnROYW1lOiAiIiwNCiAgICAgICAgc3BlY2lmaWNhdGlvbjogIiIsDQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOS8geS4muWQjeensA0KICAgIHF1ZXJ5U2VhcmNoVGlhbllhbkNoYShxdWVyeVN0cmluZywgY2IpIHsNCiAgICAgIGlmIChxdWVyeVN0cmluZykgew0KICAgICAgICBzZWFyY2hDb21wYW55KHsga2V5d29yZHM6IHF1ZXJ5U3RyaW5nIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICBsZXQgZGF0YSA9IHJlcy5yb3dzOw0KICAgICAgICAgIGxldCBMaXN0ID0gW107DQogICAgICAgICAgZGF0YS5mb3JFYWNoKGZ1bmN0aW9uICh2YWwsIGluZGV4KSB7DQogICAgICAgICAgICBMaXN0LnB1c2goew0KICAgICAgICAgICAgICBpZDogaW5kZXgsDQogICAgICAgICAgICAgIHZhbHVlOiB2YWwNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSkNCiAgICAgICAgICBpZiAoZGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBjYihMaXN0KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgY2IoW3sNCiAgICAgICAgICAgICAgaWQ6ICcnLA0KICAgICAgICAgICAgICB2YWx1ZTogJ+aaguaXoOaVsOaNricNCiAgICAgICAgICAgIH1dKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5LyB5Lia5ZCN56ew6YCJ5oupDQogICAgc2VsZWN0QXV0b0RhdGFUaWFuWWFuQ2hhKHJvdykgew0KICAgICAgZ2V0Q29tcGFueUNvZGVCeU5hbWUoeyBrZXl3b3Jkczogcm93LnZhbHVlIH0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIGxldCBkYXRhID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhpcy4kc2V0KHRoaXMuZm9ybSwgJ3NvY2lhbENyZWRpdENvZGUnLCBkYXRhLnRheE5vKQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/reApplication", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"content_card\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <div class=\"title\">基本信息</div>\r\n            <div class=\"titleLine\"></div>\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"企业名称\" prop=\"companyName\">\r\n                  <el-autocomplete v-model=\"form.companyName\" placeholder=\"请输入您公司的完整名称\" style=\"width: 100%;\"\r\n                    :fetch-suggestions=\"querySearchTianYanCha\" @select=\"selectAutoDataTianYanCha\"></el-autocomplete>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"社会信用代码\">\r\n                  <el-input v-model=\"form.socialCreditCode\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"注册资本\">\r\n                  <el-input v-model=\"form.registeredCapital\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"联系电话\">\r\n                  <el-input v-model=\"form.contactPhone\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"行业\">\r\n                  <el-input v-model=\"form.industry\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"地址\">\r\n                  <el-input v-model=\"form.companyAddress\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-form-item prop=\"technologyType\">\r\n              <div slot=\"label\">经营范围</div>\r\n              <el-input v-model=\"form.businessScope\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\"\r\n                show-word-limit placeholder=\"请输入\" />\r\n            </el-form-item>\r\n            <div class=\"title\">企业能力</div>\r\n            <div class=\"titleLine\"></div>\r\n            <el-form-item label=\"\">\r\n              <div slot=\"label\">\r\n                <div style=\"display: flex; width: 1080px\">\r\n                  <div>人员能力</div>\r\n                  <div class=\"addStyle\" @click=\"addPersonnelList\">新增行</div>\r\n                </div>\r\n              </div>\r\n              <el-table :data=\"form.personnelList\">\r\n                <el-table-column label=\"技术人员姓名\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.technicianName\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"专业技术工种\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.technicalType\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </el-form-item>\r\n            <el-form-item label=\"技术实力\">\r\n              <el-input v-model=\"form.technicalCapability\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"\">\r\n              <div slot=\"label\">\r\n                <div style=\"display: flex; width: 1080px\">\r\n                  <div>\r\n                    资质证件\r\n                    <span style=\"color: #999999; font-size: 14px; margin-left: 11px\">（专利、商标、资质、证书等）</span>\r\n                  </div>\r\n                  <div class=\"addStyle\" @click=\"addQualificationList\">\r\n                    新增行\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <el-table :data=\"form.qualificationList\">\r\n                <el-table-column label=\"资质名称\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.qualificationName\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"附件\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <ImageUpload :limit=\"1\" v-model=\"scope.row.attachment\" />\r\n                    <!-- <el-input v-model=\"scope.row.jobId\"></el-input> -->\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </el-form-item>\r\n            <el-form-item label=\"\">\r\n              <div slot=\"label\">\r\n                <div style=\"display: flex; width: 1080px\">\r\n                  <div>设备信息</div>\r\n                  <div class=\"addStyle\" @click=\"addEquipmentList\">新增行</div>\r\n                </div>\r\n              </div>\r\n              <el-table :data=\"form.equipmentList\">\r\n                <el-table-column label=\"生产设备\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.equipmentName\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"规格型号\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.specification\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n              <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { searchCompany, getCompanyCodeByName } from \"@/api/system/company\";\r\nimport { enteringFactoryAdd } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      form: {\r\n        companyName: \"\", // 企业名称\r\n        socialCreditCode: \"\", // 社会信用代码\r\n        registeredCapital: \"\", // 注册资本\r\n        contactPhone: \"\", // 联系电话\r\n        industry: \"\", // 行业\r\n        companyAddress: \"\", // 地址\r\n        businessScope: \"\", // 经营范围\r\n        personnelList: [], // 人员能力\r\n        qualificationList: [], // 资质证件\r\n        equipmentList: [], // 设备信息列表\r\n        settledStatus: \"0\", // 默认传待审核\r\n      },\r\n      rules: {\r\n        companyName: [\r\n          { required: true, message: \"请输入企业名称\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() { },\r\n  methods: {\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          enteringFactoryAdd(this.form).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功\");\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    onCancel() { },\r\n    addPersonnelList() {\r\n      this.form.personnelList.push({\r\n        technicianName: \"\",\r\n        technicalType: \"\",\r\n      });\r\n    },\r\n    addQualificationList() {\r\n      this.form.qualificationList.push({\r\n        qualificationName: \"\",\r\n        attachment: \"\",\r\n      });\r\n    },\r\n    addEquipmentList() {\r\n      this.form.equipmentList.push({\r\n        equipmentName: \"\",\r\n        specification: \"\",\r\n      });\r\n    },\r\n    // 企业名称\r\n    querySearchTianYanCha(queryString, cb) {\r\n      if (queryString) {\r\n        searchCompany({ keywords: queryString }).then(res => {\r\n          let data = res.rows;\r\n          let List = [];\r\n          data.forEach(function (val, index) {\r\n            List.push({\r\n              id: index,\r\n              value: val\r\n            })\r\n          })\r\n          if (data.length > 0) {\r\n            cb(List);\r\n          } else {\r\n            cb([{\r\n              id: '',\r\n              value: '暂无数据'\r\n            }]);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 企业名称选择\r\n    selectAutoDataTianYanCha(row) {\r\n      getCompanyCodeByName({ keywords: row.value }).then(res => {\r\n        if (res.code == 200) {\r\n          let data = res.data;\r\n          this.$set(this.form, 'socialCreditCode', data.taxNo)\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n}\r\n\r\n.content_card {\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  // margin-top: 30px;\r\n  padding: 59px 60px 57px 60px;\r\n}\r\n\r\n.title {\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 18px;\r\n  color: #21c9b8;\r\n}\r\n\r\n.titleLine {\r\n  width: 100%;\r\n  height: 1px;\r\n  background: #21c9b8;\r\n  margin: 20px 0 30px 0;\r\n}\r\n\r\n.addStyle {\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  color: #21c9b8;\r\n  margin-left: auto;\r\n  cursor: pointer;\r\n}\r\n\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"]}]}