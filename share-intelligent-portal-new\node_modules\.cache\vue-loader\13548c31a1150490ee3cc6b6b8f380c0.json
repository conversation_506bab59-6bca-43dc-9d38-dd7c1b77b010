{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\myCollect\\index.vue?vue&type=style&index=0&id=e87d975a&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\myCollect\\index.vue", "mtime": 1750311963061}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogI2Y0ZjVmOTsNCn0NCi5jb250ZW50IHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogY2FsYygxMDB2aCAtIDE1MHB4KTsNCiAgcGFkZGluZzogNDBweDsNCiAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgLy8gYmFja2dyb3VuZDogcmdiKDI0MiwgMjQ4LCAyNTUpOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/myCollect", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\" v-loading=\"loading\">\r\n          <el-table :data=\"tableData\" style=\"width: 100%\">\r\n            <el-table-column prop=\"appName\" label=\"收藏应用\"> </el-table-column>\r\n            <el-table-column prop=\"appLabel\" label=\"应用标签\">\r\n            </el-table-column>\r\n            <el-table-column label=\"状态\"> 已收藏 </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  @click.native.prevent=\"viewDetail(scope.row.id)\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                >\r\n                  详情\r\n                </el-button>\r\n                <el-button\r\n                  @click.native.prevent=\"cancelCollect(scope.row.id)\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                >\r\n                  取消收藏\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <el-pagination\r\n            v-show=\"total > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            :page-size=\"5\"\r\n            :current-page.sync=\"queryParams.pageNum\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :total=\"total\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { collectList, appliCancelCollect } from \"@/api/appliMarket\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport store from \"@/store\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      tableData: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      collectList().then((res) => {\r\n        this.loading = false;\r\n        if (res.code === 200) {\r\n          this.tableData = res.data;\r\n          // this.total = res.total;\r\n        }\r\n      });\r\n    },\r\n    viewDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/purchaseapp\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    cancelCollect(id) {\r\n      let data = {\r\n        id,\r\n        userId: store.getters.userId,\r\n      };\r\n      appliCancelCollect(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message.success(\"操作成功!\");\r\n          this.getList();\r\n        }\r\n      });\r\n    },\r\n    handleCurrentChange(num) {\r\n      this.queryParams.pageNum = num;\r\n      this.getList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  height: calc(100vh - 150px);\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  // background: rgb(242, 248, 255);\r\n}\r\n</style>\r\n"]}]}