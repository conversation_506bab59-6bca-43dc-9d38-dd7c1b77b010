{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\talentJoinNow\\index.vue?vue&type=style&index=0&id=6cd725b5&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\talentJoinNow\\index.vue", "mtime": 1750311963088}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwOA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/talentJoinNow", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-row :gutter=\"20\">\r\n            <el-col :span=\"2.5\" :xs=\"24\">\r\n                <user-menu activeIndex=\"1\" />\r\n            </el-col>\r\n            <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n                <div class=\"main-content\">\r\n                    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n                        <el-row :gutter=\"50\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"姓名\" prop=\"name\">\r\n                                    <el-input v-model=\"form.name\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系电话\" prop=\"contactPhone\">\r\n                                    <el-input v-model=\"form.contactPhone\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"出生年月\">\r\n                                    <el-date-picker v-model=\"form.birthDate\" type=\"date\" placeholder=\"选择日期\"\r\n                                        value-format=\"yyyy-MM-dd\" style=\"width: 100%\" />\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"所在地\">\r\n                                    <el-input v-model=\"form.location\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"毕业院校\">\r\n                                    <el-input v-model=\"form.graduateSchool\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"最高学历\" prop=\"education\">\r\n                                    <el-select v-model=\"form.education\" placeholder=\"请选择最高学历\" clearable\r\n                                        style=\"width: 100%\">\r\n                                        <el-option v-for=\"dict in educationList\" :key=\"dict.dictLabel\"\r\n                                            :label=\"dict.dictLabel\" :value=\"dict.dictValue\" />\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"所在单位\">\r\n                                    <el-input v-model=\"form.currentCompany\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"工作状态\" prop=\"workStatus\">\r\n                                    <el-radio-group v-model=\"form.workStatus\">\r\n                                        <el-radio v-for=\"dict in workStatusList\" :key=\"dict.dictValue\"\r\n                                            :label=\"dict.dictValue\" :value=\"dict.dictValue\">{{ dict.dictLabel\r\n                                            }}</el-radio>\r\n                                    </el-radio-group>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"职务\">\r\n                                    <el-input v-model=\"form.position\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"职称\" prop=\"jobTitle\">\r\n                                    <el-select v-model=\"form.jobTitle\" placeholder=\"请选择职称\" clearable\r\n                                        style=\"width: 100%\">\r\n                                        <el-option v-for=\"dict in jobTitleList\" :key=\"dict.dictLabel\"\r\n                                            :label=\"dict.dictLabel\" :value=\"dict.dictValue\" />\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"技术领域\">\r\n                                    <el-input v-model=\"form.skills\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"岗位分类\" prop=\"positionType\">\r\n                                    <el-select v-model=\"form.positionType\" placeholder=\"请选择岗位分类\" clearable\r\n                                        style=\"width: 100%\">\r\n                                        <el-option v-for=\"dict in positionTypeList\" :key=\"dict.dictLabel\"\r\n                                            :label=\"dict.dictLabel\" :value=\"dict.dictValue\" />\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"24\">\r\n                                <el-form-item label=\"个人简介\">\r\n                                    <el-input v-model=\"form.workExperience\" type=\"textarea\" resize=\"none\" :rows=\"8\"\r\n                                        maxlength=\"500\" show-word-limit placeholder=\"请输入\" />\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"个人照片\">\r\n                                    <ImageUpload v-model=\"photoList\" :limit=\"1\"/>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"附件\">\r\n                                    <FileUpload v-model=\"form.resumeFile\" />\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"24\">\r\n                                <el-form-item class=\"footer-submit\">\r\n                                    <el-button type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n                                    <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                    </el-form>\r\n                </div>\r\n            </el-col>\r\n        </el-row>\r\n    </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { talentAdd, talentDetailData } from \"@/api/serviceSharing\";\r\n\r\nexport default {\r\n    components: { UserMenu },\r\n    data() {\r\n        return {\r\n            id: \"\",\r\n            form: {\r\n                name: \"\",\r\n                contactPhone: \"\",\r\n                birthDate: \"\",\r\n                location: \"\",\r\n                graduateSchool: \"\",\r\n                education: \"\",\r\n                currentCompany: \"\",\r\n                workStatus: \"\",\r\n                position: \"\",\r\n                jobTitle: \"\",\r\n                skills: \"\",\r\n                positionType: \"\", // 岗位分类\r\n                workExperience: \"\", // 个人简介\r\n                photo: \"\", // 照片\r\n                resumeFile: \"\", // 简历附件\r\n                settledStatus: \"0\",\r\n            },\r\n            rules: {\r\n                name: [{ required: true, message: \"姓名不能为空\", trigger: \"blur\" }],\r\n                positionType: [\r\n                    { required: true, message: \"岗位分类不能为空\", trigger: \"change\" },\r\n                ],\r\n                education: [\r\n                    { required: true, message: \"最高学历不能为空\", trigger: \"blur\" },\r\n                ],\r\n                jobTitle: [\r\n                    { required: true, message: \"职称不能为空\", trigger: \"blur\" },\r\n                ],\r\n                workStatus: [\r\n                    { required: true, message: \"工作状态不能为空\", trigger: \"change\" },\r\n                ],\r\n            },\r\n            positionTypeList: [], // 岗位分类\r\n            educationList: [], // 最高学历\r\n            jobTitleList: [], // 职称\r\n            workStatusList: [], // 工作状态\r\n            photoList: [],\r\n        };\r\n    },\r\n    created() {\r\n        this.getPositionType();\r\n        this.getEducation();\r\n        this.getJobTitle();\r\n        this.getWorkStatus();\r\n        if (this.$route.query.id) {\r\n            this.id = this.$route.query.id;\r\n            this.getTalentInfo();\r\n        }\r\n    },\r\n    methods: {\r\n        getTalentInfo() {\r\n            talentDetailData(this.id).then((res) => {\r\n                this.form = res.data;\r\n                this.photoList = res.data.photo ? [res.data.photo] : [];\r\n            })\r\n        },\r\n        // 岗位分类\r\n        getPositionType() {\r\n            let params = { dictType: \"position_type\" };\r\n            listData(params).then((response) => {\r\n                this.positionTypeList = response.rows;\r\n            });\r\n        },\r\n        // 最高学历\r\n        getEducation() {\r\n            let params = { dictType: \"education\" };\r\n            listData(params).then((response) => {\r\n                this.educationList = response.rows;\r\n            });\r\n        },\r\n        // 职称\r\n        getJobTitle() {\r\n            let params = { dictType: \"job_title\" };\r\n            listData(params).then((response) => {\r\n                this.jobTitleList = response.rows;\r\n            });\r\n        },\r\n        // 工作状态\r\n        getWorkStatus() {\r\n            let params = { dictType: \"work_status\" };\r\n            listData(params).then((response) => {\r\n                this.workStatusList = response.rows;\r\n            });\r\n        },\r\n        onSubmit() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                this.form.photo = this.photoList[0]?.url ? this.photoList[0]?.url : \"\";\r\n                if (valid) {\r\n                    talentAdd(this.form).then((res) => {\r\n                        if (res.code === 200) {\r\n                            this.$message.success(\"操作成功\");\r\n                            this.onCancel();\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        },\r\n        onCancel() {\r\n            this.$router.go(-1);\r\n        },\r\n    },\r\n};\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n    background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n}\r\n\r\n.main-content {\r\n    background-color: #fff;\r\n    padding: 20px;\r\n    padding-bottom: 100px;\r\n\r\n    .btn-box {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin-top: 40px;\r\n\r\n        .btn {\r\n            width: 200px;\r\n            height: 50px;\r\n        }\r\n    }\r\n\r\n    .card {\r\n        margin-top: 170px;\r\n        padding: 10px;\r\n        box-sizing: border-box;\r\n        width: 280px;\r\n        height: 240px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        align-items: center;\r\n        background: url(\"../../../../assets/userCenter/card_bg.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n\r\n\r\n        .card-title {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            margin-bottom: 10px;\r\n            color: #030A1A;\r\n        }\r\n\r\n        .card-content {\r\n            font-size: 14px;\r\n            color: #666666;\r\n            line-height: 30px;\r\n        }\r\n\r\n        .success {\r\n            color: #21C9B8;\r\n        }\r\n\r\n        .btn-card {\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            margin-top: 0px;\r\n\r\n            .btn {\r\n                width: 200px;\r\n                height: 50px;\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n\r\n\r\n.content {\r\n    width: 100%;\r\n    padding-bottom: 60px;\r\n    background-color: #f2f2f2;\r\n}\r\n\r\n.content_banner {\r\n    width: 100%;\r\n    height: 300px;\r\n    background-image: url(\"../../../../assets/release/banner.png\");\r\n    background-size: 100% 100%;\r\n    text-align: center;\r\n    margin: 0 auto;\r\n    padding-top: 28px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    font-size: 40px;\r\n    color: #000;\r\n\r\n    .imgContent {\r\n        width: 100%;\r\n        display: flex;\r\n        justify-content: center;\r\n        margin-top: 10px;\r\n\r\n        .imgStyle {\r\n            width: 1256px;\r\n            height: 206px;\r\n            position: relative;\r\n        }\r\n    }\r\n}\r\n\r\n.content_card {\r\n    // height: 1530px;\r\n    background: #ffffff;\r\n    border-radius: 2px;\r\n    margin-top: 30px;\r\n    padding: 59px 60px 57px 60px;\r\n}\r\n\r\n.addStyle {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #21c9b8;\r\n    margin-left: auto;\r\n    cursor: pointer;\r\n}\r\n\r\n.footer-submit {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-top: 60px;\r\n\r\n    .el-button {\r\n        width: 140px;\r\n        height: 50px;\r\n    }\r\n}\r\n</style>"]}]}