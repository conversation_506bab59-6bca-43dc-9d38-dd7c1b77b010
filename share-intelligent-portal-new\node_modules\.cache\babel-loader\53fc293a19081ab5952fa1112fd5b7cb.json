{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Sidebar\\Link.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Sidebar\\Link.vue", "mtime": 1750311962853}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfdmFsaWRhdGUgPSByZXF1aXJlKCJAL3V0aWxzL3ZhbGlkYXRlIik7Ci8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBwcm9wczogewogICAgdG86IHsKICAgICAgdHlwZTogW1N0cmluZywgT2JqZWN0XSwKICAgICAgcmVxdWlyZWQ6IHRydWUKICAgIH0KICB9LAogIGNvbXB1dGVkOiB7CiAgICBpc0V4dGVybmFsOiBmdW5jdGlvbiBpc0V4dGVybmFsKCkgewogICAgICByZXR1cm4gKDAsIF92YWxpZGF0ZS5pc0V4dGVybmFsKSh0aGlzLnRvKTsKICAgIH0sCiAgICB0eXBlOiBmdW5jdGlvbiB0eXBlKCkgewogICAgICBpZiAodGhpcy5pc0V4dGVybmFsKSB7CiAgICAgICAgcmV0dXJuICdhJzsKICAgICAgfQogICAgICByZXR1cm4gJ3JvdXRlci1saW5rJzsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGxpbmtQcm9wczogZnVuY3Rpb24gbGlua1Byb3BzKHRvKSB7CiAgICAgIGlmICh0aGlzLmlzRXh0ZXJuYWwpIHsKICAgICAgICByZXR1cm4gewogICAgICAgICAgaHJlZjogdG8sCiAgICAgICAgICB0YXJnZXQ6ICdfYmxhbmsnLAogICAgICAgICAgcmVsOiAnbm9vcGVuZXInCiAgICAgICAgfTsKICAgICAgfQogICAgICByZXR1cm4gewogICAgICAgIHRvOiB0bwogICAgICB9OwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_validate", "require", "props", "to", "type", "String", "Object", "required", "computed", "isExternal", "methods", "linkProps", "href", "target", "rel"], "sources": ["src/layout/components/Sidebar/Link.vue"], "sourcesContent": ["<template>\r\n  <component :is=\"type\" v-bind=\"linkProps(to)\">\r\n    <slot />\r\n  </component>\r\n</template>\r\n\r\n<script>\r\nimport { isExternal } from '@/utils/validate'\r\n\r\nexport default {\r\n  props: {\r\n    to: {\r\n      type: [String, Object],\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    isExternal() {\r\n      return isExternal(this.to)\r\n    },\r\n    type() {\r\n      if (this.isExternal) {\r\n        return 'a'\r\n      }\r\n      return 'router-link'\r\n    }\r\n  },\r\n  methods: {\r\n    linkProps(to) {\r\n      if (this.isExternal) {\r\n        return {\r\n          href: to,\r\n          target: '_blank',\r\n          rel: 'noopener'\r\n        }\r\n      }\r\n      return {\r\n        to: to\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;AAOA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;iCAEA;EACAC,KAAA;IACAC,EAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,WAAAA,oBAAA,OAAAN,EAAA;IACA;IACAC,IAAA,WAAAA,KAAA;MACA,SAAAK,UAAA;QACA;MACA;MACA;IACA;EACA;EACAC,OAAA;IACAC,SAAA,WAAAA,UAAAR,EAAA;MACA,SAAAM,UAAA;QACA;UACAG,IAAA,EAAAT,EAAA;UACAU,MAAA;UACAC,GAAA;QACA;MACA;MACA;QACAX,EAAA,EAAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}