import request from '@/utils/request'

// 查询人才培训基地申请列表
export function listTalentTrainingApplication(query) {
  return request({
    url: '/system/talentTrainingApplication/list',
    method: 'get',
    params: query
  })
}

// 查询人才培训基地申请详细
export function getTalentTrainingApplication(id) {
  return request({
    url: '/system/talentTrainingApplication/' + id,
    method: 'get'
  })
}

// 新增人才培训基地申请
export function addTalentTrainingApplication(data) {
  return request({
    url: '/system/talentTrainingApplication',
    method: 'post',
    data: data
  })
}

// 修改人才培训基地申请
export function updateTalentTrainingApplication(data) {
  return request({
    url: '/system/talentTrainingApplication',
    method: 'put',
    data: data
  })
}

// 删除人才培训基地申请
export function delTalentTrainingApplication(id) {
  return request({
    url: '/system/talentTrainingApplication/' + id,
    method: 'delete'
  })
}
