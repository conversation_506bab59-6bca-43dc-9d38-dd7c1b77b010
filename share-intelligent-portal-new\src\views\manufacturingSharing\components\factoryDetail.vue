<template>
  <div class="content">
    <div class="card-container cardStyle">
      <!-- 左侧 -->
      <div class="card_left">
        <div class="card_left_bottom">
          <div style="margin-left: 14px">
            <div class="title">{{ detailsData.companyName }}</div>
            <div class="everyOption">
              <div class="optionName">行业：</div>
              <div class="optionValue">{{ detailsData.industry }}</div>
            </div>
            <div class="everyOption">
              <div class="optionName">公司地址：</div>
              <div class="optionValue">{{ detailsData.companyAddress }}</div>
            </div>
          </div>
          <div>
            <div class="content_title">
              <div class="icon"></div>
              <div class="phoneTitle">联系方式</div>
            </div>
            <div style="padding-left: 14px">
              <div class="everyOption">
                <div class="optionName">联系电话：</div>
                <div class="optionValue">{{ detailsData.contactPhone }}</div>
              </div>
              <!-- <div class="everyOption">
                <div class="optionName">二维码：</div>
                <div class="optionImg">
                  <img
                    style="width: 100%; height: 100%"
                    :src="detailsData.qrCode"
                    alt=""
                  />
                </div>
              </div> -->
            </div>
          </div>
        </div>
      </div>
      <!-- 中间 -->
      <div class="card_center_line"></div>
      <!-- 右侧 -->
      <div class="card_right">
        <!-- <div>
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">基本信息</div>
          </div>
          <div style="margin-top: 22px">
            <el-descriptions class="margin-top" title="" :column="2" :size="size" border>
              <el-descriptions-item>
                <template slot="label"> 社会信用代码 </template>
{{ detailsData.socialCreditCode }}
</el-descriptions-item>
<el-descriptions-item>
  <template slot="label"> 注册资本 </template>
  {{ detailsData.registeredCapital }}
</el-descriptions-item>
<el-descriptions-item>
  <template slot="label"> 经营范围 </template>
  {{ detailsData.businessScope }}
</el-descriptions-item>
</el-descriptions>
</div>
</div>
<div style="margin-top: 30px">
  <div class="content_title">
    <div class="icon"></div>
    <div class="title">资质证件</div>
  </div>
  <div style="margin-top: 22px">
    <div class="certificateStyle">
      <img :src="detailsData.qualificationFiles" alt="" />
    </div>
  </div>
</div>
<div style="margin-top: 30px">
  <div class="content_title">
    <div class="icon"></div>
    <div class="title">人员能力</div>
  </div>
  <div class="center-text" style="margin-top: 21px">
    <el-table :data="detailsData.personnelList">
      <el-table-column label="技术人员姓名" align="center">
        <template slot-scope="scope">
                  {{ scope.row.technicianName }}
                </template>
      </el-table-column>
      <el-table-column label="专业技术工种" align="center">
        <template slot-scope="scope">
                  {{ scope.row.technicalType }}
                </template>
      </el-table-column>
    </el-table>
  </div>
</div>
<div style="margin-top: 30px">
  <div class="content_title">
    <div class="icon"></div>
    <div class="title">技术实力</div>
  </div>
  <div style="margin-top: 21px">
    <div class="desc">
      {{ detailsData.technicalCapability }}
    </div>
  </div>
</div>
<div style="margin-top: 30px">
  <div class="content_title">
    <div class="icon"></div>
    <div class="title">设备信息</div>
  </div>
  <div class="center-text" style="margin-top: 21px">
    <el-table :data="detailsData.equipmentList">
      <el-table-column label="生产设备" align="center">
        <template slot-scope="scope">
                  {{ scope.row.equipmentName }}
                </template>
      </el-table-column>
      <el-table-column label="规格型号" align="center">
        <template slot-scope="scope">
                  {{ scope.row.specification }}
                </template>
      </el-table-column>
    </el-table>
  </div>
</div> -->
        <div>
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">公司简介</div>
          </div>
          <div style="margin-top: 21px">
            <div class="desc">
              {{ detailsData.qrCode }}
            </div>
          </div>
        </div>
        <div style="margin-top: 30px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">产品与服务</div>
          </div>
          <div style="margin-top: 21px">
            <div class="desc">
              {{ detailsData.registeredCapital }}
            </div>
          </div>
        </div>
        <div style="margin-top: 30px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">产品图片</div>
          </div>
          <div class="content_desc">
            <div class="imgList">
              <div v-for="item in productList" :key="item.id" @click="toExhibitionHall(item.productId)" class="imgItem">
                <img :src="item.productImageUrl" alt="" />
                <div class="imgTitle">{{ item.productName }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { enteringFactoryDetail } from "@/api/manufacturingSharing";
import { listSysProduct } from "@/api/manufacturingSharing";
export default {
  name: "deviceDetail",
  data() {
    return {
      detailsData: {},
      id: null,
      size: "",
      productList: [],
    };
  },
  created() {
    this.id = this.$route.query.id;
    this.getFactoryDetail();
    this.getProductList();
  },
  methods: {
    getFactoryDetail() {
      enteringFactoryDetail(this.id).then((res) => {
        this.detailsData = res.data;
      });
    },
    getProductList() {
      listSysProduct({
        factoryId: this.id,
        pageNum: 1,
        pageSize: 1000,
      }).then((res) => {
        if (res.code == 200) {
          this.productList = res.rows || [];
        }
      });
    },
    toExhibitionHall(id) {
      this.$router.push({
        path: "/compositeExhibitionHall",
        query: {
          productId: id,
        },
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  background-color: #f2f2f2;
  padding: 30px 0 61px 0;
}

.cardStyle {
  // height: 950px;
  background-color: #ffffff;
  padding: 59px 56px 75px 46px;
  display: flex;
}

.card_left {
  .card_left_bottom {
    width: 263px;

    .title {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 20px;
      color: #222222;
      margin-bottom: 25px;
    }

    .everyOption {
      display: flex;
      align-items: center;
      margin-top: 12px;

      .optionName {
        // height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
      }

      .optionValue {
        // height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        text-overflow: ellipsis;
        word-wrap: break-word;
      }

      .optionImg {
        width: 120px;
        height: 120px;
      }
    }

    .content_title {
      display: flex;
      align-items: center;
      margin-top: 32px;

      .icon {
        width: 4px;
        height: 20px;
        background: #21c9b8;
      }

      .phoneTitle {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #030a1a;
        margin-left: 10px;
      }
    }

    .content_desc {
      // width: 631px;
      // height: 159px;
      margin-top: 20px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      line-height: 24px;

      .imgList {
        display: flex;
        flex-wrap: wrap;

        .imgItem {
          width: 150px;
          margin-right: 6px;
          margin-bottom: 10px;
          text-align: center;
          cursor: pointer;

          img {
            width: 100%;
          }
        }
      }
    }

  }
}

.card_center_line {
  width: 1px;
  height: 100%;
  background: #e1e1e1;
  margin-left: 60px;
  margin-right: 61px;
}

.card_right {
  width: 100%;
  overflow-y: auto;

  .content_title {
    display: flex;
    align-items: center;

    .icon {
      width: 4px;
      height: 20px;
      background: #21c9b8;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #030a1a;
      margin-left: 10px;
    }
  }

  .certificateStyle {
    width: 170px;
    height: 220px;
    background: #ffffff;
    box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.14);
    margin-left: 15px;
    padding: 10px;
  }

  .desc {
    // width: 741px;
    height: 38px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;
  }

  .content_desc {
    // width: 631px;
    // height: 159px;
    margin-top: 20px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;

    .imgList {
      display: flex;
      flex-wrap: wrap;

      .imgItem {
        width: 150px;
        height: 200px;
        margin-right: 20px;
        margin-bottom: 10px;
        text-align: center;

        img {
          width: 100%;
          height: 75%;
        }
      }
    }
  }
}

::v-deep .center-text .el-descriptions-item__cell {
  text-align: center;
}
</style>
