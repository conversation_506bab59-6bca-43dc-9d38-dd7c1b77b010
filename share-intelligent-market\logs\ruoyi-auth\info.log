15:04:42.332 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
15:04:43.118 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0
15:04:43.159 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 3 keys and 6 values 
15:04:43.187 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
15:04:43.204 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
15:04:43.392 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 185 ms to scan 208 urls, producing 0 keys and 0 values 
15:04:43.407 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
15:04:43.422 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
15:04:43.435 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
15:04:43.609 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 172 ms to scan 208 urls, producing 0 keys and 0 values 
15:04:43.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:04:43.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/2016038911
15:04:43.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1187410086
15:04:43.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:04:43.613 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:04:43.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:04:44.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750230284553_127.0.0.1_60112
15:04:44.712 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Notify connected event to listeners.
15:04:44.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:04:44.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1183905155
15:04:44.768 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
15:04:46.053 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
15:04:46.054 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:04:46.054 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
15:04:46.171 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:04:47.046 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:04:47.573 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 61d6c5fe-b47d-4483-a72f-996ed3b31790
15:04:47.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] RpcClient init label, labels = {module=naming, source=sdk}
15:04:47.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:04:47.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:04:47.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:04:47.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:04:47.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750230287579_127.0.0.1_60121
15:04:47.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:04:47.685 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Notify connected event to listeners.
15:04:47.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1183905155
15:04:48.439 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
15:04:48.455 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
15:04:48.772 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 7.149 seconds (JVM running for 8.08)
15:04:48.780 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
15:04:48.782 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
15:04:48.783 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
15:04:48.967 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Receive server push request, request = NotifySubscriberRequest, requestId = 10
15:04:48.971 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Ack server push request, request = NotifySubscriberRequest, requestId = 10
15:04:49.081 [RMI TCP Connection(3)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:38:13.950 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:38:13.953 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:38:14.288 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:38:14.289 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4b154ae9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:38:14.289 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750230287579_127.0.0.1_60121
16:38:14.295 [nacos-grpc-client-executor-1139] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750230287579_127.0.0.1_60121]Ignore complete event,isRunning:false,isAbandon=false
16:38:14.319 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@64fbc208[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1140]
16:38:18.978 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
16:38:20.572 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3d4ad9f0-2540-461c-abea-25aed234601e_config-0
16:38:20.666 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 52 ms to scan 1 urls, producing 3 keys and 6 values 
16:38:20.712 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
16:38:20.727 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
16:38:20.922 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 192 ms to scan 209 urls, producing 0 keys and 0 values 
16:38:20.931 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
16:38:20.948 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
16:38:20.960 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
16:38:21.025 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
16:38:21.137 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 175 ms to scan 209 urls, producing 0 keys and 0 values 
16:38:21.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d4ad9f0-2540-461c-abea-25aed234601e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:38:21.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d4ad9f0-2540-461c-abea-25aed234601e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/2121551683
16:38:21.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d4ad9f0-2540-461c-abea-25aed234601e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/296223788
16:38:21.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d4ad9f0-2540-461c-abea-25aed234601e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:38:21.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d4ad9f0-2540-461c-abea-25aed234601e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:38:21.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d4ad9f0-2540-461c-abea-25aed234601e_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:22.225 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 07364bff-8c15-444e-aa86-2a71204a0df3_config-0
16:38:22.317 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 52 ms to scan 1 urls, producing 3 keys and 6 values 
16:38:22.360 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
16:38:22.373 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
16:38:22.542 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 167 ms to scan 209 urls, producing 0 keys and 0 values 
16:38:22.553 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
16:38:22.567 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
16:38:22.581 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
16:38:22.777 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 194 ms to scan 209 urls, producing 0 keys and 0 values 
16:38:22.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07364bff-8c15-444e-aa86-2a71204a0df3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:38:22.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07364bff-8c15-444e-aa86-2a71204a0df3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/399715995
16:38:22.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07364bff-8c15-444e-aa86-2a71204a0df3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1727026317
16:38:22.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07364bff-8c15-444e-aa86-2a71204a0df3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:38:22.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07364bff-8c15-444e-aa86-2a71204a0df3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:38:22.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07364bff-8c15-444e-aa86-2a71204a0df3_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:23.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d4ad9f0-2540-461c-abea-25aed234601e_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750235902854_127.0.0.1_53526
16:38:23.189 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d4ad9f0-2540-461c-abea-25aed234601e_config-0] Notify connected event to listeners.
16:38:23.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d4ad9f0-2540-461c-abea-25aed234601e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:38:23.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3d4ad9f0-2540-461c-abea-25aed234601e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/984235065
16:38:23.319 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
16:38:24.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07364bff-8c15-444e-aa86-2a71204a0df3_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750235904553_127.0.0.1_53594
16:38:24.856 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07364bff-8c15-444e-aa86-2a71204a0df3_config-0] Notify connected event to listeners.
16:38:24.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07364bff-8c15-444e-aa86-2a71204a0df3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:38:24.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07364bff-8c15-444e-aa86-2a71204a0df3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/255156436
16:38:24.989 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
16:38:26.372 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
16:38:26.373 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:38:26.374 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
16:38:26.689 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:38:28.044 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
16:38:28.046 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:38:28.046 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
16:38:28.395 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:38:30.026 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:38:30.869 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a3d24679-dc6e-4a04-9181-9c12921e6a2e
16:38:30.870 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3d24679-dc6e-4a04-9181-9c12921e6a2e] RpcClient init label, labels = {module=naming, source=sdk}
16:38:30.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3d24679-dc6e-4a04-9181-9c12921e6a2e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:38:30.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3d24679-dc6e-4a04-9181-9c12921e6a2e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:38:30.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3d24679-dc6e-4a04-9181-9c12921e6a2e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:38:30.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3d24679-dc6e-4a04-9181-9c12921e6a2e] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:30.987 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3d24679-dc6e-4a04-9181-9c12921e6a2e] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750235910880_127.0.0.1_53609
16:38:30.987 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3d24679-dc6e-4a04-9181-9c12921e6a2e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:38:30.987 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3d24679-dc6e-4a04-9181-9c12921e6a2e] Notify connected event to listeners.
16:38:30.987 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3d24679-dc6e-4a04-9181-9c12921e6a2e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/984235065
16:38:31.538 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:38:32.436 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9ccfadcb-897c-4249-9fdf-b00a8f24d199
16:38:32.438 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ccfadcb-897c-4249-9fdf-b00a8f24d199] RpcClient init label, labels = {module=naming, source=sdk}
16:38:32.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ccfadcb-897c-4249-9fdf-b00a8f24d199] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:38:32.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ccfadcb-897c-4249-9fdf-b00a8f24d199] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:38:32.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ccfadcb-897c-4249-9fdf-b00a8f24d199] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:38:32.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ccfadcb-897c-4249-9fdf-b00a8f24d199] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:32.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ccfadcb-897c-4249-9fdf-b00a8f24d199] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750235912450_127.0.0.1_53616
16:38:32.568 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ccfadcb-897c-4249-9fdf-b00a8f24d199] Notify connected event to listeners.
16:38:32.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ccfadcb-897c-4249-9fdf-b00a8f24d199] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:38:32.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ccfadcb-897c-4249-9fdf-b00a8f24d199] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/255156436
16:38:32.831 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
16:38:32.882 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
16:38:33.282 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 15.524 seconds (JVM running for 17.44)
16:38:33.308 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
16:38:33.309 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
16:38:33.310 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
16:38:33.392 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3d24679-dc6e-4a04-9181-9c12921e6a2e] Receive server push request, request = NotifySubscriberRequest, requestId = 16
16:38:33.396 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a3d24679-dc6e-4a04-9181-9c12921e6a2e] Ack server push request, request = NotifySubscriberRequest, requestId = 16
16:38:33.547 [RMI TCP Connection(1)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:38:34.509 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
16:38:35.031 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ccfadcb-897c-4249-9fdf-b00a8f24d199] Receive server push request, request = NotifySubscriberRequest, requestId = 17
16:38:35.032 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9ccfadcb-897c-4249-9fdf-b00a8f24d199] Ack server push request, request = NotifySubscriberRequest, requestId = 17
16:38:35.177 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:38:35.179 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@11678469[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:38:35.179 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750235912450_127.0.0.1_53616
16:38:35.180 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750235912450_127.0.0.1_53616]Ignore complete event,isRunning:false,isAbandon=false
16:38:35.188 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3d0f4ac4[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 7]
16:38:35.258 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e66add63-3a88-4a73-bed8-24eafb8e5f30
16:38:35.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e66add63-3a88-4a73-bed8-24eafb8e5f30] RpcClient init label, labels = {module=naming, source=sdk}
16:38:35.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e66add63-3a88-4a73-bed8-24eafb8e5f30] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:38:35.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e66add63-3a88-4a73-bed8-24eafb8e5f30] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:38:35.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e66add63-3a88-4a73-bed8-24eafb8e5f30] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:38:35.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e66add63-3a88-4a73-bed8-24eafb8e5f30] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:35.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e66add63-3a88-4a73-bed8-24eafb8e5f30] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750235915266_127.0.0.1_53688
16:38:35.381 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e66add63-3a88-4a73-bed8-24eafb8e5f30] Notify connected event to listeners.
16:38:35.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e66add63-3a88-4a73-bed8-24eafb8e5f30] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:38:35.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e66add63-3a88-4a73-bed8-24eafb8e5f30] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/255156436
16:38:35.445 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
16:38:35.445 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
16:38:35.454 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
16:38:35.456 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
17:52:35.084 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:52:35.086 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:52:35.426 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:52:35.427 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@69ac0e15[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:52:35.428 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750235910880_127.0.0.1_53609
17:52:35.444 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4503813e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 900]
17:52:41.860 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:52:43.190 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 27e6717e-6ec0-4108-a441-c53fa57ba56b_config-0
17:52:43.285 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
17:52:43.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
17:52:43.362 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
17:52:43.544 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 178 ms to scan 209 urls, producing 0 keys and 0 values 
17:52:43.557 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
17:52:43.577 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
17:52:43.593 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
17:52:43.763 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 169 ms to scan 209 urls, producing 0 keys and 0 values 
17:52:43.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27e6717e-6ec0-4108-a441-c53fa57ba56b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:52:43.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27e6717e-6ec0-4108-a441-c53fa57ba56b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1948224958
17:52:43.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27e6717e-6ec0-4108-a441-c53fa57ba56b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/556662073
17:52:43.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27e6717e-6ec0-4108-a441-c53fa57ba56b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:52:43.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27e6717e-6ec0-4108-a441-c53fa57ba56b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:52:43.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27e6717e-6ec0-4108-a441-c53fa57ba56b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:52:46.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27e6717e-6ec0-4108-a441-c53fa57ba56b_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750240365636_127.0.0.1_61170
17:52:46.044 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27e6717e-6ec0-4108-a441-c53fa57ba56b_config-0] Notify connected event to listeners.
17:52:46.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27e6717e-6ec0-4108-a441-c53fa57ba56b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:52:46.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27e6717e-6ec0-4108-a441-c53fa57ba56b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1168849885
17:52:46.200 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:52:50.150 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
17:52:50.152 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:52:50.152 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:52:50.456 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:52:53.508 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:52:54.529 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1f99d2a7-f042-4b1f-960c-76f2a17097fc
17:52:54.530 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] RpcClient init label, labels = {module=naming, source=sdk}
17:52:54.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:52:54.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:52:54.537 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:52:54.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:52:54.658 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750240374543_127.0.0.1_61266
17:52:54.658 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:52:54.658 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] Notify connected event to listeners.
17:52:54.658 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1168849885
17:52:56.502 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
17:52:56.547 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
17:52:56.908 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 15.837 seconds (JVM running for 17.215)
17:52:56.940 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
17:52:56.940 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
17:52:56.943 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
17:52:57.067 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] Receive server push request, request = NotifySubscriberRequest, requestId = 21
17:52:57.073 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] Ack server push request, request = NotifySubscriberRequest, requestId = 21
17:52:57.620 [RMI TCP Connection(5)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:55:54.296 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] Receive server push request, request = NotifySubscriberRequest, requestId = 24
17:55:54.296 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f99d2a7-f042-4b1f-960c-76f2a17097fc] Ack server push request, request = NotifySubscriberRequest, requestId = 24
17:55:55.427 [http-nio-9700-exec-10] INFO  c.r.a.s.SSOClientService - [createLocalUser,226] - 创建本地用户成功: testuser
18:00:19.310 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:00:19.313 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:00:19.648 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:00:19.668 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@488f59a4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:00:19.668 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750240374543_127.0.0.1_61266
18:00:19.688 [nacos-grpc-client-executor-102] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750240374543_127.0.0.1_61266]Ignore complete event,isRunning:false,isAbandon=false
18:00:19.709 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5bce1a73[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 103]
18:00:23.446 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
18:00:24.413 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4f4cc916-2700-4574-a389-50aa5d14ba07_config-0
18:00:24.481 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
18:00:24.519 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
18:00:24.532 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
18:00:24.685 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 150 ms to scan 209 urls, producing 0 keys and 0 values 
18:00:24.696 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
18:00:24.710 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
18:00:24.721 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
18:00:24.890 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 166 ms to scan 209 urls, producing 0 keys and 0 values 
18:00:24.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f4cc916-2700-4574-a389-50aa5d14ba07_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:00:24.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f4cc916-2700-4574-a389-50aa5d14ba07_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/131709548
18:00:24.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f4cc916-2700-4574-a389-50aa5d14ba07_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/148813381
18:00:24.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f4cc916-2700-4574-a389-50aa5d14ba07_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:00:24.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f4cc916-2700-4574-a389-50aa5d14ba07_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:00:24.907 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f4cc916-2700-4574-a389-50aa5d14ba07_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
18:00:26.875 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f4cc916-2700-4574-a389-50aa5d14ba07_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750240826462_127.0.0.1_62135
18:00:26.878 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f4cc916-2700-4574-a389-50aa5d14ba07_config-0] Notify connected event to listeners.
18:00:26.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f4cc916-2700-4574-a389-50aa5d14ba07_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:00:26.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f4cc916-2700-4574-a389-50aa5d14ba07_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/951317000
18:00:27.061 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
18:00:31.279 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
18:00:31.280 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:00:31.280 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
18:00:31.566 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:00:34.201 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:00:35.002 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d624392c-7d88-4737-89ed-813448b44b8b
18:00:35.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d624392c-7d88-4737-89ed-813448b44b8b] RpcClient init label, labels = {module=naming, source=sdk}
18:00:35.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d624392c-7d88-4737-89ed-813448b44b8b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:00:35.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d624392c-7d88-4737-89ed-813448b44b8b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:00:35.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d624392c-7d88-4737-89ed-813448b44b8b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:00:35.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d624392c-7d88-4737-89ed-813448b44b8b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
18:00:35.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d624392c-7d88-4737-89ed-813448b44b8b] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750240835021_127.0.0.1_62244
18:00:35.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d624392c-7d88-4737-89ed-813448b44b8b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:00:35.128 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d624392c-7d88-4737-89ed-813448b44b8b] Notify connected event to listeners.
18:00:35.128 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d624392c-7d88-4737-89ed-813448b44b8b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/951317000
18:00:36.789 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
18:00:36.834 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
18:00:37.137 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 14.413 seconds (JVM running for 15.862)
18:00:37.165 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
18:00:37.166 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
18:00:37.167 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
18:00:37.299 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d624392c-7d88-4737-89ed-813448b44b8b] Receive server push request, request = NotifySubscriberRequest, requestId = 29
18:00:37.303 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d624392c-7d88-4737-89ed-813448b44b8b] Ack server push request, request = NotifySubscriberRequest, requestId = 29
18:00:37.810 [RMI TCP Connection(3)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:11:01.511 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:11:01.513 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:11:01.846 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:11:01.846 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6e269989[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:11:01.846 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750240835021_127.0.0.1_62244
18:11:01.849 [nacos-grpc-client-executor-137] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750240835021_127.0.0.1_62244]Ignore complete event,isRunning:false,isAbandon=false
18:11:01.855 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@71727e43[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 138]
18:11:08.064 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
18:11:09.069 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8866495b-208f-448f-99f6-9344ecfa128f_config-0
18:11:09.135 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
18:11:09.172 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
18:11:09.191 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
18:11:09.353 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 159 ms to scan 209 urls, producing 0 keys and 0 values 
18:11:09.368 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
18:11:09.381 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
18:11:09.397 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
18:11:09.581 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 182 ms to scan 209 urls, producing 0 keys and 0 values 
18:11:09.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8866495b-208f-448f-99f6-9344ecfa128f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:11:09.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8866495b-208f-448f-99f6-9344ecfa128f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1018742990
18:11:09.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8866495b-208f-448f-99f6-9344ecfa128f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/534643569
18:11:09.589 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8866495b-208f-448f-99f6-9344ecfa128f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:11:09.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8866495b-208f-448f-99f6-9344ecfa128f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:11:09.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8866495b-208f-448f-99f6-9344ecfa128f_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
18:11:11.675 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8866495b-208f-448f-99f6-9344ecfa128f_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750241471332_127.0.0.1_63957
18:11:11.677 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8866495b-208f-448f-99f6-9344ecfa128f_config-0] Notify connected event to listeners.
18:11:11.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8866495b-208f-448f-99f6-9344ecfa128f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:11:11.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8866495b-208f-448f-99f6-9344ecfa128f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/2063581529
18:11:11.857 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
18:11:15.079 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
18:11:15.079 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:11:15.080 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
18:11:15.337 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:11:17.750 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:11:18.510 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 040a66c6-1ac8-443d-a5b0-5082d431719d
18:11:18.510 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040a66c6-1ac8-443d-a5b0-5082d431719d] RpcClient init label, labels = {module=naming, source=sdk}
18:11:18.515 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040a66c6-1ac8-443d-a5b0-5082d431719d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:11:18.515 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040a66c6-1ac8-443d-a5b0-5082d431719d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:11:18.515 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040a66c6-1ac8-443d-a5b0-5082d431719d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:11:18.516 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040a66c6-1ac8-443d-a5b0-5082d431719d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
18:11:18.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040a66c6-1ac8-443d-a5b0-5082d431719d] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750241478520_127.0.0.1_64052
18:11:18.638 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040a66c6-1ac8-443d-a5b0-5082d431719d] Notify connected event to listeners.
18:11:18.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040a66c6-1ac8-443d-a5b0-5082d431719d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:11:18.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040a66c6-1ac8-443d-a5b0-5082d431719d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/2063581529
18:11:20.110 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
18:11:20.156 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
18:11:20.469 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 13.178 seconds (JVM running for 14.694)
18:11:20.492 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
18:11:20.493 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
18:11:20.494 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
18:11:20.593 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040a66c6-1ac8-443d-a5b0-5082d431719d] Receive server push request, request = NotifySubscriberRequest, requestId = 36
18:11:20.598 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [040a66c6-1ac8-443d-a5b0-5082d431719d] Ack server push request, request = NotifySubscriberRequest, requestId = 36
18:11:21.134 [RMI TCP Connection(2)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
