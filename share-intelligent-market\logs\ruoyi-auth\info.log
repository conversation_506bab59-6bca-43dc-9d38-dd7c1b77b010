15:04:42.332 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
15:04:43.118 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0
15:04:43.159 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 3 keys and 6 values 
15:04:43.187 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
15:04:43.204 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
15:04:43.392 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 185 ms to scan 208 urls, producing 0 keys and 0 values 
15:04:43.407 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
15:04:43.422 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
15:04:43.435 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
15:04:43.609 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 172 ms to scan 208 urls, producing 0 keys and 0 values 
15:04:43.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:04:43.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/2016038911
15:04:43.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1187410086
15:04:43.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:04:43.613 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:04:43.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:04:44.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750230284553_127.0.0.1_60112
15:04:44.712 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Notify connected event to listeners.
15:04:44.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:04:44.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [251b5f3b-cd2e-4dfc-9821-0b420492ff79_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1183905155
15:04:44.768 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
15:04:46.053 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
15:04:46.054 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:04:46.054 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
15:04:46.171 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:04:47.046 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:04:47.573 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 61d6c5fe-b47d-4483-a72f-996ed3b31790
15:04:47.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] RpcClient init label, labels = {module=naming, source=sdk}
15:04:47.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:04:47.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:04:47.574 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:04:47.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:04:47.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750230287579_127.0.0.1_60121
15:04:47.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:04:47.685 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Notify connected event to listeners.
15:04:47.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1183905155
15:04:48.439 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
15:04:48.455 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
15:04:48.772 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 7.149 seconds (JVM running for 8.08)
15:04:48.780 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=DEFAULT_GROUP
15:04:48.782 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=DEFAULT_GROUP
15:04:48.783 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=DEFAULT_GROUP
15:04:48.967 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Receive server push request, request = NotifySubscriberRequest, requestId = 10
15:04:48.971 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [61d6c5fe-b47d-4483-a72f-996ed3b31790] Ack server push request, request = NotifySubscriberRequest, requestId = 10
15:04:49.081 [RMI TCP Connection(3)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
