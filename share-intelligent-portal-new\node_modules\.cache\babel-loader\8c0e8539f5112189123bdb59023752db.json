{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\verificationCode\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\verificationCode\\index.vue", "mtime": 1750311962830}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "props", "value", "String", "mobile", "type", "Object", "default", "placeholder", "size", "beforeSendCode", "Function", "sendParams", "Array", "showIcon", "Boolean", "maxlength", "Number", "data", "msgText", "msgTime", "msgKey", "sendLoading", "computed", "hValue", "get", "set", "val", "$emit", "config", "MSGINIT", "MSGSCUCCESS", "MSGTIME", "created", "methods", "handleSendCode", "_this", "result", "Promise", "resolve", "then", "sendCode", "_this2", "phone", "$message", "warning", "params", "push", "concat", "getCommonCode", "telphone", "res", "code", "error", "msg", "$refs", "input", "focus", "time", "setInterval", "clearInterval", "catch"], "sources": ["src/components/verificationCode/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"bc-verification-code\">\r\n    <el-input\r\n      ref=\"input\"\r\n      v-model=\"hValue\"\r\n      autocomplete=\"off\"\r\n      class=\"bc-verification-input\"\r\n      :size=\"size\"\r\n      :maxlength=\"maxlength\"\r\n      :placeholder=\"placeholder\"\r\n    >\r\n      <img\r\n        slot=\"prefix\"\r\n        src=\"../../assets/login/mailIcon.png\"\r\n        alt=\"\"\r\n        class=\"input-icon\"\r\n      />\r\n      <el-button\r\n        slot=\"suffix\"\r\n        type=\"text\"\r\n        class=\"sendCode\"\r\n        :size=\"size\"\r\n        :disabled=\"msgKey\"\r\n        :loading=\"sendLoading\"\r\n        @click=\"handleSendCode\"\r\n      >\r\n        {{ msgKey ? `${msgTime} S后重新发送` : \"发送验证码\" }}\r\n      </el-button>\r\n    </el-input>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCommonCode } from \"@/api/login\";\r\n\r\nexport default {\r\n  props: {\r\n    value: String,\r\n    mobile: {\r\n      type: [String, Object],\r\n      default: \"\",\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: \"请输入验证码\",\r\n    },\r\n    size: String,\r\n    beforeSendCode: Function,\r\n    sendParams: {\r\n      type: Array,\r\n      default() {\r\n        return [];\r\n      },\r\n    },\r\n    showIcon: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    maxlength: {\r\n      type: Number,\r\n      default: 6,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      msgText: \"\",\r\n      msgTime: \"\",\r\n      msgKey: false,\r\n      sendLoading: false,\r\n    };\r\n  },\r\n  computed: {\r\n    hValue: {\r\n      get() {\r\n        return this.value;\r\n      },\r\n      set(val) {\r\n        this.$emit(\"input\", val);\r\n      },\r\n    },\r\n    config() {\r\n      return {\r\n        MSGINIT: \"发送验证码\",\r\n        MSGSCUCCESS: \"秒后重发\",\r\n        MSGTIME: 60,\r\n      };\r\n    },\r\n  },\r\n  created() {\r\n    this.msgText = this.config.MSGINIT;\r\n    this.msgTime = this.config.MSGTIME;\r\n  },\r\n  methods: {\r\n    handleSendCode() {\r\n      this.hValue = \"\";\r\n      if (this.beforeSendCode) {\r\n        let result = this.beforeSendCode();\r\n        if (result === false) {\r\n          return;\r\n        }\r\n        if (result !== true) {\r\n          Promise.resolve(result).then(() => {\r\n            this.sendCode();\r\n          });\r\n        }\r\n      } else {\r\n        this.sendCode();\r\n      }\r\n    },\r\n    sendCode() {\r\n      if (!this.mobile.phone) {\r\n        this.$message.warning(\"请输入账号\");\r\n        return;\r\n      }\r\n      this.sendLoading = true;\r\n      this.msgText = \"发送中\";\r\n      let params = [];\r\n      if (this.mobile) {\r\n        params.push(this.mobile);\r\n      }\r\n      if (this.sendParams) {\r\n        params = params.concat(this.sendParams);\r\n      }\r\n      getCommonCode({ telphone: this.mobile.phone })\r\n        .then((res) => {\r\n          if (res.code !== 200) {\r\n            this.$message.error(res.msg);\r\n            this.sendLoading = false;\r\n            return;\r\n          }\r\n          this.$emit(\"after-send\", res.data || {});\r\n          this.sendLoading = false;\r\n          this.msgText = this.msgTime + this.config.MSGSCUCCESS;\r\n          this.msgKey = true;\r\n          this.$refs.input.focus();\r\n          const time = setInterval(() => {\r\n            this.msgTime--;\r\n            this.msgText = this.msgTime + this.config.MSGSCUCCESS;\r\n            if (this.msgTime === 0) {\r\n              this.msgTime = this.config.MSGTIME;\r\n              this.msgText = this.config.MSGINIT;\r\n              this.msgKey = false;\r\n              clearInterval(time);\r\n            }\r\n          }, 1000);\r\n        })\r\n        .catch(() => {\r\n          this.sendLoading = false;\r\n          this.msgText = this.config.MSGINIT;\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.bc-verification-code {\r\n  display: flex;\r\n  .bc-verification-input {\r\n    flex: 1;\r\n    .el-input__inner {\r\n      // width: 400px;\r\n      height: 40px;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 14px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 14px;\r\n      padding-left: 40px;\r\n    }\r\n    .input-icon {\r\n      width: 16px;\r\n      height: 16px;\r\n      margin: 12px;\r\n    }\r\n  }\r\n  .el-button {\r\n    margin-left: 10px;\r\n    height: 40px !important;\r\n    line-height: 40px;\r\n    padding: 0;\r\n    float: right;\r\n  }\r\n  .sendCode {\r\n    margin-right: 12px;\r\n    text-decoration: inherit;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    color: #21c9b8;\r\n    line-height: 16px;\r\n  }\r\n  .el-button.is-disabled {\r\n    color: #cfcfcf;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAiCA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAEA;EACAC,KAAA;IACAC,KAAA,EAAAC,MAAA;IACAC,MAAA;MACAC,IAAA,GAAAF,MAAA,EAAAG,MAAA;MACAC,OAAA;IACA;IACAC,WAAA;MACAH,IAAA,EAAAF,MAAA;MACAI,OAAA;IACA;IACAE,IAAA,EAAAN,MAAA;IACAO,cAAA,EAAAC,QAAA;IACAC,UAAA;MACAP,IAAA,EAAAQ,KAAA;MACAN,OAAA,WAAAA,SAAA;QACA;MACA;IACA;IACAO,QAAA;MACAT,IAAA,EAAAU,OAAA;MACAR,OAAA;IACA;IACAS,SAAA;MACAX,IAAA,EAAAY,MAAA;MACAV,OAAA;IACA;EACA;EACAW,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,OAAA;MACAC,MAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACAC,MAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAvB,KAAA;MACA;MACAwB,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAC,KAAA,UAAAD,GAAA;MACA;IACA;IACAE,MAAA,WAAAA,OAAA;MACA;QACAC,OAAA;QACAC,WAAA;QACAC,OAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAd,OAAA,QAAAU,MAAA,CAAAC,OAAA;IACA,KAAAV,OAAA,QAAAS,MAAA,CAAAG,OAAA;EACA;EACAE,OAAA;IACAC,cAAA,WAAAA,eAAA;MAAA,IAAAC,KAAA;MACA,KAAAZ,MAAA;MACA,SAAAd,cAAA;QACA,IAAA2B,MAAA,QAAA3B,cAAA;QACA,IAAA2B,MAAA;UACA;QACA;QACA,IAAAA,MAAA;UACAC,OAAA,CAAAC,OAAA,CAAAF,MAAA,EAAAG,IAAA;YACAJ,KAAA,CAAAK,QAAA;UACA;QACA;MACA;QACA,KAAAA,QAAA;MACA;IACA;IACAA,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,UAAAtC,MAAA,CAAAuC,KAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAvB,WAAA;MACA,KAAAH,OAAA;MACA,IAAA2B,MAAA;MACA,SAAA1C,MAAA;QACA0C,MAAA,CAAAC,IAAA,MAAA3C,MAAA;MACA;MACA,SAAAQ,UAAA;QACAkC,MAAA,GAAAA,MAAA,CAAAE,MAAA,MAAApC,UAAA;MACA;MACA,IAAAqC,oBAAA;QAAAC,QAAA,OAAA9C,MAAA,CAAAuC;MAAA,GACAH,IAAA,WAAAW,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAV,MAAA,CAAAE,QAAA,CAAAS,KAAA,CAAAF,GAAA,CAAAG,GAAA;UACAZ,MAAA,CAAApB,WAAA;UACA;QACA;QACAoB,MAAA,CAAAd,KAAA,eAAAuB,GAAA,CAAAjC,IAAA;QACAwB,MAAA,CAAApB,WAAA;QACAoB,MAAA,CAAAvB,OAAA,GAAAuB,MAAA,CAAAtB,OAAA,GAAAsB,MAAA,CAAAb,MAAA,CAAAE,WAAA;QACAW,MAAA,CAAArB,MAAA;QACAqB,MAAA,CAAAa,KAAA,CAAAC,KAAA,CAAAC,KAAA;QACA,IAAAC,IAAA,GAAAC,WAAA;UACAjB,MAAA,CAAAtB,OAAA;UACAsB,MAAA,CAAAvB,OAAA,GAAAuB,MAAA,CAAAtB,OAAA,GAAAsB,MAAA,CAAAb,MAAA,CAAAE,WAAA;UACA,IAAAW,MAAA,CAAAtB,OAAA;YACAsB,MAAA,CAAAtB,OAAA,GAAAsB,MAAA,CAAAb,MAAA,CAAAG,OAAA;YACAU,MAAA,CAAAvB,OAAA,GAAAuB,MAAA,CAAAb,MAAA,CAAAC,OAAA;YACAY,MAAA,CAAArB,MAAA;YACAuC,aAAA,CAAAF,IAAA;UACA;QACA;MACA,GACAG,KAAA;QACAnB,MAAA,CAAApB,WAAA;QACAoB,MAAA,CAAAvB,OAAA,GAAAuB,MAAA,CAAAb,MAAA,CAAAC,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}