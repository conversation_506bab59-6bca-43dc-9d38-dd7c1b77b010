{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\detail.vue", "mtime": 1750311962979}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRQb2xpY3lEZWNsYXJlRGV0YWlsIH0gZnJvbSAiQC9hcGkvcG9saWN5RGVjbGFyZSI7DQppbXBvcnQgeyBtYXBHZXR0ZXJzIH0gZnJvbSAidnVleCI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBkYXRhOiB7fSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuaW5pdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaW5pdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBnZXRQb2xpY3lEZWNsYXJlRGV0YWlsKHsgaWQ6IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkIH0pDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmRhdGEgPSByZXMuZGF0YSB8fCB7fTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDot7PovazlnKjnur/nlLPmiqXpobXpnaINCiAgICBnb0RlY2xhcmUoKSB7DQogICAgICAvLyDliKTmlq3mmK/lkKbnmbvlvZUNCiAgICAgIGlmICghdGhpcy50b2tlbikgew0KICAgICAgICB0aGlzLiRjb25maXJtKCLor7flhYjnmbvlvZUiLCAi5o+Q56S6Iiwgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi5Y6755m75b2VIiwNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCJMb2dPdXQiKS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgIGxvY2F0aW9uLmhyZWYgPSAiL2xvZ2luIjsNCiAgICAgICAgICB9KTsNCiAgICAgICAgfSk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgbmFtZTogIkFkZFBvbGljeSIsDQogICAgICAgIHBhcmFtczogew0KICAgICAgICAgIGlkOiB0aGlzLiRyb3V0ZS5xdWVyeS5pZCwNCiAgICAgICAgICB0aXRsZTogdGhpcy5kYXRhLnRpdGxlLA0KICAgICAgICB9LA0KICAgICAgfSk7DQogICAgfSwNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAuLi5tYXBHZXR0ZXJzKFsidG9rZW4iXSksDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/policy/declare", "sourcesContent": ["<template>\r\n  <div class=\"policy-declare-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"policy-declarel-detail-banner\">\r\n      <img\r\n        src=\"../../../assets/policyDeclare/policyDeclareDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"policy-declarel-detail-title-box\">\r\n      <div class=\"policy-declarel-detail-divider\"></div>\r\n      <div class=\"policy-declarel-detail-title\">政策详情</div>\r\n      <div class=\"policy-declarel-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"policy-declarel-detail-card\">\r\n      <div class=\"policy-declarel-detail-content\">\r\n        <div class=\"policy-declarel-detail-title\">{{ data.title }}</div>\r\n        <div class=\"headline-box\">\r\n          <div class=\"headline-address\">{{ data.releaseUnitName }}</div>\r\n          <div>{{ data.createTime }}</div>\r\n        </div>\r\n        <div class=\"declarel-detail-content\">\r\n          <div\r\n            v-html=\"data.content\"\r\n            class=\"declarel-detail-text ql-editor\"\r\n          ></div>\r\n        </div>\r\n        <!-- 在线申报按钮 -->\r\n        <div v-if=\"data.policyStatus !== 2\" class=\"activity-area-btn\">\r\n          <el-button class=\"activity-sign-up\" @click=\"goDeclare\"\r\n            >在线申报\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getPolicyDeclareDetail } from \"@/api/policyDeclare\";\r\nimport { mapGetters } from \"vuex\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      getPolicyDeclareDetail({ id: this.$route.query.id })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 跳转在线申报页面\r\n    goDeclare() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.$router.push({\r\n        name: \"AddPolicy\",\r\n        params: {\r\n          id: this.$route.query.id,\r\n          title: this.data.title,\r\n        },\r\n      });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.policy-declare-detail-container {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n  background: #f4f5f9;\r\n\r\n  .policy-declarel-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .policy-declarel-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .policy-declarel-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .policy-declarel-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .policy-declarel-detail-card {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n\r\n    .policy-declarel-detail-content {\r\n      padding: 60px;\r\n\r\n      .policy-declarel-detail-title {\r\n        font-size: 32px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      .headline-box {\r\n        display: flex;\r\n        font-size: 12px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        font-weight: 400;\r\n        color: #999;\r\n        line-height: 12px;\r\n        padding: 40px 0 10px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n\r\n        .headline-address {\r\n          max-width: 990px;\r\n          word-break: break-all;\r\n          padding-right: 40px;\r\n        }\r\n      }\r\n\r\n      .declarel-detail-content {\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n        padding-top: 60px;\r\n      }\r\n\r\n      .activity-area-btn {\r\n        text-align: center;\r\n        margin-top: 96px;\r\n\r\n        .activity-sign-up {\r\n          width: 400px;\r\n          height: 50px;\r\n          background: #21c9b8;\r\n          border-radius: 4px;\r\n          font-size: 20px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #fff;\r\n          line-height: 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.policy-declare-detail-container {\r\n  .declarel-detail-content {\r\n    .declarel-detail-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}