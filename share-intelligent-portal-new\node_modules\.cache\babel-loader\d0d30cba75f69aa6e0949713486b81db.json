{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\utils\\generator\\js.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\utils\\generator\\js.js", "mtime": 1750311962905}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_util", "require", "_index", "_config", "units", "KB", "MB", "GB", "confGlobal", "inheritAttrs", "file", "dialog", "makeUpJs", "conf", "type", "JSON", "parse", "stringify", "dataList", "ruleList", "optionsList", "propsList", "methodList", "mixinMethod", "uploadVarList", "fields", "for<PERSON>ach", "el", "buildAttributes", "script", "buildexport", "join", "buildData", "buildRules", "options", "length", "buildOptions", "dataType", "model", "concat", "vModel", "titleCase", "buildOptionMethod", "props", "buildProps", "action", "tag", "push", "buildBeforeUpload", "buildSubmitUpload", "children", "el2", "list", "minxins", "formBtns", "submitForm", "formRef", "resetForm", "onOpen", "onClose", "close", "handleConfirm", "methods", "Object", "keys", "key", "undefined", "defaultValue", "multiple", "rules", "trigger", "required", "isArray", "message", "placeholder", "label", "regList", "item", "pattern", "eval", "str", "valueKey", "value", "labelKey", "<PERSON><PERSON><PERSON>", "unitNum", "sizeUnit", "rightSizeCode", "acceptCode", "returnList", "fileSize", "accept", "methodName", "data", "selectOptions", "uploadVar", "exportDefault", "formModel", "formRules"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/utils/generator/js.js"], "sourcesContent": ["import { isArray } from 'util'\r\nimport { exportDefault, titleCase } from '@/utils/index'\r\nimport { trigger } from './config'\r\n\r\nconst units = {\r\n  KB: '1024',\r\n  MB: '1024 / 1024',\r\n  GB: '1024 / 1024 / 1024'\r\n}\r\nlet confGlobal\r\nconst inheritAttrs = {\r\n  file: '',\r\n  dialog: 'inheritAttrs: false,'\r\n}\r\n\r\n\r\nexport function makeUpJs(conf, type) {\r\n  confGlobal = conf = JSON.parse(JSON.stringify(conf))\r\n  const dataList = []\r\n  const ruleList = []\r\n  const optionsList = []\r\n  const propsList = []\r\n  const methodList = mixinMethod(type)\r\n  const uploadVarList = []\r\n\r\n  conf.fields.forEach(el => {\r\n    buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList)\r\n  })\r\n\r\n  const script = buildexport(\r\n    conf,\r\n    type,\r\n    dataList.join('\\n'),\r\n    ruleList.join('\\n'),\r\n    optionsList.join('\\n'),\r\n    uploadVarList.join('\\n'),\r\n    propsList.join('\\n'),\r\n    methodList.join('\\n')\r\n  )\r\n  confGlobal = null\r\n  return script\r\n}\r\n\r\nfunction buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList) {\r\n  buildData(el, dataList)\r\n  buildRules(el, ruleList)\r\n\r\n  if (el.options && el.options.length) {\r\n    buildOptions(el, optionsList)\r\n    if (el.dataType === 'dynamic') {\r\n      const model = `${el.vModel}Options`\r\n      const options = titleCase(model)\r\n      buildOptionMethod(`get${options}`, model, methodList)\r\n    }\r\n  }\r\n\r\n  if (el.props && el.props.props) {\r\n    buildProps(el, propsList)\r\n  }\r\n\r\n  if (el.action && el.tag === 'el-upload') {\r\n    uploadVarList.push(\r\n      `${el.vModel}Action: '${el.action}',\r\n      ${el.vModel}fileList: [],`\r\n    )\r\n    methodList.push(buildBeforeUpload(el))\r\n    if (!el['auto-upload']) {\r\n      methodList.push(buildSubmitUpload(el))\r\n    }\r\n  }\r\n\r\n  if (el.children) {\r\n    el.children.forEach(el2 => {\r\n      buildAttributes(el2, dataList, ruleList, optionsList, methodList, propsList, uploadVarList)\r\n    })\r\n  }\r\n}\r\n\r\nfunction mixinMethod(type) {\r\n  const list = []; const\r\n    minxins = {\r\n      file: confGlobal.formBtns ? {\r\n        submitForm: `submitForm() {\r\n        this.$refs['${confGlobal.formRef}'].validate(valid => {\r\n          if(!valid) return\r\n          // TODO 提交表单\r\n        })\r\n      },`,\r\n        resetForm: `resetForm() {\r\n        this.$refs['${confGlobal.formRef}'].resetFields()\r\n      },`\r\n      } : null,\r\n      dialog: {\r\n        onOpen: 'onOpen() {},',\r\n        onClose: `onClose() {\r\n        this.$refs['${confGlobal.formRef}'].resetFields()\r\n      },`,\r\n        close: `close() {\r\n        this.$emit('update:visible', false)\r\n      },`,\r\n        handleConfirm: `handleConfirm() {\r\n        this.$refs['${confGlobal.formRef}'].validate(valid => {\r\n          if(!valid) return\r\n          this.close()\r\n        })\r\n      },`\r\n      }\r\n    }\r\n\r\n  const methods = minxins[type]\r\n  if (methods) {\r\n    Object.keys(methods).forEach(key => {\r\n      list.push(methods[key])\r\n    })\r\n  }\r\n\r\n  return list\r\n}\r\n\r\nfunction buildData(conf, dataList) {\r\n  if (conf.vModel === undefined) return\r\n  let defaultValue\r\n  if (typeof (conf.defaultValue) === 'string' && !conf.multiple) {\r\n    defaultValue = `'${conf.defaultValue}'`\r\n  } else {\r\n    defaultValue = `${JSON.stringify(conf.defaultValue)}`\r\n  }\r\n  dataList.push(`${conf.vModel}: ${defaultValue},`)\r\n}\r\n\r\nfunction buildRules(conf, ruleList) {\r\n  if (conf.vModel === undefined) return\r\n  const rules = []\r\n  if (trigger[conf.tag]) {\r\n    if (conf.required) {\r\n      const type = isArray(conf.defaultValue) ? 'type: \\'array\\',' : ''\r\n      let message = isArray(conf.defaultValue) ? `请至少选择一个${conf.vModel}` : conf.placeholder\r\n      if (message === undefined) message = `${conf.label}不能为空`\r\n      rules.push(`{ required: true, ${type} message: '${message}', trigger: '${trigger[conf.tag]}' }`)\r\n    }\r\n    if (conf.regList && isArray(conf.regList)) {\r\n      conf.regList.forEach(item => {\r\n        if (item.pattern) {\r\n          rules.push(`{ pattern: ${eval(item.pattern)}, message: '${item.message}', trigger: '${trigger[conf.tag]}' }`)\r\n        }\r\n      })\r\n    }\r\n    ruleList.push(`${conf.vModel}: [${rules.join(',')}],`)\r\n  }\r\n}\r\n\r\nfunction buildOptions(conf, optionsList) {\r\n  if (conf.vModel === undefined) return\r\n  if (conf.dataType === 'dynamic') { conf.options = [] }\r\n  const str = `${conf.vModel}Options: ${JSON.stringify(conf.options)},`\r\n  optionsList.push(str)\r\n}\r\n\r\nfunction buildProps(conf, propsList) {\r\n  if (conf.dataType === 'dynamic') {\r\n    conf.valueKey !== 'value' && (conf.props.props.value = conf.valueKey)\r\n    conf.labelKey !== 'label' && (conf.props.props.label = conf.labelKey)\r\n    conf.childrenKey !== 'children' && (conf.props.props.children = conf.childrenKey)\r\n  }\r\n  const str = `${conf.vModel}Props: ${JSON.stringify(conf.props.props)},`\r\n  propsList.push(str)\r\n}\r\n\r\nfunction buildBeforeUpload(conf) {\r\n  const unitNum = units[conf.sizeUnit]; let rightSizeCode = ''; let acceptCode = ''; const\r\n    returnList = []\r\n  if (conf.fileSize) {\r\n    rightSizeCode = `let isRightSize = file.size / ${unitNum} < ${conf.fileSize}\r\n    if(!isRightSize){\r\n      this.$message.error('文件大小超过 ${conf.fileSize}${conf.sizeUnit}')\r\n    }`\r\n    returnList.push('isRightSize')\r\n  }\r\n  if (conf.accept) {\r\n    acceptCode = `let isAccept = new RegExp('${conf.accept}').test(file.type)\r\n    if(!isAccept){\r\n      this.$message.error('应该选择${conf.accept}类型的文件')\r\n    }`\r\n    returnList.push('isAccept')\r\n  }\r\n  const str = `${conf.vModel}BeforeUpload(file) {\r\n    ${rightSizeCode}\r\n    ${acceptCode}\r\n    return ${returnList.join('&&')}\r\n  },`\r\n  return returnList.length ? str : ''\r\n}\r\n\r\nfunction buildSubmitUpload(conf) {\r\n  const str = `submitUpload() {\r\n    this.$refs['${conf.vModel}'].submit()\r\n  },`\r\n  return str\r\n}\r\n\r\nfunction buildOptionMethod(methodName, model, methodList) {\r\n  const str = `${methodName}() {\r\n    // TODO 发起请求获取数据\r\n    this.${model}\r\n  },`\r\n  methodList.push(str)\r\n}\r\n\r\nfunction buildexport(conf, type, data, rules, selectOptions, uploadVar, props, methods) {\r\n  const str = `${exportDefault}{\r\n  ${inheritAttrs[type]}\r\n  components: {},\r\n  props: [],\r\n  data () {\r\n    return {\r\n      ${conf.formModel}: {\r\n        ${data}\r\n      },\r\n      ${conf.formRules}: {\r\n        ${rules}\r\n      },\r\n      ${uploadVar}\r\n      ${selectOptions}\r\n      ${props}\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {},\r\n  created () {},\r\n  mounted () {},\r\n  methods: {\r\n    ${methods}\r\n  }\r\n}`\r\n  return str\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AAEA,IAAMG,KAAK,GAAG;EACZC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,aAAa;EACjBC,EAAE,EAAE;AACN,CAAC;AACD,IAAIC,UAAU;AACd,IAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE;AACV,CAAC;AAGM,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACnCN,UAAU,GAAGK,IAAI,GAAGE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,IAAI,CAAC,CAAC;EACpD,IAAMK,QAAQ,GAAG,EAAE;EACnB,IAAMC,QAAQ,GAAG,EAAE;EACnB,IAAMC,WAAW,GAAG,EAAE;EACtB,IAAMC,SAAS,GAAG,EAAE;EACpB,IAAMC,UAAU,GAAGC,WAAW,CAACT,IAAI,CAAC;EACpC,IAAMU,aAAa,GAAG,EAAE;EAExBX,IAAI,CAACY,MAAM,CAACC,OAAO,CAAC,UAAAC,EAAE,EAAI;IACxBC,eAAe,CAACD,EAAE,EAAET,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,CAAC;EAC5F,CAAC,CAAC;EAEF,IAAMK,MAAM,GAAGC,WAAW,CACxBjB,IAAI,EACJC,IAAI,EACJI,QAAQ,CAACa,IAAI,CAAC,IAAI,CAAC,EACnBZ,QAAQ,CAACY,IAAI,CAAC,IAAI,CAAC,EACnBX,WAAW,CAACW,IAAI,CAAC,IAAI,CAAC,EACtBP,aAAa,CAACO,IAAI,CAAC,IAAI,CAAC,EACxBV,SAAS,CAACU,IAAI,CAAC,IAAI,CAAC,EACpBT,UAAU,CAACS,IAAI,CAAC,IAAI,CACtB,CAAC;EACDvB,UAAU,GAAG,IAAI;EACjB,OAAOqB,MAAM;AACf;AAEA,SAASD,eAAeA,CAACD,EAAE,EAAET,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,EAAE;EAClGQ,SAAS,CAACL,EAAE,EAAET,QAAQ,CAAC;EACvBe,UAAU,CAACN,EAAE,EAAER,QAAQ,CAAC;EAExB,IAAIQ,EAAE,CAACO,OAAO,IAAIP,EAAE,CAACO,OAAO,CAACC,MAAM,EAAE;IACnCC,YAAY,CAACT,EAAE,EAAEP,WAAW,CAAC;IAC7B,IAAIO,EAAE,CAACU,QAAQ,KAAK,SAAS,EAAE;MAC7B,IAAMC,KAAK,MAAAC,MAAA,CAAMZ,EAAE,CAACa,MAAM,YAAS;MACnC,IAAMN,OAAO,GAAG,IAAAO,gBAAS,EAACH,KAAK,CAAC;MAChCI,iBAAiB,OAAAH,MAAA,CAAOL,OAAO,GAAII,KAAK,EAAEhB,UAAU,CAAC;IACvD;EACF;EAEA,IAAIK,EAAE,CAACgB,KAAK,IAAIhB,EAAE,CAACgB,KAAK,CAACA,KAAK,EAAE;IAC9BC,UAAU,CAACjB,EAAE,EAAEN,SAAS,CAAC;EAC3B;EAEA,IAAIM,EAAE,CAACkB,MAAM,IAAIlB,EAAE,CAACmB,GAAG,KAAK,WAAW,EAAE;IACvCtB,aAAa,CAACuB,IAAI,IAAAR,MAAA,CACbZ,EAAE,CAACa,MAAM,eAAAD,MAAA,CAAYZ,EAAE,CAACkB,MAAM,gBAAAN,MAAA,CAC/BZ,EAAE,CAACa,MAAM,kBACb,CAAC;IACDlB,UAAU,CAACyB,IAAI,CAACC,iBAAiB,CAACrB,EAAE,CAAC,CAAC;IACtC,IAAI,CAACA,EAAE,CAAC,aAAa,CAAC,EAAE;MACtBL,UAAU,CAACyB,IAAI,CAACE,iBAAiB,CAACtB,EAAE,CAAC,CAAC;IACxC;EACF;EAEA,IAAIA,EAAE,CAACuB,QAAQ,EAAE;IACfvB,EAAE,CAACuB,QAAQ,CAACxB,OAAO,CAAC,UAAAyB,GAAG,EAAI;MACzBvB,eAAe,CAACuB,GAAG,EAAEjC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,CAAC;IAC7F,CAAC,CAAC;EACJ;AACF;AAEA,SAASD,WAAWA,CAACT,IAAI,EAAE;EACzB,IAAMsC,IAAI,GAAG,EAAE;EAAE,IACfC,OAAO,GAAG;IACR3C,IAAI,EAAEF,UAAU,CAAC8C,QAAQ,GAAG;MAC1BC,UAAU,yCAAAhB,MAAA,CACI/B,UAAU,CAACgD,OAAO,0HAI/B;MACDC,SAAS,wCAAAlB,MAAA,CACK/B,UAAU,CAACgD,OAAO;IAElC,CAAC,GAAG,IAAI;IACR7C,MAAM,EAAE;MACN+C,MAAM,EAAE,cAAc;MACtBC,OAAO,sCAAApB,MAAA,CACO/B,UAAU,CAACgD,OAAO,+BAC/B;MACDI,KAAK,oEAEJ;MACDC,aAAa,4CAAAtB,MAAA,CACC/B,UAAU,CAACgD,OAAO;IAKlC;EACF,CAAC;EAEH,IAAMM,OAAO,GAAGT,OAAO,CAACvC,IAAI,CAAC;EAC7B,IAAIgD,OAAO,EAAE;IACXC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACpC,OAAO,CAAC,UAAAuC,GAAG,EAAI;MAClCb,IAAI,CAACL,IAAI,CAACe,OAAO,CAACG,GAAG,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ;EAEA,OAAOb,IAAI;AACb;AAEA,SAASpB,SAASA,CAACnB,IAAI,EAAEK,QAAQ,EAAE;EACjC,IAAIL,IAAI,CAAC2B,MAAM,KAAK0B,SAAS,EAAE;EAC/B,IAAIC,YAAY;EAChB,IAAI,OAAQtD,IAAI,CAACsD,YAAa,KAAK,QAAQ,IAAI,CAACtD,IAAI,CAACuD,QAAQ,EAAE;IAC7DD,YAAY,OAAA5B,MAAA,CAAO1B,IAAI,CAACsD,YAAY,MAAG;EACzC,CAAC,MAAM;IACLA,YAAY,MAAA5B,MAAA,CAAMxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAACsD,YAAY,CAAC,CAAE;EACvD;EACAjD,QAAQ,CAAC6B,IAAI,IAAAR,MAAA,CAAI1B,IAAI,CAAC2B,MAAM,QAAAD,MAAA,CAAK4B,YAAY,MAAG,CAAC;AACnD;AAEA,SAASlC,UAAUA,CAACpB,IAAI,EAAEM,QAAQ,EAAE;EAClC,IAAIN,IAAI,CAAC2B,MAAM,KAAK0B,SAAS,EAAE;EAC/B,IAAMG,KAAK,GAAG,EAAE;EAChB,IAAIC,eAAO,CAACzD,IAAI,CAACiC,GAAG,CAAC,EAAE;IACrB,IAAIjC,IAAI,CAAC0D,QAAQ,EAAE;MACjB,IAAMzD,IAAI,GAAG,IAAA0D,aAAO,EAAC3D,IAAI,CAACsD,YAAY,CAAC,GAAG,kBAAkB,GAAG,EAAE;MACjE,IAAIM,OAAO,GAAG,IAAAD,aAAO,EAAC3D,IAAI,CAACsD,YAAY,CAAC,gDAAA5B,MAAA,CAAa1B,IAAI,CAAC2B,MAAM,IAAK3B,IAAI,CAAC6D,WAAW;MACrF,IAAID,OAAO,KAAKP,SAAS,EAAEO,OAAO,MAAAlC,MAAA,CAAM1B,IAAI,CAAC8D,KAAK,6BAAM;MACxDN,KAAK,CAACtB,IAAI,sBAAAR,MAAA,CAAsBzB,IAAI,iBAAAyB,MAAA,CAAckC,OAAO,mBAAAlC,MAAA,CAAgB+B,eAAO,CAACzD,IAAI,CAACiC,GAAG,CAAC,QAAK,CAAC;IAClG;IACA,IAAIjC,IAAI,CAAC+D,OAAO,IAAI,IAAAJ,aAAO,EAAC3D,IAAI,CAAC+D,OAAO,CAAC,EAAE;MACzC/D,IAAI,CAAC+D,OAAO,CAAClD,OAAO,CAAC,UAAAmD,IAAI,EAAI;QAC3B,IAAIA,IAAI,CAACC,OAAO,EAAE;UAChBT,KAAK,CAACtB,IAAI,eAAAR,MAAA,CAAewC,IAAI,CAACF,IAAI,CAACC,OAAO,CAAC,kBAAAvC,MAAA,CAAesC,IAAI,CAACJ,OAAO,mBAAAlC,MAAA,CAAgB+B,eAAO,CAACzD,IAAI,CAACiC,GAAG,CAAC,QAAK,CAAC;QAC/G;MACF,CAAC,CAAC;IACJ;IACA3B,QAAQ,CAAC4B,IAAI,IAAAR,MAAA,CAAI1B,IAAI,CAAC2B,MAAM,SAAAD,MAAA,CAAM8B,KAAK,CAACtC,IAAI,CAAC,GAAG,CAAC,OAAI,CAAC;EACxD;AACF;AAEA,SAASK,YAAYA,CAACvB,IAAI,EAAEO,WAAW,EAAE;EACvC,IAAIP,IAAI,CAAC2B,MAAM,KAAK0B,SAAS,EAAE;EAC/B,IAAIrD,IAAI,CAACwB,QAAQ,KAAK,SAAS,EAAE;IAAExB,IAAI,CAACqB,OAAO,GAAG,EAAE;EAAC;EACrD,IAAM8C,GAAG,MAAAzC,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,eAAAD,MAAA,CAAYxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAACqB,OAAO,CAAC,MAAG;EACrEd,WAAW,CAAC2B,IAAI,CAACiC,GAAG,CAAC;AACvB;AAEA,SAASpC,UAAUA,CAAC/B,IAAI,EAAEQ,SAAS,EAAE;EACnC,IAAIR,IAAI,CAACwB,QAAQ,KAAK,SAAS,EAAE;IAC/BxB,IAAI,CAACoE,QAAQ,KAAK,OAAO,KAAKpE,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAACuC,KAAK,GAAGrE,IAAI,CAACoE,QAAQ,CAAC;IACrEpE,IAAI,CAACsE,QAAQ,KAAK,OAAO,KAAKtE,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAACgC,KAAK,GAAG9D,IAAI,CAACsE,QAAQ,CAAC;IACrEtE,IAAI,CAACuE,WAAW,KAAK,UAAU,KAAKvE,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAACO,QAAQ,GAAGrC,IAAI,CAACuE,WAAW,CAAC;EACnF;EACA,IAAMJ,GAAG,MAAAzC,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,aAAAD,MAAA,CAAUxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAAC,MAAG;EACvEtB,SAAS,CAAC0B,IAAI,CAACiC,GAAG,CAAC;AACrB;AAEA,SAAShC,iBAAiBA,CAACnC,IAAI,EAAE;EAC/B,IAAMwE,OAAO,GAAGjF,KAAK,CAACS,IAAI,CAACyE,QAAQ,CAAC;EAAE,IAAIC,aAAa,GAAG,EAAE;EAAE,IAAIC,UAAU,GAAG,EAAE;EAAE,IACjFC,UAAU,GAAG,EAAE;EACjB,IAAI5E,IAAI,CAAC6E,QAAQ,EAAE;IACjBH,aAAa,oCAAAhD,MAAA,CAAoC8C,OAAO,SAAA9C,MAAA,CAAM1B,IAAI,CAAC6E,QAAQ,+FAAAnD,MAAA,CAE3C1B,IAAI,CAAC6E,QAAQ,EAAAnD,MAAA,CAAG1B,IAAI,CAACyE,QAAQ,cAC3D;IACFG,UAAU,CAAC1C,IAAI,CAAC,aAAa,CAAC;EAChC;EACA,IAAIlC,IAAI,CAAC8E,MAAM,EAAE;IACfH,UAAU,iCAAAjD,MAAA,CAAiC1B,IAAI,CAAC8E,MAAM,iGAAApD,MAAA,CAEzB1B,IAAI,CAAC8E,MAAM,4CACtC;IACFF,UAAU,CAAC1C,IAAI,CAAC,UAAU,CAAC;EAC7B;EACA,IAAMiC,GAAG,MAAAzC,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,gCAAAD,MAAA,CACtBgD,aAAa,YAAAhD,MAAA,CACbiD,UAAU,mBAAAjD,MAAA,CACHkD,UAAU,CAAC1D,IAAI,CAAC,IAAI,CAAC,WAC7B;EACH,OAAO0D,UAAU,CAACtD,MAAM,GAAG6C,GAAG,GAAG,EAAE;AACrC;AAEA,SAAS/B,iBAAiBA,CAACpC,IAAI,EAAE;EAC/B,IAAMmE,GAAG,wCAAAzC,MAAA,CACO1B,IAAI,CAAC2B,MAAM,sBACxB;EACH,OAAOwC,GAAG;AACZ;AAEA,SAAStC,iBAAiBA,CAACkD,UAAU,EAAEtD,KAAK,EAAEhB,UAAU,EAAE;EACxD,IAAM0D,GAAG,MAAAzC,MAAA,CAAMqD,UAAU,mFAAArD,MAAA,CAEhBD,KAAK,WACX;EACHhB,UAAU,CAACyB,IAAI,CAACiC,GAAG,CAAC;AACtB;AAEA,SAASlD,WAAWA,CAACjB,IAAI,EAAEC,IAAI,EAAE+E,IAAI,EAAExB,KAAK,EAAEyB,aAAa,EAAEC,SAAS,EAAEpD,KAAK,EAAEmB,OAAO,EAAE;EACtF,IAAMkB,GAAG,MAAAzC,MAAA,CAAMyD,oBAAa,WAAAzD,MAAA,CAC1B9B,YAAY,CAACK,IAAI,CAAC,0EAAAyB,MAAA,CAKd1B,IAAI,CAACoF,SAAS,mBAAA1D,MAAA,CACZsD,IAAI,wBAAAtD,MAAA,CAEN1B,IAAI,CAACqF,SAAS,mBAAA3D,MAAA,CACZ8B,KAAK,wBAAA9B,MAAA,CAEPwD,SAAS,cAAAxD,MAAA,CACTuD,aAAa,cAAAvD,MAAA,CACbI,KAAK,0GAAAJ,MAAA,CAQPuB,OAAO,aAEX;EACA,OAAOkB,GAAG;AACZ", "ignoreList": []}]}