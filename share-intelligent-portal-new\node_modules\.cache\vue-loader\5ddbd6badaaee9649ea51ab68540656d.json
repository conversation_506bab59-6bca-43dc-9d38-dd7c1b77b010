{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\interested.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\interested.vue", "mtime": 1750311963023}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["interested.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "interested.vue", "sourceRoot": "src/views/supplyDemandDocking/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">我有意向</div>\r\n      <div style=\"height: 33px; margin-top: 21px\">I have intentions</div>\r\n    </div>\r\n    <div class=\"card-container card-content\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <div class=\"imgStyle\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../assets/device/ceshi.png\" alt=\"\" />\r\n        </div>\r\n        <div class=\"title\">{{ form.title || '' }}</div>\r\n        <div style=\"display: flex; align-items: center; margin-top: 15px\">\r\n          <div class=\"publishTimeStyle\">发布时间：{{ updateTime }}</div>\r\n          <!-- <div class=\"detailStyle\" @click=\"goDetail\">查看详情 >></div> -->\r\n        </div>\r\n      </div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <span class=\"resourceType\">资源类型：</span>\r\n          <span class=\"resourceValue\">{{ form.fieldName || '' }}</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <span class=\"resourceType\">资源名称：</span>\r\n          <span class=\"resourceValue\">{{ form.title || '' }}</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"意向描述\">\r\n              <el-input v-model=\"form.intentionContent\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\"\r\n                show-word-limit placeholder=\"\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人：\" prop=\"linkMan\">\r\n              <el-input disabled v-model=\"form.linkMan\" placeholder=\"请先维护联系人\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话：\" prop=\"linkTel\">\r\n              <el-input disabled v-model=\"form.linkTel\" placeholder=\"请先维护联系方式\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button style=\"width: 100%; height: 50px\" type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n        <div class=\"promptStyle\">温馨提示</div>\r\n        <div class=\"desc\" style=\"margin-top: 20px\">\r\n          1、我们会在最快的时间和您取得联系（工作时间周一至周五8:00-18:00）\r\n        </div>\r\n        <div class=\"desc\" style=\"margin-top: 13px\">\r\n          2、紧急问题请拨打：15512688882\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { submitIntention } from \"@/api/home\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        title: \"\",\r\n      },\r\n      updateTime: \"暂无\",\r\n      applicationAreaName: \"\",\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n    if (this.$route.query.demandName && this.$route.query.demandName != 'null') {\r\n      this.form.title = this.$route.query.demandName\r\n    }\r\n    if (this.$route.query.intentionType) {\r\n      this.form.intentionType = parseInt(this.$route.query.intentionType)\r\n    }\r\n    if (this.$route.query.fieldName) {\r\n      this.form.fieldName = this.$route.query.fieldName\r\n    }\r\n    if (this.$route.query.intentionId) {\r\n      this.form.intentionId = this.$route.query.intentionId\r\n    }\r\n    if (this.$route.query.updateTime && this.$route.query.updateTime != 'null') {\r\n      this.updateTime = this.$route.query.updateTime\r\n    }\r\n    if (this.$route.query.applicationAreaName && this.$route.query.applicationAreaName != 'null') {\r\n      this.applicationAreaName = this.$route.query.applicationAreaName\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit() {\r\n      submitIntention(this.form).then((res) => {\r\n        if (res.code == 200) {\r\n          this.$message.success(\"提交成功\")\r\n          this.cancel()\r\n        } else {\r\n          this.$message.error(res.msg)\r\n        }\r\n      })\r\n    },\r\n    goDetail() {\r\n      // this.$router.push(\"/productOrderDetail\");\r\n    },\r\n    // 判断是否关联企业\r\n    initData() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (userInfo && userInfo != 'null') {\r\n        this.form.linkMan = userInfo.memberRealName;\r\n        this.form.linkTel = userInfo.memberPhone;\r\n        this.form.companyName = userInfo.memberCompanyName;\r\n      }\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      }\r\n    },\r\n    cancel() {\r\n      this.$router.go(-1)\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: rgb(242, 242, 242);\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n}\r\n\r\n.card-content {\r\n  display: flex;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: -70px;\r\n  padding: 60px 59px 62px 60px;\r\n\r\n  .card_left {\r\n    .imgStyle {\r\n      width: 330px;\r\n      height: 230px;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #333333;\r\n      margin-top: 23px;\r\n    }\r\n\r\n    .publishTimeStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n    }\r\n\r\n    .detailStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #21c9b8;\r\n      margin-left: auto;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .card_right {\r\n    margin-left: 40px;\r\n    width: 100%;\r\n\r\n    .resourceType {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .resourceValue {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #666666;\r\n    }\r\n\r\n    .footer-submit {\r\n      margin-top: 40px;\r\n    }\r\n\r\n    .promptStyle {\r\n      margin-top: 30px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .desc {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}