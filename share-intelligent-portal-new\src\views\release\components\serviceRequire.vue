<template>
  <div>
    <el-form ref="form" :rules="rules" :model="form" label-position="top">
      <el-form-item label="需求标题" prop="title">
        <el-input
          v-model="form.title"
          maxlength="50"
          show-word-limit
          placeholder="请输入需求标题"
        ></el-input>
      </el-form-item>
      <el-form-item prop="type" label="需求类型">
        <el-radio-group v-model="form.type" placeholder="请选择" clearable>
          <el-radio
            v-for="dict in requireTypeList"
            :key="dict.value"
            :label="dict.value"
            :value="dict.value"
            >{{ dict.label }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="description">
        <div slot="label">
          需求描述
          <span style="color: #999999; font-size: 14px; margin-left: 11px"
            >(可按需求产品+应用行业+应用领域进行描述)</span
          >
        </div>
        <el-input
          v-model="form.description"
          type="textarea"
          resize="none"
          :rows="8"
          maxlength="500"
          show-word-limit
          placeholder="我企业是做什么的，哪个环节产生这个需求，现状及问题点、想达到的效果，需求的数量，改善的预算等。填写越详细，匹配越准确。"
        />
      </el-form-item>
      <el-form-item label="场景图片" prop="imageUrl">
        <ImageUpload v-model="imageUrlList" :limit="1" />
      </el-form-item>
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          disabled
          v-model="form.companyName"
          placeholder="请先绑定公司"
        ></el-input>
      </el-form-item>
      <!-- <el-form-item label="上传附件" prop="enclosure">
        <FileUpload v-model="form.enclosure" />
      </el-form-item> -->
      <el-form-item label="联系人" prop="contact">
        <el-input
          disabled
          v-model="form.contact"
          placeholder="请先维护联系人"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input
          disabled
          v-model="form.phone"
          placeholder="请先维护联系方式"
        ></el-input>
      </el-form-item>
      <el-form-item class="footer-submit">
        <el-button type="primary" @click="onSubmit">发布</el-button>
        <el-button style="margin-left: 140px" @click.once="onCancel"
          >取消</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { releaseService } from "@/api/release";
export default {
  data() {
    return {
      form: {
        title: "",
        type: "",
        description: "",
        imageUrl: "",
        companyName: "",
        contact: "",
        phone: "",
      },
      requireTypeList: [
        { label: "创新研发", value: "1" },
        { label: "数字化管理", value: "2" },
        { label: "智能制造", value: "3" },
        { label: "软件服务", value: "4" },
        { label: "金融服务", value: "5" },
        { label: "运营宣传", value: "6" },
        { label: "人力资源", value: "7" },
        { label: "其他", value: "8" },
      ],
      // 表单校验
      rules: {
        title: [
          { required: true, message: "需求标题不能为空", trigger: "blur" },
        ],
        // type: [
        //   { required: true, message: "请选择需求类型", trigger: "change" },
        // ],
        // description: [
        //   { required: true, message: "需求描述不能为空", trigger: "blur" },
        // ],
        // companyName: [
        //   { required: true, message: "公司名称不能为空", trigger: "blur" },
        // ],
        // contact: [
        //   { required: true, message: "联系人不能为空", trigger: "blur" },
        // ],
        // phone: [
        //   { required: true, message: "联系电话不能为空", trigger: "blur" },
        // ],
      },
      imageUrlList: [],
    };
  },
  created() {
    let userinfo = JSON.parse(window.sessionStorage.getItem("userinfo"));
    if(userinfo && userinfo != 'null') {
      this.form.companyName = userinfo.memberCompanyName;
      this.form.contact = userinfo.memberRealName;
      this.form.phone = userinfo.memberPhone;
    }
  },
  methods: {
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.imageUrl = this.imageUrlList.length>0 ? this.imageUrlList[0].url:'';
          releaseService(this.form).then((res) => {
            if (res.code == 200) {
              this.$message.success("发布成功");
              this.onCancel()
            } else {
              this.$message.error("发布失败");
            }
          });
        }
      });
    },
    onCancel() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
.footer-submit {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 60px;

  .el-button {
    width: 140px;
    height: 50px;
  }
}
</style>
