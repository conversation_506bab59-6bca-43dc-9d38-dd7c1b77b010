package com.ruoyi.auth.controller;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.auth.service.SSOService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * SSO单点登录控制器
 * 用于与SSO认证服务的集成
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sso")
public class SSOController {

    private static final Logger log = LoggerFactory.getLogger(SSOController.class);

    @Autowired
    private SSOService ssoService;

    @Autowired
    private TokenService tokenService;

    /**
     * SSO登录回调处理
     * SSO服务重定向到此接口进行登录处理
     *
     * @param code 授权码
     * @param state 状态参数（原始重定向URL）
     * @param error 错误信息
     */
    @GetMapping("/callback")
    public void ssoCallback(@RequestParam(required = false) String code,
                           @RequestParam(required = false) String state,
                           @RequestParam(required = false) String error,
                           HttpServletResponse response) {
        try {
            if (StringUtils.isNotEmpty(error)) {
                log.error("SSO登录失败: {}", error);
                response.sendRedirect("/login?error=" + error);
                return;
            }

            if (StringUtils.isEmpty(code)) {
                log.error("SSO回调缺少授权码");
                response.sendRedirect("/login?error=missing_code");
                return;
            }

            log.info("SSO登录回调，code: {}, state: {}", code, state);

            // 1. 使用授权码换取访问令牌
            Map<String, Object> tokenResult = ssoService.exchangeToken(code);
            if (tokenResult == null) {
                log.error("授权码换取令牌失败");
                response.sendRedirect("/login?error=token_exchange_failed");
                return;
            }

            String accessToken = (String) tokenResult.get("access_token");
            if (StringUtils.isEmpty(accessToken)) {
                log.error("获取访问令牌失败");
                response.sendRedirect("/login?error=invalid_token");
                return;
            }

            // 2. 获取用户信息
            Map<String, Object> ssoUserInfo = ssoService.getUserInfo(accessToken);
            if (ssoUserInfo == null) {
                log.error("获取用户信息失败");
                response.sendRedirect("/login?error=userinfo_failed");
                return;
            }

            // 3. 根据SSO用户信息获取或创建本地用户
            LoginUser loginUser = ssoService.getOrCreateLocalUser(ssoUserInfo);
            if (loginUser == null) {
                log.error("用户信息同步失败");
                response.sendRedirect("/login?error=user_sync_failed");
                return;
            }

            // 4. 生成本地系统的访问Token
            Map<String, Object> localTokenMap = tokenService.createToken(loginUser);

            // 5. 记录SSO登录日志
            ssoService.recordSSOLogin(loginUser, accessToken);

            // 6. 重定向到原始请求页面或首页
            String redirectUrl = StringUtils.isNotEmpty(state) ? state : "/index";

            // 将Token信息添加到重定向URL中（或者存储到Session中）
            if (redirectUrl.contains("?")) {
                redirectUrl += "&token=" + localTokenMap.get("access_token");
            } else {
                redirectUrl += "?token=" + localTokenMap.get("access_token");
            }

            response.sendRedirect(redirectUrl);

        } catch (Exception e) {
            log.error("SSO回调处理异常", e);
            try {
                response.sendRedirect("/login?error=callback_error");
            } catch (Exception ex) {
                log.error("重定向失败", ex);
            }
        }
    }

    /**
     * 获取SSO登录URL
     * 前端可调用此接口获取SSO登录地址
     */
    @GetMapping("/login-url")
    public R<?> getSSOLoginUrl(@RequestParam(value = "redirect", required = false) String redirectUrl) {
        try {
            String loginUrl = ssoService.getSSOLoginUrl(redirectUrl);

            Map<String, Object> result = new HashMap<>();
            result.put("loginUrl", loginUrl);

            return R.ok(result);

        } catch (Exception e) {
            log.error("获取SSO登录地址失败", e);
            return R.fail("获取SSO登录地址失败: " + e.getMessage());
        }
    }

    /**
     * SSO登出接口
     */
    @PostMapping("/logout")
    public R<?> ssoLogout(HttpServletRequest request) {
        try {
            // 获取当前用户Token
            String token = request.getHeader("Authorization");
            if (StringUtils.isEmpty(token)) {
                return R.fail("未登录");
            }

            // 获取用户信息
            LoginUser loginUser = tokenService.getLoginUser(token);
            if (loginUser == null) {
                return R.fail("用户信息无效");
            }

            log.info("用户 {} 请求SSO登出", loginUser.getUsername());

            // 这里需要获取SSO访问令牌，可能需要从Session或缓存中获取
            // 暂时跳过SSO服务登出，只清除本地会话

            // 1. 清除本地用户Token
            tokenService.delLoginUser(token);

            // 2. 可以在这里调用SSO服务登出
            // ssoService.logout(ssoAccessToken);

            return R.ok("登出成功");

        } catch (Exception e) {
            log.error("SSO登出失败", e);
            return R.fail("SSO登出失败: " + e.getMessage());
        }
    }

    /**
     * 检查SSO服务状态
     */
    @GetMapping("/status")
    public R<?> checkSSOStatus() {
        try {
            boolean serverStatus = ssoService.checkSSOServerStatus();

            Map<String, Object> result = new HashMap<>();
            result.put("ssoServerStatus", serverStatus);
            result.put("clientId", "market");
            result.put("timestamp", System.currentTimeMillis());

            return R.ok(result);

        } catch (Exception e) {
            log.error("检查SSO状态失败", e);
            return R.fail("检查SSO状态失败: " + e.getMessage());
        }
    }

    /**
     * 验证用户权限
     */
    @GetMapping("/check-permission")
    public R<?> checkPermission(@RequestParam String username,
                               @RequestParam String permission) {
        try {
            boolean hasPermission = ssoService.checkUserPermission(username, permission);

            Map<String, Object> result = new HashMap<>();
            result.put("username", username);
            result.put("permission", permission);
            result.put("hasPermission", hasPermission);

            return R.ok(result);

        } catch (Exception e) {
            log.error("检查用户权限失败", e);
            return R.fail("检查用户权限失败: " + e.getMessage());
        }
    }

    /**
     * 验证用户角色
     */
    @GetMapping("/check-role")
    public R<?> checkRole(@RequestParam String username,
                         @RequestParam String role) {
        try {
            boolean hasRole = ssoService.checkUserRole(username, role);

            Map<String, Object> result = new HashMap<>();
            result.put("username", username);
            result.put("role", role);
            result.put("hasRole", hasRole);

            return R.ok(result);

        } catch (Exception e) {
            log.error("检查用户角色失败", e);
            return R.fail("检查用户角色失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/userinfo")
    public R<?> getCurrentUserInfo(HttpServletRequest request) {
        try {
            // 获取当前用户Token
            String token = request.getHeader("Authorization");
            if (StringUtils.isEmpty(token)) {
                return R.fail("未登录");
            }

            // 获取用户信息
            LoginUser loginUser = tokenService.getLoginUser(token);
            if (loginUser == null) {
                return R.fail("用户信息无效");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("userId", loginUser.getUserId());
            result.put("username", loginUser.getUsername());
            result.put("user", loginUser.getUser());

            return R.ok(result);

        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return R.fail("获取用户信息失败: " + e.getMessage());
        }
    }
}
