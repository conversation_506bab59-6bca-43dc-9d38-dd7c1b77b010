import request from '@/utils/request'

// 查询车间信息列表
export function listWorkInfo(query) {
  return request({
    url: '/system/workInfo/list',
    method: 'get',
    params: query
  })
}

// 查询车间信息详细
export function getWorkInfo(id) {
  return request({
    url: '/system/workInfo/' + id,
    method: 'get'
  })
}

// 新增车间信息
export function addWorkInfo(data) {
  return request({
    url: '/system/workInfo',
    method: 'post',
    data: data
  })
}

// 修改车间信息
export function updateWorkInfo(data) {
  return request({
    url: '/system/workInfo',
    method: 'put',
    data: data
  })
}

// 删除车间信息
export function delWorkInfo(id) {
  return request({
    url: '/system/workInfo/' + id,
    method: 'delete'
  })
}
