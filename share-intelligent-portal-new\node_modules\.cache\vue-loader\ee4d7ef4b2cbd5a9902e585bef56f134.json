{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\AppMain.vue?vue&type=style&index=0&id=078753dd&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\AppMain.vue", "mtime": 1750311962841}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1tYWluIHsNCiAgLyogNTA9IG5hdmJhciAgNTAgICovDQogIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA4MHB4KTsNCiAgd2lkdGg6IDEwMCU7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgb3ZlcmZsb3cteDogaGlkZGVuOw0KfQ0KDQouZml4ZWQtaGVhZGVyICsgLmFwcC1tYWluIHsNCiAgcGFkZGluZy10b3A6IDgwcHg7DQp9DQoNCi5oYXNUYWdzVmlldyB7DQogIC5hcHAtbWFpbiB7DQogICAgLyogODQgPSBuYXZiYXIgKyB0YWdzLXZpZXcgPSA1MCArIDM0ICovDQogICAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDg0cHgpOw0KICB9DQoNCiAgLmZpeGVkLWhlYWRlciArIC5hcHAtbWFpbiB7DQogICAgcGFkZGluZy10b3A6IDg0cHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["AppMain.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "AppMain.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\r\n  <section class=\"app-main el-scrollbar\">\r\n    <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <keep-alive :include=\"cachedViews\">\r\n        <router-view v-if=\"!$route.meta.link\" :key=\"key\" />\r\n      </keep-alive>\r\n    </transition>\r\n    <iframe-toggle />\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport iframeToggle from \"./IframeToggle/index\"\r\n\r\nexport default {\r\n  name: 'AppMain',\r\n  components: { iframeToggle },\r\n  computed: {\r\n    cachedViews() {\r\n      return this.$store.state.tagsView.cachedViews\r\n    },\r\n    key() {\r\n      return this.$route.path\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-main {\r\n  /* 50= navbar  50  */\r\n  min-height: calc(100vh - 80px);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow-x: hidden;\r\n}\r\n\r\n.fixed-header + .app-main {\r\n  padding-top: 80px;\r\n}\r\n\r\n.hasTagsView {\r\n  .app-main {\r\n    /* 84 = navbar + tags-view = 50 + 34 */\r\n    min-height: calc(100vh - 84px);\r\n  }\r\n\r\n  .fixed-header + .app-main {\r\n    padding-top: 84px;\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n// fix css style bug in open el-dialog\r\n.el-popup-parent--hidden {\r\n  .fixed-header {\r\n    padding-right: 17px;\r\n  }\r\n}\r\n</style>\r\n"]}]}