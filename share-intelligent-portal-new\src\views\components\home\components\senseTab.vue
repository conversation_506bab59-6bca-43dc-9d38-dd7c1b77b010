<template>
  <div v-loading="loading" class="tab-main">
    <template v-if="items.length > 0">
      <div v-for="item in items" :key="item.id">
        <router-link
          class="card"
          target="_blank"
          :to="`/scenarioDetail?id=${item.id}`"
        >
          <div class="list-item-content">
            <div class="list-item-info">
              <div class="list-item-title">
                {{ item.title }}
              </div>
              <div class="list-item-text">{{ item.content }}</div>
              <div class="list-item-time">{{ item.updateTime }}</div>
            </div>
          </div>
        </router-link>
      </div>
    </template>
    <template v-else>
      <el-empty />
    </template>
    <div class="tab-page-end">
      <!-- <span class="demonstration">完整功能</span> -->
      <el-pagination
        class="company-tab-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNum"
        :page-sizes="[100, 200, 300, 400]"
        :page-size="pageSize"
        layout=" prev, pager, next "
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { head, map } from "ramda";
import { getListByText } from "@/api/scene";

export default {
  name: "SenseTab",
  data() {
    return {
      loading: false,
      items: [],
      pageNum: 1,
      pageSize: 3,
      total: 0,
    };
  },
  created() {
    this.getSenseData();
  },
  methods: {
    getSenseData() {
      this.loading = true;
      getListByText({ pageNum: this.pageNum, pageSize: this.pageSize })
        .then((res) => {
          const { code, rows = [], total } = res;
          if (code === 200) {
            this.items = map(
              ({
                id,
                title,
                simpleContent,
                updateTime,
                coverPictureList = [],
              }) => {
                const image = head(coverPictureList || []) || {};
                return {
                  id,
                  title,
                  content: simpleContent,
                  updateTime,
                  src: image.url,
                };
              },
              rows
            );
            this.to;
            this.total = total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.getSenseData();
    },
    handleCurrentChange(newPage) {
      this.pageNum = newPage;
      this.getSenseData();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";

.tab-main {
  position: relative;
  width: 100%;

  .el-row--flex {
    flex-wrap: wrap;
  }

  ::v-deep .el-scrollbar__wrap {
    overflow-x: hidden;
    overflow-y: auto;
  }

  .card {
    display: flex;
    flex-direction: column;
    width: 100%;
    flex-shrink: 0;
    min-height: 100px;
    background: #ffffff;
    box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.1);
    margin-bottom: 24px;
    .list-item-content {
      display: flex;
      padding: 24px 32px;
      cursor: pointer;

      .list-item-info {
        padding-left: 24px;
        .list-item-title {
          width: 806px;
          height: 24px;
          text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
          white-space: nowrap; /*让文字不换行*/
          overflow: hidden; /*超出要隐藏*/
          font-size: 20px;
          font-weight: 500;
          color: #323233;
          line-height: 24px;
          margin: 8px 0 14px;
        }
        .list-item-text {
          width: 806px;
          height: 60px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          font-size: 14px;
          color: #666;
          line-height: 30px;
        }
        .list-item-time {
          color: #999;
          line-height: 14px;
          margin-top: 22px;
        }
      }
      &:hover {
        .list-item-title {
          color: #21c9b8;
        }
      }
    }
    &-footer {
      padding: 16px 24px;

      .title {
        @include multiEllipsis(2);
        font-size: 18px;
        font-weight: 500;
        color: #333333;
        line-height: 26px;
        margin-bottom: 12px;
      }
    }
  }
}
</style>
<style lang="scss">
.tab-main {
  .tab-page-end {
    .company-tab-pagination {
      width: 280px;
      margin-left: calc(45% - 200px);
      // margin: 0 auto;
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #ffffff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        margin: 0 6px;
        color: #333;
      }
      .el-pager {
        .number {
          width: 32px;
          height: 32px;
          border: 1px solid #d9d9d9;
          background: #ffffff;
          border-radius: 4px;
          line-height: 32px;
          margin: 0 6px;
          &.active {
            background: #21c9b8;
            border: 1px solid #21c9b8;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
