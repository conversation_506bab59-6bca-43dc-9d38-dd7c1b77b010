/*
 * @Author: zhc
 * @Date: 2023-02-13 16:07:37
 * @LastEditTime: 2023-02-13 16:14:03
 * @Description:
 * @LastEditors: zhc
 */
/*
 * @Author: zhc
 * @Date: 2023-02-11 14:45:07
 * @LastEditTime: 2023-02-12 11:18:58
 * @Description:
 * @LastEditors: zhc
 */

import request from "@/utils/request";

// 需求列表查询
export function getApproveDetail(params) {
  return request({
    url: "/system/authentication/info/detail",
    method: "get",
    params: params,
  });
}

// 认证(名片认证)
export function personalApprove(params) {
  return request({
    url: "/system/authentication/info/authentication_personal",
    method: "post",
    data: params,
  });
}
// 认证(企业认证)
export function companyApprove(params) {
  return request({
    url: "/system/authentication/info/authentication_company",
    method: "post",
    data: params,
  });
}
