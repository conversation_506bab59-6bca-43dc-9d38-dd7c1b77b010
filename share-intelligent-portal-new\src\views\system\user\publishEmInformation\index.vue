<template>
  <div class="app-container">
    <el-row
      :gutter="20"
      style="background: linear-gradient(to right, #e1f7f0, #f4fcfa)"
    >
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
        <div class="formStyle">
          <el-form ref="form" :rules="rules" :model="form" label-position="top">
            <el-form-item label="岗位名称" prop="positionName">
              <el-input
                v-model="form.positionName"
                placeholder="请输入岗位名称"
              />
            </el-form-item>
            <el-form-item label="薪资范围" prop="salaryRange">
              <el-select
                v-model="form.salaryRange"
                placeholder="请选择薪资范围"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in salaryRangeList"
                  :key="dict.dictLabel"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="最低薪资" prop="salaryMin">
              <el-input v-model="form.salaryMin" placeholder="请输入最低薪资" />
            </el-form-item>
            <el-form-item label="最高薪资" prop="salaryMax">
              <el-input v-model="form.salaryMax" placeholder="请输入最高薪资" />
            </el-form-item>
            <el-form-item label="年龄限制" prop="ageLimit">
              <el-input v-model="form.ageLimit" placeholder="请输入年龄限制" />
            </el-form-item>
            <el-form-item label="用工单位" prop="company">
              <el-input v-model="form.company" placeholder="请输入用工单位" />
            </el-form-item>
            <el-form-item label="用工地点" prop="location">
              <el-select
                v-model="form.location"
                placeholder="请选择薪资范围"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in locationList"
                  :key="dict.dictLabel"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="联系方式" prop="contactPhone">
              <el-input
                v-model="form.contactPhone"
                placeholder="请输入联系方式"
              />
            </el-form-item>
            <el-form-item label="岗位要求" prop="requirements">
              <el-input
                v-model="form.requirements"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
            <el-form-item label="岗位职责" prop="responsibilities">
              <el-input
                v-model="form.responsibilities"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
            <el-form-item label="其他限制" prop="otherLimits">
              <el-input
                v-model="form.otherLimits"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
            <el-form-item class="footer-submit">
              <el-button type="primary" @click="onSubmit">发布</el-button>
              <el-button style="margin-left: 140px" @click.once="onCancel"
                >取消</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import UserMenu from "../components/userMenu.vue";
import { listData } from "@/api/system/dict/data";
import { employAddData } from "@/api/serviceSharing";
export default {
  name: "User",
  components: { UserMenu },
  data() {
    return {
      form: {
        positionName: "",
        requirements: "",
        salaryRange: "",
        responsibilities: "",
        desc: "",
        supplyName: "",
        technologyType: [],
        applicationArea: [],
        productPhoto: [],
        cooperationMode: "",
        productStage: "",
        enclosure: [],
        companyName: "",
        contactsName: "",
        contactsMobile: "",
      },
      // 表单校验
      rules: {
        positionName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        salaryRange: [
          {
            required: true,
            message: "薪资范围不能为空",
            trigger: "blur",
          },
        ],
        salaryMin: [
          { required: true, message: "最低薪资不能为空", trigger: "blur" },
        ],
        salaryMax: [
          { required: true, message: "最高薪资不能为空", trigger: "blur" },
        ],
        company: [
          { required: true, message: "用工单位不能为空", trigger: "blur" },
        ],
        location: [
          { required: true, message: "用工地点不能为空", trigger: "blur" },
        ],
        contactPhone: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
        ],
      },
      queryParams: {
        categoryId: undefined,
        status: undefined,
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      labelStyle: {
        fontWeight: 400,
        fontSize: "14px",
        color: "#999999",
        width: "60px",
        justifyContent: "flex-end",
      },
      contentStyle: {
        fontWeight: 400,
        fontSize: "14px",
        color: "#333333",
      },
      salaryRangeList: [], // 薪资范围
      locationList: [], // 用工地点
    };
  },
  created() {
    this.getSalaryRange();
    this.getLocation();
  },
  methods: {
    // 薪资范围字典
    getSalaryRange() {
      let params = { dictType: "salary_range" };
      listData(params).then((response) => {
        this.salaryRangeList = response.rows;
      });
    },
    // 用工地点字典
    getLocation() {
      let params = { dictType: "location" };
      listData(params).then((response) => {
        this.locationList = response.rows;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.queryParams = {
        categoryId: undefined,
        status: undefined,
        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    onSubmit() {
      let userinfo = JSON.parse(window.sessionStorage.getItem("userinfo"));
      this.form.createBy = userinfo.memberPhone;
      console.log(this.form)
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // this.form.materials = this.jobList;
          employAddData(this.form).then((res) => {
            if (res.code === 200) {
              this.$message.success("发布成功");
              this.onCancel();
            }
          });
        }
      });
    },
    onCancel() {
      this.$router.push("/user/emInformation");
    }
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));
  height: 100vh;
}

.formStyle {
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  .footer-submit {
    text-align: center;
  }
}
</style>
