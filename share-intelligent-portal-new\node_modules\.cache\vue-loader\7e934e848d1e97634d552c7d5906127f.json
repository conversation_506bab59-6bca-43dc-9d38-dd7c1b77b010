{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\resource\\labDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\resource\\labDetail.vue", "mtime": 1750311963002}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["labDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+EA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "labDetail.vue", "sourceRoot": "src/views/resource", "sourcesContent": ["<template>\r\n  <div class=\"resource-hall-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"resource-hall-detail-banner\">\r\n      <img\r\n        src=\"../../assets/resourceHall/resourceHallDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"resource-hall-detail-title-box\">\r\n      <div class=\"resource-hall-detail-divider\"></div>\r\n      <div class=\"resource-hall-detail-title\">实验室详情</div>\r\n      <div class=\"resource-hall-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"resource-hall-detail-content\">\r\n      <div class=\"resource-hall-detail-box\">\r\n        <div class=\"resource-hall-detail-box-title\">\r\n          {{ data.name }}\r\n        </div>\r\n        <div class=\"resource-hall-detail-headline\">\r\n          <div class=\"headline-content\">\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">所属单位：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.institution }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">行业领域：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.industry }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">所属区域：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.province }}{{ data.city ? \" \" + data.city : \"\"\r\n                }}{{ data.district ? \" \" + data.district : \"\" }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-btn\">\r\n              <el-button\r\n                v-if=\"showBtn\"\r\n                class=\"headline-btn-style intention-btn\"\r\n                @click=\"goIntention\"\r\n                >我有意向</el-button\r\n              >\r\n              <el-button\r\n                @click=\"goChat\"\r\n                class=\"headline-btn-style communication-btn\"\r\n                icon=\"el-icon-chat-dot-round\"\r\n                >在线沟通</el-button\r\n              >\r\n            </div>\r\n          </div>\r\n          <div class=\"headline-img\">\r\n            <img v-if=\"data.picUrl\" :src=\"data.picUrl\" alt=\"\" />\r\n            <img\r\n              v-else\r\n              src=\"../../assets/resourceHall/resourceHallDetailBanner.png\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"resource-hall-detail-description\">\r\n          <div class=\"description-title-box\">\r\n            <div class=\"description-divider\"></div>\r\n            <div class=\"description-title\">成果描述</div>\r\n          </div>\r\n          <div class=\"description-content\">\r\n            <div v-html=\"data.detail\" class=\"description-text ql-editor\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { laboratoryDetail, getCheckSubmit } from \"@/api/purchaseSales\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { getCompanyInfoByLoginInfo } from \"@/api/apathy\";\r\nimport { mapGetters } from \"vuex\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n      showBtn: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      laboratoryDetail(this.$route.query.id)\r\n        .then((res) => {\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          if (!this.token) {\r\n            this.showBtn = true;\r\n          } else {\r\n            this.getInfo();\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 判断此资源是不是自己发布的\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (this.data.createById === res.user.userId) {\r\n          this.showBtn = false;\r\n        } else {\r\n          this.showBtn = true;\r\n        }\r\n      });\r\n    },\r\n    goChat() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      let routeData = this.$router.resolve({\r\n        path: \"/user/im\",\r\n        query: {\r\n          userId: this.data.createImById,\r\n        },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳到我有意向页面\r\n    goIntention() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      // 是否加入企业\r\n      getCompanyInfoByLoginInfo()\r\n        .then((res) => {\r\n          if (res.data) {\r\n            // 是否对此资源提交过意向\r\n            getCheckSubmit({\r\n              id: this.$route.query.id,\r\n              resourceType: \"resource_supply\",\r\n            })\r\n              .then((res) => {\r\n                this.loading = false;\r\n                // true 提交过  false未提交过\r\n                if (res.data) {\r\n                  this.$message({\r\n                    type: \"warning\",\r\n                    message: \"已经提交过了哦！\",\r\n                  });\r\n                } else {\r\n                  let routeData = this.$router.resolve({\r\n                    path: \"/addIntention\",\r\n                    query: {\r\n                      id: this.$route.query.id,\r\n                      type: \"resource_supply\",\r\n                      title: this.data.supplyName,\r\n                    },\r\n                  });\r\n                  window.open(routeData.href, \"_blank\");\r\n                }\r\n              })\r\n              .catch(() => {\r\n                this.loading = false;\r\n              });\r\n          } else {\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"warning\",\r\n              message: \"必须加入企业才可提交我有意向\",\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.resource-hall-detail-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .resource-hall-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .resource-hall-detail-title-box {\r\n    width: 380px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .resource-hall-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .resource-hall-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .resource-hall-detail-content {\r\n    background: #f4f5f9;\r\n    padding-bottom: 70px;\r\n    .resource-hall-detail-box {\r\n      width: 1200px;\r\n      background: #fff;\r\n      margin: 0 auto;\r\n      padding: 60px 60px 192px;\r\n      .resource-hall-detail-box-title {\r\n        width: 100%;\r\n        font-size: 32px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        word-wrap: break-word;\r\n      }\r\n      .resource-hall-detail-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-top: 40px;\r\n        padding-bottom: 40px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        .headline-content {\r\n          flex: 1;\r\n          .headline-content-item {\r\n            display: flex;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            line-height: 32px;\r\n            .item-title {\r\n              width: 80px;\r\n              color: #666;\r\n            }\r\n            .item-content {\r\n              flex: 1;\r\n              max-width: 560px;\r\n              color: #333;\r\n              word-wrap: break-word;\r\n            }\r\n          }\r\n          .headline-content-btn {\r\n            padding-top: 112px;\r\n            .headline-btn-style {\r\n              width: 100px;\r\n              height: 32px;\r\n              border-radius: 4px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              padding: 8px 11px;\r\n            }\r\n            .intention-btn {\r\n              background: #21c9b8;\r\n              color: #fff;\r\n            }\r\n            .communication-btn {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        .headline-img {\r\n          width: 400px;\r\n          height: 240px;\r\n          margin-left: 20px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .resource-hall-detail-description {\r\n      padding-top: 39px;\r\n      .description-title-box {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-bottom: 40px;\r\n        .description-divider {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .description-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n      .description-content {\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.resource-hall-detail-container {\r\n  .description-content {\r\n    .description-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}