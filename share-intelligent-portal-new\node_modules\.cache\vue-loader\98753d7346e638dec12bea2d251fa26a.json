{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\emInformation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\emInformation\\index.vue", "mtime": 1750311963056}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/emInformation", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"top\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">用工信息</div>\r\n            <div class=\"buttonStyle\" @click=\"toPublish\">发布用工信息</div>\r\n          </div>\r\n        </div>\r\n        <el-form class=\"queryForm\" :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\">\r\n          <el-form-item label=\"岗位名称\" prop=\"positionName\">\r\n            <el-input v-model=\"queryParams.positionName\" placeholder=\"请输入岗位名称\" clearable\r\n              @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div class=\"cards\">\r\n          <div class=\"card\" v-for=\"item in employmentList\" :key=\"item.id\" @click=\"goDetail(item.id)\">\r\n            <el-descriptions :column=\"1\" :labelStyle=\"labelStyle\" :contentStyle=\"contentStyle\">\r\n              <el-descriptions-item label=\"岗位名称\">{{\r\n                item.positionName\r\n                }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"岗位要求\">\r\n                {{ item.requirements }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"时间\">{{\r\n                item.createTime\r\n                }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"状态\">{{\r\n                item.status == \"0\" ? \"正常\" : \"停用\"\r\n                }}</el-descriptions-item>\r\n            </el-descriptions>\r\n            <!-- <div class=\"btn\">\r\n              <el-button type=\"primary\" plain size=\"mini\">修改</el-button>\r\n              <el-button type=\"primary\" size=\"mini\">详情</el-button>\r\n            </div> -->\r\n          </div>\r\n          <!-- 分页 -->\r\n          <div class=\"pageStyle\">\r\n            <el-pagination v-if=\"employmentList && employmentList.length > 0\" background layout=\"prev, pager, next\"\r\n              class=\"activity-pagination\" :page-size=\"pageSize\" :current-page=\"pageNum\" :total=\"total\"\r\n              @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n        <el-empty v-if=\"employmentList.length == 0\"></el-empty>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { employUserListData } from \"@/api/serviceSharing\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      pageNum: 1,\r\n      pageSize: 8,\r\n      total: 0,\r\n      employmentList: [],\r\n      queryParams: {\r\n        positionName: \"\",\r\n      },\r\n      labelStyle: {\r\n        fontWeight: 400,\r\n        fontSize: \"14px\",\r\n        color: \"#999999\",\r\n        width: \"60px\",\r\n        justifyContent: \"flex-end\",\r\n      },\r\n      contentStyle: {\r\n        fontWeight: 400,\r\n        fontSize: \"14px\",\r\n        color: \"#333333\",\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    // 列表接口\r\n    getList() {\r\n      this.loading = true;\r\n      let userinfo = JSON.parse(window.sessionStorage.getItem(\"userinfo\"));\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        positionName: this.queryParams.positionName,\r\n        createBy: userinfo.memberPhone\r\n      };\r\n      employUserListData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.employmentList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams = {\r\n        categoryId: undefined,\r\n        status: undefined,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      };\r\n      this.getList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    toPublish() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(\"/publishEmInformation\");\r\n      }\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push(\"/employmentInfoDetail?id=\" + id);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 1000px;\r\n}\r\n\r\n.top {\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  // margin-top: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n    width: 100%;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n\r\n    .buttonStyle {\r\n      padding: 10px 20px;\r\n      background: #21c9b8;\r\n      color: #fff;\r\n      text-align: center;\r\n      cursor: pointer;\r\n      border-radius: 10px;\r\n      margin-left: auto;\r\n    }\r\n  }\r\n}\r\n\r\n.queryForm {\r\n  padding: 20px;\r\n}\r\n\r\n.cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n\r\n  .card {\r\n    width: 24%;\r\n    // height: 200px;\r\n    background: #fff;\r\n    padding: 20px;\r\n    margin-right: 10px;\r\n    margin-bottom: 10px;\r\n    border-radius: 10px;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n    cursor: pointer;\r\n\r\n    .btn {\r\n      position: absolute;\r\n      right: 20px;\r\n      bottom: 20px;\r\n    }\r\n  }\r\n\r\n  .pageStyle {\r\n    width: 100%;\r\n    margin-top: 61px;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}