package com.ruoyi.im.api.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName(value = "im_user")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImUser extends Model<ImUser> {

        @TableId(value = "id", type = IdType.AUTO)
        private Long id;//自增

        private String userId;//融云ID

        private String portraitUri;//头像

        private String customer;//客服模块

        private String name;//昵称

        private String token;//融云Token

        private Integer online;//在线数量

        private String os;//最新在线系统

        private Long time;//最新时间

        private String clientIp;//客户端IP

        private Date create_time;//创建时间

        private Date update_time;//更新时间

        private String openid;

        @TableField(exist = false)
        private String roomId;
}
