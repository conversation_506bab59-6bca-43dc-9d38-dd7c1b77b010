package com.ruoyi.portalweb.api.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 解决方案行业痛点对象 solution_pain
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public class SolutionPain extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    public SolutionPain(Long solutionId){
        setSolutionId(solutionId);
    }

    public SolutionPain() {}

    /** 解决方案行业痛点ID */
    private Long solutionPainId;

    /** 方案ID */
    @Excel(name = "方案ID")
    private Long solutionId;

    /** 名称 */
    @Excel(name = "名称")
    private String solutionPainName;

    /** 内容 */
    @Excel(name = "内容")
    private String solutionPainContent;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setSolutionPainId(Long solutionPainId) 
    {
        this.solutionPainId = solutionPainId;
    }

    public Long getSolutionPainId() 
    {
        return solutionPainId;
    }
    public void setSolutionId(Long solutionId) 
    {
        this.solutionId = solutionId;
    }

    public Long getSolutionId() 
    {
        return solutionId;
    }
    public void setSolutionPainName(String solutionPainName) 
    {
        this.solutionPainName = solutionPainName;
    }

    public String getSolutionPainName() 
    {
        return solutionPainName;
    }
    public void setSolutionPainContent(String solutionPainContent) 
    {
        this.solutionPainContent = solutionPainContent;
    }

    public String getSolutionPainContent() 
    {
        return solutionPainContent;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("solutionPainId", getSolutionPainId())
            .append("solutionId", getSolutionId())
            .append("solutionPainName", getSolutionPainName())
            .append("solutionPainContent", getSolutionPainContent())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
