{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyApply\\detail\\index1.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyApply\\detail\\index1.vue", "mtime": 1750311963046}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index1.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2EA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index1.vue", "sourceRoot": "src/views/system/user/companyApply/detail", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-11 15:20:15\r\n * @LastEditTime: 2023-02-28 08:48:59\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-11 15:18:41\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"company-demand-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">匹配资源列表</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                资源列表\r\n              </div>\r\n            </div>\r\n            <el-table :data=\"tableData\" style=\"width: 100%\">\r\n              <el-table-column prop=\"companyName\" label=\"公司名称\" width=\"240\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"applicationArea\"\r\n                label=\"应用领域\"\r\n                width=\"180\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  -webkit-line-clamp: 2;\r\n                  line-clamp: 2;\r\n                \"\r\n                label=\"描述\"\r\n              >\r\n                <template slot-scope=\"scoped\">\r\n                  <div v-html=\"scoped.row.summary\"></div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"danger\"\r\n                    @click=\"handleDelete(scope.row)\"\r\n                    >查看详情</el-button\r\n                  >\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport { getDemandDetail, createDemand, editDemand } from \"@/api/system/demand\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport store from \"@/store\";\r\nimport { keywordList2 } from \"@/api/zhm\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\r\n    \"affiliated_unit\",\r\n    \"capital_source\",\r\n    \"affiliated_street\",\r\n    \"display_restrictions\",\r\n  ],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      title: \"需求详情\",\r\n      imageUrl: \"\",\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      isCreate: false,\r\n      imgVisible: false,\r\n      user: {\r\n        tel: store.getters.tel,\r\n        name: store.getters.name,\r\n        companyName: store.getters.companyName,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        phonenumber: store.getters.phonenumber,\r\n      },\r\n      keywords: [],\r\n      applicationsInput: \"\",\r\n      info: {},\r\n      // 展示限制\r\n      displayRestrictions: undefined,\r\n      form: {},\r\n      accountLicenceList: [],\r\n      list: {},\r\n      tableData: [],\r\n      // 表单校验\r\n      rules: {\r\n        demandTitle: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" },\r\n        ],\r\n        displayRestrictions: [\r\n          { required: true, message: \"请选择展示性质\", trigger: \"blur\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"需求描述不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.handleKeywordList();\r\n  },\r\n  methods: {\r\n    initForm() {\r\n      this.form = {\r\n        applicationArea: [],\r\n        scenePicture: [],\r\n        applicationAreaList: [],\r\n        scenePictureList: [],\r\n        keywords: [],\r\n        auditStatus: \"1\",\r\n        displayStatus: \"2\",\r\n        publisherName: this.user.name,\r\n        publisherMobile: this.user.tel,\r\n      };\r\n    },\r\n    handleDelete(data) {\r\n      this.$router.push(\"/resourceHallDetail?id=\" + data.id);\r\n    },\r\n    getDetail() {\r\n      let id = this.$route.query.id;\r\n      getDemandDetail(id).then((response) => {\r\n        let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n        let decrypt = CryptoJS.AES.decrypt(response, key, {\r\n          mode: CryptoJS.mode.ECB,\r\n          padding: CryptoJS.pad.Pkcs7,\r\n        });\r\n        response = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    getUrl(str) {\r\n      if (str && str != null) {\r\n        var list = JSON.parse(str);\r\n        if (list && list.length > 0) {\r\n          return list[0].url;\r\n        }\r\n      }\r\n\r\n      return null;\r\n    },\r\n    // 应用领域新增\r\n    handleInputConfirm() {\r\n      let val = this.applicationsInput;\r\n      if (val) {\r\n        this.form.applicationAreaList.push(val);\r\n      }\r\n      this.applicationsInput = \"\";\r\n    },\r\n    // 应用领域移除\r\n    handleClose(tag) {\r\n      this.form.applicationAreaList.splice(\r\n        this.form.applicationAreaList.indexOf(tag),\r\n        1\r\n      );\r\n    },\r\n    handleSummaryClose(tag) {\r\n      this.form.keywords.splice(this.form.keywords.indexOf(tag), 1);\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePictureCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.form.scenePictureList = fileList;\r\n    },\r\n    handleSuccess(response, file) {\r\n      if (response.code == 200) {\r\n        if (this.form.scenePictureList == null) {\r\n          this.form.scenePictureList = [];\r\n        }\r\n        this.form.scenePictureList.push(response.data);\r\n      }\r\n    },\r\n    changeMode() {\r\n      if (this.isCreate) {\r\n        this.goBack();\r\n        return;\r\n      }\r\n      if (this.isDetail) {\r\n        this.title = \"编辑需求\";\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n        if (this.info.applicationArea) {\r\n          this.form.applicationAreaList = this.info.applicationArea.split(\",\");\r\n        } else {\r\n          this.form.applicationAreaList = [];\r\n        }\r\n        if (this.info.keywords) {\r\n          this.form.keywords = this.info.keywords.split(\",\");\r\n        }\r\n\r\n        if (this.info.scenePicture && this.info.scenePicture != \"null\") {\r\n          this.form.scenePictureList = JSON.parse(this.info.scenePicture);\r\n        } else {\r\n          this.form.scenePictureList = [];\r\n        }\r\n      } else {\r\n        this.isDetail = true;\r\n        this.title = \"需求详情\";\r\n        this.initForm();\r\n        this.getDetail();\r\n      }\r\n    },\r\n    goCreate() {\r\n      this.title = \"新增需求\";\r\n      this.isDetail = false;\r\n      this.initForm();\r\n      this.form.companyName = this.user.companyName;\r\n      this.form.contactsName = this.user.name;\r\n      this.form.contactsMobile = this.user.phonenumber;\r\n      this.form.publisherName = this.user.name;\r\n      this.form.publisherMobile = this.user.phonenumber;\r\n      this.form.businessNo = this.user.bussinessNo;\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    displayRestrictionChanged(res) {\r\n      this.dict.type.display_restrictions.forEach((item) => {\r\n        if (item.label == res) {\r\n          this.form.displayRestrictions = item.value;\r\n        }\r\n      });\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (\r\n            this.form.applicationArea &&\r\n            this.form.applicationArea.length > 0\r\n          ) {\r\n            this.form.applicationArea = this.form.applicationAreaList.join(\",\");\r\n          } else {\r\n            this.form.applicationArea = \"\";\r\n          }\r\n          this.form.scenePicture = JSON.stringify(this.form.scenePictureList);\r\n          this.form.businessNo = this.user.bussinessNo;\r\n          if (this.form.keywords && this.form.keywords.length > 0) {\r\n            this.form.keywords = this.form.keywords.join(\",\");\r\n          } else {\r\n            this.form.keywords = \"\";\r\n          }\r\n          if (this.isCreate) {\r\n            createDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          } else {\r\n            this.form.auditStatus = 1;\r\n            editDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      console.log(this.$route.query.key);\r\n      let info = JSON.parse(this.$route.query.key);\r\n      this.list.technologyType = info.technologyType;\r\n      this.list.summary = info.summary;\r\n      this.list.applicationArea = info.applicationArea;\r\n      keywordList2(this.list).then((res) => {\r\n        this.tableData = res.rows;\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.application = res.data.url;\r\n        this.form.applicationName = res.data.name;\r\n      }\r\n    },\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .company-demand-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 20px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .mt_40 {\r\n          margin-top: 40px;\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 90%;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-textarea__inner {\r\n          width: 90%;\r\n        }\r\n        .add-demand-tag {\r\n          margin-right: 10px;\r\n          height: 32px;\r\n          line-height: 32px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n          color: #333;\r\n        }\r\n        .el-button:hover,\r\n        .el-button:focus {\r\n          border-color: #d9d9d9;\r\n          background-color: #fff;\r\n        }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8 !important;\r\n          border-color: #21c9b8 !important;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}