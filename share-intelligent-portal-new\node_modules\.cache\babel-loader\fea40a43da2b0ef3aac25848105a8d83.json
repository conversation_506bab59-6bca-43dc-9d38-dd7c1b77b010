{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\detail.vue", "mtime": 1750311962979}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_policyDeclare", "require", "_vuex", "data", "loading", "created", "init", "methods", "_this", "getPolicyDeclareDetail", "id", "$route", "query", "then", "res", "catch", "goDeclare", "_this2", "token", "$confirm", "confirmButtonText", "cancelButtonText", "type", "$store", "dispatch", "location", "href", "$router", "push", "name", "params", "title", "computed", "_objectSpread2", "default", "mapGetters"], "sources": ["src/views/policy/declare/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"policy-declare-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"policy-declarel-detail-banner\">\r\n      <img\r\n        src=\"../../../assets/policyDeclare/policyDeclareDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"policy-declarel-detail-title-box\">\r\n      <div class=\"policy-declarel-detail-divider\"></div>\r\n      <div class=\"policy-declarel-detail-title\">政策详情</div>\r\n      <div class=\"policy-declarel-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"policy-declarel-detail-card\">\r\n      <div class=\"policy-declarel-detail-content\">\r\n        <div class=\"policy-declarel-detail-title\">{{ data.title }}</div>\r\n        <div class=\"headline-box\">\r\n          <div class=\"headline-address\">{{ data.releaseUnitName }}</div>\r\n          <div>{{ data.createTime }}</div>\r\n        </div>\r\n        <div class=\"declarel-detail-content\">\r\n          <div\r\n            v-html=\"data.content\"\r\n            class=\"declarel-detail-text ql-editor\"\r\n          ></div>\r\n        </div>\r\n        <!-- 在线申报按钮 -->\r\n        <div v-if=\"data.policyStatus !== 2\" class=\"activity-area-btn\">\r\n          <el-button class=\"activity-sign-up\" @click=\"goDeclare\"\r\n            >在线申报\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getPolicyDeclareDetail } from \"@/api/policyDeclare\";\r\nimport { mapGetters } from \"vuex\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      getPolicyDeclareDetail({ id: this.$route.query.id })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 跳转在线申报页面\r\n    goDeclare() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.$router.push({\r\n        name: \"AddPolicy\",\r\n        params: {\r\n          id: this.$route.query.id,\r\n          title: this.data.title,\r\n        },\r\n      });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.policy-declare-detail-container {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n  background: #f4f5f9;\r\n\r\n  .policy-declarel-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .policy-declarel-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .policy-declarel-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .policy-declarel-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .policy-declarel-detail-card {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n\r\n    .policy-declarel-detail-content {\r\n      padding: 60px;\r\n\r\n      .policy-declarel-detail-title {\r\n        font-size: 32px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      .headline-box {\r\n        display: flex;\r\n        font-size: 12px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        font-weight: 400;\r\n        color: #999;\r\n        line-height: 12px;\r\n        padding: 40px 0 10px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n\r\n        .headline-address {\r\n          max-width: 990px;\r\n          word-break: break-all;\r\n          padding-right: 40px;\r\n        }\r\n      }\r\n\r\n      .declarel-detail-content {\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n        padding-top: 60px;\r\n      }\r\n\r\n      .activity-area-btn {\r\n        text-align: center;\r\n        margin-top: 96px;\r\n\r\n        .activity-sign-up {\r\n          width: 400px;\r\n          height: 50px;\r\n          background: #21c9b8;\r\n          border-radius: 4px;\r\n          font-size: 20px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #fff;\r\n          line-height: 20px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.policy-declare-detail-container {\r\n  .declarel-detail-content {\r\n    .declarel-detail-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AAuCA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAD,IAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,IAAA,WAAAA,KAAA;MAAA,IAAAE,KAAA;MACA,KAAAJ,OAAA;MACA,IAAAK,qCAAA;QAAAC,EAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF;MAAA,GACAG,IAAA,WAAAC,GAAA;QACAN,KAAA,CAAAJ,OAAA;QACAI,KAAA,CAAAL,IAAA,GAAAW,GAAA,CAAAX,IAAA;MACA,GACAY,KAAA;QACAP,KAAA,CAAAJ,OAAA;MACA;IACA;IACA;IACAY,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAC,KAAA;QACA,KAAAC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAAT,IAAA;UACAI,MAAA,CAAAM,MAAA,CAAAC,QAAA,WAAAX,IAAA;YACAY,QAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAC,MAAA;UACApB,EAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF,EAAA;UACAqB,KAAA,OAAA5B,IAAA,CAAA4B;QACA;MACA;IACA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA;AAEA", "ignoreList": []}]}