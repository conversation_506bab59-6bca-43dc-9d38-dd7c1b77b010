<template>
  <div
    class="serviceBg wow animate__animated animate__fadeInUp"
    data-wow-duration="1s"
  >
    <div class="serviceImg"></div>
    <div class="card-container">
      <div class="enterpriseTitle">
        <div>服务机构</div>
        <div class="allEnterprise" @click="goEnterprise">查看全部>></div>
      </div>
      <div class="content">
        <div
          class="contentItem"
          v-for="(item, index) in data"
          :key="index"
          @click="goEnterpriseDetail(item)"
          :title="item.name"
        >
          <div
            v-if="item.companyPictureList && item.companyPictureList.length > 0"
          >
            <img :src="item.companyPictureList[0].url" alt="" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCompanyHomeList } from "@/api/purchaseSales";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      loading: false,
      data: [],
      pageNum: 1,
      pageSize: 15,
      total: 0,
    };
  },
  created() {
    // this.search();
  },
  methods: {
    search() {
      this.loading = true;
      getCompanyHomeList({
        recommendStatus: 1,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      })
        .then((res) => {
          this.loading = false;
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
          let { rows, total } = res || [];
          this.data = rows;
          this.total = total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 跳转到企业名录详情页面
    goEnterpriseDetail(item) {
      let routeData = this.$router.resolve({
        path: "/enterpriseDetail",
        query: { id: item.id, businessNo: item.businessNo },
      });
      window.open(routeData.href, "_blank");
    },
    goEnterprise() {
      let routeData = this.$router.resolve({
        path: "/enterpriseList",
      });
      window.open(routeData.href, "_blank");
    },
  },
};
</script>

<style lang="scss" scoped>
.serviceBg {
  width: 100%;
  height: 340px;
  position: relative;
  .serviceImg {
    position: absolute;
    top: 0;
    right: 0;
    width: calc((100% - 1200px) / 2);
    height: 500px;
    background-image: url("../../../assets/images/home/<USER>");
    background-size: 100% 100%;
  }
}
.enterpriseTitle {
  width: 100%;
  font-size: 36px;
  text-align: center;
  margin: 10px 0 60px 0;
  position: relative;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #000000;
  .allEnterprise {
    position: absolute;
    top: 8 px;
    right: 0;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #21c9b8;
    line-height: 26px;
    cursor: pointer;
  }
}
.content {
  display: flex;
  justify-content: space-between;
  width: 100%;
  flex-wrap: wrap;
  // height: 280px;
  .contentItem {
    width: 220px;
    height: 90px;
    text-align: center;
    cursor: pointer;
    background: #ffffff;
    box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);
    border-radius: 4px;
    img {
      width: 100%;
      height: 90px;
    }
  }
  .contentItem:nth-child(n + 6) {
    margin-top: 25px;
  }
  .contentItem:hover {
    transform: translateY(-10px);
    transition: 1s;
  }
}
</style>
