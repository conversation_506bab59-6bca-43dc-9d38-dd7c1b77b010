<template>
  <div class="content">
    <div class="content_banner">
      制造共享
      <!-- <div class="imgContent">
        <div class="imgStyle">
          <img style="width: 100%; height: 100%" src="../../../assets/order/orderStep.png" alt="" />
          <div class="joinNow" @click="joinNow">立即入驻</div>
        </div>
      </div> -->
    </div>
    <div class="card-container content_card">
      <div class="leftMenu">
        <div :class="currentPage === index ? 'menuItemHover' : 'menuItem'" v-for="(item, index) in leftMenuList"
          :key="index" @click="handleClick(index)">
          {{ item.name }}
        </div>
      </div>
      <div class="tableStyle">
        <div v-if="currentPage === 0">
          <el-table v-loading="loading" :data="orderList" style="width: 910px" key="table1"
            :span-method="objectSpanMethod">
            <el-table-column label="需求截止时间" align="center" width="100">
              <template slot-scope="scope">
                <span>{{
                  scope.row.order ? scope.row.order.deadline : ""
                  }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品名称" align="center" prop="name" />
            <el-table-column label="规格型号" align="center" prop="modelNumber" />
            <el-table-column label="数量" align="center" prop="quantity" width="60" />
            <el-table-column label="单位" align="center" prop="unit" width="60" />
            <!-- <el-table-column label="可承接量" align="center" prop="capacity" /> -->
            <el-table-column label="联系人" align="center" width="100">
              <template slot-scope="scope">
                <span>{{
                  scope.row.order ? scope.row.order.contactPerson : ""
                  }}</span>
              </template>
            </el-table-column>
            <el-table-column label="联系方式" align="center" width="120">
              <template slot-scope="scope">
                <span>{{
                  scope.row.order ? scope.row.order.contactPhone : ""
                  }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="订单状态" align="center">
              <template slot-scope="scope" v-if="scope.row.order">
                {{
                  scope.row.order.status == "0"
                    ? "未接单"
                    : scope.row.order.status == "1"
                      ? "进行中"
                      : "已完成"
                }}
              </template>
            </el-table-column> -->
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="80">
              <template slot-scope="scope">
                <div class="btnStyle" @click="viewProductDetail(scope.row.order)">
                  查看详情
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!-- <el-table v-loading="loading" :data="orderList" style="width: 910px" key="table1">
            <el-table-column label="需求企业" align="center" prop="demandCompany" width="150" />
            <el-table-column label="需求截止时间" align="center" prop="deadline" width="100" />
            <el-table-column label="托单价格" align="center" prop="price" />
            <el-table-column label="联系电话" align="center" prop="contactPhone" />
            <el-table-column label="交货地址" align="center" prop="deliveryAddress" width="150" />
            <el-table-column label="文件要求" align="center">
              <template slot-scope="scope">
                <div class="text-ellipsis-2">
                  {{ scope.row.fileRequirement }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <div class="btnStyle" @click="viewProductDetail(scope.row)">
                  查看详情
                </div>
              </template>
            </el-table-column>
          </el-table> -->
          <!-- 分页 -->
          <div class="pageStyle">
            <el-pagination v-if="orderList && orderList.length > 0" background layout="prev, pager, next"
              class="activity-pagination" :page-size="pageSize" :current-page="pageNum" :total="total"
              @size-change="handleSizeChange" @current-change="handleCurrentChange">
            </el-pagination>
          </div>
        </div>
        <div v-if="currentPage === 1">
          <el-table v-loading="loading" :data="proProcessesList" style="width: 910px" key="table2">
            <el-table-column label="外协工序名称" align="center" prop="processName" />
            <el-table-column label="外协单位" align="center" prop="companyName" />
            <el-table-column label="联系人" align="center" prop="contactPerson" />
            <el-table-column label="联系方式" align="center" prop="contactPhone" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <div class="btnStyle" @click="jumpIntention(scope.row)">
                  我有意向
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="pageStyle">
            <el-pagination v-if="proProcessesList && proProcessesList.length > 0" background layout="prev, pager, next"
              class="activity-pagination" :page-size="pageSize" :current-page="pageNum" :total="total"
              @size-change="handleSizeChange" @current-change="handleCurrentChange">
            </el-pagination>
          </div>
        </div>
        <div v-if="currentPage == 2">
          <div class="factoryContent" v-loading="loading">
            <div class="factory_item" v-for="(item, index) in enteringFactoryList" :key="index"
              @click="goFactoryDetail(item.id)">
              <div class="companyName">{{ item.companyName }}</div>
              <div class="industry">{{ item.industry }}</div>
            </div>
            <!-- 分页 -->
            <div class="pageStyle">
              <el-pagination v-if="enteringFactoryList && enteringFactoryList.length > 0" background
                layout="prev, pager, next" class="activity-pagination" :page-size="pageSize" :current-page="pageNum"
                :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  orderListData,
  processListData,
  enteringFactoryListData,
} from "@/api/manufacturingSharing";

export default {
  name: "orderSharing",
  data() {
    return {
      currentPage: 0,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      leftMenuList: [
        {
          name: "生产订单",
        },
        {
          name: "工序外协",
        },
        {
          name: "入驻工厂",
        },
      ],
      loading: false,
      orderList: [],
      proProcessesList: [],
      enteringFactoryList: [],
      spanConfig: {
        // 存储合并配置，所有字段使用相同的合并规则
        deadline: [],
        contactPerson: [],
        contactPhone: []
      },
      productId: "",
    };
  },
  created() {
    this.productId = this.$route.query.productId ? this.$route.query.productId : "";
    this.handleClick(parseInt(this.$route.query.page) || 0);
    this.getOrderList();
  },
  methods: {
    getOrderList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        auditStatus: "1",
      };
      orderListData(params).then((res) => {
        if (res.code === 200) {
          this.orderList = res.rows;
          // 手机号脱敏处理
          this.orderList.forEach((item) => {
            if (item.order) {
              item.order.contactPhone = item.order.contactPhone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
            }
          });
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    getProProcesses() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      processListData(params).then((res) => {
        if (res.code === 200) {
          this.proProcessesList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    getEnteringFactory() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: 12,
        settledStatus: "1",
        productId: this.productId,
      };
      enteringFactoryListData(params).then((res) => {
        if (res.code === 200) {
          this.enteringFactoryList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    handleClick(index) {
      this.currentPage = index;
      this.pageNum = 1;
      if (index == 0) {
        this.getOrderList();
      } else if (index == 1) {
        this.getProProcesses();
      } else {
        this.getEnteringFactory();
      }
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      if (this.currentPage == 0) {
        this.getOrderList();
      } else if (this.currentPage == 1) {
        this.getProProcesses();
      } else {
        this.getEnteringFactory();
      }
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      if (this.currentPage == 0) {
        this.getOrderList();
      } else if (this.currentPage == 1) {
        this.getProProcesses();
      } else {
        this.getEnteringFactory();
      }
    },
    viewProductDetail(row) {
      if (row) {
        this.$router.push("/productOrderDetail?id=" + row.id);
      } else {
        this.$message.error("暂无该订单详情");
      }
    },
    joinNow() {
      this.$router.push("/joinNow");
    },
    jumpIntention(item) {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => { });
        return;
      } else {
        this.$router.push(
          `/interested?demandName=${item.processName}&updateTime=${item.updateTime}&intentionType=5&fieldName=工序外协&intentionId=${item.id}`
        );
      }
    },
    goFactoryDetail(id) {
      this.$router.push("/factoryDetail?id=" + id);
    },

    // 判断一行是否有效（有有效的order.id）
    isValidRow(index) {
      const row = this.orderList[index];
      return row && row.order && row.order.id && row.order.id !== '';
    },
    // 计算合并配置（基于order.id）
    calculateSpans() {
      // 初始化所有字段的合并配置
      const fields = ['deadline', 'contactPerson', 'contactPhone'];
      fields.forEach(field => {
        this.spanConfig[field] = new Array(this.orderList.length).fill(0);
      });

      if (this.orderList.length === 0) return;

      let currentGroupSize = 0; // 当前分组的大小
      let currentGroupStart = 0; // 当前分组的起始索引
      let currentGroupId = null; // 当前分组的order.id

      for (let i = 0; i <= this.orderList.length; i++) {
        // 处理最后一行或分组结束
        if (i === this.orderList.length ||
          !this.isValidRow(i) ||
          (currentGroupId !== null && this.orderList[i].order.id !== currentGroupId)) {

          // 为当前分组设置合并配置
          if (currentGroupSize > 0) {
            fields.forEach(field => {
              // 分组起始行设置为分组大小
              this.spanConfig[field][currentGroupStart] = currentGroupSize;
              // 分组内其他行设置为0（不显示）
              for (let j = 1; j < currentGroupSize; j++) {
                this.spanConfig[field][currentGroupStart + j] = 0;
              }
            });
          }

          // 重置分组状态
          currentGroupSize = 0;
          currentGroupId = null;

          // 处理当前行（如果是无效行）
          if (i < this.orderList.length && !this.isValidRow(i)) {
            fields.forEach(field => {
              this.spanConfig[field][i] = 1; // 无效行单独显示
            });
            continue;
          }
        }

        // 开始新分组或继续当前分组
        if (i < this.orderList.length) {
          const row = this.orderList[i];
          if (this.isValidRow(i)) {
            if (currentGroupSize === 0) {
              // 开始新分组
              currentGroupStart = i;
              currentGroupId = row.order.id;
              currentGroupSize = 1;
            } else {
              // 继续当前分组
              currentGroupSize++;
            }
          }
        }
      }
    },

    // 表格合并方法
    objectSpanMethod({ row, column, rowIndex }) {
      // 需求截止时间列（第0列）
      if (column.label === '需求截止时间') {
        return {
          rowspan: this.spanConfig.deadline[rowIndex],
          colspan: this.spanConfig.deadline[rowIndex] > 0 ? 1 : 0
        };
      }
      // 联系人列（第5列）
      if (column.label === '联系人') {
        return {
          rowspan: this.spanConfig.contactPerson[rowIndex],
          colspan: this.spanConfig.contactPerson[rowIndex] > 0 ? 1 : 0
        };
      }
      // 联系电话列（第6列）
      if (column.label === '联系方式') {
        return {
          rowspan: this.spanConfig.contactPhone[rowIndex],
          colspan: this.spanConfig.contactPhone[rowIndex] > 0 ? 1 : 0
        };
      }

      // 其他列不合并
      return {
        rowspan: 1,
        colspan: 1
      };
    },

  },
  watch: {
    orderList: {
      immediate: true,
      handler(newVal) {
        if (newVal && newVal.length) {
          this.calculateSpans();
        }
      }
    }
  },

};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  padding-bottom: 60px;
  background-color: #f2f2f2;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.5;
  /* 建议设置行高 */
  max-height: 3em;
  /* 2行 x 1.5行高 */
}

.content_banner {
  width: 100%;
  height: 300px;
  text-align: center;
  line-height: 300px;
  background-image: url("../../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;

  .imgContent {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 10px;

    .imgStyle {
      width: 1256px;
      height: 206px;
      position: relative;

      .joinNow {
        position: absolute;
        right: 90px;
        top: 75px;
        width: 110px;
        height: 50px;
        background: #f79a47;
        border-radius: 2px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #ffffff;
        line-height: 50px;
        text-align: center;
        cursor: pointer;
      }
    }
  }
}

.content_card {
  min-height: 436px;
  // max-height: 990px;
  background: #ffffff;
  border-radius: 2px;
  margin-top: 30px;
  padding: 40px 45px 62px 43px;
  display: flex;

  .leftMenu {
    width: 160px;
    height: 180px;
    background: #ffffff;
    box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.08);
    border-radius: 2px;

    .menuItemHover {
      width: 160px;
      height: 60px;
      background: #21c9b8;
      box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.08);
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
      text-align: center;
      line-height: 60px;
      cursor: pointer;
    }

    .menuItem {
      width: 160px;
      height: 60px;
      box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.08);
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #333333;
      text-align: center;
      line-height: 60px;
      cursor: pointer;
    }
  }

  .tableStyle {
    margin-left: 42px;
    width: 100%;

    .btnStyle {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #21c9b8;
      cursor: pointer;
    }

    .factoryContent {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .factory_item {
        width: 290px;
        height: 106px;
        background: #ffffff;
        box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.06);
        border-radius: 2px;
        margin-left: 20px;
        padding: 21px;
        cursor: pointer;
      }

      .factory_item:hover {
        box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);
        scale: 1.01;
      }

      .factory_item:nth-child(3n + 1) {
        margin-left: 0;
      }

      .factory_item:nth-child(n + 4) {
        margin-top: 20px;
      }

      .companyName {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #303133;
        height: 16px;
      }

      .industry {
        width: 90px;
        height: 34px;
        background: #eff9f9;
        border-radius: 2px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #21c9b8;
        line-height: 34px;
        text-align: center;
        margin-top: 15px;
      }
    }
  }

  .pageStyle {
    margin-top: 60px;
    width: 100%;
    text-align: center;
  }
}
</style>
