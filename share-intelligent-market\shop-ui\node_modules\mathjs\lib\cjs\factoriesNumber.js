"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.createAbs = void 0;
Object.defineProperty(exports, "createAccessorNode", {
  enumerable: true,
  get: function get() {
    return _AccessorNode.createAccessorNode;
  }
});
exports.createAnd = exports.createAddScalar = exports.createAdd = exports.createAcsch = exports.createAcsc = exports.createAcoth = exports.createAcot = exports.createAcosh = exports.createAcos = void 0;
Object.defineProperty(exports, "createApply", {
  enumerable: true,
  get: function get() {
    return _apply.createApply;
  }
});
Object.defineProperty(exports, "createApplyTransform", {
  enumerable: true,
  get: function get() {
    return _applyTransform.createApplyTransform;
  }
});
Object.defineProperty(exports, "createArrayNode", {
  enumerable: true,
  get: function get() {
    return _ArrayNode.createArrayNode;
  }
});
exports.createAsinh = exports.createAsin = exports.createAsech = exports.createAsec = void 0;
Object.defineProperty(exports, "createAssignmentNode", {
  enumerable: true,
  get: function get() {
    return _AssignmentNode.createAssignmentNode;
  }
});
exports.createAtanh = exports.createAtan2 = exports.createAtan = void 0;
Object.defineProperty(exports, "createBellNumbers", {
  enumerable: true,
  get: function get() {
    return _bellNumbers.createBellNumbers;
  }
});
exports.createBitXor = exports.createBitOr = exports.createBitNot = exports.createBitAnd = void 0;
Object.defineProperty(exports, "createBlockNode", {
  enumerable: true,
  get: function get() {
    return _BlockNode.createBlockNode;
  }
});
Object.defineProperty(exports, "createBoolean", {
  enumerable: true,
  get: function get() {
    return _boolean.createBoolean;
  }
});
Object.defineProperty(exports, "createCatalan", {
  enumerable: true,
  get: function get() {
    return _catalan.createCatalan;
  }
});
exports.createCbrt = void 0;
Object.defineProperty(exports, "createCeil", {
  enumerable: true,
  get: function get() {
    return _ceil.createCeilNumber;
  }
});
Object.defineProperty(exports, "createChain", {
  enumerable: true,
  get: function get() {
    return _chain.createChain;
  }
});
Object.defineProperty(exports, "createChainClass", {
  enumerable: true,
  get: function get() {
    return _Chain.createChainClass;
  }
});
Object.defineProperty(exports, "createClone", {
  enumerable: true,
  get: function get() {
    return _clone.createClone;
  }
});
exports.createCombinations = void 0;
Object.defineProperty(exports, "createCombinationsWithRep", {
  enumerable: true,
  get: function get() {
    return _combinationsWithRep.createCombinationsWithRep;
  }
});
Object.defineProperty(exports, "createCompare", {
  enumerable: true,
  get: function get() {
    return _compare.createCompareNumber;
  }
});
Object.defineProperty(exports, "createCompareNatural", {
  enumerable: true,
  get: function get() {
    return _compareNatural.createCompareNatural;
  }
});
Object.defineProperty(exports, "createCompareText", {
  enumerable: true,
  get: function get() {
    return _compareText.createCompareTextNumber;
  }
});
Object.defineProperty(exports, "createCompile", {
  enumerable: true,
  get: function get() {
    return _compile.createCompile;
  }
});
Object.defineProperty(exports, "createComposition", {
  enumerable: true,
  get: function get() {
    return _composition.createComposition;
  }
});
Object.defineProperty(exports, "createConditionalNode", {
  enumerable: true,
  get: function get() {
    return _ConditionalNode.createConditionalNode;
  }
});
Object.defineProperty(exports, "createConstantNode", {
  enumerable: true,
  get: function get() {
    return _ConstantNode.createConstantNode;
  }
});
exports.createCube = exports.createCsch = exports.createCsc = exports.createCoth = exports.createCot = exports.createCosh = exports.createCos = void 0;
Object.defineProperty(exports, "createCumSum", {
  enumerable: true,
  get: function get() {
    return _cumsum.createCumSum;
  }
});
Object.defineProperty(exports, "createCumSumTransform", {
  enumerable: true,
  get: function get() {
    return _cumsumTransform.createCumSumTransform;
  }
});
Object.defineProperty(exports, "createDeepEqual", {
  enumerable: true,
  get: function get() {
    return _deepEqual.createDeepEqual;
  }
});
Object.defineProperty(exports, "createDerivative", {
  enumerable: true,
  get: function get() {
    return _derivative.createDerivative;
  }
});
exports.createDivideScalar = exports.createDivide = void 0;
Object.defineProperty(exports, "createE", {
  enumerable: true,
  get: function get() {
    return _constants.createE;
  }
});
Object.defineProperty(exports, "createEqual", {
  enumerable: true,
  get: function get() {
    return _equal.createEqualNumber;
  }
});
Object.defineProperty(exports, "createEqualScalar", {
  enumerable: true,
  get: function get() {
    return _equalScalar.createEqualScalarNumber;
  }
});
Object.defineProperty(exports, "createEqualText", {
  enumerable: true,
  get: function get() {
    return _equalText.createEqualText;
  }
});
Object.defineProperty(exports, "createErf", {
  enumerable: true,
  get: function get() {
    return _erf.createErf;
  }
});
Object.defineProperty(exports, "createEvaluate", {
  enumerable: true,
  get: function get() {
    return _evaluate.createEvaluate;
  }
});
exports.createExpm1 = exports.createExp = void 0;
Object.defineProperty(exports, "createFactorial", {
  enumerable: true,
  get: function get() {
    return _factorial.createFactorial;
  }
});
Object.defineProperty(exports, "createFalse", {
  enumerable: true,
  get: function get() {
    return _constants.createFalse;
  }
});
Object.defineProperty(exports, "createFilter", {
  enumerable: true,
  get: function get() {
    return _filter.createFilter;
  }
});
Object.defineProperty(exports, "createFilterTransform", {
  enumerable: true,
  get: function get() {
    return _filterTransform.createFilterTransform;
  }
});
Object.defineProperty(exports, "createFix", {
  enumerable: true,
  get: function get() {
    return _fix.createFixNumber;
  }
});
Object.defineProperty(exports, "createFloor", {
  enumerable: true,
  get: function get() {
    return _floor.createFloorNumber;
  }
});
Object.defineProperty(exports, "createForEach", {
  enumerable: true,
  get: function get() {
    return _forEach.createForEach;
  }
});
Object.defineProperty(exports, "createForEachTransform", {
  enumerable: true,
  get: function get() {
    return _forEachTransform.createForEachTransform;
  }
});
Object.defineProperty(exports, "createFormat", {
  enumerable: true,
  get: function get() {
    return _format.createFormat;
  }
});
Object.defineProperty(exports, "createFunctionAssignmentNode", {
  enumerable: true,
  get: function get() {
    return _FunctionAssignmentNode.createFunctionAssignmentNode;
  }
});
Object.defineProperty(exports, "createFunctionNode", {
  enumerable: true,
  get: function get() {
    return _FunctionNode.createFunctionNode;
  }
});
exports.createGcd = exports.createGamma = void 0;
Object.defineProperty(exports, "createHasNumericValue", {
  enumerable: true,
  get: function get() {
    return _hasNumericValue.createHasNumericValue;
  }
});
Object.defineProperty(exports, "createHelp", {
  enumerable: true,
  get: function get() {
    return _help.createHelp;
  }
});
Object.defineProperty(exports, "createHelpClass", {
  enumerable: true,
  get: function get() {
    return _Help.createHelpClass;
  }
});
Object.defineProperty(exports, "createHypot", {
  enumerable: true,
  get: function get() {
    return _hypot.createHypot;
  }
});
exports.createIndex = void 0;
Object.defineProperty(exports, "createIndexNode", {
  enumerable: true,
  get: function get() {
    return _IndexNode.createIndexNode;
  }
});
Object.defineProperty(exports, "createInfinity", {
  enumerable: true,
  get: function get() {
    return _constants.createInfinity;
  }
});
exports.createIsNegative = exports.createIsNaN = exports.createIsInteger = void 0;
Object.defineProperty(exports, "createIsNumeric", {
  enumerable: true,
  get: function get() {
    return _isNumeric.createIsNumeric;
  }
});
exports.createIsPositive = void 0;
Object.defineProperty(exports, "createIsPrime", {
  enumerable: true,
  get: function get() {
    return _isPrime.createIsPrime;
  }
});
exports.createIsZero = void 0;
Object.defineProperty(exports, "createLN10", {
  enumerable: true,
  get: function get() {
    return _constants.createLN10;
  }
});
Object.defineProperty(exports, "createLN2", {
  enumerable: true,
  get: function get() {
    return _constants.createLN2;
  }
});
Object.defineProperty(exports, "createLOG10E", {
  enumerable: true,
  get: function get() {
    return _constants.createLOG10E;
  }
});
Object.defineProperty(exports, "createLOG2E", {
  enumerable: true,
  get: function get() {
    return _constants.createLOG2E;
  }
});
Object.defineProperty(exports, "createLarger", {
  enumerable: true,
  get: function get() {
    return _larger.createLargerNumber;
  }
});
Object.defineProperty(exports, "createLargerEq", {
  enumerable: true,
  get: function get() {
    return _largerEq.createLargerEqNumber;
  }
});
exports.createLeftShift = exports.createLcm = void 0;
exports.createLog2 = exports.createLog1p = exports.createLog10 = exports.createLog = exports.createLgamma = void 0;
Object.defineProperty(exports, "createMad", {
  enumerable: true,
  get: function get() {
    return _mad.createMad;
  }
});
Object.defineProperty(exports, "createMap", {
  enumerable: true,
  get: function get() {
    return _map.createMap;
  }
});
Object.defineProperty(exports, "createMapTransform", {
  enumerable: true,
  get: function get() {
    return _mapTransform.createMapTransform;
  }
});
exports.createMatrix = void 0;
Object.defineProperty(exports, "createMax", {
  enumerable: true,
  get: function get() {
    return _max.createMax;
  }
});
Object.defineProperty(exports, "createMaxTransform", {
  enumerable: true,
  get: function get() {
    return _maxTransform.createMaxTransform;
  }
});
Object.defineProperty(exports, "createMean", {
  enumerable: true,
  get: function get() {
    return _mean.createMean;
  }
});
Object.defineProperty(exports, "createMeanTransform", {
  enumerable: true,
  get: function get() {
    return _meanTransform.createMeanTransform;
  }
});
Object.defineProperty(exports, "createMedian", {
  enumerable: true,
  get: function get() {
    return _median.createMedian;
  }
});
Object.defineProperty(exports, "createMin", {
  enumerable: true,
  get: function get() {
    return _min.createMin;
  }
});
Object.defineProperty(exports, "createMinTransform", {
  enumerable: true,
  get: function get() {
    return _minTransform.createMinTransform;
  }
});
exports.createMod = void 0;
Object.defineProperty(exports, "createMode", {
  enumerable: true,
  get: function get() {
    return _mode.createMode;
  }
});
Object.defineProperty(exports, "createMultinomial", {
  enumerable: true,
  get: function get() {
    return _multinomial.createMultinomial;
  }
});
exports.createMultiplyScalar = exports.createMultiply = void 0;
Object.defineProperty(exports, "createNaN", {
  enumerable: true,
  get: function get() {
    return _constants.createNaN;
  }
});
Object.defineProperty(exports, "createNode", {
  enumerable: true,
  get: function get() {
    return _Node.createNode;
  }
});
exports.createNthRoot = exports.createNot = exports.createNorm = void 0;
Object.defineProperty(exports, "createNull", {
  enumerable: true,
  get: function get() {
    return _constants.createNull;
  }
});
Object.defineProperty(exports, "createNumber", {
  enumerable: true,
  get: function get() {
    return _number.createNumber;
  }
});
Object.defineProperty(exports, "createNumeric", {
  enumerable: true,
  get: function get() {
    return _numeric.createNumeric;
  }
});
Object.defineProperty(exports, "createObjectNode", {
  enumerable: true,
  get: function get() {
    return _ObjectNode.createObjectNode;
  }
});
Object.defineProperty(exports, "createOperatorNode", {
  enumerable: true,
  get: function get() {
    return _OperatorNode.createOperatorNode;
  }
});
exports.createOr = void 0;
Object.defineProperty(exports, "createParenthesisNode", {
  enumerable: true,
  get: function get() {
    return _ParenthesisNode.createParenthesisNode;
  }
});
Object.defineProperty(exports, "createParse", {
  enumerable: true,
  get: function get() {
    return _parse.createParse;
  }
});
Object.defineProperty(exports, "createParser", {
  enumerable: true,
  get: function get() {
    return _parser.createParser;
  }
});
Object.defineProperty(exports, "createParserClass", {
  enumerable: true,
  get: function get() {
    return _Parser.createParserClass;
  }
});
Object.defineProperty(exports, "createPartitionSelect", {
  enumerable: true,
  get: function get() {
    return _partitionSelect.createPartitionSelect;
  }
});
Object.defineProperty(exports, "createPermutations", {
  enumerable: true,
  get: function get() {
    return _permutations.createPermutations;
  }
});
Object.defineProperty(exports, "createPhi", {
  enumerable: true,
  get: function get() {
    return _constants.createPhi;
  }
});
Object.defineProperty(exports, "createPi", {
  enumerable: true,
  get: function get() {
    return _constants.createPi;
  }
});
Object.defineProperty(exports, "createPickRandom", {
  enumerable: true,
  get: function get() {
    return _pickRandom.createPickRandom;
  }
});
exports.createPow = void 0;
Object.defineProperty(exports, "createPrint", {
  enumerable: true,
  get: function get() {
    return _print.createPrint;
  }
});
Object.defineProperty(exports, "createProd", {
  enumerable: true,
  get: function get() {
    return _prod.createProd;
  }
});
Object.defineProperty(exports, "createQuantileSeq", {
  enumerable: true,
  get: function get() {
    return _quantileSeq.createQuantileSeq;
  }
});
Object.defineProperty(exports, "createRandom", {
  enumerable: true,
  get: function get() {
    return _random.createRandomNumber;
  }
});
Object.defineProperty(exports, "createRandomInt", {
  enumerable: true,
  get: function get() {
    return _randomInt.createRandomInt;
  }
});
Object.defineProperty(exports, "createRange", {
  enumerable: true,
  get: function get() {
    return _range.createRange;
  }
});
Object.defineProperty(exports, "createRangeClass", {
  enumerable: true,
  get: function get() {
    return _Range.createRangeClass;
  }
});
Object.defineProperty(exports, "createRangeNode", {
  enumerable: true,
  get: function get() {
    return _RangeNode.createRangeNode;
  }
});
Object.defineProperty(exports, "createRangeTransform", {
  enumerable: true,
  get: function get() {
    return _rangeTransform.createRangeTransform;
  }
});
Object.defineProperty(exports, "createRationalize", {
  enumerable: true,
  get: function get() {
    return _rationalize.createRationalize;
  }
});
Object.defineProperty(exports, "createRelationalNode", {
  enumerable: true,
  get: function get() {
    return _RelationalNode.createRelationalNode;
  }
});
Object.defineProperty(exports, "createReplacer", {
  enumerable: true,
  get: function get() {
    return _replacer.createReplacer;
  }
});
Object.defineProperty(exports, "createResolve", {
  enumerable: true,
  get: function get() {
    return _resolve.createResolve;
  }
});
Object.defineProperty(exports, "createResultSet", {
  enumerable: true,
  get: function get() {
    return _ResultSet.createResultSet;
  }
});
Object.defineProperty(exports, "createReviver", {
  enumerable: true,
  get: function get() {
    return _reviver.createReviver;
  }
});
exports.createRound = exports.createRightLogShift = exports.createRightArithShift = void 0;
Object.defineProperty(exports, "createSQRT1_2", {
  enumerable: true,
  get: function get() {
    return _constants.createSQRT1_2;
  }
});
Object.defineProperty(exports, "createSQRT2", {
  enumerable: true,
  get: function get() {
    return _constants.createSQRT2;
  }
});
exports.createSign = exports.createSech = exports.createSec = void 0;
Object.defineProperty(exports, "createSimplify", {
  enumerable: true,
  get: function get() {
    return _simplify.createSimplify;
  }
});
Object.defineProperty(exports, "createSimplifyCore", {
  enumerable: true,
  get: function get() {
    return _simplifyCore.createSimplifyCore;
  }
});
exports.createSinh = exports.createSin = void 0;
Object.defineProperty(exports, "createSize", {
  enumerable: true,
  get: function get() {
    return _size.createSize;
  }
});
Object.defineProperty(exports, "createSmaller", {
  enumerable: true,
  get: function get() {
    return _smaller.createSmallerNumber;
  }
});
Object.defineProperty(exports, "createSmallerEq", {
  enumerable: true,
  get: function get() {
    return _smallerEq.createSmallerEqNumber;
  }
});
exports.createSquare = exports.createSqrt = void 0;
Object.defineProperty(exports, "createStd", {
  enumerable: true,
  get: function get() {
    return _std.createStd;
  }
});
Object.defineProperty(exports, "createStdTransform", {
  enumerable: true,
  get: function get() {
    return _stdTransform.createStdTransform;
  }
});
Object.defineProperty(exports, "createStirlingS2", {
  enumerable: true,
  get: function get() {
    return _stirlingS.createStirlingS2;
  }
});
Object.defineProperty(exports, "createString", {
  enumerable: true,
  get: function get() {
    return _string.createString;
  }
});
exports.createSubtract = exports.createSubsetTransform = exports.createSubset = void 0;
Object.defineProperty(exports, "createSum", {
  enumerable: true,
  get: function get() {
    return _sum.createSum;
  }
});
Object.defineProperty(exports, "createSumTransform", {
  enumerable: true,
  get: function get() {
    return _sumTransform.createSumTransform;
  }
});
Object.defineProperty(exports, "createSymbolNode", {
  enumerable: true,
  get: function get() {
    return _SymbolNode.createSymbolNode;
  }
});
exports.createTanh = exports.createTan = void 0;
Object.defineProperty(exports, "createTau", {
  enumerable: true,
  get: function get() {
    return _constants.createTau;
  }
});
Object.defineProperty(exports, "createTrue", {
  enumerable: true,
  get: function get() {
    return _constants.createTrue;
  }
});
Object.defineProperty(exports, "createTypeOf", {
  enumerable: true,
  get: function get() {
    return _typeOf.createTypeOf;
  }
});
Object.defineProperty(exports, "createTyped", {
  enumerable: true,
  get: function get() {
    return _typed.createTyped;
  }
});
exports.createUnaryPlus = exports.createUnaryMinus = void 0;
Object.defineProperty(exports, "createUnequal", {
  enumerable: true,
  get: function get() {
    return _unequal.createUnequalNumber;
  }
});
Object.defineProperty(exports, "createUppercaseE", {
  enumerable: true,
  get: function get() {
    return _constants.createUppercaseE;
  }
});
Object.defineProperty(exports, "createUppercasePi", {
  enumerable: true,
  get: function get() {
    return _constants.createUppercasePi;
  }
});
Object.defineProperty(exports, "createVariance", {
  enumerable: true,
  get: function get() {
    return _variance.createVariance;
  }
});
Object.defineProperty(exports, "createVarianceTransform", {
  enumerable: true,
  get: function get() {
    return _varianceTransform.createVarianceTransform;
  }
});
Object.defineProperty(exports, "createVersion", {
  enumerable: true,
  get: function get() {
    return _constants.createVersion;
  }
});
exports.createXor = exports.createXgcd = void 0;

var _index = require("./plain/number/index.js");

var _factory = require("./utils/factory.js");

var _noop = require("./utils/noop.js");

var _typed = require("./core/function/typed.js");

var _ResultSet = require("./type/resultset/ResultSet.js");

var _Range = require("./type/matrix/Range.js");

var _Help = require("./expression/Help.js");

var _Chain = require("./type/chain/Chain.js");

var _help = require("./expression/function/help.js");

var _chain = require("./type/chain/function/chain.js");

var _resolve = require("./function/algebra/resolve.js");

var _simplify = require("./function/algebra/simplify.js");

var _simplifyCore = require("./function/algebra/simplifyCore.js");

var _derivative = require("./function/algebra/derivative.js");

var _rationalize = require("./function/algebra/rationalize.js");

var _ceil = require("./function/arithmetic/ceil.js");

var _fix = require("./function/arithmetic/fix.js");

var _floor = require("./function/arithmetic/floor.js");

var _hypot = require("./function/arithmetic/hypot.js");

var _stirlingS = require("./function/combinatorics/stirlingS2.js");

var _bellNumbers = require("./function/combinatorics/bellNumbers.js");

var _catalan = require("./function/combinatorics/catalan.js");

var _composition = require("./function/combinatorics/composition.js");

var _constants = require("./constants.js");

var _number = require("./type/number.js");

var _string = require("./type/string.js");

var _boolean = require("./type/boolean.js");

var _parser = require("./expression/function/parser.js");

var _Node = require("./expression/node/Node.js");

var _AccessorNode = require("./expression/node/AccessorNode.js");

var _ArrayNode = require("./expression/node/ArrayNode.js");

var _AssignmentNode = require("./expression/node/AssignmentNode.js");

var _BlockNode = require("./expression/node/BlockNode.js");

var _ConditionalNode = require("./expression/node/ConditionalNode.js");

var _ConstantNode = require("./expression/node/ConstantNode.js");

var _FunctionAssignmentNode = require("./expression/node/FunctionAssignmentNode.js");

var _IndexNode = require("./expression/node/IndexNode.js");

var _ObjectNode = require("./expression/node/ObjectNode.js");

var _OperatorNode = require("./expression/node/OperatorNode.js");

var _ParenthesisNode = require("./expression/node/ParenthesisNode.js");

var _RangeNode = require("./expression/node/RangeNode.js");

var _RelationalNode = require("./expression/node/RelationalNode.js");

var _SymbolNode = require("./expression/node/SymbolNode.js");

var _FunctionNode = require("./expression/node/FunctionNode.js");

var _parse = require("./expression/parse.js");

var _compile = require("./expression/function/compile.js");

var _evaluate = require("./expression/function/evaluate.js");

var _Parser = require("./expression/Parser.js");

var _apply = require("./function/matrix/apply.js");

var _filter = require("./function/matrix/filter.js");

var _forEach = require("./function/matrix/forEach.js");

var _map = require("./function/matrix/map.js");

var _range = require("./function/matrix/range.js");

var _size = require("./function/matrix/size.js");

var _partitionSelect = require("./function/matrix/partitionSelect.js");

var _combinationsWithRep = require("./function/probability/combinationsWithRep.js");

var _factorial = require("./function/probability/factorial.js");

var _multinomial = require("./function/probability/multinomial.js");

var _permutations = require("./function/probability/permutations.js");

var _pickRandom = require("./function/probability/pickRandom.js");

var _random = require("./function/probability/random.js");

var _randomInt = require("./function/probability/randomInt.js");

var _equalScalar = require("./function/relational/equalScalar.js");

var _compare = require("./function/relational/compare.js");

var _compareNatural = require("./function/relational/compareNatural.js");

var _compareText = require("./function/relational/compareText.js");

var _equal = require("./function/relational/equal.js");

var _equalText = require("./function/relational/equalText.js");

var _smaller = require("./function/relational/smaller.js");

var _smallerEq = require("./function/relational/smallerEq.js");

var _larger = require("./function/relational/larger.js");

var _largerEq = require("./function/relational/largerEq.js");

var _deepEqual = require("./function/relational/deepEqual.js");

var _unequal = require("./function/relational/unequal.js");

var _erf = require("./function/special/erf.js");

var _mode = require("./function/statistics/mode.js");

var _prod = require("./function/statistics/prod.js");

var _max = require("./function/statistics/max.js");

var _min = require("./function/statistics/min.js");

var _sum = require("./function/statistics/sum.js");

var _cumsum = require("./function/statistics/cumsum.js");

var _mean = require("./function/statistics/mean.js");

var _median = require("./function/statistics/median.js");

var _mad = require("./function/statistics/mad.js");

var _variance = require("./function/statistics/variance.js");

var _quantileSeq = require("./function/statistics/quantileSeq.js");

var _std = require("./function/statistics/std.js");

var _format = require("./function/string/format.js");

var _print = require("./function/string/print.js");

var _applyTransform = require("./expression/transform/apply.transform.js");

var _filterTransform = require("./expression/transform/filter.transform.js");

var _forEachTransform = require("./expression/transform/forEach.transform.js");

var _mapTransform = require("./expression/transform/map.transform.js");

var _maxTransform = require("./expression/transform/max.transform.js");

var _meanTransform = require("./expression/transform/mean.transform.js");

var _minTransform = require("./expression/transform/min.transform.js");

var _rangeTransform = require("./expression/transform/range.transform.js");

var _stdTransform = require("./expression/transform/std.transform.js");

var _sumTransform = require("./expression/transform/sum.transform.js");

var _cumsumTransform = require("./expression/transform/cumsum.transform.js");

var _varianceTransform = require("./expression/transform/variance.transform.js");

var _clone = require("./function/utils/clone.js");

var _isNumeric = require("./function/utils/isNumeric.js");

var _hasNumericValue = require("./function/utils/hasNumericValue.js");

var _typeOf = require("./function/utils/typeOf.js");

var _isPrime = require("./function/utils/isPrime.js");

var _numeric = require("./function/utils/numeric.js");

var _reviver = require("./json/reviver.js");

var _replacer = require("./json/replacer.js");

// ----------------------------------------------------------------------------
// classes and functions
// core
// classes
// algebra
// arithmetic
var createUnaryMinus = /* #__PURE__ */createNumberFactory('unaryMinus', _index.unaryMinusNumber);
exports.createUnaryMinus = createUnaryMinus;
var createUnaryPlus = /* #__PURE__ */createNumberFactory('unaryPlus', _index.unaryPlusNumber);
exports.createUnaryPlus = createUnaryPlus;
var createAbs = /* #__PURE__ */createNumberFactory('abs', _index.absNumber);
exports.createAbs = createAbs;
var createAddScalar = /* #__PURE__ */createNumberFactory('addScalar', _index.addNumber);
exports.createAddScalar = createAddScalar;
var createCbrt = /* #__PURE__ */createNumberFactory('cbrt', _index.cbrtNumber);
exports.createCbrt = createCbrt;
var createCube = /* #__PURE__ */createNumberFactory('cube', _index.cubeNumber);
exports.createCube = createCube;
var createExp = /* #__PURE__ */createNumberFactory('exp', _index.expNumber);
exports.createExp = createExp;
var createExpm1 = /* #__PURE__ */createNumberFactory('expm1', _index.expm1Number);
exports.createExpm1 = createExpm1;
var createGcd = /* #__PURE__ */createNumberFactory('gcd', _index.gcdNumber);
exports.createGcd = createGcd;
var createLcm = /* #__PURE__ */createNumberFactory('lcm', _index.lcmNumber);
exports.createLcm = createLcm;
var createLog10 = /* #__PURE__ */createNumberFactory('log10', _index.log10Number);
exports.createLog10 = createLog10;
var createLog2 = /* #__PURE__ */createNumberFactory('log2', _index.log2Number);
exports.createLog2 = createLog2;
var createMod = /* #__PURE__ */createNumberFactory('mod', _index.modNumber);
exports.createMod = createMod;
var createMultiplyScalar = /* #__PURE__ */createNumberFactory('multiplyScalar', _index.multiplyNumber);
exports.createMultiplyScalar = createMultiplyScalar;
var createMultiply = /* #__PURE__ */createNumberFactory('multiply', _index.multiplyNumber);
exports.createMultiply = createMultiply;
var createNthRoot = /* #__PURE__ */createNumberOptionalSecondArgFactory('nthRoot', _index.nthRootNumber);
exports.createNthRoot = createNthRoot;
var createSign = /* #__PURE__ */createNumberFactory('sign', _index.signNumber);
exports.createSign = createSign;
var createSqrt = /* #__PURE__ */createNumberFactory('sqrt', _index.sqrtNumber);
exports.createSqrt = createSqrt;
var createSquare = /* #__PURE__ */createNumberFactory('square', _index.squareNumber);
exports.createSquare = createSquare;
var createSubtract = /* #__PURE__ */createNumberFactory('subtract', _index.subtractNumber);
exports.createSubtract = createSubtract;
var createXgcd = /* #__PURE__ */createNumberFactory('xgcd', _index.xgcdNumber);
exports.createXgcd = createXgcd;
var createDivideScalar = /* #__PURE__ */createNumberFactory('divideScalar', _index.divideNumber);
exports.createDivideScalar = createDivideScalar;
var createPow = /* #__PURE__ */createNumberFactory('pow', _index.powNumber);
exports.createPow = createPow;
var createRound = /* #__PURE__ */createNumberOptionalSecondArgFactory('round', _index.roundNumber);
exports.createRound = createRound;
var createLog = /* #__PURE__ */createNumberOptionalSecondArgFactory('log', _index.logNumber);
exports.createLog = createLog;
var createLog1p = /* #__PURE__ */createNumberFactory('log1p', _index.log1pNumber);
exports.createLog1p = createLog1p;
var createAdd = /* #__PURE__ */createNumberFactory('add', _index.addNumber);
exports.createAdd = createAdd;
var createNorm = /* #__PURE__ */createNumberFactory('norm', _index.normNumber);
exports.createNorm = createNorm;
var createDivide = /* #__PURE__ */createNumberFactory('divide', _index.divideNumber); // bitwise

exports.createDivide = createDivide;
var createBitAnd = /* #__PURE__ */createNumberFactory('bitAnd', _index.bitAndNumber);
exports.createBitAnd = createBitAnd;
var createBitNot = /* #__PURE__ */createNumberFactory('bitNot', _index.bitNotNumber);
exports.createBitNot = createBitNot;
var createBitOr = /* #__PURE__ */createNumberFactory('bitOr', _index.bitOrNumber);
exports.createBitOr = createBitOr;
var createBitXor = /* #__PURE__ */createNumberFactory('bitXor', _index.bitXorNumber);
exports.createBitXor = createBitXor;
var createLeftShift = /* #__PURE__ */createNumberFactory('leftShift', _index.leftShiftNumber);
exports.createLeftShift = createLeftShift;
var createRightArithShift = /* #__PURE__ */createNumberFactory('rightArithShift', _index.rightArithShiftNumber);
exports.createRightArithShift = createRightArithShift;
var createRightLogShift = /* #__PURE__ */createNumberFactory('rightLogShift', _index.rightLogShiftNumber); // combinatorics

exports.createRightLogShift = createRightLogShift;
// logical
var createAnd = /* #__PURE__ */createNumberFactory('and', _index.andNumber);
exports.createAnd = createAnd;
var createNot = /* #__PURE__ */createNumberFactory('not', _index.notNumber);
exports.createNot = createNot;
var createOr = /* #__PURE__ */createNumberFactory('or', _index.orNumber);
exports.createOr = createOr;
var createXor = /* #__PURE__ */createNumberFactory('xor', _index.xorNumber); // matrix

exports.createXor = createXor;
// FIXME: create a lightweight "number" implementation of subset only supporting plain objects/arrays
var createIndex = /* #__PURE__ */(0, _factory.factory)('index', [], function () {
  return _noop.noIndex;
});
exports.createIndex = createIndex;
var createMatrix = /* #__PURE__ */(0, _factory.factory)('matrix', [], function () {
  return _noop.noMatrix;
}); // FIXME: needed now because subset transform needs it. Remove the need for it in subset

exports.createMatrix = createMatrix;
var createSubset = /* #__PURE__ */(0, _factory.factory)('subset', [], function () {
  return _noop.noSubset;
}); // TODO: provide number+array implementations for map, filter, forEach, zeros, ...?
// TODO: create range implementation for range?

exports.createSubset = createSubset;
// probability
var createCombinations = createNumberFactory('combinations', _index.combinationsNumber);
exports.createCombinations = createCombinations;
var createGamma = createNumberFactory('gamma', _index.gammaNumber);
exports.createGamma = createGamma;
var createLgamma = createNumberFactory('lgamma', _index.lgammaNumber);
exports.createLgamma = createLgamma;
// trigonometry
var createAcos = /* #__PURE__ */createNumberFactory('acos', _index.acosNumber);
exports.createAcos = createAcos;
var createAcosh = /* #__PURE__ */createNumberFactory('acosh', _index.acoshNumber);
exports.createAcosh = createAcosh;
var createAcot = /* #__PURE__ */createNumberFactory('acot', _index.acotNumber);
exports.createAcot = createAcot;
var createAcoth = /* #__PURE__ */createNumberFactory('acoth', _index.acothNumber);
exports.createAcoth = createAcoth;
var createAcsc = /* #__PURE__ */createNumberFactory('acsc', _index.acscNumber);
exports.createAcsc = createAcsc;
var createAcsch = /* #__PURE__ */createNumberFactory('acsch', _index.acschNumber);
exports.createAcsch = createAcsch;
var createAsec = /* #__PURE__ */createNumberFactory('asec', _index.asecNumber);
exports.createAsec = createAsec;
var createAsech = /* #__PURE__ */createNumberFactory('asech', _index.asechNumber);
exports.createAsech = createAsech;
var createAsin = /* #__PURE__ */createNumberFactory('asin', _index.asinNumber);
exports.createAsin = createAsin;
var createAsinh = /* #__PURE__ */createNumberFactory('asinh', _index.asinhNumber);
exports.createAsinh = createAsinh;
var createAtan = /* #__PURE__ */createNumberFactory('atan', _index.atanNumber);
exports.createAtan = createAtan;
var createAtan2 = /* #__PURE__ */createNumberFactory('atan2', _index.atan2Number);
exports.createAtan2 = createAtan2;
var createAtanh = /* #__PURE__ */createNumberFactory('atanh', _index.atanhNumber);
exports.createAtanh = createAtanh;
var createCos = /* #__PURE__ */createNumberFactory('cos', _index.cosNumber);
exports.createCos = createCos;
var createCosh = /* #__PURE__ */createNumberFactory('cosh', _index.coshNumber);
exports.createCosh = createCosh;
var createCot = /* #__PURE__ */createNumberFactory('cot', _index.cotNumber);
exports.createCot = createCot;
var createCoth = /* #__PURE__ */createNumberFactory('coth', _index.cothNumber);
exports.createCoth = createCoth;
var createCsc = /* #__PURE__ */createNumberFactory('csc', _index.cscNumber);
exports.createCsc = createCsc;
var createCsch = /* #__PURE__ */createNumberFactory('csch', _index.cschNumber);
exports.createCsch = createCsch;
var createSec = /* #__PURE__ */createNumberFactory('sec', _index.secNumber);
exports.createSec = createSec;
var createSech = /* #__PURE__ */createNumberFactory('sech', _index.sechNumber);
exports.createSech = createSech;
var createSin = /* #__PURE__ */createNumberFactory('sin', _index.sinNumber);
exports.createSin = createSin;
var createSinh = /* #__PURE__ */createNumberFactory('sinh', _index.sinhNumber);
exports.createSinh = createSinh;
var createTan = /* #__PURE__ */createNumberFactory('tan', _index.tanNumber);
exports.createTan = createTan;
var createTanh = /* #__PURE__ */createNumberFactory('tanh', _index.tanhNumber); // transforms

exports.createTanh = createTanh;
var createSubsetTransform = /* #__PURE__ */(0, _factory.factory)('subset', [], function () {
  return _noop.noSubset;
}, {
  isTransformFunction: true
});
exports.createSubsetTransform = createSubsetTransform;
var createIsInteger = /* #__PURE__ */createNumberFactory('isInteger', _index.isIntegerNumber);
exports.createIsInteger = createIsInteger;
var createIsNegative = /* #__PURE__ */createNumberFactory('isNegative', _index.isNegativeNumber);
exports.createIsNegative = createIsNegative;
var createIsPositive = /* #__PURE__ */createNumberFactory('isPositive', _index.isPositiveNumber);
exports.createIsPositive = createIsPositive;
var createIsZero = /* #__PURE__ */createNumberFactory('isZero', _index.isZeroNumber);
exports.createIsZero = createIsZero;
var createIsNaN = /* #__PURE__ */createNumberFactory('isNaN', _index.isNaNNumber);
exports.createIsNaN = createIsNaN;

// helper functions to create a factory function for a function which only needs typed-function
function createNumberFactory(name, fn) {
  return (0, _factory.factory)(name, ['typed'], function (_ref) {
    var typed = _ref.typed;
    return typed(fn);
  });
}

function createNumberOptionalSecondArgFactory(name, fn) {
  return (0, _factory.factory)(name, ['typed'], function (_ref2) {
    var typed = _ref2.typed;
    return typed({
      number: fn,
      'number,number': fn
    });
  });
}