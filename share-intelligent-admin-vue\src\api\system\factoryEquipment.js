import request from '@/utils/request'

// 查询工厂设备信息列表
export function listFactoryEquipment(query) {
  return request({
    url: '/system/factoryEquipment/list',
    method: 'get',
    params: query
  })
}

// 查询工厂设备信息详细
export function getFactoryEquipment(id) {
  return request({
    url: '/system/factoryEquipment/' + id,
    method: 'get'
  })
}

// 新增工厂设备信息
export function addFactoryEquipment(data) {
  return request({
    url: '/system/factoryEquipment',
    method: 'post',
    data: data
  })
}

// 修改工厂设备信息
export function updateFactoryEquipment(data) {
  return request({
    url: '/system/factoryEquipment',
    method: 'put',
    data: data
  })
}

// 删除工厂设备信息
export function delFactoryEquipment(id) {
  return request({
    url: '/system/factoryEquipment/' + id,
    method: 'delete'
  })
}
