{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\newsCenter\\index.vue?vue&type=style&index=0&id=6a8080b6&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\newsCenter\\index.vue", "mtime": 1750311962978}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/newsCenter", "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/newsCenter/newsCenterBanner.png\" alt=\"\" />\r\n      <div class=\"bannerTitle\">新闻中心</div>\r\n      <div class=\"bannerDesc\">即时了解行业新闻动态,助力企业高质量迅速发展</div>\r\n    </div>\r\n    <div class=\"content\">\r\n      <div class=\"content_type\">\r\n        <div\r\n          class=\"everyType\"\r\n          v-for=\"item in typeList\"\r\n          :key=\"item.dictValue\"\r\n          @click=\"getItemData(item.dictValue)\"\r\n        >\r\n          <div\r\n            class=\"title\"\r\n            :style=\"item.dictValue == flag ? 'color:#21C9B8' : ''\"\r\n          >\r\n            {{ item.dictLabel }}\r\n          </div>\r\n          <div v-show=\"item.dictValue == flag\" class=\"icon\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content_item\" v-loading=\"loading\">\r\n        <div\r\n          class=\"everyItem\"\r\n          v-for=\"item in newsList\"\r\n          :key=\"item.id\"\r\n          @click=\"goDetail(item.id)\"\r\n        >\r\n          <div class=\"item_left\" v-if=\"item.updateTime\">\r\n            <div class=\"item_year\">{{ item.updateTime.slice(0, 4) }}</div>\r\n            <div class=\"icon_year\"></div>\r\n            <div class=\"item_month\">{{ item.updateTime.slice(5, 10) }}</div>\r\n          </div>\r\n          <div class=\"item_middle\">\r\n            <div class=\"title\">\r\n              {{ item.title }}\r\n            </div>\r\n            <div class=\"desc\">\r\n              {{ item.brief }}\r\n            </div>\r\n          </div>\r\n          <div class=\"item_right\">\r\n            <img :src=\"getPicUrl(item.picUrl)\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div></div>\r\n      <div class=\"activity-page-end\">\r\n        <el-button class=\"activity-page-btn\" @click=\"goHome\">首页</el-button>\r\n        <el-pagination\r\n          v-if=\"newsList && newsList.length > 0\"\r\n          background\r\n          layout=\"prev, pager, next\"\r\n          class=\"activity-pagination\"\r\n          :page-size=\"pageSize\"\r\n          :current-page=\"pageNum\"\r\n          :total=\"total\"\r\n          @current-change=\"handleCurrentChange\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import { getActivityList } from \"@/api/purchaseSales\";\r\nimport { newsType, newsList } from \"@/api/newsCenter\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { caseList } from \"@/api/classicCase\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      loading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        caseType: \"\", // 案例类型\r\n      },\r\n      caseTypeList: [],\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      solutionTypeList: [\r\n        {\r\n          value: \"0\",\r\n          label: \"全部\",\r\n        },\r\n        {\r\n          value: \"1\",\r\n          label: \"节能减排\",\r\n        },\r\n        {\r\n          value: \"2\",\r\n          label: \"低碳认证\",\r\n        },\r\n        {\r\n          value: \"3\",\r\n          label: \"数据核算\",\r\n        },\r\n        {\r\n          value: \"4\",\r\n          label: \"中和服务\",\r\n        },\r\n        {\r\n          value: \"5\",\r\n          label: \"星碳培训\",\r\n        },\r\n        {\r\n          value: \"6\",\r\n          label: \"绿色会议\",\r\n        },\r\n        {\r\n          value: \"7\",\r\n          label: \"数据建模\",\r\n        },\r\n        {\r\n          value: \"8\",\r\n          label: \"资产管理\",\r\n        },\r\n      ],\r\n      flag: \"1\",\r\n      typeList: [\r\n        {\r\n          dictValue: \"1\",\r\n          dictLabel: \"平台动态\",\r\n        },\r\n        {\r\n          dictValue: \"2\",\r\n          dictLabel: \"行业动态\",\r\n        },\r\n        {\r\n          dictValue: \"3\",\r\n          dictLabel: \"政策法规\",\r\n        },\r\n      ],\r\n      newsList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n    // this.getDictsList(\"activity_type\", \"activityTypeList\");\r\n    // this.search();\r\n  },\r\n  methods: {\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.initData();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.initData();\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    initData() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        kind: \"0\",\r\n        typeTop: this.flag,\r\n      };\r\n      newsList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res.rows, \"----------------\");\r\n          this.newsList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n      // newsType().then((res) => {\r\n      //   if (res.code === 200) {\r\n      //     console.log(res, \"-----------------\");\r\n      //   }\r\n      // });\r\n      // getDicts(\"case_industry\").then((res) => {\r\n      //   const { code, data = [] } = res;\r\n      //   if (code === 200) {\r\n      //     this.caseTypeList = data;\r\n      //     this.getCaseList();\r\n      //   }\r\n      // });\r\n    },\r\n    getItemData(value) {\r\n      this.flag = value;\r\n      this.initData();\r\n    },\r\n    goDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/newsDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    getPicUrl(picUrl) {\r\n      try {\r\n        let data = JSON.parse(picUrl);\r\n        return data[0].url;\r\n      } catch (error) {\r\n        return picUrl;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #ffffff;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 500px;\r\n    position: relative;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    .bannerTitle {\r\n      position: absolute;\r\n      top: 161px;\r\n      left: 24%;\r\n      font-size: 50px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n    }\r\n    .bannerDesc {\r\n      position: absolute;\r\n      top: 249px;\r\n      left: 24%;\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .content {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    .content_type {\r\n      display: flex;\r\n      width: 100%;\r\n      margin-bottom: 30px;\r\n      .everyType {\r\n        width: 110px;\r\n        text-align: center;\r\n        margin-left: 66px;\r\n        cursor: pointer;\r\n        .title {\r\n          font-size: 18px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #333333;\r\n        }\r\n        .icon {\r\n          width: 110px;\r\n          height: 4px;\r\n          background: #21c9b8;\r\n          margin-top: 30px;\r\n        }\r\n      }\r\n      .everyType:nth-child(1) {\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n    .content_item {\r\n      width: 100%;\r\n      .everyItem {\r\n        display: flex;\r\n        width: 100%;\r\n        height: 230px;\r\n        background: #ffffff;\r\n        box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n        padding: 45px 50px;\r\n        margin-top: 30px;\r\n        cursor: pointer;\r\n        .item_left {\r\n          margin-top: 43px;\r\n          width: 53px;\r\n          .item_year {\r\n            font-size: 24px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            color: #222222;\r\n          }\r\n          .icon_year {\r\n            width: 58px;\r\n            height: 2px;\r\n            background: #21c9b8;\r\n            margin-top: 2px;\r\n          }\r\n          .item_month {\r\n            font-size: 18px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            color: #222222;\r\n            margin-top: 5px;\r\n          }\r\n        }\r\n        .item_middle {\r\n          width: 710px;\r\n          margin-left: 40px;\r\n          .title {\r\n            font-size: 18px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            color: #222222;\r\n            margin-top: 31px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 1;\r\n            text-overflow: ellipsis;\r\n            word-wrap: break-word;\r\n          }\r\n          .desc {\r\n            font-size: 16px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            color: #65676a;\r\n            margin-top: 17px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            text-overflow: ellipsis;\r\n            word-wrap: break-word;\r\n          }\r\n        }\r\n        .item_right {\r\n          width: 200px;\r\n          height: 140px;\r\n          margin-left: 85px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n      .everyItem:hover {\r\n        box-shadow: 0px 4px 20px 0px rgba(58, 180, 118, 0.3);\r\n        .item_year {\r\n          color: #21c9b8;\r\n        }\r\n        .title {\r\n          color: #21c9b8;\r\n        }\r\n      }\r\n      .everyItem:nth-child(1) {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n    .activity-page-end {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin: 0 auto;\r\n      padding: 24px 0 60px;\r\n      .activity-page-btn {\r\n        width: 82px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    padding: 8% 0;\r\n    background: #fff;\r\n    margin-top: 25px;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}