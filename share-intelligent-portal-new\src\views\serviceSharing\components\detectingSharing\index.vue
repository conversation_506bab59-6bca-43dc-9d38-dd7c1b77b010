<template>
  <div class="content">
    <div class="content_banner">检验检测</div>
    <div class="card-container">
      <div class="content_top">
        <div
          class="content_top_item"
          :class="currentMenu == index ? 'active' : ''"
          v-for="(item, index) in deviceMenuList"
          :key="index"
          @click="handleClick(index)"
        >
          <div class="imgStyle">
            <img
              :src="currentMenu == index ? item.hoverUrl : item.url"
              alt=""
            />
          </div>
          <div :class="currentMenu == index ? 'titleActive' : 'title'">
            {{ item.dictLabel }}
          </div>
        </div>
      </div>
      <div class="content_bottom" v-loading="loading">
        <div
          class="content_list"
          v-if="deviceDataList && deviceDataList.length > 0"
        >
          <div
            class="content_bottom_item tr2"
            v-for="(item, index) in deviceDataList"
            :key="index"
            @click="workShopDetail(item)"
          >
            <span class="bottom"></span>
            <span class="right"></span>
            <span class="top"></span>
            <span class="left"></span>
            <div class="imgStyle">
              <img
                style="width: 100%; height: 100%"
                :src="item.url ? item.url[0].url : defaultImg"
                alt=""
              />
            </div>
            <div class="title">
              {{ item.itemName }}
            </div>
            <div class="companyStyle">
              <div class="companyName">{{ item.labName }}</div>
            </div>
          </div>
        </div>
        <div class="content_empty" v-else>
          <el-empty description="暂无数据"></el-empty>
        </div>
        <!-- 分页 -->
        <div class="pageStyle">
          <el-pagination
            v-if="deviceDataList && deviceDataList.length > 0"
            background
            layout="prev, pager, next"
            class="activity-pagination"
            :page-size="pageSize"
            :current-page="pageNum"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { getDetectionList } from "@/api/serviceSharing";
export default {
  name: "deviceSharing",
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 10,
      total: 1,
      currentMenu: 0,
      deviceMenuList: [
        {
          url: require("@/assets/detectingSharing/power.png"),
          hoverUrl: require("@/assets/detectingSharing/power_active.png"),
        },
        {
          url: require("@/assets/detectingSharing/old.png"),
          hoverUrl: require("@/assets/detectingSharing/old_active.png"),
        },
        {
          url: require("@/assets/detectingSharing/tiwenji.png"),
          hoverUrl: require("@/assets/detectingSharing/tiwenji_active.png"),
        },
        {
          url: require("@/assets/detectingSharing/other.png"),
          hoverUrl: require("@/assets/detectingSharing/other_active.png"),
        },
        {
          url: require("@/assets/detectingSharing/other.png"),
          hoverUrl: require("@/assets/detectingSharing/other_active.png"),
        },
        {
          url: require("@/assets/detectingSharing/other.png"),
          hoverUrl: require("@/assets/detectingSharing/other_active.png"),
        },
        {
          url: require("@/assets/detectingSharing/other.png"),
          hoverUrl: require("@/assets/detectingSharing/other_active.png"),
        },
        {
          url: require("@/assets/detectingSharing/other.png"),
          hoverUrl: require("@/assets/detectingSharing/other_active.png"),
        },
      ],
      deviceDataList: [
        {
          url: require("@/assets/detectingSharing/defult.png"),
          title: "纤维老化度测试检验",
          companyName: "北京融创科技实验室",
        },
        {
          url: require("@/assets/detectingSharing/defult.png"),
          title: "纤维老化度测试检验",
          companyName: "北京融创科技实验室",
        },
        {
          url: require("@/assets/detectingSharing/defult.png"),
          title: "纤维老化度测试检验",
          companyName: "北京融创科技实验室",
        },
        {
          url: require("@/assets/detectingSharing/defult.png"),
          title: "纤维老化度测试检验",
          companyName: "北京融创科技实验室",
        },
        {
          url: require("@/assets/detectingSharing/defult.png"),
          title: "纤维老化度测试检验",
          companyName: "北京融创科技实验室",
        },
        {
          url: require("@/assets/detectingSharing/defult.png"),
          title: "纤维老化度测试检验",
          companyName: "北京融创科技实验室",
        },
        {
          url: require("@/assets/detectingSharing/defult.png"),
          title: "纤维老化度测试检验",
          companyName: "北京融创科技实验室",
        },
        {
          url: require("@/assets/detectingSharing/defult.png"),
          title: "纤维老化度测试检验",
          companyName: "北京融创科技实验室",
        },
      ],
      defaultImg: require("@/assets/detectingSharing/defult.png"),
    };
  },
  created() {
    this.getList();
    this.getDicts();
  },
  methods: {
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        labType: this.currentMenu,
      };
      getDetectionList(params).then((response) => {
        if (response.code == 200 && response.rows.length > 0) {
          this.deviceDataList = [];
          response.rows.forEach((item) => {
            let labName = item.labs[0].labName;
            let labId = item.labs[0].id;
            this.deviceDataList.push({ ...item.testingItem, labName, labId });
          });
          console.log(this.deviceDataList, "res");
          this.total = response.total;
          this.loading = false;
        } else {
          this.deviceDataList = [];
          this.total = 0;
          this.loading = false;
        }
      });
    },
    getDicts() {
      let params = { dictType: "lab_type" };
      listData(params).then((response) => {
        console.log(response, "res");
        if (response.code == 200) {
          let temp = { ...this.deviceMenuList };
          this.deviceMenuList = response.rows;
          this.deviceMenuList = this.deviceMenuList.map((item, index) => {
            return { ...item, ...temp[index] };
          });
        }
      });
    },
    handleClick(index) {
      this.currentMenu = index;
      this.getList();
    },
    workShopDetail(item) {
      this.$router.push(
        "/detectingSharingDetail?id=" + item.id + "&labId=" + item.labId
      );
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  padding-bottom: 60px;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../../assets/release/banner.png");
  background-size: 100% 100%;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
  text-align: center;
  line-height: 300px;
}

.content_top {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 40px;

  .content_top_item {
    width: 275px;
    height: 68px;
    background: #ffffff;
    box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 26px;

    .imgStyle {
      width: 27px;
      height: 27px;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #333333;
      margin-left: 20px;
    }

    .titleActive {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
      margin-left: 20px;
    }
  }

  .content_top_item:nth-child(1) {
    margin-left: 0;
  }

  .active {
    background-color: #0cad9d;
  }
}

.content_bottom {
  margin-top: 40px;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-around;

  .content_list {
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: start;
  }

  .content_empty {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .content_bottom_item {
    width: 280px;
    height: 290px;
    background: #f8f8fa;
    box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    border: 2px solid #ffffff;
    padding: 20px;
    cursor: pointer;
    margin-right: 25px;
    position: relative;
    span {
      position: absolute;
      z-index: 1;
      background-color: #37c9b8;
      transition: transform 0.5s ease;
    }
    .bottom,
    .top {
      height: 2px;
      left: -1px;
      right: -1px;
      transform: scaleX(0);
    }
    .left,
    .right {
      width: 2px;
      top: -1px;
      bottom: -1px;
      transform: scaleY(0);
    }
    .bottom {
      bottom: -1px;
      transform-origin: bottom right;
    }
    .right {
      right: -1px;
      transform-origin: top right;
    }
    .top {
      top: -1px;
      transform-origin: top left;
    }
    .left {
      left: -1px;
      transform-origin: bottom left;
    }
    .title {
      height: 18px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #000000;
      margin-top: 17px;
      margin-bottom: 14px;
    }

    .companyStyle {
      display: flex;
      align-items: center;
    }

    .companyName {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #7e7e7e;
    }
  }

  .content_bottom_item:nth-child(n + 5) {
    margin-top: 26px;
  }

  .content_bottom_item:nth-child(4n) {
    margin-right: 0px;
  }

  .content_bottom_item:hover {
    background-color: #ffffff;
    box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.1);
    .top {
      transform-origin: top right;
      transform: scaleX(1);
    }
    .left {
      transform-origin: top left;
      transform: scaleY(1);
    }
    .bottom {
      transform-origin: bottom left;
      transform: scaleX(1);
    }
    .right {
      transform-origin: bottom right;
      transform: scaleY(1);
    }
  }

  .pageStyle {
    margin-top: 61px;
  }
}
</style>
