{"version": 3, "file": "merge.js", "sources": ["../../src/internal/observable/merge.ts"], "names": [], "mappings": ";;AAAA,4CAA2C;AAE3C,mDAAkD;AAClD,kDAAiD;AACjD,yCAAwC;AAqHxC,SAAgB,KAAK;IAAO,qBAAoE;SAApE,UAAoE,EAApE,qBAAoE,EAApE,IAAoE;QAApE,gCAAoE;;IAC/F,IAAI,UAAU,GAAG,MAAM,CAAC,iBAAiB,CAAC;IAC1C,IAAI,SAAS,GAAkB,IAAI,CAAC;IACnC,IAAI,IAAI,GAAQ,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACpD,IAAI,yBAAW,CAAC,IAAI,CAAC,EAAE;QACrB,SAAS,GAAkB,WAAW,CAAC,GAAG,EAAE,CAAC;QAC7C,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,QAAQ,EAAE;YACrF,UAAU,GAAW,WAAW,CAAC,GAAG,EAAE,CAAC;SACxC;KACF;SAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;QACnC,UAAU,GAAW,WAAW,CAAC,GAAG,EAAE,CAAC;KACxC;IAED,IAAI,SAAS,KAAK,IAAI,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,YAAY,uBAAU,EAAE;QAC1F,OAAsB,WAAW,CAAC,CAAC,CAAC,CAAC;KACtC;IAED,OAAO,mBAAQ,CAAI,UAAU,CAAC,CAAC,qBAAS,CAAM,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;AACzE,CAAC;AAlBD,sBAkBC"}