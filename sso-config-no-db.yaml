# 服务端口配置
server:
  port: 9100
  servlet:
    context-path: /

# Spring配置
spring:
  # Redis配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 1
    # 密码
    password: 
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# SSO服务配置
sso:
  # 服务基本信息
  service:
    name: SSO认证服务
    version: 1.0.0
    description: 统一单点登录认证服务
  
  # 支持的客户端系统
  clients:
    backend:
      name: 复合材料共享智造平台
      url: http://localhost:9200
      callback-url: http://localhost:9200/sso/callback
      database: industry
      secret: backend_secret_key_2024
      enabled: true
    market:
      name: 智能市场系统
      url: http://localhost:8081
      callback-url: http://localhost:8081/sso/callback
      database: market
      secret: market_secret_key_2024
      enabled: true
  
  # Token配置
  token:
    # SSO Token有效期（分钟）
    expire-minutes: 30
    # 访问Token有效期（分钟）
    access-token-expire-minutes: 480
    # 刷新Token有效期（天）
    refresh-token-expire-days: 7
    # Token签名密钥
    secret: sso_jwt_secret_key_2024_ruoyi_share_intelligent
    # Token前缀
    prefix: "sso_token:"
    access-prefix: "access_token:"
    refresh-prefix: "refresh_token:"
  
  # 会话配置
  session:
    # 会话超时时间（分钟）
    timeout-minutes: 480
    # 最大并发会话数
    max-sessions: 1
    # 会话前缀
    prefix: "sso_session:"
  
  # 安全配置
  security:
    # 是否启用CSRF保护
    csrf-enabled: false
    # 允许的跨域来源
    allowed-origins:
      - http://localhost:9200
      - http://localhost:8081
      - http://localhost:80
      - http://127.0.0.1:9200
      - http://127.0.0.1:8081
      - http://127.0.0.1:80
    # 登录失败最大尝试次数
    max-login-attempts: 5
    # 登录失败锁定时间（分钟）
    lockout-duration-minutes: 15
  
  # 缓存配置
  cache:
    # Redis前缀
    redis-prefix: "sso:"
    # 缓存过期时间（秒）
    default-expire-seconds: 3600

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework.security: debug
    root: info
  config: classpath:logback-spring.xml

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
