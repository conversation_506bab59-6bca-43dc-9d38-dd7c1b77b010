/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
import { addDependencies } from './dependenciesAdd.generated.js';
import { applyDependencies } from './dependenciesApply.generated.js';
import { divideDependencies } from './dependenciesDivide.generated.js';
import { isNaNDependencies } from './dependenciesIsNaN.generated.js';
import { multiplyDependencies } from './dependenciesMultiply.generated.js';
import { subtractDependencies } from './dependenciesSubtract.generated.js';
import { typedDependencies } from './dependenciesTyped.generated.js';
import { createVarianceTransform } from '../../factoriesNumber.js';
export var varianceTransformDependencies = {
  addDependencies,
  applyDependencies,
  divideDependencies,
  isNaNDependencies,
  multiplyDependencies,
  subtractDependencies,
  typedDependencies,
  createVarianceTransform
};