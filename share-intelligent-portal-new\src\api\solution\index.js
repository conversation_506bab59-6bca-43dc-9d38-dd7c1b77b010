import request from "@/utils/request";


// 解决方案-列表
export function getSolutionList(params) {
  return request({
    url: `/portalweb/solution/listDesk`,
    method: "get",
    params,
  });
}
// 获取解决方案二级菜单列表
export function getSolutionTypeList(params) {
  return request({
    url: `/portalweb/solutionType/listSolution`,
    method: "get",
    params,
  });
}

// 解决方案详情
export function solutionDetail(params) {
  return request({
    url: `/portalweb/solution/detailDesk`,
    method: "get",
    params,
  });
}
// 获取解决方案dict
export function solutionDicts(params) {
  return request({
    url: `system/dict/data/type/xp_require_category`,
    method: "get",
    params,
  });
}

