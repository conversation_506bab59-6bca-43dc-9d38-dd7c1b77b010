{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\serviceSharing\\components\\talentPool\\index copy.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\serviceSharing\\components\\talentPool\\index copy.vue", "mtime": 1750311963013}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_data", "require", "_serviceSharing", "data", "loading", "pageNum", "pageSize", "total", "postCategory", "educational", "technicalTitle", "workingCondition", "talentList", "positionTypeList", "educationList", "jobTitleList", "workStatusList", "fit", "created", "getPositionType", "getEducation", "getJobTitle", "getWorkStatus", "getList", "methods", "_this", "params", "dictType", "listData", "then", "response", "rows", "unshift", "dict<PERSON><PERSON>ue", "dict<PERSON><PERSON>l", "_this2", "_this3", "_this4", "_this5", "positionType", "education", "jobTitle", "workStatus", "settledStatus", "talentListData", "res", "code", "joinNow", "$router", "push", "path", "switchPostCategory", "index", "switchEducational", "switchTechnicalTitle", "switchWorkingCondition", "handleSizeChange", "handleCurrentChange", "goDetail", "id", "initList", "resetData"], "sources": ["src/views/serviceSharing/components/talentPool/index copy.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      人才服务\r\n      <div class=\"imgContent\">\r\n        <div class=\"imgStyle\">\r\n          <img\r\n            style=\"width: 100%; height: 100%\"\r\n            src=\"../../../../assets/order/orderStep.png\"\r\n            alt=\"\"\r\n          />\r\n          <div class=\"joinNow\" @click=\"joinNow\">立即入驻</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container card_top\">\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">岗位分类：</div>\r\n        <div\r\n          class=\"smallCategory\"\r\n          :class=\"postCategory === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in positionTypeList\"\r\n          :key=\"index\"\r\n          @click=\"switchPostCategory(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">最高学历：</div>\r\n        <div\r\n          class=\"smallCategory\"\r\n          :class=\"educational === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in educationList\"\r\n          :key=\"index\"\r\n          @click=\"switchEducational(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">职 称：</div>\r\n        <div\r\n          class=\"smallCategory\"\r\n          :class=\"\r\n            technicalTitle === item.dictValue ? 'smallCategoryActive' : ''\r\n          \"\r\n          v-for=\"(item, index) in jobTitleList\"\r\n          :key=\"index\"\r\n          @click=\"switchTechnicalTitle(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">工作状态：</div>\r\n        <div\r\n          class=\"smallCategory\"\r\n          :class=\"\r\n            workingCondition === item.dictValue ? 'smallCategoryActive' : ''\r\n          \"\r\n          v-for=\"(item, index) in workStatusList\"\r\n          :key=\"index\"\r\n          @click=\"switchWorkingCondition(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"buttonStyle\">\r\n        <div class=\"imgStyle\" @click=\"initList\">\r\n          <img\r\n            style=\"width: 100%; height: 100%\"\r\n            src=\"../../../../assets/serviceSharing/reset.png\"\r\n            alt=\"\"\r\n          />\r\n        </div>\r\n        <div class=\"buttonText\" @click=\"resetData\">重置筛选</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container card_bottom\">\r\n      <div\r\n        class=\"content_bottom\"\r\n        v-loading=\"loading\"\r\n        v-if=\"talentList && talentList.length > 0\"\r\n      >\r\n        <div\r\n          class=\"card_bottom_item tr2\"\r\n          v-for=\"(item, index) in talentList\"\r\n          :key=\"index\"\r\n          @click=\"goDetail(item.id)\"\r\n        >\r\n          <span class=\"bottom\"></span>\r\n          <span class=\"right\"></span>\r\n          <span class=\"top\"></span>\r\n          <span class=\"left\"></span>\r\n          <!-- 左侧图片 -->\r\n          <div class=\"imgStyle\">\r\n            <img\r\n              style=\"width: 100%; height: 100%\"\r\n              :src=\"\r\n                item.photo\r\n                  ? item.photo\r\n                  : require('../../../../assets/serviceSharing/ceshi.png')\r\n              \"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <!-- 右侧内容 -->\r\n          <div class=\"content_bottom_right\">\r\n            <div class=\"title ellips1\">{{ item.name }}</div>\r\n            <div class=\"category\" v-if=\"item.education\">\r\n              {{\r\n                educationList.filter(\r\n                  (item1) => item1.dictValue == item.education\r\n                )[0].dictLabel\r\n              }}\r\n            </div>\r\n            <div class=\"statusStyle\">\r\n              {{ item.workStatus == \"0\" ? \"在职\" : \"离职\" }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"none-class\" v-else>\r\n        <el-image\r\n          style=\"width: 160px; height: 160px\"\r\n          :src=\"require('@/assets/user/none.png')\"\r\n          :fit=\"fit\"\r\n        ></el-image>\r\n        <div class=\"text\">暂无数据</div>\r\n      </div>\r\n      <!-- 分页 -->\r\n      <div class=\"pageStyle\">\r\n        <el-pagination\r\n          v-if=\"talentList && talentList.length > 0\"\r\n          background\r\n          layout=\"prev, pager, next\"\r\n          class=\"activity-pagination\"\r\n          :page-size=\"pageSize\"\r\n          :current-page=\"pageNum\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { talentListData } from \"@/api/serviceSharing\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageNum: 1,\r\n      pageSize: 16,\r\n      total: 0,\r\n      postCategory: \"\",\r\n      educational: \"\",\r\n      technicalTitle: \"\",\r\n      workingCondition: \"\",\r\n      talentList: [],\r\n      positionTypeList: [], // 岗位分类\r\n      educationList: [], // 最高学历\r\n      jobTitleList: [], // 职称\r\n      workStatusList: [], // 工作状态\r\n      fit: \"cover\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getPositionType();\r\n    this.getEducation();\r\n    this.getJobTitle();\r\n    this.getWorkStatus();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    // 岗位分类\r\n    getPositionType() {\r\n      let params = { dictType: \"position_type\" };\r\n      listData(params).then((response) => {\r\n        this.positionTypeList = response.rows;\r\n        this.positionTypeList.unshift({\r\n          dictValue: \"\",\r\n          dictLabel: \"全部\",\r\n        });\r\n      });\r\n    },\r\n    // 最高学历\r\n    getEducation() {\r\n      let params = { dictType: \"education\" };\r\n      listData(params).then((response) => {\r\n        this.educationList = response.rows;\r\n        this.educationList.unshift({\r\n          dictValue: \"\",\r\n          dictLabel: \"全部\",\r\n        });\r\n      });\r\n    },\r\n    // 职称\r\n    getJobTitle() {\r\n      let params = { dictType: \"job_title\" };\r\n      listData(params).then((response) => {\r\n        this.jobTitleList = response.rows;\r\n        this.jobTitleList.unshift({\r\n          dictValue: \"\",\r\n          dictLabel: \"全部\",\r\n        });\r\n      });\r\n    },\r\n    // 工作状态\r\n    getWorkStatus() {\r\n      let params = { dictType: \"work_status\" };\r\n      listData(params).then((response) => {\r\n        this.workStatusList = response.rows;\r\n        this.workStatusList.unshift({\r\n          dictValue: \"\",\r\n          dictLabel: \"全部\",\r\n        });\r\n      });\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        positionType: this.postCategory,\r\n        education: this.educational,\r\n        jobTitle: this.technicalTitle,\r\n        workStatus: this.workingCondition,\r\n        settledStatus: \"1\",\r\n      };\r\n      talentListData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.talentList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    joinNow() {\r\n      this.$router.push({\r\n        path: \"/talentJoinNow\",\r\n      });\r\n    },\r\n    switchPostCategory(index) {\r\n      this.postCategory = index;\r\n      this.getList();\r\n    },\r\n    switchEducational(index) {\r\n      this.educational = index;\r\n      this.getList();\r\n    },\r\n    switchTechnicalTitle(index) {\r\n      this.technicalTitle = index;\r\n      this.getList();\r\n    },\r\n    switchWorkingCondition(index) {\r\n      this.workingCondition = index;\r\n      this.getList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push(\"/talentDetail?id=\" + id);\r\n    },\r\n    initList() {\r\n      this.getList();\r\n    },\r\n    resetData() {\r\n      this.postCategory = \"\";\r\n      this.educational = \"\";\r\n      this.technicalTitle = \"\";\r\n      this.workingCondition = \"\";\r\n      this.getList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n  background-color: #f2f2f2;\r\n}\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 28px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n  .imgContent {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-top: 10px;\r\n    .imgStyle {\r\n      width: 1256px;\r\n      height: 206px;\r\n      position: relative;\r\n      .joinNow {\r\n        position: absolute;\r\n        right: 90px;\r\n        top: 75px;\r\n        width: 110px;\r\n        height: 50px;\r\n        background: #f79a47;\r\n        border-radius: 2px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 18px;\r\n        color: #ffffff;\r\n        line-height: 50px;\r\n        text-align: center;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n}\r\n.card_top {\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: 30px;\r\n  padding: 30px;\r\n  // padding: 58px 60px 32px 62px;\r\n  .card_top_item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    .largeCategory {\r\n      width: 90px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #222222;\r\n      margin-right: 28px;\r\n    }\r\n    .smallCategory {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n      padding: 12px 24px;\r\n      cursor: pointer;\r\n    }\r\n    .smallCategoryActive {\r\n      background: #e0f7f5;\r\n      border-radius: 2px;\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n  .card_top_item:nth-child(1) {\r\n    margin-top: 0;\r\n  }\r\n  .card_top_itemLine {\r\n    width: 100%;\r\n    height: 1px;\r\n    background: #eeeeee;\r\n    margin-top: 20px;\r\n  }\r\n  .buttonStyle {\r\n    margin-top: 9px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    .imgStyle {\r\n      width: 19px;\r\n      height: 16px;\r\n      cursor: pointer;\r\n    }\r\n    .buttonText {\r\n      margin-left: 10px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #21c9b8;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n.card_bottom {\r\n  height: 850px;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: 30px;\r\n  padding: 40px 60px 62px 60px;\r\n  .content_bottom {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    .card_bottom_item {\r\n      width: 255px;\r\n      height: 150px;\r\n      background: #ffffff;\r\n      box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.06);\r\n      border-radius: 4px;\r\n      border: 2px solid #ffffff;\r\n      margin-left: 20px;\r\n      padding: 20px 18px;\r\n      display: flex;\r\n      cursor: pointer;\r\n\r\n      span {\r\n        position: absolute;\r\n        z-index: 1;\r\n        background-color: #37c9b8;\r\n        transition: transform 0.5s ease;\r\n      }\r\n      .bottom,\r\n      .top {\r\n        height: 2px;\r\n        left: -1px;\r\n        right: -1px;\r\n        transform: scaleX(0);\r\n      }\r\n      .left,\r\n      .right {\r\n        width: 2px;\r\n        top: -1px;\r\n        bottom: -1px;\r\n        transform: scaleY(0);\r\n      }\r\n      .bottom {\r\n        bottom: -1px;\r\n        transform-origin: bottom right;\r\n      }\r\n      .right {\r\n        right: -1px;\r\n        transform-origin: top right;\r\n      }\r\n      .top {\r\n        top: -1px;\r\n        transform-origin: top left;\r\n      }\r\n      .left {\r\n        left: -1px;\r\n        transform-origin: bottom left;\r\n      }\r\n      .imgStyle {\r\n        width: 90px;\r\n        height: 108px;\r\n      }\r\n      .content_bottom_right {\r\n        margin-left: 17px;\r\n      }\r\n      .title {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 18px;\r\n        color: #000000;\r\n        height: 24px;\r\n      }\r\n      .category {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 16px;\r\n        color: #7e7e7e;\r\n        margin-top: 14px;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 1;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n      .statusStyle {\r\n        width: 70px;\r\n        height: 30px;\r\n        margin-top: 22px;\r\n        background: #e0f7f5;\r\n        border-radius: 2px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #21c9b8;\r\n        line-height: 450px;\r\n        text-align: center;\r\n        line-height: 30px;\r\n      }\r\n    }\r\n    .card_bottom_item:nth-child(4n + 1) {\r\n      margin-left: 0;\r\n    }\r\n    .card_bottom_item:nth-child(n + 5) {\r\n      margin-top: 20px;\r\n    }\r\n    .card_bottom_item:hover {\r\n      box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);\r\n      scale: 1.01;\r\n      .top {\r\n        transform-origin: top right;\r\n        transform: scaleX(1);\r\n      }\r\n      .left {\r\n        transform-origin: top left;\r\n        transform: scaleY(1);\r\n      }\r\n      .bottom {\r\n        transform-origin: bottom left;\r\n        transform: scaleX(1);\r\n      }\r\n      .right {\r\n        transform-origin: bottom right;\r\n        transform: scaleY(1);\r\n      }\r\n    }\r\n  }\r\n  .pageStyle {\r\n    margin-top: 60px;\r\n    width: 100%;\r\n    text-align: center;\r\n  }\r\n}\r\n.none-class {\r\n  text-align: center;\r\n  padding: 8% 0;\r\n  .text {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #999999;\r\n    line-height: 14px;\r\n  }\r\n}\r\n.ellips1 {\r\n  overflow: hidden;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 1;\r\n  text-overflow: ellipsis;\r\n  word-wrap: break-word;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAyJA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,YAAA;MACAC,WAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,UAAA;MACAC,gBAAA;MAAA;MACAC,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;MAAA;MACAC,GAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,eAAA;IACA,KAAAC,YAAA;IACA,KAAAC,WAAA;IACA,KAAAC,aAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAL,eAAA,WAAAA,gBAAA;MAAA,IAAAM,KAAA;MACA,IAAAC,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAAZ,gBAAA,GAAAiB,QAAA,CAAAC,IAAA;QACAN,KAAA,CAAAZ,gBAAA,CAAAmB,OAAA;UACAC,SAAA;UACAC,SAAA;QACA;MACA;IACA;IACA;IACAd,YAAA,WAAAA,aAAA;MAAA,IAAAe,MAAA;MACA,IAAAT,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAK,MAAA,CAAArB,aAAA,GAAAgB,QAAA,CAAAC,IAAA;QACAI,MAAA,CAAArB,aAAA,CAAAkB,OAAA;UACAC,SAAA;UACAC,SAAA;QACA;MACA;IACA;IACA;IACAb,WAAA,WAAAA,YAAA;MAAA,IAAAe,MAAA;MACA,IAAAV,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAM,MAAA,CAAArB,YAAA,GAAAe,QAAA,CAAAC,IAAA;QACAK,MAAA,CAAArB,YAAA,CAAAiB,OAAA;UACAC,SAAA;UACAC,SAAA;QACA;MACA;IACA;IACA;IACAZ,aAAA,WAAAA,cAAA;MAAA,IAAAe,MAAA;MACA,IAAAX,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAO,MAAA,CAAArB,cAAA,GAAAc,QAAA,CAAAC,IAAA;QACAM,MAAA,CAAArB,cAAA,CAAAgB,OAAA;UACAC,SAAA;UACAC,SAAA;QACA;MACA;IACA;IACAX,OAAA,WAAAA,QAAA;MAAA,IAAAe,MAAA;MACA,KAAAlC,OAAA;MACA,IAAAsB,MAAA;QACArB,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA,QAAA;QACAiC,YAAA,OAAA/B,YAAA;QACAgC,SAAA,OAAA/B,WAAA;QACAgC,QAAA,OAAA/B,cAAA;QACAgC,UAAA,OAAA/B,gBAAA;QACAgC,aAAA;MACA;MACA,IAAAC,8BAAA,EAAAlB,MAAA,EAAAG,IAAA,WAAAgB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAR,MAAA,CAAA1B,UAAA,GAAAiC,GAAA,CAAAd,IAAA;UACAO,MAAA,CAAA/B,KAAA,GAAAsC,GAAA,CAAAtC,KAAA;UACA+B,MAAA,CAAAlC,OAAA;QACA;MACA;IACA;IACA2C,OAAA,WAAAA,QAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAC,IAAA;MACA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,KAAA;MACA,KAAA5C,YAAA,GAAA4C,KAAA;MACA,KAAA7B,OAAA;IACA;IACA8B,iBAAA,WAAAA,kBAAAD,KAAA;MACA,KAAA3C,WAAA,GAAA2C,KAAA;MACA,KAAA7B,OAAA;IACA;IACA+B,oBAAA,WAAAA,qBAAAF,KAAA;MACA,KAAA1C,cAAA,GAAA0C,KAAA;MACA,KAAA7B,OAAA;IACA;IACAgC,sBAAA,WAAAA,uBAAAH,KAAA;MACA,KAAAzC,gBAAA,GAAAyC,KAAA;MACA,KAAA7B,OAAA;IACA;IACAiC,gBAAA,WAAAA,iBAAAlD,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAiB,OAAA;IACA;IACAkC,mBAAA,WAAAA,oBAAApD,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAkB,OAAA;IACA;IACAmC,QAAA,WAAAA,SAAAC,EAAA;MACA,KAAAX,OAAA,CAAAC,IAAA,uBAAAU,EAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAArC,OAAA;IACA;IACAsC,SAAA,WAAAA,UAAA;MACA,KAAArD,YAAA;MACA,KAAAC,WAAA;MACA,KAAAC,cAAA;MACA,KAAAC,gBAAA;MACA,KAAAY,OAAA;IACA;EACA;AACA", "ignoreList": []}]}