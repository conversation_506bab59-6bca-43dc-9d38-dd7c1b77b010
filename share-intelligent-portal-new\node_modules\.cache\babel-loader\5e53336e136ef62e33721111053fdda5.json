{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\system\\demand.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\system\\demand.js", "mtime": 1750311961346}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "getDemandList", "params", "request", "url", "method", "getDemandDetail", "id", "checkManagerAuth", "checkSmsCode", "createDemand", "data", "edit<PERSON><PERSON><PERSON>", "applyRefuse", "userId", "editPolicyApply", "askResignation"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/api/system/demand.js"], "sourcesContent": ["/*\r\n * @Author: zhc\r\n * @Date: 2023-02-11 14:45:07\r\n * @LastEditTime: 2023-02-20 19:00:23\r\n * @Description:\r\n * @LastEditors: zhc\r\n */\r\n\r\nimport request from \"@/utils/request\";\r\n\r\n// 需求列表查询\r\nexport function getDemandList(params) {\r\n  return request({\r\n    url: \"/system/demand/secret/list\",\r\n    method: \"get\",\r\n    params: params,\r\n  });\r\n}\r\n// 需求详情\r\nexport function getDemandDetail(id) {\r\n  return request({\r\n    url: `/system/demand/secret/` + id,\r\n    method: \"get\",\r\n  });\r\n}\r\n\r\n// 请离、管理员转移按钮权限判断\r\nexport function checkManagerAuth() {\r\n  return request({\r\n    url: \"/system/company/apply/checkManagerAuth\",\r\n    method: \"get\",\r\n  });\r\n}\r\n\r\n// 验证手机验证码\r\nexport function checkSmsCode(params) {\r\n  return request({\r\n    url: \"/system/company/apply/checkSmsCode\",\r\n    method: \"get\",\r\n    params: params,\r\n  });\r\n}\r\n// 新增需求\r\nexport function createDemand(params) {\r\n  return request({\r\n    url: \"/system/demand\",\r\n    method: \"post\",\r\n    data: params,\r\n  });\r\n}\r\n// 新增需求\r\nexport function editDemand(params) {\r\n  return request({\r\n    url: \"/system/demand\",\r\n    method: \"put\",\r\n    data: params,\r\n  });\r\n}\r\n// 拒绝（1.发送系统消息）\r\nexport function applyRefuse(userId) {\r\n  return request({\r\n    url: \"/system/company/apply/refuse\",\r\n    method: \"post\",\r\n    data: { userId: userId },\r\n  });\r\n}\r\n// 门户PC-保存申报信息-草稿-提审\r\nexport function editPolicyApply(params) {\r\n  return request({\r\n    url: \"/system/policyApply/submit\",\r\n    method: \"post\",\r\n    data: params,\r\n  });\r\n}\r\n// 请离\r\nexport function askResignation(params) {\r\n  return request({\r\n    url: \"/system/company/apply/askResignation\",\r\n    method: \"post\",\r\n    data: params,\r\n  });\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;AAQA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AARA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACO,SAASC,aAAaA,CAACC,MAAM,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;AACA;AACO,SAASI,eAAeA,CAACC,EAAE,EAAE;EAClC,OAAO,IAAAJ,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2BG,EAAE;IAClCF,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,gBAAgBA,CAAA,EAAG;EACjC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAACP,MAAM,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,KAAK;IACbH,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ;AACA;AACO,SAASQ,YAAYA,CAACR,MAAM,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAET;EACR,CAAC,CAAC;AACJ;AACA;AACO,SAASU,UAAUA,CAACV,MAAM,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAET;EACR,CAAC,CAAC;AACJ;AACA;AACO,SAASW,WAAWA,CAACC,MAAM,EAAE;EAClC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAE;MAAEG,MAAM,EAAEA;IAAO;EACzB,CAAC,CAAC;AACJ;AACA;AACO,SAASC,eAAeA,CAACb,MAAM,EAAE;EACtC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAET;EACR,CAAC,CAAC;AACJ;AACA;AACO,SAASc,cAAcA,CAACd,MAAM,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAET;EACR,CAAC,CAAC;AACJ", "ignoreList": []}]}