{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index.vue", "mtime": 1750311963019}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/solution", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">解决方案</div>\r\n      <div style=\"height: 33px; margin-top: 1px\"></div>\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\">\r\n            <el-form-item>\r\n              <el-input v-model=\"params.searchStr\" placeholder=\"请输入搜索内容\" class=\"activity-search-input\">\r\n                <el-button slot=\"append\" class=\"activity-search-btn\" @click=\"onSearch\">搜索</el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"hot_search\">\r\n        <span>热门搜索：</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('供应链管理')\">供应链管理</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('设备智慧物联')\">设备智慧物联</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('生产过程管控')\">生产过程管控</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('科技成果转化')\">科技成果转化</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('企业运营管理')\">企业运营管理</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('产业转型升级')\">产业转型升级</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('产融服务')\">产融服务</span>\r\n      </div>\r\n    </div>\r\n    <!-- 底部内容 -->\r\n    <div class=\"content_bottom\">\r\n      <div class=\"icondiv\">\r\n        <div class=\"solutioniconFlex\">\r\n          <div v-for=\"(item, index) in typeList\" :key=\"item.id\"\r\n            :class=\"['iconFlexTitle', aaa == item.id ? 'activeTitle' : '']\" @click=\"changeSolve(item.id)\">\r\n            {{ item.typeName }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"demandContent\" style=\"padding-top: 40px\">\r\n        <div class=\"demandflex\" style=\"height: 715px\">\r\n          <div class=\"leftsolution\">\r\n            <div :class=\"['leftTitle', bbb == 1 ? 'leftTitleHover' : '']\" @click=\"changeSolveB(1)\">\r\n              全部（{{ total1 }}）\r\n            </div>\r\n            <div v-for=\"(item, index) in typeNestList\" :key=\"index\" :class=\"[\r\n              'leftTitle',\r\n              bbb == item.solutionTypeId ? 'leftTitleHover' : '',\r\n            ]\" @click=\"changeSolveB(item.solutionTypeId)\">\r\n              <span class=\"tr2\">{{ item.solutionTypeName }}（{{ item.totalCount }}）</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"rightSolution\" v-if=\"dataList && dataList.length > 0\">\r\n            <div v-for=\"(item, index) in dataList\" :key=\"index\" class=\"solutionContent tr2\">\r\n              <div @click=\"goDetail(item.solutionId)\">\r\n                <div class=\"solutionContentTitle tr2\">\r\n                  {{ item.solutionName }}\r\n                </div>\r\n                <div class=\"solutionContentValue tr2 textOverflow\">\r\n                  {{ item.solutionIntroduction }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"rightEmpty\" v-else>\r\n            <el-empty description=\"暂无数据\"></el-empty>\r\n          </div>\r\n        </div>\r\n        <!-- 分页 -->\r\n        <div class=\"pageStyle\">\r\n          <el-pagination v-if=\"dataList && dataList.length > 0\" background layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\" :page-size=\"params.pageSize\" :current-page=\"params.pageNum\" :total=\"total\"\r\n            @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getSolutionList, getSolutionTypeList } from \"@/api/solution\";\r\nexport default {\r\n  name: \"demandHall\",\r\n  data() {\r\n    return {\r\n      params: {\r\n        parentId: \"\",\r\n        searchStr: \"\",\r\n        solutionTypeId: \"\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        category: 1,\r\n      },\r\n      total: 0,\r\n      total1: 0,\r\n      keywords: \"\",\r\n      form: {},\r\n      flag: \"全部\",\r\n      appliTypeData: [\r\n        {\r\n          dictValue: \"0\",\r\n          dictLabel: \"全部\",\r\n        },\r\n        {\r\n          dictLabel: \"创新研发\",\r\n          dictValue: \"1\",\r\n        },\r\n        {\r\n          dictLabel: \"物料采购\",\r\n          dictValue: \"2\",\r\n        },\r\n        {\r\n          dictLabel: \"智能制造\",\r\n          dictValue: \"3\",\r\n        },\r\n        {\r\n          dictLabel: \"数字化管理\",\r\n          dictValue: \"4\",\r\n        },\r\n        {\r\n          dictLabel: \"软件服务\",\r\n          dictValue: \"5\",\r\n        },\r\n        {\r\n          dictLabel: \"供应链金融\",\r\n          dictValue: \"6\",\r\n        },\r\n        {\r\n          dictLabel: \"运营宣传\",\r\n          dictValue: \"7\",\r\n        },\r\n        {\r\n          dictLabel: \"其他\",\r\n          dictValue: \"8\",\r\n        },\r\n      ],\r\n      appliTypeImgList: [\r\n        {\r\n          url: require(\"@/assets/appliMarket/type1.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type2.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type3.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type4.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type5.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type6.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type7.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type8.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type9.png\"),\r\n        },\r\n      ],\r\n      demandList: [\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n      ],\r\n      aaa: \"1\",\r\n      bbb: 1,\r\n      typeList: [\r\n        {\r\n          id: \"1\",\r\n          typeName: \"行业解决方案\",\r\n        },\r\n        {\r\n          id: \"2\",\r\n          typeName: \"领域解决方案\",\r\n        },\r\n      ],\r\n      typeNestList: [],\r\n      dataList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getTypeNext('1');\r\n  },\r\n  methods: {\r\n    async getDemandList() {\r\n      this.params.category = this.aaa;\r\n      let res = await getSolutionList(this.params);\r\n      if (res.code == 200) {\r\n        this.dataList = res.rows;\r\n        this.total = res.total;\r\n        console.log(this.total, \"total\");\r\n        if (this.params.solutionTypeId == \"\") {\r\n          this.total1 = res.total;\r\n        }\r\n      }\r\n    },\r\n    searchHot(val) {\r\n      this.params.searchStr = val;\r\n      this.onSearch();\r\n    },\r\n    onSearch() {\r\n      this.params.pageNum = 1;\r\n      this.getDemandList();\r\n    },\r\n    getappliData(value) {\r\n      this.flag = value;\r\n      this.getDemandList();\r\n    },\r\n    async getTypeNext(val) {\r\n      let res = await getSolutionTypeList({ category: val });\r\n      if (res.code == 200) {\r\n        this.typeNestList = res.rows;\r\n        this.getDemandList();\r\n      }\r\n    },\r\n    changeSolve(val) {\r\n      this.aaa = val;\r\n      this.params.parentId = val;\r\n      this.params.solutionTypeId = \"\";\r\n      this.bbb = 1;\r\n      this.params.pageSize = 10;\r\n      this.params.pageNum = 1;\r\n      this.getTypeNext(val);\r\n    },\r\n    changeSolveB(val) {\r\n      this.bbb = val;\r\n      this.params.pageSize = 10;\r\n      this.params.pageNum = 1;\r\n      if (val == 1) {\r\n        this.params.solutionTypeId = \"\";\r\n      } else {\r\n        this.params.solutionTypeId = val;\r\n      }\r\n      this.getDemandList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.params.pageSize = pageSize;\r\n      this.getDemandList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.params.pageNum = pageNum;\r\n      this.getDemandList();\r\n    },\r\n\r\n    goDetail(id) {\r\n      this.$router.push(\"/solutionDetail?id=\" + id);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n\r\n  .hot_search {\r\n    font-size: 14px;\r\n    color: #000;\r\n\r\n    .hot_search_item {\r\n      margin-right: 20px;\r\n      color: #000;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  .icondiv {\r\n    background-color: rgba(255, 255, 255, 1);\r\n    width: 100%;\r\n    height: 100px;\r\n    position: relative;\r\n\r\n    .solutioniconFlex {\r\n      display: flex;\r\n      position: absolute;\r\n      bottom: 0;\r\n      width: 1200px;\r\n      right: 0;\r\n      left: 0;\r\n      margin: auto;\r\n      justify-content: center;\r\n\r\n      .iconFlexTitle {\r\n        width: 110px;\r\n        height: 45px;\r\n        line-height: 26px;\r\n        border-radius: 2px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 18px;\r\n        text-align: center;\r\n        margin: 0 20px;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .activeTitle {\r\n        color: #0cad9d;\r\n        border-bottom: 2px solid #0cad9d;\r\n      }\r\n    }\r\n  }\r\n\r\n  .demandContent {\r\n    width: 100%;\r\n    background: #f7f8fa;\r\n    // background: #fff;\r\n    padding-top: 20px;\r\n    box-shadow: #21c9b8 solid 1px;\r\n    // border: #21c9b8 solid 1px;\r\n\r\n    .demandflex {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      flex-wrap: wrap;\r\n\r\n      .leftsolution {\r\n        width: 185px;\r\n        height: 715px;\r\n        line-height: 20px;\r\n        opacity: 0.95;\r\n        border-radius: 4px;\r\n        background: linear-gradient(180deg,\r\n            rgba(244, 246, 249, 1) 0%,\r\n            rgba(255, 255, 255, 1) 100%);\r\n        color: rgba(16, 16, 16, 1);\r\n        font-size: 14px;\r\n        box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);\r\n        border: 2px solid rgba(255, 255, 255, 1);\r\n        padding: 20px 0;\r\n        box-sizing: border-box;\r\n        overflow-y: auto;\r\n\r\n        .leftTitle {\r\n          color: rgba(51, 51, 51, 1);\r\n          font-size: 16px;\r\n          margin: 30px 0;\r\n          padding-left: 20px;\r\n          border-left: 3px solid transparent;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .leftTitleHover {\r\n          color: #0cad9d;\r\n          border-left: 3px solid #0cad9d;\r\n        }\r\n      }\r\n\r\n      .rightSolution {\r\n        width: 1000px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        flex-wrap: wrap;\r\n        align-content: flex-start;\r\n\r\n        .solutionContent {\r\n          width: 490px;\r\n          height: 124px;\r\n          border: 2px solid transparent;\r\n          padding: 20px;\r\n          box-sizing: border-box;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .solutionContent:hover {\r\n          opacity: 0.95;\r\n          border-radius: 4px;\r\n          background: linear-gradient(180deg,\r\n              rgba(244, 246, 249, 1) 0%,\r\n              rgba(255, 255, 255, 1) 100%);\r\n          color: rgba(16, 16, 16, 1);\r\n          font-size: 14px;\r\n          box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);\r\n          border: 2px solid rgba(255, 255, 255, 1);\r\n        }\r\n\r\n        .solutionContentTitle {\r\n          color: rgba(51, 51, 51, 1);\r\n          font-size: 18px;\r\n          margin-bottom: 10px;\r\n        }\r\n\r\n        .solutionContent:hover .solutionContentTitle {\r\n          color: #0cad9d;\r\n        }\r\n\r\n        .solutionContentValue {\r\n          color: rgba(102, 102, 102, 1);\r\n          font-size: 12px;\r\n          line-height: 1.5;\r\n        }\r\n      }\r\n\r\n      .rightEmpty {\r\n        width: 1000px;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.activity-title-content {\r\n  width: 100%;\r\n\r\n  // background-color: #fff;\r\n  .activity-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .activity-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .activity-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .activity-search-box {\r\n    margin-top: 40px;\r\n\r\n    .activity-search-form {\r\n      text-align: center;\r\n\r\n      .activity-search-input {\r\n        width: 792px;\r\n        height: 54px;\r\n\r\n        .activity-search-btn {\r\n          width: 100px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n\r\n  .content_bottom_item {\r\n    margin-top: 20px;\r\n    width: 590px;\r\n    height: 208px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 4px 18px 2px #e8f1fa;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    cursor: pointer;\r\n\r\n    .detailTitle {\r\n      height: 30px;\r\n      color: rgba(51, 51, 51, 1);\r\n      font-size: 18px;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .textOverflow1 {\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 1;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n\r\n    .demandChunk {\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .demand_right {\r\n        width: 413px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n      }\r\n\r\n      .demandTopRightflex {\r\n        display: flex;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .detailrightTitle {\r\n        color: rgba(153, 153, 153, 1);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightTitle2 {\r\n        color: rgba(0, 0, 0, 0.85);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightContent {\r\n        width: 343px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .content_bottom_item:hover {\r\n    box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);\r\n    scale: 1.01;\r\n  }\r\n\r\n  .content_bottom_item:nth-child(2n) {\r\n    margin-left: 20px;\r\n  }\r\n}\r\n\r\n.pageStyle {\r\n  margin-top: 60px;\r\n  width: 100%;\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.activity-search-input {\r\n  .el-input__inner {\r\n    height: 54px;\r\n    background: #fff;\r\n    border-radius: 27px 0 0 27px;\r\n    border: 1px solid #d9d9d9;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    line-height: 24px;\r\n    padding-left: 30px;\r\n  }\r\n\r\n  .el-input-group__append {\r\n    border-radius: 0px 100px 100px 0px;\r\n    background: #21c9b8;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    color: #fff;\r\n    line-height: 24px;\r\n  }\r\n}\r\n</style>\r\n"]}]}