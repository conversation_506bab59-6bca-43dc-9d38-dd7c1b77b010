{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\demandHall\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\demandHall\\detail.vue", "mtime": 1750311962984}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_purchaseSales", "require", "_login", "_apathy", "_vuex", "_cryptoJs", "_interopRequireDefault", "secret<PERSON>ey", "_default", "exports", "default", "data", "loading", "showBtn", "created", "init", "methods", "_this", "getDemandDetail", "$route", "query", "id", "then", "res", "console", "log", "key", "CryptoJS", "enc", "Utf8", "parse", "decrypt", "AES", "mode", "ECB", "padding", "pad", "Pkcs7", "JSON", "stringify", "scenePicture", "companyName", "displayRestrictions", "length", "substring", "slice", "token", "getInfo", "catch", "_this2", "createById", "user", "userId", "goChat", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "$store", "dispatch", "location", "href", "routeData", "$router", "resolve", "path", "createImById", "window", "open", "goIntention", "_this4", "getCompanyInfoByLoginInfo", "getCheckSubmit", "resourceType", "$message", "message", "title", "demandTitle", "computed", "_objectSpread2", "mapGetters"], "sources": ["src/views/purchaseSales/component/demandHall/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"demand-hall-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"demand-hall-detail-banner\">\r\n      <img\r\n        src=\"../../../../assets/demandHall/demandHallDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"demand-hall-detail-title-box\">\r\n      <div class=\"demand-hall-detail-divider\"></div>\r\n      <div class=\"demand-hall-detail-title\">需求详情</div>\r\n      <div class=\"demand-hall-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"demand-hall-detail-content\">\r\n      <div class=\"demand-hall-detail-box\">\r\n        <div class=\"demand-hall-detail-box-title\">\r\n          {{ data.demandTitle }}\r\n        </div>\r\n        <div class=\"demand-hall-detail-headline\">\r\n          <div class=\"headline-content\">\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">应用领域：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.applicationArea }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">需求方：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.companyName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">联系人：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.contactsName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">联系方式：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.contactsMobile }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">发布时间：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.createTimeString }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-btn\">\r\n              <el-button\r\n                v-if=\"showBtn\"\r\n                class=\"headline-btn-style intention-btn\"\r\n                @click=\"goIntention\"\r\n                >我有意向\r\n              </el-button>\r\n              <el-button\r\n                class=\"headline-btn-style communication-btn\"\r\n                @click=\"goChat\"\r\n                icon=\"el-icon-chat-dot-round\"\r\n                >在线沟通</el-button\r\n              >\r\n            </div>\r\n          </div>\r\n          <div class=\"headline-img\">\r\n            <img\r\n              v-if=\"data.scenePicture && data.scenePicture.length > 0\"\r\n              :src=\"data.scenePicture[0].url\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-else\r\n              src=\"../../../../assets/purchaseSales/demandDefault.png\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"demand-hall-detail-description\">\r\n          <div class=\"description-title-box\">\r\n            <div class=\"description-divider\"></div>\r\n            <div class=\"description-title\">需求描述</div>\r\n          </div>\r\n          <div class=\"description-content\">\r\n            <div v-html=\"data.summary\" class=\"description-text ql-editor\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getDemandDetail, getCheckSubmit } from \"@/api/purchaseSales\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { getCompanyInfoByLoginInfo } from \"@/api/apathy\";\r\nimport { mapGetters } from \"vuex\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n      showBtn: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      getDemandDetail(this.$route.query.id)\r\n        .then((res) => {\r\n          console.log(res);\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          console.log(res);\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          this.data.scenePicture = this.data.scenePicture\r\n            ? JSON.parse(this.data.scenePicture)\r\n            : [];\r\n          if (this.data.companyName && this.data.displayRestrictions === 2) {\r\n            if (this.data.companyName.length > 2) {\r\n              this.data.companyName =\r\n                this.data.companyName.substring(0, 2) +\r\n                \"****\" +\r\n                this.data.companyName.slice(6);\r\n            }\r\n          }\r\n          if (!this.token) {\r\n            this.showBtn = true;\r\n          } else {\r\n            this.getInfo();\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 判断此资源是不是自己发布的\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (this.data.createById === res.user.userId) {\r\n          this.showBtn = false;\r\n        } else {\r\n          this.showBtn = true;\r\n        }\r\n      });\r\n    },\r\n    goChat() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      let routeData = this.$router.resolve({\r\n        path: \"/user/im\",\r\n        query: {\r\n          userId: this.data.createImById,\r\n        },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳到我有意向页面\r\n    goIntention() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      // 是否加入企业\r\n      getCompanyInfoByLoginInfo()\r\n        .then((res) => {\r\n          if (res.data) {\r\n            // 是否对此资源提交过意向\r\n            getCheckSubmit({\r\n              id: this.$route.query.id,\r\n              resourceType: \"resource_demand\",\r\n            })\r\n              .then((res) => {\r\n                this.loading = false;\r\n                // true 提交过  false未提交过\r\n                if (res.data) {\r\n                  this.$message({\r\n                    type: \"warning\",\r\n                    message: \"已经提交过了哦！\",\r\n                  });\r\n                } else {\r\n                  let routeData = this.$router.resolve({\r\n                    path: \"/addIntention\",\r\n                    query: {\r\n                      id: this.$route.query.id,\r\n                      type: \"resource_demand\",\r\n                      title: this.data.demandTitle,\r\n                    },\r\n                  });\r\n                  window.open(routeData.href, \"_blank\");\r\n                }\r\n              })\r\n              .catch(() => {\r\n                this.loading = false;\r\n              });\r\n          } else {\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"warning\",\r\n              message: \"必须加入企业才可提交我有意向\",\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.demand-hall-detail-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n\r\n  .demand-hall-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .demand-hall-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .demand-hall-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .demand-hall-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .demand-hall-detail-content {\r\n    background: #f4f5f9;\r\n    padding-bottom: 70px;\r\n\r\n    .demand-hall-detail-box {\r\n      width: 1200px;\r\n      background: #fff;\r\n      margin: 0 auto;\r\n      padding: 60px 60px 192px;\r\n\r\n      .demand-hall-detail-box-title {\r\n        width: 100%;\r\n        font-size: 32px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      .demand-hall-detail-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-top: 40px;\r\n        padding-bottom: 40px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n\r\n        .headline-content {\r\n          flex: 1;\r\n\r\n          .headline-content-item {\r\n            display: flex;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            line-height: 32px;\r\n\r\n            .item-title {\r\n              width: 80px;\r\n              color: #666;\r\n            }\r\n\r\n            .item-content {\r\n              flex: 1;\r\n              max-width: 590px;\r\n              color: #333;\r\n              word-wrap: break-word;\r\n            }\r\n          }\r\n\r\n          .headline-content-btn {\r\n            padding-top: 48px;\r\n\r\n            .headline-btn-style {\r\n              width: 100px;\r\n              height: 32px;\r\n              border-radius: 4px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              padding: 8px 11px;\r\n            }\r\n\r\n            .intention-btn {\r\n              background: #21c9b8;\r\n              color: #fff;\r\n            }\r\n\r\n            .communication-btn {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n\r\n        .headline-img {\r\n          width: 400px;\r\n          height: 240px;\r\n          margin-left: 20px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .demand-hall-detail-description {\r\n      padding-top: 39px;\r\n\r\n      .description-title-box {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-bottom: 40px;\r\n\r\n        .description-divider {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n\r\n        .description-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n\r\n      .description-content {\r\n        width: 1072px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n        word-wrap: break-word;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.demand-hall-detail-container {\r\n  .description-content {\r\n    .description-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AA8FA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAC,sBAAA,CAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAM,SAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAD,IAAA;MACAE,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,IAAA,WAAAA,KAAA;MAAA,IAAAE,KAAA;MACA,KAAAL,OAAA;MACA,IAAAM,8BAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,EAAA,EACAC,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAG,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAvB,SAAA;QACA,IAAAwB,OAAA,GAAAJ,iBAAA,CAAAK,GAAA,CAAAD,OAAA,CAAAR,GAAA,EAAAG,GAAA;UACAO,IAAA,EAAAN,iBAAA,CAAAM,IAAA,CAAAC,GAAA;UACAC,OAAA,EAAAR,iBAAA,CAAAS,GAAA,CAAAC;QACA;QACAd,GAAA,GAAAe,IAAA,CAAAR,KAAA,CAAAH,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAU,SAAA,CAAAR,OAAA;QACAP,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACAN,KAAA,CAAAL,OAAA;QACAK,KAAA,CAAAN,IAAA,GAAAY,GAAA,CAAAZ,IAAA;QACAM,KAAA,CAAAN,IAAA,CAAA6B,YAAA,GAAAvB,KAAA,CAAAN,IAAA,CAAA6B,YAAA,GACAF,IAAA,CAAAR,KAAA,CAAAb,KAAA,CAAAN,IAAA,CAAA6B,YAAA,IACA;QACA,IAAAvB,KAAA,CAAAN,IAAA,CAAA8B,WAAA,IAAAxB,KAAA,CAAAN,IAAA,CAAA+B,mBAAA;UACA,IAAAzB,KAAA,CAAAN,IAAA,CAAA8B,WAAA,CAAAE,MAAA;YACA1B,KAAA,CAAAN,IAAA,CAAA8B,WAAA,GACAxB,KAAA,CAAAN,IAAA,CAAA8B,WAAA,CAAAG,SAAA,SACA,SACA3B,KAAA,CAAAN,IAAA,CAAA8B,WAAA,CAAAI,KAAA;UACA;QACA;QACA,KAAA5B,KAAA,CAAA6B,KAAA;UACA7B,KAAA,CAAAJ,OAAA;QACA;UACAI,KAAA,CAAA8B,OAAA;QACA;MACA,GACAC,KAAA;QACA/B,KAAA,CAAAL,OAAA;MACA;IACA;IACA;IACAmC,OAAA,WAAAA,QAAA;MAAA,IAAAE,MAAA;MACA,IAAAF,cAAA,IAAAzB,IAAA,WAAAC,GAAA;QACA,IAAA0B,MAAA,CAAAtC,IAAA,CAAAuC,UAAA,KAAA3B,GAAA,CAAA4B,IAAA,CAAAC,MAAA;UACAH,MAAA,CAAApC,OAAA;QACA;UACAoC,MAAA,CAAApC,OAAA;QACA;MACA;IACA;IACAwC,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAR,KAAA;QACA,KAAAS,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAApC,IAAA;UACAgC,MAAA,CAAAK,MAAA,CAAAC,QAAA,WAAAtC,IAAA;YACAuC,QAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;MACA,IAAAC,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACA9C,KAAA;UACAgC,MAAA,OAAAzC,IAAA,CAAAwD;QACA;MACA;MACAC,MAAA,CAAAC,IAAA,CAAAN,SAAA,CAAAD,IAAA;IACA;IACA;IACAQ,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAzB,KAAA;QACA,KAAAS,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAApC,IAAA;UACAiD,MAAA,CAAAZ,MAAA,CAAAC,QAAA,WAAAtC,IAAA;YACAuC,QAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;MACA,KAAAlD,OAAA;MACA;MACA,IAAA4D,iCAAA,IACAlD,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAZ,IAAA;UACA;UACA,IAAA8D,6BAAA;YACApD,EAAA,EAAAkD,MAAA,CAAApD,MAAA,CAAAC,KAAA,CAAAC,EAAA;YACAqD,YAAA;UACA,GACApD,IAAA,WAAAC,GAAA;YACAgD,MAAA,CAAA3D,OAAA;YACA;YACA,IAAAW,GAAA,CAAAZ,IAAA;cACA4D,MAAA,CAAAI,QAAA;gBACAjB,IAAA;gBACAkB,OAAA;cACA;YACA;cACA,IAAAb,SAAA,GAAAQ,MAAA,CAAAP,OAAA,CAAAC,OAAA;gBACAC,IAAA;gBACA9C,KAAA;kBACAC,EAAA,EAAAkD,MAAA,CAAApD,MAAA,CAAAC,KAAA,CAAAC,EAAA;kBACAqC,IAAA;kBACAmB,KAAA,EAAAN,MAAA,CAAA5D,IAAA,CAAAmE;gBACA;cACA;cACAV,MAAA,CAAAC,IAAA,CAAAN,SAAA,CAAAD,IAAA;YACA;UACA,GACAd,KAAA;YACAuB,MAAA,CAAA3D,OAAA;UACA;QACA;UACA2D,MAAA,CAAA3D,OAAA;UACA2D,MAAA,CAAAI,QAAA;YACAjB,IAAA;YACAkB,OAAA;UACA;QACA;MACA,GACA5B,KAAA;QACAuB,MAAA,CAAA3D,OAAA;MACA;IACA;EACA;EACAmE,QAAA,MAAAC,cAAA,CAAAtE,OAAA,MACA,IAAAuE,gBAAA;AAEA", "ignoreList": []}]}