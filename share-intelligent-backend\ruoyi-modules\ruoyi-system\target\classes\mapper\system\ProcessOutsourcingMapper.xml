<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ProcessOutsourcingMapper">
    
    <resultMap type="ProcessOutsourcing" id="ProcessOutsourcingResult">
        <result property="id"    column="id"    />
        <result property="processName"    column="process_name"    />
        <result property="outsourcingUnit"    column="outsourcing_unit"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectProcessOutsourcingVo">
        select id, process_name, outsourcing_unit, contact_person, contact_phone, status, create_time, update_time from process_outsourcing
    </sql>

    <select id="selectProcessOutsourcingList" parameterType="ProcessOutsourcing" resultMap="ProcessOutsourcingResult">
        <include refid="selectProcessOutsourcingVo"/>
        <where>  
            <if test="processName != null  and processName != ''"> and process_name like concat('%', #{processName}, '%')</if>
            <if test="outsourcingUnit != null  and outsourcingUnit != ''"> and outsourcing_unit = #{outsourcingUnit}</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person = #{contactPerson}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectProcessOutsourcingById" parameterType="Long" resultMap="ProcessOutsourcingResult">
        <include refid="selectProcessOutsourcingVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertProcessOutsourcing" parameterType="ProcessOutsourcing" useGeneratedKeys="true" keyProperty="id">
        insert into process_outsourcing
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="processName != null and processName != ''">process_name,</if>
            <if test="outsourcingUnit != null and outsourcingUnit != ''">outsourcing_unit,</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="processName != null and processName != ''">#{processName},</if>
            <if test="outsourcingUnit != null and outsourcingUnit != ''">#{outsourcingUnit},</if>
            <if test="contactPerson != null and contactPerson != ''">#{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateProcessOutsourcing" parameterType="ProcessOutsourcing">
        update process_outsourcing
        <trim prefix="SET" suffixOverrides=",">
            <if test="processName != null and processName != ''">process_name = #{processName},</if>
            <if test="outsourcingUnit != null and outsourcingUnit != ''">outsourcing_unit = #{outsourcingUnit},</if>
            <if test="contactPerson != null and contactPerson != ''">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProcessOutsourcingById" parameterType="Long">
        delete from process_outsourcing where id = #{id}
    </delete>

    <delete id="deleteProcessOutsourcingByIds" parameterType="String">
        delete from process_outsourcing where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>