{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\reApplication\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\reApplication\\index.vue", "mtime": 1750311963081}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_company", "_manufacturingSharing", "name", "components", "UserMenu", "data", "form", "companyName", "socialCreditCode", "registeredCapital", "contactPhone", "industry", "companyAddress", "businessScope", "personnelList", "qualificationList", "equipmentList", "settledStatus", "rules", "required", "message", "trigger", "created", "methods", "onSubmit", "_this", "$refs", "validate", "valid", "enteringFactoryAdd", "then", "res", "code", "$message", "success", "onCancel", "addPersonnelList", "push", "<PERSON><PERSON><PERSON>", "technicalType", "addQualificationList", "qualificationName", "attachment", "addEquipmentList", "equipmentName", "specification", "querySearchTianYanCha", "queryString", "cb", "searchCompany", "keywords", "rows", "List", "for<PERSON>ach", "val", "index", "id", "value", "length", "selectAutoDataTianYanCha", "row", "_this2", "getCompanyCodeByName", "$set", "taxNo"], "sources": ["src/views/system/user/reApplication/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"content_card\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <div class=\"title\">基本信息</div>\r\n            <div class=\"titleLine\"></div>\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"企业名称\" prop=\"companyName\">\r\n                  <el-autocomplete v-model=\"form.companyName\" placeholder=\"请输入您公司的完整名称\" style=\"width: 100%;\"\r\n                    :fetch-suggestions=\"querySearchTianYanCha\" @select=\"selectAutoDataTianYanCha\"></el-autocomplete>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"社会信用代码\">\r\n                  <el-input v-model=\"form.socialCreditCode\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"注册资本\">\r\n                  <el-input v-model=\"form.registeredCapital\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"联系电话\">\r\n                  <el-input v-model=\"form.contactPhone\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"行业\">\r\n                  <el-input v-model=\"form.industry\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"地址\">\r\n                  <el-input v-model=\"form.companyAddress\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-form-item prop=\"technologyType\">\r\n              <div slot=\"label\">经营范围</div>\r\n              <el-input v-model=\"form.businessScope\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\"\r\n                show-word-limit placeholder=\"请输入\" />\r\n            </el-form-item>\r\n            <div class=\"title\">企业能力</div>\r\n            <div class=\"titleLine\"></div>\r\n            <el-form-item label=\"\">\r\n              <div slot=\"label\">\r\n                <div style=\"display: flex; width: 1080px\">\r\n                  <div>人员能力</div>\r\n                  <div class=\"addStyle\" @click=\"addPersonnelList\">新增行</div>\r\n                </div>\r\n              </div>\r\n              <el-table :data=\"form.personnelList\">\r\n                <el-table-column label=\"技术人员姓名\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.technicianName\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"专业技术工种\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.technicalType\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </el-form-item>\r\n            <el-form-item label=\"技术实力\">\r\n              <el-input v-model=\"form.technicalCapability\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"\">\r\n              <div slot=\"label\">\r\n                <div style=\"display: flex; width: 1080px\">\r\n                  <div>\r\n                    资质证件\r\n                    <span style=\"color: #999999; font-size: 14px; margin-left: 11px\">（专利、商标、资质、证书等）</span>\r\n                  </div>\r\n                  <div class=\"addStyle\" @click=\"addQualificationList\">\r\n                    新增行\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <el-table :data=\"form.qualificationList\">\r\n                <el-table-column label=\"资质名称\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.qualificationName\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"附件\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <ImageUpload :limit=\"1\" v-model=\"scope.row.attachment\" />\r\n                    <!-- <el-input v-model=\"scope.row.jobId\"></el-input> -->\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </el-form-item>\r\n            <el-form-item label=\"\">\r\n              <div slot=\"label\">\r\n                <div style=\"display: flex; width: 1080px\">\r\n                  <div>设备信息</div>\r\n                  <div class=\"addStyle\" @click=\"addEquipmentList\">新增行</div>\r\n                </div>\r\n              </div>\r\n              <el-table :data=\"form.equipmentList\">\r\n                <el-table-column label=\"生产设备\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.equipmentName\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"规格型号\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.specification\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n              <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { searchCompany, getCompanyCodeByName } from \"@/api/system/company\";\r\nimport { enteringFactoryAdd } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      form: {\r\n        companyName: \"\", // 企业名称\r\n        socialCreditCode: \"\", // 社会信用代码\r\n        registeredCapital: \"\", // 注册资本\r\n        contactPhone: \"\", // 联系电话\r\n        industry: \"\", // 行业\r\n        companyAddress: \"\", // 地址\r\n        businessScope: \"\", // 经营范围\r\n        personnelList: [], // 人员能力\r\n        qualificationList: [], // 资质证件\r\n        equipmentList: [], // 设备信息列表\r\n        settledStatus: \"0\", // 默认传待审核\r\n      },\r\n      rules: {\r\n        companyName: [\r\n          { required: true, message: \"请输入企业名称\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() { },\r\n  methods: {\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          enteringFactoryAdd(this.form).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功\");\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    onCancel() { },\r\n    addPersonnelList() {\r\n      this.form.personnelList.push({\r\n        technicianName: \"\",\r\n        technicalType: \"\",\r\n      });\r\n    },\r\n    addQualificationList() {\r\n      this.form.qualificationList.push({\r\n        qualificationName: \"\",\r\n        attachment: \"\",\r\n      });\r\n    },\r\n    addEquipmentList() {\r\n      this.form.equipmentList.push({\r\n        equipmentName: \"\",\r\n        specification: \"\",\r\n      });\r\n    },\r\n    // 企业名称\r\n    querySearchTianYanCha(queryString, cb) {\r\n      if (queryString) {\r\n        searchCompany({ keywords: queryString }).then(res => {\r\n          let data = res.rows;\r\n          let List = [];\r\n          data.forEach(function (val, index) {\r\n            List.push({\r\n              id: index,\r\n              value: val\r\n            })\r\n          })\r\n          if (data.length > 0) {\r\n            cb(List);\r\n          } else {\r\n            cb([{\r\n              id: '',\r\n              value: '暂无数据'\r\n            }]);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 企业名称选择\r\n    selectAutoDataTianYanCha(row) {\r\n      getCompanyCodeByName({ keywords: row.value }).then(res => {\r\n        if (res.code == 200) {\r\n          let data = res.data;\r\n          this.$set(this.form, 'socialCreditCode', data.taxNo)\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n}\r\n\r\n.content_card {\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  // margin-top: 30px;\r\n  padding: 59px 60px 57px 60px;\r\n}\r\n\r\n.title {\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 18px;\r\n  color: #21c9b8;\r\n}\r\n\r\n.titleLine {\r\n  width: 100%;\r\n  height: 1px;\r\n  background: #21c9b8;\r\n  margin: 20px 0 30px 0;\r\n}\r\n\r\n.addStyle {\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  color: #21c9b8;\r\n  margin-left: auto;\r\n  cursor: pointer;\r\n}\r\n\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AAmIA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,qBAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,WAAA;QAAA;QACAC,gBAAA;QAAA;QACAC,iBAAA;QAAA;QACAC,YAAA;QAAA;QACAC,QAAA;QAAA;QACAC,cAAA;QAAA;QACAC,aAAA;QAAA;QACAC,aAAA;QAAA;QACAC,iBAAA;QAAA;QACAC,aAAA;QAAA;QACAC,aAAA;MACA;MACAC,KAAA;QACAX,WAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,wCAAA,EAAAJ,KAAA,CAAAnB,IAAA,EAAAwB,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACAP,KAAA,CAAAQ,QAAA,CAAAC,OAAA;YACA;UACA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAA9B,IAAA,CAAAQ,aAAA,CAAAuB,IAAA;QACAC,cAAA;QACAC,aAAA;MACA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MACA,KAAAlC,IAAA,CAAAS,iBAAA,CAAAsB,IAAA;QACAI,iBAAA;QACAC,UAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAArC,IAAA,CAAAU,aAAA,CAAAqB,IAAA;QACAO,aAAA;QACAC,aAAA;MACA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,WAAA,EAAAC,EAAA;MACA,IAAAD,WAAA;QACA,IAAAE,sBAAA;UAAAC,QAAA,EAAAH;QAAA,GAAAjB,IAAA,WAAAC,GAAA;UACA,IAAA1B,IAAA,GAAA0B,GAAA,CAAAoB,IAAA;UACA,IAAAC,IAAA;UACA/C,IAAA,CAAAgD,OAAA,WAAAC,GAAA,EAAAC,KAAA;YACAH,IAAA,CAAAf,IAAA;cACAmB,EAAA,EAAAD,KAAA;cACAE,KAAA,EAAAH;YACA;UACA;UACA,IAAAjD,IAAA,CAAAqD,MAAA;YACAV,EAAA,CAAAI,IAAA;UACA;YACAJ,EAAA;cACAQ,EAAA;cACAC,KAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAE,wBAAA,WAAAA,yBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,6BAAA;QAAAZ,QAAA,EAAAU,GAAA,CAAAH;MAAA,GAAA3B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,IAAA3B,IAAA,GAAA0B,GAAA,CAAA1B,IAAA;UACAwD,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAAvD,IAAA,sBAAAD,IAAA,CAAA2D,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}