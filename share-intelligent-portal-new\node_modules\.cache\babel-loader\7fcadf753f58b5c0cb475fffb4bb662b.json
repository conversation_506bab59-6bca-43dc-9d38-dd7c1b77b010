{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\const\\status.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\const\\status.js", "mtime": 1750311962831}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLlBPTElDWV9TVEFUVVMgPSBleHBvcnRzLkVOVEVSUFJJU0VfVFlQRSA9IHZvaWQgMDsKLyoNCiAqIEBBdXRob3I6IGpoeQ0KICogQERhdGU6IDIwMjItMTEtMDMgMTY6MDk6MTANCiAqIEBMYXN0RWRpdG9yczogSkhZDQogKiBATGFzdEVkaXRUaW1lOiAyMDIzLTAyLTE0IDEwOjUzOjE1DQogKi8KLy8g5LyB5Lia5ZCN5b2VLS3kvIHkuJrliIbnsbsKdmFyIEVOVEVSUFJJU0VfVFlQRSA9IGV4cG9ydHMuRU5URVJQUklTRV9UWVBFID0gW3sKICB2YWx1ZTogIumrmOaWsOaKgOacr+S8geS4miIsCiAgbGFiZWw6ICLpq5jmlrDmioDmnK/kvIHkuJoiCn0sIHsKICB2YWx1ZTogIuS4k+eyvueJueaWsOS8geS4miIsCiAgbGFiZWw6ICLkuJPnsr7nibnmlrDkvIHkuJoiCn0sIHsKICB2YWx1ZTogIuWIm+aWsOWei+S8geS4miIsCiAgbGFiZWw6ICLliJvmlrDlnovkvIHkuJoiCn0sIHsKICB2YWx1ZTogIueLrOinkuWFveS8geS4miIsCiAgbGFiZWw6ICLni6zop5Llhb3kvIHkuJoiCn0sIHsKICB2YWx1ZTogIueeque+muS8geS4miIsCiAgbGFiZWw6ICLnnqrnvprkvIHkuJoiCn1dOwoKLy8g5pS/562W55Sz5oqlLS3mlL/nrZbnirbmgIEKdmFyIFBPTElDWV9TVEFUVVMgPSBleHBvcnRzLlBPTElDWV9TVEFUVVMgPSBbewogIHZhbHVlOiAxLAogIGxhYmVsOiAi6L+b6KGM5LitIgp9LCB7CiAgdmFsdWU6IDIsCiAgbGFiZWw6ICLlt7LmiKrmraIiCn1dOw=="}, {"version": 3, "names": ["ENTERPRISE_TYPE", "exports", "value", "label", "POLICY_STATUS"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/const/status.js"], "sourcesContent": ["/*\r\n * @Author: jhy\r\n * @Date: 2022-11-03 16:09:10\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-02-14 10:53:15\r\n */\r\n// 企业名录--企业分类\r\nexport const ENTERPRISE_TYPE = [\r\n  { value: \"高新技术企业\", label: \"高新技术企业\" },\r\n  { value: \"专精特新企业\", label: \"专精特新企业\" },\r\n  { value: \"创新型企业\", label: \"创新型企业\" },\r\n  { value: \"独角兽企业\", label: \"独角兽企业\" },\r\n  { value: \"瞪羚企业\", label: \"瞪羚企业\" },\r\n];\r\n\r\n// 政策申报--政策状态\r\nexport const POLICY_STATUS = [\r\n  { value: 1, label: \"进行中\" },\r\n  { value: 2, label: \"已截止\" },\r\n];\r\n\r\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,IAAMA,eAAe,GAAAC,OAAA,CAAAD,eAAA,GAAG,CAC7B;EAAEE,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,QAAQ;EAAEC,KAAK,EAAE;AAAS,CAAC,EACpC;EAAED,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAClC;EAAED,KAAK,EAAE,OAAO;EAAEC,KAAK,EAAE;AAAQ,CAAC,EAClC;EAAED,KAAK,EAAE,MAAM;EAAEC,KAAK,EAAE;AAAO,CAAC,CACjC;;AAED;AACO,IAAMC,aAAa,GAAAH,OAAA,CAAAG,aAAA,GAAG,CAC3B;EAAEF,KAAK,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAM,CAAC,EAC1B;EAAED,KAAK,EAAE,CAAC;EAAEC,KAAK,EAAE;AAAM,CAAC,CAC3B", "ignoreList": []}]}