package com.ruoyi.portalweb.api.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 意向申请对象 intention_apply
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
public class IntentionApply extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /** 意向类型(字典):需求;供给 */
    @Excel(name = "意向类型(字典):需求;供给")
    private String intentionType;

    /** 意向描述 */
    @Excel(name = "意向描述")
    private String intentionContent;

     /** 供需意向id */
    @Excel(name = "供需意向id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long intentionId;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contacts;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 会员id */
    @Excel(name = "会员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long memberId;

    /** 供需发布会员id */
    @Excel(name = "供需发布会员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long intentionMemberId;

    /** 状态 */
    @Excel(name = "状态")
    private String intentionStatus;

    /** 供需发布公司名称 */
    @Excel(name = "供需发布公司名称")
    private String intentionCompanyName;

    /** 申请公司名称 */
    @Excel(name = "申请公司名称")
    private String companyName;

    /** 完成日期 */
    @Excel(name = "完成日期")
    private String completionDate;

    /** 承接量 */
    @Excel(name = "承接量")
    private String quantity;

    /** 含税单价 */
    @Excel(name = "含税单价")
    private String price;

    /** 税率 */
    @Excel(name = "税率")
    private String rate;

    /** 运费 */
    @Excel(name = "运费")
    private String shippingFee;

    /** 含税合计 */
    @Excel(name = "含税合计")
    private String sum;

    /** 申请期限 */
    @Excel(name = "申请期限")
    private String term;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    public String getIntentionCompanyName() {
        return intentionCompanyName;
    }

    public void setIntentionCompanyName(String intentionCompanyName) {
        this.intentionCompanyName = intentionCompanyName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getIntentionMemberId() {
        return intentionMemberId;
    }

    public void setIntentionMemberId(Long intentionMemberId) {
        this.intentionMemberId = intentionMemberId;
    }

    public String getIntentionStatus() {
        return intentionStatus;
    }

    public void setIntentionStatus(String intentionStatus) {
        this.intentionStatus = intentionStatus;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setIntentionType(String intentionType) 
    {
        this.intentionType = intentionType;
    }

    public String getIntentionType() 
    {
        return intentionType;
    }
    public void setIntentionContent(String intentionContent) 
    {
        this.intentionContent = intentionContent;
    }

    public String getIntentionContent() 
    {
        return intentionContent;
    }
    public void setContacts(String contacts) 
    {
        this.contacts = contacts;
    }

    public String getContacts() 
    {
        return contacts;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }
    public void setMemberId(Long memberId) 
    {
        this.memberId = memberId;
    }

    public Long getMemberId() 
    {
        return memberId;
    }

    public void setIntentionId(Long intentionId)
    {
        this.intentionId = intentionId;
    }

    public Long getIntentionId()
    {
        return intentionId;
    }

    public String getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(String completionDate) {
        this.completionDate = completionDate;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getRate() {
        return rate;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public String getShippingFee() {
        return shippingFee;
    }

    public void setShippingFee(String shippingFee) {
        this.shippingFee = shippingFee;
    }

    public String getSum() {
        return sum;
    }

    public void setSum(String sum) {
        this.sum = sum;
    }

    public String getTerm() {
        return term;
    }

    public void setTerm(String term) {
        this.term = term;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("intentionType", getIntentionType())
            .append("intentionContent", getIntentionContent())
            .append("contacts", getContacts())
            .append("phone", getPhone())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("memberId", getMemberId())
            .append("intentionId", getIntentionId())
            .append("completionDate", getCompletionDate())
            .append("quantity", getQuantity())
            .append("price", getPrice())
            .append("rate", getRate())
            .append("shippingFee", getShippingFee())
            .append("sum", getSum())
            .append("term", getTerm())
            .append("title", getTitle())
            .toString();
    }
}
