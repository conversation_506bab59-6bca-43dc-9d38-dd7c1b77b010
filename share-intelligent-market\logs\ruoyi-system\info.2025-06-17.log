17:44:03.041 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:44:04.459 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 17f32228-d7ef-44eb-9936-f18cf9cf5f66_config-0
17:44:04.590 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 83 ms to scan 1 urls, producing 3 keys and 6 values 
17:44:04.656 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 4 keys and 9 values 
17:44:04.678 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 10 values 
17:44:05.018 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 335 ms to scan 272 urls, producing 0 keys and 0 values 
17:44:05.035 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 5 values 
17:44:05.051 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
17:44:05.065 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
17:44:05.318 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 250 ms to scan 272 urls, producing 0 keys and 0 values 
17:44:05.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17f32228-d7ef-44eb-9936-f18cf9cf5f66_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:44:05.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17f32228-d7ef-44eb-9936-f18cf9cf5f66_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/311559967
17:44:05.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17f32228-d7ef-44eb-9936-f18cf9cf5f66_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/903195729
17:44:05.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17f32228-d7ef-44eb-9936-f18cf9cf5f66_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:44:05.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17f32228-d7ef-44eb-9936-f18cf9cf5f66_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:44:05.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17f32228-d7ef-44eb-9936-f18cf9cf5f66_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:44:07.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17f32228-d7ef-44eb-9936-f18cf9cf5f66_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153447515_127.0.0.1_49990
17:44:07.853 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17f32228-d7ef-44eb-9936-f18cf9cf5f66_config-0] Notify connected event to listeners.
17:44:07.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17f32228-d7ef-44eb-9936-f18cf9cf5f66_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:44:07.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17f32228-d7ef-44eb-9936-f18cf9cf5f66_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/2141094945
17:44:08.006 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:44:11.233 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,53] - Automatically configure Seata
17:44:11.244 [main] INFO  i.s.c.FileConfiguration - [<init>,108] - The file name of the operation is registry
17:44:11.356 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration:FileConfiguration$$EnhancerByCGLIB$$862af1eb
17:44:11.373 [main] INFO  i.s.c.FileConfiguration - [<init>,108] - The file name of the operation is file.conf
17:44:11.374 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration:FileConfiguration$$EnhancerByCGLIB$$862af1eb
17:44:11.519 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,189] - Initializing Global Transaction Clients ... 
17:44:11.750 [main] INFO  i.s.c.r.n.NettyClientBootstrap - [start,147] - NettyClientBootstrap has started
17:44:11.751 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,197] - Transaction Manager Client is initialized. applicationId[ruoyi-system] txServiceGroup[ruoyi-system-seata-service-group]
17:44:11.775 [main] INFO  i.s.r.d.AsyncWorker - [<init>,71] - Async Commit Buffer Limit: 10000
17:44:11.775 [main] INFO  i.s.r.d.x.ResourceManagerXA - [init,40] - ResourceManagerXA init ...
17:44:11.790 [main] INFO  i.s.c.r.n.NettyClientBootstrap - [start,147] - NettyClientBootstrap has started
17:44:11.790 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,202] - Resource Manager is initialized. applicationId[ruoyi-system] txServiceGroup[ruoyi-system-seata-service-group]
17:44:11.792 [main] INFO  i.s.s.a.GlobalTransactionScanner - [initClient,206] - Global Transaction Clients are initialized. 
17:44:13.734 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
17:44:13.735 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:44:13.736 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:44:14.220 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:44:15.333 [main] INFO  i.s.s.a.d.SeataAutoDataSourceProxyCreator - [getAdvicesAndAdvisorsForBean,47] - Auto proxy of [dataSource]
17:44:15.459 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
17:50:20.249 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:50:21.460 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dc3dfd26-e6bd-42f4-86b9-289b1c216ddc_config-0
17:50:21.552 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
17:50:21.595 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
17:50:21.614 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
17:50:21.851 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 235 ms to scan 272 urls, producing 0 keys and 0 values 
17:50:21.867 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
17:50:21.887 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
17:50:21.905 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
17:50:22.149 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 240 ms to scan 272 urls, producing 0 keys and 0 values 
17:50:22.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc3dfd26-e6bd-42f4-86b9-289b1c216ddc_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:50:22.154 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc3dfd26-e6bd-42f4-86b9-289b1c216ddc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/342740966
17:50:22.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc3dfd26-e6bd-42f4-86b9-289b1c216ddc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/576222471
17:50:22.156 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc3dfd26-e6bd-42f4-86b9-289b1c216ddc_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:50:22.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc3dfd26-e6bd-42f4-86b9-289b1c216ddc_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:50:22.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc3dfd26-e6bd-42f4-86b9-289b1c216ddc_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:50:24.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc3dfd26-e6bd-42f4-86b9-289b1c216ddc_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153823880_127.0.0.1_51591
17:50:24.174 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc3dfd26-e6bd-42f4-86b9-289b1c216ddc_config-0] Notify connected event to listeners.
17:50:24.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc3dfd26-e6bd-42f4-86b9-289b1c216ddc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:50:24.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc3dfd26-e6bd-42f4-86b9-289b1c216ddc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1502843539
17:50:24.296 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:50:28.432 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
17:50:28.433 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:50:28.433 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:50:28.760 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:50:30.367 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1,master} inited
17:50:30.372 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,148] - dynamic-datasource - add a datasource named [master] success
17:50:30.372 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,228] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:50:37.798 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:50:38.546 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 358318b0-1bbf-4366-9688-ca13427463a0
17:50:38.546 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [358318b0-1bbf-4366-9688-ca13427463a0] RpcClient init label, labels = {module=naming, source=sdk}
17:50:38.560 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [358318b0-1bbf-4366-9688-ca13427463a0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:50:38.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [358318b0-1bbf-4366-9688-ca13427463a0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:50:38.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [358318b0-1bbf-4366-9688-ca13427463a0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:50:38.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [358318b0-1bbf-4366-9688-ca13427463a0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:50:38.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [358318b0-1bbf-4366-9688-ca13427463a0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153838569_127.0.0.1_51692
17:50:38.689 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [358318b0-1bbf-4366-9688-ca13427463a0] Notify connected event to listeners.
17:50:38.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [358318b0-1bbf-4366-9688-ca13427463a0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:50:38.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [358318b0-1bbf-4366-9688-ca13427463a0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1502843539
17:50:42.384 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9701"]
17:50:42.451 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.0.68:9701 register finished
17:50:42.852 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 23.543 seconds (JVM running for 25.226)
17:50:42.887 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system.yaml, group=DEFAULT_GROUP
17:50:42.887 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
17:50:42.889 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system-dev.yaml, group=DEFAULT_GROUP
17:50:42.967 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [358318b0-1bbf-4366-9688-ca13427463a0] Receive server push request, request = NotifySubscriberRequest, requestId = 11
17:50:42.971 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [358318b0-1bbf-4366-9688-ca13427463a0] Ack server push request, request = NotifySubscriberRequest, requestId = 11
17:50:43.216 [RMI TCP Connection(4)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
