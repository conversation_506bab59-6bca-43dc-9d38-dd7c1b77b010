<template>
  <div>
    <el-form ref="form" :model="form" label-width="80px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="供给类型">
            <el-select
              v-model="requireType"
              placeholder="请选择"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in typeList"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态">
            <el-select
              v-model="requireStatus"
              placeholder="请选择"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in statusList"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="small"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="small" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="content">
      <div
        class="everyItem"
        v-for="(item, index) in dataList"
        :key="index"
        @click="goDetail(item.id)"
      >
        <!-- 左侧 -->
        <div class="item_left">
          <div class="item_content">
            <div class="itemName">需求类型：</div>
            <div class="itemValue">{{ item.typeName }}</div>
          </div>
          <div class="item_content">
            <div class="itemName">标题：</div>
            <div class="itemValue ellips1">{{ item.title }}</div>
          </div>
          <div class="item_content">
            <div class="itemName">时间：</div>
            <div class="itemValue">{{ item.createTime }}</div>
          </div>
          <div class="item_content">
            <div class="itemName">状态：</div>
            <div class="itemValue">{{ item.auditStatusName }}</div>
          </div>
        </div>
        <!-- 右侧 -->
        <div class="item_right">
          <div class="imgStyle">
            <img
              style="width: 100%; height: 100%"
              :src="
                item.imageUrl
                  ? item.imageUrl
                  : require('../../../../../assets/user/ceshi2.png')
              "
              alt=""
            />
          </div>
          <!-- <div class="buttonStyle">
            <div class="button1" @click="editData(item.id)">修改</div>
            <div class="button2">详情</div>
          </div> -->
        </div>
      </div>
      <!-- 分页 -->
      <div class="pageStyle">
        <el-pagination
          v-if="dataList && dataList.length > 0"
          background
          layout="prev, pager, next"
          class="activity-pagination"
          :page-size="pageSize"
          :current-page="pageNum"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
    <!-- 添加或修改参数配置对话框 -->
    <el-dialog
      title="修改"
      :visible.sync="dialogVisible"
      width="500px"
      append-to-body
    >
      <!-- 需求名称 编号 -->
      <div class="dialog_top">
        <div style="display: flex; align-items: center">
          <div class="requireName">需求编号：</div>
          <div class="requireValue">4211</div>
        </div>
        <div style="display: flex; align-items: center; margin-top: 10px">
          <div class="requireName">需求名称：</div>
          <div class="requireValue">名称</div>
        </div>
        <div class="lineStyle"></div>
        <!-- <div>
          <div>需求名称</div>
          <div>需求名称</div>
        </div> -->
      </div>
      <div>
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="服务质量">
            <el-radio-group v-model="form.value">
              <el-radio
                v-for="dict in dictList"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item label="服务内容">
            <el-radio-group v-model="form.value">
              <el-radio
                v-for="dict in dictList"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item label="服务准时性">
            <el-radio-group v-model="form.value">
              <el-radio
                v-for="dict in dictList"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
          <el-form-item label="业主意见">
            <el-radio-group v-model="form.value">
              <el-radio
                v-for="dict in dictList"
                :key="dict.value"
                :label="dict.value"
                >{{ dict.label }}</el-radio
              >
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div class="dialogFooter">
        <el-button type="primary" @click="submitForm">提 交</el-button>
        <!-- <el-button @click="cancel">取 消</el-button> -->
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mySupply } from "@/api/home";
import { listData } from "@/api/system/dict/data";

export default {
  data() {
    return {
      pageNum: 1,
      pageSize: 8,
      total: 0,
      requireType: "",
      requireStatus: "",
      form: {
        value: "",
      },
      rules: {},
      typeList: [],
      statusList: [],
      dataList: [],
      dialogVisible: false,
      dictList: [
        {
          label: "满意",
          value: "1",
        },
        {
          label: "基本满意",
          value: "2",
        },
        {
          label: "不满意",
          value: "3",
        },
      ],
    };
  },
  created() {
    this.getDictList();
    this.getDictStatusList();
    this.getList();
  },
  methods: {
    /** 查询字典数据列表 */
    getDictList() {
      let params = { dictType: "supply_type" };
      listData(params).then((response) => {
        this.typeList = response.rows;
      });
    },
    getDictStatusList() {
      let params = { dictType: "demand_status" };
      listData(params).then((response) => {
        this.statusList = response.rows;
      });
    },
    getList() {
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        queryType: "my",
        type: this.requireType,
        auditStatus: this.requireStatus,
      };
      mySupply(params).then((res) => {
        if (res.code === 200) {
          this.dataList = res.rows;
          this.total = res.total;
        }
      });
    },
    handleQuery() {
      this.getList();
    },
    resetQuery() {
      this.pageNum = 1;
      this.form = {};
      this.requireType = "";
      this.requireStatus = "";
      this.getList();
    },

    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    editData() {
      this.dialogVisible = true;
    },
    submitForm() {
      this.$modal
        .confirm("是否确认提交？提交后不可修改")
        .then(function () {
          // return delJobLog(jobLogIds);
        })
        .then(() => {
          // this.getList();
          // this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    goDetail(id) {
      this.$router.push(`/supplyDetail?id=${id}`);
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  .everyItem {
    width: 23.5%;
    height: 174px;
    background: #ffffff;
    box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    border: 2px solid #ffffff;
    margin-left: 2%;
    padding: 20px;
    display: flex;
    cursor: pointer;
    .item_left {
      width: 55%;
    }
    .item_content {
      display: flex;
      align-items: center;
      margin-top: 13px;
      .itemName {
        // height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
      }
      .itemValue {
        // height: 15px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }
    }
    .item_content:nth-child(1) {
      margin-top: 0;
    }
    .item_right {
      width: 40%;
      margin-left: 5%;
      .imgStyle {
        width: 100%;
        height: 98px;
      }
      .buttonStyle {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 13px;
      }
      .button1 {
        width: 60px;
        height: 28px;
        background: #e8f9f8;
        border-radius: 2px;
        border: 1px solid #21c9b8;
        text-align: center;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #21c9b8;
        line-height: 28px;
        cursor: pointer;
      }
      .button2 {
        width: 60px;
        height: 28px;
        background: #21c9b8;
        border-radius: 2px;
        margin-left: 10px;
        text-align: center;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
        line-height: 28px;
        cursor: pointer;
      }
    }
  }
  .everyItem:hover {
    box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);
    scale: 1.01;
  }
  .everyItem:nth-child(4n + 1) {
    margin-left: 0;
  }
  .everyItem:nth-child(n + 5) {
    margin-top: 22px;
  }
  .pageStyle {
    margin-top: 60px;
    width: 100%;
    text-align: center;
  }
}
.dialog_top {
  .requireName {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #999999;
  }
  .requireValue {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
  }
  .lineStyle {
    width: 100%;
    height: 1px;
    background: #eeeeee;
    margin: 20px 0;
  }
}
.dialogFooter {
  text-align: center;
}
.ellips1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  word-wrap: break-word;
}
</style>
