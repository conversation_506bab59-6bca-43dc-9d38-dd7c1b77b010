package com.ruoyi.portalweb.api.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 应用领域对象 application_field
 * 
 * <AUTHOR>
 * @date 2024-08-20
 */
public class ApplicationField extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 领域名称 */
    @Excel(name = "领域名称")
    private String applicationFieldName;

    /** 主键Id */
    private Long applicationFieldId;

    public void setApplicationFieldName(String applicationFieldName) 
    {
        this.applicationFieldName = applicationFieldName;
    }

    public String getApplicationFieldName() 
    {
        return applicationFieldName;
    }
    public void setApplicationFieldId(Long applicationFieldId) 
    {
        this.applicationFieldId = applicationFieldId;
    }

    public Long getApplicationFieldId() 
    {
        return applicationFieldId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("applicationFieldName", getApplicationFieldName())
            .append("applicationFieldId", getApplicationFieldId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
