{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\deviceDetail.vue?vue&type=style&index=0&id=f6dbd62e&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\deviceDetail.vue", "mtime": 1750311962965}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["deviceDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkLA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "deviceDetail.vue", "sourceRoot": "src/views/manufacturingSharing/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"card-container cardStyle\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <!-- 上半部分 -->\r\n        <div class=\"card_left_top\">\r\n          <div class=\"imgStyle\">\r\n            <img style=\"width: 100%; height: 100%\" :src=\"detailsData.images\r\n              ? detailsData.images.split(',')[0]\r\n              : require('@/assets/device/ceshi.png')\r\n              \" alt=\"\" />\r\n          </div>\r\n          <!-- <div class=\"imgContent\">\r\n            <div style=\"cursor: pointer\">\r\n              <img src=\"../../../assets/device/icon_left.png\" alt=\"\" />\r\n            </div>\r\n            <div style=\"display: flex; align-items: center; margin: 0 10px\">\r\n              <div\r\n                class=\"everyImgStyle\"\r\n                v-for=\"(item, index) in imgList\"\r\n                :key=\"index\"\r\n              >\r\n                <img\r\n                  style=\"width: 100%; height: 100%\"\r\n                  src=\"../../../assets/device/ceshi.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n            </div>\r\n            <div style=\"cursor: pointer\">\r\n              <img src=\"../../../assets/device/icon_right.png\" alt=\"\" />\r\n            </div>\r\n          </div> -->\r\n        </div>\r\n        <!-- 下半部分 -->\r\n        <div class=\"card_left_bottom\">\r\n          <div class=\"title\">{{ detailsData.name }}</div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">设备类别：</div>\r\n            <div class=\"optionValue\">\r\n              {{ deviceType }}\r\n            </div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">所属单位：</div>\r\n            <div class=\"optionValue\">{{ detailsData.location }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">租赁模式：</div>\r\n            <div class=\"optionValue\">{{ detailsData.rentMode }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">参考价格：</div>\r\n            <div class=\"optionValue\" style=\"color: #cc0a0a\">\r\n              {{ detailsData.rentPrice }}\r\n            </div>\r\n          </div>\r\n          <div class=\"buttonStyle\" @click=\"jumpIntention\">我有意向</div>\r\n        </div>\r\n      </div>\r\n      <!-- 中间 -->\r\n      <div class=\"card_center_line\"></div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">设备用途</div>\r\n          </div>\r\n          <div class=\"content_desc\">\r\n            {{ detailsData.description }}\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">技术指标</div>\r\n          </div>\r\n          <div style=\"margin-top: 22px\">\r\n            <el-descriptions class=\"margin-top\" title=\"\" :column=\"2\" border>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 采购日期 </template>\r\n                {{ detailsData.createTime }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 厂商品牌 </template>\r\n                {{ detailsData.brand }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 规格型号 </template>\r\n                {{ detailsData.modelNumber }}\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { deviceDetailData } from \"@/api/manufacturingSharing\";\r\nexport default {\r\n  name: \"deviceDetail\",\r\n  data() {\r\n    return {\r\n      detailsData: {},\r\n      deviceType: \"\",\r\n      imgList: [\r\n        {\r\n          id: 1,\r\n        },\r\n        {\r\n          id: 2,\r\n        },\r\n        {\r\n          id: 3,\r\n        },\r\n        {\r\n          id: 4,\r\n        },\r\n        {\r\n          id: 5,\r\n        },\r\n      ],\r\n      deviceMenuList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getDeviceDict();\r\n  },\r\n  methods: {\r\n    /** 查询字典数据列表 */\r\n    getDeviceDict() {\r\n      let params = { dictType: \"device_share_type\" };\r\n      listData(params).then((response) => {\r\n        this.deviceMenuList = response.rows;\r\n        this.getDetailData();\r\n      });\r\n    },\r\n    getDetailData() {\r\n      deviceDetailData(this.id).then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res.data, \"----------\");\r\n          this.detailsData = res.data;\r\n          this.deviceMenuList.forEach((item) => {\r\n            if (item.dictValue == this.detailsData.category) {\r\n              this.deviceType = item.dictLabel;\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    jumpIntention() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(`/demandInterested?demandName=${this.detailsData.name}&updateTime=${this.detailsData.updateTime}&intentionType=3&fieldName=设备共享&intentionId=${this.detailsData.id}`);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: #f2f2f2;\r\n  padding: 30px 0 61px 0;\r\n}\r\n\r\n.cardStyle {\r\n  height: 660px;\r\n  background-color: #ffffff;\r\n  padding: 60px 56px 54px 50px;\r\n  display: flex;\r\n}\r\n\r\n.card_left {\r\n  .card_left_top {\r\n    .imgStyle {\r\n      width: 330px;\r\n      height: 230px;\r\n      border-radius: 2px;\r\n      margin-left: 10px;\r\n    }\r\n\r\n    .imgContent {\r\n      margin-top: 15px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .everyImgStyle {\r\n        width: 54px;\r\n        height: 50px;\r\n        margin-left: 10px;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .everyImgStyle:nth-child(1) {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .card_left_bottom {\r\n    margin-top: 30px;\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      font-size: 20px;\r\n      color: #222222;\r\n      margin-bottom: 13px;\r\n    }\r\n\r\n    .everyOption {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 12px;\r\n\r\n      .optionName {\r\n        min-height: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #999999;\r\n        min-width: 75px;\r\n      }\r\n\r\n      .optionValue {\r\n        min-height: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #333333;\r\n      }\r\n    }\r\n\r\n    .buttonStyle {\r\n      margin-top: 32px;\r\n      margin-left: 55px;\r\n      width: 220px;\r\n      height: 50px;\r\n      background: #21c9b8;\r\n      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);\r\n      border-radius: 2px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      text-align: center;\r\n      line-height: 50px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.card_center_line {\r\n  width: 1px;\r\n  height: 100%;\r\n  background: #e1e1e1;\r\n  margin-left: 51px;\r\n  margin-right: 61px;\r\n}\r\n\r\n.card_right {\r\n  width: 100%;\r\n  overflow-y: auto;\r\n\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n\r\n  .content_desc {\r\n    // width: 631px;\r\n    // height: 159px;\r\n    margin-top: 20px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #666666;\r\n    line-height: 24px;\r\n  }\r\n}\r\n</style>\r\n"]}]}