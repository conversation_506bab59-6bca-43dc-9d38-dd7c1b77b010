<template>
    <div class="demanddetailbg" v-loading="loading">
      <div class="card-container">
        <div class="demandDetaitop">
          <div class="carouselDemand">
            <img :src="getImage()" style="width: 85%; height: 100%" />
            <!-- <el-carousel height="200px">
                <el-carousel-item v-for="item in imageList" :key="item">
                  <img :src="item" style="width: 100%; height: 100%" />
                </el-carousel-item>
              </el-carousel> -->
          </div>
          <div class="demandTopRight">
            <div class="demandTopRighttitle">
              <el-tooltip class="item" effect="dark" :content="title" placement="top">
                <span> {{ detailData.testingContent }}</span>
              </el-tooltip>
            </div>
            <!-- <div class="demandTopRightflex">
              <div class="detailrightTitle" style="width: 70px">应用领域：</div>
              <div class="detailrightContent" style="width: calc(100% - 70px)">
                {{ detailData.applicationAreaName }}
              </div>
            </div> -->
            <div class="demandTopRightflex">
              <div class="detailrightTitle">需求方：</div>
              <div class="detailrightContent">{{ detailData.companyName }}</div>
            </div>
            <!-- <div class="demandTopRightflex">
              <div class="detailrightTitle">需求类型：</div>
              <div class="detailrightContent">{{ detailData.typeName }}</div>
            </div> -->
            <!-- <div class="demandTopRightflex">
              <div class="detailrightTitle">处理状态：</div>
              <div class="detailrightContent">
                <span v-if="detailData.processStatus == '0'">招募中</span>
                <span v-if="detailData.processStatus == '1'">对接中</span>
                <span v-if="detailData.processStatus == '2'">已关闭</span>
                <span v-if="detailData.processStatus == '3'">已失效</span>
              </div>
            </div> -->
            <div class="demandTopRightflex">
              <div class="detailrightTitle">联系人：</div>
              <div class="detailrightContent">{{ detailData.contactPerson }}</div>
            </div>
            <div class="demandTopRightflex">
              <div class="detailrightTitle">联系电话：</div>
              <div class="detailrightContent">{{ detailData.contactPhone }}</div>
            </div>
            <div class="demandTopRightflex">
              <div class="detailrightTitle">发布时间：</div>
              <div class="detailrightContent">{{ detailData.createTime }}</div>
            </div>
            <div class="demandTopRightflex">
              <div class="intentionBtn" @click="jumpIntention">
                <!-- ../images/new/intentionIcon.png -->
                <!-- <img
                  src=""
                  style="margin-right: 10px; width: 15px; height: 15px"
                /> -->
                我有意向
              </div>
              <!-- <div class="onlineBtn" @click="opChat" v-if="showUser">
                  <img
                    src="../images/new/onlineIcon.png"
                    style="margin-right: 10px"
                  />在线沟通
                </div> -->
              <!-- <div
                  class="onlineBtn collectBtn"
                  @click="handleCollect(id, isLike, likeDelID)"
                >
                  <span
                    v-if="isLike == '1'"
                    style="margin-right: 10px"
                    class="iconfont"
                    >&#xe8c6;</span
                  >
                  <span v-else style="margin-right: 10px" class="iconfont"
                    >&#xe8b9;</span
                  >
                  {{ isLike == "1" ? "取消收藏" : "立即收藏" }}
                </div> -->
            </div>
          </div>
        </div>
        <div class="demandDetailbottom">
          <div style="width: 100%; height: 10px">
            <div class="demandDetailTitle">需求描述</div>
          </div>
          <div class="demandDetailContent" v-html="detailData.testingRequirements"></div>
        </div>
      </div>
    </div>
  </template>
  <script>
  import { releaseDetectionDetail } from "@/api/release";
  export default {
    name: "demandDetail",
    data() {
      return {
        loading: false,
        pageSize: 10,
        total: 0,
        pageNum: 1,
        title: "",
        tableData: [],
        imageList: [],
        detailData: {},
        id: null,
      };
    },
    created() {
      this.id = this.$route.query.id;
      this.getData();
    },
    methods: {
      getData() {
        this.loading = true;
        releaseDetectionDetail(this.id).then((res) => {
          if (res.code === 200) {
            this.detailData = res.data;
            this.loading = false;
          }
        });
      },
      getImage() {
        if (this.detailData.imageUrl) {
          return this.detailData.imageUrl;
        } else if (
          this.detailData.alFileDetailVOs &&
          this.detailData.alFileDetailVOs.length > 0
        ) {
          return this.detailData.alFileDetailVOs[0].fileFullPath;
        } else {
          return require("@/assets/demand/xqimgdefault.png");
        }
      },
      jumpIntention() {
        let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
        if (!userInfo?.memberCompanyName) {
          this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
            cancelButtonClass: "cancelButtonClass",
            confirmButtonClass: "customClass",
          })
            .then(() => {
              this.$router.push("/user/userCenter");
            })
            .catch(() => { });
          return;
        } else {
          this.$router.push(`/demandInterested?demandName=${this.detailData.testingContent}&updateTime=${this.detailData.updateTime}&intentionType=13&fieldName=检测需求&intentionId=${this.detailData.id}`);
        }
      },
    },
  };
  </script>
  <style lang="scss" scoped>
  .demanddetailbg {
    background: #f7f8fa;
    padding: 20px 0;
  }
  
  .demandDetaitop {
    background: white;
    // height: 348px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  
  .carouselDemand {
    width: 334px;
    height: 268px;
    margin: 40px;
  }
  
  .demandDetailbottom {
    background: white;
    min-height: 500px;
    padding: 20px;
    box-sizing: border-box;
  }
  
  .demandTopRight {
    height: 268px;
    margin: 40px;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .demandTopRighttitle {
    width: 500px;
    color: rgba(51, 51, 51, 1);
    font-size: 24px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .demandTopRightflex {
    display: flex;
    line-height: 24px;
  }
  
  .detailrightTitle {
    color: rgba(153, 153, 153, 1);
    font-size: 14px;
  }
  
  .detailrightContent {
    color: rgba(51, 51, 51, 1);
    font-size: 14px;
  }
  
  .intentionBtn {
    width: 110px;
    height: 40px;
    line-height: 40px;
    border-radius: 4px;
    background-color: #21c9b8;
    color: rgba(255, 255, 255, 1);
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    cursor: pointer;
  }
  
  .onlineBtn {
    width: 110px;
    height: 40px;
    line-height: 40px;
    border-radius: 4px;
    background-color: rgba(247, 154, 71, 0.2);
    color: rgba(247, 154, 71, 1);
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  
  .collectBtn {
    margin-left: 20px;
    border-radius: 4px;
    background-color: #ffffff;
    border: 1px solid #f79a47;
    color: #f79a47;
    box-sizing: border-box;
  }
  
  .demandDetailTitle {
    // width: 8%;
    border-left: 3px solid #21c9b8;
    height: 21px;
    color: rgba(16, 16, 16, 1);
    font-size: 20px;
    line-height: 21px;
    padding-left: 10px;
    float: left;
  }
  
  .demandDetailContent {
    padding: 10px;
    color: rgba(102, 102, 102, 1);
    font-size: 16px;
    margin-top: 20px;
  }
  
  .textOverflow {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .textOverflow1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  </style>
  