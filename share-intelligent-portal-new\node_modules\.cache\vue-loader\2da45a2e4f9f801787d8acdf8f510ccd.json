{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index(copy).vue?vue&type=style&index=0&id=781c21fc&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index(copy).vue", "mtime": 1750311963019}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index(copy).vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index(copy).vue", "sourceRoot": "src/views/solution", "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/solution/solution.png\" alt=\"\" />\r\n      <div class=\"bannerTitle\">解决方案</div>\r\n      <div class=\"bannerDesc\">\r\n        沉淀众多优秀解决方案，提供适用于不同行业、领域的数字化转型服务方案\r\n      </div>\r\n    </div>\r\n    <div>\r\n      <div class=\"activity-title-content\">\r\n        <!-- <div class=\"activity-title-box\">\r\n          <div class=\"activity-divider\"></div>\r\n          <div class=\"activity-title\">解决方案</div>\r\n          <div class=\"activity-divider\"></div>\r\n        </div> -->\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.keywords\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"solutionContent\">\r\n        <div class=\"solutionLeft\">\r\n          <div\r\n            class=\"itemType\"\r\n            :class=\"flag === '全部' ? 'itemTypeHover' : ''\"\r\n            @click=\"getItemData({ typeName: '全部' })\"\r\n          >\r\n            全部（{{ allAmounts }}）\r\n          </div>\r\n          <div\r\n            class=\"itemType\"\r\n            :class=\"flag === item.typeName ? 'itemTypeHover' : ''\"\r\n            v-for=\"item in solutionTypeList\"\r\n            :key=\"item.id\"\r\n            @click=\"getItemData(item)\"\r\n          >\r\n            {{ item.typeName }}（{{ item.value }}）\r\n          </div>\r\n        </div>\r\n        <div class=\"solutionRight\">\r\n          <div style=\"display: flex; flex-wrap: wrap\" v-loading=\"loading\">\r\n            <div\r\n              class=\"itemContent\"\r\n              v-for=\"item in solutionListData\"\r\n              :key=\"item.id\"\r\n              @click=\"goDetail(item.id)\"\r\n            >\r\n              <!-- <div class=\"content_left\">\r\n              <img src=\"\" alt=\"\">\r\n            </div> -->\r\n              <div class=\"content_right\">\r\n                <div class=\"title\">{{ item.name }}</div>\r\n                <div class=\"desc\">\r\n                  {{ item.introduction }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"activity-page-end\">\r\n            <!-- <el-button class=\"activity-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            > -->\r\n            <el-pagination\r\n              v-if=\"solutionListData && solutionListData.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"activity-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import { getActivityList } from \"@/api/purchaseSales\";\r\nimport { solutionType, solutionTypeName, solutionList } from \"@/api/solution\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { caseList } from \"@/api/classicCase\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      loading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        caseType: \"\", // 案例类型\r\n      },\r\n      caseTypeList: [],\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      allAmounts: 0,\r\n      solutionTypeList: [\r\n        {\r\n          value: \"0\",\r\n          label: \"全部\",\r\n        },\r\n        {\r\n          value: \"1\",\r\n          label: \"节能减排\",\r\n        },\r\n        {\r\n          value: \"2\",\r\n          label: \"低碳认证\",\r\n        },\r\n        {\r\n          value: \"3\",\r\n          label: \"数据核算\",\r\n        },\r\n        {\r\n          value: \"4\",\r\n          label: \"中和服务\",\r\n        },\r\n        {\r\n          value: \"5\",\r\n          label: \"星碳培训\",\r\n        },\r\n        {\r\n          value: \"6\",\r\n          label: \"绿色会议\",\r\n        },\r\n        {\r\n          value: \"7\",\r\n          label: \"数据建模\",\r\n        },\r\n        {\r\n          value: \"8\",\r\n          label: \"资产管理\",\r\n        },\r\n      ],\r\n      flag: \"全部\",\r\n      solutionListData: [],\r\n    };\r\n  },\r\n  created() {\r\n    // this.initData();\r\n    // this.getDictsList(\"activity_type\", \"activityTypeList\");\r\n    // this.search();\r\n  },\r\n  methods: {\r\n    getList(id) {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        grounding: 0,\r\n        parentId: 1,\r\n        searchStr: this.form.keywords,\r\n        typeId: id,\r\n      };\r\n      solutionList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res.rows, \"-------------\");\r\n          this.solutionListData = res.rows;\r\n          if (!id) {\r\n            this.allAmounts = res.total;\r\n          }\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    initData() {\r\n      let params = {\r\n        type: \"industry\",\r\n      };\r\n      solutionType(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.getTypeName(res.data);\r\n        }\r\n      });\r\n      // getDicts(\"case_industry\").then((res) => {\r\n      //   const { code, data = [] } = res;\r\n      //   if (code === 200) {\r\n      //     this.caseTypeList = data;\r\n      //     this.getCaseList();\r\n      //   }\r\n      // });\r\n    },\r\n    getTypeName(list) {\r\n      let params = {\r\n        parentId: 1,\r\n      };\r\n      solutionTypeName(params).then((res) => {\r\n        if (res.code === 200) {\r\n          for (var i = 0; i < res.rows.length; i++) {\r\n            for (var k = 0; k < res.rows.length; k++) {\r\n              if (res.rows[i].typeName == list[k].key) {\r\n                res.rows[i].value = list[k].value;\r\n              }\r\n            }\r\n          }\r\n          this.solutionTypeList = res.rows;\r\n          this.getList();\r\n        }\r\n      });\r\n    },\r\n    getItemData(item) {\r\n      this.flag = item.typeName;\r\n      this.getList(item.id);\r\n    },\r\n    goDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/solutionDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #ffffff;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 500px;\r\n    position: relative;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    .bannerTitle {\r\n      position: absolute;\r\n      top: 161px;\r\n      left: 24%;\r\n      font-size: 50px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n    }\r\n    .bannerDesc {\r\n      position: absolute;\r\n      top: 249px;\r\n      left: 24%;\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    // padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      margin-top: 40px;\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    padding: 8% 0;\r\n    background: #fff;\r\n    margin-top: 25px;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .solutionContent {\r\n    width: 1200px;\r\n    background-color: rgb(252, 252, 252);\r\n    margin: 40px auto;\r\n    display: flex;\r\n    .solutionLeft {\r\n      width: 172px;\r\n      height: 100%;\r\n      overflow-y: auto;\r\n      .itemType {\r\n        width: 100%;\r\n        height: 24px;\r\n        margin-top: 42px;\r\n        padding-left: 30px;\r\n        cursor: pointer;\r\n      }\r\n      .itemTypeHover {\r\n        border-left: 4px solid #21c9b8;\r\n        color: #21c9b8;\r\n      }\r\n      .itemType:nth-child(1) {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n    .solutionRight {\r\n      width: 1000px;\r\n      margin-left: 20px;\r\n      // display: flex;\r\n      // flex-wrap: wrap;\r\n      margin: 0 auto;\r\n      .itemContent {\r\n        width: 490px;\r\n        height: 190px;\r\n        padding: 30px 42px 30px 26px;\r\n        display: flex;\r\n        background: #ffffff;\r\n        margin-left: 20px;\r\n        margin-top: 20px;\r\n        cursor: pointer;\r\n      }\r\n      .itemContent:hover {\r\n        box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);\r\n        border-radius: 2px;\r\n      }\r\n      .itemContent:nth-child(2n + 1) {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n    // .content_left {\r\n    //   width: 130px;\r\n    //   height: 130px;\r\n    //   background: #ccc;\r\n    // }\r\n    .content_right {\r\n      width: 402px;\r\n      height: 130px;\r\n      margin-left: 20px;\r\n      .title {\r\n        font-size: 20px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #222222;\r\n        margin-top: 20px;\r\n      }\r\n      .desc {\r\n        margin-top: 24px;\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #65676a;\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    padding: 24px 0 60px;\r\n    .activity-page-btn {\r\n      width: 82px;\r\n      height: 32px;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      border: 1px solid #d9d9d9;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #333;\r\n      line-height: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}