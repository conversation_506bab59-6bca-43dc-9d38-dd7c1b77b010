{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\orderManage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\orderManage\\index.vue", "mtime": 1750311963069}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBvcmRlckxpc3QsDQogIG1vZGlmeVN0YXR1cywNCiAgaW52b2ljZUxpc3QsDQogIHNlbmRJbnZvaWNlLA0KfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7DQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsgdXBsb2FkVXJsIH0gZnJvbSAiQC9hcGkvb3NzIjsNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiT3BlcmxvZyIsDQogIGRpY3RzOiBbInN5c19vcGVyX3R5cGUiLCAic3lzX2NvbW1vbl9zdGF0dXMiXSwNCiAgY29tcG9uZW50czogeyBVc2VyTWVudSB9LA0KICBkYXRhKCkgew0KICAgIHZhciB2YWxpZElzRW1wdHlBcnIgPSAocywgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBpZiAoIUFycmF5LmlzQXJyYXkodmFsdWUpIHx8IHZhbHVlLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuivt+S4iuS8oOaWh+S7tiIpKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9Ow0KICAgIHJldHVybiB7DQogICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkgfSwNCiAgICAgIGFjdGlvblVybDogdXBsb2FkVXJsKCksDQogICAgICBmb3JtOiB7DQogICAgICAgIGlkOiAiIiwNCiAgICAgICAgcGhvbmU6ICIiLA0KICAgICAgICBhcHBOYW1lOiAiIiwNCiAgICAgICAgb3JkZXJDb2RlOiAiIiwNCiAgICAgICAgb3JkZXJTdGF0dXM6ICIiLA0KICAgICAgfSwNCiAgICAgIGNyZWF0ZVRpbWU6IFtdLA0KICAgICAgc3Vic2NyaWJlTGlzdDogW10sDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgfSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgZmxhZzogMSwNCiAgICAgIHR5cGVMaXN0OiBbDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0VmFsdWU6IDEsDQogICAgICAgICAgZGljdExhYmVsOiAi5bel5Lia5bqU55SoIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RWYWx1ZTogMiwNCiAgICAgICAgICBkaWN0TGFiZWw6ICLlt6XkuJrmqKHlnosiLA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIG9yZGVyU3RhdHVzTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgZGljdFZhbHVlOiAxLA0KICAgICAgICAgIGRpY3RMYWJlbDogIuW+heaUr+S7mCIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0VmFsdWU6IDIsDQogICAgICAgICAgZGljdExhYmVsOiAi5b6F5Y+R6LSnIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RWYWx1ZTogMywNCiAgICAgICAgICBkaWN0TGFiZWw6ICLmlK/ku5jlpLHotKUiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdFZhbHVlOiA0LA0KICAgICAgICAgIGRpY3RMYWJlbDogIuW3suWPkei0pyIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0VmFsdWU6IDUsDQogICAgICAgICAgZGljdExhYmVsOiAi5bey5oiQ5LqkIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RWYWx1ZTogNiwNCiAgICAgICAgICBkaWN0TGFiZWw6ICLlvoXnu63otLkiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdFZhbHVlOiA3LA0KICAgICAgICAgIGRpY3RMYWJlbDogIuW3suWFs+mXrSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0VmFsdWU6IDgsDQogICAgICAgICAgZGljdExhYmVsOiAi5pSv5LuY5LitIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RWYWx1ZTogOSwNCiAgICAgICAgICBkaWN0TGFiZWw6ICLlt7Llj5bmtogiLA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIG9yZGVyVHlwZUxpc3Q6IFsNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiAiMCIsDQogICAgICAgICAgbGFiZWw6ICLlhajpg6giLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6ICIxIiwNCiAgICAgICAgICBsYWJlbDogIuW+heS7mOasviIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogIjIiLA0KICAgICAgICAgIGxhYmVsOiAi5b6F5Y+R6LSnIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiAiMyIsDQogICAgICAgICAgbGFiZWw6ICLlvoXmlLbotKciLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6ICI0IiwNCiAgICAgICAgICBsYWJlbDogIuW3suWujOaIkCIsDQogICAgICAgIH0sDQogICAgICBdLA0KICAgICAgZmxhZzogIjAiLA0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBpbnZvaWNlVmlzaWJsZTogZmFsc2UsDQogICAgICBpbnZvaWNlRGF0YToge30sDQogICAgICBydWxlRm9ybTogew0KICAgICAgICBjb21wYW55Q2FyZExpc3Q6IFtdLA0KICAgICAgfSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGNvbXBhbnlDYXJkTGlzdDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogdmFsaWRJc0VtcHR5QXJyLCB0cmlnZ2VyOiAiY2hhbmdlIiB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICAgIGN1cnJlbnRJZDogbnVsbCwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICBjcmVhdGVCeTogdGhpcy4kc3RvcmUuc3RhdGUudXNlci51c2VySWQsDQogICAgICAgIHBhZ2VOdW06IHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSwNCiAgICAgICAgcGFnZVNpemU6IDUsDQogICAgICAgIC4uLnRoaXMuZm9ybSwNCiAgICAgICAgc3RhcnRUaW1lOiB0aGlzLmNyZWF0ZVRpbWUubGVuZ3RoID4gMCA/IHRoaXMuY3JlYXRlVGltZVswXSA6ICIiLA0KICAgICAgICBlbmRUaW1lOiB0aGlzLmNyZWF0ZVRpbWUubGVuZ3RoID4gMCA/IHRoaXMuY3JlYXRlVGltZVsxXSA6ICIiLA0KICAgICAgfTsNCiAgICAgIG9yZGVyTGlzdChwYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuc3Vic2NyaWJlTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UocGFnZU51bSkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gcGFnZU51bTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBpZDogIiIsDQogICAgICAgIHBob25lOiAiIiwNCiAgICAgICAgYXBwTmFtZTogIiIsDQogICAgICAgIG9yZGVyQ29kZTogIiIsDQogICAgICAgIG9yZGVyU3RhdHVzOiAiIiwNCiAgICAgIH07DQogICAgICB0aGlzLmNyZWF0ZVRpbWUgPSBbXTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgZ2V0RmxhZyh2YWx1ZSkgew0KICAgICAgdGhpcy5mbGFnID0gdmFsdWU7DQogICAgfSwNCiAgICBnb3RvRGV0YWlsKGlkKSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgIHBhdGg6ICIvdXNlci9vcmRlck1hbmFnZURldGFpbCIsDQogICAgICAgIHF1ZXJ5OiB7DQogICAgICAgICAgaWQsDQogICAgICAgIH0sDQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldFN0YXR1cyhzdGF0dXMpIHsNCiAgICAgIGxldCBvcmRlclN0YXR1czsNCiAgICAgIHRoaXMub3JkZXJTdGF0dXNMaXN0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgaWYgKGl0ZW0uZGljdFZhbHVlID09IHN0YXR1cykgew0KICAgICAgICAgIG9yZGVyU3RhdHVzID0gaXRlbS5kaWN0TGFiZWw7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgICAgcmV0dXJuIG9yZGVyU3RhdHVzOw0KICAgIH0sDQogICAgZ29TaGlwKGlkKSB7DQogICAgICB0aGlzLiRjb25maXJtKCLotKflk4Hlj5HotKflkI7ml6Dms5XmkqTplIDvvIznoa7orqTlj5HotKflkJfvvJ8iLCAi5Y+R6LSn56Gu6K6kIiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgbGV0IGRhdGEgPSB7DQogICAgICAgICAgICBpZCwNCiAgICAgICAgICAgIG9yZGVyU3RhdHVzOiA0LA0KICAgICAgICAgIH07DQogICAgICAgICAgbW9kaWZ5U3RhdHVzKGRhdGEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8hIik7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgaW52b2ljaW5nKGlkKSB7DQogICAgICB0aGlzLmN1cnJlbnRJZCA9IGlkOw0KICAgICAgaW52b2ljZUxpc3QoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmludm9pY2VEYXRhID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhpcy5pbnZvaWNlVmlzaWJsZSA9IHRydWU7DQogICAgICAgICAgdGhpcy5ydWxlRm9ybS5jb21wYW55Q2FyZExpc3QgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBjYW5jZWxEaWFsb2coKSB7DQogICAgICB0aGlzLmludm9pY2VWaXNpYmxlID0gZmFsc2U7DQogICAgfSwNCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmcy5ydWxlRm9ybS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgbGV0IGRhdGEgPSB7DQogICAgICAgICAgICBjb21wYW55Q2FyZExpc3Q6IHRoaXMucnVsZUZvcm0uY29tcGFueUNhcmRMaXN0LA0KICAgICAgICAgICAgb3JkZXJJZDogdGhpcy5jdXJyZW50SWQsDQogICAgICAgICAgfTsNCiAgICAgICAgICBzZW5kSW52b2ljZShkYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICAgIHRoaXMuaW52b2ljZVZpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8hIik7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUubG9nKCJlcnJvciBzdWJtaXQhISIpOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVBcHBsaWNhdGlvblJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5ydWxlRm9ybS5jb21wYW55Q2FyZExpc3QgPSBbXTsNCiAgICB9LA0KICAgIGhhbmRsZUFwcGxpY2F0aW9uU3VjY2VzcyhyZXMsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIHRoaXMucnVsZUZvcm0uY29tcGFueUNhcmRMaXN0LnB1c2goew0KICAgICAgICAgIG5hbWU6IHJlcy5kYXRhLm5hbWUsDQogICAgICAgICAgdXJsOiByZXMuZGF0YS51cmwsDQogICAgICAgICAgdHlwZTogcmVzLmRhdGEudHlwZSwNCiAgICAgICAgICBzdWZmaXg6IHJlcy5kYXRhLnN1ZmZpeCwNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVFeGNlZWRMaWNlbmNlKGZpbGVzLCBmaWxlTGlzdCkgew0KICAgICAgbGV0IG51bSA9IGZpbGVzLmxlbmd0aCArIGZpbGVMaXN0Lmxlbmd0aDsNCiAgICAgIGlmIChudW0gPj0gMSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLkuIrkvKDmlbDph4/otoXov4fkuIrpmZAiKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlUHJldmlldyhmaWxlKSB7DQogICAgICB3aW5kb3cub3BlbihmaWxlLnVybCk7DQogICAgfSwNCiAgICAvLyDmlofku7bkuIrkvKDkuYvliY3nmoTpkqnlrZANCiAgICBoYW5kbGVCZWZvcmVVcGxvYWQoZmlsZSkgew0KICAgICAgbGV0IHsgbmFtZSwgdHlwZSwgc2l6ZSB9ID0gZmlsZTsNCiAgICAgIC8vIGxldCB0eXBlTGlzdCA9IHRoaXMuYWNjZXB0DQogICAgICAvLyAgIC5zcGxpdCgiLCIpDQogICAgICAvLyAgIC5tYXAoKGl0ZW0pID0+IGl0ZW0udHJpbSgpLnRvTG93ZXJDYXNlKCkuc3Vic3RyKDEpKTsNCiAgICAgIC8vIGxldCBkb3RJbmRleCA9IG5hbWUubGFzdEluZGV4T2YoIi4iKTsNCiAgICAgIC8vIC8vIOaWh+S7tuexu+Wei+agoemqjA0KICAgICAgLy8gaWYgKGRvdEluZGV4ID09PSAtMSkgew0KICAgICAgLy8gICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7fkuIrkvKDmraPnoa7moLzlvI/nmoTmlofku7YiKTsNCiAgICAgIC8vICAgcmV0dXJuIGZhbHNlOw0KICAgICAgLy8gfSBlbHNlIHsNCiAgICAgIC8vICAgbGV0IHN1ZmZpeCA9IG5hbWUuc3Vic3RyaW5nKGRvdEluZGV4ICsgMSk7DQogICAgICAvLyAgIGlmICh0eXBlTGlzdC5pbmRleE9mKHN1ZmZpeC50b0xvd2VyQ2FzZSgpKSA9PT0gLTEpIHsNCiAgICAgIC8vICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7fkuIrkvKDmraPnoa7moLzlvI/nmoTmlofku7YiKTsNCiAgICAgIC8vICAgICByZXR1cm4gZmFsc2U7DQogICAgICAvLyAgIH0NCiAgICAgIC8vIH0NCiAgICAgIC8vIOaWh+S7tuS4iuS8oOWkp+Wwj+mZkOWItg0KICAgICAgaWYgKHNpemUgPiAxMDQ4NTc2ICogNSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmlofku7blpKflsI/kuI3og73otoXov4c1Te+8gSIpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0RA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/orderManage", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\">\r\n          <div class=\"content_type\">\r\n            <div class=\"title\">订单管理</div>\r\n          </div>\r\n          <div class=\"tabStyle\">\r\n            <!-- <div\r\n              v-for=\"item in orderTypeList\"\r\n              :key=\"item.value\"\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == item.value ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(item.value)\"\r\n            >\r\n              {{ item.label }}\r\n            </div> -->\r\n            <!-- <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 2 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(2)\"\r\n            >\r\n              待付款\r\n            </div>\r\n            <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 3 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(3)\"\r\n            >\r\n              待发货\r\n            </div>\r\n            <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 4 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(4)\"\r\n            >\r\n              待收货\r\n            </div>\r\n            <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 5 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(5)\"\r\n            >\r\n              已完成\r\n            </div> -->\r\n          </div>\r\n          <div style=\"margin-top: 20px\">\r\n            <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"订单编号\" prop=\"nickName\">\r\n                    <el-input v-model=\"form.id\" placeholder=\"请输入\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <!-- <el-col :span=\"6\">\r\n                  <el-form-item label=\"手机号\" prop=\"userName\">\r\n                    <el-input v-model=\"form.phone\" placeholder=\"请输入\" />\r\n                  </el-form-item>\r\n                </el-col> -->\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"应用名称\" prop=\"userName\">\r\n                    <el-input v-model=\"form.appName\" placeholder=\"请输入\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"订货编码\" prop=\"userName\">\r\n                    <el-input v-model=\"form.orderCode\" placeholder=\"请输入\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"订单状态\" prop=\"userName\">\r\n                    <el-select\r\n                      v-model=\"form.orderStatus\"\r\n                      placeholder=\"请选择\"\r\n                      clearable\r\n                      style=\"width: 100%\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"dict in orderStatusList\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictLabel\"\r\n                        :value=\"dict.dictValue\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"订单生成时间\" prop=\"userName\">\r\n                    <el-date-picker\r\n                      v-model=\"createTime\"\r\n                      type=\"daterange\"\r\n                      range-separator=\"至\"\r\n                      start-placeholder=\"开始日期\"\r\n                      end-placeholder=\"结束日期\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                    >\r\n                    </el-date-picker>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item>\r\n                    <el-button\r\n                      type=\"primary\"\r\n                      icon=\"el-icon-search\"\r\n                      size=\"mini\"\r\n                      @click=\"handleQuery\"\r\n                      >查询</el-button\r\n                    >\r\n                    <el-button\r\n                      icon=\"el-icon-refresh\"\r\n                      size=\"mini\"\r\n                      @click=\"resetQuery\"\r\n                      >重置</el-button\r\n                    >\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n          </div>\r\n          <div class=\"tableStyle\" v-loading=\"loading\">\r\n            <div class=\"everyItem\" v-for=\"item in subscribeList\" :key=\"item.id\">\r\n              <div class=\"orderNumTime\">\r\n                <div>订单编号: {{ item.id }}</div>\r\n                <div style=\"margin-left: 5%\">\r\n                  下单时间: {{ item.createTime }}\r\n                </div>\r\n                <div style=\"margin-left: 5%\">下单人: {{ item.nickName }}</div>\r\n                <div style=\"margin-left: 5%\">企业名称: {{ item.supply }}</div>\r\n              </div>\r\n              <div class=\"driver\"></div>\r\n              <div class=\"item_content\">\r\n                <div class=\"item_img\">\r\n                  <img :src=\"item.appLogo\" alt=\"\" />\r\n                </div>\r\n                <div class=\"item_desc\">\r\n                  <div class=\"title\">{{ item.remark }}</div>\r\n                  <!-- <div style=\"font-size: 14px; margin-top: 10px\">\r\n                    <span style=\"color: #999999\">规格:</span>\r\n                    <span style=\"margin-left: 5px\">{{ item.spec }}</span>\r\n                  </div> -->\r\n                  <div style=\"font-size: 14px; margin-top: 10px\">\r\n                    <span style=\"color: #999999\">可用时长:</span>\r\n                    <span style=\"margin-left: 5px\">{{\r\n                      item.validTime == \"1\" ? \"一年\" : \"永久\"\r\n                    }}</span>\r\n                  </div>\r\n                  <div style=\"font-size: 14px; margin-top: 10px\">\r\n                    <span style=\"color: #999999\">可用人数:</span>\r\n                    <span style=\"margin-left: 5px\">{{ item.userNumber }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item_amounts\">\r\n                  <div style=\"color: #999999; font-size: 14px\">订单金额</div>\r\n                  <div style=\"margin-top: 10px; font-weight: 400\">\r\n                    ￥{{ item.price }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"driverVertical\"></div>\r\n                <div>\r\n                  <div>{{ getStatus(item.orderStatus) }}</div>\r\n                  <!-- <div\r\n                    style=\"margin-top: 10px; color: #21C9B8; cursor: pointer\"\r\n                    @click=\"gotoDetail(item.id)\"\r\n                  >\r\n                    订单详情\r\n                  </div> -->\r\n                </div>\r\n                <div style=\"margin: 0 7%\">\r\n                  <div\r\n                    style=\"color: #21c9b8; cursor: pointer\"\r\n                    @click=\"gotoDetail(item.id)\"\r\n                  >\r\n                    订单详情\r\n                  </div>\r\n                  <!-- 待发货 -->\r\n                  <div v-if=\"item.orderStatus == 2\">\r\n                    <div\r\n                      @click=\"goShip(item.id)\"\r\n                      style=\"margin-top: 10px; color: #21c9b8; cursor: pointer\"\r\n                    >\r\n                      去发货\r\n                    </div>\r\n                  </div>\r\n                  <!-- 已发货 -->\r\n                  <div v-if=\"item.orderStatus !== 2 && item.orderStatus !== 9\">\r\n                    <div\r\n                      @click=\"invoicing(item.id)\"\r\n                      style=\"margin-top: 10px; color: #21c9b8; cursor: pointer\"\r\n                    >\r\n                      {{ item.makeinvoice == 0 ? \"开具发票\" : \"重新开票\" }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div style=\"text-align: center; margin-top: 45px\">\r\n              <el-pagination\r\n                v-show=\"total > 0\"\r\n                background\r\n                layout=\"prev, pager, next\"\r\n                :page-size=\"5\"\r\n                :current-page.sync=\"queryParams.pageNum\"\r\n                @current-change=\"handleCurrentChange\"\r\n                :total=\"total\"\r\n              >\r\n              </el-pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      title=\"开票信息\"\r\n      :visible.sync=\"invoiceVisible\"\r\n      width=\"750px\"\r\n      append-to-body\r\n    >\r\n      <el-form\r\n        ref=\"ruleForm\"\r\n        :model=\"ruleForm\"\r\n        :rules=\"rules\"\r\n        label-width=\"80px\"\r\n      >\r\n        <el-form-item label=\"发票类型:\">\r\n          {{ invoiceData.invoiceType == 1 ? \"专票\" : \"普票\" }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司名称:\">\r\n          {{ invoiceData.companyName }}\r\n        </el-form-item>\r\n        <el-form-item label=\"税号:\">\r\n          {{ invoiceData.dutyParagraph }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司地址:\">\r\n          {{ invoiceData.address }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司电话:\">\r\n          {{ invoiceData.phone }}\r\n        </el-form-item>\r\n        <el-form-item label=\"开户银行:\">\r\n          {{ invoiceData.openAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"银行账号:\">\r\n          {{ invoiceData.bankAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱地址:\">\r\n          {{ invoiceData.email }}\r\n        </el-form-item>\r\n        <el-form-item label=\"上传发票\" prop=\"companyCardList\">\r\n          <el-upload\r\n            :headers=\"headers\"\r\n            :action=\"actionUrl\"\r\n            accept=\".pdf,.jpg,.png\"\r\n            :file-list=\"ruleForm.companyCardList\"\r\n            :before-upload=\"handleBeforeUpload\"\r\n            :on-remove=\"handleApplicationRemove\"\r\n            :on-success=\"handleApplicationSuccess\"\r\n            :on-exceed=\"handleExceedLicence\"\r\n            :on-preview=\"handlePreview\"\r\n            :limit=\"1\"\r\n          >\r\n            <div>\r\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload2\"\r\n                >上传文件</el-button\r\n              >\r\n              <span style=\"margin-left: 10px\">可上传pdf,jpg,png格式</span>\r\n            </div>\r\n          </el-upload>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">发送发票</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  orderList,\r\n  modifyStatus,\r\n  invoiceList,\r\n  sendInvoice,\r\n} from \"@/api/system/user\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    var validIsEmptyArr = (s, value, callback) => {\r\n      if (!Array.isArray(value) || value.length === 0) {\r\n        callback(new Error(\"请上传文件\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    return {\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      actionUrl: uploadUrl(),\r\n      form: {\r\n        id: \"\",\r\n        phone: \"\",\r\n        appName: \"\",\r\n        orderCode: \"\",\r\n        orderStatus: \"\",\r\n      },\r\n      createTime: [],\r\n      subscribeList: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      flag: 1,\r\n      typeList: [\r\n        {\r\n          dictValue: 1,\r\n          dictLabel: \"工业应用\",\r\n        },\r\n        {\r\n          dictValue: 2,\r\n          dictLabel: \"工业模型\",\r\n        },\r\n      ],\r\n      orderStatusList: [\r\n        {\r\n          dictValue: 1,\r\n          dictLabel: \"待支付\",\r\n        },\r\n        {\r\n          dictValue: 2,\r\n          dictLabel: \"待发货\",\r\n        },\r\n        {\r\n          dictValue: 3,\r\n          dictLabel: \"支付失败\",\r\n        },\r\n        {\r\n          dictValue: 4,\r\n          dictLabel: \"已发货\",\r\n        },\r\n        {\r\n          dictValue: 5,\r\n          dictLabel: \"已成交\",\r\n        },\r\n        {\r\n          dictValue: 6,\r\n          dictLabel: \"待续费\",\r\n        },\r\n        {\r\n          dictValue: 7,\r\n          dictLabel: \"已关闭\",\r\n        },\r\n        {\r\n          dictValue: 8,\r\n          dictLabel: \"支付中\",\r\n        },\r\n        {\r\n          dictValue: 9,\r\n          dictLabel: \"已取消\",\r\n        },\r\n      ],\r\n      orderTypeList: [\r\n        {\r\n          value: \"0\",\r\n          label: \"全部\",\r\n        },\r\n        {\r\n          value: \"1\",\r\n          label: \"待付款\",\r\n        },\r\n        {\r\n          value: \"2\",\r\n          label: \"待发货\",\r\n        },\r\n        {\r\n          value: \"3\",\r\n          label: \"待收货\",\r\n        },\r\n        {\r\n          value: \"4\",\r\n          label: \"已完成\",\r\n        },\r\n      ],\r\n      flag: \"0\",\r\n      loading: false,\r\n      invoiceVisible: false,\r\n      invoiceData: {},\r\n      ruleForm: {\r\n        companyCardList: [],\r\n      },\r\n      rules: {\r\n        companyCardList: [\r\n          { required: true, validator: validIsEmptyArr, trigger: \"change\" },\r\n        ],\r\n      },\r\n      currentId: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        createBy: this.$store.state.user.userId,\r\n        pageNum: this.queryParams.pageNum,\r\n        pageSize: 5,\r\n        ...this.form,\r\n        startTime: this.createTime.length > 0 ? this.createTime[0] : \"\",\r\n        endTime: this.createTime.length > 0 ? this.createTime[1] : \"\",\r\n      };\r\n      orderList(params).then((response) => {\r\n        this.subscribeList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.form = {\r\n        id: \"\",\r\n        phone: \"\",\r\n        appName: \"\",\r\n        orderCode: \"\",\r\n        orderStatus: \"\",\r\n      };\r\n      this.createTime = [];\r\n      this.getList();\r\n    },\r\n    getFlag(value) {\r\n      this.flag = value;\r\n    },\r\n    gotoDetail(id) {\r\n      this.$router.push({\r\n        path: \"/user/orderManageDetail\",\r\n        query: {\r\n          id,\r\n        },\r\n      });\r\n    },\r\n    getStatus(status) {\r\n      let orderStatus;\r\n      this.orderStatusList.forEach((item) => {\r\n        if (item.dictValue == status) {\r\n          orderStatus = item.dictLabel;\r\n        }\r\n      });\r\n      return orderStatus;\r\n    },\r\n    goShip(id) {\r\n      this.$confirm(\"货品发货后无法撤销，确认发货吗？\", \"发货确认\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          let data = {\r\n            id,\r\n            orderStatus: 4,\r\n          };\r\n          modifyStatus(data).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    invoicing(id) {\r\n      this.currentId = id;\r\n      invoiceList().then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceData = res.data;\r\n          this.invoiceVisible = true;\r\n          this.ruleForm.companyCardList = [];\r\n        }\r\n      });\r\n    },\r\n    cancelDialog() {\r\n      this.invoiceVisible = false;\r\n    },\r\n    submitForm() {\r\n      this.$refs.ruleForm.validate((valid) => {\r\n        if (valid) {\r\n          let data = {\r\n            companyCardList: this.ruleForm.companyCardList,\r\n            orderId: this.currentId,\r\n          };\r\n          sendInvoice(data).then((res) => {\r\n            if (res.code === 200) {\r\n              this.invoiceVisible = false;\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        } else {\r\n          console.log(\"error submit!!\");\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.ruleForm.companyCardList = [];\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      if (res.code == 200) {\r\n        this.ruleForm.companyCardList.push({\r\n          name: res.data.name,\r\n          url: res.data.url,\r\n          type: res.data.type,\r\n          suffix: res.data.suffix,\r\n        });\r\n      }\r\n    },\r\n    handleExceedLicence(files, fileList) {\r\n      let num = files.length + fileList.length;\r\n      if (num >= 1) {\r\n        this.$message.error(\"上传数量超过上限\");\r\n        return false;\r\n      }\r\n    },\r\n    handlePreview(file) {\r\n      window.open(file.url);\r\n    },\r\n    // 文件上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      // let typeList = this.accept\r\n      //   .split(\",\")\r\n      //   .map((item) => item.trim().toLowerCase().substr(1));\r\n      // let dotIndex = name.lastIndexOf(\".\");\r\n      // // 文件类型校验\r\n      // if (dotIndex === -1) {\r\n      //   this.$message.error(\"请上传正确格式的文件\");\r\n      //   return false;\r\n      // } else {\r\n      //   let suffix = name.substring(dotIndex + 1);\r\n      //   if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n      //     this.$message.error(\"请上传正确格式的文件\");\r\n      //     return false;\r\n      //   }\r\n      // }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 5) {\r\n        this.$message.error(\"文件大小不能超过5M！\");\r\n        return false;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  // height: 800px;\r\n  // background: rgb(242, 248, 255);\r\n  .content_type {\r\n    display: flex;\r\n    width: 100%;\r\n    margin-bottom: 30px;\r\n    .title {\r\n      width: 100px;\r\n      padding-left: 20px;\r\n      height: 30px;\r\n      line-height: 30px;\r\n      // border-left: 4px solid #21C9B8;\r\n      font-weight: 600;\r\n      font-size: 18px;\r\n    }\r\n    // .right_content {\r\n    //   width: calc(100% - 100px);\r\n    //   text-align: right;\r\n    // }\r\n  }\r\n  .tabStyle {\r\n    display: flex;\r\n    // background: rgb(243, 248, 254);\r\n    .buttonStyle {\r\n      width: 100px;\r\n      padding: 10px;\r\n      color: #21c9b8;\r\n      text-align: center;\r\n      cursor: pointer;\r\n      border: 1px solid #21c9b8;\r\n    }\r\n    .buttonStyle:nth-child(n + 2) {\r\n      border-left: none;\r\n    }\r\n    .buttonHover {\r\n      background: #21c9b8;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .tableStyle {\r\n    .everyItem {\r\n      width: 100%;\r\n      height: 200px;\r\n      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n      margin-top: 20px;\r\n      padding: 20px;\r\n      // background: #ffffff;\r\n      .orderNumTime {\r\n        display: flex;\r\n      }\r\n      .driver {\r\n        width: 100%;\r\n        height: 1px;\r\n        background: #ccc;\r\n        margin: 15px 0;\r\n      }\r\n      .item_content {\r\n        width: 100%;\r\n        // height: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        .item_img {\r\n          width: 14%;\r\n          height: 110px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .item_desc {\r\n          margin-left: 20px;\r\n          width: 25%;\r\n          .title {\r\n            font-size: 16px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            color: #333333;\r\n          }\r\n        }\r\n        .item_amounts {\r\n          width: 10%;\r\n          text-align: right;\r\n        }\r\n        .driverVertical {\r\n          width: 1px;\r\n          height: 110px;\r\n          background: #ccc;\r\n          margin: 0 8%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}