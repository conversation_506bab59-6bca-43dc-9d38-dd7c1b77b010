{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\publishWorkshop\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\publishWorkshop\\index.vue", "mtime": 1750311963079}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/publishWorkshop", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row\r\n      :gutter=\"20\"\r\n      style=\"background: linear-gradient(to right, #e1f7f0, #f4fcfa)\"\r\n    >\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"formStyle\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"车间名称\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" placeholder=\"请输入车间名称\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"车间类型\" prop=\"type\">\r\n              <el-select\r\n                v-model=\"form.type\"\r\n                placeholder=\"请选择车间类型\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in workShopTypeList\"\r\n                  :key=\"dict.dictValue\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"所属单位\" prop=\"company\">\r\n              <el-input v-model=\"form.company\" placeholder=\"请输入所属单位\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"车间地址\" prop=\"address\">\r\n              <el-input\r\n                v-model=\"form.address\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"车间面积\" prop=\"area\">\r\n              <el-input v-model=\"form.area\" placeholder=\"请输入车间面积\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"参考价格\" prop=\"price\">\r\n              <el-input v-model=\"form.price\" placeholder=\"请输入参考价格\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"车间概况\" prop=\"description\">\r\n              <el-input\r\n                v-model=\"form.description\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"设备资源\" prop=\"resources\">\r\n              <el-input\r\n                v-model=\"form.resources\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"生产能力\" prop=\"capability\">\r\n              <el-input\r\n                v-model=\"form.capability\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"注意事项\" prop=\"notes\">\r\n              <el-input\r\n                v-model=\"form.notes\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"车间图片\" prop=\"images\">\r\n              <ImageUpload v-model=\"form.images\" resultType=\"string\"></ImageUpload>\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button type=\"primary\" @click=\"onSubmit\">{{\r\n                form.id ? \"保存\" : \"发布\"\r\n              }}</el-button>\r\n              <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\"\r\n                >取消</el-button\r\n              >\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport {\r\n  addWorkInfo,\r\n  updateWorkInfo,\r\n  workDetailData,\r\n} from \"@/api/manufacturingSharing\";\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      form: {\r\n        id: null,\r\n        name: null,\r\n        company: null,\r\n        address: null,\r\n        area: null,\r\n        price: null,\r\n        description: null,\r\n        resources: null,\r\n        capability: null,\r\n        notes: null,\r\n        images: null,\r\n        type: null,\r\n        createTime: null,\r\n        updateTime: null,\r\n        checkStatus: null,\r\n        createBy: null,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        name: [\r\n          { required: true, message: \"车间名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        company: [\r\n          { required: true, message: \"所属单位不能为空\", trigger: \"blur\" },\r\n        ],\r\n        address: [\r\n          { required: true, message: \"车间地址不能为空\", trigger: \"blur\" },\r\n        ],\r\n        type: [\r\n          { required: true, message: \"车间类型不能为空\", trigger: \"change\" },\r\n        ],\r\n      },\r\n      queryParams: {\r\n        categoryId: undefined,\r\n        status: undefined,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        total: 0,\r\n      },\r\n      workShopTypeList: [], // 车间类型\r\n    };\r\n  },\r\n  created() {\r\n    this.getDicts();\r\n    if (this.$route.query.id) {\r\n      this.getDetail();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询字典数据列表 */\r\n    getDicts() {\r\n      let params = { dictType: \"workshop_type\" };\r\n      listData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.workShopTypeList = res.rows;\r\n        }\r\n      });\r\n    },\r\n    getDetail() {\r\n      workDetailData(this.$route.query.id).then((res) => {\r\n        if (res.code == 200) {\r\n          this.form = res.data;\r\n        }\r\n      });\r\n    },\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            this.form.checkStatus = 0;\r\n            updateWorkInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.$router.go(-1);\r\n            });\r\n          } else {\r\n            this.form.checkStatus = 0;\r\n            let userinfo = JSON.parse(\r\n              window.sessionStorage.getItem(\"userinfo\")\r\n            );\r\n            this.form.createBy = userinfo.memberPhone;\r\n            addWorkInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"发布成功,请等待审核\");\r\n              this.$router.go(-1);\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.formStyle {\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  .footer-submit {\r\n    text-align: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}