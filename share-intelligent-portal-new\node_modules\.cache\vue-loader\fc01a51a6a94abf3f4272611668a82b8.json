{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\serviceSharing\\components\\talentPool\\index copy.vue?vue&type=template&id=61e2820f&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\serviceSharing\\components\\talentPool\\index copy.vue", "mtime": 1750311963013}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}