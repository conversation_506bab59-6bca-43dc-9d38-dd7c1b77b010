<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
        <div class="main-content">
          <div class="title">
            <div class="title-text">
              <span>企业认证</span>
              <el-button class="title-btn" plain :type="colorClass[form.companyStatus]">{{
                companyNameObj[form.companyStatus] }}</el-button>
            </div>
            <div class="title-desc">请核对您的个人信息。若审核通过后再进行修改，需要再次进行认证。</div>
          </div>
          <el-row :gutter="30">
            <el-col :span="4" style="opacity: 0;">marginleft</el-col>
            <el-col :span="8">
              <el-form :model="form" :rules="rules" ref="form" label-width="80px" label-position="top">
                <el-form-item label="真实姓名" prop="companyRealName">
                  <el-input v-model="form.companyRealName" placeholder="请输入真实姓名" />
                </el-form-item>
                <el-form-item label="联系方式" prop="phone">
                  <el-input v-model="form.phone" placeholder="请输入联系方式" />
                </el-form-item>
                <el-form-item label="企业名称" prop="companyName">
                  <!-- <el-input v-model="form.companyName" placeholder="请输入企业名称" /> -->
                  <el-select v-model="form.companyName" filterable remote reserve-keyword placeholder="请输入您公司的完整名称"
                    :remote-method="remoteMethod" :loading="loading" @change="changeData" style="display: block;">
                    <el-option v-for="item in options" :key="item" :label="item" :value="item">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="邮箱" prop="companyEmail">
                  <el-input v-model="form.companyEmail" placeholder="请输入邮箱" />
                </el-form-item>
              </el-form>
            </el-col>
            <el-col :span="8">
              <el-form :model="form" label-width="80px" label-position="top">
                <el-form-item label="上传授权书" prop="companyEmpower">
                  <ImageUpload v-model="form.companyEmpower" />
                </el-form-item>
                <el-form-item label="上传附件" prop="alFileDetailVOs">
                  <FileUpload v-model="form.alFileDetailVOs" />
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <div class="btn-box">
            <el-button class="btn" type="primary" @click="submitForm">认证</el-button>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import UserMenu from "../components/userMenu.vue";
import ImageUpload from "@/components/ImageUpload";
import FileUpload from "@/components/FileUpload";
import { setCompanyAuth, searchCompany, getCompanyCodeByName, getMyCompanyInfo } from "@/api/system/company";
export default {
  name: "User",
  components: { UserMenu },
  data() {
    return {
      form: {
        companyRealName: "",
        phone: "",
        companyName: "",
        companyEmail: "",
        companyEmpower: "",
        alFileDetailVOs: [],
        socialUnityCreditCode: "",
        companyStatus: 0,
        companyStatusName: "未认证",
      },
      rules: {
        companyRealName: [
          { required: true, message: "请输入姓名", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          { pattern: /^1[3456789]\d{9}$/, message: "请输入正确的手机号", trigger: "blur" },
        ],
        companyName: [
          { required: true, message: "请输入企业名称", trigger: "blur" },
        ],
      },
      settled: true,
      loading: false,
      options: [],
      list: [],
      companyNameObj: {
        "0": '未认证',
        "1": '认证中',
        "2": '认证驳回',
        "3": '已认证',
      },
      colorClass: {
        "0": 'info', // 未认证
        "1": 'warning', // 认证中
        "2": 'danger', // 认证驳回
        "3": 'success', // 未认证
      },

    };
  },
  created() {
    this.getCompany()
  },
  methods: {
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          setCompanyAuth(this.form).then((res) => {
            if (res.code === 200) {
              this.$message.success("提交成功");
            } else {
              this.$message.error(res.msg);
            }
          });
        }
      })

    },
    // 远程搜索
    getList(row) {
      searchCompany({ keywords: row }).then(res => {
        this.options = res.rows
      })
    },
    // 远程搜索
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true;
        setTimeout(() => {
          this.loading = false;
          this.getList(query)
          this.options = this.list.filter(item => {
            return item;
          });
        }, 200);
      } else {
        this.options = [];
      }
    },
    // 根据公司名称查询社会信用代码
    changeData(val) {
      console.log("val", val)
      getCompanyCodeByName({ keywords: val }).then(res => {
        let data = res.data;
        this.$set(this.form, 'socialUnityCreditCode', data.taxNo)
      })
    },
    // 获取我的公司信息
    getCompany() {
      getMyCompanyInfo().then(res => {
        let data = res.data;
        if (data) {
          if (data && data.alFileDetailVOs) {
            data.alFileDetailVOs.forEach(item => {
              item.url = item.fileFullPath
              item.name = item.fileName
            })
          }
          this.form = data
        }
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));
  height: 100vh;
}

.main-content {
  background-color: #fff;
  padding: 20px;
  padding-bottom: 100px;


  .title {
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-bottom: 20px;


    .title-text {
      font-size: 32px;
      font-weight: bold;
      line-height: 40px;
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .title-btn {
        margin-left: 10px;
      }
    }

    .title-desc {
      font-size: 14px;
      color: #999;
    }
  }

  .btn-box {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40px;
    width: 100%;

    .btn {
      width: 300px;
      height: 50px;
    }
  }


}
</style>
