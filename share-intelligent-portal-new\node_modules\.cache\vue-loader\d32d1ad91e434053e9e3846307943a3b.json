{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\mySubscriptions\\index.vue?vue&type=style&index=0&id=5ea0ac9f&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\mySubscriptions\\index.vue", "mtime": 1750311963063}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogI2Y0ZjVmOTsNCn0NCi5jb250ZW50IHsNCiAgd2lkdGg6IDEwMCU7DQogIHBhZGRpbmc6IDQwcHg7DQogIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIC8vIGhlaWdodDogY2FsYygxMDB2aCAtIDE1MHB4KTsNCiAgLy8gYmFja2dyb3VuZDogcmdiKDI0MiwgMjQ4LCAyNTUpOw0KICAuY29udGVudF90eXBlIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIHdpZHRoOiAxMDAlOw0KICAgIG1hcmdpbi1ib3R0b206IDMwcHg7DQogICAgLnRpdGxlIHsNCiAgICAgIHdpZHRoOiAxMDBweDsNCiAgICAgIHBhZGRpbmctbGVmdDogMjBweDsNCiAgICAgIGhlaWdodDogMzBweDsNCiAgICAgIGxpbmUtaGVpZ2h0OiAzMHB4Ow0KICAgICAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjMjFjOWI4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICB9DQogICAgLnJpZ2h0X2NvbnRlbnQgew0KICAgICAgd2lkdGg6IGNhbGMoMTAwJSAtIDEwMHB4KTsNCiAgICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICAgIH0NCiAgfQ0KICAudGFibGVTdHlsZSB7DQogICAgLmV2ZXJ5SXRlbSB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGhlaWdodDogMjAwcHg7DQogICAgICBib3gtc2hhZG93OiAwcHggNHB4IDIwcHggMHB4IHJnYmEoMCwgMCwgMCwgMC4wNik7DQogICAgICBtYXJnaW4tdG9wOiAyMHB4Ow0KICAgICAgcGFkZGluZzogMjBweDsNCiAgICAgIC8vIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogICAgICAub3JkZXJOdW1UaW1lIHsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIH0NCiAgICAgIC5kcml2ZXIgew0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgaGVpZ2h0OiAxcHg7DQogICAgICAgIGJhY2tncm91bmQ6ICNjY2M7DQogICAgICAgIG1hcmdpbjogMTVweCAwOw0KICAgICAgfQ0KICAgICAgLml0ZW1fY29udGVudCB7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAvLyBoZWlnaHQ6IDEwMCU7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIC5pdGVtX2ltZyB7DQogICAgICAgICAgd2lkdGg6IDE0JTsNCiAgICAgICAgICBoZWlnaHQ6IDExMHB4Ow0KICAgICAgICAgIGltZyB7DQogICAgICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLml0ZW1fZGVzYyB7DQogICAgICAgICAgbWFyZ2luLWxlZnQ6IDIwcHg7DQogICAgICAgICAgd2lkdGg6IDI1JTsNCiAgICAgICAgICAudGl0bGUgew0KICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgICAgZm9udC1mYW1pbHk6IFNvdXJjZSBIYW4gU2FucyBDTjsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLml0ZW1fYW1vdW50cyB7DQogICAgICAgICAgd2lkdGg6IDEwJTsNCiAgICAgICAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICAgICAgfQ0KICAgICAgICAuZHJpdmVyVmVydGljYWwgew0KICAgICAgICAgIHdpZHRoOiAxcHg7DQogICAgICAgICAgaGVpZ2h0OiAxMTBweDsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAjY2NjOw0KICAgICAgICAgIG1hcmdpbjogMCA4JTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICAuY29tcGFueS10YWItcGFnaW5hdGlvbiB7DQogICAgd2lkdGg6IDI4MHB4Ow0KICAgIG1hcmdpbi1sZWZ0OiBjYWxjKDQ1JSAtIDIwMHB4KTsNCiAgICAvLyBtYXJnaW46IDAgYXV0bzsNCiAgICAuYnRuLXByZXYsDQogICAgLmJ0bi1uZXh0LA0KICAgIC5idG4tcXVpY2twcmV2IHsNCiAgICAgIHdpZHRoOiAzMnB4Ow0KICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7DQogICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICBtYXJnaW46IDAgNnB4Ow0KICAgICAgY29sb3I6ICMzMzM7DQogICAgfQ0KICAgIC5lbC1wYWdlciB7DQogICAgICAubnVtYmVyIHsNCiAgICAgICAgd2lkdGg6IDMycHg7DQogICAgICAgIGhlaWdodDogMzJweDsNCiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICBsaW5lLWhlaWdodDogMzJweDsNCiAgICAgICAgbWFyZ2luOiAwIDZweDsNCiAgICAgICAgJi5hY3RpdmUgew0KICAgICAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzIxYzliODsNCiAgICAgICAgICBjb2xvcjogI2ZmZjsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAseA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/mySubscriptions", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\">\r\n          <div class=\"content_type\">\r\n            <div class=\"title\">我的订阅</div>\r\n            <div class=\"right_content\">\r\n              <div style=\"color: #21c9b8\">\r\n                您有\r\n                <span\r\n                  ><el-tag>{{ feesNum }}</el-tag></span\r\n                >\r\n                个待续费应用，请尽快续费，以免影响正常使用\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"tableStyle\" v-loading=\"loading\">\r\n            <div class=\"everyItem\" v-for=\"item in subscribeList\" :key=\"item.id\">\r\n              <div class=\"orderNumTime\">\r\n                <div>订单编号: {{ item.id }}</div>\r\n                <div style=\"margin-left: 10%\">\r\n                  下单时间: {{ item.createTime }}\r\n                </div>\r\n              </div>\r\n              <div class=\"driver\"></div>\r\n              <div class=\"item_content\">\r\n                <div class=\"item_img\">\r\n                  <img :src=\"item.appLogo\" alt=\"\" />\r\n                </div>\r\n                <div class=\"item_desc\">\r\n                  <div class=\"title\">{{ item.remark }}</div>\r\n                  <!-- <div style=\"font-size: 14px; margin-top: 10px\">\r\n                    <span style=\"color: #999999\">规格:</span>\r\n                    <span style=\"margin-left: 5px\">{{\r\n                      item.specs == \"1\" ? \"基础版\" : \"高级版\"\r\n                    }}</span>\r\n                  </div> -->\r\n                  <div style=\"font-size: 14px; margin-top: 10px\">\r\n                    <span style=\"color: #999999\">可用时长:</span>\r\n                    <span style=\"margin-left: 5px\">{{\r\n                      item.validTime == \"1\" ? \"一年\" : \"永久\"\r\n                    }}</span>\r\n                  </div>\r\n                  <div style=\"font-size: 14px; margin-top: 10px\">\r\n                    <span style=\"color: #999999\">可用人数:</span>\r\n                    <span style=\"margin-left: 5px\">不限</span>\r\n                    <!-- <span style=\"margin-left: 5px\">{{ item.userNumber }}</span> -->\r\n                  </div>\r\n                </div>\r\n                <div class=\"item_amounts\">\r\n                  <div style=\"color: #999999; font-size: 14px\">订单金额</div>\r\n                  <div style=\"margin-top: 10px; font-weight: 400\">\r\n                    ￥{{ item.price }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"driverVertical\"></div>\r\n                <div>\r\n                  <div v-if=\"item.orderStatus\">\r\n                    {{\r\n                      orderStatusList.filter(\r\n                        (item1) => item1.dictValue == item.orderStatus\r\n                      )[0].dictLabel\r\n                    }}\r\n                  </div>\r\n                  <!-- <div\r\n                    style=\"margin-top: 10px; color: #21C9B8; cursor: pointer\"\r\n                    @click=\"goDetail(item.id)\"\r\n                  >\r\n                    订单详情\r\n                  </div> -->\r\n                </div>\r\n                <!-- 待支付 支付中 -->\r\n                <div style=\"margin: 0 7%\">\r\n                  <div\r\n                    style=\"color: #21c9b8; cursor: pointer\"\r\n                    @click=\"goDetail(item.id)\"\r\n                  >\r\n                    订单详情\r\n                  </div>\r\n                  <div\r\n                    v-if=\"\r\n                      item.orderStatus == 1 ||\r\n                      item.orderStatus == 6 ||\r\n                      item.orderStatus == 8\r\n                    \"\r\n                    style=\"margin-top: 10px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"goPay(item.id)\"\r\n                  >\r\n                    去支付\r\n                  </div>\r\n                  <div\r\n                    v-if=\"item.orderStatus == 1 || item.orderStatus == 8\"\r\n                    style=\"margin-top: 10px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"cancelOrder(item.id)\"\r\n                  >\r\n                    取消订单\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  v-if=\"item.orderStatus == 1 || item.orderStatus == 8\"\r\n                  style=\"color: #21c9b8; cursor: pointer; margin: 0 1%\"\r\n                  @click=\"tryout(item)\"\r\n                >\r\n                  前往试用\r\n                </div>\r\n                <!-- 待发货 -->\r\n                <div\r\n                  v-if=\"item.orderStatus == 2\"\r\n                  style=\"color: #21c9b8; cursor: pointer; margin: 0 1%\"\r\n                  @click=\"tryout(item)\"\r\n                >\r\n                  前往试用\r\n                </div>\r\n                <div\r\n                  style=\"margin: 0 1%\"\r\n                  v-if=\"\r\n                    item.orderStatus != 1 &&\r\n                    item.orderStatus != 2 &&\r\n                    item.orderStatus != 8 &&\r\n                    item.orderStatus != 9\r\n                  \"\r\n                >\r\n                  <div\r\n                    style=\"color: #21c9b8; cursor: pointer\"\r\n                    @click=\"getInvoiceData(item.id)\"\r\n                  >\r\n                    {{ item.applyBilling == 0 ? \"申请开票\" : \"重新开票\" }}\r\n                  </div>\r\n                  <div\r\n                    v-if=\"item.makeinvoice == 1\"\r\n                    style=\"margin-top: 10px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"viewInvoiceData(item.id)\"\r\n                  >\r\n                    查看发票\r\n                  </div>\r\n                  <div\r\n                    v-if=\"item.orderStatus == 4\"\r\n                    style=\"margin-top: 10px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"confirmReceipt(item.id)\"\r\n                  >\r\n                    确认收货\r\n                  </div>\r\n                </div>\r\n                <!-- 已成交 -->\r\n                <!-- <div\r\n                  style=\"margin: 0 7%\"\r\n                  v-if=\"item.orderStatus == 5 && item.makeinvoice == 1\"\r\n                >\r\n                  <div\r\n                    style=\"margin-top: 10px; color: #21C9B8; cursor: pointer\"\r\n                    @click=\"cancelOrder(item.id)\"\r\n                  >\r\n                    已开票\r\n                  </div>\r\n                </div> -->\r\n              </div>\r\n            </div>\r\n            <div style=\"text-align: center; margin-top: 45px\">\r\n              <el-pagination\r\n                v-show=\"total > 0\"\r\n                :total=\"total\"\r\n                background\r\n                layout=\"prev, pager, next\"\r\n                :page-size=\"5\"\r\n                :current-page=\"pageNum\"\r\n                @current-change=\"handleCurrentChange\"\r\n              >\r\n              </el-pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      title=\"开票信息\"\r\n      :visible.sync=\"invoiceVisible\"\r\n      width=\"750px\"\r\n      append-to-body\r\n    >\r\n      <el-form :model=\"invoiceData\" label-width=\"80px\">\r\n        <el-form-item label=\"发票类型:\" prop=\"realName\">\r\n          {{ invoiceData.invoiceType == 1 ? \"专票\" : \"普票\" }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司名称:\" prop=\"phonenumber\">\r\n          {{ invoiceData.companyName }}\r\n        </el-form-item>\r\n        <el-form-item label=\"税号:\" prop=\"weixin\">\r\n          {{ invoiceData.dutyParagraph }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司地址:\" prop=\"email\">\r\n          {{ invoiceData.address }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司电话:\" prop=\"email\">\r\n          {{ invoiceData.phone }}\r\n        </el-form-item>\r\n        <el-form-item label=\"开户银行:\" prop=\"email\">\r\n          {{ invoiceData.openAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"银行账号:\" prop=\"email\">\r\n          {{ invoiceData.bankAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱地址:\" prop=\"email\">\r\n          {{ invoiceData.email }}\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  sublist,\r\n  cancelOrder,\r\n  invoiceList,\r\n  applyInvoice,\r\n  downLoadInvoice,\r\n  pendingFeesNum,\r\n  modifyStatus,\r\n} from \"@/api/system/user\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      subscribeList: [\r\n        {\r\n          id: 1,\r\n          title: \"檬豆云供应链管理系统\",\r\n          specs: \"正式版\",\r\n          duration: \"永久\",\r\n          number: \"不限\",\r\n          amounts: \"9999\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"檬豆云供应链管理系统\",\r\n          specs: \"正式版\",\r\n          duration: \"永久\",\r\n          number: \"不限\",\r\n          amounts: \"9999\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"檬豆云供应链管理系统\",\r\n          specs: \"正式版\",\r\n          duration: \"永久\",\r\n          number: \"不限\",\r\n          amounts: \"9999\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"檬豆云供应链管理系统\",\r\n          specs: \"正式版\",\r\n          duration: \"永久\",\r\n          number: \"不限\",\r\n          amounts: \"9999\",\r\n        },\r\n        {\r\n          id: 5,\r\n          title: \"檬豆云供应链管理系统\",\r\n          specs: \"正式版\",\r\n          duration: \"永久\",\r\n          number: \"不限\",\r\n          amounts: \"9999\",\r\n        },\r\n      ],\r\n      pageNum: 1,\r\n      total: 0,\r\n      flag: 1,\r\n      orderStatusList: [\r\n        {\r\n          dictValue: 1,\r\n          dictLabel: \"待支付\",\r\n        },\r\n        {\r\n          dictValue: 2,\r\n          dictLabel: \"待发货\",\r\n        },\r\n        {\r\n          dictValue: 3,\r\n          dictLabel: \"支付失败\",\r\n        },\r\n        {\r\n          dictValue: 4,\r\n          dictLabel: \"已发货\",\r\n        },\r\n        {\r\n          dictValue: 5,\r\n          dictLabel: \"已成交\",\r\n        },\r\n        {\r\n          dictValue: 6,\r\n          dictLabel: \"待续费\",\r\n        },\r\n        {\r\n          dictValue: 7,\r\n          dictLabel: \"已关闭\",\r\n        },\r\n        {\r\n          dictValue: 8,\r\n          dictLabel: \"支付中\",\r\n        },\r\n        {\r\n          dictValue: 9,\r\n          dictLabel: \"已取消\",\r\n        },\r\n      ],\r\n      loading: false,\r\n      invoiceVisible: false,\r\n      invoiceData: {},\r\n      currentId: null,\r\n      feesNum: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getPendingFeesNum();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getPendingFeesNum() {\r\n      let params = {\r\n        userId: this.$store.state.user.userId,\r\n        orderStatus: \"6\",\r\n      };\r\n      pendingFeesNum(params).then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res, \"--------\");\r\n          this.feesNum = res.data;\r\n        }\r\n      });\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: 5,\r\n      };\r\n      sublist(params).then((res) => {\r\n        this.loading = false;\r\n        if (res.code === 200) {\r\n          this.subscribeList = res.rows;\r\n          this.total = res.total;\r\n        }\r\n      });\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push({\r\n        path: \"/user/orderSubDetail\",\r\n        query: {\r\n          id,\r\n        },\r\n      });\r\n    },\r\n    cancelOrder(id) {\r\n      this.$confirm(\"订单取消后无法恢复，请谨慎操作!\", \"取消订单\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          cancelOrder(id).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    tryout(item) {\r\n      // window.open(href);\r\n\r\n      console.log(item);\r\n      if (item.appName == \"云端研发\") {\r\n        let url;\r\n        let hostname;\r\n        var result;\r\n        hostname = \" https://yunduanyanfa.ningmengdou.com/login \";\r\n        result = encodeURIComponent(hostname);\r\n        url = \"https://sso.ningmengdou.com/single/login?returnUrl=\" + result;\r\n        window.open(url, \"_blank\");\r\n      } else if (item.appName == \"檬豆云供应链管理系统\") {\r\n      } else if (item.appName == \"集采平台\") {\r\n        window.open(\"https://mdy.ningmengdou.com\");\r\n      } else if (item.appName == \"云MES\") {\r\n        let userid = \"18660283726\";\r\n        console.log(userid);\r\n        let jsonData = { U: userid, P: \"12a\", A: \"acb\" };\r\n        console.log(jsonData);\r\n        const encodedData = btoa(JSON.stringify(jsonData));\r\n        console.log(encodedData);\r\n        window.open(\r\n          \"http://mes.ningmengdou.com/default.html?parm=\" + encodedData,\r\n          \"_blank\"\r\n        );\r\n      } else {\r\n        window.open(\"//\" + item.webexperienceUrl, \"_blank\");\r\n      }\r\n    },\r\n    getInvoiceData(id) {\r\n      this.currentId = id;\r\n      invoiceList().then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceData = res.data;\r\n          this.invoiceVisible = true;\r\n        }\r\n      });\r\n    },\r\n    submitForm() {\r\n      let data = {\r\n        invoiceMedium: \"1\",\r\n        invoiceType: \"1\",\r\n        issueType: \"1\",\r\n        invoiceHeader: this.invoiceData.companyName,\r\n        dutyParagraph: this.invoiceData.dutyParagraph,\r\n        email: this.invoiceData.email,\r\n        orderId: this.currentId,\r\n        sendTo: this.invoiceData.userId,\r\n      };\r\n      applyInvoice(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceVisible = false;\r\n          this.$message.success(\"操作成功!\");\r\n          this.getList();\r\n        }\r\n      });\r\n    },\r\n    cancelDialog() {\r\n      this.invoiceVisible = false;\r\n    },\r\n    viewInvoiceData(id) {\r\n      let params = {\r\n        orderid: id,\r\n      };\r\n      downLoadInvoice(params).then((res) => {\r\n        let url = res[0].url;\r\n        window.open(url);\r\n      });\r\n    },\r\n    confirmReceipt(id) {\r\n      this.$confirm(\"确认后订单状态无法变更，确认收货吗？\", \"确认收货\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          let data = {\r\n            id,\r\n            orderStatus: 5,\r\n          };\r\n          modifyStatus(data).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    goPay(id) {\r\n      this.$router.push({\r\n        path: \"/payment\",\r\n        query: {\r\n          id,\r\n        },\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  // height: calc(100vh - 150px);\r\n  // background: rgb(242, 248, 255);\r\n  .content_type {\r\n    display: flex;\r\n    width: 100%;\r\n    margin-bottom: 30px;\r\n    .title {\r\n      width: 100px;\r\n      padding-left: 20px;\r\n      height: 30px;\r\n      line-height: 30px;\r\n      border-left: 4px solid #21c9b8;\r\n      font-weight: 600;\r\n      font-size: 18px;\r\n    }\r\n    .right_content {\r\n      width: calc(100% - 100px);\r\n      text-align: right;\r\n    }\r\n  }\r\n  .tableStyle {\r\n    .everyItem {\r\n      width: 100%;\r\n      height: 200px;\r\n      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n      margin-top: 20px;\r\n      padding: 20px;\r\n      // background: #ffffff;\r\n      .orderNumTime {\r\n        display: flex;\r\n      }\r\n      .driver {\r\n        width: 100%;\r\n        height: 1px;\r\n        background: #ccc;\r\n        margin: 15px 0;\r\n      }\r\n      .item_content {\r\n        width: 100%;\r\n        // height: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        .item_img {\r\n          width: 14%;\r\n          height: 110px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .item_desc {\r\n          margin-left: 20px;\r\n          width: 25%;\r\n          .title {\r\n            font-size: 16px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            color: #333333;\r\n          }\r\n        }\r\n        .item_amounts {\r\n          width: 10%;\r\n          text-align: right;\r\n        }\r\n        .driverVertical {\r\n          width: 1px;\r\n          height: 110px;\r\n          background: #ccc;\r\n          margin: 0 8%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .company-tab-pagination {\r\n    width: 280px;\r\n    margin-left: calc(45% - 200px);\r\n    // margin: 0 auto;\r\n    .btn-prev,\r\n    .btn-next,\r\n    .btn-quickprev {\r\n      width: 32px;\r\n      height: 32px;\r\n      background: #ffffff;\r\n      border: 1px solid #d9d9d9;\r\n      border-radius: 4px;\r\n      margin: 0 6px;\r\n      color: #333;\r\n    }\r\n    .el-pager {\r\n      .number {\r\n        width: 32px;\r\n        height: 32px;\r\n        border: 1px solid #d9d9d9;\r\n        background: #ffffff;\r\n        border-radius: 4px;\r\n        line-height: 32px;\r\n        margin: 0 6px;\r\n        &.active {\r\n          background: #21c9b8;\r\n          border: 1px solid #21c9b8;\r\n          color: #fff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}