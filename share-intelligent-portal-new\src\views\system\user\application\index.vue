<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu />
      </el-col>
      <el-col :span="20" :xs="24">
        <div class="content">
          <div class="applicationDesc">
            <div class="desc_left">
              <div>
                商品发布说明:
                SaaS、API商品发布前需先进行接入调试，接入流程请查看商品接入指南。
              </div>
              <div>
                商品修改说明:
                只有在销售商品才能进行修改操作，提交修改申请后，需要等待运营审核后才能生效。
              </div>
              <div>
                商品升级说明:
                SaaS商品上架后，如需支持用户升级已购买的规格，请在操作中设置升级规则。
              </div>
              <div>
                商品下架说明:
                为保障用户正常访问，商品下架需由云商店审核通过后方可下架。下架全部商品规格后，商品进入“已停售”状态，停售后将不会在商店中呈现和售卖，但不影响已购用户的使用和续订。
              </div>
            </div>
            <div class="driver"></div>
            <div class="desc_right">
              <div class="statistics">
                <div>待发货 {{ shipNum }}</div>
                <div class="statisticsItem">待开票 {{ invoiceNum }}</div>
              </div>
              <div class="submitStyle">
                <div class="buttonStyle" @click="publishAppli">发布应用</div>
              </div>
            </div>
          </div>
          <div class="appliTitle">我的应用</div>
          <div class="appliPart" v-loading="loading">
            <div class="everyItem" v-for="item in appliList" :key="item.id">
              <div class="item_img">
                <img :src="item.appLogo" alt="" />
              </div>
              <div class="item_text">
                <div class="title">{{ item.appName }}</div>
                <div class="desc">{{ item.briefInto }}</div>
              </div>
              <div class="timeStyle">
                <div class="tabHeader">交付方式</div>
                <div style="margin-top: 10px">
                  {{ item.delivery == "0" ? "Saas服务" : "本地部署" }}
                </div>
              </div>
              <div class="timeStyle">
                <div class="tabHeader">创建时间</div>
                <div style="margin-top: 10px">{{ item.createTime }}</div>
              </div>
              <div class="timeStyle">
                <div class="tabHeader">应用状态</div>
                <div style="margin-top: 10px">
                  {{
                    item.appState == 0
                      ? "待配置"
                      : item.appState == 1
                      ? "待上架"
                      : item.appState == 2
                      ? "已上架"
                      : "已下架"
                  }}
                </div>
              </div>
              <div class="option">
                <div style="display: flex">
                  <div
                    class="buttonStyle"
                    @click="goEdit(item.id, item.appState)"
                  >
                    编辑
                  </div>
                  <div
                    v-if="item.appState == 2"
                    class="buttonStyle"
                    @click="offShelf(item.id, item.appState)"
                  >
                    下架
                  </div>
                  <div
                    v-if="item.appState == 3"
                    class="buttonStyle"
                    @click="appliGround(item.id, item.appState)"
                  >
                    上架
                  </div>
                </div>
                <div style="display: flex; margin-top: 10px">
                  <div class="buttonStyle" @click="goDetail(item.id)">详情</div>
                  <div class="buttonStyle" @click="handleDelete(item.id)">
                    删除
                  </div>
                </div>
              </div>
            </div>
            <div style="text-align: center; margin-top: 45px">
              <el-pagination
                v-show="total > 0"
                background
                layout="prev, pager, next"
                :page-size="5"
                :current-page.sync="queryParams.pageNum"
                @current-change="handleCurrentChange"
                :total="total"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  appliList,
  delAppli,
  orderStatusNum,
  appliGroundOff,
} from "@/api/appliMarket";
import UserMenu from "../components/userMenu.vue";
import { checkAuthStatus } from "@/api/system/user";

export default {
  name: "Operlog",
  dicts: ["sys_oper_type", "sys_common_status"],
  components: { UserMenu },
  data() {
    return {
      appliList: [],
      queryParams: {
        pageNum: 1,
      },
      total: 0,
      loading: false,
      shipNum: 0,
      invoiceNum: 0,
      companyStatus: "0",
    };
  },
  created() {
    this.getUser();
    this.getOrderStatusNum();
    this.getList();
  },
  methods: {
    getOrderStatusNum() {
      orderStatusNum().then((res) => {
        if (res.code === 200) {
          (this.shipNum = res.data.waitSendNum),
            (this.invoiceNum = res.data.waitMakeNum);
        }
      });
    },
    getList() {
      this.loading = true;
      let params = {
        createBy: this.$store.state.user.userId,
        pageNum: this.queryParams.pageNum,
        pageSize: 5,
      };
      appliList(params).then((res) => {
        this.appliList = res.rows;
        this.total = res.total;
        this.loading = false;
      });
    },
    // 操作日志类型字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(
        this.dict.type.sys_oper_type,
        row.businessType
      );
    },
    /** 删除按钮操作 */
    handleDelete(id) {
      this.$modal
        .confirm("是否确认删除该数据项？")
        .then(function () {
          return delAppli(id);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("操作成功!");
        })
        .catch(() => {});
    },
    handleCurrentChange(pageNum) {
      this.queryParams.pageNum = pageNum;
      this.getList();
    },
    publishAppli() {
      if (this.companyStatus == "0") {
        this.$confirm("当前用户未完成服务商认证，请认证后再进行操作。", "", {
          confirmButtonText: "去认证",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.$router.push({
              path: "/user/spCertification",
            });
          })
          .catch(() => {});
      } else {
        this.$router.push({
          path: "/user/publishAppli",
        });
      }
    },
    goDetail(id) {
      this.$router.push({
        path: "/user/appliDetail",
        query: {
          id,
        },
      });
    },
    goEdit(id, status) {
      if (status == 2) {
        this.$message.warning("当前应用已上架，请下架后再进行编辑！");
      } else {
        this.$router.push({
          path: "/user/publishAppli",
          query: {
            id,
          },
        });
      }
    },
    // 上架
    appliGround(id, appState) {
      let data = {
        id,
        appState,
      };
      appliGroundOff(data).then((res) => {
        if (res.code === 200) {
          this.$message.success("操作成功!");
          this.getList();
        }
      });
    },
    // 下架
    offShelf(id, appState) {
      let data = {
        id,
        appState,
      };
      appliGroundOff(data).then((res) => {
        if (res.code === 200) {
          this.$message.success("操作成功!");
          this.getList();
        }
      });
      // this.$message.warning("当前应用存在进行中订单，无法下架！");
    },
    getUser() {
      checkAuthStatus().then((res) => {
        if (res.code === 200) {
          this.companyStatus = res.data.companyStatus;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #f4f5f9;
}
.content {
  width: 100%;
  padding: 40px;
  background: #ffffff;
  // background: rgb(242, 248, 255);
  .applicationDesc {
    width: 100%;
    height: 150px;
    display: flex;
    padding: 20px;
    .desc_left {
      width: 80%;
    }
    .driver {
      width: 2px;
      height: 100%;
      background: #ccc;
      margin: 0 5%;
    }
    .desc_right {
      width: 30%;
      display: flex;
      align-items: center;
      .statistics {
        margin-left: 10px;
        width: 50%;
        .statisticsItem {
          margin-top: 12px;
        }
      }
      .submitStyle {
        width: 50%;
        display: flex;
        justify-content: right;
        .buttonStyle {
          width: 100px;
          padding: 10px;
          background: #21c9b8;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
          border-radius: 4px;
        }
      }
    }
  }
  .appliTitle {
    font-size: 22px;
    margin: 30px;
    font-weight: 600;
  }
  .appliPart {
    .everyItem {
      width: 100%;
      height: 150px;
      background: #ffffff;
      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);
      display: flex;
      padding: 20px;
      align-items: center;
      margin-top: 20px;
      .item_img {
        width: 17%;
        height: 100%;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .item_text {
        width: 20%;
        margin-left: 2%;
        .title {
          font-size: 16px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #333333;
        }
        .desc {
          margin-top: 10px;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #999999;
        }
      }
      // .delivery {
      //   margin-left: 2%;
      // }
      .timeStyle {
        width: 15%;
        text-align: center;
      }
      .option {
        // display: flex;
        // justify-content: center;
        width: 18%;
        .buttonStyle {
          color: #21c9b8;
          cursor: pointer;
          margin-left: 20%;
        }
      }
      .tabHeader {
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #999999;
      }
    }
  }
}
</style>
