{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\detail.vue?vue&type=style&index=1&id=554ca6fd&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\detail.vue", "mtime": 1750311963017}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5ub3RpY2UtZGV0YWlsLWNvbnRhaW5lciB7DQogIC5ub3RpY2UtaW5mby1jb250ZW50IHsNCiAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICAgIGxpbmUtaGVpZ2h0OiAyOHB4Ow0KICAgIGNvbG9yOiAjMzMzOw0KICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLVJlZ3VsYXIsIFBpbmdGYW5nIFNDOw0KDQogICAgaW1nIHsNCiAgICAgIG1heC13aWR0aDogMTAwJTsNCiAgICB9DQogIH0NCn0NCg0KLnN3aXBlci1wYWdpbmF0aW9uLWJ1bGxldCB7DQogIGJhY2tncm91bmQ6ICNmZmY7DQp9DQoNCi5zd2lwZXItd3JhcHBlciB7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCn0NCg0KLnN3aXBlci1jb250YWluZXIgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLnN3aXBlci1jb250YWluZXIyIHsNCiAgd2lkdGg6IDEwMCU7DQogIG92ZXJmbG93OiBoaWRkZW47DQp9DQoNCi5zb0RldGFpbEJhbm5lciB7DQogIGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7DQogIGJhY2tncm91bmQtc2l6ZTogMTAwJTsNCn0NCg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmMA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/solution", "sourcesContent": ["<template>\r\n  <div class=\"notice-detail-container\">\r\n    <div class=\"soDetailBanner\" :style=\"{ 'background-image': `url(${form.solutionBanner})` }\">\r\n      <div class=\"bannerSolutionFlex\" style=\"height: 100%\">\r\n        <div>\r\n          <p class=\"solutionTitle\" style=\"color:#333;\">{{ form.solutionName }}</p>\r\n          <p class=\"solutionEng\" style=\"color:#333;\">{{ form.solutionIntroduction }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"schemeBox\" v-if=\"form.solutionOverview\">\r\n      <div class=\"serveContent\">\r\n        <div class=\"newsHeader\">\r\n          <div class=\"titleAll\">方案概述</div>\r\n        </div>\r\n        <div class=\"serveBottom\">\r\n          <div class=\"sketchLeft\">\r\n            <p style=\"display: inline-block;position: absolute; width: 505px;height: 297px;background: #21c9b8;\"></p>\r\n            <!-- <img src=\"@/assets/solution/sketchBg.png\" class=\"sketchBg\"> -->\r\n            <img width=\"506\" v-if='form.solutionImg' height=\"296\" :src=\"form.solutionImg\" class=\"sketch\">\r\n            <img width=\"506\" v-else height=\"296\" src=\"@/assets/solution/default.jpg\" class=\"sketch\">\r\n          </div>\r\n          <div class=\"sketchRight\">\r\n            <div style=\"text-align: end;height: 58px;\">\r\n              <img src=\"@/assets/solution/comma.png\">\r\n            </div>\r\n            {{ form.solutionOverview }}\r\n            <div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"painSpotBox\" v-if=\"painList != null && painList.length != 0\">\r\n      <div class=\"serveContent\">\r\n        <div class=\"newsHeader\">\r\n          <div class=\"titleAll\">行业痛点</div>\r\n        </div>\r\n        <div class=\"painContent\">\r\n          <div v-for=\"(item, index) in painList\" :key=\"index\" class=\"painDiv\">\r\n            <!-- <div><img :src=\"`../images/icon/fa0${index + 1}.svg`\" style=\"width: 40px\"></div> -->\r\n            <div class=\"painDivTitle\">{{ item.solutionPainName }}</div>\r\n            <div class=\"textOverflow3\">{{ item.solutionPainContent }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"advantageBox\" v-if=\"advantageList != null && advantageList.length != 0\">\r\n      <div class=\"serveContent\">\r\n        <div class=\"newsHeader\">\r\n          <div class=\"titleAll\">方案优势</div>\r\n        </div>\r\n        <div>\r\n          <el-carousel ref=\"carousel\" :autoplay=\"false\" indicator-position=\"none\" @change=\"changeadvantageListHover\">\r\n            <el-carousel-item v-for=\"(item, index) in advantageList\" :key=\"index\">\r\n              <div class=\"serveBottom\">\r\n                <div class=\"advantageLeft\">\r\n                  <div class=\"advantageTitleBox\">\r\n                    <img src=\"@/assets/solution/advantageIcon.svg\">\r\n                    <div class=\"advantageTitle\">{{ item.solutionAdvantageName }}</div>\r\n                  </div>\r\n                  <div class=\"advsubtitle\">{{ item.solutionAdvantageType }}</div>\r\n                  <div>{{ item.solutionAdvantageContent }}</div>\r\n                </div>\r\n                <div style=\"width: 552px;height: 358px\">\r\n                  <img :src=\"item.solutionAdvantageImage ? item.solutionAdvantageImage : gjImgdefault\"\r\n                    style=\"width: 100%;height: 100%\">\r\n                </div>\r\n              </div>\r\n            </el-carousel-item>\r\n          </el-carousel>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"caseBoxdetail\" v-if=\"caseList != null && caseList.length != 0\">\r\n      <div class=\"serveContent\">\r\n        <div class=\"newsHeader\">\r\n          <div class=\"titleAll\">实施案例</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"casecontent\" v-if=\"caseList.length > 0\">\r\n        <div class=\"serveContent serveBottom\">\r\n          <div class=\"caseLeft\">\r\n            <div v-for=\"(item, index) in caseList\" :key=\"index\"\r\n              :class=\"['caseLeftbtn', caseIndex == index ? 'caseLeftbtnhover' : '']\" @click=\"changeCaseIndex(index)\"\r\n              style=\"display: flex;align-items: center;\">\r\n              <img style=\"margin-left: 10px;width: 17px\" src=\"@/assets/solution/caseicon.png\">\r\n              <span style=\"margin-left: 20px;\" class=\"textOverflow1\">{{ item.solutionCaseName }}</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"caseRight\">\r\n            <div class=\"caseRightTitle\">{{ caseList[caseIndex].solutionCaseName }}</div>\r\n            <div class=\"caseRightContent\">{{ caseList[caseIndex].solutionCaseContent }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { solutionDetail, solutionDicts } from \"@/api/solution\";\r\nimport \"@/assets/styles/index.css\";\r\n\r\nexport default {\r\n  name: \"policyPage\",\r\n  data() {\r\n    return {\r\n      showLogin: false,\r\n      userinfo: [],\r\n      token: '',\r\n      productList: [],\r\n      cmsList: [],\r\n      cmsData: [],\r\n      demandList: [],\r\n      input: '',\r\n      requireList: [],\r\n      queryIndex: 0,\r\n      total: 0,\r\n      pageSize: 10,\r\n      pageNum: 1,\r\n      titleContent: '',\r\n      requirementTypeCodeArray: [],\r\n      form: {},\r\n      imageUrl: '',\r\n      aaa: '1',\r\n      dataList: [],\r\n      painList: [],\r\n      advantageList: [],\r\n      advantageListHover: 0,\r\n      benefitList: [],\r\n      caseList: [],\r\n      caseIndex: 0,\r\n      gjImgdefault: '@/assets/solution/gjimgdefault.png'\r\n    };\r\n  },\r\n  created() {\r\n    if (this.$route.query.id) {\r\n      this.id = this.$route.query.id\r\n      this.getDemandList()\r\n    }\r\n  },\r\n  methods: {\r\n    async getDemandList() {\r\n      let res = await solutionDetail({ solutionId: this.id });\r\n      if (res.code == 200) {\r\n        this.form = res.data;\r\n        this.painList = res.data.alSolutionPainVOs;\r\n        this.caseList = res.data.alSolutionCaseVOs;\r\n        this.advantageList = res.data.alSolutionAdvantageVOs;\r\n      }\r\n    },\r\n    async getDicts() {\r\n      let res = await solutionDicts();\r\n      if (res.code == 200) {\r\n        this.requireList = res.data;\r\n      }\r\n    },\r\n    currentChange(val) {\r\n      this.pageNum = val\r\n      this.getDemandList()\r\n    },\r\n    changeSolve(val) {\r\n      this.aaa = val\r\n    },\r\n    changeadvantageListHover(val) {\r\n      this.advantageListHover = val\r\n    },\r\n    lastStep() {\r\n      this.$refs.carousel.setActiveItem(this.advantageListHover - 1)\r\n    },\r\n    nextStep() {\r\n      this.$refs.carousel.setActiveItem(this.advantageListHover + 1)\r\n    },\r\n    changeCaseIndex(index) {\r\n      this.caseIndex = index\r\n    },\r\n    getUrlKey: function (name) {\r\n      return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(\r\n        location.href) || [, \"\"])[\r\n        1].replace(/\\+/g, '%20')) || null;\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.notice-detail-container {\r\n  width: 100%;\r\n  padding: 0 0 100px;\r\n  background: #f4f5f9;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.notice-detail-container {\r\n  .notice-info-content {\r\n    word-break: break-all;\r\n    font-size: 16px;\r\n    line-height: 28px;\r\n    color: #333;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n\r\n    img {\r\n      max-width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.swiper-pagination-bullet {\r\n  background: #fff;\r\n}\r\n\r\n.swiper-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.swiper-container {\r\n  width: 100%;\r\n}\r\n\r\n.swiper-container2 {\r\n  width: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.soDetailBanner {\r\n  background-repeat: no-repeat;\r\n  background-size: 100%;\r\n}\r\n</style>\r\n"]}]}