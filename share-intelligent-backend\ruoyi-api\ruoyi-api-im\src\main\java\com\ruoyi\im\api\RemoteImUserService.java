package com.ruoyi.im.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api
 * @ClassName: remoteImUserService
 * @Author: ${maguojun}
 * @Description: ${description}
 * @Date: 2022/3/11 9:23
 * @Version: 1.0
 */
@FeignClient(contextId = "remoteImUserService", value = ServiceNameConstants.IM_SERVICE)
public interface RemoteImUserService {

    /***
     * ImUser分页条件搜索实现
     * @param
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/im/user/search/{page}/{size}")
    R<TableDataInfo> findPage(@PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);

    /***
     * ImUser分页条件搜索实现
     * @param
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/im/user/query/{page}/{size}")
    R<TableDataInfo> queryPage(@RequestBody(required = false) ImUser imUser,@PathVariable("page") int page, @PathVariable("size") int size, @RequestParam(value = "fields", required = false) String fields);


    /**
     * @Description:
     * @Param:
     * @return:
     * @Author: 新增ImUser数据注册账户
     * @Date:
     */
    @PostMapping(value = "/im/user/add")
    R<JSONObject> add(@RequestBody ImUser imUser);

    /**
     * @Description:
     * @Param:
     * @return:
     * @Author: 根据userId集合查询所有用户信息数据
     * @Date:
     */
    @GetMapping("/im/user/userlist")
    R<List<ImUser>> findByUseridList(@RequestParam("userId") String userId);

    /**
     * 客服列表
     * @return
     * @param module
     */
    @GetMapping(value = "/im/user/customer" )
    R<List<ImUser>> findCustomer(@RequestParam(value = "module",required = false)String module);

    /**
     * @Description:
     * @Param:
     * @return:
     * @Author: 查看好友的基本详情信息
     * @Date:
     */
    @PostMapping(value = "/im/user/detail")
    R<ImUser> detail(@RequestParam("friendId") String
                                  friendId);

    /**
     * 接受Token
     *
     * @param jsonArray
     * @return
     */
    @PostMapping(value = "/im/user/token")
    R<Boolean> token(@RequestBody JSONArray jsonArray);

    /**
    * @Description:
    * @Param:
    * @return:
    * @Author: 设置客服
    * @Date:
    */
    @PostMapping(value="/im/user/update")
    R<Boolean> update(@RequestBody ImUser imUser);

    @PostMapping(value = "/im/user/sync" )
    R<Boolean> syncUser(@RequestParam("userName") String userName, @RequestParam("nickName") String nickName,
                             @RequestParam("avatar") String avatar, @RequestParam("rongYunToken") String rongYunToken);

    @GetMapping(value = "/im/user/detail/userid" )
    R<JSONObject> getUserByUserId(@RequestParam("userId") String userId);

    @PostMapping(value = "/im/user/openid" )
    R<Boolean> updateOpenid(@RequestBody JSONObject imUser);
}
