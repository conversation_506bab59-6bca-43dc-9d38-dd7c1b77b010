{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index.vue?vue&type=style&index=0&id=2720d356&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index.vue", "mtime": 1750311963019}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4UA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/solution", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">解决方案</div>\r\n      <div style=\"height: 33px; margin-top: 1px\"></div>\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\">\r\n            <el-form-item>\r\n              <el-input v-model=\"params.searchStr\" placeholder=\"请输入搜索内容\" class=\"activity-search-input\">\r\n                <el-button slot=\"append\" class=\"activity-search-btn\" @click=\"onSearch\">搜索</el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"hot_search\">\r\n        <span>热门搜索：</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('供应链管理')\">供应链管理</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('设备智慧物联')\">设备智慧物联</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('生产过程管控')\">生产过程管控</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('科技成果转化')\">科技成果转化</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('企业运营管理')\">企业运营管理</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('产业转型升级')\">产业转型升级</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('产融服务')\">产融服务</span>\r\n      </div>\r\n    </div>\r\n    <!-- 底部内容 -->\r\n    <div class=\"content_bottom\">\r\n      <div class=\"icondiv\">\r\n        <div class=\"solutioniconFlex\">\r\n          <div v-for=\"(item, index) in typeList\" :key=\"item.id\"\r\n            :class=\"['iconFlexTitle', aaa == item.id ? 'activeTitle' : '']\" @click=\"changeSolve(item.id)\">\r\n            {{ item.typeName }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"demandContent\" style=\"padding-top: 40px\">\r\n        <div class=\"demandflex\" style=\"height: 715px\">\r\n          <div class=\"leftsolution\">\r\n            <div :class=\"['leftTitle', bbb == 1 ? 'leftTitleHover' : '']\" @click=\"changeSolveB(1)\">\r\n              全部（{{ total1 }}）\r\n            </div>\r\n            <div v-for=\"(item, index) in typeNestList\" :key=\"index\" :class=\"[\r\n              'leftTitle',\r\n              bbb == item.solutionTypeId ? 'leftTitleHover' : '',\r\n            ]\" @click=\"changeSolveB(item.solutionTypeId)\">\r\n              <span class=\"tr2\">{{ item.solutionTypeName }}（{{ item.totalCount }}）</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"rightSolution\" v-if=\"dataList && dataList.length > 0\">\r\n            <div v-for=\"(item, index) in dataList\" :key=\"index\" class=\"solutionContent tr2\">\r\n              <div @click=\"goDetail(item.solutionId)\">\r\n                <div class=\"solutionContentTitle tr2\">\r\n                  {{ item.solutionName }}\r\n                </div>\r\n                <div class=\"solutionContentValue tr2 textOverflow\">\r\n                  {{ item.solutionIntroduction }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"rightEmpty\" v-else>\r\n            <el-empty description=\"暂无数据\"></el-empty>\r\n          </div>\r\n        </div>\r\n        <!-- 分页 -->\r\n        <div class=\"pageStyle\">\r\n          <el-pagination v-if=\"dataList && dataList.length > 0\" background layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\" :page-size=\"params.pageSize\" :current-page=\"params.pageNum\" :total=\"total\"\r\n            @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getSolutionList, getSolutionTypeList } from \"@/api/solution\";\r\nexport default {\r\n  name: \"demandHall\",\r\n  data() {\r\n    return {\r\n      params: {\r\n        parentId: \"\",\r\n        searchStr: \"\",\r\n        solutionTypeId: \"\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        category: 1,\r\n      },\r\n      total: 0,\r\n      total1: 0,\r\n      keywords: \"\",\r\n      form: {},\r\n      flag: \"全部\",\r\n      appliTypeData: [\r\n        {\r\n          dictValue: \"0\",\r\n          dictLabel: \"全部\",\r\n        },\r\n        {\r\n          dictLabel: \"创新研发\",\r\n          dictValue: \"1\",\r\n        },\r\n        {\r\n          dictLabel: \"物料采购\",\r\n          dictValue: \"2\",\r\n        },\r\n        {\r\n          dictLabel: \"智能制造\",\r\n          dictValue: \"3\",\r\n        },\r\n        {\r\n          dictLabel: \"数字化管理\",\r\n          dictValue: \"4\",\r\n        },\r\n        {\r\n          dictLabel: \"软件服务\",\r\n          dictValue: \"5\",\r\n        },\r\n        {\r\n          dictLabel: \"供应链金融\",\r\n          dictValue: \"6\",\r\n        },\r\n        {\r\n          dictLabel: \"运营宣传\",\r\n          dictValue: \"7\",\r\n        },\r\n        {\r\n          dictLabel: \"其他\",\r\n          dictValue: \"8\",\r\n        },\r\n      ],\r\n      appliTypeImgList: [\r\n        {\r\n          url: require(\"@/assets/appliMarket/type1.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type2.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type3.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type4.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type5.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type6.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type7.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type8.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type9.png\"),\r\n        },\r\n      ],\r\n      demandList: [\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n      ],\r\n      aaa: \"1\",\r\n      bbb: 1,\r\n      typeList: [\r\n        {\r\n          id: \"1\",\r\n          typeName: \"行业解决方案\",\r\n        },\r\n        {\r\n          id: \"2\",\r\n          typeName: \"领域解决方案\",\r\n        },\r\n      ],\r\n      typeNestList: [],\r\n      dataList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getTypeNext('1');\r\n  },\r\n  methods: {\r\n    async getDemandList() {\r\n      this.params.category = this.aaa;\r\n      let res = await getSolutionList(this.params);\r\n      if (res.code == 200) {\r\n        this.dataList = res.rows;\r\n        this.total = res.total;\r\n        console.log(this.total, \"total\");\r\n        if (this.params.solutionTypeId == \"\") {\r\n          this.total1 = res.total;\r\n        }\r\n      }\r\n    },\r\n    searchHot(val) {\r\n      this.params.searchStr = val;\r\n      this.onSearch();\r\n    },\r\n    onSearch() {\r\n      this.params.pageNum = 1;\r\n      this.getDemandList();\r\n    },\r\n    getappliData(value) {\r\n      this.flag = value;\r\n      this.getDemandList();\r\n    },\r\n    async getTypeNext(val) {\r\n      let res = await getSolutionTypeList({ category: val });\r\n      if (res.code == 200) {\r\n        this.typeNestList = res.rows;\r\n        this.getDemandList();\r\n      }\r\n    },\r\n    changeSolve(val) {\r\n      this.aaa = val;\r\n      this.params.parentId = val;\r\n      this.params.solutionTypeId = \"\";\r\n      this.bbb = 1;\r\n      this.params.pageSize = 10;\r\n      this.params.pageNum = 1;\r\n      this.getTypeNext(val);\r\n    },\r\n    changeSolveB(val) {\r\n      this.bbb = val;\r\n      this.params.pageSize = 10;\r\n      this.params.pageNum = 1;\r\n      if (val == 1) {\r\n        this.params.solutionTypeId = \"\";\r\n      } else {\r\n        this.params.solutionTypeId = val;\r\n      }\r\n      this.getDemandList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.params.pageSize = pageSize;\r\n      this.getDemandList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.params.pageNum = pageNum;\r\n      this.getDemandList();\r\n    },\r\n\r\n    goDetail(id) {\r\n      this.$router.push(\"/solutionDetail?id=\" + id);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n\r\n  .hot_search {\r\n    font-size: 14px;\r\n    color: #000;\r\n\r\n    .hot_search_item {\r\n      margin-right: 20px;\r\n      color: #000;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  .icondiv {\r\n    background-color: rgba(255, 255, 255, 1);\r\n    width: 100%;\r\n    height: 100px;\r\n    position: relative;\r\n\r\n    .solutioniconFlex {\r\n      display: flex;\r\n      position: absolute;\r\n      bottom: 0;\r\n      width: 1200px;\r\n      right: 0;\r\n      left: 0;\r\n      margin: auto;\r\n      justify-content: center;\r\n\r\n      .iconFlexTitle {\r\n        width: 110px;\r\n        height: 45px;\r\n        line-height: 26px;\r\n        border-radius: 2px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 18px;\r\n        text-align: center;\r\n        margin: 0 20px;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .activeTitle {\r\n        color: #0cad9d;\r\n        border-bottom: 2px solid #0cad9d;\r\n      }\r\n    }\r\n  }\r\n\r\n  .demandContent {\r\n    width: 100%;\r\n    background: #f7f8fa;\r\n    // background: #fff;\r\n    padding-top: 20px;\r\n    box-shadow: #21c9b8 solid 1px;\r\n    // border: #21c9b8 solid 1px;\r\n\r\n    .demandflex {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      flex-wrap: wrap;\r\n\r\n      .leftsolution {\r\n        width: 185px;\r\n        height: 715px;\r\n        line-height: 20px;\r\n        opacity: 0.95;\r\n        border-radius: 4px;\r\n        background: linear-gradient(180deg,\r\n            rgba(244, 246, 249, 1) 0%,\r\n            rgba(255, 255, 255, 1) 100%);\r\n        color: rgba(16, 16, 16, 1);\r\n        font-size: 14px;\r\n        box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);\r\n        border: 2px solid rgba(255, 255, 255, 1);\r\n        padding: 20px 0;\r\n        box-sizing: border-box;\r\n        overflow-y: auto;\r\n\r\n        .leftTitle {\r\n          color: rgba(51, 51, 51, 1);\r\n          font-size: 16px;\r\n          margin: 30px 0;\r\n          padding-left: 20px;\r\n          border-left: 3px solid transparent;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .leftTitleHover {\r\n          color: #0cad9d;\r\n          border-left: 3px solid #0cad9d;\r\n        }\r\n      }\r\n\r\n      .rightSolution {\r\n        width: 1000px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        flex-wrap: wrap;\r\n        align-content: flex-start;\r\n\r\n        .solutionContent {\r\n          width: 490px;\r\n          height: 124px;\r\n          border: 2px solid transparent;\r\n          padding: 20px;\r\n          box-sizing: border-box;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .solutionContent:hover {\r\n          opacity: 0.95;\r\n          border-radius: 4px;\r\n          background: linear-gradient(180deg,\r\n              rgba(244, 246, 249, 1) 0%,\r\n              rgba(255, 255, 255, 1) 100%);\r\n          color: rgba(16, 16, 16, 1);\r\n          font-size: 14px;\r\n          box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);\r\n          border: 2px solid rgba(255, 255, 255, 1);\r\n        }\r\n\r\n        .solutionContentTitle {\r\n          color: rgba(51, 51, 51, 1);\r\n          font-size: 18px;\r\n          margin-bottom: 10px;\r\n        }\r\n\r\n        .solutionContent:hover .solutionContentTitle {\r\n          color: #0cad9d;\r\n        }\r\n\r\n        .solutionContentValue {\r\n          color: rgba(102, 102, 102, 1);\r\n          font-size: 12px;\r\n          line-height: 1.5;\r\n        }\r\n      }\r\n\r\n      .rightEmpty {\r\n        width: 1000px;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.activity-title-content {\r\n  width: 100%;\r\n\r\n  // background-color: #fff;\r\n  .activity-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .activity-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .activity-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .activity-search-box {\r\n    margin-top: 40px;\r\n\r\n    .activity-search-form {\r\n      text-align: center;\r\n\r\n      .activity-search-input {\r\n        width: 792px;\r\n        height: 54px;\r\n\r\n        .activity-search-btn {\r\n          width: 100px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n\r\n  .content_bottom_item {\r\n    margin-top: 20px;\r\n    width: 590px;\r\n    height: 208px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 4px 18px 2px #e8f1fa;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    cursor: pointer;\r\n\r\n    .detailTitle {\r\n      height: 30px;\r\n      color: rgba(51, 51, 51, 1);\r\n      font-size: 18px;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .textOverflow1 {\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 1;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n\r\n    .demandChunk {\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .demand_right {\r\n        width: 413px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n      }\r\n\r\n      .demandTopRightflex {\r\n        display: flex;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .detailrightTitle {\r\n        color: rgba(153, 153, 153, 1);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightTitle2 {\r\n        color: rgba(0, 0, 0, 0.85);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightContent {\r\n        width: 343px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .content_bottom_item:hover {\r\n    box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);\r\n    scale: 1.01;\r\n  }\r\n\r\n  .content_bottom_item:nth-child(2n) {\r\n    margin-left: 20px;\r\n  }\r\n}\r\n\r\n.pageStyle {\r\n  margin-top: 60px;\r\n  width: 100%;\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.activity-search-input {\r\n  .el-input__inner {\r\n    height: 54px;\r\n    background: #fff;\r\n    border-radius: 27px 0 0 27px;\r\n    border: 1px solid #d9d9d9;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    line-height: 24px;\r\n    padding-left: 30px;\r\n  }\r\n\r\n  .el-input-group__append {\r\n    border-radius: 0px 100px 100px 0px;\r\n    background: #21c9b8;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    color: #fff;\r\n    line-height: 24px;\r\n  }\r\n}\r\n</style>\r\n"]}]}