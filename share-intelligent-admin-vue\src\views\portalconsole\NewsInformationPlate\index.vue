<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="父级编码" prop="parentId">
        <el-input
          v-model="queryParams.parentId"
          placeholder="请输入父级编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="名称" prop="newsInformationPlateName">
        <el-input
          v-model="queryParams.newsInformationPlateName"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:NewsInformationPlate:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:NewsInformationPlate:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:NewsInformationPlate:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:NewsInformationPlate:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> 

    <el-table v-loading="loading" :data="NewsInformationPlateList" 
    @selection-change="handleSelectionChange"
    row-key="newsInformationPlateId"
    :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <!-- <el-table-column label="咨询板块ID" align="center" prop="newsInformationPlateId" /> -->
      <el-table-column label="父级编码" align="center" prop="parentId" />
      <el-table-column label="名称" align="center" prop="newsInformationPlateName" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:NewsInformationPlate:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:NewsInformationPlate:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改咨询板块对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="父级编码" prop="parentId">
          <!-- <el-input v-model="form.parentId" placeholder="请输入父级编码" /> -->
          <Treeselect v-model="form.parentId" 
          :options="industryOptions" 
          :normalizer="normalizer" 
          placeholder="请选择父级编码" />
        </el-form-item>
        <el-form-item label="名称" prop="newsInformationPlateName">
          <el-input v-model="form.newsInformationPlateName" placeholder="请输入名称" />
        </el-form-item>
        <!-- <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item> -->
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listNewsInformationPlate, getNewsInformationPlate, delNewsInformationPlate, addNewsInformationPlate, updateNewsInformationPlate } from "@/api/portalconsole/NewsInformationPlate";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: "NewsInformationPlate",
  components:{
    Treeselect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 咨询板块表格数据
      NewsInformationPlateList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        parentId: null,
        newsInformationPlateName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      industryOptions:[],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询咨询板块列表 */
    getList() {
      this.loading = true;
      listNewsInformationPlate(this.queryParams).then(response => {
        this.NewsInformationPlateList =this.handleTree(response.rows, "newsInformationPlateId", "parentId");
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        newsInformationPlateId: null,
        parentId: null,
        newsInformationPlateName: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.newsInformationPlateId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getTreeselect()
      this.open = true;
      this.title = "添加咨询板块";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect()
      const newsInformationPlateId = row.newsInformationPlateId || this.ids
      getNewsInformationPlate(newsInformationPlateId).then(response => {
        this.form = response.data;
        if (response.data.parentId!= null) {
          this.form.parentId = response.data.parentId;
        }else{
          this.form.parentId=0
        }
        this.open = true;
        this.title = "修改咨询板块";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(this.form.parentId==0){
            this.form.parentId=null
          }
          if (this.form.newsInformationPlateId != null) {
            updateNewsInformationPlate(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNewsInformationPlate(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const newsInformationPlateIds = row.newsInformationPlateId || this.ids;
      this.$modal.confirm('是否确认删除咨询板块编号为"' + newsInformationPlateIds + '"的数据项？').then(function() {
        return delNewsInformationPlate(newsInformationPlateIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/NewsInformationPlate/export', {
        ...this.queryParams
      }, `NewsInformationPlate_${new Date().getTime()}.xlsx`)
    },
    /** 转换行业维护数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      };
    },
    //树形下拉
    getTreeselect() {
      let queryParams={
        pageNum: 1,
        pageSize: 1000,
        parentId: null,
        solutionTypeName: null,
      }
      listNewsInformationPlate(queryParams).then(response => {
        console.log("data",response.rows)
        let arr=[]
        response.rows.forEach(item => {
          arr.push({
            id:item.newsInformationPlateId,
            pId:item.parentId,
            name:item.newsInformationPlateName
          })
          
        });
        this.industryOptions=[]
        //console.log("arr",arr)
        const list = { id: 0, name: '顶级节点', children: [] };
        list.children = this.handleTree(arr, "id", "pId");
        this.industryOptions.push(list);
        console.log("this.industryOptions",this.industryOptions)
      });
    },
  }
};
</script>
