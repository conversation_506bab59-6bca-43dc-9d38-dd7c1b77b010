import request from '@/utils/request'

// 查询服务需求(NEW)列表
export function listDemand(query) {
  return request({
    url: '/portalconsole/demand/list',
    method: 'get',
    params: query
  })
}

// 查询服务需求(NEW)详细
export function getDemand(id) {
  return request({
    url: '/portalconsole/demand/' + id,
    method: 'get'
  })
}

// 新增服务需求(NEW)
export function addDemand(data) {
  return request({
    url: '/portalconsole/demand',
    method: 'post',
    data: data
  })
}

// 修改服务需求(NEW)
export function updateDemand(data) {
  return request({
    url: '/portalconsole/demand',
    method: 'put',
    data: data
  })
}

// 删除服务需求(NEW)
export function delDemand(id) {
  return request({
    url: '/portalconsole/demand/' + id,
    method: 'delete'
  })
}
