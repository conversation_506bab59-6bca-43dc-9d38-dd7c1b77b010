{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\approveSetting\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\approveSetting\\index.vue", "mtime": 1750311963043}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IEZpbGVVcGxvYWQgZnJvbSAiQC9jb21wb25lbnRzL0ZpbGVVcGxvYWQiOw0KaW1wb3J0IHN0b3JlIGZyb20gIkAvc3RvcmUiOw0KaW1wb3J0IHsgZ2V0Q29tcGFueUxpc3RCeU5hbWUgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7DQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7DQppbXBvcnQgew0KICBwZXJzb25hbEFwcHJvdmUsDQogIGNvbXBhbnlBcHByb3ZlLA0KICBnZXRBcHByb3ZlRGV0YWlsLA0KfSBmcm9tICJAL2FwaS9zeXN0ZW0vYXBwcm92ZS5qcyI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJBcHByb3ZlU2V0dGluZyIsDQogIGNvbXBvbmVudHM6IHsgVXNlck1lbnUsIEZpbGVVcGxvYWQgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgYWN0aXZlTmFtZTogIjEiLA0KICAgICAgdXBsb2FkVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9maWxlL3VwbG9hZCIsIC8v5LiK5Lyg5Zyw5Z2ADQogICAgICBhY2NlcHQ6ICIuanBnLCAuanBlZywgLnBuZywgLmJtcCIsDQogICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkgfSwNCiAgICAgIGNvbXBhbnlPcHRpb25zOiBbXSwNCiAgICAgIGlzQWRtaW46IGZhbHNlLA0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBpbWdWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHBlcnNvbmFsRWRpdDogZmFsc2UsDQogICAgICBjb21wYW55RWRpdDogZmFsc2UsDQogICAgICBjb21wYW55TG9hZGluZzogZmFsc2UsDQogICAgICBwZXJzb25hbExvYWRpbmc6IGZhbHNlLA0KICAgICAgaW1hZ2VVcmw6ICIiLA0KICAgICAgdXNlcjogew0KICAgICAgICB1c2VySWQ6IHN0b3JlLmdldHRlcnMudXNlcklkLA0KICAgICAgICBidXNzaW5lc3NObzogc3RvcmUuZ2V0dGVycy5idXNzaW5lc3NObywNCiAgICAgICAgcGhvbmVudW1iZXI6IHN0b3JlLmdldHRlcnMucGhvbmVudW1iZXIsDQogICAgICB9LA0KICAgICAgcGVyc29uYWxGb3JtOiB7DQogICAgICAgIHBlcnNvbmFsQ2FyZExpc3Q6IFtdLA0KICAgICAgfSwNCiAgICAgIGNvbXBhbnlGb3JtOiB7DQogICAgICAgIGF1dGhNYXRUZXJpYWxMaXN0OiBbXSwNCiAgICAgICAgY29tcGFueUNhcmRMaXN0OiBbXSwNCiAgICAgIH0sDQogICAgICBwZXJzb25hUnVsZXM6IHsNCiAgICAgICAgY29tcGFueU5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5YWs5Y+45ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIG5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi55yf5a6e5aeT5ZCN5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIHBob25lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiBlOezu+aWueW8j+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBwZXJzb25hbENhcmRMaXN0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1lc3NhZ2U6ICLouqvku73or4HmmI7kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgICBjb21wYW55UnVsZXM6IHsNCiAgICAgICAgbmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnnJ/lrp7lp5PlkI3kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29tcGFueU5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5YWs5Y+45ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQoNCiAgICAgICAgcGhvbmU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6IGU57O75pa55byP5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGF1dGhNYXRUZXJpYWxMaXN0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1lc3NhZ2U6ICLmjojmnYPkuabkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29tcGFueUNhcmRMaXN0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1lc3NhZ2U6ICLpmYTku7bkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldEFwcHJvdmVEZXRhaWwoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUNsaWNrKHRhYiwgZXZlbnQpIHsNCiAgICAgIGlmICh0aGlzLmFjdGl2ZU5hbWUgPT0gIjEiKSB7DQogICAgICB9IGVsc2Ugew0KICAgICAgfQ0KICAgICAgdGhpcy5nZXRBcHByb3ZlRGV0YWlsKCk7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zID0gew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogNSwNCiAgICAgIH07DQogICAgfSwNCiAgICBjaGFuZ2VQZXJzb25FZGl0KCkgew0KICAgICAgdGhpcy5wZXJzb25hbEVkaXQgPSB0cnVlOw0KICAgIH0sDQogICAgY2hhbmdlQ29tcGFueUVkaXQoKSB7DQogICAgICB0aGlzLmNvbXBhbnlFZGl0ID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8vIOS6p+WTgeeFp+eJh+S4iuS8oOS5i+WJjeeahOmSqeWtkA0KICAgIGhhbmRsZUJlZm9yZVVwbG9hZChmaWxlKSB7DQogICAgICBsZXQgeyBuYW1lLCB0eXBlLCBzaXplIH0gPSBmaWxlOw0KICAgICAgbGV0IHR5cGVMaXN0ID0gdGhpcy5hY2NlcHQNCiAgICAgICAgLnNwbGl0KCIsIikNCiAgICAgICAgLm1hcCgoaXRlbSkgPT4gaXRlbS50cmltKCkudG9Mb3dlckNhc2UoKS5zdWJzdHIoMSkpOw0KICAgICAgbGV0IGRvdEluZGV4ID0gbmFtZS5sYXN0SW5kZXhPZigiLiIpOw0KICAgICAgLy8g5paH5Lu257G75Z6L5qCh6aqMDQogICAgICBpZiAoZG90SW5kZXggPT09IC0xKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivt+S4iuS8oOato+ehruagvOW8j+eahOaWh+S7tiIpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9IGVsc2Ugew0KICAgICAgICBsZXQgc3VmZml4ID0gbmFtZS5zdWJzdHJpbmcoZG90SW5kZXggKyAxKTsNCiAgICAgICAgaWYgKHR5cGVMaXN0LmluZGV4T2Yoc3VmZml4LnRvTG93ZXJDYXNlKCkpID09PSAtMSkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivt+S4iuS8oOato+ehruagvOW8j+eahOaWh+S7tiIpOw0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLy8g5paH5Lu25LiK5Lyg5aSn5bCP6ZmQ5Yi2DQogICAgICBpZiAoc2l6ZSA+IDEwNDg1NzYgKiAyMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmlofku7blpKflsI/kuI3og73otoXov4cyME3vvIEiKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g54K55Ye75Lqn5ZOB54Wn54mHDQogICAgaGFuZGxlUGVyc29uYWxDYXJkUHJldmlldyhmaWxlKSB7DQogICAgICB0aGlzLmltYWdlVXJsID0gZmlsZS51cmw7DQogICAgICB0aGlzLmltZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgaGFuZGxlQ29tcGFueUNhcmRQcmV2aWV3KGZpbGUpIHsNCiAgICAgIHRoaXMuaW1hZ2VVcmwgPSBmaWxlLnVybDsNCiAgICAgIHRoaXMuaW1nVmlzaWJsZSA9IHRydWU7DQogICAgfSwNCiAgICAvLyDliKDpmaTkuqflk4HnhafniYcNCiAgICBoYW5kbGVSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMucGVyc29uYWxGb3JtLnBlcnNvbmFsQ2FyZExpc3QgPSBmaWxlTGlzdDsNCiAgICB9LA0KICAgIC8vIOWIoOmZpOS6p+WTgeeFp+eJhw0KICAgIGhhbmRsZUNvbXBhbnlDYXJkUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLmNvbXBhbnlGb3JtLmNvbXBhbnlDYXJkTGlzdCA9IGZpbGVMaXN0Ow0KICAgIH0sDQogICAgaGFuZGxlY29tcGFueUNhcmRTdWNjZXNzKHJlcywgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIGlmICghdGhpcy5jb21wYW55Rm9ybS5jb21wYW55Rm9ybSkgew0KICAgICAgICB0aGlzLmNvbXBhbnlGb3JtLmNvbXBhbnlDYXJkTGlzdCA9IFtdOw0KICAgICAgfQ0KICAgICAgLy/mraTlpITlhpnkuIrkvKBvc3PmiJDlip/kuYvlkI7nmoTpgLvovpENCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy5jb21wYW55Rm9ybS5jb21wYW55Q2FyZExpc3QucHVzaChyZXMuZGF0YSk7DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVQZXJzb25hbENhcmRTdWNjZXNzKHJlcywgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIGlmICghdGhpcy5wZXJzb25hbEZvcm0ucGVyc29uYWxDYXJkTGlzdCkgew0KICAgICAgICB0aGlzLnBlcnNvbmFsRm9ybS5wZXJzb25hbENhcmRMaXN0ID0gW107DQogICAgICB9DQogICAgICAvL+atpOWkhOWGmeS4iuS8oG9zc+aIkOWKn+S5i+WQjueahOmAu+i+kQ0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICB0aGlzLnBlcnNvbmFsRm9ybS5wZXJzb25hbENhcmRMaXN0LnB1c2gocmVzLmRhdGEpOw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQXV0aE1hdFRlcmlhbFN1Y2Nlc3MocmVzLCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgaWYgKCF0aGlzLmNvbXBhbnlGb3JtLmF1dGhNYXRUZXJpYWxMaXN0KSB7DQogICAgICAgIHRoaXMuY29tcGFueUZvcm0uYXV0aE1hdFRlcmlhbExpc3QgPSBbXTsNCiAgICAgIH0NCiAgICAgIC8v5q2k5aSE5YaZ5LiK5Lygb3Nz5oiQ5Yqf5LmL5ZCO55qE6YC76L6RDQogICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIHRoaXMuY29tcGFueUZvcm0uYXV0aE1hdFRlcmlhbExpc3QucHVzaChyZXMuZGF0YSk7DQogICAgICB9DQogICAgfSwNCiAgICBnZXRDb21wYW55TGlzdChxdWVyeSkgew0KICAgICAgaWYgKHF1ZXJ5ICE9PSAiIikgew0KICAgICAgICBnZXRDb21wYW55TGlzdEJ5TmFtZShxdWVyeSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICB0aGlzLmNvbXBhbnlPcHRpb25zID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBjb21wYW55Q2hhbmdlZChyZXMpIHsNCiAgICAgIHRoaXMuY29tcGFueU9wdGlvbnMuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICBpZiAoaXRlbS5pZCA9PSByZXMpIHsNCiAgICAgICAgICB0aGlzLmNvbXBhbnlGb3JtLmJ1c3NpbmVzc05vID0gaXRlbS5jcmVkaXRDb2RlOw0KICAgICAgICAgIHRoaXMuY29tcGFueUZvcm0udGlhbnlhbklkID0gaXRlbS5pZDsNCiAgICAgICAgICB0aGlzLmNvbXBhbnlGb3JtLmNvbXBhbnlOYW1lID0gaXRlbS5uYW1lOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHBlcnNvbkNoYW5nZWQocmVzKSB7DQogICAgICB0aGlzLmNvbXBhbnlPcHRpb25zLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgaWYgKGl0ZW0uaWQgPT0gcmVzKSB7DQogICAgICAgICAgdGhpcy5wZXJzb25hbEZvcm0uYnVzc2luZXNzTm8gPSBpdGVtLmNyZWRpdENvZGU7DQogICAgICAgICAgdGhpcy5wZXJzb25hbEZvcm0udGlhbnlhbklkID0gaXRlbS5pZDsNCiAgICAgICAgICB0aGlzLnBlcnNvbmFsRm9ybS5jb21wYW55TmFtZSA9IGl0ZW0ubmFtZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVvbXBhbnlDYXJkUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLmNvbXBhbnlGb3JtLmNvbXBhbnlDYXJkTGlzdCA9IHJlcy5kYXRhOw0KICAgIH0sDQogICAgc3VibWl0UGVyc29uYWwoKSB7DQogICAgICB0aGlzLiRyZWZzLnBlcnNvbmFsRm9ybS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgcGVyc29uYWxBcHByb3ZlKHsNCiAgICAgICAgICAgIC4uLnRoaXMucGVyc29uYWxGb3JtLA0KICAgICAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmk43kvZzmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRBcHByb3ZlRGV0YWlsKCk7DQogICAgICAgICAgICAgIHRoaXMucGVyc29uYWxFZGl0ID0gZmFsc2U7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgc3VibWl0Q29tcGFueSgpIHsNCiAgICAgIC8vIGlmICghdGhpcy5jb21wYW55Rm9ybS5hdXRoTWF0VGVyaWFsTGlzdCkgew0KICAgICAgLy8gICB0aGlzLiRtb2RhbC5tc2dFcnJvcigi6K+35LiK5Lyg5o6I5p2D5LmmIik7DQogICAgICAvLyB9DQogICAgICB0aGlzLiRyZWZzLmNvbXBhbnlGb3JtLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBjb21wYW55QXBwcm92ZSh7DQogICAgICAgICAgICAuLi50aGlzLmNvbXBhbnlGb3JtLA0KICAgICAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmk43kvZzmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRBcHByb3ZlRGV0YWlsKCk7DQogICAgICAgICAgICAgIHRoaXMuY29tcGFueUVkaXQgPSBmYWxzZTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBkb3dubG9hZCgpIHsNCiAgICAgIC8vIGxldCB1cmwgPQ0KICAgICAgLy8gICAiaHR0cHM6Ly94cC10ZWNoLm9zcy1jbi1iZWlqaW5nLmFsaXl1bmNzLmNvbS9jeWtjLzIwMjMwMjEzLzE2NzYyNzc4MDE0NTQ3OTAuZG9jeCI7DQogICAgICAvLyB3aW5kb3cub3Blbih1cmwsICJfYmxhbmsiKTsNCiAgICAgIHZhciBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpOw0KICAgICAgbGluay5ocmVmID0NCiAgICAgICAgImh0dHBzOi8veHAtdGVjaC5vc3MtY24tYmVpamluZy5hbGl5dW5jcy5jb20vY3lrYy8yMDIzMDIxMy8xNjc2Mjc3ODAxNDU0NzkwLmRvY3giOw0KICAgICAgbGluay5kb3dubG9hZCA9ICLmjojmnYPkuaYiOw0KICAgICAgbGluay50YXJnZXQgPSAiX2JsYW5rIjsNCiAgICAgIGxpbmsuY2xpY2soKTsNCiAgICB9LA0KDQogICAgZ2V0QXBwcm92ZURldGFpbCgpIHsNCiAgICAgIGdldEFwcHJvdmVEZXRhaWwoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgIHRoaXMucGVyc29uYWxGb3JtID0gcmVzLmRhdGEgPz8ge307DQogICAgICAgICAgdGhpcy5jb21wYW55Rm9ybSA9IHJlcy5kYXRhID8/IHt9Ow0KICAgICAgICAgIGlmICghdGhpcy5wZXJzb25hbEZvcm0ucGVyc29uYWxDYXJkTGlzdCkgew0KICAgICAgICAgICAgdGhpcy5wZXJzb25hbEZvcm0ucGVyc29uYWxDYXJkTGlzdCA9IFtdOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoIXRoaXMuY29tcGFueUZvcm0uYXV0aE1hdFRlcmlhbExpc3QpIHsNCiAgICAgICAgICAgIHRoaXMuY29tcGFueUZvcm0uYXV0aE1hdFRlcmlhbExpc3QgPSBbXTsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKCF0aGlzLmNvbXBhbnlGb3JtLmNvbXBhbnlDYXJkTGlzdCkgew0KICAgICAgICAgICAgdGhpcy5jb21wYW55Rm9ybS5jb21wYW55Q2FyZExpc3QgPSBbXTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6QA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/approveSetting", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-12 10:25:16\r\n * @LastEditTime: 2023-02-27 14:43:13\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-11 11:21:19\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"approve-setting-container\">\r\n          <div class=\"header-small\">\r\n            <div class=\"red-tag\"></div>\r\n            申请认证\r\n          </div>\r\n          <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n            <el-tab-pane label=\"名片认证\" name=\"1\">\r\n              <el-form\r\n                ref=\"personalForm\"\r\n                :model=\"personalForm\"\r\n                :rules=\"personaRules\"\r\n                label-width=\"120px\"\r\n              >\r\n                <el-form-item label=\"真实姓名：\" prop=\"name\">\r\n                  <div v-if=\"!personalEdit\">{{ personalForm.name }}</div>\r\n                  <el-input\r\n                    v-model=\"personalForm.name\"\r\n                    v-else\r\n                    placeholder=\"请输入真实姓名\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式：\" prop=\"phone\">\r\n                  <div v-if=\"!personalEdit\">{{ personalForm.phone }}</div>\r\n                  <el-input\r\n                    v-model=\"personalForm.phone\"\r\n                    v-else\r\n                    placeholder=\"请选择联系方式\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"公司名称：\" prop=\"companyName\">\r\n                  <div v-if=\"!personalEdit\">{{ personalForm.companyName }}</div>\r\n                  <el-select\r\n                    v-model=\"personalForm.companyName\"\r\n                    v-else\r\n                    filterable\r\n                    remote\r\n                    reserve-keyword\r\n                    placeholder=\"请输入关键词\"\r\n                    :remote-method=\"getCompanyList\"\r\n                    :loading=\"personalLoading\"\r\n                    @change=\"personChanged\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in companyOptions\"\r\n                      :key=\"item.id\"\r\n                      :label=\"item.name\"\r\n                      :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"身份证明：\" prop=\"personalCardList\">\r\n                  <div class=\"pic-class\">\r\n                    <el-upload\r\n                      list-type=\"picture-card\"\r\n                      :headers=\"headers\"\r\n                      :action=\"uploadUrl\"\r\n                      :class=\"[personalEdit ? '' : 'hide']\"\r\n                      :disabled=\"!personalEdit\"\r\n                      :file-list=\"personalForm.personalCardList\"\r\n                      :accept=\"accept\"\r\n                      :before-upload=\"handleBeforeUpload\"\r\n                      :on-preview=\"handlePersonalCardPreview\"\r\n                      :on-remove=\"handleRemove\"\r\n                      :on-success=\"handlePersonalCardSuccess\"\r\n                    >\r\n                      <i class=\"el-icon-plus\"></i>\r\n                      <div\r\n                        v-if=\"personalEdit\"\r\n                        slot=\"tip\"\r\n                        class=\"el-upload__tip\"\r\n                      >\r\n                        点击上传\r\n                        <span class=\"red-text\">名片、工作证、工牌</span>\r\n                        等可以认证身份的图片\r\n                      </div>\r\n                    </el-upload>\r\n                  </div>\r\n\r\n                  <el-dialog\r\n                    append-to-body\r\n                    :visible.sync=\"imgVisible\"\r\n                    :close-on-click-modal=\"false\"\r\n                  >\r\n                    <img width=\"100%\" :src=\"imageUrl\" alt=\"\" />\r\n                  </el-dialog>\r\n                </el-form-item>\r\n              </el-form>\r\n              <div class=\"button-container\">\r\n                <el-button\r\n                  v-if=\"!personalEdit\"\r\n                  type=\"danger\"\r\n                  @click=\"changePersonEdit\"\r\n                  >编辑</el-button\r\n                >\r\n                <el-button v-else type=\"danger\" @click=\"submitPersonal()\"\r\n                  >保存</el-button\r\n                >\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane label=\"企业认证\" name=\"2\">\r\n              <div class=\"staff-list\">\r\n                <el-form\r\n                  ref=\"companyForm\"\r\n                  :model=\"companyForm\"\r\n                  :rules=\"companyRules\"\r\n                  label-width=\"120px\"\r\n                >\r\n                  <el-row>\r\n                    <el-col :span=\"24\">\r\n                      <el-form-item label=\"真实姓名：\" prop=\"name\">\r\n                        <div v-if=\"!companyEdit\">\r\n                          {{ companyForm.name }}\r\n                        </div>\r\n                        <el-input\r\n                          v-model=\"companyForm.name\"\r\n                          v-else\r\n                          placeholder=\"请输入真实姓名\"\r\n                        />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n\r\n                  <el-row>\r\n                    <el-col :span=\"24\">\r\n                      <el-form-item label=\"联系方式：\" prop=\"phone\">\r\n                        <div v-if=\"!companyEdit\">\r\n                          {{ companyForm.phone }}\r\n                        </div>\r\n                        <el-input\r\n                          v-model=\"companyForm.phone\"\r\n                          v-else\r\n                          placeholder=\"请选择联系方式\"\r\n                        />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row>\r\n                    <el-col :span=\"24\">\r\n                      <el-form-item label=\"公司名称：\" prop=\"companyName\">\r\n                        <div v-if=\"!companyEdit\">\r\n                          {{ companyForm.companyName }}\r\n                        </div>\r\n                        <el-select\r\n                          v-model=\"companyForm.companyName\"\r\n                          v-else\r\n                          filterable\r\n                          remote\r\n                          reserve-keyword\r\n                          placeholder=\"请输入关键词\"\r\n                          :remote-method=\"getCompanyList\"\r\n                          :loading=\"companyLoading\"\r\n                          @change=\"companyChanged\"\r\n                        >\r\n                          <el-option\r\n                            v-for=\"item in companyOptions\"\r\n                            :key=\"item.id\"\r\n                            :label=\"item.name\"\r\n                            :value=\"item.id\"\r\n                          >\r\n                          </el-option>\r\n                        </el-select>\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-form-item label=\"企业授权书：\">\r\n                    <div class=\"pic-class\">\r\n                      <el-upload\r\n                        list-type=\"picture-card\"\r\n                        :class=\"[companyEdit ? '' : 'hide']\"\r\n                        :headers=\"headers\"\r\n                        :action=\"uploadUrl\"\r\n                        :file-list=\"companyForm.companyCardList\"\r\n                        :accept=\"accept\"\r\n                        :disabled=\"!companyEdit\"\r\n                        :before-upload=\"handleBeforeUpload\"\r\n                        :on-preview=\"handleCompanyCardPreview\"\r\n                        :on-remove=\"handleCompanyCardRemove\"\r\n                        :on-success=\"handlecompanyCardSuccess\"\r\n                      >\r\n                        <i class=\"el-icon-plus\"></i>\r\n                        <div\r\n                          v-if=\"companyEdit\"\r\n                          slot=\"tip\"\r\n                          class=\"el-upload__tip\"\r\n                        >\r\n                          支持jpg、png格式\r\n                          <a class=\"red-text\" @click=\"download\">查看模板>></a>\r\n                        </div>\r\n                      </el-upload>\r\n                    </div>\r\n                    <el-dialog\r\n                      append-to-body\r\n                      :visible.sync=\"imgVisible\"\r\n                      :close-on-click-modal=\"false\"\r\n                    >\r\n                      <img width=\"100%\" :src=\"imageUrl\" alt=\"\" />\r\n                    </el-dialog>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"\" prop=\"companyCardList\">\r\n                    <el-upload\r\n                      :file-list=\"companyForm.authMatTerialList\"\r\n                      :headers=\"headers\"\r\n                      :action=\"uploadUrl\"\r\n                      :disabled=\"!companyEdit\"\r\n                      accept=\".pdf, .docx, .xls\"\r\n                      :on-remove=\"handleompanyCardRemove\"\r\n                      :on-success=\"handleAuthMatTerialSuccess\"\r\n                      :limit=\"10\"\r\n                    >\r\n                      <el-button\r\n                        v-if=\"companyEdit\"\r\n                        class=\"apathy-upload-btn\"\r\n                        size=\"small\"\r\n                        icon=\"el-icon-upload2\"\r\n                        >上传文件\r\n                      </el-button>\r\n                      <span\r\n                        v-if=\"companyEdit\"\r\n                        slot=\"tip\"\r\n                        class=\"el-upload__tip\"\r\n                      >\r\n                        仅限doc、pdf、xls格式\r\n                      </span>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-form>\r\n                <div class=\"button-container\">\r\n                  <el-button\r\n                    v-if=\"!companyEdit\"\r\n                    type=\"danger\"\r\n                    @click=\"changeCompanyEdit\"\r\n                    >编辑</el-button\r\n                  >\r\n                  <el-button v-else type=\"danger\" @click=\"submitCompany()\"\r\n                    >保存</el-button\r\n                  >\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport FileUpload from \"@/components/FileUpload\";\r\nimport store from \"@/store\";\r\nimport { getCompanyListByName } from \"@/api/system/user\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport {\r\n  personalApprove,\r\n  companyApprove,\r\n  getApproveDetail,\r\n} from \"@/api/system/approve.js\";\r\nexport default {\r\n  name: \"ApproveSetting\",\r\n  components: { UserMenu, FileUpload },\r\n  data() {\r\n    return {\r\n      activeName: \"1\",\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      companyOptions: [],\r\n      isAdmin: false,\r\n      loading: false,\r\n      imgVisible: false,\r\n      personalEdit: false,\r\n      companyEdit: false,\r\n      companyLoading: false,\r\n      personalLoading: false,\r\n      imageUrl: \"\",\r\n      user: {\r\n        userId: store.getters.userId,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        phonenumber: store.getters.phonenumber,\r\n      },\r\n      personalForm: {\r\n        personalCardList: [],\r\n      },\r\n      companyForm: {\r\n        authMatTerialList: [],\r\n        companyCardList: [],\r\n      },\r\n      personaRules: {\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        name: [\r\n          { required: true, message: \"真实姓名不能为空\", trigger: \"blur\" },\r\n        ],\r\n        phone: [\r\n          { required: true, message: \"联系方式不能为空\", trigger: \"blur\" },\r\n        ],\r\n        personalCardList: [\r\n          { required: false, message: \"身份证明不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      companyRules: {\r\n        name: [\r\n          { required: true, message: \"真实姓名不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n\r\n        phone: [\r\n          { required: true, message: \"联系方式不能为空\", trigger: \"blur\" },\r\n        ],\r\n        authMatTerialList: [\r\n          { required: false, message: \"授权书不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyCardList: [\r\n          { required: false, message: \"附件不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getApproveDetail();\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      if (this.activeName == \"1\") {\r\n      } else {\r\n      }\r\n      this.getApproveDetail();\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 5,\r\n      };\r\n    },\r\n    changePersonEdit() {\r\n      this.personalEdit = true;\r\n    },\r\n    changeCompanyEdit() {\r\n      this.companyEdit = true;\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePersonalCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    handleCompanyCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.personalForm.personalCardList = fileList;\r\n    },\r\n    // 删除产品照片\r\n    handleCompanyCardRemove(file, fileList) {\r\n      this.companyForm.companyCardList = fileList;\r\n    },\r\n    handlecompanyCardSuccess(res, file, fileList) {\r\n      if (!this.companyForm.companyForm) {\r\n        this.companyForm.companyCardList = [];\r\n      }\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.companyForm.companyCardList.push(res.data);\r\n      }\r\n    },\r\n    handlePersonalCardSuccess(res, file, fileList) {\r\n      if (!this.personalForm.personalCardList) {\r\n        this.personalForm.personalCardList = [];\r\n      }\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.personalForm.personalCardList.push(res.data);\r\n      }\r\n    },\r\n    handleAuthMatTerialSuccess(res, file, fileList) {\r\n      if (!this.companyForm.authMatTerialList) {\r\n        this.companyForm.authMatTerialList = [];\r\n      }\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.companyForm.authMatTerialList.push(res.data);\r\n      }\r\n    },\r\n    getCompanyList(query) {\r\n      if (query !== \"\") {\r\n        getCompanyListByName(query).then((response) => {\r\n          this.companyOptions = response.data;\r\n        });\r\n      }\r\n    },\r\n    companyChanged(res) {\r\n      this.companyOptions.forEach((item) => {\r\n        if (item.id == res) {\r\n          this.companyForm.bussinessNo = item.creditCode;\r\n          this.companyForm.tianyanId = item.id;\r\n          this.companyForm.companyName = item.name;\r\n        }\r\n      });\r\n    },\r\n    personChanged(res) {\r\n      this.companyOptions.forEach((item) => {\r\n        if (item.id == res) {\r\n          this.personalForm.bussinessNo = item.creditCode;\r\n          this.personalForm.tianyanId = item.id;\r\n          this.personalForm.companyName = item.name;\r\n        }\r\n      });\r\n    },\r\n    handleompanyCardRemove(file, fileList) {\r\n      this.companyForm.companyCardList = res.data;\r\n    },\r\n    submitPersonal() {\r\n      this.$refs.personalForm.validate((valid) => {\r\n        if (valid) {\r\n          personalApprove({\r\n            ...this.personalForm,\r\n          }).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.getApproveDetail();\r\n              this.personalEdit = false;\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    submitCompany() {\r\n      // if (!this.companyForm.authMatTerialList) {\r\n      //   this.$modal.msgError(\"请上传授权书\");\r\n      // }\r\n      this.$refs.companyForm.validate((valid) => {\r\n        if (valid) {\r\n          companyApprove({\r\n            ...this.companyForm,\r\n          }).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.getApproveDetail();\r\n              this.companyEdit = false;\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    download() {\r\n      // let url =\r\n      //   \"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230213/1676277801454790.docx\";\r\n      // window.open(url, \"_blank\");\r\n      var link = document.createElement(\"a\");\r\n      link.href =\r\n        \"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230213/1676277801454790.docx\";\r\n      link.download = \"授权书\";\r\n      link.target = \"_blank\";\r\n      link.click();\r\n    },\r\n\r\n    getApproveDetail() {\r\n      getApproveDetail().then((res) => {\r\n        if (res.code == 200) {\r\n          this.personalForm = res.data ?? {};\r\n          this.companyForm = res.data ?? {};\r\n          if (!this.personalForm.personalCardList) {\r\n            this.personalForm.personalCardList = [];\r\n          }\r\n          if (!this.companyForm.authMatTerialList) {\r\n            this.companyForm.authMatTerialList = [];\r\n          }\r\n          if (!this.companyForm.companyCardList) {\r\n            this.companyForm.companyCardList = [];\r\n          }\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .approve-setting-container {\r\n    width: 100%;\r\n    background: #fff;\r\n    min-height: 700px;\r\n    padding: 20px;\r\n    .el-select {\r\n      display: inline-block;\r\n      position: relative;\r\n      width: 100%;\r\n    }\r\n    .el-button--danger {\r\n      background: #21c9b8;\r\n      color: #fff;\r\n      border-color: #21c9b8;\r\n    }\r\n    .header-small {\r\n      text-align: center;\r\n      display: flex;\r\n      font-size: 20px;\r\n      font-weight: 500;\r\n      color: #333333;\r\n      line-height: 20px;\r\n\r\n      .red-tag {\r\n        margin-right: 12px;\r\n        width: 3px;\r\n        height: 22px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .apathy-upload-btn {\r\n      margin-right: 20px;\r\n    }\r\n    .button-container {\r\n      width: 100%;\r\n      margin-top: 50px;\r\n      text-align: center;\r\n    }\r\n\r\n    .el-tabs__nav {\r\n      width: 100%;\r\n      height: 40px;\r\n      padding: 0 43%;\r\n      display: flex;\r\n      // justify-content: space-between;\r\n    }\r\n\r\n    .el-tabs__nav-wrap::after {\r\n      background-color: transparent;\r\n    }\r\n    .el-tabs__active-bar {\r\n      margin-left: 43%;\r\n      background-color: #21c9b8;\r\n    }\r\n    .el-tabs__item.is-active {\r\n      color: #21c9b8;\r\n    }\r\n    .el-tabs__item:hover {\r\n      color: #21c9b8;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n  .pic-class .hide .el-upload--picture-card {\r\n    display: none;\r\n  }\r\n  .el-pagination {\r\n    width: 100%;\r\n    margin-top: 20px;\r\n    text-align: center;\r\n  }\r\n  .el-pagination.is-background .el-pager li {\r\n    background-color: #fff;\r\n  }\r\n  .el-pagination.is-background .el-pager li:not(.disabled).active {\r\n    background-color: #21c9b8;\r\n    color: #ffffff;\r\n  }\r\n  .el-pagination.is-background .el-pager li:not(.disabled):hover {\r\n    color: #21c9b8;\r\n  }\r\n  .red-text {\r\n    color: #21c9b8;\r\n  }\r\n  .trans-form {\r\n    border-radius: 6px;\r\n    background: #ffffff;\r\n    width: 400px;\r\n    padding: 25px 5px 5px 25px;\r\n    .header {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #121620;\r\n      line-height: 18px;\r\n      margin-bottom: 12px;\r\n    }\r\n    .el-input {\r\n      height: 38px;\r\n      input {\r\n        height: 38px;\r\n      }\r\n    }\r\n    .el-select {\r\n      display: inline-block;\r\n      position: relative;\r\n      width: 100%;\r\n    }\r\n    .input-icon {\r\n      height: 39px;\r\n      width: 14px;\r\n      margin-left: 2px;\r\n    }\r\n    .el-form-item--medium .el-form-item__content {\r\n      display: flex;\r\n      line-height: 36px;\r\n    }\r\n    .el-form-item {\r\n      margin-bottom: 12px;\r\n    }\r\n    .el-input__suffix-inner {\r\n      .active-style {\r\n        color: #21c9b8;\r\n        font-size: 14px;\r\n      }\r\n      .disabled-style {\r\n        color: #999;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n  .el-button--primary {\r\n    /* color: #FFFFFF; */\r\n    background-color: #21c9b8 !important;\r\n    border-color: #21c9b8;\r\n  }\r\n}\r\n</style>\r\n"]}]}