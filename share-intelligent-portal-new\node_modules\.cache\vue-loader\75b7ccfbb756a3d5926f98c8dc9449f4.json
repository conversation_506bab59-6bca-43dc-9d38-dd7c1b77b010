{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\application\\detail.vue?vue&type=style&index=0&id=f25ed524&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\application\\detail.vue", "mtime": 1750311963040}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogI2Y0ZjVmOTsNCn0NCi5jb250ZW50IHsNCiAgd2lkdGg6IDEwMCU7DQogIHBhZGRpbmc6IDQwcHg7DQogIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIC8vIGJhY2tncm91bmQ6IHJnYigyNDIsIDI0OCwgMjU1KTsNCiAgLnRhYlN0eWxlIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIC5idXR0b25TdHlsZSB7DQogICAgICB3aWR0aDogMTAwcHg7DQogICAgICBwYWRkaW5nOiAxMHB4Ow0KICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICBib3JkZXI6IDFweCBzb2xpZCAjMjFjOWI4Ow0KICAgIH0NCiAgICAuYnV0dG9uU3R5bGU6bnRoLWNoaWxkKDEpIHsNCiAgICAgIGJvcmRlci1yaWdodDogbm9uZTsNCiAgICB9DQogICAgLmJ1dHRvblN0eWxlOm50aC1jaGlsZCgyKSB7DQogICAgICBib3JkZXItbGVmdDogbm9uZTsNCiAgICB9DQogICAgLmJ1dHRvbkhvdmVyIHsNCiAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICB9DQogIH0NCiAgLnRpdGxlIHsNCiAgICBmb250LXNpemU6IDE4cHg7DQogICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICB9DQogIC5idG5TdHlsZSB7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICB9DQp9DQo6OnYtZGVlcCAuYXBwbGlEZXRhaWwgaW1nIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMTAwJTsNCiAgb2JqZWN0LWZpdDogY29udGFpbjsNCn0NCg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsSA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/system/user/application", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\">\r\n          <div class=\"tabStyle\">\r\n            <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 1 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(1)\"\r\n            >\r\n              基本信息\r\n            </div>\r\n            <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 2 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(2)\"\r\n            >\r\n              开发管理\r\n            </div>\r\n          </div>\r\n          <div style=\"margin-left: 20px\">\r\n            <div v-show=\"flag == 1\">\r\n              <div style=\"margin-top: 20px\">\r\n                <div class=\"title\">应用凭证</div>\r\n                <el-descriptions>\r\n                  <el-descriptions-item label=\"AppKey:\"\r\n                    >kooriookami</el-descriptions-item\r\n                  >\r\n                  <el-descriptions-item label=\"AESKey\"\r\n                    >18100000000</el-descriptions-item\r\n                  >\r\n                  <el-descriptions-item label=\"AppSecret\"\r\n                    >苏州市</el-descriptions-item\r\n                  >\r\n                </el-descriptions>\r\n              </div>\r\n              <div style=\"margin-top: 20px\">\r\n                <div class=\"title\">应用信息</div>\r\n                <el-form\r\n                  :model=\"ruleForm\"\r\n                  label-width=\"100px\"\r\n                  class=\"demo-ruleForm\"\r\n                >\r\n                  <el-form-item label=\"应用名称:\">\r\n                    {{ ruleForm.appName }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用类型:\">\r\n                    {{ ruleForm.appCategory }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用服务端:\">\r\n                    {{ ruleForm.applicaServer == 0 ? \"APP端\" : \"web端\" }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"交付方式:\">\r\n                    {{ ruleForm.delivery == 0 ? \"Saas服务\" : \"本地部署\" }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用简介:\">\r\n                    {{ ruleForm.briefInto }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用详情:\">\r\n                    <div class=\"appliDetail\" v-html=\"ruleForm.content\"></div>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用封面:\">\r\n                    <img\r\n                      style=\"width: 100px; height: 100px\"\r\n                      :src=\"ruleForm.appLogo\"\r\n                      alt=\"\"\r\n                    />\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用提供:\">\r\n                    {{ ruleForm.supply }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"联系人:\">\r\n                    {{ ruleForm.linkman }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"联系方式:\">\r\n                    {{ ruleForm.phone }}\r\n                  </el-form-item>\r\n                </el-form>\r\n              </div>\r\n              <div style=\"margin-top: 20px\">\r\n                <div class=\"title\">商品规格信息</div>\r\n                <div style=\"margin-top: 20px\">\r\n                  <el-table\r\n                    :data=\"tableData\"\r\n                    style=\"width: 50%; mni-height: 200px\"\r\n                  >\r\n                    <el-table-column prop=\"spec\" label=\"规格\" width=\"180\">\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      prop=\"userNumber\"\r\n                      label=\"使用用户数\"\r\n                      width=\"180\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column label=\"有效时间\" width=\"200\">\r\n                      <template slot-scope=\"scope\">\r\n                        {{ parseTime(scope.row.validTime) }}\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n              </div>\r\n              <div style=\"margin-top: 20px\">\r\n                <div class=\"title\">商品价格信息</div>\r\n                <div style=\"margin-top: 20px\">\r\n                  <el-table\r\n                    :data=\"tableData\"\r\n                    style=\"width: 70%; mni-height: 200px\"\r\n                  >\r\n                    <!-- <el-table-column prop=\"date\" label=\"规格\">\r\n                    </el-table-column> -->\r\n                    <el-table-column prop=\"orderCode\" label=\"订货编码\">\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      prop=\"originalPrice\"\r\n                      label=\"商品原价（元）\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      prop=\"promotionPrice\"\r\n                      label=\"商品促销价（元）\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      prop=\"commissionRatio\"\r\n                      label=\"商品分佣比例（%）\"\r\n                    >\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n              </div>\r\n              <div style=\"margin-top: 20px\">\r\n                <div class=\"title\">商品参数信息</div>\r\n                <div style=\"margin-top: 20px\">\r\n                  <el-form\r\n                    :model=\"ruleForm\"\r\n                    label-width=\"160px\"\r\n                    class=\"demo-ruleForm\"\r\n                  >\r\n                    <el-form-item label=\"服务咨询电话:\">\r\n                      {{ ruleForm.appName }}\r\n                    </el-form-item>\r\n                    <el-form-item label=\"产品运营联系人手机号:\">\r\n                      {{ ruleForm.appCategory }}\r\n                    </el-form-item>\r\n                  </el-form>\r\n                  <!-- <el-table\r\n                    :data=\"tableData\"\r\n                    style=\"width: 50%; mni-height: 200px\"\r\n                  >\r\n                    <el-table-column prop=\"date\" label=\"名称\" width=\"180\">\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"name\" label=\"内容\" width=\"180\">\r\n                    </el-table-column>\r\n                  </el-table> -->\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div v-show=\"flag == 2\">\r\n              <div style=\"margin-top: 20px\">\r\n                <div class=\"title\">开发管理配置</div>\r\n                <el-form\r\n                  ref=\"ruleForm\"\r\n                  label-width=\"170px\"\r\n                  class=\"demo-ruleForm\"\r\n                >\r\n                  <el-form-item label=\"服务器出口IP:\">\r\n                    {{ ruleForm.serverIp }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"网页端（web）应用地址:\">\r\n                    {{ ruleForm.webUrl }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"网页端（web）体验地址:\">\r\n                    {{ ruleForm.webexperienceUrl }}\r\n                  </el-form-item>\r\n                  <!-- <el-form-item label=\"订单信息接收地址:\">\r\n                    {{ruleForm.serverexamineUrl}}\r\n                  </el-form-item> -->\r\n                  <el-form-item label=\"健康检查服务端地址:\">\r\n                    {{ ruleForm.serverexamineUrl }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"开发联系人:\">\r\n                    {{ ruleForm.developmentPeople }}\r\n                  </el-form-item>\r\n                  <!-- <el-form-item label=\"测试token:\" prop=\"name\"> </el-form-item> -->\r\n                </el-form>\r\n                <!-- <el-descriptions :column=\"1\" style=\"height: 300px\">\r\n                  <el-descriptions-item label=\"服务器出口IP\"\r\n                    >kooriookami</el-descriptions-item\r\n                  >\r\n                  <el-descriptions-item label=\"网页端（web）应用地址\"\r\n                    >18100000000</el-descriptions-item\r\n                  >\r\n                  <el-descriptions-item label=\"网页端（web）体验地址\"\r\n                    >苏州市</el-descriptions-item\r\n                  >\r\n                  <el-descriptions-item label=\"订单信息接收地址\"\r\n                    >苏州市</el-descriptions-item\r\n                  >\r\n                  <el-descriptions-item label=\"健康检查服务端地址\"\r\n                    >苏州市</el-descriptions-item\r\n                  >\r\n                  <el-descriptions-item label=\"开发联系人手机号\"\r\n                    >苏州市</el-descriptions-item\r\n                  >\r\n                  <el-descriptions-item label=\"测试token\"\r\n                    >daddad#215</el-descriptions-item\r\n                  >\r\n                </el-descriptions> -->\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"btnStyle\">\r\n            <el-button @click=\"goBack\">返回</el-button>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { appliDetail } from \"@/api/appliMarket\";\r\nimport store from \"@/store\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      flag: 1,\r\n      tableData: [\r\n        {\r\n          date: \"2016-05-02\",\r\n          name: \"王小虎\",\r\n          address: \"上海市普陀区金沙江路 1518 弄\",\r\n        },\r\n        {\r\n          date: \"2016-05-04\",\r\n          name: \"王小虎\",\r\n          address: \"上海市普陀区金沙江路 1517 弄\",\r\n        },\r\n        {\r\n          date: \"2016-05-01\",\r\n          name: \"王小虎\",\r\n          address: \"上海市普陀区金沙江路 1519 弄\",\r\n        },\r\n        {\r\n          date: \"2016-05-03\",\r\n          name: \"王小虎\",\r\n          address: \"上海市普陀区金沙江路 1516 弄\",\r\n        },\r\n      ],\r\n      ruleForm: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    getDetail() {\r\n      this.tableData = [];\r\n      let id = this.$route.query.id;\r\n      let params = {\r\n        id,\r\n        userId: store.getters.userId,\r\n      };\r\n      appliDetail(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.ruleForm = res.data;\r\n          this.tableData.push(res.data);\r\n        }\r\n      });\r\n    },\r\n    getFlag(value) {\r\n      this.flag = value;\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  // background: rgb(242, 248, 255);\r\n  .tabStyle {\r\n    display: flex;\r\n    .buttonStyle {\r\n      width: 100px;\r\n      padding: 10px;\r\n      color: #21c9b8;\r\n      text-align: center;\r\n      cursor: pointer;\r\n      border: 1px solid #21c9b8;\r\n    }\r\n    .buttonStyle:nth-child(1) {\r\n      border-right: none;\r\n    }\r\n    .buttonStyle:nth-child(2) {\r\n      border-left: none;\r\n    }\r\n    .buttonHover {\r\n      background: #21c9b8;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .title {\r\n    font-size: 18px;\r\n    font-weight: 600;\r\n    margin-bottom: 10px;\r\n  }\r\n  .btnStyle {\r\n    text-align: center;\r\n  }\r\n}\r\n::v-deep .appliDetail img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n}\r\n</style>\r\n"]}]}