{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\utils\\generator\\html.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\utils\\generator\\html.js", "mtime": 1750311962903}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_config", "require", "confGlobal", "someSpanIsNot24", "dialogWrapper", "str", "concat", "vueTemplate", "vueScript", "cssStyle", "cssStr", "buildFormTemplate", "conf", "child", "type", "labelPosition", "disabled", "formRef", "formModel", "formRules", "size", "labelWidth", "buildFromBtns", "gutter", "formBtns", "colWrapper", "element", "span", "layouts", "colFormItem", "required", "trigger", "tag", "tagDom", "tags", "label", "vModel", "rowFormItem", "justify", "align", "children", "map", "el", "layout", "join", "elButton", "_attrBuilder", "attrBuilder", "icon", "buildElButtonChild", "elInput", "_attrBuilder2", "clearable", "placeholder", "width", "maxlength", "showWordLimit", "readonly", "prefixIcon", "suffixIcon", "showPassword", "autosize", "minRows", "maxRows", "buildElInputChild", "elInputNumber", "_attrBuilder3", "controlsPosition", "min", "max", "step", "stepStrictly", "precision", "elSelect", "_attrBuilder4", "filterable", "multiple", "buildElSelectChild", "elRadioGroup", "_attrBuilder5", "buildElRadioGroupChild", "elCheckboxGroup", "_attrBuilder6", "buildElCheckboxGroupChild", "elSwitch", "_attrBuilder7", "activeText", "inactiveText", "activeColor", "inactiveColor", "activeValue", "JSON", "stringify", "inactiveValue", "elCascader", "_attrBuilder8", "options", "props", "showAllLevels", "separator", "<PERSON><PERSON><PERSON><PERSON>", "_attrBuilder9", "range", "showStops", "elTimePicker", "_attrBuilder0", "startPlaceholder", "endPlaceholder", "rangeSeparator", "isRange", "format", "valueFormat", "pickerOptions", "elDatePicker", "_attrBuilder1", "elRate", "_attrBuilder10", "allowHalf", "showText", "showScore", "elColorPicker", "_attrBuilder11", "showAlpha", "colorFormat", "elUpload", "action", "listType", "accept", "name", "autoUpload", "beforeUpload", "fileList", "ref", "buildElUploadChild", "style", "default", "push", "prepend", "append", "length", "optionType", "border", "list", "buttonText", "showTip", "fileSize", "sizeUnit", "makeUpHtml", "htmlList", "fields", "some", "item", "for<PERSON>ach", "htmlStr", "temp"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/utils/generator/html.js"], "sourcesContent": ["/* eslint-disable max-len */\r\nimport { trigger } from './config'\r\n\r\nlet confGlobal\r\nlet someSpanIsNot24\r\n\r\nexport function dialogWrapper(str) {\r\n  return `<el-dialog v-bind=\"$attrs\" v-on=\"$listeners\" @open=\"onOpen\" @close=\"onClose\" title=\"Dialog Title\">\r\n    ${str}\r\n    <div slot=\"footer\">\r\n      <el-button @click=\"close\">取消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleConfirm\">确定</el-button>\r\n    </div>\r\n  </el-dialog>`\r\n}\r\n\r\nexport function vueTemplate(str) {\r\n  return `<template>\r\n    <div>\r\n      ${str}\r\n    </div>\r\n  </template>`\r\n}\r\n\r\nexport function vueScript(str) {\r\n  return `<script>\r\n    ${str}\r\n  </script>`\r\n}\r\n\r\nexport function cssStyle(cssStr) {\r\n  return `<style>\r\n    ${cssStr}\r\n  </style>`\r\n}\r\n\r\nfunction buildFormTemplate(conf, child, type) {\r\n  let labelPosition = ''\r\n  if (conf.labelPosition !== 'right') {\r\n    labelPosition = `label-position=\"${conf.labelPosition}\"`\r\n  }\r\n  const disabled = conf.disabled ? `:disabled=\"${conf.disabled}\"` : ''\r\n  let str = `<el-form ref=\"${conf.formRef}\" :model=\"${conf.formModel}\" :rules=\"${conf.formRules}\" size=\"${conf.size}\" ${disabled} label-width=\"${conf.labelWidth}px\" ${labelPosition}>\r\n      ${child}\r\n      ${buildFromBtns(conf, type)}\r\n    </el-form>`\r\n  if (someSpanIsNot24) {\r\n    str = `<el-row :gutter=\"${conf.gutter}\">\r\n        ${str}\r\n      </el-row>`\r\n  }\r\n  return str\r\n}\r\n\r\nfunction buildFromBtns(conf, type) {\r\n  let str = ''\r\n  if (conf.formBtns && type === 'file') {\r\n    str = `<el-form-item size=\"large\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">提交</el-button>\r\n          <el-button @click=\"resetForm\">重置</el-button>\r\n        </el-form-item>`\r\n    if (someSpanIsNot24) {\r\n      str = `<el-col :span=\"24\">\r\n          ${str}\r\n        </el-col>`\r\n    }\r\n  }\r\n  return str\r\n}\r\n\r\n// span不为24的用el-col包裹\r\nfunction colWrapper(element, str) {\r\n  if (someSpanIsNot24 || element.span !== 24) {\r\n    return `<el-col :span=\"${element.span}\">\r\n      ${str}\r\n    </el-col>`\r\n  }\r\n  return str\r\n}\r\n\r\nconst layouts = {\r\n  colFormItem(element) {\r\n    let labelWidth = ''\r\n    if (element.labelWidth && element.labelWidth !== confGlobal.labelWidth) {\r\n      labelWidth = `label-width=\"${element.labelWidth}px\"`\r\n    }\r\n    const required = !trigger[element.tag] && element.required ? 'required' : ''\r\n    const tagDom = tags[element.tag] ? tags[element.tag](element) : null\r\n    let str = `<el-form-item ${labelWidth} label=\"${element.label}\" prop=\"${element.vModel}\" ${required}>\r\n        ${tagDom}\r\n      </el-form-item>`\r\n    str = colWrapper(element, str)\r\n    return str\r\n  },\r\n  rowFormItem(element) {\r\n    const type = element.type === 'default' ? '' : `type=\"${element.type}\"`\r\n    const justify = element.type === 'default' ? '' : `justify=\"${element.justify}\"`\r\n    const align = element.type === 'default' ? '' : `align=\"${element.align}\"`\r\n    const gutter = element.gutter ? `gutter=\"${element.gutter}\"` : ''\r\n    const children = element.children.map(el => layouts[el.layout](el))\r\n    let str = `<el-row ${type} ${justify} ${align} ${gutter}>\r\n      ${children.join('\\n')}\r\n    </el-row>`\r\n    str = colWrapper(element, str)\r\n    return str\r\n  }\r\n}\r\n\r\nconst tags = {\r\n  'el-button': el => {\r\n    const {\r\n      tag, disabled\r\n    } = attrBuilder(el)\r\n    const type = el.type ? `type=\"${el.type}\"` : ''\r\n    const icon = el.icon ? `icon=\"${el.icon}\"` : ''\r\n    const size = el.size ? `size=\"${el.size}\"` : ''\r\n    let child = buildElButtonChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${type} ${icon} ${size} ${disabled}>${child}</${el.tag}>`\r\n  },\r\n  'el-input': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const maxlength = el.maxlength ? `:maxlength=\"${el.maxlength}\"` : ''\r\n    const showWordLimit = el['show-word-limit'] ? 'show-word-limit' : ''\r\n    const readonly = el.readonly ? 'readonly' : ''\r\n    const prefixIcon = el['prefix-icon'] ? `prefix-icon='${el['prefix-icon']}'` : ''\r\n    const suffixIcon = el['suffix-icon'] ? `suffix-icon='${el['suffix-icon']}'` : ''\r\n    const showPassword = el['show-password'] ? 'show-password' : ''\r\n    const type = el.type ? `type=\"${el.type}\"` : ''\r\n    const autosize = el.autosize && el.autosize.minRows\r\n      ? `:autosize=\"{minRows: ${el.autosize.minRows}, maxRows: ${el.autosize.maxRows}}\"`\r\n      : ''\r\n    let child = buildElInputChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${vModel} ${type} ${placeholder} ${maxlength} ${showWordLimit} ${readonly} ${disabled} ${clearable} ${prefixIcon} ${suffixIcon} ${showPassword} ${autosize} ${width}>${child}</${el.tag}>`\r\n  },\r\n  'el-input-number': el => {\r\n    const { disabled, vModel, placeholder } = attrBuilder(el)\r\n    const controlsPosition = el['controls-position'] ? `controls-position=${el['controls-position']}` : ''\r\n    const min = el.min ? `:min='${el.min}'` : ''\r\n    const max = el.max ? `:max='${el.max}'` : ''\r\n    const step = el.step ? `:step='${el.step}'` : ''\r\n    const stepStrictly = el['step-strictly'] ? 'step-strictly' : ''\r\n    const precision = el.precision ? `:precision='${el.precision}'` : ''\r\n\r\n    return `<${el.tag} ${vModel} ${placeholder} ${step} ${stepStrictly} ${precision} ${controlsPosition} ${min} ${max} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-select': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const filterable = el.filterable ? 'filterable' : ''\r\n    const multiple = el.multiple ? 'multiple' : ''\r\n    let child = buildElSelectChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${vModel} ${placeholder} ${disabled} ${multiple} ${filterable} ${clearable} ${width}>${child}</${el.tag}>`\r\n  },\r\n  'el-radio-group': el => {\r\n    const { disabled, vModel } = attrBuilder(el)\r\n    const size = `size=\"${el.size}\"`\r\n    let child = buildElRadioGroupChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${vModel} ${size} ${disabled}>${child}</${el.tag}>`\r\n  },\r\n  'el-checkbox-group': el => {\r\n    const { disabled, vModel } = attrBuilder(el)\r\n    const size = `size=\"${el.size}\"`\r\n    const min = el.min ? `:min=\"${el.min}\"` : ''\r\n    const max = el.max ? `:max=\"${el.max}\"` : ''\r\n    let child = buildElCheckboxGroupChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${vModel} ${min} ${max} ${size} ${disabled}>${child}</${el.tag}>`\r\n  },\r\n  'el-switch': el => {\r\n    const { disabled, vModel } = attrBuilder(el)\r\n    const activeText = el['active-text'] ? `active-text=\"${el['active-text']}\"` : ''\r\n    const inactiveText = el['inactive-text'] ? `inactive-text=\"${el['inactive-text']}\"` : ''\r\n    const activeColor = el['active-color'] ? `active-color=\"${el['active-color']}\"` : ''\r\n    const inactiveColor = el['inactive-color'] ? `inactive-color=\"${el['inactive-color']}\"` : ''\r\n    const activeValue = el['active-value'] !== true ? `:active-value='${JSON.stringify(el['active-value'])}'` : ''\r\n    const inactiveValue = el['inactive-value'] !== false ? `:inactive-value='${JSON.stringify(el['inactive-value'])}'` : ''\r\n\r\n    return `<${el.tag} ${vModel} ${activeText} ${inactiveText} ${activeColor} ${inactiveColor} ${activeValue} ${inactiveValue} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-cascader': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const options = el.options ? `:options=\"${el.vModel}Options\"` : ''\r\n    const props = el.props ? `:props=\"${el.vModel}Props\"` : ''\r\n    const showAllLevels = el['show-all-levels'] ? '' : ':show-all-levels=\"false\"'\r\n    const filterable = el.filterable ? 'filterable' : ''\r\n    const separator = el.separator === '/' ? '' : `separator=\"${el.separator}\"`\r\n\r\n    return `<${el.tag} ${vModel} ${options} ${props} ${width} ${showAllLevels} ${placeholder} ${separator} ${filterable} ${clearable} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-slider': el => {\r\n    const { disabled, vModel } = attrBuilder(el)\r\n    const min = el.min ? `:min='${el.min}'` : ''\r\n    const max = el.max ? `:max='${el.max}'` : ''\r\n    const step = el.step ? `:step='${el.step}'` : ''\r\n    const range = el.range ? 'range' : ''\r\n    const showStops = el['show-stops'] ? `:show-stops=\"${el['show-stops']}\"` : ''\r\n\r\n    return `<${el.tag} ${min} ${max} ${step} ${vModel} ${range} ${showStops} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-time-picker': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const startPlaceholder = el['start-placeholder'] ? `start-placeholder=\"${el['start-placeholder']}\"` : ''\r\n    const endPlaceholder = el['end-placeholder'] ? `end-placeholder=\"${el['end-placeholder']}\"` : ''\r\n    const rangeSeparator = el['range-separator'] ? `range-separator=\"${el['range-separator']}\"` : ''\r\n    const isRange = el['is-range'] ? 'is-range' : ''\r\n    const format = el.format ? `format=\"${el.format}\"` : ''\r\n    const valueFormat = el['value-format'] ? `value-format=\"${el['value-format']}\"` : ''\r\n    const pickerOptions = el['picker-options'] ? `:picker-options='${JSON.stringify(el['picker-options'])}'` : ''\r\n\r\n    return `<${el.tag} ${vModel} ${isRange} ${format} ${valueFormat} ${pickerOptions} ${width} ${placeholder} ${startPlaceholder} ${endPlaceholder} ${rangeSeparator} ${clearable} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-date-picker': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const startPlaceholder = el['start-placeholder'] ? `start-placeholder=\"${el['start-placeholder']}\"` : ''\r\n    const endPlaceholder = el['end-placeholder'] ? `end-placeholder=\"${el['end-placeholder']}\"` : ''\r\n    const rangeSeparator = el['range-separator'] ? `range-separator=\"${el['range-separator']}\"` : ''\r\n    const format = el.format ? `format=\"${el.format}\"` : ''\r\n    const valueFormat = el['value-format'] ? `value-format=\"${el['value-format']}\"` : ''\r\n    const type = el.type === 'date' ? '' : `type=\"${el.type}\"`\r\n    const readonly = el.readonly ? 'readonly' : ''\r\n\r\n    return `<${el.tag} ${type} ${vModel} ${format} ${valueFormat} ${width} ${placeholder} ${startPlaceholder} ${endPlaceholder} ${rangeSeparator} ${clearable} ${readonly} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-rate': el => {\r\n    const { disabled, vModel } = attrBuilder(el)\r\n    const max = el.max ? `:max='${el.max}'` : ''\r\n    const allowHalf = el['allow-half'] ? 'allow-half' : ''\r\n    const showText = el['show-text'] ? 'show-text' : ''\r\n    const showScore = el['show-score'] ? 'show-score' : ''\r\n\r\n    return `<${el.tag} ${vModel} ${allowHalf} ${showText} ${showScore} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-color-picker': el => {\r\n    const { disabled, vModel } = attrBuilder(el)\r\n    const size = `size=\"${el.size}\"`\r\n    const showAlpha = el['show-alpha'] ? 'show-alpha' : ''\r\n    const colorFormat = el['color-format'] ? `color-format=\"${el['color-format']}\"` : ''\r\n\r\n    return `<${el.tag} ${vModel} ${size} ${showAlpha} ${colorFormat} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-upload': el => {\r\n    const disabled = el.disabled ? ':disabled=\\'true\\'' : ''\r\n    const action = el.action ? `:action=\"${el.vModel}Action\"` : ''\r\n    const multiple = el.multiple ? 'multiple' : ''\r\n    const listType = el['list-type'] !== 'text' ? `list-type=\"${el['list-type']}\"` : ''\r\n    const accept = el.accept ? `accept=\"${el.accept}\"` : ''\r\n    const name = el.name !== 'file' ? `name=\"${el.name}\"` : ''\r\n    const autoUpload = el['auto-upload'] === false ? ':auto-upload=\"false\"' : ''\r\n    const beforeUpload = `:before-upload=\"${el.vModel}BeforeUpload\"`\r\n    const fileList = `:file-list=\"${el.vModel}fileList\"`\r\n    const ref = `ref=\"${el.vModel}\"`\r\n    let child = buildElUploadChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${ref} ${fileList} ${action} ${autoUpload} ${multiple} ${beforeUpload} ${listType} ${accept} ${name} ${disabled}>${child}</${el.tag}>`\r\n  }\r\n}\r\n\r\nfunction attrBuilder(el) {\r\n  return {\r\n    vModel: `v-model=\"${confGlobal.formModel}.${el.vModel}\"`,\r\n    clearable: el.clearable ? 'clearable' : '',\r\n    placeholder: el.placeholder ? `placeholder=\"${el.placeholder}\"` : '',\r\n    width: el.style && el.style.width ? ':style=\"{width: \\'100%\\'}\"' : '',\r\n    disabled: el.disabled ? ':disabled=\\'true\\'' : ''\r\n  }\r\n}\r\n\r\n// el-buttin 子级\r\nfunction buildElButtonChild(conf) {\r\n  const children = []\r\n  if (conf.default) {\r\n    children.push(conf.default)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\n// el-input innerHTML\r\nfunction buildElInputChild(conf) {\r\n  const children = []\r\n  if (conf.prepend) {\r\n    children.push(`<template slot=\"prepend\">${conf.prepend}</template>`)\r\n  }\r\n  if (conf.append) {\r\n    children.push(`<template slot=\"append\">${conf.append}</template>`)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\nfunction buildElSelectChild(conf) {\r\n  const children = []\r\n  if (conf.options && conf.options.length) {\r\n    children.push(`<el-option v-for=\"(item, index) in ${conf.vModel}Options\" :key=\"index\" :label=\"item.label\" :value=\"item.value\" :disabled=\"item.disabled\"></el-option>`)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\nfunction buildElRadioGroupChild(conf) {\r\n  const children = []\r\n  if (conf.options && conf.options.length) {\r\n    const tag = conf.optionType === 'button' ? 'el-radio-button' : 'el-radio'\r\n    const border = conf.border ? 'border' : ''\r\n    children.push(`<${tag} v-for=\"(item, index) in ${conf.vModel}Options\" :key=\"index\" :label=\"item.value\" :disabled=\"item.disabled\" ${border}>{{item.label}}</${tag}>`)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\nfunction buildElCheckboxGroupChild(conf) {\r\n  const children = []\r\n  if (conf.options && conf.options.length) {\r\n    const tag = conf.optionType === 'button' ? 'el-checkbox-button' : 'el-checkbox'\r\n    const border = conf.border ? 'border' : ''\r\n    children.push(`<${tag} v-for=\"(item, index) in ${conf.vModel}Options\" :key=\"index\" :label=\"item.value\" :disabled=\"item.disabled\" ${border}>{{item.label}}</${tag}>`)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\nfunction buildElUploadChild(conf) {\r\n  const list = []\r\n  if (conf['list-type'] === 'picture-card') list.push('<i class=\"el-icon-plus\"></i>')\r\n  else list.push(`<el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload\">${conf.buttonText}</el-button>`)\r\n  if (conf.showTip) list.push(`<div slot=\"tip\" class=\"el-upload__tip\">只能上传不超过 ${conf.fileSize}${conf.sizeUnit} 的${conf.accept}文件</div>`)\r\n  return list.join('\\n')\r\n}\r\n\r\nexport function makeUpHtml(conf, type) {\r\n  const htmlList = []\r\n  confGlobal = conf\r\n  someSpanIsNot24 = conf.fields.some(item => item.span !== 24)\r\n  conf.fields.forEach(el => {\r\n    htmlList.push(layouts[el.layout](el))\r\n  })\r\n  const htmlStr = htmlList.join('\\n')\r\n\r\n  let temp = buildFormTemplate(conf, htmlStr, type)\r\n  if (type === 'dialog') {\r\n    temp = dialogWrapper(temp)\r\n  }\r\n  confGlobal = null\r\n  return temp\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AADA;;AAGA,IAAIC,UAAU;AACd,IAAIC,eAAe;AAEZ,SAASC,aAAaA,CAACC,GAAG,EAAE;EACjC,4HAAAC,MAAA,CACID,GAAG;AAMT;AAEO,SAASE,WAAWA,CAACF,GAAG,EAAE;EAC/B,uCAAAC,MAAA,CAEMD,GAAG;AAGX;AAEO,SAASG,SAASA,CAACH,GAAG,EAAE;EAC7B,wBAAAC,MAAA,CACID,GAAG;AAET;AAEO,SAASI,QAAQA,CAACC,MAAM,EAAE;EAC/B,uBAAAJ,MAAA,CACII,MAAM;AAEZ;AAEA,SAASC,iBAAiBA,CAACC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC5C,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIH,IAAI,CAACG,aAAa,KAAK,OAAO,EAAE;IAClCA,aAAa,uBAAAT,MAAA,CAAsBM,IAAI,CAACG,aAAa,OAAG;EAC1D;EACA,IAAMC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ,kBAAAV,MAAA,CAAiBM,IAAI,CAACI,QAAQ,UAAM,EAAE;EACpE,IAAIX,GAAG,qBAAAC,MAAA,CAAoBM,IAAI,CAACK,OAAO,kBAAAX,MAAA,CAAaM,IAAI,CAACM,SAAS,kBAAAZ,MAAA,CAAaM,IAAI,CAACO,SAAS,gBAAAb,MAAA,CAAWM,IAAI,CAACQ,IAAI,SAAAd,MAAA,CAAKU,QAAQ,qBAAAV,MAAA,CAAiBM,IAAI,CAACS,UAAU,WAAAf,MAAA,CAAOS,aAAa,eAAAT,MAAA,CAC5KO,KAAK,cAAAP,MAAA,CACLgB,aAAa,CAACV,IAAI,EAAEE,IAAI,CAAC,qBAClB;EACb,IAAIX,eAAe,EAAE;IACnBE,GAAG,wBAAAC,MAAA,CAAuBM,IAAI,CAACW,MAAM,mBAAAjB,MAAA,CAC/BD,GAAG,sBACG;EACd;EACA,OAAOA,GAAG;AACZ;AAEA,SAASiB,aAAaA,CAACV,IAAI,EAAEE,IAAI,EAAE;EACjC,IAAIT,GAAG,GAAG,EAAE;EACZ,IAAIO,IAAI,CAACY,QAAQ,IAAIV,IAAI,KAAK,MAAM,EAAE;IACpCT,GAAG,qNAGiB;IACpB,IAAIF,eAAe,EAAE;MACnBE,GAAG,uCAAAC,MAAA,CACGD,GAAG,wBACG;IACd;EACF;EACA,OAAOA,GAAG;AACZ;;AAEA;AACA,SAASoB,UAAUA,CAACC,OAAO,EAAErB,GAAG,EAAE;EAChC,IAAIF,eAAe,IAAIuB,OAAO,CAACC,IAAI,KAAK,EAAE,EAAE;IAC1C,0BAAArB,MAAA,CAAyBoB,OAAO,CAACC,IAAI,iBAAArB,MAAA,CACjCD,GAAG;EAET;EACA,OAAOA,GAAG;AACZ;AAEA,IAAMuB,OAAO,GAAG;EACdC,WAAW,WAAXA,WAAWA,CAACH,OAAO,EAAE;IACnB,IAAIL,UAAU,GAAG,EAAE;IACnB,IAAIK,OAAO,CAACL,UAAU,IAAIK,OAAO,CAACL,UAAU,KAAKnB,UAAU,CAACmB,UAAU,EAAE;MACtEA,UAAU,oBAAAf,MAAA,CAAmBoB,OAAO,CAACL,UAAU,SAAK;IACtD;IACA,IAAMS,QAAQ,GAAG,CAACC,eAAO,CAACL,OAAO,CAACM,GAAG,CAAC,IAAIN,OAAO,CAACI,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC5E,IAAMG,MAAM,GAAGC,IAAI,CAACR,OAAO,CAACM,GAAG,CAAC,GAAGE,IAAI,CAACR,OAAO,CAACM,GAAG,CAAC,CAACN,OAAO,CAAC,GAAG,IAAI;IACpE,IAAIrB,GAAG,oBAAAC,MAAA,CAAoBe,UAAU,eAAAf,MAAA,CAAWoB,OAAO,CAACS,KAAK,gBAAA7B,MAAA,CAAWoB,OAAO,CAACU,MAAM,SAAA9B,MAAA,CAAKwB,QAAQ,iBAAAxB,MAAA,CAC7F2B,MAAM,4BACM;IAClB5B,GAAG,GAAGoB,UAAU,CAACC,OAAO,EAAErB,GAAG,CAAC;IAC9B,OAAOA,GAAG;EACZ,CAAC;EACDgC,WAAW,WAAXA,WAAWA,CAACX,OAAO,EAAE;IACnB,IAAMZ,IAAI,GAAGY,OAAO,CAACZ,IAAI,KAAK,SAAS,GAAG,EAAE,aAAAR,MAAA,CAAYoB,OAAO,CAACZ,IAAI,OAAG;IACvE,IAAMwB,OAAO,GAAGZ,OAAO,CAACZ,IAAI,KAAK,SAAS,GAAG,EAAE,gBAAAR,MAAA,CAAeoB,OAAO,CAACY,OAAO,OAAG;IAChF,IAAMC,KAAK,GAAGb,OAAO,CAACZ,IAAI,KAAK,SAAS,GAAG,EAAE,cAAAR,MAAA,CAAaoB,OAAO,CAACa,KAAK,OAAG;IAC1E,IAAMhB,MAAM,GAAGG,OAAO,CAACH,MAAM,eAAAjB,MAAA,CAAcoB,OAAO,CAACH,MAAM,UAAM,EAAE;IACjE,IAAMiB,QAAQ,GAAGd,OAAO,CAACc,QAAQ,CAACC,GAAG,CAAC,UAAAC,EAAE;MAAA,OAAId,OAAO,CAACc,EAAE,CAACC,MAAM,CAAC,CAACD,EAAE,CAAC;IAAA,EAAC;IACnE,IAAIrC,GAAG,cAAAC,MAAA,CAAcQ,IAAI,OAAAR,MAAA,CAAIgC,OAAO,OAAAhC,MAAA,CAAIiC,KAAK,OAAAjC,MAAA,CAAIiB,MAAM,eAAAjB,MAAA,CACnDkC,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC,oBACb;IACVvC,GAAG,GAAGoB,UAAU,CAACC,OAAO,EAAErB,GAAG,CAAC;IAC9B,OAAOA,GAAG;EACZ;AACF,CAAC;AAED,IAAM6B,IAAI,GAAG;EACX,WAAW,EAAE,SAAbW,QAAWA,CAAEH,EAAE,EAAI;IACjB,IAAAI,YAAA,GAEIC,WAAW,CAACL,EAAE,CAAC;MADjBV,GAAG,GAAAc,YAAA,CAAHd,GAAG;MAAEhB,QAAQ,GAAA8B,YAAA,CAAR9B,QAAQ;IAEf,IAAMF,IAAI,GAAG4B,EAAE,CAAC5B,IAAI,aAAAR,MAAA,CAAYoC,EAAE,CAAC5B,IAAI,UAAM,EAAE;IAC/C,IAAMkC,IAAI,GAAGN,EAAE,CAACM,IAAI,aAAA1C,MAAA,CAAYoC,EAAE,CAACM,IAAI,UAAM,EAAE;IAC/C,IAAM5B,IAAI,GAAGsB,EAAE,CAACtB,IAAI,aAAAd,MAAA,CAAYoC,EAAE,CAACtB,IAAI,UAAM,EAAE;IAC/C,IAAIP,KAAK,GAAGoC,kBAAkB,CAACP,EAAE,CAAC;IAElC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAIQ,IAAI,OAAAR,MAAA,CAAI0C,IAAI,OAAA1C,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EAC3E,CAAC;EACD,UAAU,EAAE,SAAZkB,OAAUA,CAAER,EAAE,EAAI;IAChB,IAAAS,aAAA,GAEIJ,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAAmC,aAAA,CAARnC,QAAQ;MAAEoB,MAAM,GAAAe,aAAA,CAANf,MAAM;MAAEgB,SAAS,GAAAD,aAAA,CAATC,SAAS;MAAEC,WAAW,GAAAF,aAAA,CAAXE,WAAW;MAAEC,KAAK,GAAAH,aAAA,CAALG,KAAK;IAEjD,IAAMC,SAAS,GAAGb,EAAE,CAACa,SAAS,mBAAAjD,MAAA,CAAkBoC,EAAE,CAACa,SAAS,UAAM,EAAE;IACpE,IAAMC,aAAa,GAAGd,EAAE,CAAC,iBAAiB,CAAC,GAAG,iBAAiB,GAAG,EAAE;IACpE,IAAMe,QAAQ,GAAGf,EAAE,CAACe,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC9C,IAAMC,UAAU,GAAGhB,EAAE,CAAC,aAAa,CAAC,mBAAApC,MAAA,CAAmBoC,EAAE,CAAC,aAAa,CAAC,SAAM,EAAE;IAChF,IAAMiB,UAAU,GAAGjB,EAAE,CAAC,aAAa,CAAC,mBAAApC,MAAA,CAAmBoC,EAAE,CAAC,aAAa,CAAC,SAAM,EAAE;IAChF,IAAMkB,YAAY,GAAGlB,EAAE,CAAC,eAAe,CAAC,GAAG,eAAe,GAAG,EAAE;IAC/D,IAAM5B,IAAI,GAAG4B,EAAE,CAAC5B,IAAI,aAAAR,MAAA,CAAYoC,EAAE,CAAC5B,IAAI,UAAM,EAAE;IAC/C,IAAM+C,QAAQ,GAAGnB,EAAE,CAACmB,QAAQ,IAAInB,EAAE,CAACmB,QAAQ,CAACC,OAAO,4BAAAxD,MAAA,CACvBoC,EAAE,CAACmB,QAAQ,CAACC,OAAO,iBAAAxD,MAAA,CAAcoC,EAAE,CAACmB,QAAQ,CAACE,OAAO,WAC5E,EAAE;IACN,IAAIlD,KAAK,GAAGmD,iBAAiB,CAACtB,EAAE,CAAC;IAEjC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIQ,IAAI,OAAAR,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIiD,SAAS,OAAAjD,MAAA,CAAIkD,aAAa,OAAAlD,MAAA,CAAImD,QAAQ,OAAAnD,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAIoD,UAAU,OAAApD,MAAA,CAAIqD,UAAU,OAAArD,MAAA,CAAIsD,YAAY,OAAAtD,MAAA,CAAIuD,QAAQ,OAAAvD,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EAC5M,CAAC;EACD,iBAAiB,EAAE,SAAnBiC,aAAiBA,CAAEvB,EAAE,EAAI;IACvB,IAAAwB,aAAA,GAA0CnB,WAAW,CAACL,EAAE,CAAC;MAAjD1B,QAAQ,GAAAkD,aAAA,CAARlD,QAAQ;MAAEoB,MAAM,GAAA8B,aAAA,CAAN9B,MAAM;MAAEiB,WAAW,GAAAa,aAAA,CAAXb,WAAW;IACrC,IAAMc,gBAAgB,GAAGzB,EAAE,CAAC,mBAAmB,CAAC,wBAAApC,MAAA,CAAwBoC,EAAE,CAAC,mBAAmB,CAAC,IAAK,EAAE;IACtG,IAAM0B,GAAG,GAAG1B,EAAE,CAAC0B,GAAG,YAAA9D,MAAA,CAAYoC,EAAE,CAAC0B,GAAG,SAAM,EAAE;IAC5C,IAAMC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,YAAA/D,MAAA,CAAYoC,EAAE,CAAC2B,GAAG,SAAM,EAAE;IAC5C,IAAMC,IAAI,GAAG5B,EAAE,CAAC4B,IAAI,aAAAhE,MAAA,CAAaoC,EAAE,CAAC4B,IAAI,SAAM,EAAE;IAChD,IAAMC,YAAY,GAAG7B,EAAE,CAAC,eAAe,CAAC,GAAG,eAAe,GAAG,EAAE;IAC/D,IAAM8B,SAAS,GAAG9B,EAAE,CAAC8B,SAAS,kBAAAlE,MAAA,CAAkBoC,EAAE,CAAC8B,SAAS,SAAM,EAAE;IAEpE,WAAAlE,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIgE,IAAI,OAAAhE,MAAA,CAAIiE,YAAY,OAAAjE,MAAA,CAAIkE,SAAS,OAAAlE,MAAA,CAAI6D,gBAAgB,OAAA7D,MAAA,CAAI8D,GAAG,OAAA9D,MAAA,CAAI+D,GAAG,OAAA/D,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EAC3I,CAAC;EACD,WAAW,EAAE,SAAbyC,QAAWA,CAAE/B,EAAE,EAAI;IACjB,IAAAgC,aAAA,GAEI3B,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAA0D,aAAA,CAAR1D,QAAQ;MAAEoB,MAAM,GAAAsC,aAAA,CAANtC,MAAM;MAAEgB,SAAS,GAAAsB,aAAA,CAATtB,SAAS;MAAEC,WAAW,GAAAqB,aAAA,CAAXrB,WAAW;MAAEC,KAAK,GAAAoB,aAAA,CAALpB,KAAK;IAEjD,IAAMqB,UAAU,GAAGjC,EAAE,CAACiC,UAAU,GAAG,YAAY,GAAG,EAAE;IACpD,IAAMC,QAAQ,GAAGlC,EAAE,CAACkC,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC9C,IAAI/D,KAAK,GAAGgE,kBAAkB,CAACnC,EAAE,CAAC;IAElC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIsE,QAAQ,OAAAtE,MAAA,CAAIqE,UAAU,OAAArE,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EAC5H,CAAC;EACD,gBAAgB,EAAE,SAAlB8C,YAAgBA,CAAEpC,EAAE,EAAI;IACtB,IAAAqC,aAAA,GAA6BhC,WAAW,CAACL,EAAE,CAAC;MAApC1B,QAAQ,GAAA+D,aAAA,CAAR/D,QAAQ;MAAEoB,MAAM,GAAA2C,aAAA,CAAN3C,MAAM;IACxB,IAAMhB,IAAI,aAAAd,MAAA,CAAYoC,EAAE,CAACtB,IAAI,OAAG;IAChC,IAAIP,KAAK,GAAGmE,sBAAsB,CAACtC,EAAE,CAAC;IAEtC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EACrE,CAAC;EACD,mBAAmB,EAAE,SAArBiD,eAAmBA,CAAEvC,EAAE,EAAI;IACzB,IAAAwC,aAAA,GAA6BnC,WAAW,CAACL,EAAE,CAAC;MAApC1B,QAAQ,GAAAkE,aAAA,CAARlE,QAAQ;MAAEoB,MAAM,GAAA8C,aAAA,CAAN9C,MAAM;IACxB,IAAMhB,IAAI,aAAAd,MAAA,CAAYoC,EAAE,CAACtB,IAAI,OAAG;IAChC,IAAMgD,GAAG,GAAG1B,EAAE,CAAC0B,GAAG,aAAA9D,MAAA,CAAYoC,EAAE,CAAC0B,GAAG,UAAM,EAAE;IAC5C,IAAMC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,aAAA/D,MAAA,CAAYoC,EAAE,CAAC2B,GAAG,UAAM,EAAE;IAC5C,IAAIxD,KAAK,GAAGsE,yBAAyB,CAACzC,EAAE,CAAC;IAEzC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI8D,GAAG,OAAA9D,MAAA,CAAI+D,GAAG,OAAA/D,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EACnF,CAAC;EACD,WAAW,EAAE,SAAboD,QAAWA,CAAE1C,EAAE,EAAI;IACjB,IAAA2C,aAAA,GAA6BtC,WAAW,CAACL,EAAE,CAAC;MAApC1B,QAAQ,GAAAqE,aAAA,CAARrE,QAAQ;MAAEoB,MAAM,GAAAiD,aAAA,CAANjD,MAAM;IACxB,IAAMkD,UAAU,GAAG5C,EAAE,CAAC,aAAa,CAAC,oBAAApC,MAAA,CAAmBoC,EAAE,CAAC,aAAa,CAAC,UAAM,EAAE;IAChF,IAAM6C,YAAY,GAAG7C,EAAE,CAAC,eAAe,CAAC,sBAAApC,MAAA,CAAqBoC,EAAE,CAAC,eAAe,CAAC,UAAM,EAAE;IACxF,IAAM8C,WAAW,GAAG9C,EAAE,CAAC,cAAc,CAAC,qBAAApC,MAAA,CAAoBoC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IACpF,IAAM+C,aAAa,GAAG/C,EAAE,CAAC,gBAAgB,CAAC,uBAAApC,MAAA,CAAsBoC,EAAE,CAAC,gBAAgB,CAAC,UAAM,EAAE;IAC5F,IAAMgD,WAAW,GAAGhD,EAAE,CAAC,cAAc,CAAC,KAAK,IAAI,qBAAApC,MAAA,CAAqBqF,IAAI,CAACC,SAAS,CAAClD,EAAE,CAAC,cAAc,CAAC,CAAC,SAAM,EAAE;IAC9G,IAAMmD,aAAa,GAAGnD,EAAE,CAAC,gBAAgB,CAAC,KAAK,KAAK,uBAAApC,MAAA,CAAuBqF,IAAI,CAACC,SAAS,CAAClD,EAAE,CAAC,gBAAgB,CAAC,CAAC,SAAM,EAAE;IAEvH,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIgF,UAAU,OAAAhF,MAAA,CAAIiF,YAAY,OAAAjF,MAAA,CAAIkF,WAAW,OAAAlF,MAAA,CAAImF,aAAa,OAAAnF,MAAA,CAAIoF,WAAW,OAAApF,MAAA,CAAIuF,aAAa,OAAAvF,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EACnJ,CAAC;EACD,aAAa,EAAE,SAAf8D,UAAaA,CAAEpD,EAAE,EAAI;IACnB,IAAAqD,aAAA,GAEIhD,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAA+E,aAAA,CAAR/E,QAAQ;MAAEoB,MAAM,GAAA2D,aAAA,CAAN3D,MAAM;MAAEgB,SAAS,GAAA2C,aAAA,CAAT3C,SAAS;MAAEC,WAAW,GAAA0C,aAAA,CAAX1C,WAAW;MAAEC,KAAK,GAAAyC,aAAA,CAALzC,KAAK;IAEjD,IAAM0C,OAAO,GAAGtD,EAAE,CAACsD,OAAO,iBAAA1F,MAAA,CAAgBoC,EAAE,CAACN,MAAM,iBAAa,EAAE;IAClE,IAAM6D,KAAK,GAAGvD,EAAE,CAACuD,KAAK,eAAA3F,MAAA,CAAcoC,EAAE,CAACN,MAAM,eAAW,EAAE;IAC1D,IAAM8D,aAAa,GAAGxD,EAAE,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,0BAA0B;IAC7E,IAAMiC,UAAU,GAAGjC,EAAE,CAACiC,UAAU,GAAG,YAAY,GAAG,EAAE;IACpD,IAAMwB,SAAS,GAAGzD,EAAE,CAACyD,SAAS,KAAK,GAAG,GAAG,EAAE,kBAAA7F,MAAA,CAAiBoC,EAAE,CAACyD,SAAS,OAAG;IAE3E,WAAA7F,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI0F,OAAO,OAAA1F,MAAA,CAAI2F,KAAK,OAAA3F,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAI4F,aAAa,OAAA5F,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAI6F,SAAS,OAAA7F,MAAA,CAAIqE,UAAU,OAAArE,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EAC1J,CAAC;EACD,WAAW,EAAE,SAAboE,QAAWA,CAAE1D,EAAE,EAAI;IACjB,IAAA2D,aAAA,GAA6BtD,WAAW,CAACL,EAAE,CAAC;MAApC1B,QAAQ,GAAAqF,aAAA,CAARrF,QAAQ;MAAEoB,MAAM,GAAAiE,aAAA,CAANjE,MAAM;IACxB,IAAMgC,GAAG,GAAG1B,EAAE,CAAC0B,GAAG,YAAA9D,MAAA,CAAYoC,EAAE,CAAC0B,GAAG,SAAM,EAAE;IAC5C,IAAMC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,YAAA/D,MAAA,CAAYoC,EAAE,CAAC2B,GAAG,SAAM,EAAE;IAC5C,IAAMC,IAAI,GAAG5B,EAAE,CAAC4B,IAAI,aAAAhE,MAAA,CAAaoC,EAAE,CAAC4B,IAAI,SAAM,EAAE;IAChD,IAAMgC,KAAK,GAAG5D,EAAE,CAAC4D,KAAK,GAAG,OAAO,GAAG,EAAE;IACrC,IAAMC,SAAS,GAAG7D,EAAE,CAAC,YAAY,CAAC,oBAAApC,MAAA,CAAmBoC,EAAE,CAAC,YAAY,CAAC,UAAM,EAAE;IAE7E,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8D,GAAG,OAAA9D,MAAA,CAAI+D,GAAG,OAAA/D,MAAA,CAAIgE,IAAI,OAAAhE,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIgG,KAAK,OAAAhG,MAAA,CAAIiG,SAAS,OAAAjG,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EACjG,CAAC;EACD,gBAAgB,EAAE,SAAlBwE,YAAgBA,CAAE9D,EAAE,EAAI;IACtB,IAAA+D,aAAA,GAEI1D,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAAyF,aAAA,CAARzF,QAAQ;MAAEoB,MAAM,GAAAqE,aAAA,CAANrE,MAAM;MAAEgB,SAAS,GAAAqD,aAAA,CAATrD,SAAS;MAAEC,WAAW,GAAAoD,aAAA,CAAXpD,WAAW;MAAEC,KAAK,GAAAmD,aAAA,CAALnD,KAAK;IAEjD,IAAMoD,gBAAgB,GAAGhE,EAAE,CAAC,mBAAmB,CAAC,0BAAApC,MAAA,CAAyBoC,EAAE,CAAC,mBAAmB,CAAC,UAAM,EAAE;IACxG,IAAMiE,cAAc,GAAGjE,EAAE,CAAC,iBAAiB,CAAC,wBAAApC,MAAA,CAAuBoC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMkE,cAAc,GAAGlE,EAAE,CAAC,iBAAiB,CAAC,wBAAApC,MAAA,CAAuBoC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMmE,OAAO,GAAGnE,EAAE,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,EAAE;IAChD,IAAMoE,MAAM,GAAGpE,EAAE,CAACoE,MAAM,eAAAxG,MAAA,CAAcoC,EAAE,CAACoE,MAAM,UAAM,EAAE;IACvD,IAAMC,WAAW,GAAGrE,EAAE,CAAC,cAAc,CAAC,qBAAApC,MAAA,CAAoBoC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IACpF,IAAMsE,aAAa,GAAGtE,EAAE,CAAC,gBAAgB,CAAC,uBAAApC,MAAA,CAAuBqF,IAAI,CAACC,SAAS,CAAClD,EAAE,CAAC,gBAAgB,CAAC,CAAC,SAAM,EAAE;IAE7G,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIuG,OAAO,OAAAvG,MAAA,CAAIwG,MAAM,OAAAxG,MAAA,CAAIyG,WAAW,OAAAzG,MAAA,CAAI0G,aAAa,OAAA1G,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIoG,gBAAgB,OAAApG,MAAA,CAAIqG,cAAc,OAAArG,MAAA,CAAIsG,cAAc,OAAAtG,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EACvM,CAAC;EACD,gBAAgB,EAAE,SAAlBiF,YAAgBA,CAAEvE,EAAE,EAAI;IACtB,IAAAwE,aAAA,GAEInE,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAAkG,aAAA,CAARlG,QAAQ;MAAEoB,MAAM,GAAA8E,aAAA,CAAN9E,MAAM;MAAEgB,SAAS,GAAA8D,aAAA,CAAT9D,SAAS;MAAEC,WAAW,GAAA6D,aAAA,CAAX7D,WAAW;MAAEC,KAAK,GAAA4D,aAAA,CAAL5D,KAAK;IAEjD,IAAMoD,gBAAgB,GAAGhE,EAAE,CAAC,mBAAmB,CAAC,0BAAApC,MAAA,CAAyBoC,EAAE,CAAC,mBAAmB,CAAC,UAAM,EAAE;IACxG,IAAMiE,cAAc,GAAGjE,EAAE,CAAC,iBAAiB,CAAC,wBAAApC,MAAA,CAAuBoC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMkE,cAAc,GAAGlE,EAAE,CAAC,iBAAiB,CAAC,wBAAApC,MAAA,CAAuBoC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMoE,MAAM,GAAGpE,EAAE,CAACoE,MAAM,eAAAxG,MAAA,CAAcoC,EAAE,CAACoE,MAAM,UAAM,EAAE;IACvD,IAAMC,WAAW,GAAGrE,EAAE,CAAC,cAAc,CAAC,qBAAApC,MAAA,CAAoBoC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IACpF,IAAM5B,IAAI,GAAG4B,EAAE,CAAC5B,IAAI,KAAK,MAAM,GAAG,EAAE,aAAAR,MAAA,CAAYoC,EAAE,CAAC5B,IAAI,OAAG;IAC1D,IAAM2C,QAAQ,GAAGf,EAAE,CAACe,QAAQ,GAAG,UAAU,GAAG,EAAE;IAE9C,WAAAnD,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAIQ,IAAI,OAAAR,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIwG,MAAM,OAAAxG,MAAA,CAAIyG,WAAW,OAAAzG,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIoG,gBAAgB,OAAApG,MAAA,CAAIqG,cAAc,OAAArG,MAAA,CAAIsG,cAAc,OAAAtG,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAImD,QAAQ,OAAAnD,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EAC/L,CAAC;EACD,SAAS,EAAE,SAAXmF,MAASA,CAAEzE,EAAE,EAAI;IACf,IAAA0E,cAAA,GAA6BrE,WAAW,CAACL,EAAE,CAAC;MAApC1B,QAAQ,GAAAoG,cAAA,CAARpG,QAAQ;MAAEoB,MAAM,GAAAgF,cAAA,CAANhF,MAAM;IACxB,IAAMiC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,YAAA/D,MAAA,CAAYoC,EAAE,CAAC2B,GAAG,SAAM,EAAE;IAC5C,IAAMgD,SAAS,GAAG3E,EAAE,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE;IACtD,IAAM4E,QAAQ,GAAG5E,EAAE,CAAC,WAAW,CAAC,GAAG,WAAW,GAAG,EAAE;IACnD,IAAM6E,SAAS,GAAG7E,EAAE,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE;IAEtD,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI+G,SAAS,OAAA/G,MAAA,CAAIgH,QAAQ,OAAAhH,MAAA,CAAIiH,SAAS,OAAAjH,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EAC3F,CAAC;EACD,iBAAiB,EAAE,SAAnBwF,aAAiBA,CAAE9E,EAAE,EAAI;IACvB,IAAA+E,cAAA,GAA6B1E,WAAW,CAACL,EAAE,CAAC;MAApC1B,QAAQ,GAAAyG,cAAA,CAARzG,QAAQ;MAAEoB,MAAM,GAAAqF,cAAA,CAANrF,MAAM;IACxB,IAAMhB,IAAI,aAAAd,MAAA,CAAYoC,EAAE,CAACtB,IAAI,OAAG;IAChC,IAAMsG,SAAS,GAAGhF,EAAE,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE;IACtD,IAAMiF,WAAW,GAAGjF,EAAE,CAAC,cAAc,CAAC,qBAAApC,MAAA,CAAoBoC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IAEpF,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIoH,SAAS,OAAApH,MAAA,CAAIqH,WAAW,OAAArH,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EACzF,CAAC;EACD,WAAW,EAAE,SAAb4F,QAAWA,CAAElF,EAAE,EAAI;IACjB,IAAM1B,QAAQ,GAAG0B,EAAE,CAAC1B,QAAQ,GAAG,oBAAoB,GAAG,EAAE;IACxD,IAAM6G,MAAM,GAAGnF,EAAE,CAACmF,MAAM,gBAAAvH,MAAA,CAAeoC,EAAE,CAACN,MAAM,gBAAY,EAAE;IAC9D,IAAMwC,QAAQ,GAAGlC,EAAE,CAACkC,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC9C,IAAMkD,QAAQ,GAAGpF,EAAE,CAAC,WAAW,CAAC,KAAK,MAAM,kBAAApC,MAAA,CAAiBoC,EAAE,CAAC,WAAW,CAAC,UAAM,EAAE;IACnF,IAAMqF,MAAM,GAAGrF,EAAE,CAACqF,MAAM,eAAAzH,MAAA,CAAcoC,EAAE,CAACqF,MAAM,UAAM,EAAE;IACvD,IAAMC,IAAI,GAAGtF,EAAE,CAACsF,IAAI,KAAK,MAAM,aAAA1H,MAAA,CAAYoC,EAAE,CAACsF,IAAI,UAAM,EAAE;IAC1D,IAAMC,UAAU,GAAGvF,EAAE,CAAC,aAAa,CAAC,KAAK,KAAK,GAAG,sBAAsB,GAAG,EAAE;IAC5E,IAAMwF,YAAY,uBAAA5H,MAAA,CAAsBoC,EAAE,CAACN,MAAM,mBAAe;IAChE,IAAM+F,QAAQ,mBAAA7H,MAAA,CAAkBoC,EAAE,CAACN,MAAM,eAAW;IACpD,IAAMgG,GAAG,YAAA9H,MAAA,CAAWoC,EAAE,CAACN,MAAM,OAAG;IAChC,IAAIvB,KAAK,GAAGwH,kBAAkB,CAAC3F,EAAE,CAAC;IAElC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8H,GAAG,OAAA9H,MAAA,CAAI6H,QAAQ,OAAA7H,MAAA,CAAIuH,MAAM,OAAAvH,MAAA,CAAI2H,UAAU,OAAA3H,MAAA,CAAIsE,QAAQ,OAAAtE,MAAA,CAAI4H,YAAY,OAAA5H,MAAA,CAAIwH,QAAQ,OAAAxH,MAAA,CAAIyH,MAAM,OAAAzH,MAAA,CAAI0H,IAAI,OAAA1H,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EACxJ;AACF,CAAC;AAED,SAASe,WAAWA,CAACL,EAAE,EAAE;EACvB,OAAO;IACLN,MAAM,eAAA9B,MAAA,CAAcJ,UAAU,CAACgB,SAAS,OAAAZ,MAAA,CAAIoC,EAAE,CAACN,MAAM,OAAG;IACxDgB,SAAS,EAAEV,EAAE,CAACU,SAAS,GAAG,WAAW,GAAG,EAAE;IAC1CC,WAAW,EAAEX,EAAE,CAACW,WAAW,oBAAA/C,MAAA,CAAmBoC,EAAE,CAACW,WAAW,UAAM,EAAE;IACpEC,KAAK,EAAEZ,EAAE,CAAC4F,KAAK,IAAI5F,EAAE,CAAC4F,KAAK,CAAChF,KAAK,GAAG,4BAA4B,GAAG,EAAE;IACrEtC,QAAQ,EAAE0B,EAAE,CAAC1B,QAAQ,GAAG,oBAAoB,GAAG;EACjD,CAAC;AACH;;AAEA;AACA,SAASiC,kBAAkBA,CAACrC,IAAI,EAAE;EAChC,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAAC2H,OAAO,EAAE;IAChB/F,QAAQ,CAACgG,IAAI,CAAC5H,IAAI,CAAC2H,OAAO,CAAC;EAC7B;EACA,OAAO/F,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;;AAEA;AACA,SAASoB,iBAAiBA,CAACpD,IAAI,EAAE;EAC/B,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAAC6H,OAAO,EAAE;IAChBjG,QAAQ,CAACgG,IAAI,+BAAAlI,MAAA,CAA6BM,IAAI,CAAC6H,OAAO,gBAAa,CAAC;EACtE;EACA,IAAI7H,IAAI,CAAC8H,MAAM,EAAE;IACflG,QAAQ,CAACgG,IAAI,8BAAAlI,MAAA,CAA4BM,IAAI,CAAC8H,MAAM,gBAAa,CAAC;EACpE;EACA,OAAOlG,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASiC,kBAAkBA,CAACjE,IAAI,EAAE;EAChC,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAACoF,OAAO,IAAIpF,IAAI,CAACoF,OAAO,CAAC2C,MAAM,EAAE;IACvCnG,QAAQ,CAACgG,IAAI,wCAAAlI,MAAA,CAAuCM,IAAI,CAACwB,MAAM,kHAAsG,CAAC;EACxK;EACA,OAAOI,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASoC,sBAAsBA,CAACpE,IAAI,EAAE;EACpC,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAACoF,OAAO,IAAIpF,IAAI,CAACoF,OAAO,CAAC2C,MAAM,EAAE;IACvC,IAAM3G,GAAG,GAAGpB,IAAI,CAACgI,UAAU,KAAK,QAAQ,GAAG,iBAAiB,GAAG,UAAU;IACzE,IAAMC,MAAM,GAAGjI,IAAI,CAACiI,MAAM,GAAG,QAAQ,GAAG,EAAE;IAC1CrG,QAAQ,CAACgG,IAAI,KAAAlI,MAAA,CAAK0B,GAAG,gCAAA1B,MAAA,CAA4BM,IAAI,CAACwB,MAAM,iFAAA9B,MAAA,CAAuEuI,MAAM,uBAAAvI,MAAA,CAAoB0B,GAAG,MAAG,CAAC;EACtK;EACA,OAAOQ,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASuC,yBAAyBA,CAACvE,IAAI,EAAE;EACvC,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAACoF,OAAO,IAAIpF,IAAI,CAACoF,OAAO,CAAC2C,MAAM,EAAE;IACvC,IAAM3G,GAAG,GAAGpB,IAAI,CAACgI,UAAU,KAAK,QAAQ,GAAG,oBAAoB,GAAG,aAAa;IAC/E,IAAMC,MAAM,GAAGjI,IAAI,CAACiI,MAAM,GAAG,QAAQ,GAAG,EAAE;IAC1CrG,QAAQ,CAACgG,IAAI,KAAAlI,MAAA,CAAK0B,GAAG,gCAAA1B,MAAA,CAA4BM,IAAI,CAACwB,MAAM,iFAAA9B,MAAA,CAAuEuI,MAAM,uBAAAvI,MAAA,CAAoB0B,GAAG,MAAG,CAAC;EACtK;EACA,OAAOQ,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASyF,kBAAkBA,CAACzH,IAAI,EAAE;EAChC,IAAMkI,IAAI,GAAG,EAAE;EACf,IAAIlI,IAAI,CAAC,WAAW,CAAC,KAAK,cAAc,EAAEkI,IAAI,CAACN,IAAI,CAAC,8BAA8B,CAAC,MAC9EM,IAAI,CAACN,IAAI,uEAAAlI,MAAA,CAAiEM,IAAI,CAACmI,UAAU,iBAAc,CAAC;EAC7G,IAAInI,IAAI,CAACoI,OAAO,EAAEF,IAAI,CAACN,IAAI,0FAAAlI,MAAA,CAAmDM,IAAI,CAACqI,QAAQ,EAAA3I,MAAA,CAAGM,IAAI,CAACsI,QAAQ,aAAA5I,MAAA,CAAKM,IAAI,CAACmH,MAAM,uBAAU,CAAC;EACtI,OAAOe,IAAI,CAAClG,IAAI,CAAC,IAAI,CAAC;AACxB;AAEO,SAASuG,UAAUA,CAACvI,IAAI,EAAEE,IAAI,EAAE;EACrC,IAAMsI,QAAQ,GAAG,EAAE;EACnBlJ,UAAU,GAAGU,IAAI;EACjBT,eAAe,GAAGS,IAAI,CAACyI,MAAM,CAACC,IAAI,CAAC,UAAAC,IAAI;IAAA,OAAIA,IAAI,CAAC5H,IAAI,KAAK,EAAE;EAAA,EAAC;EAC5Df,IAAI,CAACyI,MAAM,CAACG,OAAO,CAAC,UAAA9G,EAAE,EAAI;IACxB0G,QAAQ,CAACZ,IAAI,CAAC5G,OAAO,CAACc,EAAE,CAACC,MAAM,CAAC,CAACD,EAAE,CAAC,CAAC;EACvC,CAAC,CAAC;EACF,IAAM+G,OAAO,GAAGL,QAAQ,CAACxG,IAAI,CAAC,IAAI,CAAC;EAEnC,IAAI8G,IAAI,GAAG/I,iBAAiB,CAACC,IAAI,EAAE6I,OAAO,EAAE3I,IAAI,CAAC;EACjD,IAAIA,IAAI,KAAK,QAAQ,EAAE;IACrB4I,IAAI,GAAGtJ,aAAa,CAACsJ,IAAI,CAAC;EAC5B;EACAxJ,UAAU,GAAG,IAAI;EACjB,OAAOwJ,IAAI;AACb", "ignoreList": []}]}