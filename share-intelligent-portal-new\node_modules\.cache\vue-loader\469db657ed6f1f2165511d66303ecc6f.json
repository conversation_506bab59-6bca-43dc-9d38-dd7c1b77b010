{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\joinNow.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\joinNow.vue", "mtime": 1750311962967}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBlbnRlcmluZ0ZhY3RvcnlBZGQgfSBmcm9tICJAL2FwaS9tYW51ZmFjdHVyaW5nU2hhcmluZyI7DQppbXBvcnQgeyBhZGQgfSBmcm9tICJyYW1kYSI7DQppbXBvcnQgeyBzZWFyY2hDb21wYW55LCBnZXRDb21wYW55Q29kZUJ5TmFtZSB9IGZyb20gIkAvYXBpL3N5c3RlbS9jb21wYW55IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBmb3JtOiB7DQogICAgICAgIGNvbXBhbnlOYW1lOiAiIiwgLy8g5LyB5Lia5ZCN56ewDQogICAgICAgIHNvY2lhbENyZWRpdENvZGU6ICIiLCAvLyDnpL7kvJrkv6HnlKjku6PnoIENCiAgICAgICAgcmVnaXN0ZXJlZENhcGl0YWw6ICIiLCAvLyDms6jlhozotYTmnKwNCiAgICAgICAgY29udGFjdFBob25lOiAiIiwgLy8g6IGU57O755S16K+dDQogICAgICAgIGluZHVzdHJ5OiAiIiwgLy8g6KGM5LiaDQogICAgICAgIGNvbXBhbnlBZGRyZXNzOiAiIiwgLy8g5Zyw5Z2ADQogICAgICAgIGJ1c2luZXNzU2NvcGU6ICIiLCAvLyDnu4/okKXojIPlm7QNCiAgICAgICAgcGVyc29ubmVsTGlzdDogW10sIC8vIOS6uuWRmOiDveWKmw0KICAgICAgICBxdWFsaWZpY2F0aW9uTGlzdDogW10sIC8vIOi1hOi0qOivgeS7tg0KICAgICAgICBlcXVpcG1lbnRMaXN0OiBbXSwgLy8g6K6+5aSH5L+h5oGv5YiX6KGoDQogICAgICAgIHNldHRsZWRTdGF0dXM6ICIwIiwgLy8g6buY6K6k5Lyg5b6F5a6h5qC4DQogICAgICB9LA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgY29tcGFueU5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5LyB5Lia5ZCN56ewIiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICB9LA0KICAgIH07DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBvblN1Ym1pdCgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgZW50ZXJpbmdGYWN0b3J5QWRkKHRoaXMuZm9ybSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIG9uQ2FuY2VsKCkgeyB9LA0KICAgIGFkZFBlcnNvbm5lbExpc3QoKSB7DQogICAgICB0aGlzLmZvcm0ucGVyc29ubmVsTGlzdC5wdXNoKHsNCiAgICAgICAgdGVjaG5pY2lhbk5hbWU6ICIiLA0KICAgICAgICB0ZWNobmljYWxUeXBlOiAiIiwNCiAgICAgIH0pOw0KICAgIH0sDQogICAgYWRkUXVhbGlmaWNhdGlvbkxpc3QoKSB7DQogICAgICB0aGlzLmZvcm0ucXVhbGlmaWNhdGlvbkxpc3QucHVzaCh7DQogICAgICAgIHF1YWxpZmljYXRpb25OYW1lOiAiIiwNCiAgICAgICAgYXR0YWNobWVudDogIiIsDQogICAgICB9KTsNCiAgICB9LA0KICAgIGFkZEVxdWlwbWVudExpc3QoKSB7DQogICAgICB0aGlzLmZvcm0uZXF1aXBtZW50TGlzdC5wdXNoKHsNCiAgICAgICAgZXF1aXBtZW50TmFtZTogIiIsDQogICAgICAgIHNwZWNpZmljYXRpb246ICIiLA0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDkvIHkuJrlkI3np7ANCiAgICBxdWVyeVNlYXJjaFRpYW5ZYW5DaGEocXVlcnlTdHJpbmcsIGNiKSB7DQogICAgICBpZiAocXVlcnlTdHJpbmcpIHsNCiAgICAgICAgc2VhcmNoQ29tcGFueSh7IGtleXdvcmRzOiBxdWVyeVN0cmluZyB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgbGV0IGRhdGEgPSByZXMucm93czsNCiAgICAgICAgICBsZXQgTGlzdCA9IFtdOw0KICAgICAgICAgIGRhdGEuZm9yRWFjaChmdW5jdGlvbiAodmFsLCBpbmRleCkgew0KICAgICAgICAgICAgTGlzdC5wdXNoKHsNCiAgICAgICAgICAgICAgaWQ6IGluZGV4LA0KICAgICAgICAgICAgICB2YWx1ZTogdmFsDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0pDQogICAgICAgICAgaWYgKGRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgY2IoTGlzdCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGNiKFt7DQogICAgICAgICAgICAgIGlkOiAnJywNCiAgICAgICAgICAgICAgdmFsdWU6ICfmmoLml6DmlbDmja4nDQogICAgICAgICAgICB9XSk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOS8geS4muWQjeensOmAieaLqQ0KICAgIHNlbGVjdEF1dG9EYXRhVGlhbllhbkNoYShyb3cpIHsNCiAgICAgIGdldENvbXBhbnlDb2RlQnlOYW1lKHsga2V5d29yZHM6IHJvdy52YWx1ZSB9KS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICBsZXQgZGF0YSA9IHJlcy5kYXRhOw0KICAgICAgICAgIHRoaXMuJHNldCh0aGlzLmZvcm0sICdzb2NpYWxDcmVkaXRDb2RlJywgZGF0YS50YXhObykNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["joinNow.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiKA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "joinNow.vue", "sourceRoot": "src/views/manufacturingSharing/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      制造共享\r\n      <div class=\"imgContent\">\r\n        <div class=\"imgStyle\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../assets/order/orderStep.png\" alt=\"\" />\r\n          <!-- <div class=\"joinNow\" @click=\"joinNow\">立即入驻</div> -->\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container content_card\">\r\n      <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n        <div class=\"title\">基本信息</div>\r\n        <div class=\"titleLine\"></div>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"企业名称\" prop=\"companyName\">\r\n              <el-autocomplete v-model=\"form.companyName\" placeholder=\"请输入您公司的完整名称\" style=\"width: 100%;\"\r\n                :fetch-suggestions=\"querySearchTianYanCha\" @select=\"selectAutoDataTianYanCha\"></el-autocomplete>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"社会信用代码\">\r\n              <el-input v-model=\"form.socialCreditCode\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"注册资本\">\r\n              <el-input v-model=\"form.registeredCapital\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系电话\">\r\n              <el-input v-model=\"form.contactPhone\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"行业\">\r\n              <el-input v-model=\"form.industry\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"地址\">\r\n              <el-input v-model=\"form.companyAddress\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item prop=\"technologyType\">\r\n          <div slot=\"label\">经营范围</div>\r\n          <el-input v-model=\"form.businessScope\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\" show-word-limit\r\n            placeholder=\"请输入\" />\r\n        </el-form-item>\r\n        <div class=\"title\">企业能力</div>\r\n        <div class=\"titleLine\"></div>\r\n        <!-- <el-form-item label=\"\">\r\n          <div slot=\"label\">\r\n            <div style=\"display: flex; width: 1080px\">\r\n              <div>业绩情况</div>\r\n              <div class=\"addStyle\">新增行</div>\r\n            </div>\r\n          </div>\r\n          <el-table :data=\"jobList\">\r\n            <el-table-column label=\"项目名称\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.jobId\"></el-input>\r\n              </template>\r\n</el-table-column>\r\n<el-table-column label=\"联系人\" align=\"center\">\r\n  <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.jobId\"></el-input>\r\n              </template>\r\n</el-table-column>\r\n<el-table-column label=\"联系电话\" align=\"center\">\r\n  <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.jobId\"></el-input>\r\n              </template>\r\n</el-table-column>\r\n<el-table-column label=\"附件\" align=\"center\">\r\n  <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.jobId\"></el-input>\r\n              </template>\r\n</el-table-column>\r\n</el-table>\r\n</el-form-item> -->\r\n        <el-form-item label=\"\">\r\n          <div slot=\"label\">\r\n            <div style=\"display: flex; width: 1080px\">\r\n              <div>人员能力</div>\r\n              <div class=\"addStyle\" @click=\"addPersonnelList\">新增行</div>\r\n            </div>\r\n          </div>\r\n          <el-table :data=\"form.personnelList\">\r\n            <el-table-column label=\"技术人员姓名\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.technicianName\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"专业技术工种\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.technicalType\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-form-item>\r\n        <el-form-item label=\"技术实力\">\r\n          <el-input v-model=\"form.technicalCapability\" placeholder=\"请输入\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"\">\r\n          <div slot=\"label\">\r\n            <div style=\"display: flex; width: 1080px\">\r\n              <div>\r\n                资质证件\r\n                <span style=\"color: #999999; font-size: 14px; margin-left: 11px\">（专利、商标、资质、证书等）</span>\r\n              </div>\r\n              <div class=\"addStyle\" @click=\"addQualificationList\">新增行</div>\r\n            </div>\r\n          </div>\r\n          <el-table :data=\"form.qualificationList\">\r\n            <el-table-column label=\"资质名称\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.qualificationName\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"附件\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <ImageUpload :limit=\"1\" v-model=\"scope.row.attachment\" />\r\n                <!-- <el-input v-model=\"scope.row.jobId\"></el-input> -->\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-form-item>\r\n        <el-form-item label=\"\">\r\n          <div slot=\"label\">\r\n            <div style=\"display: flex; width: 1080px\">\r\n              <div>设备信息</div>\r\n              <div class=\"addStyle\" @click=\"addEquipmentList\">新增行</div>\r\n            </div>\r\n          </div>\r\n          <el-table :data=\"form.equipmentList\">\r\n            <el-table-column label=\"生产设备\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.equipmentName\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"规格型号\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.specification\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-form-item>\r\n        <el-form-item class=\"footer-submit\">\r\n          <el-button type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n          <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { enteringFactoryAdd } from \"@/api/manufacturingSharing\";\r\nimport { add } from \"ramda\";\r\nimport { searchCompany, getCompanyCodeByName } from \"@/api/system/company\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        companyName: \"\", // 企业名称\r\n        socialCreditCode: \"\", // 社会信用代码\r\n        registeredCapital: \"\", // 注册资本\r\n        contactPhone: \"\", // 联系电话\r\n        industry: \"\", // 行业\r\n        companyAddress: \"\", // 地址\r\n        businessScope: \"\", // 经营范围\r\n        personnelList: [], // 人员能力\r\n        qualificationList: [], // 资质证件\r\n        equipmentList: [], // 设备信息列表\r\n        settledStatus: \"0\", // 默认传待审核\r\n      },\r\n      rules: {\r\n        companyName: [\r\n          { required: true, message: \"请输入企业名称\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          enteringFactoryAdd(this.form).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功\");\r\n              this.$router.go(-1);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    onCancel() { },\r\n    addPersonnelList() {\r\n      this.form.personnelList.push({\r\n        technicianName: \"\",\r\n        technicalType: \"\",\r\n      });\r\n    },\r\n    addQualificationList() {\r\n      this.form.qualificationList.push({\r\n        qualificationName: \"\",\r\n        attachment: \"\",\r\n      });\r\n    },\r\n    addEquipmentList() {\r\n      this.form.equipmentList.push({\r\n        equipmentName: \"\",\r\n        specification: \"\",\r\n      });\r\n    },\r\n    // 企业名称\r\n    querySearchTianYanCha(queryString, cb) {\r\n      if (queryString) {\r\n        searchCompany({ keywords: queryString }).then(res => {\r\n          let data = res.rows;\r\n          let List = [];\r\n          data.forEach(function (val, index) {\r\n            List.push({\r\n              id: index,\r\n              value: val\r\n            })\r\n          })\r\n          if (data.length > 0) {\r\n            cb(List);\r\n          } else {\r\n            cb([{\r\n              id: '',\r\n              value: '暂无数据'\r\n            }]);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 企业名称选择\r\n    selectAutoDataTianYanCha(row) {\r\n      getCompanyCodeByName({ keywords: row.value }).then(res => {\r\n        if (res.code == 200) {\r\n          let data = res.data;\r\n          this.$set(this.form, 'socialCreditCode', data.taxNo)\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n  background-color: #f2f2f2;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 28px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n\r\n  .imgContent {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-top: 10px;\r\n\r\n    .imgStyle {\r\n      width: 1256px;\r\n      height: 206px;\r\n      position: relative;\r\n      // .joinNow {\r\n      //   position: absolute;\r\n      //   right: 90px;\r\n      //   top: 75px;\r\n      //   width: 110px;\r\n      //   height: 50px;\r\n      //   background: #f79a47;\r\n      //   border-radius: 2px;\r\n      //   font-family: Source Han Sans CN;\r\n      //   font-weight: 400;\r\n      //   font-size: 18px;\r\n      //   color: #ffffff;\r\n      //   line-height: 50px;\r\n      //   text-align: center;\r\n      //   cursor: pointer;\r\n      // }\r\n    }\r\n  }\r\n}\r\n\r\n.content_card {\r\n  // height: 1530px;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: 30px;\r\n  padding: 59px 60px 57px 60px;\r\n}\r\n\r\n.title {\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 18px;\r\n  color: #21c9b8;\r\n}\r\n\r\n.titleLine {\r\n  width: 100%;\r\n  height: 1px;\r\n  background: #21c9b8;\r\n  margin: 20px 0 30px 0;\r\n}\r\n\r\n.addStyle {\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  color: #21c9b8;\r\n  margin-left: auto;\r\n  cursor: pointer;\r\n}\r\n\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"]}]}