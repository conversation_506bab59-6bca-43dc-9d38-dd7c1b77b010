<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="100px"
    >
      <el-form-item label="设备名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备分类" prop="category">
        <el-input
          v-model="queryParams.category"
          placeholder="请输入设备分类"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属单位/位置" prop="location">
        <el-input
          v-model="queryParams.location"
          placeholder="请输入所属单位/位置"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="租用模式" prop="rentMode">
        <el-input
          v-model="queryParams.rentMode"
          placeholder="请输入租用模式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="租用价格" prop="rentPrice">
        <el-input
          v-model="queryParams.rentPrice"
          placeholder="请输入租用价格"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:deviceInfo:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:deviceInfo:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:deviceInfo:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:deviceInfo:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="deviceInfoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备ID" align="center" prop="id" />
      <el-table-column label="设备名称" align="center" prop="name" />
      <el-table-column label="设备分类" align="center" prop="category">
        <template slot-scope="scope">
          {{
            deviceMenuList.filter(
              (item) => item.dictValue == scope.row.category
            )[0].dictLabel
          }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="设备规格" align="center" prop="specifications" /> -->
      <el-table-column label="所属单位/位置" align="center" prop="location" />
      <!-- <el-table-column label="设备用途描述" align="center" prop="description" /> -->
      <!-- <el-table-column label="设备图片" align="center" prop="images" />
      <el-table-column label="技术参数" align="center" prop="technicalParams" />
      <el-table-column label="租用模式" align="center" prop="rentMode" />
      <el-table-column label="租用价格" align="center" prop="rentPrice" /> -->
      <el-table-column label="发布时间" align="center" prop="createTime" />
      <el-table-column label="审核状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.checkStatus == 1 ? "已审核" : "待审核" }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:deviceInfo:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:deviceInfo:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改设备信息对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1200px"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备分类" prop="category">
          <el-select
            v-model="form.category"
            placeholder="请选择设备分类"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in deviceMenuList"
              :key="dict.dictLabel"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设备规格" prop="specifications">
          <el-input
            v-model="form.specifications"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="所属单位/位置" prop="location">
          <el-input v-model="form.location" placeholder="请输入所属单位/位置" />
        </el-form-item>
        <el-form-item label="设备用途描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="设备图片" prop="images">
          <ImageUpload v-model="form.images" :limit="1" :multiple="false" />
        </el-form-item>
        <el-form-item label="租用模式" prop="rentMode">
          <el-input v-model="form.rentMode" placeholder="请输入租用模式" />
        </el-form-item>
        <el-form-item label="租用价格" prop="rentPrice">
          <el-input v-model="form.rentPrice" placeholder="请输入租用价格" />
        </el-form-item>
        <el-form-item label="压力(MPa)" prop="pressure">
          <el-input v-model="form.pressure" placeholder="请输入压力(MPa)" />
        </el-form-item>
        <el-form-item label="温度" prop="temperature">
          <el-input v-model="form.temperature" placeholder="请输入温度" />
        </el-form-item>
        <el-form-item label="尺寸" prop="dimension">
          <el-input v-model="form.dimension" placeholder="请输入尺寸" />
        </el-form-item>
        <el-form-item label="规格型号" prop="modelNumber">
          <el-input v-model="form.modelNumber" placeholder="请输入规格型号" />
        </el-form-item>
        <el-form-item label="审核状态" prop="settledStatus">
          <el-select
            v-model="form.checkStatus"
            placeholder="请选择"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in auditStatusList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listDeviceInfo,
  getDeviceInfo,
  delDeviceInfo,
  addDeviceInfo,
  updateDeviceInfo,
} from "@/api/system/deviceInfo";
import { listData } from "@/api/system/dict/data";
import { comUpload } from "@/api/portalconsole/uploadApi";

export default {
  name: "DeviceInfo",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备信息表格数据
      deviceInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        category: null,
        specifications: null,
        location: null,
        description: null,
        images: null,
        technicalParams: null,
        rentMode: null,
        rentPrice: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "设备名称不能为空", trigger: "blur" },
        ],
        category: [
          { required: true, message: "设备分类不能为空", trigger: "change" },
        ],
        location: [
          { required: true, message: "所属单位/位置不能为空", trigger: "blur" },
        ],
      },
      deviceMenuList: [],
      auditStatusList: [
        {
          dictValue: 0,
          dictLabel: "待审核",
        },
        {
          dictValue: 1,
          dictLabel: "已审核",
        },
      ],
    };
  },
  created() {
    this.getDictList();
    this.getList();
  },
  methods: {
    getDictList() {
      let params = { dictType: "device_share_type" };
      listData(params).then((response) => {
        this.deviceMenuList = response.rows;
        console.log(this.deviceMenuList, "------------------");
      });
    },
    /** 查询设备信息列表 */
    getList() {
      this.loading = true;
      listDeviceInfo(this.queryParams).then((response) => {
        this.deviceInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        category: null,
        specifications: null,
        location: null,
        description: null,
        images: null,
        technicalParams: null,
        rentMode: null,
        rentPrice: null,
        createTime: null,
        updateTime: null,
        checkStatus: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getDeviceInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改设备信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateDeviceInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDeviceInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除设备信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delDeviceInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/deviceInfo/export",
        {
          ...this.queryParams,
        },
        `deviceInfo_${new Date().getTime()}.xlsx`
      );
    },
    // 图片上传
    uploadFun(params) {
      const file = params.file;
      let form = new FormData();
      form.append("file", file); // 文件对象
      comUpload(form).then((res) => {
        let data = res.data;
        this.form.images = data.fileFullPath;
        // this.$set(this.form, "images", data.fileFullPath); // 图片全路径
      });
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg" || "image/png" || "image/jpg";
      const isLt2M = file.size / 1024 / 1024 < 50;
      if (!isJPG) {
        this.$message.error("上传图片只能是 jpg、jpeg、png 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 50MB!");
      }
      return isJPG && isLt2M;
    },
  },
};
</script>
