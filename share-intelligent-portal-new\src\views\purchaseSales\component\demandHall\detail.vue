<template>
  <div class="demand-hall-detail-container">
    <!-- banner图 -->
    <div class="demand-hall-detail-banner">
      <img
        src="../../../../assets/demandHall/demandHallDetailBanner.png"
        alt=""
      />
    </div>
    <div class="demand-hall-detail-title-box">
      <div class="demand-hall-detail-divider"></div>
      <div class="demand-hall-detail-title">需求详情</div>
      <div class="demand-hall-detail-divider"></div>
    </div>
    <div v-loading="loading" class="demand-hall-detail-content">
      <div class="demand-hall-detail-box">
        <div class="demand-hall-detail-box-title">
          {{ data.demandTitle }}
        </div>
        <div class="demand-hall-detail-headline">
          <div class="headline-content">
            <div class="headline-content-item">
              <div class="item-title">应用领域：</div>
              <div class="item-content">
                {{ data.applicationArea }}
              </div>
            </div>
            <div class="headline-content-item">
              <div class="item-title">需求方：</div>
              <div class="item-content">
                {{ data.companyName }}
              </div>
            </div>
            <div class="headline-content-item">
              <div class="item-title">联系人：</div>
              <div class="item-content">
                {{ data.contactsName }}
              </div>
            </div>
            <div class="headline-content-item">
              <div class="item-title">联系方式：</div>
              <div class="item-content">
                {{ data.contactsMobile }}
              </div>
            </div>
            <div class="headline-content-item">
              <div class="item-title">发布时间：</div>
              <div class="item-content">
                {{ data.createTimeString }}
              </div>
            </div>
            <div class="headline-content-btn">
              <el-button
                v-if="showBtn"
                class="headline-btn-style intention-btn"
                @click="goIntention"
                >我有意向
              </el-button>
              <el-button
                class="headline-btn-style communication-btn"
                @click="goChat"
                icon="el-icon-chat-dot-round"
                >在线沟通</el-button
              >
            </div>
          </div>
          <div class="headline-img">
            <img
              v-if="data.scenePicture && data.scenePicture.length > 0"
              :src="data.scenePicture[0].url"
              alt=""
            />
            <img
              v-else
              src="../../../../assets/purchaseSales/demandDefault.png"
              alt=""
            />
          </div>
        </div>
        <div class="demand-hall-detail-description">
          <div class="description-title-box">
            <div class="description-divider"></div>
            <div class="description-title">需求描述</div>
          </div>
          <div class="description-content">
            <div v-html="data.summary" class="description-text ql-editor"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDemandDetail, getCheckSubmit } from "@/api/purchaseSales";
import { getInfo } from "@/api/login";
import { getCompanyInfoByLoginInfo } from "@/api/apathy";
import { mapGetters } from "vuex";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      loading: false,
      data: {},
      showBtn: true,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.loading = true;
      getDemandDetail(this.$route.query.id)
        .then((res) => {
          console.log(res);
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
          console.log(res);
          this.loading = false;
          this.data = res.data || {};
          this.data.scenePicture = this.data.scenePicture
            ? JSON.parse(this.data.scenePicture)
            : [];
          if (this.data.companyName && this.data.displayRestrictions === 2) {
            if (this.data.companyName.length > 2) {
              this.data.companyName =
                this.data.companyName.substring(0, 2) +
                "****" +
                this.data.companyName.slice(6);
            }
          }
          if (!this.token) {
            this.showBtn = true;
          } else {
            this.getInfo();
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 判断此资源是不是自己发布的
    getInfo() {
      getInfo().then((res) => {
        if (this.data.createById === res.user.userId) {
          this.showBtn = false;
        } else {
          this.showBtn = true;
        }
      });
    },
    goChat() {
      // 判断是否登录
      if (!this.token) {
        this.$confirm("请先登录", "提示", {
          confirmButtonText: "去登录",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/login";
          });
        });
        return;
      }
      let routeData = this.$router.resolve({
        path: "/user/im",
        query: {
          userId: this.data.createImById,
        },
      });
      window.open(routeData.href, "_blank");
    },
    // 跳到我有意向页面
    goIntention() {
      // 判断是否登录
      if (!this.token) {
        this.$confirm("请先登录", "提示", {
          confirmButtonText: "去登录",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.$store.dispatch("LogOut").then(() => {
            location.href = "/login";
          });
        });
        return;
      }
      this.loading = true;
      // 是否加入企业
      getCompanyInfoByLoginInfo()
        .then((res) => {
          if (res.data) {
            // 是否对此资源提交过意向
            getCheckSubmit({
              id: this.$route.query.id,
              resourceType: "resource_demand",
            })
              .then((res) => {
                this.loading = false;
                // true 提交过  false未提交过
                if (res.data) {
                  this.$message({
                    type: "warning",
                    message: "已经提交过了哦！",
                  });
                } else {
                  let routeData = this.$router.resolve({
                    path: "/addIntention",
                    query: {
                      id: this.$route.query.id,
                      type: "resource_demand",
                      title: this.data.demandTitle,
                    },
                  });
                  window.open(routeData.href, "_blank");
                }
              })
              .catch(() => {
                this.loading = false;
              });
          } else {
            this.loading = false;
            this.$message({
              type: "warning",
              message: "必须加入企业才可提交我有意向",
            });
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
  computed: {
    ...mapGetters(["token"]),
  },
};
</script>

<style lang="scss" scoped>
.demand-hall-detail-container {
  width: 100%;
  background: #f4f5f9;

  .demand-hall-detail-banner {
    width: 100%;
    height: 25.93vh;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .demand-hall-detail-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;

    .demand-hall-detail-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }

    .demand-hall-detail-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }

  .demand-hall-detail-content {
    background: #f4f5f9;
    padding-bottom: 70px;

    .demand-hall-detail-box {
      width: 1200px;
      background: #fff;
      margin: 0 auto;
      padding: 60px 60px 192px;

      .demand-hall-detail-box-title {
        width: 100%;
        font-size: 32px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333;
        line-height: 32px;
        word-wrap: break-word;
      }

      .demand-hall-detail-headline {
        display: flex;
        justify-content: space-between;
        margin-top: 40px;
        padding-bottom: 40px;
        border-bottom: 1px solid #e8e8e8;

        .headline-content {
          flex: 1;

          .headline-content-item {
            display: flex;
            font-family: PingFangSC-Regular, PingFang SC;
            line-height: 32px;

            .item-title {
              width: 80px;
              color: #666;
            }

            .item-content {
              flex: 1;
              max-width: 590px;
              color: #333;
              word-wrap: break-word;
            }
          }

          .headline-content-btn {
            padding-top: 48px;

            .headline-btn-style {
              width: 100px;
              height: 32px;
              border-radius: 4px;
              font-family: PingFangSC-Regular, PingFang SC;
              padding: 8px 11px;
            }

            .intention-btn {
              background: #21c9b8;
              color: #fff;
            }

            .communication-btn {
              border: 1px solid #21c9b8;
              color: #21c9b8;
            }
          }
        }

        .headline-img {
          width: 400px;
          height: 240px;
          margin-left: 20px;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }

    .demand-hall-detail-description {
      padding-top: 39px;

      .description-title-box {
        display: flex;
        align-items: center;
        padding-bottom: 40px;

        .description-divider {
          width: 4px;
          height: 20px;
          background: #21c9b8;
        }

        .description-title {
          font-size: 24px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333;
          line-height: 24px;
          padding-left: 8px;
        }
      }

      .description-content {
        width: 1072px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        line-height: 28px;
        word-wrap: break-word;
      }
    }
  }
}
</style>
<style lang="scss">
.demand-hall-detail-container {
  .description-content {
    .description-text {
      word-break: break-all;
      font-size: 16px;
      line-height: 28px;
      color: #333;
      font-family: PingFangSC-Regular, PingFang SC;

      img {
        max-width: 100%;
      }
    }
  }
}
</style>
