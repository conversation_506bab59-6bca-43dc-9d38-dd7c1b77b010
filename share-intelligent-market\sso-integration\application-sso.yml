# SSO单点登录配置
sso:
  # SSO服务器配置
  server:
    # SSO服务器地址
    url: http://localhost:9300
    # 登录接口
    login-url: /sso/login
    # Token换取接口
    token-url: /sso/token
    # Token验证接口
    validate-url: /sso/validate
    # 用户信息接口
    userinfo-url: /sso/userinfo
    # 登出接口
    logout-url: /sso/logout
    # 状态检查接口
    status-url: /sso/status

  # 客户端配置
  client:
    # 客户端ID
    id: market
    # 客户端密钥
    secret: "market_2024#RuoYi@Share$Key!9999"
    # 回调地址
    callback-url: http://localhost:8081/sso/callback
    # 系统名称
    name: 智能市场系统

  # Token配置
  token:
    # Token过期时间（秒）
    expire-time: 28800
    # Token刷新时间（秒）
    refresh-time: 1800

  # 缓存配置
  cache:
    # 缓存前缀
    prefix: sso:market:
    # 缓存过期时间（秒）
    expire-time: 28800

# RestTemplate配置
rest-template:
  # 连接超时时间（毫秒）
  connect-timeout: 5000
  # 读取超时时间（毫秒）
  read-timeout: 10000
