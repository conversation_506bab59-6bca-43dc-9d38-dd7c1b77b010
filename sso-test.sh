#!/bin/bash

# SSO功能测试脚本
# 用于验证主系统和从系统之间的SSO功能

echo "=========================================="
echo "         SSO单点登录功能测试"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置
MAIN_SYSTEM_URL="http://localhost:9200"
SUB_SYSTEM_URL="http://localhost:8081"

# 测试函数
test_api() {
    local url=$1
    local description=$2
    local expected_code=$3
    
    echo -e "${YELLOW}测试: $description${NC}"
    echo "URL: $url"
    
    response=$(curl -s -w "HTTPSTATUS:%{http_code}" "$url")
    http_code=$(echo $response | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')
    body=$(echo $response | sed -e 's/HTTPSTATUS\:.*//g')
    
    if [ "$http_code" -eq "$expected_code" ]; then
        echo -e "${GREEN}✓ 成功 (HTTP $http_code)${NC}"
    else
        echo -e "${RED}✗ 失败 (HTTP $http_code)${NC}"
    fi
    
    echo "响应内容: $body"
    echo "----------------------------------------"
}

# 开始测试
echo -e "${YELLOW}开始SSO功能测试...${NC}"
echo

# 1. 测试主系统SSO状态接口
test_api "$MAIN_SYSTEM_URL/sso/status" "主系统SSO状态检查" 200

# 2. 测试从系统SSO状态接口
test_api "$SUB_SYSTEM_URL/sso/status" "从系统SSO状态检查" 200

# 3. 测试从系统获取主系统登录地址
test_api "$SUB_SYSTEM_URL/sso/loginUrl" "获取主系统登录地址" 200

# 4. 测试从系统获取主系统登录地址（带重定向）
test_api "$SUB_SYSTEM_URL/sso/loginUrl?redirect=http://localhost:8081/dashboard" "获取主系统登录地址(带重定向)" 200

# 5. 测试主系统配置接口
test_api "$MAIN_SYSTEM_URL/sso/config/market" "获取市场系统配置" 200

echo
echo -e "${GREEN}=========================================="
echo "           测试完成"
echo "==========================================${NC}"
echo
echo -e "${YELLOW}手动测试步骤:${NC}"
echo "1. 清除浏览器缓存和Cookie"
echo "2. 访问从系统: $SUB_SYSTEM_URL"
echo "3. 验证是否自动跳转到主系统登录页面"
echo "4. 在主系统登录后验证是否跳转回从系统"
echo "5. 测试登出功能"
echo
echo -e "${YELLOW}检查日志:${NC}"
echo "- 主系统认证中心日志"
echo "- 从系统认证服务日志"
echo "- Redis缓存操作日志"
echo "- 浏览器控制台日志"
echo
