{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\TopNav\\index.vue?vue&type=template&id=35f3a2c1&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\TopNav\\index.vue", "mtime": 1750311962827}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygKICAgICJlbC1tZW51IiwKICAgIHsKICAgICAgYXR0cnM6IHsKICAgICAgICAiZGVmYXVsdC1hY3RpdmUiOiBfdm0uYWN0aXZlTWVudSwKICAgICAgICAibWVudS10cmlnZ2VyIjogImhvdmVyIiwKICAgICAgICBtb2RlOiAiaG9yaXpvbnRhbCIsCiAgICAgICAgImJhY2tncm91bmQtY29sb3IiOiAidHJhbnNwYXJlbnQiLAogICAgICB9LAogICAgICBvbjogeyBzZWxlY3Q6IF92bS5oYW5kbGVTZWxlY3QgfSwKICAgIH0sCiAgICBbCiAgICAgIF92bS5fbChfdm0ubWVudXMsIGZ1bmN0aW9uIChpdGVtLCBpbmRleCkgewogICAgICAgIHJldHVybiBbCiAgICAgICAgICBfYygKICAgICAgICAgICAgImVsLW1lbnUtaXRlbSIsCiAgICAgICAgICAgIHsKICAgICAgICAgICAgICBrZXk6IGluZGV4LAogICAgICAgICAgICAgIHN0YXRpY0NsYXNzOiAibmF2X3NwYW4iLAogICAgICAgICAgICAgIHN0eWxlOiB7ICItLXRoZW1lIjogIiM0NWM5YjgiIH0sCiAgICAgICAgICAgICAgYXR0cnM6IHsgaW5kZXg6IGl0ZW0ucGF0aCB9LAogICAgICAgICAgICB9LAogICAgICAgICAgICBbX3ZtLl92KCIgIiArIF92bS5fcyhpdGVtLm1ldGEudGl0bGUpICsgIiAiKV0KICAgICAgICAgICksCiAgICAgICAgXQogICAgICB9KSwKICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJzdXBwbHlEZW1hbmREb2NraW5nIiB9LCBbCiAgICAgICAgX2MoCiAgICAgICAgICAiZGl2IiwKICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJzdWItcHVyY2hhc2UtY29udGVudCIgfSwKICAgICAgICAgIF92bS5fbChfdm0uc3VwcGx5RGVtYW5kRG9ja2luZywgZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7CiAgICAgICAgICAgIHJldHVybiBfYygKICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICBrZXk6IGluZGV4LAogICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJjb250ZW50X2l0ZW0gaGVhZF90aXRsZV9saW5lIiwKICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgIGNsaWNrOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF92bS5nb1N1cHBseURlbWFuZERvY2tpbmcoaW5kZXgpCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJ0aXRsZSIgfSwgWwogICAgICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKGl0ZW0udGl0bGUpKSwKICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJ0aXRsZUxpbmUiIH0pLAogICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJkZXNjIiB9LCBbX3ZtLl92KF92bS5fcyhpdGVtLmRlc2MpKV0pLAogICAgICAgICAgICAgIF0KICAgICAgICAgICAgKQogICAgICAgICAgfSksCiAgICAgICAgICAwCiAgICAgICAgKSwKICAgICAgXSksCiAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAibWFudWZhY3R1cmluZ1NoYXJlIiB9LCBbCiAgICAgICAgX2MoCiAgICAgICAgICAiZGl2IiwKICAgICAgICAgIHsgc3RhdGljQ2xhc3M6ICJzdWItcHVyY2hhc2UtY29udGVudCIgfSwKICAgICAgICAgIF92bS5fbChfdm0ubWFudWZhY3R1cmluZ1NoYXJpbmcsIGZ1bmN0aW9uIChpdGVtLCBpbmRleCkgewogICAgICAgICAgICByZXR1cm4gX2MoCiAgICAgICAgICAgICAgImRpdiIsCiAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAga2V5OiBpbmRleCwKICAgICAgICAgICAgICAgIHN0YXRpY0NsYXNzOiAiY29udGVudF9pdGVtIGhlYWRfdGl0bGVfbGluZSIsCiAgICAgICAgICAgICAgICBvbjogewogICAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgIHJldHVybiBfdm0uZ29NYW51ZmFjdHVyaW5nU2hhcmluZyhpbmRleCkKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInRpdGxlIiB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fdihfdm0uX3MoaXRlbS50aXRsZSkpLAogICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInRpdGxlTGluZSIgfSksCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImRlc2MiIH0sIFtfdm0uX3YoX3ZtLl9zKGl0ZW0uZGVzYykpXSksCiAgICAgICAgICAgICAgXQogICAgICAgICAgICApCiAgICAgICAgICB9KSwKICAgICAgICAgIDAKICAgICAgICApLAogICAgICBdKSwKICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJzZXJ2aWNlU2hhcmUiIH0sIFsKICAgICAgICBfYygKICAgICAgICAgICJkaXYiLAogICAgICAgICAgeyBzdGF0aWNDbGFzczogInN1Yi1wdXJjaGFzZS1jb250ZW50IiB9LAogICAgICAgICAgX3ZtLl9sKF92bS5zZXJ2aWNlU2hhcmluZywgZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7CiAgICAgICAgICAgIHJldHVybiBfYygKICAgICAgICAgICAgICAiZGl2IiwKICAgICAgICAgICAgICB7CiAgICAgICAgICAgICAgICBrZXk6IGluZGV4LAogICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJjb250ZW50X2l0ZW0gaGVhZF90aXRsZV9saW5lIiwKICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgIGNsaWNrOiBmdW5jdGlvbiAoJGV2ZW50KSB7CiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIF92bS5nb1NlcnZpY2VTaGFyaW5nKGluZGV4KQogICAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIFsKICAgICAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAidGl0bGUiIH0sIFsKICAgICAgICAgICAgICAgICAgX3ZtLl92KF92bS5fcyhpdGVtLnRpdGxlKSksCiAgICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAidGl0bGVMaW5lIiB9KSwKICAgICAgICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiZGVzYyIgfSwgW192bS5fdihfdm0uX3MoaXRlbS5kZXNjKSldKSwKICAgICAgICAgICAgICBdCiAgICAgICAgICAgICkKICAgICAgICAgIH0pLAogICAgICAgICAgMAogICAgICAgICksCiAgICAgIF0pLAogICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImlubm92YXRpb25TaGFyZSIgfSwgWwogICAgICAgIF9jKAogICAgICAgICAgImRpdiIsCiAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAic3ViLXB1cmNoYXNlLWNvbnRlbnQiIH0sCiAgICAgICAgICBfdm0uX2woX3ZtLmlubm92YXRpb25TaGFyaW5nLCBmdW5jdGlvbiAoaXRlbSwgaW5kZXgpIHsKICAgICAgICAgICAgcmV0dXJuIF9jKAogICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIGtleTogaW5kZXgsCiAgICAgICAgICAgICAgICBzdGF0aWNDbGFzczogImNvbnRlbnRfaXRlbSBoZWFkX3RpdGxlX2xpbmUiLAogICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLmdvSW5ub3ZhdGlvblNoYXJpbmcoaW5kZXgpCiAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgWwogICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJ0aXRsZSIgfSwgWwogICAgICAgICAgICAgICAgICBfdm0uX3YoX3ZtLl9zKGl0ZW0udGl0bGUpKSwKICAgICAgICAgICAgICAgIF0pLAogICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJ0aXRsZUxpbmUiIH0pLAogICAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJkZXNjIiB9LCBbX3ZtLl92KF92bS5fcyhpdGVtLmRlc2MpKV0pLAogICAgICAgICAgICAgIF0KICAgICAgICAgICAgKQogICAgICAgICAgfSksCiAgICAgICAgICAwCiAgICAgICAgKSwKICAgICAgXSksCiAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAiYWJvdXRVcyIgfSwgWwogICAgICAgIF9jKAogICAgICAgICAgImRpdiIsCiAgICAgICAgICB7IHN0YXRpY0NsYXNzOiAic3ViLXB1cmNoYXNlLWNvbnRlbnQiIH0sCiAgICAgICAgICBfdm0uX2woX3ZtLmFib3V0VXNMaXN0LCBmdW5jdGlvbiAoaXRlbSwgaW5kZXgpIHsKICAgICAgICAgICAgcmV0dXJuIF9jKAogICAgICAgICAgICAgICJkaXYiLAogICAgICAgICAgICAgIHsKICAgICAgICAgICAgICAgIGtleTogaW5kZXgsCiAgICAgICAgICAgICAgICBzdGF0aWNDbGFzczogImNvbnRlbnRfaXRlbSBoZWFkX3RpdGxlX2xpbmUiLAogICAgICAgICAgICAgICAgb246IHsKICAgICAgICAgICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLmdvQWJvdXRVcyhpbmRleCkKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICBbCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInRpdGxlIiB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fdihfdm0uX3MoaXRlbS50aXRsZSkpLAogICAgICAgICAgICAgICAgXSksCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInRpdGxlTGluZSIgfSksCiAgICAgICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImRlc2MiIH0sIFtfdm0uX3YoX3ZtLl9zKGl0ZW0uZGVzYykpXSksCiAgICAgICAgICAgICAgXQogICAgICAgICAgICApCiAgICAgICAgICB9KSwKICAgICAgICAgIDAKICAgICAgICApLAogICAgICBdKSwKICAgIF0sCiAgICAyCiAgKQp9CnZhciBzdGF0aWNSZW5kZXJGbnMgPSBbXQpyZW5kZXIuX3dpdGhTdHJpcHBlZCA9IHRydWUKCmV4cG9ydCB7IHJlbmRlciwgc3RhdGljUmVuZGVyRm5zIH0="}]}