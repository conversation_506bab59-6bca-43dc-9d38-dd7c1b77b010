{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\IframeToggle\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\IframeToggle\\index.vue", "mtime": 1750311962844}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgSW5uZXJMaW5rIGZyb20gIi4uL0lubmVyTGluay9pbmRleCINCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7IElubmVyTGluayB9LA0KICBjb21wdXRlZDogew0KICAgIGlmcmFtZVZpZXdzKCkgew0KICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLnN0YXRlLnRhZ3NWaWV3LmlmcmFtZVZpZXdzDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;AAaA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/IframeToggle", "sourcesContent": ["<template>\r\n  <transition-group name=\"fade-transform\" mode=\"out-in\">\r\n    <inner-link\r\n      v-for=\"(item, index) in iframeViews\"\r\n      :key=\"item.path\"\r\n      :iframeId=\"'iframe' + index\"\r\n      v-show=\"$route.path === item.path\"\r\n      :src=\"item.meta.link\"\r\n    ></inner-link>\r\n  </transition-group>\r\n</template>\r\n\r\n<script>\r\nimport InnerLink from \"../InnerLink/index\"\r\n\r\nexport default {\r\n  components: { InnerLink },\r\n  computed: {\r\n    iframeViews() {\r\n      return this.$store.state.tagsView.iframeViews\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}