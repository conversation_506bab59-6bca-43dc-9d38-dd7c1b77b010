{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\enterpriseList\\index.vue?vue&type=style&index=1&id=6d8d0f52&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\enterpriseList\\index.vue", "mtime": 1750311962987}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZW50ZXJwcmlzZS1saXN0LWNvbnRhaW5lciB7DQogIC5lbnRlcnByaXNlLWxpc3Qtc2VhcmNoLWlucHV0IHsNCiAgICAuZWwtaW5wdXRfX2lubmVyIHsNCiAgICAgIGhlaWdodDogNTRweDsNCiAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICBib3JkZXItcmFkaXVzOiAyN3B4IDAgMCAyN3B4Ow0KICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLVJlZ3VsYXIsIFBpbmdGYW5nIFNDOw0KICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgICBwYWRkaW5nLWxlZnQ6IDMwcHg7DQogICAgfQ0KICAgIC5lbC1pbnB1dC1ncm91cF9fYXBwZW5kIHsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDBweCAxMDBweCAxMDBweCAwcHg7DQogICAgICBiYWNrZ3JvdW5kOiAjMjFjOWI4Ow0KICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtUmVndWxhciwgUGluZ0ZhbmcgU0M7DQogICAgICBjb2xvcjogI2ZmZjsNCiAgICAgIGxpbmUtaGVpZ2h0OiAyNHB4Ow0KICAgIH0NCiAgfQ0KICAuZWwtZm9ybS1pdGVtX19sYWJlbCB7DQogICAgd2lkdGg6IDg4cHg7DQogICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtTWVkaXVtLCBQaW5nRmFuZyBTQzsNCiAgICBmb250LXdlaWdodDogNTAwOw0KICAgIGNvbG9yOiAjOTk5Ow0KICAgIHBhZGRpbmctcmlnaHQ6IDMycHg7DQogICAgdGV4dC1hbGlnbjogbGVmdDsNCiAgfQ0KICAuZW50ZXJwcmlzZS1saXN0LXNlYXJjaC1saW5lIHsNCiAgICAuZWwtZm9ybS1pdGVtX19jb250ZW50IHsNCiAgICAgIHdpZHRoOiA5NzBweDsNCiAgICB9DQogIH0NCiAgLmVsLXJhZGlvLWJ1dHRvbiB7DQogICAgcGFkZGluZy1ib3R0b206IDIwcHg7DQogICAgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgew0KICAgICAgYm9yZGVyOiBub25lOw0KICAgICAgcGFkZGluZzogMCAzMnB4IDAgMDsNCiAgICAgIGJhY2tncm91bmQ6IG5vbmU7DQogICAgICAmOmhvdmVyIHsNCiAgICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgICB9DQogICAgfQ0KICAgICYuaXMtYWN0aXZlIHsNCiAgICAgIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsNCiAgICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgICAgIGJhY2tncm91bmQ6IG5vbmU7DQogICAgICB9DQogICAgfQ0KICAgIC5lbC1yYWRpby1idXR0b25fX29yaWctcmFkaW86Y2hlY2tlZCB7DQogICAgICAmICsgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgew0KICAgICAgICBib3gtc2hhZG93OiB1bnNldDsNCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLmVudGVycHJpc2UtbGlzdC1wYWdlLWVuZCB7DQogICAgLmVudGVycHJpc2UtbGlzdC1wYWdpbmF0aW9uIHsNCiAgICAgIC5idG4tcHJldiwNCiAgICAgIC5idG4tbmV4dCwNCiAgICAgIC5idG4tcXVpY2twcmV2IHsNCiAgICAgICAgd2lkdGg6IDMycHg7DQogICAgICAgIGhlaWdodDogMzJweDsNCiAgICAgICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgIH0NCiAgICAgICYuaXMtYmFja2dyb3VuZCB7DQogICAgICAgIC5lbC1wYWdlciB7DQogICAgICAgICAgLm51bWJlciB7DQogICAgICAgICAgICB3aWR0aDogMzJweDsNCiAgICAgICAgICAgIGhlaWdodDogMzJweDsNCiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDMycHg7DQogICAgICAgICAgICAmLmFjdGl2ZSB7DQogICAgICAgICAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMyMWM5Yjg7DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/purchaseSales/component/enterpriseList", "sourcesContent": ["<template>\r\n  <div class=\"enterprise-list-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"enterprise-list-banner\">\r\n      <img src=\"../../../../assets/enterprise/enterpriseBanner.png\" alt=\"\" />\r\n      <div class=\"bannerTitle\">服务机构</div>\r\n      <div class=\"bannerDesc\">\r\n        凝聚行业顶尖服务机构，提供全方面多领域的星碳服务\r\n      </div>\r\n    </div>\r\n    <div v-loading=\"loading\" style=\"margin-top: 40px\">\r\n      <div class=\"enterprise-list-title-content\">\r\n        <div class=\"enterprise-list-search-box\">\r\n          <el-form ref=\"form\" class=\"enterprise-list-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.queryParam\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"enterprise-list-search-input\"\r\n                :maxlength=\"255\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"enterprise-list-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"enterprise-list-card\">\r\n        <div class=\"enterprise-list-content\">\r\n          <div class=\"enterprise-list-search-type-box\">\r\n            <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n              <div class=\"enterprise-list-search-line\">\r\n                <el-form-item\r\n                  label=\"企业分类\"\r\n                  class=\"enterprise-list-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.companyLabel\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in companyLabelList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.value\"\r\n                      >{{ item.label }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"enterprise-list-search-line\">\r\n                <el-form-item\r\n                  label=\"产业链\"\r\n                  class=\"enterprise-list-search-line-item\"\r\n                  :class=\"{ advanced: !advancedIndustrial }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.industrialChain\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in industrialChainList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"enterprise-list-search-line-btn\"\r\n                  @click=\"toggleIndustry\"\r\n                  >{{ advancedIndustrial ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n              <div class=\"enterprise-list-search-line\">\r\n                <el-form-item\r\n                  label=\"行业领域\"\r\n                  class=\"enterprise-list-search-line-item\"\r\n                  :class=\"{ advanced: !advancedIndustry }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.industry\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in industryList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"enterprise-list-search-line-btn\"\r\n                  @click=\"toggleTrade\"\r\n                  >{{ advancedIndustry ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"enterprise-list-list-item\"\r\n            @click=\"goEnterpriseDetail(item)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img\r\n                  v-if=\"\r\n                    item.companyPictureList &&\r\n                    item.companyPictureList.length > 0\r\n                  \"\r\n                  alt=\"\"\r\n                  :src=\"item.companyPictureList[0].url\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/companyDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"list-item-box\">\r\n                <div class=\"item-title\">{{ item.name }}</div>\r\n                <div class=\"item-info-box\">\r\n                  {{ item.introduce }}\r\n                </div>\r\n                <div class=\"item-tag-content\">\r\n                  <div class=\"item-tag-box\">\r\n                    <div class=\"item-tag\" v-show=\"item.category\">\r\n                      {{ item.category }}\r\n                    </div>\r\n                    <div v-if=\"item.address\" class=\"item-address-tag\">\r\n                      <i class=\"el-icon-location item-address-img\"></i>\r\n                      <!-- <img\r\n                        src=\"../../../../assets/enterprise/addressIcon.png\"\r\n                        alt=\"\"\r\n                        class=\"item-address-img\"\r\n                      /> -->\r\n                      <div class=\"item-address-text\">{{ item.address }}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- <div class=\"list-item-img\">\r\n                <img\r\n                  v-if=\"\r\n                    item.companyPictureList &&\r\n                    item.companyPictureList.length > 0\r\n                  \"\r\n                  alt=\"\"\r\n                  :src=\"item.companyPictureList[0].url\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/companyDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div> -->\r\n            </div>\r\n          </div>\r\n          <div class=\"enterprise-list-page-end\">\r\n            <el-button class=\"enterprise-list-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            >\r\n            <el-pagination\r\n              v-if=\"data && data.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"enterprise-list-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @size-change=\"handleSizeChange\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { ENTERPRISE_TYPE } from \"@/const/status\";\r\nimport { getCompanyHomeList } from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        queryParam: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        companyLabel: \"\", //企业分类\r\n        industrialChain: \"\", //产业链\r\n        industry: \"\", //行业领域\r\n      },\r\n      companyLabelList: ENTERPRISE_TYPE, //企业分类列表\r\n      industrialChainList: [], //产业链字典列表\r\n      industryList: [], //行业领域字典列表\r\n      advancedIndustrial: false,\r\n      advancedIndustry: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getDictsList(\"industrial_chain\", \"industrialChainList\");\r\n    this.getDictsList(\"company_industry\", \"industryList\");\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getCompanyHomeList({\r\n        ...this.form,\r\n        ...this.formInfo,\r\n        recommendStatus: 1,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    // 显示更多产业链\r\n    toggleIndustry() {\r\n      this.advancedIndustrial = !this.advancedIndustrial;\r\n    },\r\n    // 显示更多行业领域\r\n    toggleTrade() {\r\n      this.advancedIndustry = !this.advancedIndustry;\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    // 跳转到企业名录详情页面\r\n    goEnterpriseDetail(item) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/enterpriseDetail\",\r\n        query: { id: item.id, businessNo: item.businessNo },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.enterprise-list-container {\r\n  width: 100%;\r\n  .enterprise-list-banner {\r\n    width: 100%;\r\n    height: 500px;\r\n    position: relative;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    .bannerTitle {\r\n      position: absolute;\r\n      top: 161px;\r\n      left: 24%;\r\n      font-size: 50px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n    }\r\n    .bannerDesc {\r\n      position: absolute;\r\n      top: 249px;\r\n      left: 24%;\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .enterprise-list-title-content {\r\n    width: 100%;\r\n    .enterprise-list-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .enterprise-list-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .enterprise-list-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .enterprise-list-search-box {\r\n      .enterprise-list-search-form {\r\n        text-align: center;\r\n        .enterprise-list-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .enterprise-list-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .enterprise-list-card {\r\n    background: #ffffff;\r\n    .enterprise-list-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      .enterprise-list-search-type-box {\r\n        background: #fff;\r\n        margin-bottom: 17px;\r\n        .enterprise-list-search-line {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 14px 24px 4px;\r\n          .enterprise-list-search-line-item {\r\n            flex: 1;\r\n            margin-bottom: 0;\r\n            display: flex;\r\n            &.advanced {\r\n              overflow: hidden;\r\n              height: 45px;\r\n            }\r\n            .more-radio {\r\n              margin-top: 11px;\r\n              flex: 1;\r\n            }\r\n          }\r\n          .enterprise-list-search-line-btn {\r\n            display: inline-block;\r\n            width: 64px;\r\n            height: 24px;\r\n            background: #fff;\r\n            border-radius: 2px;\r\n            border: 1px solid #d9d9d9;\r\n            font-size: 12px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            color: #333;\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 0 16px;\r\n            margin-top: 5px;\r\n            &:hover {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n          & + .enterprise-list-search-line {\r\n            border-top: 1px solid #f5f5f5;\r\n          }\r\n        }\r\n      }\r\n      .enterprise-list-list-item {\r\n        width: 100%;\r\n        height: 220px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n        .list-item-content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 24px 24px 24px 32px;\r\n          cursor: pointer;\r\n          .list-item-box {\r\n            width: 880px;\r\n            // margin-left: 32px;\r\n            // padding-top: 8px;\r\n            .item-title {\r\n              width: 806px;\r\n              height: 24px;\r\n              font-size: 22px;\r\n              font-family: Source Han Sans CN;\r\n              font-weight: 500;\r\n              color: #222222;\r\n              line-height: 24px;\r\n              margin-bottom: 12px;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              word-break: break-all;\r\n            }\r\n            .item-tag-content {\r\n              display: flex;\r\n              align-items: top;\r\n              .item-tag-box {\r\n                display: flex;\r\n                width: 852px;\r\n                flex-wrap: wrap;\r\n                .item-tag {\r\n                  height: 38px;\r\n                  background: rgb(242, 245, 255);\r\n                  border-radius: 4px;\r\n                  padding: 0 12px;\r\n                  max-width: 860px;\r\n                  line-height: 38px;\r\n                  margin-right: 16px;\r\n                  margin-top: 12px;\r\n                  color: #21c9b8;\r\n                  word-break: break-all;\r\n                }\r\n                .item-address-tag {\r\n                  display: flex;\r\n                  align-items: center;\r\n                  padding: 0 12px 0 5px;\r\n                  line-height: 24px;\r\n                  text-align: center;\r\n                  margin-right: 16px;\r\n                  margin-top: 12px;\r\n                  background: rgb(240, 249, 247);\r\n                  border-radius: 2px;\r\n                  .item-address-img {\r\n                    width: 14px;\r\n                    height: 18px;\r\n                    line-height: 18px;\r\n                    margin-right: 4px;\r\n                    color: #039477;\r\n                  }\r\n                  .item-address-text {\r\n                    color: #039477;\r\n                    max-width: 860px;\r\n                    word-break: break-all;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n            .item-info-box {\r\n              width: 806px;\r\n              height: 78px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #65676a;\r\n              line-height: 26px;\r\n              margin-top: 24px;\r\n              overflow: hidden;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n              -webkit-line-clamp: 3;\r\n              text-overflow: ellipsis;\r\n              word-break: break-all;\r\n            }\r\n          }\r\n          .list-item-img {\r\n            width: 220px;\r\n            height: 160px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n              border-radius: 7px;\r\n            }\r\n          }\r\n          &:hover {\r\n            .list-item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        & + .enterprise-list-list-item {\r\n          margin-top: 24px;\r\n        }\r\n      }\r\n      .enterprise-list-list-item:hover {\r\n        box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.2);\r\n      }\r\n      .enterprise-list-page-end {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        padding: 24px 0 60px;\r\n        .enterprise-list-page-btn {\r\n          width: 82px;\r\n          height: 32px;\r\n          background: #fff;\r\n          border-radius: 4px;\r\n          border: 1px solid #d9d9d9;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.enterprise-list-container {\r\n  .enterprise-list-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .el-form-item__label {\r\n    width: 88px;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #999;\r\n    padding-right: 32px;\r\n    text-align: left;\r\n  }\r\n  .enterprise-list-search-line {\r\n    .el-form-item__content {\r\n      width: 970px;\r\n    }\r\n  }\r\n  .el-radio-button {\r\n    padding-bottom: 20px;\r\n    .el-radio-button__inner {\r\n      border: none;\r\n      padding: 0 32px 0 0;\r\n      background: none;\r\n      &:hover {\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        color: #21c9b8;\r\n        background: none;\r\n      }\r\n    }\r\n    .el-radio-button__orig-radio:checked {\r\n      & + .el-radio-button__inner {\r\n        box-shadow: unset;\r\n      }\r\n    }\r\n  }\r\n  .enterprise-list-page-end {\r\n    .enterprise-list-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}