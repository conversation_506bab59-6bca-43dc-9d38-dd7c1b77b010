{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\noninductive\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\noninductive\\detail\\index.vue", "mtime": 1750311963064}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZm9yLWVhY2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiKTsKdmFyIF91c2VyTWVudSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiKSk7CnZhciBfbm9uaW5kdWN0aXZlID0gcmVxdWlyZSgiQC9hcGkvc3lzdGVtL25vbmluZHVjdGl2ZSIpOwp2YXIgX2RhdGEgPSByZXF1aXJlKCJAL2FwaS9zeXN0ZW0vZGljdC9kYXRhLmpzIik7CnZhciBfb3NzID0gcmVxdWlyZSgiQC9hcGkvb3NzIik7CnZhciBfYXV0aCA9IHJlcXVpcmUoIkAvdXRpbHMvYXV0aCIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKdmFyIGlkID0gMDsKdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJOb3RpY2UiLAogIGRpY3RzOiBbImFmZmlsaWF0ZWRfdW5pdCIsICJjYXBpdGFsX3NvdXJjZSIsICJhZmZpbGlhdGVkX3N0cmVldCJdLAogIGNvbXBvbmVudHM6IHsKICAgIFVzZXJNZW51OiBfdXNlck1lbnUuZGVmYXVsdAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlzRGV0YWlsOiB0cnVlLAogICAgICBhY3Rpb25Vcmw6ICgwLCBfb3NzLnVwbG9hZFVybCkoKSwKICAgICAgaGVhZGVyczogewogICAgICAgIEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArICgwLCBfYXV0aC5nZXRUb2tlbikoKQogICAgICB9LAogICAgICBpbmZvOiB7fSwKICAgICAgZm9ybToge30sCiAgICAgIGFjY291bnRMaWNlbmNlTGlzdDogW10sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIGFmZmlsaWF0ZWRVbml0Q29kZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuaJgOWxnuWNleS9jeS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBjb21wYW55TmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuWFrOWPuOWQjeensOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBjb21wYW55Q3JlZGl0Q29kZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuS8geS4mue7n+S4gOS7o+eggeS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBjb250YWN0c05hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLogZTns7vkurrkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgY2FwaXRhbFNvdXJjZUNvZGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLotYTph5HmnaXmupDkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYWZmaWxpYXRlZFN0cmVldENvZGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmiYDlsZ7ooZfpgZPkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYmFua05hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLlvIDmiLfpk7booYzkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgY29udGFjdHNQaG9uZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuiBlOezu+eUteivneS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBpdGVtVHlwZUNvZGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLpgInmi6npobnnm67kuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgY29tcGFueUFkZHJlc3M6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLkvIHkuJrms6jlhozlnLDlnYDkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYmFua0NvZGU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLpk7booYzotKblj7fkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYXBwbGljYXRpb246IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLnlLPor7fkuabkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYWNjb3VudExpY2VuY2U6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLnlLPor7fkuabkuI3og73kuLrnqboiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0KICAgICAgfSwKICAgICAgcHJvamVjdFByb3BzOiB7CiAgICAgICAgbGF6eTogdHJ1ZSwKICAgICAgICBsYXp5TG9hZDogZnVuY3Rpb24gbGF6eUxvYWQobm9kZSwgcmVzb2x2ZSkgewogICAgICAgICAgdmFyIHJlcyA9IFtdOwogICAgICAgICAgaWYgKG5vZGUubGV2ZWwgPT0gMCkgewogICAgICAgICAgICAoMCwgX2RhdGEuZ2V0RGljdHMpKCJpdGVtX3R5cGUiKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIHJlc3BvbnNlLmRhdGEuZm9yRWFjaChmdW5jdGlvbiAoZWxlbWVudCkgewogICAgICAgICAgICAgICAgcmVzLnB1c2goewogICAgICAgICAgICAgICAgICBsYWJlbDogZWxlbWVudC5kaWN0TGFiZWwsCiAgICAgICAgICAgICAgICAgIHZhbHVlOiBlbGVtZW50LmRpY3RWYWx1ZSwKICAgICAgICAgICAgICAgICAgZGljdENvZGU6IGVsZW1lbnQuZGljdENvZGUKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHZhciBub2RlcyA9IHJlczsKICAgICAgICAgICAgICByZXNvbHZlKG5vZGVzKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAoMCwgX2RhdGEuZ2V0U2Vjb25kRGljdHMpKHsKICAgICAgICAgICAgICBwYXJlbnRDb2RlOiBub2RlLmRhdGEuZGljdENvZGUKICAgICAgICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goZnVuY3Rpb24gKGVsZW1lbnQpIHsKICAgICAgICAgICAgICAgIHJlcy5wdXNoKHsKICAgICAgICAgICAgICAgICAgbGFiZWw6IGVsZW1lbnQuZGljdExhYmVsLAogICAgICAgICAgICAgICAgICB2YWx1ZTogZWxlbWVudC5kaWN0VmFsdWUsCiAgICAgICAgICAgICAgICAgIGxlYWY6IHRydWUKICAgICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICAgIHZhciBub2RlcyA9IHJlczsKICAgICAgICAgICAgICByZXNvbHZlKG5vZGVzKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgICAvLyBjb25zdCBub2RlcyA9IEFycmF5LmZyb20oeyBsZW5ndGg6IGxldmVsICsgMSB9KS5tYXAoKGl0ZW0pID0+ICh7CiAgICAgICAgICAvLyAgIHZhbHVlOiArK2lkLAogICAgICAgICAgLy8gICBsYWJlbDogYOmAiemhuSR7aWR9YCwKICAgICAgICAgIC8vICAgbGVhZjogbGV2ZWwgPj0gMiwKICAgICAgICAgIC8vIH0pKTsKICAgICAgICAgIC8vIOmAmui/h+iwg+eUqHJlc29sdmXlsIblrZDoioLngrnmlbDmja7ov5Tlm57vvIzpgJrnn6Xnu4Tku7bmlbDmja7liqDovb3lrozmiJAKICAgICAgICB9CiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXREZXRhaWwoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGl0ZW1UeXBlQ2hhbmdlZDogZnVuY3Rpb24gaXRlbVR5cGVDaGFuZ2VkKHZhbCkgewogICAgICB0aGlzLmZvcm0uaXRlbVR5cGVGaXJzdENvZGUgPSB2YWxbMF07CiAgICAgIHRoaXMuZm9ybS5pdGVtVHlwZVNlY29uZENvZGUgPSB2YWxbMV07CiAgICB9LAogICAgZ2V0RGV0YWlsOiBmdW5jdGlvbiBnZXREZXRhaWwoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHZhciB1c2VySWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsKICAgICAgKDAsIF9ub25pbmR1Y3RpdmUuZ2V0Tm9uaW5kdWN0aXZlRGV0YWlsKSh1c2VySWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMuaW5mbyA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsKICAgICAgfSk7CiAgICB9LAogICAgLy8g55Sz6K+35Lmm44CB5byA5oi36K645Y+v6K+B6aKE6KeICiAgICBoYW5kbGVQcmV2aWV3OiBmdW5jdGlvbiBoYW5kbGVQcmV2aWV3KGZpbGUpIHsKICAgICAgd2luZG93Lm9wZW4oZmlsZS51cmwpOwogICAgfSwKICAgIC8vIOW8gOaIt+iuuOWPr+ivgeS4iuS8oOaVsOmHj+mZkOWItgogICAgaGFuZGxlRXhjZWVkTGljZW5jZTogZnVuY3Rpb24gaGFuZGxlRXhjZWVkTGljZW5jZShmaWxlcywgZmlsZUxpc3QpIHsKICAgICAgdmFyIG51bSA9IGZpbGVzLmxlbmd0aCArIGZpbGVMaXN0Lmxlbmd0aDsKICAgICAgaWYgKG51bSA+PSAxKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5LiK5Lyg5pWw6YeP6LaF6L+H5LiK6ZmQIik7CiAgICAgICAgcmV0dXJuIGZhbHNlOwogICAgICB9CiAgICB9LAogICAgZ29CYWNrOiBmdW5jdGlvbiBnb0JhY2soKSB7CiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7CiAgICB9LAogICAgY2hhbmdlTW9kZTogZnVuY3Rpb24gY2hhbmdlTW9kZSgpIHsKICAgICAgaWYgKHRoaXMuaXNEZXRhaWwpIHsKICAgICAgICB0aGlzLmlzRGV0YWlsID0gZmFsc2U7CiAgICAgICAgdGhpcy5mb3JtID0gdGhpcy5pbmZvOwogICAgICAgIHRoaXMuZm9ybS5pdGVtVHlwZUNvZGUgPSBbdGhpcy5pbmZvLml0ZW1UeXBlRmlyc3RDb2RlLCB0aGlzLmluZm8uaXRlbVR5cGVTZWNvbmRDb2RlXTsKICAgICAgICB0aGlzLmFjY291bnRMaWNlbmNlTGlzdCA9IHRoaXMuaW5mby5hY2NvdW50TGljZW5jZSA/IFt7CiAgICAgICAgICBuYW1lOiB0aGlzLmluZm8uYWNjb3VudExpY2VuY2VOYW1lLAogICAgICAgICAgdXJsOiB0aGlzLmluZm8uYWNjb3VudExpY2VuY2UKICAgICAgICB9XSA6IFtdOwogICAgICAgIHRoaXMuYXBwbGljYXRpb25MaXN0ID0gdGhpcy5pbmZvLmFwcGxpY2F0aW9uID8gW3sKICAgICAgICAgIG5hbWU6IHRoaXMuaW5mby5hcHBsaWNhdGlvbk5hbWUsCiAgICAgICAgICB1cmw6IHRoaXMuaW5mby5hcHBsaWNhdGlvbgogICAgICAgIH1dIDogW107CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5pc0RldGFpbCA9IHRydWU7CiAgICAgICAgdGhpcy5mb3JtID0ge307CiAgICAgICAgdGhpcy5nZXREZXRhaWwoKTsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZUZpbGVQcmV2aWV3OiBmdW5jdGlvbiBoYW5kbGVGaWxlUHJldmlldyhmaWxlKSB7CiAgICAgIHdpbmRvdy5vcGVuKGZpbGUpOwogICAgfSwKICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uIHN1Ym1pdEZvcm0odHlwZSkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgKDAsIF9ub25pbmR1Y3RpdmUuZWRpdE5vbmluZHVjdGl2ZSkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgX3RoaXMyLmZvcm0pLCB7fSwgewogICAgICAgICAgICBpc1N1Ym1pdDogdHlwZQogICAgICAgICAgfSkpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgIF90aGlzMi4kbW9kYWwubXNnU3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7CiAgICAgICAgICAgIF90aGlzMi5jaGFuZ2VNb2RlKCk7CiAgICAgICAgICB9KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUFwcGxpY2F0aW9uUmVtb3ZlOiBmdW5jdGlvbiBoYW5kbGVBcHBsaWNhdGlvblJlbW92ZShmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb24gPSAiIjsKICAgIH0sCiAgICBoYW5kbGVBcHBsaWNhdGlvblN1Y2Nlc3M6IGZ1bmN0aW9uIGhhbmRsZUFwcGxpY2F0aW9uU3VjY2VzcyhyZXMsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIC8v5q2k5aSE5YaZ5LiK5Lygb3Nz5oiQ5Yqf5LmL5ZCO55qE6YC76L6RCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsKICAgICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb24gPSByZXMuZGF0YS51cmw7CiAgICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uTmFtZSA9IHJlcy5kYXRhLm5hbWU7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVBY2NvdW50UmVtb3ZlOiBmdW5jdGlvbiBoYW5kbGVBY2NvdW50UmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIHRoaXMuZm9ybS5hY2NvdW50TGljZW5jZSA9ICIiOwogICAgfSwKICAgIGhhbmRsZUFjY291bnRTdWNjZXNzOiBmdW5jdGlvbiBoYW5kbGVBY2NvdW50U3VjY2VzcyhyZXMsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIC8v5q2k5aSE5YaZ5LiK5Lygb3Nz5oiQ5Yqf5LmL5ZCO55qE6YC76L6RCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsKICAgICAgICB0aGlzLmZvcm0uYWNjb3VudExpY2VuY2UgPSByZXMuZGF0YS51cmw7CiAgICAgICAgdGhpcy5mb3JtLmFjY291bnRMaWNlbmNlTmFtZSA9IHJlcy5kYXRhLm5hbWU7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_noninductive", "_data", "_oss", "_auth", "id", "_default", "exports", "default", "name", "dicts", "components", "UserMenu", "data", "isDetail", "actionUrl", "uploadUrl", "headers", "Authorization", "getToken", "info", "form", "accountLicenceList", "rules", "affiliatedUnitCode", "required", "message", "trigger", "companyName", "companyCreditCode", "contactsName", "capitalSourceCode", "affiliatedStreetCode", "bankName", "contactsPhone", "itemTypeCode", "companyAddress", "bankCode", "application", "accountLicence", "projectProps", "lazy", "lazyLoad", "node", "resolve", "res", "level", "getDicts", "then", "response", "for<PERSON>ach", "element", "push", "label", "dict<PERSON><PERSON>l", "value", "dict<PERSON><PERSON>ue", "dictCode", "nodes", "getSecondDicts", "parentCode", "leaf", "created", "getDetail", "methods", "itemTypeChanged", "val", "itemTypeFirstCode", "itemTypeSecondCode", "_this", "userId", "$route", "query", "getNoninductiveDetail", "total", "handlePreview", "file", "window", "open", "url", "handleExceedLicence", "files", "fileList", "num", "length", "$message", "error", "goBack", "$router", "go", "changeMode", "accountLicenceName", "applicationList", "applicationName", "handleFilePreview", "submitForm", "type", "_this2", "$refs", "validate", "valid", "editNoninductive", "_objectSpread2", "isSubmit", "$modal", "msgSuccess", "handleApplicationRemove", "handleApplicationSuccess", "code", "handleAccountRemove", "handleAccountSuccess"], "sources": ["src/views/system/user/noninductive/detail/index.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-03-24 08:55:04\r\n * @Description:\r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"notice-record-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"18\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">兑现详情</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                基本信息\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 所属单位 </template>\r\n                  {{ info.affiliatedUnitName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 资金来源 </template>\r\n                  {{ info.capitalSourceName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 项目名称</template>\r\n                  {{\r\n                    info.itemTypeFirstName || \"\" + info.itemTypeSecondName || \"\"\r\n                  }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 公司名称</template>\r\n                  {{ info.companyName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 所属街道</template>\r\n                  {{ info.affiliatedStreetName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 企业注册地址</template>\r\n                  {{ info.companyAddress }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 企业统一代码 </template>\r\n                  {{ info.companyCreditCode }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 开户银行</template>\r\n                  {{ info.bankName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 银行账号</template>\r\n                  {{ info.bankCode }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系人</template>\r\n                  {{ info.contactsName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系电话 </template>\r\n                  {{ info.contactsPhone }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 申请书 </template>\r\n                  <div v-if=\"!info.application\">--</div>\r\n                  <a\r\n                    v-else\r\n                    class=\"file-class\"\r\n                    @click=\"handleFilePreview(info.application)\"\r\n                  >\r\n                    <el-image\r\n                      style=\"width: 14px; height: 17px\"\r\n                      :src=\"require('@/assets/user/file_pdf.png')\"\r\n                    ></el-image>\r\n                    {{ info.applicationName }}\r\n                    <div class=\"previwe-class\">立即查看</div>\r\n                  </a>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 开户许可证 </template>\r\n                  <div v-if=\"!info.accountLicence\">--</div>\r\n\r\n                  <a\r\n                    class=\"file-class\"\r\n                    v-else\r\n                    @click=\"handleFilePreview(info.accountLicence)\"\r\n                  >\r\n                    <el-image\r\n                      style=\"width: 14px; height: 17px\"\r\n                      :src=\"require('@/assets/user/file_pdf.png')\"\r\n                    ></el-image>\r\n                    {{ info.accountLicenceName }}\r\n                    <div class=\"previwe-class\">立即查看</div>\r\n                  </a>\r\n                  <!-- <el-upload\r\n                    v-if=\"info.accountLicence\"\r\n                    class=\"upload-demo\"\r\n                    :on-preview=\"handleFilePreview(info.accountLicence)\"\r\n                  >\r\n                  </el-upload> -->\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <el-image\r\n                class=\"status_approving\"\r\n                v-if=\"info.status === '1'\"\r\n                style=\"width: 120px; height: 102px\"\r\n                :src=\"require('@/assets/user/status_approving.png')\"\r\n              ></el-image>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"goBack\">返回</el-button>\r\n                <el-button\r\n                  v-if=\"info.status == '0'\"\r\n                  type=\"danger\"\r\n                  @click=\"changeMode\"\r\n                  >编辑</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n            <div class=\"edit-page\" v-else>\r\n              <el-form\r\n                ref=\"form\"\r\n                :model=\"form\"\r\n                :rules=\"rules\"\r\n                label-width=\"120px\"\r\n              >\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"所属单位\" prop=\"affiliatedUnitCode\">\r\n                      <el-select\r\n                        v-model=\"form.affiliatedUnitCode\"\r\n                        placeholder=\"请选择所属单位\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.affiliated_unit\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n                      <el-input\r\n                        v-model=\"form.companyName\"\r\n                        placeholder=\"公司名称\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"企业统一代码\" prop=\"companyCreditCode\">\r\n                      <el-input\r\n                        v-model=\"form.companyCreditCode\"\r\n                        placeholder=\"请输入企业统一代码\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"联系人\" prop=\"contactsName\">\r\n                      <el-input\r\n                        v-model=\"form.contactsName\"\r\n                        placeholder=\"请输入联系人\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"资金来源\" prop=\"capitalSourceCode\">\r\n                      <el-select\r\n                        v-model=\"form.capitalSourceCode\"\r\n                        placeholder=\"请选择资金来源\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.capital_source\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"所属街道\" prop=\"affiliatedStreetCode\">\r\n                      <el-select\r\n                        v-model=\"form.affiliatedStreetCode\"\r\n                        placeholder=\"请选择所属街道\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.affiliated_street\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"开户银行\" prop=\"bankName\">\r\n                      <el-input\r\n                        v-model=\"form.bankName\"\r\n                        placeholder=\"请选择开户银行\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"联系电话\" prop=\"contactsPhone\">\r\n                      <el-input\r\n                        v-model=\"form.contactsPhone\"\r\n                        placeholder=\"请选择联系电话\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"选择项目\" prop=\"itemTypeCode\">\r\n                      <el-cascader\r\n                        ref=\"test\"\r\n                        :props=\"projectProps\"\r\n                        v-model=\"form.itemTypeCode\"\r\n                        placeholder=\"请选择项目\"\r\n                        @change=\"itemTypeChanged\"\r\n                      ></el-cascader>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"企业注册地址\" prop=\"companyAddress\">\r\n                      <el-input\r\n                        v-model=\"form.companyAddress\"\r\n                        placeholder=\"请输入企业注册地址\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"银行账号\" prop=\"bankCode\">\r\n                      <el-input\r\n                        v-model=\"form.bankCode\"\r\n                        placeholder=\"输入请银行账号\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"申请书上传\" prop=\"application\">\r\n                      <el-upload\r\n                        :headers=\"headers\"\r\n                        :action=\"actionUrl\"\r\n                        accept=\".pdf\"\r\n                        :file-list=\"applicationList\"\r\n                        :on-remove=\"handleApplicationRemove\"\r\n                        :on-success=\"handleApplicationSuccess\"\r\n                        :on-exceed=\"handleExceedLicence\"\r\n                        :on-preview=\"handlePreview\"\r\n                        :limit=\"1\"\r\n                      >\r\n                        <div>\r\n                          <el-button\r\n                            size=\"small\"\r\n                            type=\"primary\"\r\n                            icon=\"el-icon-upload2\"\r\n                            >上传文件</el-button\r\n                          >\r\n                          <span class=\"tip\">仅限pdf格式</span>\r\n                        </div>\r\n                      </el-upload>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"开户许可证上传\" prop=\"accountLicence\">\r\n                      <el-upload\r\n                        :headers=\"headers\"\r\n                        :action=\"actionUrl\"\r\n                        accept=\".pdf\"\r\n                        :file-list=\"accountLicenceList\"\r\n                        :on-remove=\"handleAccountRemove\"\r\n                        :on-exceed=\"handleExceedLicence\"\r\n                        :on-success=\"handleAccountSuccess\"\r\n                        :on-preview=\"handlePreview\"\r\n                        :limit=\"1\"\r\n                      >\r\n                        <div>\r\n                          <el-button\r\n                            size=\"small\"\r\n                            type=\"danger\"\r\n                            icon=\"el-icon-upload2\"\r\n                            >上传文件</el-button\r\n                          >\r\n                          <span class=\"tip\">仅限pdf格式</span>\r\n                        </div>\r\n                      </el-upload>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"changeMode\">返回</el-button>\r\n                <el-button type=\"error\" @click=\"changeMode(0)\"\r\n                  >暂存草稿</el-button\r\n                >\r\n                <el-button type=\"danger\" @click=\"submitForm(1)\"\r\n                  >提交审核</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport {\r\n  getNoninductiveDetail,\r\n  editNoninductive,\r\n} from \"@/api/system/noninductive\";\r\nimport { getDicts, getSecondDicts } from \"@/api/system/dict/data.js\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\"affiliated_unit\", \"capital_source\", \"affiliated_street\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      info: {},\r\n      form: {},\r\n      accountLicenceList: [],\r\n      // 表单校验\r\n      rules: {\r\n        affiliatedUnitCode: [\r\n          { required: true, message: \"所属单位不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyCreditCode: [\r\n          { required: true, message: \"企业统一代码不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        capitalSourceCode: [\r\n          { required: true, message: \"资金来源不能为空\", trigger: \"blur\" },\r\n        ],\r\n        affiliatedStreetCode: [\r\n          { required: true, message: \"所属街道不能为空\", trigger: \"blur\" },\r\n        ],\r\n        bankName: [\r\n          { required: true, message: \"开户银行不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsPhone: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n        itemTypeCode: [\r\n          { required: true, message: \"选择项目不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyAddress: [\r\n          { required: true, message: \"企业注册地址不能为空\", trigger: \"blur\" },\r\n        ],\r\n        bankCode: [\r\n          { required: true, message: \"银行账号不能为空\", trigger: \"blur\" },\r\n        ],\r\n        application: [\r\n          { required: true, message: \"申请书不能为空\", trigger: \"blur\" },\r\n        ],\r\n        accountLicence: [\r\n          { required: true, message: \"申请书不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      projectProps: {\r\n        lazy: true,\r\n        lazyLoad(node, resolve) {\r\n          let res = [];\r\n          if (node.level == 0) {\r\n            getDicts(\"item_type\").then((response) => {\r\n              response.data.forEach((element) => {\r\n                res.push({\r\n                  label: element.dictLabel,\r\n                  value: element.dictValue,\r\n                  dictCode: element.dictCode,\r\n                });\r\n              });\r\n              const nodes = res;\r\n              resolve(nodes);\r\n            });\r\n          } else {\r\n            getSecondDicts({ parentCode: node.data.dictCode }).then(\r\n              (response) => {\r\n                response.data.forEach((element) => {\r\n                  res.push({\r\n                    label: element.dictLabel,\r\n                    value: element.dictValue,\r\n                    leaf: true,\r\n                  });\r\n                });\r\n                const nodes = res;\r\n                resolve(nodes);\r\n              }\r\n            );\r\n          }\r\n          // const nodes = Array.from({ length: level + 1 }).map((item) => ({\r\n          //   value: ++id,\r\n          //   label: `选项${id}`,\r\n          //   leaf: level >= 2,\r\n          // }));\r\n          // 通过调用resolve将子节点数据返回，通知组件数据加载完成\r\n        },\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    itemTypeChanged(val) {\r\n      this.form.itemTypeFirstCode = val[0];\r\n      this.form.itemTypeSecondCode = val[1];\r\n    },\r\n    getDetail() {\r\n      let userId = this.$route.query.id;\r\n      getNoninductiveDetail(userId).then((response) => {\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    // 申请书、开户许可证预览\r\n    handlePreview(file) {\r\n      window.open(file.url);\r\n    },\r\n    // 开户许可证上传数量限制\r\n    handleExceedLicence(files, fileList) {\r\n      let num = files.length + fileList.length;\r\n      if (num >= 1) {\r\n        this.$message.error(\"上传数量超过上限\");\r\n        return false;\r\n      }\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n\r\n    changeMode() {\r\n      if (this.isDetail) {\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n        this.form.itemTypeCode = [\r\n          this.info.itemTypeFirstCode,\r\n          this.info.itemTypeSecondCode,\r\n        ];\r\n        this.accountLicenceList = this.info.accountLicence\r\n          ? [\r\n              {\r\n                name: this.info.accountLicenceName,\r\n                url: this.info.accountLicence,\r\n              },\r\n            ]\r\n          : [];\r\n        this.applicationList = this.info.application\r\n          ? [\r\n              {\r\n                name: this.info.applicationName,\r\n                url: this.info.application,\r\n              },\r\n            ]\r\n          : [];\r\n      } else {\r\n        this.isDetail = true;\r\n        this.form = {};\r\n        this.getDetail();\r\n      }\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          editNoninductive({ ...this.form, isSubmit: type }).then(\r\n            (response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            }\r\n          );\r\n        }\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.application = res.data.url;\r\n        this.form.applicationName = res.data.name;\r\n      }\r\n    },\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .notice-record-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 20px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 300px;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n        }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AA4UA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AAIA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATA,IAAAK,EAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAWA;EACAC,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA,MAAAC,cAAA;MACAC,OAAA;QAAAC,aAAA,kBAAAC,cAAA;MAAA;MACAC,IAAA;MACAC,IAAA;MACAC,kBAAA;MACA;MACAC,KAAA;QACAC,kBAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,WAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,iBAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,YAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,iBAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,oBAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAM,QAAA,GACA;UAAAR,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAO,aAAA,GACA;UAAAT,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAQ,YAAA,GACA;UAAAV,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAS,cAAA,GACA;UAAAX,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAU,QAAA,GACA;UAAAZ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAW,WAAA,GACA;UAAAb,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAY,cAAA,GACA;UAAAd,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAa,YAAA;QACAC,IAAA;QACAC,QAAA,WAAAA,SAAAC,IAAA,EAAAC,OAAA;UACA,IAAAC,GAAA;UACA,IAAAF,IAAA,CAAAG,KAAA;YACA,IAAAC,cAAA,eAAAC,IAAA,WAAAC,QAAA;cACAA,QAAA,CAAApC,IAAA,CAAAqC,OAAA,WAAAC,OAAA;gBACAN,GAAA,CAAAO,IAAA;kBACAC,KAAA,EAAAF,OAAA,CAAAG,SAAA;kBACAC,KAAA,EAAAJ,OAAA,CAAAK,SAAA;kBACAC,QAAA,EAAAN,OAAA,CAAAM;gBACA;cACA;cACA,IAAAC,KAAA,GAAAb,GAAA;cACAD,OAAA,CAAAc,KAAA;YACA;UACA;YACA,IAAAC,oBAAA;cAAAC,UAAA,EAAAjB,IAAA,CAAA9B,IAAA,CAAA4C;YAAA,GAAAT,IAAA,CACA,UAAAC,QAAA;cACAA,QAAA,CAAApC,IAAA,CAAAqC,OAAA,WAAAC,OAAA;gBACAN,GAAA,CAAAO,IAAA;kBACAC,KAAA,EAAAF,OAAA,CAAAG,SAAA;kBACAC,KAAA,EAAAJ,OAAA,CAAAK,SAAA;kBACAK,IAAA;gBACA;cACA;cACA,IAAAH,KAAA,GAAAb,GAAA;cACAD,OAAA,CAAAc,KAAA;YACA,CACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAC,eAAA,WAAAA,gBAAAC,GAAA;MACA,KAAA7C,IAAA,CAAA8C,iBAAA,GAAAD,GAAA;MACA,KAAA7C,IAAA,CAAA+C,kBAAA,GAAAF,GAAA;IACA;IACAH,SAAA,WAAAA,UAAA;MAAA,IAAAM,KAAA;MACA,IAAAC,MAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAnE,EAAA;MACA,IAAAoE,mCAAA,EAAAH,MAAA,EAAAtB,IAAA,WAAAC,QAAA;QACAoB,KAAA,CAAAjD,IAAA,GAAA6B,QAAA,CAAApC,IAAA;QACAwD,KAAA,CAAAK,KAAA,GAAAzB,QAAA,CAAAyB,KAAA;MACA;IACA;IACA;IACAC,aAAA,WAAAA,cAAAC,IAAA;MACAC,MAAA,CAAAC,IAAA,CAAAF,IAAA,CAAAG,GAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,KAAA,EAAAC,QAAA;MACA,IAAAC,GAAA,GAAAF,KAAA,CAAAG,MAAA,GAAAF,QAAA,CAAAE,MAAA;MACA,IAAAD,GAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEAC,UAAA,WAAAA,WAAA;MACA,SAAA5E,QAAA;QACA,KAAAA,QAAA;QACA,KAAAO,IAAA,QAAAD,IAAA;QACA,KAAAC,IAAA,CAAAc,YAAA,IACA,KAAAf,IAAA,CAAA+C,iBAAA,EACA,KAAA/C,IAAA,CAAAgD,kBAAA,CACA;QACA,KAAA9C,kBAAA,QAAAF,IAAA,CAAAmB,cAAA,GACA,CACA;UACA9B,IAAA,OAAAW,IAAA,CAAAuE,kBAAA;UACAZ,GAAA,OAAA3D,IAAA,CAAAmB;QACA,EACA,GACA;QACA,KAAAqD,eAAA,QAAAxE,IAAA,CAAAkB,WAAA,GACA,CACA;UACA7B,IAAA,OAAAW,IAAA,CAAAyE,eAAA;UACAd,GAAA,OAAA3D,IAAA,CAAAkB;QACA,EACA,GACA;MACA;QACA,KAAAxB,QAAA;QACA,KAAAO,IAAA;QACA,KAAA0C,SAAA;MACA;IACA;IACA+B,iBAAA,WAAAA,kBAAAlB,IAAA;MACAC,MAAA,CAAAC,IAAA,CAAAF,IAAA;IACA;IACAmB,UAAA,WAAAA,WAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,8BAAA,MAAAC,cAAA,CAAA9F,OAAA,MAAA8F,cAAA,CAAA9F,OAAA,MAAAyF,MAAA,CAAA5E,IAAA;YAAAkF,QAAA,EAAAP;UAAA,IAAAhD,IAAA,CACA,UAAAC,QAAA;YACAgD,MAAA,CAAAO,MAAA,CAAAC,UAAA;YACAR,MAAA,CAAAP,UAAA;UACA,CACA;QACA;MACA;IACA;IACAgB,uBAAA,WAAAA,wBAAA9B,IAAA,EAAAM,QAAA;MACA,KAAA7D,IAAA,CAAAiB,WAAA;IACA;IACAqE,wBAAA,WAAAA,yBAAA9D,GAAA,EAAA+B,IAAA,EAAAM,QAAA;MACA;MACA,IAAArC,GAAA,CAAA+D,IAAA;QACA,KAAAvF,IAAA,CAAAiB,WAAA,GAAAO,GAAA,CAAAhC,IAAA,CAAAkE,GAAA;QACA,KAAA1D,IAAA,CAAAwE,eAAA,GAAAhD,GAAA,CAAAhC,IAAA,CAAAJ,IAAA;MACA;IACA;IACAoG,mBAAA,WAAAA,oBAAAjC,IAAA,EAAAM,QAAA;MACA,KAAA7D,IAAA,CAAAkB,cAAA;IACA;IACAuE,oBAAA,WAAAA,qBAAAjE,GAAA,EAAA+B,IAAA,EAAAM,QAAA;MACA;MACA,IAAArC,GAAA,CAAA+D,IAAA;QACA,KAAAvF,IAAA,CAAAkB,cAAA,GAAAM,GAAA,CAAAhC,IAAA,CAAAkE,GAAA;QACA,KAAA1D,IAAA,CAAAsE,kBAAA,GAAA9C,GAAA,CAAAhC,IAAA,CAAAJ,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}