{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\talentJoinNow\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\talentJoinNow\\index.vue", "mtime": 1750311963088}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsgbGlzdERhdGEgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGljdC9kYXRhIjsNCmltcG9ydCB7IHRhbGVudEFkZCwgdGFsZW50RGV0YWlsRGF0YSB9IGZyb20gIkAvYXBpL3NlcnZpY2VTaGFyaW5nIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICAgIGNvbXBvbmVudHM6IHsgVXNlck1lbnUgfSwNCiAgICBkYXRhKCkgew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgaWQ6ICIiLA0KICAgICAgICAgICAgZm9ybTogew0KICAgICAgICAgICAgICAgIG5hbWU6ICIiLA0KICAgICAgICAgICAgICAgIGNvbnRhY3RQaG9uZTogIiIsDQogICAgICAgICAgICAgICAgYmlydGhEYXRlOiAiIiwNCiAgICAgICAgICAgICAgICBsb2NhdGlvbjogIiIsDQogICAgICAgICAgICAgICAgZ3JhZHVhdGVTY2hvb2w6ICIiLA0KICAgICAgICAgICAgICAgIGVkdWNhdGlvbjogIiIsDQogICAgICAgICAgICAgICAgY3VycmVudENvbXBhbnk6ICIiLA0KICAgICAgICAgICAgICAgIHdvcmtTdGF0dXM6ICIiLA0KICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAiIiwNCiAgICAgICAgICAgICAgICBqb2JUaXRsZTogIiIsDQogICAgICAgICAgICAgICAgc2tpbGxzOiAiIiwNCiAgICAgICAgICAgICAgICBwb3NpdGlvblR5cGU6ICIiLCAvLyDlspfkvY3liIbnsbsNCiAgICAgICAgICAgICAgICB3b3JrRXhwZXJpZW5jZTogIiIsIC8vIOS4quS6uueugOS7iw0KICAgICAgICAgICAgICAgIHBob3RvOiAiIiwgLy8g54Wn54mHDQogICAgICAgICAgICAgICAgcmVzdW1lRmlsZTogIiIsIC8vIOeugOWOhumZhOS7tg0KICAgICAgICAgICAgICAgIHNldHRsZWRTdGF0dXM6ICIwIiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBydWxlczogew0KICAgICAgICAgICAgICAgIG5hbWU6IFt7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5aeT5ZCN5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH1dLA0KICAgICAgICAgICAgICAgIHBvc2l0aW9uVHlwZTogWw0KICAgICAgICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5bKX5L2N5YiG57G75LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfSwNCiAgICAgICAgICAgICAgICBdLA0KICAgICAgICAgICAgICAgIGVkdWNhdGlvbjogWw0KICAgICAgICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5pyA6auY5a2m5Y6G5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgICAgICBqb2JUaXRsZTogWw0KICAgICAgICAgICAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6IGM56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgICAgICAgICAgXSwNCiAgICAgICAgICAgICAgICB3b3JrU3RhdHVzOiBbDQogICAgICAgICAgICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlt6XkvZznirbmgIHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiY2hhbmdlIiB9LA0KICAgICAgICAgICAgICAgIF0sDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgcG9zaXRpb25UeXBlTGlzdDogW10sIC8vIOWyl+S9jeWIhuexuw0KICAgICAgICAgICAgZWR1Y2F0aW9uTGlzdDogW10sIC8vIOacgOmrmOWtpuWOhg0KICAgICAgICAgICAgam9iVGl0bGVMaXN0OiBbXSwgLy8g6IGM56ewDQogICAgICAgICAgICB3b3JrU3RhdHVzTGlzdDogW10sIC8vIOW3peS9nOeKtuaAgQ0KICAgICAgICAgICAgcGhvdG9MaXN0OiBbXSwNCiAgICAgICAgfTsNCiAgICB9LA0KICAgIGNyZWF0ZWQoKSB7DQogICAgICAgIHRoaXMuZ2V0UG9zaXRpb25UeXBlKCk7DQogICAgICAgIHRoaXMuZ2V0RWR1Y2F0aW9uKCk7DQogICAgICAgIHRoaXMuZ2V0Sm9iVGl0bGUoKTsNCiAgICAgICAgdGhpcy5nZXRXb3JrU3RhdHVzKCk7DQogICAgICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5pZCkgew0KICAgICAgICAgICAgdGhpcy5pZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkOw0KICAgICAgICAgICAgdGhpcy5nZXRUYWxlbnRJbmZvKCk7DQogICAgICAgIH0NCiAgICB9LA0KICAgIG1ldGhvZHM6IHsNCiAgICAgICAgZ2V0VGFsZW50SW5mbygpIHsNCiAgICAgICAgICAgIHRhbGVudERldGFpbERhdGEodGhpcy5pZCkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5mb3JtID0gcmVzLmRhdGE7DQogICAgICAgICAgICAgICAgdGhpcy5waG90b0xpc3QgPSByZXMuZGF0YS5waG90byA/IFtyZXMuZGF0YS5waG90b10gOiBbXTsNCiAgICAgICAgICAgIH0pDQogICAgICAgIH0sDQogICAgICAgIC8vIOWyl+S9jeWIhuexuw0KICAgICAgICBnZXRQb3NpdGlvblR5cGUoKSB7DQogICAgICAgICAgICBsZXQgcGFyYW1zID0geyBkaWN0VHlwZTogInBvc2l0aW9uX3R5cGUiIH07DQogICAgICAgICAgICBsaXN0RGF0YShwYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5wb3NpdGlvblR5cGVMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvLyDmnIDpq5jlrabljoYNCiAgICAgICAgZ2V0RWR1Y2F0aW9uKCkgew0KICAgICAgICAgICAgbGV0IHBhcmFtcyA9IHsgZGljdFR5cGU6ICJlZHVjYXRpb24iIH07DQogICAgICAgICAgICBsaXN0RGF0YShwYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgICAgdGhpcy5lZHVjYXRpb25MaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvLyDogYznp7ANCiAgICAgICAgZ2V0Sm9iVGl0bGUoKSB7DQogICAgICAgICAgICBsZXQgcGFyYW1zID0geyBkaWN0VHlwZTogImpvYl90aXRsZSIgfTsNCiAgICAgICAgICAgIGxpc3REYXRhKHBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLmpvYlRpdGxlTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g5bel5L2c54q25oCBDQogICAgICAgIGdldFdvcmtTdGF0dXMoKSB7DQogICAgICAgICAgICBsZXQgcGFyYW1zID0geyBkaWN0VHlwZTogIndvcmtfc3RhdHVzIiB9Ow0KICAgICAgICAgICAgbGlzdERhdGEocGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMud29ya1N0YXR1c0xpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIG9uU3VibWl0KCkgew0KICAgICAgICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5waG90byA9IHRoaXMucGhvdG9MaXN0WzBdPy51cmwgPyB0aGlzLnBob3RvTGlzdFswXT8udXJsIDogIiI7DQogICAgICAgICAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgICAgICAgICAgIHRhbGVudEFkZCh0aGlzLmZvcm0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMub25DYW5jZWwoKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIG9uQ2FuY2VsKCkgew0KICAgICAgICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsNCiAgICAgICAgfSwNCiAgICB9LA0KfTsNCg0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqHA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/talentJoinNow", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-row :gutter=\"20\">\r\n            <el-col :span=\"2.5\" :xs=\"24\">\r\n                <user-menu activeIndex=\"1\" />\r\n            </el-col>\r\n            <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n                <div class=\"main-content\">\r\n                    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n                        <el-row :gutter=\"50\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"姓名\" prop=\"name\">\r\n                                    <el-input v-model=\"form.name\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系电话\" prop=\"contactPhone\">\r\n                                    <el-input v-model=\"form.contactPhone\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"出生年月\">\r\n                                    <el-date-picker v-model=\"form.birthDate\" type=\"date\" placeholder=\"选择日期\"\r\n                                        value-format=\"yyyy-MM-dd\" style=\"width: 100%\" />\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"所在地\">\r\n                                    <el-input v-model=\"form.location\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"毕业院校\">\r\n                                    <el-input v-model=\"form.graduateSchool\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"最高学历\" prop=\"education\">\r\n                                    <el-select v-model=\"form.education\" placeholder=\"请选择最高学历\" clearable\r\n                                        style=\"width: 100%\">\r\n                                        <el-option v-for=\"dict in educationList\" :key=\"dict.dictLabel\"\r\n                                            :label=\"dict.dictLabel\" :value=\"dict.dictValue\" />\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"所在单位\">\r\n                                    <el-input v-model=\"form.currentCompany\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"工作状态\" prop=\"workStatus\">\r\n                                    <el-radio-group v-model=\"form.workStatus\">\r\n                                        <el-radio v-for=\"dict in workStatusList\" :key=\"dict.dictValue\"\r\n                                            :label=\"dict.dictValue\" :value=\"dict.dictValue\">{{ dict.dictLabel\r\n                                            }}</el-radio>\r\n                                    </el-radio-group>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"职务\">\r\n                                    <el-input v-model=\"form.position\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"职称\" prop=\"jobTitle\">\r\n                                    <el-select v-model=\"form.jobTitle\" placeholder=\"请选择职称\" clearable\r\n                                        style=\"width: 100%\">\r\n                                        <el-option v-for=\"dict in jobTitleList\" :key=\"dict.dictLabel\"\r\n                                            :label=\"dict.dictLabel\" :value=\"dict.dictValue\" />\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"技术领域\">\r\n                                    <el-input v-model=\"form.skills\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"岗位分类\" prop=\"positionType\">\r\n                                    <el-select v-model=\"form.positionType\" placeholder=\"请选择岗位分类\" clearable\r\n                                        style=\"width: 100%\">\r\n                                        <el-option v-for=\"dict in positionTypeList\" :key=\"dict.dictLabel\"\r\n                                            :label=\"dict.dictLabel\" :value=\"dict.dictValue\" />\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"24\">\r\n                                <el-form-item label=\"个人简介\">\r\n                                    <el-input v-model=\"form.workExperience\" type=\"textarea\" resize=\"none\" :rows=\"8\"\r\n                                        maxlength=\"500\" show-word-limit placeholder=\"请输入\" />\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"个人照片\">\r\n                                    <ImageUpload v-model=\"photoList\" :limit=\"1\"/>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"附件\">\r\n                                    <FileUpload v-model=\"form.resumeFile\" />\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"24\">\r\n                                <el-form-item class=\"footer-submit\">\r\n                                    <el-button type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n                                    <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                    </el-form>\r\n                </div>\r\n            </el-col>\r\n        </el-row>\r\n    </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { talentAdd, talentDetailData } from \"@/api/serviceSharing\";\r\n\r\nexport default {\r\n    components: { UserMenu },\r\n    data() {\r\n        return {\r\n            id: \"\",\r\n            form: {\r\n                name: \"\",\r\n                contactPhone: \"\",\r\n                birthDate: \"\",\r\n                location: \"\",\r\n                graduateSchool: \"\",\r\n                education: \"\",\r\n                currentCompany: \"\",\r\n                workStatus: \"\",\r\n                position: \"\",\r\n                jobTitle: \"\",\r\n                skills: \"\",\r\n                positionType: \"\", // 岗位分类\r\n                workExperience: \"\", // 个人简介\r\n                photo: \"\", // 照片\r\n                resumeFile: \"\", // 简历附件\r\n                settledStatus: \"0\",\r\n            },\r\n            rules: {\r\n                name: [{ required: true, message: \"姓名不能为空\", trigger: \"blur\" }],\r\n                positionType: [\r\n                    { required: true, message: \"岗位分类不能为空\", trigger: \"change\" },\r\n                ],\r\n                education: [\r\n                    { required: true, message: \"最高学历不能为空\", trigger: \"blur\" },\r\n                ],\r\n                jobTitle: [\r\n                    { required: true, message: \"职称不能为空\", trigger: \"blur\" },\r\n                ],\r\n                workStatus: [\r\n                    { required: true, message: \"工作状态不能为空\", trigger: \"change\" },\r\n                ],\r\n            },\r\n            positionTypeList: [], // 岗位分类\r\n            educationList: [], // 最高学历\r\n            jobTitleList: [], // 职称\r\n            workStatusList: [], // 工作状态\r\n            photoList: [],\r\n        };\r\n    },\r\n    created() {\r\n        this.getPositionType();\r\n        this.getEducation();\r\n        this.getJobTitle();\r\n        this.getWorkStatus();\r\n        if (this.$route.query.id) {\r\n            this.id = this.$route.query.id;\r\n            this.getTalentInfo();\r\n        }\r\n    },\r\n    methods: {\r\n        getTalentInfo() {\r\n            talentDetailData(this.id).then((res) => {\r\n                this.form = res.data;\r\n                this.photoList = res.data.photo ? [res.data.photo] : [];\r\n            })\r\n        },\r\n        // 岗位分类\r\n        getPositionType() {\r\n            let params = { dictType: \"position_type\" };\r\n            listData(params).then((response) => {\r\n                this.positionTypeList = response.rows;\r\n            });\r\n        },\r\n        // 最高学历\r\n        getEducation() {\r\n            let params = { dictType: \"education\" };\r\n            listData(params).then((response) => {\r\n                this.educationList = response.rows;\r\n            });\r\n        },\r\n        // 职称\r\n        getJobTitle() {\r\n            let params = { dictType: \"job_title\" };\r\n            listData(params).then((response) => {\r\n                this.jobTitleList = response.rows;\r\n            });\r\n        },\r\n        // 工作状态\r\n        getWorkStatus() {\r\n            let params = { dictType: \"work_status\" };\r\n            listData(params).then((response) => {\r\n                this.workStatusList = response.rows;\r\n            });\r\n        },\r\n        onSubmit() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                this.form.photo = this.photoList[0]?.url ? this.photoList[0]?.url : \"\";\r\n                if (valid) {\r\n                    talentAdd(this.form).then((res) => {\r\n                        if (res.code === 200) {\r\n                            this.$message.success(\"操作成功\");\r\n                            this.onCancel();\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        },\r\n        onCancel() {\r\n            this.$router.go(-1);\r\n        },\r\n    },\r\n};\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n    background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n}\r\n\r\n.main-content {\r\n    background-color: #fff;\r\n    padding: 20px;\r\n    padding-bottom: 100px;\r\n\r\n    .btn-box {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin-top: 40px;\r\n\r\n        .btn {\r\n            width: 200px;\r\n            height: 50px;\r\n        }\r\n    }\r\n\r\n    .card {\r\n        margin-top: 170px;\r\n        padding: 10px;\r\n        box-sizing: border-box;\r\n        width: 280px;\r\n        height: 240px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        align-items: center;\r\n        background: url(\"../../../../assets/userCenter/card_bg.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n\r\n\r\n        .card-title {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            margin-bottom: 10px;\r\n            color: #030A1A;\r\n        }\r\n\r\n        .card-content {\r\n            font-size: 14px;\r\n            color: #666666;\r\n            line-height: 30px;\r\n        }\r\n\r\n        .success {\r\n            color: #21C9B8;\r\n        }\r\n\r\n        .btn-card {\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            margin-top: 0px;\r\n\r\n            .btn {\r\n                width: 200px;\r\n                height: 50px;\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n\r\n\r\n.content {\r\n    width: 100%;\r\n    padding-bottom: 60px;\r\n    background-color: #f2f2f2;\r\n}\r\n\r\n.content_banner {\r\n    width: 100%;\r\n    height: 300px;\r\n    background-image: url(\"../../../../assets/release/banner.png\");\r\n    background-size: 100% 100%;\r\n    text-align: center;\r\n    margin: 0 auto;\r\n    padding-top: 28px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    font-size: 40px;\r\n    color: #000;\r\n\r\n    .imgContent {\r\n        width: 100%;\r\n        display: flex;\r\n        justify-content: center;\r\n        margin-top: 10px;\r\n\r\n        .imgStyle {\r\n            width: 1256px;\r\n            height: 206px;\r\n            position: relative;\r\n        }\r\n    }\r\n}\r\n\r\n.content_card {\r\n    // height: 1530px;\r\n    background: #ffffff;\r\n    border-radius: 2px;\r\n    margin-top: 30px;\r\n    padding: 59px 60px 57px 60px;\r\n}\r\n\r\n.addStyle {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #21c9b8;\r\n    margin-left: auto;\r\n    cursor: pointer;\r\n}\r\n\r\n.footer-submit {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-top: 60px;\r\n\r\n    .el-button {\r\n        width: 140px;\r\n        height: 50px;\r\n    }\r\n}\r\n</style>"]}]}