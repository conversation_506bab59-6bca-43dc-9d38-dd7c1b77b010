{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\demandHall\\detail.vue?vue&type=style&index=0&id=287bf048&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\demandHall\\detail.vue", "mtime": 1750311962984}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwPA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/purchaseSales/component/demandHall", "sourcesContent": ["<template>\r\n  <div class=\"demand-hall-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"demand-hall-detail-banner\">\r\n      <img\r\n        src=\"../../../../assets/demandHall/demandHallDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"demand-hall-detail-title-box\">\r\n      <div class=\"demand-hall-detail-divider\"></div>\r\n      <div class=\"demand-hall-detail-title\">需求详情</div>\r\n      <div class=\"demand-hall-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"demand-hall-detail-content\">\r\n      <div class=\"demand-hall-detail-box\">\r\n        <div class=\"demand-hall-detail-box-title\">\r\n          {{ data.demandTitle }}\r\n        </div>\r\n        <div class=\"demand-hall-detail-headline\">\r\n          <div class=\"headline-content\">\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">应用领域：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.applicationArea }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">需求方：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.companyName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">联系人：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.contactsName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">联系方式：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.contactsMobile }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">发布时间：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.createTimeString }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-btn\">\r\n              <el-button\r\n                v-if=\"showBtn\"\r\n                class=\"headline-btn-style intention-btn\"\r\n                @click=\"goIntention\"\r\n                >我有意向\r\n              </el-button>\r\n              <el-button\r\n                class=\"headline-btn-style communication-btn\"\r\n                @click=\"goChat\"\r\n                icon=\"el-icon-chat-dot-round\"\r\n                >在线沟通</el-button\r\n              >\r\n            </div>\r\n          </div>\r\n          <div class=\"headline-img\">\r\n            <img\r\n              v-if=\"data.scenePicture && data.scenePicture.length > 0\"\r\n              :src=\"data.scenePicture[0].url\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-else\r\n              src=\"../../../../assets/purchaseSales/demandDefault.png\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"demand-hall-detail-description\">\r\n          <div class=\"description-title-box\">\r\n            <div class=\"description-divider\"></div>\r\n            <div class=\"description-title\">需求描述</div>\r\n          </div>\r\n          <div class=\"description-content\">\r\n            <div v-html=\"data.summary\" class=\"description-text ql-editor\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getDemandDetail, getCheckSubmit } from \"@/api/purchaseSales\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { getCompanyInfoByLoginInfo } from \"@/api/apathy\";\r\nimport { mapGetters } from \"vuex\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n      showBtn: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      getDemandDetail(this.$route.query.id)\r\n        .then((res) => {\r\n          console.log(res);\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          console.log(res);\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          this.data.scenePicture = this.data.scenePicture\r\n            ? JSON.parse(this.data.scenePicture)\r\n            : [];\r\n          if (this.data.companyName && this.data.displayRestrictions === 2) {\r\n            if (this.data.companyName.length > 2) {\r\n              this.data.companyName =\r\n                this.data.companyName.substring(0, 2) +\r\n                \"****\" +\r\n                this.data.companyName.slice(6);\r\n            }\r\n          }\r\n          if (!this.token) {\r\n            this.showBtn = true;\r\n          } else {\r\n            this.getInfo();\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 判断此资源是不是自己发布的\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (this.data.createById === res.user.userId) {\r\n          this.showBtn = false;\r\n        } else {\r\n          this.showBtn = true;\r\n        }\r\n      });\r\n    },\r\n    goChat() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      let routeData = this.$router.resolve({\r\n        path: \"/user/im\",\r\n        query: {\r\n          userId: this.data.createImById,\r\n        },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳到我有意向页面\r\n    goIntention() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      // 是否加入企业\r\n      getCompanyInfoByLoginInfo()\r\n        .then((res) => {\r\n          if (res.data) {\r\n            // 是否对此资源提交过意向\r\n            getCheckSubmit({\r\n              id: this.$route.query.id,\r\n              resourceType: \"resource_demand\",\r\n            })\r\n              .then((res) => {\r\n                this.loading = false;\r\n                // true 提交过  false未提交过\r\n                if (res.data) {\r\n                  this.$message({\r\n                    type: \"warning\",\r\n                    message: \"已经提交过了哦！\",\r\n                  });\r\n                } else {\r\n                  let routeData = this.$router.resolve({\r\n                    path: \"/addIntention\",\r\n                    query: {\r\n                      id: this.$route.query.id,\r\n                      type: \"resource_demand\",\r\n                      title: this.data.demandTitle,\r\n                    },\r\n                  });\r\n                  window.open(routeData.href, \"_blank\");\r\n                }\r\n              })\r\n              .catch(() => {\r\n                this.loading = false;\r\n              });\r\n          } else {\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"warning\",\r\n              message: \"必须加入企业才可提交我有意向\",\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.demand-hall-detail-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n\r\n  .demand-hall-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .demand-hall-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .demand-hall-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .demand-hall-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .demand-hall-detail-content {\r\n    background: #f4f5f9;\r\n    padding-bottom: 70px;\r\n\r\n    .demand-hall-detail-box {\r\n      width: 1200px;\r\n      background: #fff;\r\n      margin: 0 auto;\r\n      padding: 60px 60px 192px;\r\n\r\n      .demand-hall-detail-box-title {\r\n        width: 100%;\r\n        font-size: 32px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      .demand-hall-detail-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-top: 40px;\r\n        padding-bottom: 40px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n\r\n        .headline-content {\r\n          flex: 1;\r\n\r\n          .headline-content-item {\r\n            display: flex;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            line-height: 32px;\r\n\r\n            .item-title {\r\n              width: 80px;\r\n              color: #666;\r\n            }\r\n\r\n            .item-content {\r\n              flex: 1;\r\n              max-width: 590px;\r\n              color: #333;\r\n              word-wrap: break-word;\r\n            }\r\n          }\r\n\r\n          .headline-content-btn {\r\n            padding-top: 48px;\r\n\r\n            .headline-btn-style {\r\n              width: 100px;\r\n              height: 32px;\r\n              border-radius: 4px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              padding: 8px 11px;\r\n            }\r\n\r\n            .intention-btn {\r\n              background: #21c9b8;\r\n              color: #fff;\r\n            }\r\n\r\n            .communication-btn {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n\r\n        .headline-img {\r\n          width: 400px;\r\n          height: 240px;\r\n          margin-left: 20px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .demand-hall-detail-description {\r\n      padding-top: 39px;\r\n\r\n      .description-title-box {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-bottom: 40px;\r\n\r\n        .description-divider {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n\r\n        .description-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n\r\n      .description-content {\r\n        width: 1072px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n        word-wrap: break-word;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.demand-hall-detail-container {\r\n  .description-content {\r\n    .description-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}