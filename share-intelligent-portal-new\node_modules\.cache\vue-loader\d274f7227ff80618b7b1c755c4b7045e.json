{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\demandForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\demandForm.vue", "mtime": 1750311962955}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBkZW1hbmRBZGQsIGtleXdvcmRMaXN0IH0gZnJvbSAiQC9hcGkvemhtIjsNCmltcG9ydCBjYWNoZSBmcm9tICJAL3BsdWdpbnMvY2FjaGUiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJkZW1hbmRGb3JtIiwNCiAgZGljdHM6IFsiZGVtYW5kX3R5cGUiLCAiZGlzcGxheV9yZXN0cmljdGlvbnMiLCAiYXBwbGljYXRpb25fYXJlYSJdLA0KICBkYXRhKCkgew0KICAgIGNvbnN0IHsgdXNlciB9ID0gdGhpcy4kc3RvcmUuc3RhdGU7DQogICAgcmV0dXJuIHsNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgZm9ybTogew0KICAgICAgICAvLyDpnIDmsYLmoIfpopgNCiAgICAgICAgZGVtYW5kVGl0bGU6IHVuZGVmaW5lZCwNCiAgICAgICAgLy8g6ZyA5rGC57G75Z6LDQogICAgICAgIGRlbWFuZFR5cGU6IFtdLA0KICAgICAgICAvLyDpnIDmsYLmj4/ov7ANCiAgICAgICAgc3VtbWFyeTogdW5kZWZpbmVkLA0KICAgICAgICAvLyDlhbPplK7or40NCiAgICAgICAga2V5d29yZHM6IFtdLA0KICAgICAgICAvLyDlupTnlKjpoobln58NCiAgICAgICAgYXBwbGljYXRpb25BcmVhOiBbXSwNCiAgICAgICAgLy8g5Zy65pmv5Zu+54mHDQogICAgICAgIHNjZW5lUGljdHVyZTogW10sDQogICAgICAgIC8vIOWxleekuumZkOWItg0KICAgICAgICBkaXNwbGF5UmVzdHJpY3Rpb25zOiB1bmRlZmluZWQsDQogICAgICAgIC8vIOWFrOWPuOWQjeensA0KICAgICAgICBjb21wYW55TmFtZTogdXNlci5jb21wYW55TmFtZSwNCiAgICAgICAgLy8g6IGU57O75Lq6DQogICAgICAgIGNvbnRhY3RzTmFtZTogdXNlci5uYW1lLA0KICAgICAgICAvLyDogZTns7vnlLXor50NCiAgICAgICAgY29udGFjdHNNb2JpbGU6IHVzZXIudGVsLA0KICAgICAgICBhdWRpdFN0YXR1czogIjEiLA0KICAgICAgICBkaXNwbGF5U3RhdHVzOiAiMSIsDQogICAgICAgIHB1Ymxpc2hlck5hbWU6IHVzZXIubmFtZSwNCiAgICAgICAgcHVibGlzaGVyTW9iaWxlOiB1c2VyLnRlbCwNCiAgICAgICAgYnVzaW5lc3NObzogdXNlci5idXNzaW5lc3NObywNCiAgICAgIH0sDQogICAgICBydWxlczogew0KICAgICAgICBkZW1hbmRUaXRsZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXpnIDmsYLmoIfpopgiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgZGVtYW5kVHlwZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6npnIDmsYLnsbvlnosiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgc3VtbWFyeTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXpnIDmsYLmj4/ov7AiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgYXBwbGljYXRpb25BcmVhOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeW6lOeUqOmihuWfnyIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBkaXNwbGF5UmVzdHJpY3Rpb25zOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeWxleekuumZkOWItiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBjb21wYW55TmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fnu7TmiqTlhazlj7jlkI3np7AiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29udGFjdHNOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+e7tOaKpOiBlOezu+S6uiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBjb250YWN0c01vYmlsZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fnu7TmiqTogZTns7vnlLXor50iLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgfTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGluaXQoKSB7DQogICAgICBjb25zdCBkYXRhID0gY2FjaGUubG9jYWwuZ2V0SlNPTigiZGVtYW5kX2RhdGEiKTsNCiAgICAgIGlmIChkYXRhKSB7DQogICAgICAgIHRoaXMuZm9ybSA9IGRhdGE7DQogICAgICB9DQogICAgfSwNCiAgICBvbkNhbmNlbCgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5iYWNrKCk7DQogICAgfSwNCiAgICBvblNhdmUoKSB7DQogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGNhY2hlLmxvY2FsLnNldEpTT04oImRlbWFuZF9kYXRhIiwgdGhpcy5mb3JtKTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaaguWtmOaIkOWKnyIpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIG9uU3VibWl0KHN0YXR1cykgew0KICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIGNvbnN0IHsNCiAgICAgICAgICAgIGRlbWFuZFR5cGUsDQogICAgICAgICAgICBrZXl3b3JkcywNCiAgICAgICAgICAgIGFwcGxpY2F0aW9uQXJlYSwNCiAgICAgICAgICAgIHNjZW5lUGljdHVyZSwNCiAgICAgICAgICAgIC4uLnJlc3QNCiAgICAgICAgICB9ID0gdGhpcy5mb3JtOw0KICAgICAgICAgIGNvbnN0IGRhdGEgPSB7DQogICAgICAgICAgICAuLi5yZXN0LA0KICAgICAgICAgICAgYXVkaXRTdGF0dXM6IHN0YXR1cywNCiAgICAgICAgICB9Ow0KICAgICAgICAgIGlmIChkZW1hbmRUeXBlLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIGRhdGFbImRlbWFuZFR5cGUiXSA9IGRlbWFuZFR5cGUuam9pbigpOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoa2V5d29yZHMubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgZGF0YVsia2V5d29yZHMiXSA9IGtleXdvcmRzLmpvaW4oKTsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGFwcGxpY2F0aW9uQXJlYS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBkYXRhWyJhcHBsaWNhdGlvbkFyZWEiXSA9IGFwcGxpY2F0aW9uQXJlYS5qb2luKCk7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChzY2VuZVBpY3R1cmUubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgZGF0YVsic2NlbmVQaWN0dXJlIl0gPSBKU09OLnN0cmluZ2lmeShzY2VuZVBpY3R1cmUpOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIGRlbWFuZEFkZChkYXRhKQ0KICAgICAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgICAgICBjb25zdCB7IGNvZGUsIG1zZyB9ID0gcmVzOw0KICAgICAgICAgICAgICBpZiAoY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICAgICAgY2FjaGUubG9jYWwucmVtb3ZlKCJkZW1hbmRfZGF0YSIpOw0KICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Y+R5biD5oiQ5YqfIik7DQogICAgICAgICAgICAgICAgdGhpcy4kcm91dGVyLmJhY2soKTsNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKG1zZyB8fCAi5Y+R5biD5aSx6LSlIik7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgICAuZmluYWxseSgoKSA9PiAodGhpcy5sb2FkaW5nID0gZmFsc2UpKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVLZXl3b3JkTGlzdCgpIHsNCiAgICAgIGNvbnN0IHsgc3VtbWFyeSB9ID0gdGhpcy5mb3JtOw0KICAgICAgaWYgKHN1bW1hcnkpIHsNCiAgICAgICAga2V5d29yZExpc3Qoc3VtbWFyeSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgY29uc3QgeyBjb2RlLCBkYXRhLCBtc2cgfSA9IHJlczsNCiAgICAgICAgICBpZiAoY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLmZvcm0ua2V5d29yZHMgPSBkYXRhOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKG1zZyk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36L6T5YWl6ZyA5rGC5o+P6L+wIik7DQogICAgICB9DQogICAgfSwNCg0KICAgIGhhbmRsZUNsb3NlKHRhZykgew0KICAgICAgY29uc29sZS5sb2coInRhZyIsIHRhZyk7DQogICAgICB0aGlzLmZvcm0ua2V5d29yZHMgPSB0aGlzLmZvcm0ua2V5d29yZHMuZmlsdGVyKChpdGVtKSA9PiBpdGVtICE9PSB0YWcpOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["demandForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiIA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "demandForm.vue", "sourceRoot": "src/views/form/components", "sourcesContent": ["<template>\r\n  <div class=\"demand-form\">\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item prop=\"demandTitle\" label=\"需求标题\">\r\n        <el-input\r\n          v-model=\"form.demandTitle\"\r\n          maxlength=\"50\"\r\n          show-word-limit\r\n          placeholder=\"请输入标签\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"demandType\">\r\n        <div class=\"label-item\" slot=\"label\">\r\n          <span>需求类型</span>\r\n          <span class=\"extra\">（可按需求产品+应用行业+应用领域进行描述）</span>\r\n        </div>\r\n        <el-checkbox-group\r\n          v-model=\"form.demandType\"\r\n          placeholder=\"请选择\"\r\n          clearable\r\n        >\r\n          <el-checkbox\r\n            v-for=\"dict in dict.type.demand_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.value\"\r\n            :value=\"dict.value\"\r\n            >{{ dict.label }}</el-checkbox\r\n          >\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <el-form-item prop=\"summary\" label=\"需求描述\">\r\n        <el-input\r\n          type=\"textarea\"\r\n          v-model=\"form.summary\"\r\n          maxlength=\"500\"\r\n          rows=\"6\"\r\n          show-word-limit\r\n          placeholder=\"请输入需求描述\"\r\n        ></el-input>\r\n        <div class=\"extra-content\">\r\n          <div class=\"extra-content-header\">\r\n            <el-button @click=\"handleKeywordList\" size=\"small\" type=\"primary\"\r\n              >生成关键词</el-button\r\n            >\r\n            <span class=\"tip\">生成关键词有利于实现精准匹配哦！</span>\r\n          </div>\r\n          <div v-if=\"form.keywords.length > 0\" class=\"extra-content-body\">\r\n            <el-tag\r\n              :key=\"`${tag}_${index}`\"\r\n              v-for=\"(tag, index) in form.keywords\"\r\n              closable\r\n              size=\"small\"\r\n              disable-transitions\r\n              @close=\"handleClose(tag)\"\r\n            >\r\n              {{ tag }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item prop=\"applicationArea\" label=\"应用领域\">\r\n        <el-select\r\n          v-model=\"form.applicationArea\"\r\n          filterable\r\n          multiple\r\n          allow-create\r\n          style=\"width: 100%\"\r\n          placeholder=\"请选择\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in dict.type.application_area\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.label\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品照片\" prop=\"scenePicture\">\r\n        <ImageUpload v-model=\"form.scenePicture\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"展示限制\" prop=\"displayRestrictions\">\r\n        <el-select\r\n          v-model=\"form.displayRestrictions\"\r\n          placeholder=\"请选择\"\r\n          style=\"width: 100%\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.display_restrictions\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.companyName\"\r\n          placeholder=\"请先绑定公司\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系人\" prop=\"contactsName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactsName\"\r\n          placeholder=\"请先维护联系人\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系方式\" prop=\"contactsMobile\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactsMobile\"\r\n          placeholder=\"请先维护联系方式\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button @click.once=\"onCancel\">取消</el-button>\r\n        <el-button @click=\"onSubmit('0')\" type=\"primary\" plain\r\n          >暂存草稿</el-button\r\n        >\r\n        <el-button type=\"primary\" @click=\"onSubmit('1')\">发布</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { demandAdd, keywordList } from \"@/api/zhm\";\r\nimport cache from \"@/plugins/cache\";\r\n\r\nexport default {\r\n  name: \"demandForm\",\r\n  dicts: [\"demand_type\", \"display_restrictions\", \"application_area\"],\r\n  data() {\r\n    const { user } = this.$store.state;\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        // 需求标题\r\n        demandTitle: undefined,\r\n        // 需求类型\r\n        demandType: [],\r\n        // 需求描述\r\n        summary: undefined,\r\n        // 关键词\r\n        keywords: [],\r\n        // 应用领域\r\n        applicationArea: [],\r\n        // 场景图片\r\n        scenePicture: [],\r\n        // 展示限制\r\n        displayRestrictions: undefined,\r\n        // 公司名称\r\n        companyName: user.companyName,\r\n        // 联系人\r\n        contactsName: user.name,\r\n        // 联系电话\r\n        contactsMobile: user.tel,\r\n        auditStatus: \"1\",\r\n        displayStatus: \"1\",\r\n        publisherName: user.name,\r\n        publisherMobile: user.tel,\r\n        businessNo: user.bussinessNo,\r\n      },\r\n      rules: {\r\n        demandTitle: [\r\n          { required: true, message: \"请输入需求标题\", trigger: \"blur\" },\r\n        ],\r\n        demandType: [\r\n          { required: true, message: \"请选择需求类型\", trigger: \"blur\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"请输入需求描述\", trigger: \"blur\" },\r\n        ],\r\n        applicationArea: [\r\n          { required: true, message: \"请选择应用领域\", trigger: \"blur\" },\r\n        ],\r\n        displayRestrictions: [\r\n          { required: true, message: \"请选择展示限制\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"请维护公司名称\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"请维护联系人\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"请维护联系电话\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      const data = cache.local.getJSON(\"demand_data\");\r\n      if (data) {\r\n        this.form = data;\r\n      }\r\n    },\r\n    onCancel() {\r\n      this.$router.back();\r\n    },\r\n    onSave() {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          cache.local.setJSON(\"demand_data\", this.form);\r\n          this.$message.success(\"暂存成功\");\r\n        }\r\n      });\r\n    },\r\n    onSubmit(status) {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          const {\r\n            demandType,\r\n            keywords,\r\n            applicationArea,\r\n            scenePicture,\r\n            ...rest\r\n          } = this.form;\r\n          const data = {\r\n            ...rest,\r\n            auditStatus: status,\r\n          };\r\n          if (demandType.length > 0) {\r\n            data[\"demandType\"] = demandType.join();\r\n          }\r\n          if (keywords.length > 0) {\r\n            data[\"keywords\"] = keywords.join();\r\n          }\r\n          if (applicationArea.length > 0) {\r\n            data[\"applicationArea\"] = applicationArea.join();\r\n          }\r\n          if (scenePicture.length > 0) {\r\n            data[\"scenePicture\"] = JSON.stringify(scenePicture);\r\n          }\r\n\r\n          demandAdd(data)\r\n            .then((res) => {\r\n              const { code, msg } = res;\r\n              if (code === 200) {\r\n                cache.local.remove(\"demand_data\");\r\n                this.$message.success(\"发布成功\");\r\n                this.$router.back();\r\n              } else {\r\n                this.$message.error(msg || \"发布失败\");\r\n              }\r\n            })\r\n            .finally(() => (this.loading = false));\r\n        }\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      const { summary } = this.form;\r\n      if (summary) {\r\n        keywordList(summary).then((res) => {\r\n          const { code, data, msg } = res;\r\n          if (code === 200) {\r\n            this.form.keywords = data;\r\n          } else {\r\n            this.$message.error(msg);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning(\"请输入需求描述\");\r\n      }\r\n    },\r\n\r\n    handleClose(tag) {\r\n      console.log(\"tag\", tag);\r\n      this.form.keywords = this.form.keywords.filter((item) => item !== tag);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.demand-form {\r\n  width: 676px;\r\n  .label-item {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    .title {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #333333;\r\n      line-height: 18px;\r\n    }\r\n    .extra {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .extra-content {\r\n    padding: 12px 0;\r\n    &-header {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      .tip {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n        line-height: 14px;\r\n        margin-left: 12px;\r\n      }\r\n    }\r\n    &-body {\r\n      padding-top: 6px;\r\n      .el-tag {\r\n        margin-right: 12px;\r\n        &:last-child {\r\n          margin-right: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  ::v-deep.el-form-item__label {\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    line-height: 18px;\r\n    margin-bottom: 12px;\r\n    padding: 0;\r\n  }\r\n  .el-checkbox {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #262626;\r\n    line-height: 18px;\r\n    margin-right: 28px;\r\n  }\r\n  .footer-submit {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-top: 40px;\r\n    .el-button {\r\n      width: 160px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}