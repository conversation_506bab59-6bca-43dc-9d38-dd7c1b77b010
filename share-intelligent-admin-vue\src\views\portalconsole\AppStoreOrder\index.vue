<template>
  <!-- 企业管理订单 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单编号" prop="appStoreOrderNo">
        <el-input
          v-model="queryParams.appStoreOrderNo"
          placeholder="请输入订单编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="销售公司ID" prop="saleCompanyId">
        <el-input
          v-model="queryParams.saleCompanyId"
          placeholder="请输入销售公司ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="购买公司ID" prop="buyCompanyId">
        <el-input
          v-model="queryParams.buyCompanyId"
          placeholder="请输入购买公司ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="下单时间" prop="orderTime">
        <!-- <el-input
          v-model="queryParams.orderTime"
          placeholder="请输入下单时间"
          clearable
          @keyup.enter.native="handleQuery"
        /> -->
        <el-date-picker
          v-model="queryParams.orderTime"
          type="date"
          format="yyyy 年 MM 月 dd 日"
          value-format="yyyy-MM-dd"
          placeholder="选择下单时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="价格" prop="appStorePrice">
        <el-input
          v-model="queryParams.appStorePrice"
          placeholder="请输入价格"
          type="number"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:AppStoreOrder:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:AppStoreOrder:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:AppStoreOrder:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:AppStoreOrder:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <el-table v-loading="loading" :data="AppStoreOrderList" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <!-- <el-table-column label="应用商店订单ID" align="center" prop="appStoreOrderId" /> -->
      <el-table-column label="订单编号" align="center" prop="appStoreOrderNo" />
      <!-- <el-table-column label="销售公司ID" align="center" prop="saleCompanyId" /> -->
      <!-- <el-table-column label="购买公司ID" align="center" prop="buyCompanyId" /> -->
      <el-table-column label="销售公司" align="center" prop="buyCompanyName" />
      <el-table-column label="购买公司" align="center" prop="saleCompanyName" />
      <el-table-column label="下单时间" align="center" prop="orderTime" />
      <el-table-column label="价格" align="center" prop="appStorePrice" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDetail(scope.row)"
            v-hasPermi="['portalconsole:AppStoreOrder:edit']"
          >详情</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:AppStoreOrder:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:AppStoreOrder:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改应用商店订单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="订单编号" prop="appStoreOrderNo">
          <el-input v-model="form.appStoreOrderNo" placeholder="请输入订单编号" />
        </el-form-item>
        <el-form-item label="销售公司ID" prop="saleCompanyId">
          <el-input v-model="form.saleCompanyId" placeholder="请输入销售公司ID" />
        </el-form-item>
        <el-form-item label="购买公司ID" prop="buyCompanyId">
          <el-input v-model="form.buyCompanyId" placeholder="请输入购买公司ID" />
        </el-form-item>
        <el-form-item label="下单时间" prop="orderTime">
          <el-input v-model="form.orderTime" placeholder="请输入下单时间" />
        </el-form-item>
        <el-form-item label="价格" prop="appStorePrice">
          <el-input v-model="form.appStorePrice" placeholder="请输入价格" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <detailDialog ref="detailDialog"></detailDialog>
  </div>
</template>

<script>
import { listAppStoreOrder, getAppStoreOrder, delAppStoreOrder, addAppStoreOrder, updateAppStoreOrder } from "@/api/portalconsole/AppStoreOrder";
import detailDialog from'./components/detailDialog.vue'
import {formatDate} from '@/utils/time'
export default {
  name: "AppStoreOrder",
  components:{
    detailDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 应用商店订单表格数据
      AppStoreOrderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        appStoreOrderNo: null,
        // saleCompanyId: null,
        // buyCompanyId: null,
        orderTime: null,
        appStorePrice: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询应用商店订单列表 */
    getList() {
      this.loading = true;
      listAppStoreOrder(this.queryParams).then(response => {
        this.AppStoreOrderList = response.rows;
        this.AppStoreOrderList.forEach(item=>{
          item.orderTime=formatDate(item.orderTime)
        })
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        appStoreOrderId: null,
        appStoreOrderNo: null,
        saleCompanyId: null,
        buyCompanyId: null,
        orderTime: null,
        appStorePrice: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      console.log("111",this.queryParams)
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.appStoreOrderId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加应用商店订单";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const appStoreOrderId = row.appStoreOrderId || this.ids
      getAppStoreOrder(appStoreOrderId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改应用商店订单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.appStoreOrderId != null) {
            updateAppStoreOrder(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAppStoreOrder(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const appStoreOrderIds = row.appStoreOrderId || this.ids;
      this.$modal.confirm('是否确认删除应用商店订单编号为"' + appStoreOrderIds + '"的数据项？').then(function() {
        return delAppStoreOrder(appStoreOrderIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/AppStoreOrder/export', {
        ...this.queryParams
      }, `AppStoreOrder_${new Date().getTime()}.xlsx`)
    },
    //详情
    handleDetail(row){
      getAppStoreOrder(row.appStoreOrderId).then(response => {
        const list = response.data;
        this.$refs.detailDialog.show(list)
      });
      
    }
  }
};
</script>
