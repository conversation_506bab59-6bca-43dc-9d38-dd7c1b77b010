package com.ruoyi.sso.service;

import java.util.Map;

/**
 * 用户映射服务接口
 * 负责将SSO用户信息映射到各个系统的用户权限
 * 
 * <AUTHOR>
 */
public interface UserMappingService {
    
    /**
     * 根据SSO用户信息获取用户在指定系统中的权限信息
     * 
     * @param ssoUsername SSO用户名
     * @param clientId 客户端系统ID
     * @return 用户在该系统中的权限信息
     */
    Map<String, Object> getUserPermissions(String ssoUsername, String clientId);
    
    /**
     * 创建用户映射关系
     * 
     * @param ssoUsername SSO用户名
     * @param clientId 客户端系统ID
     * @param systemUserId 系统内部用户ID
     * @param permissions 权限信息
     * @return 是否创建成功
     */
    boolean createUserMapping(String ssoUsername, String clientId, String systemUserId, Map<String, Object> permissions);
    
    /**
     * 更新用户映射关系
     * 
     * @param ssoUsername SSO用户名
     * @param clientId 客户端系统ID
     * @param permissions 新的权限信息
     * @return 是否更新成功
     */
    boolean updateUserMapping(String ssoUsername, String clientId, Map<String, Object> permissions);
    
    /**
     * 删除用户映射关系
     * 
     * @param ssoUsername SSO用户名
     * @param clientId 客户端系统ID
     * @return 是否删除成功
     */
    boolean deleteUserMapping(String ssoUsername, String clientId);
    
    /**
     * 检查用户在指定系统中是否有权限
     * 
     * @param ssoUsername SSO用户名
     * @param clientId 客户端系统ID
     * @return 是否有权限
     */
    boolean hasPermission(String ssoUsername, String clientId);
    
    /**
     * 获取用户在指定系统中的角色列表
     * 
     * @param ssoUsername SSO用户名
     * @param clientId 客户端系统ID
     * @return 角色列表
     */
    String[] getUserRoles(String ssoUsername, String clientId);
    
    /**
     * 获取用户在指定系统中的权限列表
     * 
     * @param ssoUsername SSO用户名
     * @param clientId 客户端系统ID
     * @return 权限列表
     */
    String[] getUserPermissionCodes(String ssoUsername, String clientId);
}
