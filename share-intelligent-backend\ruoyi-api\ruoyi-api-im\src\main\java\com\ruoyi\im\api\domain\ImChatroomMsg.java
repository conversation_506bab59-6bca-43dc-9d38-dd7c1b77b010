package com.ruoyi.im.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName(value = "im_chatroom_msg")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImChatroomMsg extends Model<ImChatroomMsg> {

        @TableId(value = "id", type = IdType.AUTO)
        private Long id;//自增

        @Excel(name = "聊天室ID")
        private String toUserId;//聊天室ID

        @Excel(name = "用户ID")
        private String fromUserId;//用户ID

        private String objectName;//消息类型

        @Excel(name = "聊天内容")
        private String message;//聊天内容

        private Integer sensitiveType;//敏感词

        @Excel(name = "发送时间")
        private String msgTimestamp;//创建时间
}
