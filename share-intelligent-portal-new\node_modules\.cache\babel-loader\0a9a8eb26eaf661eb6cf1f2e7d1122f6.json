{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\productOrderDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\productOrderDetail.vue", "mtime": 1750311962969}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_manufacturingSharing", "require", "name", "data", "detailsData", "created", "id", "$route", "query", "getDetailData", "methods", "_this", "orderDetailData", "then", "res", "code", "takeOrder", "$router", "push", "jumpIntention", "_this2", "userInfo", "JSON", "parse", "sessionStorage", "getItem", "memberCompanyName", "$confirm", "confirmButtonText", "cancelButtonText", "type", "cancelButtonClass", "confirmButtonClass", "catch", "concat", "demandCompany", "updateTime"], "sources": ["src/views/manufacturingSharing/components/productOrderDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"card-container cardStyle\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <div class=\"card_left_bottom\">\r\n          <div class=\"title\">{{ detailsData.demandCompany }}</div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">联系人：</div>\r\n            <div class=\"optionValue\">{{ detailsData.contactPerson }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">联系电话：</div>\r\n            <div class=\"optionValue\">{{ detailsData.contactPhone }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">交货地址：</div>\r\n            <div class=\"optionValue\">{{ detailsData.deliveryAddress }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">需求截至时间：</div>\r\n            <div class=\"optionValue\">{{ detailsData.deadline }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">订单状态：</div>\r\n            <div class=\"optionValue\">\r\n              {{\r\n                detailsData.status == \"0\"\r\n                  ? \"未接单\"\r\n                  : detailsData.status == \"1\"\r\n                    ? \"进行中\"\r\n                    : \"已完成\"\r\n              }}\r\n            </div>\r\n          </div>\r\n          <div class=\"buttonStyle\" @click=\"jumpIntention\">我要接单</div>\r\n        </div>\r\n      </div>\r\n      <!-- 中间 -->\r\n      <div class=\"card_center_line\"></div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">需求物料</div>\r\n          </div>\r\n          <div style=\"margin-top: 22px\">\r\n            <el-table :data=\"detailsData.materials\" style=\"margin-top: 20px\">\r\n              <el-table-column label=\"物料名称\" align=\"center\" prop=\"name\">\r\n              </el-table-column>\r\n              <el-table-column label=\"规格型号\" align=\"center\" prop=\"modelNumber\">\r\n              </el-table-column>\r\n              <el-table-column label=\"数量\" align=\"center\" prop=\"quantity\">\r\n              </el-table-column>\r\n              <el-table-column label=\"单位\" align=\"center\" prop=\"unit\">\r\n              </el-table-column>\r\n              <el-table-column label=\"可承接量\" align=\"center\" prop=\"capacity\">\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">交付要求</div>\r\n          </div>\r\n          <div style=\"margin-top: 22px\">\r\n            <el-descriptions class=\"margin-top\" title=\"\" :column=\"2\" border>\r\n              <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 压力 </template>\r\n60Mpa\r\n</el-descriptions-item>\r\n<el-descriptions-item>\r\n  <template slot=\"label\"> 温度 </template>\r\n  18100000000\r\n</el-descriptions-item>\r\n<el-descriptions-item>\r\n  <template slot=\"label\"> 尺寸 </template>\r\n  3.5m\r\n</el-descriptions-item> -->\r\n              <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 规格型号 </template>\r\n                T-565487\r\n              </el-descriptions-item> -->\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 拦标价 </template>\r\n                <!-- {{ detailsData.deliveryAddress }} -->\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 是否允许接单 </template>\r\n                是\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 交付时间 </template>\r\n                <!-- 2025-12-05 -->\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 需求截至时间： </template>\r\n                {{ detailsData.deadline }}\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">基本信息</div>\r\n          </div>\r\n          <div style=\"margin-top: 22px\">\r\n            <el-descriptions class=\"margin-top\" title=\"\" :column=\"2\" border>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 需求企业 </template>\r\n                {{ detailsData.demandCompany }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 联系人 </template>\r\n                {{ detailsData.contactPerson }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 联系电话 </template>\r\n                {{ detailsData.contactPhone }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 交货地址 </template>\r\n                {{ detailsData.deliveryAddress }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item :span=\"2\">\r\n                <template slot=\"label\"> 交货要求 </template>\r\n                {{ detailsData.fileRequirement }}\r\n              </el-descriptions-item>\r\n              <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 开户行 </template>\r\n                {{ detailsData.bankName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 付款账号 </template>\r\n                {{ detailsData.paymentAccount }}\r\n              </el-descriptions-item> -->\r\n            </el-descriptions>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { orderDetailData } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"deviceDetail\",\r\n  data() {\r\n    return {\r\n      detailsData: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getDetailData();\r\n  },\r\n  methods: {\r\n    getDetailData() {\r\n      orderDetailData(this.id).then((res) => {\r\n        if (res.code === 200) {\r\n          this.detailsData = res.data;\r\n        }\r\n      });\r\n    },\r\n    takeOrder(id) {\r\n      this.$router.push(\"/receiveOrder\"); // 传id\r\n    },\r\n    jumpIntention() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(`/receiveOrder?demandName=${this.detailsData.demandCompany}&updateTime=${this.detailsData.updateTime}&intentionType=12&fieldName=生成订单&intentionId=${this.detailsData.id}`);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: #f2f2f2;\r\n  padding: 30px 0 61px 0;\r\n}\r\n\r\n.cardStyle {\r\n  height: 100%;\r\n  // height: 660px;\r\n  background-color: #ffffff;\r\n  padding: 60px 56px 54px 50px;\r\n  display: flex;\r\n}\r\n\r\n.card_left {\r\n  .card_left_bottom {\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      font-size: 20px;\r\n      color: #222222;\r\n      margin-bottom: 25px;\r\n    }\r\n\r\n    .everyOption {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 12px;\r\n\r\n      .optionName {\r\n        // height: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #999999;\r\n      }\r\n\r\n      .optionValue {\r\n        // height: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #333333;\r\n      }\r\n    }\r\n\r\n    .buttonStyle {\r\n      margin-top: 32px;\r\n      // margin-left: 55px;\r\n      width: 220px;\r\n      height: 50px;\r\n      background: #21c9b8;\r\n      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);\r\n      border-radius: 2px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      text-align: center;\r\n      line-height: 50px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.card_center_line {\r\n  width: 1px;\r\n  height: 660px;\r\n  background: #e1e1e1;\r\n  margin-left: 60px;\r\n  margin-right: 61px;\r\n}\r\n\r\n.card_right {\r\n  width: 100%;\r\n\r\n  // overflow-y: auto;\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AAmJA,IAAAA,qBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,EAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,EAAA;IACA,KAAAG,aAAA;EACA;EACAC,OAAA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,qCAAA,OAAAN,EAAA,EAAAO,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAP,WAAA,GAAAU,GAAA,CAAAX,IAAA;QACA;MACA;IACA;IACAa,SAAA,WAAAA,UAAAV,EAAA;MACA,KAAAW,OAAA,CAAAC,IAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,cAAA,CAAAC,OAAA;MACA,MAAAJ,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAK,iBAAA;QACA,KAAAC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;UACAC,iBAAA;UACAC,kBAAA;QACA,GACAnB,IAAA;UACAO,MAAA,CAAAH,OAAA,CAAAC,IAAA;QACA,GACAe,KAAA;QACA;MACA;QACA,KAAAhB,OAAA,CAAAC,IAAA,6BAAAgB,MAAA,MAAA9B,WAAA,CAAA+B,aAAA,kBAAAD,MAAA,MAAA9B,WAAA,CAAAgC,UAAA,uEAAAF,MAAA,MAAA9B,WAAA,CAAAE,EAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}