<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="岗位名称" prop="positionName">
        <el-input
          v-model="queryParams.positionName"
          placeholder="请输入岗位名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="薪资范围" prop="salaryRange">
        <el-input
          v-model="queryParams.salaryRange"
          placeholder="请输入薪资范围"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最低薪资" prop="salaryMin">
        <el-input
          v-model="queryParams.salaryMin"
          placeholder="请输入最低薪资"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最高薪资" prop="salaryMax">
        <el-input
          v-model="queryParams.salaryMax"
          placeholder="请输入最高薪资"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="年龄限制" prop="ageLimit">
        <el-input
          v-model="queryParams.ageLimit"
          placeholder="请输入年龄限制"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用工单位" prop="company">
        <el-input
          v-model="queryParams.company"
          placeholder="请输入用工单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用工地点" prop="location">
        <el-input
          v-model="queryParams.location"
          placeholder="请输入用工地点"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系方式" prop="contactPhone">
        <el-input
          v-model="queryParams.contactPhone"
          placeholder="请输入联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:jobInfo:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:jobInfo:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:jobInfo:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:jobInfo:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="jobInfoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="岗位名称" align="center" prop="positionName" />
      <el-table-column label="薪资范围" align="center">
        <template slot-scope="scope">
          {{
            salaryRangeList.filter(
              (item) => item.dictValue == scope.row.salaryRange
            )[0].dictLabel
          }}
        </template>
      </el-table-column>
      <el-table-column label="最低薪资" align="center" prop="salaryMin" />
      <el-table-column label="最高薪资" align="center" prop="salaryMax" />
      <!-- <el-table-column label="年龄限制" align="center" prop="ageLimit" /> -->
      <el-table-column label="用工单位" align="center" prop="company" />
      <el-table-column label="用工地点" align="center">
        <template slot-scope="scope">
          {{
            locationList.filter(
              (item) => item.dictValue == scope.row.location
            )[0].dictLabel
          }}
        </template>
      </el-table-column>
      <el-table-column label="联系方式" align="center" prop="contactPhone" />
      <!-- <el-table-column label="岗位要求" align="center" prop="requirements" />
      <el-table-column label="岗位职责" align="center" prop="responsibilities" />
      <el-table-column label="其他限制" align="center" prop="otherLimits" /> -->
      <el-table-column label="状态" align="center" prop="status" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:jobInfo:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:jobInfo:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用工信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="岗位名称" prop="positionName">
          <el-input v-model="form.positionName" placeholder="请输入岗位名称" />
        </el-form-item>
          <el-form-item label="岗位分类" prop="jobCategory">
          <el-select
            v-model="form.jobCategory"
            placeholder="请选择岗位分类"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in dict.type.job_category"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="薪资范围" prop="salaryRange">
          <el-select
            v-model="form.salaryRange"
            placeholder="请选择薪资范围"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in salaryRangeList"
              :key="dict.dictLabel"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="最低薪资" prop="salaryMin">
          <el-input v-model="form.salaryMin" placeholder="请输入最低薪资" />
        </el-form-item>
        <el-form-item label="最高薪资" prop="salaryMax">
          <el-input v-model="form.salaryMax" placeholder="请输入最高薪资" />
        </el-form-item>
        <el-form-item label="年龄限制" prop="ageLimit">
          <el-input v-model="form.ageLimit" placeholder="请输入年龄限制" />
        </el-form-item>
        <el-form-item label="用工单位" prop="company">
          <el-input v-model="form.company" placeholder="请输入用工单位" />
        </el-form-item>
        <el-form-item label="用工地点" prop="location">
          <el-select
            v-model="form.location"
            placeholder="请选择薪资范围"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="dict in locationList"
              :key="dict.dictLabel"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="联系方式" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="岗位要求" prop="requirements">
          <el-input
            v-model="form.requirements"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="岗位职责" prop="responsibilities">
          <el-input
            v-model="form.responsibilities"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
        <el-form-item label="其他限制" prop="otherLimits">
          <el-input
            v-model="form.otherLimits"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listJobInfo,
  getJobInfo,
  delJobInfo,
  addJobInfo,
  updateJobInfo,
} from "@/api/system/jobInfo";
import { listData } from "@/api/system/dict/data";

export default {
  name: "JobInfo",
  dicts: ["job_category"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用工信息表格数据
      jobInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        positionName: null,
        salaryRange: null,
        salaryMin: null,
        salaryMax: null,
        ageLimit: null,
        company: null,
        location: null,
        contactPhone: null,
        requirements: null,
        responsibilities: null,
        otherLimits: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        positionName: [
          { required: true, message: "岗位名称不能为空", trigger: "blur" },
        ],
        salaryRange: [
          {
            required: true,
            message: "薪资范围不能为空",
            trigger: "blur",
          },
        ],
        salaryMin: [
          { required: true, message: "最低薪资不能为空", trigger: "blur" },
        ],
        salaryMax: [
          { required: true, message: "最高薪资不能为空", trigger: "blur" },
        ],
        company: [
          { required: true, message: "用工单位不能为空", trigger: "blur" },
        ],
        location: [
          { required: true, message: "用工地点不能为空", trigger: "blur" },
        ],
        contactPhone: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
        ],
      },
      salaryRangeList: [], // 薪资范围
      locationList: [], // 用工地点
    };
  },
  created() {
    this.getSalaryRange();
    this.getLocation();
    this.getList();
  },
  methods: {
    // 薪资范围字典
    getSalaryRange() {
      let params = { dictType: "salary_range" };
      listData(params).then((response) => {
        this.salaryRangeList = response.rows;
      });
    },
    // 用工地点字典
    getLocation() {
      let params = { dictType: "location" };
      listData(params).then((response) => {
        this.locationList = response.rows;
      });
    },
    /** 查询用工信息列表 */
    getList() {
      this.loading = true;
      listJobInfo(this.queryParams).then((response) => {
        this.jobInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        positionName: null,
        salaryRange: null,
        salaryMin: null,
        salaryMax: null,
        ageLimit: null,
        company: null,
        location: null,
        contactPhone: null,
        requirements: null,
        responsibilities: null,
        otherLimits: null,
        status: null,
        createTime: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用工信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getJobInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用工信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateJobInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addJobInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除用工信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delJobInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/jobInfo/export",
        {
          ...this.queryParams,
        },
        `jobInfo_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
