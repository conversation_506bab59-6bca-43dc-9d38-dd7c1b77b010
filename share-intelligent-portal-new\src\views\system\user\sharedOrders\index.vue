<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
        <div class="top">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">共享订单</div>
          </div>
          <el-button class="btn" type="primary" plain @click="handleAdd">发布共享订单</el-button>
        </div>
        <div class="table">
          <div style="width: 100%">
            <el-table :data="tableData" style="width: 100%" :v-loading="loading" max-height="600">
              <el-table-column label="需求企业" align="center" prop="demandCompany" width="150" />
              <el-table-column label="需求截止时间" align="center" prop="deadline" width="100" />
              <el-table-column label="托单价格" align="center" prop="price" />
              <el-table-column label="联系电话" align="center" prop="contactPhone" />
              <el-table-column label="交货地址" align="center" prop="deliveryAddress" width="150" />
              <el-table-column label="文件要求" align="center" prop="fileRequirement" width="500">
                <template slot-scope="scope">
                  <div class="desc">{{ scope.row.fileRequirement ? scope.row.fileRequirement : "无"}}</div>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="viewProductDetail(scope.row)">查看详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <!-- 分页 -->
          <div class="pageStyle">
            <el-pagination v-if="tableData && tableData.length > 0" background layout="prev, pager, next"
              class="activity-pagination" :page-size="pageSize" :current-page="pageNum" :total="total"
              @size-change="handleSizeChange" @current-change="handleCurrentChange">
            </el-pagination>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import UserMenu from "../components/userMenu.vue";
import { manufactureOrderListData } from "@/api/manufacturingSharing";

export default {
  name: "User",
  components: { UserMenu },
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      tableData: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      manufactureOrderListData(params).then((res) => {
        if (res.code === 200) {
          this.tableData = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    handleAdd() {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => { });
        return;
      } else {
        this.$router.push("/release?index=2");
      }
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    viewProductDetail(row) {
      if (row) {
        this.$router.push("/productOrderDetail?id=" + row.id);
      } else {
        this.$message.error("暂无该订单详情");
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));
}

.top {
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  // margin-top: 20px;
  display: flex;
  justify-content: space-between;

  .content_title {
    display: flex;
    align-items: center;

    .icon {
      width: 4px;
      height: 20px;
      background: #21c9b8;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #030a1a;
      margin-left: 10px;
    }
  }
}

.table {
  margin-top: 20px;
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-around;

  .desc{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 7;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
  }
}

.pageStyle {
  width: 100%;
  margin-top: 61px;
  display: flex;
  justify-content: center;
}
</style>
