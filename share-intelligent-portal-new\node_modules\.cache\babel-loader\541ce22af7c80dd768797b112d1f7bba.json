{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\policyDeclare\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\policyDeclare\\detail\\index.vue", "mtime": 1750311963071}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_policy", "_oss", "_auth", "id", "_default", "exports", "default", "name", "dicts", "components", "UserMenu", "data", "isDetail", "actionUrl", "uploadUrl", "headers", "Authorization", "getToken", "info", "form", "accountLicenceList", "rules", "policyName", "required", "message", "trigger", "contractPerson", "contractPhone", "fileList", "created", "getDetail", "methods", "_this", "userId", "$route", "query", "getPolicyDetail", "then", "response", "total", "goBack", "$router", "go", "changeMode", "handleFilePreview", "file", "window", "open", "submitForm", "type", "_this2", "$refs", "validate", "valid", "status", "editPolicyApply", "_objectSpread2", "$modal", "msgSuccess", "handleApplicationRemove", "application", "handleApplicationSuccess", "res", "code", "url", "applicationName", "handleAccountRemove", "accountLicence", "handleAccountSuccess", "accountLicenceName"], "sources": ["src/views/system/user/policyDeclare/detail/index.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-17 16:19:40\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"notice-record-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">申报详情</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                基本信息\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 政策名称 </template>\r\n                  {{ info.policyName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系人 </template>\r\n                  {{ info.contractPerson }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系电话</template>\r\n                  {{ info.contractPhone }}\r\n                </el-descriptions-item>\r\n\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 资料上传 </template>\r\n                  <a\r\n                    class=\"file-class\"\r\n                    v-for=\"item in info.fileList\"\r\n                    v-bind:key=\"item.url\"\r\n                    @click=\"handleFilePreview(item.url)\"\r\n                  >\r\n                    <el-image\r\n                      style=\"width: 14px; height: 17px\"\r\n                      :src=\"require('@/assets/user/file_pdf.png')\"\r\n                    ></el-image>\r\n                    {{ item.name }}\r\n                    <div class=\"previwe-class\">立即查看</div>\r\n                  </a>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <el-image\r\n                class=\"status_approving\"\r\n                v-if=\"info.status == '2'\"\r\n                style=\"width: 120px; height: 102px\"\r\n                :src=\"require('@/assets/user/status_approving.png')\"\r\n              ></el-image>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"goBack\">返回</el-button>\r\n                <el-button\r\n                  v-if=\"info.status == '1'\"\r\n                  type=\"danger\"\r\n                  @click=\"changeMode\"\r\n                  >编辑</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n            <div class=\"edit-page\" v-else>\r\n              <el-form\r\n                ref=\"form\"\r\n                :model=\"form\"\r\n                :rules=\"rules\"\r\n                label-width=\"120px\"\r\n              >\r\n                <el-form-item label=\"政策名称\" prop=\"policyName\">\r\n                  <el-input\r\n                    v-model=\"form.policyName\"\r\n                    disabled\r\n                    placeholder=\"政策名称\"\r\n                  />\r\n                </el-form-item>\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"联系人\" prop=\"contractPerson\">\r\n                      <el-input\r\n                        v-model=\"form.contractPerson\"\r\n                        placeholder=\"请输入联系人\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"联系电话\" prop=\"contractPhone\">\r\n                      <el-input\r\n                        v-model=\"form.contractPhone\"\r\n                        placeholder=\"请选择联系电话\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"上传文件\" prop=\"fileList\">\r\n                      <el-upload\r\n                        :headers=\"headers\"\r\n                        :action=\"actionUrl\"\r\n                        accept=\".pdf, .doc, .xls\"\r\n                        :file-list=\"form.fileList\"\r\n                        :on-remove=\"handleApplicationRemove\"\r\n                        :on-success=\"handleApplicationSuccess\"\r\n                        :limit=\"10\"\r\n                      >\r\n                        <div>\r\n                          <el-button\r\n                            size=\"small\"\r\n                            type=\"primary\"\r\n                            icon=\"el-icon-upload2\"\r\n                            >上传文件</el-button\r\n                          >\r\n                          <span class=\"tip\">仅限doc、pdf、xls格式</span>\r\n                        </div>\r\n                      </el-upload>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"changeMode\">返回</el-button>\r\n                <el-button type=\"error\" @click=\"changeMode(1)\"\r\n                  >暂存草稿</el-button\r\n                >\r\n                <el-button type=\"danger\" @click=\"submitForm(2)\"\r\n                  >提交审核</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport { getPolicyDetail, editPolicyApply } from \"@/api/system/policy\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\"affiliated_unit\", \"capital_source\", \"affiliated_street\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      info: {},\r\n      form: {},\r\n      accountLicenceList: [],\r\n      // 表单校验\r\n      rules: {\r\n        policyName: [\r\n          { required: true, message: \"所属单位不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractPerson: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractPhone: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n        fileList: [\r\n          { required: true, message: \"文件不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    getDetail() {\r\n      let userId = this.$route.query.id;\r\n      getPolicyDetail(userId).then((response) => {\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n\r\n    changeMode() {\r\n      if (this.isDetail) {\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n      } else {\r\n        this.isDetail = true;\r\n        this.form = {};\r\n      }\r\n      this.getDetail();\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          this.form.status = type;\r\n          editPolicyApply({ ...this.form }).then((response) => {\r\n            this.$modal.msgSuccess(\"操作成功\");\r\n            this.changeMode();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.application = res.data.url;\r\n        this.form.applicationName = res.data.name;\r\n      }\r\n    },\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .notice-record-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 20px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 90%;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n          color: #333;\r\n        }\r\n        // .el-button:hover,\r\n        // .el-button:focus {\r\n        //   border-color: #d9d9d9;\r\n        //   background-color: transparent;\r\n        // }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AA0JA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,IAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AALA,IAAAI,EAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAOA;EACAC,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA,MAAAC,cAAA;MACAC,OAAA;QAAAC,aAAA,kBAAAC,cAAA;MAAA;MACAC,IAAA;MACAC,IAAA;MACAC,kBAAA;MACA;MACAC,KAAA;QACAC,UAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,cAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,aAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,QAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAD,SAAA,WAAAA,UAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,MAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAhC,EAAA;MACA,IAAAiC,uBAAA,EAAAH,MAAA,EAAAI,IAAA,WAAAC,QAAA;QACAN,KAAA,CAAAd,IAAA,GAAAoB,QAAA,CAAA3B,IAAA;QACAqB,KAAA,CAAAO,KAAA,GAAAD,QAAA,CAAAC,KAAA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IAEAC,UAAA,WAAAA,WAAA;MACA,SAAA/B,QAAA;QACA,KAAAA,QAAA;QACA,KAAAO,IAAA,QAAAD,IAAA;MACA;QACA,KAAAN,QAAA;QACA,KAAAO,IAAA;MACA;MACA,KAAAW,SAAA;IACA;IACAc,iBAAA,WAAAA,kBAAAC,IAAA;MACAC,MAAA,CAAAC,IAAA,CAAAF,IAAA;IACA;IACAG,UAAA,WAAAA,WAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAA/B,IAAA,CAAAmC,MAAA,GAAAL,IAAA;UACA,IAAAM,uBAAA,MAAAC,cAAA,CAAAlD,OAAA,MAAA4C,MAAA,CAAA/B,IAAA,GAAAkB,IAAA,WAAAC,QAAA;YACAY,MAAA,CAAAO,MAAA,CAAAC,UAAA;YACAR,MAAA,CAAAP,UAAA;UACA;QACA;MACA;IACA;IACAgB,uBAAA,WAAAA,wBAAAd,IAAA,EAAAjB,QAAA;MACA,KAAAT,IAAA,CAAAyC,WAAA;IACA;IACAC,wBAAA,WAAAA,yBAAAC,GAAA,EAAAjB,IAAA,EAAAjB,QAAA;MACA;MACA,IAAAkC,GAAA,CAAAC,IAAA;QACA,KAAA5C,IAAA,CAAAyC,WAAA,GAAAE,GAAA,CAAAnD,IAAA,CAAAqD,GAAA;QACA,KAAA7C,IAAA,CAAA8C,eAAA,GAAAH,GAAA,CAAAnD,IAAA,CAAAJ,IAAA;MACA;IACA;IACA2D,mBAAA,WAAAA,oBAAArB,IAAA,EAAAjB,QAAA;MACA,KAAAT,IAAA,CAAAgD,cAAA;IACA;IACAC,oBAAA,WAAAA,qBAAAN,GAAA,EAAAjB,IAAA,EAAAjB,QAAA;MACA;MACA,IAAAkC,GAAA,CAAAC,IAAA;QACA,KAAA5C,IAAA,CAAAgD,cAAA,GAAAL,GAAA,CAAAnD,IAAA,CAAAqD,GAAA;QACA,KAAA7C,IAAA,CAAAkD,kBAAA,GAAAP,GAAA,CAAAnD,IAAA,CAAAJ,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}