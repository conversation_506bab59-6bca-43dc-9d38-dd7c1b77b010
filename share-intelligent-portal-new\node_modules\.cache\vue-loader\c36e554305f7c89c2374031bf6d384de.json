{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\emInformation\\index.vue?vue&type=style&index=0&id=6d57a54e&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\emInformation\\index.vue", "mtime": 1750311963056}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6JA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/emInformation", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"top\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">用工信息</div>\r\n            <div class=\"buttonStyle\" @click=\"toPublish\">发布用工信息</div>\r\n          </div>\r\n        </div>\r\n        <el-form class=\"queryForm\" :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\">\r\n          <el-form-item label=\"岗位名称\" prop=\"positionName\">\r\n            <el-input v-model=\"queryParams.positionName\" placeholder=\"请输入岗位名称\" clearable\r\n              @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div class=\"cards\">\r\n          <div class=\"card\" v-for=\"item in employmentList\" :key=\"item.id\" @click=\"goDetail(item.id)\">\r\n            <el-descriptions :column=\"1\" :labelStyle=\"labelStyle\" :contentStyle=\"contentStyle\">\r\n              <el-descriptions-item label=\"岗位名称\">{{\r\n                item.positionName\r\n                }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"岗位要求\">\r\n                {{ item.requirements }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"时间\">{{\r\n                item.createTime\r\n                }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"状态\">{{\r\n                item.status == \"0\" ? \"正常\" : \"停用\"\r\n                }}</el-descriptions-item>\r\n            </el-descriptions>\r\n            <!-- <div class=\"btn\">\r\n              <el-button type=\"primary\" plain size=\"mini\">修改</el-button>\r\n              <el-button type=\"primary\" size=\"mini\">详情</el-button>\r\n            </div> -->\r\n          </div>\r\n          <!-- 分页 -->\r\n          <div class=\"pageStyle\">\r\n            <el-pagination v-if=\"employmentList && employmentList.length > 0\" background layout=\"prev, pager, next\"\r\n              class=\"activity-pagination\" :page-size=\"pageSize\" :current-page=\"pageNum\" :total=\"total\"\r\n              @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n        <el-empty v-if=\"employmentList.length == 0\"></el-empty>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { employUserListData } from \"@/api/serviceSharing\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      pageNum: 1,\r\n      pageSize: 8,\r\n      total: 0,\r\n      employmentList: [],\r\n      queryParams: {\r\n        positionName: \"\",\r\n      },\r\n      labelStyle: {\r\n        fontWeight: 400,\r\n        fontSize: \"14px\",\r\n        color: \"#999999\",\r\n        width: \"60px\",\r\n        justifyContent: \"flex-end\",\r\n      },\r\n      contentStyle: {\r\n        fontWeight: 400,\r\n        fontSize: \"14px\",\r\n        color: \"#333333\",\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    // 列表接口\r\n    getList() {\r\n      this.loading = true;\r\n      let userinfo = JSON.parse(window.sessionStorage.getItem(\"userinfo\"));\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        positionName: this.queryParams.positionName,\r\n        createBy: userinfo.memberPhone\r\n      };\r\n      employUserListData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.employmentList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams = {\r\n        categoryId: undefined,\r\n        status: undefined,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      };\r\n      this.getList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    toPublish() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(\"/publishEmInformation\");\r\n      }\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push(\"/employmentInfoDetail?id=\" + id);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 1000px;\r\n}\r\n\r\n.top {\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  // margin-top: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n    width: 100%;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n\r\n    .buttonStyle {\r\n      padding: 10px 20px;\r\n      background: #21c9b8;\r\n      color: #fff;\r\n      text-align: center;\r\n      cursor: pointer;\r\n      border-radius: 10px;\r\n      margin-left: auto;\r\n    }\r\n  }\r\n}\r\n\r\n.queryForm {\r\n  padding: 20px;\r\n}\r\n\r\n.cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n\r\n  .card {\r\n    width: 24%;\r\n    // height: 200px;\r\n    background: #fff;\r\n    padding: 20px;\r\n    margin-right: 10px;\r\n    margin-bottom: 10px;\r\n    border-radius: 10px;\r\n    box-sizing: border-box;\r\n    position: relative;\r\n    cursor: pointer;\r\n\r\n    .btn {\r\n      position: absolute;\r\n      right: 20px;\r\n      bottom: 20px;\r\n    }\r\n  }\r\n\r\n  .pageStyle {\r\n    width: 100%;\r\n    margin-top: 61px;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}