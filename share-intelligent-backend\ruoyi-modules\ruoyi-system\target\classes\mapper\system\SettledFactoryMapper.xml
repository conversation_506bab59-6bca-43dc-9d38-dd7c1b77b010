<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SettledFactoryMapper">

    <resultMap type="SettledFactory" id="SettledFactoryResult">
        <result property="id" column="id"/>
        <result property="companyName" column="company_name"/>
        <result property="industry" column="industry"/>
        <result property="factoryType" column="factory_type"/>
        <result property="companyAddress" column="company_address"/>
        <result property="socialCreditCode" column="social_credit_code"/>
        <result property="businessScope" column="business_scope"/>
        <result property="registeredCapital" column="registered_capital"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="qrCode" column="qr_code"/>
        <result property="settledStatus" column="settled_status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectSettledFactoryVo">
        select id, company_name, industry, factory_type, company_address, social_credit_code, business_scope, registered_capital, contact_phone, qr_code, settled_status, create_time, update_time
        from settled_factory
    </sql>

    <select id="selectSettledFactoryList" parameterType="SettledFactory" resultMap="SettledFactoryResult">
        <include refid="selectSettledFactoryVo"/>
        <where>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="industry != null  and industry != ''"> and industry = #{industry}</if>
            <if test="factoryType != null  and factoryType != ''"> and factory_type = #{factoryType}</if>
            <if test="companyAddress != null  and companyAddress != ''"> and company_address = #{companyAddress}</if>
            <if test="socialCreditCode != null  and socialCreditCode != ''"> and social_credit_code = #{socialCreditCode}</if>
            <if test="businessScope != null  and businessScope != ''"> and business_scope = #{businessScope}</if>
            <if test="registeredCapital != null  and registeredCapital != ''"> and registered_capital = #{registeredCapital}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="qrCode != null  and qrCode != ''"> and qr_code = #{qrCode}</if>
            <if test="settledStatus != null  and settledStatus != ''"> and settled_status = #{settledStatus}</if>
        </where>
    </select>

    <select id="selectSettledFactoryById" parameterType="Long" resultMap="SettledFactoryResult">
        <include refid="selectSettledFactoryVo"/>
        where id = #{id}
    </select>

    <!-- 根据产品ID查询关联的工厂列表 -->
    <select id="selectSettledFactoryListByProductId" parameterType="Long" resultMap="SettledFactoryResult">
        SELECT sf.id, sf.company_name, sf.industry, sf.factory_type, sf.company_address,
               sf.social_credit_code, sf.business_scope, sf.registered_capital,
               sf.contact_phone, sf.qr_code, sf.settled_status, sf.create_time, sf.update_time
        FROM settled_factory sf
        INNER JOIN sys_product sp ON FIND_IN_SET(sf.id, sp.factory_id)
        WHERE sp.product_id = #{productId}
        AND sf.settled_status = '1'
        ORDER BY sf.create_time DESC
    </select>

    <insert id="insertSettledFactory" parameterType="SettledFactory" useGeneratedKeys="true" keyProperty="id">
        insert into settled_factory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null">company_name,</if>
            <if test="industry != null">industry,</if>
            <if test="factoryType != null">factory_type,</if>
            <if test="companyAddress != null">company_address,</if>
            <if test="socialCreditCode != null">social_credit_code,</if>
            <if test="businessScope != null">business_scope,</if>
            <if test="registeredCapital != null">registered_capital,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="qrCode != null">qr_code,</if>
            <if test="settledStatus != null">settled_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null">#{companyName},</if>
            <if test="industry != null">#{industry},</if>
            <if test="factoryType != null">#{factoryType},</if>
            <if test="companyAddress != null">#{companyAddress},</if>
            <if test="socialCreditCode != null">#{socialCreditCode},</if>
            <if test="businessScope != null">#{businessScope},</if>
            <if test="registeredCapital != null">#{registeredCapital},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="qrCode != null">#{qrCode},</if>
            <if test="settledStatus != null">#{settledStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSettledFactory" parameterType="SettledFactory">
        update settled_factory
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="industry != null">industry = #{industry},</if>
            <if test="factoryType != null">factory_type = #{factoryType},</if>
            <if test="companyAddress != null">company_address = #{companyAddress},</if>
            <if test="socialCreditCode != null">social_credit_code = #{socialCreditCode},</if>
            <if test="businessScope != null">business_scope = #{businessScope},</if>
            <if test="registeredCapital != null">registered_capital = #{registeredCapital},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="qrCode != null">qr_code = #{qrCode},</if>
            <if test="settledStatus != null">settled_status = #{settledStatus},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSettledFactoryById" parameterType="Long">
        delete from settled_factory where id = #{id}
    </delete>

    <delete id="deleteSettledFactoryByIds" parameterType="String">
        delete from settled_factory where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>