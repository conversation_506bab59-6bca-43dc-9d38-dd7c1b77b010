<template>
  <!-- 政策申报 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="发布单位" prop="policySubmitUnit">
        <el-input
          v-model="queryParams.policySubmitUnit"
          placeholder="请输入发布单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="政策标题" prop="policySubmitTitle">
        <el-input
          v-model="queryParams.policySubmitTitle"
          placeholder="请输入政策标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="截止时间" prop="policySubmitEndDate">
        <el-date-picker clearable
          v-model="queryParams.policySubmitEndDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择截止时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:PolicySubmit:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:PolicySubmit:edit']"
        >修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:PolicySubmit:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:PolicySubmit:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" class="myTable" :data="PolicySubmitList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" align="center" prop="policySubmitId" />
      <el-table-column label="政策标题" align="center" prop="policySubmitTitle" />
      <el-table-column label="状态" align="center" prop="policySubmitStatus" >
        <template slot-scope="scope">
          <div v-if="scope.row.policySubmitStatus==='0'">草稿</div>
          <div v-else-if="scope.row.policySubmitStatus==='1'">审核中</div>
          <div v-else-if="scope.row.policySubmitStatus==='2'">已通过</div>
          <div v-else-if="scope.row.policySubmitStatus==='3'">已驳回</div>
        </template>
      </el-table-column>
      <el-table-column label="发布单位" align="center" prop="policySubmitUnit" />
      <!-- <el-table-column label="政策类型" align="center" prop="policySubmitType" /> -->
      <el-table-column label="截止时间" align="center" prop="policySubmitEndDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.policySubmitEndDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最高奖励" align="center" prop="policySubmitReward" />
      <el-table-column label="条款内容" align="center" prop="policySubmitContent" />
      <!-- <el-table-column label="封面" align="center" prop="policySubmitImg" />
      <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleDetail(scope.row)"
            v-hasPermi="['portalconsole:PolicySubmit:edit']"
          >详情</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:PolicySubmit:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:PolicySubmit:remove']"
          >删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改政策申报对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="政策标题" prop="policySubmitTitle">
          <el-input v-model="form.policySubmitTitle" placeholder="请输入政策标题" />
        </el-form-item>
        <el-form-item label="发布单位" prop="policySubmitUnit">
          <el-input v-model="form.policySubmitUnit" placeholder="请输入发布单位" />
          <!-- <el-select v-model="form.policySubmitUnit" placeholder="请选择发布单位" clearable>
                <el-option
                  v-for="dict in dict.type.demand_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select> -->
        </el-form-item>
        <!-- <el-form-item label="政策类型" prop="policySubmitUnit">
          <el-select v-model="form.policySubmitUnit" placeholder="请选择政策类型" clearable>
                <el-option
                  v-for="dict in dict.type.demand_status"
                  :key="dict.value"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                ></el-option>
              </el-select> 
        </el-form-item>-->
        <el-form-item label="政策标签" prop="policyLabel">
          <el-checkbox-group v-model="policyLabel">
            <el-checkbox-button v-for="dict in dict.type.sys_policydeclaration" :label="dict.label" :key="dict.value">{{dict.label}}</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="截止时间" prop="policySubmitEndDate">
          <el-date-picker clearable
            v-model="form.policySubmitEndDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择截止时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="最高奖励" prop="policySubmitReward">
          <el-input v-model="form.policySubmitReward" placeholder="请输入最高奖励" />
        </el-form-item>
        <el-form-item label="企业标签" prop="companyLabel">
          <el-checkbox-group v-model="companyLabel">
            <el-checkbox-button v-for="dict in dict.type.sys_qylabel" :label="dict.label" :key="dict.value">{{dict.label}}</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="条款内容" prop="policySubmitContent">
          <editor v-model="form.policySubmitContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="上传封面" prop="policySubmitImg">
          <!-- <el-input v-model="form.policySubmitImg" type="textarea" placeholder="请输入内容" /> -->
          <el-upload
            class="avatar-uploader"
            action=""
            :show-file-list="false"
            :http-request="uploadFun"
            :before-upload="beforeAvatarUpload">
            <el-image
              v-if="form.policySubmitImg"
              :src="form.policySubmitImg"
              class="avatar"
            >
              <div slot="error" class="image-slot">
                <span>暂无图片</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            <div slot="tip" class="el-upload__tip">支持扩展名：.png/.jpg格式，文件大小不超过10M</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="上传附件" prop="attachment">
          <el-upload class="upload-demo" drag :file-list='fileAddList' :limit='10'
              action=""  
              :on-preview="handlePreview"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :http-request="uploadFile" >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
              <div slot="tip" class="el-upload__tip">支持扩展名：.xls .doc .docx .pdf .jpg，文件大小不超过10M</div>
            </el-upload>
        </el-form-item>
        <el-form-item label="状态" prop="policySubmitStatus">
          <el-radio v-model="form.policySubmitStatus" v-for="dict in dict.type.sys_zc_status" :key="dict.value"
                :label="dict.value"
                :value="dict.value">{{ dict.label }}</el-radio>
        </el-form-item>
        <el-form-item label="设为推荐" prop="recommend">
          <el-switch
            v-model="form.recommend"
            active-color="#13ce66"
            inactive-color="rgb(140, 147, 157)">
          </el-switch>
        </el-form-item>
        <!-- <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item> -->

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <detailDialog ref="detailDialog" @submit="submit"></detailDialog>
  </div>
</template>

<script>
import { listPolicySubmit, getPolicySubmit, delPolicySubmit, addPolicySubmit, updatePolicySubmit } from "@/api/portalconsole/PolicySubmit";
import {comUpload} from "@/api/portalconsole/uploadApi";
import detailDialog from "./components/detailDialog.vue";
export default {
  name: "PolicySubmitList",
  components:{
    detailDialog
  },
  dicts:["sys_enterprise_label",'sys_zc_status','sys_policydeclaration','sys_qylabel'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 政策申报表格数据
      PolicySubmitList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        policySubmitUnit: null,
        policySubmitType: null,
        policySubmitTitle: null,
        policySubmitEndDate: null,
        policySubmitReward: null,
        policySubmitImg: null,
        policySubmitContent: null,
        policySubmitStatus: null,
        
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        policySubmitTitle:[
          { required: true, message: "政策标题不能为空", trigger: "blur" }
        ],
        policySubmitUnit:[
          { required: true, message: "发布单位不能为空", trigger: "change" }
        ],
        policySubmitEndDate:[
          { required: true, message: "截止时间不能为空", trigger: "change" }
        ],
        policySubmitReward:[
          { required: true, message: "最高奖励不能为空", trigger: "change" }
        ],
        policySubmitContent:[
          { required: true, message: "条款内容不能为空", trigger: "change" }
        ]
      },
      //附件
      fileAddList:[],
      policyLabel:[],
      companyLabel:[],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询政策申报列表 */
    getList() {
      this.loading = true;
      listPolicySubmit(this.queryParams).then(response => {
        this.PolicySubmitList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        policySubmitId: null,
        policySubmitUnit: null,
        policySubmitType: null,
        policySubmitTitle: null,
        policySubmitEndDate: null,
        policySubmitReward: null,
        policySubmitImg: null,
        policySubmitContent: null,
        policySubmitStatus: null,
        delFlag: null,
        // createBy: null,
        // createTime: null,
        // updateBy: null,
        // updateTime: null,
        // remark: null,
        policyLabel:null,
        companyLabel:null,
        //设为推荐
        recommend:false,
      };
      this.policyLabel=[]
      this.companyLabel=[]
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.policySubmitId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加政策申报";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const policySubmitId = row.policySubmitId || this.ids
      getPolicySubmit(policySubmitId).then(response => {
        this.form = response.data;
        if(this.form.policyLabel){
          this.policyLabel=this.form.policyLabel.split(',')
        }
        if(this.form.companyLabel){
          this.companyLabel=this.form.companyLabel.split(',')
        }
        this.open = true;
        this.title = "修改政策申报";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.policyLabel=this.policyLabel.join(',')
          this.form.companyLabel=this.companyLabel.join(',')
          if (this.form.policySubmitId != null) {
            updatePolicySubmit(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPolicySubmit(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const policySubmitIds = row.policySubmitId || this.ids;
      this.$modal.confirm('是否确认删除政策申报编号为"' + policySubmitIds + '"的数据项？').then(function() {
        return delPolicySubmit(policySubmitIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/PolicySubmit/export', {
        ...this.queryParams
      }, `PolicySubmit_${new Date().getTime()}.xlsx`)
    },
     // 图片上传
     uploadFun(params) {
        const file = params.file;
        let form = new FormData();
        form.append("file", file); // 文件对象
        comUpload(form).then(res => {
          let data = res.data;
          this.$set(this.form,'policySubmitImg',data.fileFullPath)  // 图片全路径
          // this.$set(this.form,'imageUrl',data.fileId) // 图片Id
        })
      },
      beforeAvatarUpload(file) {
        const isJPG = file.type === 'image/jpeg' || 'image/png' || 'image/jpg';
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isJPG) {
          this.$message.error('上传图片只能是 jpg、jpeg、png 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传头像图片大小不能超过 2MB!');
        }
        return isJPG && isLt2M;
      },
      //附件上传
      handleRemove(file, fileList) {
        this.fileAddList.splice(this.fileAddList.findIndex(item => item.id == file.id), 1)
        this.$set(this.form,'attachment',JSON.stringify(this.fileAddList))  // 图片全路径
      },
      handlePreview(file) {
        console.log(file);
      },
      beforeRemove(file, fileList) {
        return this.$confirm(`确定移除 ${ file.name }？`);
      },
      uploadFile(params){
        const file = params.file;
        let form = new FormData();
        form.append("file", file); // 文件对象
        comUpload(form).then(res => {
          let data = res.data;
          console.log("data",data)
          this.fileAddList.push({
            name:data.fileName,
            id:data.fileId,
            fileUrl:data.fileFullPath
          })
          this.$set(this.form,'attachment',JSON.stringify(this.fileAddList))  // 图片全路径
        })
      },
      //详情
      handleDetail(row){
        this.$refs.detailDialog.show(row.policySubmitId)
      },
      submit(updata){
        if(updata){
          this.getList()
        }
      }
  }
};
</script>
<style  scoped>
>>>.el-table .el-dropdown-link {
    cursor: pointer;
    color: #409eff;
    margin-left: 5px;
}
.avatar-uploader >>> .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
/deep/.el-checkbox-button {
    position: relative;
    display: inline-block;
    margin-right: 5px;
}
/deep/.el-checkbox-button:last-child .el-checkbox-button__inner {
    border-radius: 0 4px 4px 0;
    border: 1px solid #DCDFE6;
}
.myTable .el-table__row {
  height: 50px;
  line-height: 50px;
}
>>>.el-table .cell{
  height: 20px;
  overflow: hidden;
}
</style>