package com.ruoyi.portalweb.api.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 专家库对象 expert_database
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public class ExpertDatabase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 专家库ID */
    private Long expertDatabaseId;

    /** 姓名 */
    @Excel(name = "姓名")
    private String expertDatabaseName;

    /** 特约专家：业务字典 */
    @Excel(name = "特约专家：业务字典")
    private Long expertDatabaseExpert;

    /** 工作单位 */
    @Excel(name = "工作单位")
    private String expertDatabaseWorkunit;

    /** 职务 */
    @Excel(name = "职务")
    private String expertDatabasePost;

    /** 平台对接人 */
    @Excel(name = "平台对接人")
    private String expertDatabaseContact;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String expertDatabaseEmail;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String expertDatabasePhone;

    /** 微信号 */
    @Excel(name = "微信号")
    private String expertDatabaseWechat;

    /** 研究方向 */
    @Excel(name = "研究方向")
    private String expertDatabaseDirection;

    /** 主要成果 */
    @Excel(name = "主要成果")
    private String expertDatabaseAchievement;

    /** 标签 */
    @Excel(name = "标签")
    private String expertDatabaseTag;

    /** 技术类别：业务字典 */
    @Excel(name = "技术类别：业务字典")
    private String expertDatabaseTechnology;

    /** 专家简介 */
    @Excel(name = "专家简介")
    private String expertDatabaseIntroduction;

    /** 头像 */
    @Excel(name = "头像")
    private String expertDatabaseImghead;

    /** 专家介绍 */
    @Excel(name = "专家介绍")
    private String expertDatabaseIntroduce;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setExpertDatabaseId(Long expertDatabaseId) 
    {
        this.expertDatabaseId = expertDatabaseId;
    }

    public Long getExpertDatabaseId() 
    {
        return expertDatabaseId;
    }
    public void setExpertDatabaseName(String expertDatabaseName) 
    {
        this.expertDatabaseName = expertDatabaseName;
    }

    public String getExpertDatabaseName() 
    {
        return expertDatabaseName;
    }
    public void setExpertDatabaseExpert(Long expertDatabaseExpert) 
    {
        this.expertDatabaseExpert = expertDatabaseExpert;
    }

    public Long getExpertDatabaseExpert() 
    {
        return expertDatabaseExpert;
    }
    public void setExpertDatabaseWorkunit(String expertDatabaseWorkunit) 
    {
        this.expertDatabaseWorkunit = expertDatabaseWorkunit;
    }

    public String getExpertDatabaseWorkunit() 
    {
        return expertDatabaseWorkunit;
    }
    public void setExpertDatabasePost(String expertDatabasePost) 
    {
        this.expertDatabasePost = expertDatabasePost;
    }

    public String getExpertDatabasePost() 
    {
        return expertDatabasePost;
    }
    public void setExpertDatabaseContact(String expertDatabaseContact) 
    {
        this.expertDatabaseContact = expertDatabaseContact;
    }

    public String getExpertDatabaseContact() 
    {
        return expertDatabaseContact;
    }
    public void setExpertDatabaseEmail(String expertDatabaseEmail) 
    {
        this.expertDatabaseEmail = expertDatabaseEmail;
    }

    public String getExpertDatabaseEmail() 
    {
        return expertDatabaseEmail;
    }
    public void setExpertDatabasePhone(String expertDatabasePhone) 
    {
        this.expertDatabasePhone = expertDatabasePhone;
    }

    public String getExpertDatabasePhone() 
    {
        return expertDatabasePhone;
    }
    public void setExpertDatabaseWechat(String expertDatabaseWechat) 
    {
        this.expertDatabaseWechat = expertDatabaseWechat;
    }

    public String getExpertDatabaseWechat() 
    {
        return expertDatabaseWechat;
    }
    public void setExpertDatabaseDirection(String expertDatabaseDirection) 
    {
        this.expertDatabaseDirection = expertDatabaseDirection;
    }

    public String getExpertDatabaseDirection() 
    {
        return expertDatabaseDirection;
    }
    public void setExpertDatabaseAchievement(String expertDatabaseAchievement) 
    {
        this.expertDatabaseAchievement = expertDatabaseAchievement;
    }

    public String getExpertDatabaseAchievement() 
    {
        return expertDatabaseAchievement;
    }
    public void setExpertDatabaseTag(String expertDatabaseTag) 
    {
        this.expertDatabaseTag = expertDatabaseTag;
    }

    public String getExpertDatabaseTag() 
    {
        return expertDatabaseTag;
    }
    public void setExpertDatabaseTechnology(String expertDatabaseTechnology) 
    {
        this.expertDatabaseTechnology = expertDatabaseTechnology;
    }

    public String getExpertDatabaseTechnology() 
    {
        return expertDatabaseTechnology;
    }
    public void setExpertDatabaseIntroduction(String expertDatabaseIntroduction) 
    {
        this.expertDatabaseIntroduction = expertDatabaseIntroduction;
    }

    public String getExpertDatabaseIntroduction() 
    {
        return expertDatabaseIntroduction;
    }
    public void setExpertDatabaseImghead(String expertDatabaseImghead) 
    {
        this.expertDatabaseImghead = expertDatabaseImghead;
    }

    public String getExpertDatabaseImghead() 
    {
        return expertDatabaseImghead;
    }
    public void setExpertDatabaseIntroduce(String expertDatabaseIntroduce) 
    {
        this.expertDatabaseIntroduce = expertDatabaseIntroduce;
    }

    public String getExpertDatabaseIntroduce() 
    {
        return expertDatabaseIntroduce;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("expertDatabaseId", getExpertDatabaseId())
            .append("expertDatabaseName", getExpertDatabaseName())
            .append("expertDatabaseExpert", getExpertDatabaseExpert())
            .append("expertDatabaseWorkunit", getExpertDatabaseWorkunit())
            .append("expertDatabasePost", getExpertDatabasePost())
            .append("expertDatabaseContact", getExpertDatabaseContact())
            .append("expertDatabaseEmail", getExpertDatabaseEmail())
            .append("expertDatabasePhone", getExpertDatabasePhone())
            .append("expertDatabaseWechat", getExpertDatabaseWechat())
            .append("expertDatabaseDirection", getExpertDatabaseDirection())
            .append("expertDatabaseAchievement", getExpertDatabaseAchievement())
            .append("expertDatabaseTag", getExpertDatabaseTag())
            .append("expertDatabaseTechnology", getExpertDatabaseTechnology())
            .append("expertDatabaseIntroduction", getExpertDatabaseIntroduction())
            .append("expertDatabaseImghead", getExpertDatabaseImghead())
            .append("expertDatabaseIntroduce", getExpertDatabaseIntroduce())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
