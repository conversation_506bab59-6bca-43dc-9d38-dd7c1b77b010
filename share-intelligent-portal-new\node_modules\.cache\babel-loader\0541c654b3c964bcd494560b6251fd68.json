{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\icons\\element-icons.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\icons\\element-icons.js", "mtime": 1750311962938}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["elementIcons", "_default", "exports", "default"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/views/components/icons/element-icons.js"], "sourcesContent": ["const elementIcons = ['platform-eleme', 'eleme', 'delete-solid', 'delete', 's-tools', 'setting', 'user-solid', 'user', 'phone', 'phone-outline', 'more', 'more-outline', 'star-on', 'star-off', 's-goods', 'goods', 'warning', 'warning-outline', 'question', 'info', 'remove', 'circle-plus', 'success', 'error', 'zoom-in', 'zoom-out', 'remove-outline', 'circle-plus-outline', 'circle-check', 'circle-close', 's-help', 'help', 'minus', 'plus', 'check', 'close', 'picture', 'picture-outline', 'picture-outline-round', 'upload', 'upload2', 'download', 'camera-solid', 'camera', 'video-camera-solid', 'video-camera', 'message-solid', 'bell', 's-cooperation', 's-order', 's-platform', 's-fold', 's-unfold', 's-operation', 's-promotion', 's-home', 's-release', 's-ticket', 's-management', 's-open', 's-shop', 's-marketing', 's-flag', 's-comment', 's-finance', 's-claim', 's-custom', 's-opportunity', 's-data', 's-check', 's-grid', 'menu', 'share', 'd-caret', 'caret-left', 'caret-right', 'caret-bottom', 'caret-top', 'bottom-left', 'bottom-right', 'back', 'right', 'bottom', 'top', 'top-left', 'top-right', 'arrow-left', 'arrow-right', 'arrow-down', 'arrow-up', 'd-arrow-left', 'd-arrow-right', 'video-pause', 'video-play', 'refresh', 'refresh-right', 'refresh-left', 'finished', 'sort', 'sort-up', 'sort-down', 'rank', 'loading', 'view', 'c-scale-to-original', 'date', 'edit', 'edit-outline', 'folder', 'folder-opened', 'folder-add', 'folder-remove', 'folder-delete', 'folder-checked', 'tickets', 'document-remove', 'document-delete', 'document-copy', 'document-checked', 'document', 'document-add', 'printer', 'paperclip', 'takeaway-box', 'search', 'monitor', 'attract', 'mobile', 'scissors', 'umbrella', 'headset', 'brush', 'mouse', 'coordinate', 'magic-stick', 'reading', 'data-line', 'data-board', 'pie-chart', 'data-analysis', 'collection-tag', 'film', 'suitcase', 'suitcase-1', 'receiving', 'collection', 'files', 'notebook-1', 'notebook-2', 'toilet-paper', 'office-building', 'school', 'table-lamp', 'house', 'no-smoking', 'smoking', 'shopping-cart-full', 'shopping-cart-1', 'shopping-cart-2', 'shopping-bag-1', 'shopping-bag-2', 'sold-out', 'sell', 'present', 'box', 'bank-card', 'money', 'coin', 'wallet', 'discount', 'price-tag', 'news', 'guide', 'male', 'female', 'thumb', 'cpu', 'link', 'connection', 'open', 'turn-off', 'set-up', 'chat-round', 'chat-line-round', 'chat-square', 'chat-dot-round', 'chat-dot-square', 'chat-line-square', 'message', 'postcard', 'position', 'turn-off-microphone', 'microphone', 'close-notification', 'bangzhu', 'time', 'odometer', 'crop', 'aim', 'switch-button', 'full-screen', 'copy-document', 'mic', 'stopwatch', 'medal-1', 'medal', 'trophy', 'trophy-1', 'first-aid-kit', 'discover', 'place', 'location', 'location-outline', 'location-information', 'add-location', 'delete-location', 'map-location', 'alarm-clock', 'timer', 'watch-1', 'watch', 'lock', 'unlock', 'key', 'service', 'mobile-phone', 'bicycle', 'truck', 'ship', 'basketball', 'football', 'soccer', 'baseball', 'wind-power', 'light-rain', 'lightning', 'heavy-rain', 'sunrise', 'sunrise-1', 'sunset', 'sunny', 'cloudy', 'partly-cloudy', 'cloudy-and-sunny', 'moon', 'moon-night', 'dish', 'dish-1', 'food', 'chicken', 'fork-spoon', 'knife-fork', 'burger', 'tableware', 'sugar', 'dessert', 'ice-cream', 'hot-water', 'water-cup', 'coffee-cup', 'cold-drink', 'goblet', 'goblet-full', 'goblet-square', 'goblet-square-full', 'refrigerator', 'grape', 'watermelon', 'cherry', 'apple', 'pear', 'orange', 'coffee', 'ice-tea', 'ice-drink', 'milk-tea', 'potato-strips', 'lollipop', 'ice-cream-square', 'ice-cream-round']\r\n\r\nexport default elementIcons\r\n"], "mappings": ";;;;;;AAAA,IAAMA,YAAY,GAAG,CAAC,gBAAgB,EAAE,OAAO,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,uBAAuB,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,QAAQ,EAAE,oBAAoB,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,aAAa,EAAE,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,SAAS,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,eAAe,EAAE,kBAAkB,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,iBAAiB,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY,EAAE,iBAAiB,EAAE,aAAa,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,qBAAqB,EAAE,YAAY,EAAE,oBAAoB,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,eAAe,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,cAAc,EAAE,iBAAiB,EAAE,cAAc,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,cAAc,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,YAAY,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,kBAAkB,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE,eAAe,EAAE,oBAAoB,EAAE,cAAc,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,kBAAkB,EAAE,iBAAiB,CAAC;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAElgHH,YAAY", "ignoreList": []}]}