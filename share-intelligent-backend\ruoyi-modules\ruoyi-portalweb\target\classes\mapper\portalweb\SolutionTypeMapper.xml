<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.SolutionTypeMapper">

    <resultMap type="com.ruoyi.portalweb.vo.SolutionTypeVO" id="SolutionTypeResult">
        <result property="solutionTypeId" column="solution_type_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="solutionTypeName" column="solution_type_name"/>
        <result property="category" column="category"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>

        <result property="categoryName" column="category_name"/>
        <result property="totalCount" column="total_count"/>
    </resultMap>

    <sql id="selectSolutionTypeVo">
        select solution_type_id, parent_id, solution_type_name,category, del_flag, create_by, create_time, update_by,
        update_time, remark from solution_type
    </sql>

    <sql id="Base_Column_List">
		a.*, s1.dict_label AS category_name
	</sql>

	<sql id="Base_Table_List">
		FROM solution_type a
        LEFT JOIN sys_dict_data s1 ON a.category = s1.dict_value AND s1.dict_type = 'solution_type_category'
	</sql>

    <select id="selectSolutionTypeList" parameterType="SolutionType" resultMap="SolutionTypeResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        <where>
            <if test="parentId != null ">and a.parent_id = #{parentId}</if>
            <if test="solutionTypeName != null  and solutionTypeName != ''">
                and a.solution_type_name like concat('%', #{solutionTypeName}, '%')
            </if>
            <if test="category != null and category != ''">
                and a.category = #{category}
            </if>
            <if test="solutionTypeId != null ">
                and a.solution_type_id in (
                WITH recursive tmp_type AS (
                SELECT * FROM solution_type WHERE solution_type_id = #{solutionTypeId}
                UNION ALL
                SELECT a.*
                FROM solution_type a
                INNER JOIN tmp_type b ON a.parent_id = b.solution_type_id
                )
                SELECT solution_type_id FROM tmp_type
                )
            </if>
            <if test="solutionTypeId == null ">
                and a.solution_type_id in (
                WITH recursive tmp_type AS (
                SELECT * FROM solution_type WHERE parent_id is null
                UNION ALL
                SELECT a.*
                FROM solution_type a
                INNER JOIN tmp_type b ON a.parent_id = b.solution_type_id
                )
                SELECT solution_type_id FROM tmp_type
                )
            </if>
        </where>
    </select>

    <!-- 父级分类查询 -->
    <select id="parentList" parameterType="SolutionType" resultMap="SolutionTypeResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        WHERE a.del_flag = 0 AND a.parent_id IS NULL
        <if test="category != null and category != ''">
            and a.category = #{category}
        </if>
    </select>

    <select id="selectSolutionTypeBySolutionTypeId" parameterType="Long" resultMap="SolutionTypeResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        where a.solution_type_id = #{solutionTypeId}
    </select>

    <insert id="insertSolutionType" parameterType="SolutionType" useGeneratedKeys="true" keyProperty="solutionTypeId">
        insert into solution_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="solutionTypeName != null">solution_type_name,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="solutionTypeName != null">#{solutionTypeName},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateSolutionType" parameterType="SolutionType">
        update solution_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="solutionTypeName != null">solution_type_name = #{solutionTypeName},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where solution_type_id = #{solutionTypeId}
    </update>

    <delete id="deleteSolutionTypeBySolutionTypeId" parameterType="Long">
        delete from solution_type where solution_type_id = #{solutionTypeId}
    </delete>

    <delete id="deleteSolutionTypeBySolutionTypeIds" parameterType="String">
        delete from solution_type where solution_type_id in
        <foreach item="solutionTypeId" collection="array" open="(" separator="," close=")">
            #{solutionTypeId}
        </foreach>
    </delete>

    <!-- 典型案例分类列表 -->
    <select id="classicCaseList" parameterType="SolutionType" resultMap="SolutionTypeResult">
        SELECT
        a.solution_type_id,a.solution_type_name,a.category,count(b.classic_case_id) as total_count,s1.dict_label AS category_name
        FROM solution_type a
        LEFT JOIN (
            SELECT cc.classic_case_id,st.total_id as solution_type_id
            FROM classic_case cc
            LEFT JOIN (
                WITH recursive tmp_type AS (
                        SELECT a1.*,a1.solution_type_id as total_id FROM solution_type a1 WHERE a1.parent_id IS NULL
                        UNION ALL
                        SELECT a2.*,b2.total_id
                        FROM solution_type a2
                        INNER JOIN tmp_type b2 ON a2.parent_id = b2.solution_type_id
                )
                SELECT solution_type_id,total_id FROM tmp_type
            ) st ON st.solution_type_id = cc.solution_type_id
            WHERE cc.del_flag = 0
        ) b on a.solution_type_id = b.solution_type_id
        LEFT JOIN sys_dict_data s1 ON a.category = s1.dict_value AND s1.dict_type = 'solution_type_category'
        WHERE a.del_flag = 0 AND a.parent_id IS NULL
        <if test="category != null and category != ''">
            and a.category = #{category}
        </if>
        GROUP BY a.solution_type_id,a.solution_type_name,a.category,s1.dict_label
        ORDER BY a.solution_type_id
    </select>

    <!-- 解决方案分类列表 -->
    <select id="solutionList" parameterType="SolutionType" resultMap="SolutionTypeResult">
        SELECT
        a.solution_type_id,a.solution_type_name,a.category,count(b.solution_id) as total_count,s1.dict_label AS category_name
        FROM solution_type a
        LEFT JOIN (
                SELECT so.solution_id,st.total_id as solution_type_id
                FROM solution so
                LEFT JOIN (
                        WITH recursive tmp_type AS (
                                SELECT a1.*,a1.solution_type_id as total_id FROM solution_type a1 WHERE a1.parent_id IS NULL
                                UNION ALL
                                SELECT a2.*,b2.total_id
                                FROM solution_type a2
                                INNER JOIN tmp_type b2 ON a2.parent_id = b2.solution_type_id
                        )
                        SELECT solution_type_id,total_id FROM tmp_type
                ) st ON st.solution_type_id = so.solution_type_id
                WHERE so.del_flag = 0
        ) b on a.solution_type_id = b.solution_type_id
        LEFT JOIN sys_dict_data s1 ON a.category = s1.dict_value AND s1.dict_type = 'solution_type_category'
        WHERE a.del_flag = 0 AND a.parent_id IS NULL
        <if test="category != null and category != ''">
            and a.category = #{category}
        </if>
        GROUP BY a.solution_type_id,a.solution_type_name,a.category,s1.dict_label
        ORDER BY a.solution_type_id
    </select>

</mapper>