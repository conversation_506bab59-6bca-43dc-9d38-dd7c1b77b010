import request from "@/utils/request";

// 获取检测共享列表
export function getDetectionList(params) {
  return request({
    url: `/system/testingItem/leftJoinLabs`,
    method: "get",
    params,
  });
}
// 获取检测共享详情
export function getDetectionDetail(params) {
  return request({
    url: `/system/testingItem/${params}`,
    method: "get",
  });
}
// 获取实验室列表
export function getLaboratoryList(params) {
  return request({
    url: "system/laboratoryInfo/list",
    method: "get",
    params,
  });
}
// 查询实验室详情
// 导出一个函数，用于获取实验室详情
export function getLaboratoryDetail(params) {
  return request({
    url: `system/laboratoryInfo/${params}`,
    method: "get",
  });
}

// 获取证书列表
export function getCertificateList(params) {
  return request({
    url: "system/certificate/list",
    method: "get",
    params,
  });
}

// 服务共享-人才库列表
export function talentListData(params) {
  return request({
    url: "/system/info/list",
    method: "get",
    params,
  });
}

// 服务共享-人才入驻新增
export function talentAdd(data) {
  return request({
    url: "/system/info",
    method: "post",
    data,
  });
}
// 服务共享-人才入驻编辑
export function talentEdit(data) {
  return request({
    url: "/system/info",
    method: "put",
    data,
  });
}

// 服务共享-人才库详情
export function talentDetailData(id) {
  return request({
    url: `/system/info/${id}`,
    method: "get",
  });
}

// 服务共享-用工信息列表
export function employListData(params) {
  return request({
    url: "/system/jobInfo/list",
    method: "get",
    params,
  });
}

// 服务共享-用工信息-用户个人列表
export function employUserListData(params) {
  return request({
    url: "/system/jobInfo/user/list",
    method: "get",
    params,
  });
}

// 服务共享-用工信息详情
export function employDetailData(id) {
  return request({
    url: `/system/jobInfo/${id}`,
    method: "get",
  });
}

// 服务共享-用工信息发布
export function employAddData(data) {
  return request({
    url: `/system/jobInfo`,
    method: "post",
    data,
  });
}
// 技能鉴定申请
export function applySkill(data) {
  return request({
    url: `/system/talentCertificationApplication`,
    method: "post",
    data,
  });
}
// 培训报名申请
export function applyTrain(data) {
  return request({
    url: `/system/talentTrainingApplication`,
    method: "post",
    data,
  });
}
