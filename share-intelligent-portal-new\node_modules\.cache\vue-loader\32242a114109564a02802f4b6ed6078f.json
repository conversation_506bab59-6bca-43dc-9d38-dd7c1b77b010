{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\resource\\insDetail.vue?vue&type=style&index=0&id=629d7490&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\resource\\insDetail.vue", "mtime": 1750311963001}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["insDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "insDetail.vue", "sourceRoot": "src/views/resource", "sourcesContent": ["<template>\r\n  <div class=\"resource-hall-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"resource-hall-detail-banner\">\r\n      <img\r\n        src=\"../../assets/resourceHall/resourceHallDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"resource-hall-detail-title-box\">\r\n      <div class=\"resource-hall-detail-divider\"></div>\r\n      <div class=\"resource-hall-detail-title\">设备详情</div>\r\n      <div class=\"resource-hall-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"resource-hall-detail-content\">\r\n      <div class=\"resource-hall-detail-box\">\r\n        <div class=\"resource-hall-detail-box-title\">\r\n          {{ data.name }}\r\n        </div>\r\n        <div class=\"resource-hall-detail-headline\">\r\n          <div class=\"headline-content\">\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">所属单位：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.institution }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">规格型号：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.specification }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">生产国别：</div>\r\n              <div class=\"item-content\">{{ data.country }}</div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">购置日期：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.acquireTime }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-btn\">\r\n              <el-button\r\n                v-if=\"showBtn\"\r\n                class=\"headline-btn-style intention-btn\"\r\n                @click=\"goIntention\"\r\n                >我有意向</el-button\r\n              >\r\n              <el-button\r\n                @click=\"goChat\"\r\n                class=\"headline-btn-style communication-btn\"\r\n                icon=\"el-icon-chat-dot-round\"\r\n                >在线沟通</el-button\r\n              >\r\n            </div>\r\n          </div>\r\n          <div class=\"headline-img\">\r\n            <img v-if=\"data.picUrl\" :src=\"data.picUrl\" alt=\"\" />\r\n            <img\r\n              v-else\r\n              src=\"../../assets/resourceHall/resourceHallDetailBanner.png\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"resource-hall-detail-description\">\r\n          <div class=\"description-title-box\">\r\n            <div class=\"description-divider\"></div>\r\n            <div class=\"description-title\">成果描述</div>\r\n          </div>\r\n          <div class=\"description-content\">\r\n            <div v-html=\"data.detail\" class=\"description-text ql-editor\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { insDetail, getCheckSubmit } from \"@/api/purchaseSales\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { getCompanyInfoByLoginInfo } from \"@/api/apathy\";\r\nimport { mapGetters } from \"vuex\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n      showBtn: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      insDetail(this.$route.query.id)\r\n        .then((res) => {\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          if (!this.token) {\r\n            this.showBtn = true;\r\n          } else {\r\n            this.getInfo();\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 判断此资源是不是自己发布的\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (this.data.createById === res.user.userId) {\r\n          this.showBtn = false;\r\n        } else {\r\n          this.showBtn = true;\r\n        }\r\n      });\r\n    },\r\n    goChat() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      let routeData = this.$router.resolve({\r\n        path: \"/user/im\",\r\n        query: {\r\n          userId: this.data.createImById,\r\n        },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳到我有意向页面\r\n    goIntention() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      // 是否加入企业\r\n      getCompanyInfoByLoginInfo()\r\n        .then((res) => {\r\n          if (res.data) {\r\n            // 是否对此资源提交过意向\r\n            getCheckSubmit({\r\n              id: this.$route.query.id,\r\n              resourceType: \"resource_supply\",\r\n            })\r\n              .then((res) => {\r\n                this.loading = false;\r\n                // true 提交过  false未提交过\r\n                if (res.data) {\r\n                  this.$message({\r\n                    type: \"warning\",\r\n                    message: \"已经提交过了哦！\",\r\n                  });\r\n                } else {\r\n                  let routeData = this.$router.resolve({\r\n                    path: \"/addIntention\",\r\n                    query: {\r\n                      id: this.$route.query.id,\r\n                      type: \"resource_supply\",\r\n                      title: this.data.supplyName,\r\n                    },\r\n                  });\r\n                  window.open(routeData.href, \"_blank\");\r\n                }\r\n              })\r\n              .catch(() => {\r\n                this.loading = false;\r\n              });\r\n          } else {\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"warning\",\r\n              message: \"必须加入企业才可提交我有意向\",\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.resource-hall-detail-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .resource-hall-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .resource-hall-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .resource-hall-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .resource-hall-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .resource-hall-detail-content {\r\n    background: #f4f5f9;\r\n    padding-bottom: 70px;\r\n    .resource-hall-detail-box {\r\n      width: 1200px;\r\n      background: #fff;\r\n      margin: 0 auto;\r\n      padding: 60px 60px 192px;\r\n      .resource-hall-detail-box-title {\r\n        width: 100%;\r\n        font-size: 32px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        word-wrap: break-word;\r\n      }\r\n      .resource-hall-detail-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-top: 40px;\r\n        padding-bottom: 40px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        .headline-content {\r\n          flex: 1;\r\n          .headline-content-item {\r\n            display: flex;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            line-height: 32px;\r\n            .item-title {\r\n              width: 80px;\r\n              color: #666;\r\n            }\r\n            .item-content {\r\n              flex: 1;\r\n              max-width: 560px;\r\n              color: #333;\r\n              word-wrap: break-word;\r\n            }\r\n          }\r\n          .headline-content-btn {\r\n            padding-top: 112px;\r\n            .headline-btn-style {\r\n              width: 100px;\r\n              height: 32px;\r\n              border-radius: 4px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              padding: 8px 11px;\r\n            }\r\n            .intention-btn {\r\n              background: #21c9b8;\r\n              color: #fff;\r\n            }\r\n            .communication-btn {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        .headline-img {\r\n          width: 400px;\r\n          height: 240px;\r\n          margin-left: 20px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .resource-hall-detail-description {\r\n      padding-top: 39px;\r\n      .description-title-box {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-bottom: 40px;\r\n        .description-divider {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .description-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n      .description-content {\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.resource-hall-detail-container {\r\n  .description-content {\r\n    .description-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}