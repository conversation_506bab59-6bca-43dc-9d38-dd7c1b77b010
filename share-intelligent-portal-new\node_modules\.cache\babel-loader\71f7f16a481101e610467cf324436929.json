{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index(copy).vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index(copy).vue", "mtime": 1750311963019}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_solution", "require", "_data", "_classicCase", "_default", "exports", "default", "data", "fit", "loading", "form", "keywords", "formInfo", "caseType", "caseTypeList", "pageNum", "pageSize", "total", "allAmounts", "solutionTypeList", "value", "label", "flag", "solutionListData", "created", "methods", "getList", "id", "_this", "params", "grounding", "parentId", "searchStr", "typeId", "solutionList", "then", "res", "code", "console", "log", "rows", "changeRadio", "onSearch", "handleSizeChange", "handleCurrentChange", "goHome", "$router", "push", "path", "initData", "_this2", "type", "solutionType", "getTypeName", "list", "_this3", "solutionTypeName", "i", "length", "k", "typeName", "key", "getItemData", "item", "goDetail", "routeData", "resolve", "query", "window", "open", "href"], "sources": ["src/views/solution/index(copy).vue"], "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/solution/solution.png\" alt=\"\" />\r\n      <div class=\"bannerTitle\">解决方案</div>\r\n      <div class=\"bannerDesc\">\r\n        沉淀众多优秀解决方案，提供适用于不同行业、领域的数字化转型服务方案\r\n      </div>\r\n    </div>\r\n    <div>\r\n      <div class=\"activity-title-content\">\r\n        <!-- <div class=\"activity-title-box\">\r\n          <div class=\"activity-divider\"></div>\r\n          <div class=\"activity-title\">解决方案</div>\r\n          <div class=\"activity-divider\"></div>\r\n        </div> -->\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.keywords\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"solutionContent\">\r\n        <div class=\"solutionLeft\">\r\n          <div\r\n            class=\"itemType\"\r\n            :class=\"flag === '全部' ? 'itemTypeHover' : ''\"\r\n            @click=\"getItemData({ typeName: '全部' })\"\r\n          >\r\n            全部（{{ allAmounts }}）\r\n          </div>\r\n          <div\r\n            class=\"itemType\"\r\n            :class=\"flag === item.typeName ? 'itemTypeHover' : ''\"\r\n            v-for=\"item in solutionTypeList\"\r\n            :key=\"item.id\"\r\n            @click=\"getItemData(item)\"\r\n          >\r\n            {{ item.typeName }}（{{ item.value }}）\r\n          </div>\r\n        </div>\r\n        <div class=\"solutionRight\">\r\n          <div style=\"display: flex; flex-wrap: wrap\" v-loading=\"loading\">\r\n            <div\r\n              class=\"itemContent\"\r\n              v-for=\"item in solutionListData\"\r\n              :key=\"item.id\"\r\n              @click=\"goDetail(item.id)\"\r\n            >\r\n              <!-- <div class=\"content_left\">\r\n              <img src=\"\" alt=\"\">\r\n            </div> -->\r\n              <div class=\"content_right\">\r\n                <div class=\"title\">{{ item.name }}</div>\r\n                <div class=\"desc\">\r\n                  {{ item.introduction }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"activity-page-end\">\r\n            <!-- <el-button class=\"activity-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            > -->\r\n            <el-pagination\r\n              v-if=\"solutionListData && solutionListData.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"activity-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import { getActivityList } from \"@/api/purchaseSales\";\r\nimport { solutionType, solutionTypeName, solutionList } from \"@/api/solution\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { caseList } from \"@/api/classicCase\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      loading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        caseType: \"\", // 案例类型\r\n      },\r\n      caseTypeList: [],\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      allAmounts: 0,\r\n      solutionTypeList: [\r\n        {\r\n          value: \"0\",\r\n          label: \"全部\",\r\n        },\r\n        {\r\n          value: \"1\",\r\n          label: \"节能减排\",\r\n        },\r\n        {\r\n          value: \"2\",\r\n          label: \"低碳认证\",\r\n        },\r\n        {\r\n          value: \"3\",\r\n          label: \"数据核算\",\r\n        },\r\n        {\r\n          value: \"4\",\r\n          label: \"中和服务\",\r\n        },\r\n        {\r\n          value: \"5\",\r\n          label: \"星碳培训\",\r\n        },\r\n        {\r\n          value: \"6\",\r\n          label: \"绿色会议\",\r\n        },\r\n        {\r\n          value: \"7\",\r\n          label: \"数据建模\",\r\n        },\r\n        {\r\n          value: \"8\",\r\n          label: \"资产管理\",\r\n        },\r\n      ],\r\n      flag: \"全部\",\r\n      solutionListData: [],\r\n    };\r\n  },\r\n  created() {\r\n    // this.initData();\r\n    // this.getDictsList(\"activity_type\", \"activityTypeList\");\r\n    // this.search();\r\n  },\r\n  methods: {\r\n    getList(id) {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        grounding: 0,\r\n        parentId: 1,\r\n        searchStr: this.form.keywords,\r\n        typeId: id,\r\n      };\r\n      solutionList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res.rows, \"-------------\");\r\n          this.solutionListData = res.rows;\r\n          if (!id) {\r\n            this.allAmounts = res.total;\r\n          }\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    initData() {\r\n      let params = {\r\n        type: \"industry\",\r\n      };\r\n      solutionType(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.getTypeName(res.data);\r\n        }\r\n      });\r\n      // getDicts(\"case_industry\").then((res) => {\r\n      //   const { code, data = [] } = res;\r\n      //   if (code === 200) {\r\n      //     this.caseTypeList = data;\r\n      //     this.getCaseList();\r\n      //   }\r\n      // });\r\n    },\r\n    getTypeName(list) {\r\n      let params = {\r\n        parentId: 1,\r\n      };\r\n      solutionTypeName(params).then((res) => {\r\n        if (res.code === 200) {\r\n          for (var i = 0; i < res.rows.length; i++) {\r\n            for (var k = 0; k < res.rows.length; k++) {\r\n              if (res.rows[i].typeName == list[k].key) {\r\n                res.rows[i].value = list[k].value;\r\n              }\r\n            }\r\n          }\r\n          this.solutionTypeList = res.rows;\r\n          this.getList();\r\n        }\r\n      });\r\n    },\r\n    getItemData(item) {\r\n      this.flag = item.typeName;\r\n      this.getList(item.id);\r\n    },\r\n    goDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/solutionDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #ffffff;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 500px;\r\n    position: relative;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    .bannerTitle {\r\n      position: absolute;\r\n      top: 161px;\r\n      left: 24%;\r\n      font-size: 50px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n    }\r\n    .bannerDesc {\r\n      position: absolute;\r\n      top: 249px;\r\n      left: 24%;\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    // padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      margin-top: 40px;\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    padding: 8% 0;\r\n    background: #fff;\r\n    margin-top: 25px;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .solutionContent {\r\n    width: 1200px;\r\n    background-color: rgb(252, 252, 252);\r\n    margin: 40px auto;\r\n    display: flex;\r\n    .solutionLeft {\r\n      width: 172px;\r\n      height: 100%;\r\n      overflow-y: auto;\r\n      .itemType {\r\n        width: 100%;\r\n        height: 24px;\r\n        margin-top: 42px;\r\n        padding-left: 30px;\r\n        cursor: pointer;\r\n      }\r\n      .itemTypeHover {\r\n        border-left: 4px solid #21c9b8;\r\n        color: #21c9b8;\r\n      }\r\n      .itemType:nth-child(1) {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n    .solutionRight {\r\n      width: 1000px;\r\n      margin-left: 20px;\r\n      // display: flex;\r\n      // flex-wrap: wrap;\r\n      margin: 0 auto;\r\n      .itemContent {\r\n        width: 490px;\r\n        height: 190px;\r\n        padding: 30px 42px 30px 26px;\r\n        display: flex;\r\n        background: #ffffff;\r\n        margin-left: 20px;\r\n        margin-top: 20px;\r\n        cursor: pointer;\r\n      }\r\n      .itemContent:hover {\r\n        box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);\r\n        border-radius: 2px;\r\n      }\r\n      .itemContent:nth-child(2n + 1) {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n    // .content_left {\r\n    //   width: 130px;\r\n    //   height: 130px;\r\n    //   background: #ccc;\r\n    // }\r\n    .content_right {\r\n      width: 402px;\r\n      height: 130px;\r\n      margin-left: 20px;\r\n      .title {\r\n        font-size: 20px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #222222;\r\n        margin-top: 20px;\r\n      }\r\n      .desc {\r\n        margin-top: 24px;\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #65676a;\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    padding: 24px 0 60px;\r\n    .activity-page-btn {\r\n      width: 82px;\r\n      height: 32px;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      border: 1px solid #d9d9d9;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #333;\r\n      line-height: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAiGA,IAAAA,SAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;AAAA,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAKA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;MACAC,OAAA;MACAC,IAAA;QACAC,QAAA;MACA;MACAC,QAAA;QACAC,QAAA;MACA;MACAC,YAAA;MACAP,IAAA;MACAQ,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,UAAA;MACAC,gBAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAC,IAAA;MACAC,gBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA;IACA;EAAA,CACA;EACAC,OAAA;IACAC,OAAA,WAAAA,QAAAC,EAAA;MAAA,IAAAC,KAAA;MACA,KAAAnB,OAAA;MACA,IAAAoB,MAAA;QACAd,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA,QAAA;QACAc,SAAA;QACAC,QAAA;QACAC,SAAA,OAAAtB,IAAA,CAAAC,QAAA;QACAsB,MAAA,EAAAN;MACA;MACA,IAAAO,sBAAA,EAAAL,MAAA,EAAAM,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAC,OAAA,CAAAC,GAAA,CAAAH,GAAA,CAAAI,IAAA;UACAZ,KAAA,CAAAL,gBAAA,GAAAa,GAAA,CAAAI,IAAA;UACA,KAAAb,EAAA;YACAC,KAAA,CAAAV,UAAA,GAAAkB,GAAA,CAAAnB,KAAA;UACA;UACAW,KAAA,CAAAX,KAAA,GAAAmB,GAAA,CAAAnB,KAAA;UACAW,KAAA,CAAAnB,OAAA;QACA;MACA;IACA;IACAgC,WAAA,WAAAA,YAAA;MACA,KAAAC,QAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA3B,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAA0B,QAAA;IACA;IACAE,mBAAA,WAAAA,oBAAA7B,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAW,OAAA;IACA;IACAgB,QAAA,WAAAA,SAAA;MACA,KAAA3B,OAAA;MACA,KAAAW,OAAA;IACA;IACAmB,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,IAAArB,MAAA;QACAsB,IAAA;MACA;MACA,IAAAC,sBAAA,EAAAvB,MAAA,EAAAM,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAa,MAAA,CAAAG,WAAA,CAAAjB,GAAA,CAAA7B,IAAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA8C,WAAA,WAAAA,YAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,IAAA1B,MAAA;QACAE,QAAA;MACA;MACA,IAAAyB,0BAAA,EAAA3B,MAAA,EAAAM,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,SAAAoB,CAAA,MAAAA,CAAA,GAAArB,GAAA,CAAAI,IAAA,CAAAkB,MAAA,EAAAD,CAAA;YACA,SAAAE,CAAA,MAAAA,CAAA,GAAAvB,GAAA,CAAAI,IAAA,CAAAkB,MAAA,EAAAC,CAAA;cACA,IAAAvB,GAAA,CAAAI,IAAA,CAAAiB,CAAA,EAAAG,QAAA,IAAAN,IAAA,CAAAK,CAAA,EAAAE,GAAA;gBACAzB,GAAA,CAAAI,IAAA,CAAAiB,CAAA,EAAArC,KAAA,GAAAkC,IAAA,CAAAK,CAAA,EAAAvC,KAAA;cACA;YACA;UACA;UACAmC,MAAA,CAAApC,gBAAA,GAAAiB,GAAA,CAAAI,IAAA;UACAe,MAAA,CAAA7B,OAAA;QACA;MACA;IACA;IACAoC,WAAA,WAAAA,YAAAC,IAAA;MACA,KAAAzC,IAAA,GAAAyC,IAAA,CAAAH,QAAA;MACA,KAAAlC,OAAA,CAAAqC,IAAA,CAAApC,EAAA;IACA;IACAqC,QAAA,WAAAA,SAAArC,EAAA;MACA,IAAAsC,SAAA,QAAAnB,OAAA,CAAAoB,OAAA;QACAlB,IAAA;QACAmB,KAAA;UAAAxC,EAAA,EAAAA;QAAA;MACA;MACAyC,MAAA,CAAAC,IAAA,CAAAJ,SAAA,CAAAK,IAAA;IACA;EACA;AACA", "ignoreList": []}]}