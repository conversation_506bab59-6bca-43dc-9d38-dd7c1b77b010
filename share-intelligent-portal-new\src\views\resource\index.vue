<template>
  <div class="activity-container">
    <div class="activity-banner">
      <img src="../../assets/resource/resourceBanner.png" alt="" />
    </div>
    <div v-loading="loading">
      <div class="activity-title-content">
        <div class="activity-title-box">
          <div class="activity-divider"></div>
          <div class="activity-title">链资源</div>
          <div class="activity-divider"></div>
        </div>
        <div class="activity-search-box">
          <el-form ref="form" class="activity-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.name"
                placeholder="请输入搜索内容"
                class="activity-search-input"
              >
                <el-button
                  slot="append"
                  class="activity-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="activity-info-content">
        <div class="activity-search-type-box">
          <el-form ref="formInfo" :model="formInfo">
            <div class="activity-search-line">
              <el-form-item label="资源类型" class="activity-search-line-item">
                <el-radio-group
                  v-model="formInfo.supplyType"
                  class="activity-search-radio"
                  @input="changeRadio"
                >
                  <el-radio-button label="">全部</el-radio-button>
                  <el-radio-button
                    v-for="(item, index) in resourceTypeList"
                    :key="index"
                    :label="item.dictValue"
                    >{{ item.dictLabel }}</el-radio-button
                  >
                </el-radio-group>
              </el-form-item>
              <el-form-item
                v-if="
                  formInfo.supplyType !== 4 &&
                  formInfo.supplyType !== 6 &&
                  formInfo.supplyType !== 7
                "
                label="技术类别"
                class="activity-search-line-item"
              >
                <el-radio-group
                  v-model="formInfo.technologyType"
                  class="activity-search-radio"
                  @input="changeRadio"
                >
                  <el-radio-button label="">全部</el-radio-button>
                  <el-radio-button
                    v-for="(item, index) in technologyTypeList"
                    :key="index"
                    :label="item.dictLabel"
                    >{{ item.dictLabel }}</el-radio-button
                  >
                </el-radio-group>
              </el-form-item>
              <el-form-item
                v-if="formInfo.supplyType === 4"
                label="技术类别"
                class="activity-search-line-item"
              >
                <el-radio-group
                  v-model="techniqueTypeName"
                  class="activity-search-radio"
                  @input="changeRadio"
                >
                  <el-radio-button label="">全部</el-radio-button>
                  <el-radio-button
                    v-for="(item, index) in technologyTypeList2"
                    :key="index"
                    :label="item.dictLabel"
                    >{{ item.dictLabel }}</el-radio-button
                  >
                </el-radio-group>
              </el-form-item>
              <el-form-item
                label="成果阶段"
                class="activity-search-line-item"
                v-if="
                  formInfo.supplyType !== 4 &&
                  formInfo.supplyType !== 6 &&
                  formInfo.supplyType !== 7
                "
              >
                <el-radio-group
                  v-model="formInfo.productStage"
                  class="activity-search-radio"
                  @input="changeRadio"
                >
                  <el-radio-button label="">全部</el-radio-button>
                  <el-radio-button
                    v-for="(item, index) in achievementList"
                    :key="index"
                    :label="item.dictValue"
                    >{{ item.dictLabel }}</el-radio-button
                  >
                </el-radio-group>
              </el-form-item>
              <el-form-item
                label="合作方式"
                class="activity-search-line-item"
                v-if="
                  formInfo.supplyType !== 4 &&
                  formInfo.supplyType !== 6 &&
                  formInfo.supplyType !== 7
                "
              >
                <el-radio-group
                  v-model="formInfo.cooperationMode"
                  class="activity-search-radio"
                  @input="changeRadio"
                >
                  <el-radio-button label="">全部</el-radio-button>
                  <el-radio-button
                    v-for="(item, index) in cooperationModeList"
                    :key="index"
                    :label="item.dictValue"
                    >{{ item.dictLabel }}</el-radio-button
                  >
                </el-radio-group>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div
          v-if="
            formInfo.supplyType !== 4 &&
            formInfo.supplyType !== 6 &&
            formInfo.supplyType !== 7
          "
        >
          <div
            v-for="(item, index) in data"
            :key="index"
            class="activity-list-item"
            @click="goResourceDetail(item.id)"
          >
            <div class="list-item-content">
              <div class="list-item-img">
                <img
                  v-if="
                    item.productPhoto &&
                    item.productPhoto.length > 0 &&
                    item.productPhoto[0].url
                  "
                  :src="item.productPhoto[0].url"
                  alt=""
                />
                <img
                  v-else
                  src="../../assets/purchaseSales/resourceDefault.png"
                  alt=""
                />
              </div>
              <div class="list-item-info" style="margin: auto 0">
                <div class="list-item-title">
                  {{ item.supplyName }}
                </div>
                <div
                  style="
                    margin-top: 10px;
                    margin-left: 10px;
                    display: flex;
                    align-items: center;
                  "
                >
                  <div style="color: rgb(77, 77, 78)">应用领域:</div>
                  <div
                    style="margin-left: 10px"
                    v-if="
                      item.applicationArea && item.applicationArea.length > 0
                    "
                  >
                    <el-tag
                      class="tagStyle"
                      v-for="(val, num) in item.applicationArea"
                      :key="num"
                      >{{ val }}</el-tag
                    >
                  </div>
                  <div v-else style="margin-left: 10px">
                    <el-tag>暂无</el-tag>
                  </div>
                </div>
                <div
                  style="
                    margin-top: 10px;
                    margin-left: 10px;
                    display: flex;
                    align-items: center;
                  "
                >
                  <div style="color: rgb(77, 77, 78)">技术类别:</div>
                  <div
                    style="margin-left: 10px"
                    v-if="item.technologyType && item.technologyType.length > 0"
                  >
                    <el-tag
                      class="tagStyle"
                      type="success"
                      v-for="(val, num) in item.technologyType"
                      :key="num"
                      >{{ val }}</el-tag
                    >
                  </div>
                  <div v-else style="margin-left: 10px">
                    <el-tag type="success">暂无</el-tag>
                  </div>
                </div>
                <div
                  v-if="formInfo.supplyType === 6"
                  style="
                    margin-top: 10px;
                    margin-left: 10px;
                    display: flex;
                    align-items: center;
                  "
                >
                  <div style="color: rgb(77, 77, 78)">规格型号:</div>
                  <div style="margin-left: 10px" v-if="item.specification">
                    <el-tag class="tagStyle" type="success">{{
                      item.specification
                    }}</el-tag>
                  </div>
                  <div v-else style="margin-left: 10px">
                    <el-tag type="success">暂无</el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="formInfo.supplyType === 6">
          <div
            v-for="(item, index) in data"
            :key="index"
            class="activity-list-item"
            @click="goInsDetail(item.id)"
          >
            <div class="list-item-content">
              <div class="list-item-img">
                <img v-if="item.picUrl" :src="item.picUrl" alt="" />
                <!-- <img
                  v-else
                  src="../../assets/purchaseSales/resourceDefault.png"
                  alt=""
                /> -->
              </div>
              <div class="list-item-info">
                <div class="list-item-title">
                  {{ item.name }}
                </div>
                <div
                  style="
                    margin-top: 10px;
                    margin-left: 10px;
                    display: flex;
                    align-items: center;
                  "
                >
                  <div style="color: rgb(77, 77, 78)">规格型号:</div>
                  <div style="margin-left: 10px" v-if="item.specification">
                    <el-tag class="tagStyle" type="success">{{
                      item.specification
                    }}</el-tag>
                  </div>
                  <div v-else style="margin-left: 10px">
                    <el-tag type="success">暂无</el-tag>
                  </div>
                </div>
                <!-- <div class="list-item-text">
                {{ item.activityOverview }}
              </div>
              <div class="list-item-time">{{ item.createTimeStr }}</div> -->
              </div>
            </div>
          </div>
        </div>
        <div v-if="formInfo.supplyType === 7">
          <div
            v-for="(item, index) in data"
            :key="index"
            class="activity-list-item"
            @click="goLabDetail(item.id)"
          >
            <div class="list-item-content">
              <div class="list-item-img">
                <img v-if="item.picUrl" :src="item.picUrl" alt="" />
              </div>
              <div class="list-item-info">
                <div class="list-item-title">
                  {{ item.name }}
                </div>
                <div
                  style="
                    margin-top: 10px;
                    margin-left: 10px;
                    display: flex;
                    align-items: center;
                  "
                >
                  <div style="color: rgb(77, 77, 78)">行业领域:</div>
                  <div style="margin-left: 10px" v-if="item.industry">
                    <el-tag class="tagStyle" type="success">{{
                      item.industry
                    }}</el-tag>
                  </div>
                  <div v-else style="margin-left: 10px">
                    <el-tag type="success">暂无</el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="formInfo.supplyType === 4">
          <div class="expert-library-list">
            <div
              v-for="(item, index) in data"
              :key="index"
              class="list-item-content"
              @click="goExpertLibrary(item.id)"
            >
              <div class="list-item-box">
                <div class="item-headline">
                  <div class="item-title">
                    {{ item.expertName }}
                  </div>
                </div>
                <div class="expert-library-label">
                  <div
                    v-for="(val, index1) in item.techniqueTypeName"
                    :key="index1"
                    class="library-label-item"
                  >
                    <span v-if="index1 < 2" class="expert-library-type">{{
                      `#${val}`
                    }}</span>
                    <span v-else>…</span>
                  </div>
                </div>
                <div class="expert-library-box">
                  {{ item.synopsis }}
                </div>
              </div>
              <div class="list-item-img">
                <img v-if="item.headPortrait" :src="item.headPortrait" alt="" />
                <img
                  v-else
                  src="../../assets/expertLibrary/defaultImg.png"
                  alt=""
                />
              </div>
            </div>
          </div>
        </div>
        <div class="activity-page-end">
          <el-button class="activity-page-btn" @click="goHome">首页</el-button>
          <el-pagination
            v-if="data && data.length > 0"
            background
            layout="prev, pager, next"
            class="activity-pagination"
            :page-size="pageSize"
            :current-page="pageNum"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getResourceHallList,
  getExpertList,
  insList,
  laboratoryList,
} from "@/api/purchaseSales";
import { getDicts } from "@/api/system/dict/data";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      loading: false,
      form: {
        name: "", //搜索内容
      },
      formInfo: {
        supplyType: "", // 资源类型
        technologyType: "", // 技术类别
        productStage: "", // 成果
        cooperationMode: "", // 合作方式
      },
      techniqueTypeName: "", // 专家-技术类别
      resourceTypeList: [
        {
          dictLabel: "成果",
          dictValue: 1,
        },
        {
          dictLabel: "产品",
          dictValue: 2,
        },
        {
          dictLabel: "服务",
          dictValue: 3,
        },
        {
          dictLabel: "专家",
          dictValue: 4,
        },
        {
          dictLabel: "设备",
          dictValue: 6,
        },
        {
          dictLabel: "实验室",
          dictValue: 7,
        },
      ],
      technologyTypeList: [
        {
          dictLabel: "国产化替代",
          dictValue: 1,
        },
        {
          dictLabel: "机器替人",
          dictValue: 2,
        },
        {
          dictLabel: "管理提升",
          dictValue: 3,
        },
        {
          dictLabel: "质量提升",
          dictValue: 4,
        },
        {
          dictLabel: "灭菌消杀",
          dictValue: 5,
        },
        {
          dictLabel: "新材料",
          dictValue: 6,
        },
        {
          dictLabel: "绿色星碳",
          dictValue: 7,
        },
      ],
      technologyTypeList2: [
        {
          dictLabel: "国产化替代",
          dictValue: 1,
        },
        {
          dictLabel: "新材料",
          dictValue: 2,
        },
        {
          dictLabel: "机器替人",
          dictValue: 3,
        },
        {
          dictLabel: "管理提升",
          dictValue: 4,
        },
        {
          dictLabel: "技术提升",
          dictValue: 5,
        },
        {
          dictLabel: "绿色星碳",
          dictValue: 6,
        },
        {
          dictLabel: "集中采购",
          dictValue: 7,
        },
      ],
      achievementList: [
        {
          dictLabel: "正在研发",
          dictValue: 1,
        },
        {
          dictLabel: "已有样品",
          dictValue: 2,
        },
        {
          dictLabel: "通过中试",
          dictValue: 3,
        },
        {
          dictLabel: "已量产",
          dictValue: 4,
        },
      ],
      cooperationModeList: [
        {
          dictLabel: "双方协商",
          dictValue: 1,
        },
        {
          dictLabel: "作价入股",
          dictValue: 2,
        },
        {
          dictLabel: "合作转换",
          dictValue: 3,
        },
        {
          dictLabel: "专利许可",
          dictValue: 4,
        },
      ],
      data: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  created() {
    // this.getDictsList("activity_type", "activityTypeList");
    const flag = this.$route.query.flag;
    if (flag) {
      this.formInfo.supplyType = Number(flag);
      if (flag == 4) {
        this.searchExpert();
      }
      if (flag == 6) {
        this.getInsList();
      }
      if (flag == 7) {
        this.getLaboratoryList();
      }
    } else {
      this.search();
    }
  },
  methods: {
    search() {
      this.loading = true;
      getResourceHallList({
        ...this.form,
        ...this.formInfo,
        auditStatus: 2,
        displayStatus: 1,
        pageNum: this.pageNum,
      })
        .then((res) => {
          console.log(res);
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
          console.log(res);
          this.loading = false;
          let { rows, total } = res || [];
          this.data = rows;
          this.data.forEach((item) => {
            item.productPhoto = item.productPhoto
              ? JSON.parse(item.productPhoto)
              : [];
            item.applicationArea = item.applicationArea
              ? item.applicationArea.split(",")
              : "";
            item.technologyType = item.technologyType
              ? item.technologyType.split(",")
              : "";
          });
          this.total = total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    searchExpert() {
      this.loading = true;
      getExpertList({
        keywords: this.form.name,
        techniqueTypeName: this.techniqueTypeName,
        pageNum: this.pageNum,
      })
        .then((res) => {
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));

          this.loading = false;
          let { rows, total } = res || [];
          this.data = rows;
          this.data.forEach((item) => {
            item.techniqueTypeName = item.techniqueTypeName
              ? item.techniqueTypeName.split(",")
              : [];
          });
          this.total = total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    getInsList() {
      let params = {
        ...this.form,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      insList(params)
        .then((res) => {
          this.data = res.rows;
          this.total = res.total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    getLaboratoryList() {
      let params = {
        ...this.form,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      };
      laboratoryList(params)
        .then((res) => {
          this.data = res.rows;
          this.total = res.total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 字典
    getDictsList(code, propertyName) {
      getDicts(code).then((res) => {
        this[propertyName] = res.data || [];
      });
    },
    changeRadio() {
      if (
        this.formInfo.supplyType === "" ||
        this.formInfo.supplyType === 1 ||
        this.formInfo.supplyType === 2 ||
        this.formInfo.supplyType === 3
      ) {
        this.pageNum = 1;
        this.pageSize = 10;
        this.search();
      }
      if (this.formInfo.supplyType === 4) {
        this.pageNum = 1;
        this.pageSize = 10;
        this.searchExpert();
      }
      if (this.formInfo.supplyType === 6) {
        this.pageNum = 1;
        this.pageSize = 10;
        this.getInsList();
      }
      if (this.formInfo.supplyType === 7) {
        this.pageNum = 1;
        this.pageSize = 10;
        this.getLaboratoryList();
      }
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.changeRadio();
    },
    onSearch() {
      this.pageNum = 1;
      this.changeRadio();
    },
    // 跳转资源详情
    goResourceDetail(id) {
      let routeData = this.$router.resolve({
        path: "/resourceHallDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    goHome() {
      this.$router.push({ path: "/index" });
    },
    // 跳转到专家详情页面
    goExpertLibrary(id) {
      let routeData = this.$router.resolve({
        path: "/expertDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    goInsDetail(id) {
      let routeData = this.$router.resolve({
        path: "/insDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    goLabDetail(id) {
      let routeData = this.$router.resolve({
        path: "/labDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
  },
};
</script>

<style lang="scss" scoped>
.activity-container {
  width: 100%;
  background: #f4f5f9;
  .activity-banner {
    width: 100%;
    height: 50vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .activity-title-content {
    width: 100%;
    background-color: #fff;
    padding-bottom: 18px;
    .activity-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .activity-title {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .activity-divider {
        width: 48px;
        height: 4px;
        background: #21c9b8;
      }
    }
    .activity-search-box {
      .activity-search-form {
        text-align: center;
        .activity-search-input {
          width: 792px;
          height: 54px;
          .activity-search-btn {
            width: 100px;
          }
        }
      }
    }
  }
  .activity-info-content {
    width: 1200px;
    margin: 40px auto 0;
    .activity-search-type-box {
      background: #fff;
      margin-bottom: -7px;
      .activity-search-line {
        padding: 14px 24px;
        .activity-search-line-item {
          margin-bottom: 0;
        }
        & + .activity-search-line {
          border-top: 1px solid #f5f5f5;
        }
      }
    }
    .activity-list-item {
      width: 100%;
      background: #fff;
      border-radius: 12px;
      margin-top: 24px;
      .list-item-content {
        display: flex;
        padding: 24px 32px;
        cursor: pointer;
        .list-item-img {
          width: 230px;
          height: 164px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 5px;
          }
        }
        .list-item-info {
          // margin: auto 0;
          padding-left: 24px;
          font-family: PingFangSC-Regular, PingFang SC;
          .list-item-title {
            width: 806px;
            // height: 24px;
            text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
            white-space: nowrap; /*让文字不换行*/
            overflow: hidden; /*超出要隐藏*/
            font-size: 24px;
            font-weight: 500;
            color: #323233;
            // line-height: 24px;
            margin: 8px 0 24px;
            word-wrap: break-word;
          }
          .list-item-text {
            width: 806px;
            height: 60px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            font-size: 16px;
            color: #666;
            line-height: 30px;
            word-wrap: break-word;
          }
          .list-item-time {
            color: #999;
            line-height: 14px;
            margin-top: 24px;
          }
          .tagStyle {
            margin-left: 20px;
          }
          .tagStyle:nth-child(1) {
            margin-left: 0;
          }
        }
        &:hover {
          .list-item-title {
            color: #21c9b8;
          }
        }
      }
    }
    .activity-page-end {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      padding: 24px 0 60px;
      .activity-page-btn {
        width: 82px;
        height: 32px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        line-height: 10px;
      }
    }
  }
}
</style>

<style lang="scss">
.activity-container {
  .activity-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #21c9b8;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #fff;
      line-height: 24px;
    }
  }
  .activity-search-line {
    .el-form-item__label {
      width: 88px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #999;
      padding-right: 32px;
      text-align: left;
    }
    .activity-search-radio {
      width: 1050px;
      margin-top: 11px;
      .el-radio-button {
        padding-bottom: 20px;
        .el-radio-button__inner {
          border: none;
          padding: 0 32px 0 0;
          background: none;
          &:hover {
            color: #21c9b8;
          }
        }
        &.is-active {
          .el-radio-button__inner {
            color: #21c9b8;
            background: none;
          }
        }
        .el-radio-button__orig-radio:checked {
          & + .el-radio-button__inner {
            box-shadow: unset;
          }
        }
      }
    }
  }
  .activity-page-end {
    .activity-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
}
::v-deep .el-form-item--medium .el-form-item__content {
  border-bottom: 1px solid rgb(246, 246, 246);
}
.expert-library-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 100%;
  .list-item-content {
    display: flex;
    justify-content: space-between;
    width: 578px;
    background: #fff;
    margin-top: 36px;
    padding: 28px 32px;
    min-height: 240px;
    .list-item-box {
      flex: 1;
      .item-headline {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .item-title {
          width: 280px;
          font-size: 32px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333;
          line-height: 32px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          word-wrap: break-word;
        }
      }
      .expert-library-label {
        display: flex;
        flex-wrap: wrap;
        margin: 0 0 16px;
        .library-label-item {
          max-width: 350px;
          padding: 6px 12px;
          background: #f4f5f9;
          border-radius: 4px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #666;
          line-height: 12px;
          margin: 24px 16px 0 0;
          .expert-library-type {
            word-wrap: break-word;
          }
        }
      }
      .expert-library-box {
        width: 370px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #666;
        line-height: 32px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        word-wrap: break-word;
      }
    }
    .list-item-img {
      width: 120px;
      height: 168px;
      margin-left: 24px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    &:hover {
      cursor: pointer;
    }
  }
}
</style>
