<!--
 * @Author: jhy
 * @Date: 2023-02-03 09:26:43
 * @LastEditors: JHY
 * @LastEditTime: 2023-12-09 21:08:18
-->
<template>
  <div class="enterprise-detail-container">
    <!-- banner图 -->
    <div class="enterprise-detail-banner">
      <img
        src="../../../../assets/enterprise/enterpriseDetailBanner.png"
        alt=""
      />
    </div>
    <div class="enterprise-detail-title-box">
      <div class="enterprise-detail-divider"></div>
      <div class="enterprise-detail-title">机构详情</div>
      <div class="enterprise-detail-divider"></div>
    </div>
    <div class="enterprise-detail-content">
      <div class="enterprise-title">{{ data.name }}</div>
      <div class="enterprise-title-carousel">
        <el-carousel
          v-if="data.companyPictureList && data.companyPictureList.length > 0"
          class="carousel-content"
          arrow="always"
          :interval="5000"
        >
          <el-carousel-item
            v-for="(item, index) in data.companyPictureList"
            :key="index"
            class="carousel-item-content"
          >
            <div class="carousel-item-box">
              <img :src="item.url" alt="" />
            </div>
          </el-carousel-item>
        </el-carousel>
        <img
          v-else
          src="../../../../assets/purchaseSales/companyDefault.png"
          alt=""
          class="carousel-default-img"
        />
      </div>
      <div class="enterprise-title-tag">
        <div
          v-for="(item, index) in data.industrialChainValueList"
          :key="index"
          class="title-tag-item"
        >
          {{ item }}
        </div>
      </div>
      <div class="enterprise-title-address">
        <div class="address-content">
          <img
            src="../../../../assets/enterprise/addressIcon.png"
            alt=""
            class="address-content-img"
          />
          <div class="address-content-text">
            {{ data.address }}
          </div>
        </div>
      </div>
      <!-- 企业简介 -->
      <div class="enterprise-introduction-box" v-if="this.data.introduce != 0">
        <div class="enterprise-introduction-title">
          <div class="introduction-line"></div>
          <div class="introduction-title">企业简介</div>
        </div>
        <div class="enterprise-introduction-info">
          {{ data.introduce }}
        </div>
      </div>
      <!-- 企业需求 -->
      <div class="enterprise-demand-content" v-if="this.demandTotal != 0">
        <div class="enterprise-introduction-title">
          <div class="introduction-line"></div>
          <div class="introduction-title">企业需求（{{ demandTotal }}）</div>
        </div>
        <div class="enterprise-demand-info">
          <div
            v-for="(item, index) in demandData"
            :key="index"
            class="enterprise-demand-item"
          >
            <router-link
              :to="{ name: 'demandHallDetail', query: { id: item.id } }"
            >
              <div class="item-img">
                <img
                  v-if="item.scenePicture && item.scenePicture.length > 0"
                  :src="item.scenePicture[0].url"
                  alt=""
                />
                <img
                  v-else
                  src="../../../../assets/purchaseSales/companyDefault.png"
                  alt=""
                />
              </div>
              <!-- </router-link> -->
              <div class="item-content">
                <div class="item-title">
                  {{ item.demandTitle }}
                </div>
                <div class="item-content-tag">
                  <div
                    v-for="(val, num) in item.applicationArea"
                    :key="num"
                    class="item-tag"
                  >
                    {{ val }}
                  </div>
                </div>
              </div>
            </router-link>
          </div>
        </div>
      </div>
      <!-- 企业供给 -->
      <div class="enterprise-demand-content" v-if="this.supplyTotal != 0">
        <div class="enterprise-introduction-title">
          <div class="introduction-line"></div>
          <div class="introduction-title">企业供给（{{ supplyTotal }}）</div>
        </div>
        <div class="enterprise-demand-info">
          <div
            v-for="(item, index) in supplyData"
            :key="index"
            class="enterprise-demand-item"
          >
            <router-link
              :to="{ name: 'resourceHallDetail', query: { id: item.id } }"
            >
              <div class="item-img">
                <img
                  v-if="item.productPhoto && item.productPhoto.length > 0"
                  :src="item.productPhoto[0].url"
                  alt=""
                />
                <img
                  v-else
                  src="../../../../assets/purchaseSales/resourceDefault.png"
                  alt=""
                />
              </div>
              <!-- </router-link> -->
              <div class="item-content">
                <div class="item-title">
                  {{ item.supplyName }}
                </div>
                <div class="item-content-tag">
                  <div
                    v-for="(val, num) in item.applicationArea"
                    :key="num"
                    class="item-tag"
                  >
                    {{ val }}
                  </div>
                </div>
              </div>
            </router-link>
          </div>
        </div>
      </div>
      <!-- 企业资料 -->
      <div
        class="enterprise-data-content"
        v-if="this.data.companyMaterialList != 0"
      >
        <div class="enterprise-introduction-title introduction-title-data">
          <div class="introduction-line"></div>
          <div class="introduction-title">企业资料</div>
        </div>
        <div
          v-for="(item, index) in data.companyMaterialList"
          :key="index"
          class="introduction-data-info"
        >
          <div class="item-introduction">
            <div class="item-introduction-name">
              <div class="item-introduction-img">
                <img
                  src="../../../../assets/purchaseSales/linkIcon.png"
                  alt=""
                />
              </div>
              <div class="item-introduction-file">
                {{ item.name }}
              </div>
            </div>
            <div class="item-introduction-btn">
              <el-button @click="viewData(item.url)">立即查看</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="enterprise-detail-btn">
        <!-- <el-button icon="el-icon-chat-dot-round">在线沟通</el-button> -->
      </div>
    </div>
  </div>
</template>

<script>
import {
  getCompanyDetail,
  getDemandList,
  getSupplyList,
} from "@/api/purchaseSales";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      data: {},
      status: true,
      demandData: [],
      demandTotal: 0,
      supplyData: [],
      supplyTotal: 0,
      arr: [],
    };
  },
  created() {
    this.getCompanyDetail();
    this.getDemandList();
    this.getSupplyList();
  },
  methods: {
    // 企业详情
    getCompanyDetail() {
      this.loading = true;
      getCompanyDetail({ id: this.$route.query.id })
        .then((res) => {
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
          this.loading = false;
          this.data = res.data || {};
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 企业需求
    getDemandList() {
      getDemandList({
        businessNo: this.$route.query.businessNo,
        displayStatus: 1,
        auditStatus: 2,
      }).then((res) => {
        let { rows, total } = res || [];
        this.demandData = rows;
        this.demandData.forEach((item) => {
          item.scenePicture = item.scenePicture
            ? JSON.parse(item.scenePicture)
            : [];
          item.applicationArea = item.applicationArea
            ? item.applicationArea.split(",")
            : [];
        });
        this.demandTotal = total;
      });
    },
    // btn11(){
    //   this.$router.push({path:'/demandHallDetail',query:{id:this.demandData.id}})
    // },
    // 企业供给
    getSupplyList() {
      getSupplyList({
        businessNo: this.$route.query.businessNo,
        auditStatus: 2,
        displayStatus: 1,
      }).then((res) => {
        let { rows, total } = res || [];
        this.supplyData = rows;
        this.supplyData.forEach((item) => {
          item.productPhoto = item.productPhoto
            ? JSON.parse(item.productPhoto)
            : [];
          item.applicationArea = item.applicationArea
            ? item.applicationArea.split(",")
            : [];
        });
        this.supplyTotal = total;
      });
    },
    // 查看企业资料
    viewData(url) {
      window.open(url);
    },
  },
};
</script>

<style lang="scss" scoped>
.enterprise-detail-container {
  width: 100%;
  background: #f4f5f9;
  padding-bottom: 60px;
  .enterprise-detail-banner {
    width: 100%;
    height: 25.93vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .enterprise-detail-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;
    .enterprise-detail-title {
      font-size: 40px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }
    .enterprise-detail-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }
  .enterprise-detail-content {
    width: 1200px;
    margin: 0 auto;
    padding: 60px 116px 58px;
    background: #fff;
    .enterprise-title {
      max-width: 960px;
      font-size: 32px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333;
      line-height: 32px;
      text-align: center;
      padding-bottom: 44px;
      word-break: break-all;
    }
    .enterprise-title-carousel {
      width: 720px;
      height: 360px;
      margin: 0 auto;
      .carousel-content {
        width: 100%;
        height: 360px;
        .carousel-item-content {
          width: 100%;
          height: 100%;
          .carousel-item-box {
            margin: 0 auto;
            width: 600px;
            height: 100%;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
      .carousel-default-img {
        width: 600px;
        height: 100%;
        margin: 0 auto;
        display: block;
      }
    }
    .enterprise-title-tag {
      display: flex;
      flex-wrap: wrap;
      width: 600px;
      margin: 0 auto 21px;
      .title-tag-item {
        max-width: 660px;
        background: rgba(197, 37, 33, 0.1);
        border-radius: 4px;
        padding: 6px 12px;
        font-size: 12px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #21c9b8;
        line-height: 12px;
        margin: 16px 16px 0 0;
        word-wrap: break-word;
        text-align: left;
      }
    }
    .enterprise-title-address {
      width: 600px;
      margin: 21px auto 0;
      .address-content {
        display: flex;
        align-items: center;
        .address-content-img {
          width: 12px;
          height: 14px;
        }
        .address-content-text {
          max-width: 600px;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #666;
          line-height: 14px;
          padding-left: 6px;
          word-wrap: break-word;
          text-align: left;
        }
      }
    }
    .enterprise-introduction-box {
      width: 960px;
      padding-top: 61px;
      .enterprise-introduction-info {
        width: 960px;
        padding-top: 40px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        line-height: 28px;
        word-wrap: break-word;
        text-align: left;
      }
    }
    .enterprise-introduction-title {
      display: flex;
      align-items: center;
      .introduction-line {
        width: 4px;
        height: 20px;
        background: #21c9b8;
      }
      .introduction-title {
        font-size: 24px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 24px;
        padding-left: 8px;
      }
    }
    .introduction-title-data {
      margin-bottom: 40px;
    }
    .enterprise-demand-content {
      width: 960px;
      padding-top: 60px;
      .enterprise-demand-info {
        display: flex;
        flex-wrap: wrap;
        .enterprise-demand-item {
          width: 222px;
          margin: 40px 18px 0 0;
          background: #f8f9fb;
          .item-img {
            width: 100%;
            height: 160px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .item-content {
            padding: 16px 16px 14px;
            .item-title {
              width: 190px;
              height: 52px;
              font-size: 18px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #333;
              line-height: 26px;
              overflow: hidden;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              text-overflow: ellipsis;
              word-wrap: break-word;
            }
            .item-content-tag {
              display: flex;
              flex-wrap: wrap;
              .item-tag {
                max-width: 190px;
                padding: 12px;
                font-size: 12px;
                font-family: PingFangSC-Regular, PingFang SC;
                color: #214dc5;
                line-height: 12px;
                background: rgba(33, 77, 197, 0.1);
                border-radius: 4px;
                margin: 12px 12px 0 0;
                word-wrap: break-word;
              }
            }
          }
          &:hover {
            cursor: pointer;
            .item-title {
              color: #21c9b8;
            }
          }
        }
      }
    }
    .enterprise-data-content {
      width: 960px;
      margin-top: 60px;
    }
    .introduction-data-info {
      margin-top: 10px;
      .item-introduction {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 13px 13px 16px;
        background: #f7f8fa;
        border-radius: 4px;
        .item-introduction-name {
          display: flex;
          align-items: center;
          width: 850px;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #333;
          line-height: 14px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          .item-introduction-img {
            width: 26px;
            height: 32px;
            margin-right: 12px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .item-introduction-file {
            width: 800px;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }
        }
        .item-introduction-btn {
          .el-button {
            width: 72px;
            height: 24px;
            border-radius: 16px;
            border: 1px solid #2f76e0;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            color: #2f76e0;
            padding: 0px 8px;
          }
        }
      }
    }
    .enterprise-detail-btn {
      margin-top: 60px;
      text-align: center;
      .el-button {
        width: 163px;
        height: 40px;
        border-radius: 4px;
        border: 1px solid #21c9b8;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #21c9b8;
        line-height: 16px;
        &:hover {
          background-color: transparent;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.enterprise-detail-container {
  .enterprise-title-carousel {
    .el-carousel__container {
      height: 100%;
      .el-carousel__arrow {
        height: 48px;
        width: 48px;
        background: #f4f5f9;
        &:hover {
          background: #21c9b8;
        }
      }
      .el-icon-arrow-left,
      .el-icon-arrow-right {
        font-size: 20px;
      }
      .el-carousel__arrow--left {
        left: 0;
      }
      .el-carousel__arrow--right {
        right: 0;
      }
    }
    .el-carousel__indicator {
      .el-carousel__button {
        width: 4px;
        height: 4px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 3px;
        &:hover {
          width: 24px;
          height: 4px;
          background: #21c9b8;
          border-radius: 3px;
        }
      }
      .el-carousel__indicator--horizontal {
        padding: 12px 8px;
      }
    }
  }
}
</style>
