<template>
  <div class="enterprise-list-container">
    <!-- banner图 -->
    <div class="enterprise-list-banner">
      <img src="../../../../assets/enterprise/enterpriseBanner.png" alt="" />
      <div class="bannerTitle">服务机构</div>
      <div class="bannerDesc">
        凝聚行业顶尖服务机构，提供全方面多领域的星碳服务
      </div>
    </div>
    <div v-loading="loading" style="margin-top: 40px">
      <div class="enterprise-list-title-content">
        <div class="enterprise-list-search-box">
          <el-form ref="form" class="enterprise-list-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.queryParam"
                placeholder="请输入搜索内容"
                class="enterprise-list-search-input"
                :maxlength="255"
              >
                <el-button
                  slot="append"
                  class="enterprise-list-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="enterprise-list-card">
        <div class="enterprise-list-content">
          <div class="enterprise-list-search-type-box">
            <el-form ref="formInfo" :model="formInfo">
              <div class="enterprise-list-search-line">
                <el-form-item
                  label="企业分类"
                  class="enterprise-list-search-line-item"
                >
                  <el-radio-group
                    v-model="formInfo.companyLabel"
                    class="more-radio"
                    @input="changeRadio"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button
                      v-for="(item, index) in companyLabelList"
                      :key="index"
                      :label="item.value"
                      >{{ item.label }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </div>
              <div class="enterprise-list-search-line">
                <el-form-item
                  label="产业链"
                  class="enterprise-list-search-line-item"
                  :class="{ advanced: !advancedIndustrial }"
                >
                  <el-radio-group
                    v-model="formInfo.industrialChain"
                    class="more-radio"
                    @input="changeRadio"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button
                      v-for="(item, index) in industrialChainList"
                      :key="index"
                      :label="item.dictValue"
                      >{{ item.dictLabel }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
                <el-button
                  class="enterprise-list-search-line-btn"
                  @click="toggleIndustry"
                  >{{ advancedIndustrial ? "收起" : "更多"
                  }}<i class="el-icon-arrow-down"></i>
                </el-button>
              </div>
              <div class="enterprise-list-search-line">
                <el-form-item
                  label="行业领域"
                  class="enterprise-list-search-line-item"
                  :class="{ advanced: !advancedIndustry }"
                >
                  <el-radio-group
                    v-model="formInfo.industry"
                    class="more-radio"
                    @input="changeRadio"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button
                      v-for="(item, index) in industryList"
                      :key="index"
                      :label="item.dictValue"
                      >{{ item.dictLabel }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
                <el-button
                  class="enterprise-list-search-line-btn"
                  @click="toggleTrade"
                  >{{ advancedIndustry ? "收起" : "更多"
                  }}<i class="el-icon-arrow-down"></i>
                </el-button>
              </div>
            </el-form>
          </div>
          <div
            v-for="(item, index) in data"
            :key="index"
            class="enterprise-list-list-item"
            @click="goEnterpriseDetail(item)"
          >
            <div class="list-item-content">
              <div class="list-item-img">
                <img
                  v-if="
                    item.companyPictureList &&
                    item.companyPictureList.length > 0
                  "
                  alt=""
                  :src="item.companyPictureList[0].url"
                />
                <img
                  v-else
                  src="../../../../assets/purchaseSales/companyDefault.png"
                  alt=""
                />
              </div>
              <div class="list-item-box">
                <div class="item-title">{{ item.name }}</div>
                <div class="item-info-box">
                  {{ item.introduce }}
                </div>
                <div class="item-tag-content">
                  <div class="item-tag-box">
                    <div class="item-tag" v-show="item.category">
                      {{ item.category }}
                    </div>
                    <div v-if="item.address" class="item-address-tag">
                      <i class="el-icon-location item-address-img"></i>
                      <!-- <img
                        src="../../../../assets/enterprise/addressIcon.png"
                        alt=""
                        class="item-address-img"
                      /> -->
                      <div class="item-address-text">{{ item.address }}</div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="list-item-img">
                <img
                  v-if="
                    item.companyPictureList &&
                    item.companyPictureList.length > 0
                  "
                  alt=""
                  :src="item.companyPictureList[0].url"
                />
                <img
                  v-else
                  src="../../../../assets/purchaseSales/companyDefault.png"
                  alt=""
                />
              </div> -->
            </div>
          </div>
          <div class="enterprise-list-page-end">
            <el-button class="enterprise-list-page-btn" @click="goHome"
              >首页</el-button
            >
            <el-pagination
              v-if="data && data.length > 0"
              background
              layout="prev, pager, next"
              class="enterprise-list-pagination"
              :page-size="pageSize"
              :current-page="pageNum"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDicts } from "@/api/system/dict/data";
import { ENTERPRISE_TYPE } from "@/const/status";
import { getCompanyHomeList } from "@/api/purchaseSales";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      loading: false,
      form: {
        queryParam: "", //搜索内容
      },
      formInfo: {
        companyLabel: "", //企业分类
        industrialChain: "", //产业链
        industry: "", //行业领域
      },
      companyLabelList: ENTERPRISE_TYPE, //企业分类列表
      industrialChainList: [], //产业链字典列表
      industryList: [], //行业领域字典列表
      advancedIndustrial: false,
      advancedIndustry: false,
      data: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  created() {
    this.getDictsList("industrial_chain", "industrialChainList");
    this.getDictsList("company_industry", "industryList");
    this.search();
  },
  methods: {
    search() {
      this.loading = true;
      getCompanyHomeList({
        ...this.form,
        ...this.formInfo,
        recommendStatus: 1,
        pageNum: this.pageNum,
        // pageSize: this.pageSize,
      })
        .then((res) => {
          this.loading = false;
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
          let { rows, total } = res || [];
          this.data = rows;
          this.total = total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 字典
    getDictsList(code, propertyName) {
      getDicts(code).then((res) => {
        this[propertyName] = res.data || [];
      });
    },
    // 显示更多产业链
    toggleIndustry() {
      this.advancedIndustrial = !this.advancedIndustrial;
    },
    // 显示更多行业领域
    toggleTrade() {
      this.advancedIndustry = !this.advancedIndustry;
    },
    changeRadio() {
      this.onSearch();
    },
    // 跳转到企业名录详情页面
    goEnterpriseDetail(item) {
      let routeData = this.$router.resolve({
        path: "/enterpriseDetail",
        query: { id: item.id, businessNo: item.businessNo },
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到首页
    goHome() {
      this.$router.push({ path: "/index" });
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.search();
    },
    onSearch() {
      this.pageNum = 1;
      this.search();
    },
  },
};
</script>

<style lang="scss" scoped>
.enterprise-list-container {
  width: 100%;
  .enterprise-list-banner {
    width: 100%;
    height: 500px;
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .bannerTitle {
      position: absolute;
      top: 161px;
      left: 24%;
      font-size: 50px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
    }
    .bannerDesc {
      position: absolute;
      top: 249px;
      left: 24%;
      font-size: 24px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }
  }
  .enterprise-list-title-content {
    width: 100%;
    .enterprise-list-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .enterprise-list-title {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .enterprise-list-divider {
        width: 48px;
        height: 4px;
        background: #21c9b8;
      }
    }
    .enterprise-list-search-box {
      .enterprise-list-search-form {
        text-align: center;
        .enterprise-list-search-input {
          width: 792px;
          height: 54px;
          .enterprise-list-search-btn {
            width: 100px;
          }
        }
      }
    }
  }
  .enterprise-list-card {
    background: #ffffff;
    .enterprise-list-content {
      width: 1200px;
      margin: 0 auto;
      .enterprise-list-search-type-box {
        background: #fff;
        margin-bottom: 17px;
        .enterprise-list-search-line {
          display: flex;
          justify-content: space-between;
          padding: 14px 24px 4px;
          .enterprise-list-search-line-item {
            flex: 1;
            margin-bottom: 0;
            display: flex;
            &.advanced {
              overflow: hidden;
              height: 45px;
            }
            .more-radio {
              margin-top: 11px;
              flex: 1;
            }
          }
          .enterprise-list-search-line-btn {
            display: inline-block;
            width: 64px;
            height: 24px;
            background: #fff;
            border-radius: 2px;
            border: 1px solid #d9d9d9;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            color: #333;
            display: flex;
            align-items: center;
            padding: 0 16px;
            margin-top: 5px;
            &:hover {
              border: 1px solid #21c9b8;
              color: #21c9b8;
            }
          }
          & + .enterprise-list-search-line {
            border-top: 1px solid #f5f5f5;
          }
        }
      }
      .enterprise-list-list-item {
        width: 100%;
        height: 220px;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);
        .list-item-content {
          display: flex;
          justify-content: space-between;
          padding: 24px 24px 24px 32px;
          cursor: pointer;
          .list-item-box {
            width: 880px;
            // margin-left: 32px;
            // padding-top: 8px;
            .item-title {
              width: 806px;
              height: 24px;
              font-size: 22px;
              font-family: Source Han Sans CN;
              font-weight: 500;
              color: #222222;
              line-height: 24px;
              margin-bottom: 12px;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              word-break: break-all;
            }
            .item-tag-content {
              display: flex;
              align-items: top;
              .item-tag-box {
                display: flex;
                width: 852px;
                flex-wrap: wrap;
                .item-tag {
                  height: 38px;
                  background: rgb(242, 245, 255);
                  border-radius: 4px;
                  padding: 0 12px;
                  max-width: 860px;
                  line-height: 38px;
                  margin-right: 16px;
                  margin-top: 12px;
                  color: #21c9b8;
                  word-break: break-all;
                }
                .item-address-tag {
                  display: flex;
                  align-items: center;
                  padding: 0 12px 0 5px;
                  line-height: 24px;
                  text-align: center;
                  margin-right: 16px;
                  margin-top: 12px;
                  background: rgb(240, 249, 247);
                  border-radius: 2px;
                  .item-address-img {
                    width: 14px;
                    height: 18px;
                    line-height: 18px;
                    margin-right: 4px;
                    color: #039477;
                  }
                  .item-address-text {
                    color: #039477;
                    max-width: 860px;
                    word-break: break-all;
                  }
                }
              }
            }
            .item-info-box {
              width: 806px;
              height: 78px;
              font-family: PingFangSC-Regular, PingFang SC;
              color: #65676a;
              line-height: 26px;
              margin-top: 24px;
              overflow: hidden;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 3;
              text-overflow: ellipsis;
              word-break: break-all;
            }
          }
          .list-item-img {
            width: 220px;
            height: 160px;
            img {
              width: 100%;
              height: 100%;
              border-radius: 7px;
            }
          }
          &:hover {
            .list-item-title {
              color: #21c9b8;
            }
          }
        }
        & + .enterprise-list-list-item {
          margin-top: 24px;
        }
      }
      .enterprise-list-list-item:hover {
        box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.2);
      }
      .enterprise-list-page-end {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        padding: 24px 0 60px;
        .enterprise-list-page-btn {
          width: 82px;
          height: 32px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #333;
          line-height: 10px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.enterprise-list-container {
  .enterprise-list-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #21c9b8;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #fff;
      line-height: 24px;
    }
  }
  .el-form-item__label {
    width: 88px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #999;
    padding-right: 32px;
    text-align: left;
  }
  .enterprise-list-search-line {
    .el-form-item__content {
      width: 970px;
    }
  }
  .el-radio-button {
    padding-bottom: 20px;
    .el-radio-button__inner {
      border: none;
      padding: 0 32px 0 0;
      background: none;
      &:hover {
        color: #21c9b8;
      }
    }
    &.is-active {
      .el-radio-button__inner {
        color: #21c9b8;
        background: none;
      }
    }
    .el-radio-button__orig-radio:checked {
      & + .el-radio-button__inner {
        box-shadow: unset;
      }
    }
  }
  .enterprise-list-page-end {
    .enterprise-list-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
}
</style>
