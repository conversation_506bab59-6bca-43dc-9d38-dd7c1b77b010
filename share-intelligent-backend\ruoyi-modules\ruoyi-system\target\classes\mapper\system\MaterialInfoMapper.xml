<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MaterialInfoMapper">
    
    <resultMap type="MaterialInfo" id="MaterialInfoResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="modelNumber"    column="model_number"    />
        <result property="quantity"    column="quantity"    />
        <result property="unit"    column="unit"    />
        <result property="capacity"    column="capacity"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
    
    <resultMap type="com.ruoyi.system.domain.dto.MaterialWithOrderDTO" id="MaterialWithOrderResult" extends="MaterialInfoResult">
        <!-- 订单物料关联信息 -->
        <association property="relation" javaType="com.ruoyi.system.domain.OrderMaterialRelation">
            <result property="id"    column="relation_id"    />
            <result property="orderId"    column="order_id"    />
            <result property="materialId"    column="material_id"    />
            <result property="quantity"    column="relation_quantity"    />
            <result property="unit"    column="relation_unit"    />
            <result property="remark"    column="relation_remark"    />
            <result property="createTime"    column="relation_create_time"    />
            <result property="updateTime"    column="relation_update_time"    />
        </association>
        <!-- 制造订单信息 -->
        <association property="order" javaType="com.ruoyi.system.domain.ManufactureOrder">
            <result property="id"    column="order_id"    />
            <result property="deadline"    column="deadline"    />
            <result property="status"    column="order_status"    />
            <result property="orderType"    column="order_type"    />
            <result property="price"    column="price"    />
            <result property="demandCompany"    column="demand_company"    />
            <result property="contactPhone"    column="contact_phone"    />
            <result property="deliveryAddress"    column="delivery_address"    />
            <result property="fileRequirement"    column="file_requirement"    />
            <result property="bankName"    column="bank_name"    />
            <result property="paymentAccount"    column="payment_account"    />
            <result property="createTime"    column="order_create_time"    />
            <result property="updateTime"    column="order_update_time"    />
        </association>
    </resultMap>

    <sql id="selectMaterialInfoVo">
        select id, name, model_number, quantity, unit, capacity, create_time, update_time from material_info
    </sql>
    
    <sql id="selectMaterialWithOrderVo">
        select 
            m.id, m.name, m.model_number, m.quantity, m.unit, m.capacity, m.create_time, m.update_time,
            r.id as relation_id, r.order_id, r.material_id, r.quantity as relation_quantity, r.unit as relation_unit, 
            r.remark as relation_remark, r.create_time as relation_create_time, r.update_time as relation_update_time,
            o.id as order_id, o.deadline, o.status as order_status, o.order_type, o.price, o.demand_company, 
            o.contact_phone, o.delivery_address, o.file_requirement, o.bank_name, o.payment_account,
            o.create_time as order_create_time, o.update_time as order_update_time
        from material_info m
        left join order_material_relation r on m.id = r.material_id
        left join manufacture_order o on r.order_id = o.id
    </sql>

    <select id="selectMaterialInfoList" parameterType="MaterialInfo" resultMap="MaterialInfoResult">
        <include refid="selectMaterialInfoVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="modelNumber != null  and modelNumber != ''"> and model_number = #{modelNumber}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="capacity != null  and capacity != ''"> and capacity = #{capacity}</if>
        </where>
    </select>
    
    <select id="selectMaterialInfoWithOrderList" parameterType="MaterialInfo" resultMap="MaterialWithOrderResult">
        <include refid="selectMaterialWithOrderVo"/>
        <where>  
            <if test="name != null  and name != ''"> and m.name like concat('%', #{name}, '%')</if>
            <if test="modelNumber != null  and modelNumber != ''"> and m.model_number = #{modelNumber}</if>
            <if test="quantity != null "> and m.quantity = #{quantity}</if>
            <if test="unit != null  and unit != ''"> and m.unit = #{unit}</if>
            <if test="capacity != null  and capacity != ''"> and m.capacity = #{capacity}</if>
        </where>
    </select>
    
    <select id="selectMaterialInfoById" parameterType="Long" resultMap="MaterialInfoResult">
        <include refid="selectMaterialInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertMaterialInfo" parameterType="MaterialInfo" useGeneratedKeys="true" keyProperty="id">
        insert into material_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="modelNumber != null">model_number,</if>
            <if test="quantity != null">quantity,</if>
            <if test="unit != null">unit,</if>
            <if test="capacity != null">capacity,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="modelNumber != null">#{modelNumber},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="unit != null">#{unit},</if>
            <if test="capacity != null">#{capacity},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMaterialInfo" parameterType="MaterialInfo">
        update material_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="modelNumber != null">model_number = #{modelNumber},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="capacity != null">capacity = #{capacity},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMaterialInfoById" parameterType="Long">
        delete from material_info where id = #{id}
    </delete>

    <delete id="deleteMaterialInfoByIds" parameterType="String">
        delete from material_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>