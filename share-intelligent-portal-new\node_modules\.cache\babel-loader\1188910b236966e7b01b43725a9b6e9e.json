{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\supplyForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\supplyForm.vue", "mtime": 1750311962955}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_zhm", "require", "_cache", "_interopRequireDefault", "_excluded", "name", "dicts", "data", "user", "$store", "state", "loading", "keywordLoading", "form", "supplyType", "supplyName", "undefined", "summary", "keywords", "technologyType", "applicationArea", "productPhoto", "enclosure", "productStage", "cooperationMode", "companyName", "contactsName", "contactsMobile", "tel", "publisherName", "publisherMobile", "businessNo", "bussinessNo", "auditStatus", "displayStatus", "rules", "required", "message", "trigger", "methods", "init", "cache", "local", "getJSON", "onCancel", "$router", "back", "onSave", "_this", "$refs", "validate", "valid", "setJSON", "$message", "success", "onSubmit", "status", "_this2", "_this2$form", "rest", "_objectWithoutProperties2", "default", "_objectSpread2", "length", "join", "JSON", "stringify", "supplyAdd", "then", "res", "code", "msg", "remove", "error", "finally", "handleKeywordList", "_this3", "keywordList", "warning", "handleClose", "tag", "filter", "item"], "sources": ["src/views/form/components/supplyForm.vue"], "sourcesContent": ["<template>\r\n  <div class=\"supply-form\">\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item label=\"资源类型\" prop=\"supplyType\">\r\n        <el-checkbox-group\r\n          v-model=\"form.supplyType\"\r\n          placeholder=\"请选择\"\r\n          clearable\r\n        >\r\n          <el-checkbox\r\n            v-for=\"dict in dict.type.supply_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.value\"\r\n            :value=\"dict.value\"\r\n            >{{ dict.label }}</el-checkbox\r\n          >\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <el-form-item prop=\"supplyName\" label=\"资源标题\">\r\n        <el-input\r\n          v-model=\"form.supplyName\"\r\n          maxlength=\"50\"\r\n          show-word-limit\r\n          placeholder=\"请输入\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"summary\" label=\"资源描述\">\r\n        <el-input\r\n          type=\"textarea\"\r\n          v-model=\"form.summary\"\r\n          maxlength=\"500\"\r\n          rows=\"6\"\r\n          show-word-limit\r\n          placeholder=\"请输入资源描述\"\r\n        ></el-input>\r\n        <div class=\"extra-content\">\r\n          <div class=\"extra-content-header\">\r\n            <el-button\r\n              :loading=\"keywordLoading\"\r\n              @click=\"handleKeywordList\"\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              >生成关键词</el-button\r\n            >\r\n            <span class=\"tip\">生成关键词有利于实现精准匹配哦！</span>\r\n          </div>\r\n          <div v-if=\"form.keywords.length > 0\" class=\"extra-content-body\">\r\n            <el-tag\r\n              :key=\"`${tag}_${index}`\"\r\n              v-for=\"(tag, index) in form.keywords\"\r\n              closable\r\n              size=\"small\"\r\n              disable-transitions\r\n              @close=\"handleClose(tag)\"\r\n            >\r\n              {{ tag }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item prop=\"technologyType\" label=\"技术类别\">\r\n        <el-select\r\n          v-model=\"form.technologyType\"\r\n          filterable\r\n          allow-create\r\n          multiple\r\n          style=\"width: 100%\"\r\n          placeholder=\"请选择\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in dict.type.technology_type\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.label\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item prop=\"applicationArea\" label=\"应用领域\">\r\n        <el-select\r\n          v-model=\"form.applicationArea\"\r\n          filterable\r\n          allow-create\r\n          multiple\r\n          style=\"width: 100%\"\r\n          placeholder=\"请选择\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in dict.type.application_area\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.label\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"资源图片\" prop=\"productPhoto\">\r\n        <ImageUpload v-model=\"form.productPhoto\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"合作方式\" prop=\"cooperationMode\">\r\n        <el-select\r\n          v-model=\"form.cooperationMode\"\r\n          placeholder=\"请选择\"\r\n          style=\"width: 100%\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.cooperation_mode\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品阶段\" prop=\"productStage\">\r\n        <el-select\r\n          v-model=\"form.productStage\"\r\n          placeholder=\"请选择\"\r\n          style=\"width: 100%\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.product_stage\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"上传附件\" prop=\"enclosure\">\r\n        <FileUpload v-model=\"form.enclosure\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.companyName\"\r\n          placeholder=\"请先绑定公司\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系人\" prop=\"contactsName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactsName\"\r\n          placeholder=\"请先维护联系人\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系方式\" prop=\"contactsMobile\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactsMobile\"\r\n          placeholder=\"请先维护联系方式\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button @click.once=\"onCancel\">取消</el-button>\r\n        <el-button @click=\"onSubmit('0')\" type=\"primary\" plain\r\n          >暂存草稿</el-button\r\n        >\r\n        <el-button type=\"primary\" @click=\"onSubmit('2')\">发布</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { keywordList, supplyAdd } from \"@/api/zhm\";\r\nimport cache from \"@/plugins/cache\";\r\n\r\nexport default {\r\n  name: \"supplyForm\",\r\n  dicts: [\r\n    \"supply_type\",\r\n    \"technology_type\",\r\n    \"cooperation_mode\",\r\n    \"product_stage\",\r\n    \"application_area\",\r\n  ],\r\n  data() {\r\n    const { user } = this.$store.state;\r\n    return {\r\n      loading: false,\r\n      keywordLoading: false,\r\n      form: {\r\n        // 供给类型\r\n        supplyType: [],\r\n        // 需求标题\r\n        supplyName: undefined,\r\n        // 描述\r\n        summary: undefined,\r\n        // 关键词\r\n        keywords: [],\r\n        // 技术类别\r\n        technologyType: [],\r\n        // 应用领域\r\n        applicationArea: [],\r\n        // 产品图片\r\n        productPhoto: [],\r\n        // 附件\r\n        enclosure: [],\r\n        // 产品阶段\r\n        productStage: undefined,\r\n        // 合作方式\r\n        cooperationMode: undefined,\r\n        // 公司名称\r\n        companyName: user.companyName || \"柠檬豆\",\r\n        // 联系人\r\n        contactsName: user.name,\r\n        // 联系电话\r\n        contactsMobile: user.tel,\r\n        publisherName: user.name,\r\n        publisherMobile: user.tel,\r\n        businessNo: user.bussinessNo,\r\n        auditStatus: \"2\",\r\n        displayStatus: \"1\",\r\n      },\r\n      rules: {\r\n        supplyType: [\r\n          { required: true, message: \"请选择供给类型\", trigger: \"blur\" },\r\n        ],\r\n        supplyName: [\r\n          { required: true, message: \"请输入供给标题\", trigger: \"blur\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"请输入供给描述\", trigger: \"blur\" },\r\n        ],\r\n        technologyType: [\r\n          { required: true, message: \"请选择技术类别\", trigger: \"blur\" },\r\n        ],\r\n        applicationArea: [\r\n          { required: true, message: \"请选择应用领域\", trigger: \"blur\" },\r\n        ],\r\n        productPhoto: [\r\n          { required: true, message: \"请选择供给图片\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"请维护公司名称\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"请维护联系人\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"请维护联系电话\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      const data = cache.local.getJSON(\"supply_data\");\r\n      if (data) {\r\n        this.form = data;\r\n      }\r\n    },\r\n    onCancel() {\r\n      this.$router.back();\r\n    },\r\n    onSave() {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          cache.local.setJSON(\"supply_data\", this.form);\r\n          this.$message.success(\"暂存成功\");\r\n        }\r\n      });\r\n    },\r\n    onSubmit(status) {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          const {\r\n            supplyType,\r\n            keywords,\r\n            applicationArea,\r\n            productPhoto,\r\n            enclosure,\r\n            technologyType,\r\n            ...rest\r\n          } = this.form;\r\n          const data = {\r\n            ...rest,\r\n            auditStatus: status,\r\n          };\r\n          if (supplyType.length > 0) {\r\n            data[\"supplyType\"] = supplyType.join();\r\n          }\r\n          if (keywords.length > 0) {\r\n            data[\"keywords\"] = keywords.join();\r\n          }\r\n          if (technologyType.length > 0) {\r\n            data[\"technologyType\"] = technologyType.join();\r\n          }\r\n          if (applicationArea.length > 0) {\r\n            data[\"applicationArea\"] = applicationArea.join();\r\n          }\r\n          if (productPhoto.length > 0) {\r\n            data[\"productPhoto\"] = JSON.stringify(productPhoto);\r\n          }\r\n          if (enclosure.length > 0) {\r\n            data[\"enclosure\"] = JSON.stringify(enclosure);\r\n          }\r\n          supplyAdd(data)\r\n            .then((res) => {\r\n              const { code, msg } = res;\r\n              if (code === 200) {\r\n                cache.local.remove(\"supply_data\");\r\n                this.$message.success(\"发布成功\");\r\n                this.$router.back();\r\n              } else {\r\n                this.$message.error(msg || \"发布失败\");\r\n              }\r\n            })\r\n            .finally(() => (this.loading = false));\r\n        }\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      const { summary } = this.form;\r\n      if (summary) {\r\n        this.keywordLoading = true;\r\n        keywordList(summary)\r\n          .then((res) => {\r\n            const { code, data, msg } = res;\r\n            if (code === 200) {\r\n              this.form.keywords = data;\r\n            } else {\r\n              this.$message.error(msg);\r\n            }\r\n          })\r\n          .finally(() => (this.keywordLoading = false));\r\n      } else {\r\n        this.$message.warning(\"请输入需求描述\");\r\n      }\r\n    },\r\n\r\n    handleClose(tag) {\r\n      this.form.keywords = this.form.keywords.filter((item) => item !== tag);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.supply-form {\r\n  width: 676px;\r\n  .label-item {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    .title {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #333333;\r\n      line-height: 18px;\r\n    }\r\n    .extra {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .extra-content {\r\n    padding: 12px 0;\r\n    &-header {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      .tip {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n        line-height: 14px;\r\n        margin-left: 12px;\r\n      }\r\n    }\r\n    &-body {\r\n      padding-top: 6px;\r\n      .el-tag {\r\n        margin-right: 12px;\r\n        &:last-child {\r\n          margin-right: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  ::v-deep.el-form-item__label {\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    line-height: 18px;\r\n    margin-bottom: 12px;\r\n    padding: 0;\r\n  }\r\n  .el-checkbox {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #262626;\r\n    line-height: 18px;\r\n    margin-right: 28px;\r\n  }\r\n  .footer-submit {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-top: 40px;\r\n    .el-button {\r\n      width: 160px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAqKA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAA,IAAAG,SAAA,iG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA,GACA,eACA,mBACA,oBACA,iBACA,mBACA;EACAC,IAAA,WAAAA,KAAA;IACA,IAAAC,IAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,IAAA;IACA;MACAG,OAAA;MACAC,cAAA;MACAC,IAAA;QACA;QACAC,UAAA;QACA;QACAC,UAAA,EAAAC,SAAA;QACA;QACAC,OAAA,EAAAD,SAAA;QACA;QACAE,QAAA;QACA;QACAC,cAAA;QACA;QACAC,eAAA;QACA;QACAC,YAAA;QACA;QACAC,SAAA;QACA;QACAC,YAAA,EAAAP,SAAA;QACA;QACAQ,eAAA,EAAAR,SAAA;QACA;QACAS,WAAA,EAAAjB,IAAA,CAAAiB,WAAA;QACA;QACAC,YAAA,EAAAlB,IAAA,CAAAH,IAAA;QACA;QACAsB,cAAA,EAAAnB,IAAA,CAAAoB,GAAA;QACAC,aAAA,EAAArB,IAAA,CAAAH,IAAA;QACAyB,eAAA,EAAAtB,IAAA,CAAAoB,GAAA;QACAG,UAAA,EAAAvB,IAAA,CAAAwB,WAAA;QACAC,WAAA;QACAC,aAAA;MACA;MACAC,KAAA;QACArB,UAAA,GACA;UAAAsB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAvB,UAAA,GACA;UAAAqB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACArB,OAAA,GACA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,cAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,eAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjB,YAAA,GACA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,WAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,YAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,cAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,IAAAjC,IAAA,GAAAkC,cAAA,CAAAC,KAAA,CAAAC,OAAA;MACA,IAAApC,IAAA;QACA,KAAAM,IAAA,GAAAN,IAAA;MACA;IACA;IACAqC,QAAA,WAAAA,SAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAApC,IAAA,CAAAqC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAV,cAAA,CAAAC,KAAA,CAAAU,OAAA,gBAAAJ,KAAA,CAAAnC,IAAA;UACAmC,KAAA,CAAAK,QAAA,CAAAC,OAAA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAAC,MAAA;MAAA,IAAAC,MAAA;MACA,KAAAR,KAAA,CAAApC,IAAA,CAAAqC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAM,MAAA,CAAA9C,OAAA;UACA,IAAA+C,WAAA,GAQAD,MAAA,CAAA5C,IAAA;YAPAC,UAAA,GAAA4C,WAAA,CAAA5C,UAAA;YACAI,QAAA,GAAAwC,WAAA,CAAAxC,QAAA;YACAE,eAAA,GAAAsC,WAAA,CAAAtC,eAAA;YACAC,YAAA,GAAAqC,WAAA,CAAArC,YAAA;YACAC,SAAA,GAAAoC,WAAA,CAAApC,SAAA;YACAH,cAAA,GAAAuC,WAAA,CAAAvC,cAAA;YACAwC,IAAA,OAAAC,yBAAA,CAAAC,OAAA,EAAAH,WAAA,EAAAtD,SAAA;UAEA,IAAAG,IAAA,OAAAuD,cAAA,CAAAD,OAAA,MAAAC,cAAA,CAAAD,OAAA,MACAF,IAAA;YACA1B,WAAA,EAAAuB;UAAA,EACA;UACA,IAAA1C,UAAA,CAAAiD,MAAA;YACAxD,IAAA,iBAAAO,UAAA,CAAAkD,IAAA;UACA;UACA,IAAA9C,QAAA,CAAA6C,MAAA;YACAxD,IAAA,eAAAW,QAAA,CAAA8C,IAAA;UACA;UACA,IAAA7C,cAAA,CAAA4C,MAAA;YACAxD,IAAA,qBAAAY,cAAA,CAAA6C,IAAA;UACA;UACA,IAAA5C,eAAA,CAAA2C,MAAA;YACAxD,IAAA,sBAAAa,eAAA,CAAA4C,IAAA;UACA;UACA,IAAA3C,YAAA,CAAA0C,MAAA;YACAxD,IAAA,mBAAA0D,IAAA,CAAAC,SAAA,CAAA7C,YAAA;UACA;UACA,IAAAC,SAAA,CAAAyC,MAAA;YACAxD,IAAA,gBAAA0D,IAAA,CAAAC,SAAA,CAAA5C,SAAA;UACA;UACA,IAAA6C,cAAA,EAAA5D,IAAA,EACA6D,IAAA,WAAAC,GAAA;YACA,IAAAC,IAAA,GAAAD,GAAA,CAAAC,IAAA;cAAAC,GAAA,GAAAF,GAAA,CAAAE,GAAA;YACA,IAAAD,IAAA;cACA7B,cAAA,CAAAC,KAAA,CAAA8B,MAAA;cACAf,MAAA,CAAAJ,QAAA,CAAAC,OAAA;cACAG,MAAA,CAAAZ,OAAA,CAAAC,IAAA;YACA;cACAW,MAAA,CAAAJ,QAAA,CAAAoB,KAAA,CAAAF,GAAA;YACA;UACA,GACAG,OAAA;YAAA,OAAAjB,MAAA,CAAA9C,OAAA;UAAA;QACA;MACA;IACA;IACAgE,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAA3D,OAAA,QAAAJ,IAAA,CAAAI,OAAA;MACA,IAAAA,OAAA;QACA,KAAAL,cAAA;QACA,IAAAiE,gBAAA,EAAA5D,OAAA,EACAmD,IAAA,WAAAC,GAAA;UACA,IAAAC,IAAA,GAAAD,GAAA,CAAAC,IAAA;YAAA/D,IAAA,GAAA8D,GAAA,CAAA9D,IAAA;YAAAgE,GAAA,GAAAF,GAAA,CAAAE,GAAA;UACA,IAAAD,IAAA;YACAM,MAAA,CAAA/D,IAAA,CAAAK,QAAA,GAAAX,IAAA;UACA;YACAqE,MAAA,CAAAvB,QAAA,CAAAoB,KAAA,CAAAF,GAAA;UACA;QACA,GACAG,OAAA;UAAA,OAAAE,MAAA,CAAAhE,cAAA;QAAA;MACA;QACA,KAAAyC,QAAA,CAAAyB,OAAA;MACA;IACA;IAEAC,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAAnE,IAAA,CAAAK,QAAA,QAAAL,IAAA,CAAAK,QAAA,CAAA+D,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,KAAAF,GAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}