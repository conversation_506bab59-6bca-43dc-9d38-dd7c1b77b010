<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.AppStoreOrderMapper">

    <resultMap type="AppStoreOrderVO" id="AppStoreOrderResult">
        <result property="appStoreOrderId" column="app_store_order_id"/>
        <result property="appStoreOrderNo" column="app_store_order_no"/>
        <result property="saleCompanyId" column="sale_company_id"/>
        <result property="buyCompanyId" column="buy_company_id"/>
        <result property="deliveryTime" column="delivery_time"/>
        <result property="appStorePrice" column="app_store_price"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="appStoreId" column="app_store_id"/>
        <result property="saleMemberId" column="sale_member_id"/>
        <result property="buyMemberId" column="buy_member_id"/>
        <result property="appStoreName" column="app_store_name"/>
        <result property="orderStatus" column="order_status"/>
        <result property="erweima" column="erweima"/>
        <result property="downloadUrl" column="download_url"/>
        <result property="invoiceStatus" column="invoice_status"/>
        <result property="invoiceImageUrl" column="invoice_image_url"/>
        <result property="ip"    column="ip"    />
        <result property="appWebUrl"    column="app_web_url"    />
        <result property="appWebTrialUrl"    column="app_web_trial_url"    />
        <result property="healthInspectionUrl"    column="health_inspection_url"    />
        <result property="contact"    column="contact"    />
        <result property="testToken"    column="test_token"    />
        <result property="deliveryMethod" column="delivery_method"/>
        <result property="supply" column="supply"/>
        <result property="appServer" column="app_server"/>
        <result property="phone" column="phone"/>
        <result property="payTime" column="pay_time"/>
        <result property="confirmTime" column="confirm_time"/>

        <result property="saleCompanyName"    column="sale_company_name"    />
        <result property="buyCompanyName"    column="buy_company_name"    />
    </resultMap>

    <resultMap type="AppStoreOrder" id="AppStoreOrderDetailResult">
        <result property="appStoreOrderId" column="app_store_order_id"/>
        <result property="appStoreOrderNo" column="app_store_order_no"/>
        <result property="saleCompanyId" column="sale_company_id"/>
        <result property="buyCompanyId" column="buy_company_id"/>
        <result property="deliveryTime" column="delivery_time"/>
        <result property="appStorePrice" column="app_store_price"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="appStoreId" column="app_store_id"/>
        <result property="saleMemberId" column="sale_member_id"/>
        <result property="buyMemberId" column="buy_member_id"/>
        <result property="appStoreName" column="app_store_name"/>
        <result property="orderStatus" column="order_status"/>
        <result property="erweima" column="erweima"/>
        <result property="downloadUrl" column="download_url"/>
        <result property="invoiceStatus" column="invoice_status"/>
        <result property="invoiceImageUrl" column="invoice_image_url"/>
        <result property="ip"    column="ip"    />
        <result property="appWebUrl"    column="app_web_url"    />
        <result property="appWebTrialUrl"    column="app_web_trial_url"    />
        <result property="healthInspectionUrl"    column="health_inspection_url"    />
        <result property="contact"    column="contact"    />
        <result property="testToken"    column="test_token"    />
        <result property="deliveryMethod" column="delivery_method"/>
        <result property="supply" column="supply"/>
        <result property="appServer" column="app_server"/>
        <result property="phone" column="phone"/>
        <result property="payTime" column="pay_time"/>
        <result property="confirmTime" column="confirm_time"/>

    </resultMap>

    <resultMap id="appStoreOrderCountResult" type="com.ruoyi.portalweb.vo.AppStoreOrderCountVO">
        <result property="totalCount" column="total_count"/>
        <result property="unpaidOrderCount" column="unpaid_order_count"/>
        <result property="paidOrderCount" column="paid_order_count"/>
        <result property="refundedOrderCount" column="refunded_order_count"/>
        <result property="refundingOrderCount" column="refunding_order_count"/>
        <result property="deliveryOrderCount" column="delivered_order_count"/>
        <result property="confirmedOrderCount" column="confirmed_order_count"/>
        <result property="invoiceAppliedOrderCount" column="invoice_applied_order_count"/>
    </resultMap>

    <sql id="selectAppStoreOrderVo">
        select app_store_order_id, app_store_order_no, sale_company_id, buy_company_id, delivery_time, app_store_price,invoice_status, invoice_image_url, pay_time, confirm_time,
               app_store_order.del_flag, app_store_order.create_by, app_store_order.create_time, app_store_order.update_by, app_store_order.update_time, app_store_order.remark ,
               app_store_id,sale_member_id,buy_member_id, app_store_name, order_status, erweima, download_url, supply, delivery_method, app_server, app_store_order.phone,
        ip, app_web_url, app_web_trial_url, health_inspection_url, contact, test_token, sale_company.company_name sale_company_name,buy_company.company_name buy_company_name
        from app_store_order
        LEFT JOIN company sale_company ON sale_company.company_id = app_store_order.sale_company_id
        LEFT JOIN company buy_company ON buy_company.company_id = app_store_order.buy_company_id

    </sql>

    <select id="selectAppStoreOrderList" parameterType="AppStoreOrder" resultMap="AppStoreOrderResult">
        <include refid="selectAppStoreOrderVo"/>
        <where>
            <if test="appStoreOrderNo != null  and appStoreOrderNo != ''">and app_store_order_no = #{appStoreOrderNo}
            </if>
            <if test="saleCompanyId != null ">and sale_company_id = #{saleCompanyId}</if>
            <if test="buyCompanyId != null ">and buy_company_id = #{buyCompanyId}</if>
            <if test="deliveryTime != null  and deliveryTime != ''">and delivery_time BETWEEN  #{deliveryTime} AND DATE_ADD(#{deliveryTime}, INTERVAL 1 DAY)</if>
            <if test="appStorePrice != null ">and app_store_price = #{appStorePrice}</if>
            <if test="appStoreId != null ">and app_store_id = #{appStoreId}</if>
            <if test="saleMemberId != null ">and sale_member_id = #{saleMemberId}</if>
            <if test="buyMemberId != null ">and buy_member_id = #{buyMemberId}</if>
            <if test="orderStatus != null  and orderStatus != ''">and order_status = #{orderStatus}</if>
            <if test="invoiceStatus != null and invoiceStatus != ''">and invoice_status = #{invoiceStatus}</if>
            <if test="appStoreName != null and appStoreName != ''">and app_store_name like concat('%',#{appStoreName},'%') </if>
            <if test="deliveryMethod != null and deliveryMethod != ''">and delivery_method = #{deliveryMethod}</if>
            <if test="appServer != null and appServer != ''">and app_server = #{appServer}</if>
            <if test="createTime != null ">and app_store_order.create_time  BETWEEN  #{createTime} AND DATE_ADD(#{createTime}, INTERVAL 1 DAY)</if>
            <if test="payTime != null ">and pay_time BETWEEN  #{payTime} AND DATE_ADD(#{payTime}, INTERVAL 1 DAY)</if>
            <if test="confirmTime != null ">and confirm_time BETWEEN  #{confirmTime} AND DATE_ADD(#{confirmTime}, INTERVAL 1 DAY)</if>
        </where>
    </select>

    <select id="selectAppStoreOrderByAppStoreOrderId" parameterType="Long" resultMap="AppStoreOrderResult">
        <include refid="selectAppStoreOrderVo"/>
        where app_store_order_id = #{appStoreOrderId}
    </select>
    <select id="selectAppStoreOrderCount" resultMap="appStoreOrderCountResult" parameterType="com.ruoyi.portalweb.vo.AppStoreOrderVO">
        WITH  counts as (
            SELECT
            count(*) AS total_count,
            SUM(CASE WHEN order_status = '1' THEN 1 ELSE 0 END) AS unpaid_order_count,
            SUM(CASE WHEN order_status ='2' THEN 1 ELSE 0 END) AS paid_order_count,
            SUM(CASE WHEN order_status = '3' THEN 1 ELSE 0 END) AS refunding_order_count,
            SUM(CASE WHEN order_status = '4' THEN 1 ELSE 0 END) AS refunded_ordet_count,
            SUM(CASE WHEN order_status = '5' THEN 1 ELSE 0 END) AS delivered_order_count,
            SUM(CASE WHEN order_status = '6' THEN 1 ELSE 0 END) AS confirmed_order_count,
            SUM(CASE WHEN invoice_status = 'applied' THEN 1 ELSE 0 END) AS invoice_applied_order_count
            FROM app_store_order
        <where>
            <if test="saleMemberId != null ">and sale_member_id = #{saleMemberId}</if>
            <if test="buyMemberId != null ">and buy_member_id = #{buyMemberId}</if>
        </where>
        )
        SELECT * FROM counts
    </select>
    <select id="selectAppStoreOrderByAppStoreOrderNo" parameterType="String" resultMap="AppStoreOrderResult">
        <include refid="selectAppStoreOrderVo"/>
        where app_store_order_no = #{appStoreOrderNo}
    </select>

    <insert id="insertAppStoreOrder" parameterType="AppStoreOrder" useGeneratedKeys="true"
            keyProperty="appStoreOrderId">
        insert into app_store_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appStoreOrderNo != null">app_store_order_no,</if>
            <if test="saleCompanyId != null">sale_company_id,</if>
            <if test="buyCompanyId != null">buy_company_id,</if>
            <if test="deliveryTime != null">delivery_time,</if>
            <if test="appStorePrice != null">app_store_price,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="appStoreId != null">app_store_id,</if>
            <if test="saleMemberId != null">sale_member_id,</if>
            <if test="buyMemberId != null">buy_member_id,</if>
            <if test="appStoreName != null">app_store_name,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="erweima != null">erweima,</if>
            <if test="downloadUrl != null">download_url,</if>
            <if test="invoiceStatus != null">invoice_status,</if>
            <if test="invoiceImageUrl != null">invoice_image_url,</if>
            <if test="ip != null">ip,</if>
            <if test="appWebUrl != null">app_web_url,</if>
            <if test="appWebTrialUrl != null">app_web_trial_url,</if>
            <if test="healthInspectionUrl != null">health_inspection_url,</if>
            <if test="contact != null">contact,</if>
            <if test="testToken != null">test_token,</if>
            <if test="deliveryMethod != null">delivery_method,</if>
            <if test="supply != null">supply,</if>
            <if test="appServer != null">app_server,</if>
            <if test="phone != null">phone,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="confirmTime != null">confirm_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appStoreOrderNo != null">#{appStoreOrderNo},</if>
            <if test="saleCompanyId != null">#{saleCompanyId},</if>
            <if test="buyCompanyId != null">#{buyCompanyId},</if>
            <if test="deliveryTime != null">#{deliveryTime},</if>
            <if test="appStorePrice != null">#{appStorePrice},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="appStoreId != null">#{appStoreId},</if>
            <if test="saleMemberId != null">#{saleMemberId},</if>
            <if test="buyMemberId != null">#{buyMemberId},</if>
            <if test="appStoreName != null">#{appStoreName},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="erweima != null">#{erweima},</if>
            <if test="downloadUrl != null">#{downloadUrl},</if>
            <if test="invoiceStatus != null">#{invoiceStatus},</if>
            <if test="invoiceImageUrl != null">#{invoiceImageUrl},</if>
            <if test="ip != null">#{ip},</if>
            <if test="appWebUrl != null">#{appWebUrl},</if>
            <if test="appWebTrialUrl != null">#{appWebTrialUrl},</if>
            <if test="healthInspectionUrl != null">#{healthInspectionUrl},</if>
            <if test="contact != null">#{contact},</if>
            <if test="testToken != null">#{testToken},</if>
            <if test="deliveryMethod != null">#{deliveryMethod},</if>
            <if test="supply != null">#{supply},</if>
            <if test="appServer != null">#{appServer},</if>
            <if test="phone != null">#{phone},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="confirmTime != null">#{confirmTime},</if>
        </trim>
    </insert>

    <update id="updateAppStoreOrder" parameterType="AppStoreOrder">
        update app_store_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="appStoreOrderNo != null">app_store_order_no = #{appStoreOrderNo},</if>
            <if test="saleCompanyId != null">sale_company_id = #{saleCompanyId},</if>
            <if test="buyCompanyId != null">buy_company_id = #{buyCompanyId},</if>
            <if test="deliveryTime != null">delivery_time = #{deliveryTime},</if>
            <if test="appStorePrice != null">app_store_price = #{appStorePrice},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="appStoreId != null">app_store_id = #{appStoreId},</if>
            <if test="saleMemberId != null">sale_member_id = #{saleMemberId},</if>
            <if test="buyMemberId != null">buy_member_id = #{buyMemberId},</if>
            <if test="appStoreName != null">app_store_name = #{appStoreName},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="erweima != null">erweima = #{erweima},</if>
            <if test="downloadUrl != null">download_url = #{downloadUrl},</if>
            <if test="invoiceStatus != null">invoice_status = #{invoiceStatus},</if>
            <if test="invoiceImageUrl != null">invoice_image_url = #{invoiceImageUrl},</if>
            <if test="ip != null">ip = #{ip},</if>
            <if test="appWebUrl != null">app_web_url = #{appWebUrl},</if>
            <if test="appWebTrialUrl != null">app_web_trial_url = #{appWebTrialUrl},</if>
            <if test="healthInspectionUrl != null">health_inspection_url = #{healthInspectionUrl},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="testToken != null">test_token = #{testToken},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="confirmTime != null">confirm_time = #{confirmTime},</if>
        </trim>
        where app_store_order_id = #{appStoreOrderId}
    </update>
    <update id="updateAppStoreOrderByOrderNo" parameterType="com.ruoyi.portalweb.api.domain.AppStoreOrder">
        UPDATE app_store_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="deliveryTime != null">delivery_time = #{deliveryTime},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="invoiceStatus != null">invoice_status = #{invoiceStatus},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
        </trim>
        where order_no = #{orderNo}
    </update>

    <delete id="deleteAppStoreOrderByAppStoreOrderId" parameterType="Long">
        delete from app_store_order where app_store_order_id = #{appStoreOrderId}
    </delete>

    <delete id="deleteAppStoreOrderByAppStoreOrderIds" parameterType="String">
        delete from app_store_order where app_store_order_id in
        <foreach item="appStoreOrderId" collection="array" open="(" separator="," close=")">
            #{appStoreOrderId}
        </foreach>
    </delete>
</mapper>