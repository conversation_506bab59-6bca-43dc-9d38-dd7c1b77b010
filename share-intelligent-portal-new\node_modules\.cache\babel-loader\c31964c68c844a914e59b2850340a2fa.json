{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\demand\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\demand\\index.vue", "mtime": 1750311962949}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_data", "require", "_purchaseSales", "_cryptoJs", "_interopRequireDefault", "secret<PERSON>ey", "_default", "exports", "default", "data", "fit", "demandLoading", "form", "keywords", "formInfo", "demandType", "demandTypeList", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "pageNum", "pageSize", "total", "created", "getDemandList", "methods", "_this", "gatewayDemandListShow", "displayStatus", "auditStatus", "name", "then", "res", "key", "CryptoJS", "enc", "Utf8", "parse", "decrypt", "AES", "mode", "ECB", "padding", "pad", "Pkcs7", "JSON", "stringify", "_ref", "rows", "for<PERSON>ach", "item", "scenePicture", "applicationArea", "split", "catch", "getDictsList", "code", "propertyName", "_this2", "getDicts", "changeRadio", "onSearch", "handleSizeChange", "handleCurrentChange", "goDemandDetail", "id", "routeData", "$router", "resolve", "path", "query", "window", "open", "href", "goHome", "push"], "sources": ["src/views/demand/index.vue"], "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-01-30 11:29:06\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-02-13 11:28:34\r\n-->\r\n<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/demand/demandBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div>\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-title-box\">\r\n          <div class=\"activity-divider\"></div>\r\n          <div class=\"activity-title\">链需求</div>\r\n          <div class=\"activity-divider\"></div>\r\n        </div>\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.keywords\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"activity-info-content\">\r\n        <div class=\"activity-search-type-box\">\r\n          <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n            <div class=\"activity-search-line\">\r\n              <el-form-item label=\"需求类型\" class=\"activity-search-line-item\">\r\n                <el-radio-group\r\n                  v-model=\"formInfo.demandType\"\r\n                  class=\"activity-search-radio\"\r\n                  @input=\"changeRadio\"\r\n                >\r\n                  <el-radio-button label=\"\">全部</el-radio-button>\r\n                  <el-radio-button\r\n                    v-for=\"(item, index) in demandTypeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.dictValue\"\r\n                    >{{ item.dictLabel }}</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n        <div v-loading=\"demandLoading\" v-if=\"data && data.length > 0\">\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"activity-list-item\"\r\n            @click=\"goDemandDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img\r\n                  v-if=\"item.scenePicture && item.scenePicture.length > 0\"\r\n                  :src=\"item.scenePicture[0].url\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../assets/purchaseSales/demandDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"list-item-info\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.demandTitle }}\r\n                </div>\r\n                <div\r\n                  style=\"\r\n                    margin-top: 10px;\r\n                    margin-left: 10px;\r\n                    display: flex;\r\n                    align-items: center;\r\n                  \"\r\n                >\r\n                  <div\r\n                    style=\"\r\n                      height: 30px;\r\n                      line-height: 28px;\r\n                      color: rgb(77, 77, 78);\r\n                    \"\r\n                  >\r\n                    应用领域:\r\n                  </div>\r\n                  <div\r\n                    style=\"margin-left: 10px; height: 30px; line-height: 28px\"\r\n                  >\r\n                    <el-tag\r\n                      class=\"tagStyle\"\r\n                      v-for=\"(areaItem, index) in item.applicationArea\"\r\n                      :key=\"index\"\r\n                      >{{ areaItem }}</el-tag\r\n                    >\r\n                    <!-- <el-tag style=\"margin-left: 20px\">空调外机</el-tag>\r\n                    <el-tag style=\"margin-left: 20px\">语言芯片</el-tag> -->\r\n                  </div>\r\n                </div>\r\n                <!-- <div class=\"list-item-text\">\r\n                {{ item.activityOverview }}\r\n              </div>\r\n              <div class=\"list-item-time\">{{ item.createTimeStr }}</div> -->\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"none-class\" v-else>\r\n          <el-image\r\n            style=\"width: 160px; height: 160px\"\r\n            :src=\"require('@/assets/user/none.png')\"\r\n            :fit=\"fit\"\r\n          ></el-image>\r\n          <div class=\"text\">暂无数据</div>\r\n        </div>\r\n        <div class=\"activity-page-end\">\r\n          <el-button class=\"activity-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"data && data.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { gatewayDemandListShow } from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      demandLoading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        demandType: \"\", //需求类型\r\n      },\r\n      demandTypeList: [\r\n        {\r\n          dictLabel: \"创新研发\",\r\n          dictValue: \"1\",\r\n        },\r\n        {\r\n          dictLabel: \"物料采购\",\r\n          dictValue: \"2\",\r\n        },\r\n        {\r\n          dictLabel: \"智能制造\",\r\n          dictValue: \"3\",\r\n        },\r\n        {\r\n          dictLabel: \"数字化管理\",\r\n          dictValue: \"4\",\r\n        },\r\n        {\r\n          dictLabel: \"软件服务\",\r\n          dictValue: \"5\",\r\n        },\r\n        {\r\n          dictLabel: \"供应链金融\",\r\n          dictValue: \"6\",\r\n        },\r\n        {\r\n          dictLabel: \"运营宣传\",\r\n          dictValue: \"7\",\r\n        },\r\n        {\r\n          dictLabel: \"其他\",\r\n          dictValue: \"8\",\r\n        },\r\n      ], //活动类型列表\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    // this.getDictsList(\"activity_type\", \"activityTypeList\");\r\n    this.getDemandList();\r\n  },\r\n  methods: {\r\n    // 找需求\r\n    getDemandList() {\r\n      this.demandLoading = true;\r\n      gatewayDemandListShow({\r\n        demandType: this.formInfo.demandType,\r\n        // city: \"青岛市\",\r\n        // region: \"城阳区\",\r\n        displayStatus: 1,\r\n        auditStatus: 2,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        name: this.form.keywords,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          this.demandLoading = false;\r\n          let { rows } = res || [];\r\n          this.data = rows;\r\n          this.total = res.total;\r\n          this.data.forEach((item) => {\r\n            if (item.scenePicture) {\r\n              item.scenePicture = JSON.parse(item.scenePicture);\r\n            }\r\n            if (item.applicationArea) {\r\n              item.applicationArea = item.applicationArea.split(\",\");\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.demandLoading = false;\r\n          this.data = [];\r\n        });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getDemandList();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.getDemandList();\r\n    },\r\n    // 跳转到需求页面\r\n    goDemandDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/demandHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-info-content {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    .activity-search-type-box {\r\n      background: #fff;\r\n      margin-bottom: -7px;\r\n      .activity-search-line {\r\n        padding: 14px 24px 4px;\r\n        .activity-search-line-item {\r\n          margin-bottom: 0;\r\n        }\r\n        & + .activity-search-line {\r\n          border-top: 1px solid #f5f5f5;\r\n        }\r\n      }\r\n    }\r\n    .activity-list-item {\r\n      width: 100%;\r\n      background: #fff;\r\n      border-radius: 12px;\r\n      margin-top: 24px;\r\n      .list-item-content {\r\n        display: flex;\r\n        padding: 24px 32px;\r\n        cursor: pointer;\r\n        .list-item-img {\r\n          width: 230px;\r\n          height: 164px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n            border-radius: 5px;\r\n          }\r\n        }\r\n        .list-item-info {\r\n          margin: auto 0;\r\n          padding-left: 24px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          .list-item-title {\r\n            width: 806px;\r\n            // height: 24px;\r\n            text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n            white-space: nowrap; /*让文字不换行*/\r\n            overflow: hidden; /*超出要隐藏*/\r\n            font-size: 24px;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            // line-height: 24px;\r\n            margin: 8px 0 24px;\r\n            word-wrap: break-word;\r\n          }\r\n          .list-item-text {\r\n            width: 806px;\r\n            height: 60px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            text-overflow: ellipsis;\r\n            font-size: 16px;\r\n            color: #666;\r\n            line-height: 30px;\r\n            word-wrap: break-word;\r\n          }\r\n          .list-item-time {\r\n            color: #999;\r\n            line-height: 14px;\r\n            margin-top: 24px;\r\n          }\r\n          .tagStyle {\r\n            margin-left: 20px;\r\n          }\r\n          .tagStyle:nth-child(1) {\r\n            margin-left: 0;\r\n          }\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .activity-page-end {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin: 0 auto;\r\n      padding: 24px 0 60px;\r\n      .activity-page-btn {\r\n        width: 82px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    padding: 8% 0;\r\n    background: #fff;\r\n    margin-top: 25px;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;AAsJA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAC,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAI,SAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;MACAC,aAAA;MACAC,IAAA;QACAC,QAAA;MACA;MACAC,QAAA;QACAC,UAAA;MACA;MACAC,cAAA,GACA;QACAC,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,EACA;MAAA;MACAT,IAAA;MACAU,OAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,KAAA;MACA,KAAAd,aAAA;MACA,IAAAe,oCAAA;QACAX,UAAA,OAAAD,QAAA,CAAAC,UAAA;QACA;QACA;QACAY,aAAA;QACAC,WAAA;QACAT,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA,QAAA;QACAS,IAAA,OAAAjB,IAAA,CAAAC;MACA,GACAiB,IAAA,WAAAC,GAAA;QACA,IAAAC,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAA/B,SAAA;QACA,IAAAgC,OAAA,GAAAJ,iBAAA,CAAAK,GAAA,CAAAD,OAAA,CAAAN,GAAA,EAAAC,GAAA;UACAO,IAAA,EAAAN,iBAAA,CAAAM,IAAA,CAAAC,GAAA;UACAC,OAAA,EAAAR,iBAAA,CAAAS,GAAA,CAAAC;QACA;QACAZ,GAAA,GAAAa,IAAA,CAAAR,KAAA,CAAAH,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAU,SAAA,CAAAR,OAAA;QACAZ,KAAA,CAAAd,aAAA;QACA,IAAAmC,IAAA,GAAAf,GAAA;UAAAgB,IAAA,GAAAD,IAAA,CAAAC,IAAA;QACAtB,KAAA,CAAAhB,IAAA,GAAAsC,IAAA;QACAtB,KAAA,CAAAJ,KAAA,GAAAU,GAAA,CAAAV,KAAA;QACAI,KAAA,CAAAhB,IAAA,CAAAuC,OAAA,WAAAC,IAAA;UACA,IAAAA,IAAA,CAAAC,YAAA;YACAD,IAAA,CAAAC,YAAA,GAAAN,IAAA,CAAAR,KAAA,CAAAa,IAAA,CAAAC,YAAA;UACA;UACA,IAAAD,IAAA,CAAAE,eAAA;YACAF,IAAA,CAAAE,eAAA,GAAAF,IAAA,CAAAE,eAAA,CAAAC,KAAA;UACA;QACA;MACA,GACAC,KAAA;QACA5B,KAAA,CAAAd,aAAA;QACAc,KAAA,CAAAhB,IAAA;MACA;IACA;IACA;IACA6C,YAAA,WAAAA,aAAAC,IAAA,EAAAC,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,cAAA,EAAAH,IAAA,EAAAzB,IAAA,WAAAC,GAAA;QACA0B,MAAA,CAAAD,YAAA,IAAAzB,GAAA,CAAAtB,IAAA;MACA;IACA;IACAkD,WAAA,WAAAA,YAAA;MACA,KAAAC,QAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAzC,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAwC,QAAA;IACA;IACAE,mBAAA,WAAAA,oBAAA3C,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAI,aAAA;IACA;IACAqC,QAAA,WAAAA,SAAA;MACA,KAAAzC,OAAA;MACA,KAAAI,aAAA;IACA;IACA;IACAwC,cAAA,WAAAA,eAAAC,EAAA;MACA,IAAAC,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACAC,KAAA;UAAAL,EAAA,EAAAA;QAAA;MACA;MACAM,MAAA,CAAAC,IAAA,CAAAN,SAAA,CAAAO,IAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAP,OAAA,CAAAQ,IAAA;QAAAN,IAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}