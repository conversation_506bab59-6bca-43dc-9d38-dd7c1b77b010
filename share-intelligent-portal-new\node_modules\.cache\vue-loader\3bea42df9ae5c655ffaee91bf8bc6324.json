{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\menu\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\menu\\index.vue", "mtime": 1750311963032}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0TWVudSwgZ2V0TWVudSwgZGVsTWVudSwgYWRkTWVudSwgdXBkYXRlTWVudSB9IGZyb20gIkAvYXBpL3N5c3RlbS9tZW51IjsNCmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsNCmltcG9ydCAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiOw0KaW1wb3J0IEljb25TZWxlY3QgZnJvbSAiQC9jb21wb25lbnRzL0ljb25TZWxlY3QiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJNZW51IiwNCiAgZGljdHM6IFsnc3lzX3Nob3dfaGlkZScsICdzeXNfbm9ybWFsX2Rpc2FibGUnXSwNCiAgY29tcG9uZW50czogeyBUcmVlc2VsZWN0LCBJY29uU2VsZWN0IH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOiPnOWNleihqOagvOagkeaVsOaNrg0KICAgICAgbWVudUxpc3Q6IFtdLA0KICAgICAgLy8g6I+c5Y2V5qCR6YCJ6aG5DQogICAgICBtZW51T3B0aW9uczogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmmK/lkKblsZXlvIDvvIzpu5jorqTlhajpg6jmipjlj6ANCiAgICAgIGlzRXhwYW5kQWxsOiBmYWxzZSwNCiAgICAgIC8vIOmHjeaWsOa4suafk+ihqOagvOeKtuaAgQ0KICAgICAgcmVmcmVzaFRhYmxlOiB0cnVlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBtZW51TmFtZTogdW5kZWZpbmVkLA0KICAgICAgICB2aXNpYmxlOiB1bmRlZmluZWQNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBtZW51TmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLoj5zljZXlkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBvcmRlck51bTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLoj5zljZXpobrluo/kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBwYXRoOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIui3r+eUseWcsOWdgOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g6YCJ5oup5Zu+5qCHDQogICAgc2VsZWN0ZWQobmFtZSkgew0KICAgICAgdGhpcy5mb3JtLmljb24gPSBuYW1lOw0KICAgIH0sDQogICAgLyoqIOafpeivouiPnOWNleWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdE1lbnUodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMubWVudUxpc3QgPSB0aGlzLmhhbmRsZVRyZWUocmVzcG9uc2UuZGF0YSwgIm1lbnVJZCIpOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOi9rOaNouiPnOWNleaVsOaNrue7k+aehCAqLw0KICAgIG5vcm1hbGl6ZXIobm9kZSkgew0KICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgIW5vZGUuY2hpbGRyZW4ubGVuZ3RoKSB7DQogICAgICAgIGRlbGV0ZSBub2RlLmNoaWxkcmVuOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgaWQ6IG5vZGUubWVudUlkLA0KICAgICAgICBsYWJlbDogbm9kZS5tZW51TmFtZSwNCiAgICAgICAgY2hpbGRyZW46IG5vZGUuY2hpbGRyZW4NCiAgICAgIH07DQogICAgfSwNCiAgICAvKiog5p+l6K+i6I+c5Y2V5LiL5ouJ5qCR57uT5p6EICovDQogICAgZ2V0VHJlZXNlbGVjdCgpIHsNCiAgICAgIGxpc3RNZW51KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMubWVudU9wdGlvbnMgPSBbXTsNCiAgICAgICAgY29uc3QgbWVudSA9IHsgbWVudUlkOiAwLCBtZW51TmFtZTogJ+S4u+exu+ebricsIGNoaWxkcmVuOiBbXSB9Ow0KICAgICAgICBtZW51LmNoaWxkcmVuID0gdGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJtZW51SWQiKTsNCiAgICAgICAgdGhpcy5tZW51T3B0aW9ucy5wdXNoKG1lbnUpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBtZW51SWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgcGFyZW50SWQ6IDAsDQogICAgICAgIG1lbnVOYW1lOiB1bmRlZmluZWQsDQogICAgICAgIGljb246IHVuZGVmaW5lZCwNCiAgICAgICAgbWVudVR5cGU6ICJNIiwNCiAgICAgICAgb3JkZXJOdW06IHVuZGVmaW5lZCwNCiAgICAgICAgaXNGcmFtZTogIjEiLA0KICAgICAgICBpc0NhY2hlOiAiMCIsDQogICAgICAgIHZpc2libGU6ICIwIiwNCiAgICAgICAgc3RhdHVzOiAiMCINCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLmdldFRyZWVzZWxlY3QoKTsNCiAgICAgIGlmIChyb3cgIT0gbnVsbCAmJiByb3cubWVudUlkKSB7DQogICAgICAgIHRoaXMuZm9ybS5wYXJlbnRJZCA9IHJvdy5tZW51SWQ7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmZvcm0ucGFyZW50SWQgPSAwOw0KICAgICAgfQ0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6I+c5Y2VIjsNCiAgICB9LA0KICAgIC8qKiDlsZXlvIAv5oqY5Y+g5pON5L2cICovDQogICAgdG9nZ2xlRXhwYW5kQWxsKCkgew0KICAgICAgdGhpcy5yZWZyZXNoVGFibGUgPSBmYWxzZTsNCiAgICAgIHRoaXMuaXNFeHBhbmRBbGwgPSAhdGhpcy5pc0V4cGFuZEFsbDsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgdGhpcy5yZWZyZXNoVGFibGUgPSB0cnVlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5nZXRUcmVlc2VsZWN0KCk7DQogICAgICBnZXRNZW51KHJvdy5tZW51SWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueiPnOWNlSI7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbigpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0ubWVudUlkICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgdXBkYXRlTWVudSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZE1lbnUodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5ZCN56ew5Li6IicgKyByb3cubWVudU5hbWUgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxNZW51KHJvdy5tZW51SWQpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqRA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/menu", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"菜单名称\" prop=\"menuName\">\r\n        <el-input\r\n          v-model=\"queryParams.menuName\"\r\n          placeholder=\"请输入菜单名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select v-model=\"queryParams.status\" placeholder=\"菜单状态\" clearable>\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:menu:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-sort\"\r\n          size=\"mini\"\r\n          @click=\"toggleExpandAll\"\r\n        >展开/折叠</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table\r\n      v-if=\"refreshTable\"\r\n      v-loading=\"loading\"\r\n      :data=\"menuList\"\r\n      row-key=\"menuId\"\r\n      :default-expand-all=\"isExpandAll\"\r\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n    >\r\n      <el-table-column prop=\"menuName\" label=\"菜单名称\" :show-overflow-tooltip=\"true\" width=\"160\"></el-table-column>\r\n      <el-table-column prop=\"icon\" label=\"图标\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <svg-icon :icon-class=\"scope.row.icon\" />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"orderNum\" label=\"排序\" width=\"60\"></el-table-column>\r\n      <el-table-column prop=\"perms\" label=\"权限标识\" :show-overflow-tooltip=\"true\"></el-table-column>\r\n      <el-table-column prop=\"component\" label=\"组件路径\" :show-overflow-tooltip=\"true\"></el-table-column>\r\n      <el-table-column prop=\"status\" label=\"状态\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button \r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:menu:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"handleAdd(scope.row)\"\r\n            v-hasPermi=\"['system:menu:add']\"\r\n          >新增</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:menu:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <!-- 添加或修改菜单对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"680px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"上级菜单\" prop=\"parentId\">\r\n              <treeselect\r\n                v-model=\"form.parentId\"\r\n                :options=\"menuOptions\"\r\n                :normalizer=\"normalizer\"\r\n                :show-count=\"true\"\r\n                placeholder=\"选择上级菜单\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"菜单类型\" prop=\"menuType\">\r\n              <el-radio-group v-model=\"form.menuType\">\r\n                <el-radio label=\"M\">目录</el-radio>\r\n                <el-radio label=\"C\">菜单</el-radio>\r\n                <el-radio label=\"F\">按钮</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item label=\"菜单图标\" prop=\"icon\">\r\n              <el-popover\r\n                placement=\"bottom-start\"\r\n                width=\"460\"\r\n                trigger=\"click\"\r\n                @show=\"$refs['iconSelect'].reset()\"\r\n              >\r\n                <IconSelect ref=\"iconSelect\" @selected=\"selected\" />\r\n                <el-input slot=\"reference\" v-model=\"form.icon\" placeholder=\"点击选择图标\" readonly>\r\n                  <svg-icon\r\n                    v-if=\"form.icon\"\r\n                    slot=\"prefix\"\r\n                    :icon-class=\"form.icon\"\r\n                    class=\"el-input__icon\"\r\n                    style=\"height: 32px;width: 16px;\"\r\n                  />\r\n                  <i v-else slot=\"prefix\" class=\"el-icon-search el-input__icon\" />\r\n                </el-input>\r\n              </el-popover>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"菜单名称\" prop=\"menuName\">\r\n              <el-input v-model=\"form.menuName\" placeholder=\"请输入菜单名称\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\r\n              <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item prop=\"isFrame\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择是外链则路由地址需要以`http(s)://`开头\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                是否外链\r\n              </span>\r\n              <el-radio-group v-model=\"form.isFrame\">\r\n                <el-radio label=\"0\">是</el-radio>\r\n                <el-radio label=\"1\">否</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item prop=\"path\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                路由地址\r\n              </span>\r\n              <el-input v-model=\"form.path\" placeholder=\"请输入路由地址\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\r\n            <el-form-item prop=\"component\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"访问的组件路径，如：`system/user/index`，默认在`views`目录下\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                组件路径\r\n              </span>\r\n              <el-input v-model=\"form.component\" placeholder=\"请输入组件路径\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'M'\">\r\n            <el-form-item prop=\"perms\">\r\n              <el-input v-model=\"form.perms\" placeholder=\"请输入权限标识\" maxlength=\"100\" />\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                权限字符\r\n              </span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\r\n            <el-form-item prop=\"query\">\r\n              <el-input v-model=\"form.query\" placeholder=\"请输入路由参数\" maxlength=\"255\" />\r\n              <span slot=\"label\">\r\n                <el-tooltip content='访问路由的默认传递参数，如：`{\"id\": 1, \"name\": \"ry\"}`' placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                路由参数\r\n              </span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType == 'C'\">\r\n            <el-form-item prop=\"isCache\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                是否缓存\r\n              </span>\r\n              <el-radio-group v-model=\"form.isCache\">\r\n                <el-radio label=\"0\">缓存</el-radio>\r\n                <el-radio label=\"1\">不缓存</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item prop=\"visible\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                显示状态\r\n              </span>\r\n              <el-radio-group v-model=\"form.visible\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_show_hide\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\" v-if=\"form.menuType != 'F'\">\r\n            <el-form-item prop=\"status\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择停用则路由将不会出现在侧边栏，也不能被访问\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                菜单状态\r\n              </span>\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listMenu, getMenu, delMenu, addMenu, updateMenu } from \"@/api/system/menu\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport IconSelect from \"@/components/IconSelect\";\r\n\r\nexport default {\r\n  name: \"Menu\",\r\n  dicts: ['sys_show_hide', 'sys_normal_disable'],\r\n  components: { Treeselect, IconSelect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 菜单表格树数据\r\n      menuList: [],\r\n      // 菜单树选项\r\n      menuOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否展开，默认全部折叠\r\n      isExpandAll: false,\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 查询参数\r\n      queryParams: {\r\n        menuName: undefined,\r\n        visible: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        menuName: [\r\n          { required: true, message: \"菜单名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        orderNum: [\r\n          { required: true, message: \"菜单顺序不能为空\", trigger: \"blur\" }\r\n        ],\r\n        path: [\r\n          { required: true, message: \"路由地址不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    // 选择图标\r\n    selected(name) {\r\n      this.form.icon = name;\r\n    },\r\n    /** 查询菜单列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listMenu(this.queryParams).then(response => {\r\n        this.menuList = this.handleTree(response.data, \"menuId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 转换菜单数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.menuId,\r\n        label: node.menuName,\r\n        children: node.children\r\n      };\r\n    },\r\n    /** 查询菜单下拉树结构 */\r\n    getTreeselect() {\r\n      listMenu().then(response => {\r\n        this.menuOptions = [];\r\n        const menu = { menuId: 0, menuName: '主类目', children: [] };\r\n        menu.children = this.handleTree(response.data, \"menuId\");\r\n        this.menuOptions.push(menu);\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        menuId: undefined,\r\n        parentId: 0,\r\n        menuName: undefined,\r\n        icon: undefined,\r\n        menuType: \"M\",\r\n        orderNum: undefined,\r\n        isFrame: \"1\",\r\n        isCache: \"0\",\r\n        visible: \"0\",\r\n        status: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      this.getTreeselect();\r\n      if (row != null && row.menuId) {\r\n        this.form.parentId = row.menuId;\r\n      } else {\r\n        this.form.parentId = 0;\r\n      }\r\n      this.open = true;\r\n      this.title = \"添加菜单\";\r\n    },\r\n    /** 展开/折叠操作 */\r\n    toggleExpandAll() {\r\n      this.refreshTable = false;\r\n      this.isExpandAll = !this.isExpandAll;\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      this.getTreeselect();\r\n      getMenu(row.menuId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改菜单\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.menuId != undefined) {\r\n            updateMenu(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addMenu(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal.confirm('是否确认删除名称为\"' + row.menuName + '\"的数据项？').then(function() {\r\n        return delMenu(row.menuId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}