import request from '@/utils/request'

// 查询制造订单列表
export function listManufactureOrder(query) {
  return request({
    url: '/system/manufactureOrder/list',
    method: 'get',
    params: query
  })
}

// 查询制造订单详细
export function getManufactureOrder(id) {
  return request({
    // url: '/system/manufactureOrder/' + id,
    url: '/system/manufactureOrder/withMaterials/' + id,
    method: 'get'
  })
}

// 新增制造订单
export function addManufactureOrder(data) {
  return request({
    // url: '/system/manufactureOrder',
    url: '/system/manufactureOrder/withMaterials',
    method: 'post',
    data: data
  })
}

// 修改制造订单
export function updateManufactureOrder(data) {
  return request({
    // url: '/system/manufactureOrder',
    url: '/system/manufactureOrder/withMaterials',
    method: 'put',
    data: data
  })
}

// 删除制造订单
export function delManufactureOrder(id) {
  return request({
    url: '/system/manufactureOrder/' + id,
    method: 'delete'
  })
}
