<template>
  <div class="bc-verification-code">
    <el-input
      ref="input"
      v-model="hValue"
      autocomplete="off"
      class="bc-verification-input"
      :size="size"
      :maxlength="maxlength"
      :placeholder="placeholder"
    >
      <img
        slot="prefix"
        src="../../assets/login/mailIcon.png"
        alt=""
        class="input-icon"
      />
      <el-button
        slot="suffix"
        type="text"
        class="sendCode"
        :size="size"
        :disabled="msgKey"
        :loading="sendLoading"
        @click="handleSendCode"
      >
        {{ msgKey ? `${msgTime} S后重新发送` : "发送验证码" }}
      </el-button>
    </el-input>
  </div>
</template>

<script>
import { getCommonCode } from "@/api/login";

export default {
  props: {
    value: String,
    mobile: {
      type: [String, Object],
      default: "",
    },
    placeholder: {
      type: String,
      default: "请输入验证码",
    },
    size: String,
    beforeSendCode: Function,
    sendParams: {
      type: Array,
      default() {
        return [];
      },
    },
    showIcon: {
      type: Boolean,
      default: true,
    },
    maxlength: {
      type: Number,
      default: 6,
    },
  },
  data() {
    return {
      msgText: "",
      msgTime: "",
      msgKey: false,
      sendLoading: false,
    };
  },
  computed: {
    hValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
    config() {
      return {
        MSGINIT: "发送验证码",
        MSGSCUCCESS: "秒后重发",
        MSGTIME: 60,
      };
    },
  },
  created() {
    this.msgText = this.config.MSGINIT;
    this.msgTime = this.config.MSGTIME;
  },
  methods: {
    handleSendCode() {
      this.hValue = "";
      if (this.beforeSendCode) {
        let result = this.beforeSendCode();
        if (result === false) {
          return;
        }
        if (result !== true) {
          Promise.resolve(result).then(() => {
            this.sendCode();
          });
        }
      } else {
        this.sendCode();
      }
    },
    sendCode() {
      if (!this.mobile.phone) {
        this.$message.warning("请输入账号");
        return;
      }
      this.sendLoading = true;
      this.msgText = "发送中";
      let params = [];
      if (this.mobile) {
        params.push(this.mobile);
      }
      if (this.sendParams) {
        params = params.concat(this.sendParams);
      }
      getCommonCode({ telphone: this.mobile.phone })
        .then((res) => {
          if (res.code !== 200) {
            this.$message.error(res.msg);
            this.sendLoading = false;
            return;
          }
          this.$emit("after-send", res.data || {});
          this.sendLoading = false;
          this.msgText = this.msgTime + this.config.MSGSCUCCESS;
          this.msgKey = true;
          this.$refs.input.focus();
          const time = setInterval(() => {
            this.msgTime--;
            this.msgText = this.msgTime + this.config.MSGSCUCCESS;
            if (this.msgTime === 0) {
              this.msgTime = this.config.MSGTIME;
              this.msgText = this.config.MSGINIT;
              this.msgKey = false;
              clearInterval(time);
            }
          }, 1000);
        })
        .catch(() => {
          this.sendLoading = false;
          this.msgText = this.config.MSGINIT;
        });
    },
  },
};
</script>

<style lang="scss">
.bc-verification-code {
  display: flex;
  .bc-verification-input {
    flex: 1;
    .el-input__inner {
      // width: 400px;
      height: 40px;
      background: #fff;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      font-size: 14px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 14px;
      padding-left: 40px;
    }
    .input-icon {
      width: 16px;
      height: 16px;
      margin: 12px;
    }
  }
  .el-button {
    margin-left: 10px;
    height: 40px !important;
    line-height: 40px;
    padding: 0;
    float: right;
  }
  .sendCode {
    margin-right: 12px;
    text-decoration: inherit;
    font-family: PingFangSC-Regular, PingFang SC;
    color: #21c9b8;
    line-height: 16px;
  }
  .el-button.is-disabled {
    color: #cfcfcf;
  }
}
</style>
