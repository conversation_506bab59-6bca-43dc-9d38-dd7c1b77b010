<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-03-24 09:12:12
 * @Description:
 * @LastEditors: zhc
-->
<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24">
        <div class="team-manage-container">
          <div class="header-small">
            <div class="red-tag"></div>
            企业员工
          </div>
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="员工列表" name="first">
              <div class="staff-list">
                <div
                  class="staff-list-item"
                  v-for="item in records"
                  v-bind:key="item.userId"
                >
                  <el-image
                    style="width: 42px; height: 42px; border-radius: 50%"
                    :fit="fit"
                    :src="item.avatar"
                  ></el-image>
                  <div class="user-name">{{ item.realName }}</div>
                  <div
                    v-if="item.managerType == '1'"
                    class="user-type-admin-class"
                  >
                    管理员
                  </div>
                  <div v-else class="user-type-stuff-class">员工</div>
                  <div class="user-phone">{{ item.phonenumber }}</div>
                  <div class="actions-container">
                    <a
                      class="remove-button"
                      @click="removeUser(item.userId)"
                      v-if="isAdmin && item.userId != user.userId"
                      >请离</a
                    >
                    <a
                      class="trans-button"
                      @click="handleTransDialogOpen"
                      v-if="isAdmin && item.userId == user.userId"
                      >管理员转移</a
                    >
                  </div>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="申请记录" name="second">
              <div class="staff-list">
                <div class="none-class" v-if="!applys || applys.length == 0">
                  <el-image
                    style="width: 160px; height: 160px"
                    :src="require('@/assets/user/none.png')"
                    :fit="fit"
                  ></el-image>
                  <div class="text">暂无数据</div>
                </div>
                <div
                  class="staff-list-item"
                  v-for="item in applys"
                  v-else
                  v-bind:key="item.id"
                >
                  <el-image
                    style="width: 42px; height: 42px; border-radius: 50%"
                    :fit="fit"
                    :src="item.avatar"
                  ></el-image>
                  <div class="user-name">{{ item.realName }}</div>
                  <div class="user-phone apply-phone">{{ item.userPhone }}</div>
                  <div class="create-time">{{ item.createTime }}</div>

                  <div class="actions-container">
                    <a
                      class="remove-button refuse"
                      @click="applyRefuse(item.id)"
                      v-if="isAdmin && item.status == '0'"
                      >拒绝</a
                    >
                    <a
                      class="trans-button agree"
                      @click="applyAgree(item.id)"
                      v-if="isAdmin && item.status == '0'"
                      >同意</a
                    >
                    <div class="already-accept" v-if="item.status == '1'">
                      已同意
                    </div>
                    <div class="already-refuse" v-if="item.status == '2'">
                      已拒绝
                    </div>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>

        <el-pagination
          v-show="total > 0"
          background
          class="team-manage-pagination"
          layout="prev, pager, next"
          :page-size="7"
          :current-page.sync="queryParams.pageNum"
          @current-change="handleCurrentChange"
          :total="total"
        >
        </el-pagination>
        <el-dialog
          :visible.sync="transDialogVisible"
          width="450px"
          :before-close="handleTransDialogClose"
        >
          <el-form
            ref="transForm"
            :model="transForm"
            :rules="transRules"
            class="trans-form"
          >
            <div class="header">请输入验证码，确定管理员身份</div>
            <el-form-item prop="userName" label="手机号：">
              {{ transForm.username }}
            </el-form-item>
            <el-form-item prop="smsCode" label="验证码：">
              <el-input
                v-model="transForm.smsCode"
                auto-complete="off"
                placeholder="请输入短信验证码"
                style="width: 100%"
                :maxlength="6"
              >
                <template slot="suffix">
                  <a @click="getSmsCode" class="active-style" v-if="!showCode"
                    >获取验证码</a
                  >
                  <span class="disabled-style" v-else
                    >{{ countMiao }}秒</span
                  ></template
                >
              </el-input>
              <div class="login-code"></div>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="handleTransDialogClose">取 消</el-button>
            <el-button
              type="primary"
              class="confirm-button"
              @click="submitSmsCode"
              >确 定</el-button
            >
          </span>
        </el-dialog>
        <el-dialog
          :visible.sync="phoneDialogVisible"
          width="450px"
          :before-close="handlePhoneDialogClose"
        >
          <el-form
            ref="transForm"
            :model="phoneForm"
            :rules="phoneRules"
            class="trans-form"
          >
            <div class="header">转移管理员</div>
            <el-form-item prop="userName">
              <el-select
                v-model="phoneForm.userId"
                filterable
                remote
                :remote-method="getTransUserList"
                :loading="loading"
                placeholder="请输入姓名"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.userId"
                  :label="item.realName"
                  :value="item.userId"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="handlePhoneDialogClose">取 消</el-button>
            <el-button
              type="primary"
              class="confirm-button"
              @click="submitTransPhone"
              >确 定</el-button
            >
          </span>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import UserMenu from "../components/userMenu.vue";
import {
  getCompanyUserList,
  checkManagerAuth,
  askResignation,
  checkSmsCode,
  getApplyList,
  applyAgree,
  transferManager,
  applyRefuse,
} from "@/api/system/team.js";
import { getCommonCode } from "@/api/login";

import store from "@/store";

import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";
export default {
  name: "TeamManage",
  components: { UserMenu },
  data() {
    return {
      activeName: "first",
      fit: "cover",
      transDialogVisible: false,
      phoneDialogVisible: false,
      records: [],
      applys: [],
      isAdmin: false,
      showCode: false,
      loading: false,
      countMiao: 60,
      transForm: {},
      transRules: {},
      phoneForm: {},
      phoneRules: {},
      userList: [],

      queryParams: {
        pageNum: 1,
        pageSize: 7,
      },

      total: 0,
      user: {
        userId: store.getters.userId,
        bussinessNo: store.getters.bussinessNo,
        phonenumber: store.getters.phonenumber,
      },
      transForm: {},
    };
  },
  created() {
    this.getUserList();
    this.checkManagerAuth();
    // this.getUserList();
    // this.getApplyList();
  },
  methods: {
    handleClick(tab, event) {
      if (this.activeName == "first") {
        this.getUserList();
        this.checkManagerAuth();
      } else {
        this.getApplyList();
      }
      this.queryParams = {
        pageNum: 1,
        pageSize: 7,
      };
    },
    handleTransDialogClose() {
      this.transDialogVisible = false;
      this.transForm = {};
    },
    handlePhoneDialogOpen() {
      this.phoneDialogVisible = true;
    },
    handlePhoneDialogClose() {
      this.phoneDialogVisible = false;
      this.phoneForm = {};
    },
    handleTransDialogOpen() {
      (this.transForm.username = this.user.phonenumber),
        (this.transDialogVisible = true);
    },
    submitSmsCode() {
      if (!this.transForm.smsCode) {
        this.$message({
          message: "请输入验证码",
          type: "warning",
        });
        return;
      }
      checkSmsCode({
        userName: this.transForm.username,
        smsCode: this.transForm.smsCode,
      }).then((res) => {
        if (res.code == 200) {
          this.handleTransDialogClose();
          this.handlePhoneDialogOpen();
        }
      });
    },
    applyAgree(id) {
      applyAgree(id).then((res) => {
        if (res.code == 200) {
          this.$modal.msgSuccess("操作成功");
          this.getApplyList();
          this.checkManagerAuth();
        }
      });
    },
    applyRefuse(id) {
      applyRefuse(id).then((res) => {
        if (res.code == 200) {
          this.$modal.msgSuccess("操作成功");
          this.getApplyList();
          this.checkManagerAuth();
        }
      });
    },

    submitTransPhone() {
      transferManager({
        userId: this.phoneForm.userId,
      }).then((res) => {
        if (res.code == 200) {
          this.handlePhoneDialogClose();
          this.$modal.msgSuccess("操作成功");
          this.getUserList();
          this.checkManagerAuth();
        }
      });
    },
    // 获取验证码
    getSmsCode() {
      if (!this.transForm.username) {
        this.$message({
          message: "请输入账号",
          type: "warning",
        });
        return;
      }
      getCommonCode({ telphone: this.transForm.username }).then(() => {
        this.countTime();
      });
    },
    // 验证码倒计时
    countTime() {
      this.showCode = true;
      var times = setInterval(() => {
        this.countMiao--; //递减
        if (this.countMiao < 0) {
          // <=0 变成获取按钮
          this.showCode = false;
          clearInterval(times);
        }
      }, 1000);
    },
    getUserList() {
      getCompanyUserList({
        ...this.queryParams,
        bussinessNo: this.user.bussinessNo,
      }).then((response) => {
        this.records = response.rows;
        this.total = response.total;
      });
    },
    getTransUserList(res) {
      getCompanyUserList({
        ...this.queryParams,
        bussinessNo: this.user.bussinessNo,
        realName: res,
      }).then((response) => {
        this.userList = response.rows;
      });
    },
    handleCurrentChange(pageNum) {
      this.queryParams.pageNum = pageNum;
      if (this.activeName == "first") {
        this.getUserList();
        this.checkManagerAuth();
      } else {
        this.getApplyList();
      }
    },
    getApplyList() {
      getApplyList(this.queryParams).then((response) => {
        let key = CryptoJS.enc.Utf8.parse(secretKey);
        let decrypt = CryptoJS.AES.decrypt(response, key, {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7,
        });
        res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
        this.applys = response.rows;
        this.total = response.total;
      });
    },
    removeUser(id) {
      this.$modal
        .confirm("是否确认请离该员工")
        .then(() => {
          askResignation({ userId: id }).then((response) => {
            if (response.code == 200) {
              this.$modal.msgSuccess("操作成功");
              this.getUserList();
            }
          });
        })
        .catch(() => {});
    },
    checkManagerAuth() {
      checkManagerAuth().then((response) => {
        if (response.code == 200) {
          this.isAdmin = true;
        } else {
          this.isAdmin = false;
        }
      });
    },
  },
};
</script>

<style lang="scss">
.app-container {
  background: #f4f5f9;
  .team-manage-container {
    width: 100%;
    background: #fff;
    height: 700px;
    padding: 20px;
    .header-small {
      text-align: center;
      display: flex;
      font-size: 20px;
      font-weight: 500;
      color: #333333;
      line-height: 20px;

      .red-tag {
        margin-right: 12px;
        width: 3px;
        height: 22px;
        background: #21c9b8;
      }
    }
    .staff-list {
      width: 100%;
      padding: 0 30px;
      .none-class {
        text-align: center;
        padding: 10% 0;
        .text {
          font-size: 14px;
          font-weight: 400;
          color: #999999;
          line-height: 14px;
        }
      }
      .staff-list-item {
        display: flex;
        margin-top: 20px;
        width: 100%;
        height: 50px;
        border-bottom: 1px solid #e8e8e8;

        .user-name {
          font-size: 14px;
          width: 20%;
          margin-left: 12px;
          font-weight: 500;
          color: #333333;
          line-height: 40px;
        }

        .create-time {
          font-size: 14px;
          width: 300px;
          margin-left: 12px;
          color: #333333;
          line-height: 40px;
        }
        .user-type-stuff-class {
          width: 48px;
          margin: 10px 10% 0 10%;
          height: 24px;
          text-align: center;
          background: rgba(33, 77, 197, 0.1);
          border-radius: 4px;
          font-size: 12px;
          font-weight: 400;
          color: #214dc5;
          line-height: 24px;
        }
        .user-type-admin-class {
          width: 60px;
          height: 24px;
          background: rgba(197, 37, 33, 0.1);
          border-radius: 4px;
          margin: 10px 10% 0 10%;
          text-align: center;
          font-size: 12px;
          font-weight: 400;
          color: #21c9b8;
          line-height: 24px;
        }
        .user-phone {
          margin: 0 10%;
          font-size: 14px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #333333;
          line-height: 40px;
        }
        .apply-phone {
          text-align: left;
        }
        .actions-container {
          display: flex;
          width: 200px;
          position: relative;

          .remove-button {
            width: 50px;
            height: 26px;
            margin: 2px 0% 0 10%;
            border-radius: 4px;
            border: 1px solid #21c9b8;
            font-size: 12px;
            font-weight: 400;
            color: #21c9b8;
            line-height: 26px;
            text-align: center;
          }

          .trans-button {
            width: 84px;
            height: 26px;
            background: #21c9b8;
            border-radius: 4px;
            margin: 2px 0% 0 10%;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 400;
            color: #fff;
            line-height: 26px;
            text-align: center;
          }
          .refuse {
            margin: 2px 0px 0 10%;
          }
          .agree {
            right: 0;
            position: absolute;
          }
          .already-accept {
            right: 0;
            position: absolute;
            width: 62px;
            height: 26px;
            background: rgba(210, 210, 210, 0.3);
            border-radius: 4px;
            font-size: 12px;
            font-weight: 400;
            color: #b7b7b7;
            line-height: 26px;
            text-align: center;
          }
          .already-refuse {
            right: 0;
            position: absolute;
            width: 62px;
            height: 26px;
            background: rgba(255, 77, 77, 0.15);
            border-radius: 4px;
            font-size: 12px;
            font-weight: 400;
            color: #ff4d4d;
            line-height: 26px;
            text-align: center;
          }
        }
      }
    }

    .el-tabs__nav {
      width: 100%;
      height: 40px;
      padding: 0 43%;
      display: flex;
      // justify-content: space-between;
    }

    .el-tabs__nav-wrap::after {
      background-color: transparent;
    }
    .el-tabs__active-bar {
      margin-left: 43%;
      background-color: #21c9b8;
    }
    .el-tabs__item.is-active {
      color: #21c9b8;
    }
    .el-tabs__item:hover {
      color: #21c9b8;
      cursor: pointer;
    }
  }
  .el-pagination {
    width: 100%;
    margin-top: 20px;
    text-align: center;
  }
  .el-pagination.is-background .el-pager li {
    background-color: #fff;
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: #21c9b8;
    color: #ffffff;
  }
  .el-pagination.is-background .el-pager li:not(.disabled):hover {
    color: #21c9b8;
  }
  .trans-form {
    border-radius: 6px;
    background: #ffffff;
    width: 400px;
    padding: 25px 5px 5px 25px;
    .header {
      font-size: 18px;
      font-weight: 500;
      color: #121620;
      line-height: 18px;
      margin-bottom: 12px;
    }
    .el-input {
      height: 38px;
      input {
        height: 38px;
      }
    }
    .el-select {
      display: inline-block;
      position: relative;
      width: 100%;
    }
    .input-icon {
      height: 39px;
      width: 14px;
      margin-left: 2px;
    }
    .el-form-item--medium .el-form-item__content {
      display: flex;
      line-height: 36px;
    }
    .el-form-item {
      margin-bottom: 12px;
    }
    .el-input__suffix-inner {
      .active-style {
        color: #21c9b8;
        font-size: 14px;
      }
      .disabled-style {
        color: #999;
        font-size: 14px;
      }
    }
  }
  .el-button--primary {
    /* color: #FFFFFF; */
    background-color: #21c9b8 !important;
    border-color: #21c9b8;
  }
}
</style>
