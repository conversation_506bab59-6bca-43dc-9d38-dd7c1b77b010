package com.ruoyi.im.api.util;

import com.alibaba.fastjson.JSONObject;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

public class ImUtil {



    /**
     * 使用 Map按key进行排序
     * @param map
     * @return
     */
    public static Map<String, List<JSONObject>> sortMapByKey(Map<String, List<JSONObject>> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        Map<String, List<JSONObject>> sortMap = new TreeMap<String, List<JSONObject>>(
                new MapKeyComparator());
        sortMap.putAll(map);
        return sortMap;
    }

    static class MapKeyComparator implements Comparator<String> {
        @Override
        public int compare(String str1, String str2) {
            return str1.compareTo(str2);
        }
    }
}
