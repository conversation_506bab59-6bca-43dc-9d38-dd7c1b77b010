{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\talentDetail\\index.vue?vue&type=style&index=0&id=664da7c4&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\talentDetail\\index.vue", "mtime": 1750311963087}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6OA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/talentDetail", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-row :gutter=\"20\">\r\n            <el-col :span=\"2.5\" :xs=\"24\">\r\n                <user-menu activeIndex=\"1\" />\r\n            </el-col>\r\n            <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n                <div class=\"cardStyle\">\r\n                    <!-- 左侧 -->\r\n                    <div class=\"card_left\">\r\n                        <div class=\"card_left_bottom\">\r\n                            <div class=\"imgStyle\">\r\n                                <img style=\"width: 100%; height: 100%\" :src=\"detailsData.photo\r\n                                    ? detailsData.photo\r\n                                    : require('../../../../assets/serviceSharing/ceshi2.png')\r\n                                    \" alt=\"\" />\r\n                            </div>\r\n                            <div class=\"title\">{{ detailsData.name }}</div>\r\n                            <div class=\"everyOption\">\r\n                                <div class=\"optionName\">岗位分类：</div>\r\n                                <div class=\"optionValue\" v-if=\"detailsData.positionType\">\r\n                                    {{\r\n                                        positionTypeList.filter(\r\n                                            (item) => item.dictValue == detailsData.positionType\r\n                                        )[0].dictLabel\r\n                                    }}\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"everyOption\">\r\n                                <div class=\"optionName\">最高学历：</div>\r\n                                <div class=\"optionValue\" v-if=\"detailsData.education\">\r\n                                    {{\r\n                                        educationList.filter(\r\n                                            (item) => item.dictValue == detailsData.education\r\n                                        )[0].dictLabel\r\n                                    }}\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"everyOption\">\r\n                                <div class=\"optionName\">工作状态：</div>\r\n                                <div class=\"optionValue\" v-if=\"detailsData.workStatus\">\r\n                                    {{\r\n                                        workStatusList.filter(\r\n                                            (item) => item.dictValue == detailsData.workStatus\r\n                                        )[0].dictLabel\r\n                                    }}\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"everyOption\">\r\n                                <div class=\"optionName\">联系方式：</div>\r\n                                <div class=\"optionValue\">{{ detailsData.contactPhone }}</div>\r\n                            </div>\r\n                            <!-- <div class=\"buttonStyle\" @click=\"intention\">我有意向</div> -->\r\n                        </div>\r\n                    </div>\r\n                    <!-- 中间 -->\r\n                    <div class=\"card_center_line\"></div>\r\n                    <!-- 右侧 -->\r\n                    <div class=\"card_right\">\r\n                        <div>\r\n                            <div class=\"content_title\">\r\n                                <div class=\"icon\"></div>\r\n                                <div class=\"title\">基本信息</div>\r\n                            </div>\r\n                            <div style=\"margin-top: 22px\">\r\n                                <el-descriptions class=\"margin-top\" title=\"\" :column=\"2\" :size=\"size\" border>\r\n                                    <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 毕业院校 </template>\r\n北京大学\r\n</el-descriptions-item> -->\r\n                                    <el-descriptions-item>\r\n                                        <template slot=\"label\"> 最高学历 </template>\r\n                                        {{\r\n                                            educationList.filter(\r\n                                                (item) => item.dictValue == detailsData.education\r\n                                            )[0].dictLabel\r\n                                        }}\r\n                                    </el-descriptions-item>\r\n                                    <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 出生年月 </template>\r\n                1999年5月25日\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 所在地 </template>\r\n                北京市\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 所在单位 </template>\r\n                北京爱德华科技有限公司\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 职务 </template>\r\n                经理\r\n              </el-descriptions-item> -->\r\n                                    <el-descriptions-item>\r\n                                        <template slot=\"label\"> 职称 </template>\r\n                                        {{\r\n                                            jobTitleList.filter(\r\n                                                (item) => item.dictValue == detailsData.jobTitle\r\n                                            )[0].dictLabel\r\n                                        }}\r\n                                    </el-descriptions-item>\r\n                                    <el-descriptions-item>\r\n                                        <template slot=\"label\"> 工作状态 </template>\r\n                                        {{\r\n                                            workStatusList.filter(\r\n                                                (item) => item.dictValue == detailsData.workStatus\r\n                                            )[0].dictLabel\r\n                                        }}\r\n                                    </el-descriptions-item>\r\n                                </el-descriptions>\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"margin-top: 31px\">\r\n                            <div class=\"content_title\">\r\n                                <div class=\"icon\"></div>\r\n                                <div class=\"title\">个人简历</div>\r\n                            </div>\r\n                            <div style=\"margin-top: 22px\" class=\"desc\">\r\n                                {{ detailsData.workExperience }}\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"margin-top: 31px\">\r\n                            <div class=\"content_title\">\r\n                                <div class=\"icon\"></div>\r\n                                <div class=\"title\">技术领域</div>\r\n                            </div>\r\n                            <div style=\"margin-top: 22px\" class=\"desc\">\r\n                                {{ detailsData.skills }}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </el-col>\r\n        </el-row>\r\n    </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { talentAdd, talentDetailData } from \"@/api/serviceSharing\";\r\nexport default {\r\n    components: { UserMenu },\r\n    data() {\r\n        return {\r\n            detailsData: {},\r\n            fileList: [\r\n                {\r\n                    fileName: \"报表数据.csv\",\r\n                    fileUrl: \"\",\r\n                },\r\n                {\r\n                    fileName: \"报表数据.csv\",\r\n                    fileUrl: \"\",\r\n                },\r\n            ],\r\n            id: null,\r\n            size: \"\",\r\n            positionTypeList: [], // 岗位分类\r\n            educationList: [], // 最高学历\r\n            jobTitleList: [], // 职称\r\n            workStatusList: [], // 工作状态\r\n        };\r\n    },\r\n    created() {\r\n        this.id = this.$route.query.id;\r\n        this.getPositionType();\r\n        this.getEducation();\r\n        this.getJobTitle();\r\n        this.getWorkStatus();\r\n        this.getDetailData();\r\n    },\r\n    methods: {\r\n        getDetailData() {\r\n            talentDetailData(this.id).then((res) => {\r\n                if (res.code === 200) {\r\n                    this.detailsData = res.data;\r\n                }\r\n            });\r\n        },\r\n        // 岗位分类\r\n        getPositionType() {\r\n            let params = { dictType: \"position_type\" };\r\n            listData(params).then((response) => {\r\n                this.positionTypeList = response.rows;\r\n                this.positionTypeList.unshift({\r\n                    dictValue: \"\",\r\n                    dictLabel: \"全部\",\r\n                });\r\n            });\r\n        },\r\n        // 最高学历\r\n        getEducation() {\r\n            let params = { dictType: \"education\" };\r\n            listData(params).then((response) => {\r\n                this.educationList = response.rows;\r\n                this.educationList.unshift({\r\n                    dictValue: \"\",\r\n                    dictLabel: \"全部\",\r\n                });\r\n            });\r\n        },\r\n        // 职称\r\n        getJobTitle() {\r\n            let params = { dictType: \"job_title\" };\r\n            listData(params).then((response) => {\r\n                this.jobTitleList = response.rows;\r\n                this.jobTitleList.unshift({\r\n                    dictValue: \"\",\r\n                    dictLabel: \"全部\",\r\n                });\r\n            });\r\n        },\r\n        // 工作状态\r\n        getWorkStatus() {\r\n            let params = { dictType: \"work_status\" };\r\n            listData(params).then((response) => {\r\n                this.workStatusList = response.rows;\r\n                this.workStatusList.unshift({\r\n                    dictValue: \"\",\r\n                    dictLabel: \"全部\",\r\n                });\r\n            });\r\n        },\r\n        intention(id) {\r\n            this.$router.push(\"/receiveOrder\"); // 传id\r\n        },\r\n        downLoadFile(url) {\r\n            if (url) {\r\n                window.open(url);\r\n            }\r\n        },\r\n    },\r\n};\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n    background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n    min-height: 85vh;\r\n}\r\n\r\n.content {\r\n    width: 100%;\r\n    background-color: #f2f2f2;\r\n    padding: 30px 0 61px 0;\r\n}\r\n\r\n.cardStyle {\r\n    // height: 630px;\r\n    background-color: #ffffff;\r\n    padding: 60px 56px 54px 50px;\r\n    display: flex;\r\n}\r\n\r\n.card_left {\r\n    .card_left_bottom {\r\n        .imgStyle {\r\n            width: 150px;\r\n            height: 180px;\r\n            margin-left: 35px;\r\n        }\r\n\r\n        .title {\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            font-size: 20px;\r\n            color: #222222;\r\n            margin-top: 19px;\r\n            margin-bottom: 18px;\r\n            text-align: center;\r\n        }\r\n\r\n        .everyOption {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-top: 12px;\r\n\r\n            .optionName {\r\n                // height: 14px;\r\n                font-family: Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 14px;\r\n                color: #999999;\r\n            }\r\n\r\n            .optionValue {\r\n                // height: 14px;\r\n                font-family: Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 14px;\r\n                color: #333333;\r\n            }\r\n        }\r\n\r\n        .buttonStyle {\r\n            margin-top: 32px;\r\n            // margin-left: 55px;\r\n            width: 220px;\r\n            height: 50px;\r\n            background: #21c9b8;\r\n            box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);\r\n            border-radius: 2px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #ffffff;\r\n            text-align: center;\r\n            line-height: 50px;\r\n            cursor: pointer;\r\n        }\r\n    }\r\n}\r\n\r\n.card_center_line {\r\n    width: 1px;\r\n    height: 100%;\r\n    background: #e1e1e1;\r\n    margin-left: 60px;\r\n    margin-right: 61px;\r\n}\r\n\r\n.card_right {\r\n    width: 100%;\r\n\r\n    // overflow-y: auto;\r\n    .content_title {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .icon {\r\n            width: 4px;\r\n            height: 20px;\r\n            background: #21c9b8;\r\n        }\r\n\r\n        .title {\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 18px;\r\n            color: #030a1a;\r\n            margin-left: 10px;\r\n        }\r\n    }\r\n\r\n    .desc {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #666666;\r\n        line-height: 24px;\r\n    }\r\n\r\n    .fileContent {\r\n        margin-top: 22px;\r\n        display: flex;\r\n        align-items: center;\r\n        flex-wrap: wrap;\r\n\r\n        .fileItem {\r\n            width: 280px;\r\n            height: 50px;\r\n            background: #e8f9f8;\r\n            border-radius: 2px;\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 10px;\r\n            margin-left: 20px;\r\n            cursor: pointer;\r\n\r\n            .fileImg {\r\n                width: 24px;\r\n                height: 28px;\r\n            }\r\n\r\n            .fileName {\r\n                font-family: Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 14px;\r\n                color: #666666;\r\n                margin-left: 15px;\r\n            }\r\n        }\r\n\r\n        .fileItem:nth-child(2n + 1) {\r\n            margin-left: 0;\r\n        }\r\n    }\r\n}\r\n</style>"]}]}