{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\addIntention.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\addIntention.vue", "mtime": 1750311962953}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["addIntention.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "addIntention.vue", "sourceRoot": "src/views/form", "sourcesContent": ["<template>\r\n  <div class=\"intention-page\">\r\n    <div class=\"intention-page-header\">\r\n      <div class=\"banner\">\r\n        <img\r\n          src=\"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/1676034162343360.webp\"\r\n          alt=\"我有意向\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <div class=\"intention-page-title\">我有意向</div>\r\n    <div v-loading=\"loading\" class=\"card-container intention-form\">\r\n      <div class=\"form-content\">\r\n        <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n          <el-form-item label=\"意向类型\">\r\n            {{ formatTitle(form.resourceType) }}\r\n          </el-form-item>\r\n          <el-form-item label=\"意向名称\">\r\n            {{form.resourceTitle || \"--\"}}\r\n          </el-form-item>\r\n          <el-form-item label=\"意向描述\">\r\n            <el-input\r\n              type=\"textarea\"\r\n              v-model=\"form.resourceDescribe\"\r\n              maxlength=\"500\"\r\n              rows=\"6\"\r\n              show-word-limit\r\n              placeholder=\"请输入需求描述\"\r\n            ></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"联系人\">\r\n            <el-input disabled v-model=\"form.contactPerson\" placeholder=\"请输入联系人\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"联系方式\">\r\n            <el-input disabled v-model=\"form.contactPhone\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item class=\"footer-submit\">\r\n            <el-button type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>/**\r\n * businessId 业务ID\r\n * resourceType类型是下面:\r\n * 专家智库  resource_expet\r\n * 成果云服  resource_supply\r\n * 商机需求  resource_demand\r\n * resourceTitle: 资源名称\r\n *\r\n * **/\r\nimport {interactRecordAdd} from \"@/api/zhm\";\r\n\r\nconst TYPES = {\r\n  \"resource_expet\": \"专家智库\",\r\n  \"resource_supply\": \"成果云服\",\r\n  \"resource_demand\": \"商机需求\",\r\n};\r\n\r\nexport default {\r\n  name: \"addIntention\",\r\n  data() {\r\n    const { user } = this.$store.state;\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        businessId: undefined,\r\n        resourceType: undefined,\r\n        resourceTitle: undefined,\r\n        resourceDescribe: undefined,\r\n        contactPerson: user.name,\r\n        contactPhone: user.tel,\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    const { id, type, title } = this.$route.query;\r\n    if (id) {\r\n      this.form.businessId = id;\r\n      this.form.resourceType = type;\r\n      this.form.resourceTitle = title;\r\n    }\r\n  },\r\n  methods: {\r\n    formatTitle(key) {\r\n      return TYPES[key] || '--';\r\n    },\r\n    onSubmit() {\r\n      this.loading = true;\r\n      interactRecordAdd(this.form).then((res) => {\r\n        const { code, msg } = res;\r\n        if (code === 200) {\r\n          this.$message.success(\"提交成功\");\r\n          // TODO: 跳转到提交记录\r\n          // this.$router.push({\r\n          //   path: \"/addIntention\",\r\n          //   params: {\r\n          //     id: '对应当前资源的id',\r\n          //     type: '专家智库: resource_expet 成果云服  resource_supply 商机需求  resource_demand',\r\n          //     title: '资源名称'\r\n          //   },\r\n          // });\r\n        } else {\r\n          this.$message.error(msg || \"提交失败\");\r\n        }\r\n      }).finally(() => this.loading = false)\r\n      console.log('submit!', this.form);\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n.intention-page {\r\n  background-color: #F4F5F9;\r\n  padding-bottom: 80px;\r\n  &-header {\r\n    background-color: #FFFFFF;\r\n    .banner {\r\n      width: 100%;\r\n      height: 540px;\r\n      background-color: #f5f5f5;\r\n      img {\r\n        width: 100%;\r\n        height: 540px;\r\n        object-fit: fill;\r\n      }\r\n    }\r\n    .body {\r\n      padding: 60px 0;\r\n    }\r\n  }\r\n  &-title {\r\n    font-size: 40px;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    line-height: 40px;\r\n    text-align: center;\r\n    padding: 60px 0;\r\n  }\r\n  .intention-form {\r\n    @include flexCenter;\r\n    height: 664px;\r\n    background-color: #FFFFFF;\r\n    margin-bottom: 80px;\r\n    .form-content {\r\n      width: 750px;\r\n      .footer-submit {\r\n        text-align: center;\r\n        margin-top: 40px;\r\n        .el-button {\r\n          width: 400px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}