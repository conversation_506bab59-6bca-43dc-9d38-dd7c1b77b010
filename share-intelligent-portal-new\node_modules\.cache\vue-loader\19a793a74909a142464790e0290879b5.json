{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\index.vue", "mtime": 1750311962980}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBmb3JFYWNoT2JqSW5kZXhlZCwgbWFwIH0gZnJvbSAicmFtZGEiOw0KLy8gaW1wb3J0IEhlYWRlclRhZyBmcm9tICJAL3ZpZXdzL2NvbXBvbmVudHMvaG9tZS9oZWFkZXItdGFnLnZ1ZSI7DQppbXBvcnQgeyBnZXRMaXN0QnlUZXh0IH0gZnJvbSAiQC9hcGkvbm90aWNlIjsNCmltcG9ydCB7IGxpc3RQb2xpY3lCeVR5cGUsIGxpc3RCeVR5cGVQb2xpY3kgfSBmcm9tICJAL2FwaS96aG0iOw0KLy8gaW1wb3J0IHsgcG9saWN5bW9kZSB9IGZyb20gIi4uL2NvbXBvbmVudHMvaG9tZS9wb2xpY3ltb2RlLnZ1ZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogInBvbGljeVBhZ2UiLA0KICBjb21wb25lbnRzOiB7DQogICAgLy8gSGVhZGVyVGFnLA0KICAgIC8vIHBvbGljeW1vZGUsDQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIG5vdGljZUxvYWRpbmc6IGZhbHNlLA0KICAgICAgZm9ybTogew0KICAgICAgICB0aXRsZTogdW5kZWZpbmVkLA0KICAgICAgfSwNCiAgICAgIG5vdGljZXM6IFtdLA0KICAgICAgbGV0SXRlbXM6IFtdLA0KICAgICAgcmlnaHRJdGVtczogW10sDQogICAgICBjaGVja2VkQXJyOiBbXSwNCiAgICAgIHBvbGljeUl0ZW1zOiBbXSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0Tm90aWNlRGF0YSgpOw0KICAgIHRoaXMuZ2V0VHlwZVBvbGljeSgpOw0KICAgIHRoaXMuZ2V0TGVmdEl0ZW1zKCk7DQogICAgdGhpcy5nZXRSaWdodEl0ZW1zKCk7DQogIH0sDQogIHdhdGNoOiB7DQogICAgJHJvdXRlKHRvLCBmcm9tKSB7DQogICAgICAvLyB0b+ihqOekuuimgei3s+i9rOeahOi3r+eUse+8jGZyb23ooajnpLrku47lk6rlhL/ot7PnmoTot6/nlLEgICB0by5wYXRoDQogICAgICB0aGlzLiRyb3V0ZXIuZ28oMCk7DQogICAgfSwNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICBsZXQgc3BlY2lhbExvYyA9IHRoaXMuJHJvdXRlLnF1ZXJ5LnNwZWNpYWxMb2M7DQogICAgaWYgKHNwZWNpYWxMb2MpIHsNCiAgICAgIGxldCBpbnRlciA9IHNldEludGVydmFsKCgpID0+IHsNCiAgICAgICAgbGV0IHRhcmdldCA9IHRoaXMuJHJlZnNbc3BlY2lhbExvY107DQogICAgICAgIGlmICh0YXJnZXQpIHsNCiAgICAgICAgICBjbGVhckludGVydmFsKGludGVyKTsNCiAgICAgICAgICB0YXJnZXQuc2Nyb2xsSW50b1ZpZXcoKTsNCiAgICAgICAgfQ0KICAgICAgfSwgMTAwKTsNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXROb3RpY2VEYXRhKCkgew0KICAgICAgdGhpcy5ub3RpY2VMb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGdldExpc3RCeVRleHQoew0KICAgICAgICB0eXBlVG9wOiAyLA0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogNCwNCiAgICAgIH0pDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBjb25zdCB7IGNvZGUsIHJvd3MgfSA9IHJlczsNCiAgICAgICAgICBpZiAoY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLm5vdGljZXMgPSByb3dzOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmZpbmFsbHkoKCkgPT4gKHRoaXMubm90aWNlTG9hZGluZyA9IGZhbHNlKSk7DQogICAgfSwNCiAgICBnZXRMZWZ0SXRlbXMoKSB7DQogICAgICBsaXN0UG9saWN5QnlUeXBlKDEwMCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGNvbnN0IHsgY29kZSwgcm93cyB9ID0gcmVzOw0KICAgICAgICBpZiAoY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5sZXRJdGVtcyA9IHJvd3M7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0UmlnaHRJdGVtcygpIHsNCiAgICAgIGxpc3RQb2xpY3lCeVR5cGUoMTAxKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgY29uc3QgeyBjb2RlLCByb3dzIH0gPSByZXM7DQogICAgICAgIGlmIChjb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLnJpZ2h0SXRlbXMgPSByb3dzOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldFR5cGVQb2xpY3koKSB7DQogICAgICBsaXN0QnlUeXBlUG9saWN5KCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGNvbnN0IHsgY29kZSwgZGF0YSB9ID0gcmVzOw0KICAgICAgICBpZiAoY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgY29uc3QgaXRlbXMgPSBbXTsNCiAgICAgICAgICBmb3JFYWNoT2JqSW5kZXhlZCgodmFsdWUsIGtleSkgPT4gew0KICAgICAgICAgICAgaXRlbXMucHVzaCh7DQogICAgICAgICAgICAgIGtleSwNCiAgICAgICAgICAgICAgY2hpbGRyZW46IG1hcCgNCiAgICAgICAgICAgICAgICAoaXRlbSkgPT4gKHsNCiAgICAgICAgICAgICAgICAgIGlkOiBpdGVtLmlkLA0KICAgICAgICAgICAgICAgICAgY29kZTogaXRlbS5sYWJlbENvZGUsDQogICAgICAgICAgICAgICAgICB0ZXh0OiBpdGVtLmxhYmVsVGV4dCwNCiAgICAgICAgICAgICAgICB9KSwNCiAgICAgICAgICAgICAgICB2YWx1ZQ0KICAgICAgICAgICAgICApLA0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSwgZGF0YSk7DQogICAgICAgICAgdGhpcy5wb2xpY3lJdGVtcyA9IGl0ZW1zOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHNlYXJjaCgpIHsNCiAgICAgIGNvbnN0IHsgdGl0bGUgfSA9IHRoaXMuZm9ybTsNCiAgICAgIGlmICh0aXRsZSkgew0KICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaChgL25vdGljZT9rZXl3b3JkPSR7dGl0bGV9YCk7DQogICAgICB9DQogICAgfSwNCiAgICBvbkNhbmNlbCgpIHsNCiAgICAgIHRoaXMuY2hlY2tlZEFyciA9IFtdOw0KICAgIH0sDQogICAgb25Db25maXJtKCkgew0KICAgICAgaWYgKHRoaXMuY2hlY2tlZEFyci5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCLor7fpgInmi6nnlLvlg48iKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICBuYW1lOiAicG9saWN5RGVjbGFyZSIsDQogICAgICAgIHBhcmFtczogeyBjb2RlOiB0aGlzLmNoZWNrZWRBcnIgfSwNCiAgICAgIH0pOw0KICAgICAgY29uc29sZS5sb2codGhpcy5jaGVja2VkQXJyKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/policy", "sourcesContent": ["<template>\r\n  <div class=\"policy-page\">\r\n    <div class=\"policy-page-header\">\r\n      <div class=\"banner\">\r\n        <img src=\"../../assets/policy/banner.png\" alt=\"政策大厅\" />\r\n      </div>\r\n      <div class=\"body\">\r\n        <div class=\"enterprise-list-title-box\">\r\n          <div class=\"enterprise-list-divider\"></div>\r\n          <div class=\"enterprise-list-title\">链政策</div>\r\n          <div class=\"enterprise-list-divider\"></div>\r\n        </div>\r\n        <!-- <header-tag title=\"链政策\" /> -->\r\n        <div class=\"search-box\">\r\n          <el-form ref=\"form\" class=\"search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.title\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"search-input\"\r\n              >\r\n                <el-button slot=\"append\" class=\"search-btn\" @click=\"search\"\r\n                  >搜索\r\n                </el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container policy-page-body\">\r\n      <div class=\"card\">\r\n        <router-link to=\"/policymode\" class=\"card-left\">\r\n          <el-image\r\n            fit=\"contain\"\r\n            class=\"notice-icon\"\r\n            :src=\"require('../../assets/policy/zixun.png')\"\r\n          />\r\n          <div class=\"imageBtn\">查看更多</div>\r\n        </router-link>\r\n        <div v-loading=\"noticeLoading\" class=\"card-right notice-content\">\r\n          <template v-if=\"notices.length > 0\">\r\n            <router-link\r\n              class=\"notice-item\"\r\n              v-for=\"item in notices\"\r\n              :key=\"item.id\"\r\n              :to=\"`/policyDetail?id=${item.id}`\"\r\n            >\r\n              <div class=\"notice-item-content\">\r\n                <div class=\"title\">{{ item.title }}</div>\r\n                <div class=\"footer\">\r\n                  <div class=\"company\">{{ item.company }}</div>\r\n                  <div class=\"date\">{{ item.updateTime }}</div>\r\n                  <!-- <div class=\"date\">发文日期：{{ item.updateTime }}</div> -->\r\n                </div>\r\n              </div>\r\n              <div class=\"notice-item-btn\">查看详情</div>\r\n            </router-link>\r\n          </template>\r\n          <template v-else>\r\n            <el-empty />\r\n          </template>\r\n        </div>\r\n      </div>\r\n      <div>\r\n        <div class=\"card-left\">\r\n          <el-image\r\n            fit=\"contain\"\r\n            class=\"draw-icon\"\r\n            :src=\"require('../../assets/policy/huaxiang.png')\"\r\n          />\r\n        </div>\r\n        <div class=\"card-right policy-content\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\" v-for=\"item in policyItems\" :key=\"item.key\">\r\n              <div class=\"policy-card\">\r\n                <el-checkbox-group v-model=\"checkedArr\">\r\n                  <el-checkbox\r\n                    class=\"checkbox-policy\"\r\n                    v-for=\"code in item.children\"\r\n                    :label=\"code.code\"\r\n                    :key=\"code.code\"\r\n                    >{{ code.text }}</el-checkbox\r\n                  >\r\n                </el-checkbox-group>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <div class=\"policy-content-footer\">\r\n            <div @click=\"onCancel\" class=\"btn-cancel\">取消</div>\r\n            <div @click=\"onConfirm\" class=\"btn-confirm\">确定</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"card\">\r\n        <router-link to=\"/policyDeclare\" class=\"card-left\">\r\n          <el-image\r\n            fit=\"fill\"\r\n            class=\"apply-icon\"\r\n            :src=\"require('../../assets/policy/shenbao.png')\"\r\n          />\r\n          <div class=\"imageBtn\">在线查看</div>\r\n        </router-link>\r\n        <div class=\"card-right apply-content\">\r\n          <div class=\"apply-card\">\r\n            <router-link to=\"/policyDeclare\" class=\"apply-card-header\">\r\n              <div class=\"left\">\r\n                <div class=\"tag\" />\r\n                <div class=\"title\">科创平台</div>\r\n              </div>\r\n              <div class=\"right\">更多>></div>\r\n            </router-link>\r\n            <div class=\"apply-card-body\">\r\n              <template v-if=\"letItems.length > 0\">\r\n                <router-link\r\n                  class=\"item\"\r\n                  :to=\"`/policyDeclareDetail?id=${item.id}`\"\r\n                  v-for=\"item in letItems\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div class=\"item-tag\" />\r\n                  <div class=\"item-text\">{{ item.title }}</div>\r\n                </router-link>\r\n              </template>\r\n              <template v-else>\r\n                <el-empty />\r\n              </template>\r\n            </div>\r\n          </div>\r\n          <div class=\"apply-card\">\r\n            <router-link to=\"/policyDeclare\" class=\"apply-card-header\">\r\n              <div class=\"left\">\r\n                <div class=\"tag\" />\r\n                <div class=\"title\">人才政策</div>\r\n              </div>\r\n              <div class=\"right\">更多>></div>\r\n            </router-link>\r\n            <div class=\"apply-card-body\">\r\n              <template v-if=\"letItems.length > 0\">\r\n                <router-link\r\n                  class=\"item\"\r\n                  :to=\"`/policyDeclareDetail?id=${item.id}`\"\r\n                  v-for=\"item in rightItems\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div class=\"item-tag\" />\r\n                  <div class=\"item-text\">{{ item.title }}</div>\r\n                </router-link>\r\n              </template>\r\n              <template v-else>\r\n                <el-empty />\r\n              </template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { forEachObjIndexed, map } from \"ramda\";\r\n// import HeaderTag from \"@/views/components/home/<USER>\";\r\nimport { getListByText } from \"@/api/notice\";\r\nimport { listPolicyByType, listByTypePolicy } from \"@/api/zhm\";\r\n// import { policymode } from \"../components/home/<USER>\";\r\n\r\nexport default {\r\n  name: \"policyPage\",\r\n  components: {\r\n    // HeaderTag,\r\n    // policymode,\r\n  },\r\n  data() {\r\n    return {\r\n      noticeLoading: false,\r\n      form: {\r\n        title: undefined,\r\n      },\r\n      notices: [],\r\n      letItems: [],\r\n      rightItems: [],\r\n      checkedArr: [],\r\n      policyItems: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getNoticeData();\r\n    this.getTypePolicy();\r\n    this.getLeftItems();\r\n    this.getRightItems();\r\n  },\r\n  watch: {\r\n    $route(to, from) {\r\n      // to表示要跳转的路由，from表示从哪儿跳的路由   to.path\r\n      this.$router.go(0);\r\n    },\r\n  },\r\n  mounted() {\r\n    let specialLoc = this.$route.query.specialLoc;\r\n    if (specialLoc) {\r\n      let inter = setInterval(() => {\r\n        let target = this.$refs[specialLoc];\r\n        if (target) {\r\n          clearInterval(inter);\r\n          target.scrollIntoView();\r\n        }\r\n      }, 100);\r\n    }\r\n  },\r\n  methods: {\r\n    getNoticeData() {\r\n      this.noticeLoading = true;\r\n      getListByText({\r\n        typeTop: 2,\r\n        pageNum: 1,\r\n        pageSize: 4,\r\n      })\r\n        .then((res) => {\r\n          const { code, rows } = res;\r\n          if (code === 200) {\r\n            this.notices = rows;\r\n          }\r\n        })\r\n        .finally(() => (this.noticeLoading = false));\r\n    },\r\n    getLeftItems() {\r\n      listPolicyByType(100).then((res) => {\r\n        const { code, rows } = res;\r\n        if (code === 200) {\r\n          this.letItems = rows;\r\n        }\r\n      });\r\n    },\r\n    getRightItems() {\r\n      listPolicyByType(101).then((res) => {\r\n        const { code, rows } = res;\r\n        if (code === 200) {\r\n          this.rightItems = rows;\r\n        }\r\n      });\r\n    },\r\n    getTypePolicy() {\r\n      listByTypePolicy().then((res) => {\r\n        const { code, data } = res;\r\n        if (code === 200) {\r\n          const items = [];\r\n          forEachObjIndexed((value, key) => {\r\n            items.push({\r\n              key,\r\n              children: map(\r\n                (item) => ({\r\n                  id: item.id,\r\n                  code: item.labelCode,\r\n                  text: item.labelText,\r\n                }),\r\n                value\r\n              ),\r\n            });\r\n          }, data);\r\n          this.policyItems = items;\r\n        }\r\n      });\r\n    },\r\n    search() {\r\n      const { title } = this.form;\r\n      if (title) {\r\n        this.$router.push(`/notice?keyword=${title}`);\r\n      }\r\n    },\r\n    onCancel() {\r\n      this.checkedArr = [];\r\n    },\r\n    onConfirm() {\r\n      if (this.checkedArr.length === 0) {\r\n        this.$message.info(\"请选择画像\");\r\n        return;\r\n      }\r\n      this.$router.push({\r\n        name: \"policyDeclare\",\r\n        params: { code: this.checkedArr },\r\n      });\r\n      console.log(this.checkedArr);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n\r\n.policy-page {\r\n  background-color: #f4f5f9;\r\n  &-header {\r\n    background-color: #ffffff;\r\n    .banner {\r\n      width: 100%;\r\n      height: 50vh;\r\n      background-color: #f5f5f5;\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: fill;\r\n      }\r\n    }\r\n\r\n    .body {\r\n      padding: 20px 0;\r\n    }\r\n    .search-box {\r\n      // padding-top: 40px;\r\n      .search-form {\r\n        text-align: center;\r\n\r\n        .search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n\r\n          .search-btn {\r\n            width: 100px;\r\n          }\r\n\r\n          ::v-deep.el-input__inner {\r\n            height: 54px;\r\n            background: #fff;\r\n            border-radius: 27px 0 0 27px;\r\n            border: 1px solid #d9d9d9;\r\n            font-size: 16px;\r\n            line-height: 24px;\r\n            padding-left: 30px;\r\n          }\r\n\r\n          ::v-deep.el-input-group__append {\r\n            border: 1px solid #21c9b8;\r\n            border-radius: 0 100px 100px 0;\r\n            background: #21c9b8;\r\n            font-size: 16px;\r\n            color: #fff;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &-body {\r\n    padding: 40px 0;\r\n    .card {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      flex-shrink: 0;\r\n      width: 100%;\r\n      margin-bottom: 40px;\r\n      &-left {\r\n        // width: 292px;\r\n        position: relative;\r\n        flex-shrink: 0;\r\n        .notice-icon {\r\n          width: 100%;\r\n          // height: 632px;\r\n        }\r\n        .draw-icon {\r\n          width: 100%;\r\n          height: 100%;\r\n          // height: 526px;\r\n        }\r\n        .apply-icon {\r\n          width: 100%;\r\n          height: 304px;\r\n        }\r\n        .imageBtn {\r\n          position: absolute;\r\n          left: calc((100% - 200px) / 2);\r\n          bottom: 20px;\r\n          width: 200px;\r\n          height: 44px;\r\n          line-height: 44px;\r\n          background: #ffffff;\r\n          border-radius: 2px;\r\n          text-align: center;\r\n          cursor: pointer;\r\n          color: #21c9b8;\r\n        }\r\n      }\r\n      &-right {\r\n        flex: 1;\r\n        &.notice-content {\r\n          // height: 632px;\r\n          padding-left: 24px;\r\n          padding-top: 20px;\r\n          overflow-y: auto;\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          justify-content: space-between;\r\n          .notice-item {\r\n            // display: flex;\r\n            // flex-shrink: 0;\r\n            // flex-direction: row;\r\n            // align-items: center;\r\n            width: 49%;\r\n            height: 200px;\r\n            background-color: #ffffff;\r\n            padding: 20px 24px;\r\n            margin-bottom: 24px;\r\n            &-content {\r\n              // display: flex;\r\n              // flex-direction: column;\r\n              // flex: 1;\r\n              // justify-content: space-between;\r\n              // height: 100%;\r\n              padding-right: 24px;\r\n              .title {\r\n                @include multiEllipsis(1);\r\n                font-size: 24px;\r\n                font-weight: 500;\r\n                color: #323233;\r\n                line-height: 32px;\r\n              }\r\n              .footer {\r\n                display: flex;\r\n                flex-direction: row;\r\n                align-items: center;\r\n                margin-top: 50px;\r\n                margin-bottom: 20px;\r\n                .company {\r\n                  font-size: 14px;\r\n                  font-weight: 400;\r\n                  color: #999999;\r\n                  line-height: 14px;\r\n                  margin-right: 40px;\r\n                }\r\n                .date {\r\n                  font-size: 14px;\r\n                  font-weight: 400;\r\n                  color: #999999;\r\n                  line-height: 14px;\r\n                }\r\n              }\r\n            }\r\n            &-btn {\r\n              @include flexCenter;\r\n              width: 128px;\r\n              height: 40px;\r\n              background: #21c9b8;\r\n              border-radius: 4px;\r\n              font-size: 16px;\r\n              font-weight: 500;\r\n              color: #ffffff;\r\n              line-height: 16px;\r\n            }\r\n            &:last-child {\r\n              margin-bottom: 0;\r\n            }\r\n          }\r\n        }\r\n        &.policy-content {\r\n          display: flex;\r\n          flex-direction: column;\r\n          flex-shrink: 0;\r\n          justify-content: space-between;\r\n          height: 526px;\r\n          padding: 24px;\r\n          background-color: #ffffff;\r\n          margin: 24px 0 60px 0;\r\n          .policy-card {\r\n            height: 202px;\r\n            background: #f4f5f9;\r\n            border-radius: 8px;\r\n            overflow-y: auto;\r\n            overflow-x: hidden;\r\n            margin-bottom: 10px;\r\n            padding: 10px 0;\r\n            .checkbox-policy {\r\n              padding: 6px 26px;\r\n              ::v-deep.el-checkbox__label {\r\n                @include ellipsis;\r\n                display: inline-block;\r\n                max-width: 130px;\r\n                font-size: 12px;\r\n                font-weight: 400;\r\n                color: #262626;\r\n                line-height: 12px;\r\n              }\r\n            }\r\n          }\r\n          .policy-content-footer {\r\n            @include flexCenter;\r\n            .btn-cancel {\r\n              @include flexCenter;\r\n              width: 160px;\r\n              height: 40px;\r\n              border-radius: 4px;\r\n              border: 1px solid #d9d9d9;\r\n              font-size: 16px;\r\n              font-weight: 500;\r\n              color: #333333;\r\n              line-height: 16px;\r\n              margin-right: 25px;\r\n              cursor: pointer;\r\n            }\r\n            .btn-confirm {\r\n              @include flexCenter;\r\n              width: 160px;\r\n              height: 40px;\r\n              background: #21c9b8;\r\n              border-radius: 4px;\r\n              font-size: 16px;\r\n              font-weight: 500;\r\n              color: #ffffff;\r\n              line-height: 16px;\r\n              cursor: pointer;\r\n            }\r\n          }\r\n        }\r\n        &.apply-content {\r\n          height: 304px;\r\n          display: flex;\r\n          flex-direction: row;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          padding-left: 24px;\r\n          .apply-card {\r\n            width: 426px;\r\n            height: 304px;\r\n            background: #ffffff;\r\n            &-header {\r\n              display: flex;\r\n              flex-direction: row;\r\n              align-items: center;\r\n              justify-content: space-between;\r\n              height: 66px;\r\n              background-color: #ffffff;\r\n              padding: 0 24px;\r\n              border-bottom: 1px solid #e8e8e8;\r\n              .left {\r\n                display: flex;\r\n                flex-direction: row;\r\n                align-items: center;\r\n                .tag {\r\n                  width: 4px;\r\n                  height: 20px;\r\n                  background: #21c9b8;\r\n                  margin-right: 8px;\r\n                }\r\n                .title {\r\n                  font-size: 18px;\r\n                  font-weight: 500;\r\n                  color: #333333;\r\n                  line-height: 18px;\r\n                }\r\n              }\r\n              .right {\r\n                font-size: 18px;\r\n                font-weight: 400;\r\n                color: #21c9b8;\r\n                line-height: 18px;\r\n              }\r\n            }\r\n            &-body {\r\n              padding: 12px 0;\r\n              .item {\r\n                display: flex;\r\n                flex-direction: row;\r\n                align-items: center;\r\n                padding: 12px 24px;\r\n                &-tag {\r\n                  width: 6px;\r\n                  height: 6px;\r\n                  background: #21c9b8;\r\n                }\r\n                &-text {\r\n                  @include ellipsis;\r\n                  flex: 1;\r\n                  font-size: 16px;\r\n                  font-weight: 400;\r\n                  color: #666666;\r\n                  line-height: 16px;\r\n                  padding-left: 14px;\r\n                }\r\n              }\r\n              .el-empty {\r\n                padding: 0 !important;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n.enterprise-list-title-box {\r\n  width: 336px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30px 0 40px;\r\n  .enterprise-list-title {\r\n    font-size: 40px;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #333;\r\n    line-height: 40px;\r\n    padding: 0 40px;\r\n  }\r\n  .enterprise-list-divider {\r\n    width: 48px;\r\n    height: 4px;\r\n    background: #21c9b8;\r\n  }\r\n}\r\n</style>\r\n"]}]}