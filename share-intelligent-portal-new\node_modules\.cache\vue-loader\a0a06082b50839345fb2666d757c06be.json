{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\senseTab.vue?vue&type=style&index=1&id=478dbcdf&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\senseTab.vue", "mtime": 1750311962930}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi50YWItbWFpbiB7DQogIC50YWItcGFnZS1lbmQgew0KICAgIC5jb21wYW55LXRhYi1wYWdpbmF0aW9uIHsNCiAgICAgIHdpZHRoOiAyODBweDsNCiAgICAgIG1hcmdpbi1sZWZ0OiBjYWxjKDQ1JSAtIDIwMHB4KTsNCiAgICAgIC8vIG1hcmdpbjogMCBhdXRvOw0KICAgICAgLmJ0bi1wcmV2LA0KICAgICAgLmJ0bi1uZXh0LA0KICAgICAgLmJ0bi1xdWlja3ByZXYgew0KICAgICAgICB3aWR0aDogMzJweDsNCiAgICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmZmZmOw0KICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgIG1hcmdpbjogMCA2cHg7DQogICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgfQ0KICAgICAgLmVsLXBhZ2VyIHsNCiAgICAgICAgLm51bWJlciB7DQogICAgICAgICAgd2lkdGg6IDMycHg7DQogICAgICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7DQogICAgICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDMycHg7DQogICAgICAgICAgbWFyZ2luOiAwIDZweDsNCiAgICAgICAgICAmLmFjdGl2ZSB7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjMjFjOWI4Ow0KICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzIxYzliODsNCiAgICAgICAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["senseTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "senseTab.vue", "sourceRoot": "src/views/components/home/<USER>", "sourcesContent": ["<template>\r\n  <div v-loading=\"loading\" class=\"tab-main\">\r\n    <template v-if=\"items.length > 0\">\r\n      <div v-for=\"item in items\" :key=\"item.id\">\r\n        <router-link\r\n          class=\"card\"\r\n          target=\"_blank\"\r\n          :to=\"`/scenarioDetail?id=${item.id}`\"\r\n        >\r\n          <div class=\"list-item-content\">\r\n            <div class=\"list-item-info\">\r\n              <div class=\"list-item-title\">\r\n                {{ item.title }}\r\n              </div>\r\n              <div class=\"list-item-text\">{{ item.content }}</div>\r\n              <div class=\"list-item-time\">{{ item.updateTime }}</div>\r\n            </div>\r\n          </div>\r\n        </router-link>\r\n      </div>\r\n    </template>\r\n    <template v-else>\r\n      <el-empty />\r\n    </template>\r\n    <div class=\"tab-page-end\">\r\n      <!-- <span class=\"demonstration\">完整功能</span> -->\r\n      <el-pagination\r\n        class=\"company-tab-pagination\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"pageNum\"\r\n        :page-sizes=\"[100, 200, 300, 400]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\" prev, pager, next \"\r\n        :total=\"total\"\r\n      >\r\n      </el-pagination>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { head, map } from \"ramda\";\r\nimport { getListByText } from \"@/api/scene\";\r\n\r\nexport default {\r\n  name: \"SenseTab\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      items: [],\r\n      pageNum: 1,\r\n      pageSize: 3,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getSenseData();\r\n  },\r\n  methods: {\r\n    getSenseData() {\r\n      this.loading = true;\r\n      getListByText({ pageNum: this.pageNum, pageSize: this.pageSize })\r\n        .then((res) => {\r\n          const { code, rows = [], total } = res;\r\n          if (code === 200) {\r\n            this.items = map(\r\n              ({\r\n                id,\r\n                title,\r\n                simpleContent,\r\n                updateTime,\r\n                coverPictureList = [],\r\n              }) => {\r\n                const image = head(coverPictureList || []) || {};\r\n                return {\r\n                  id,\r\n                  title,\r\n                  content: simpleContent,\r\n                  updateTime,\r\n                  src: image.url,\r\n                };\r\n              },\r\n              rows\r\n            );\r\n            this.to;\r\n            this.total = total;\r\n          }\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    handleSizeChange(newSize) {\r\n      this.pageSize = newSize;\r\n      this.getSenseData();\r\n    },\r\n    handleCurrentChange(newPage) {\r\n      this.pageNum = newPage;\r\n      this.getSenseData();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n\r\n.tab-main {\r\n  position: relative;\r\n  width: 100%;\r\n\r\n  .el-row--flex {\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  ::v-deep .el-scrollbar__wrap {\r\n    overflow-x: hidden;\r\n    overflow-y: auto;\r\n  }\r\n\r\n  .card {\r\n    display: flex;\r\n    flex-direction: column;\r\n    width: 100%;\r\n    flex-shrink: 0;\r\n    min-height: 100px;\r\n    background: #ffffff;\r\n    box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.1);\r\n    margin-bottom: 24px;\r\n    .list-item-content {\r\n      display: flex;\r\n      padding: 24px 32px;\r\n      cursor: pointer;\r\n\r\n      .list-item-info {\r\n        padding-left: 24px;\r\n        .list-item-title {\r\n          width: 806px;\r\n          height: 24px;\r\n          text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n          white-space: nowrap; /*让文字不换行*/\r\n          overflow: hidden; /*超出要隐藏*/\r\n          font-size: 20px;\r\n          font-weight: 500;\r\n          color: #323233;\r\n          line-height: 24px;\r\n          margin: 8px 0 14px;\r\n        }\r\n        .list-item-text {\r\n          width: 806px;\r\n          height: 60px;\r\n          overflow: hidden;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n          -webkit-line-clamp: 2;\r\n          text-overflow: ellipsis;\r\n          font-size: 14px;\r\n          color: #666;\r\n          line-height: 30px;\r\n        }\r\n        .list-item-time {\r\n          color: #999;\r\n          line-height: 14px;\r\n          margin-top: 22px;\r\n        }\r\n      }\r\n      &:hover {\r\n        .list-item-title {\r\n          color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n    &-footer {\r\n      padding: 16px 24px;\r\n\r\n      .title {\r\n        @include multiEllipsis(2);\r\n        font-size: 18px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        line-height: 26px;\r\n        margin-bottom: 12px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.tab-main {\r\n  .tab-page-end {\r\n    .company-tab-pagination {\r\n      width: 280px;\r\n      margin-left: calc(45% - 200px);\r\n      // margin: 0 auto;\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #ffffff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        margin: 0 6px;\r\n        color: #333;\r\n      }\r\n      .el-pager {\r\n        .number {\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 1px solid #d9d9d9;\r\n          background: #ffffff;\r\n          border-radius: 4px;\r\n          line-height: 32px;\r\n          margin: 0 6px;\r\n          &.active {\r\n            background: #21c9b8;\r\n            border: 1px solid #21c9b8;\r\n            color: #fff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}