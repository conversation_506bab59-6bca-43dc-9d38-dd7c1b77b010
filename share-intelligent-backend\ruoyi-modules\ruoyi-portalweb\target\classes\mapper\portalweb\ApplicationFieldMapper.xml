<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.ApplicationFieldMapper">
    
    <resultMap type="ApplicationField" id="ApplicationFieldResult">
        <result property="applicationFieldName"    column="application_field_name"    />
        <result property="applicationFieldId"    column="application_field_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectApplicationFieldVo">
        select application_field_name, application_field_id, create_by, create_time, update_by, update_time, remark from application_field
    </sql>

    <select id="selectApplicationFieldList" parameterType="ApplicationField" resultMap="ApplicationFieldResult">
        <include refid="selectApplicationFieldVo"/>
        <where>  
            <if test="applicationFieldName != null  and applicationFieldName != ''"> and application_field_name like concat('%', #{applicationFieldName}, '%')</if>
        </where>
    </select>
    
    <select id="selectApplicationFieldByApplicationFieldId" parameterType="Long" resultMap="ApplicationFieldResult">
        <include refid="selectApplicationFieldVo"/>
        where application_field_id = #{applicationFieldId}
    </select>
        
    <insert id="insertApplicationField" parameterType="ApplicationField">
        insert into application_field
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applicationFieldName != null">application_field_name,</if>
            <if test="applicationFieldId != null">application_field_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applicationFieldName != null">#{applicationFieldName},</if>
            <if test="applicationFieldId != null">#{applicationFieldId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateApplicationField" parameterType="ApplicationField">
        update application_field
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationFieldName != null">application_field_name = #{applicationFieldName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where application_field_id = #{applicationFieldId}
    </update>

    <delete id="deleteApplicationFieldByApplicationFieldId" parameterType="Long">
        delete from application_field where application_field_id = #{applicationFieldId}
    </delete>

    <delete id="deleteApplicationFieldByApplicationFieldIds" parameterType="String">
        delete from application_field where application_field_id in 
        <foreach item="applicationFieldId" collection="array" open="(" separator="," close=")">
            #{applicationFieldId}
        </foreach>
    </delete>
</mapper>