<template>
  <div class="app-container">
    <el-row
      :gutter="20"
      style="background: linear-gradient(to right, #e1f7f0, #f4fcfa)"
    >
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
        <div class="formStyle">
          <el-form ref="form" :rules="rules" :model="form" label-position="top">
            <el-form-item label="车间名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入车间名称" />
            </el-form-item>
            <el-form-item label="车间类型" prop="type">
              <el-select
                v-model="form.type"
                placeholder="请选择车间类型"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in workShopTypeList"
                  :key="dict.dictValue"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="所属单位" prop="company">
              <el-input v-model="form.company" placeholder="请输入所属单位" />
            </el-form-item>
            <el-form-item label="车间地址" prop="address">
              <el-input
                v-model="form.address"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
            <el-form-item label="车间面积" prop="area">
              <el-input v-model="form.area" placeholder="请输入车间面积" />
            </el-form-item>
            <el-form-item label="参考价格" prop="price">
              <el-input v-model="form.price" placeholder="请输入参考价格" />
            </el-form-item>
            <el-form-item label="车间概况" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
            <el-form-item label="设备资源" prop="resources">
              <el-input
                v-model="form.resources"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
            <el-form-item label="生产能力" prop="capability">
              <el-input
                v-model="form.capability"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
            <el-form-item label="注意事项" prop="notes">
              <el-input
                v-model="form.notes"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
            <el-form-item label="车间图片" prop="images">
              <ImageUpload v-model="form.images" resultType="string"></ImageUpload>
            </el-form-item>
            <el-form-item class="footer-submit">
              <el-button type="primary" @click="onSubmit">{{
                form.id ? "保存" : "发布"
              }}</el-button>
              <el-button style="margin-left: 140px" @click.once="onCancel"
                >取消</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import UserMenu from "../components/userMenu.vue";
import { listData } from "@/api/system/dict/data";
import {
  addWorkInfo,
  updateWorkInfo,
  workDetailData,
} from "@/api/manufacturingSharing";
export default {
  name: "User",
  components: { UserMenu },
  data() {
    return {
      form: {
        id: null,
        name: null,
        company: null,
        address: null,
        area: null,
        price: null,
        description: null,
        resources: null,
        capability: null,
        notes: null,
        images: null,
        type: null,
        createTime: null,
        updateTime: null,
        checkStatus: null,
        createBy: null,
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "车间名称不能为空", trigger: "blur" },
        ],
        company: [
          { required: true, message: "所属单位不能为空", trigger: "blur" },
        ],
        address: [
          { required: true, message: "车间地址不能为空", trigger: "blur" },
        ],
        type: [
          { required: true, message: "车间类型不能为空", trigger: "change" },
        ],
      },
      queryParams: {
        categoryId: undefined,
        status: undefined,
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      workShopTypeList: [], // 车间类型
    };
  },
  created() {
    this.getDicts();
    if (this.$route.query.id) {
      this.getDetail();
    }
  },
  methods: {
    /** 查询字典数据列表 */
    getDicts() {
      let params = { dictType: "workshop_type" };
      listData(params).then((res) => {
        if (res.code === 200) {
          this.workShopTypeList = res.rows;
        }
      });
    },
    getDetail() {
      workDetailData(this.$route.query.id).then((res) => {
        if (res.code == 200) {
          this.form = res.data;
        }
      });
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            this.form.checkStatus = 0;
            updateWorkInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.$router.go(-1);
            });
          } else {
            this.form.checkStatus = 0;
            let userinfo = JSON.parse(
              window.sessionStorage.getItem("userinfo")
            );
            this.form.createBy = userinfo.memberPhone;
            addWorkInfo(this.form).then((response) => {
              this.$modal.msgSuccess("发布成功,请等待审核");
              this.$router.go(-1);
            });
          }
        }
      });
    },
    onCancel() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));
  height: 100vh;
}

.formStyle {
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  .footer-submit {
    text-align: center;
  }
}
</style>
