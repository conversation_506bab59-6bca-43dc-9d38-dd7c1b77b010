<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.TestingConsultMapper">
    
    <resultMap type="TestingConsult" id="TestingConsultResult">
        <result property="id"    column="id"    />
        <result property="testingItemId"    column="testing_item_id"    />
        <result property="testingItemName"    column="testing_item_name"    />
        <result property="consultName"    column="consult_name"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="consultContent"    column="consult_content"    />
        <result property="replyContent"    column="reply_content"    />
        <result property="status"    column="status"    />
        <result property="replyTime"    column="reply_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTestingConsultVo">
        select id, testing_item_id, testing_item_name, consult_name, contact_phone, consult_content, reply_content, status, reply_time, create_time, update_time from testing_consult
    </sql>

    <select id="selectTestingConsultList" parameterType="TestingConsult" resultMap="TestingConsultResult">
        <include refid="selectTestingConsultVo"/>
        <where>  
            <if test="testingItemId != null "> and testing_item_id = #{testingItemId}</if>
            <if test="testingItemName != null  and testingItemName != ''"> and testing_item_name like concat('%', #{testingItemName}, '%')</if>
            <if test="consultName != null  and consultName != ''"> and consult_name like concat('%', #{consultName}, '%')</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="consultContent != null  and consultContent != ''"> and consult_content = #{consultContent}</if>
            <if test="replyContent != null  and replyContent != ''"> and reply_content = #{replyContent}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="replyTime != null "> and reply_time = #{replyTime}</if>
        </where>
    </select>
    
    <select id="selectTestingConsultById" parameterType="Long" resultMap="TestingConsultResult">
        <include refid="selectTestingConsultVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTestingConsult" parameterType="TestingConsult" useGeneratedKeys="true" keyProperty="id">
        insert into testing_consult
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="testingItemId != null">testing_item_id,</if>
            <if test="testingItemName != null and testingItemName != ''">testing_item_name,</if>
            <if test="consultName != null and consultName != ''">consult_name,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="consultContent != null and consultContent != ''">consult_content,</if>
            <if test="replyContent != null">reply_content,</if>
            <if test="status != null">status,</if>
            <if test="replyTime != null">reply_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="testingItemId != null">#{testingItemId},</if>
            <if test="testingItemName != null and testingItemName != ''">#{testingItemName},</if>
            <if test="consultName != null and consultName != ''">#{consultName},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="consultContent != null and consultContent != ''">#{consultContent},</if>
            <if test="replyContent != null">#{replyContent},</if>
            <if test="status != null">#{status},</if>
            <if test="replyTime != null">#{replyTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTestingConsult" parameterType="TestingConsult">
        update testing_consult
        <trim prefix="SET" suffixOverrides=",">
            <if test="testingItemId != null">testing_item_id = #{testingItemId},</if>
            <if test="testingItemName != null and testingItemName != ''">testing_item_name = #{testingItemName},</if>
            <if test="consultName != null and consultName != ''">consult_name = #{consultName},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="consultContent != null and consultContent != ''">consult_content = #{consultContent},</if>
            <if test="replyContent != null">reply_content = #{replyContent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="replyTime != null">reply_time = #{replyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTestingConsultById" parameterType="Long">
        delete from testing_consult where id = #{id}
    </delete>

    <delete id="deleteTestingConsultByIds" parameterType="String">
        delete from testing_consult where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>