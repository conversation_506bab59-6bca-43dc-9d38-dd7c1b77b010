<template>
  <div class="content">
    <div class="card-container cardStyle">
      <!-- 左侧 -->
      <div class="card_left">
        <!-- 上半部分 -->
        <div class="card_left_top">
          <div class="imgStyle">
            <img style="width: 100%; height: 100%" :src="detailsData.labImages
              ? detailsData.labImages.split(',')[0]
              : require('@/assets/device/ceshi.png')" alt="" />
          </div>
          <!-- <div class="imgContent">
            <div style="cursor: pointer">
              <img src="@/assets/device/icon_left.png" alt="" />
            </div>
            <div style="display: flex; align-items: center; margin: 0 10px">
              <div class="everyImgStyle" v-for="(item, index) in imgList" :key="index">
                <img style="width: 100%; height: 100%" src="@/assets/detectingSharing/ceshi_small.png" alt="" />
              </div>
            </div>
            <div style="cursor: pointer">
              <img src="@/assets/device/icon_right.png" alt="" />
            </div>
          </div> -->
        </div>
        <!-- 下半部分 -->
        <div class="card_left_bottom">
          <div class="title">{{ detailsData.labName }}</div>
          <div class="everyOption">
            <div class="optionName">实验室地址：</div>
            <div class="optionValue">{{ detailsData.labAddress }}</div>
          </div>
          <div class="everyOption">
            <div class="optionName">联系方式：</div>
            <div class="optionValue">{{ detailsData.contactPhone }}</div>
          </div>
          <div class="buttonStyle" @click="jumpIntention">申请使用</div>
        </div>
      </div>
      <!-- 中间 -->
      <div class="card_center_line"></div>
      <!-- 右侧 -->
      <div class="card_right">
        <div>
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">检测范围</div>
          </div>
          <div class="content_desc">
            {{ detailsData.testingScope }}
          </div>
        </div>
        <div style="margin-top: 41px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">实验室介绍</div>
          </div>
          <div class="content_desc">
            {{ detailsData.labIntroduction }}
          </div>
        </div>
        <div style="margin-top: 41px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">资质证件</div>
          </div>
          <div class="content_desc">
            <div class="imgList">
              <div v-for="item in certificateList" :key="item.id" class="imgItem">
                <img src="@/assets/detectingSharing/certificate.png" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getLaboratoryDetail } from "@/api/serviceSharing";
export default {
  name: "deviceDetail",
  data() {
    return {
      id: 0,
      detailsData: {},
      imgList: [
        {
          id: 1,
        },
        {
          id: 2,
        },
        {
          id: 3,
        },
        {
          id: 4,
        },
        {
          id: 5,
        },
      ],
      certificateList: [
        {
          id: 1,
        },
        {
          id: 2,
        },
        {
          id: 3,
        },
        {
          id: 4,
        },
        {
          id: 5,
        },
      ]
    };
  },
  created() {
    this.id = this.$route.query.id;
    this.getDetails();
  },
  methods: {
    async getDetails() {
      // 获取详情
      let res = await getLaboratoryDetail(this.id);
      if (res.code == 200) {
        this.detailsData = res.data;
      }
    },
    jumpIntention() {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => { });
        return;
      } else {
        this.$router.push(`/demandInterested?demandName=${this.detailsData.labName}&updateTime=${this.detailsData.updateTime}&intentionType=9&fieldName=实验室共享&intentionId=${this.detailsData.id}`);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  background-color: #f2f2f2;
  padding: 30px 0 61px 0;
}

.cardStyle {
  height: 660px;
  background-color: #ffffff;
  padding: 60px 56px 54px 50px;
  display: flex;
}

.card_left {
  .card_left_top {
    .imgStyle {
      width: 330px;
      height: 230px;
      border-radius: 2px;
      margin-left: 10px;
    }

    .imgContent {
      margin-top: 15px;
      display: flex;
      align-items: center;

      .everyImgStyle {
        width: 54px;
        height: 50px;
        margin-left: 10px;
        cursor: pointer;
      }

      .everyImgStyle:nth-child(1) {
        margin-left: 0;
      }
    }
  }

  .card_left_bottom {
    margin-top: 30px;

    .title {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 20px;
      color: #222222;
      margin-bottom: 13px;
    }

    .everyOption {
      display: flex;
      align-items: center;
      margin-top: 12px;

      .optionName {
        height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
      }

      .optionValue {
        height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }
    }

    .buttonStyle {
      margin-top: 32px;
      margin-left: 55px;
      width: 220px;
      height: 50px;
      background: #21c9b8;
      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);
      border-radius: 2px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
    }
  }
}

.card_center_line {
  width: 1px;
  height: 533px;
  background: #e1e1e1;
  margin-left: 51px;
  margin-right: 61px;
}

.card_right {
  width: 100%;
  overflow-y: auto;

  .content_title {
    display: flex;
    align-items: center;

    .icon {
      width: 4px;
      height: 20px;
      background: #21c9b8;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #030a1a;
      margin-left: 10px;
    }
  }

  .content_desc {
    // width: 631px;
    // height: 159px;
    margin-top: 20px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;

    .imgList {
      display: flex;
      flex-wrap: wrap;

      .imgItem {
        width: 150px;
        height: 190px;
        margin-right: 6px;
        margin-bottom: 10px;
      }
    }
  }
}
</style>