{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\classicCase.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\classicCase.vue", "mtime": 1750311962928}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["classicCase.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4EA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "classicCase.vue", "sourceRoot": "src/views/components/home", "sourcesContent": ["<template>\r\n  <div\r\n    class=\"content wow animate__animated animate__fadeInUp\"\r\n    data-wow-duration=\"1s\"\r\n  >\r\n    <div class=\"img_right\">\r\n      <img src=\"../../../assets/images/home/<USER>\" alt=\"\" />\r\n    </div>\r\n    <div class=\"card-container\">\r\n      <div class=\"caseTitle\">\r\n        <div>典型案例</div>\r\n        <div class=\"allCase\" @click=\"goclassicCase\">查看全部>></div>\r\n      </div>\r\n      <div style=\"margin: 40px 0\">\r\n        <div style=\"display: flex; justify-content: center\">\r\n          <div\r\n            v-for=\"item in tabs\"\r\n            :key=\"item.dictValue\"\r\n            class=\"caseName\"\r\n            :class=\"activeName == item.dictValue ? 'caseNameHover' : ''\"\r\n            @click=\"getCaseType(item.dictValue)\"\r\n          >\r\n            {{ item.dictLabel }}\r\n          </div>\r\n        </div>\r\n        <!-- <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\" stretch>\r\n          <el-tab-pane\r\n            v-for=\"item in tabs\"\r\n            :key=\"item.dictValue\"\r\n            :label=\"item.dictLabel\"\r\n            :name=\"item.dictValue\"\r\n          ></el-tab-pane>\r\n        </el-tabs> -->\r\n      </div>\r\n      <div v-if=\"list && list.length > 0\">\r\n        <el-carousel\r\n          v-loading=\"loading\"\r\n          class=\"body\"\r\n          height=\"360px\"\r\n          :autoplay=\"false\"\r\n        >\r\n          <el-carousel-item\r\n            class=\"body-item\"\r\n            v-for=\"item in list\"\r\n            :key=\"item.id\"\r\n          >\r\n            <div class=\"card\" @click=\"goCaseDetail(item.id)\">\r\n              <el-image\r\n                v-if=\"item.coverPicUrl\"\r\n                class=\"card-img\"\r\n                :src=\"item.coverPicUrl\"\r\n                fit=\"fill\"\r\n              />\r\n              <div class=\"card-content\">\r\n                <div class=\"title\">{{ item.name }}</div>\r\n                <div class=\"desc\">\r\n                  <div>{{ item.introduction }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n      <div class=\"none-class\" v-else>\r\n        <el-image\r\n          style=\"width: 160px; height: 160px\"\r\n          :src=\"require('@/assets/user/none.png')\"\r\n          :fit=\"fit\"\r\n        ></el-image>\r\n        <div class=\"text\">暂无数据</div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { caseList } from \"@/api/classicCase\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      activeName: \"0\",\r\n      loading: false,\r\n      tabs: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        industry: \"\",\r\n      },\r\n      list: [],\r\n    };\r\n  },\r\n  created() {\r\n    // this.initData();\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.$wow.init();\r\n    });\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      this.getCaseList();\r\n    },\r\n    goclassicCase() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/classicCase\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    initData() {\r\n      getDicts(\"case_industry\").then((res) => {\r\n        const { code, data = [] } = res;\r\n        if (code === 200) {\r\n          this.tabs = data;\r\n          this.tabs.unshift({\r\n            dictLabel: \"全部\",\r\n            dictValue: \"0\",\r\n          });\r\n          this.getCaseList();\r\n        }\r\n      });\r\n    },\r\n    getCaseList() {\r\n      this.loading = true;\r\n      this.queryParams.industry = this.activeName == \"0\" ? \"\" : this.activeName;\r\n      caseList(this.queryParams).then((response) => {\r\n        this.list = response.rows;\r\n        // this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    goCaseDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/caseDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    getCaseType(value) {\r\n      this.activeName = value;\r\n      this.queryParams.industry = this.activeName == \"0\" ? \"\" : this.activeName;\r\n      this.getCaseList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.caseTitle {\r\n  width: 100%;\r\n  font-size: 36px;\r\n  text-align: center;\r\n  margin-top: 84px;\r\n  position: relative;\r\n  // padding-top: 64px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  color: #000000;\r\n  .allCase {\r\n    position: absolute;\r\n    top: 50px;\r\n    right: 0;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    line-height: 26px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n::v-deep .el-tabs__item {\r\n  font-size: 16px;\r\n}\r\n::v-deep .el-tabs__nav-scroll {\r\n  width: 67%;\r\n  margin: 0 auto;\r\n}\r\n::v-deep .el-tabs__nav-wrap::after {\r\n  display: none;\r\n}\r\n.body {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 360px;\r\n  background: #ffffff;\r\n  box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.1);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  ::v-deep.el-carousel__arrow {\r\n    i {\r\n      color: #777777;\r\n    }\r\n    &:hover {\r\n      background-color: #21c9b8;\r\n      i {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n  ::v-deep.el-carousel__indicators--horizontal {\r\n    bottom: 30px;\r\n    .el-carousel__indicator {\r\n      .el-carousel__button {\r\n        width: 4px;\r\n        height: 4px;\r\n        background: #d8d8d8;\r\n        border-radius: 3px;\r\n      }\r\n      &.is-active {\r\n        .el-carousel__button {\r\n          width: 24px;\r\n          height: 4px;\r\n          background: #21c9b8;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &-item {\r\n    display: flex;\r\n    width: 100%;\r\n    flex-shrink: 0;\r\n    flex-direction: row;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n  .card {\r\n    display: flex;\r\n    flex-direction: row;\r\n    flex-shrink: 0;\r\n    height: 290px;\r\n    cursor: pointer;\r\n    &-img {\r\n      width: 406px;\r\n      height: 290px;\r\n    }\r\n    &-content {\r\n      width: 500px;\r\n      height: 100%;\r\n      padding-left: 50px;\r\n      padding-right: 50px;\r\n      padding-top: 50px;\r\n      background-image: url(\"../../../assets/images/home/<USER>\");\r\n      background-size: 100% 100%;\r\n      .title {\r\n        font-size: 22px;\r\n        font-weight: 500;\r\n        color: #ffffff;\r\n        line-height: 24px;\r\n        margin-bottom: 20px;\r\n        font-family: Source Han Sans CN;\r\n      }\r\n      .desc {\r\n        min-height: 78px;\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #ffffff;\r\n        line-height: 26px;\r\n        margin-bottom: 17px;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 6;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n      .info {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        margin-bottom: 32px;\r\n        &-tag {\r\n          background: rgba(197, 37, 33, 0.1);\r\n          border-radius: 4px;\r\n          padding: 10px 12px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #21c9b8;\r\n          line-height: 12px;\r\n          margin-right: 18px;\r\n        }\r\n        &-day {\r\n          display: flex;\r\n          flex-direction: row;\r\n          align-items: center;\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 18px;\r\n          .count {\r\n            font-size: 24px;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            line-height: 24px;\r\n            padding: 0 8px;\r\n          }\r\n        }\r\n      }\r\n      .view-btn {\r\n        // @include flexCenter;\r\n        width: 120px;\r\n        height: 40px;\r\n        background: #21c9b8;\r\n        border-radius: 4px;\r\n        font-size: 16px;\r\n        font-weight: 500;\r\n        color: #ffffff;\r\n        line-height: 16px;\r\n      }\r\n    }\r\n  }\r\n}\r\n.none-class {\r\n  text-align: center;\r\n  padding: 8% 0;\r\n  .text {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #999999;\r\n    line-height: 14px;\r\n  }\r\n}\r\n.content {\r\n  width: 100%;\r\n  // height: 730px;\r\n  // background: url(\"../../../assets/images/home/<USER>\") no-repeat;\r\n  // background-size: 100% 100%;\r\n  // animation: fadeInUp; /* referring directly to the animation's @keyframe declaration */\r\n  // animation-duration: 2s; /* don't forget to set a duration! */\r\n  position: relative;\r\n  .img_right {\r\n    position: absolute;\r\n    top: 110px;\r\n    right: 0;\r\n    width: calc((100% - 1200px) / 2);\r\n    height: 450px;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n}\r\n.caseName {\r\n  width: 110px;\r\n  height: 40px;\r\n  border-radius: 20px;\r\n  margin-left: 15px;\r\n  text-align: center;\r\n  line-height: 40px;\r\n  cursor: pointer;\r\n}\r\n.caseNameHover {\r\n  background: #21c9b8;\r\n  color: #ffffff;\r\n}\r\n.caseName:nth-child(1) {\r\n  margin-left: 0;\r\n}\r\n</style>\r\n"]}]}