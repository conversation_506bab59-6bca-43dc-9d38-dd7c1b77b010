{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\publishEmInformation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\publishEmInformation\\index.vue", "mtime": 1750311963077}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/publishEmInformation", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row\r\n      :gutter=\"20\"\r\n      style=\"background: linear-gradient(to right, #e1f7f0, #f4fcfa)\"\r\n    >\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"formStyle\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"岗位名称\" prop=\"positionName\">\r\n              <el-input\r\n                v-model=\"form.positionName\"\r\n                placeholder=\"请输入岗位名称\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"薪资范围\" prop=\"salaryRange\">\r\n              <el-select\r\n                v-model=\"form.salaryRange\"\r\n                placeholder=\"请选择薪资范围\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in salaryRangeList\"\r\n                  :key=\"dict.dictLabel\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"最低薪资\" prop=\"salaryMin\">\r\n              <el-input v-model=\"form.salaryMin\" placeholder=\"请输入最低薪资\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"最高薪资\" prop=\"salaryMax\">\r\n              <el-input v-model=\"form.salaryMax\" placeholder=\"请输入最高薪资\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"年龄限制\" prop=\"ageLimit\">\r\n              <el-input v-model=\"form.ageLimit\" placeholder=\"请输入年龄限制\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"用工单位\" prop=\"company\">\r\n              <el-input v-model=\"form.company\" placeholder=\"请输入用工单位\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"用工地点\" prop=\"location\">\r\n              <el-select\r\n                v-model=\"form.location\"\r\n                placeholder=\"请选择薪资范围\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in locationList\"\r\n                  :key=\"dict.dictLabel\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系方式\" prop=\"contactPhone\">\r\n              <el-input\r\n                v-model=\"form.contactPhone\"\r\n                placeholder=\"请输入联系方式\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"岗位要求\" prop=\"requirements\">\r\n              <el-input\r\n                v-model=\"form.requirements\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"岗位职责\" prop=\"responsibilities\">\r\n              <el-input\r\n                v-model=\"form.responsibilities\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"其他限制\" prop=\"otherLimits\">\r\n              <el-input\r\n                v-model=\"form.otherLimits\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button type=\"primary\" @click=\"onSubmit\">发布</el-button>\r\n              <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\"\r\n                >取消</el-button\r\n              >\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { employAddData } from \"@/api/serviceSharing\";\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      form: {\r\n        positionName: \"\",\r\n        requirements: \"\",\r\n        salaryRange: \"\",\r\n        responsibilities: \"\",\r\n        desc: \"\",\r\n        supplyName: \"\",\r\n        technologyType: [],\r\n        applicationArea: [],\r\n        productPhoto: [],\r\n        cooperationMode: \"\",\r\n        productStage: \"\",\r\n        enclosure: [],\r\n        companyName: \"\",\r\n        contactsName: \"\",\r\n        contactsMobile: \"\",\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        positionName: [\r\n          { required: true, message: \"岗位名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        salaryRange: [\r\n          {\r\n            required: true,\r\n            message: \"薪资范围不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        salaryMin: [\r\n          { required: true, message: \"最低薪资不能为空\", trigger: \"blur\" },\r\n        ],\r\n        salaryMax: [\r\n          { required: true, message: \"最高薪资不能为空\", trigger: \"blur\" },\r\n        ],\r\n        company: [\r\n          { required: true, message: \"用工单位不能为空\", trigger: \"blur\" },\r\n        ],\r\n        location: [\r\n          { required: true, message: \"用工地点不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactPhone: [\r\n          { required: true, message: \"联系方式不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      queryParams: {\r\n        categoryId: undefined,\r\n        status: undefined,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        total: 0,\r\n      },\r\n      labelStyle: {\r\n        fontWeight: 400,\r\n        fontSize: \"14px\",\r\n        color: \"#999999\",\r\n        width: \"60px\",\r\n        justifyContent: \"flex-end\",\r\n      },\r\n      contentStyle: {\r\n        fontWeight: 400,\r\n        fontSize: \"14px\",\r\n        color: \"#333333\",\r\n      },\r\n      salaryRangeList: [], // 薪资范围\r\n      locationList: [], // 用工地点\r\n    };\r\n  },\r\n  created() {\r\n    this.getSalaryRange();\r\n    this.getLocation();\r\n  },\r\n  methods: {\r\n    // 薪资范围字典\r\n    getSalaryRange() {\r\n      let params = { dictType: \"salary_range\" };\r\n      listData(params).then((response) => {\r\n        this.salaryRangeList = response.rows;\r\n      });\r\n    },\r\n    // 用工地点字典\r\n    getLocation() {\r\n      let params = { dictType: \"location\" };\r\n      listData(params).then((response) => {\r\n        this.locationList = response.rows;\r\n      });\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams = {\r\n        categoryId: undefined,\r\n        status: undefined,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      };\r\n      this.getList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    onSubmit() {\r\n      let userinfo = JSON.parse(window.sessionStorage.getItem(\"userinfo\"));\r\n      this.form.createBy = userinfo.memberPhone;\r\n      console.log(this.form)\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          // this.form.materials = this.jobList;\r\n          employAddData(this.form).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"发布成功\");\r\n              this.onCancel();\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$router.push(\"/user/emInformation\");\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.formStyle {\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  .footer-submit {\r\n    text-align: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}