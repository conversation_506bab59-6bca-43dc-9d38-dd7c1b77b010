<template>
  <div
    class="content wow animate__animated animate__fadeInUp"
    data-wow-duration="1s"
  >
    <div class="img_right">
      <img src="../../../assets/images/home/<USER>" alt="" />
    </div>
    <div class="card-container">
      <div class="caseTitle">
        <div>典型案例</div>
        <div class="allCase" @click="goclassicCase">查看全部>></div>
      </div>
      <div style="margin: 40px 0">
        <div style="display: flex; justify-content: center">
          <div
            v-for="item in tabs"
            :key="item.dictValue"
            class="caseName"
            :class="activeName == item.dictValue ? 'caseNameHover' : ''"
            @click="getCaseType(item.dictValue)"
          >
            {{ item.dictLabel }}
          </div>
        </div>
        <!-- <el-tabs v-model="activeName" @tab-click="handleClick" stretch>
          <el-tab-pane
            v-for="item in tabs"
            :key="item.dictValue"
            :label="item.dictLabel"
            :name="item.dictValue"
          ></el-tab-pane>
        </el-tabs> -->
      </div>
      <div v-if="list && list.length > 0">
        <el-carousel
          v-loading="loading"
          class="body"
          height="360px"
          :autoplay="false"
        >
          <el-carousel-item
            class="body-item"
            v-for="item in list"
            :key="item.id"
          >
            <div class="card" @click="goCaseDetail(item.id)">
              <el-image
                v-if="item.coverPicUrl"
                class="card-img"
                :src="item.coverPicUrl"
                fit="fill"
              />
              <div class="card-content">
                <div class="title">{{ item.name }}</div>
                <div class="desc">
                  <div>{{ item.introduction }}</div>
                </div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="none-class" v-else>
        <el-image
          style="width: 160px; height: 160px"
          :src="require('@/assets/user/none.png')"
          :fit="fit"
        ></el-image>
        <div class="text">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDicts } from "@/api/system/dict/data";
import { caseList } from "@/api/classicCase";

export default {
  data() {
    return {
      fit: "cover",
      activeName: "0",
      loading: false,
      tabs: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        industry: "",
      },
      list: [],
    };
  },
  created() {
    // this.initData();
  },
  mounted() {
    this.$nextTick(() => {
      this.$wow.init();
    });
  },
  methods: {
    handleClick(tab, event) {
      this.getCaseList();
    },
    goclassicCase() {
      let routeData = this.$router.resolve({
        path: "/classicCase",
      });
      window.open(routeData.href, "_blank");
    },
    initData() {
      getDicts("case_industry").then((res) => {
        const { code, data = [] } = res;
        if (code === 200) {
          this.tabs = data;
          this.tabs.unshift({
            dictLabel: "全部",
            dictValue: "0",
          });
          this.getCaseList();
        }
      });
    },
    getCaseList() {
      this.loading = true;
      this.queryParams.industry = this.activeName == "0" ? "" : this.activeName;
      caseList(this.queryParams).then((response) => {
        this.list = response.rows;
        // this.total = response.total;
        this.loading = false;
      });
    },
    goCaseDetail(id) {
      let routeData = this.$router.resolve({
        path: "/caseDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    getCaseType(value) {
      this.activeName = value;
      this.queryParams.industry = this.activeName == "0" ? "" : this.activeName;
      this.getCaseList();
    },
  },
};
</script>

<style lang="scss" scoped>
.caseTitle {
  width: 100%;
  font-size: 36px;
  text-align: center;
  margin-top: 84px;
  position: relative;
  // padding-top: 64px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #000000;
  .allCase {
    position: absolute;
    top: 50px;
    right: 0;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #21c9b8;
    line-height: 26px;
    cursor: pointer;
  }
}
::v-deep .el-tabs__item {
  font-size: 16px;
}
::v-deep .el-tabs__nav-scroll {
  width: 67%;
  margin: 0 auto;
}
::v-deep .el-tabs__nav-wrap::after {
  display: none;
}
.body {
  position: relative;
  width: 100%;
  height: 360px;
  background: #ffffff;
  box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.1);
  border-radius: 10px;
  overflow: hidden;
  ::v-deep.el-carousel__arrow {
    i {
      color: #777777;
    }
    &:hover {
      background-color: #21c9b8;
      i {
        color: #ffffff;
      }
    }
  }
  ::v-deep.el-carousel__indicators--horizontal {
    bottom: 30px;
    .el-carousel__indicator {
      .el-carousel__button {
        width: 4px;
        height: 4px;
        background: #d8d8d8;
        border-radius: 3px;
      }
      &.is-active {
        .el-carousel__button {
          width: 24px;
          height: 4px;
          background: #21c9b8;
          border-radius: 3px;
        }
      }
    }
  }
  &-item {
    display: flex;
    width: 100%;
    flex-shrink: 0;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
  .card {
    display: flex;
    flex-direction: row;
    flex-shrink: 0;
    height: 290px;
    cursor: pointer;
    &-img {
      width: 406px;
      height: 290px;
    }
    &-content {
      width: 500px;
      height: 100%;
      padding-left: 50px;
      padding-right: 50px;
      padding-top: 50px;
      background-image: url("../../../assets/images/home/<USER>");
      background-size: 100% 100%;
      .title {
        font-size: 22px;
        font-weight: 500;
        color: #ffffff;
        line-height: 24px;
        margin-bottom: 20px;
        font-family: Source Han Sans CN;
      }
      .desc {
        min-height: 78px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #ffffff;
        line-height: 26px;
        margin-bottom: 17px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 6;
        text-overflow: ellipsis;
        word-wrap: break-word;
      }
      .info {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 32px;
        &-tag {
          background: rgba(197, 37, 33, 0.1);
          border-radius: 4px;
          padding: 10px 12px;
          font-size: 12px;
          font-weight: 400;
          color: #21c9b8;
          line-height: 12px;
          margin-right: 18px;
        }
        &-day {
          display: flex;
          flex-direction: row;
          align-items: center;
          font-size: 14px;
          font-weight: 500;
          color: #333333;
          line-height: 18px;
          .count {
            font-size: 24px;
            font-weight: 500;
            color: #333333;
            line-height: 24px;
            padding: 0 8px;
          }
        }
      }
      .view-btn {
        // @include flexCenter;
        width: 120px;
        height: 40px;
        background: #21c9b8;
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        color: #ffffff;
        line-height: 16px;
      }
    }
  }
}
.none-class {
  text-align: center;
  padding: 8% 0;
  .text {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
  }
}
.content {
  width: 100%;
  // height: 730px;
  // background: url("../../../assets/images/home/<USER>") no-repeat;
  // background-size: 100% 100%;
  // animation: fadeInUp; /* referring directly to the animation's @keyframe declaration */
  // animation-duration: 2s; /* don't forget to set a duration! */
  position: relative;
  .img_right {
    position: absolute;
    top: 110px;
    right: 0;
    width: calc((100% - 1200px) / 2);
    height: 450px;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
.caseName {
  width: 110px;
  height: 40px;
  border-radius: 20px;
  margin-left: 15px;
  text-align: center;
  line-height: 40px;
  cursor: pointer;
}
.caseNameHover {
  background: #21c9b8;
  color: #ffffff;
}
.caseName:nth-child(1) {
  margin-left: 0;
}
</style>
