{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\components\\userMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\components\\userMenu.vue", "mtime": 1750311963053}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBjaGVja1Nob3dNZW51Um9sZSB9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyIjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlVzZXJNZW51IiwNCiAgcHJvcHM6IHt9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBhY3RpdmVJbmRleDogdGhpcy4kcm91dGUucGF0aCwNCiAgICAgIGNvbXBhbnlTdGF0dXM6IGZhbHNlLA0KICAgICAgbW9iaWxlOiAiIiwNCiAgICAgIGtleTogIlFtUmxPREpUVkdoa05nPT0iLA0KICAgICAgdHlwZTogImNHOXNhV041WTJGemFBPT0iLA0KICAgICAgYmFzZTY0RW5jb2RlQ2hhcnM6DQogICAgICAgICJBQkNERUZHSElKS0xNTk9QT1JTVFVXWFlaYWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXowMTIzNDU2Nzg5Ky8iLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgLy8gdGhpcy5nZXRVc2VyKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXRVc2VyKCkgew0KICAgICAgLy8gZ2V0VXNlckluZm8odGhpcy51c2VyLmlkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgLy8gICB0aGlzLnVzZXIgPSByZXNwb25zZS5kYXRhOw0KICAgICAgLy8gICB0aGlzLnJvbGVHcm91cCA9IHJlc3BvbnNlLnJvbGVHcm91cDsNCiAgICAgIC8vICAgdGhpcy5wb3N0R3JvdXAgPSByZXNwb25zZS5wb3N0R3JvdXA7DQogICAgICAvLyB9KTsNCiAgICAgIGNoZWNrU2hvd01lbnVSb2xlKCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5jb21wYW55U3RhdHVzID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3Qoa2V5LCBrZXlQYXRoKSB7DQogICAgICBjb25zb2xlLmxvZyhrZXksIGtleVBhdGgpOw0KICAgICAgY29uc29sZS5sb2codGhpcy4kcm91dGUucGF0aCk7DQogICAgfSwNCiAgICBhZGQoKSB7DQogICAgICB3aW5kb3cub3BlbigiaHR0cHM6Ly96aGVuZHVhbi5uaW5nbWVuZ2RvdS5jb20vZGlnaXRhbC1kaWFnb3Npcy13ZWIvIik7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["userMenu.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userMenu.vue", "sourceRoot": "src/views/system/user/components", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 10:58:54\r\n * @LastEditTime: 2023-02-13 19:08:08\r\n * @Description:\r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <el-menu\r\n    :default-active=\"this.$route.path\"\r\n    class=\"user-center-menu\"\r\n    @select=\"handleSelect\"\r\n    :router=\"true\"\r\n  >\r\n    <el-menu-item index=\"/user/profile\" route=\"/user/profile\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-menu\"></i>\r\n        <span class=\"menu-text\" slot=\"title\">首页</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/supplyDemand\" route=\"/user/supplyDemand\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">供需管理</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/reApplication\" route=\"/user/reApplication\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">入驻申请</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/sharedOrders\" route=\"/user/sharedOrders\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">共享订单</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/emInformation\" route=\"/user/emInformation\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">用工信息</span>\r\n      </div>\r\n    </el-menu-item>\r\n      <el-menu-item index=\"/user/equipmentManagement\" route=\"/user/equipmentManagement\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">设备管理</span>\r\n      </div>\r\n    </el-menu-item>\r\n      <el-menu-item index=\"/user/workshopManagement\" route=\"/user/workshopManagement\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">车间管理</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/dockingRecords\" route=\"/user/dockingRecords\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">对接记录</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/userCenter\" route=\"/user/userCenter\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-user\"></i>\r\n        <span slot=\"title\">用户中心</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/spCertification\" route=\"/user/spCertification\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">认证设置</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <!-- <el-menu-item index=\"/user/userInfo\" route=\"/user/userInfo\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-user\"></i>\r\n        <span slot=\"title\">个人资料</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/invoiceInfo\" route=\"/user/invoiceInfo\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-tickets\"></i>\r\n        <span slot=\"title\">发票信息</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n    <!-- <el-menu-item\r\n      index=\"/user/companyInfo\"\r\n      route=\"/user/companyInfo\"\r\n      v-show=\"this.companyStatus\"\r\n    >\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">公司信息</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n    <!-- <el-menu-item index=\"/user/spCertification\" route=\"/user/spCertification\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">认证设置</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/myCollect\" route=\"/user/myCollect\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-star-off\"></i>\r\n        <span slot=\"title\">我的收藏</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/mySubscriptions\" route=\"/user/mySubscriptions\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-finished\"></i>\r\n        <span slot=\"title\">我的订阅</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/application\" route=\"/user/application\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-news\"></i>\r\n        <span slot=\"title\">应用管理</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/orderManage\" route=\"/user/orderManage\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">订单管理</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n    <!-- <el-menu-item index=\"/user/policyDeclare\" route=\"/user/policyDeclare\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">政策申报</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n    <!-- <el-menu-item index=\"/user/noninductive\" route=\"/user/noninductive\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\" @click=\"add\">无感兑现</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n    <!-- <el-menu-item index=\"/user/commercial\" route=\"/user/commercial\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">商机推荐</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item>\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\" @click=\"add\">数字化诊断</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/companyDemand\" route=\"/user/companyDemand\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">企业需求</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/companyApply\" route=\"/user/companyApply\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">企业资源</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/abutmentRecord\" route=\"/user/abutmentRecord\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">对接记录</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/im\" route=\"/user/im\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\" route=\"/user/im\"></i>\r\n        <span slot=\"title\">即时沟通</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/approveSetting\" route=\"/user/approveSetting\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">认证设置</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item\r\n      index=\"/user/teamManage\"\r\n      route=\"/user/teamManage\"\r\n      v-show=\"this.companyStatus\"\r\n    >\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">团队管理</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n    <!-- <el-menu-item index=\"/user/notice\" route=\"/user/notice\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-chat-dot-round\"></i>\r\n        <span slot=\"title\">通知中心</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { checkShowMenuRole } from \"@/api/system/user\";\r\nexport default {\r\n  name: \"UserMenu\",\r\n  props: {},\r\n  data() {\r\n    return {\r\n      activeIndex: this.$route.path,\r\n      companyStatus: false,\r\n      mobile: \"\",\r\n      key: \"QmRlODJTVGhkNg==\",\r\n      type: \"cG9saWN5Y2FzaA==\",\r\n      base64EncodeChars:\r\n        \"ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",\r\n    };\r\n  },\r\n  created() {\r\n    // this.getUser();\r\n  },\r\n  methods: {\r\n    getUser() {\r\n      // getUserInfo(this.user.id).then((response) => {\r\n      //   this.user = response.data;\r\n      //   this.roleGroup = response.roleGroup;\r\n      //   this.postGroup = response.postGroup;\r\n      // });\r\n      checkShowMenuRole().then((response) => {\r\n        if (response.code == 200) {\r\n          this.companyStatus = true;\r\n        }\r\n      });\r\n    },\r\n    handleSelect(key, keyPath) {\r\n      console.log(key, keyPath);\r\n      console.log(this.$route.path);\r\n    },\r\n    add() {\r\n      window.open(\"https://zhenduan.ningmengdou.com/digital-diagosis-web/\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.user-center-menu {\r\n  width: 160px;\r\n  border-right: #fff;\r\n  padding: 20px 0 20px 0;\r\n  background-color: rgb(239, 251, 247);\r\n  .menu-icon {\r\n    color: #333333;\r\n    margin-right: 8px;\r\n    margin-left: 16px;\r\n  }\r\n  .el-menu-item {\r\n    color: #333333;\r\n    font-weight: 500;\r\n  }\r\n  .item-hover:hover {\r\n    color: #21c9b8;\r\n    .menu-icon {\r\n      color: #21c9b8 !important;\r\n    }\r\n  }\r\n  .el-menu-item:hover {\r\n    outline: 0 !important;\r\n    color: #21c9b8 !important;\r\n    background: #fff !important;\r\n  }\r\n  .el-menu-item.is-active {\r\n    color: #fff !important;\r\n    background-color: #21c9b8 !important;\r\n    .menu-icon {\r\n      color: #fff !important;\r\n    }\r\n  }\r\n  .el-menu-item.is-active :hover {\r\n    color: #fff !important;\r\n    .menu-icon {\r\n      color: #fff !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}