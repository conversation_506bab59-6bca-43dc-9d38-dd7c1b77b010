import request from "@/utils/request";
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function listUser(query) {
  return request({
    url: "/system/user/list",
    method: "get",
    params: query,
  });
}
// 获取用户imToken
export function getUserIMToken(query) {
  return request({
    url: "/system/im/get-token",
    method: "get",
    params: query,
  });
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: "/system/user/" + parseStrEmpty(userId),
    method: "get",
  });
}
// 个人中心-个人资料-用户信息
export function getUserInfo(userId) {
  return request({
    url: "/system/user/consumer/userInfo",
    method: "get",
    params: { userId: userId },
  });
}
// 天眼查查询企业列表
export function getCompanyListByName(name) {
  return request({
    url: "/system/company/mag/selectTianYanCompanyListByName",
    method: "get",
    params: { name: name, pageNum: 1, pageSize: 10 },
  });
}
// pc门户端个人中心-个人资料-显示认证状态的条件判断
export function checkAuthStatus() {
  return request({
    url: "/system/authentication/info/checkAuthStatus",
    method: "get",
  });
}
//公司信息、团队管理模块菜单访问逻辑
export function checkShowMenuRole() {
  return request({
    url: "/system/company/apply/checkShowMenuRole",
    method: "get",
  });
}
// 获取职务数据
export function getPositionData() {
  return request({
    url: "/system/identity/type/getData",
    method: "get",
  });
}

// 新增用户
export function addUser(data) {
  return request({
    url: "/system/user",
    method: "post",
    data: data,
  });
}
//im  根据用户id列表获取用户信息
export function getUserListByIds(ids) {
  return request({
    url: "/system/im/getUserListByIds",
    method: "post",
    data: { ids: ids },
  });
}
// 公司变更
export function transferCompany(data) {
  return request({
    url: "/system/company/apply/transferCompany",
    method: "post",
    data: data,
  });
}

// 修改用户
export function updateUser(data) {
  return request({
    url: "/system/user",
    method: "put",
    data: data,
  });
}
// 修改用户新
export function updateUserInfo(data) {
  return request({
    url: "/system/user/consumer",
    method: "put",
    data: data,
  });
}

// 删除用户
export function delUser(userId) {
  return request({
    url: "/system/user/" + userId,
    method: "delete",
  });
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password,
  };
  return request({
    url: "/system/user/resetPwd",
    method: "put",
    data: data,
  });
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status,
  };
  return request({
    url: "/system/user/changeStatus",
    method: "put",
    data: data,
  });
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: "/system/user/profile",
    method: "get",
  });
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: "/system/user/profile",
    method: "put",
    data: data,
  });
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword,
  };
  return request({
    url: "/system/user/profile/updatePwd",
    method: "put",
    params: data,
  });
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: "/system/user/profile/avatar",
    method: "post",
    data: data,
  });
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: "/system/user/authRole/" + userId,
    method: "get",
  });
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: "/system/user/authRole",
    method: "put",
    params: data,
  });
}

// 查询部门下拉树结构
export function deptTreeSelect() {
  return request({
    url: "/system/user/deptTree",
    method: "get",
  });
}

// 服务商认证-新增提交
export function spCertificationAdd(data) {
  return request({
    url: "/system/authentication/info/authentication_company",
    method: "post",
    data,
  });
}

// 服务商认证-详情
export function spCertificationDetail() {
  return request({
    url: "/system/authentication/info/detail",
    method: "get",
  });
}

// 我的订阅-订阅列表
export function sublist(params) {
  return request({
    url: "/uuc/scm/order/mylist",
    method: "get",
    params,
  });
}

// 我的订阅-订阅列表-取消订单
export function cancelOrder(id) {
  return request({
    url: `/uuc/scm/order/${id}`,
    method: "delete",
  });
}

// 我的订阅-订阅订单详情
export function orderDetail(id) {
  return request({
    url: `/uuc/scm/order/${id}`,
    method: "get",
  });
}

// 开票信息-查询开票信息列表
export function invoiceList() {
  return request({
    url: "/uuc/invoice/selectInvoiceInformationDetails",
    method: "get",
  });
}

// 开票信息-新增
export function invoiceAdd(data) {
  return request({
    url: "/uuc/invoice/addInvoiceInformation",
    method: "post",
    data,
  });
}

// 开票信息-修改
export function invoiceEdit(data) {
  return request({
    url: "/uuc/invoice/updateInformation",
    method: "post",
    data,
  });
}

// 我的订阅-申请开票
export function applyInvoice(data) {
  return request({
    url: "/uuc/invoice/addInvoice",
    method: "post",
    data,
  });
}

// 订单管理-列表
export function orderList(params) {
  return request({
    url: "/uuc/scm/order/servicelist",
    method: "get",
    params,
  });
}

// 订单管理-详情
export function orderDel(params) {
  return request({
    url: "/uuc/scm/order/selectServiceProviderOrder",
    method: "get",
    params,
  });
}

// 订单-修改状态(收发货)
export function modifyStatus(data) {
  return request({
    url: "/uuc/scm/order",
    method: "put",
    data,
  });
}

// 订单-发送发票
export function sendInvoice(data) {
  return request({
    url: "/uuc/invoice/addInvoicePdf",
    method: "post",
    data,
  });
}

// 订阅订单-下载发票
export function downLoadInvoice(params) {
  return request({
    url: "/uuc/invoice/downloadPdf",
    method: "get",
    params,
  });
}

// 查询订单个数根据订单状态
export function pendingFeesNum(data) {
  return request({
    url: "/uuc/scm/order/selectOrderNumberByStatus",
    method: "post",
    data,
  });
}

// 首页-数据面板-应用、订单、开票个数
export function appOrderNum() {
  return request({
    url: "/uuc/scm/order/selectDataPanel",
    method: "get",
  });
}

// 首页-订阅统计
export function subStatistics(params) {
  return request({
    url: "/uuc/scm/order/selectSubscriptionNumber",
    method: "get",
    params,
  });
}

// 个人资料-验证原手机号
export function oldPhone(data) {
  return request({
    url: "/auth/chiWeb/confirmUserBySmsCode",
    method: "post",
    data,
  });
}

// 个人资料-修改原手机号
export function newPhone(data) {
  return request({
    url: "/auth/chiWeb/confirmUserPhoneBySmsCode",
    method: "post",
    data,
  });
}

// 所在行业列表
export function industryList() {
  return request({
    url: "/portalweb/solutionType/listDesk",
    method: "get",
  });
}
// 获取地区数据
export function getAreaData() {
  return request({
    url: "/portalweb/region/tree",
    method: "get",
    transformRequest: [
      function (data) {
        let ret = "";
        for (let i in data) {
          ret += encodeURIComponent(i) + "=" + encodeURIComponent(data[i]) + "&";
        }
        return ret;
      },
    ],
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
}

// 用户信息保存
export function userInfoSave(data) {
  return request({
    url: "/portalweb/Member/update",
    method: "post",
    data,
  });
}

// 对接记录列表
export function dockingList(params) {
  return request({
    url: "/portalweb/IntentionApply/list",
    method: "get",
    params,
  });
}