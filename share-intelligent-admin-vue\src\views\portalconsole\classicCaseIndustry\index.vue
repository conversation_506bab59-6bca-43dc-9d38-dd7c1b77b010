<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="父级编码" prop="parentId">
        <el-input
          v-model="queryParams.parentId"
          placeholder="请输入父级编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="名称" prop="classicCaseIndustryName">
        <el-input
          v-model="queryParams.classicCaseIndustryName"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="服务范围" prop="category">
        <el-select v-model="queryParams.category" placeholder="请选择服务范围" clearable>
          <el-option
            v-for="dict in dict.type.classic_case_category"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:classicCaseIndustry:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:classicCaseIndustry:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:classicCaseIndustry:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:classicCaseIndustry:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="classicCaseIndustryList" @selection-change="handleSelectionChange"
    row-key="classicCaseIndustryId"
    :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="案例行业ID" align="center" prop="classicCaseIndustryId" /> -->
      <el-table-column label="父级编码" align="center" prop="parentId" />
      <el-table-column label="名称" align="center" prop="classicCaseIndustryName" />
      <el-table-column label="服务范围" align="center" prop="category">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.classic_case_category" :value="scope.row.category"/>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:classicCaseIndustry:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:classicCaseIndustry:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改典型案例行业对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="父级编码" prop="parentId">
          <!-- <el-input v-model="form.parentId" placeholder="请输入父级编码" /> -->
          <Treeselect v-model="form.parentId" 
          :options="industryOptions" 
          :normalizer="normalizer" 
          placeholder="请选择父级编码" />
        </el-form-item>
        <el-form-item label="名称" prop="classicCaseIndustryName">
          <el-input v-model="form.classicCaseIndustryName" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item label="服务范围" prop="category">
          <el-select v-model="form.category" placeholder="请选择服务范围">
            <el-option
              v-for="dict in dict.type.classic_case_category"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listClassicCaseIndustry, getClassicCaseIndustry, delClassicCaseIndustry, addClassicCaseIndustry, updateClassicCaseIndustry } from "@/api/portalconsole/classicCaseIndustry";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
  name: "ClassicCaseIndustry",
  dicts: ['classic_case_category'],
  components:{
    Treeselect
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 典型案例行业表格数据
      classicCaseIndustryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        parentId: null,
        classicCaseIndustryName: null,
        category: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      industryOptions:[]
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询典型案例行业列表 */
    getList() {
      this.loading = true;
      listClassicCaseIndustry(this.queryParams).then(response => {
        this.classicCaseIndustryList = this.handleTree(response.rows, "classicCaseIndustryId", "parentId");
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        classicCaseIndustryId: null,
        parentId: null,
        classicCaseIndustryName: null,
        category: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.classicCaseIndustryId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加典型案例行业";
      this.getTreeselect()
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.getTreeselect()
      const classicCaseIndustryId = row.classicCaseIndustryId || this.ids
      getClassicCaseIndustry(classicCaseIndustryId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改典型案例行业";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.classicCaseIndustryId != null) {
            updateClassicCaseIndustry(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addClassicCaseIndustry(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const classicCaseIndustryIds = row.classicCaseIndustryId || this.ids;
      this.$modal.confirm('是否确认删除典型案例行业编号为"' + classicCaseIndustryIds + '"的数据项？').then(function() {
        return delClassicCaseIndustry(classicCaseIndustryIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/classicCaseIndustry/export', {
        ...this.queryParams
      }, `classicCaseIndustry_${new Date().getTime()}.xlsx`)
    },

     /** 转换行业维护数据结构 */
     normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      };
    },
    //树形下拉
    getTreeselect() {
      let queryParams={
        pageNum: 1,
        pageSize: 100,
        parentId: null,
        classicCaseIndustryName: null,
      }
      listClassicCaseIndustry(queryParams).then(response => {
        console.log("data",response.rows)
        let arr=[]
        response.rows.forEach(item => {
          arr.push({
            id:item.classicCaseIndustryId,
            pId:item.parentId,
            name:item.classicCaseIndustryName
          })
          
        });
        this.industryOptions=[]
        //console.log("arr",arr)
        const list = { id: 0, name: '顶级节点', children: [] };
        list.children = this.handleTree(arr, "id", "pId");
        this.industryOptions.push(list);
        console.log("this.industryOptions",this.industryOptions)
      });
    },
  }
};
</script>
