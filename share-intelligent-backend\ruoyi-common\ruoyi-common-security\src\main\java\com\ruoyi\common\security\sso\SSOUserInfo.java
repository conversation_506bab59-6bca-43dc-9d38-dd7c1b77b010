package com.ruoyi.common.security.sso;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Map;

/**
 * SSO用户信息
 * 
 * <AUTHOR>
 */
public class SSOUserInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /** SSO用户ID */
    private String userId;
    
    /** SSO用户名 */
    private String username;
    
    /** 客户端ID */
    private String clientId;
    
    /** 登录时间 */
    private Long loginTime;
    
    /** 角色列表 */
    private String[] roles;
    
    /** 权限列表 */
    private String[] permissions;
    
    /** 是否有权限 */
    private Boolean hasPermission;
    
    /** 访问令牌 */
    private String accessToken;
    
    /** 令牌过期时间 */
    private Long tokenExpireTime;
    
    public SSOUserInfo() {
    }
    
    public SSOUserInfo(Map<String, Object> userInfoMap) {
        if (userInfoMap != null) {
            this.userId = (String) userInfoMap.get("userId");
            this.username = (String) userInfoMap.get("username");
            this.clientId = (String) userInfoMap.get("clientId");
            this.loginTime = (Long) userInfoMap.get("loginTime");
            this.hasPermission = (Boolean) userInfoMap.get("hasPermission");
            
            // 处理角色数组
            Object rolesObj = userInfoMap.get("roles");
            if (rolesObj instanceof String[]) {
                this.roles = (String[]) rolesObj;
            } else if (rolesObj instanceof Object[]) {
                Object[] roleArray = (Object[]) rolesObj;
                this.roles = Arrays.stream(roleArray).map(Object::toString).toArray(String[]::new);
            }
            
            // 处理权限数组
            Object permissionsObj = userInfoMap.get("permissions");
            if (permissionsObj instanceof String[]) {
                this.permissions = (String[]) permissionsObj;
            } else if (permissionsObj instanceof Object[]) {
                Object[] permissionArray = (Object[]) permissionsObj;
                this.permissions = Arrays.stream(permissionArray).map(Object::toString).toArray(String[]::new);
            }
        }
    }
    
    /**
     * 检查是否有指定角色
     */
    public boolean hasRole(String role) {
        if (roles == null || role == null) {
            return false;
        }
        return Arrays.asList(roles).contains(role);
    }
    
    /**
     * 检查是否有指定权限
     */
    public boolean hasPermission(String permission) {
        if (permissions == null || permission == null) {
            return false;
        }
        return Arrays.asList(permissions).contains(permission);
    }
    
    /**
     * 检查是否有任意一个角色
     */
    public boolean hasAnyRole(String... roles) {
        if (this.roles == null || roles == null) {
            return false;
        }
        for (String role : roles) {
            if (hasRole(role)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查是否有任意一个权限
     */
    public boolean hasAnyPermission(String... permissions) {
        if (this.permissions == null || permissions == null) {
            return false;
        }
        for (String permission : permissions) {
            if (hasPermission(permission)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查是否有所有角色
     */
    public boolean hasAllRoles(String... roles) {
        if (this.roles == null || roles == null) {
            return false;
        }
        for (String role : roles) {
            if (!hasRole(role)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 检查是否有所有权限
     */
    public boolean hasAllPermissions(String... permissions) {
        if (this.permissions == null || permissions == null) {
            return false;
        }
        for (String permission : permissions) {
            if (!hasPermission(permission)) {
                return false;
            }
        }
        return true;
    }
    
    // Getters and Setters
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getClientId() {
        return clientId;
    }
    
    public void setClientId(String clientId) {
        this.clientId = clientId;
    }
    
    public Long getLoginTime() {
        return loginTime;
    }
    
    public void setLoginTime(Long loginTime) {
        this.loginTime = loginTime;
    }
    
    public String[] getRoles() {
        return roles;
    }
    
    public void setRoles(String[] roles) {
        this.roles = roles;
    }
    
    public String[] getPermissions() {
        return permissions;
    }
    
    public void setPermissions(String[] permissions) {
        this.permissions = permissions;
    }
    
    public Boolean getHasPermission() {
        return hasPermission;
    }
    
    public void setHasPermission(Boolean hasPermission) {
        this.hasPermission = hasPermission;
    }
    
    public String getAccessToken() {
        return accessToken;
    }
    
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }
    
    public Long getTokenExpireTime() {
        return tokenExpireTime;
    }
    
    public void setTokenExpireTime(Long tokenExpireTime) {
        this.tokenExpireTime = tokenExpireTime;
    }
    
    @Override
    public String toString() {
        return "SSOUserInfo{" +
                "userId='" + userId + '\'' +
                ", username='" + username + '\'' +
                ", clientId='" + clientId + '\'' +
                ", loginTime=" + loginTime +
                ", roles=" + Arrays.toString(roles) +
                ", permissions=" + Arrays.toString(permissions) +
                ", hasPermission=" + hasPermission +
                ", accessToken='" + accessToken + '\'' +
                ", tokenExpireTime=" + tokenExpireTime +
                '}';
    }
}
