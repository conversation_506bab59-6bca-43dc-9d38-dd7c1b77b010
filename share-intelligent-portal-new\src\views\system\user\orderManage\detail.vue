<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu />
      </el-col>
      <el-col :span="20" :xs="24">
        <div class="content">
          <div class="content_top">
            <div class="orderStatus">
              <div class="statusName" v-if="info.orderStatus">
                {{
                  orderForm.filter((item) => item.value == info.orderStatus)[0]
                    .statusName
                }}
              </div>
              <div class="desc" v-if="info.orderStatus">
                {{
                  orderForm.filter((item) => item.value == info.orderStatus)[0]
                    .desc
                }}
              </div>
            </div>
            <div class="amountMoney">
              <span style="color: rgb(173, 173, 173)">订单金额:</span>
              <span style="margin-left: 10px">¥ {{ info.price }}</span>
            </div>
            <!-- 待支付 -->
            <!-- <div class="button_content" v-if="info.orderStatus == 1">
              <div>
                <div class="buttonStyle">去支付</div>
                <div style="margin-top: 10px">
                  <span
                    style="color: #21C9B8; cursor: pointer"
                    @click="cancelOrder(info.id)"
                    >取消订单</span
                  >
                  <span
                    style="margin-left: 20px; color: #21C9B8; cursor: pointer"
                    @click="tryout(info.erweima)"
                    >前往试用</span
                  >
                </div>
              </div>
            </div> -->
            <!-- 待发货 -->
            <div class="button_content" v-if="info.orderStatus == 2">
              <div class="buttonStyle" @click="goShip(info.id)">去发货</div>
            </div>
            <!-- 已发货 -->
            <div class="button_content" v-if="info.orderStatus == 4">
              <div class="buttonStyle" @click="invoicing">
                {{ info.makeinvoice == 0 ? "开具发票" : "重新开票" }}
              </div>
            </div>
            <!-- 已成交 -->
            <!-- <div class="button_content" v-if="info.orderStatus == 5">
              <div>
                <div>
                  <span
                    style="color: #21C9B8; cursor: pointer"
                    @click="applyInvoice(info.id)"
                    >已开票</span
                  >
                </div>
              </div>
            </div> -->
            <!-- 待续费 -->
            <!-- <div class="button_content" v-if="info.orderStatus == 6">
              <div>
                <div>
                  <span
                    style="color: #21C9B8; cursor: pointer"
                    @click="applyInvoice(info.id)"
                    >去支付</span
                  >
                </div>
              </div>
            </div> -->
          </div>
          <div class="content_bottom">
            <div>
              <el-descriptions title="订单信息" :column="2">
                <el-descriptions-item label="订单编号">{{
                  info.id
                }}</el-descriptions-item>
                <el-descriptions-item label="下单时间">{{
                  info.orderDate
                }}</el-descriptions-item>
                <el-descriptions-item label="应用提供">{{
                  info.supply
                }}</el-descriptions-item>
                <el-descriptions-item label="付款时间">
                  <el-tag size="small">{{
                    info.payTime ? parseTime(info.payTime) : "--"
                  }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="应用编号">{{
                  info.appCode
                }}</el-descriptions-item>
                <el-descriptions-item label="发货时间">{{
                  info.deliverTime
                }}</el-descriptions-item>
                <el-descriptions-item label="付款方式">{{
                  info.payWay
                }}</el-descriptions-item>
                <el-descriptions-item label="成交时间">{{
                  info.makeTime
                }}</el-descriptions-item>
              </el-descriptions>
            </div>
            <div style="margin-top: 30px">
              <el-table :data="tableData" style="width: 100%">
                <el-table-column prop="remark" label="产品标题">
                </el-table-column>
                <el-table-column label="产品图片">
                  <template slot-scope="scope">
                    <img
                      style="width: 100px; height: 100px"
                      :src="scope.row.appLogo"
                      alt=""
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="appCategory" label="产品类型">
                </el-table-column>
                <el-table-column label="规格信息">
                  <template slot-scope="scoped">
                    <!-- <div>规格: {{ scoped.spec }}</div> -->
                    <div>
                      可用时长: {{ scoped.validTime == "1" ? "一年" : "永久" }}
                    </div>
                    <div>可用人数: 不限</div>
                  </template>
                </el-table-column>
                <el-table-column label="有效时间">
                  <template slot-scope="scope">
                    {{
                      scope.row.expirationTime
                        ? parseTime(scope.row.expirationTime)
                        : "--"
                    }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="btnStyle">
            <el-button @click="goBack">返回</el-button>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      title="开票信息"
      :visible.sync="invoiceVisible"
      width="750px"
      append-to-body
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="发票类型:">
          {{ invoiceData.invoiceType == 1 ? "专票" : "普票" }}
        </el-form-item>
        <el-form-item label="公司名称:">
          {{ invoiceData.companyName }}
        </el-form-item>
        <el-form-item label="税号:">
          {{ invoiceData.dutyParagraph }}
        </el-form-item>
        <el-form-item label="公司地址:">
          {{ invoiceData.address }}
        </el-form-item>
        <el-form-item label="公司电话:">
          {{ invoiceData.phone }}
        </el-form-item>
        <el-form-item label="开户银行:">
          {{ invoiceData.openAccount }}
        </el-form-item>
        <el-form-item label="银行账号:">
          {{ invoiceData.bankAccount }}
        </el-form-item>
        <el-form-item label="邮箱地址:">
          {{ invoiceData.email }}
        </el-form-item>
        <el-form-item label="上传发票" prop="companyCardList">
          <el-upload
            :headers="headers"
            :action="actionUrl"
            accept=".pdf"
            :file-list="ruleForm.companyCardList"
            :on-remove="handleApplicationRemove"
            :on-success="handleApplicationSuccess"
            :on-exceed="handleExceedLicence"
            :on-preview="handlePreview"
            :limit="1"
          >
            <div>
              <el-button size="small" type="primary" icon="el-icon-upload2"
                >上传文件</el-button
              >
              <span style="margin-left: 10px">仅限pdf格式</span>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm">发送发票</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { orderDel, invoiceList } from "@/api/system/user";
import UserMenu from "../components/userMenu.vue";
import { uploadUrl } from "@/api/oss";
import { getToken } from "@/utils/auth";

export default {
  name: "Operlog",
  dicts: ["sys_oper_type", "sys_common_status"],
  components: { UserMenu },
  data() {
    var validIsEmptyArr = (s, value, callback) => {
      if (!Array.isArray(value) || value.length === 0) {
        callback(new Error("请上传文件"));
        return;
      }
      callback();
    };
    return {
      headers: { Authorization: "Bearer " + getToken() },
      actionUrl: uploadUrl(),
      queryParams: {
        pageNum: 1,
      },
      total: 0,
      flag: "0",
      tableData: [],
      info: {},
      invoiceData: {},
      orderForm: [
        {
          value: 1,
          statusName: "待支付",
          desc: "如对订单有疑问，可联系客服4008-939-365",
        },
        {
          value: 2,
          statusName: "待发货",
          desc: "平台将于2023-08-04日前发货，感谢您的支持!如您对订单有疑问，可联系客服4008-939-365",
        },
        {
          value: 3,
          statusName: "支付失败",
          desc: "订单支付失败，如您对订单有疑问，可联系客服4008-939-365",
        },
        {
          value: 4,
          statusName: "已发货",
          desc: "使用过程中有任何问题，可联系客服4008-939-365",
        },
        {
          value: 5,
          statusName: "已成交",
          desc: "使用过程中有任何问题，可联系客服4008-939-365",
        },
        {
          value: 6,
          statusName: "待续费",
          desc: "请尽快续费，以免影响正常使用",
        },
        {
          value: 7,
          statusName: "已关闭",
          desc: "如对订单有疑问，可联系客服4008-939-365",
        },
        {
          value: 8,
          statusName: "支付中",
          desc: "如对订单有疑问，可联系客服4008-939-365",
        },
        {
          value: 9,
          statusName: "已取消",
          desc: "如对订单有疑问，可联系客服4008-939-365",
        },
      ],
      invoiceVisible: false,
      ruleForm: {
        companyCardList: [],
      },
      rules: {
        companyCardList: [
          { required: true, validator: validIsEmptyArr, trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      let id = this.$route.query.id;
      let data = {
        id,
      };
      orderDel(data).then((res) => {
        this.loading = false;
        this.tableData = [];
        if (res.code === 200) {
          this.info = res.data;
          this.tableData.push(res.data);
        }
      });
    },
    handleCurrentChange(pageNum) {
      this.queryParams.pageNum = pageNum;
      this.getList();
    },
    cancelOrder(id) {
      this.$confirm("订单取消后无法恢复，请谨慎操作!", "取消订单", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          cancelOrder(id).then((res) => {
            if (res.code === 200) {
              this.$message.success("操作成功!");
              this.$router.go(-1);
            }
          });
        })
        .catch(() => {});
    },
    applyInvoice() {
      this.getInvoiceData();
    },
    getInvoiceData() {
      invoiceList().then((res) => {
        if (res.code === 200) {
          this.invoiceData = res.data;
          this.invoiceVisible = true;
        }
      });
    },
    submitForm() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let data = {
            companyCardList: this.ruleForm.companyCardList,
            orderId: this.currentId,
          };
          sendInvoice(data).then((res) => {
            if (res.code === 200) {
              this.invoiceVisible = false;
              this.$message.success("操作成功!");
              this.getList();
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    cancelDialog() {
      this.invoiceVisible = false;
    },
    goBack() {
      this.$router.go(-1);
    },
    goShip(id) {
      this.$confirm("货品发货后无法撤销，确认发货吗？", "发货确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let data = {
            id,
            orderStatus: 4,
          };
          modifyStatus(data).then((res) => {
            if (res.code === 200) {
              this.$message.success("操作成功!");
              this.getList();
            }
          });
        })
        .catch(() => {});
    },
    invoicing() {
      invoiceList().then((res) => {
        if (res.code === 200) {
          this.invoiceData = res.data;
          this.invoiceVisible = true;
        }
      });
    },
    handleApplicationRemove(file, fileList) {
      this.ruleForm.companyCardList = [];
    },
    handleApplicationSuccess(res, file, fileList) {
      if (res.code == 200) {
        this.ruleForm.companyCardList.push({
          name: res.data.name,
          url: res.data.url,
          type: res.data.type,
          suffix: res.data.suffix,
        });
      }
    },
    handleExceedLicence(files, fileList) {
      let num = files.length + fileList.length;
      if (num >= 1) {
        this.$message.error("上传数量超过上限");
        return false;
      }
    },
    handlePreview(file) {
      window.open(file.url);
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #f4f5f9;
}
.content {
  width: 100%;
  padding: 40px;
  background: #ffffff;
  .content_top {
    display: flex;
    width: 100%;
    height: 120px;
    align-items: center;
    .orderStatus {
      width: 30%;
      text-align: center;
      .statusName {
        font-size: 18px;
        font-weight: 600;
      }
      .desc {
        font-size: 14px;
        color: rgb(173, 173, 173);
        margin-top: 30px;
      }
    }
    .amountMoney {
      width: 40%;
      text-align: center;
    }
    .button_content {
      width: 20%;
      text-align: center;
      .buttonStyle {
        height: 50px;
        background: #21c9b8;
        line-height: 50px;
        color: #ffffff;
        cursor: pointer;
      }
    }
  }
  .content_bottom {
    margin-top: 20px;
    padding: 20px;
    width: 100%;
  }
  .btnStyle {
    text-align: center;
  }
}
</style>
