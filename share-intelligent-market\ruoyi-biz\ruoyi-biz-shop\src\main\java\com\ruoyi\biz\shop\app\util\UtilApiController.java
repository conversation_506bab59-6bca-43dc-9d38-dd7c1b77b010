package com.ruoyi.biz.shop.app.util;

import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.ruoyi.auth.config.QWTSmsConfig;
import com.ruoyi.auth.model.QWTSmsResponse;
import com.ruoyi.auth.util.QWTSendUtils;
import com.ruoyi.biz.shop.util.pay.wechat.util.WeixinUtil;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.shop.key.UserKey;
import com.ruoyi.shop.result.CodeMsg;
import com.ruoyi.shop.result.Result;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * created by micah on 2018-08-03
 * 工具类模块
 */

@RestController
@RequestMapping("/data/util")
@RefreshScope
public class UtilApiController {

    @Value("${wx.pay.appid.app}")
    private String appid;

    @Value("${wx.pay.appsecret.app}")
    private String appsecret;

    @Resource
    private RedisService redisService;

    @Resource
    private QWTSmsConfig qwtSmsConfig;

    /**
     * 获取Openid，自动登录
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/get_openid_session", method = RequestMethod.GET)
    public Result<JSONObject> getOpenidAndSession(@RequestParam("code") String code) {
        Map<String, String> map = WeixinUtil.getVisitOpenid(appid,appsecret, code);
        if (map != null) {
            JSONObject retVal = new JSONObject();
            retVal.put("openid", map.get("openid"));
            retVal.put("unionid", map.get("unionid"));
            retVal.put("session_key", map.get("session_key"));
            return Result.success(retVal);
        } else {
            return Result.error(CodeMsg.SERVER_ERROR);
        }
    }

    /**
     * 获取Openid，自动登录+获取用户信息
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/get_userinfo", method = RequestMethod.POST)
    public Result<JSONObject> getUserinfo(@RequestParam("session_key") String session_key,
                                          @RequestParam("ivdata") String ivdata,
                                          @RequestParam("encrypdata") String encrypdata) {
        JSONObject retVal = WeixinUtil.getVisitInfo(encrypdata, ivdata, session_key);
        if (retVal != null) {
            return Result.success(retVal);
        }
        return Result.error(CodeMsg.SERVER_ERROR);
    }

    /**
     * 发送手机验证码
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/code", method = RequestMethod.GET)
    public Result<Boolean> getCode(@RequestParam("telphone") String telphone) {
        if (!PhoneUtil.isMobile(telphone)) {
            return Result.error(CodeMsg.BIND_ERROR.fillArgs("无效的手机号"));
        }

        // 检查发送频率限制（1分钟内只能发送一次）
        String rateKey = "sms:rate:" + telphone;
        if (redisService.hasKey(rateKey)) {
            return Result.error(CodeMsg.BIND_ERROR.fillArgs("短信发送过于频繁，请稍后再试"));
        }

        String code = QWTSendUtils.getRandomNum(6);
        Boolean retVal = opQWTCode(telphone, code);
        if (retVal) {
            // 存储验证码到Redis，5分钟过期
            redisService.setCacheMapValue(UserKey.getCode.getPrefix(), telphone, code);
            // 设置发送频率限制，1分钟过期
            redisService.setCacheObject(rateKey, "1", 1L, TimeUnit.MINUTES);
            return Result.success(true);
        } else {
            return Result.error(CodeMsg.BIND_ERROR.fillArgs("短信发送失败"));
        }
    }

    /**
     * 全网智能通讯平台短信发送
     * @param telphone
     * @param code
     * @return
     */
    private Boolean opQWTCode(String telphone, String code){
        try {
            // 构建短信内容
            String content = "您的验证码为：" + code + "，5分钟内有效，如非本人操作，请忽略本短信！";

            // 发送短信
            QWTSmsResponse retVal = QWTSendUtils.sendQWTSms(telphone, content, qwtSmsConfig);
            return "OK".equals(retVal.getCode());
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
