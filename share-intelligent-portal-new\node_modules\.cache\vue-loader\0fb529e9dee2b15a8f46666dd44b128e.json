{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\newsCenter\\detail.vue?vue&type=template&id=258b448e&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\newsCenter\\detail.vue", "mtime": 1750311962976}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9Im5vdGljZS1kZXRhaWwtY29udGFpbmVyIj4KICA8ZGl2IGNsYXNzPSJub3RpY2UtZGV0YWlsLWJhbm5lciI+CiAgICA8aW1nIHNyYz0iLi4vLi4vYXNzZXRzL25vdGljZS9ub3RpY2VEZXRhaWxCYW5uZXIucG5nIiBhbHQ9IiIgLz4KICA8L2Rpdj4KICA8ZGl2IGNsYXNzPSJub3RpY2UtZGV0YWlsLXRpdGxlLWJveCI+CiAgICA8ZGl2IGNsYXNzPSJub3RpY2UtZGl2aWRlciI+PC9kaXY+CiAgICA8ZGl2IGNsYXNzPSJub3RpY2UtZGV0YWlsLXRpdGxlIj7mlrDpl7vor6bmg4U8L2Rpdj4KICAgIDxkaXYgY2xhc3M9Im5vdGljZS1kaXZpZGVyIj48L2Rpdj4KICA8L2Rpdj4KICA8ZGl2IGNsYXNzPSJub3RpY2UtZGV0YWlsLWNvbnRlbnQiPgogICAgPGRpdiBjbGFzcz0ibm90aWNlLWRldGFpbC1ib3giPgogICAgICA8ZGl2IGNsYXNzPSJub3RpY2UtaW5mby10aXRsZSI+CiAgICAgICAge3sgZGF0YS50aXRsZSB9fQogICAgICA8L2Rpdj4KICAgICAgPGRpdiBjbGFzcz0ibm90aWNlLWluZm8tdGltZSI+CiAgICAgICAge3sgZGF0YS51cGRhdGVUaW1lIH19CiAgICAgICAgPHNwYW4gc3R5bGU9Im1hcmdpbi1sZWZ0OiAxMHB4Ij7kvZzogIU6e3sgZGF0YS5jcmVhdGVCeSB9fTwvc3Bhbj4KICAgICAgPC9kaXY+CiAgICAgIDxkaXYKICAgICAgICB2LWlmPSJkYXRhLmF0dGFjaG1lbnQgJiYgZGF0YS5hdHRhY2htZW50Lmxlbmd0aCA+IDAiCiAgICAgICAgc3R5bGU9Im1hcmdpbjogMTBweCAwOyBjdXJzb3I6IHBvaW50ZXIiCiAgICAgICAgQGNsaWNrPSJkb3duTG9hZEZpbGUoZGF0YS5hdHRhY2htZW50KSIKICAgICAgPgogICAgICAgIDxpIGNsYXNzPSJlbC1pY29uLWRvd25sb2FkIj7kuIvovb3pmYTku7Y8L2k+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJub3RpY2UtaW5mby1kaXZpZGVyIj48L2Rpdj4KICAgICAgPGRpdiBjbGFzcz0ibm90aWNlLWluZm8tYm94Ij4KICAgICAgICA8ZGl2CiAgICAgICAgICB2LWh0bWw9ImRhdGEuY29udGVudCIKICAgICAgICAgIGNsYXNzPSJub3RpY2UtaW5mby1jb250ZW50IHFsLWVkaXRvciIKICAgICAgICA+PC9kaXY+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgPC9kaXY+CjwvZGl2Pgo="}, null]}