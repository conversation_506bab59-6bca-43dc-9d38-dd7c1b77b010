{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\serviceAgency.vue?vue&type=style&index=0&id=37330859&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\serviceAgency.vue", "mtime": 1750311962936}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouc2VydmljZUJnIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogMzQwcHg7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgLnNlcnZpY2VJbWcgew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICB0b3A6IDA7DQogICAgcmlnaHQ6IDA7DQogICAgd2lkdGg6IGNhbGMoKDEwMCUgLSAxMjAwcHgpIC8gMik7DQogICAgaGVpZ2h0OiA1MDBweDsNCiAgICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoIi4uLy4uLy4uL2Fzc2V0cy9pbWFnZXMvaG9tZS9zZXJ2aWNlQmcucG5nIik7DQogICAgYmFja2dyb3VuZC1zaXplOiAxMDAlIDEwMCU7DQogIH0NCn0NCi5lbnRlcnByaXNlVGl0bGUgew0KICB3aWR0aDogMTAwJTsNCiAgZm9udC1zaXplOiAzNnB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG1hcmdpbjogMTBweCAwIDYwcHggMDsNCiAgcG9zaXRpb246IHJlbGF0aXZlOw0KICBmb250LWZhbWlseTogU291cmNlIEhhbiBTYW5zIENOOw0KICBmb250LXdlaWdodDogNTAwOw0KICBjb2xvcjogIzAwMDAwMDsNCiAgLmFsbEVudGVycHJpc2Ugew0KICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICB0b3A6IDggcHg7DQogICAgcmlnaHQ6IDA7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICAgIGZvbnQtZmFtaWx5OiBTb3VyY2UgSGFuIFNhbnMgQ047DQogICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICBjb2xvcjogIzIxYzliODsNCiAgICBsaW5lLWhlaWdodDogMjZweDsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogIH0NCn0NCi5jb250ZW50IHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICB3aWR0aDogMTAwJTsNCiAgZmxleC13cmFwOiB3cmFwOw0KICAvLyBoZWlnaHQ6IDI4MHB4Ow0KICAuY29udGVudEl0ZW0gew0KICAgIHdpZHRoOiAyMjBweDsNCiAgICBoZWlnaHQ6IDkwcHg7DQogICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmZmZmOw0KICAgIGJveC1zaGFkb3c6IDBweCAycHggMjBweCAwcHggcmdiYSgzMiwgODQsIDI1MiwgMC4xNCk7DQogICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgIGltZyB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGhlaWdodDogOTBweDsNCiAgICB9DQogIH0NCiAgLmNvbnRlbnRJdGVtOm50aC1jaGlsZChuICsgNikgew0KICAgIG1hcmdpbi10b3A6IDI1cHg7DQogIH0NCiAgLmNvbnRlbnRJdGVtOmhvdmVyIHsNCiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTEwcHgpOw0KICAgIHRyYW5zaXRpb246IDFzOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["serviceAgency.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "serviceAgency.vue", "sourceRoot": "src/views/components/home", "sourcesContent": ["<template>\r\n  <div\r\n    class=\"serviceBg wow animate__animated animate__fadeInUp\"\r\n    data-wow-duration=\"1s\"\r\n  >\r\n    <div class=\"serviceImg\"></div>\r\n    <div class=\"card-container\">\r\n      <div class=\"enterpriseTitle\">\r\n        <div>服务机构</div>\r\n        <div class=\"allEnterprise\" @click=\"goEnterprise\">查看全部>></div>\r\n      </div>\r\n      <div class=\"content\">\r\n        <div\r\n          class=\"contentItem\"\r\n          v-for=\"(item, index) in data\"\r\n          :key=\"index\"\r\n          @click=\"goEnterpriseDetail(item)\"\r\n          :title=\"item.name\"\r\n        >\r\n          <div\r\n            v-if=\"item.companyPictureList && item.companyPictureList.length > 0\"\r\n          >\r\n            <img :src=\"item.companyPictureList[0].url\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCompanyHomeList } from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 15,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    // this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getCompanyHomeList({\r\n        recommendStatus: 1,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 跳转到企业名录详情页面\r\n    goEnterpriseDetail(item) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/enterpriseDetail\",\r\n        query: { id: item.id, businessNo: item.businessNo },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goEnterprise() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/enterpriseList\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.serviceBg {\r\n  width: 100%;\r\n  height: 340px;\r\n  position: relative;\r\n  .serviceImg {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    width: calc((100% - 1200px) / 2);\r\n    height: 500px;\r\n    background-image: url(\"../../../assets/images/home/<USER>\");\r\n    background-size: 100% 100%;\r\n  }\r\n}\r\n.enterpriseTitle {\r\n  width: 100%;\r\n  font-size: 36px;\r\n  text-align: center;\r\n  margin: 10px 0 60px 0;\r\n  position: relative;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  color: #000000;\r\n  .allEnterprise {\r\n    position: absolute;\r\n    top: 8 px;\r\n    right: 0;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    line-height: 26px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n  flex-wrap: wrap;\r\n  // height: 280px;\r\n  .contentItem {\r\n    width: 220px;\r\n    height: 90px;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    background: #ffffff;\r\n    box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);\r\n    border-radius: 4px;\r\n    img {\r\n      width: 100%;\r\n      height: 90px;\r\n    }\r\n  }\r\n  .contentItem:nth-child(n + 6) {\r\n    margin-top: 25px;\r\n  }\r\n  .contentItem:hover {\r\n    transform: translateY(-10px);\r\n    transition: 1s;\r\n  }\r\n}\r\n</style>\r\n"]}]}