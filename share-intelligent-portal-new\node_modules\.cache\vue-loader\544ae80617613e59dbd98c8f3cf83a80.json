{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\interested.vue?vue&type=style&index=0&id=db7d3c04&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\supplyDemandDocking\\components\\interested.vue", "mtime": 1750311963023}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["interested.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "interested.vue", "sourceRoot": "src/views/supplyDemandDocking/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">我有意向</div>\r\n      <div style=\"height: 33px; margin-top: 21px\">I have intentions</div>\r\n    </div>\r\n    <div class=\"card-container card-content\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <div class=\"imgStyle\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../assets/device/ceshi.png\" alt=\"\" />\r\n        </div>\r\n        <div class=\"title\">{{ form.title || '' }}</div>\r\n        <div style=\"display: flex; align-items: center; margin-top: 15px\">\r\n          <div class=\"publishTimeStyle\">发布时间：{{ updateTime }}</div>\r\n          <!-- <div class=\"detailStyle\" @click=\"goDetail\">查看详情 >></div> -->\r\n        </div>\r\n      </div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <span class=\"resourceType\">资源类型：</span>\r\n          <span class=\"resourceValue\">{{ form.fieldName || '' }}</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <span class=\"resourceType\">资源名称：</span>\r\n          <span class=\"resourceValue\">{{ form.title || '' }}</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"意向描述\">\r\n              <el-input v-model=\"form.intentionContent\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\"\r\n                show-word-limit placeholder=\"\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人：\" prop=\"linkMan\">\r\n              <el-input disabled v-model=\"form.linkMan\" placeholder=\"请先维护联系人\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话：\" prop=\"linkTel\">\r\n              <el-input disabled v-model=\"form.linkTel\" placeholder=\"请先维护联系方式\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button style=\"width: 100%; height: 50px\" type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n        <div class=\"promptStyle\">温馨提示</div>\r\n        <div class=\"desc\" style=\"margin-top: 20px\">\r\n          1、我们会在最快的时间和您取得联系（工作时间周一至周五8:00-18:00）\r\n        </div>\r\n        <div class=\"desc\" style=\"margin-top: 13px\">\r\n          2、紧急问题请拨打：15512688882\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { submitIntention } from \"@/api/home\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        title: \"\",\r\n      },\r\n      updateTime: \"暂无\",\r\n      applicationAreaName: \"\",\r\n      rules: {\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n    if (this.$route.query.demandName && this.$route.query.demandName != 'null') {\r\n      this.form.title = this.$route.query.demandName\r\n    }\r\n    if (this.$route.query.intentionType) {\r\n      this.form.intentionType = parseInt(this.$route.query.intentionType)\r\n    }\r\n    if (this.$route.query.fieldName) {\r\n      this.form.fieldName = this.$route.query.fieldName\r\n    }\r\n    if (this.$route.query.intentionId) {\r\n      this.form.intentionId = this.$route.query.intentionId\r\n    }\r\n    if (this.$route.query.updateTime && this.$route.query.updateTime != 'null') {\r\n      this.updateTime = this.$route.query.updateTime\r\n    }\r\n    if (this.$route.query.applicationAreaName && this.$route.query.applicationAreaName != 'null') {\r\n      this.applicationAreaName = this.$route.query.applicationAreaName\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit() {\r\n      submitIntention(this.form).then((res) => {\r\n        if (res.code == 200) {\r\n          this.$message.success(\"提交成功\")\r\n          this.cancel()\r\n        } else {\r\n          this.$message.error(res.msg)\r\n        }\r\n      })\r\n    },\r\n    goDetail() {\r\n      // this.$router.push(\"/productOrderDetail\");\r\n    },\r\n    // 判断是否关联企业\r\n    initData() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (userInfo && userInfo != 'null') {\r\n        this.form.linkMan = userInfo.memberRealName;\r\n        this.form.linkTel = userInfo.memberPhone;\r\n        this.form.companyName = userInfo.memberCompanyName;\r\n      }\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      }\r\n    },\r\n    cancel() {\r\n      this.$router.go(-1)\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: rgb(242, 242, 242);\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n}\r\n\r\n.card-content {\r\n  display: flex;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: -70px;\r\n  padding: 60px 59px 62px 60px;\r\n\r\n  .card_left {\r\n    .imgStyle {\r\n      width: 330px;\r\n      height: 230px;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #333333;\r\n      margin-top: 23px;\r\n    }\r\n\r\n    .publishTimeStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n    }\r\n\r\n    .detailStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #21c9b8;\r\n      margin-left: auto;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .card_right {\r\n    margin-left: 40px;\r\n    width: 100%;\r\n\r\n    .resourceType {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .resourceValue {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #666666;\r\n    }\r\n\r\n    .footer-submit {\r\n      margin-top: 40px;\r\n    }\r\n\r\n    .promptStyle {\r\n      margin-top: 30px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .desc {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}