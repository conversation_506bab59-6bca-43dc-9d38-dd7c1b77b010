<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.SolutionTypeMapper">
    
    <resultMap type="SolutionType" id="SolutionTypeResult">
        <result property="solutionTypeId"    column="solution_type_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="solutionTypeName"    column="solution_type_name"    />
        <result property="category"    column="category"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSolutionTypeVo">
        select solution_type_id, parent_id, solution_type_name, category, del_flag, create_by, create_time, update_by, update_time, remark from solution_type
    </sql>

    <select id="selectSolutionTypeList" parameterType="SolutionType" resultMap="SolutionTypeResult">
        <include refid="selectSolutionTypeVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="solutionTypeName != null  and solutionTypeName != ''"> and solution_type_name like concat('%', #{solutionTypeName}, '%')</if>
            <if test="category != null  and category != ''"> and category = #{solutionTypeName}</if>
        </where>
    </select>
    
    <select id="selectSolutionTypeBySolutionTypeId" parameterType="Long" resultMap="SolutionTypeResult">
        <include refid="selectSolutionTypeVo"/>
        where solution_type_id = #{solutionTypeId}
    </select>
    <select id="selectSolutionTypeByParentId"  resultMap="SolutionTypeResult">
        <include refid="selectSolutionTypeVo" />
        where parent_id in
        <foreach collection="array" item="item" open="(" separator="," close=")">#{item}</foreach>
    </select>

    <insert id="insertSolutionType" parameterType="SolutionType" useGeneratedKeys="true" keyProperty="solutionTypeId">
        insert into solution_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="solutionTypeName != null">solution_type_name,</if>
            <if test="category != null">category,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="solutionTypeName != null">#{solutionTypeName},</if>
            <if test="category != null">#{category},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSolutionType" parameterType="SolutionType">
        update solution_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="solutionTypeName != null">solution_type_name = #{solutionTypeName},</if>
            <if test="category != null">category = #{category},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where solution_type_id = #{solutionTypeId}
    </update>

    <delete id="deleteSolutionTypeBySolutionTypeId" parameterType="Long">
        delete from solution_type where solution_type_id = #{solutionTypeId}
    </delete>

    <delete id="deleteSolutionTypeBySolutionTypeIds" parameterType="String">
        delete from solution_type where solution_type_id in 
        <foreach item="solutionTypeId" collection="array" open="(" separator="," close=")">
            #{solutionTypeId}
        </foreach>
    </delete>
</mapper>