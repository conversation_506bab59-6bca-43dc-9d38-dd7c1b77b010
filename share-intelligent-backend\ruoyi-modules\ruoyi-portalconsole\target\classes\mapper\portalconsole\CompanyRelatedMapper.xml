<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.CompanyRelatedMapper">
    
    <resultMap type="CompanyRelated" id="CompanyRelatedResult">
        <result property="companyRelatedId"    column="company_related_id"    />
        <result property="companyName"    column="company_name"    />
        <result property="companyEmail"    column="company_email"    />
        <result property="businessLicenseImageUrl"    column="business_license_image_url"    />
        <result property="logoImageUrl"    column="logo_image_url"    />
        <result property="socialUnityCreditCode"    column="social_unity_credit_code"    />
        <result property="serviceIndustry"    column="service_industry"    />
        <result property="companySize"    column="company_size"    />
        <result property="phone"    column="phone"    />
        <result property="address"    column="address"    />
        <result property="registeredCapital"    column="registered_capital"    />
        <result property="intrduction"    column="intrduction"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="companyLegal"    column="company_legal"    />
        <result property="establishTime"    column="establish_time"    />
        <result property="status"    column="status"    />
        <result property="regNumber"    column="reg_number"    />
        <result property="orgNumber"    column="org_number"    />
        <result property="companyOrgType"    column="company_org_type"    />
        <result property="historyNameList"    column="history_name_list"    />
        <result property="actualCapital"    column="actual_capital"    />
        <result property="viewCount"    column="view_count"    />
        <result property="label"    column="label"    />
    </resultMap>

    <sql id="selectCompanyRelatedVo">
        select company_related_id, company_name, company_email, business_license_image_url, logo_image_url, social_unity_credit_code, view_count, label,
               service_industry, company_size, phone, address, registered_capital, intrduction, create_by, create_time, update_by, update_time, remark,
               del_flag, company_legal, establish_time, status, reg_number, org_number, company_org_type, history_name_list, actual_capital from company_related
    </sql>

    <select id="selectCompanyRelatedList" parameterType="CompanyRelated" resultMap="CompanyRelatedResult">
        <include refid="selectCompanyRelatedVo"/>
        <where>  
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="companyEmail != null  and companyEmail != ''"> and company_email = #{companyEmail}</if>
            <if test="businessLicenseImageUrl != null  and businessLicenseImageUrl != ''"> and business_license_image_url = #{businessLicenseImageUrl}</if>
            <if test="socialUnityCreditCode != null  and socialUnityCreditCode != ''"> and social_unity_credit_code like concat('%', #{socialUnityCreditCode}, '%')</if>
            <if test="serviceIndustry != null  and serviceIndustry != ''"> and service_industry = #{serviceIndustry}</if>
            <if test="companySize != null  and companySize != ''"> and company_size = #{companySize}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="registeredCapital != null  and registeredCapital != ''"> and registered_capital = #{registeredCapital}</if>
            <if test="intrduction != null  and intrduction != ''"> and intrduction = #{intrduction}</if>
            <if test="companyLegal != null  and companyLegal != ''"> and company_legal = #{companyLegal}</if>
            <if test="establishTime != null  and establishTime != ''"> and establish_time = #{establishTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="regNumber != null  and regNumber != ''"> and reg_number = #{regNumber}</if>
            <if test="orgNumber != null  and orgNumber != ''"> and org_number like concat('%', #{orgNumber}, '%')</if>
            <if test="companyOrgType != null  and companyOrgType != ''"> and company_org_type = #{companyOrgType}</if>
            <if test="historyNameList != null  and historyNameList != ''"> and history_name_list = #{historyNameList}</if>
            <if test="actualCapital != null  and actualCapital != ''"> and actual_capital = #{actualCapital}</if>
            <if test="viewCount != null  and viewCount != ''"> and view_count = #{viewCount}</if>
            <if test="label != null  and label != ''"> and label like concat('%', #{label}, '%')</if>
        </where>
    </select>
    
    <select id="selectCompanyRelatedByCompanyRelatedId" parameterType="Long" resultMap="CompanyRelatedResult">
        <include refid="selectCompanyRelatedVo"/>
        where company_related_id = #{companyRelatedId}
    </select>
        
    <insert id="insertCompanyRelated" parameterType="CompanyRelated" useGeneratedKeys="true" keyProperty="companyRelatedId">
        insert into company_related
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null">company_name,</if>
            <if test="companyEmail != null">company_email,</if>
            <if test="businessLicenseImageUrl != null">business_license_image_url,</if>
            <if test="logoImageUrl != null">logo_image_url,</if>
            <if test="socialUnityCreditCode != null">social_unity_credit_code,</if>
            <if test="serviceIndustry != null">service_industry,</if>
            <if test="companySize != null">company_size,</if>
            <if test="phone != null">phone,</if>
            <if test="address != null">address,</if>
            <if test="registeredCapital != null">registered_capital,</if>
            <if test="intrduction != null">intrduction,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="companyLegal != null">company_legal,</if>
            <if test="establishTime != null">establish_time,</if>
            <if test="status != null">status,</if>
            <if test="regNumber != null">reg_number,</if>
            <if test="orgNumber != null">org_number,</if>
            <if test="companyOrgType != null">company_org_type,</if>
            <if test="historyNameList != null">history_name_list,</if>
            <if test="actualCapital != null">actual_capital,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="label != null">label,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null">#{companyName},</if>
            <if test="companyEmail != null">#{companyEmail},</if>
            <if test="businessLicenseImageUrl != null">#{businessLicenseImageUrl},</if>
            <if test="logoImageUrl != null">#{logoImageUrl},</if>
            <if test="socialUnityCreditCode != null">#{socialUnityCreditCode},</if>
            <if test="serviceIndustry != null">#{serviceIndustry},</if>
            <if test="companySize != null">#{companySize},</if>
            <if test="phone != null">#{phone},</if>
            <if test="address != null">#{address},</if>
            <if test="registeredCapital != null">#{registeredCapital},</if>
            <if test="intrduction != null">#{intrduction},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="companyLegal != null">#{companyLegal},</if>
            <if test="establishTime != null">#{establishTime},</if>
            <if test="status != null">#{status},</if>
            <if test="regNumber != null">#{regNumber},</if>
            <if test="orgNumber != null">#{orgNumber},</if>
            <if test="companyOrgType != null">#{companyOrgType},</if>
            <if test="historyNameList != null">#{historyNameList},</if>
            <if test="actualCapital != null">#{actualCapital},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="label != null">#{label},</if>
         </trim>
    </insert>
    <insert id="batchImportSlFormulasInfo">
        INSERT INTO company_related (company_name, company_email, business_license_image_url, logo_image_url, social_unity_credit_code, label,
                                     service_industry, company_size, phone, address, registered_capital, intrduction, create_by, create_time, update_by, update_time, remark,
                                     del_flag, company_legal, establish_time, status, reg_number, org_number, company_org_type, history_name_list, actual_capital,view_count)
        VALUES
            <foreach collection="list"  separator=","  item="data">
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    <choose>
                        <when test="data.companyName != null">#{data.companyName},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.companyEmail != null">#{data.companyEmail},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.businessLicenseImageUrl != null">#{data.businessLicenseImageUrl},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.logoImageUrl != null">#{data.logoImageUrl},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.socialUnityCreditCode != null">#{data.socialUnityCreditCode},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.label != null">#{data.label},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.serviceIndustry != null">#{data.serviceIndustry},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.companySize != null">#{data.companySize},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.phone != null">#{data.phone},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.address != null">#{data.address},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.registeredCapital != null">#{data.registeredCapital},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.intrduction != null">#{data.intrduction},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.createBy != null">#{data.createBy},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.createTime != null">#{data.createTime},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.updateBy != null">#{data.updateBy},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.updateTime != null">#{data.updateTime},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.remark != null">#{data.remark},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.delFlag != null">#{data.delFlag},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.companyLegal != null">#{data.companyLegal},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.establishTime != null">#{data.establishTime},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.regNumber != null">#{data.regNumber},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.orgNumber != null">#{data.orgNumber},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.status != null">#{data.status},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.companyOrgType != null">#{data.companyOrgType},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.historyNameList != null">#{data.historyNameList},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.actualCapital != null">#{data.actualCapital},</when>
                        <otherwise>null,</otherwise>
                    </choose>
                    <choose>
                        <when test="data.viewCount != null">#{data.viewCount},</when>
                        <otherwise>0,</otherwise>
                    </choose>
                </trim>
            </foreach>
    </insert>

    <update id="updateCompanyRelated" parameterType="CompanyRelated">
        update company_related
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="companyEmail != null">company_email = #{companyEmail},</if>
            <if test="businessLicenseImageUrl != null">business_license_image_url = #{businessLicenseImageUrl},</if>
            <if test="logoImageUrl != null">logo_image_url = #{logoImageUrl},</if>
            <if test="socialUnityCreditCode != null">social_unity_credit_code = #{socialUnityCreditCode},</if>
            <if test="serviceIndustry != null">service_industry = #{serviceIndustry},</if>
            <if test="companySize != null">company_size = #{companySize},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="address != null">address = #{address},</if>
            <if test="registeredCapital != null">registered_capital = #{registeredCapital},</if>
            <if test="intrduction != null">intrduction = #{intrduction},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="companyLegal != null">company_legal = #{companyLegal},</if>
            <if test="establishTime != null">establish_time = #{establishTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="regNumber != null">reg_number = #{regNumber},</if>
            <if test="orgNumber != null">org_number = #{orgNumber},</if>
            <if test="companyOrgType != null">company_org_type = #{companyOrgType},</if>
            <if test="historyNameList != null">history_name_list = #{historyNameList},</if>
            <if test="actualCapital != null">actual_capital = #{actualCapital},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="label != null">label = #{label},</if>
        </trim>
        where company_related_id = #{companyRelatedId}
    </update>

    <delete id="deleteCompanyRelatedByCompanyRelatedId" parameterType="Long">
        delete from company_related where company_related_id = #{companyRelatedId}
    </delete>

    <delete id="deleteCompanyRelatedByCompanyRelatedIds" parameterType="String">
        delete from company_related where company_related_id in 
        <foreach item="companyRelatedId" collection="array" open="(" separator="," close=")">
            #{companyRelatedId}
        </foreach>
    </delete>
</mapper>