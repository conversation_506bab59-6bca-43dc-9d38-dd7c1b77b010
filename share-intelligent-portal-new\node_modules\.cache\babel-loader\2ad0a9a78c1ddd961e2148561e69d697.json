{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\index.vue", "mtime": 1750311963019}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnB1c2guanMiKTsKdmFyIF9yZWdlbmVyYXRvcjIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkU6L2NvbXBhbnkvbm1kL25tZG5ldy9zaGFyZS1pbnRlbGxpZ2VudC9zaGFyZS1pbnRlbGxpZ2VudC1wb3J0YWwtbmV3L25vZGVfbW9kdWxlcy9AYmFiZWwvcnVudGltZS9oZWxwZXJzL3JlZ2VuZXJhdG9yLmpzIikpOwp2YXIgX2FzeW5jVG9HZW5lcmF0b3IyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9hc3luY1RvR2VuZXJhdG9yLmpzIikpOwp2YXIgX3NvbHV0aW9uID0gcmVxdWlyZSgiQC9hcGkvc29sdXRpb24iKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJkZW1hbmRIYWxsIiwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcGFyYW1zOiB7CiAgICAgICAgcGFyZW50SWQ6ICIiLAogICAgICAgIHNlYXJjaFN0cjogIiIsCiAgICAgICAgc29sdXRpb25UeXBlSWQ6ICIiLAogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDEwLAogICAgICAgIGNhdGVnb3J5OiAxCiAgICAgIH0sCiAgICAgIHRvdGFsOiAwLAogICAgICB0b3RhbDE6IDAsCiAgICAgIGtleXdvcmRzOiAiIiwKICAgICAgZm9ybToge30sCiAgICAgIGZsYWc6ICLlhajpg6giLAogICAgICBhcHBsaVR5cGVEYXRhOiBbewogICAgICAgIGRpY3RWYWx1ZTogIjAiLAogICAgICAgIGRpY3RMYWJlbDogIuWFqOmDqCIKICAgICAgfSwgewogICAgICAgIGRpY3RMYWJlbDogIuWIm+aWs<PERSON>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"}, {"version": 3, "names": ["_solution", "require", "name", "data", "params", "parentId", "searchStr", "solutionTypeId", "pageNum", "pageSize", "category", "total", "total1", "keywords", "form", "flag", "appliTypeData", "dict<PERSON><PERSON>ue", "dict<PERSON><PERSON>l", "appliTypeImgList", "url", "demandList", "title", "appliArea", "requireType", "desc", "publishTime", "aaa", "bbb", "typeList", "id", "typeName", "typeNestList", "dataList", "created", "getTypeNext", "methods", "getDemandList", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "res", "w", "_context", "n", "getSolutionList", "v", "code", "rows", "console", "log", "a", "searchHot", "val", "onSearch", "getappliData", "value", "_this2", "_callee2", "_context2", "getSolutionTypeList", "changeSolve", "changeSolveB", "handleSizeChange", "handleCurrentChange", "goDetail", "$router", "push"], "sources": ["src/views/solution/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">解决方案</div>\r\n      <div style=\"height: 33px; margin-top: 1px\"></div>\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\">\r\n            <el-form-item>\r\n              <el-input v-model=\"params.searchStr\" placeholder=\"请输入搜索内容\" class=\"activity-search-input\">\r\n                <el-button slot=\"append\" class=\"activity-search-btn\" @click=\"onSearch\">搜索</el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"hot_search\">\r\n        <span>热门搜索：</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('供应链管理')\">供应链管理</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('设备智慧物联')\">设备智慧物联</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('生产过程管控')\">生产过程管控</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('科技成果转化')\">科技成果转化</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('企业运营管理')\">企业运营管理</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('产业转型升级')\">产业转型升级</span>\r\n        <span class=\"hot_search_item\" @click=\"searchHot('产融服务')\">产融服务</span>\r\n      </div>\r\n    </div>\r\n    <!-- 底部内容 -->\r\n    <div class=\"content_bottom\">\r\n      <div class=\"icondiv\">\r\n        <div class=\"solutioniconFlex\">\r\n          <div v-for=\"(item, index) in typeList\" :key=\"item.id\"\r\n            :class=\"['iconFlexTitle', aaa == item.id ? 'activeTitle' : '']\" @click=\"changeSolve(item.id)\">\r\n            {{ item.typeName }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"demandContent\" style=\"padding-top: 40px\">\r\n        <div class=\"demandflex\" style=\"height: 715px\">\r\n          <div class=\"leftsolution\">\r\n            <div :class=\"['leftTitle', bbb == 1 ? 'leftTitleHover' : '']\" @click=\"changeSolveB(1)\">\r\n              全部（{{ total1 }}）\r\n            </div>\r\n            <div v-for=\"(item, index) in typeNestList\" :key=\"index\" :class=\"[\r\n              'leftTitle',\r\n              bbb == item.solutionTypeId ? 'leftTitleHover' : '',\r\n            ]\" @click=\"changeSolveB(item.solutionTypeId)\">\r\n              <span class=\"tr2\">{{ item.solutionTypeName }}（{{ item.totalCount }}）</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"rightSolution\" v-if=\"dataList && dataList.length > 0\">\r\n            <div v-for=\"(item, index) in dataList\" :key=\"index\" class=\"solutionContent tr2\">\r\n              <div @click=\"goDetail(item.solutionId)\">\r\n                <div class=\"solutionContentTitle tr2\">\r\n                  {{ item.solutionName }}\r\n                </div>\r\n                <div class=\"solutionContentValue tr2 textOverflow\">\r\n                  {{ item.solutionIntroduction }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"rightEmpty\" v-else>\r\n            <el-empty description=\"暂无数据\"></el-empty>\r\n          </div>\r\n        </div>\r\n        <!-- 分页 -->\r\n        <div class=\"pageStyle\">\r\n          <el-pagination v-if=\"dataList && dataList.length > 0\" background layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\" :page-size=\"params.pageSize\" :current-page=\"params.pageNum\" :total=\"total\"\r\n            @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { getSolutionList, getSolutionTypeList } from \"@/api/solution\";\r\nexport default {\r\n  name: \"demandHall\",\r\n  data() {\r\n    return {\r\n      params: {\r\n        parentId: \"\",\r\n        searchStr: \"\",\r\n        solutionTypeId: \"\",\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        category: 1,\r\n      },\r\n      total: 0,\r\n      total1: 0,\r\n      keywords: \"\",\r\n      form: {},\r\n      flag: \"全部\",\r\n      appliTypeData: [\r\n        {\r\n          dictValue: \"0\",\r\n          dictLabel: \"全部\",\r\n        },\r\n        {\r\n          dictLabel: \"创新研发\",\r\n          dictValue: \"1\",\r\n        },\r\n        {\r\n          dictLabel: \"物料采购\",\r\n          dictValue: \"2\",\r\n        },\r\n        {\r\n          dictLabel: \"智能制造\",\r\n          dictValue: \"3\",\r\n        },\r\n        {\r\n          dictLabel: \"数字化管理\",\r\n          dictValue: \"4\",\r\n        },\r\n        {\r\n          dictLabel: \"软件服务\",\r\n          dictValue: \"5\",\r\n        },\r\n        {\r\n          dictLabel: \"供应链金融\",\r\n          dictValue: \"6\",\r\n        },\r\n        {\r\n          dictLabel: \"运营宣传\",\r\n          dictValue: \"7\",\r\n        },\r\n        {\r\n          dictLabel: \"其他\",\r\n          dictValue: \"8\",\r\n        },\r\n      ],\r\n      appliTypeImgList: [\r\n        {\r\n          url: require(\"@/assets/appliMarket/type1.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type2.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type3.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type4.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type5.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type6.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type7.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type8.png\"),\r\n        },\r\n        {\r\n          url: require(\"@/assets/appliMarket/type9.png\"),\r\n        },\r\n      ],\r\n      demandList: [\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n        {\r\n          title: \"需要采购KBOFLEX伺服编码器电缆\",\r\n          url: require(\"@/assets/demand/xqimgdefault.png\"),\r\n          appliArea: \"\",\r\n          requireType: \"其它\",\r\n          desc: \"需要采购KBOFLEX品牌的伺服编码器电缆，规格为4*0.75，材质是标柔线，属于高柔性耐弯曲线缆\",\r\n          publishTime: \"2025-02-28 09:49:21\",\r\n        },\r\n      ],\r\n      aaa: \"1\",\r\n      bbb: 1,\r\n      typeList: [\r\n        {\r\n          id: \"1\",\r\n          typeName: \"行业解决方案\",\r\n        },\r\n        {\r\n          id: \"2\",\r\n          typeName: \"领域解决方案\",\r\n        },\r\n      ],\r\n      typeNestList: [],\r\n      dataList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getTypeNext('1');\r\n  },\r\n  methods: {\r\n    async getDemandList() {\r\n      this.params.category = this.aaa;\r\n      let res = await getSolutionList(this.params);\r\n      if (res.code == 200) {\r\n        this.dataList = res.rows;\r\n        this.total = res.total;\r\n        console.log(this.total, \"total\");\r\n        if (this.params.solutionTypeId == \"\") {\r\n          this.total1 = res.total;\r\n        }\r\n      }\r\n    },\r\n    searchHot(val) {\r\n      this.params.searchStr = val;\r\n      this.onSearch();\r\n    },\r\n    onSearch() {\r\n      this.params.pageNum = 1;\r\n      this.getDemandList();\r\n    },\r\n    getappliData(value) {\r\n      this.flag = value;\r\n      this.getDemandList();\r\n    },\r\n    async getTypeNext(val) {\r\n      let res = await getSolutionTypeList({ category: val });\r\n      if (res.code == 200) {\r\n        this.typeNestList = res.rows;\r\n        this.getDemandList();\r\n      }\r\n    },\r\n    changeSolve(val) {\r\n      this.aaa = val;\r\n      this.params.parentId = val;\r\n      this.params.solutionTypeId = \"\";\r\n      this.bbb = 1;\r\n      this.params.pageSize = 10;\r\n      this.params.pageNum = 1;\r\n      this.getTypeNext(val);\r\n    },\r\n    changeSolveB(val) {\r\n      this.bbb = val;\r\n      this.params.pageSize = 10;\r\n      this.params.pageNum = 1;\r\n      if (val == 1) {\r\n        this.params.solutionTypeId = \"\";\r\n      } else {\r\n        this.params.solutionTypeId = val;\r\n      }\r\n      this.getDemandList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.params.pageSize = pageSize;\r\n      this.getDemandList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.params.pageNum = pageNum;\r\n      this.getDemandList();\r\n    },\r\n\r\n    goDetail(id) {\r\n      this.$router.push(\"/solutionDetail?id=\" + id);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n\r\n  .hot_search {\r\n    font-size: 14px;\r\n    color: #000;\r\n\r\n    .hot_search_item {\r\n      margin-right: 20px;\r\n      color: #000;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  .icondiv {\r\n    background-color: rgba(255, 255, 255, 1);\r\n    width: 100%;\r\n    height: 100px;\r\n    position: relative;\r\n\r\n    .solutioniconFlex {\r\n      display: flex;\r\n      position: absolute;\r\n      bottom: 0;\r\n      width: 1200px;\r\n      right: 0;\r\n      left: 0;\r\n      margin: auto;\r\n      justify-content: center;\r\n\r\n      .iconFlexTitle {\r\n        width: 110px;\r\n        height: 45px;\r\n        line-height: 26px;\r\n        border-radius: 2px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 18px;\r\n        text-align: center;\r\n        margin: 0 20px;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .activeTitle {\r\n        color: #0cad9d;\r\n        border-bottom: 2px solid #0cad9d;\r\n      }\r\n    }\r\n  }\r\n\r\n  .demandContent {\r\n    width: 100%;\r\n    background: #f7f8fa;\r\n    // background: #fff;\r\n    padding-top: 20px;\r\n    box-shadow: #21c9b8 solid 1px;\r\n    // border: #21c9b8 solid 1px;\r\n\r\n    .demandflex {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      flex-wrap: wrap;\r\n\r\n      .leftsolution {\r\n        width: 185px;\r\n        height: 715px;\r\n        line-height: 20px;\r\n        opacity: 0.95;\r\n        border-radius: 4px;\r\n        background: linear-gradient(180deg,\r\n            rgba(244, 246, 249, 1) 0%,\r\n            rgba(255, 255, 255, 1) 100%);\r\n        color: rgba(16, 16, 16, 1);\r\n        font-size: 14px;\r\n        box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);\r\n        border: 2px solid rgba(255, 255, 255, 1);\r\n        padding: 20px 0;\r\n        box-sizing: border-box;\r\n        overflow-y: auto;\r\n\r\n        .leftTitle {\r\n          color: rgba(51, 51, 51, 1);\r\n          font-size: 16px;\r\n          margin: 30px 0;\r\n          padding-left: 20px;\r\n          border-left: 3px solid transparent;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .leftTitleHover {\r\n          color: #0cad9d;\r\n          border-left: 3px solid #0cad9d;\r\n        }\r\n      }\r\n\r\n      .rightSolution {\r\n        width: 1000px;\r\n        display: flex;\r\n        justify-content: space-between;\r\n        flex-wrap: wrap;\r\n        align-content: flex-start;\r\n\r\n        .solutionContent {\r\n          width: 490px;\r\n          height: 124px;\r\n          border: 2px solid transparent;\r\n          padding: 20px;\r\n          box-sizing: border-box;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .solutionContent:hover {\r\n          opacity: 0.95;\r\n          border-radius: 4px;\r\n          background: linear-gradient(180deg,\r\n              rgba(244, 246, 249, 1) 0%,\r\n              rgba(255, 255, 255, 1) 100%);\r\n          color: rgba(16, 16, 16, 1);\r\n          font-size: 14px;\r\n          box-shadow: 0px 0px 8px 0px rgba(218, 235, 253, 75);\r\n          border: 2px solid rgba(255, 255, 255, 1);\r\n        }\r\n\r\n        .solutionContentTitle {\r\n          color: rgba(51, 51, 51, 1);\r\n          font-size: 18px;\r\n          margin-bottom: 10px;\r\n        }\r\n\r\n        .solutionContent:hover .solutionContentTitle {\r\n          color: #0cad9d;\r\n        }\r\n\r\n        .solutionContentValue {\r\n          color: rgba(102, 102, 102, 1);\r\n          font-size: 12px;\r\n          line-height: 1.5;\r\n        }\r\n      }\r\n\r\n      .rightEmpty {\r\n        width: 1000px;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.activity-title-content {\r\n  width: 100%;\r\n\r\n  // background-color: #fff;\r\n  .activity-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .activity-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .activity-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .activity-search-box {\r\n    margin-top: 40px;\r\n\r\n    .activity-search-form {\r\n      text-align: center;\r\n\r\n      .activity-search-input {\r\n        width: 792px;\r\n        height: 54px;\r\n\r\n        .activity-search-btn {\r\n          width: 100px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.content_bottom {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n\r\n  .content_bottom_item {\r\n    margin-top: 20px;\r\n    width: 590px;\r\n    height: 208px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 4px 18px 2px #e8f1fa;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    cursor: pointer;\r\n\r\n    .detailTitle {\r\n      height: 30px;\r\n      color: rgba(51, 51, 51, 1);\r\n      font-size: 18px;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .textOverflow1 {\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      display: -webkit-box;\r\n      -webkit-line-clamp: 1;\r\n      -webkit-box-orient: vertical;\r\n    }\r\n\r\n    .demandChunk {\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .demand_right {\r\n        width: 413px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n      }\r\n\r\n      .demandTopRightflex {\r\n        display: flex;\r\n        line-height: 24px;\r\n      }\r\n\r\n      .detailrightTitle {\r\n        color: rgba(153, 153, 153, 1);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightTitle2 {\r\n        color: rgba(0, 0, 0, 0.85);\r\n        font-size: 14px;\r\n      }\r\n\r\n      .detailrightContent {\r\n        width: 343px;\r\n        color: rgba(51, 51, 51, 1);\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .content_bottom_item:hover {\r\n    box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);\r\n    scale: 1.01;\r\n  }\r\n\r\n  .content_bottom_item:nth-child(2n) {\r\n    margin-left: 20px;\r\n  }\r\n}\r\n\r\n.pageStyle {\r\n  margin-top: 60px;\r\n  width: 100%;\r\n  text-align: center;\r\n  margin-bottom: 30px;\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.activity-search-input {\r\n  .el-input__inner {\r\n    height: 54px;\r\n    background: #fff;\r\n    border-radius: 27px 0 0 27px;\r\n    border: 1px solid #d9d9d9;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    line-height: 24px;\r\n    padding-left: 30px;\r\n  }\r\n\r\n  .el-input-group__append {\r\n    border-radius: 0px 100px 100px 0px;\r\n    background: #21c9b8;\r\n    font-size: 16px;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    color: #fff;\r\n    line-height: 24px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AA8EA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;QACAC,QAAA;QACAC,SAAA;QACAC,cAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;MACA;MACAC,KAAA;MACAC,MAAA;MACAC,QAAA;MACAC,IAAA;MACAC,IAAA;MACAC,aAAA,GACA;QACAC,SAAA;QACAC,SAAA;MACA,GACA;QACAA,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,GACA;QACAC,SAAA;QACAD,SAAA;MACA,EACA;MACAE,gBAAA,GACA;QACAC,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,GACA;QACAmB,GAAA,EAAAnB,OAAA;MACA,EACA;MACAoB,UAAA,GACA;QACAC,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,GACA;QACAJ,KAAA;QACAF,GAAA,EAAAnB,OAAA;QACAsB,SAAA;QACAC,WAAA;QACAC,IAAA;QACAC,WAAA;MACA,EACA;MACAC,GAAA;MACAC,GAAA;MACAC,QAAA,GACA;QACAC,EAAA;QACAC,QAAA;MACA,GACA;QACAD,EAAA;QACAC,QAAA;MACA,EACA;MACAC,YAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAT,KAAA,CAAAlC,MAAA,CAAAM,QAAA,GAAA4B,KAAA,CAAAX,GAAA;cAAAmB,QAAA,CAAAC,CAAA;cAAA,OACA,IAAAC,yBAAA,EAAAV,KAAA,CAAAlC,MAAA;YAAA;cAAAwC,GAAA,GAAAE,QAAA,CAAAG,CAAA;cACA,IAAAL,GAAA,CAAAM,IAAA;gBACAZ,KAAA,CAAAL,QAAA,GAAAW,GAAA,CAAAO,IAAA;gBACAb,KAAA,CAAA3B,KAAA,GAAAiC,GAAA,CAAAjC,KAAA;gBACAyC,OAAA,CAAAC,GAAA,CAAAf,KAAA,CAAA3B,KAAA;gBACA,IAAA2B,KAAA,CAAAlC,MAAA,CAAAG,cAAA;kBACA+B,KAAA,CAAA1B,MAAA,GAAAgC,GAAA,CAAAjC,KAAA;gBACA;cACA;YAAA;cAAA,OAAAmC,QAAA,CAAAQ,CAAA;UAAA;QAAA,GAAAX,OAAA;MAAA;IACA;IACAY,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAApD,MAAA,CAAAE,SAAA,GAAAkD,GAAA;MACA,KAAAC,QAAA;IACA;IACAA,QAAA,WAAAA,SAAA;MACA,KAAArD,MAAA,CAAAI,OAAA;MACA,KAAA6B,aAAA;IACA;IACAqB,YAAA,WAAAA,aAAAC,KAAA;MACA,KAAA5C,IAAA,GAAA4C,KAAA;MACA,KAAAtB,aAAA;IACA;IACAF,WAAA,WAAAA,YAAAqB,GAAA;MAAA,IAAAI,MAAA;MAAA,WAAArB,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAmB,SAAA;QAAA,IAAAjB,GAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAiB,SAAA;UAAA,kBAAAA,SAAA,CAAAf,CAAA;YAAA;cAAAe,SAAA,CAAAf,CAAA;cAAA,OACA,IAAAgB,6BAAA;gBAAArD,QAAA,EAAA8C;cAAA;YAAA;cAAAZ,GAAA,GAAAkB,SAAA,CAAAb,CAAA;cACA,IAAAL,GAAA,CAAAM,IAAA;gBACAU,MAAA,CAAA5B,YAAA,GAAAY,GAAA,CAAAO,IAAA;gBACAS,MAAA,CAAAvB,aAAA;cACA;YAAA;cAAA,OAAAyB,SAAA,CAAAR,CAAA;UAAA;QAAA,GAAAO,QAAA;MAAA;IACA;IACAG,WAAA,WAAAA,YAAAR,GAAA;MACA,KAAA7B,GAAA,GAAA6B,GAAA;MACA,KAAApD,MAAA,CAAAC,QAAA,GAAAmD,GAAA;MACA,KAAApD,MAAA,CAAAG,cAAA;MACA,KAAAqB,GAAA;MACA,KAAAxB,MAAA,CAAAK,QAAA;MACA,KAAAL,MAAA,CAAAI,OAAA;MACA,KAAA2B,WAAA,CAAAqB,GAAA;IACA;IACAS,YAAA,WAAAA,aAAAT,GAAA;MACA,KAAA5B,GAAA,GAAA4B,GAAA;MACA,KAAApD,MAAA,CAAAK,QAAA;MACA,KAAAL,MAAA,CAAAI,OAAA;MACA,IAAAgD,GAAA;QACA,KAAApD,MAAA,CAAAG,cAAA;MACA;QACA,KAAAH,MAAA,CAAAG,cAAA,GAAAiD,GAAA;MACA;MACA,KAAAnB,aAAA;IACA;IACA6B,gBAAA,WAAAA,iBAAAzD,QAAA;MACA,KAAAL,MAAA,CAAAK,QAAA,GAAAA,QAAA;MACA,KAAA4B,aAAA;IACA;IACA8B,mBAAA,WAAAA,oBAAA3D,OAAA;MACA,KAAAJ,MAAA,CAAAI,OAAA,GAAAA,OAAA;MACA,KAAA6B,aAAA;IACA;IAEA+B,QAAA,WAAAA,SAAAtC,EAAA;MACA,KAAAuC,OAAA,CAAAC,IAAA,yBAAAxC,EAAA;IACA;EACA;AACA", "ignoreList": []}]}