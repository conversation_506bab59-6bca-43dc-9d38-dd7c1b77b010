<template>
  <div class="app-container">
    <el-table v-loading="loading" :data="data">
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="手机号" align="center" prop="fromUserId" />
      <el-table-column label="聊天内容" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.objectName == 'RC:TxtMsg'">
            {{ scope.row.message }}
          </div>
          <div v-if="scope.row.objectName == 'RC:ImgMsg'">
            <img :src="scope.row.message" width="80" height="80" />
          </div>
          <div v-if="
                          scope.row.objectName == 'RC:HQVCMsg' ||
                          scope.row.objectName == 'RC:VcMsg'
                      ">
                      <audio controls>
                        <source :src="scope.row.message" type="audio/ogg">
                      </audio>
          </div>
          <div v-if="scope.row.objectName == 'RC:SightMsg'">
            <video :src="scope.row.message" controls width="300" height="100"></video>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="发言时间" align="center" prop="create_time" />
    </el-table>
  </div>
</template>

<script>
// import {
//     getRoom
// } from "@/api/sso/room";

export default {
    props: {
      form: Object
    },
    name: "Room",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            data: [],
            queryParams: {
                page: 1,
                size: 10
            },
        };
    },
    created() {
        this.getList();
    },
    methods: {
        getList() {
            // getRoom({
            //     roomId: this.form.platform=='uuc'?"C_demand_"+this.form.id:"C_require_"+this.form.id,
            //     page: this.queryParams.page,
            //     size: this.queryParams.size,
            // }).then((res) => {
            //     this.data = res.data.rows;
            //     this.total = res.data.total;
            //     this.loading = false;
            // });
        },
    },
};
</script>


<style>
</style>
