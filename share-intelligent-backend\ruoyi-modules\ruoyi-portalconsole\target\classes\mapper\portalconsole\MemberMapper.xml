<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.MemberMapper">
    
    <resultMap type="Member" id="MemberResult">
        <result property="memberId"    column="member_id"    />
        <result property="memberPhone"    column="member_phone"    />
        <result property="memberPassword"    column="member_password"    />
        <result property="lastLoginTime"    column="last_login_time"    />
        <result property="memberRealName"    column="member_real_name"    />
        <result property="memberWechat"    column="member_wechat"    />
        <result property="solutionTypeId"    column="solution_type_id"    />
        <result property="memberPost"    column="member_post"    />
        <result property="memberCompanyName"    column="member_company_name"    />
        <result property="memberCompanyArea"    column="member_company_area"    />
        <result property="memberCompanyAddr"    column="member_company_addr"    />
        <result property="memberStatus"    column="member_status"    />
        <result property="memberCompanyId"    column="member_company_id"    />
        <result property="companyRelatedId"    column="company_related_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="isAdmin" column="is_admin"/>
    </resultMap>

    <resultMap id="MemberTelephoneResult" type="MemberTelephoneVO">
        <result property="memberId"    column="member_id"    />
        <result property="memberPhone"    column="member_phone"    />
    </resultMap>

    <sql id="selectMemberVo">
        select member_id, member_phone, member_password, last_login_time, member_real_name, member_wechat, solution_type_id, member_post, member_company_name, member_company_area,
               member_company_addr, member_status, member_company_id, company_related_id, del_flag, create_by, create_time, update_by, update_time, remark, is_admin from member
    </sql>

    <select id="selectMemberList" parameterType="Member" resultMap="MemberResult">
        <include refid="selectMemberVo"/>
        <where>  
            <if test="memberPhone != null  and memberPhone != ''"> and member_phone = #{memberPhone}</if>
            <if test="memberPassword != null  and memberPassword != ''"> and member_password = #{memberPassword}</if>
            <if test="lastLoginTime != null "> and last_login_time = #{lastLoginTime}</if>
            <if test="memberRealName != null  and memberRealName != ''"> and member_real_name like concat('%', #{memberRealName}, '%')</if>
            <if test="memberWechat != null  and memberWechat != ''"> and member_wechat = #{memberWechat}</if>
            <if test="solutionTypeId != null "> and solution_type_id = #{solutionTypeId}</if>
            <if test="memberPost != null  and memberPost != ''"> and member_post = #{memberPost}</if>
            <if test="memberCompanyName != null  and memberCompanyName != ''"> and member_company_name like concat('%', #{memberCompanyName}, '%')</if>
            <if test="memberCompanyArea != null  and memberCompanyArea != ''"> and member_company_area = #{memberCompanyArea}</if>
            <if test="memberCompanyAddr != null  and memberCompanyAddr != ''"> and member_company_addr = #{memberCompanyAddr}</if>
            <if test="memberStatus != null  and memberStatus != ''"> and member_status = #{memberStatus}</if>
            <if test="memberCompanyId != null "> and member_company_id = #{memberCompanyId}</if>
            <if test="isAdmin != null and isAdmin != '' "> and is_admin = #{isAdmin}</if>
        </where>
    </select>
    
    <select id="selectMemberByMemberId" parameterType="Long" resultMap="MemberResult">
        <include refid="selectMemberVo"/>
        where member_id = #{memberId}
    </select>
    <select id="selectMemberIdList" resultMap="MemberTelephoneResult"
            resultType="com.ruoyi.portalconsole.domain.vo.MemberTelephoneVO">
        SELECT member_id, member_phone from member LEFT JOIN solution_type ON member.solution_type_id = solution_type.solution_type_id
        <where>
            member_status = '0'
            <if test="array != null">
            and  solution_type_name IN
                <foreach collection="array" open="(" close=")" separator="," item="typeName">
                    #{typeName}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectMembersByCompanyIds" parameterType="java.util.List" resultMap="MemberTelephoneResult">
        select member_id, member_phone from company join member on member.member_company_id = company.company_id
        <where>
            company_id IN
            <foreach collection="list" open="(" close=")" separator="," item="companyId">
                #{companyId}
            </foreach>
            and is_admin = 'Y'

        </where>
    </select>
    <insert id="insertMember" parameterType="Member" useGeneratedKeys="true" keyProperty="memberId">
        insert into member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="memberPhone != null">member_phone,</if>
            <if test="memberPassword != null">member_password,</if>
            <if test="lastLoginTime != null">last_login_time,</if>
            <if test="memberRealName != null">member_real_name,</if>
            <if test="memberWechat != null">member_wechat,</if>
            <if test="solutionTypeId != null">solution_type_id,</if>
            <if test="memberPost != null">member_post,</if>
            <if test="memberCompanyName != null">member_company_name,</if>
            <if test="memberCompanyArea != null">member_company_area,</if>
            <if test="memberCompanyAddr != null">member_company_addr,</if>
            <if test="memberStatus != null">member_status,</if>
            <if test="memberCompanyId != null">member_company_id,</if>
            <if test="companyRelatedId != null">company_related_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="isAdmin != null">is_admin,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="memberPhone != null">#{memberPhone},</if>
            <if test="memberPassword != null">#{memberPassword},</if>
            <if test="lastLoginTime != null">#{lastLoginTime},</if>
            <if test="memberRealName != null">#{memberRealName},</if>
            <if test="memberWechat != null">#{memberWechat},</if>
            <if test="solutionTypeId != null">#{solutionTypeId},</if>
            <if test="memberPost != null">#{memberPost},</if>
            <if test="memberCompanyName != null">#{memberCompanyName},</if>
            <if test="memberCompanyArea != null">#{memberCompanyArea},</if>
            <if test="memberCompanyAddr != null">#{memberCompanyAddr},</if>
            <if test="memberStatus != null">#{memberStatus},</if>
            <if test="memberCompanyId != null">#{memberCompanyId},</if>
            <if test="companyRelatedId != null">#{companyRelatedId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isAdmin != null">#{isAdmin},</if>
         </trim>
    </insert>

    <update id="updateMember" parameterType="Member">
        update member
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberPhone != null">member_phone = #{memberPhone},</if>
            <if test="memberPassword != null">member_password = #{memberPassword},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            <if test="memberRealName != null">member_real_name = #{memberRealName},</if>
            <if test="memberWechat != null">member_wechat = #{memberWechat},</if>
            <if test="solutionTypeId != null">solution_type_id = #{solutionTypeId},</if>
            <if test="memberPost != null">member_post = #{memberPost},</if>
            <if test="memberCompanyName != null">member_company_name = #{memberCompanyName},</if>
            <if test="memberCompanyArea != null">member_company_area = #{memberCompanyArea},</if>
            <if test="memberCompanyAddr != null">member_company_addr = #{memberCompanyAddr},</if>
            <if test="memberStatus != null">member_status = #{memberStatus},</if>
            <if test="memberCompanyId != null">member_company_id = #{memberCompanyId},</if>
            <if test="companyRelatedId != null">company_related_id = #{companyRelatedId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isAdmin != null">is_admin = #{isAdmin},</if>
        </trim>
        where member_id = #{memberId}
    </update>

    <delete id="deleteMemberByMemberId" parameterType="Long">
        delete from member where member_id = #{memberId}
    </delete>

    <delete id="deleteMemberByMemberIds" parameterType="String">
        delete from member where member_id in 
        <foreach item="memberId" collection="array" open="(" separator="," close=")">
            #{memberId}
        </foreach>
    </delete>
</mapper>