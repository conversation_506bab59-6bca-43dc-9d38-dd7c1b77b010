{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\dashboard\\PieChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\dashboard\\PieChart.vue", "mtime": 1750311962947}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PieChart.vue"], "names": [], "mappings": ";;;;;AAKA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PieChart.vue", "sourceRoot": "src/views/dashboard", "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '300px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n\r\n      this.chart.setOption({\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b} : {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          left: 'center',\r\n          bottom: '10',\r\n          data: ['Industries', 'Technology', 'Forex', 'Gold', 'Forecasts']\r\n        },\r\n        series: [\r\n          {\r\n            name: 'WEEKLY WRITE ARTICLES',\r\n            type: 'pie',\r\n            roseType: 'radius',\r\n            radius: [15, 95],\r\n            center: ['50%', '38%'],\r\n            data: [\r\n              { value: 320, name: 'Industries' },\r\n              { value: 240, name: 'Technology' },\r\n              { value: 149, name: 'Forex' },\r\n              { value: 100, name: 'Gold' },\r\n              { value: 59, name: 'Forecasts' }\r\n            ],\r\n            animationEasing: 'cubicInOut',\r\n            animationDuration: 2600\r\n          }\r\n        ]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}