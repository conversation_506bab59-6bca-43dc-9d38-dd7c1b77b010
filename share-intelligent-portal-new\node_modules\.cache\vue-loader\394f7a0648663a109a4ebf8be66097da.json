{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\detail.vue", "mtime": 1750311963017}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/solution", "sourcesContent": ["<template>\r\n  <div class=\"notice-detail-container\">\r\n    <div class=\"soDetailBanner\" :style=\"{ 'background-image': `url(${form.solutionBanner})` }\">\r\n      <div class=\"bannerSolutionFlex\" style=\"height: 100%\">\r\n        <div>\r\n          <p class=\"solutionTitle\" style=\"color:#333;\">{{ form.solutionName }}</p>\r\n          <p class=\"solutionEng\" style=\"color:#333;\">{{ form.solutionIntroduction }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"schemeBox\" v-if=\"form.solutionOverview\">\r\n      <div class=\"serveContent\">\r\n        <div class=\"newsHeader\">\r\n          <div class=\"titleAll\">方案概述</div>\r\n        </div>\r\n        <div class=\"serveBottom\">\r\n          <div class=\"sketchLeft\">\r\n            <p style=\"display: inline-block;position: absolute; width: 505px;height: 297px;background: #21c9b8;\"></p>\r\n            <!-- <img src=\"@/assets/solution/sketchBg.png\" class=\"sketchBg\"> -->\r\n            <img width=\"506\" v-if='form.solutionImg' height=\"296\" :src=\"form.solutionImg\" class=\"sketch\">\r\n            <img width=\"506\" v-else height=\"296\" src=\"@/assets/solution/default.jpg\" class=\"sketch\">\r\n          </div>\r\n          <div class=\"sketchRight\">\r\n            <div style=\"text-align: end;height: 58px;\">\r\n              <img src=\"@/assets/solution/comma.png\">\r\n            </div>\r\n            {{ form.solutionOverview }}\r\n            <div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"painSpotBox\" v-if=\"painList != null && painList.length != 0\">\r\n      <div class=\"serveContent\">\r\n        <div class=\"newsHeader\">\r\n          <div class=\"titleAll\">行业痛点</div>\r\n        </div>\r\n        <div class=\"painContent\">\r\n          <div v-for=\"(item, index) in painList\" :key=\"index\" class=\"painDiv\">\r\n            <!-- <div><img :src=\"`../images/icon/fa0${index + 1}.svg`\" style=\"width: 40px\"></div> -->\r\n            <div class=\"painDivTitle\">{{ item.solutionPainName }}</div>\r\n            <div class=\"textOverflow3\">{{ item.solutionPainContent }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"advantageBox\" v-if=\"advantageList != null && advantageList.length != 0\">\r\n      <div class=\"serveContent\">\r\n        <div class=\"newsHeader\">\r\n          <div class=\"titleAll\">方案优势</div>\r\n        </div>\r\n        <div>\r\n          <el-carousel ref=\"carousel\" :autoplay=\"false\" indicator-position=\"none\" @change=\"changeadvantageListHover\">\r\n            <el-carousel-item v-for=\"(item, index) in advantageList\" :key=\"index\">\r\n              <div class=\"serveBottom\">\r\n                <div class=\"advantageLeft\">\r\n                  <div class=\"advantageTitleBox\">\r\n                    <img src=\"@/assets/solution/advantageIcon.svg\">\r\n                    <div class=\"advantageTitle\">{{ item.solutionAdvantageName }}</div>\r\n                  </div>\r\n                  <div class=\"advsubtitle\">{{ item.solutionAdvantageType }}</div>\r\n                  <div>{{ item.solutionAdvantageContent }}</div>\r\n                </div>\r\n                <div style=\"width: 552px;height: 358px\">\r\n                  <img :src=\"item.solutionAdvantageImage ? item.solutionAdvantageImage : gjImgdefault\"\r\n                    style=\"width: 100%;height: 100%\">\r\n                </div>\r\n              </div>\r\n            </el-carousel-item>\r\n          </el-carousel>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"caseBoxdetail\" v-if=\"caseList != null && caseList.length != 0\">\r\n      <div class=\"serveContent\">\r\n        <div class=\"newsHeader\">\r\n          <div class=\"titleAll\">实施案例</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"casecontent\" v-if=\"caseList.length > 0\">\r\n        <div class=\"serveContent serveBottom\">\r\n          <div class=\"caseLeft\">\r\n            <div v-for=\"(item, index) in caseList\" :key=\"index\"\r\n              :class=\"['caseLeftbtn', caseIndex == index ? 'caseLeftbtnhover' : '']\" @click=\"changeCaseIndex(index)\"\r\n              style=\"display: flex;align-items: center;\">\r\n              <img style=\"margin-left: 10px;width: 17px\" src=\"@/assets/solution/caseicon.png\">\r\n              <span style=\"margin-left: 20px;\" class=\"textOverflow1\">{{ item.solutionCaseName }}</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"caseRight\">\r\n            <div class=\"caseRightTitle\">{{ caseList[caseIndex].solutionCaseName }}</div>\r\n            <div class=\"caseRightContent\">{{ caseList[caseIndex].solutionCaseContent }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { solutionDetail, solutionDicts } from \"@/api/solution\";\r\nimport \"@/assets/styles/index.css\";\r\n\r\nexport default {\r\n  name: \"policyPage\",\r\n  data() {\r\n    return {\r\n      showLogin: false,\r\n      userinfo: [],\r\n      token: '',\r\n      productList: [],\r\n      cmsList: [],\r\n      cmsData: [],\r\n      demandList: [],\r\n      input: '',\r\n      requireList: [],\r\n      queryIndex: 0,\r\n      total: 0,\r\n      pageSize: 10,\r\n      pageNum: 1,\r\n      titleContent: '',\r\n      requirementTypeCodeArray: [],\r\n      form: {},\r\n      imageUrl: '',\r\n      aaa: '1',\r\n      dataList: [],\r\n      painList: [],\r\n      advantageList: [],\r\n      advantageListHover: 0,\r\n      benefitList: [],\r\n      caseList: [],\r\n      caseIndex: 0,\r\n      gjImgdefault: '@/assets/solution/gjimgdefault.png'\r\n    };\r\n  },\r\n  created() {\r\n    if (this.$route.query.id) {\r\n      this.id = this.$route.query.id\r\n      this.getDemandList()\r\n    }\r\n  },\r\n  methods: {\r\n    async getDemandList() {\r\n      let res = await solutionDetail({ solutionId: this.id });\r\n      if (res.code == 200) {\r\n        this.form = res.data;\r\n        this.painList = res.data.alSolutionPainVOs;\r\n        this.caseList = res.data.alSolutionCaseVOs;\r\n        this.advantageList = res.data.alSolutionAdvantageVOs;\r\n      }\r\n    },\r\n    async getDicts() {\r\n      let res = await solutionDicts();\r\n      if (res.code == 200) {\r\n        this.requireList = res.data;\r\n      }\r\n    },\r\n    currentChange(val) {\r\n      this.pageNum = val\r\n      this.getDemandList()\r\n    },\r\n    changeSolve(val) {\r\n      this.aaa = val\r\n    },\r\n    changeadvantageListHover(val) {\r\n      this.advantageListHover = val\r\n    },\r\n    lastStep() {\r\n      this.$refs.carousel.setActiveItem(this.advantageListHover - 1)\r\n    },\r\n    nextStep() {\r\n      this.$refs.carousel.setActiveItem(this.advantageListHover + 1)\r\n    },\r\n    changeCaseIndex(index) {\r\n      this.caseIndex = index\r\n    },\r\n    getUrlKey: function (name) {\r\n      return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(\r\n        location.href) || [, \"\"])[\r\n        1].replace(/\\+/g, '%20')) || null;\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.notice-detail-container {\r\n  width: 100%;\r\n  padding: 0 0 100px;\r\n  background: #f4f5f9;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.notice-detail-container {\r\n  .notice-info-content {\r\n    word-break: break-all;\r\n    font-size: 16px;\r\n    line-height: 28px;\r\n    color: #333;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n\r\n    img {\r\n      max-width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.swiper-pagination-bullet {\r\n  background: #fff;\r\n}\r\n\r\n.swiper-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.swiper-container {\r\n  width: 100%;\r\n}\r\n\r\n.swiper-container2 {\r\n  width: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.soDetailBanner {\r\n  background-repeat: no-repeat;\r\n  background-size: 100%;\r\n}\r\n</style>\r\n"]}]}