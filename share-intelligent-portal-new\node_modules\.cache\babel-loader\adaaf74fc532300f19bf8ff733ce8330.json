{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\workshopDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\workshopDetail.vue", "mtime": 1750311962970}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_manufacturingSharing", "require", "name", "data", "detailsData", "imgList", "id", "created", "$route", "query", "getWorkData", "methods", "_this", "workDetailData", "then", "res", "code", "jumpIntention", "_this2", "userInfo", "JSON", "parse", "sessionStorage", "getItem", "memberCompanyName", "$confirm", "confirmButtonText", "cancelButtonText", "type", "cancelButtonClass", "confirmButtonClass", "$router", "push", "catch", "concat", "updateTime"], "sources": ["src/views/manufacturingSharing/components/workshopDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"card-container cardStyle\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <!-- 上半部分 -->\r\n        <div class=\"card_left_top\">\r\n          <div class=\"imgStyle\">\r\n            <img style=\"width: 100%; height: 100%\" :src=\"detailsData.images\r\n              ? detailsData.images.split(',')[0]\r\n              : require('@/assets/device/ceshi.png')\r\n              \" alt=\"\" />\r\n          </div>\r\n          <!-- <div class=\"imgContent\">\r\n            <div style=\"cursor: pointer\">\r\n              <img src=\"../../../assets/device/icon_left.png\" alt=\"\" />\r\n            </div>\r\n            <div style=\"display: flex; align-items: center; margin: 0 10px\">\r\n              <div\r\n                class=\"everyImgStyle\"\r\n                v-for=\"(item, index) in imgList\"\r\n                :key=\"index\"\r\n              >\r\n                <img\r\n                  style=\"width: 100%; height: 100%\"\r\n                  src=\"../../../assets/device/ceshi.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n            </div>\r\n            <div style=\"cursor: pointer\">\r\n              <img src=\"../../../assets/device/icon_right.png\" alt=\"\" />\r\n            </div>\r\n          </div> -->\r\n        </div>\r\n        <!-- 下半部分 -->\r\n        <div class=\"card_left_bottom\">\r\n          <div class=\"title\">{{ detailsData.name }}</div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">所属单位：</div>\r\n            <div class=\"optionValue\">{{ detailsData.company }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">车间地址：</div>\r\n            <div class=\"optionValue\">{{ detailsData.address }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">车间面积：</div>\r\n            <div class=\"optionValue\">{{ detailsData.area || \"--\" }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">参考价格：</div>\r\n            <div class=\"optionValue\" style=\"color: #cc0a0a\">\r\n              {{ detailsData.price || \"--\" }}\r\n            </div>\r\n          </div>\r\n          <div class=\"buttonStyle\" @click=\"jumpIntention\">申请试用</div>\r\n        </div>\r\n      </div>\r\n      <!-- 中间 -->\r\n      <div class=\"card_center_line\"></div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">车间概况</div>\r\n          </div>\r\n          <div class=\"content_desc\">\r\n            {{ detailsData.description }}\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">设备资源</div>\r\n          </div>\r\n          <div class=\"content_desc\">\r\n            {{ detailsData.resources }}\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">生产能力</div>\r\n          </div>\r\n          <div class=\"content_desc\">\r\n            {{ detailsData.capability }}\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">注意事项</div>\r\n          </div>\r\n          <div class=\"content_desc\">\r\n            {{ detailsData.notes }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { workDetailData } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"deviceDetail\",\r\n  data() {\r\n    return {\r\n      detailsData: {},\r\n      imgList: [\r\n        {\r\n          id: 1,\r\n        },\r\n        {\r\n          id: 2,\r\n        },\r\n        {\r\n          id: 3,\r\n        },\r\n        {\r\n          id: 4,\r\n        },\r\n        {\r\n          id: 5,\r\n        },\r\n      ],\r\n      id: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getWorkData();\r\n  },\r\n  methods: {\r\n    getWorkData() {\r\n      workDetailData(this.id).then((res) => {\r\n        if (res.code === 200) {\r\n          this.detailsData = res.data;\r\n        }\r\n      });\r\n    },\r\n    jumpIntention() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(`/demandInterested?demandName=${this.detailsData.name}&updateTime=${this.detailsData.updateTime}&intentionType=4&fieldName=车间共享&intentionId=${this.detailsData.id}`);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: #f2f2f2;\r\n  padding: 30px 0 61px 0;\r\n}\r\n\r\n.cardStyle {\r\n  height: 660px;\r\n  background-color: #ffffff;\r\n  padding: 60px 56px 54px 50px;\r\n  display: flex;\r\n}\r\n\r\n.card_left {\r\n  .card_left_top {\r\n    .imgStyle {\r\n      width: 330px;\r\n      height: 230px;\r\n      border-radius: 2px;\r\n      margin-left: 10px;\r\n    }\r\n\r\n    .imgContent {\r\n      margin-top: 15px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .everyImgStyle {\r\n        width: 54px;\r\n        height: 50px;\r\n        margin-left: 10px;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .everyImgStyle:nth-child(1) {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .card_left_bottom {\r\n    margin-top: 30px;\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      font-size: 20px;\r\n      color: #222222;\r\n      margin-bottom: 13px;\r\n    }\r\n\r\n    .everyOption {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 12px;\r\n\r\n      .optionName {\r\n        min-height: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #999999;\r\n        min-width: 75px;\r\n      }\r\n\r\n      .optionValue {\r\n        min-height: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #333333;\r\n      }\r\n    }\r\n\r\n    .buttonStyle {\r\n      margin-top: 32px;\r\n      margin-left: 55px;\r\n      width: 220px;\r\n      height: 50px;\r\n      background: #21c9b8;\r\n      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);\r\n      border-radius: 2px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      text-align: center;\r\n      line-height: 50px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.card_center_line {\r\n  width: 1px;\r\n  height: 100%;\r\n  background: #e1e1e1;\r\n  margin-left: 51px;\r\n  margin-right: 61px;\r\n}\r\n\r\n.card_right {\r\n  width: 100%;\r\n  overflow-y: auto;\r\n\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n\r\n  .content_desc {\r\n    // width: 631px;\r\n    // height: 159px;\r\n    margin-top: 20px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #666666;\r\n    line-height: 24px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAwGA,IAAAA,qBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,OAAA,GACA;QACAC,EAAA;MACA,GACA;QACAA,EAAA;MACA,GACA;QACAA,EAAA;MACA,GACA;QACAA,EAAA;MACA,GACA;QACAA,EAAA;MACA,EACA;MACAA,EAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,EAAA,QAAAE,MAAA,CAAAC,KAAA,CAAAH,EAAA;IACA,KAAAI,WAAA;EACA;EACAC,OAAA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,oCAAA,OAAAP,EAAA,EAAAQ,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAR,WAAA,GAAAW,GAAA,CAAAZ,IAAA;QACA;MACA;IACA;IACAc,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,cAAA,CAAAC,OAAA;MACA,MAAAJ,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAK,iBAAA;QACA,KAAAC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;UACAC,iBAAA;UACAC,kBAAA;QACA,GACAhB,IAAA;UACAI,MAAA,CAAAa,OAAA,CAAAC,IAAA;QACA,GACAC,KAAA;QACA;MACA;QACA,KAAAF,OAAA,CAAAC,IAAA,iCAAAE,MAAA,MAAA9B,WAAA,CAAAF,IAAA,kBAAAgC,MAAA,MAAA9B,WAAA,CAAA+B,UAAA,sEAAAD,MAAA,MAAA9B,WAAA,CAAAE,EAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}