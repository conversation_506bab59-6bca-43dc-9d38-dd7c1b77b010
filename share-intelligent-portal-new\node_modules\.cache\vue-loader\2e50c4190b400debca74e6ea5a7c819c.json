{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\reApplication\\index.vue?vue&type=style&index=0&id=4d24585c&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\reApplication\\index.vue", "mtime": 1750311963081}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCByZ2IoMjI1LCAyNDcsIDI0MCksIHJnYigyNDQsIDI1MiwgMjUwKSk7DQp9DQoNCi5jb250ZW50X2NhcmQgew0KICBiYWNrZ3JvdW5kOiAjZmZmZmZmOw0KICBib3JkZXItcmFkaXVzOiAycHg7DQogIC8vIG1hcmdpbi10b3A6IDMwcHg7DQogIHBhZGRpbmc6IDU5cHggNjBweCA1N3B4IDYwcHg7DQp9DQoNCi50aXRsZSB7DQogIGZvbnQtZmFtaWx5OiBTb3VyY2UgSGFuIFNhbnMgQ047DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQogIGZvbnQtc2l6ZTogMThweDsNCiAgY29sb3I6ICMyMWM5Yjg7DQp9DQoNCi50aXRsZUxpbmUgew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiAxcHg7DQogIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogIG1hcmdpbjogMjBweCAwIDMwcHggMDsNCn0NCg0KLmFkZFN0eWxlIHsNCiAgZm9udC1mYW1pbHk6IFNvdXJjZSBIYW4gU2FucyBDTjsNCiAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBjb2xvcjogIzIxYzliODsNCiAgbWFyZ2luLWxlZnQ6IGF1dG87DQogIGN1cnNvcjogcG9pbnRlcjsNCn0NCg0KLmZvb3Rlci1zdWJtaXQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgbWFyZ2luLXRvcDogNjBweDsNCg0KICAuZWwtYnV0dG9uIHsNCiAgICB3aWR0aDogMTQwcHg7DQogICAgaGVpZ2h0OiA1MHB4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoOA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/reApplication", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"content_card\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <div class=\"title\">基本信息</div>\r\n            <div class=\"titleLine\"></div>\r\n            <el-row :gutter=\"20\">\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"企业名称\" prop=\"companyName\">\r\n                  <el-autocomplete v-model=\"form.companyName\" placeholder=\"请输入您公司的完整名称\" style=\"width: 100%;\"\r\n                    :fetch-suggestions=\"querySearchTianYanCha\" @select=\"selectAutoDataTianYanCha\"></el-autocomplete>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"社会信用代码\">\r\n                  <el-input v-model=\"form.socialCreditCode\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"注册资本\">\r\n                  <el-input v-model=\"form.registeredCapital\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"联系电话\">\r\n                  <el-input v-model=\"form.contactPhone\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"行业\">\r\n                  <el-input v-model=\"form.industry\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"地址\">\r\n                  <el-input v-model=\"form.companyAddress\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-form-item prop=\"technologyType\">\r\n              <div slot=\"label\">经营范围</div>\r\n              <el-input v-model=\"form.businessScope\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\"\r\n                show-word-limit placeholder=\"请输入\" />\r\n            </el-form-item>\r\n            <div class=\"title\">企业能力</div>\r\n            <div class=\"titleLine\"></div>\r\n            <el-form-item label=\"\">\r\n              <div slot=\"label\">\r\n                <div style=\"display: flex; width: 1080px\">\r\n                  <div>人员能力</div>\r\n                  <div class=\"addStyle\" @click=\"addPersonnelList\">新增行</div>\r\n                </div>\r\n              </div>\r\n              <el-table :data=\"form.personnelList\">\r\n                <el-table-column label=\"技术人员姓名\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.technicianName\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"专业技术工种\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.technicalType\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </el-form-item>\r\n            <el-form-item label=\"技术实力\">\r\n              <el-input v-model=\"form.technicalCapability\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"\">\r\n              <div slot=\"label\">\r\n                <div style=\"display: flex; width: 1080px\">\r\n                  <div>\r\n                    资质证件\r\n                    <span style=\"color: #999999; font-size: 14px; margin-left: 11px\">（专利、商标、资质、证书等）</span>\r\n                  </div>\r\n                  <div class=\"addStyle\" @click=\"addQualificationList\">\r\n                    新增行\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <el-table :data=\"form.qualificationList\">\r\n                <el-table-column label=\"资质名称\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.qualificationName\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"附件\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <ImageUpload :limit=\"1\" v-model=\"scope.row.attachment\" />\r\n                    <!-- <el-input v-model=\"scope.row.jobId\"></el-input> -->\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </el-form-item>\r\n            <el-form-item label=\"\">\r\n              <div slot=\"label\">\r\n                <div style=\"display: flex; width: 1080px\">\r\n                  <div>设备信息</div>\r\n                  <div class=\"addStyle\" @click=\"addEquipmentList\">新增行</div>\r\n                </div>\r\n              </div>\r\n              <el-table :data=\"form.equipmentList\">\r\n                <el-table-column label=\"生产设备\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.equipmentName\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"规格型号\" align=\"center\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-input v-model=\"scope.row.specification\"></el-input>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n              <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { searchCompany, getCompanyCodeByName } from \"@/api/system/company\";\r\nimport { enteringFactoryAdd } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      form: {\r\n        companyName: \"\", // 企业名称\r\n        socialCreditCode: \"\", // 社会信用代码\r\n        registeredCapital: \"\", // 注册资本\r\n        contactPhone: \"\", // 联系电话\r\n        industry: \"\", // 行业\r\n        companyAddress: \"\", // 地址\r\n        businessScope: \"\", // 经营范围\r\n        personnelList: [], // 人员能力\r\n        qualificationList: [], // 资质证件\r\n        equipmentList: [], // 设备信息列表\r\n        settledStatus: \"0\", // 默认传待审核\r\n      },\r\n      rules: {\r\n        companyName: [\r\n          { required: true, message: \"请输入企业名称\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() { },\r\n  methods: {\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          enteringFactoryAdd(this.form).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功\");\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    onCancel() { },\r\n    addPersonnelList() {\r\n      this.form.personnelList.push({\r\n        technicianName: \"\",\r\n        technicalType: \"\",\r\n      });\r\n    },\r\n    addQualificationList() {\r\n      this.form.qualificationList.push({\r\n        qualificationName: \"\",\r\n        attachment: \"\",\r\n      });\r\n    },\r\n    addEquipmentList() {\r\n      this.form.equipmentList.push({\r\n        equipmentName: \"\",\r\n        specification: \"\",\r\n      });\r\n    },\r\n    // 企业名称\r\n    querySearchTianYanCha(queryString, cb) {\r\n      if (queryString) {\r\n        searchCompany({ keywords: queryString }).then(res => {\r\n          let data = res.rows;\r\n          let List = [];\r\n          data.forEach(function (val, index) {\r\n            List.push({\r\n              id: index,\r\n              value: val\r\n            })\r\n          })\r\n          if (data.length > 0) {\r\n            cb(List);\r\n          } else {\r\n            cb([{\r\n              id: '',\r\n              value: '暂无数据'\r\n            }]);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 企业名称选择\r\n    selectAutoDataTianYanCha(row) {\r\n      getCompanyCodeByName({ keywords: row.value }).then(res => {\r\n        if (res.code == 200) {\r\n          let data = res.data;\r\n          this.$set(this.form, 'socialCreditCode', data.taxNo)\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n}\r\n\r\n.content_card {\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  // margin-top: 30px;\r\n  padding: 59px 60px 57px 60px;\r\n}\r\n\r\n.title {\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 18px;\r\n  color: #21c9b8;\r\n}\r\n\r\n.titleLine {\r\n  width: 100%;\r\n  height: 1px;\r\n  background: #21c9b8;\r\n  margin: 20px 0 30px 0;\r\n}\r\n\r\n.addStyle {\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  color: #21c9b8;\r\n  margin-left: auto;\r\n  cursor: pointer;\r\n}\r\n\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"]}]}