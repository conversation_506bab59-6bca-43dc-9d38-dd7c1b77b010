{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\noninductive\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\noninductive\\index.vue", "mtime": 1750311963065}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsNCiAgbGlzdE5vbmluZHVjdGl2ZSwNCiAgcmV2b2NhdGlvbk5vbmluZHVjdGl2ZSwNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL25vbmluZHVjdGl2ZSI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJOb25pbmR1Y3RpdmUiLA0KICBjb21wb25lbnRzOiB7IFVzZXJNZW51IH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHN0YXR1czogIiIsDQogICAgICByZWNvcmRzOiBbXSwNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiA2LA0KICAgICAgfSwNCiAgICAgIHRvdGFsOiAxLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBjaGFuZ2VUeXBlKHJlcykgew0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBnZXRMaXN0KCkgew0KICAgICAgbGlzdE5vbmluZHVjdGl2ZSh7IC4uLnRoaXMucXVlcnlQYXJhbXMsIHN0YXR1czogdGhpcy5zdGF0dXMgfSkudGhlbigNCiAgICAgICAgKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgdGhpcy5yZWNvcmRzID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIH0NCiAgICAgICk7DQogICAgfSwNCiAgICBkb1Jldm9jYXRpb24oaXRlbSkgew0KICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm56Gu6K6k5pKk5Zue6K+l5o+Q5oql77yfIiwgeyB0eXBlOiAiZXJyb3IiIH0pDQogICAgICAgIC50aGVuKChfKSA9PiB7DQogICAgICAgICAgcmV2b2NhdGlvbk5vbmluZHVjdGl2ZShpdGVtLmlkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgICAgICAgIG1lc3NhZ2U6ICLmk43kvZzmiJDlip8iLA0KICAgICAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKF8pID0+IHt9KTsNCiAgICB9LA0KICAgIGdvRGV0YWlsKGlkKSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL3VzZXIvbm9uaW5kdWN0aXZlRGV0YWlsP2lkPSIgKyBpZCk7DQogICAgfSwNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKHBhZ2VOdW0pIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IHBhZ2VOdW07DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/noninductive", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:38:32\r\n * @LastEditTime: 2023-03-24 09:19:21\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-03 11:20:59\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"noninductive-record-page\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"18\" :xs=\"24\">\r\n          <div>\r\n            <el-radio-group v-model=\"status\" @change=\"changeType\">\r\n              <el-radio-button label=\"\">全部</el-radio-button>\r\n              <el-radio-button label=\"1\">审核中</el-radio-button>\r\n              <el-radio-button label=\"2\">审核通过</el-radio-button>\r\n              <el-radio-button label=\"3\">审核驳回</el-radio-button>\r\n              <el-radio-button label=\"0\">草稿箱</el-radio-button>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"noninductive-pannel\">\r\n            <div class=\"none-class\" v-if=\"!records || records.length == 0\">\r\n              <el-image\r\n                style=\"width: 160px; height: 160px\"\r\n                :src=\"require('@/assets/user/none.png')\"\r\n                :fit=\"fit\"\r\n              ></el-image>\r\n              <div class=\"text\">暂无数据</div>\r\n            </div>\r\n            <div\r\n              class=\"noninductive-item\"\r\n              v-for=\"item in records\"\r\n              v-bind:key=\"item.id\"\r\n            >\r\n              <a class=\"left\" @click=\"goDetail(item.id)\">\r\n                <div class=\"noninductive-title\">\r\n                  {{\r\n                    item.itemTypeFirstName || \"\" + item.itemTypeSecondName || \"\"\r\n                  }}\r\n                </div>\r\n                <div class=\"item-info\">\r\n                  <div class=\"company-name\">{{ item.companyName }}</div>\r\n                  <div class=\"company-name ml_150\">\r\n                    提交时间：{{ item.updateTime }}\r\n                  </div>\r\n                </div>\r\n              </a>\r\n              <el-image\r\n                class=\"status-icon\"\r\n                v-if=\"item.status != '0'\"\r\n                style=\"width: 16px; height: 16px\"\r\n                :src=\"\r\n                  require('@/assets/user/noninductive_status_' +\r\n                    item.status +\r\n                    '.png')\r\n                \"\r\n              ></el-image>\r\n\r\n              <div class=\"noninductive-status\">{{ item.statusName }}</div>\r\n              <a\r\n                @click=\"doRevocation(item)\"\r\n                class=\"revocation-button\"\r\n                v-if=\"item.status === '1'\"\r\n              >\r\n                <div>撤回</div>\r\n              </a>\r\n            </div>\r\n          </div>\r\n          <el-pagination\r\n            v-show=\"total > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            :page-size=\"6\"\r\n            :current-page.sync=\"queryParams.pageNum\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :total=\"total\"\r\n          >\r\n          </el-pagination>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport {\r\n  listNoninductive,\r\n  revocationNoninductive,\r\n} from \"@/api/system/noninductive\";\r\nexport default {\r\n  name: \"Noninductive\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      status: \"\",\r\n      records: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 6,\r\n      },\r\n      total: 1,\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    changeType(res) {\r\n      this.getList();\r\n    },\r\n    getList() {\r\n      listNoninductive({ ...this.queryParams, status: this.status }).then(\r\n        (response) => {\r\n          this.records = response.rows;\r\n          this.total = response.total;\r\n        }\r\n      );\r\n    },\r\n    doRevocation(item) {\r\n      this.$confirm(\"是否确认撤回该提报？\", { type: \"error\" })\r\n        .then((_) => {\r\n          revocationNoninductive(item.id).then((response) => {\r\n            this.$message({\r\n              message: \"操作成功\",\r\n              type: \"success\",\r\n            });\r\n            this.getList();\r\n          });\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push(\"/user/noninductiveDetail?id=\" + id);\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .noninductive-record-page {\r\n    .noninductive-pannel {\r\n      margin-top: 24px;\r\n      width: 100%;\r\n      height: 600px;\r\n      background: #fff;\r\n      .none-class {\r\n        text-align: center;\r\n        padding: 10% 0;\r\n        .text {\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #999999;\r\n          line-height: 14px;\r\n        }\r\n      }\r\n      .noninductive-item {\r\n        display: flex;\r\n        padding: 0 20px;\r\n        height: 100px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        .left {\r\n          width: 73%;\r\n          .noninductive-title {\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            width: 100%;\r\n            line-height: 56px;\r\n            overflow: hidden;\r\n            -webkit-line-clamp: 1;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n          }\r\n          .item-info {\r\n            display: flex;\r\n            width: 800px;\r\n            justify-content: space-between;\r\n            .company-name {\r\n              font-size: 14px;\r\n              font-weight: 400;\r\n              color: #666666;\r\n              line-height: 20px;\r\n            }\r\n            .ml_150 {\r\n              margin-left: 150px;\r\n            }\r\n          }\r\n        }\r\n        .status-icon {\r\n          margin: auto 10px;\r\n          height: 100px;\r\n        }\r\n        .noninductive-status {\r\n          line-height: 100px;\r\n          font-size: 15px;\r\n          width: 180px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          overflow: hidden;\r\n          -webkit-line-clamp: 1;\r\n          text-overflow: ellipsis;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n        }\r\n        .revocation-button {\r\n          width: 50px;\r\n          height: 26px;\r\n          border-radius: 4px;\r\n          border: 1px solid #21c9b8;\r\n          font-size: 12px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          font-weight: 400;\r\n          color: #21c9b8;\r\n          line-height: 26px;\r\n          text-align: center;\r\n          margin: auto;\r\n        }\r\n      }\r\n    }\r\n    .el-radio-button {\r\n      margin-right: 30px;\r\n    }\r\n    .el-radio-button__inner {\r\n      width: 96px;\r\n      height: 32px;\r\n      background: transparent;\r\n      border-radius: 20px;\r\n      text-align: center;\r\n      color: #333333;\r\n      border: none;\r\n    }\r\n    .el-radio-button__orig-radio:checked + .el-radio-button__inner {\r\n      background: #21c9b8 !important;\r\n      color: #fff;\r\n      box-shadow: none;\r\n    }\r\n    .el-radio-button__inner:hover {\r\n      color: #333333;\r\n    }\r\n\r\n    .el-pagination {\r\n      width: 100%;\r\n      margin-top: 20px;\r\n      text-align: center;\r\n    }\r\n    .el-pagination.is-background .el-pager li {\r\n      background-color: #fff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled).active {\r\n      background-color: #21c9b8;\r\n      color: #ffffff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled):hover {\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}