{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\policyDeclare\\detail\\index.vue?vue&type=style&index=0&id=40f84b2e&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\policyDeclare\\detail\\index.vue", "mtime": 1750311963071}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4PA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/policyDeclare/detail", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-17 16:19:40\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"notice-record-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">申报详情</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                基本信息\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 政策名称 </template>\r\n                  {{ info.policyName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系人 </template>\r\n                  {{ info.contractPerson }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系电话</template>\r\n                  {{ info.contractPhone }}\r\n                </el-descriptions-item>\r\n\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 资料上传 </template>\r\n                  <a\r\n                    class=\"file-class\"\r\n                    v-for=\"item in info.fileList\"\r\n                    v-bind:key=\"item.url\"\r\n                    @click=\"handleFilePreview(item.url)\"\r\n                  >\r\n                    <el-image\r\n                      style=\"width: 14px; height: 17px\"\r\n                      :src=\"require('@/assets/user/file_pdf.png')\"\r\n                    ></el-image>\r\n                    {{ item.name }}\r\n                    <div class=\"previwe-class\">立即查看</div>\r\n                  </a>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <el-image\r\n                class=\"status_approving\"\r\n                v-if=\"info.status == '2'\"\r\n                style=\"width: 120px; height: 102px\"\r\n                :src=\"require('@/assets/user/status_approving.png')\"\r\n              ></el-image>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"goBack\">返回</el-button>\r\n                <el-button\r\n                  v-if=\"info.status == '1'\"\r\n                  type=\"danger\"\r\n                  @click=\"changeMode\"\r\n                  >编辑</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n            <div class=\"edit-page\" v-else>\r\n              <el-form\r\n                ref=\"form\"\r\n                :model=\"form\"\r\n                :rules=\"rules\"\r\n                label-width=\"120px\"\r\n              >\r\n                <el-form-item label=\"政策名称\" prop=\"policyName\">\r\n                  <el-input\r\n                    v-model=\"form.policyName\"\r\n                    disabled\r\n                    placeholder=\"政策名称\"\r\n                  />\r\n                </el-form-item>\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"联系人\" prop=\"contractPerson\">\r\n                      <el-input\r\n                        v-model=\"form.contractPerson\"\r\n                        placeholder=\"请输入联系人\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"联系电话\" prop=\"contractPhone\">\r\n                      <el-input\r\n                        v-model=\"form.contractPhone\"\r\n                        placeholder=\"请选择联系电话\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"上传文件\" prop=\"fileList\">\r\n                      <el-upload\r\n                        :headers=\"headers\"\r\n                        :action=\"actionUrl\"\r\n                        accept=\".pdf, .doc, .xls\"\r\n                        :file-list=\"form.fileList\"\r\n                        :on-remove=\"handleApplicationRemove\"\r\n                        :on-success=\"handleApplicationSuccess\"\r\n                        :limit=\"10\"\r\n                      >\r\n                        <div>\r\n                          <el-button\r\n                            size=\"small\"\r\n                            type=\"primary\"\r\n                            icon=\"el-icon-upload2\"\r\n                            >上传文件</el-button\r\n                          >\r\n                          <span class=\"tip\">仅限doc、pdf、xls格式</span>\r\n                        </div>\r\n                      </el-upload>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"changeMode\">返回</el-button>\r\n                <el-button type=\"error\" @click=\"changeMode(1)\"\r\n                  >暂存草稿</el-button\r\n                >\r\n                <el-button type=\"danger\" @click=\"submitForm(2)\"\r\n                  >提交审核</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport { getPolicyDetail, editPolicyApply } from \"@/api/system/policy\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\"affiliated_unit\", \"capital_source\", \"affiliated_street\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      info: {},\r\n      form: {},\r\n      accountLicenceList: [],\r\n      // 表单校验\r\n      rules: {\r\n        policyName: [\r\n          { required: true, message: \"所属单位不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractPerson: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contractPhone: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n        fileList: [\r\n          { required: true, message: \"文件不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    getDetail() {\r\n      let userId = this.$route.query.id;\r\n      getPolicyDetail(userId).then((response) => {\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n\r\n    changeMode() {\r\n      if (this.isDetail) {\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n      } else {\r\n        this.isDetail = true;\r\n        this.form = {};\r\n      }\r\n      this.getDetail();\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          this.form.status = type;\r\n          editPolicyApply({ ...this.form }).then((response) => {\r\n            this.$modal.msgSuccess(\"操作成功\");\r\n            this.changeMode();\r\n          });\r\n        }\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.application = res.data.url;\r\n        this.form.applicationName = res.data.name;\r\n      }\r\n    },\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .notice-record-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 20px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 90%;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n          color: #333;\r\n        }\r\n        // .el-button:hover,\r\n        // .el-button:focus {\r\n        //   border-color: #d9d9d9;\r\n        //   background-color: transparent;\r\n        // }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}