<!--
 * @Author: jhy
 * @Date: 2023-01-30 11:29:06
 * @LastEditors: JHY
 * @LastEditTime: 2023-02-13 11:28:34
-->
<template>
  <div class="activity-container">
    <div class="activity-banner">
      <img src="../../assets/demand/demandBanner.png" alt="" />
    </div>
    <div>
      <div class="activity-title-content">
        <div class="activity-title-box">
          <div class="activity-divider"></div>
          <div class="activity-title">链需求</div>
          <div class="activity-divider"></div>
        </div>
        <div class="activity-search-box">
          <el-form ref="form" class="activity-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.keywords"
                placeholder="请输入搜索内容"
                class="activity-search-input"
              >
                <el-button
                  slot="append"
                  class="activity-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="activity-info-content">
        <div class="activity-search-type-box">
          <el-form ref="formInfo" :model="formInfo">
            <div class="activity-search-line">
              <el-form-item label="需求类型" class="activity-search-line-item">
                <el-radio-group
                  v-model="formInfo.demandType"
                  class="activity-search-radio"
                  @input="changeRadio"
                >
                  <el-radio-button label="">全部</el-radio-button>
                  <el-radio-button
                    v-for="(item, index) in demandTypeList"
                    :key="index"
                    :label="item.dictValue"
                    >{{ item.dictLabel }}</el-radio-button
                  >
                </el-radio-group>
              </el-form-item>
            </div>
          </el-form>
        </div>
        <div v-loading="demandLoading" v-if="data && data.length > 0">
          <div
            v-for="(item, index) in data"
            :key="index"
            class="activity-list-item"
            @click="goDemandDetail(item.id)"
          >
            <div class="list-item-content">
              <div class="list-item-img">
                <img
                  v-if="item.scenePicture && item.scenePicture.length > 0"
                  :src="item.scenePicture[0].url"
                  alt=""
                />
                <img
                  v-else
                  src="../../assets/purchaseSales/demandDefault.png"
                  alt=""
                />
              </div>
              <div class="list-item-info">
                <div class="list-item-title">
                  {{ item.demandTitle }}
                </div>
                <div
                  style="
                    margin-top: 10px;
                    margin-left: 10px;
                    display: flex;
                    align-items: center;
                  "
                >
                  <div
                    style="
                      height: 30px;
                      line-height: 28px;
                      color: rgb(77, 77, 78);
                    "
                  >
                    应用领域:
                  </div>
                  <div
                    style="margin-left: 10px; height: 30px; line-height: 28px"
                  >
                    <el-tag
                      class="tagStyle"
                      v-for="(areaItem, index) in item.applicationArea"
                      :key="index"
                      >{{ areaItem }}</el-tag
                    >
                    <!-- <el-tag style="margin-left: 20px">空调外机</el-tag>
                    <el-tag style="margin-left: 20px">语言芯片</el-tag> -->
                  </div>
                </div>
                <!-- <div class="list-item-text">
                {{ item.activityOverview }}
              </div>
              <div class="list-item-time">{{ item.createTimeStr }}</div> -->
              </div>
            </div>
          </div>
        </div>
        <div class="none-class" v-else>
          <el-image
            style="width: 160px; height: 160px"
            :src="require('@/assets/user/none.png')"
            :fit="fit"
          ></el-image>
          <div class="text">暂无数据</div>
        </div>
        <div class="activity-page-end">
          <el-button class="activity-page-btn" @click="goHome">首页</el-button>
          <el-pagination
            v-if="data && data.length > 0"
            background
            layout="prev, pager, next"
            class="activity-pagination"
            :page-size="pageSize"
            :current-page="pageNum"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getDicts } from "@/api/system/dict/data";
import { gatewayDemandListShow } from "@/api/purchaseSales";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      fit: "cover",
      demandLoading: false,
      form: {
        keywords: "", //搜索内容
      },
      formInfo: {
        demandType: "", //需求类型
      },
      demandTypeList: [
        {
          dictLabel: "创新研发",
          dictValue: "1",
        },
        {
          dictLabel: "物料采购",
          dictValue: "2",
        },
        {
          dictLabel: "智能制造",
          dictValue: "3",
        },
        {
          dictLabel: "数字化管理",
          dictValue: "4",
        },
        {
          dictLabel: "软件服务",
          dictValue: "5",
        },
        {
          dictLabel: "供应链金融",
          dictValue: "6",
        },
        {
          dictLabel: "运营宣传",
          dictValue: "7",
        },
        {
          dictLabel: "其他",
          dictValue: "8",
        },
      ], //活动类型列表
      data: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  created() {
    // this.getDictsList("activity_type", "activityTypeList");
    this.getDemandList();
  },
  methods: {
    // 找需求
    getDemandList() {
      this.demandLoading = true;
      gatewayDemandListShow({
        demandType: this.formInfo.demandType,
        // city: "青岛市",
        // region: "城阳区",
        displayStatus: 1,
        auditStatus: 2,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        name: this.form.keywords,
      })
        .then((res) => {
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
          this.demandLoading = false;
          let { rows } = res || [];
          this.data = rows;
          this.total = res.total;
          this.data.forEach((item) => {
            if (item.scenePicture) {
              item.scenePicture = JSON.parse(item.scenePicture);
            }
            if (item.applicationArea) {
              item.applicationArea = item.applicationArea.split(",");
            }
          });
        })
        .catch(() => {
          this.demandLoading = false;
          this.data = [];
        });
    },
    // 字典
    getDictsList(code, propertyName) {
      getDicts(code).then((res) => {
        this[propertyName] = res.data || [];
      });
    },
    changeRadio() {
      this.onSearch();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getDemandList();
    },
    onSearch() {
      this.pageNum = 1;
      this.getDemandList();
    },
    // 跳转到需求页面
    goDemandDetail(id) {
      let routeData = this.$router.resolve({
        path: "/demandHallDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    goHome() {
      this.$router.push({ path: "/index" });
    },
  },
};
</script>

<style lang="scss" scoped>
.activity-container {
  width: 100%;
  background: #f4f5f9;
  .activity-banner {
    width: 100%;
    height: 50vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .activity-title-content {
    width: 100%;
    background-color: #fff;
    padding-bottom: 18px;
    .activity-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .activity-title {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .activity-divider {
        width: 48px;
        height: 4px;
        background: #21c9b8;
      }
    }
    .activity-search-box {
      .activity-search-form {
        text-align: center;
        .activity-search-input {
          width: 792px;
          height: 54px;
          .activity-search-btn {
            width: 100px;
          }
        }
      }
    }
  }
  .activity-info-content {
    width: 1200px;
    margin: 40px auto 0;
    .activity-search-type-box {
      background: #fff;
      margin-bottom: -7px;
      .activity-search-line {
        padding: 14px 24px 4px;
        .activity-search-line-item {
          margin-bottom: 0;
        }
        & + .activity-search-line {
          border-top: 1px solid #f5f5f5;
        }
      }
    }
    .activity-list-item {
      width: 100%;
      background: #fff;
      border-radius: 12px;
      margin-top: 24px;
      .list-item-content {
        display: flex;
        padding: 24px 32px;
        cursor: pointer;
        .list-item-img {
          width: 230px;
          height: 164px;
          img {
            width: 100%;
            height: 100%;
            border-radius: 5px;
          }
        }
        .list-item-info {
          margin: auto 0;
          padding-left: 24px;
          font-family: PingFangSC-Regular, PingFang SC;
          .list-item-title {
            width: 806px;
            // height: 24px;
            text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
            white-space: nowrap; /*让文字不换行*/
            overflow: hidden; /*超出要隐藏*/
            font-size: 24px;
            font-weight: 500;
            color: #323233;
            // line-height: 24px;
            margin: 8px 0 24px;
            word-wrap: break-word;
          }
          .list-item-text {
            width: 806px;
            height: 60px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            text-overflow: ellipsis;
            font-size: 16px;
            color: #666;
            line-height: 30px;
            word-wrap: break-word;
          }
          .list-item-time {
            color: #999;
            line-height: 14px;
            margin-top: 24px;
          }
          .tagStyle {
            margin-left: 20px;
          }
          .tagStyle:nth-child(1) {
            margin-left: 0;
          }
        }
        &:hover {
          .list-item-title {
            color: #21c9b8;
          }
        }
      }
    }
    .activity-page-end {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      padding: 24px 0 60px;
      .activity-page-btn {
        width: 82px;
        height: 32px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        line-height: 10px;
      }
    }
  }
}
</style>

<style lang="scss">
.activity-container {
  .activity-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #21c9b8;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #fff;
      line-height: 24px;
    }
  }
  .activity-search-line {
    .el-form-item__label {
      width: 88px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #999;
      padding-right: 32px;
      text-align: left;
    }
    .activity-search-radio {
      width: 1050px;
      margin-top: 11px;
      .el-radio-button {
        padding-bottom: 20px;
        .el-radio-button__inner {
          border: none;
          padding: 0 32px 0 0;
          background: none;
          &:hover {
            color: #21c9b8;
          }
        }
        &.is-active {
          .el-radio-button__inner {
            color: #21c9b8;
            background: none;
          }
        }
        .el-radio-button__orig-radio:checked {
          & + .el-radio-button__inner {
            box-shadow: unset;
          }
        }
      }
    }
  }
  .activity-page-end {
    .activity-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
  .none-class {
    text-align: center;
    padding: 8% 0;
    background: #fff;
    margin-top: 25px;
    .text {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 14px;
    }
  }
}
</style>
