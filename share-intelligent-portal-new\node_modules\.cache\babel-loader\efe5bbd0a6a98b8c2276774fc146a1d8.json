{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\talentDetail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\talentDetail\\index.vue", "mtime": 1750311963087}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_data", "_serviceSharing", "components", "UserMenu", "data", "detailsData", "fileList", "fileName", "fileUrl", "id", "size", "positionTypeList", "educationList", "jobTitleList", "workStatusList", "created", "$route", "query", "getPositionType", "getEducation", "getJobTitle", "getWorkStatus", "getDetailData", "methods", "_this", "talentDetailData", "then", "res", "code", "_this2", "params", "dictType", "listData", "response", "rows", "unshift", "dict<PERSON><PERSON>ue", "dict<PERSON><PERSON>l", "_this3", "_this4", "_this5", "intention", "$router", "push", "downLoadFile", "url", "window", "open"], "sources": ["src/views/system/user/talentDetail/index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-row :gutter=\"20\">\r\n            <el-col :span=\"2.5\" :xs=\"24\">\r\n                <user-menu activeIndex=\"1\" />\r\n            </el-col>\r\n            <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n                <div class=\"cardStyle\">\r\n                    <!-- 左侧 -->\r\n                    <div class=\"card_left\">\r\n                        <div class=\"card_left_bottom\">\r\n                            <div class=\"imgStyle\">\r\n                                <img style=\"width: 100%; height: 100%\" :src=\"detailsData.photo\r\n                                    ? detailsData.photo\r\n                                    : require('../../../../assets/serviceSharing/ceshi2.png')\r\n                                    \" alt=\"\" />\r\n                            </div>\r\n                            <div class=\"title\">{{ detailsData.name }}</div>\r\n                            <div class=\"everyOption\">\r\n                                <div class=\"optionName\">岗位分类：</div>\r\n                                <div class=\"optionValue\" v-if=\"detailsData.positionType\">\r\n                                    {{\r\n                                        positionTypeList.filter(\r\n                                            (item) => item.dictValue == detailsData.positionType\r\n                                        )[0].dictLabel\r\n                                    }}\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"everyOption\">\r\n                                <div class=\"optionName\">最高学历：</div>\r\n                                <div class=\"optionValue\" v-if=\"detailsData.education\">\r\n                                    {{\r\n                                        educationList.filter(\r\n                                            (item) => item.dictValue == detailsData.education\r\n                                        )[0].dictLabel\r\n                                    }}\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"everyOption\">\r\n                                <div class=\"optionName\">工作状态：</div>\r\n                                <div class=\"optionValue\" v-if=\"detailsData.workStatus\">\r\n                                    {{\r\n                                        workStatusList.filter(\r\n                                            (item) => item.dictValue == detailsData.workStatus\r\n                                        )[0].dictLabel\r\n                                    }}\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"everyOption\">\r\n                                <div class=\"optionName\">联系方式：</div>\r\n                                <div class=\"optionValue\">{{ detailsData.contactPhone }}</div>\r\n                            </div>\r\n                            <!-- <div class=\"buttonStyle\" @click=\"intention\">我有意向</div> -->\r\n                        </div>\r\n                    </div>\r\n                    <!-- 中间 -->\r\n                    <div class=\"card_center_line\"></div>\r\n                    <!-- 右侧 -->\r\n                    <div class=\"card_right\">\r\n                        <div>\r\n                            <div class=\"content_title\">\r\n                                <div class=\"icon\"></div>\r\n                                <div class=\"title\">基本信息</div>\r\n                            </div>\r\n                            <div style=\"margin-top: 22px\">\r\n                                <el-descriptions class=\"margin-top\" title=\"\" :column=\"2\" :size=\"size\" border>\r\n                                    <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 毕业院校 </template>\r\n北京大学\r\n</el-descriptions-item> -->\r\n                                    <el-descriptions-item>\r\n                                        <template slot=\"label\"> 最高学历 </template>\r\n                                        {{\r\n                                            educationList.filter(\r\n                                                (item) => item.dictValue == detailsData.education\r\n                                            )[0].dictLabel\r\n                                        }}\r\n                                    </el-descriptions-item>\r\n                                    <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 出生年月 </template>\r\n                1999年5月25日\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 所在地 </template>\r\n                北京市\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 所在单位 </template>\r\n                北京爱德华科技有限公司\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 职务 </template>\r\n                经理\r\n              </el-descriptions-item> -->\r\n                                    <el-descriptions-item>\r\n                                        <template slot=\"label\"> 职称 </template>\r\n                                        {{\r\n                                            jobTitleList.filter(\r\n                                                (item) => item.dictValue == detailsData.jobTitle\r\n                                            )[0].dictLabel\r\n                                        }}\r\n                                    </el-descriptions-item>\r\n                                    <el-descriptions-item>\r\n                                        <template slot=\"label\"> 工作状态 </template>\r\n                                        {{\r\n                                            workStatusList.filter(\r\n                                                (item) => item.dictValue == detailsData.workStatus\r\n                                            )[0].dictLabel\r\n                                        }}\r\n                                    </el-descriptions-item>\r\n                                </el-descriptions>\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"margin-top: 31px\">\r\n                            <div class=\"content_title\">\r\n                                <div class=\"icon\"></div>\r\n                                <div class=\"title\">个人简历</div>\r\n                            </div>\r\n                            <div style=\"margin-top: 22px\" class=\"desc\">\r\n                                {{ detailsData.workExperience }}\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"margin-top: 31px\">\r\n                            <div class=\"content_title\">\r\n                                <div class=\"icon\"></div>\r\n                                <div class=\"title\">技术领域</div>\r\n                            </div>\r\n                            <div style=\"margin-top: 22px\" class=\"desc\">\r\n                                {{ detailsData.skills }}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </el-col>\r\n        </el-row>\r\n    </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { talentAdd, talentDetailData } from \"@/api/serviceSharing\";\r\nexport default {\r\n    components: { UserMenu },\r\n    data() {\r\n        return {\r\n            detailsData: {},\r\n            fileList: [\r\n                {\r\n                    fileName: \"报表数据.csv\",\r\n                    fileUrl: \"\",\r\n                },\r\n                {\r\n                    fileName: \"报表数据.csv\",\r\n                    fileUrl: \"\",\r\n                },\r\n            ],\r\n            id: null,\r\n            size: \"\",\r\n            positionTypeList: [], // 岗位分类\r\n            educationList: [], // 最高学历\r\n            jobTitleList: [], // 职称\r\n            workStatusList: [], // 工作状态\r\n        };\r\n    },\r\n    created() {\r\n        this.id = this.$route.query.id;\r\n        this.getPositionType();\r\n        this.getEducation();\r\n        this.getJobTitle();\r\n        this.getWorkStatus();\r\n        this.getDetailData();\r\n    },\r\n    methods: {\r\n        getDetailData() {\r\n            talentDetailData(this.id).then((res) => {\r\n                if (res.code === 200) {\r\n                    this.detailsData = res.data;\r\n                }\r\n            });\r\n        },\r\n        // 岗位分类\r\n        getPositionType() {\r\n            let params = { dictType: \"position_type\" };\r\n            listData(params).then((response) => {\r\n                this.positionTypeList = response.rows;\r\n                this.positionTypeList.unshift({\r\n                    dictValue: \"\",\r\n                    dictLabel: \"全部\",\r\n                });\r\n            });\r\n        },\r\n        // 最高学历\r\n        getEducation() {\r\n            let params = { dictType: \"education\" };\r\n            listData(params).then((response) => {\r\n                this.educationList = response.rows;\r\n                this.educationList.unshift({\r\n                    dictValue: \"\",\r\n                    dictLabel: \"全部\",\r\n                });\r\n            });\r\n        },\r\n        // 职称\r\n        getJobTitle() {\r\n            let params = { dictType: \"job_title\" };\r\n            listData(params).then((response) => {\r\n                this.jobTitleList = response.rows;\r\n                this.jobTitleList.unshift({\r\n                    dictValue: \"\",\r\n                    dictLabel: \"全部\",\r\n                });\r\n            });\r\n        },\r\n        // 工作状态\r\n        getWorkStatus() {\r\n            let params = { dictType: \"work_status\" };\r\n            listData(params).then((response) => {\r\n                this.workStatusList = response.rows;\r\n                this.workStatusList.unshift({\r\n                    dictValue: \"\",\r\n                    dictLabel: \"全部\",\r\n                });\r\n            });\r\n        },\r\n        intention(id) {\r\n            this.$router.push(\"/receiveOrder\"); // 传id\r\n        },\r\n        downLoadFile(url) {\r\n            if (url) {\r\n                window.open(url);\r\n            }\r\n        },\r\n    },\r\n};\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n    background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n    min-height: 85vh;\r\n}\r\n\r\n.content {\r\n    width: 100%;\r\n    background-color: #f2f2f2;\r\n    padding: 30px 0 61px 0;\r\n}\r\n\r\n.cardStyle {\r\n    // height: 630px;\r\n    background-color: #ffffff;\r\n    padding: 60px 56px 54px 50px;\r\n    display: flex;\r\n}\r\n\r\n.card_left {\r\n    .card_left_bottom {\r\n        .imgStyle {\r\n            width: 150px;\r\n            height: 180px;\r\n            margin-left: 35px;\r\n        }\r\n\r\n        .title {\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            font-size: 20px;\r\n            color: #222222;\r\n            margin-top: 19px;\r\n            margin-bottom: 18px;\r\n            text-align: center;\r\n        }\r\n\r\n        .everyOption {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-top: 12px;\r\n\r\n            .optionName {\r\n                // height: 14px;\r\n                font-family: Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 14px;\r\n                color: #999999;\r\n            }\r\n\r\n            .optionValue {\r\n                // height: 14px;\r\n                font-family: Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 14px;\r\n                color: #333333;\r\n            }\r\n        }\r\n\r\n        .buttonStyle {\r\n            margin-top: 32px;\r\n            // margin-left: 55px;\r\n            width: 220px;\r\n            height: 50px;\r\n            background: #21c9b8;\r\n            box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);\r\n            border-radius: 2px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #ffffff;\r\n            text-align: center;\r\n            line-height: 50px;\r\n            cursor: pointer;\r\n        }\r\n    }\r\n}\r\n\r\n.card_center_line {\r\n    width: 1px;\r\n    height: 100%;\r\n    background: #e1e1e1;\r\n    margin-left: 60px;\r\n    margin-right: 61px;\r\n}\r\n\r\n.card_right {\r\n    width: 100%;\r\n\r\n    // overflow-y: auto;\r\n    .content_title {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .icon {\r\n            width: 4px;\r\n            height: 20px;\r\n            background: #21c9b8;\r\n        }\r\n\r\n        .title {\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 18px;\r\n            color: #030a1a;\r\n            margin-left: 10px;\r\n        }\r\n    }\r\n\r\n    .desc {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #666666;\r\n        line-height: 24px;\r\n    }\r\n\r\n    .fileContent {\r\n        margin-top: 22px;\r\n        display: flex;\r\n        align-items: center;\r\n        flex-wrap: wrap;\r\n\r\n        .fileItem {\r\n            width: 280px;\r\n            height: 50px;\r\n            background: #e8f9f8;\r\n            border-radius: 2px;\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 10px;\r\n            margin-left: 20px;\r\n            cursor: pointer;\r\n\r\n            .fileImg {\r\n                width: 24px;\r\n                height: 28px;\r\n            }\r\n\r\n            .fileName {\r\n                font-family: Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 14px;\r\n                color: #666666;\r\n                margin-left: 15px;\r\n            }\r\n        }\r\n\r\n        .fileItem:nth-child(2n + 1) {\r\n            margin-left: 0;\r\n        }\r\n    }\r\n}\r\n</style>"], "mappings": ";;;;;;;;AA0IA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAG,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,QAAA,GACA;QACAC,QAAA;QACAC,OAAA;MACA,GACA;QACAD,QAAA;QACAC,OAAA;MACA,EACA;MACAC,EAAA;MACAC,IAAA;MACAC,gBAAA;MAAA;MACAC,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAN,EAAA,QAAAO,MAAA,CAAAC,KAAA,CAAAR,EAAA;IACA,KAAAS,eAAA;IACA,KAAAC,YAAA;IACA,KAAAC,WAAA;IACA,KAAAC,aAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,gCAAA,OAAAhB,EAAA,EAAAiB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAnB,WAAA,GAAAsB,GAAA,CAAAvB,IAAA;QACA;MACA;IACA;IACA;IACAc,eAAA,WAAAA,gBAAA;MAAA,IAAAW,MAAA;MACA,IAAAC,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAJ,IAAA,WAAAO,QAAA;QACAJ,MAAA,CAAAlB,gBAAA,GAAAsB,QAAA,CAAAC,IAAA;QACAL,MAAA,CAAAlB,gBAAA,CAAAwB,OAAA;UACAC,SAAA;UACAC,SAAA;QACA;MACA;IACA;IACA;IACAlB,YAAA,WAAAA,aAAA;MAAA,IAAAmB,MAAA;MACA,IAAAR,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAJ,IAAA,WAAAO,QAAA;QACAK,MAAA,CAAA1B,aAAA,GAAAqB,QAAA,CAAAC,IAAA;QACAI,MAAA,CAAA1B,aAAA,CAAAuB,OAAA;UACAC,SAAA;UACAC,SAAA;QACA;MACA;IACA;IACA;IACAjB,WAAA,WAAAA,YAAA;MAAA,IAAAmB,MAAA;MACA,IAAAT,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAJ,IAAA,WAAAO,QAAA;QACAM,MAAA,CAAA1B,YAAA,GAAAoB,QAAA,CAAAC,IAAA;QACAK,MAAA,CAAA1B,YAAA,CAAAsB,OAAA;UACAC,SAAA;UACAC,SAAA;QACA;MACA;IACA;IACA;IACAhB,aAAA,WAAAA,cAAA;MAAA,IAAAmB,MAAA;MACA,IAAAV,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAJ,IAAA,WAAAO,QAAA;QACAO,MAAA,CAAA1B,cAAA,GAAAmB,QAAA,CAAAC,IAAA;QACAM,MAAA,CAAA1B,cAAA,CAAAqB,OAAA;UACAC,SAAA;UACAC,SAAA;QACA;MACA;IACA;IACAI,SAAA,WAAAA,UAAAhC,EAAA;MACA,KAAAiC,OAAA,CAAAC,IAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,IAAAA,GAAA;QACAC,MAAA,CAAAC,IAAA,CAAAF,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}