import request from '@/utils/request'

// 查询检测项目列表
export function listTestingItem(query) {
  return request({
    url: '/system/testingItem/list',
    method: 'get',
    params: query
  })
}

// 查询检测项目详细
export function getTestingItem(id) {
  return request({
    url: '/system/testingItem/' + id,
    method: 'get'
  })
}
// 查询检测项目详细并关联实验室
export function getTestingItemAndLab(id) {
  return request({
    url: '/system/testingItem/withLabs/' + id,
    method: 'get'
  })
}

// 新增检测项目
export function addTestingItem(data) {
  return request({
    url: '/system/testingItem',
    method: 'post',
    data: data
  })
}
// 新增检测项目并关联实验室
export function addTestingItemAndLab(data) {
  return request({
    url: '/system/testingItem/withLabs',
    method: 'post',
    data: data
  })
}

// 修改检测项目
export function updateTestingItem(data) {
  return request({
    url: '/system/testingItem',
    method: 'put',
    data: data
  })
}
// 修改检测项目并关联实验室
export function updateTestingItemAndLab(data) {
  return request({
    url: '/system/testingItem/withLabs',
    method: 'put',
    data: data
  })
}

// 删除检测项目
export function delTestingItem(id) {
  return request({
    url: '/system/testingItem/' + id,
    method: 'delete'
  })
}
