{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyApply\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyApply\\detail\\index.vue", "mtime": 1750311963046}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpsZXQgaWQgPSAwOw0KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsgZ2V0QXBwbHlEZXRhaWwsIGNyZWF0ZUFwcGx5LCBlZGl0QXBwbHkgfSBmcm9tICJAL2FwaS9zeXN0ZW0vYXBwbHkiOw0KaW1wb3J0IHsgdXBsb2FkVXJsIH0gZnJvbSAiQC9hcGkvb3NzIjsNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsNCmltcG9ydCBGaWxlVXBsb2FkIGZyb20gIkAvY29tcG9uZW50cy9GaWxlVXBsb2FkIjsNCmltcG9ydCB7IGRlbWFuZEFkZCwga2V5d29yZExpc3QgfSBmcm9tICJAL2FwaS96aG0iOw0KaW1wb3J0IHN0b3JlIGZyb20gIkAvc3RvcmUiOw0KaW1wb3J0IENyeXB0b0pTIGZyb20gImNyeXB0by1qcyI7DQpsZXQgc2VjcmV0S2V5ID0gIjl6Vm4wJWJxbVVZU0d3Mm4iOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJDb21wYW55QXBwbHlEZXRhaWwiLA0KICBkaWN0czogWw0KICAgICJ0ZWNobm9sb2d5X3R5cGUiLA0KICAgICJjb29wZXJhdGlvbl9tb2RlIiwNCiAgICAicHJvZHVjdF9zdGFnZSIsDQogICAgInN1cHBseV90eXBlIiwNCiAgICAiYXBwbGljYXRpb25fYXJlYSIsDQogIF0sDQogIGNvbXBvbmVudHM6IHsgVXNlck1lbnUsIEZpbGVVcGxvYWQgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgaXNEZXRhaWw6IHRydWUsDQogICAgICB0aXRsZTogIuS+m+e7meivpuaDhSIsDQogICAgICBhY3Rpb25Vcmw6IHVwbG9hZFVybCgpLA0KICAgICAgaGVhZGVyczogeyBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpIH0sDQogICAgICB1cGxvYWRVcmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL2ZpbGUvdXBsb2FkIiwgLy/kuIrkvKDlnLDlnYANCiAgICAgIGFjY2VwdDogIi5qcGcsIC5qcGVnLCAucG5nLCAuYm1wIiwNCiAgICAgIGlzQ3JlYXRlOiBmYWxzZSwNCiAgICAgIGltZ1Zpc2libGU6IGZhbHNlLA0KICAgICAga2V5d29yZHM6IFtdLA0KICAgICAgaW1hZ2VVcmw6ICIiLA0KICAgICAgYXBwbGljYXRpb25zSW5wdXQ6ICIiLA0KICAgICAgaW5mbzoge30sDQogICAgICBmb3JtOiB7fSwNCiAgICAgIGFjY291bnRMaWNlbmNlTGlzdDogW10sDQogICAgICB1c2VyOiB7DQogICAgICAgIHRlbDogc3RvcmUuZ2V0dGVycy50ZWwsDQogICAgICAgIG5hbWU6IHN0b3JlLmdldHRlcnMubmFtZSwNCiAgICAgICAgY29tcGFueU5hbWU6IHN0b3JlLmdldHRlcnMuY29tcGFueU5hbWUsDQogICAgICAgIGJ1c3NpbmVzc05vOiBzdG9yZS5nZXR0ZXJzLmJ1c3NpbmVzc05vLA0KICAgICAgICBwaG9uZW51bWJlcjogc3RvcmUuZ2V0dGVycy5waG9uZW51bWJlciwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIHN1cHBseVR5cGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5L6b57uZ57G75Z6LIiwgdHJpZ2dlcjogImNoYW5nZSIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgc3VwcGx5TmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkvpvnu5nmoIfpopjkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgc3VtbWFyeTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkvpvnu5nmj4/ov7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgdGVjaG5vbG9neVR5cGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5oqA5pyv57G75Yir5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImNoYW5nZSIgfSwNCiAgICAgICAgXSwNCg0KICAgICAgICBhcHBsaWNhdGlvbkFyZWFMaXN0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuW6lOeUqOmihuWfn+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvbnRhY3RzTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLogZTns7vkurrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29tcGFueU5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5YWs5Y+45ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvbnRhY3RzTW9iaWxlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiBlOezu+eUteivneS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuaXNDcmVhdGUgPSB0aGlzLiRyb3V0ZS5xdWVyeS50eXBlID09IDE7DQogICAgaWYgKHRoaXMuaXNDcmVhdGUpIHsNCiAgICAgIHRoaXMuZ29DcmVhdGUoKTsNCiAgICB9IGVsc2Ugew0KICAgICAgdGhpcy5nZXREZXRhaWwoKTsNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBpbml0Rm9ybSgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgYXBwbGljYXRpb25BcmVhOiBbXSwNCiAgICAgICAgcHJvZHVjdFBob3RvOiBbXSwNCiAgICAgICAgYXBwbGljYXRpb25BcmVhTGlzdDogW10sDQogICAgICAgIHByb2R1Y3RQaG90b0xpc3Q6IFtdLA0KICAgICAgICBzdXBwbHlUeXBlOiBbXSwNCiAgICAgICAga2V5d29yZHM6IFtdLA0KICAgICAgICBjb29wZXJhdGlvbk1vZGVOYW1lOiAi5Y+M5pa55Y2P5ZWGIiwNCiAgICAgICAgY29vcGVyYXRpb25Nb2RlOiAiMSIsDQogICAgICAgIGF1ZGl0U3RhdHVzOiAiMSIsDQogICAgICAgIGRpc3BsYXlTdGF0dXM6ICIyIiwNCiAgICAgICAgcHVibGlzaGVyTmFtZTogdGhpcy51c2VyLm5hbWUsDQogICAgICAgIHB1Ymxpc2hlck1vYmlsZTogdGhpcy51c2VyLnRlbCwNCiAgICAgICAgdGVjaG5vbG9neVR5cGU6IFtdLA0KICAgICAgfTsNCiAgICB9LA0KICAgIGdldERldGFpbCgpIHsNCiAgICAgIGxldCBpZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkOw0KICAgICAgZ2V0QXBwbHlEZXRhaWwoaWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIGxldCBrZXkgPSBDcnlwdG9KUy5lbmMuVXRmOC5wYXJzZShzZWNyZXRLZXkpOw0KICAgICAgICBsZXQgZGVjcnlwdCA9IENyeXB0b0pTLkFFUy5kZWNyeXB0KHJlc3BvbnNlLCBrZXksIHsNCiAgICAgICAgICBtb2RlOiBDcnlwdG9KUy5tb2RlLkVDQiwNCiAgICAgICAgICBwYWRkaW5nOiBDcnlwdG9KUy5wYWQuUGtjczcsDQogICAgICAgIH0pOw0KICAgICAgICByZXNwb25zZSA9IEpTT04ucGFyc2UoQ3J5cHRvSlMuZW5jLlV0Zjguc3RyaW5naWZ5KGRlY3J5cHQpKTsNCiAgICAgICAgdGhpcy5pbmZvID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBnb0JhY2soKSB7DQogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOw0KICAgIH0sDQogICAgZ2V0VXJsKHN0cikgew0KICAgICAgY29uc29sZS5sb2coc3RyKTsNCiAgICAgIGlmIChzdHIpIHsNCiAgICAgICAgdmFyIGxpc3QgPSBKU09OLnBhcnNlKHN0cik7DQogICAgICAgIGlmIChsaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICByZXR1cm4gbGlzdFswXS51cmw7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIG51bGw7DQogICAgfSwNCiAgICAvLyDlupTnlKjpoobln5/mlrDlop4NCiAgICBoYW5kbGVJbnB1dENvbmZpcm0oKSB7DQogICAgICBsZXQgdmFsID0gdGhpcy5hcHBsaWNhdGlvbnNJbnB1dDsNCiAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgaWYgKCF0aGlzLmZvcm0uYXBwbGljYXRpb25BcmVhTGlzdCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5hcHBsaWNhdGlvbkFyZWFMaXN0ID0gW107DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uQXJlYUxpc3QucHVzaCh2YWwpOw0KICAgICAgfQ0KICAgICAgdGhpcy5hcHBsaWNhdGlvbnNJbnB1dCA9ICIiOw0KICAgIH0sDQogICAgLy8g5bqU55So6aKG5Z+f56e76ZmkDQogICAgaGFuZGxlQ2xvc2UodGFnKSB7DQogICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb25BcmVhTGlzdC5zcGxpY2UoDQogICAgICAgIHRoaXMuZm9ybS5hcHBsaWNhdGlvbkFyZWFMaXN0LmluZGV4T2YodGFnKSwNCiAgICAgICAgMQ0KICAgICAgKTsNCiAgICB9LA0KICAgIGhhbmRsZVN1bW1hcnlDbG9zZSh0YWcpIHsNCiAgICAgIHRoaXMuZm9ybS5rZXl3b3Jkcy5zcGxpY2UodGhpcy5mb3JtLmtleXdvcmRzLmluZGV4T2YodGFnKSwgMSk7DQogICAgfSwNCiAgICBjb29wZXJhdGlvbk1vZGVDaGFuZ2UocmVzKSB7DQogICAgICB0aGlzLmZvcm0uY29vcGVyYXRpb25Nb2RlID0gcmVzOw0KICAgIH0sDQogICAgLy8g5Lqn5ZOB54Wn54mH5LiK5Lyg5LmL5YmN55qE6ZKp5a2QDQogICAgaGFuZGxlQmVmb3JlVXBsb2FkKGZpbGUpIHsNCiAgICAgIGxldCB7IG5hbWUsIHR5cGUsIHNpemUgfSA9IGZpbGU7DQogICAgICBsZXQgdHlwZUxpc3QgPSB0aGlzLmFjY2VwdA0KICAgICAgICAuc3BsaXQoIiwiKQ0KICAgICAgICAubWFwKChpdGVtKSA9PiBpdGVtLnRyaW0oKS50b0xvd2VyQ2FzZSgpLnN1YnN0cigxKSk7DQogICAgICBsZXQgZG90SW5kZXggPSBuYW1lLmxhc3RJbmRleE9mKCIuIik7DQogICAgICAvLyDmlofku7bnsbvlnovmoKHpqowNCiAgICAgIGlmIChkb3RJbmRleCA9PT0gLTEpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+35LiK5Lyg5q2j56Gu5qC85byP55qE5paH5Lu2Iik7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldCBzdWZmaXggPSBuYW1lLnN1YnN0cmluZyhkb3RJbmRleCArIDEpOw0KICAgICAgICBpZiAodHlwZUxpc3QuaW5kZXhPZihzdWZmaXgudG9Mb3dlckNhc2UoKSkgPT09IC0xKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+35LiK5Lyg5q2j56Gu5qC85byP55qE5paH5Lu2Iik7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgICAvLyDmlofku7bkuIrkvKDlpKflsI/pmZDliLYNCiAgICAgIGlmIChzaXplID4gMTA0ODU3NiAqIDIwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hzIwTe+8gSIpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDngrnlh7vkuqflk4HnhafniYcNCiAgICBoYW5kbGVQaWN0dXJlQ2FyZFByZXZpZXcoZmlsZSkgew0KICAgICAgdGhpcy5pbWFnZVVybCA9IGZpbGUudXJsOw0KICAgICAgdGhpcy5pbWdWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8vIOWIoOmZpOS6p+WTgeeFp+eJhw0KICAgIGhhbmRsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5mb3JtLnByb2R1Y3RQaG90b0xpc3QgPSBmaWxlTGlzdDsNCiAgICB9LA0KICAgIGhhbmRsZVN1Y2Nlc3MocmVzLCBmaWxlKSB7DQogICAgICAvL+atpOWkhOWGmeS4iuS8oG9zc+aIkOWKn+S5i+WQjueahOmAu+i+kQ0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICBpZiAodGhpcy5mb3JtLnByb2R1Y3RQaG90b0xpc3QgPT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0UGhvdG9MaXN0ID0gW107DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5mb3JtLnByb2R1Y3RQaG90b0xpc3QucHVzaChyZXMuZGF0YSk7DQogICAgICB9DQogICAgfSwNCiAgICBjaGFuZ2VNb2RlKCkgew0KICAgICAgaWYgKHRoaXMuaXNDcmVhdGUpIHsNCiAgICAgICAgdGhpcy5nb0JhY2soKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuaXNEZXRhaWwpIHsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLnvJbovpHkvpvnu5kiOw0KICAgICAgICB0aGlzLmlzRGV0YWlsID0gZmFsc2U7DQogICAgICAgIHRoaXMuZm9ybSA9IHRoaXMuaW5mbzsNCiAgICAgICAgaWYgKHRoaXMuaW5mby5hcHBsaWNhdGlvbkFyZWEpIHsNCiAgICAgICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb25BcmVhTGlzdCA9IHRoaXMuaW5mby5hcHBsaWNhdGlvbkFyZWEuc3BsaXQoIiwiKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb25BcmVhTGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLmluZm8uc3VwcGx5VHlwZSkgew0KICAgICAgICAgIHRoaXMuZm9ybS5zdXBwbHlUeXBlID0gdGhpcy5pbmZvLnN1cHBseVR5cGUuc3BsaXQoIiwiKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmZvcm0uc3VwcGx5VHlwZSA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLmluZm8udGVjaG5vbG9neVR5cGUpIHsNCiAgICAgICAgICB0aGlzLmZvcm0udGVjaG5vbG9neVR5cGUgPSB0aGlzLmluZm8udGVjaG5vbG9neVR5cGUuc3BsaXQoIiwiKTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5pbmZvLmtleXdvcmRzKSB7DQogICAgICAgICAgdGhpcy5mb3JtLmtleXdvcmRzID0gdGhpcy5pbmZvLmtleXdvcmRzLnNwbGl0KCIsIik7DQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuaW5mby5lbmNsb3N1cmUpIHsNCiAgICAgICAgICB0aGlzLmZvcm0uZW5jbG9zdXJlTGlzdCA9IEpTT04ucGFyc2UodGhpcy5pbmZvLmVuY2xvc3VyZSk7DQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuaW5mby5wcm9kdWN0UGhvdG8pIHsNCiAgICAgICAgICB0aGlzLmZvcm0ucHJvZHVjdFBob3RvTGlzdCA9IEpTT04ucGFyc2UodGhpcy5pbmZvLnByb2R1Y3RQaG90byk7DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuaXNEZXRhaWwgPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS+m+e7meivpuaDhSI7DQogICAgICAgIHRoaXMuaW5pdEZvcm0oKTsNCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0RGV0YWlsKCk7DQogICAgfSwNCiAgICBwcm9kdWN0U3RhZ2VDaGFuZ2VkKHJlcykgew0KICAgICAgdGhpcy5kaWN0LnR5cGUucHJvZHVjdF9zdGFnZS5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGlmIChpdGVtLmxhYmVsID09IHJlcykgew0KICAgICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0U3RhZ2UgPSBpdGVtLnZhbHVlOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZUtleXdvcmRMaXN0KCkgew0KICAgICAgY29uc3QgeyBzdW1tYXJ5IH0gPSB0aGlzLmZvcm07DQogICAgICBpZiAoc3VtbWFyeSkgew0KICAgICAgICBrZXl3b3JkTGlzdChzdW1tYXJ5KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBjb25zdCB7IGNvZGUsIGRhdGEsIG1zZyB9ID0gcmVzOw0KICAgICAgICAgIGlmIChjb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5rZXl3b3JkcyA9IGRhdGE7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IobXNnKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fovpPlhaXpnIDmsYLmj4/ov7AiKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGdvQ3JlYXRlKCkgew0KICAgICAgdGhpcy50aXRsZSA9ICLmlrDlop7kvpvnu5kiOw0KICAgICAgdGhpcy5pc0RldGFpbCA9IGZhbHNlOw0KICAgICAgdGhpcy5pbml0Rm9ybSgpOw0KICAgICAgdGhpcy5mb3JtLmNvbXBhbnlOYW1lID0gdGhpcy51c2VyLmNvbXBhbnlOYW1lOw0KICAgICAgdGhpcy5mb3JtLmNvbnRhY3RzTmFtZSA9IHRoaXMudXNlci5uYW1lOw0KICAgICAgdGhpcy5mb3JtLmNvbnRhY3RzTW9iaWxlID0gdGhpcy51c2VyLnBob25lbnVtYmVyOw0KICAgICAgdGhpcy5mb3JtLnB1Ymxpc2hlck5hbWUgPSB0aGlzLnVzZXIubmFtZTsNCiAgICAgIHRoaXMuZm9ybS5wdWJsaXNoZXJNb2JpbGUgPSB0aGlzLnVzZXIucGhvbmVudW1iZXI7DQogICAgICB0aGlzLmZvcm0uYnVzaW5lc3NObyA9IHRoaXMudXNlci5idXNzaW5lc3NObzsNCiAgICB9LA0KICAgIGhhbmRsZUZpbGVQcmV2aWV3KGZpbGUpIHsNCiAgICAgIHdpbmRvdy5vcGVuKGZpbGUpOw0KICAgIH0sDQogICAgc3VibWl0Rm9ybSh0eXBlKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICgNCiAgICAgICAgICAgIHRoaXMuZm9ybS5hcHBsaWNhdGlvbkFyZWFMaXN0ICYmDQogICAgICAgICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb25BcmVhTGlzdC5sZW5ndGggPiAwDQogICAgICAgICAgKSB7DQogICAgICAgICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb25BcmVhID0gdGhpcy5mb3JtLmFwcGxpY2F0aW9uQXJlYUxpc3Quam9pbigiLCIpOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb25BcmVhID0gIiI7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmICh0aGlzLmZvcm0udGVjaG5vbG9neVR5cGUgJiYgdGhpcy5mb3JtLnRlY2hub2xvZ3lUeXBlLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS50ZWNobm9sb2d5VHlwZSA9IHRoaXMuZm9ybS50ZWNobm9sb2d5VHlwZS5qb2luKCIsIik7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS50ZWNobm9sb2d5VHlwZSA9ICIiOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAodGhpcy5mb3JtLnN1cHBseVR5cGUgJiYgdGhpcy5mb3JtLnN1cHBseVR5cGUubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtLnN1cHBseVR5cGUgPSB0aGlzLmZvcm0uc3VwcGx5VHlwZS5qb2luKCIsIik7DQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuZm9ybS5wcm9kdWN0UGhvdG8gPSBKU09OLnN0cmluZ2lmeSh0aGlzLmZvcm0ucHJvZHVjdFBob3RvTGlzdCk7DQogICAgICAgICAgdGhpcy5mb3JtLmVuY2xvc3VyZSA9IEpTT04uc3RyaW5naWZ5KHRoaXMuZm9ybS5lbmNsb3N1cmVMaXN0KTsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmtleXdvcmRzICYmIHRoaXMuZm9ybS5rZXl3b3Jkcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICB0aGlzLmZvcm0ua2V5d29yZHMgPSB0aGlzLmZvcm0ua2V5d29yZHMuam9pbigiLCIpOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLmZvcm0ua2V5d29yZHMgPSAiIjsNCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy5mb3JtLmJ1c2luZXNzTm8gPSB0aGlzLnVzZXIuYnVzc2luZXNzTm87DQoNCiAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmZvcm0pOw0KICAgICAgICAgIGlmICh0aGlzLmlzQ3JlYXRlKSB7DQogICAgICAgICAgICBjcmVhdGVBcHBseSh7IC4uLnRoaXMuZm9ybSwgaXNTdWJtaXQ6IHR5cGUgfSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMuY2hhbmdlTW9kZSgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5hdWRpdFN0YXR1cyA9IDE7DQogICAgICAgICAgICBlZGl0QXBwbHkoeyAuLi50aGlzLmZvcm0sIGlzU3VibWl0OiB0eXBlIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLmNoYW5nZU1vZGUoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVBcHBsaWNhdGlvblJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uID0gIiI7DQogICAgfSwNCg0KICAgIGhhbmRsZUFjY291bnRSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMuZm9ybS5hY2NvdW50TGljZW5jZSA9ICIiOw0KICAgIH0sDQogICAgdG9aaXl1YW4oKSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgIHBhdGg6ICIvdXNlci9jb21wYW55RGVtYW5kRGV0YWlsMSIsDQogICAgICAgIHF1ZXJ5OiB7IGtleTogSlNPTi5zdHJpbmdpZnkodGhpcy5pbmZvKSB9LA0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVBY2NvdW50U3VjY2VzcyhyZXMsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICAvL+atpOWkhOWGmeS4iuS8oG9zc+aIkOWKn+S5i+WQjueahOmAu+i+kQ0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICBjb25zb2xlLmxvZyhyZXMpOw0KICAgICAgICB0aGlzLmZvcm0uYWNjb3VudExpY2VuY2UgPSByZXMuZGF0YS51cmw7DQogICAgICAgIHRoaXMuZm9ybS5hY2NvdW50TGljZW5jZU5hbWUgPSByZXMuZGF0YS5uYW1lOw0KICAgICAgfQ0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4TA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/companyApply/detail", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-11 15:20:15\r\n * @LastEditTime: 2023-03-06 13:59:14\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"company-demand-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">{{ this.title }}</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                基本信息<el-button\r\n                  plain\r\n                  type=\"primary\"\r\n                  style=\"position: absolute; right: 0\"\r\n                  @click=\"toZiyuan\"\r\n                  >查看平台匹配资源</el-button\r\n                >\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 成果标题 </template>\r\n                  {{ info.supplyName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 成果描述 </template>\r\n                  <div v-html=\"info.summary\"></div>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 应用领域</template>\r\n                  {{ info.applicationArea }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 技术类别</template>\r\n                  {{ info.technologyType }}\r\n                </el-descriptions-item>\r\n\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 成果封面 </template>\r\n                  <el-image\r\n                    style=\"width: 90px; height: 64px\"\r\n                    :src=\"getUrl(info.productPhoto)\"\r\n                  ></el-image>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <div class=\"header-small mt_40\">\r\n                <div class=\"red-tag\"></div>\r\n                联系信息\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 公司名称 </template>\r\n                  {{ info.companyName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系人 </template>\r\n                  {{ info.contactsName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系电话</template>\r\n                  {{ info.contactsMobile }}\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <el-image\r\n                class=\"status_approving\"\r\n                v-if=\"info.status == '1'\"\r\n                style=\"width: 120px; height: 102px\"\r\n                :src=\"require('@/assets/user/status_approving.png')\"\r\n              ></el-image>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"goBack\">返回</el-button>\r\n                <el-button type=\"danger\" @click=\"changeMode\">编辑</el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"edit-page\" v-else>\r\n              <el-form\r\n                ref=\"form\"\r\n                :model=\"form\"\r\n                :rules=\"rules\"\r\n                label-width=\"120px\"\r\n              >\r\n                <el-form-item label=\"供给类型\" prop=\"supplyType\">\r\n                  <el-checkbox-group\r\n                    v-model=\"form.supplyType\"\r\n                    placeholder=\"请选择\"\r\n                    clearable\r\n                  >\r\n                    <el-checkbox\r\n                      v-for=\"dict in dict.type.supply_type\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.value\"\r\n                      :value=\"dict.value\"\r\n                      >{{ dict.label }}</el-checkbox\r\n                    >\r\n                  </el-checkbox-group>\r\n                </el-form-item>\r\n                <el-form-item label=\"供给标题\" prop=\"supplyName\">\r\n                  <el-input\r\n                    v-model=\"form.supplyName\"\r\n                    :maxlength=\"50\"\r\n                    placeholder=\"请输入\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"供给描述\" prop=\"summary\">\r\n                  <el-input\r\n                    v-model=\"form.summary\"\r\n                    type=\"textarea\"\r\n                    :rows=\"2\"\r\n                    :maxlength=\"500\"\r\n                    placeholder=\"请输入\"\r\n                  />\r\n                  <div class=\"extra-content\">\r\n                    <div class=\"extra-content-header\">\r\n                      <el-button\r\n                        @click=\"handleKeywordList\"\r\n                        size=\"small\"\r\n                        type=\"primary\"\r\n                        >生成关键词</el-button\r\n                      >\r\n                      <span class=\"tip\">生成关键词有利于实现精准匹配哦！</span>\r\n                    </div>\r\n                    <div\r\n                      v-if=\"form.keywords && form.keywords.length > 0\"\r\n                      class=\"extra-content-body\"\r\n                    >\r\n                      <el-tag\r\n                        :key=\"`${tag}_${index}`\"\r\n                        v-for=\"(tag, index) in form.keywords\"\r\n                        closable\r\n                        size=\"small\"\r\n                        disable-transitions\r\n                        @close=\"handleSummaryClose(tag)\"\r\n                      >\r\n                        {{ tag }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n                <el-form-item label=\"技术类别\" prop=\"technologyType\">\r\n                  <el-select\r\n                    v-model=\"form.technologyType\"\r\n                    :multiple=\"true\"\r\n                    allow-create\r\n                    filterable\r\n                    style=\"width: 100%\"\r\n                    placeholder=\"请选择\"\r\n                    clearable\r\n                  >\r\n                    <el-option\r\n                      v-for=\"dict in dict.type.technology_type\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.label\"\r\n                      :value=\"dict.label\"\r\n                    />\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"应用领域\" prop=\"applicationAreaList\">\r\n                  <el-select\r\n                    v-model=\"form.applicationAreaList\"\r\n                    filterable\r\n                    multiple\r\n                    allow-create\r\n                    style=\"width: 100%\"\r\n                    placeholder=\"请选择\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in dict.type.application_area\"\r\n                      :key=\"item.value\"\r\n                      :label=\"item.label\"\r\n                      :value=\"item.label\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n\r\n                  <!-- <el-tag\r\n                    v-for=\"tag in form.applicationAreaList\"\r\n                    closable\r\n                    class=\"add-demand-tag\"\r\n                    :key=\"tag\"\r\n                    :disable-transitions=\"false\"\r\n                    @close=\"handleClose(tag)\"\r\n                  >\r\n                    {{ tag }}\r\n                  </el-tag> -->\r\n                  <!-- <el-input v-model=\"applicationsInput\" :maxlength=\"255\">\r\n                  </el-input>\r\n                  <el-button\r\n                    size=\"small\"\r\n                    icon=\"el-icon-plus\"\r\n                    class=\"add-demand-btn-tag\"\r\n                    @click=\"handleInputConfirm\"\r\n                    >新增</el-button\r\n                  > -->\r\n                </el-form-item>\r\n                <el-form-item label=\"合作方式\" prop=\"cooperationModeName\">\r\n                  <el-select\r\n                    v-model=\"form.cooperationModeName\"\r\n                    placeholder=\"请选择\"\r\n                    @change=\"cooperationModeChange\"\r\n                    clearable\r\n                  >\r\n                    <el-option\r\n                      v-for=\"dict in dict.type.cooperation_mode\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.label\"\r\n                      :value=\"dict.value\"\r\n                    />\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"产品阶段\" prop=\"productStageName\">\r\n                  <el-select\r\n                    v-model=\"form.productStageName\"\r\n                    placeholder=\"请选择\"\r\n                    clearable\r\n                    @change=\"productStageChanged\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"dict in dict.type.product_stage\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.label\"\r\n                      :value=\"dict.label\"\r\n                    />\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"产品图片\">\r\n                  <el-upload\r\n                    list-type=\"picture-card\"\r\n                    :headers=\"headers\"\r\n                    :action=\"uploadUrl\"\r\n                    :file-list=\"form.productPhotoList\"\r\n                    :accept=\"accept\"\r\n                    :before-upload=\"handleBeforeUpload\"\r\n                    :on-preview=\"handlePictureCardPreview\"\r\n                    :on-remove=\"handleRemove\"\r\n                    :on-success=\"handleSuccess\"\r\n                  >\r\n                    <i class=\"el-icon-plus\"></i>\r\n                  </el-upload>\r\n                  <el-dialog\r\n                    append-to-body\r\n                    :visible.sync=\"imgVisible\"\r\n                    :close-on-click-modal=\"false\"\r\n                  >\r\n                    <img width=\"100%\" :src=\"imageUrl\" alt=\"\" />\r\n                  </el-dialog>\r\n                </el-form-item>\r\n                <el-form-item label=\"附件\" prop=\"enclosure\">\r\n                  <FileUpload v-model=\"form.enclosureList\" />\r\n                </el-form-item>\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n                      <el-input\r\n                        disabled\r\n                        v-model=\"form.companyName\"\r\n                        placeholder=\"请输入公司名称\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"联系人\" prop=\"contactsName\">\r\n                      <el-input\r\n                        disabled\r\n                        v-model=\"form.contactsName\"\r\n                        placeholder=\"请输入联系人\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"联系电话\" prop=\"contactsMobile\">\r\n                      <el-input\r\n                        disabled\r\n                        v-model=\"form.contactsMobile\"\r\n                        placeholder=\"请选择联系电话\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"changeMode\">返回</el-button>\r\n                <!-- <el-button type=\"error\" @click=\"changeMode(0)\"\r\n                  >暂存草稿</el-button\r\n                > -->\r\n                <el-button type=\"danger\" @click=\"submitForm(1)\"\r\n                  >提交审核</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport { getApplyDetail, createApply, editApply } from \"@/api/system/apply\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport FileUpload from \"@/components/FileUpload\";\r\nimport { demandAdd, keywordList } from \"@/api/zhm\";\r\nimport store from \"@/store\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  name: \"CompanyApplyDetail\",\r\n  dicts: [\r\n    \"technology_type\",\r\n    \"cooperation_mode\",\r\n    \"product_stage\",\r\n    \"supply_type\",\r\n    \"application_area\",\r\n  ],\r\n  components: { UserMenu, FileUpload },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      title: \"供给详情\",\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      isCreate: false,\r\n      imgVisible: false,\r\n      keywords: [],\r\n      imageUrl: \"\",\r\n      applicationsInput: \"\",\r\n      info: {},\r\n      form: {},\r\n      accountLicenceList: [],\r\n      user: {\r\n        tel: store.getters.tel,\r\n        name: store.getters.name,\r\n        companyName: store.getters.companyName,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        phonenumber: store.getters.phonenumber,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        supplyType: [\r\n          { required: true, message: \"请选择供给类型\", trigger: \"change\" },\r\n        ],\r\n        supplyName: [\r\n          { required: true, message: \"供给标题不能为空\", trigger: \"blur\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"供给描述不能为空\", trigger: \"blur\" },\r\n        ],\r\n        technologyType: [\r\n          { required: true, message: \"技术类别不能为空\", trigger: \"change\" },\r\n        ],\r\n\r\n        applicationAreaList: [\r\n          { required: true, message: \"应用领域不能为空\", trigger: \"change\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.isCreate = this.$route.query.type == 1;\r\n    if (this.isCreate) {\r\n      this.goCreate();\r\n    } else {\r\n      this.getDetail();\r\n    }\r\n  },\r\n  methods: {\r\n    initForm() {\r\n      this.form = {\r\n        applicationArea: [],\r\n        productPhoto: [],\r\n        applicationAreaList: [],\r\n        productPhotoList: [],\r\n        supplyType: [],\r\n        keywords: [],\r\n        cooperationModeName: \"双方协商\",\r\n        cooperationMode: \"1\",\r\n        auditStatus: \"1\",\r\n        displayStatus: \"2\",\r\n        publisherName: this.user.name,\r\n        publisherMobile: this.user.tel,\r\n        technologyType: [],\r\n      };\r\n    },\r\n    getDetail() {\r\n      let id = this.$route.query.id;\r\n      getApplyDetail(id).then((response) => {\r\n        let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n        let decrypt = CryptoJS.AES.decrypt(response, key, {\r\n          mode: CryptoJS.mode.ECB,\r\n          padding: CryptoJS.pad.Pkcs7,\r\n        });\r\n        response = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    getUrl(str) {\r\n      console.log(str);\r\n      if (str) {\r\n        var list = JSON.parse(str);\r\n        if (list.length > 0) {\r\n          return list[0].url;\r\n        }\r\n      }\r\n\r\n      return null;\r\n    },\r\n    // 应用领域新增\r\n    handleInputConfirm() {\r\n      let val = this.applicationsInput;\r\n      if (val) {\r\n        if (!this.form.applicationAreaList) {\r\n          this.form.applicationAreaList = [];\r\n        }\r\n        this.form.applicationAreaList.push(val);\r\n      }\r\n      this.applicationsInput = \"\";\r\n    },\r\n    // 应用领域移除\r\n    handleClose(tag) {\r\n      this.form.applicationAreaList.splice(\r\n        this.form.applicationAreaList.indexOf(tag),\r\n        1\r\n      );\r\n    },\r\n    handleSummaryClose(tag) {\r\n      this.form.keywords.splice(this.form.keywords.indexOf(tag), 1);\r\n    },\r\n    cooperationModeChange(res) {\r\n      this.form.cooperationMode = res;\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePictureCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.form.productPhotoList = fileList;\r\n    },\r\n    handleSuccess(res, file) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        if (this.form.productPhotoList == null) {\r\n          this.form.productPhotoList = [];\r\n        }\r\n        this.form.productPhotoList.push(res.data);\r\n      }\r\n    },\r\n    changeMode() {\r\n      if (this.isCreate) {\r\n        this.goBack();\r\n        return;\r\n      }\r\n      if (this.isDetail) {\r\n        this.title = \"编辑供给\";\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n        if (this.info.applicationArea) {\r\n          this.form.applicationAreaList = this.info.applicationArea.split(\",\");\r\n        } else {\r\n          this.form.applicationAreaList = [];\r\n        }\r\n        if (this.info.supplyType) {\r\n          this.form.supplyType = this.info.supplyType.split(\",\");\r\n        } else {\r\n          this.form.supplyType = [];\r\n        }\r\n        if (this.info.technologyType) {\r\n          this.form.technologyType = this.info.technologyType.split(\",\");\r\n        }\r\n        if (this.info.keywords) {\r\n          this.form.keywords = this.info.keywords.split(\",\");\r\n        }\r\n        if (this.info.enclosure) {\r\n          this.form.enclosureList = JSON.parse(this.info.enclosure);\r\n        }\r\n        if (this.info.productPhoto) {\r\n          this.form.productPhotoList = JSON.parse(this.info.productPhoto);\r\n        }\r\n      } else {\r\n        this.isDetail = true;\r\n        this.title = \"供给详情\";\r\n        this.initForm();\r\n      }\r\n      this.getDetail();\r\n    },\r\n    productStageChanged(res) {\r\n      this.dict.type.product_stage.forEach((item) => {\r\n        if (item.label == res) {\r\n          this.form.productStage = item.value;\r\n        }\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      const { summary } = this.form;\r\n      if (summary) {\r\n        keywordList(summary).then((res) => {\r\n          const { code, data, msg } = res;\r\n          if (code === 200) {\r\n            this.form.keywords = data;\r\n          } else {\r\n            this.$message.error(msg);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning(\"请输入需求描述\");\r\n      }\r\n    },\r\n    goCreate() {\r\n      this.title = \"新增供给\";\r\n      this.isDetail = false;\r\n      this.initForm();\r\n      this.form.companyName = this.user.companyName;\r\n      this.form.contactsName = this.user.name;\r\n      this.form.contactsMobile = this.user.phonenumber;\r\n      this.form.publisherName = this.user.name;\r\n      this.form.publisherMobile = this.user.phonenumber;\r\n      this.form.businessNo = this.user.bussinessNo;\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (\r\n            this.form.applicationAreaList &&\r\n            this.form.applicationAreaList.length > 0\r\n          ) {\r\n            this.form.applicationArea = this.form.applicationAreaList.join(\",\");\r\n          } else {\r\n            this.form.applicationArea = \"\";\r\n          }\r\n          if (this.form.technologyType && this.form.technologyType.length > 0) {\r\n            this.form.technologyType = this.form.technologyType.join(\",\");\r\n          } else {\r\n            this.form.technologyType = \"\";\r\n          }\r\n          if (this.form.supplyType && this.form.supplyType.length > 0) {\r\n            this.form.supplyType = this.form.supplyType.join(\",\");\r\n          }\r\n          this.form.productPhoto = JSON.stringify(this.form.productPhotoList);\r\n          this.form.enclosure = JSON.stringify(this.form.enclosureList);\r\n          if (this.form.keywords && this.form.keywords.length > 0) {\r\n            this.form.keywords = this.form.keywords.join(\",\");\r\n          } else {\r\n            this.form.keywords = \"\";\r\n          }\r\n          this.form.businessNo = this.user.bussinessNo;\r\n\r\n          console.log(this.form);\r\n          if (this.isCreate) {\r\n            createApply({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          } else {\r\n            this.form.auditStatus = 1;\r\n            editApply({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    toZiyuan() {\r\n      this.$router.push({\r\n        path: \"/user/companyDemandDetail1\",\r\n        query: { key: JSON.stringify(this.info) },\r\n      });\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        console.log(res);\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .company-demand-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 36px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .mt_40 {\r\n          margin-top: 40px;\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 90%;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-select .el-input .el-select__caret {\r\n          color: transparent;\r\n        }\r\n        .el-textarea__inner {\r\n          width: 90%;\r\n        }\r\n        .add-demand-tag {\r\n          margin-right: 10px;\r\n          height: 32px;\r\n          line-height: 32px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n          color: #333;\r\n        }\r\n        .el-button:hover,\r\n        .el-button:focus {\r\n          border-color: #d9d9d9;\r\n          background-color: #fff;\r\n        }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8 !important;\r\n          border-color: #21c9b8 !important;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}