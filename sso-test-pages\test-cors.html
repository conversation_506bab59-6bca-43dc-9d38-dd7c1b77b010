<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CORS测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 CORS跨域测试页面</h1>
        
        <div class="test-section info">
            <h3>📋 测试说明</h3>
            <p>此页面用于测试SSO服务的CORS跨域配置是否正确。</p>
            <p><strong>当前页面地址:</strong> <span id="currentUrl"></span></p>
        </div>

        <div class="test-section">
            <h3>🚀 快速测试</h3>
            <button onclick="testAllPorts()">测试所有端口</button>
            <button onclick="clearResults()">清空结果</button>
            <div id="testResults" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🎯 单独测试</h3>
            <button onclick="testPort(9100)">测试9100端口 (SSO服务)</button>
            <button onclick="testPort(9200)">测试9200端口 (主系统)</button>
            <button onclick="testPort(9700)">测试9700端口 (从系统)</button>
            <div id="singleResults" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 详细信息</h3>
            <div id="detailResults" class="result"></div>
        </div>
    </div>

    <script>
        // 显示当前页面地址
        document.getElementById('currentUrl').textContent = window.location.href;

        const testPorts = [
            { port: 9100, name: 'SSO认证服务', path: '/sso/status' },
            { port: 9200, name: '主系统认证服务', path: '/sso/status' },
            { port: 9700, name: '从系统认证服务', path: '/sso/status' }
        ];

        async function testPort(port) {
            const portInfo = testPorts.find(p => p.port === port) || { port, name: '未知服务', path: '/sso/status' };
            const url = `http://localhost:${port}${portInfo.path}`;
            
            log(`🔍 测试端口 ${port} (${portInfo.name})...`);
            
            try {
                const startTime = Date.now();
                const response = await fetch(url, {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const endTime = Date.now();
                
                const result = {
                    port,
                    name: portInfo.name,
                    url,
                    status: response.status,
                    statusText: response.statusText,
                    success: response.ok,
                    responseTime: endTime - startTime,
                    headers: {}
                };

                // 获取响应头
                for (let [key, value] of response.headers.entries()) {
                    result.headers[key] = value;
                }

                if (response.ok) {
                    try {
                        const data = await response.json();
                        result.data = data;
                    } catch (e) {
                        result.data = 'Non-JSON response';
                    }
                }

                logResult(result);
                return result;
                
            } catch (error) {
                const result = {
                    port,
                    name: portInfo.name,
                    url,
                    success: false,
                    error: error.message,
                    errorType: error.name
                };
                
                logResult(result);
                return result;
            }
        }

        async function testAllPorts() {
            clearResults();
            log('🚀 开始测试所有端口的CORS配置...');
            
            const results = [];
            for (const portInfo of testPorts) {
                const result = await testPort(portInfo.port);
                results.push(result);
                await new Promise(resolve => setTimeout(resolve, 500)); // 延迟500ms
            }
            
            // 显示汇总
            showSummary(results);
        }

        function logResult(result) {
            const resultDiv = document.getElementById('singleResults');
            const timestamp = new Date().toLocaleTimeString();
            
            if (result.success) {
                resultDiv.innerHTML += `
                    <div style="color: green; margin: 5px 0;">
                        ✅ [${timestamp}] ${result.name} (${result.port}) - 成功
                        <br>&nbsp;&nbsp;&nbsp;&nbsp;状态: ${result.status} | 响应时间: ${result.responseTime}ms
                    </div>
                `;
            } else {
                resultDiv.innerHTML += `
                    <div style="color: red; margin: 5px 0;">
                        ❌ [${timestamp}] ${result.name} (${result.port}) - 失败
                        <br>&nbsp;&nbsp;&nbsp;&nbsp;错误: ${result.error || '服务不可用'}
                    </div>
                `;
            }
        }

        function showSummary(results) {
            const detailDiv = document.getElementById('detailResults');
            const successCount = results.filter(r => r.success).length;
            const totalCount = results.length;
            
            let html = `
                <h4>📊 测试汇总</h4>
                <p><strong>成功:</strong> ${successCount}/${totalCount}</p>
                <h4>📋 详细结果:</h4>
            `;
            
            results.forEach(result => {
                html += `
                    <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 3px;">
                        <h5>${result.name} (${result.port})</h5>
                        <p><strong>URL:</strong> ${result.url}</p>
                        <p><strong>状态:</strong> ${result.success ? '✅ 成功' : '❌ 失败'}</p>
                        ${result.success ? `
                            <p><strong>HTTP状态:</strong> ${result.status} ${result.statusText}</p>
                            <p><strong>响应时间:</strong> ${result.responseTime}ms</p>
                            <p><strong>CORS头:</strong></p>
                            <pre>${JSON.stringify(result.headers, null, 2)}</pre>
                        ` : `
                            <p><strong>错误:</strong> ${result.error}</p>
                            <p><strong>错误类型:</strong> ${result.errorType}</p>
                        `}
                    </div>
                `;
            });
            
            detailDiv.innerHTML = html;
        }

        function log(message) {
            const resultDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('singleResults').innerHTML = '';
            document.getElementById('detailResults').innerHTML = '';
        }

        // 页面加载时显示当前环境信息
        window.onload = function() {
            log('📍 页面已加载，准备进行CORS测试');
            log(`🌐 当前页面协议: ${window.location.protocol}`);
            log(`🏠 当前页面主机: ${window.location.host}`);
        };
    </script>
</body>
</html>
