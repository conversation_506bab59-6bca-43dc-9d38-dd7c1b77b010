var parent = require('../../stable/typed-array');
require('../../modules/esnext.typed-array.find-last');
require('../../modules/esnext.typed-array.find-last-index');
require('../../modules/esnext.typed-array.to-reversed');
require('../../modules/esnext.typed-array.to-sorted');
// TODO: Remove from `core-js@4`
require('../../modules/esnext.typed-array.to-spliced');
require('../../modules/esnext.typed-array.with');

module.exports = parent;
