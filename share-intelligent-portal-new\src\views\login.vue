<!--
 * @Author: jhy
 * @Date: 2023-01-28 09:15:15
 * @LastEditors: JHY
 * @LastEditTime: 2023-02-15 14:46:27
-->
<template>
  <div class="login-container">
    <div class="title" @click="toIndex">
      <div class="titLeft">
        <img src="@/assets/images/home/<USER>" alt="" />
      </div>
      <div class="titRight">易复材共享智造工业互联网平台</div>
    </div>
    <div class="login_content">
      <div class="left_img"></div>
      <div
        :class="{
          'login-content-code': type === 'code',
          'login-content-account': type === 'account',
          'login-content-set': type === 'set',
        }"
      >
        <div class="login-info">
          <!-- <div class="login-project-name">星碳生态平台</div> -->
          <div class="login-box">
            <div class="login-tab">
              <div
                class="tabStyle"
                v-show="type == 'account' || type == 'code'"
              >
                <div
                  class="tab_left"
                  :style="
                    type == 'code'
                      ? 'color: #21C9B8;border-bottom: 3px solid #21C9B8;'
                      : ''
                  "
                  @click="switchLogin('code')"
                >
                  验证码登录
                </div>
                <div
                  class="tab_right"
                  :style="
                    type == 'account'
                      ? 'color: #21C9B8;border-bottom: 3px solid #21C9B8;'
                      : ''
                  "
                  @click="switchLogin('account')"
                >
                  密码登录
                </div>
              </div>
              <div
                v-show="type == 'set'"
                style="width: 100%; text-align: center"
              >
                设置密码
                <!-- {{
                  type === "account"
                    ? "账号密码登录"
                    : type === "code"
                    ? "验证码登录"
                    : "设置密码"
                }} -->
              </div>
            </div>
            <el-form ref="form" :model="form" :rules="rules">
              <el-form-item prop="username">
                <el-input
                  v-model="form.username"
                  autocomplete="off"
                  auto-complete="new-password"
                  placeholder="请输入手机号"
                  class="form-input-style"
                  :maxlength="11"
                >
                  <img
                    slot="prefix"
                    src="../assets/login/mobile.png"
                    alt=""
                    style="width: 12px; height: 16px; margin: 12px"
                  />
                </el-input>
              </el-form-item>
              <!-- 验证码登录 -->
              <el-form-item
                v-if="type !== 'account'"
                prop="smsCode"
                class="form-item-style"
              >
                <verification-code
                  v-model="form.smsCode"
                  :mobile="{ phone: form.username }"
                  :before-send-code="beforeSendCode"
                ></verification-code>
              </el-form-item>
              <!-- 账号密码登录、密码设置 -->
              <el-form-item
                v-if="type === 'account' || type === 'set'"
                prop="password"
                class="form-item-password-style"
              >
                <el-input
                  v-model="form.password"
                  type="password"
                  autocomplete="off"
                  auto-complete="new-password"
                  placeholder="请输入密码"
                  class="form-input-style"
                  :maxlength="11"
                >
                  <img
                    slot="prefix"
                    src="../assets/login/lockIcon.png"
                    alt=""
                    class="input-icon"
                  />
                </el-input>
              </el-form-item>
              <!-- 账号密码登录 -->
              <!-- <el-form-item
                v-if="type === 'account'"
                prop="code"
                class="form-item-style"
              >
                <el-input
                  v-model="form.code"
                  placeholder="请输入验证码"
                  auto-complete="off"
                  style="width: 63%"
                  class="form-input-img-style"
                  :maxlength="200"
                >
                  <img
                    slot="prefix"
                    src="../assets/login/imgCode.png"
                    alt=""
                    class="input-icon"
                  />
                </el-input>
                <div class="login-code">
                  <img
                    class="login-code-img"
                    :src="codeUrl"
                    @click="getCodeImg"
                  />
                </div>
              </el-form-item> -->
              <!-- 密码设置 -->
              <el-form-item
                v-if="type === 'set'"
                prop="password1"
                class="form-item-style"
              >
                <el-input
                  v-model="form.password1"
                  type="password"
                  autocomplete="off"
                  auto-complete="new-password"
                  placeholder="请确认密码"
                  class="form-input-style"
                  :maxlength="11"
                >
                  <img
                    slot="prefix"
                    src="../assets/login/lockIcon.png"
                    alt=""
                    class="input-icon"
                  />
                </el-input>
              </el-form-item>
              <el-form-item>
                <el-checkbox
                  v-model="agreement"
                  :true-label="1"
                  :false-label="0"
                >
                  <span class="login-agreement-text">已阅读并同意</span>
                  <el-button
                    type="text"
                    class="login-agreement-btn"
                    @click="viewProtocols"
                  >
                    《服务协议》
                  </el-button>
                </el-checkbox>
              </el-form-item>
            </el-form>
            <el-button
              type="primary"
              size="medium"
              class="button-area"
              @click="handleLogin"
              >登录</el-button
            >
            <div class="button-switch-box">
              <el-button
                v-if="type === 'account'"
                type="text"
                class="button-switch-style"
                @click="switchLogin('set')"
                >设置密码</el-button
              >
              <el-button
                v-if="type == 'set'"
                type="text"
                class="button-switch-style"
                @click="switchLogin('account')"
                >密码登录</el-button
              >
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 服务协议弹窗 -->
    <agreement-dialog :visible.sync="protocolsVisible"></agreement-dialog>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import VerificationCode from "@/components/verificationCode/";
import AgreementDialog from "./agreementDialog";

export default {
  components: {
    VerificationCode,
    AgreementDialog,
  },
  data() {
    return {
      form: {},
      codeUrl: "", //图形验证码图片
      type: "code", //账号类型 (code：验证码登录  account:账号密码登录  set:密码设置)
      agreement: 0, //协议
      protocolsVisible: false,
    };
  },
  created() {
    this.initForm();
  },
  computed: {
    rules() {
      let rules = {
        username: [
          {
            required: true,
            message: "请输入手机号",
            trigger: "blur",
          },
          { min: 11, message: "请输入11位手机号", trigger: "blur" },
        ],
        smsCode: [
          { required: true, message: "请输入验证码", trigger: "change" },
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback();
              } else if (value.length !== 6) {
                callback(new Error("验证码格式不正确"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        code: [{ required: true, trigger: "change", message: "请输入验证码" }],
        password1: [
          { required: true, message: "请输入确认密码", trigger: "blur" },
          { min: 6, message: "请输入6-11位确认密码", trigger: "blur" },
          { max: 11, message: "请输入6-11位确认密码", trigger: "blur" },
          { validator: this.validatorPassword },
        ],
      };
      if (this.type === "account") {
        rules.password = [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 6, message: "请输入6-11位密码", trigger: "blur" },
          { max: 11, message: "请输入6-11位密码", trigger: "blur" },
        ];
      } else if (this.type === "set") {
        rules.password = [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 6, message: "请输入6-11位密码", trigger: "blur" },
          { max: 11, message: "请输入6-11位密码", trigger: "blur" },
          { validator: this.validatorPassword },
        ];
      }
      return rules;
    },
  },
  watch: {
    "form.password"() {
      if (this.form.password1 && this.form.password === this.form.password1) {
        this.$refs.form.validateField("password1");
      }
    },
    "form.password1"() {
      if (this.form.password1 && this.form.password === this.form.password1) {
        this.$refs.form.validateField("password");
      }
    },
  },
  methods: {
    initForm() {
      this.form = {
        username: "", //账号
        smsCode: "", //短信验证码
        password: "", //密码
        password1: "", //确认密码
        code: "", //图形验证码
        uuid: "",
      };
    },
    // 切换登录方式
    switchLogin(val) {
      if (val === "account") {
        this.getCodeImg();
      }
      this.initForm();
      this.type = val;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    // 获取图形验证码
    getCodeImg() {
      getCodeImg().then((res) => {
        this.codeUrl = "data:image/gif;base64," + res.img;
        this.form.uuid = res.uuid;
      });
    },
    // 密码校验
    validatorPassword(rule, value, callback) {
      let password = this.form.password;
      let password1 = this.form.password1;
      if (password && password1 && password !== password1) {
        callback(new Error("密码输入不一致，请重新输入"));
      } else {
        callback();
      }
    },
    beforeSendCode() {
      return new Promise((resolve, reject) => {
        this.$refs.form.validateField("username", (errorMessage) => {
          errorMessage ? reject() : resolve();
        });
      });
    },
    // 打开服务协议弹窗
    viewProtocols() {
      this.protocolsVisible = true;
    },
    // 登录
    handleLogin() {
      if (this.agreement !== 1) {
        this.$message({
          message: "请阅读并同意《服务协议》",
          type: "warning",
        });
        return;
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          let obj = { ...this.form, type: this.type };
          this.loading = true;
          this.$store
            .dispatch("Login", obj)
            .then(() => {
              this.$router.push({ path: this.redirect || "/" }).catch(() => {});
            })
            .catch(() => {
              if (this.type === "account") {
                this.form.code = "";
                this.getCodeImg();
                this.$nextTick(() => {
                  this.$refs.form.clearValidate("code");
                });
              }
              this.loading = false;
            });
        }
      });
    },
    authentication() {
      window.location.href =
        "https://qyzhfw.chengyang.gov.cn/sso/login?redirectUrl=https://qyfw.chengyang.gov.cn/index";
      // window.location.href =
      //   "https://qyzhfw.chengyang.gov.cn/sso/login?redirectUrl=http://localhost/index";
    },
    toIndex(){
      this.$router.push({ path: "/" })
    }
  },
};
</script>

<style lang="scss" scoped>
.login-container {
  width: 100%;
  height: 100%;
  background: url("../assets/login/background.png") no-repeat;
  background-size: 100% 100%;
  padding-top: calc((100vh - 580px) / 2);
  position: relative;
  .title {
    display: flex;
    position: absolute;
    left: 2%;
    top: 72px;
    width: 15%;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    .titLeft {
      width: 60px;
      height: 50px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .titRight {
      font-size: 24px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      color: #000000;
      margin-left: 1%;
      width: 210px;
    }
  }
  .left_img {
    width: 50%;
    height: 367px;
    position: absolute;
    top: 95px;
    left: 3.3%;
    background: url("../assets/login/image.png") no-repeat;
    background-size: 100% 100%;
  }
  .login_content {
    position: relative;
    width: 62.5%;
    height: 580px;
    margin-left: 18.75%;
    // margin-top: 100px;
    // left: 18.75%;
    // top: calc((100vh - 580px) / 2);
    background: url("../assets/login/background1.png") no-repeat;
    background-size: 100% 100%;
  }
  .login-logo {
    width: 139px;
    height: 48px;
    position: absolute;
    top: 4.07%;
    left: 2.6%;
  }
  .login-background1 {
    position: absolute;
    top: 24.07%;
    left: 9.01%;
    width: 1638px;
    height: 613px;
  }
  .login-content-code {
    position: absolute;
    top: calc((100% - 400px) / 2);
    // top: 28.33%;
    right: 3.7%; // 21.46
    display: flex;
    justify-content: space-between;
    height: 470px;
  }
  .login-content-account {
    position: absolute;
    top: calc((100% - 465px) / 2);
    // top: 25.37%;
    right: 3.7%; // 21.46
    display: flex;
    justify-content: space-between;
    height: 535px;
  }
  .login-content-set {
    position: absolute;
    top: calc((100% - 530px) / 2);
    // top: 22.4%;
    right: 3.7%; // 21.46
    display: flex;
    justify-content: space-between;
    height: 600px;
  }
  .login-background2-code {
    width: 503px;
    height: 438px;
    margin-top: 22px;
  }
  .login-background2-account {
    width: 503px;
    height: 438px;
    margin-top: 49px;
  }
  .login-background2-set {
    width: 503px;
    height: 438px;
    margin-top: 77px;
  }
  .login-info {
    width: 393px;
    // width: 464px;
    .login-project-name {
      height: 70px;
      font-size: 22px;
      font-family: AlibabaPuHuiTi_2_85_Bold;
      color: #fff;
      line-height: 70px;
      text-align: center;
      // margin-bottom: 36px;
      // background: rgb(41, 92, 233);
      background: linear-gradient(
        to right,
        rgb(83, 140, 241),
        rgb(41, 92, 233)
      );
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
    }
    .login-box {
      width: 100%;
      background: #fff;
      box-shadow: 0px 10px 30px 0px #********;
      padding: 0 32px;
      .login-tab {
        display: flex;
        align-items: center;
        font-size: 20px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 24px;
        text-align: center;
        padding: 40px 0 32px;
        .tabStyle {
          display: flex;
          justify-content: center;
          width: 100%;
          .tab_left {
            width: 91px;
            height: 35px;
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            cursor: pointer;
          }
          .tab_right {
            margin-left: 60px;
            width: 73px;
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            cursor: pointer;
          }
        }
      }
      .form-item-password-style {
        margin-top: 24px;
      }
      .form-item-style {
        margin: 24px 0 15px;
        .login-code {
          width: 33%;
          height: 40px;
          float: right;
          img {
            width: 100%;
            height: 100%;
            cursor: pointer;
            vertical-align: middle;
          }
        }
      }
      .input-icon {
        width: 16px;
        height: 16px;
        margin: 12px;
      }
      .login-agreement-text {
        font-family: PingFangSC-Regular, PingFang SC;
        color: #333;
        line-height: 14px;
      }
      .login-agreement-btn {
        color: #21c9b8;
        font-family: PingFangSC-Regular, PingFang SC;
        line-height: 14px;
      }
      .button-area {
        margin: 7px 0 26px;
        width: 330px;
        height: 40px;
        background: #21c9b8;
        border-color: #21c9b8;
        border-radius: 4px;
        font-size: 16px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
      }
      .button-switch-box {
        display: flex;
        justify-content: right;
        align-items: center;
        padding: 0 0 18px 69px;
        .button-switch-style {
          font-family: PingFangSC-Regular, PingFang SC;
          color: #21c9b8;
          line-height: 14px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.login-container {
  .login-box {
    .form-input-style {
      .el-input__inner {
        // width: 400px;
        height: 40px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        line-height: 14px;
        padding-left: 40px;
      }
    }
    .el-form-item__error {
      background: url("../assets/login/warningIcon.png") no-repeat;
      background-size: 16px 16px;
      padding-left: 18px;
      padding-top: 3px;
      margin-top: 2px;
      height: 16px;
      color: #f05642;
    }
    .form-input-img-style {
      .el-input__inner {
        height: 40px;
        background: #fff;
        border-radius: 4px;
        border: 1px solid #d9d9d9;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        line-height: 14px;
        padding-left: 40px;
      }
    }
    .el-checkbox__inner {
      width: 16px;
      height: 16px;
      margin-top: -1px;
      border-color: #d9d9d9;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner::after {
      margin-top: 0.5px;
      margin-left: 1px;
    }
    .el-checkbox__inner::after {
      margin-top: 0.5px;
      margin-left: 1px;
    }
    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #21c9b8;
      border-color: #21c9b8;
    }
    .el-checkbox__inner:hover {
      border-color: #21c9b8;
    }
  }
}
</style>
