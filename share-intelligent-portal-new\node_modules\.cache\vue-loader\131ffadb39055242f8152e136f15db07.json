{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\activitySquare\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\activitySquare\\detail.vue", "mtime": 1750311962983}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRBY3Rpdml0eURldGFpbCwgYWRkQWN0aXZpdHlFbnJvbGwgfSBmcm9tICJAL2FwaS9wdXJjaGFzZVNhbGVzIjsNCmltcG9ydCB7IG1hcEdldHRlcnMgfSBmcm9tICJ2dWV4IjsNCmltcG9ydCB7IGdldEluZm8gfSBmcm9tICJAL2FwaS9sb2dpbiI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBkYXRhOiB7fSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuaW5pdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaW5pdCgpIHsNCiAgICAgIHRoaXMudG9rZW4gPyB0aGlzLmdldEluZm8oKSA6IHRoaXMuZ2V0QWN0aXZpdHlEZXRhaWwoKTsNCiAgICB9LA0KICAgIC8vIOivpuaDheaOpeWPow0KICAgIGdldEFjdGl2aXR5RGV0YWlsKGlkKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGV0IHVzZXJJZCA9IGlkID8gaWQgOiBudWxsOw0KICAgICAgZ2V0QWN0aXZpdHlEZXRhaWwoeyBpZDogdGhpcy4kcm91dGUucXVlcnkuaWQsIHVzZXJJZDogdXNlcklkIH0pDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmRhdGEgPSByZXMuZGF0YSB8fCB7fTsNCiAgICAgICAgICBpZiAodGhpcy5kYXRhLmV4cGVydExpc3QgJiYgdGhpcy5kYXRhLmV4cGVydExpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy5kYXRhLmV4cGVydExpc3QuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgICBpdGVtLnRlY2huaXF1ZVR5cGVOYW1lID0gaXRlbS50ZWNobmlxdWVUeXBlTmFtZS5zcGxpdCgiLCIpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmICh0aGlzLmRhdGEuZGVtYW5kTGlzdCAmJiB0aGlzLmRhdGEuZGVtYW5kTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICB0aGlzLmRhdGEuZGVtYW5kTGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICAgIGl0ZW0uc2NlbmVQaWN0dXJlID0gSlNPTi5wYXJzZShpdGVtLnNjZW5lUGljdHVyZSk7DQogICAgICAgICAgICAgIGl0ZW0uYXBwbGljYXRpb25BcmVhID0gaXRlbS5hcHBsaWNhdGlvbkFyZWEuc3BsaXQoIiwiKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAodGhpcy5kYXRhLnN1cHBseUxpc3QgJiYgdGhpcy5kYXRhLnN1cHBseUxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy5kYXRhLnN1cHBseUxpc3QuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgICBpdGVtLnByb2R1Y3RQaG90byA9IEpTT04ucGFyc2UoaXRlbS5wcm9kdWN0UGhvdG8pOw0KICAgICAgICAgICAgICBpdGVtLmFwcGxpY2F0aW9uQXJlYSA9IGl0ZW0uYXBwbGljYXRpb25BcmVhLnNwbGl0KCIsIik7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0SW5mbygpIHsNCiAgICAgIGdldEluZm8oKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgbGV0IGlkID0gcmVzLnVzZXIudXNlcklkIHx8IG51bGw7DQogICAgICAgIHRoaXMuZ2V0QWN0aXZpdHlEZXRhaWwoaWQpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDml7bpl7TlsZXnpLoNCiAgICBnZXRUaW1lKCkgew0KICAgICAgbGV0IGluZm8gPSAiLS0iOw0KICAgICAgaWYgKHRoaXMuZGF0YS5zdGFydFRpbWUgJiYgdGhpcy5kYXRhLmVuZFRpbWUpIHsNCiAgICAgICAgaW5mbyA9IGAke3RoaXMuZGF0YS5zdGFydFRpbWV96IezJHt0aGlzLmRhdGEuZW5kVGltZX1gOw0KICAgICAgfSBlbHNlIGlmICh0aGlzLmRhdGEuc3RhcnRUaW1lIHx8IHRoaXMuZGF0YS5lbmRUaW1lKSB7DQogICAgICAgIGluZm8gPSB0aGlzLmRhdGEuc3RhcnRUaW1lIHx8IHRoaXMuZGF0YS5lbmRUaW1lOw0KICAgICAgfQ0KICAgICAgcmV0dXJuIGluZm87DQogICAgfSwNCiAgICAvLyDot7PovazliLDkuJPlrrbor6bmg4XpobXpnaINCiAgICBnb0V4cGVydExpYnJhcnlEZXRhaWwoaWQpIHsNCiAgICAgIGxldCByb3V0ZURhdGEgPSB0aGlzLiRyb3V0ZXIucmVzb2x2ZSh7DQogICAgICAgIHBhdGg6ICIvZXhwZXJ0RGV0YWlsIiwNCiAgICAgICAgcXVlcnk6IHsgaWQgfSwNCiAgICAgIH0pOw0KICAgICAgd2luZG93Lm9wZW4ocm91dGVEYXRhLmhyZWYsICJfYmxhbmsiKTsNCiAgICB9LA0KICAgIC8vIOi3s+i9rOWIsOmcgOaxguivpuaDhemhtemdog0KICAgIGdvRGVtYW5kRGV0YWlsKGlkKSB7DQogICAgICBsZXQgcm91dGVEYXRhID0gdGhpcy4kcm91dGVyLnJlc29sdmUoew0KICAgICAgICBwYXRoOiAiL2RlbWFuZEhhbGxEZXRhaWwiLA0KICAgICAgICBxdWVyeTogeyBpZCB9LA0KICAgICAgfSk7DQogICAgICB3aW5kb3cub3Blbihyb3V0ZURhdGEuaHJlZiwgIl9ibGFuayIpOw0KICAgIH0sDQogICAgLy8g6Lez6L2s5Yiw6LWE5rqQ6K+m5oOF6aG16Z2iDQogICAgZ29SZXNvdXJjZURldGFpbChpZCkgew0KICAgICAgbGV0IHJvdXRlRGF0YSA9IHRoaXMuJHJvdXRlci5yZXNvbHZlKHsNCiAgICAgICAgcGF0aDogIi9yZXNvdXJjZUhhbGxEZXRhaWwiLA0KICAgICAgICBxdWVyeTogeyBpZCB9LA0KICAgICAgfSk7DQogICAgICB3aW5kb3cub3Blbihyb3V0ZURhdGEuaHJlZiwgIl9ibGFuayIpOw0KICAgIH0sDQogICAgLy8g56uL5Y2z5oql5ZCNDQogICAgc2lnblVwKCkgew0KICAgICAgLy8g5Yik5pat5piv5ZCm55m75b2VDQogICAgICBpZiAoIXRoaXMudG9rZW4pIHsNCiAgICAgICAgdGhpcy4kY29uZmlybSgi6K+35YWI55m75b2VIiwgIuaPkOekuiIsIHsNCiAgICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuWOu+eZu+W9lSIsDQogICAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgiTG9nT3V0IikudGhlbigoKSA9PiB7DQogICAgICAgICAgICBsb2NhdGlvbi5ocmVmID0gIi9sb2dpbiI7DQogICAgICAgICAgfSk7DQogICAgICAgIH0pOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLiRjb25maXJtKA0KICAgICAgICBg5oKo5Y+C5Yqg55qE5pivJHt0aGlzLmRhdGEuYWN0aXZpdHlOYW1lfSzor7fnoa7orqTjgILmiqXlkI3miJDlip/lkI7vvIzlubPlj7DlrqLmnI3kvJrkuI7mgqjlr7nmjqVgLA0KICAgICAgICAi5o+Q56S6IiwNCiAgICAgICAgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi5oiR6KaB5Y+C5YqgIiwNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0NCiAgICAgICkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgIGFkZEFjdGl2aXR5RW5yb2xsKHsgYWN0aXZpdHlJZDogdGhpcy4kcm91dGUucXVlcnkuaWQgfSkNCiAgICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5oql5ZCN5oiQ5YqfIik7DQogICAgICAgICAgICB0aGlzLmluaXQoKTsNCiAgICAgICAgICB9KQ0KICAgICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLi4ubWFwR2V0dGVycyhbInRva2VuIl0pLA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4LA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/purchaseSales/component/activitySquare", "sourcesContent": ["<template>\r\n  <div class=\"activity-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"activity-detail-banner\">\r\n      <img\r\n        src=\"../../../../assets/activitySquare/activityDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"activity-detail-title-box\">\r\n      <div class=\"activity-detail-divider\"></div>\r\n      <div class=\"activity-detail-title\">活动详情</div>\r\n      <div class=\"activity-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"activity-detail-card\">\r\n      <div class=\"activity-detail-content\">\r\n        <div class=\"activity-detail-headline\">\r\n          <div class=\"headline-title\">\r\n            {{ data.activityName }}\r\n          </div>\r\n          <div class=\"headline-time\">\r\n            {{ getTime() }}\r\n          </div>\r\n        </div>\r\n        <!-- 活动详情标题 -->\r\n        <div class=\"activity-detail-caption\" v-if=\"data.activityContent\">\r\n          <div class=\"caption-line\"></div>\r\n          <div class=\"caption-title\">活动详情</div>\r\n        </div>\r\n        <!-- 活动详情 -->\r\n        <div class=\"activity-detail-img\">\r\n          <img v-if=\"data.activityContent\" :src=\"data.activityContent\" alt=\"\" />\r\n        </div>\r\n        <!-- 相关专家标题 -->\r\n        <div\r\n          class=\"activity-detail-caption\"\r\n          v-if=\"data.expertList && data.expertList.length > 0\"\r\n        >\r\n          <div class=\"caption-line\"></div>\r\n          <div class=\"caption-title\">相关专家</div>\r\n        </div>\r\n        <!-- 相关专家 -->\r\n        <div class=\"activity-detai-list\">\r\n          <div\r\n            v-for=\"(item, index) in data.expertList\"\r\n            :key=\"index\"\r\n            class=\"list-item-content\"\r\n            @click=\"goExpertLibraryDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-box\">\r\n              <div class=\"item-headline\">\r\n                <div class=\"item-title\">\r\n                  {{ item.expertName }}\r\n                </div>\r\n              </div>\r\n              <div class=\"activity-detai-label\">\r\n                <div\r\n                  v-for=\"(val, index1) in item.techniqueTypeName\"\r\n                  :key=\"index1\"\r\n                  class=\"activity-label-item\"\r\n                >\r\n                  <span v-if=\"index1 < 2\" class=\"activity-label-type\">{{\r\n                    `#${val}`\r\n                  }}</span>\r\n                  <span v-else>…</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"activity-detai-box\">\r\n                {{ item.synopsis }}\r\n              </div>\r\n            </div>\r\n            <div class=\"list-item-img\">\r\n              <img v-if=\"item.headPortrait\" :src=\"item.headPortrait\" alt=\"\" />\r\n              <img\r\n                v-else\r\n                src=\"../../../../assets/expertLibrary/defaultImg.png\"\r\n                alt=\"\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 相关需求标题 -->\r\n        <div\r\n          class=\"activity-detail-caption\"\r\n          v-if=\"data.demandList && data.demandList.length > 0\"\r\n        >\r\n          <div class=\"caption-line\"></div>\r\n          <div class=\"caption-title\">相关需求</div>\r\n        </div>\r\n        <!-- 相关需求内容 -->\r\n        <div class=\"activity-demand-info\">\r\n          <div\r\n            v-for=\"(item, index) in data.demandList\"\r\n            :key=\"index\"\r\n            class=\"activity-demand-item\"\r\n            @click=\"goDemandDetail(item.id)\"\r\n          >\r\n            <div class=\"activity-item-img\">\r\n              <img\r\n                v-if=\"item.scenePicture && item.scenePicture.length > 0\"\r\n                :src=\"item.scenePicture[0].url\"\r\n                alt=\"\"\r\n              />\r\n              <img\r\n                v-else\r\n                src=\"../../../../assets/purchaseSales/demandDefault.png\"\r\n                alt=\"\"\r\n              />\r\n            </div>\r\n            <div class=\"activity-item-content\">\r\n              <div class=\"activity-item-title\">\r\n                {{ item.demandTitle }}\r\n              </div>\r\n              <div class=\"activity-item-content-tag\">\r\n                <div\r\n                  v-for=\"(val, num) in item.applicationArea\"\r\n                  :key=\"num\"\r\n                  class=\"activity-item-tag\"\r\n                >\r\n                  {{ val }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 相关供给标题 -->\r\n        <div\r\n          class=\"activity-detail-caption related-supply-title\"\r\n          v-if=\"data.supplyList && data.supplyList.length > 0\"\r\n        >\r\n          <div class=\"caption-line\"></div>\r\n          <div class=\"caption-title\">相关供给</div>\r\n        </div>\r\n        <!-- 相关供给内容 -->\r\n        <div class=\"activity-demand-info\">\r\n          <div\r\n            v-for=\"(item, index) in data.supplyList\"\r\n            :key=\"index\"\r\n            class=\"activity-demand-item\"\r\n            @click=\"goResourceDetail(item.id)\"\r\n          >\r\n            <div class=\"activity-item-img\">\r\n              <img\r\n                v-if=\"item.productPhoto && item.productPhoto.length > 0\"\r\n                :src=\"item.productPhoto[0].url\"\r\n                alt=\"\"\r\n              />\r\n              <img\r\n                v-else\r\n                src=\"../../../../assets/purchaseSales/resourceDefault.png\"\r\n                alt=\"\"\r\n              />\r\n            </div>\r\n            <div class=\"activity-item-content\">\r\n              <div class=\"activity-item-title\">\r\n                {{ item.supplyName }}\r\n              </div>\r\n              <div class=\"activity-item-content-tag\">\r\n                <div\r\n                  v-for=\"(val, num) in item.applicationArea\"\r\n                  :key=\"num\"\r\n                  class=\"activity-item-tag\"\r\n                >\r\n                  {{ val }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 立即报名按钮 -->\r\n        <div class=\"activity-area-btn\">\r\n          <el-button\r\n            v-if=\"data.isEnroll === 1\"\r\n            disabled\r\n            type=\"info\"\r\n            class=\"activity-disabled-btn\"\r\n            >您已报名</el-button\r\n          >\r\n          <el-button v-else class=\"activity-sign-up\" @click=\"signUp\"\r\n            >立即报名</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getActivityDetail, addActivityEnroll } from \"@/api/purchaseSales\";\r\nimport { mapGetters } from \"vuex\";\r\nimport { getInfo } from \"@/api/login\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.token ? this.getInfo() : this.getActivityDetail();\r\n    },\r\n    // 详情接口\r\n    getActivityDetail(id) {\r\n      this.loading = true;\r\n      let userId = id ? id : null;\r\n      getActivityDetail({ id: this.$route.query.id, userId: userId })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          if (this.data.expertList && this.data.expertList.length > 0) {\r\n            this.data.expertList.forEach((item) => {\r\n              item.techniqueTypeName = item.techniqueTypeName.split(\",\");\r\n            });\r\n          }\r\n          if (this.data.demandList && this.data.demandList.length > 0) {\r\n            this.data.demandList.forEach((item) => {\r\n              item.scenePicture = JSON.parse(item.scenePicture);\r\n              item.applicationArea = item.applicationArea.split(\",\");\r\n            });\r\n          }\r\n          if (this.data.supplyList && this.data.supplyList.length > 0) {\r\n            this.data.supplyList.forEach((item) => {\r\n              item.productPhoto = JSON.parse(item.productPhoto);\r\n              item.applicationArea = item.applicationArea.split(\",\");\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        let id = res.user.userId || null;\r\n        this.getActivityDetail(id);\r\n      });\r\n    },\r\n    // 时间展示\r\n    getTime() {\r\n      let info = \"--\";\r\n      if (this.data.startTime && this.data.endTime) {\r\n        info = `${this.data.startTime}至${this.data.endTime}`;\r\n      } else if (this.data.startTime || this.data.endTime) {\r\n        info = this.data.startTime || this.data.endTime;\r\n      }\r\n      return info;\r\n    },\r\n    // 跳转到专家详情页面\r\n    goExpertLibraryDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/expertDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到需求详情页面\r\n    goDemandDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/demandHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到资源详情页面\r\n    goResourceDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/resourceHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 立即报名\r\n    signUp() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm(\r\n        `您参加的是${this.data.activityName},请确认。报名成功后，平台客服会与您对接`,\r\n        \"提示\",\r\n        {\r\n          confirmButtonText: \"我要参加\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }\r\n      ).then(() => {\r\n        this.loading = true;\r\n        addActivityEnroll({ activityId: this.$route.query.id })\r\n          .then(() => {\r\n            this.loading = false;\r\n            this.$message.success(\"报名成功\");\r\n            this.init();\r\n          })\r\n          .catch(() => {\r\n            this.loading = false;\r\n          });\r\n      });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-detail-container {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n  background: #f4f5f9;\r\n  .activity-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .activity-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .activity-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .activity-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .activity-detail-card {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n    .activity-detail-content {\r\n      padding: 52px 116px 60px;\r\n      .activity-detail-headline {\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        padding-bottom: 32px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        margin-bottom: 40px;\r\n        .headline-title {\r\n          font-size: 32px;\r\n          font-weight: 600;\r\n          color: #333;\r\n          line-height: 48px;\r\n          word-wrap: break-word;\r\n        }\r\n        .headline-time {\r\n          color: #333;\r\n          line-height: 14px;\r\n          padding-top: 16px;\r\n        }\r\n      }\r\n      .activity-detail-caption {\r\n        display: flex;\r\n        align-items: center;\r\n        .caption-line {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .caption-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n      .related-supply-title {\r\n        margin-top: 60px;\r\n      }\r\n      .activity-demand-info {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        .activity-demand-item {\r\n          width: 222px;\r\n          margin: 40px 18px 0 0;\r\n          background: #f8f9fb;\r\n          .activity-item-img {\r\n            width: 100%;\r\n            height: 160px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          .activity-item-content {\r\n            padding: 16px 16px 14px;\r\n            .activity-item-title {\r\n              width: 190px;\r\n              height: 52px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Medium, PingFang SC;\r\n              font-weight: 500;\r\n              color: #333;\r\n              line-height: 26px;\r\n              overflow: hidden;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n              -webkit-line-clamp: 2;\r\n              text-overflow: ellipsis;\r\n            }\r\n            .activity-item-content-tag {\r\n              display: flex;\r\n              flex-wrap: wrap;\r\n              .activity-item-tag {\r\n                max-width: 190px;\r\n                padding: 6px 12px;\r\n                font-size: 12px;\r\n                font-family: PingFangSC-Regular, PingFang SC;\r\n                color: #214dc5;\r\n                line-height: 12px;\r\n                background: rgba(33, 77, 197, 0.1);\r\n                border-radius: 4px;\r\n                margin: 12px 12px 0 0;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n          }\r\n          &:hover {\r\n            cursor: pointer;\r\n            .activity-item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .activity-detail-img {\r\n        width: 960px;\r\n        // height: 1160px;\r\n        height: 100%;\r\n        margin: 40px 0 60px;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .activity-detai-list {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        flex-wrap: wrap;\r\n        width: 100%;\r\n        margin-bottom: 60px;\r\n        .list-item-content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          width: 462px;\r\n          background: #fff;\r\n          box-shadow: 0px 8px 32px 0px rgba(38, 74, 116, 0.1);\r\n          margin-top: 40px;\r\n          padding: 22px 25px 23px 26px;\r\n          min-height: 179px;\r\n          .list-item-box {\r\n            flex: 1;\r\n            .item-headline {\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              .item-title {\r\n                width: 200px;\r\n                font-size: 26px;\r\n                font-family: PingFangSC-Medium, PingFang SC;\r\n                font-weight: 500;\r\n                color: #333;\r\n                line-height: 26px;\r\n                text-overflow: ellipsis;\r\n                white-space: nowrap;\r\n                overflow: hidden;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n            .activity-detai-label {\r\n              display: flex;\r\n              flex-wrap: wrap;\r\n              margin: 0 0 15px;\r\n              .activity-label-item {\r\n                max-width: 280px;\r\n                padding: 5px 12px;\r\n                background: #f4f5f9;\r\n                border-radius: 4px;\r\n                font-size: 10px;\r\n                font-family: PingFangSC-Regular, PingFang SC;\r\n                color: #666;\r\n                line-height: 10px;\r\n                margin: 19px 12px 0 0;\r\n                .activity-label-type {\r\n                  word-wrap: break-word;\r\n                }\r\n              }\r\n            }\r\n            .activity-detai-box {\r\n              width: 296px;\r\n              font-size: 12px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #666;\r\n              line-height: 24px;\r\n              overflow: hidden;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n              -webkit-line-clamp: 2;\r\n              text-overflow: ellipsis;\r\n              word-wrap: break-word;\r\n            }\r\n          }\r\n          .list-item-img {\r\n            width: 96px;\r\n            height: 134px;\r\n            margin-left: 19px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          &:hover {\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n      .activity-area-btn {\r\n        text-align: center;\r\n        margin-top: 60px;\r\n        .activity-sign-up {\r\n          width: 400px;\r\n          height: 50px;\r\n          background: #21c9b8;\r\n          border-radius: 4px;\r\n          font-size: 20px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #fff;\r\n          line-height: 20px;\r\n        }\r\n      }\r\n      .activity-disabled-btn {\r\n        width: 400px;\r\n        height: 50px;\r\n        border-radius: 4px;\r\n        font-size: 20px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        line-height: 20px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}