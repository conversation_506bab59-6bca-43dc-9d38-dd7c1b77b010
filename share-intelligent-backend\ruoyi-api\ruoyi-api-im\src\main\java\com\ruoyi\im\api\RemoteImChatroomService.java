package com.ruoyi.im.api;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.page.TableDataInfo;
import com.ruoyi.im.api.domain.ImChatroom;
import com.ruoyi.im.api.domain.ImChatroomUser;
import com.ruoyi.im.api.dto.ChatroomPojo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api
 * @ClassName: RemoteImChatroomService
 * @Author: ${maguojun}
 * @Description: ${description}
 * @Date: 2022/3/16 13:51
 * @Version: 1.0
 */
@FeignClient(contextId = "remoteImChatroomService", value = ServiceNameConstants.IM_SERVICE)
public interface RemoteImChatroomService {

    @PostMapping(value = "/im/chatroom//query/{page}/{size}" )
    R<TableDataInfo> queryPage(@RequestBody(required = false)  ImChatroom imChatroom, @PathVariable("page")  int page, @PathVariable("size")  int size, @RequestParam(value = "fields",required = false) String fields);


        /***
         * 新增ImChatroom数据 聊天室加入
         * @param imChatroom
         * @return
         */
    @PostMapping(value="/im/chatroom/signin")
    R<String> add(@RequestBody ImChatroom imChatroom,@RequestParam("userId") String userId);



    /***
     * 聊天室退出
     * @param
     * @return
     */
    @PostMapping(value="/im/chatroom/signout")
    R<Boolean> delete(@RequestBody ImChatroomUser imChatroomUser);


    /***
     * 修改ImChatroom数据
     * @param imChatroom
     * @return
     */
    @PostMapping(value="/im/chatroom/update")
    R<Boolean> update(@RequestBody ImChatroom imChatroom);

    /***
     * ImChatroom分页条件搜索实现
     * @param
     * @param page
     * @param size
     * @return
     */
    @PostMapping(value = "/im/chatroom/search/{page}/{size}" )
    R<TableDataInfo> findPage(@PathVariable("page")  int page, @PathVariable("size")  int size, @RequestParam(value = "fields",required = false) String fields);

    /**
     * 列表
     * @param imChatroom
     * @param fields
     * @return
     */
    @PostMapping(value = "/im/chatroom/list" )
    R<List<ImChatroom>> findList(@RequestBody(required = false)  ImChatroom imChatroom, @RequestParam(value = "fields",required = false) String fields);

    @PostMapping(value="/im/chatroom/ownlist")
    R<List<ChatroomPojo>> list(@RequestParam("userId") String userId);
}
