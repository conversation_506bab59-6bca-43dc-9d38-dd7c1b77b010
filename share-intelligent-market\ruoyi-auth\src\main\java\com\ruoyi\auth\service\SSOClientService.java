package com.ruoyi.auth.service;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.RemoteUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * SSO客户端服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SSOClientService {

    private static final Logger log = LoggerFactory.getLogger(SSOClientService.class);

    @Autowired
    private RedisService redisService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RestTemplate restTemplate;

    // 主系统配置
    @Value("${sso.main-system.url:http://localhost:9200}")
    private String mainSystemUrl;

    @Value("${sso.main-system.verify-token-url:/sso/verify}")
    private String verifyTokenUrl;

    @Value("${sso.main-system.login-url:/sso/login}")
    private String mainLoginUrl;

    // 当前系统配置
    @Value("${sso.current-system.system-id:market}")
    private String currentSystemId;

    @Value("${sso.current-system.callback-url:http://localhost:8081/sso/login}")
    private String callbackUrl;

    // Token配置
    private static final String LOCAL_TOKEN_PREFIX = "local_token:";
    private static final int LOCAL_TOKEN_EXPIRE = 480; // 8小时

    /**
     * 验证SSO Token
     * 
     * @param token SSO Token
     * @param system 系统标识
     * @return 用户信息
     */
    public Map<String, Object> validateSSOToken(String token, String system) {
        try {
            String verifyUrl = mainSystemUrl + verifyTokenUrl;
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("token", token);
            params.add("system", system);
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(verifyUrl, request, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                if ("0".equals(String.valueOf(result.get("code")))) {
                    return (Map<String, Object>) result.get("data");
                }
            }
            
            return null;
        } catch (Exception e) {
            log.error("验证SSO Token失败", e);
            return null;
        }
    }

    /**
     * 获取或创建本地用户
     * 
     * @param userInfo 主系统用户信息
     * @return 本地登录用户
     */
    public LoginUser getOrCreateLocalUser(Map<String, Object> userInfo) {
        try {
            Long mainUserId = Long.valueOf(String.valueOf(userInfo.get("userId")));
            String username = (String) userInfo.get("username");
            
            // 查找本地用户（通过用户名或者扩展字段关联主系统用户ID）
            SysUser localUser = remoteUserService.selectSysUserByUserName(username);
            
            if (localUser == null) {
                // 创建新的本地用户
                localUser = createLocalUser(userInfo);
                if (localUser == null) {
                    return null;
                }
            } else {
                // 更新现有用户信息
                updateLocalUser(localUser, userInfo);
            }
            
            // 构造LoginUser
            LoginUser loginUser = new LoginUser();
            loginUser.setUserid(localUser.getUserId());
            loginUser.setUsername(localUser.getUserName());
            loginUser.setUser(localUser);
            
            return loginUser;
        } catch (Exception e) {
            log.error("获取或创建本地用户失败", e);
            return null;
        }
    }

    /**
     * 创建本地用户
     * 
     * @param userInfo 主系统用户信息
     * @return 本地用户
     */
    private SysUser createLocalUser(Map<String, Object> userInfo) {
        try {
            SysUser user = new SysUser();
            user.setUserName((String) userInfo.get("username"));
            user.setNickName((String) userInfo.get("nickName"));
            user.setEmail((String) userInfo.get("email"));
            user.setPhonenumber((String) userInfo.get("phonenumber"));
            user.setSex((String) userInfo.get("sex"));
            user.setAvatar((String) userInfo.get("avatar"));
            user.setStatus("0"); // 正常状态
            user.setPassword(""); // SSO用户不需要密码
            user.setRemark("SSO同步用户，主系统用户ID: " + userInfo.get("userId"));
            
            // 调用用户服务创建用户
            // 注意：这里需要根据实际的用户服务接口进行调用
            // remoteUserService.insertUser(user);
            
            log.info("创建本地用户成功: {}", user.getUserName());
            return user;
        } catch (Exception e) {
            log.error("创建本地用户失败", e);
            return null;
        }
    }

    /**
     * 更新本地用户信息
     * 
     * @param localUser 本地用户
     * @param userInfo 主系统用户信息
     */
    private void updateLocalUser(SysUser localUser, Map<String, Object> userInfo) {
        try {
            // 更新用户基本信息
            localUser.setNickName((String) userInfo.get("nickName"));
            localUser.setEmail((String) userInfo.get("email"));
            localUser.setPhonenumber((String) userInfo.get("phonenumber"));
            localUser.setSex((String) userInfo.get("sex"));
            localUser.setAvatar((String) userInfo.get("avatar"));
            
            // 调用用户服务更新用户
            // remoteUserService.updateUser(localUser);
            
            log.info("更新本地用户信息成功: {}", localUser.getUserName());
        } catch (Exception e) {
            log.error("更新本地用户信息失败", e);
        }
    }

    /**
     * 生成本地访问Token
     * 
     * @param loginUser 登录用户
     * @return 本地Token
     */
    public String generateLocalToken(LoginUser loginUser) {
        String token = IdUtils.fastSimpleUUID();
        loginUser.setToken(token);
        return token;
    }

    /**
     * 设置登录状态
     * 
     * @param token 本地Token
     * @param loginUser 登录用户
     */
    public void setLoginStatus(String token, LoginUser loginUser) {
        String redisKey = LOCAL_TOKEN_PREFIX + token;
        redisService.setCacheObject(redisKey, loginUser, LOCAL_TOKEN_EXPIRE, TimeUnit.MINUTES);
        log.info("设置用户 {} 登录状态成功", loginUser.getUsername());
    }

    /**
     * 清除本地用户会话
     * 
     * @param token 本地Token
     */
    public void clearLocalUserSession(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String redisKey = LOCAL_TOKEN_PREFIX + token;
            redisService.deleteObject(redisKey);
            log.info("清除本地用户会话: {}", token);
        }
    }

    /**
     * 通知主系统登出
     * 
     * @param token 本地Token
     */
    public void notifyMainSystemLogout(String token) {
        try {
            String logoutUrl = mainSystemUrl + "/sso/logout";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("token", token);
            params.add("system", currentSystemId);
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            restTemplate.postForEntity(logoutUrl, request, Map.class);
            
            log.info("通知主系统登出成功");
        } catch (Exception e) {
            log.error("通知主系统登出失败", e);
        }
    }

    /**
     * 检查主系统登录状态
     * 
     * @return 主系统登录状态
     */
    public boolean checkMainSystemStatus() {
        try {
            String statusUrl = mainSystemUrl + "/sso/status";
            ResponseEntity<Map> response = restTemplate.getForEntity(statusUrl, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                if ("0".equals(String.valueOf(result.get("code")))) {
                    Map<String, Object> data = (Map<String, Object>) result.get("data");
                    return Boolean.TRUE.equals(data.get("isLogin"));
                }
            }
            
            return false;
        } catch (Exception e) {
            log.error("检查主系统登录状态失败", e);
            return false;
        }
    }

    /**
     * 获取主系统登录地址
     * 
     * @param redirect 登录成功后的跳转地址
     * @return 主系统登录地址
     */
    public String getMainSystemLoginUrl(String redirect) {
        StringBuilder url = new StringBuilder();
        url.append(mainSystemUrl).append(mainLoginUrl);
        url.append("?target=").append(currentSystemId);
        
        if (StringUtils.isNotEmpty(redirect)) {
            try {
                url.append("&redirect=").append(java.net.URLEncoder.encode(redirect, "UTF-8"));
            } catch (Exception e) {
                log.error("URL编码失败", e);
            }
        }
        
        return url.toString();
    }

    /**
     * 同步用户信息
     * 
     * @param userInfo 用户信息
     * @return 同步结果
     */
    public boolean syncUserInfo(Map<String, Object> userInfo) {
        try {
            String username = (String) userInfo.get("username");
            SysUser localUser = remoteUserService.selectSysUserByUserName(username);
            
            if (localUser != null) {
                updateLocalUser(localUser, userInfo);
                return true;
            } else {
                SysUser newUser = createLocalUser(userInfo);
                return newUser != null;
            }
        } catch (Exception e) {
            log.error("同步用户信息失败", e);
            return false;
        }
    }
}
