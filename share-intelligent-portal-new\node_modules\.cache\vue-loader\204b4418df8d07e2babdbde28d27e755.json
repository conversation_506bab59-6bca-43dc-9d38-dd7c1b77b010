{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\profile\\index.vue?vue&type=style&index=0&id=03488e44&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\profile\\index.vue", "mtime": 1750311963073}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgcmdiKDIyNSwgMjQ3LCAyNDApLCByZ2IoMjQ0LCAyNTIsIDI1MCkpOw0KICBoZWlnaHQ6IDEwODBweDsNCn0NCg0KLnVzZXItaW5mby1jYXJkIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgcGFkZGluZzogMjBweDsNCiAgaGVpZ2h0OiA0NTBweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KDQogIC51c2VyLWluZm8tY2FyZC10b3Agew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGhlaWdodDogMTQwcHg7DQogICAgYmFja2dyb3VuZDogdXJsKCIuLi8uLi8uLi8uLi9hc3NldHMvdXNlci9ob21lUGFnZUJhbm5lci5wbmciKSBuby1yZXBlYXQ7DQogICAgYmFja2dyb3VuZC1zaXplOiAxMDAlIDEwMCU7DQogICAgcGFkZGluZzogMzJweCAzMHB4IDM2cHggMzBweDsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KDQogICAgLmltZ1N0eWxlIHsNCiAgICAgIHdpZHRoOiA2MHB4Ow0KICAgICAgaGVpZ2h0OiA2MHB4Ow0KICAgIH0NCg0KICAgIC51c2VyLWluZm8tY2FyZC10b3AtcmlnaHQgew0KICAgICAgbWFyZ2luLWxlZnQ6IDIwcHg7DQoNCiAgICAgIC5uYW1lU3R5bGUgew0KICAgICAgICBoZWlnaHQ6IDE0cHg7DQogICAgICAgIGZvbnQtZmFtaWx5OiBTb3VyY2UgSGFuIFNhbnMgQ047DQogICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgY29sb3I6ICNmZmZmZmY7DQogICAgICB9DQoNCiAgICAgIC5waG9uZVN0eWxlIHsNCiAgICAgICAgaGVpZ2h0OiAxNXB4Ow0KICAgICAgICBmb250LWZhbWlseTogU291cmNlIEhhbiBTYW5zIENOOw0KICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgIGNvbG9yOiAjZmZmZmZmOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC51c2VyLWluZm8tY2FyZC1ib3R0b20gew0KICAgIG1hcmdpbi10b3A6IDIwcHg7DQoNCiAgICAuYnVzaW5lc3NBcHBsaSB7DQogICAgICBtYXJnaW4tdG9wOiAzMXB4Ow0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQoNCiAgICAgIC5hcHBsaUl0ZW0gew0KICAgICAgICB3aWR0aDogNzBweDsNCiAgICAgICAgbWFyZ2luLWxlZnQ6IDQxcHg7DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgfQ0KDQogICAgICAuYXBwbGlJdGVtOm50aC1jaGlsZCgxKSB7DQogICAgICAgIG1hcmdpbi1sZWZ0OiAwOw0KICAgICAgfQ0KDQogICAgICAuYXBwbGlJbWcgew0KICAgICAgICB3aWR0aDogNzBweDsNCiAgICAgICAgaGVpZ2h0OiA3NXB4Ow0KICAgICAgfQ0KDQogICAgICAuYXBwbGlOYW1lIHsNCiAgICAgICAgZm9udC1mYW1pbHk6IFNvdXJjZSBIYW4gU2FucyBDTjsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBjb2xvcjogIzIyMjIyMjsNCiAgICAgICAgbWFyZ2luLXRvcDogMTFweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg0KLmRlbWFuZFN1cHBseSB7DQogIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIHBhZGRpbmc6IDIwcHg7DQogIGhlaWdodDogNDUwcHg7DQogIGJvcmRlci1yYWRpdXM6IDRweDsNCn0NCg0KLm1lTm90aWZpY2F0aW9uIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgcGFkZGluZzogMjBweDsNCiAgaGVpZ2h0OiA0NTBweDsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICBtYXJnaW4tdG9wOiAyNHB4Ow0KfQ0KDQouZG9ja2luZ1JlY29yZHMgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICBwYWRkaW5nOiAyMHB4Ow0KICBoZWlnaHQ6IDQ1MHB4Ow0KICBib3JkZXItcmFkaXVzOiA0cHg7DQogIG1hcmdpbi10b3A6IDI0cHg7DQp9DQoNCi5uYXZTdHlsZSB7DQogIGhlaWdodDogNTBweDsNCiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNjY2M7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQoNCiAgLm5hdlN0eWxlLWxlZnQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBoZWlnaHQ6IDUwcHg7DQogICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICMyMWM5Yjg7DQogIH0NCg0KICAudGV4dCB7DQogICAgZm9udC1mYW1pbHk6IFNvdXJjZSBIYW4gU2FucyBDTjsNCiAgICBmb250LXdlaWdodDogNDAwOw0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICBjb2xvcjogIzIxYzliODsNCiAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgfQ0KDQogIC5uYXZTdHlsZS1yaWdodCB7DQogICAgZm9udC1mYW1pbHk6IFNvdXJjZSBIYW4gU2FucyBDTjsNCiAgICBmb250LXdlaWdodDogNDAwOw0KICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICBjb2xvcjogIzMzMzMzMzsNCiAgICBtYXJnaW4tbGVmdDogYXV0bzsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogIH0NCn0NCg0KLm5vRGF0YVN0eWxlIHsNCiAgd2lkdGg6IDI1OXB4Ow0KICBoZWlnaHQ6IDI1OXB4Ow0KICBtYXJnaW4tdG9wOiA2M3B4Ow0KICBtYXJnaW4tbGVmdDogY2FsYygoMTAwJSAtIDI1OXB4KSAvIDIpOw0KfQ0KDQoubm9EYXRhVGV4dCB7DQogIHdpZHRoOiAxMDAlOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGZvbnQtZmFtaWx5OiBTb3VyY2UgSGFuIFNhbnMgQ047DQogIGZvbnQtd2VpZ2h0OiA0MDA7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgY29sb3I6ICM5OTk5OTk7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0PA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <div class=\"user-info-card\">\r\n              <!-- 上部分 -->\r\n              <div class=\"user-info-card-top\">\r\n                <!-- 左侧图片 -->\r\n                <div class=\"imgStyle\">\r\n                  <img style=\"width: 100%; height: 100%\" :src=\"userinfo.avatar ? userinfo.avatar : defaultAvatar\"\r\n                    alt=\"\" />\r\n                </div>\r\n                <!-- 右侧内容 -->\r\n                <div class=\"user-info-card-top-right\">\r\n                  <div class=\"nameStyle\">{{ userinfo.memberRealName || '暂无' }}</div>\r\n                  <div class=\"phoneStyle\" style=\"margin-top: 17px; margin-bottom: 11px\">\r\n                    联系电话：{{ userinfo.memberPhone || '暂无' }}\r\n                  </div>\r\n                  <!-- <div class=\"phoneStyle\">所属企业：立即加入></div> -->\r\n                </div>\r\n              </div>\r\n              <!-- 下部分 -->\r\n              <div class=\"user-info-card-bottom\">\r\n                <!-- 顶部导航 -->\r\n                <div class=\"navStyle\">\r\n                  <div class=\"navStyle-left\">\r\n                    <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/business_icon.png\" alt=\"\" />\r\n                    <div class=\"text\">企业应用</div>\r\n                  </div>\r\n                  <!-- <div class=\"navStyle-right\">查看更多 >></div> -->\r\n                </div>\r\n                <!-- 底部内容 -->\r\n                <div class=\"businessAppli\">\r\n                  <div class=\"appliItem\" v-for=\"(item, index) in appliList\" :key=\"index\" @click=\"handleLink(item)\">\r\n                    <div class=\"appliImg\">\r\n                      <img style=\"width: 100%; height: 100%\" :src=\"item.url\" alt=\"\" />\r\n                    </div>\r\n                    <div class=\"appliName\">{{ item.name }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"demandSupply\">\r\n              <!-- 顶部导航 -->\r\n              <div class=\"navStyle\">\r\n                <div class=\"navStyle-left\" @click=\"handleClick('1')\" :style=\"demandSupply == '1'\r\n                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'\r\n                  : 'border-bottom: none;cursor: pointer;'\r\n                  \">\r\n                  <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/demand_icon.png\" alt=\"\" />\r\n                  <div class=\"text\">我的需求</div>\r\n                </div>\r\n                <div class=\"navStyle-left\" @click=\"handleClick('2')\" style=\"margin-left: 24px\" :style=\"demandSupply == '2'\r\n                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'\r\n                  : 'border-bottom: none;cursor: pointer;'\r\n                  \">\r\n                  <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/supply_icon.png\" alt=\"\" />\r\n                  <div class=\"text\">我的供给</div>\r\n                </div>\r\n                <div class=\"navStyle-right\" @click=\"goTosupplyDemand\">查看更多 >></div>\r\n              </div>\r\n              <el-table :data=\"demandList\" v-if=\"demandSupply == '1'\">\r\n                <el-table-column prop=\"title\" label=\"需求标题\" />\r\n                <el-table-column prop=\"description\" label=\"需求描述\" />\r\n              </el-table>\r\n              <el-table :data=\"supplyList\" v-if=\"demandSupply == '2'\">\r\n                <el-table-column prop=\"title\" label=\"供给标题\" />\r\n                <el-table-column prop=\"description\" label=\"供给描述\" />\r\n              </el-table>\r\n              <!-- 暂无数据 -->\r\n              <!-- <div>\r\n                <div class=\"noDataStyle\">\r\n                  <img style=\"width: 100%; height: 100%\" src=\"../../../../assets/user/noData.png\" alt=\"\" />\r\n                </div>\r\n                <div class=\"noDataText\">暂无内容</div>\r\n              </div> -->\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"meNotification\">\r\n              <!-- 顶部导航 -->\r\n              <div class=\"navStyle\">\r\n                <div class=\"navStyle-left\">\r\n                  <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/message_icon.png\" alt=\"\" />\r\n                  <div class=\"text\">消息通知</div>\r\n                </div>\r\n                <div class=\"navStyle-right\" @click=\"goToNotice\">查看更多 >></div>\r\n              </div>\r\n              <!-- 暂无数据 -->\r\n              <div>\r\n                <div class=\"noDataStyle\">\r\n                  <img style=\"width: 100%; height: 100%\" src=\"../../../../assets/user/noData.png\" alt=\"\" />\r\n                </div>\r\n                <div class=\"noDataText\">暂无内容</div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"dockingRecords\">\r\n              <!-- 顶部导航 -->\r\n              <div class=\"navStyle\">\r\n                <div class=\"navStyle-left\">\r\n                  <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/record_icon.png\" alt=\"\" />\r\n                  <div class=\"text\">对接记录</div>\r\n                </div>\r\n                <div class=\"navStyle-right\" @click=\"goTodockingRecords\">查看更多 >></div>\r\n              </div>\r\n              <!-- 暂无数据 -->\r\n              <el-table :data=\"dockingList\" v-if=\"dockingList.length > 0\">\r\n                <el-table-column align=\"center\" prop=\"title\" label=\"资源名称\">\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" prop=\"intentionContent\" label=\"申请内容\">\r\n                </el-table-column>\r\n              </el-table>\r\n              <div v-if=\"dockingList.length == 0\">\r\n                <div class=\"noDataStyle\">\r\n                  <img style=\"width: 100%; height: 100%\" src=\"../../../../assets/user/noData.png\" alt=\"\" />\r\n                </div>\r\n                <div class=\"noDataText\">暂无内容</div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { mySupply, myDemand } from \"@/api/home\";\r\nimport { dockingList } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      appliList: [\r\n        {\r\n          url: require(\"../../../../assets/user/appli1.png\"),\r\n          name: \"复材商城\",\r\n          link: 'http://**************:1001/'\r\n        },\r\n        {\r\n          url: require(\"../../../../assets/user/appli2.png\"),\r\n          name: \"仓储物流\",\r\n        },\r\n        {\r\n          url: require(\"../../../../assets/user/appli3.png\"),\r\n          name: \"金融服务\",\r\n        },\r\n        {\r\n          url: require(\"../../../../assets/user/appli4.png\"),\r\n          name: \"设备管理\",\r\n          router: '/user/equipmentManagement'\r\n        },\r\n      ],\r\n      defaultAvatar: require('@/assets/images/avatar.png'),\r\n      demandSupply: \"1\",\r\n      userinfo: {},\r\n      supplyList: [],\r\n      demandList: [],\r\n      dockingList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getInfo();\r\n    this.getSupplyList();\r\n    this.getDemandList();\r\n    this.getDockingList();\r\n  },\r\n  methods: {\r\n    // 获取用户信息\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (res.code == 200) {\r\n          this.userinfo = res.member;\r\n          window.sessionStorage.setItem(\"userinfo\", JSON.stringify(res.member));\r\n        }\r\n      });\r\n    },\r\n    handleClick(val) {\r\n      this.demandSupply = val;\r\n    },\r\n    handleLink(val) {\r\n      if (val.link) {\r\n        window.open(val.link)\r\n      } else if (val.router) {\r\n        this.$router.push({ path: val.router });\r\n      }\r\n      else {\r\n        this.$notify.info({\r\n          title: '敬请期待',\r\n        });\r\n      }\r\n    },\r\n    getSupplyList() {\r\n      let params = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        queryType: \"my\",\r\n      };\r\n      mySupply(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.supplyList = res.rows;\r\n        }\r\n      });\r\n    },\r\n    getDemandList() {\r\n      let params = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        queryType: \"my\",\r\n      };\r\n      myDemand(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.demandList = res.rows;\r\n        }\r\n      });\r\n    },\r\n    getDockingList() {\r\n      dockingList({\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        queryType: 'my',\r\n      }).then((res) => {\r\n        this.dockingList = res.rows;\r\n      });\r\n    },\r\n    goTosupplyDemand() {\r\n      this.$router.push({ path: '/user/supplyDemand' })\r\n    },\r\n    goToNotice() {\r\n      // this.$router.push({path:'/user/notice'})\r\n    },\r\n    goTodockingRecords() {\r\n      this.$router.push({ path: '/user/dockingRecords' })\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 1080px;\r\n}\r\n\r\n.user-info-card {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  height: 450px;\r\n  border-radius: 4px;\r\n\r\n  .user-info-card-top {\r\n    width: 100%;\r\n    height: 140px;\r\n    background: url(\"../../../../assets/user/homePageBanner.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n    padding: 32px 30px 36px 30px;\r\n    display: flex;\r\n\r\n    .imgStyle {\r\n      width: 60px;\r\n      height: 60px;\r\n    }\r\n\r\n    .user-info-card-top-right {\r\n      margin-left: 20px;\r\n\r\n      .nameStyle {\r\n        height: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        font-size: 18px;\r\n        color: #ffffff;\r\n      }\r\n\r\n      .phoneStyle {\r\n        height: 15px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .user-info-card-bottom {\r\n    margin-top: 20px;\r\n\r\n    .businessAppli {\r\n      margin-top: 31px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .appliItem {\r\n        width: 70px;\r\n        margin-left: 41px;\r\n        text-align: center;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .appliItem:nth-child(1) {\r\n        margin-left: 0;\r\n      }\r\n\r\n      .appliImg {\r\n        width: 70px;\r\n        height: 75px;\r\n      }\r\n\r\n      .appliName {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #222222;\r\n        margin-top: 11px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.demandSupply {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  height: 450px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.meNotification {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  height: 450px;\r\n  border-radius: 4px;\r\n  margin-top: 24px;\r\n}\r\n\r\n.dockingRecords {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  height: 450px;\r\n  border-radius: 4px;\r\n  margin-top: 24px;\r\n}\r\n\r\n.navStyle {\r\n  height: 50px;\r\n  border-bottom: 1px solid #ccc;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .navStyle-left {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 50px;\r\n    border-bottom: 2px solid #21c9b8;\r\n  }\r\n\r\n  .text {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #21c9b8;\r\n    margin-left: 10px;\r\n  }\r\n\r\n  .navStyle-right {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #333333;\r\n    margin-left: auto;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.noDataStyle {\r\n  width: 259px;\r\n  height: 259px;\r\n  margin-top: 63px;\r\n  margin-left: calc((100% - 259px) / 2);\r\n}\r\n\r\n.noDataText {\r\n  width: 100%;\r\n  text-align: center;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 400;\r\n  font-size: 16px;\r\n  color: #999999;\r\n}\r\n</style>\r\n"]}]}