<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.UserRongYunStateMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.UserRongYunState">

    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="online_num" jdbcType="INTEGER" property="onlineNum" />
    <result column="timestamp" jdbcType="BIGINT" property="timestamp" />
    <result column="clientIp" jdbcType="VARCHAR" property="clientip" />
    <result column="open_id" jdbcType="VARCHAR" property="openId" />
    <result column="wx_update_time" jdbcType="TIMESTAMP" property="wxUpdateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_name, `timestamp`, clientIp, open_id, wx_update_time,online_num
  </sql>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.ruoyi.system.domain.UserRongYunState" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into user_rong_yun_state
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userName != null">
        user_name,
      </if>
      <if test="onlineNum != null">
        online_num,
      </if>

      <if test="timestamp != null">
        `timestamp`,
      </if>
      <if test="clientip != null">
        clientIp,
      </if>
      <if test="openId != null">
        open_id,
      </if>
      <if test="wxUpdateTime != null">
        wx_update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="onlineNum != null">
        #{onlineNum,jdbcType=INTEGER},
      </if>
      <if test="timestamp != null">
        #{timestamp,jdbcType=BIGINT},
      </if>
      <if test="clientip != null">
        #{clientip,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        #{openId,jdbcType=VARCHAR},
      </if>
      <if test="wxUpdateTime != null">
        #{wxUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.system.domain.UserRongYunState">
    <!--@mbg.generated-->
    update user_rong_yun_state
    <set>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="onlineNum != null">
        online_num = #{onlineNum,jdbcType=INTEGER},
      </if>
      <if test="timestamp != null">
        `timestamp` = #{timestamp,jdbcType=BIGINT},
      </if>
      <if test="clientip != null">
        clientIp = #{clientip,jdbcType=VARCHAR},
      </if>
      <if test="openId != null">
        open_id = #{openId,jdbcType=VARCHAR},
      </if>
      <if test="wxUpdateTime != null">
        wx_update_time = #{wxUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.system.domain.UserRongYunState">
    <!--@mbg.generated-->
    update user_rong_yun_state
    set user_name = #{userName,jdbcType=VARCHAR},
    online_num = #{onlineNum,jdbcType=INTEGER},
    `timestamp` = #{timestamp,jdbcType=BIGINT},
    clientIp = #{clientip,jdbcType=VARCHAR},
    open_id = #{openId,jdbcType=VARCHAR},
    wx_update_time = #{wxUpdateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByuserName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from user_rong_yun_state where user_name = #{userName}
  </select>

  <update id="updateOnlineNum" parameterType="com.ruoyi.system.domain.UserRongYunState">
    update user_rong_yun_state
    set online_num  = online_num + #{onlineNum},
    `timestamp` = #{timestamp},
    clientIp = #{clientip}
    where id = #{id}
  </update>
</mapper>