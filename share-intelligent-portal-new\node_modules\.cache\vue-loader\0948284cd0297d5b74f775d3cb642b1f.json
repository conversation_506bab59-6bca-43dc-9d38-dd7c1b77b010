{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\addPolicy.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\addPolicy.vue", "mtime": 1750311962954}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQoNCmltcG9ydCB7ZWRpdFBvbGljeUFwcGx5fSBmcm9tICJAL2FwaS9zeXN0ZW0vZGVtYW5kIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiYWRkUG9saWN5IiwNCiAgZGF0YSgpIHsNCiAgICBjb25zdCB7IHVzZXIgfSA9IHRoaXMuJHN0b3JlLnN0YXRlOw0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIGZvcm06IHsNCiAgICAgICAgdGl0bGU6IHVuZGVmaW5lZCwNCiAgICAgICAgcG9saWN5SWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgZmlsZUxpc3Q6IFtdLA0KICAgICAgICBjb250cmFjdFBlcnNvbjogdXNlci5uYW1lLA0KICAgICAgICBjb250cmFjdFBob25lOiB1c2VyLnRlbCwNCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQsDQogICAgICB9LA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgY29udHJhY3RQZXJzb246IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl6IGU57O75Lq6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvbnRyYWN0UGhvbmU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+35YWI57u05oqk6IGU57O75pa55byPIiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGZpbGVMaXN0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+S4iuS8oOaWh+S7tiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgY29uc3QgeyBpZCwgdGl0bGUgfSA9IHRoaXMuJHJvdXRlLnBhcmFtczsNCiAgICBpZiAoaWQpIHsNCiAgICAgIHRoaXMuZm9ybS50aXRsZSA9IHRpdGxlOw0KICAgICAgdGhpcy5mb3JtLnBvbGljeUlkID0gaWQ7DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgb25TdWJtaXQoc3RhdHVzKSB7DQogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICAgICAgZWRpdFBvbGljeUFwcGx5KHsNCiAgICAgICAgICAgIC4uLnRoaXMuZm9ybSwNCiAgICAgICAgICAgIHN0YXR1cywNCiAgICAgICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IHsgY29kZSwgbXNnIH0gPSByZXM7DQogICAgICAgICAgICBpZiAoY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5o+Q5Lqk5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMuJHJvdXRlci5iYWNrKCk7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKG1zZyB8fCAi5o+Q5Lqk5aSx6LSlIik7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkuZmluYWxseSgoKSA9PiB0aGlzLmxvYWRpbmcgPSBmYWxzZSkNCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgIH0sDQogIH0NCn0NCg=="}, {"version": 3, "sources": ["addPolicy.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "addPolicy.vue", "sourceRoot": "src/views/form", "sourcesContent": ["<template>\r\n  <div class=\"intention-page\">\r\n    <div class=\"intention-page-header\">\r\n      <div class=\"banner\">\r\n        <img\r\n          src=\"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/1676034162343360.webp\"\r\n          alt=\"在线申报\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <div class=\"intention-page-title\">在线申报</div>\r\n    <div v-loading=\"loading\" class=\"card-container intention-form\">\r\n      <div class=\"form-content\">\r\n        <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"80px\">\r\n          <el-form-item label=\"政策名称\">\r\n            {{ form.title }}\r\n          </el-form-item>\r\n          <el-form-item label=\"联系人\" prop=\"contractPerson\">\r\n            <el-input v-model=\"form.contractPerson\" placeholder=\"请输入联系人\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"联系方式\" prop=\"contractPhone\">\r\n            <el-input disabled v-model=\"form.contractPhone\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"上传文件\" prop=\"fileList\">\r\n            <FileUpload v-model=\"form.fileList\" />\r\n          </el-form-item>\r\n          <el-form-item class=\"footer-submit\">\r\n            <el-button @click=\"onSubmit(1)\" type=\"primary\" plain>暂存草稿</el-button>\r\n            <el-button type=\"primary\" @click=\"onSubmit(2)\">提交审核</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport {editPolicyApply} from \"@/api/system/demand\";\r\n\r\nexport default {\r\n  name: \"addPolicy\",\r\n  data() {\r\n    const { user } = this.$store.state;\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        title: undefined,\r\n        policyId: undefined,\r\n        fileList: [],\r\n        contractPerson: user.name,\r\n        contractPhone: user.tel,\r\n        status: undefined,\r\n      },\r\n      rules: {\r\n        contractPerson: [\r\n          { required: true, message: \"请输入联系人\", trigger: \"blur\" },\r\n        ],\r\n        contractPhone: [\r\n          { required: true, message: \"请先维护联系方式\", trigger: \"blur\" },\r\n        ],\r\n        fileList: [\r\n          { required: true, message: \"请上传文件\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    }\r\n  },\r\n  created() {\r\n    const { id, title } = this.$route.params;\r\n    if (id) {\r\n      this.form.title = title;\r\n      this.form.policyId = id;\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit(status) {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          editPolicyApply({\r\n            ...this.form,\r\n            status,\r\n          }).then((res) => {\r\n            const { code, msg } = res;\r\n            if (code === 200) {\r\n              this.$message.success(\"提交成功\");\r\n              this.$router.back();\r\n            } else {\r\n              this.$message.error(msg || \"提交失败\");\r\n            }\r\n          }).finally(() => this.loading = false)\r\n        }\r\n      })\r\n\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n.intention-page {\r\n  background-color: #F4F5F9;\r\n  padding-bottom: 80px;\r\n  &-header {\r\n    background-color: #FFFFFF;\r\n    .banner {\r\n      width: 100%;\r\n      height: 540px;\r\n      background-color: #f5f5f5;\r\n      img {\r\n        width: 100%;\r\n        height: 540px;\r\n        object-fit: fill;\r\n      }\r\n    }\r\n    .body {\r\n      padding: 60px 0;\r\n    }\r\n  }\r\n  &-title {\r\n    font-size: 40px;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    line-height: 40px;\r\n    text-align: center;\r\n    padding: 60px 0;\r\n  }\r\n  .intention-form {\r\n    @include flexCenter;\r\n    height: 664px;\r\n    background-color: #FFFFFF;\r\n    margin-bottom: 80px;\r\n    .form-content {\r\n      width: 750px;\r\n      .footer-submit {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-top: 40px;\r\n        .el-button {\r\n          width: 160px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}