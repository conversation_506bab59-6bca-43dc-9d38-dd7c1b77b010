{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\demandTab.vue?vue&type=style&index=1&id=17827bbc&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\demandTab.vue", "mtime": 1750311962930}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZGFtYW5kLXRhYi1jb250YWluZXIgew0KICAudGFiLXBhZ2UtZW5kIHsNCiAgICAuY29tcGFueS10YWItcGFnaW5hdGlvbiB7DQogICAgICB3aWR0aDogMjQwcHg7DQogICAgICBtYXJnaW46IDAgYXV0bzsNCiAgICAgIC5idG4tcHJldiwNCiAgICAgIC5idG4tbmV4dCwNCiAgICAgIC5idG4tcXVpY2twcmV2IHsNCiAgICAgICAgd2lkdGg6IDMycHg7DQogICAgICAgIGhlaWdodDogMzJweDsNCiAgICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICBtYXJnaW46IDAgNnB4Ow0KICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgIH0NCiAgICAgIC5lbC1wYWdlciB7DQogICAgICAgIC5udW1iZXIgew0KICAgICAgICAgIHdpZHRoOiAzMnB4Ow0KICAgICAgICAgIGhlaWdodDogMzJweDsNCiAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAzMnB4Ow0KICAgICAgICAgIG1hcmdpbjogMCA2cHg7DQogICAgICAgICAgJi5hY3RpdmUgew0KICAgICAgICAgICAgYmFja2dyb3VuZDogIzIxYzliODsNCiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMyMWM5Yjg7DQogICAgICAgICAgICBjb2xvcjogI2ZmZjsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["demandTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "demandTab.vue", "sourceRoot": "src/views/components/home/<USER>", "sourcesContent": ["<template>\r\n  <div class=\"damand-tab-container\">\r\n    <div v-loading=\"loading\" class=\"tab-main\">\r\n      <el-scrollbar noresize class=\"left\">\r\n        <div class=\"tab-content\">\r\n          <div\r\n            v-for=\"(item, index) in tabs\"\r\n            :key=\"index\"\r\n            :class=\"{ active: tabIndex === index }\"\r\n            class=\"tab-content-item\"\r\n            @click=\"onTabChange(index)\"\r\n          >\r\n            {{ item.dictLabel }}\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n      <el-row class=\"right\" :gutter=\"24\">\r\n        <template v-if=\"items.length > 0\">\r\n          <el-col :span=\"8\" v-for=\"item in items\" :key=\"item.id\">\r\n            <router-link\r\n              target=\"_blank\"\r\n              :to=\"`/demandHallDetail?id=${item.id}`\"\r\n            >\r\n              <div class=\"card\">\r\n                <el-image\r\n                  class=\"card-img\"\r\n                  :src=\"item.url ? item.url : defaultUrl\"\r\n                  fit=\"fill\"\r\n                />\r\n                <div class=\"card-footer\">\r\n                  <div class=\"title\" :title=\"item.title\">{{ item.title }}</div>\r\n                  <div class=\"subtitle\">{{ item.company }}</div>\r\n                </div>\r\n              </div>\r\n            </router-link>\r\n          </el-col>\r\n        </template>\r\n        <template v-else>\r\n          <el-empty />\r\n        </template>\r\n      </el-row>\r\n    </div>\r\n    <div class=\"tab-page-end\">\r\n      <!-- <span class=\"demonstration\">完整功能</span> -->\r\n      <el-pagination\r\n        class=\"company-tab-pagination\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"pageNum\"\r\n        :page-sizes=\"[100, 200, 300, 400]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\" prev, pager, next \"\r\n        :total=\"total\"\r\n      >\r\n      </el-pagination>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { head, map } from \"ramda\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { listDemand } from \"@/api/zhm/demand\";\r\nimport { formatCompanyName } from \"@/utils\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\nexport const AESUtil = {\r\n  /**\r\n   * AES加密方法\r\n   * @param content 要加密的字符串\r\n   * @returns {string} 加密结果\r\n   */\r\n  aesEncrypt: (content) => {\r\n    let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n    let srcs = CryptoJS.enc.Utf8.parse(content);\r\n    let encrypted = CryptoJS.AES.encrypt(srcs, key, {\r\n      mode: CryptoJS.mode.ECB,\r\n      padding: CryptoJS.pad.Pkcs7,\r\n    });\r\n    return encrypted.toString();\r\n  },\r\n\r\n  /**\r\n   * 解密方法\r\n   * @param encryptStr 密文\r\n   * @returns {string} 明文\r\n   */\r\n  aesDecrypt: (encryptStr) => {\r\n    let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n    let decrypt = CryptoJS.AES.decrypt(encryptStr, key, {\r\n      mode: CryptoJS.mode.ECB,\r\n      padding: CryptoJS.pad.Pkcs7,\r\n    });\r\n    return CryptoJS.enc.Utf8.stringify(decrypt).toString();\r\n  },\r\n};\r\nexport default {\r\n  name: \"DemandTab\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tabs: [],\r\n      tabIndex: 0,\r\n      items: [],\r\n      pageNum: 1,\r\n      pageSize: 6,\r\n      total: 0,\r\n      defaultUrl: require(\"../../../../assets/purchaseSales/demandDefault.png\"),\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  mounted() {\r\n    this.getDemandData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      getDicts(\"demand_type\").then((res) => {\r\n        const { code, data = [] } = res;\r\n        if (code === 200) {\r\n          this.tabs = data;\r\n          this.tabs.unshift({\r\n            dictLabel: \"全部\",\r\n            dictValue: undefined,\r\n            dictSort: 1,\r\n            dictType: \"demand_type\",\r\n          });\r\n          const item = head(data);\r\n          this.getDemandData(item.dictValue);\r\n        }\r\n      });\r\n    },\r\n    getDemandData(type) {\r\n      this.loading = true;\r\n      listDemand({\r\n        demandType: type,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n        displayStatus: 1,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          const { code, rows = [] } = res;\r\n\r\n          if (code === 200) {\r\n            // this.items = rows;\r\n            this.items = map((item) => {\r\n              let url;\r\n              const images = JSON.parse(item.scenePicture) || [];\r\n              if (images.length > 0) {\r\n                url = head(images).url;\r\n              }\r\n              return {\r\n                id: item.id,\r\n                title: item.demandTitle,\r\n                company:\r\n                  item.displayRestrictions === 2\r\n                    ? formatCompanyName(item.companyName)\r\n                    : item.companyName,\r\n                url,\r\n              };\r\n            }, rows);\r\n          }\r\n          this.total = res.total;\r\n          // this.items = res.rows;\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    onTabChange(index) {\r\n      if (index !== this.tabIndex) {\r\n        this.tabIndex = index;\r\n        this.pageNum = 1;\r\n        const item = this.tabs[index] || {};\r\n        this.getDemandData(item.dictValue);\r\n      }\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.pageNum = 1;\r\n      const item = this.tabs[this.tabIndex] || {};\r\n      this.getDemandData(item.dictValue);\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      const item = this.tabs[this.tabIndex] || {};\r\n      this.getDemandData(item.dictValue);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n\r\n.damand-tab-container {\r\n  .tab-main {\r\n    position: relative;\r\n    display: flex;\r\n    flex-shrink: 0;\r\n    width: 100%;\r\n    flex-direction: row;\r\n    ::v-deep .el-scrollbar__wrap {\r\n      overflow-x: hidden;\r\n      overflow-y: auto;\r\n    }\r\n    .left {\r\n      width: 148px;\r\n      height: 580px;\r\n      background: #21c9b8;\r\n      .tab-content {\r\n        padding: 24px 0 24px 18px;\r\n        &-item {\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          flex-shrink: 0;\r\n          height: 40px;\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #ffffff;\r\n          line-height: 14px;\r\n          transition: background, color 0.25ms ease;\r\n          margin-bottom: 12px;\r\n          cursor: pointer;\r\n          &.active {\r\n            color: #21c9b8;\r\n            background: linear-gradient(270deg, #fbfdff 0%, #ffffff 100%);\r\n          }\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .right {\r\n      flex: 1;\r\n      padding-left: 36px;\r\n      .card {\r\n        width: 100%;\r\n        min-height: 318px;\r\n        background: #ffffff;\r\n        box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.1);\r\n        margin-bottom: 24px;\r\n        &-img {\r\n          width: 100%;\r\n          height: 200px;\r\n          background: #ffffff;\r\n        }\r\n        &-footer {\r\n          padding: 16px 24px;\r\n          .title {\r\n            // @include multiEllipsis(2);\r\n            @include ellipsis;\r\n            font-size: 18px;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            line-height: 26px;\r\n            margin-bottom: 12px;\r\n          }\r\n          .subtitle {\r\n            @include ellipsis;\r\n            font-size: 14px;\r\n            font-weight: 400;\r\n            color: #666666;\r\n            line-height: 14px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.damand-tab-container {\r\n  .tab-page-end {\r\n    .company-tab-pagination {\r\n      width: 240px;\r\n      margin: 0 auto;\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #ffffff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        margin: 0 6px;\r\n        color: #333;\r\n      }\r\n      .el-pager {\r\n        .number {\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 1px solid #d9d9d9;\r\n          background: #ffffff;\r\n          border-radius: 4px;\r\n          line-height: 32px;\r\n          margin: 0 6px;\r\n          &.active {\r\n            background: #21c9b8;\r\n            border: 1px solid #21c9b8;\r\n            color: #fff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}