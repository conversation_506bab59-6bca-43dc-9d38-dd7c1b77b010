{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\publishEquipment\\index.vue?vue&type=style&index=0&id=6f596b80&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\publishEquipment\\index.vue", "mtime": 1750311963078}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgcmdiKDIyNSwgMjQ3LCAyNDApLCByZ2IoMjQ0LCAyNTIsIDI1MCkpOw0KICBoZWlnaHQ6IDEwMHZoOw0KfQ0KDQouZm9ybVN0eWxlIHsNCiAgcGFkZGluZzogMjBweDsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogMTBweDsNCiAgLmZvb3Rlci1zdWJtaXQgew0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwLA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/publishEquipment", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row\r\n      :gutter=\"20\"\r\n      style=\"background: linear-gradient(to right, #e1f7f0, #f4fcfa)\"\r\n    >\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"formStyle\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"设备名称\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" placeholder=\"请输入设备名称\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"设备分类\" prop=\"category\">\r\n              <el-select\r\n                v-model=\"form.category\"\r\n                placeholder=\"请选择设备分类\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in deviceMenuList\"\r\n                  :key=\"dict.dictLabel\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"设备规格\" prop=\"specifications\">\r\n              <el-input\r\n                v-model=\"form.specifications\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"所属单位/位置\" prop=\"location\">\r\n              <el-input\r\n                v-model=\"form.location\"\r\n                placeholder=\"请输入所属单位/位置\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"设备用途描述\" prop=\"description\">\r\n              <el-input\r\n                v-model=\"form.description\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"设备图片\" prop=\"images\">\r\n              <ImageUpload v-model=\"form.images\" resultType=\"string\" :limit=\"1\" :multiple=\"false\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"租用模式\" prop=\"rentMode\">\r\n              <el-input v-model=\"form.rentMode\" placeholder=\"请输入租用模式\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"租用价格\" prop=\"rentPrice\">\r\n              <el-input v-model=\"form.rentPrice\" placeholder=\"请输入租用价格\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"压力(MPa)\" prop=\"pressure\">\r\n              <el-input v-model=\"form.pressure\" placeholder=\"请输入压力(MPa)\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"温度\" prop=\"temperature\">\r\n              <el-input v-model=\"form.temperature\" placeholder=\"请输入温度\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"尺寸\" prop=\"dimension\">\r\n              <el-input v-model=\"form.dimension\" placeholder=\"请输入尺寸\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"规格型号\" prop=\"modelNumber\">\r\n              <el-input\r\n                v-model=\"form.modelNumber\"\r\n                placeholder=\"请输入规格型号\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button type=\"primary\" @click=\"onSubmit\">{{\r\n                form.id ? \"保存\" : \"发布\"\r\n              }}</el-button>\r\n              <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\"\r\n                >取消</el-button\r\n              >\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport {\r\n  addDeviceInfo,\r\n  updateDeviceInfo,\r\n  deviceDetailData,\r\n} from \"@/api/manufacturingSharing\";\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      form: {\r\n        id: null,\r\n        name: null,\r\n        category: null,\r\n        specifications: null,\r\n        location: null,\r\n        description: null,\r\n        images: null,\r\n        technicalParams: null,\r\n        rentMode: null,\r\n        rentPrice: null,\r\n        createTime: null,\r\n        updateTime: null,\r\n        checkStatus: null,\r\n        createBy: null,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        name: [\r\n          { required: true, message: \"设备名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        category: [\r\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" },\r\n        ],\r\n        location: [\r\n          { required: true, message: \"所属单位/位置不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      deviceMenuList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getDicts();\r\n    if (this.$route.query.id) {\r\n      this.getDetail();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询字典数据列表 */\r\n    getDicts() {\r\n      let params = { dictType: \"device_share_type\" };\r\n      listData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.deviceMenuList = res.rows;\r\n        }\r\n      });\r\n    },\r\n    getDetail() {\r\n      deviceDetailData(this.$route.query.id).then((res) => {\r\n        if (res.code == 200) {\r\n          this.form = res.data;\r\n        }\r\n      });\r\n    },\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateDeviceInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.$router.go(-1);\r\n            });\r\n          } else {\r\n            this.form.checkStatus = 0;\r\n            let userinfo = JSON.parse(\r\n              window.sessionStorage.getItem(\"userinfo\")\r\n            );\r\n            this.form.createBy = userinfo.memberPhone;\r\n            addDeviceInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"发布成功,请等待审核\");\r\n              this.$router.go(-1);\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.formStyle {\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  .footer-submit {\r\n    text-align: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}