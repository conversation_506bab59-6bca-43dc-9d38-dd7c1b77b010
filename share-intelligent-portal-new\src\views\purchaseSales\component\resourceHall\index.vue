<template>
  <div class="resource-hall-container">
    <!-- banner图 -->
    <div class="resource-hall-banner">
      <img
        src="../../../../assets/resourceHall/resourceHallBanner.png"
        alt=""
      />
    </div>
    <div v-loading="loading">
      <div class="resource-hall-title-content">
        <div class="resource-hall-title-box">
          <div class="resource-hall-divider"></div>
          <div class="resource-hall-title">资源大厅</div>
          <div class="resource-hall-divider"></div>
        </div>
        <div class="resource-hall-search-box">
          <el-form ref="form" class="resource-hall-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.name"
                placeholder="请输入搜索内容"
                class="resource-hall-search-input"
              >
                <el-button
                  slot="append"
                  class="resource-hall-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="resource-hall-card">
        <div class="resource-hall-info-content">
          <div class="resource-hall-search-type-box">
            <el-form ref="formInfo" :model="formInfo">
              <div class="resource-hall-search-line">
                <el-form-item
                  label="供给类型"
                  class="resource-hall-search-line-item"
                >
                  <el-radio-group
                    v-model="formInfo.supplyType"
                    class="resource-hall-search-radio"
                    @input="changeRadio"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button
                      v-for="(item, index) in supplyTypeList"
                      :key="index"
                      :label="item.dictValue"
                      >{{ item.dictLabel }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </div>
              <div class="resource-hall-search-more-line">
                <el-form-item
                  label="技术类别"
                  class="resource-hall-search-more-line-item"
                  :class="{ advanced: !advanced }"
                >
                  <el-radio-group
                    v-model="formInfo.technologyType"
                    class="more-radio"
                    @input="changeRadio"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button
                      v-for="(item, index) in technologyTypeList"
                      :key="index"
                      :label="item.dictLabel"
                      >{{ item.dictLabel }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
                <el-button
                  class="resource-hall-search-more-line-btn"
                  @click="toggleAdvanced"
                  >{{ advanced ? "收起" : "更多"
                  }}<i class="el-icon-arrow-down"></i>
                </el-button>
              </div>
              <div class="resource-hall-search-line">
                <el-form-item
                  label="成果阶段"
                  class="resource-hall-search-line-item"
                >
                  <el-radio-group
                    v-model="formInfo.productStage"
                    class="resource-hall-search-radio"
                    @input="changeRadio"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button
                      v-for="(item, index) in productStageList"
                      :key="index"
                      :label="item.dictValue"
                      >{{ item.dictLabel }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </div>
              <div class="resource-hall-search-line">
                <el-form-item
                  label="合作方式"
                  class="resource-hall-search-line-item"
                >
                  <el-radio-group
                    v-model="formInfo.cooperationMode"
                    class="resource-hall-search-radio"
                    @input="changeRadio"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button
                      v-for="(item, index) in cooperationModeList"
                      :key="index"
                      :label="item.dictValue"
                      >{{ item.dictLabel }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <div
            v-for="(item, index) in data"
            :key="index"
            class="resource-hall-list-item"
            @click="goResourceDetail(item.id)"
          >
            <div class="list-item-content">
              <div class="list-item-img">
                <img
                  v-if="item.productPhoto && item.productPhoto.length > 0"
                  :src="item.productPhoto[0].url"
                  alt=""
                />
                <img
                  v-else
                  src="../../../../assets/purchaseSales/resourceDefault.png"
                  alt=""
                />
              </div>
              <div class="list-item-info">
                <div class="list-item-title">
                  {{ item.supplyName }}
                </div>
                <div class="list-item-text">
                  <div class="list-item-label">应用领域：</div>
                  <div class="list-item-tag-box">
                    <div
                      v-for="(val, num) in item.applicationArea"
                      :key="num"
                      class="lilst-item-tag red-tag"
                    >
                      {{ val }}
                    </div>
                  </div>
                </div>
                <div class="list-item-text">
                  <div class="list-item-label">技术类别：</div>
                  <div class="list-item-tag-box">
                    <div
                      v-for="(val, num) in item.technologyType"
                      :key="num"
                      class="lilst-item-tag blue-tag"
                    >
                      {{ val }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="resource-hall-page-end">
            <el-button class="resource-hall-page-btn" @click="goHome"
              >首页</el-button
            >
            <el-pagination
              v-if="data && data.length > 0"
              background
              layout="prev, pager, next"
              class="resource-hall-pagination"
              :page-size="pageSize"
              :current-page="pageNum"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getResourceHallList } from "@/api/purchaseSales";
import { getDicts } from "@/api/system/dict/data";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      loading: false,
      form: {
        name: "", //搜索内容
      },
      formInfo: {
        supplyType: "", //供给类型
        technologyType: "", //技术类别
        productStage: "", //成果阶段
        cooperationMode: "", //合作方式
      },
      supplyTypeList: [], //供给类型列表
      technologyTypeList: [], //技术类别列表
      productStageList: [], //成果阶段列表
      cooperationModeList: [], //合作方式列表
      advanced: false,
      data: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  created() {
    this.getDictsList("supply_type", "supplyTypeList");
    this.getDictsList("technology_type", "technologyTypeList");
    this.getDictsList("product_stage", "productStageList");
    this.getDictsList("cooperation_mode", "cooperationModeList");
    this.search();
  },
  methods: {
    search() {
      this.loading = true;
      getResourceHallList({
        ...this.form,
        ...this.formInfo,
        auditStatus: 2,
        displayStatus: 1,
        pageNum: this.pageNum,
        // pageSize: this.pageSize,
      })
        .then((res) => {
          console.log(res);
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
          console.log(res);
          this.loading = false;
          let { rows, total } = res || [];
          this.data = rows;
          this.data.forEach((item) => {
            item.productPhoto = item.productPhoto
              ? JSON.parse(item.productPhoto)
              : [];
            item.applicationArea = item.applicationArea
              ? item.applicationArea.split(",")
              : "";
            item.technologyType = item.technologyType
              ? item.technologyType.split(",")
              : "";
          });
          this.total = total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 字典
    getDictsList(code, propertyName) {
      getDicts(code).then((res) => {
        this[propertyName] = res.data || [];
      });
    },
    changeRadio() {
      this.onSearch();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.search();
    },
    onSearch() {
      this.pageNum = 1;
      this.search();
    },
    // 技术类别显示更多
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    // 跳转到资源详情页面
    goResourceDetail(id) {
      let routeData = this.$router.resolve({
        path: "/resourceHallDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到首页
    goHome() {
      this.$router.push({ path: "/index" });
    },
  },
};
</script>

<style lang="scss" scoped>
.resource-hall-container {
  width: 100%;
  .resource-hall-banner {
    width: 100%;
    height: 50vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .resource-hall-title-content {
    width: 100%;
    padding-bottom: 18px;
    .resource-hall-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .resource-hall-title {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .resource-hall-divider {
        width: 48px;
        height: 4px;
        background: #21c9b8;
      }
    }
    .resource-hall-search-box {
      .resource-hall-search-form {
        text-align: center;
        .resource-hall-search-input {
          width: 792px;
          height: 54px;
          .resource-hall-search-btn {
            width: 100px;
          }
        }
      }
    }
  }
  .resource-hall-card {
    background: #f4f5f9;
    padding-top: 40px;
    .resource-hall-info-content {
      width: 1200px;
      margin: 0 auto;
      .resource-hall-search-type-box {
        background: #fff;
        margin-bottom: 17px;
        .resource-hall-search-line {
          padding: 14px 24px 4px;
          .resource-hall-search-line-item {
            margin-bottom: 0;
            .resource-hall-search-radio {
              width: 1050px;
              margin-top: 11px;
            }
          }
          & + .resource-hall-search-line {
            border-top: 1px solid #f5f5f5;
          }
        }
        .resource-hall-search-more-line {
          display: flex;
          justify-content: space-between;
          padding: 14px 24px 4px;
          border-top: 1px solid #f5f5f5;
          border-bottom: 1px solid #f5f5f5;
          .resource-hall-search-more-line-item {
            flex: 1;
            margin-bottom: 0;
            display: flex;
            &.advanced {
              overflow: hidden;
              height: 45px;
            }
            .more-radio {
              margin-top: 11px;
              flex: 1;
            }
          }
          .resource-hall-search-more-line-btn {
            display: inline-block;
            width: 64px;
            height: 24px;
            background: #fff;
            border-radius: 2px;
            border: 1px solid #d9d9d9;
            font-size: 12px;
            font-family: PingFangSC-Regular, PingFang SC;
            color: #333;
            display: flex;
            align-items: center;
            padding: 0 16px;
            margin-top: 5px;
            &:hover {
              border: 1px solid #21c9b8;
              color: #21c9b8;
            }
          }
        }
      }
      .resource-hall-list-item {
        width: 100%;
        background: #fff;
        border-radius: 12px;
        .list-item-content {
          display: flex;
          padding: 24px;
          cursor: pointer;
          .list-item-img {
            width: 180px;
            height: 128px;
            img {
              width: 100%;
              height: 100%;
              border-radius: 4px;
            }
          }
          .list-item-info {
            padding-left: 24px;
            font-family: PingFangSC-Regular, PingFang SC;
            .list-item-title {
              width: 922px;
              height: 20px;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              font-size: 20px;
              font-weight: 500;
              color: #323233;
              line-height: 20px;
              margin-bottom: 24px;
              word-wrap: break-word;
            }
            .list-item-text {
              display: flex;
              align-items: top;
              .list-item-label {
                color: #323233;
                line-height: 14px;
                margin-top: 16px;
              }
              .list-item-tag-box {
                display: flex;
                width: 852px;
                flex-wrap: wrap;
                .lilst-item-tag {
                  max-width: 840px;
                  padding: 0px 12px;
                  border-radius: 4px;
                  font-size: 12px;
                  line-height: 24px;
                  text-align: center;
                  margin-right: 16px;
                  margin-top: 12px;
                  word-wrap: break-word;
                  text-align: left;
                }
                .red-tag {
                  color: #21c9b8;
                  background: #21c9b8 1a;
                }
                .blue-tag {
                  background: #214dc51a;
                  color: #214dc5;
                }
              }
            }
          }
          &:hover {
            .list-item-title {
              color: #21c9b8;
            }
          }
        }
        & + .resource-hall-list-item {
          margin-top: 24px;
        }
      }
      .resource-hall-page-end {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        padding: 24px 0 60px;
        .resource-hall-page-btn {
          width: 82px;
          height: 32px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #333;
          line-height: 10px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.resource-hall-container {
  .resource-hall-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #21c9b8;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #fff;
      line-height: 24px;
    }
  }
  .el-form-item__label {
    width: 88px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #999;
    padding-right: 32px;
    text-align: left;
  }
  .resource-hall-search-more-line {
    .el-form-item__content {
      width: 970px;
    }
  }
  .el-radio-button {
    padding-bottom: 20px;
    .el-radio-button__inner {
      border: none;
      padding: 0 32px 0 0;
      background: none;
      &:hover {
        color: #21c9b8;
      }
    }
    &.is-active {
      .el-radio-button__inner {
        color: #21c9b8;
        background: none;
      }
    }
    .el-radio-button__orig-radio:checked {
      & + .el-radio-button__inner {
        box-shadow: unset;
      }
    }
  }
  .resource-hall-page-end {
    .resource-hall-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
}
</style>
