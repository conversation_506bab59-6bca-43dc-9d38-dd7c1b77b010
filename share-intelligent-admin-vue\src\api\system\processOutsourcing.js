import request from '@/utils/request'

// 查询工序外协列表
export function listProcessOutsourcing(query) {
  return request({
    url: '/system/processOutsourcing/list',
    method: 'get',
    params: query
  })
}

// 查询工序外协详细
export function getProcessOutsourcing(id) {
  return request({
    url: '/system/processOutsourcing/' + id,
    method: 'get'
  })
}

// 新增工序外协
export function addProcessOutsourcing(data) {
  return request({
    url: '/system/processOutsourcing',
    method: 'post',
    data: data
  })
}

// 修改工序外协
export function updateProcessOutsourcing(data) {
  return request({
    url: '/system/processOutsourcing',
    method: 'put',
    data: data
  })
}

// 删除工序外协
export function delProcessOutsourcing(id) {
  return request({
    url: '/system/processOutsourcing/' + id,
    method: 'delete'
  })
}
