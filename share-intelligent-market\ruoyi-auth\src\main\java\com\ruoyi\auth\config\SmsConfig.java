package com.ruoyi.auth.config;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "ali")
public class SmsConfig {

    @Value("${ali.sign}")
    private String sign;

    @Value("${ali.template}")
    private String template;

    @Value("${ali.ACCESS_KEY_SECRET}")
    private String accessKeySecret;

    @Value("${ali.ACCESS_KEY_ID}")
    private String accessKeyId;

    @Value("${ali.product}")
    private String product;

    @Value("${ali.domain}")
    private String domain;

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this).append("sign", sign).append("template", template).append("accessKeySecret", accessKeySecret).append("accessKeyId", accessKeyId).append("product", product).append("domain", domain).toString();
    }
}
