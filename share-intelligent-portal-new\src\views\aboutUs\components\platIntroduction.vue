<template>
  <div class="content">
    <div class="content_banner">
      <div class="content_bannerTitle">易复材共享智造工业互联网平台</div>
      <div class="content_bannerBox"></div>
      <div class="content_bannerContent">创国内引领的行业级工业互联网平台</div>
    </div>
    <!-- <div class="aboutUsBody">
      <img src="@/assets/aboutUs/aboutUsBody.png" alt="" />
    </div> -->
    <div class="company">
      <div class="card-container">
        <div class="company-top">
          <img src="@/assets/aboutUs/company.png" alt="">
          <div class="company-message">
            <div class="company-title">易复材共享智造工业互联网平台</div>
            <div class="company-content">
              易复材共享智造工业互联网平台以“4+1+1”模式为核心，通过制造、集采、服务、创新四大共享，
              整合资源协同生产；依托共享示范工厂推动高端应用落地, 以数字化底座赋能设备互联与智能管理，
              助力复合材料产业向集约化、智能化、绿色化转型升级
            </div>
          </div>
        </div>
        <div class="description">
          <ul class="list">
            <li>
              <div class="imagebox">
                为集群赋能，创共享生态
                <!-- <img src="@/assets/aboutUs/company.jpg" alt="" /> -->
              </div>
              <div class="listR">
                <p>
                  易复材共享智造工业互联网平台以“共享、开放、协同、创新”为核心理念，依托枣强复合材料产业
                  集群的深厚产业基础，整合恒润集团20余年制造经验与河北省复合材料产业技术研究院的科研服务能
                  力，打造 “4+1+1” 共享智造创新平台。
                </p>
                <p>
                  通过制造共享、集采共享、服务共享、创新共享四大模块，构建资源高效协同的产业生态，赋能复合
                  材料产业向高端化、智能化、绿色化转型。
                  制造共享整合集群设备与订单资源，实现分布式协同生产，
                  盘活闲置产能，助力企业轻资产运营；集采共享通过“集采+仓储+物流+金融”一体化模式，
                  降低采 购成本， 并为企业提供高效资金支持。
                </p>
                <p>
                  服务共享联合第三方机构，覆盖人才培育、技能培训、技术咨询等全周期需求，赋能企业高效成长；
                  创新共享推动产学研深度融合，加速技术攻关与成果转化。
                  平台依托共享示范工厂引入先进工艺设备，
                  承接高端应用领域订单，并通过数字化底座实现设备互联与数据互通，构建安全高效的智能管理体系。
                  以资源开放共享与技术赋能为驱动，助力复合材料产业迈向集约化、数字化、生态化发展新阶段，为
                  区域经济高质量发展注入核心动能。
                </p>
                <!--  <p>
                  易复材共享智造工业互联网平台以“共享、开放、协同、创新”为核心理念，依托枣强复合材料产业集群的深厚产业基础，整合恒润集团20余年制造经验与河北省复合材料产业技术研究院的科研服务能力，打造“4+1+1”共享智造创新平台。通过制造共享、集采共享、服务共享、创新共享四大模块，构建资源高效协同的产业生态，赋能复合材料产业向高端化、智能化、绿色化转型。
                  制造共享整合集群设备与订单资源，实现分布式协同生产，盘活闲置产能，助力企业轻资产运营；集采共享通过“集采+仓储+物流+金融”一体化模式，降低采购成本，并为企业提供高效资金支持；服务共享联合第三方机构，覆盖人才培育、技能培训、技术咨询等全周期需求，赋能企业高效成长；创新共享推动产学研深度融合，加速技术攻关与成果转化。
                  平台依托共享示范工厂引入先进工艺设备，承接高端应用领域订单，并通过数字化底座实现设备互联与数据互通，构建安全高效的智能管理体系。以资源开放共享与技术赋能为驱动，助力复合材料产业迈向集约化、数字化、生态化发展新阶段，为区域经济高质量发展注入核心动能。
                </p> -->
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <!-- <div class="course_box">
      <div class="aboutMengdou">发展历程</div>
      <ul class="course_ul wow">
        <li v-for="item, i of list" :ref="`list_item${i + 1}`" class="animate__animated" :key="i">
          <div class="course_ul_l" :class="[i % 2 != 0 ? 'course_ul_la' : 'course_ul_lb']">
            <span>{{ item.time }}</span>
          </div>
          <div class="course_ul_r">
            <p>{{ item.text }}</p>
          </div>
        </li>
      </ul>
    </div> -->
    <div class="flexFooter ">
      <div class="card-container">
        <div class="process-title">平台发展历程</div>
        <div class="process">
          <img class="line" src="@/assets/aboutUs/process-line.png" alt="">
          <div class="process-2021">
            <div class="process-time">2021年</div>
            <div class="process-content">
              寻共需
            </div>
          </div>
          <div class="process-2022">
            <div class="process-content">
              促共享
            </div>
            <div class="process-time">2022年</div>
          </div>
          <div class="process-2023">
            <div class="process-time">2023年</div>
            <div class="process-content">
              谋发展
            </div>
          </div>
          <div class="process-2025">
            <div class="process-content">
              创未来
            </div>
            <div class="process-time">2025年</div>
          </div>
        </div>

        <video class="videoRef" src="@/assets/video/aboutUs.mp4" loop isMuted="true" controls autoplay
          playsinline></video>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.content {
  width: 100%;
  // padding-bottom: 60px;
}

.aboutUsBody {
  width: 1200px;
  height: 650px;
  margin: 30px auto;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .content_bannerBox {
    height: 11px;
  }

  .content_bannerContent {
    font-size: 22px;
    color: #222222;
  }

  .content_bannerTitle {
    font-size: 40px;
    color: #222222;
  }
}

.flexFooter {
  padding-bottom: 80px;
  box-sizing: border-box;
  background: url("../../../assets/aboutUs/aboutUs-footer.png");
  background-size: 100% auto;
  background-position: bottom;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;

  .process-title {
    font-weight: 500;
    font-size: 34px;
    color: #000000;
  }

  .process {
    position: relative;
    width: 100%;
    height: 473px;
    background-image: url("../../../assets/aboutUs/process.png");
    background-size: 100% 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .line {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 100%;
    }

    .process-2021 {
      position: absolute;
      left: 12%;
      top: 65%;
    }

    .process-2022 {
      position: absolute;
      left: 33%;
      top: 45%;
    }

    .process-2023 {
      position: absolute;
      left: 53%;
      top: 50%;
    }

    .process-2025 {
      position: absolute;
      left: 75%;
      top: 30%;
    }

    .process-time {
      font-weight: 500;
      font-size: 18px;
      color: #000000;
    }

    .process-content {
      font-weight: 400;
      font-size: 22px;
      color: #3E3D3D;
    }
  }

  .videoRef {
    width: 1200px;
  }

}
</style>

<style lang="scss" scoped>
.company {
  width: 100%;
  height: 890px;
  background-image: url("../../../assets/aboutUs/company-bg.png");
  background-size: 100%;
  /* 背景图片覆盖整个元素 */
  background-repeat: no-repeat;
  /* 背景图片不重复 */
  background-position: 0 0;
  /* 背景图片居中 */
  padding-top: 75px;
  box-sizing: border-box;

  .company-top {
    width: 100%;
    height: 300px;
    display: flex;

    img {
      height: 100%;
    }

    .company-message {
      height: 100%;
      background-color: #F5F6F6;
      padding: 0 60px;
      display: flex;
      flex-direction: column;
      justify-content: center;

      .company-title {
        font-size: 34px;
        color: #000000;
        font-weight: 500;
        padding-bottom: 45px;
      }

      .company-content {
        font-weight: 400;
        font-size: 16px;
        color: #3E3D3D;
        line-height: 28px;
        opacity: 0.94;
      }
    }
  }

  .description {
    width: 100%;
    margin: 0 auto;

    .list {
      width: 100%;
      padding: 0;
      // margin: 0 auto;
      padding: 65px 0;

      li {
        display: flex;
        justify-content: space-between;

        .listR {
          width: 713px;
          height: 362px;
          display: flex;
          flex-direction: column;
          justify-content: space-around;

          p {
            margin: 0;
            font-weight: 400;
            font-size: 16px;
            color: #3E3D3D;
            line-height: 26px;
          }
        }

        .imagebox {
          flex: 1;
          display: flex;
          align-items: center;
          // text-align: center;
          font-size: 34px;
          color: #000000;
          // img {
          //   width: 100%;
          // }
        }

      }
    }
  }
}

// .swiper-pagination-bullet {
//   background: #fff;
// }

// .swiper-wrapper {
//   position: relative;
// }

// .swiper-container {
//   width: 100%;
// }

// .swiper-container2 {
//   width: 100%;
//   overflow: hidden;
// }

.container {
  width: 100%;
}

.banner {
  width: 100%;
  height: 400px;
}
</style>
<style scoped>
.course_box {
  background: url("../../../assets/aboutUs/enterBgc.png") no-repeat center center;
  background-size: 100% 100%;
  height: 920px;
  overflow: hidden;
}

.course_box .course_ul {
  width: 1226px;
  margin: 0px auto 50px;
  display: flex;
  flex-direction: column;
}

.course_box .course_ul li {
  display: flex;
  margin-top: 20px;
  opacity: 0;
}

.course_box .course_ul li .course_ul_la::after {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  width: 14px;
  height: 14px;
  border: 1px solid #21c9b8;
  border-radius: 50%;
  background-color: #fbfbfa;
}

.course_box .course_ul li .course_ul_lb::after {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  width: 14px;
  height: 14px;
  background: #21c9b8;
  border-radius: 50%;
}

.course_box .course_ul li .course_ul_l {
  position: relative;
  width: 120px;
  position: relative;
  margin-right: 20px;
}

.course_box .course_ul li .course_ul_l::before {
  content: "";
  position: absolute;
  right: 6px;
  top: 0;
  width: 2px;
  height: 50px;
  background: linear-gradient(0deg, #f6f6f6, #21c9b8);
}

.course_box .course_ul li .course_ul_l span {
  color: #333333;
  font-size: 26px;
}

.course_box .course_ul li .course_ul_r {
  flex: 1;
  height: 60px;
  border: 1px solid #ffffff;
  background-color: rgba(247, 247, 247, 0.8);
  padding: 0 20px;
  line-height: 16px;
}

.course_box .course_ul li .course_ul_r p {
  font-size: 16px;
  color: #5a5a5a;
}

.aboutMengdou {
  width: 100%;
  text-align: center;
  padding: 50px 0 50px;
  box-sizing: border-box;
  font-size: 30px;
  color: #333333;
}
</style>

<script>
import WOW from 'wow.js';
import 'animate.css';

export default {
  name: "Com",
  data() {
    return {
      activeIndex: 0,
      list: [
        {
          time: '2023',
          text: "荣获国家级跨领域跨行业工业互联网平台"
        },
        {
          time: '2022',
          text: "建立云端研发新模式，成长为瞪羚企业，获评山东省双跨工业互联网平台、国家特色专业型工业互联网平台"
        },
        {
          time: '2021',
          text: "建立上百条优势物料资源线，上线事业合伙人模式，打破发展瓶颈，建立青岛市中小企业供应链场景实验室"
        },
        {
          time: '2020',
          text: "智能制造模式上线，平台发展扩展至电子制造、冷库冷藏仪器仪表、新零售畜牧业养殖五大行业营收破2亿"
        },
        {
          time: '2019',
          text: "完成A轮融资，玺品直播上线，企业级在线支付工具-楼豆宝上线，获批国家商务部认定的线上线下融合数字商务企业"
        }, {
          time: '2018',
          text: "玺品创新模式上线，智能采购模式行业扩展"
        }, {
          time: '2017',
          text: "总部落地青岛，首个智能采购机器人--豆小秘上线，智能采购模式上线，颠覆式赋能"
        }, {
          time: '2016',
          text: "首创上下游裂变式上线模式，檬豆云实现5次迭代，探索智能采购模式"
        },
        {
          time: '2015',
          text: "公司成立于上海，完成天使轮融资檬豆云上线"
        },
      ],
    };
  },

  methods: {
    handleMouseEnter(index) {
      this.activeIndex = index;
    },
    handleMouseLeave() {
      // 鼠标离开后恢复默认(第一个保持黑色)
      this.activeIndex = 0;
    },
    initWow() {
      var wow = new WOW({
        boxClass: "wow",
        animateClass: "animated",
        offset: 200,
        mobile: true,
        live: true,
        callback: (e) => {
          // console.log(this.$refs.list_item1);
          for (let i = 0; i < this.list.length; i++) {
            setTimeout(() => {
              var dom = this.$refs[`list_item${i + 1}`][0]
              console.log(this.$refs[`list_item${i + 1}`][0]);
              dom.classList.add('animate__fadeInUpBig');
              dom.style.opacity = 1;
            }, i * 220);
          }
        },
        scrollContainer: null,
        resetAnimation: true,
      });
      wow.init();
    }
  },

  computed: {},

  created() { },

  mounted() {
    this.initWow();
  },


  components: {},

  watch: {},
  mixins: [],
};
</script>
