{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\index.vue?vue&type=template&id=6f35d124&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\index.vue", "mtime": 1750311962991}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}