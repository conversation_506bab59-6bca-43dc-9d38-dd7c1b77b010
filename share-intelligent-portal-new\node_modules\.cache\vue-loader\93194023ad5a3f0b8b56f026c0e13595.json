{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\resource\\index.vue?vue&type=style&index=0&id=104058aa&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\resource\\index.vue", "mtime": 1750311963001}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYWN0aXZpdHktY29udGFpbmVyIHsNCiAgd2lkdGg6IDEwMCU7DQogIGJhY2tncm91bmQ6ICNmNGY1Zjk7DQogIC5hY3Rpdml0eS1iYW5uZXIgew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGhlaWdodDogNTB2aDsNCiAgICBpbWcgew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgICBoZWlnaHQ6IDEwMCU7DQogICAgfQ0KICB9DQogIC5hY3Rpdml0eS10aXRsZS1jb250ZW50IHsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICAgIHBhZGRpbmctYm90dG9tOiAxOHB4Ow0KICAgIC5hY3Rpdml0eS10aXRsZS1ib3ggew0KICAgICAgd2lkdGg6IDMzNnB4Ow0KICAgICAgbWFyZ2luOiAwIGF1dG87DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIHBhZGRpbmc6IDYwcHggMCA0MHB4Ow0KICAgICAgLmFjdGl2aXR5LXRpdGxlIHsNCiAgICAgICAgZm9udC1zaXplOiA0MHB4Ow0KICAgICAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1NZWRpdW0sIFBpbmdGYW5nIFNDOw0KICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDQwcHg7DQogICAgICAgIHBhZGRpbmc6IDAgNDBweDsNCiAgICAgIH0NCiAgICAgIC5hY3Rpdml0eS1kaXZpZGVyIHsNCiAgICAgICAgd2lkdGg6IDQ4cHg7DQogICAgICAgIGhlaWdodDogNHB4Ow0KICAgICAgICBiYWNrZ3JvdW5kOiAjMjFjOWI4Ow0KICAgICAgfQ0KICAgIH0NCiAgICAuYWN0aXZpdHktc2VhcmNoLWJveCB7DQogICAgICAuYWN0aXZpdHktc2VhcmNoLWZvcm0gew0KICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgIC5hY3Rpdml0eS1zZWFyY2gtaW5wdXQgew0KICAgICAgICAgIHdpZHRoOiA3OTJweDsNCiAgICAgICAgICBoZWlnaHQ6IDU0cHg7DQogICAgICAgICAgLmFjdGl2aXR5LXNlYXJjaC1idG4gew0KICAgICAgICAgICAgd2lkdGg6IDEwMHB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICAuYWN0aXZpdHktaW5mby1jb250ZW50IHsNCiAgICB3aWR0aDogMTIwMHB4Ow0KICAgIG1hcmdpbjogNDBweCBhdXRvIDA7DQogICAgLmFjdGl2aXR5LXNlYXJjaC10eXBlLWJveCB7DQogICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgbWFyZ2luLWJvdHRvbTogLTdweDsNCiAgICAgIC5hY3Rpdml0eS1zZWFyY2gtbGluZSB7DQogICAgICAgIHBhZGRpbmc6IDE0cHggMjRweDsNCiAgICAgICAgLmFjdGl2aXR5LXNlYXJjaC1saW5lLWl0ZW0gew0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDA7DQogICAgICAgIH0NCiAgICAgICAgJiArIC5hY3Rpdml0eS1zZWFyY2gtbGluZSB7DQogICAgICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNmNWY1ZjU7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogICAgLmFjdGl2aXR5LWxpc3QtaXRlbSB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICAgICAgbWFyZ2luLXRvcDogMjRweDsNCiAgICAgIC5saXN0LWl0ZW0tY29udGVudCB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIHBhZGRpbmc6IDI0cHggMzJweDsNCiAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgICAubGlzdC1pdGVtLWltZyB7DQogICAgICAgICAgd2lkdGg6IDIzMHB4Ow0KICAgICAgICAgIGhlaWdodDogMTY0cHg7DQogICAgICAgICAgaW1nIHsNCiAgICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNXB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAubGlzdC1pdGVtLWluZm8gew0KICAgICAgICAgIC8vIG1hcmdpbjogYXV0byAwOw0KICAgICAgICAgIHBhZGRpbmctbGVmdDogMjRweDsNCiAgICAgICAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1SZWd1bGFyLCBQaW5nRmFuZyBTQzsNCiAgICAgICAgICAubGlzdC1pdGVtLXRpdGxlIHsNCiAgICAgICAgICAgIHdpZHRoOiA4MDZweDsNCiAgICAgICAgICAgIC8vIGhlaWdodDogMjRweDsNCiAgICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOyAvKuiuqeaIquaWreeahOaWh+Wtl+aYvuekuuS4uueCueeCueOAgui/mOacieS4gOS4quWAvOaYr2NsaXDmhI/miKrmlq3kuI3mmL7npLrngrnngrkqLw0KICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsgLyrorqnmloflrZfkuI3mjaLooYwqLw0KICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsgLyrotoXlh7ropoHpmpDol48qLw0KICAgICAgICAgICAgZm9udC1zaXplOiAyNHB4Ow0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICAgIGNvbG9yOiAjMzIzMjMzOw0KICAgICAgICAgICAgLy8gbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgICAgICAgICBtYXJnaW46IDhweCAwIDI0cHg7DQogICAgICAgICAgICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7DQogICAgICAgICAgfQ0KICAgICAgICAgIC5saXN0LWl0ZW0tdGV4dCB7DQogICAgICAgICAgICB3aWR0aDogODA2cHg7DQogICAgICAgICAgICBoZWlnaHQ6IDYwcHg7DQogICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICAgICAgZGlzcGxheTogLXdlYmtpdC1ib3g7DQogICAgICAgICAgICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsOw0KICAgICAgICAgICAgLXdlYmtpdC1saW5lLWNsYW1wOiAyOw0KICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICBjb2xvcjogIzY2NjsNCiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAzMHB4Ow0KICAgICAgICAgICAgd29yZC13cmFwOiBicmVhay13b3JkOw0KICAgICAgICAgIH0NCiAgICAgICAgICAubGlzdC1pdGVtLXRpbWUgew0KICAgICAgICAgICAgY29sb3I6ICM5OTk7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMTRweDsNCiAgICAgICAgICAgIG1hcmdpbi10b3A6IDI0cHg7DQogICAgICAgICAgfQ0KICAgICAgICAgIC50YWdTdHlsZSB7DQogICAgICAgICAgICBtYXJnaW4tbGVmdDogMjBweDsNCiAgICAgICAgICB9DQogICAgICAgICAgLnRhZ1N0eWxlOm50aC1jaGlsZCgxKSB7DQogICAgICAgICAgICBtYXJnaW4tbGVmdDogMDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgJjpob3ZlciB7DQogICAgICAgICAgLmxpc3QtaXRlbS10aXRsZSB7DQogICAgICAgICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogICAgLmFjdGl2aXR5LXBhZ2UtZW5kIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBtYXJnaW46IDAgYXV0bzsNCiAgICAgIHBhZGRpbmc6IDI0cHggMCA2MHB4Ow0KICAgICAgLmFjdGl2aXR5LXBhZ2UtYnRuIHsNCiAgICAgICAgd2lkdGg6IDgycHg7DQogICAgICAgIGhlaWdodDogMzJweDsNCiAgICAgICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICAgICAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1SZWd1bGFyLCBQaW5nRmFuZyBTQzsNCiAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICAgIGxpbmUtaGVpZ2h0OiAxMHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyuBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/resource", "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/resource/resourceBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-title-box\">\r\n          <div class=\"activity-divider\"></div>\r\n          <div class=\"activity-title\">链资源</div>\r\n          <div class=\"activity-divider\"></div>\r\n        </div>\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.name\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"activity-info-content\">\r\n        <div class=\"activity-search-type-box\">\r\n          <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n            <div class=\"activity-search-line\">\r\n              <el-form-item label=\"资源类型\" class=\"activity-search-line-item\">\r\n                <el-radio-group\r\n                  v-model=\"formInfo.supplyType\"\r\n                  class=\"activity-search-radio\"\r\n                  @input=\"changeRadio\"\r\n                >\r\n                  <el-radio-button label=\"\">全部</el-radio-button>\r\n                  <el-radio-button\r\n                    v-for=\"(item, index) in resourceTypeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.dictValue\"\r\n                    >{{ item.dictLabel }}</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n              <el-form-item\r\n                v-if=\"\r\n                  formInfo.supplyType !== 4 &&\r\n                  formInfo.supplyType !== 6 &&\r\n                  formInfo.supplyType !== 7\r\n                \"\r\n                label=\"技术类别\"\r\n                class=\"activity-search-line-item\"\r\n              >\r\n                <el-radio-group\r\n                  v-model=\"formInfo.technologyType\"\r\n                  class=\"activity-search-radio\"\r\n                  @input=\"changeRadio\"\r\n                >\r\n                  <el-radio-button label=\"\">全部</el-radio-button>\r\n                  <el-radio-button\r\n                    v-for=\"(item, index) in technologyTypeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.dictLabel\"\r\n                    >{{ item.dictLabel }}</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n              <el-form-item\r\n                v-if=\"formInfo.supplyType === 4\"\r\n                label=\"技术类别\"\r\n                class=\"activity-search-line-item\"\r\n              >\r\n                <el-radio-group\r\n                  v-model=\"techniqueTypeName\"\r\n                  class=\"activity-search-radio\"\r\n                  @input=\"changeRadio\"\r\n                >\r\n                  <el-radio-button label=\"\">全部</el-radio-button>\r\n                  <el-radio-button\r\n                    v-for=\"(item, index) in technologyTypeList2\"\r\n                    :key=\"index\"\r\n                    :label=\"item.dictLabel\"\r\n                    >{{ item.dictLabel }}</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n              <el-form-item\r\n                label=\"成果阶段\"\r\n                class=\"activity-search-line-item\"\r\n                v-if=\"\r\n                  formInfo.supplyType !== 4 &&\r\n                  formInfo.supplyType !== 6 &&\r\n                  formInfo.supplyType !== 7\r\n                \"\r\n              >\r\n                <el-radio-group\r\n                  v-model=\"formInfo.productStage\"\r\n                  class=\"activity-search-radio\"\r\n                  @input=\"changeRadio\"\r\n                >\r\n                  <el-radio-button label=\"\">全部</el-radio-button>\r\n                  <el-radio-button\r\n                    v-for=\"(item, index) in achievementList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.dictValue\"\r\n                    >{{ item.dictLabel }}</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n              <el-form-item\r\n                label=\"合作方式\"\r\n                class=\"activity-search-line-item\"\r\n                v-if=\"\r\n                  formInfo.supplyType !== 4 &&\r\n                  formInfo.supplyType !== 6 &&\r\n                  formInfo.supplyType !== 7\r\n                \"\r\n              >\r\n                <el-radio-group\r\n                  v-model=\"formInfo.cooperationMode\"\r\n                  class=\"activity-search-radio\"\r\n                  @input=\"changeRadio\"\r\n                >\r\n                  <el-radio-button label=\"\">全部</el-radio-button>\r\n                  <el-radio-button\r\n                    v-for=\"(item, index) in cooperationModeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.dictValue\"\r\n                    >{{ item.dictLabel }}</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n        <div\r\n          v-if=\"\r\n            formInfo.supplyType !== 4 &&\r\n            formInfo.supplyType !== 6 &&\r\n            formInfo.supplyType !== 7\r\n          \"\r\n        >\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"activity-list-item\"\r\n            @click=\"goResourceDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img\r\n                  v-if=\"\r\n                    item.productPhoto &&\r\n                    item.productPhoto.length > 0 &&\r\n                    item.productPhoto[0].url\r\n                  \"\r\n                  :src=\"item.productPhoto[0].url\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../assets/purchaseSales/resourceDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"list-item-info\" style=\"margin: auto 0\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.supplyName }}\r\n                </div>\r\n                <div\r\n                  style=\"\r\n                    margin-top: 10px;\r\n                    margin-left: 10px;\r\n                    display: flex;\r\n                    align-items: center;\r\n                  \"\r\n                >\r\n                  <div style=\"color: rgb(77, 77, 78)\">应用领域:</div>\r\n                  <div\r\n                    style=\"margin-left: 10px\"\r\n                    v-if=\"\r\n                      item.applicationArea && item.applicationArea.length > 0\r\n                    \"\r\n                  >\r\n                    <el-tag\r\n                      class=\"tagStyle\"\r\n                      v-for=\"(val, num) in item.applicationArea\"\r\n                      :key=\"num\"\r\n                      >{{ val }}</el-tag\r\n                    >\r\n                  </div>\r\n                  <div v-else style=\"margin-left: 10px\">\r\n                    <el-tag>暂无</el-tag>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  style=\"\r\n                    margin-top: 10px;\r\n                    margin-left: 10px;\r\n                    display: flex;\r\n                    align-items: center;\r\n                  \"\r\n                >\r\n                  <div style=\"color: rgb(77, 77, 78)\">技术类别:</div>\r\n                  <div\r\n                    style=\"margin-left: 10px\"\r\n                    v-if=\"item.technologyType && item.technologyType.length > 0\"\r\n                  >\r\n                    <el-tag\r\n                      class=\"tagStyle\"\r\n                      type=\"success\"\r\n                      v-for=\"(val, num) in item.technologyType\"\r\n                      :key=\"num\"\r\n                      >{{ val }}</el-tag\r\n                    >\r\n                  </div>\r\n                  <div v-else style=\"margin-left: 10px\">\r\n                    <el-tag type=\"success\">暂无</el-tag>\r\n                  </div>\r\n                </div>\r\n                <div\r\n                  v-if=\"formInfo.supplyType === 6\"\r\n                  style=\"\r\n                    margin-top: 10px;\r\n                    margin-left: 10px;\r\n                    display: flex;\r\n                    align-items: center;\r\n                  \"\r\n                >\r\n                  <div style=\"color: rgb(77, 77, 78)\">规格型号:</div>\r\n                  <div style=\"margin-left: 10px\" v-if=\"item.specification\">\r\n                    <el-tag class=\"tagStyle\" type=\"success\">{{\r\n                      item.specification\r\n                    }}</el-tag>\r\n                  </div>\r\n                  <div v-else style=\"margin-left: 10px\">\r\n                    <el-tag type=\"success\">暂无</el-tag>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div v-if=\"formInfo.supplyType === 6\">\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"activity-list-item\"\r\n            @click=\"goInsDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img v-if=\"item.picUrl\" :src=\"item.picUrl\" alt=\"\" />\r\n                <!-- <img\r\n                  v-else\r\n                  src=\"../../assets/purchaseSales/resourceDefault.png\"\r\n                  alt=\"\"\r\n                /> -->\r\n              </div>\r\n              <div class=\"list-item-info\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.name }}\r\n                </div>\r\n                <div\r\n                  style=\"\r\n                    margin-top: 10px;\r\n                    margin-left: 10px;\r\n                    display: flex;\r\n                    align-items: center;\r\n                  \"\r\n                >\r\n                  <div style=\"color: rgb(77, 77, 78)\">规格型号:</div>\r\n                  <div style=\"margin-left: 10px\" v-if=\"item.specification\">\r\n                    <el-tag class=\"tagStyle\" type=\"success\">{{\r\n                      item.specification\r\n                    }}</el-tag>\r\n                  </div>\r\n                  <div v-else style=\"margin-left: 10px\">\r\n                    <el-tag type=\"success\">暂无</el-tag>\r\n                  </div>\r\n                </div>\r\n                <!-- <div class=\"list-item-text\">\r\n                {{ item.activityOverview }}\r\n              </div>\r\n              <div class=\"list-item-time\">{{ item.createTimeStr }}</div> -->\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div v-if=\"formInfo.supplyType === 7\">\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"activity-list-item\"\r\n            @click=\"goLabDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img v-if=\"item.picUrl\" :src=\"item.picUrl\" alt=\"\" />\r\n              </div>\r\n              <div class=\"list-item-info\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.name }}\r\n                </div>\r\n                <div\r\n                  style=\"\r\n                    margin-top: 10px;\r\n                    margin-left: 10px;\r\n                    display: flex;\r\n                    align-items: center;\r\n                  \"\r\n                >\r\n                  <div style=\"color: rgb(77, 77, 78)\">行业领域:</div>\r\n                  <div style=\"margin-left: 10px\" v-if=\"item.industry\">\r\n                    <el-tag class=\"tagStyle\" type=\"success\">{{\r\n                      item.industry\r\n                    }}</el-tag>\r\n                  </div>\r\n                  <div v-else style=\"margin-left: 10px\">\r\n                    <el-tag type=\"success\">暂无</el-tag>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div v-if=\"formInfo.supplyType === 4\">\r\n          <div class=\"expert-library-list\">\r\n            <div\r\n              v-for=\"(item, index) in data\"\r\n              :key=\"index\"\r\n              class=\"list-item-content\"\r\n              @click=\"goExpertLibrary(item.id)\"\r\n            >\r\n              <div class=\"list-item-box\">\r\n                <div class=\"item-headline\">\r\n                  <div class=\"item-title\">\r\n                    {{ item.expertName }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"expert-library-label\">\r\n                  <div\r\n                    v-for=\"(val, index1) in item.techniqueTypeName\"\r\n                    :key=\"index1\"\r\n                    class=\"library-label-item\"\r\n                  >\r\n                    <span v-if=\"index1 < 2\" class=\"expert-library-type\">{{\r\n                      `#${val}`\r\n                    }}</span>\r\n                    <span v-else>…</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"expert-library-box\">\r\n                  {{ item.synopsis }}\r\n                </div>\r\n              </div>\r\n              <div class=\"list-item-img\">\r\n                <img v-if=\"item.headPortrait\" :src=\"item.headPortrait\" alt=\"\" />\r\n                <img\r\n                  v-else\r\n                  src=\"../../assets/expertLibrary/defaultImg.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"activity-page-end\">\r\n          <el-button class=\"activity-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"data && data.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getResourceHallList,\r\n  getExpertList,\r\n  insList,\r\n  laboratoryList,\r\n} from \"@/api/purchaseSales\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        name: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        supplyType: \"\", // 资源类型\r\n        technologyType: \"\", // 技术类别\r\n        productStage: \"\", // 成果\r\n        cooperationMode: \"\", // 合作方式\r\n      },\r\n      techniqueTypeName: \"\", // 专家-技术类别\r\n      resourceTypeList: [\r\n        {\r\n          dictLabel: \"成果\",\r\n          dictValue: 1,\r\n        },\r\n        {\r\n          dictLabel: \"产品\",\r\n          dictValue: 2,\r\n        },\r\n        {\r\n          dictLabel: \"服务\",\r\n          dictValue: 3,\r\n        },\r\n        {\r\n          dictLabel: \"专家\",\r\n          dictValue: 4,\r\n        },\r\n        {\r\n          dictLabel: \"设备\",\r\n          dictValue: 6,\r\n        },\r\n        {\r\n          dictLabel: \"实验室\",\r\n          dictValue: 7,\r\n        },\r\n      ],\r\n      technologyTypeList: [\r\n        {\r\n          dictLabel: \"国产化替代\",\r\n          dictValue: 1,\r\n        },\r\n        {\r\n          dictLabel: \"机器替人\",\r\n          dictValue: 2,\r\n        },\r\n        {\r\n          dictLabel: \"管理提升\",\r\n          dictValue: 3,\r\n        },\r\n        {\r\n          dictLabel: \"质量提升\",\r\n          dictValue: 4,\r\n        },\r\n        {\r\n          dictLabel: \"灭菌消杀\",\r\n          dictValue: 5,\r\n        },\r\n        {\r\n          dictLabel: \"新材料\",\r\n          dictValue: 6,\r\n        },\r\n        {\r\n          dictLabel: \"绿色星碳\",\r\n          dictValue: 7,\r\n        },\r\n      ],\r\n      technologyTypeList2: [\r\n        {\r\n          dictLabel: \"国产化替代\",\r\n          dictValue: 1,\r\n        },\r\n        {\r\n          dictLabel: \"新材料\",\r\n          dictValue: 2,\r\n        },\r\n        {\r\n          dictLabel: \"机器替人\",\r\n          dictValue: 3,\r\n        },\r\n        {\r\n          dictLabel: \"管理提升\",\r\n          dictValue: 4,\r\n        },\r\n        {\r\n          dictLabel: \"技术提升\",\r\n          dictValue: 5,\r\n        },\r\n        {\r\n          dictLabel: \"绿色星碳\",\r\n          dictValue: 6,\r\n        },\r\n        {\r\n          dictLabel: \"集中采购\",\r\n          dictValue: 7,\r\n        },\r\n      ],\r\n      achievementList: [\r\n        {\r\n          dictLabel: \"正在研发\",\r\n          dictValue: 1,\r\n        },\r\n        {\r\n          dictLabel: \"已有样品\",\r\n          dictValue: 2,\r\n        },\r\n        {\r\n          dictLabel: \"通过中试\",\r\n          dictValue: 3,\r\n        },\r\n        {\r\n          dictLabel: \"已量产\",\r\n          dictValue: 4,\r\n        },\r\n      ],\r\n      cooperationModeList: [\r\n        {\r\n          dictLabel: \"双方协商\",\r\n          dictValue: 1,\r\n        },\r\n        {\r\n          dictLabel: \"作价入股\",\r\n          dictValue: 2,\r\n        },\r\n        {\r\n          dictLabel: \"合作转换\",\r\n          dictValue: 3,\r\n        },\r\n        {\r\n          dictLabel: \"专利许可\",\r\n          dictValue: 4,\r\n        },\r\n      ],\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    // this.getDictsList(\"activity_type\", \"activityTypeList\");\r\n    const flag = this.$route.query.flag;\r\n    if (flag) {\r\n      this.formInfo.supplyType = Number(flag);\r\n      if (flag == 4) {\r\n        this.searchExpert();\r\n      }\r\n      if (flag == 6) {\r\n        this.getInsList();\r\n      }\r\n      if (flag == 7) {\r\n        this.getLaboratoryList();\r\n      }\r\n    } else {\r\n      this.search();\r\n    }\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getResourceHallList({\r\n        ...this.form,\r\n        ...this.formInfo,\r\n        auditStatus: 2,\r\n        displayStatus: 1,\r\n        pageNum: this.pageNum,\r\n      })\r\n        .then((res) => {\r\n          console.log(res);\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          console.log(res);\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.data.forEach((item) => {\r\n            item.productPhoto = item.productPhoto\r\n              ? JSON.parse(item.productPhoto)\r\n              : [];\r\n            item.applicationArea = item.applicationArea\r\n              ? item.applicationArea.split(\",\")\r\n              : \"\";\r\n            item.technologyType = item.technologyType\r\n              ? item.technologyType.split(\",\")\r\n              : \"\";\r\n          });\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    searchExpert() {\r\n      this.loading = true;\r\n      getExpertList({\r\n        keywords: this.form.name,\r\n        techniqueTypeName: this.techniqueTypeName,\r\n        pageNum: this.pageNum,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.data.forEach((item) => {\r\n            item.techniqueTypeName = item.techniqueTypeName\r\n              ? item.techniqueTypeName.split(\",\")\r\n              : [];\r\n          });\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    getInsList() {\r\n      let params = {\r\n        ...this.form,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      };\r\n      insList(params)\r\n        .then((res) => {\r\n          this.data = res.rows;\r\n          this.total = res.total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    getLaboratoryList() {\r\n      let params = {\r\n        ...this.form,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      };\r\n      laboratoryList(params)\r\n        .then((res) => {\r\n          this.data = res.rows;\r\n          this.total = res.total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    changeRadio() {\r\n      if (\r\n        this.formInfo.supplyType === \"\" ||\r\n        this.formInfo.supplyType === 1 ||\r\n        this.formInfo.supplyType === 2 ||\r\n        this.formInfo.supplyType === 3\r\n      ) {\r\n        this.pageNum = 1;\r\n        this.pageSize = 10;\r\n        this.search();\r\n      }\r\n      if (this.formInfo.supplyType === 4) {\r\n        this.pageNum = 1;\r\n        this.pageSize = 10;\r\n        this.searchExpert();\r\n      }\r\n      if (this.formInfo.supplyType === 6) {\r\n        this.pageNum = 1;\r\n        this.pageSize = 10;\r\n        this.getInsList();\r\n      }\r\n      if (this.formInfo.supplyType === 7) {\r\n        this.pageNum = 1;\r\n        this.pageSize = 10;\r\n        this.getLaboratoryList();\r\n      }\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.changeRadio();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.changeRadio();\r\n    },\r\n    // 跳转资源详情\r\n    goResourceDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/resourceHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    // 跳转到专家详情页面\r\n    goExpertLibrary(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/expertDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goInsDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/insDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goLabDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/labDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-info-content {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    .activity-search-type-box {\r\n      background: #fff;\r\n      margin-bottom: -7px;\r\n      .activity-search-line {\r\n        padding: 14px 24px;\r\n        .activity-search-line-item {\r\n          margin-bottom: 0;\r\n        }\r\n        & + .activity-search-line {\r\n          border-top: 1px solid #f5f5f5;\r\n        }\r\n      }\r\n    }\r\n    .activity-list-item {\r\n      width: 100%;\r\n      background: #fff;\r\n      border-radius: 12px;\r\n      margin-top: 24px;\r\n      .list-item-content {\r\n        display: flex;\r\n        padding: 24px 32px;\r\n        cursor: pointer;\r\n        .list-item-img {\r\n          width: 230px;\r\n          height: 164px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n            border-radius: 5px;\r\n          }\r\n        }\r\n        .list-item-info {\r\n          // margin: auto 0;\r\n          padding-left: 24px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          .list-item-title {\r\n            width: 806px;\r\n            // height: 24px;\r\n            text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n            white-space: nowrap; /*让文字不换行*/\r\n            overflow: hidden; /*超出要隐藏*/\r\n            font-size: 24px;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            // line-height: 24px;\r\n            margin: 8px 0 24px;\r\n            word-wrap: break-word;\r\n          }\r\n          .list-item-text {\r\n            width: 806px;\r\n            height: 60px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            text-overflow: ellipsis;\r\n            font-size: 16px;\r\n            color: #666;\r\n            line-height: 30px;\r\n            word-wrap: break-word;\r\n          }\r\n          .list-item-time {\r\n            color: #999;\r\n            line-height: 14px;\r\n            margin-top: 24px;\r\n          }\r\n          .tagStyle {\r\n            margin-left: 20px;\r\n          }\r\n          .tagStyle:nth-child(1) {\r\n            margin-left: 0;\r\n          }\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .activity-page-end {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin: 0 auto;\r\n      padding: 24px 0 60px;\r\n      .activity-page-btn {\r\n        width: 82px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n::v-deep .el-form-item--medium .el-form-item__content {\r\n  border-bottom: 1px solid rgb(246, 246, 246);\r\n}\r\n.expert-library-list {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  width: 100%;\r\n  .list-item-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    width: 578px;\r\n    background: #fff;\r\n    margin-top: 36px;\r\n    padding: 28px 32px;\r\n    min-height: 240px;\r\n    .list-item-box {\r\n      flex: 1;\r\n      .item-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        .item-title {\r\n          width: 280px;\r\n          font-size: 32px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 32px;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          word-wrap: break-word;\r\n        }\r\n      }\r\n      .expert-library-label {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        margin: 0 0 16px;\r\n        .library-label-item {\r\n          max-width: 350px;\r\n          padding: 6px 12px;\r\n          background: #f4f5f9;\r\n          border-radius: 4px;\r\n          font-size: 12px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #666;\r\n          line-height: 12px;\r\n          margin: 24px 16px 0 0;\r\n          .expert-library-type {\r\n            word-wrap: break-word;\r\n          }\r\n        }\r\n      }\r\n      .expert-library-box {\r\n        width: 370px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #666;\r\n        line-height: 32px;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n    }\r\n    .list-item-img {\r\n      width: 120px;\r\n      height: 168px;\r\n      margin-left: 24px;\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n    &:hover {\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}