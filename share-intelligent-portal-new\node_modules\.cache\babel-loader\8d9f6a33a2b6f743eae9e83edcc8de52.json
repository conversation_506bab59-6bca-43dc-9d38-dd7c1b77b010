{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\RuoYi\\Doc\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\RuoYi\\Doc\\index.vue", "mtime": 1750311962816}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAnUnVvWWlEb2MnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1cmw6ICdodHRwOi8vZG9jLnJ1b3lpLnZpcC9ydW95aS1jbG91ZCcKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBnb3RvOiBmdW5jdGlvbiBnb3RvKCkgewogICAgICB3aW5kb3cub3Blbih0aGlzLnVybCk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "data", "url", "methods", "goto", "window", "open"], "sources": ["src/components/RuoYi/Doc/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <svg-icon icon-class=\"question\" @click=\"goto\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'RuoYiDoc',\r\n  data() {\r\n    return {\r\n      url: 'http://doc.ruoyi.vip/ruoyi-cloud'\r\n    }\r\n  },\r\n  methods: {\r\n    goto() {\r\n      window.open(this.url)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;iCAOA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACAC,MAAA,CAAAC,IAAA,MAAAJ,GAAA;IACA;EACA;AACA", "ignoreList": []}]}