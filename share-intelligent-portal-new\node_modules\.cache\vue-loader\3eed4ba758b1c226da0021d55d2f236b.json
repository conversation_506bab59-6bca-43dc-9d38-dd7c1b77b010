{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\verificationCode\\index.vue?vue&type=style&index=0&id=8761618a&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\verificationCode\\index.vue", "mtime": 1750311962830}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5iYy12ZXJpZmljYXRpb24tY29kZSB7DQogIGRpc3BsYXk6IGZsZXg7DQogIC5iYy12ZXJpZmljYXRpb24taW5wdXQgew0KICAgIGZsZXg6IDE7DQogICAgLmVsLWlucHV0X19pbm5lciB7DQogICAgICAvLyB3aWR0aDogNDAwcHg7DQogICAgICBoZWlnaHQ6IDQwcHg7DQogICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLVJlZ3VsYXIsIFBpbmdGYW5nIFNDOw0KICAgICAgbGluZS1oZWlnaHQ6IDE0cHg7DQogICAgICBwYWRkaW5nLWxlZnQ6IDQwcHg7DQogICAgfQ0KICAgIC5pbnB1dC1pY29uIHsNCiAgICAgIHdpZHRoOiAxNnB4Ow0KICAgICAgaGVpZ2h0OiAxNnB4Ow0KICAgICAgbWFyZ2luOiAxMnB4Ow0KICAgIH0NCiAgfQ0KICAuZWwtYnV0dG9uIHsNCiAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgICBoZWlnaHQ6IDQwcHggIWltcG9ydGFudDsNCiAgICBsaW5lLWhlaWdodDogNDBweDsNCiAgICBwYWRkaW5nOiAwOw0KICAgIGZsb2F0OiByaWdodDsNCiAgfQ0KICAuc2VuZENvZGUgew0KICAgIG1hcmdpbi1yaWdodDogMTJweDsNCiAgICB0ZXh0LWRlY29yYXRpb246IGluaGVyaXQ7DQogICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtUmVndWxhciwgUGluZ0ZhbmcgU0M7DQogICAgY29sb3I6ICMyMWM5Yjg7DQogICAgbGluZS1oZWlnaHQ6IDE2cHg7DQogIH0NCiAgLmVsLWJ1dHRvbi5pcy1kaXNhYmxlZCB7DQogICAgY29sb3I6ICNjZmNmY2Y7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/verificationCode", "sourcesContent": ["<template>\r\n  <div class=\"bc-verification-code\">\r\n    <el-input\r\n      ref=\"input\"\r\n      v-model=\"hValue\"\r\n      autocomplete=\"off\"\r\n      class=\"bc-verification-input\"\r\n      :size=\"size\"\r\n      :maxlength=\"maxlength\"\r\n      :placeholder=\"placeholder\"\r\n    >\r\n      <img\r\n        slot=\"prefix\"\r\n        src=\"../../assets/login/mailIcon.png\"\r\n        alt=\"\"\r\n        class=\"input-icon\"\r\n      />\r\n      <el-button\r\n        slot=\"suffix\"\r\n        type=\"text\"\r\n        class=\"sendCode\"\r\n        :size=\"size\"\r\n        :disabled=\"msgKey\"\r\n        :loading=\"sendLoading\"\r\n        @click=\"handleSendCode\"\r\n      >\r\n        {{ msgKey ? `${msgTime} S后重新发送` : \"发送验证码\" }}\r\n      </el-button>\r\n    </el-input>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCommonCode } from \"@/api/login\";\r\n\r\nexport default {\r\n  props: {\r\n    value: String,\r\n    mobile: {\r\n      type: [String, Object],\r\n      default: \"\",\r\n    },\r\n    placeholder: {\r\n      type: String,\r\n      default: \"请输入验证码\",\r\n    },\r\n    size: String,\r\n    beforeSendCode: Function,\r\n    sendParams: {\r\n      type: Array,\r\n      default() {\r\n        return [];\r\n      },\r\n    },\r\n    showIcon: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    maxlength: {\r\n      type: Number,\r\n      default: 6,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      msgText: \"\",\r\n      msgTime: \"\",\r\n      msgKey: false,\r\n      sendLoading: false,\r\n    };\r\n  },\r\n  computed: {\r\n    hValue: {\r\n      get() {\r\n        return this.value;\r\n      },\r\n      set(val) {\r\n        this.$emit(\"input\", val);\r\n      },\r\n    },\r\n    config() {\r\n      return {\r\n        MSGINIT: \"发送验证码\",\r\n        MSGSCUCCESS: \"秒后重发\",\r\n        MSGTIME: 60,\r\n      };\r\n    },\r\n  },\r\n  created() {\r\n    this.msgText = this.config.MSGINIT;\r\n    this.msgTime = this.config.MSGTIME;\r\n  },\r\n  methods: {\r\n    handleSendCode() {\r\n      this.hValue = \"\";\r\n      if (this.beforeSendCode) {\r\n        let result = this.beforeSendCode();\r\n        if (result === false) {\r\n          return;\r\n        }\r\n        if (result !== true) {\r\n          Promise.resolve(result).then(() => {\r\n            this.sendCode();\r\n          });\r\n        }\r\n      } else {\r\n        this.sendCode();\r\n      }\r\n    },\r\n    sendCode() {\r\n      if (!this.mobile.phone) {\r\n        this.$message.warning(\"请输入账号\");\r\n        return;\r\n      }\r\n      this.sendLoading = true;\r\n      this.msgText = \"发送中\";\r\n      let params = [];\r\n      if (this.mobile) {\r\n        params.push(this.mobile);\r\n      }\r\n      if (this.sendParams) {\r\n        params = params.concat(this.sendParams);\r\n      }\r\n      getCommonCode({ telphone: this.mobile.phone })\r\n        .then((res) => {\r\n          if (res.code !== 200) {\r\n            this.$message.error(res.msg);\r\n            this.sendLoading = false;\r\n            return;\r\n          }\r\n          this.$emit(\"after-send\", res.data || {});\r\n          this.sendLoading = false;\r\n          this.msgText = this.msgTime + this.config.MSGSCUCCESS;\r\n          this.msgKey = true;\r\n          this.$refs.input.focus();\r\n          const time = setInterval(() => {\r\n            this.msgTime--;\r\n            this.msgText = this.msgTime + this.config.MSGSCUCCESS;\r\n            if (this.msgTime === 0) {\r\n              this.msgTime = this.config.MSGTIME;\r\n              this.msgText = this.config.MSGINIT;\r\n              this.msgKey = false;\r\n              clearInterval(time);\r\n            }\r\n          }, 1000);\r\n        })\r\n        .catch(() => {\r\n          this.sendLoading = false;\r\n          this.msgText = this.config.MSGINIT;\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.bc-verification-code {\r\n  display: flex;\r\n  .bc-verification-input {\r\n    flex: 1;\r\n    .el-input__inner {\r\n      // width: 400px;\r\n      height: 40px;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 14px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 14px;\r\n      padding-left: 40px;\r\n    }\r\n    .input-icon {\r\n      width: 16px;\r\n      height: 16px;\r\n      margin: 12px;\r\n    }\r\n  }\r\n  .el-button {\r\n    margin-left: 10px;\r\n    height: 40px !important;\r\n    line-height: 40px;\r\n    padding: 0;\r\n    float: right;\r\n  }\r\n  .sendCode {\r\n    margin-right: 12px;\r\n    text-decoration: inherit;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    color: #21c9b8;\r\n    line-height: 16px;\r\n  }\r\n  .el-button.is-disabled {\r\n    color: #cfcfcf;\r\n  }\r\n}\r\n</style>\r\n"]}]}