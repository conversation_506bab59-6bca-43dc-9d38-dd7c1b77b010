<template>
  <div class="scene-detail-container">
    <div class="scene-detail-banner">
      <img
        src="https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/167601983615484.webp"
        alt="场景详情"
      />
    </div>
    <div class="scene-detail-title-box">
      <div class="scene-divider"></div>
      <div class="scene-detail-title">场景详情</div>
      <div class="scene-divider"></div>
    </div>
    <div class="scene-detail-content">
      <template v-if="data.id">
        <div class="scene-detail-box">
          <div class="scene-info-title">
            {{ data.title }}
          </div>
          <div class="scene-info-time">{{ data.updateTime }}</div>
          <div class="scene-info-divider"></div>
          <div class="scene-info-box">
            <div
              v-html="data.content"
              class="scene-info-content ql-editor"
            ></div>
          </div>
        </div>
      </template>
      <template v-else>
        <el-empty />
      </template>
    </div>
  </div>
</template>

<script>
import { getInfoDetail } from "@/api/scene";

export default {
  name: "sceneDetailPage",
  data() {
    return {
      data: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      let id = this.$route.query.id;
      this.loading = true;
      getInfoDetail({ id: id })
        .then((res) => {
          this.loading = false;
          this.data = res.data || {};
        })
        .catch(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.scene-detail-container {
  width: 100%;
  padding: 0 0 100px;
  background: #f4f5f9;
  .scene-detail-banner {
    width: 100%;
    height: 280px;
    img {
      width: 100%;
      height: 280px;
      object-fit: fill;
    }
  }
  .scene-detail-title-box {
    width: 336px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 60px 0 40px;
    .scene-detail-title {
      font-size: 40px;
      font-weight: 500;
      color: #333;
      line-height: 40px;
      padding: 0 40px;
    }
    .scene-divider {
      width: 48px;
      height: 4px;
      background: #21c9b8;
    }
  }
  .scene-detail-content {
    width: 1200px;
    background: #fff;
    margin: 0 auto;
    .scene-detail-box {
      padding: 60px 116px 100px;
      .scene-info-title {
        width: 960px;
        font-size: 32px;
        font-weight: 600;
        color: #333;
        line-height: 32px;
        text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/
        white-space: nowrap; /*让文字不换行*/
        overflow: hidden; /*超出要隐藏*/
      }
      .scene-info-time {
        font-size: 12px;
        color: #999;
        line-height: 12px;
        padding-top: 40px;
      }
      .scene-info-divider {
        width: 100%;
        height: 1px;
        background: #e8e8e8;
        margin-top: 10px;
      }
      .scene-info-box {
        padding-top: 40px;
      }
    }
  }
}
</style>

<style lang="scss">
.scene-detail-container {
  .scene-info-content {
    word-break: break-all;
    font-size: 16px;
    line-height: 28px;
    color: #333;
    img {
      max-width: 100%;
    }
  }
}
</style>
