{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\im\\components\\video.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\im\\components\\video.vue", "mtime": 1750311963058}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfdnVlVmlkZW9QbGF5ZXIgPSByZXF1aXJlKCJ2dWUtdmlkZW8tcGxheWVyIik7CnJlcXVpcmUoInZpZGVvLmpzL2Rpc3QvdmlkZW8tanMuY3NzIik7CnJlcXVpcmUoInZ1ZS12aWRlby1wbGF5ZXIvc3JjL2N1c3RvbS10aGVtZS5jc3MiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8g5Lul5LiL5LiJ6KGM5LiA5a6a6KaB5byV5YWlCi8vIGltcG9ydCAndmlkZW8uanMvZGlzdC9sYW5nL3poLUNOJwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgLy8gbmFtZTogJ3ZpZGVvcGxheWVyJywKICBjb21wb25lbnRzOiB7CiAgICAvLyDlv4XpnIDlvJXlhaUKICAgIHZpZGVvUGxheWVyOiBfdnVlVmlkZW9QbGF5ZXIudmlkZW9QbGF5ZXIKICB9LAogIHByb3BzOiBbCiAgLy8g5o6l5pS254i257uE5Lu255qE5pWw5o2uCiAgJ21wNFBpYycsICdtcDRVcmwnXSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgZmlsZUFyZWFIZWlnaHQ6IDEwMCwKICAgICAgZmlsZVR5cGU6ICdtcDQnIC8vIOi1hOa6kOeahOexu+WeiwogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7CiAgICAvLyDkvb/nlKjorqHnrpflsZ7mgKcKICAgIHBsYXllck9wdGlvbnM6IGZ1bmN0aW9uIHBsYXllck9wdGlvbnMoKSB7CiAgICAgIHZhciBwbGF5ZXJPcHRpb25zT2JqID0gewogICAgICAgIHBsYXliYWNrUmF0ZXM6IFswLjcsIDEuMCwgMS41LCAyLjBdLAogICAgICAgIC8v6KeG6aKR5pKt5pS+6YCf5bqmCiAgICAgICAgYXV0b3BsYXk6IHRydWUsCiAgICAgICAgLy8g5aaC5p6cdHJ1Ze+8jOa1j+iniOWZqOWHhuWkh+WlveaXtuW8gOWni+WbnuaUvuOAggogICAgICAgIG11dGVkOiBmYWxzZSwKICAgICAgICAvLyDpu5jorqTmg4XlhrXkuIvlsIbkvJrmtojpmaTku7vkvZXpn7PpopHjgIIKICAgICAgICBsb29wOiBmYWxzZSwKICAgICAgICAvLyDlr7zoh7Top4bpopHkuIDnu5PmnZ/lsLHph43mlrDlvIDlp4vjgIIKICAgICAgICAvLyBwcmVsb2FkOiAnYXV0bycsIC8vIOW7uuiurua1j+iniOWZqOWcqDx2aWRlbz7liqDovb3lhYPntKDlkI7mmK/lkKblupTor6XlvIDlp4vkuIvovb3op4bpopHmlbDmja7jgIJhdXRv5rWP6KeI5Zmo6YCJ5oup5pyA5L2z6KGM5Li6LOeri+WNs+W8gOWni+WKoOi9veinhumike+8iOWmguaenOa1j+iniOWZqOaUr+aMge+8ieOAggogICAgICAgIGxhbmd1YWdlOiAnemgtQ04nLAogICAgICAgIC8vIGFzcGVjdFJhdGlvOiAnMTY6OScsIC8vIOWwhuaSreaUvuWZqOe9ruS6jua1geeVheaooeW8j++8jOW5tuWcqOiuoeeul+aSreaUvuWZqOeahOWKqOaAgeWkp+Wwj+aXtuS9v+eUqOivpeWAvOOAguWAvOW6lOivpeS7o+ihqOS4gOS4quavlOS+iyAtIOeUqOWGkuWPt+WIhumalOeahOS4pOS4quaVsOWtl++8iOS+i+WmgiIxNjo5IuaIliI0OjMi77yJ44CCCiAgICAgICAgZmx1aWQ6IGZhbHNlLAogICAgICAgIC8vIOW9k3RydWXml7bvvIxWaWRlby5qcyBwbGF5ZXLlsIbmi6XmnInmtYHkvZPlpKflsI/jgILmjaLlj6Xor53or7TvvIzlroPlsIbmjInmr5TkvovnvKnmlL7ku6XpgILlupTlhbblrrnlmajjgIIKICAgICAgICBzb3VyY2VzOiBbewogICAgICAgICAgdHlwZTogJ3ZpZGVvLycgKyB0aGlzLmZpbGVUeXBlLAogICAgICAgICAgLy8g6LWE5rqQ5qC85byP5YaZ5rOV77yaJ3ZpZGVvL21wNCfvvIzlkKbliJnmjqfliLblj7DkvJrlh7rnjrBub3RTdXBwb3J0ZWRNZXNzYWdl6K6+572u55qE6ZSZ6K+v44CCCiAgICAgICAgICBzcmM6IHRoaXMubXA0VXJsIC8vIOinhumikXVybOWcsOWdgAogICAgICAgIH1dLAogICAgICAgIHBvc3RlcjogdGhpcy5tcDRQaWMsCiAgICAgICAgLy8g6KeG6aKR5bCB6Z2i5Zyw5Z2ACiAgICAgICAgLy8gd2lkdGg6IGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGllbnRXaWR0aCwKICAgICAgICBoZWlnaHQ6IHRoaXMuZmlsZUFyZWFIZWlnaHQsCiAgICAgICAgLy8g6K6+572u6auY5bqm77yMZmx1aWTpnIDopoHorr7nva7miJBmbGFzZQogICAgICAgIG5vdFN1cHBvcnRlZE1lc3NhZ2U6ICfmraTop4bpopHmmoLml6Dms5Xmkq3mlL4uLi4nLAogICAgICAgIC8vIOWFgeiuuOimhuebllZpZGVvLmpz5peg5rOV5pKt5pS+5aqS5L2T5rqQ5pe25pi+56S655qE6buY6K6k5L+h5oGv44CCCiAgICAgICAgY29udHJvbEJhcjogewogICAgICAgICAgdGltZURpdmlkZXI6IHRydWUsCiAgICAgICAgICBkdXJhdGlvbkRpc3BsYXk6IHRydWUsCiAgICAgICAgICByZW1haW5pbmdUaW1lRGlzcGxheTogZmFsc2UsCiAgICAgICAgICBmdWxsc2NyZWVuVG9nZ2xlOiB0cnVlIC8v5YWo5bGP5oyJ6ZKuCiAgICAgICAgfQogICAgICB9OwogICAgICByZXR1cm4gcGxheWVyT3B0aW9uc09iajsKICAgIH0KICB9LAogIHdhdGNoOiB7fQp9Ow=="}, {"version": 3, "names": ["_vueVideoPlayer", "require", "_default", "exports", "default", "components", "videoPlayer", "props", "data", "fileAreaHeight", "fileType", "computed", "playerOptions", "playerOptionsObj", "playbackRates", "autoplay", "muted", "loop", "language", "fluid", "sources", "type", "src", "mp4Url", "poster", "mp4Pic", "height", "notSupportedMessage", "controlBar", "timeDivider", "durationDisplay", "remainingTimeDisplay", "fullscreenToggle", "watch"], "sources": ["src/views/system/user/im/components/video.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-05-31 11:30:14\r\n * @LastEditTime: 2023-05-31 11:51:13\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"video\">\r\n    <!-- 使用组件 -->\r\n    <video-player\r\n      class=\"video-player vjs-custom-skin\"\r\n      ref=\"videoPlayer\"\r\n      :playsinline=\"true\"\r\n      :options=\"playerOptions\"\r\n    ></video-player>\r\n  </div>\r\n</template>\r\n\r\n<script type=\"text/ecmascript-6\">\r\n// 以下三行一定要引入\r\nimport { videoPlayer } from 'vue-video-player'\r\nimport 'video.js/dist/video-js.css'\r\nimport 'vue-video-player/src/custom-theme.css'\r\n// import 'video.js/dist/lang/zh-CN'\r\n\r\nexport default {\r\n// name: 'videoplayer',\r\ncomponents: { // 必需引入\r\n  videoPlayer\r\n},\r\nprops: [ // 接收父组件的数据\r\n  'mp4Pic',\r\n  'mp4Url'\r\n],\r\ndata () {\r\n  return {\r\n    fileAreaHeight: 100,\r\n    fileType: 'mp4', // 资源的类型\r\n  }\r\n},\r\ncomputed: { // 使用计算属性\r\n    playerOptions () {\r\n      const playerOptionsObj = {\r\n        playbackRates: [0.7, 1.0, 1.5, 2.0], //视频播放速度\r\n        autoplay: true, // 如果true，浏览器准备好时开始回放。\r\n        muted: false, // 默认情况下将会消除任何音频。\r\n        loop: false, // 导致视频一结束就重新开始。\r\n        // preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）。\r\n        language: 'zh-CN',\r\n        // aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如\"16:9\"或\"4:3\"）。\r\n        fluid: false, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。\r\n        sources: [{\r\n          type: 'video/' + this.fileType,\t// 资源格式写法：'video/mp4'，否则控制台会出现notSupportedMessage设置的错误。\r\n          src: this.mp4Url // 视频url地址\r\n        }],\r\n        poster: this.mp4Pic, // 视频封面地址\r\n        // width: document.documentElement.clientWidth,\r\n        height: this.fileAreaHeight,\t// 设置高度，fluid需要设置成flase\r\n        notSupportedMessage: '此视频暂无法播放...', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。\r\n        controlBar: {\r\n          timeDivider: true,\r\n          durationDisplay: true,\r\n          remainingTimeDisplay: false,\r\n          fullscreenToggle: true  //全屏按钮\r\n        }\r\n      }\r\n      return playerOptionsObj\r\n    }\r\n  },\r\n  watch: {\r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.video /*可不设置*/ {\r\n  .video-player {\r\n    height: 500px;\r\n  }\r\n  .vjs-custom-skin > .video-js {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  // position:relative;\r\n  // padding:5px;\r\n  // padding-top: 70px;\r\n  //  .vjs-poster /*对画面静止的样式进行设置放大一倍*/\r\n  //  {\r\n  //        transform: scale(2)\r\n  //  }\r\n  //  .vjs-tech /*对画面播放的样式进行设置放大一倍*/\r\n  //  {\r\n  //      transform: scale(2)\r\n  //  }\r\n  //   .video-js .vjs-big-play-button /*对播放按钮的样式进行设置*/\r\n  //   {\r\n  //       width: 80px;\r\n  //         height: 100%;\r\n  //         border-radius: 1em;\r\n  //   }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAqBA,IAAAA,eAAA,GAAAC,OAAA;AACAA,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;AAHA;AAIA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACA;EACAC,UAAA;IAAA;IACAC,WAAA,EAAAA;EACA;EACAC,KAAA;EAAA;EACA,UACA,SACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;IAAA;IACAC,aAAA,WAAAA,cAAA;MACA,IAAAC,gBAAA;QACAC,aAAA;QAAA;QACAC,QAAA;QAAA;QACAC,KAAA;QAAA;QACAC,IAAA;QAAA;QACA;QACAC,QAAA;QACA;QACAC,KAAA;QAAA;QACAC,OAAA;UACAC,IAAA,kBAAAX,QAAA;UAAA;UACAY,GAAA,OAAAC,MAAA;QACA;QACAC,MAAA,OAAAC,MAAA;QAAA;QACA;QACAC,MAAA,OAAAjB,cAAA;QAAA;QACAkB,mBAAA;QAAA;QACAC,UAAA;UACAC,WAAA;UACAC,eAAA;UACAC,oBAAA;UACAC,gBAAA;QACA;MACA;MACA,OAAAnB,gBAAA;IACA;EACA;EACAoB,KAAA,GAEA;AACA", "ignoreList": []}]}