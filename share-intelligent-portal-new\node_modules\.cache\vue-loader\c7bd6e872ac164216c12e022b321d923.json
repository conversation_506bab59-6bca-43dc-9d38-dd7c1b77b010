{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\factoryDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\factoryDetail.vue", "mtime": 1750311962966}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBlbnRlcmluZ0ZhY3RvcnlEZXRhaWwgfSBmcm9tICJAL2FwaS9tYW51ZmFjdHVyaW5nU2hhcmluZyI7DQppbXBvcnQgeyBsaXN0U3lzUHJvZHVjdCB9IGZyb20gIkAvYXBpL21hbnVmYWN0dXJpbmdTaGFyaW5nIjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogImRldmljZURldGFpbCIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGRldGFpbHNEYXRhOiB7fSwNCiAgICAgIGlkOiBudWxsLA0KICAgICAgc2l6ZTogIiIsDQogICAgICBwcm9kdWN0TGlzdDogW10sDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7DQogICAgdGhpcy5nZXRGYWN0b3J5RGV0YWlsKCk7DQogICAgdGhpcy5nZXRQcm9kdWN0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0RmFjdG9yeURldGFpbCgpIHsNCiAgICAgIGVudGVyaW5nRmFjdG9yeURldGFpbCh0aGlzLmlkKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5kZXRhaWxzRGF0YSA9IHJlcy5kYXRhOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRQcm9kdWN0TGlzdCgpIHsNCiAgICAgIGxpc3RTeXNQcm9kdWN0KHsNCiAgICAgICAgZmFjdG9yeUlkOiB0aGlzLmlkLA0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAwMCwNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5wcm9kdWN0TGlzdCA9IHJlcy5yb3dzIHx8IFtdOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHRvRXhoaWJpdGlvbkhhbGwoaWQpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgcGF0aDogIi9jb21wb3NpdGVFeGhpYml0aW9uSGFsbCIsDQogICAgICAgIHF1ZXJ5OiB7DQogICAgICAgICAgcHJvZHVjdElkOiBpZCwNCiAgICAgICAgfSwNCiAgICAgIH0pOw0KICAgIH0NCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["factoryDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0KA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "factoryDetail.vue", "sourceRoot": "src/views/manufacturingSharing/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"card-container cardStyle\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <div class=\"card_left_bottom\">\r\n          <div style=\"margin-left: 14px\">\r\n            <div class=\"title\">{{ detailsData.companyName }}</div>\r\n            <div class=\"everyOption\">\r\n              <div class=\"optionName\">行业：</div>\r\n              <div class=\"optionValue\">{{ detailsData.industry }}</div>\r\n            </div>\r\n            <div class=\"everyOption\">\r\n              <div class=\"optionName\">公司地址：</div>\r\n              <div class=\"optionValue\">{{ detailsData.companyAddress }}</div>\r\n            </div>\r\n          </div>\r\n          <div>\r\n            <div class=\"content_title\">\r\n              <div class=\"icon\"></div>\r\n              <div class=\"phoneTitle\">联系方式</div>\r\n            </div>\r\n            <div style=\"padding-left: 14px\">\r\n              <div class=\"everyOption\">\r\n                <div class=\"optionName\">联系电话：</div>\r\n                <div class=\"optionValue\">{{ detailsData.contactPhone }}</div>\r\n              </div>\r\n              <!-- <div class=\"everyOption\">\r\n                <div class=\"optionName\">二维码：</div>\r\n                <div class=\"optionImg\">\r\n                  <img\r\n                    style=\"width: 100%; height: 100%\"\r\n                    :src=\"detailsData.qrCode\"\r\n                    alt=\"\"\r\n                  />\r\n                </div>\r\n              </div> -->\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- 中间 -->\r\n      <div class=\"card_center_line\"></div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <!-- <div>\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">基本信息</div>\r\n          </div>\r\n          <div style=\"margin-top: 22px\">\r\n            <el-descriptions class=\"margin-top\" title=\"\" :column=\"2\" :size=\"size\" border>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 社会信用代码 </template>\r\n{{ detailsData.socialCreditCode }}\r\n</el-descriptions-item>\r\n<el-descriptions-item>\r\n  <template slot=\"label\"> 注册资本 </template>\r\n  {{ detailsData.registeredCapital }}\r\n</el-descriptions-item>\r\n<el-descriptions-item>\r\n  <template slot=\"label\"> 经营范围 </template>\r\n  {{ detailsData.businessScope }}\r\n</el-descriptions-item>\r\n</el-descriptions>\r\n</div>\r\n</div>\r\n<div style=\"margin-top: 30px\">\r\n  <div class=\"content_title\">\r\n    <div class=\"icon\"></div>\r\n    <div class=\"title\">资质证件</div>\r\n  </div>\r\n  <div style=\"margin-top: 22px\">\r\n    <div class=\"certificateStyle\">\r\n      <img :src=\"detailsData.qualificationFiles\" alt=\"\" />\r\n    </div>\r\n  </div>\r\n</div>\r\n<div style=\"margin-top: 30px\">\r\n  <div class=\"content_title\">\r\n    <div class=\"icon\"></div>\r\n    <div class=\"title\">人员能力</div>\r\n  </div>\r\n  <div class=\"center-text\" style=\"margin-top: 21px\">\r\n    <el-table :data=\"detailsData.personnelList\">\r\n      <el-table-column label=\"技术人员姓名\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n                  {{ scope.row.technicianName }}\r\n                </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"专业技术工种\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n                  {{ scope.row.technicalType }}\r\n                </template>\r\n      </el-table-column>\r\n    </el-table>\r\n  </div>\r\n</div>\r\n<div style=\"margin-top: 30px\">\r\n  <div class=\"content_title\">\r\n    <div class=\"icon\"></div>\r\n    <div class=\"title\">技术实力</div>\r\n  </div>\r\n  <div style=\"margin-top: 21px\">\r\n    <div class=\"desc\">\r\n      {{ detailsData.technicalCapability }}\r\n    </div>\r\n  </div>\r\n</div>\r\n<div style=\"margin-top: 30px\">\r\n  <div class=\"content_title\">\r\n    <div class=\"icon\"></div>\r\n    <div class=\"title\">设备信息</div>\r\n  </div>\r\n  <div class=\"center-text\" style=\"margin-top: 21px\">\r\n    <el-table :data=\"detailsData.equipmentList\">\r\n      <el-table-column label=\"生产设备\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n                  {{ scope.row.equipmentName }}\r\n                </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"规格型号\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n                  {{ scope.row.specification }}\r\n                </template>\r\n      </el-table-column>\r\n    </el-table>\r\n  </div>\r\n</div> -->\r\n        <div>\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">公司简介</div>\r\n          </div>\r\n          <div style=\"margin-top: 21px\">\r\n            <div class=\"desc\">\r\n              {{ detailsData.qrCode }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 30px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">产品与服务</div>\r\n          </div>\r\n          <div style=\"margin-top: 21px\">\r\n            <div class=\"desc\">\r\n              {{ detailsData.registeredCapital }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 30px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">产品图片</div>\r\n          </div>\r\n          <div class=\"content_desc\">\r\n            <div class=\"imgList\">\r\n              <div v-for=\"item in productList\" :key=\"item.id\" @click=\"toExhibitionHall(item.productId)\" class=\"imgItem\">\r\n                <img :src=\"item.productImageUrl\" alt=\"\" />\r\n                <div class=\"imgTitle\">{{ item.productName }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { enteringFactoryDetail } from \"@/api/manufacturingSharing\";\r\nimport { listSysProduct } from \"@/api/manufacturingSharing\";\r\nexport default {\r\n  name: \"deviceDetail\",\r\n  data() {\r\n    return {\r\n      detailsData: {},\r\n      id: null,\r\n      size: \"\",\r\n      productList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getFactoryDetail();\r\n    this.getProductList();\r\n  },\r\n  methods: {\r\n    getFactoryDetail() {\r\n      enteringFactoryDetail(this.id).then((res) => {\r\n        this.detailsData = res.data;\r\n      });\r\n    },\r\n    getProductList() {\r\n      listSysProduct({\r\n        factoryId: this.id,\r\n        pageNum: 1,\r\n        pageSize: 1000,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.productList = res.rows || [];\r\n        }\r\n      });\r\n    },\r\n    toExhibitionHall(id) {\r\n      this.$router.push({\r\n        path: \"/compositeExhibitionHall\",\r\n        query: {\r\n          productId: id,\r\n        },\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: #f2f2f2;\r\n  padding: 30px 0 61px 0;\r\n}\r\n\r\n.cardStyle {\r\n  // height: 950px;\r\n  background-color: #ffffff;\r\n  padding: 59px 56px 75px 46px;\r\n  display: flex;\r\n}\r\n\r\n.card_left {\r\n  .card_left_bottom {\r\n    width: 263px;\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      font-size: 20px;\r\n      color: #222222;\r\n      margin-bottom: 25px;\r\n    }\r\n\r\n    .everyOption {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 12px;\r\n\r\n      .optionName {\r\n        // height: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #999999;\r\n      }\r\n\r\n      .optionValue {\r\n        // height: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #333333;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 1;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      .optionImg {\r\n        width: 120px;\r\n        height: 120px;\r\n      }\r\n    }\r\n\r\n    .content_title {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 32px;\r\n\r\n      .icon {\r\n        width: 4px;\r\n        height: 20px;\r\n        background: #21c9b8;\r\n      }\r\n\r\n      .phoneTitle {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 18px;\r\n        color: #030a1a;\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .content_desc {\r\n      // width: 631px;\r\n      // height: 159px;\r\n      margin-top: 20px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n      line-height: 24px;\r\n\r\n      .imgList {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n\r\n        .imgItem {\r\n          width: 150px;\r\n          margin-right: 6px;\r\n          margin-bottom: 10px;\r\n          text-align: center;\r\n          cursor: pointer;\r\n\r\n          img {\r\n            width: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n  }\r\n}\r\n\r\n.card_center_line {\r\n  width: 1px;\r\n  height: 100%;\r\n  background: #e1e1e1;\r\n  margin-left: 60px;\r\n  margin-right: 61px;\r\n}\r\n\r\n.card_right {\r\n  width: 100%;\r\n  overflow-y: auto;\r\n\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n\r\n  .certificateStyle {\r\n    width: 170px;\r\n    height: 220px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.14);\r\n    margin-left: 15px;\r\n    padding: 10px;\r\n  }\r\n\r\n  .desc {\r\n    // width: 741px;\r\n    height: 38px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #666666;\r\n    line-height: 24px;\r\n  }\r\n\r\n  .content_desc {\r\n    // width: 631px;\r\n    // height: 159px;\r\n    margin-top: 20px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #666666;\r\n    line-height: 24px;\r\n\r\n    .imgList {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n\r\n      .imgItem {\r\n        width: 150px;\r\n        height: 200px;\r\n        margin-right: 20px;\r\n        margin-bottom: 10px;\r\n        text-align: center;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 75%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .center-text .el-descriptions-item__cell {\r\n  text-align: center;\r\n}\r\n</style>\r\n"]}]}