<template>
  <div class="content">
    <div class="card-container cardStyle">
      <!-- 左侧 -->
      <div class="card_left">
        <div class="card_left_bottom">
          <div class="imgStyle">
            <img style="width: 100%; height: 100%" :src="detailsData.photo
                ? detailsData.photo
                : require('../../../../assets/serviceSharing/ceshi2.png')
              " alt="" />
          </div>
          <div class="title">{{ detailsData.name }}</div>
          <div class="everyOption">
            <div class="optionName">岗位分类：</div>
            <div class="optionValue" v-if="detailsData.positionType">
              {{
                positionTypeList.filter(
                  (item) => item.dictValue == detailsData.positionType
                )[0].dictLabel
              }}
            </div>
          </div>
          <div class="everyOption">
            <div class="optionName">最高学历：</div>
            <div class="optionValue" v-if="detailsData.education">
              {{
                educationList.filter(
                  (item) => item.dictValue == detailsData.education
                )[0].dictLabel
              }}
            </div>
          </div>
          <div class="everyOption">
            <div class="optionName">工作状态：</div>
            <div class="optionValue" v-if="detailsData.workStatus">
              {{
                workStatusList.filter(
                  (item) => item.dictValue == detailsData.workStatus
                )[0].dictLabel
              }}
            </div>
          </div>
          <div class="everyOption">
            <div class="optionName">联系方式：</div>
            <div class="optionValue">{{ detailsData.contactPhone }}</div>
          </div>
          <div class="buttonStyle" @click="jumpIntention">我有意向</div>
        </div>
      </div>
      <!-- 中间 -->
      <div class="card_center_line"></div>
      <!-- 右侧 -->
      <div class="card_right">
        <div>
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">基本信息</div>
          </div>
          <div style="margin-top: 22px">
            <el-descriptions class="margin-top" title="" :column="2" :size="size" border>
              <!-- <el-descriptions-item>
                <template slot="label"> 毕业院校 </template>
北京大学
</el-descriptions-item> -->
              <el-descriptions-item>
                <template slot="label"> 最高学历 </template>
                {{
                  educationList.filter(
                    (item) => item.dictValue == detailsData.education
                  )[0].dictLabel
                }}
              </el-descriptions-item>
              <!-- <el-descriptions-item>
                <template slot="label"> 出生年月 </template>
                1999年5月25日
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 所在地 </template>
                北京市
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 所在单位 </template>
                北京爱德华科技有限公司
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 职务 </template>
                经理
              </el-descriptions-item> -->
              <el-descriptions-item>
                <template slot="label"> 职称 </template>
                {{
                  jobTitleList.filter(
                    (item) => item.dictValue == detailsData.jobTitle
                  )[0].dictLabel
                }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 工作状态 </template>
                {{
                  workStatusList.filter(
                    (item) => item.dictValue == detailsData.workStatus
                  )[0].dictLabel
                }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
        <div style="margin-top: 31px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">个人简历</div>
          </div>
          <div style="margin-top: 22px" class="desc">
            {{ detailsData.workExperience }}
          </div>
        </div>
        <div style="margin-top: 31px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">技术领域</div>
          </div>
          <div style="margin-top: 22px" class="desc">
            {{ detailsData.skills }}
          </div>
        </div>
        <!-- <div style="margin-top: 31px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">附件</div>
          </div>
          <div class="fileContent">
            <div
              class="fileItem"
              v-for="(item, index) in fileList"
              :key="index"
              @click="downLoadFile(item.fileUrl)"
            >
              <div class="fileImg">
                <img
                  style="width: 100%; height: 100%"
                  src="../../../../assets/serviceSharing/fileImg.png"
                  alt=""
                />
              </div>
              <div class="fileName">
                {{ item.fileName }}
              </div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { talentDetailData } from "@/api/serviceSharing/index";

export default {
  name: "deviceDetail",
  data() {
    return {
      detailsData: {},
      fileList: [
        {
          fileName: "报表数据.csv",
          fileUrl: "",
        },
        {
          fileName: "报表数据.csv",
          fileUrl: "",
        },
      ],
      id: null,
      size: "",
      positionTypeList: [], // 岗位分类
      educationList: [], // 最高学历
      jobTitleList: [], // 职称
      workStatusList: [], // 工作状态
    };
  },
  created() {
    this.id = this.$route.query.id;
    this.getPositionType();
    this.getEducation();
    this.getJobTitle();
    this.getWorkStatus();
    this.getDetailData();
  },
  methods: {
    getDetailData() {
      talentDetailData(this.id).then((res) => {
        if (res.code === 200) {
          this.detailsData = res.data;
        }
      });
    },
    // 岗位分类
    getPositionType() {
      let params = { dictType: "position_type" };
      listData(params).then((response) => {
        this.positionTypeList = response.rows;
        this.positionTypeList.unshift({
          dictValue: "",
          dictLabel: "全部",
        });
      });
    },
    // 最高学历
    getEducation() {
      let params = { dictType: "education" };
      listData(params).then((response) => {
        this.educationList = response.rows;
        this.educationList.unshift({
          dictValue: "",
          dictLabel: "全部",
        });
      });
    },
    // 职称
    getJobTitle() {
      let params = { dictType: "job_title" };
      listData(params).then((response) => {
        this.jobTitleList = response.rows;
        this.jobTitleList.unshift({
          dictValue: "",
          dictLabel: "全部",
        });
      });
    },
    // 工作状态
    getWorkStatus() {
      let params = { dictType: "work_status" };
      listData(params).then((response) => {
        this.workStatusList = response.rows;
        this.workStatusList.unshift({
          dictValue: "",
          dictLabel: "全部",
        });
      });
    },
    intention(id) {
      this.$router.push("/receiveOrder"); // 传id
    },
    downLoadFile(url) {
      if (url) {
        window.open(url);
      }
    },
    jumpIntention() {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => { });
        return;
      } else {
        this.$router.push(`/demandInterested?demandName=${this.detailsData.name}&updateTime=${this.detailsData.updateTime}&intentionType=6&fieldName=人才库&intentionId=${this.detailsData.id}`);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  background-color: #f2f2f2;
  padding: 30px 0 61px 0;
}

.cardStyle {
  // height: 630px;
  background-color: #ffffff;
  padding: 60px 56px 54px 50px;
  display: flex;
}

.card_left {
  .card_left_bottom {
    .imgStyle {
      width: 150px;
      height: 180px;
      margin-left: 35px;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 20px;
      color: #222222;
      margin-top: 19px;
      margin-bottom: 18px;
      text-align: center;
    }

    .everyOption {
      display: flex;
      align-items: center;
      margin-top: 12px;

      .optionName {
        // height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
      }

      .optionValue {
        // height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }
    }

    .buttonStyle {
      margin-top: 32px;
      // margin-left: 55px;
      width: 220px;
      height: 50px;
      background: #21c9b8;
      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);
      border-radius: 2px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
    }
  }
}

.card_center_line {
  width: 1px;
  height: 100%;
  background: #e1e1e1;
  margin-left: 60px;
  margin-right: 61px;
}

.card_right {
  width: 100%;

  // overflow-y: auto;
  .content_title {
    display: flex;
    align-items: center;

    .icon {
      width: 4px;
      height: 20px;
      background: #21c9b8;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #030a1a;
      margin-left: 10px;
    }
  }

  .desc {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;
  }

  .fileContent {
    margin-top: 22px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .fileItem {
      width: 280px;
      height: 50px;
      background: #e8f9f8;
      border-radius: 2px;
      display: flex;
      align-items: center;
      padding: 10px;
      margin-left: 20px;
      cursor: pointer;

      .fileImg {
        width: 24px;
        height: 28px;
      }

      .fileName {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        margin-left: 15px;
      }
    }

    .fileItem:nth-child(2n + 1) {
      margin-left: 0;
    }
  }
}
</style>
