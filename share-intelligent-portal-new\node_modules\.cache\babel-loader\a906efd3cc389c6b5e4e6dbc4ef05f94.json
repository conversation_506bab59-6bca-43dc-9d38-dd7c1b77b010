{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\orderManage\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\orderManage\\index.vue", "mtime": 1750311963069}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwp2YXIgX2RlZmluZVByb3BlcnR5MiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRTovY29tcGFueS9ubWQvbm1kbmV3L3NoYXJlLWludGVsbGlnZW50L3NoYXJlLWludGVsbGlnZW50LXBvcnRhbC1uZXcvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZGVmaW5lUHJvcGVydHkuanMiKSk7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5lcnJvci5jYXVzZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuZnVuY3Rpb24ubmFtZS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnRvLXN0cmluZy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuZm9yLWVhY2guanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiKTsKdmFyIF91c2VyID0gcmVxdWlyZSgiQC9hcGkvc3lzdGVtL3VzZXIiKTsKdmFyIF91c2VyTWVudSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiKSk7CnZhciBfb3NzID0gcmVxdWlyZSgiQC9hcGkvb3NzIik7CnZhciBfYXV0aCA9IHJlcXVpcmUoIkAvdXRpbHMvYXV0aCIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogIk9wZXJsb2ciLAogIGRpY3RzOiBbInN5c19vcGVyX3R5cGUiLCAic3lzX2NvbW1vbl9zdGF0dXMiXSwKICBjb21wb25lbnRzOiB7CiAgICBVc2VyTWVudTogX3VzZXJNZW51LmRlZmF1bHQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICB2YXIgdmFsaWRJc0VtcHR5QXJyID0gZnVuY3Rpb24gdmFsaWRJc0VtcHR5QXJyKHMsIHZhbHVlLCBjYWxsYmFjaykgewogICAgICBpZiAoIUFycmF5LmlzQXJyYXkodmFsdWUpIHx8IHZhbHVlLmxlbmd0aCA9PT0gMCkgewogICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcigi6K+35LiK5Lyg5paH5Lu2IikpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBjYWxsYmFjaygpOwogICAgfTsKICAgIHJldHVybiAoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSgoMCwgX2RlZmluZVByb3BlcnR5Mi5kZWZhdWx0KSh7CiAgICAgIGhlYWRlcnM6IHsKICAgICAgICBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyAoMCwgX2F1dGguZ2V0VG9rZW4pKCkKICAgICAgfSwKICAgICAgYWN0aW9uVXJsOiAoMCwgX29zcy51cGxvYWRVcmwpKCksCiAgICAgIGZvcm06IHsKICAgICAgICBpZDogIiIsCiAgICAgICAgcGhvbmU6ICIiLAogICAgICAgIGFwcE5hbWU6ICIiLAogICAgICAgIG9yZGVyQ29kZTogIiIsCiAgICAgICAgb3JkZXJTdGF0dXM6ICIiCiAgICAgIH0sCiAgICAgIGNyZWF0ZVRpbWU6IFtdLAogICAgICBzdWJzY3JpYmVMaXN0OiBbXSwKICAgICAgcXVlcnlQYXJhbXM6IHsKICAgICAgICBwYWdlTnVtOiAxCiAgICAgIH0sCiAgICAgIHRvdGFsOiAwLAogICAgICBmbGFnOiAxLAogICAgICB0eXBlTGlzdDogW3sKICAgICAgICBkaWN0VmFsdWU6IDEsCiAgICAgICAgZGljdExhYmVsOiAi5bel5Lia5bqU55SoIgogICAgICB9LCB7CiAgICAgICAgZGljdFZhbHVlOiAyLAogICAgICAgIGRpY3RMYWJlbDogIuW3peS4muaooeWeiyIKICAgICAgfV0sCiAgICAgIG9yZGVyU3RhdHVzTGlzdDogW3sKICAgICAgICBkaWN0VmFsdWU6IDEsCiAgICAgICAgZGljdExhYmVsOiAi5b6F5pSv5LuYIgogICAgICB9LCB7CiAgICAgICAgZGljdFZhbHVlOiAyLAogICAgICAgIGRpY3RMYWJlbDogIuW+heWPkei0pyIKICAgICAgfSwgewogICAgICAgIGRpY3RWYWx1ZTogMywKICAgICAgICBkaWN0TGFiZWw6ICLmlK/ku5jlpLHotKUiCiAgICAgIH0sIHsKICAgICAgICBkaWN0VmFsdWU6IDQsCiAgICAgICAgZGljdExhYmVsOiAi5bey5Y+R6LSnIgogICAgICB9LCB7CiAgICAgICAgZGljdFZhbHVlOiA1LAogICAgICAgIGRpY3RMYWJlbDogIuW3suaIkOS6pCIKICAgICAgfSwgewogICAgICAgIGRpY3RWYWx1ZTogNiwKICAgICAgICBkaWN0TGFiZWw6ICLlvoXnu63otLkiCiAgICAgIH0sIHsKICAgICAgICBkaWN0VmFsdWU6IDcsCiAgICAgICAgZGljdExhYmVsOiAi5bey5YWz6ZetIgogICAgICB9LCB7CiAgICAgICAgZGljdFZhbHVlOiA4LAogICAgICAgIGRpY3RMYWJlbDogIuaUr+S7mOS4rSIKICAgICAgfSwgewogICAgICAgIGRpY3RWYWx1ZTogOSwKICAgICAgICBkaWN0TGFiZWw6ICLlt7Llj5bmtogiCiAgICAgIH1dLAogICAgICBvcmRlclR5cGVMaXN0OiBbewogICAgICAgIHZhbHVlOiAiMCIsCiAgICAgICAgbGFiZWw6ICLlhajpg6giCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogIjEiLAogICAgICAgIGxhYmVsOiAi5b6F5LuY5qy+IgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6ICIyIiwKICAgICAgICBsYWJlbDogIuW+heWPkei0pyIKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAiMyIsCiAgICAgICAgbGFiZWw6ICLlvoXmlLbotKciCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogIjQiLAogICAgICAgIGxhYmVsOiAi5bey5a6M5oiQIgogICAgICB9XQogICAgfSwgImZsYWciLCAiMCIpLCAibG9hZGluZyIsIGZhbHNlKSwgImludm9pY2VWaXNpYmxlIiwgZmFsc2UpLCAiaW52b2ljZURhdGEiLCB7fSksICJydWxlRm9ybSIsIHsKICAgICAgY29tcGFueUNhcmRMaXN0OiBbXQogICAgfSksICJydWxlcyIsIHsKICAgICAgY29tcGFueUNhcmRMaXN0OiBbewogICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgIHZhbGlkYXRvcjogdmFsaWRJc0VtcHR5QXJyLAogICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiCiAgICAgIH1dCiAgICB9KSwgImN1cnJlbnRJZCIsIG51bGwpOwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuZ2V0TGlzdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgdmFyIHBhcmFtcyA9ICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoewogICAgICAgIGNyZWF0ZUJ5OiB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnVzZXJJZCwKICAgICAgICBwYWdlTnVtOiB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0sCiAgICAgICAgcGFnZVNpemU6IDUKICAgICAgfSwgdGhpcy5mb3JtKSwge30sIHsKICAgICAgICBzdGFydFRpbWU6IHRoaXMuY3JlYXRlVGltZS5sZW5ndGggPiAwID8gdGhpcy5jcmVhdGVUaW1lWzBdIDogIiIsCiAgICAgICAgZW5kVGltZTogdGhpcy5jcmVhdGVUaW1lLmxlbmd0aCA+IDAgPyB0aGlzLmNyZWF0ZVRpbWVbMV0gOiAiIgogICAgICB9KTsKICAgICAgKDAsIF91c2VyLm9yZGVyTGlzdCkocGFyYW1zKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgIF90aGlzLnN1YnNjcmliZUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVDdXJyZW50Q2hhbmdlKHBhZ2VOdW0pIHsKICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gcGFnZU51bTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICByZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmZvcm0gPSB7CiAgICAgICAgaWQ6ICIiLAogICAgICAgIHBob25lOiAiIiwKICAgICAgICBhcHBOYW1lOiAiIiwKICAgICAgICBvcmRlckNvZGU6ICIiLAogICAgICAgIG9yZGVyU3RhdHVzOiAiIgogICAgICB9OwogICAgICB0aGlzLmNyZWF0ZVRpbWUgPSBbXTsKICAgICAgdGhpcy5nZXRMaXN0KCk7CiAgICB9LAogICAgZ2V0RmxhZzogZnVuY3Rpb24gZ2V0RmxhZyh2YWx1ZSkgewogICAgICB0aGlzLmZsYWcgPSB2YWx1ZTsKICAgIH0sCiAgICBnb3RvRGV0YWlsOiBmdW5jdGlvbiBnb3RvRGV0YWlsKGlkKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBwYXRoOiAiL3VzZXIvb3JkZXJNYW5hZ2VEZXRhaWwiLAogICAgICAgIHF1ZXJ5OiB7CiAgICAgICAgICBpZDogaWQKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGdldFN0YXR1czogZnVuY3Rpb24gZ2V0U3RhdHVzKHN0YXR1cykgewogICAgICB2YXIgb3JkZXJTdGF0dXM7CiAgICAgIHRoaXMub3JkZXJTdGF0dXNMaXN0LmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICBpZiAoaXRlbS5kaWN0VmFsdWUgPT0gc3RhdHVzKSB7CiAgICAgICAgICBvcmRlclN0YXR1cyA9IGl0ZW0uZGljdExhYmVsOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHJldHVybiBvcmRlclN0YXR1czsKICAgIH0sCiAgICBnb1NoaXA6IGZ1bmN0aW9uIGdvU2hpcChpZCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy4kY29uZmlybSgi6LSn5ZOB5Y+R6LSn5ZCO5peg5rOV5pKk6ZSA77yM56Gu6K6k5Y+R6LSn5ZCX77yfIiwgIuWPkei0p+ehruiupCIsIHsKICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsCiAgICAgICAgdHlwZTogIndhcm5pbmciCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHZhciBkYXRhID0gewogICAgICAgICAgaWQ6IGlkLAogICAgICAgICAgb3JkZXJTdGF0dXM6IDQKICAgICAgICB9OwogICAgICAgICgwLCBfdXNlci5tb2RpZnlTdGF0dXMpKGRhdGEpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyEiKTsKICAgICAgICAgICAgX3RoaXMyLmdldExpc3QoKTsKICAgICAgICAgIH0KICAgICAgICB9KTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIGludm9pY2luZzogZnVuY3Rpb24gaW52b2ljaW5nKGlkKSB7CiAgICAgIHZhciBfdGhpczMgPSB0aGlzOwogICAgICB0aGlzLmN1cnJlbnRJZCA9IGlkOwogICAgICAoMCwgX3VzZXIuaW52b2ljZUxpc3QpKCkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsKICAgICAgICAgIF90aGlzMy5pbnZvaWNlRGF0YSA9IHJlcy5kYXRhOwogICAgICAgICAgX3RoaXMzLmludm9pY2VWaXNpYmxlID0gdHJ1ZTsKICAgICAgICAgIF90aGlzMy5ydWxlRm9ybS5jb21wYW55Q2FyZExpc3QgPSBbXTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGNhbmNlbERpYWxvZzogZnVuY3Rpb24gY2FuY2VsRGlhbG9nKCkgewogICAgICB0aGlzLmludm9pY2VWaXNpYmxlID0gZmFsc2U7CiAgICB9LAogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnMucnVsZUZvcm0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICB2YXIgZGF0YSA9IHsKICAgICAgICAgICAgY29tcGFueUNhcmRMaXN0OiBfdGhpczQucnVsZUZvcm0uY29tcGFueUNhcmRMaXN0LAogICAgICAgICAgICBvcmRlcklkOiBfdGhpczQuY3VycmVudElkCiAgICAgICAgICB9OwogICAgICAgICAgKDAsIF91c2VyLnNlbmRJbnZvaWNlKShkYXRhKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsKICAgICAgICAgICAgICBfdGhpczQuaW52b2ljZVZpc2libGUgPSBmYWxzZTsKICAgICAgICAgICAgICBfdGhpczQuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfISIpOwogICAgICAgICAgICAgIF90aGlzNC5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zb2xlLmxvZygiZXJyb3Igc3VibWl0ISEiKTsKICAgICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfSwKICAgIGhhbmRsZUFwcGxpY2F0aW9uUmVtb3ZlOiBmdW5jdGlvbiBoYW5kbGVBcHBsaWNhdGlvblJlbW92ZShmaWxlLCBmaWxlTGlzdCkgewogICAgICB0aGlzLnJ1bGVGb3JtLmNvbXBhbnlDYXJkTGlzdCA9IFtdOwogICAgfSwKICAgIGhhbmRsZUFwcGxpY2F0aW9uU3VjY2VzczogZnVuY3Rpb24gaGFuZGxlQXBwbGljYXRpb25TdWNjZXNzKHJlcywgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgewogICAgICAgIHRoaXMucnVsZUZvcm0uY29tcGFueUNhcmRMaXN0LnB1c2goewogICAgICAgICAgbmFtZTogcmVzLmRhdGEubmFtZSwKICAgICAgICAgIHVybDogcmVzLmRhdGEudXJsLAogICAgICAgICAgdHlwZTogcmVzLmRhdGEudHlwZSwKICAgICAgICAgIHN1ZmZpeDogcmVzLmRhdGEuc3VmZml4CiAgICAgICAgfSk7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVFeGNlZWRMaWNlbmNlOiBmdW5jdGlvbiBoYW5kbGVFeGNlZWRMaWNlbmNlKGZpbGVzLCBmaWxlTGlzdCkgewogICAgICB2YXIgbnVtID0gZmlsZXMubGVuZ3RoICsgZmlsZUxpc3QubGVuZ3RoOwogICAgICBpZiAobnVtID49IDEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLkuIrkvKDmlbDph4/otoXov4fkuIrpmZAiKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVQcmV2aWV3OiBmdW5jdGlvbiBoYW5kbGVQcmV2aWV3KGZpbGUpIHsKICAgICAgd2luZG93Lm9wZW4oZmlsZS51cmwpOwogICAgfSwKICAgIC8vIOaWh+S7tuS4iuS8oOS5i+WJjeeahOmSqeWtkAogICAgaGFuZGxlQmVmb3JlVXBsb2FkOiBmdW5jdGlvbiBoYW5kbGVCZWZvcmVVcGxvYWQoZmlsZSkgewogICAgICB2YXIgbmFtZSA9IGZpbGUubmFtZSwKICAgICAgICB0eXBlID0gZmlsZS50eXBlLAogICAgICAgIHNpemUgPSBmaWxlLnNpemU7CiAgICAgIC8vIGxldCB0eXBlTGlzdCA9IHRoaXMuYWNjZXB0CiAgICAgIC8vICAgLnNwbGl0KCIsIikKICAgICAgLy8gICAubWFwKChpdGVtKSA9PiBpdGVtLnRyaW0oKS50b0xvd2VyQ2FzZSgpLnN1YnN0cigxKSk7CiAgICAgIC8vIGxldCBkb3RJbmRleCA9IG5hbWUubGFzdEluZGV4T2YoIi4iKTsKICAgICAgLy8gLy8g5paH5Lu257G75Z6L5qCh6aqMCiAgICAgIC8vIGlmIChkb3RJbmRleCA9PT0gLTEpIHsKICAgICAgLy8gICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7fkuIrkvKDmraPnoa7moLzlvI/nmoTmlofku7YiKTsKICAgICAgLy8gICByZXR1cm4gZmFsc2U7CiAgICAgIC8vIH0gZWxzZSB7CiAgICAgIC8vICAgbGV0IHN1ZmZpeCA9IG5hbWUuc3Vic3RyaW5nKGRvdEluZGV4ICsgMSk7CiAgICAgIC8vICAgaWYgKHR5cGVMaXN0LmluZGV4T2Yoc3VmZml4LnRvTG93ZXJDYXNlKCkpID09PSAtMSkgewogICAgICAvLyAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+35LiK5Lyg5q2j56Gu5qC85byP55qE5paH5Lu2Iik7CiAgICAgIC8vICAgICByZXR1cm4gZmFsc2U7CiAgICAgIC8vICAgfQogICAgICAvLyB9CiAgICAgIC8vIOaWh+S7tuS4iuS8oOWkp+Wwj+mZkOWItgogICAgICBpZiAoc2l6ZSA+IDEwNDg1NzYgKiA1KSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HNU3vvIEiKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0KICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_user", "require", "_userMenu", "_interopRequireDefault", "_oss", "_auth", "name", "dicts", "components", "UserMenu", "data", "validIsEmptyArr", "s", "value", "callback", "Array", "isArray", "length", "Error", "_defineProperty2", "default", "headers", "Authorization", "getToken", "actionUrl", "uploadUrl", "form", "id", "phone", "appName", "orderCode", "orderStatus", "createTime", "subscribeList", "queryParams", "pageNum", "total", "flag", "typeList", "dict<PERSON><PERSON>ue", "dict<PERSON><PERSON>l", "orderStatusList", "orderTypeList", "label", "companyCardList", "required", "validator", "trigger", "created", "getList", "methods", "_this", "loading", "params", "_objectSpread2", "createBy", "$store", "state", "user", "userId", "pageSize", "startTime", "endTime", "orderList", "then", "response", "rows", "handleCurrentChange", "handleQuery", "reset<PERSON><PERSON>y", "getFlag", "gotoDetail", "$router", "push", "path", "query", "getStatus", "status", "for<PERSON>ach", "item", "goShip", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "type", "modifyStatus", "res", "code", "$message", "success", "catch", "invoicing", "_this3", "currentId", "invoiceList", "invoiceData", "invoiceVisible", "ruleForm", "cancelDialog", "submitForm", "_this4", "$refs", "validate", "valid", "orderId", "sendInvoice", "console", "log", "handleApplicationRemove", "file", "fileList", "handleApplicationSuccess", "url", "suffix", "handleExceedLicence", "files", "num", "error", "handlePreview", "window", "open", "handleBeforeUpload", "size"], "sources": ["src/views/system/user/orderManage/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\">\r\n          <div class=\"content_type\">\r\n            <div class=\"title\">订单管理</div>\r\n          </div>\r\n          <div class=\"tabStyle\">\r\n            <!-- <div\r\n              v-for=\"item in orderTypeList\"\r\n              :key=\"item.value\"\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == item.value ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(item.value)\"\r\n            >\r\n              {{ item.label }}\r\n            </div> -->\r\n            <!-- <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 2 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(2)\"\r\n            >\r\n              待付款\r\n            </div>\r\n            <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 3 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(3)\"\r\n            >\r\n              待发货\r\n            </div>\r\n            <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 4 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(4)\"\r\n            >\r\n              待收货\r\n            </div>\r\n            <div\r\n              class=\"buttonStyle\"\r\n              :class=\"flag == 5 ? 'buttonHover' : ''\"\r\n              @click=\"getFlag(5)\"\r\n            >\r\n              已完成\r\n            </div> -->\r\n          </div>\r\n          <div style=\"margin-top: 20px\">\r\n            <el-form ref=\"form\" :model=\"form\" label-width=\"100px\">\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"订单编号\" prop=\"nickName\">\r\n                    <el-input v-model=\"form.id\" placeholder=\"请输入\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <!-- <el-col :span=\"6\">\r\n                  <el-form-item label=\"手机号\" prop=\"userName\">\r\n                    <el-input v-model=\"form.phone\" placeholder=\"请输入\" />\r\n                  </el-form-item>\r\n                </el-col> -->\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"应用名称\" prop=\"userName\">\r\n                    <el-input v-model=\"form.appName\" placeholder=\"请输入\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"订货编码\" prop=\"userName\">\r\n                    <el-input v-model=\"form.orderCode\" placeholder=\"请输入\" />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"订单状态\" prop=\"userName\">\r\n                    <el-select\r\n                      v-model=\"form.orderStatus\"\r\n                      placeholder=\"请选择\"\r\n                      clearable\r\n                      style=\"width: 100%\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"dict in orderStatusList\"\r\n                        :key=\"dict.dictValue\"\r\n                        :label=\"dict.dictLabel\"\r\n                        :value=\"dict.dictValue\"\r\n                      />\r\n                    </el-select>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"订单生成时间\" prop=\"userName\">\r\n                    <el-date-picker\r\n                      v-model=\"createTime\"\r\n                      type=\"daterange\"\r\n                      range-separator=\"至\"\r\n                      start-placeholder=\"开始日期\"\r\n                      end-placeholder=\"结束日期\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                    >\r\n                    </el-date-picker>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item>\r\n                    <el-button\r\n                      type=\"primary\"\r\n                      icon=\"el-icon-search\"\r\n                      size=\"mini\"\r\n                      @click=\"handleQuery\"\r\n                      >查询</el-button\r\n                    >\r\n                    <el-button\r\n                      icon=\"el-icon-refresh\"\r\n                      size=\"mini\"\r\n                      @click=\"resetQuery\"\r\n                      >重置</el-button\r\n                    >\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form>\r\n          </div>\r\n          <div class=\"tableStyle\" v-loading=\"loading\">\r\n            <div class=\"everyItem\" v-for=\"item in subscribeList\" :key=\"item.id\">\r\n              <div class=\"orderNumTime\">\r\n                <div>订单编号: {{ item.id }}</div>\r\n                <div style=\"margin-left: 5%\">\r\n                  下单时间: {{ item.createTime }}\r\n                </div>\r\n                <div style=\"margin-left: 5%\">下单人: {{ item.nickName }}</div>\r\n                <div style=\"margin-left: 5%\">企业名称: {{ item.supply }}</div>\r\n              </div>\r\n              <div class=\"driver\"></div>\r\n              <div class=\"item_content\">\r\n                <div class=\"item_img\">\r\n                  <img :src=\"item.appLogo\" alt=\"\" />\r\n                </div>\r\n                <div class=\"item_desc\">\r\n                  <div class=\"title\">{{ item.remark }}</div>\r\n                  <!-- <div style=\"font-size: 14px; margin-top: 10px\">\r\n                    <span style=\"color: #999999\">规格:</span>\r\n                    <span style=\"margin-left: 5px\">{{ item.spec }}</span>\r\n                  </div> -->\r\n                  <div style=\"font-size: 14px; margin-top: 10px\">\r\n                    <span style=\"color: #999999\">可用时长:</span>\r\n                    <span style=\"margin-left: 5px\">{{\r\n                      item.validTime == \"1\" ? \"一年\" : \"永久\"\r\n                    }}</span>\r\n                  </div>\r\n                  <div style=\"font-size: 14px; margin-top: 10px\">\r\n                    <span style=\"color: #999999\">可用人数:</span>\r\n                    <span style=\"margin-left: 5px\">{{ item.userNumber }}</span>\r\n                  </div>\r\n                </div>\r\n                <div class=\"item_amounts\">\r\n                  <div style=\"color: #999999; font-size: 14px\">订单金额</div>\r\n                  <div style=\"margin-top: 10px; font-weight: 400\">\r\n                    ￥{{ item.price }}\r\n                  </div>\r\n                </div>\r\n                <div class=\"driverVertical\"></div>\r\n                <div>\r\n                  <div>{{ getStatus(item.orderStatus) }}</div>\r\n                  <!-- <div\r\n                    style=\"margin-top: 10px; color: #21C9B8; cursor: pointer\"\r\n                    @click=\"gotoDetail(item.id)\"\r\n                  >\r\n                    订单详情\r\n                  </div> -->\r\n                </div>\r\n                <div style=\"margin: 0 7%\">\r\n                  <div\r\n                    style=\"color: #21c9b8; cursor: pointer\"\r\n                    @click=\"gotoDetail(item.id)\"\r\n                  >\r\n                    订单详情\r\n                  </div>\r\n                  <!-- 待发货 -->\r\n                  <div v-if=\"item.orderStatus == 2\">\r\n                    <div\r\n                      @click=\"goShip(item.id)\"\r\n                      style=\"margin-top: 10px; color: #21c9b8; cursor: pointer\"\r\n                    >\r\n                      去发货\r\n                    </div>\r\n                  </div>\r\n                  <!-- 已发货 -->\r\n                  <div v-if=\"item.orderStatus !== 2 && item.orderStatus !== 9\">\r\n                    <div\r\n                      @click=\"invoicing(item.id)\"\r\n                      style=\"margin-top: 10px; color: #21c9b8; cursor: pointer\"\r\n                    >\r\n                      {{ item.makeinvoice == 0 ? \"开具发票\" : \"重新开票\" }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div style=\"text-align: center; margin-top: 45px\">\r\n              <el-pagination\r\n                v-show=\"total > 0\"\r\n                background\r\n                layout=\"prev, pager, next\"\r\n                :page-size=\"5\"\r\n                :current-page.sync=\"queryParams.pageNum\"\r\n                @current-change=\"handleCurrentChange\"\r\n                :total=\"total\"\r\n              >\r\n              </el-pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      title=\"开票信息\"\r\n      :visible.sync=\"invoiceVisible\"\r\n      width=\"750px\"\r\n      append-to-body\r\n    >\r\n      <el-form\r\n        ref=\"ruleForm\"\r\n        :model=\"ruleForm\"\r\n        :rules=\"rules\"\r\n        label-width=\"80px\"\r\n      >\r\n        <el-form-item label=\"发票类型:\">\r\n          {{ invoiceData.invoiceType == 1 ? \"专票\" : \"普票\" }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司名称:\">\r\n          {{ invoiceData.companyName }}\r\n        </el-form-item>\r\n        <el-form-item label=\"税号:\">\r\n          {{ invoiceData.dutyParagraph }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司地址:\">\r\n          {{ invoiceData.address }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司电话:\">\r\n          {{ invoiceData.phone }}\r\n        </el-form-item>\r\n        <el-form-item label=\"开户银行:\">\r\n          {{ invoiceData.openAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"银行账号:\">\r\n          {{ invoiceData.bankAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱地址:\">\r\n          {{ invoiceData.email }}\r\n        </el-form-item>\r\n        <el-form-item label=\"上传发票\" prop=\"companyCardList\">\r\n          <el-upload\r\n            :headers=\"headers\"\r\n            :action=\"actionUrl\"\r\n            accept=\".pdf,.jpg,.png\"\r\n            :file-list=\"ruleForm.companyCardList\"\r\n            :before-upload=\"handleBeforeUpload\"\r\n            :on-remove=\"handleApplicationRemove\"\r\n            :on-success=\"handleApplicationSuccess\"\r\n            :on-exceed=\"handleExceedLicence\"\r\n            :on-preview=\"handlePreview\"\r\n            :limit=\"1\"\r\n          >\r\n            <div>\r\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload2\"\r\n                >上传文件</el-button\r\n              >\r\n              <span style=\"margin-left: 10px\">可上传pdf,jpg,png格式</span>\r\n            </div>\r\n          </el-upload>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">发送发票</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  orderList,\r\n  modifyStatus,\r\n  invoiceList,\r\n  sendInvoice,\r\n} from \"@/api/system/user\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    var validIsEmptyArr = (s, value, callback) => {\r\n      if (!Array.isArray(value) || value.length === 0) {\r\n        callback(new Error(\"请上传文件\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    return {\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      actionUrl: uploadUrl(),\r\n      form: {\r\n        id: \"\",\r\n        phone: \"\",\r\n        appName: \"\",\r\n        orderCode: \"\",\r\n        orderStatus: \"\",\r\n      },\r\n      createTime: [],\r\n      subscribeList: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      flag: 1,\r\n      typeList: [\r\n        {\r\n          dictValue: 1,\r\n          dictLabel: \"工业应用\",\r\n        },\r\n        {\r\n          dictValue: 2,\r\n          dictLabel: \"工业模型\",\r\n        },\r\n      ],\r\n      orderStatusList: [\r\n        {\r\n          dictValue: 1,\r\n          dictLabel: \"待支付\",\r\n        },\r\n        {\r\n          dictValue: 2,\r\n          dictLabel: \"待发货\",\r\n        },\r\n        {\r\n          dictValue: 3,\r\n          dictLabel: \"支付失败\",\r\n        },\r\n        {\r\n          dictValue: 4,\r\n          dictLabel: \"已发货\",\r\n        },\r\n        {\r\n          dictValue: 5,\r\n          dictLabel: \"已成交\",\r\n        },\r\n        {\r\n          dictValue: 6,\r\n          dictLabel: \"待续费\",\r\n        },\r\n        {\r\n          dictValue: 7,\r\n          dictLabel: \"已关闭\",\r\n        },\r\n        {\r\n          dictValue: 8,\r\n          dictLabel: \"支付中\",\r\n        },\r\n        {\r\n          dictValue: 9,\r\n          dictLabel: \"已取消\",\r\n        },\r\n      ],\r\n      orderTypeList: [\r\n        {\r\n          value: \"0\",\r\n          label: \"全部\",\r\n        },\r\n        {\r\n          value: \"1\",\r\n          label: \"待付款\",\r\n        },\r\n        {\r\n          value: \"2\",\r\n          label: \"待发货\",\r\n        },\r\n        {\r\n          value: \"3\",\r\n          label: \"待收货\",\r\n        },\r\n        {\r\n          value: \"4\",\r\n          label: \"已完成\",\r\n        },\r\n      ],\r\n      flag: \"0\",\r\n      loading: false,\r\n      invoiceVisible: false,\r\n      invoiceData: {},\r\n      ruleForm: {\r\n        companyCardList: [],\r\n      },\r\n      rules: {\r\n        companyCardList: [\r\n          { required: true, validator: validIsEmptyArr, trigger: \"change\" },\r\n        ],\r\n      },\r\n      currentId: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        createBy: this.$store.state.user.userId,\r\n        pageNum: this.queryParams.pageNum,\r\n        pageSize: 5,\r\n        ...this.form,\r\n        startTime: this.createTime.length > 0 ? this.createTime[0] : \"\",\r\n        endTime: this.createTime.length > 0 ? this.createTime[1] : \"\",\r\n      };\r\n      orderList(params).then((response) => {\r\n        this.subscribeList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.form = {\r\n        id: \"\",\r\n        phone: \"\",\r\n        appName: \"\",\r\n        orderCode: \"\",\r\n        orderStatus: \"\",\r\n      };\r\n      this.createTime = [];\r\n      this.getList();\r\n    },\r\n    getFlag(value) {\r\n      this.flag = value;\r\n    },\r\n    gotoDetail(id) {\r\n      this.$router.push({\r\n        path: \"/user/orderManageDetail\",\r\n        query: {\r\n          id,\r\n        },\r\n      });\r\n    },\r\n    getStatus(status) {\r\n      let orderStatus;\r\n      this.orderStatusList.forEach((item) => {\r\n        if (item.dictValue == status) {\r\n          orderStatus = item.dictLabel;\r\n        }\r\n      });\r\n      return orderStatus;\r\n    },\r\n    goShip(id) {\r\n      this.$confirm(\"货品发货后无法撤销，确认发货吗？\", \"发货确认\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          let data = {\r\n            id,\r\n            orderStatus: 4,\r\n          };\r\n          modifyStatus(data).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    invoicing(id) {\r\n      this.currentId = id;\r\n      invoiceList().then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceData = res.data;\r\n          this.invoiceVisible = true;\r\n          this.ruleForm.companyCardList = [];\r\n        }\r\n      });\r\n    },\r\n    cancelDialog() {\r\n      this.invoiceVisible = false;\r\n    },\r\n    submitForm() {\r\n      this.$refs.ruleForm.validate((valid) => {\r\n        if (valid) {\r\n          let data = {\r\n            companyCardList: this.ruleForm.companyCardList,\r\n            orderId: this.currentId,\r\n          };\r\n          sendInvoice(data).then((res) => {\r\n            if (res.code === 200) {\r\n              this.invoiceVisible = false;\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        } else {\r\n          console.log(\"error submit!!\");\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.ruleForm.companyCardList = [];\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      if (res.code == 200) {\r\n        this.ruleForm.companyCardList.push({\r\n          name: res.data.name,\r\n          url: res.data.url,\r\n          type: res.data.type,\r\n          suffix: res.data.suffix,\r\n        });\r\n      }\r\n    },\r\n    handleExceedLicence(files, fileList) {\r\n      let num = files.length + fileList.length;\r\n      if (num >= 1) {\r\n        this.$message.error(\"上传数量超过上限\");\r\n        return false;\r\n      }\r\n    },\r\n    handlePreview(file) {\r\n      window.open(file.url);\r\n    },\r\n    // 文件上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      // let typeList = this.accept\r\n      //   .split(\",\")\r\n      //   .map((item) => item.trim().toLowerCase().substr(1));\r\n      // let dotIndex = name.lastIndexOf(\".\");\r\n      // // 文件类型校验\r\n      // if (dotIndex === -1) {\r\n      //   this.$message.error(\"请上传正确格式的文件\");\r\n      //   return false;\r\n      // } else {\r\n      //   let suffix = name.substring(dotIndex + 1);\r\n      //   if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n      //     this.$message.error(\"请上传正确格式的文件\");\r\n      //     return false;\r\n      //   }\r\n      // }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 5) {\r\n        this.$message.error(\"文件大小不能超过5M！\");\r\n        return false;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  // height: 800px;\r\n  // background: rgb(242, 248, 255);\r\n  .content_type {\r\n    display: flex;\r\n    width: 100%;\r\n    margin-bottom: 30px;\r\n    .title {\r\n      width: 100px;\r\n      padding-left: 20px;\r\n      height: 30px;\r\n      line-height: 30px;\r\n      // border-left: 4px solid #21C9B8;\r\n      font-weight: 600;\r\n      font-size: 18px;\r\n    }\r\n    // .right_content {\r\n    //   width: calc(100% - 100px);\r\n    //   text-align: right;\r\n    // }\r\n  }\r\n  .tabStyle {\r\n    display: flex;\r\n    // background: rgb(243, 248, 254);\r\n    .buttonStyle {\r\n      width: 100px;\r\n      padding: 10px;\r\n      color: #21c9b8;\r\n      text-align: center;\r\n      cursor: pointer;\r\n      border: 1px solid #21c9b8;\r\n    }\r\n    .buttonStyle:nth-child(n + 2) {\r\n      border-left: none;\r\n    }\r\n    .buttonHover {\r\n      background: #21c9b8;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .tableStyle {\r\n    .everyItem {\r\n      width: 100%;\r\n      height: 200px;\r\n      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n      margin-top: 20px;\r\n      padding: 20px;\r\n      // background: #ffffff;\r\n      .orderNumTime {\r\n        display: flex;\r\n      }\r\n      .driver {\r\n        width: 100%;\r\n        height: 1px;\r\n        background: #ccc;\r\n        margin: 15px 0;\r\n      }\r\n      .item_content {\r\n        width: 100%;\r\n        // height: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        .item_img {\r\n          width: 14%;\r\n          height: 110px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .item_desc {\r\n          margin-left: 20px;\r\n          width: 25%;\r\n          .title {\r\n            font-size: 16px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            color: #333333;\r\n          }\r\n        }\r\n        .item_amounts {\r\n          width: 10%;\r\n          text-align: right;\r\n        }\r\n        .driverVertical {\r\n          width: 1px;\r\n          height: 110px;\r\n          background: #ccc;\r\n          margin: 0 8%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AA0RA,IAAAA,KAAA,GAAAC,OAAA;AAMA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,IAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAK,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA,IAAAC,eAAA,YAAAA,gBAAAC,CAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAH,KAAA,KAAAA,KAAA,CAAAI,MAAA;QACAH,QAAA,KAAAI,KAAA;QACA;MACA;MACAJ,QAAA;IACA;IACA,WAAAK,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;MACAC,OAAA;QAAAC,aAAA,kBAAAC,cAAA;MAAA;MACAC,SAAA,MAAAC,cAAA;MACAC,IAAA;QACAC,EAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,WAAA;MACA;MACAC,UAAA;MACAC,aAAA;MACAC,WAAA;QACAC,OAAA;MACA;MACAC,KAAA;MACAC,IAAA;MACAC,QAAA,GACA;QACAC,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,EACA;MACAC,eAAA,GACA;QACAF,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,EACA;MACAE,aAAA,GACA;QACA7B,KAAA;QACA8B,KAAA;MACA,GACA;QACA9B,KAAA;QACA8B,KAAA;MACA,GACA;QACA9B,KAAA;QACA8B,KAAA;MACA,GACA;QACA9B,KAAA;QACA8B,KAAA;MACA,GACA;QACA9B,KAAA;QACA8B,KAAA;MACA;IACA,WACA,iBACA,0BACA,uBACA,iBACA;MACAC,eAAA;IACA,aACA;MACAA,eAAA,GACA;QAAAC,QAAA;QAAAC,SAAA,EAAAnC,eAAA;QAAAoC,OAAA;MAAA;IAEA,iBACA;EAEA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAC,OAAA;MACA,IAAAC,MAAA,OAAAC,cAAA,CAAAlC,OAAA,MAAAkC,cAAA,CAAAlC,OAAA;QACAmC,QAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA;QACAxB,OAAA,OAAAD,WAAA,CAAAC,OAAA;QACAyB,QAAA;MAAA,GACA,KAAAlC,IAAA;QACAmC,SAAA,OAAA7B,UAAA,CAAAf,MAAA,YAAAe,UAAA;QACA8B,OAAA,OAAA9B,UAAA,CAAAf,MAAA,YAAAe,UAAA;MAAA,EACA;MACA,IAAA+B,eAAA,EAAAV,MAAA,EAAAW,IAAA,WAAAC,QAAA;QACAd,KAAA,CAAAlB,aAAA,GAAAgC,QAAA,CAAAC,IAAA;QACAf,KAAA,CAAAf,KAAA,GAAA6B,QAAA,CAAA7B,KAAA;QACAe,KAAA,CAAAC,OAAA;MACA;IACA;IACAe,mBAAA,WAAAA,oBAAAhC,OAAA;MACA,KAAAD,WAAA,CAAAC,OAAA,GAAAA,OAAA;MACA,KAAAc,OAAA;IACA;IACAmB,WAAA,WAAAA,YAAA;MACA,KAAAnB,OAAA;IACA;IACAoB,UAAA,WAAAA,WAAA;MACA,KAAAnC,WAAA,CAAAC,OAAA;MACA,KAAAT,IAAA;QACAC,EAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;QACAC,WAAA;MACA;MACA,KAAAC,UAAA;MACA,KAAAiB,OAAA;IACA;IACAqB,OAAA,WAAAA,QAAAzD,KAAA;MACA,KAAAwB,IAAA,GAAAxB,KAAA;IACA;IACA0D,UAAA,WAAAA,WAAA5C,EAAA;MACA,KAAA6C,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAC,KAAA;UACAhD,EAAA,EAAAA;QACA;MACA;IACA;IACAiD,SAAA,WAAAA,UAAAC,MAAA;MACA,IAAA9C,WAAA;MACA,KAAAU,eAAA,CAAAqC,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAxC,SAAA,IAAAsC,MAAA;UACA9C,WAAA,GAAAgD,IAAA,CAAAvC,SAAA;QACA;MACA;MACA,OAAAT,WAAA;IACA;IACAiD,MAAA,WAAAA,OAAArD,EAAA;MAAA,IAAAsD,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACArB,IAAA;QACA,IAAAtD,IAAA;UACAiB,EAAA,EAAAA,EAAA;UACAI,WAAA;QACA;QACA,IAAAuD,kBAAA,EAAA5E,IAAA,EAAAsD,IAAA,WAAAuB,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACAP,MAAA,CAAAQ,QAAA,CAAAC,OAAA;YACAT,MAAA,CAAAhC,OAAA;UACA;QACA;MACA,GACA0C,KAAA;IACA;IACAC,SAAA,WAAAA,UAAAjE,EAAA;MAAA,IAAAkE,MAAA;MACA,KAAAC,SAAA,GAAAnE,EAAA;MACA,IAAAoE,iBAAA,IAAA/B,IAAA,WAAAuB,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAK,MAAA,CAAAG,WAAA,GAAAT,GAAA,CAAA7E,IAAA;UACAmF,MAAA,CAAAI,cAAA;UACAJ,MAAA,CAAAK,QAAA,CAAAtD,eAAA;QACA;MACA;IACA;IACAuD,YAAA,WAAAA,aAAA;MACA,KAAAF,cAAA;IACA;IACAG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAJ,QAAA,CAAAK,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAA9F,IAAA;YACAkC,eAAA,EAAAyD,MAAA,CAAAH,QAAA,CAAAtD,eAAA;YACA6D,OAAA,EAAAJ,MAAA,CAAAP;UACA;UACA,IAAAY,iBAAA,EAAAhG,IAAA,EAAAsD,IAAA,WAAAuB,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACAa,MAAA,CAAAJ,cAAA;cACAI,MAAA,CAAAZ,QAAA,CAAAC,OAAA;cACAW,MAAA,CAAApD,OAAA;YACA;UACA;QACA;UACA0D,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;IACAC,uBAAA,WAAAA,wBAAAC,IAAA,EAAAC,QAAA;MACA,KAAAb,QAAA,CAAAtD,eAAA;IACA;IACAoE,wBAAA,WAAAA,yBAAAzB,GAAA,EAAAuB,IAAA,EAAAC,QAAA;MACA,IAAAxB,GAAA,CAAAC,IAAA;QACA,KAAAU,QAAA,CAAAtD,eAAA,CAAA6B,IAAA;UACAnE,IAAA,EAAAiF,GAAA,CAAA7E,IAAA,CAAAJ,IAAA;UACA2G,GAAA,EAAA1B,GAAA,CAAA7E,IAAA,CAAAuG,GAAA;UACA5B,IAAA,EAAAE,GAAA,CAAA7E,IAAA,CAAA2E,IAAA;UACA6B,MAAA,EAAA3B,GAAA,CAAA7E,IAAA,CAAAwG;QACA;MACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,KAAA,EAAAL,QAAA;MACA,IAAAM,GAAA,GAAAD,KAAA,CAAAnG,MAAA,GAAA8F,QAAA,CAAA9F,MAAA;MACA,IAAAoG,GAAA;QACA,KAAA5B,QAAA,CAAA6B,KAAA;QACA;MACA;IACA;IACAC,aAAA,WAAAA,cAAAT,IAAA;MACAU,MAAA,CAAAC,IAAA,CAAAX,IAAA,CAAAG,GAAA;IACA;IACA;IACAS,kBAAA,WAAAA,mBAAAZ,IAAA;MACA,IAAAxG,IAAA,GAAAwG,IAAA,CAAAxG,IAAA;QAAA+E,IAAA,GAAAyB,IAAA,CAAAzB,IAAA;QAAAsC,IAAA,GAAAb,IAAA,CAAAa,IAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAAA,IAAA;QACA,KAAAlC,QAAA,CAAA6B,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}