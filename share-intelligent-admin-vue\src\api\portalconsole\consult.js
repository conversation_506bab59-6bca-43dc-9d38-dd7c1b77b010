import request from '@/utils/request'

// 查询政策辅助申报列表
export function listConsult(query) {
  return request({
    url: '/portalconsole/consult/list',
    method: 'get',
    params: query
  })
}

// 查询政策辅助申报详细
export function getConsult(policySubmitConsultId) {
  return request({
    url: '/portalconsole/consult/' + policySubmitConsultId,
    method: 'get'
  })
}

// 新增政策辅助申报
export function addConsult(data) {
  return request({
    url: '/portalconsole/consult',
    method: 'post',
    data: data
  })
}

// 修改政策辅助申报
export function updateConsult(data) {
  return request({
    url: '/portalconsole/consult',
    method: 'put',
    data: data
  })
}

// 删除政策辅助申报
export function delConsult(policySubmitConsultId) {
  return request({
    url: '/portalconsole/consult/' + policySubmitConsultId,
    method: 'delete'
  })
}
