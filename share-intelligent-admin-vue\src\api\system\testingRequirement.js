import request from '@/utils/request'

// 查询检测需求列表
export function listTestingRequirement(query) {
  return request({
    url: '/system/testingRequirement/list',
    method: 'get',
    params: query
  })
}

// 查询检测需求详细
export function getTestingRequirement(id) {
  return request({
    url: '/system/testingRequirement/' + id,
    method: 'get'
  })
}

// 新增检测需求
export function addTestingRequirement(data) {
  return request({
    url: '/system/testingRequirement',
    method: 'post',
    data: data
  })
}

// 修改检测需求
export function updateTestingRequirement(data) {
  return request({
    url: '/system/testingRequirement',
    method: 'put',
    data: data
  })
}

// 删除检测需求
export function delTestingRequirement(id) {
  return request({
    url: '/system/testingRequirement/' + id,
    method: 'delete'
  })
}
