{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\userCenter\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\userCenter\\index.vue", "mtime": 1750311963090}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuFA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/userCenter", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"main-content\">\r\n          <el-row :gutter=\"30\">\r\n            <el-col :span=\"5\" style=\"opacity: 0;\">marginleft</el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form :model=\"userData\" :rules=\"rules\" ref=\"userData\" label-width=\"80px\">\r\n                <el-form-item label=\"头像\" prop=\"avatar\">\r\n                  <ImageUpload v-model=\"avatarList\" :limit=\"1\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"真实姓名\" prop=\"memberRealName\">\r\n                  <el-input v-model=\"userData.memberRealName\" placeholder=\"请输入真实姓名\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式\" prop=\"memberPhone\">\r\n                  <el-input v-model=\"userData.memberPhone\" placeholder=\"请输入联系方式\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"微信号\" prop=\"memberWechat\">\r\n                  <el-input v-model=\"userData.memberWechat\" placeholder=\"请输入微信号\" />\r\n                </el-form-item>\r\n                <el-form-item label=\"所在行业\" prop=\"solutionTypeId\">\r\n                  <el-select v-model=\"userData.solutionTypeId\" placeholder=\"请选择\" class=\"form-select\">\r\n                    <el-option v-for=\"item in solutionTypeList\" :key=\"item.solutionTypeId\"\r\n                      :label=\"item.solutionTypeName\" :value=\"item.solutionTypeId\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"职位\" prop=\"memberPost\">\r\n                  <el-select v-model=\"userData.memberPost\" placeholder=\"请选择\" class=\"form-select\">\r\n                    <el-option v-for=\"item in memberPostList\" :key=\"item.dictValue\" :label=\"item.dictLabel\"\r\n                      :value=\"item.dictValue\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"企业名称\" prop=\"memberCompanyName\">\r\n                  <el-autocomplete v-model=\"userData.memberCompanyName\" placeholder=\"请输入您公司的完整名称\"\r\n                    :fetch-suggestions=\"querySearchTianYanCha\" @select=\"selectAutoDataTianYanCha\"\r\n                    style=\"width: 84%\"></el-autocomplete>\r\n                </el-form-item>\r\n                <el-form-item label=\"企业地址\" prop=\"memberCompanyArea\">\r\n                  <el-cascader clearable label=\"title\" value=\"id\" :options=\"areaList\"\r\n                    v-model=\"userData.memberCompanyArea\" :props=\"props\" placeholder=\"请选择地区\"\r\n                    class=\"form-select\"></el-cascader>\r\n\r\n                </el-form-item>\r\n                <el-form-item label=\"企业规模\" prop=\"companyScale\">\r\n                  <el-select v-model=\"userData.companyScale\" placeholder=\"请选择\" class=\"form-select\">\r\n                    <el-option v-for=\"item in companyScaleList\" :key=\"item.dictValue\" :label=\"item.dictLabel\"\r\n                      :value=\"item.dictValue\">\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-form>\r\n              <div class=\"btn-box\">\r\n                <el-button class=\"btn\" type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <!-- <div class=\"card\" v-if=\"!talentInfo\">\r\n                <div class=\"card-title\">人才库</div>\r\n                <div class=\"card-content\">您暂未入驻!</div>\r\n                <div class=\"card-content\">入驻后可获取需求企业对接</div>\r\n                <div class=\"btn-card\">\r\n                  <el-button class=\"btn\" type=\"primary\" @click=\"gotoTalentJoinNow('')\">立即入驻</el-button>\r\n                </div>\r\n              </div>\r\n              <div class=\"card\" v-if=\"talentInfo\">\r\n                <div class=\"card-title\">人才库</div>\r\n                <div class=\"card-content success\">您已经入驻!</div>\r\n                <div class=\"card-content\">点击下方按钮可预览，</div>\r\n                <div class=\"card-content\">如修改内容需重新审核。</div>\r\n                <div class=\"btn-card\">\r\n                  <el-button class=\"btn\" type=\"primary\" @click=\"gotoTalentJoinNow(talentInfo)\">查看信息</el-button>\r\n                </div>\r\n              </div> -->\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { industryList, getAreaData, userInfoSave } from \"@/api/system/user\";\r\nimport { searchCompany, getCompanyCodeByName } from \"@/api/system/company\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { getInfo } from \"@/api/login\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      userData: {},\r\n      settled: false,\r\n      locationhost: '',\r\n      showLogin: false,\r\n      userinfo: {},\r\n      token: '',\r\n      userType: '',\r\n      id: '',\r\n      detail: {},\r\n      setting: false,\r\n      cooperationData: [],\r\n      demandData: [],\r\n      showSuccess: false,\r\n      jbzldata: true,\r\n      qyzhdata: false,\r\n      imgLen: 0,\r\n      showPop1: false,\r\n      showPop2: false,\r\n      areaList: [],\r\n      props: {\r\n        label: 'title',\r\n        value: 'id',\r\n        children: 'children',\r\n        multiple: false\r\n      },\r\n      companyScaleList: [], // 企业规模\r\n      memberPostList: [], // 职位\r\n      solutionTypeList: [], // 行业\r\n      rules: {\r\n        memberPhone: [\r\n          { pattern: /^1[3456789]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n        ],\r\n      },\r\n      talentInfo: {},\r\n      avatarList: '',\r\n    };\r\n  },\r\n  created() {\r\n    // if (window.sessionStorage.getItem('userinfo') != 'null' && window.sessionStorage.getItem('userinfo') != 'undefined' && window.sessionStorage.getItem('userinfo')) {\r\n    //   this.userinfo = JSON.parse(window.sessionStorage.getItem('userinfo'))\r\n    //   let info = this.userinfo\r\n    //   info.solutionTypeId = info.solutionTypeId ? info.solutionTypeId.toString() : ''\r\n    //   info.memberCompanyArea = info.memberCompanyArea ? info.memberCompanyArea.split(',') : []\r\n    //   this.userData = info\r\n    //   this.avatarList = this.userData.avatar ? [this.userData.avatar] : []\r\n    // } else {\r\n    // }\r\n    this.getInfo()\r\n    this.getSolutionType()\r\n    this.getMemberPost()\r\n    this.getCity()\r\n    this.getCompanyScale()\r\n  },\r\n  methods: {\r\n    // 获取用户信息\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (res.code == 200) {\r\n          this.userData = res.member || {};\r\n          this.userData.solutionTypeId = this.userData.solutionTypeId ? this.userData.solutionTypeId.toString() : ''\r\n          this.userData.memberCompanyArea = this.userData.memberCompanyArea ? this.userData.memberCompanyArea.split(',') : []\r\n          this.avatarList = this.userData.avatar ? this.userData.avatar : ''\r\n          this.talentInfo = res.talentInfo || {};\r\n        }\r\n      });\r\n    },\r\n    submitForm() {\r\n      this.$refs['userData'].validate((valid) => {\r\n        if (valid) {\r\n          let params = {\r\n            ...this.userData,\r\n            memberCompanyArea: this.userData.memberCompanyArea ? this.userData.memberCompanyArea.join(',') : '',\r\n            avatar: this.avatarList ? this.avatarList : ''\r\n          }\r\n          userInfoSave(params).then(res => {\r\n            if (res.code && res.code == 200) {\r\n              this.$message({\r\n                showClose: true,\r\n                message: '保存成功',\r\n                type: 'success'\r\n              });\r\n              window.sessionStorage.setItem('userinfo', JSON.stringify(res.member))\r\n              window.sessionStorage.setItem('userName', res.member.memberPhone);\r\n              window.sessionStorage.setItem('userId', res.member.memberId);\r\n              window.history.go(-1)\r\n            } else {\r\n              this.$message({\r\n                showClose: true,\r\n                message: '保存失败',\r\n                type: 'error'\r\n              });\r\n            }\r\n          });\r\n        }\r\n      });\r\n\r\n\r\n    },\r\n    // 行业\r\n    getSolutionType() {\r\n      industryList({}).then(res => {\r\n        if (res.code == 200) {\r\n          this.solutionTypeList = res.rows\r\n        }\r\n      })\r\n    },\r\n    // 职位\r\n    getMemberPost() {\r\n      let params = { dictType: \"member_post\" };\r\n      listData(params).then((response) => {\r\n        if (response.code == 200) {\r\n          this.memberPostList = response.rows;\r\n        }\r\n      });\r\n    },\r\n    // 企业名称\r\n    querySearchTianYanCha(queryString, cb) {\r\n      if (queryString) {\r\n        searchCompany({ keywords: queryString }).then(res => {\r\n          let data = res.rows;\r\n          let List = [];\r\n          data.forEach(function (val, index) {\r\n            List.push({\r\n              id: index,\r\n              value: val\r\n            })\r\n          })\r\n          if (data.length > 0) {\r\n            cb(List);\r\n          } else {\r\n            cb([{\r\n              id: '',\r\n              value: '暂无数据'\r\n            }]);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 企业名称选择\r\n    selectAutoDataTianYanCha(row) {\r\n      getCompanyCodeByName({ keywords: row.value }).then(res => {\r\n        if (res.code == 200) {\r\n          let data = res.data;\r\n          this.$set(this.userData, 'socialUnityCreditCode', data.taxNo)\r\n        }\r\n      });\r\n    },\r\n    // 企业地址\r\n    getCity() {\r\n      let cityList = window.sessionStorage.getItem('cityList');\r\n      if (!cityList) {\r\n        getAreaData().then(res => {\r\n          if (res.code == 200) {\r\n            let areaList = res.rows\r\n            areaList.forEach(item => {\r\n              item.children.forEach(item1 => {\r\n                item1.children.forEach(item2 => {\r\n                  delete item2.children\r\n                })\r\n              })\r\n            })\r\n            window.sessionStorage.setItem('cityList', JSON.stringify(areaList));\r\n            this.areaList = areaList;\r\n          }\r\n        })\r\n      } else {\r\n        this.areaList = JSON.parse(JSON.parse(JSON.stringify(cityList)))\r\n      }\r\n      // console.log( this.areaList)\r\n    },\r\n    // 职位\r\n    getCompanyScale() {\r\n      let params = { dictType: \"company_scale\" };\r\n      listData(params).then((response) => {\r\n        if (response.code == 200) {\r\n          this.companyScaleList = response.rows;\r\n        }\r\n      });\r\n    },\r\n    gotoTalentJoinNow(info) {\r\n      if (info) {\r\n        this.$router.push({ path: '/user/talentDetail', query: { id: info.id } })\r\n      } else {\r\n        this.$router.push({ path: '/user/talentJoinNow' })\r\n      }\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.main-content {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  padding-bottom: 100px;\r\n\r\n  .btn-box {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin-top: 40px;\r\n\r\n    .btn {\r\n      width: 200px;\r\n      height: 50px;\r\n    }\r\n  }\r\n\r\n  .card {\r\n    margin-top: 170px;\r\n    padding: 10px;\r\n    box-sizing: border-box;\r\n    width: 280px;\r\n    height: 240px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n    background: url(\"../../../../assets/userCenter/card_bg.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n\r\n\r\n    .card-title {\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      margin-bottom: 10px;\r\n      color: #030A1A;\r\n    }\r\n\r\n    .card-content {\r\n      font-size: 14px;\r\n      color: #666666;\r\n      line-height: 30px;\r\n    }\r\n\r\n    .success {\r\n      color: #21C9B8;\r\n    }\r\n\r\n    .btn-card {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin-top: 0px;\r\n\r\n      .btn {\r\n        width: 200px;\r\n        height: 50px;\r\n      }\r\n    }\r\n  }\r\n\r\n}\r\n</style>\r\n"]}]}