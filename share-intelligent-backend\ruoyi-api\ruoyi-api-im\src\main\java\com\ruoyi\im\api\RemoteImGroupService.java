package com.ruoyi.im.api;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.im.api.domain.ImGroup;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api
 * @ClassName: RemoteImGroupService
 * @Author: ${maguojun}
 * @Description: ${description}
 * @Date: 2022/3/16 13:48
 * @Version: 1.0
 */
@FeignClient(contextId = "remoteImGroupService", value = ServiceNameConstants.IM_SERVICE)
public interface RemoteImGroupService {
    /**
    * @Description:
    * @Param:
    * @return:
    * @Author: 新增ImGroup数据 创建群组
    * @Date:
    */
    @PostMapping(value="/im/group/add")
    R<String> add(@RequestBody ImGroup imGroup);



    /***
     * 解散群组
     * @param imGroup
     * @return
     */
    @PostMapping(value="/im/group/deleteGroup")
    R<Boolean> deleteGroup(@RequestBody ImGroup imGroup);



    /***
     * 修改ImGroup数据
     * @param imGroup
     * @return
     */
    @PostMapping(value="/im/group/update")
    R<Boolean> update(@RequestBody ImGroup imGroup);
}
