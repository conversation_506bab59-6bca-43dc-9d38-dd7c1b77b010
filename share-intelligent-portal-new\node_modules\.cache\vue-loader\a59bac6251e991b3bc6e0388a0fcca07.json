{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\profile\\index(copy).vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\profile\\index(copy).vue", "mtime": 1750311963072}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgc3RvcmUgZnJvbSAiQC9zdG9yZSI7DQppbXBvcnQgdXNlckF2YXRhciBmcm9tICIuL3VzZXJBdmF0YXIiOw0KaW1wb3J0IHVzZXJJbmZvIGZyb20gIi4vdXNlckluZm8iOw0KaW1wb3J0IHJlc2V0UHdkIGZyb20gIi4vcmVzZXRQd2QiOw0KLy8gaW1wb3J0IHsgbGlzdEluZm8gfSBmcm9tICJAL2FwaS9zeXN0ZW0vaW5mbyI7DQppbXBvcnQgeyBnZXRBYnV0bWVudExpc3QgfSBmcm9tICJAL2FwaS9zeXN0ZW0vYWJ1dG1lbnQiOw0KDQppbXBvcnQgew0KICBnZXRVc2VySW5mbywNCiAgY2hlY2tBdXRoU3RhdHVzLA0KICBzdWJsaXN0LA0KICBhcHBPcmRlck51bSwNCiAgc3ViU3RhdGlzdGljcywNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOw0KaW1wb3J0IFVzZXJNZW51IGZyb20gIi4uL2NvbXBvbmVudHMvdXNlck1lbnUudnVlIjsNCmltcG9ydCAqIGFzIFJvbmdJTUxpYiBmcm9tICJAcm9uZ2Nsb3VkL2ltbGliLW5leHQiOw0KaW1wb3J0IHsgZ2V0VXNlcklNVG9rZW4gfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7DQppbXBvcnQgeyBnZXRVc2VyTGlzdEJ5SWRzIH0gZnJvbSAiQC9hcGkvaW0uanMiOw0KaW1wb3J0ICogYXMgZWNoYXJ0cyBmcm9tICJlY2hhcnRzIjsNCmltcG9ydCB7IGxpc3RJbmZvIH0gZnJvbSAiQC9hcGkvc3lzdGVtL2luZm8iOw0KaW1wb3J0IHsgYXBwbGlMaXN0IH0gZnJvbSAiQC9hcGkvYXBwbGlNYXJrZXQiOw0KaW1wb3J0IHsgb3JkZXJMaXN0IH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJQcm9maWxlIiwNCiAgY29tcG9uZW50czogeyB1c2VyQXZhdGFyLCB1c2VySW5mbywgcmVzZXRQd2QsIFVzZXJNZW51IH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHVzZXI6IHsNCiAgICAgICAgbmFtZTogc3RvcmUuZ2V0dGVycy5uYW1lLA0KICAgICAgICBhdmF0YXI6IHN0b3JlLmdldHRlcnMuYXZhdGFyLA0KICAgICAgICBidXNzaW5lc3NObzogc3RvcmUuZ2V0dGVycy5idXNzaW5lc3NObywNCiAgICAgICAgaWQ6IHN0b3JlLmdldHRlcnMudXNlcklkLA0KICAgICAgfSwNCiAgICAgIHJvbGVHcm91cDoge30sDQogICAgICB1c2VySW5mbzoge30sDQogICAgICBwb3N0R3JvdXA6IHt9LA0KICAgICAgZml0OiAiY292ZXIiLA0KICAgICAgcmVzcG9uc2VMaXN0OiBbXSwNCiAgICAgIHJlc3BvbnNlTGlzdDogW10sDQogICAgICBhYnV0bWVudExpc3Q6IFtdLA0KICAgICAgY29udmVyc2F0aW9uTGlzdDogW10sDQogICAgICBjb21wYW55U3RhdHVzOiAiIiwNCiAgICAgIGFjdGl2ZVRhYjogInVzZXJpbmZvIiwNCiAgICAgIHVybDogImh0dHBzOi8vZnVzczEwLmVsZW1lY2RuLmNvbS9lLzVkLzRhNzMxYTkwNTk0YTRhZjU0NGMwYzI1OTQxMTcxanBlZy5qcGVnIiwNCiAgICAgIG5vdGlmeUxpc3Q6IFtdLA0KICAgICAgc3Vic2NyaWJlTGlzdDogW10sDQogICAgICBvcmRlcm1hbmFnZUxpc3Q6IFtdLA0KICAgICAgdmFsdWVZZWFyOiAiIiwNCiAgICAgIHZhbHVlTW9udGg6ICIiLA0KICAgICAgYXBwbGljYXRpb25MaXN0OiBbXSwNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICB9LA0KICAgICAgb3JkZXJTdGF0dXNMaXN0OiBbDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0VmFsdWU6IDEsDQogICAgICAgICAgZGljdExhYmVsOiAi5b6F5pSv5LuYIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RWYWx1ZTogMiwNCiAgICAgICAgICBkaWN0TGFiZWw6ICLlvoXlj5HotKciLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdFZhbHVlOiAzLA0KICAgICAgICAgIGRpY3RMYWJlbDogIuaUr+S7mOWksei0pSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0VmFsdWU6IDQsDQogICAgICAgICAgZGljdExhYmVsOiAi5bey5Y+R6LSnIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RWYWx1ZTogNSwNCiAgICAgICAgICBkaWN0TGFiZWw6ICLlt7LmiJDkuqQiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdFZhbHVlOiA2LA0KICAgICAgICAgIGRpY3RMYWJlbDogIuW+hee7rei0uSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0VmFsdWU6IDcsDQogICAgICAgICAgZGljdExhYmVsOiAi5bey5YWz6ZetIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RWYWx1ZTogOCwNCiAgICAgICAgICBkaWN0TGFiZWw6ICLmlK/ku5jkuK0iLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdFZhbHVlOiA5LA0KICAgICAgICAgIGRpY3RMYWJlbDogIuW3suWPlua2iCIsDQogICAgICAgIH0sDQogICAgICBdLA0KICAgICAgYXBwT3JkZXJEYXRhOiB7fSwNCiAgICAgIHN1YlN0YXRpc3RpY3NEYXRhOiBbXSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TWVzc2FnZSgpOw0KICAgIHRoaXMuZ2V0U3VibGlzdCgpOw0KICAgIHRoaXMuZ2V0QXBwbGlMaXN0KCk7DQogICAgdGhpcy5nZXRPcmRlckxpc3QoKTsNCiAgICB0aGlzLmdldEFwcE9yZGVyTnVtKCk7DQogICAgdGhpcy5nZXRTdWJTdGF0aXN0aWNzKCk7DQogICAgdGhpcy5nZXRVc2VyKCk7DQogICAgLy8gdGhpcy5nZXRSZXNwb25zZUxpc3QoKTsNCiAgICAvLyB0aGlzLmdldEFidXRtZW50TGlzdCgpOw0KICAgIC8vIHRoaXMuZ2V0SW1NZXNzYWdlKCk7DQogIH0sDQogIC8vIG1vdW50ZWQoKSB7DQogIC8vICAgdGhpcy5nZXRJbml0RWNoYXJ0cygpOw0KICAvLyB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0TWVzc2FnZSgpIHsNCiAgICAgIGxldCBkYXRhID0gew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogNCwNCiAgICAgICAgdHlwZTogMiwNCiAgICAgIH07DQogICAgICBsaXN0SW5mbyhkYXRhKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLm5vdGlmeUxpc3QgPSByZXNwb25zZS5yb3dzLnNsaWNlKDAsIDQpOw0KICAgICAgICBjb25zb2xlLmxvZyh0aGlzLm5vdGlmeUxpc3QsICItLS0tLS0tLS0tIik7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldFN1Ymxpc3QoKSB7DQogICAgICBzdWJsaXN0KCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5zdWJzY3JpYmVMaXN0ID0gcmVzLnJvd3Muc2xpY2UoMCwgMik7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0QXBwbGlMaXN0KCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsNCiAgICAgICAgY3JlYXRlQnk6IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkLA0KICAgICAgICBwYWdlTnVtOiB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0sDQogICAgICAgIHBhZ2VTaXplOiA2LA0KICAgICAgfTsNCiAgICAgIGFwcGxpTGlzdChwYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuYXBwbGljYXRpb25MaXN0ID0gcmVzLnJvd3Muc2xpY2UoMCwgNik7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0T3JkZXJMaXN0KCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsNCiAgICAgICAgY3JlYXRlQnk6IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkLA0KICAgICAgICBwYWdlTnVtOiB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0sDQogICAgICAgIHBhZ2VTaXplOiAzLA0KICAgICAgfTsNCiAgICAgIG9yZGVyTGlzdChwYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMub3JkZXJtYW5hZ2VMaXN0ID0gcmVzLnJvd3M7DQogICAgICAgICAgY29uc29sZS5sb2codGhpcy5vcmRlcm1hbmFnZUxpc3QsICItLS0tLS0tLS0tLS0tLS0tIik7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0QXBwT3JkZXJOdW0oKSB7DQogICAgICBhcHBPcmRlck51bSgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuYXBwT3JkZXJEYXRhID0gcmVzLmRhdGE7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0U3ViU3RhdGlzdGljcygpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgIHllYXI6IHRoaXMudmFsdWVZZWFyLA0KICAgICAgICBtb250aDogdGhpcy52YWx1ZU1vbnRoLA0KICAgICAgfTsNCiAgICAgIGNvbnNvbGUubG9nKHBhcmFtcywgIi0tLS0tLS0tLS0tLSIpOw0KICAgICAgc3ViU3RhdGlzdGljcyhwYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuc3ViU3RhdGlzdGljc0RhdGEgPSByZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLmdldEluaXRFY2hhcnRzKCk7DQogICAgICAgICAgY29uc29sZS5sb2cocmVzLCAiLS0tLS0tLS0tLS0tIik7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0SW1NZXNzYWdlKCkgew0KICAgICAgaWYgKHRoaXMudXNlci5pZCkgew0KICAgICAgICBnZXRVc2VySU1Ub2tlbih7IHVzZXJJZDogdGhpcy51c2VyLmlkIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGNvbnNvbGUubG9nKHJlcyk7DQogICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGEuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICB3aW5kb3cudG9rZW4gPSByZXMuZGF0YS50b2tlbjsNCiAgICAgICAgICAgIFJvbmdJTUxpYi5jb25uZWN0KHRva2VuKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgY29uc29sZS5pbmZvKCLov57mjqXnu5PmnpzmiZPljbAx77yaIiwgcmVzKTsNCiAgICAgICAgICAgICAgY29uc3Qgc3RhcnRUaW1lID0gMDsNCiAgICAgICAgICAgICAgY29uc3QgY291bnQgPSAxMDsNCiAgICAgICAgICAgICAgY29uc3Qgb3JkZXIgPSAwOw0KDQogICAgICAgICAgICAgIFJvbmdJTUxpYi5nZXRDb252ZXJzYXRpb25MaXN0KHsNCiAgICAgICAgICAgICAgICBjb3VudDogY291bnQsDQogICAgICAgICAgICAgICAgc3RhcnRUaW1lOiBzdGFydFRpbWUsDQogICAgICAgICAgICAgICAgb3JkZXI6IG9yZGVyLA0KICAgICAgICAgICAgICB9KS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDApIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMuY29udmVyc2F0aW9uTGlzdCA9IHJlcy5kYXRhOw0KICAgICAgICAgICAgICAgICAgbGV0IGlkcyA9IFtdOw0KICAgICAgICAgICAgICAgICAgdGhpcy5jb252ZXJzYXRpb25MaXN0LmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgICAgICAgICAgaWRzLnB1c2goaXRlbS50YXJnZXRJZCk7DQogICAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgICAgICAgIHRoaXMuZ2V0VXNlckxpc3RCeUlkcyhpZHMpOw0KICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMuY29kZSwgcmVzLm1zZyk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBnZXRVc2VyTGlzdEJ5SWRzKGlkcykgew0KICAgICAgZ2V0VXNlckxpc3RCeUlkcyhpZHMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgbGV0IGxpc3QgPSBbXTsNCiAgICAgICAgICB0aGlzLmNvbnZlcnNhdGlvbkxpc3QuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICAgICAgbGV0IGluZGV4ID0gcmVzLmRhdGEuZmluZEluZGV4KA0KICAgICAgICAgICAgICAoZWxlbWVudCkgPT4gZWxlbWVudC5pZCA9PSBpdGVtLnRhcmdldElkDQogICAgICAgICAgICApOw0KICAgICAgICAgICAgaWYgKGluZGV4ICE9IC0xKSB7DQogICAgICAgICAgICAgIHRoaXMuY29udmVyc2F0aW9uTGlzdFtpbmRleF0ucmVhbE5hbWUgPSByZXMuZGF0YVtpbmRleF0ucmVhbE5hbWU7DQogICAgICAgICAgICAgIHRoaXMuY29udmVyc2F0aW9uTGlzdFtpbmRleF0udXNlclBvcnRyYWl0ID0NCiAgICAgICAgICAgICAgICByZXMuZGF0YVtpbmRleF0udXNlclBvcnRyYWl0Ow0KICAgICAgICAgICAgICAvLyBsaXN0LnB1c2goeyAuLi5pdGVtLCAuLi5yZXMuZGF0YVtpbmRleF0gfSk7DQogICAgICAgICAgICB9DQogICAgICAgICAgfSk7DQogICAgICAgICAgLy8gdGhpcy5jb252ZXJzYXRpb25MaXN0ID0gbGlzdDsNCiAgICAgICAgICB0aGlzLmNvbnZlcnNhdGlvbkxpc3QgPSB0aGlzLnNsaWNlQXJyYXkodGhpcy5jb252ZXJzYXRpb25MaXN0LCAzKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRVc2VyKCkgew0KICAgICAgZ2V0VXNlckluZm8odGhpcy51c2VyLmlkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnVzZXIgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLnJvbGVHcm91cCA9IHJlc3BvbnNlLnJvbGVHcm91cDsNCiAgICAgICAgdGhpcy5wb3N0R3JvdXAgPSByZXNwb25zZS5wb3N0R3JvdXA7DQogICAgICB9KTsNCiAgICAgIGNoZWNrQXV0aFN0YXR1cygpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuY29tcGFueVN0YXR1cyA9IHJlc3BvbnNlLmRhdGEuY29tcGFueVN0YXR1czsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ29JTShpbmZvKSB7DQogICAgICBsZXQgcm91dGVEYXRhOw0KICAgICAgaWYgKGluZm8pIHsNCiAgICAgICAgcm91dGVEYXRhID0gdGhpcy4kcm91dGVyLnJlc29sdmUoew0KICAgICAgICAgIHBhdGg6ICIvdXNlci9pbSIsDQogICAgICAgICAgcXVlcnk6IHsNCiAgICAgICAgICAgIHVzZXJJZDogaW5mby50YXJnZXRJZCwNCiAgICAgICAgICB9LA0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHJvdXRlRGF0YSA9IHRoaXMuJHJvdXRlci5yZXNvbHZlKHsNCiAgICAgICAgICBwYXRoOiAiL3VzZXIvaW0iLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIHdpbmRvdy5vcGVuKHJvdXRlRGF0YS5ocmVmLCAiX2JsYW5rIik7DQogICAgfSwNCiAgICBnZXRSZXNwb25zZUxpc3QoKSB7DQogICAgICBsaXN0SW5mbyh7IHBhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMCB9KS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnJlc3BvbnNlTGlzdCA9IHRoaXMuc2xpY2VBcnJheShyZXNwb25zZS5yb3dzLCAyKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0QWJ1dG1lbnRMaXN0KCkgew0KICAgICAgZ2V0QWJ1dG1lbnRMaXN0KHsgcGFnZU51bTogMSwgcGFnZVNpemU6IDkgfSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy5hYnV0bWVudExpc3QgPSB0aGlzLnNsaWNlQXJyYXkocmVzcG9uc2Uucm93cywgMyk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldEFwcGx5U3RhdHVzTmFtZShrZXkpIHsNCiAgICAgIHN3aXRjaCAoa2V5KSB7DQogICAgICAgIGNhc2UgMToNCiAgICAgICAgICByZXR1cm4gIuWuoeaguOS4rSI7DQogICAgICAgIGNhc2UgMjoNCiAgICAgICAgICByZXR1cm4gIuWuoeaguOmAmui/hyI7DQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgIGNhc2UgMzoNCiAgICAgICAgICByZXR1cm4gIuWuoeaguOmps+WbniI7DQogICAgICB9DQogICAgfSwNCiAgICBqdW1wVG9BcHByb3ZlKCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goYC91c2VyL3VzZXJJbmZvP3JlbGV2YW5jZUNvbXBhbnk9MWApOw0KICAgIH0sDQogICAgZ2V0VXJsKHN0cikgew0KICAgICAgaWYgKCFzdHIpIHsNCiAgICAgICAgcmV0dXJuICIiOw0KICAgICAgfQ0KDQogICAgICB2YXIgbGlzdCA9IEpTT04ucGFyc2Uoc3RyKTsNCiAgICAgIGlmIChsaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgcmV0dXJuIGxpc3RbMF0udXJsOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBzbGljZUFycmF5KGFycmF5LCBudW1iZXIpIHsNCiAgICAgIHZhciByZXN1bHQgPSBbXTsNCiAgICAgIGZvciAodmFyIHggPSAwOyB4IDwgTWF0aC5jZWlsKGFycmF5Lmxlbmd0aCAvIG51bWJlcik7IHgrKykgew0KICAgICAgICB2YXIgc3RhcnQgPSB4ICogbnVtYmVyOw0KICAgICAgICB2YXIgZW5kID0gc3RhcnQgKyBudW1iZXI7DQogICAgICAgIHJlc3VsdC5wdXNoKGFycmF5LnNsaWNlKHN0YXJ0LCBlbmQpKTsNCiAgICAgIH0NCiAgICAgIGNvbnNvbGUubG9nKHJlc3VsdCk7DQogICAgICByZXR1cm4gcmVzdWx0Ow0KICAgIH0sDQogICAganVtcFRvRGVtYW5kKCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi91c2VyL2NvbXBhbnlEZW1hbmQiKTsNCiAgICB9LA0KICAgIGp1bXBUb0FwcGx5KCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi91c2VyL2NvbXBhbnlBcHBseSIpOw0KICAgIH0sDQogICAganVtcFRvTWVzc2FnZSgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCIvdXNlci9ub3RpY2UiKTsNCiAgICB9LA0KICAgIGp1bXBUb0FidXRtZW50KCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi91c2VyL2FidXRtZW50UmVjb3JkIik7DQogICAgfSwNCiAgICBnZXRJbml0RWNoYXJ0cygpIHsNCiAgICAgIC8vIOWfuuS6juWHhuWkh+WlveeahGRvbe+8jOWIneWni+WMlmVjaGFydHPlrp7kvosNCiAgICAgIHZhciBteUNoYXJ0ID0gZWNoYXJ0cy5pbml0KGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCJkYXRhUGFuZWwiKSk7DQogICAgICAvLyDnu5jliLblm77ooagNCiAgICAgIG15Q2hhcnQuc2V0T3B0aW9uKHsNCiAgICAgICAgdG9vbHRpcDogew0KICAgICAgICAgIHRyaWdnZXI6ICJpdGVtIiwNCiAgICAgICAgfSwNCiAgICAgICAgbGVnZW5kOiB7DQogICAgICAgICAgb3JpZW50OiAidmVydGljYWwiLA0KICAgICAgICAgIHJpZ2h0OiAwLA0KICAgICAgICAgIHRvcDogNDAsDQogICAgICAgICAgLy8gdG9wOiAiNSUiLA0KICAgICAgICAgIC8vIGxlZnQ6ICJjZW50ZXIiLA0KICAgICAgICB9LA0KICAgICAgICBzZXJpZXM6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICB0eXBlOiAicGllIiwNCiAgICAgICAgICAgIHJhZGl1czogWyI0MCUiLCAiNzAlIl0sDQogICAgICAgICAgICBhdm9pZExhYmVsT3ZlcmxhcDogZmFsc2UsDQogICAgICAgICAgICBjZW50ZXI6IFsiMzAlIiwgIjUwJSJdLA0KICAgICAgICAgICAgbGFiZWw6IHsNCiAgICAgICAgICAgICAgc2hvdzogZmFsc2UsDQogICAgICAgICAgICAgIHBvc2l0aW9uOiAiY2VudGVyIiwNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBsYWJlbExpbmU6IHsNCiAgICAgICAgICAgICAgc2hvdzogZmFsc2UsDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgZGF0YTogdGhpcy5zdWJTdGF0aXN0aWNzRGF0YSwNCiAgICAgICAgICB9LA0KICAgICAgICBdLA0KICAgICAgfSk7DQogICAgfSwNCiAgICB2aWV3TWVzc2FnZSgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgcGF0aDogIi91c2VyL25vdGljZSIsDQogICAgICB9KTsNCiAgICB9LA0KICAgIHZpZXdTdWJzY3JpYmUoKSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgIHBhdGg6ICIvdXNlci9teVN1YnNjcmlwdGlvbnMiLA0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRTdGF0dXMoc3RhdHVzKSB7DQogICAgICBsZXQgb3JkZXJTdGF0dXM7DQogICAgICB0aGlzLm9yZGVyU3RhdHVzTGlzdC5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGlmIChpdGVtLmRpY3RWYWx1ZSA9PSBzdGF0dXMpIHsNCiAgICAgICAgICBvcmRlclN0YXR1cyA9IGl0ZW0uZGljdExhYmVsOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIHJldHVybiBvcmRlclN0YXR1czsNCiAgICB9LA0KICAgIGdldFN1YkRhdGEoKSB7DQogICAgICB0aGlzLmdldFN1YlN0YXRpc3RpY3MoKTsNCiAgICB9LA0KICAgIHRpbWVFZGl0YWJsZSgpIHsNCiAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgbGV0IGVscyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoDQogICAgICAgICAgIi5lbC1kYXRlLXBpY2tlcl9faGVhZGVyLS1ib3JkZXJlZCINCiAgICAgICAgKTsNCiAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPD0gZWxzLmxlbmd0aCAtIDE7IGkrKykgew0KICAgICAgICAgIGVsc1sxXS5zdHlsZS5kaXNwbGF5ID0gIm5vbmUiOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index(copy).vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+YA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index(copy).vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <div class=\"user-info-card\">\r\n              <div style=\"display: flex; align-items: center\">\r\n                <el-image\r\n                  style=\"width: 18px; height: 18px\"\r\n                  :src=\"require('@/assets/user/infoIcon.png')\"\r\n                ></el-image>\r\n                <div style=\"margin-left: 10px\">我的信息</div>\r\n              </div>\r\n              <div style=\"display: flex; margin-top: 34px\">\r\n                <el-image\r\n                  style=\"width: 100px; height: 100px; border-radius: 50%\"\r\n                  :fit=\"fit\"\r\n                  :src=\"user.avatar\"\r\n                ></el-image>\r\n                <div class=\"left-info-box\">\r\n                  <div class=\"user-name\">{{ user.realName || \"--\" }}</div>\r\n                  <div class=\"tag-group\">\r\n                    <div\r\n                      v-if=\"this.companyStatus == '0'\"\r\n                      class=\"label-container\"\r\n                    >\r\n                      <el-image\r\n                        style=\"width: 12px; height: 12px\"\r\n                        :src=\"require('@/assets/user/authentication.png')\"\r\n                      ></el-image>\r\n                      <span>未认证</span>\r\n                    </div>\r\n                    <div v-else class=\"certification\">服务商认证</div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"divider\"></div>\r\n                <div class=\"right-info-box\">\r\n                  <div class=\"phone-class\">\r\n                    <el-image\r\n                      class=\"iamge\"\r\n                      style=\"width: 14px; height: 20px; margin-left: 4px\"\r\n                      :src=\"require('@/assets/user/icon_phone.png')\"\r\n                    ></el-image>\r\n                    <div class=\"phone-number\">{{ user.phonenumber }}</div>\r\n                  </div>\r\n                  <div class=\"phone-class\">\r\n                    <el-image\r\n                      class=\"iamge\"\r\n                      style=\"width: 22px; height: 20px\"\r\n                      :src=\"require('@/assets/user/icon_company.png')\"\r\n                    ></el-image>\r\n                    <div class=\"phone-number\" v-if=\"user.bussinessNo\">\r\n                      {{ user.companyName }}\r\n                    </div>\r\n                    <div class=\"phone-number grey\" v-else>\r\n                      您还未关联企业，<a\r\n                        @click=\"jumpToApprove\"\r\n                        style=\"color: #21c9b8\"\r\n                        >请先关联</a\r\n                      >\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"notify-card\">\r\n              <div style=\"display: flex; align-items: center\">\r\n                <el-image\r\n                  style=\"width: 14px; height: 20px\"\r\n                  :src=\"require('@/assets/user/notifyIcon.png')\"\r\n                ></el-image>\r\n                <div style=\"margin-left: 10px\">消息通知</div>\r\n                <div class=\"viewStyle\" @click=\"viewMessage\">查看更多>></div>\r\n              </div>\r\n              <div class=\"driver\"></div>\r\n              <div class=\"notify_content\">\r\n                <div\r\n                  class=\"everyItem\"\r\n                  v-for=\"item in notifyList\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div class=\"item_icon\">\r\n                    <div class=\"icon_small\"></div>\r\n                  </div>\r\n                  <div class=\"desc\">\r\n                    {{ item.remark || item.describeInfo || \"--\" }}\r\n                  </div>\r\n                  <div class=\"item_time\">\r\n                    {{ item.createTime.slice(0, 10) }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <div class=\"subscribe-card\">\r\n              <div style=\"display: flex; align-items: center\">\r\n                <el-image\r\n                  style=\"width: 14px; height: 20px\"\r\n                  :src=\"require('@/assets/user/notifyIcon.png')\"\r\n                ></el-image>\r\n                <div style=\"margin-left: 10px\">我的订阅</div>\r\n                <div class=\"viewStyle\" @click=\"viewSubscribe\">查看更多>></div>\r\n              </div>\r\n              <div class=\"subscribe_content\">\r\n                <div\r\n                  class=\"everyItem\"\r\n                  v-for=\"item in subscribeList\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div style=\"display: flex\">\r\n                    <div class=\"item_img\">\r\n                      <img :src=\"item.appLogo\" alt=\"\" />\r\n                      <!-- <img :src=\"require('@/assets/user/wait.png')\" alt=\"\" /> -->\r\n                    </div>\r\n                    <div class=\"item_desc\">\r\n                      <div class=\"title\">{{ item.remark }}</div>\r\n                      <div style=\"font-size: 14px; margin-top: 10px\">\r\n                        <span style=\"color: #999999\">规格:</span>\r\n                        <span style=\"margin-left: 5px\">{{\r\n                          item.specs == \"1\" ? \"基础版\" : \"高级版\"\r\n                        }}</span>\r\n                      </div>\r\n                      <div style=\"font-size: 14px; margin-top: 10px\">\r\n                        <span style=\"color: #999999\">可用时长:</span>\r\n                        <span style=\"margin-left: 5px\">{{\r\n                          item.validTime == \"1\" ? \"一年\" : \"永久\"\r\n                        }}</span>\r\n                      </div>\r\n                      <div style=\"font-size: 14px; margin-top: 10px\">\r\n                        <span style=\"color: #999999\">可用人数:</span>\r\n                        <span style=\"margin-left: 5px\">不限</span>\r\n                        <!-- <span style=\"margin-left: 5px\">{{ item.number }}</span> -->\r\n                      </div>\r\n                    </div>\r\n                    <div class=\"item_amounts\">\r\n                      <div\r\n                        style=\"\r\n                          color: #999999;\r\n                          font-size: 14px;\r\n                          margin-top: 34px;\r\n                        \"\r\n                      >\r\n                        订单金额\r\n                      </div>\r\n                      <div\r\n                        style=\"\r\n                          margin-top: 21px;\r\n                          color: #e10c02;\r\n                          font-weight: 400;\r\n                        \"\r\n                      >\r\n                        ￥{{ item.price }}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"driver\" v-show=\"item.id == 1\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"ordermanage-card\">\r\n              <div style=\"display: flex; align-items: center\">\r\n                <el-image\r\n                  style=\"width: 16px; height: 19px\"\r\n                  :src=\"require('@/assets/user/order.png')\"\r\n                ></el-image>\r\n                <div style=\"margin-left: 10px\">订单管理</div>\r\n                <div class=\"viewStyle\">查看更多>></div>\r\n              </div>\r\n              <div class=\"ordermanage_content\">\r\n                <div\r\n                  class=\"everyItem\"\r\n                  v-for=\"item in ordermanageList\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div class=\"item_company\">\r\n                    {{ item.supply }}\r\n                  </div>\r\n                  <div class=\"item_content\">\r\n                    <div class=\"item_img\">\r\n                      <img :src=\"item.appLogo\" alt=\"\" />\r\n                      <!-- <img :src=\"require('@/assets/user/wait.png')\" alt=\"\" /> -->\r\n                    </div>\r\n                    <div class=\"item_middle\">\r\n                      <div class=\"title\">{{ item.appName }}</div>\r\n                      <!-- <div class=\"desc\">{{ item.desc }}</div> -->\r\n                    </div>\r\n                    <div class=\"item_right\">\r\n                      <div\r\n                        style=\"\r\n                          font-size: 14px;\r\n                          font-family: Microsoft YaHei;\r\n                          font-weight: 400;\r\n                          color: #666666;\r\n                          margin: 10px 0;\r\n                        \"\r\n                      >\r\n                        ￥{{ item.price }}\r\n                      </div>\r\n                      <div\r\n                        class=\"payType\"\r\n                        :style=\"\r\n                          item.status == '待支付'\r\n                            ? 'color:#E10C02'\r\n                            : item.status == '待发货'\r\n                            ? 'color:#FBAC14'\r\n                            : 'color:#19C582'\r\n                        \"\r\n                      >\r\n                        {{ getStatus(item.orderStatus) }}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <div class=\"dataPanel-card\">\r\n              <div style=\"display: flex; align-items: center\">\r\n                <el-image\r\n                  style=\"width: 18px; height: 20px\"\r\n                  :src=\"require('@/assets/user/dataPanel.png')\"\r\n                ></el-image>\r\n                <div style=\"margin-left: 10px\">数据面板</div>\r\n              </div>\r\n              <div class=\"dataPanel_content\">\r\n                <div class=\"dataPanel_left\">\r\n                  <div class=\"dataPanelItem\">\r\n                    <div class=\"itemType\">应用</div>\r\n                    <div class=\"typeNum\">{{ appOrderData.appNumber }}</div>\r\n                    <div class=\"addStyle\">\r\n                      <div class=\"addText\">今日新增</div>\r\n                      <div style=\"color: #f46768; margin-left: 10%\">\r\n                        {{ appOrderData.todayAppNumber }}\r\n                      </div>\r\n                      <div style=\"margin-left: 10%\">\r\n                        <img\r\n                          :src=\"require('@/assets/user/add.png')\"\r\n                          alt=\"\"\r\n                          style=\"width: 12px; height: 16px\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"dataPanelItem\">\r\n                    <div class=\"itemType\">订单</div>\r\n                    <div class=\"typeNum\">{{ appOrderData.orderNumber }}</div>\r\n                    <div class=\"addStyle\">\r\n                      <div class=\"addText\">今日新增</div>\r\n                      <div style=\"color: #f46768; margin-left: 10%\">\r\n                        {{ appOrderData.todayOrderNumber }}\r\n                      </div>\r\n                      <div style=\"margin-left: 10%\">\r\n                        <img\r\n                          :src=\"require('@/assets/user/add.png')\"\r\n                          alt=\"\"\r\n                          style=\"width: 12px; height: 16px\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"dataPanelItem\">\r\n                    <div class=\"itemType\">开票</div>\r\n                    <div class=\"typeNum\">{{ appOrderData.invoiceNumber }}</div>\r\n                    <div class=\"addStyle\">\r\n                      <div class=\"addText\">今日新增</div>\r\n                      <div style=\"color: #f46768; margin-left: 10%\">\r\n                        {{ appOrderData.todayInvoiceNumber }}\r\n                      </div>\r\n                      <div style=\"margin-left: 10%\">\r\n                        <img\r\n                          :src=\"require('@/assets/user/add.png')\"\r\n                          alt=\"\"\r\n                          style=\"width: 12px; height: 16px\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"dataPanel_right\">\r\n                  <div style=\"display: flex; margin-top: 18px\">\r\n                    <div>\r\n                      年份:\r\n                      <el-date-picker\r\n                        v-model=\"valueYear\"\r\n                        type=\"year\"\r\n                        placeholder=\"选择年\"\r\n                        style=\"width: 70%\"\r\n                        format=\"yyyy\"\r\n                        value-format=\"yyyy\"\r\n                        @change=\"getSubData\"\r\n                      >\r\n                      </el-date-picker>\r\n                    </div>\r\n                    <div>\r\n                      月份:\r\n                      <el-date-picker\r\n                        v-model=\"valueMonth\"\r\n                        type=\"month\"\r\n                        placeholder=\"选择月\"\r\n                        style=\"width: 70%\"\r\n                        format=\"MM\"\r\n                        value-format=\"MM\"\r\n                        @change=\"getSubData\"\r\n                        @focus=\"timeEditable\"\r\n                      >\r\n                      </el-date-picker>\r\n                    </div>\r\n                  </div>\r\n                  <div id=\"dataPanel\" style=\"width: 100%; height: 200px\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"application-card\">\r\n              <div style=\"display: flex; align-items: center\">\r\n                <el-image\r\n                  style=\"width: 16px; height: 19px\"\r\n                  :src=\"require('@/assets/user/application.png')\"\r\n                ></el-image>\r\n                <div style=\"margin-left: 10px\">应用管理</div>\r\n                <div class=\"viewStyle\">查看更多>></div>\r\n              </div>\r\n              <div class=\"application-content\">\r\n                <div\r\n                  class=\"everyItem\"\r\n                  v-for=\"item in applicationList\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div class=\"item_img\">\r\n                    <!-- <img :src=\"require('@/assets/user/wait.png')\" alt=\"\" /> -->\r\n                    <img :src=\"item.appLogo\" alt=\"\" />\r\n                  </div>\r\n                  <div class=\"title\">{{ item.appName }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n      <!-- <el-col :span=\"3\" :xs=\"24\">\r\n        <div class=\"code-box\">\r\n          <div class=\"code-item\">\r\n            <el-image\r\n              style=\"width: 100px; height: 100px\"\r\n              :src=\"require('@/assets/user/more.png')\"\r\n              :fit=\"fit\"\r\n            ></el-image>\r\n            <div class=\"title\">门户端</div>\r\n            <div class=\"hint\">政策服务/申报/数据掌握</div>\r\n          </div>\r\n          <div class=\"code-item\">\r\n            <el-image\r\n              style=\"width: 100px; height: 100px\"\r\n              :src=\"require('@/assets/user/company_mini.png')\"\r\n              :fit=\"fit\"\r\n            ></el-image>\r\n            <div class=\"title\">企业端-云端研发</div>\r\n            <div class=\"hint\">研发/采购/销售/政策/服务</div>\r\n          </div>\r\n          <div class=\"code-item\">\r\n            <el-image\r\n              style=\"width: 100px; height: 100px\"\r\n              :src=\"require('@/assets/user/resource_mini.png')\"\r\n              :fit=\"fit\"\r\n            ></el-image>\r\n            <div class=\"title\">资源端</div>\r\n            <div class=\"hint\">供应商/服务商/专家</div>\r\n          </div>\r\n          <div class=\"code-item\">\r\n            <el-image\r\n              style=\"width: 100px; height: 100px\"\r\n              :src=\"require('@/assets/user/gov_mini.png')\"\r\n              :fit=\"fit\"\r\n            ></el-image>\r\n            <div class=\"title\">政府端</div>\r\n            <div class=\"hint\">政策服务/申报/数据掌握</div>\r\n          </div>\r\n        </div>\r\n      </el-col> -->\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport store from \"@/store\";\r\nimport userAvatar from \"./userAvatar\";\r\nimport userInfo from \"./userInfo\";\r\nimport resetPwd from \"./resetPwd\";\r\n// import { listInfo } from \"@/api/system/info\";\r\nimport { getAbutmentList } from \"@/api/system/abutment\";\r\n\r\nimport {\r\n  getUserInfo,\r\n  checkAuthStatus,\r\n  sublist,\r\n  appOrderNum,\r\n  subStatistics,\r\n} from \"@/api/system/user\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport * as RongIMLib from \"@rongcloud/imlib-next\";\r\nimport { getUserIMToken } from \"@/api/system/user\";\r\nimport { getUserListByIds } from \"@/api/im.js\";\r\nimport * as echarts from \"echarts\";\r\nimport { listInfo } from \"@/api/system/info\";\r\nimport { appliList } from \"@/api/appliMarket\";\r\nimport { orderList } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"Profile\",\r\n  components: { userAvatar, userInfo, resetPwd, UserMenu },\r\n  data() {\r\n    return {\r\n      user: {\r\n        name: store.getters.name,\r\n        avatar: store.getters.avatar,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        id: store.getters.userId,\r\n      },\r\n      roleGroup: {},\r\n      userInfo: {},\r\n      postGroup: {},\r\n      fit: \"cover\",\r\n      responseList: [],\r\n      responseList: [],\r\n      abutmentList: [],\r\n      conversationList: [],\r\n      companyStatus: \"\",\r\n      activeTab: \"userinfo\",\r\n      url: \"https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg\",\r\n      notifyList: [],\r\n      subscribeList: [],\r\n      ordermanageList: [],\r\n      valueYear: \"\",\r\n      valueMonth: \"\",\r\n      applicationList: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      orderStatusList: [\r\n        {\r\n          dictValue: 1,\r\n          dictLabel: \"待支付\",\r\n        },\r\n        {\r\n          dictValue: 2,\r\n          dictLabel: \"待发货\",\r\n        },\r\n        {\r\n          dictValue: 3,\r\n          dictLabel: \"支付失败\",\r\n        },\r\n        {\r\n          dictValue: 4,\r\n          dictLabel: \"已发货\",\r\n        },\r\n        {\r\n          dictValue: 5,\r\n          dictLabel: \"已成交\",\r\n        },\r\n        {\r\n          dictValue: 6,\r\n          dictLabel: \"待续费\",\r\n        },\r\n        {\r\n          dictValue: 7,\r\n          dictLabel: \"已关闭\",\r\n        },\r\n        {\r\n          dictValue: 8,\r\n          dictLabel: \"支付中\",\r\n        },\r\n        {\r\n          dictValue: 9,\r\n          dictLabel: \"已取消\",\r\n        },\r\n      ],\r\n      appOrderData: {},\r\n      subStatisticsData: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getMessage();\r\n    this.getSublist();\r\n    this.getAppliList();\r\n    this.getOrderList();\r\n    this.getAppOrderNum();\r\n    this.getSubStatistics();\r\n    this.getUser();\r\n    // this.getResponseList();\r\n    // this.getAbutmentList();\r\n    // this.getImMessage();\r\n  },\r\n  // mounted() {\r\n  //   this.getInitEcharts();\r\n  // },\r\n  methods: {\r\n    getMessage() {\r\n      let data = {\r\n        pageNum: 1,\r\n        pageSize: 4,\r\n        type: 2,\r\n      };\r\n      listInfo(data).then((response) => {\r\n        this.notifyList = response.rows.slice(0, 4);\r\n        console.log(this.notifyList, \"----------\");\r\n      });\r\n    },\r\n    getSublist() {\r\n      sublist().then((res) => {\r\n        if (res.code === 200) {\r\n          this.subscribeList = res.rows.slice(0, 2);\r\n        }\r\n      });\r\n    },\r\n    getAppliList() {\r\n      let params = {\r\n        createBy: this.$store.state.user.userId,\r\n        pageNum: this.queryParams.pageNum,\r\n        pageSize: 6,\r\n      };\r\n      appliList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.applicationList = res.rows.slice(0, 6);\r\n        }\r\n      });\r\n    },\r\n    getOrderList() {\r\n      let params = {\r\n        createBy: this.$store.state.user.userId,\r\n        pageNum: this.queryParams.pageNum,\r\n        pageSize: 3,\r\n      };\r\n      orderList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.ordermanageList = res.rows;\r\n          console.log(this.ordermanageList, \"----------------\");\r\n        }\r\n      });\r\n    },\r\n    getAppOrderNum() {\r\n      appOrderNum().then((res) => {\r\n        if (res.code === 200) {\r\n          this.appOrderData = res.data;\r\n        }\r\n      });\r\n    },\r\n    getSubStatistics() {\r\n      let params = {\r\n        year: this.valueYear,\r\n        month: this.valueMonth,\r\n      };\r\n      console.log(params, \"------------\");\r\n      subStatistics(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.subStatisticsData = res.data;\r\n          this.getInitEcharts();\r\n          console.log(res, \"------------\");\r\n        }\r\n      });\r\n    },\r\n    getImMessage() {\r\n      if (this.user.id) {\r\n        getUserIMToken({ userId: this.user.id }).then((res) => {\r\n          console.log(res);\r\n          if (res.code === 200 && res.data.code === 200) {\r\n            window.token = res.data.token;\r\n            RongIMLib.connect(token).then((res) => {\r\n              console.info(\"连接结果打印1：\", res);\r\n              const startTime = 0;\r\n              const count = 10;\r\n              const order = 0;\r\n\r\n              RongIMLib.getConversationList({\r\n                count: count,\r\n                startTime: startTime,\r\n                order: order,\r\n              }).then((res) => {\r\n                if (res.code === 0) {\r\n                  this.conversationList = res.data;\r\n                  let ids = [];\r\n                  this.conversationList.forEach((item) => {\r\n                    ids.push(item.targetId);\r\n                  });\r\n                  this.getUserListByIds(ids);\r\n                } else {\r\n                  console.log(res.code, res.msg);\r\n                }\r\n              });\r\n            });\r\n          }\r\n        });\r\n      }\r\n    },\r\n    getUserListByIds(ids) {\r\n      getUserListByIds(ids).then((res) => {\r\n        if (res.code == 200) {\r\n          let list = [];\r\n          this.conversationList.forEach((item) => {\r\n            let index = res.data.findIndex(\r\n              (element) => element.id == item.targetId\r\n            );\r\n            if (index != -1) {\r\n              this.conversationList[index].realName = res.data[index].realName;\r\n              this.conversationList[index].userPortrait =\r\n                res.data[index].userPortrait;\r\n              // list.push({ ...item, ...res.data[index] });\r\n            }\r\n          });\r\n          // this.conversationList = list;\r\n          this.conversationList = this.sliceArray(this.conversationList, 3);\r\n        }\r\n      });\r\n    },\r\n    getUser() {\r\n      getUserInfo(this.user.id).then((response) => {\r\n        this.user = response.data;\r\n        this.roleGroup = response.roleGroup;\r\n        this.postGroup = response.postGroup;\r\n      });\r\n      checkAuthStatus().then((response) => {\r\n        this.companyStatus = response.data.companyStatus;\r\n      });\r\n    },\r\n    goIM(info) {\r\n      let routeData;\r\n      if (info) {\r\n        routeData = this.$router.resolve({\r\n          path: \"/user/im\",\r\n          query: {\r\n            userId: info.targetId,\r\n          },\r\n        });\r\n      } else {\r\n        routeData = this.$router.resolve({\r\n          path: \"/user/im\",\r\n        });\r\n      }\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    getResponseList() {\r\n      listInfo({ pageNum: 1, pageSize: 10 }).then((response) => {\r\n        this.responseList = this.sliceArray(response.rows, 2);\r\n      });\r\n    },\r\n    getAbutmentList() {\r\n      getAbutmentList({ pageNum: 1, pageSize: 9 }).then((response) => {\r\n        this.abutmentList = this.sliceArray(response.rows, 3);\r\n      });\r\n    },\r\n    getApplyStatusName(key) {\r\n      switch (key) {\r\n        case 1:\r\n          return \"审核中\";\r\n        case 2:\r\n          return \"审核通过\";\r\n        default:\r\n        case 3:\r\n          return \"审核驳回\";\r\n      }\r\n    },\r\n    jumpToApprove() {\r\n      this.$router.push(`/user/userInfo?relevanceCompany=1`);\r\n    },\r\n    getUrl(str) {\r\n      if (!str) {\r\n        return \"\";\r\n      }\r\n\r\n      var list = JSON.parse(str);\r\n      if (list.length > 0) {\r\n        return list[0].url;\r\n      }\r\n    },\r\n\r\n    sliceArray(array, number) {\r\n      var result = [];\r\n      for (var x = 0; x < Math.ceil(array.length / number); x++) {\r\n        var start = x * number;\r\n        var end = start + number;\r\n        result.push(array.slice(start, end));\r\n      }\r\n      console.log(result);\r\n      return result;\r\n    },\r\n    jumpToDemand() {\r\n      this.$router.push(\"/user/companyDemand\");\r\n    },\r\n    jumpToApply() {\r\n      this.$router.push(\"/user/companyApply\");\r\n    },\r\n    jumpToMessage() {\r\n      this.$router.push(\"/user/notice\");\r\n    },\r\n    jumpToAbutment() {\r\n      this.$router.push(\"/user/abutmentRecord\");\r\n    },\r\n    getInitEcharts() {\r\n      // 基于准备好的dom，初始化echarts实例\r\n      var myChart = echarts.init(document.getElementById(\"dataPanel\"));\r\n      // 绘制图表\r\n      myChart.setOption({\r\n        tooltip: {\r\n          trigger: \"item\",\r\n        },\r\n        legend: {\r\n          orient: \"vertical\",\r\n          right: 0,\r\n          top: 40,\r\n          // top: \"5%\",\r\n          // left: \"center\",\r\n        },\r\n        series: [\r\n          {\r\n            type: \"pie\",\r\n            radius: [\"40%\", \"70%\"],\r\n            avoidLabelOverlap: false,\r\n            center: [\"30%\", \"50%\"],\r\n            label: {\r\n              show: false,\r\n              position: \"center\",\r\n            },\r\n            labelLine: {\r\n              show: false,\r\n            },\r\n            data: this.subStatisticsData,\r\n          },\r\n        ],\r\n      });\r\n    },\r\n    viewMessage() {\r\n      this.$router.push({\r\n        path: \"/user/notice\",\r\n      });\r\n    },\r\n    viewSubscribe() {\r\n      this.$router.push({\r\n        path: \"/user/mySubscriptions\",\r\n      });\r\n    },\r\n    getStatus(status) {\r\n      let orderStatus;\r\n      this.orderStatusList.forEach((item) => {\r\n        if (item.dictValue == status) {\r\n          orderStatus = item.dictLabel;\r\n        }\r\n      });\r\n      return orderStatus;\r\n    },\r\n    getSubData() {\r\n      this.getSubStatistics();\r\n    },\r\n    timeEditable() {\r\n      this.$nextTick(() => {\r\n        let els = document.querySelectorAll(\r\n          \".el-date-picker__header--bordered\"\r\n        );\r\n        for (var i = 0; i <= els.length - 1; i++) {\r\n          els[1].style.display = \"none\";\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .user-type-card {\r\n    background: rgba(33, 77, 197, 0.1);\r\n    margin-left: 20px;\r\n    margin-top: 14px;\r\n    color: #214dc5;\r\n    font-size: 12px;\r\n    border-radius: 4px;\r\n    font-weight: 400;\r\n    padding: 4px 12px 4px 12px;\r\n  }\r\n  .user-info-card {\r\n    background-color: #fff;\r\n    padding: 30px;\r\n    height: 240px;\r\n    border-radius: 4px;\r\n    .left-info-box {\r\n      width: 30%;\r\n      text-align: center;\r\n      .user-name {\r\n        font-size: 28px;\r\n        font-weight: 500;\r\n        // margin-left: 20px;\r\n        color: #333333;\r\n        line-height: 50px;\r\n      }\r\n      .tag-group {\r\n        display: flex;\r\n        justify-content: center;\r\n        .certification {\r\n          width: 110px;\r\n          height: 40px;\r\n          line-height: 40px;\r\n          background: rgb(229, 247, 243);\r\n          border-radius: 4px;\r\n          font-size: 16px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #21c9b8;\r\n        }\r\n        .label-container {\r\n          padding: 4px 6px;\r\n          margin-top: 6px;\r\n          margin-right: 6px;\r\n          height: 24px;\r\n          background: #f0f1f4;\r\n          border-radius: 4px;\r\n          font-size: 12px;\r\n          font-weight: 500;\r\n          color: #8a8c94;\r\n          line-height: 12px;\r\n          .el-image {\r\n            margin: 2px 4px 0 0;\r\n          }\r\n        }\r\n        .orange {\r\n          background: rgba(255, 191, 69, 0.2);\r\n          color: #ff8a27;\r\n        }\r\n        .red {\r\n          background: rgba(197, 37, 33, 0.1);\r\n          color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n    .divider {\r\n      width: 1px;\r\n      height: 80px;\r\n      margin: auto 0;\r\n      background: #e8e8e8;\r\n    }\r\n    .right-info-box {\r\n      width: 50%;\r\n      padding-left: 40px;\r\n      padding-top: 12px;\r\n      height: 100px;\r\n      .phone-class {\r\n        display: flex;\r\n        margin-top: 14px;\r\n      }\r\n      .phone-number {\r\n        margin-left: 12px;\r\n      }\r\n      .grey {\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n  .notify-card {\r\n    background-color: #fff;\r\n    padding: 30px;\r\n    height: 240px;\r\n    border-radius: 4px;\r\n    .driver {\r\n      width: 65%;\r\n      height: 1px;\r\n      background: #f7f8f9;\r\n      margin-top: 15px;\r\n    }\r\n    .notify_content {\r\n      margin-top: 15px;\r\n      .everyItem {\r\n        display: flex;\r\n        align-items: center;\r\n        margin-top: 22px;\r\n        .item_icon {\r\n          width: 10px;\r\n          height: 10px;\r\n          border: 1px solid #21c9b8;\r\n          border-radius: 50%;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          .icon_small {\r\n            width: 4px;\r\n            height: 4px;\r\n            background: #21c9b8;\r\n            border-radius: 50%;\r\n          }\r\n        }\r\n        .desc {\r\n          width: 80%;\r\n          overflow: hidden;\r\n          font-size: 14px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          margin-left: 10px;\r\n        }\r\n        .item_time {\r\n          width: 20%;\r\n          text-align: right;\r\n          font-size: 14px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          color: #999999;\r\n        }\r\n      }\r\n      .everyItem:nth-child(1) {\r\n        margin-top: 0;\r\n      }\r\n    }\r\n  }\r\n  .subscribe-card {\r\n    background-color: #fff;\r\n    padding: 30px;\r\n    height: 410px;\r\n    margin-top: 21px;\r\n    border-radius: 4px;\r\n    .subscribe_content {\r\n      margin-top: 20px;\r\n      .everyItem {\r\n        width: 100%;\r\n        margin-top: 30px;\r\n        .item_img {\r\n          width: 17%;\r\n          height: 120px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .item_desc {\r\n          margin-left: 20px;\r\n          width: 58%;\r\n          .title {\r\n            font-size: 16px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            margin-top: 11px;\r\n          }\r\n        }\r\n        .item_amounts {\r\n          width: 20%;\r\n          text-align: center;\r\n        }\r\n      }\r\n      .everyItem:nth-child(1) {\r\n        margin-top: 0;\r\n      }\r\n      .driver {\r\n        width: 100%;\r\n        height: 1px;\r\n        background: #f7f8f9;\r\n        margin: 30px 0;\r\n      }\r\n    }\r\n  }\r\n  .ordermanage-card {\r\n    background-color: #fff;\r\n    padding: 30px;\r\n    height: 410px;\r\n    margin-top: 21px;\r\n    border-radius: 4px;\r\n    .ordermanage_content {\r\n      .everyItem {\r\n        width: 100%;\r\n        border-bottom: 1px solid #f7f8f9;\r\n        padding: 10px 0;\r\n        .item_company {\r\n          font-size: 14px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          color: #666666;\r\n        }\r\n        .item_company:nth-child(1) {\r\n          margin-top: 0;\r\n        }\r\n        .item_content {\r\n          display: flex;\r\n          margin-top: 10px;\r\n          .item_img {\r\n            width: 11%;\r\n            height: 70px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          .item_middle {\r\n            width: 70%;\r\n            margin-left: 20px;\r\n            .title {\r\n              font-size: 16px;\r\n              font-family: Source Han Sans CN;\r\n              font-weight: 500;\r\n              color: #333333;\r\n              margin: 10px 0;\r\n            }\r\n            .desc {\r\n              font-size: 14px;\r\n              font-family: Microsoft YaHei;\r\n              font-weight: 400;\r\n              color: #999999;\r\n            }\r\n          }\r\n          .item_right {\r\n            width: 30%;\r\n            text-align: right;\r\n            .payType {\r\n              font-size: 14px;\r\n              font-family: Microsoft YaHei;\r\n              font-weight: 400;\r\n              color: #e10c02;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .everyItem:nth-child(3) {\r\n        border-bottom: none;\r\n      }\r\n    }\r\n  }\r\n  .dataPanel-card {\r\n    height: 290px;\r\n    padding: 30px;\r\n    margin-top: 21px;\r\n    background: #ffffff;\r\n    border-radius: 4px;\r\n    .dataPanel_content {\r\n      width: 100%;\r\n      display: flex;\r\n      .dataPanel_left {\r\n        width: 45%;\r\n        .dataPanelItem {\r\n          width: 100%;\r\n          height: 56px;\r\n          background: rgb(245, 252, 250);\r\n          line-height: 56px;\r\n          display: flex;\r\n          margin-top: 18px;\r\n          .itemType {\r\n            font-size: 16px;\r\n            font-family: Microsoft YaHei;\r\n            font-weight: 400;\r\n            color: #333333;\r\n            margin-left: 5%;\r\n          }\r\n          .typeNum {\r\n            font-size: 30px;\r\n            font-family: SimHei;\r\n            font-weight: 400;\r\n            color: #21c9b8;\r\n            margin-left: 9%;\r\n          }\r\n          .addStyle {\r\n            margin-left: 9%;\r\n            display: flex;\r\n            width: 45%;\r\n            .addText {\r\n              font-size: 16px;\r\n              font-family: Microsoft YaHei;\r\n              font-weight: 400;\r\n              color: #999999;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .dataPanel_right {\r\n        width: 55%;\r\n        padding-left: 10px;\r\n      }\r\n    }\r\n  }\r\n  .application-card {\r\n    height: 290px;\r\n    padding: 30px;\r\n    margin-top: 21px;\r\n    background: #ffffff;\r\n    border-radius: 4px;\r\n    .application-content {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      margin-top: 20px;\r\n      .everyItem {\r\n        display: flex;\r\n        width: 32%;\r\n        margin-left: 2%;\r\n        .item_img {\r\n          width: 48%;\r\n          height: 95px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .title {\r\n          font-size: 16px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          margin-left: 9%;\r\n          margin-top: 27px;\r\n        }\r\n      }\r\n      .everyItem:nth-child(n + 4) {\r\n        margin-top: 20px;\r\n      }\r\n      .everyItem:nth-child(3n + 1) {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n  .viewStyle {\r\n    width: calc(100% - 90px);\r\n    text-align: right;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    cursor: pointer;\r\n  }\r\n  .demand-card {\r\n    margin-top: 18px;\r\n    background-color: #fff;\r\n    height: 340px;\r\n    z-index: -1;\r\n    .demand-header {\r\n      line-height: 60px;\r\n      padding: 0 20px;\r\n      width: 100%;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      border-bottom: 1px solid #e8e8e8;\r\n\r\n      .header {\r\n        font-size: 18px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333333;\r\n      }\r\n      .more {\r\n        font-size: 14px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        font-weight: 400;\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    .demmand-carousel {\r\n      width: 100%;\r\n      height: 260px;\r\n      padding: 12px 20px;\r\n      .demand-message-item {\r\n        display: flex;\r\n        padding: 16px 0 16px 0;\r\n        .title {\r\n          width: 65%;\r\n          height: 50px;\r\n          margin-left: 12px;\r\n          font-size: 16px;\r\n          line-height: 26px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          overflow: hidden;\r\n          -webkit-line-clamp: 2;\r\n          text-overflow: ellipsis;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n        }\r\n        .status-card {\r\n          background: rgba(21, 188, 132, 0.15);\r\n          color: #15bc84;\r\n          margin: auto;\r\n          font-size: 12px;\r\n          border-radius: 4px;\r\n          padding: 0 12px;\r\n          font-weight: 400;\r\n          height: 30px;\r\n          line-height: 30px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .small-card {\r\n    margin-top: 18px;\r\n    height: 300px;\r\n    padding-bottom: 12px;\r\n    background-color: #fff;\r\n    .small-card-header {\r\n      line-height: 60px;\r\n      width: 100%;\r\n      display: flex;\r\n      padding: 0 20px;\r\n      justify-content: space-between;\r\n      border-bottom: 1px solid #e8e8e8;\r\n      .header {\r\n        font-size: 18px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333333;\r\n      }\r\n      .more {\r\n        font-size: 14px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        font-weight: 400;\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    .message-carousel {\r\n      width: 100%;\r\n      height: 260px;\r\n      .message-item {\r\n        padding: 18px 0 18px 0;\r\n        margin: 4px 20px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        .title {\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n        }\r\n        .content {\r\n          margin-top: 8px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #666666;\r\n          line-height: 23px;\r\n          overflow: hidden;\r\n          -webkit-line-clamp: 2;\r\n          text-overflow: ellipsis;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n        }\r\n      }\r\n    }\r\n    .apply-carousel {\r\n      width: 100%;\r\n      height: 230px;\r\n      .apply-item {\r\n        padding: 18px 0 10px 0;\r\n        margin: 0 20px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        display: flex;\r\n        .item-left {\r\n          width: 100%;\r\n          .title {\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            // width: 300px;\r\n            line-height: 16px;\r\n            overflow: hidden;\r\n            -webkit-line-clamp: 1;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n          }\r\n          .content {\r\n            margin-top: 8px;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #666666;\r\n            line-height: 20px;\r\n            overflow: hidden;\r\n            -webkit-line-clamp: 1;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n          }\r\n        }\r\n        .item-right {\r\n          padding: 10px 0;\r\n          .button {\r\n            text-align: center;\r\n            width: 72px;\r\n            padding: 6px 0;\r\n            border-radius: 4px;\r\n            border: 1px solid #214dc5;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #214dc5;\r\n            line-height: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .conversation-box {\r\n      width: 100%;\r\n      height: 230px;\r\n      .conversaion-item {\r\n        padding: 12px 0 10px 0;\r\n        margin: 0 20px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        display: flex;\r\n        &-avatar {\r\n          margin-right: 16px;\r\n          .el-avatar {\r\n            background: transparent;\r\n          }\r\n        }\r\n        &-center {\r\n          width: 300px;\r\n          &-content {\r\n            margin-top: 2px;\r\n            font-size: 14px;\r\n            font-weight: 400;\r\n            color: #666666;\r\n            line-height: 20px;\r\n            overflow: hidden;\r\n            -webkit-line-clamp: 1;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n          }\r\n        }\r\n\r\n        &-right {\r\n          padding: 10px 0;\r\n          .button {\r\n            text-align: center;\r\n            width: 72px;\r\n            padding: 6px 0;\r\n            border-radius: 4px;\r\n            border: 1px solid #214dc5;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #214dc5;\r\n            line-height: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    margin: 8% 0;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .code-box {\r\n    background-color: #fff;\r\n    border-radius: 8px;\r\n    text-align: center;\r\n    .code-item {\r\n      padding: 16px 0;\r\n      .title {\r\n        margin-top: 10px;\r\n        font-size: 12px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        line-height: 12px;\r\n      }\r\n      .hint {\r\n        margin-top: 10px;\r\n        margin-bottom: 6px;\r\n        font-size: 10px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}