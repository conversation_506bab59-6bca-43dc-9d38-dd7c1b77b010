{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\index.vue", "mtime": 1750311962979}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_policyDeclare", "require", "_data", "_status", "data", "loading", "form", "text", "formInfo", "releaseId", "type", "policyStatus", "labelCodeList", "releaseIdList", "typeList", "policyStatusList", "POLICY_STATUS", "advancedReleaseId", "advancedType", "pageNum", "pageSize", "total", "created", "_ref", "$route", "params", "code", "console", "log", "getDictsList", "getListByName", "search", "methods", "_this", "getPolicyDeclareList", "then", "res", "_ref2", "rows", "catch", "_this2", "propertyName", "_this3", "getDicts", "toggleReleaseId", "toggleType", "changeRadio", "_typeof2", "default", "onSearch", "handleSizeChange", "handleCurrentChange", "goPolicyDeclarelDetail", "item", "routeData", "$router", "resolve", "path", "query", "id", "window", "open", "href", "goHome", "push", "watch", "computed"], "sources": ["src/views/policy/declare/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"policy-declare-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"policy-declarel-banner\">\r\n      <img src=\"../../../assets/policyDeclare/policyDeclareBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"policy-declarel-title-content\">\r\n        <div class=\"policy-declarel-title-box\">\r\n          <div class=\"policy-declarel-divider\"></div>\r\n          <div class=\"policy-declarel-title\">政策申报</div>\r\n          <div class=\"policy-declarel-divider\"></div>\r\n        </div>\r\n        <div class=\"policy-declarel-search-box\">\r\n          <el-form ref=\"form\" class=\"policy-declarel-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.text\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"policy-declarel-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"policy-declarel-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"policy-declarel-card\">\r\n        <div class=\"policy-declarel-info-content\">\r\n          <div class=\"policy-declarel-search-type-box\">\r\n            <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n              <div class=\"policy-declarel-search-line\">\r\n                <el-form-item\r\n                  label=\"发布单位\"\r\n                  class=\"policy-declarel-search-line-item\"\r\n                  :class=\"{ advanced: !advancedReleaseId }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.releaseId\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in releaseIdList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.id\"\r\n                      >{{ item.name }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"policy-declarel-search-line-btn\"\r\n                  @click=\"toggleReleaseId\"\r\n                  >{{ advancedReleaseId ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n              <div class=\"policy-declarel-search-line\">\r\n                <el-form-item\r\n                  label=\"政策类型\"\r\n                  class=\"policy-declarel-search-line-item\"\r\n                  :class=\"{ advanced: !advancedType }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.type\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in typeList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"policy-declarel-search-line-btn\"\r\n                  @click=\"toggleType\"\r\n                  >{{ advancedType ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n              <div class=\"policy-declarel-search-line\">\r\n                <el-form-item\r\n                  label=\"政策状态\"\r\n                  class=\"policy-declarel-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.policyStatus\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in policyStatusList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.value\"\r\n                      >{{ item.label }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"policy-declarel-list-item\"\r\n            @click=\"goPolicyDeclarelDetail(item)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-headline\">\r\n                <div class=\"item-title\">{{ item.releaseUnitName }}</div>\r\n                <div v-if=\"item.releaseDistrict\" class=\"item-address-tag\">\r\n                  <img\r\n                    src=\"../../../assets/policyDeclare/policyAddressIcon.png\"\r\n                    alt=\"\"\r\n                    class=\"item-address-img\"\r\n                  />\r\n                  <div class=\"item-address-text\">\r\n                    {{ item.releaseDistrict }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"list-item-title\">\r\n                {{ item.title }}\r\n              </div>\r\n              <div class=\"list-item-box\">\r\n                <div class=\"list-item-time\">\r\n                  <div\r\n                    v-if=\"item.policyStatus === 2\"\r\n                    class=\"list-item-time-end\"\r\n                  >\r\n                    申报结束\r\n                  </div>\r\n                  <div v-else class=\"list-item-time-red\">\r\n                    距申报截止还有\r\n                    <div class=\"red-num\">{{ item.dayCount }}</div>\r\n                    天\r\n                  </div>\r\n                </div>\r\n                <div class=\"list-item-money\">\r\n                  <div class=\"list-item-money-title\">最高奖励</div>\r\n                  <span class=\"list-item-money-num\">{{ item.maxReward }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"list-item-status\">\r\n                <!-- 1进行中  2已截止 -->\r\n                <img\r\n                  v-if=\"item.policyStatus === 1\"\r\n                  src=\"../../../assets/policyDeclare/carryOnIcon.png\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../assets/policyDeclare/cutoffIcon.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"policy-declarel-page-end\">\r\n            <el-button class=\"policy-declarel-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            >\r\n            <el-pagination\r\n              v-if=\"data && data.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"policy-declarel-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @size-change=\"handleSizeChange\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getPolicyDeclareList, getListByName } from \"@/api/policyDeclare\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { POLICY_STATUS } from \"@/const/status\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        text: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        releaseId: \"\", //发布单位\r\n        type: \"\", //政策类型\r\n        policyStatus: \"\", //政策状态\r\n        labelCodeList: [], //政策画像code集合\r\n      },\r\n      releaseIdList: [], //发布单位下拉列表\r\n      typeList: [], //政策类型下拉列表\r\n      policyStatusList: POLICY_STATUS,\r\n      advancedReleaseId: false,\r\n      advancedType: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    const { code } = this.$route.params || {};\r\n    console.log(this.$route);\r\n    if (code) {\r\n      this.formInfo.labelCodeList = code;\r\n    }\r\n    this.getDictsList(\"policy_type\", \"typeList\");\r\n    this.getListByName();\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getPolicyDeclareList({\r\n        text: this.form.text,\r\n        type: this.formInfo.type,\r\n        releaseId: this.formInfo.releaseId,\r\n        policyStatus: this.formInfo.policyStatus,\r\n        labelCodeList: this.formInfo.labelCodeList,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 查询发布单位\r\n    getListByName() {\r\n      getListByName().then((res) => {\r\n        this.releaseIdList = res.data || [];\r\n      });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    // 显示更多发布单位\r\n    toggleReleaseId() {\r\n      this.advancedReleaseId = !this.advancedReleaseId;\r\n    },\r\n    // 显示更多政策类型\r\n    toggleType() {\r\n      this.advancedType = !this.advancedType;\r\n    },\r\n    changeRadio() {\r\n      console.log(typeof this.formInfo.releaseId, \"0000\");\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n    // 跳转到政策详情页面\r\n    goPolicyDeclarelDetail(item) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/policyDeclareDetail\",\r\n        query: { id: item.id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n  watch: {},\r\n  computed: {},\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.policy-declare-container {\r\n  width: 100%;\r\n  .policy-declarel-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .policy-declarel-title-content {\r\n    width: 100%;\r\n    padding-bottom: 18px;\r\n    .policy-declarel-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .policy-declarel-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .policy-declarel-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .policy-declarel-search-box {\r\n      .policy-declarel-search-form {\r\n        text-align: center;\r\n        .policy-declarel-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .policy-declarel-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .policy-declarel-card {\r\n    background: #f4f5f9;\r\n    padding-top: 40px;\r\n    .policy-declarel-info-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      .policy-declarel-search-type-box {\r\n        background: #fff;\r\n        margin-bottom: 17px;\r\n        .policy-declarel-search-line {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 14px 24px 4px;\r\n          .policy-declarel-search-line-item {\r\n            flex: 1;\r\n            margin-bottom: 0;\r\n            display: flex;\r\n            &.advanced {\r\n              overflow: hidden;\r\n              height: 45px;\r\n            }\r\n            .more-radio {\r\n              margin-top: 11px;\r\n              flex: 1;\r\n            }\r\n          }\r\n          .policy-declarel-search-line-btn {\r\n            display: inline-block;\r\n            width: 64px;\r\n            height: 24px;\r\n            background: #fff;\r\n            border-radius: 2px;\r\n            border: 1px solid #d9d9d9;\r\n            font-size: 12px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            color: #333;\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 0 16px;\r\n            margin-top: 5px;\r\n            &:hover {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n          & + .policy-declarel-search-line {\r\n            border-top: 1px solid #f5f5f5;\r\n          }\r\n        }\r\n      }\r\n      .policy-declarel-list-item {\r\n        position: relative;\r\n        width: 100%;\r\n        background: #fff;\r\n        border-radius: 12px;\r\n        .list-item-content {\r\n          padding: 27px 24px 24px 24px;\r\n          cursor: pointer;\r\n          .list-item-headline {\r\n            display: flex;\r\n            align-items: center;\r\n            .item-title {\r\n              max-width: 570px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #999;\r\n              line-height: 18px;\r\n              word-break: break-all;\r\n            }\r\n            .item-address-tag {\r\n              display: flex;\r\n              align-items: center;\r\n              border-radius: 6px;\r\n              border: 2px solid #ff8516;\r\n              font-size: 15px;\r\n              line-height: 15px;\r\n              text-align: center;\r\n              margin-left: 12px;\r\n              color: #ff8516;\r\n              .item-address-img {\r\n                width: 19px;\r\n                height: 18px;\r\n                margin-right: 1px;\r\n              }\r\n              .item-address-text {\r\n                max-width: 570px;\r\n                word-break: break-all;\r\n                padding: 3px 5px 2px 0;\r\n              }\r\n            }\r\n          }\r\n          .list-item-title {\r\n            font-size: 24px;\r\n            font-family: PingFangSC-Medium, PingFang SC;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            line-height: 36px;\r\n            word-break: break-all;\r\n            padding-top: 18px;\r\n          }\r\n          .list-item-box {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            margin-top: 20px;\r\n            .list-item-time {\r\n              background: #f5f5f5;\r\n              border-radius: 6px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #999;\r\n              line-height: 18px;\r\n              .list-item-time-end {\r\n                padding: 6px 12px;\r\n              }\r\n              .list-item-time-red {\r\n                display: flex;\r\n                align-items: center;\r\n                padding: 6px 15px 6px 12px;\r\n                .red-num {\r\n                  max-width: 270px;\r\n                  font-size: 24px;\r\n                  font-family: PingFangSC-Medium, PingFang SC;\r\n                  font-weight: 500;\r\n                  color: #cf4140;\r\n                  line-height: 24px;\r\n                  word-wrap: break-word;\r\n                }\r\n              }\r\n            }\r\n            .list-item-money {\r\n              display: flex;\r\n              align-items: flex-end;\r\n              max-width: 570px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              .list-item-money-title {\r\n                font-size: 18px;\r\n                color: #999;\r\n                line-height: 18px;\r\n                margin-right: 6px;\r\n              }\r\n              .list-item-money-num {\r\n                max-width: 270px;\r\n                font-size: 36px;\r\n                font-weight: 500;\r\n                color: #cf4140;\r\n                line-height: 36px;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n          }\r\n        }\r\n        & + .policy-declarel-list-item {\r\n          margin-top: 24px;\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        .list-item-status {\r\n          position: absolute;\r\n          top: 0;\r\n          right: 0;\r\n          img {\r\n            width: 92px;\r\n            height: 71px;\r\n          }\r\n        }\r\n      }\r\n      .policy-declarel-page-end {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        padding: 24px 0 60px;\r\n        .policy-declarel-page-btn {\r\n          width: 82px;\r\n          height: 32px;\r\n          background: #fff;\r\n          border-radius: 4px;\r\n          border: 1px solid #d9d9d9;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.policy-declare-container {\r\n  .policy-declarel-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .el-form-item__label {\r\n    width: 88px;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #999;\r\n    padding-right: 32px;\r\n    text-align: left;\r\n  }\r\n  .policy-declarel-search-line {\r\n    .el-form-item__content {\r\n      width: 970px;\r\n    }\r\n  }\r\n  .el-radio-button {\r\n    padding-bottom: 20px;\r\n    .el-radio-button__inner {\r\n      border: none;\r\n      padding: 0 32px 0 0;\r\n      background: none;\r\n      &:hover {\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        color: #21c9b8;\r\n        background: none;\r\n      }\r\n    }\r\n    .el-radio-button__orig-radio:checked {\r\n      & + .el-radio-button__inner {\r\n        box-shadow: unset;\r\n      }\r\n    }\r\n  }\r\n  .policy-declarel-page-end {\r\n    .policy-declarel-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;AAiMA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;QACAC,IAAA;MACA;MACAC,QAAA;QACAC,SAAA;QAAA;QACAC,IAAA;QAAA;QACAC,YAAA;QAAA;QACAC,aAAA;MACA;MACAC,aAAA;MAAA;MACAC,QAAA;MAAA;MACAC,gBAAA,EAAAC,qBAAA;MACAC,iBAAA;MACAC,YAAA;MACAd,IAAA;MACAe,OAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,IAAA,QAAAC,MAAA,CAAAC,MAAA;MAAAC,IAAA,GAAAH,IAAA,CAAAG,IAAA;IACAC,OAAA,CAAAC,GAAA,MAAAJ,MAAA;IACA,IAAAE,IAAA;MACA,KAAAlB,QAAA,CAAAI,aAAA,GAAAc,IAAA;IACA;IACA,KAAAG,YAAA;IACA,KAAAC,aAAA;IACA,KAAAC,MAAA;EACA;EACAC,OAAA;IACAD,MAAA,WAAAA,OAAA;MAAA,IAAAE,KAAA;MACA,KAAA5B,OAAA;MACA,IAAA6B,mCAAA;QACA3B,IAAA,OAAAD,IAAA,CAAAC,IAAA;QACAG,IAAA,OAAAF,QAAA,CAAAE,IAAA;QACAD,SAAA,OAAAD,QAAA,CAAAC,SAAA;QACAE,YAAA,OAAAH,QAAA,CAAAG,YAAA;QACAC,aAAA,OAAAJ,QAAA,CAAAI,aAAA;QACAO,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA;MACA,GACAe,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAA5B,OAAA;QACA,IAAAgC,KAAA,GAAAD,GAAA;UAAAE,IAAA,GAAAD,KAAA,CAAAC,IAAA;UAAAjB,KAAA,GAAAgB,KAAA,CAAAhB,KAAA;QACAY,KAAA,CAAA7B,IAAA,GAAAkC,IAAA;QACAL,KAAA,CAAAZ,KAAA,GAAAA,KAAA;MACA,GACAkB,KAAA;QACAN,KAAA,CAAA5B,OAAA;MACA;IACA;IACA;IACAyB,aAAA,WAAAA,cAAA;MAAA,IAAAU,MAAA;MACA,IAAAV,4BAAA,IAAAK,IAAA,WAAAC,GAAA;QACAI,MAAA,CAAA3B,aAAA,GAAAuB,GAAA,CAAAhC,IAAA;MACA;IACA;IACA;IACAyB,YAAA,WAAAA,aAAAH,IAAA,EAAAe,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,cAAA,EAAAjB,IAAA,EAAAS,IAAA,WAAAC,GAAA;QACAM,MAAA,CAAAD,YAAA,IAAAL,GAAA,CAAAhC,IAAA;MACA;IACA;IACA;IACAwC,eAAA,WAAAA,gBAAA;MACA,KAAA3B,iBAAA,SAAAA,iBAAA;IACA;IACA;IACA4B,UAAA,WAAAA,WAAA;MACA,KAAA3B,YAAA,SAAAA,YAAA;IACA;IACA4B,WAAA,WAAAA,YAAA;MACAnB,OAAA,CAAAC,GAAA,KAAAmB,QAAA,CAAAC,OAAA,OAAAxC,QAAA,CAAAC,SAAA;MACA,KAAAwC,QAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA9B,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAA6B,QAAA;IACA;IACAE,mBAAA,WAAAA,oBAAAhC,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAY,MAAA;IACA;IACAkB,QAAA,WAAAA,SAAA;MACA,KAAA9B,OAAA;MACA,KAAAY,MAAA;IACA;IACA;IACAqB,sBAAA,WAAAA,uBAAAC,IAAA;MACA,IAAAC,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACAC,KAAA;UAAAC,EAAA,EAAAN,IAAA,CAAAM;QAAA;MACA;MACAC,MAAA,CAAAC,IAAA,CAAAP,SAAA,CAAAQ,IAAA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAR,OAAA,CAAAS,IAAA;QAAAP,IAAA;MAAA;IACA;EACA;EACAQ,KAAA;EACAC,QAAA;AACA", "ignoreList": []}]}