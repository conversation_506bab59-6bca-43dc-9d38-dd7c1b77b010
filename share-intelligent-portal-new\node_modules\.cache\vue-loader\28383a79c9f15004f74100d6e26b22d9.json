{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\TopNav\\index.vue?vue&type=template&id=35f3a2c1&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\TopNav\\index.vue", "mtime": 1750311962827}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}