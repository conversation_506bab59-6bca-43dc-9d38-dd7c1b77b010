<template>
    <div class="content">
        <div class="content_banner">
            众筹科研
        </div>
        <div class="card-container content_card">
            <div class="card-title">{{ title }}项目众筹科研报名</div>
            <el-form ref="form" :rules="rules" :model="form" label-position="top">
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="单位名称" prop="companyName">
                            <el-input v-model="form.companyName" placeholder="请输入单位名称"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系人" prop="contactPerson">
                            <el-input v-model="form.contactPerson" placeholder="请输入联系人"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="联系方式" prop="contactPhone">
                            <el-input v-model="form.contactPhone" placeholder="请输入联系方式"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="供给类型" prop="supplyType">
                            <el-checkbox-group v-model="supplyTypes">
                                <el-checkbox v-for="item in typeList" :key="item.dictValue" :label="item.dictValue">{{
                                    item.dictLabel }}</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="24">
                        <el-form-item class="footer-submit">
                            <el-button type="primary" @click="onSubmit">提交</el-button>
                            <el-button style="margin-left: 140px" @click.once="onCancel">取消</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </div>
    </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { addSysSupplierInfo } from "@/api/techRequirement";


export default {
    data() {
        var validatePass = (rule, value, callback) => {
            if (this.supplyTypes.length == 0) {
                callback(new Error('请选择供给类型'));
            } else if (this.supplyTypes.length > 0) {
                callback();
            }
        };
        return {
            form: {},
            rules: {
                companyName: [{ required: true, message: "单位名称不能为空", trigger: "blur" }],
                contactPerson: [
                    { required: true, message: "联系人不能为空", trigger: "blur" },
                ],
                contactPhone: [
                    { required: true, message: "联系方式不能为空", trigger: "blur" },
                ],
                supplyType: [
                    { validator: validatePass, trigger: "blur" },
                ],
            },
            typeList: [], // 类型
            title: "",
            supplyTypes: [],
        };
    },
    created() {
        this.title = this.$route.query.title || "";
        this.form.techRequirementId = this.$route.query.requirementId || "";
        this.getType();
    },
    methods: {
        getType() {
            let params = { dictType: "sys_supply_type" };
            listData(params).then((response) => {
                this.typeList = response.rows;
            });
        },
        onSubmit() {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    this.form.supplyType = this.supplyTypes.join(",");
                    addSysSupplierInfo(this.form).then((response) => {
                        if (response.code === 200) {
                            this.$message({
                                message: "提交申请成功",
                                type: "success",
                                duration: 2000,
                            })
                            this.onCancel()
                        } else {
                            this.$message.warning(response.msg)
                        }
                    })
                }
            });
        },
        onCancel() {
            this.$router.go(-1);
        },
    },
};
</script>
<style scoped lang="scss">
.content {
    width: 100%;
    padding-bottom: 60px;
    background-color: #f2f2f2;
}

.content_banner {
    width: 100%;
    height: 300px;
    background-image: url("../../../../assets/release/banner.png");
    background-size: 100% 100%;
    text-align: center;
    margin: 0 auto;
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 40px;
    color: #000;
    line-height: 300px;

    .imgContent {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-top: 10px;

        .imgStyle {
            width: 1256px;
            height: 206px;
            position: relative;
        }
    }
}

.content_card {
    // height: 1530px;
    background: #ffffff;
    border-radius: 2px;
    margin-top: 30px;
    padding: 29px 60px 57px 60px;

    .card-title {
        font-weight: 500;
        font-size: 22px;
        color: #222222;
        line-height: 70px;
    }
}

.addStyle {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #21c9b8;
    margin-left: auto;
    cursor: pointer;
}

.footer-submit {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-top: 60px;

    .el-button {
        width: 140px;
        height: 50px;
    }
}
</style>
