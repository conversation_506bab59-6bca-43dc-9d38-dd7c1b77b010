{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyInfo\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyInfo\\index.vue", "mtime": 1750311963052}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuc3BsaWNlLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcudHJpbS5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLmNvbnN0cnVjdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IubWFwLmpzIik7CnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRTovY29tcGFueS9ubWQvbm1kbmV3L3NoYXJlLWludGVsbGlnZW50L3NoYXJlLWludGVsbGlnZW50LXBvcnRhbC1uZXcvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0U3ByZWFkMi5qcyIpKTsKdmFyIF91c2VyTWVudSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiKSk7CnZhciBfY29tcGFueSA9IHJlcXVpcmUoIkAvYXBpL3N5c3RlbS9jb21wYW55Iik7CnZhciBfc3RvcmUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvc3RvcmUiKSk7CnZhciBfYWRkcmVzcyA9IHJlcXVpcmUoIkAvYXNzZXRzL2FkZHJlc3M0Iik7CnZhciBfaW5kZXggPSByZXF1aXJlKCJAL3V0aWxzL2luZGV4Iik7CnZhciBfYXV0aCA9IHJlcXVpcmUoIkAvdXRpbHMvYXV0aCIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogIkNvbXBhbnlJbmZvIiwKICBkaWN0czogWyJjb21wYW55X2luZHVzdHJ5Il0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRhdGE6IHt9LAogICAgICBsb2NhdGlvblJlc3M6ICIiLAogICAgICBjYXJvdXNlbEluZGV4OiAwLAogICAgICB1c2VyOiBfc3RvcmUuZGVmYXVsdC5nZXR0ZXJzLnVzZXIsCiAgICAgIGhlYWRlcnM6IHsKICAgICAgICBBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyAoMCwgX2F1dGguZ2V0VG9rZW4pKCkKICAgICAgfSwKICAgICAgc291cmNlQWRkcmVzczogX2FkZHJlc3MuYWRkcmVzczQsCiAgICAgIC8vIOecgeW4guWMuuihl+mBk+a6kAogICAgICBmb3JtOiB7CiAgICAgICAgY29tcGFueVBpY3R1cmVMaXN0OiBbXQogICAgICB9LAogICAgICBydWxlczoge30sCiAgICAgIHVwbG9hZFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvZmlsZS91cGxvYWQiLAogICAgICAvL+S4iuS8oOWcsOWdgAogICAgICBhY2NlcHQ6ICIuanBnLCAuanBlZywgLnBuZywgLmJtcCIKICAgIH07CiAgfSwKICBjb21wb25lbnRzOiB7CiAgICBVc2VyTWVudTogX3VzZXJNZW51LmRlZmF1bHQKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldERldGFpbCgpOwogIH0sCiAgbWV0aG9kczogewogICAgZ2V0RGV0YWlsOiBmdW5jdGlvbiBnZXREZXRhaWwoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgICgwLCBfY29tcGFueS5nZXRDb21wYW55RGV0YWlsQnlCdXNzaW5lc3NObykoewogICAgICAgIGJ1c3NpbmVzc05vOiB0aGlzLnVzZXIuYnVzc2luZXNzTm8KICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgewogICAgICAgICAgX3RoaXMuZm9ybSA9IHJlcy5kYXRhOwogICAgICAgICAgLy/liJ3lp4vljJblnLDljLrogZTliqgKICAgICAgICAgIHZhciBwcm92aW5jZSA9IHJlcy5kYXRhLnByb3ZpbmNlOwogICAgICAgICAgdmFyIGNpdHkgPSByZXMuZGF0YS5jaXR5OwogICAgICAgICAgdmFyIHJlZ2lvbiA9IHJlcy5kYXRhLnJlZ2lvbjsKICAgICAgICAgIHZhciB2YWx1ZXMgPSAoMCwgX2luZGV4LmdldF9hZGRyZXNzX3ZhbHVlczQpKFtwcm92aW5jZSwgY2l0eSwgcmVnaW9uXSk7CiAgICAgICAgICBfdGhpcy5sb2NhdGlvblJlc3MgPSB2YWx1ZXM7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBzdWJtaXRGb3JtOiBmdW5jdGlvbiBzdWJtaXRGb3JtKCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgKDAsIF9jb21wYW55LmVkaXRDb21wYW55KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHRoaXMuZm9ybSkpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsKICAgICAgICAgIF90aGlzMi4kbW9kYWwubXNnU3VjY2Vzcygi5L+d5a2Y5oiQ5YqfIik7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDkuqflk4HnhafniYfkuIrkvKDkuYvliY3nmoTpkqnlrZAKICAgIGhhbmRsZUJlZm9yZVVwbG9hZDogZnVuY3Rpb24gaGFuZGxlQmVmb3JlVXBsb2FkKGZpbGUpIHsKICAgICAgdmFyIG5hbWUgPSBmaWxlLm5hbWUsCiAgICAgICAgdHlwZSA9IGZpbGUudHlwZSwKICAgICAgICBzaXplID0gZmlsZS5zaXplOwogICAgICB2YXIgdHlwZUxpc3QgPSB0aGlzLmFjY2VwdC5zcGxpdCgiLCIpLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLnRyaW0oKS50b0xvd2VyQ2FzZSgpLnN1YnN0cigxKTsKICAgICAgfSk7CiAgICAgIHZhciBkb3RJbmRleCA9IG5hbWUubGFzdEluZGV4T2YoIi4iKTsKICAgICAgLy8g5paH5Lu257G75Z6L5qCh6aqMCiAgICAgIGlmIChkb3RJbmRleCA9PT0gLTEpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7fkuIrkvKDmraPnoa7moLzlvI/nmoTmlofku7YiKTsKICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdmFyIHN1ZmZpeCA9IG5hbWUuc3Vic3RyaW5nKGRvdEluZGV4ICsgMSk7CiAgICAgICAgaWYgKHR5cGVMaXN0LmluZGV4T2Yoc3VmZml4LnRvTG93ZXJDYXNlKCkpID09PSAtMSkgewogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+35LiK5Lyg5q2j56Gu5qC85byP55qE5paH5Lu2Iik7CiAgICAgICAgICByZXR1cm4gZmFsc2U7CiAgICAgICAgfQogICAgICB9CiAgICAgIC8vIOaWh+S7tuS4iuS8oOWkp+Wwj+mZkOWItgogICAgICBpZiAoc2l6ZSA+IDEwNDg1NzYgKiAyMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hzIwTe+8gSIpOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgfSwKICAgIC8vIOeCueWHu+S6p+WTgeeFp+eJhwogICAgaGFuZGxlUGljdHVyZUNhcmRQcmV2aWV3OiBmdW5jdGlvbiBoYW5kbGVQaWN0dXJlQ2FyZFByZXZpZXcoZmlsZSkgewogICAgICB0aGlzLmltYWdlVXJsID0gZmlsZS51cmw7CiAgICAgIHRoaXMuaW1nVmlzaWJsZSA9IHRydWU7CiAgICB9LAogICAgaGFuZGxlU3VjY2VzczogZnVuY3Rpb24gaGFuZGxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgaWYgKCF0aGlzLmZvcm0uY29tcGFueVBpY3R1cmVMaXN0KSB7CiAgICAgICAgdGhpcy5mb3JtLmNvbXBhbnlQaWN0dXJlTGlzdCA9IFtdOwogICAgICB9CiAgICAgIHRoaXMuZm9ybS5jb21wYW55UGljdHVyZUxpc3QucHVzaChyZXNwb25zZS5kYXRhKTsKICAgICAgY29uc29sZS5sb2codGhpcy5mb3JtLmNvbXBhbnlQaWN0dXJlTGlzdCk7CiAgICB9LAogICAgY2Fyb3VzZWxDaGFuZ2U6IGZ1bmN0aW9uIGNhcm91c2VsQ2hhbmdlKG5vdywgcHJldikgewogICAgICB0aGlzLmNhcm91c2VsSW5kZXggPSBub3c7CiAgICB9LAogICAgLy8g5Yig6Zmk5Lqn5ZOB54Wn54mHCiAgICBkZWxldGVDb21wYW55UGhvdG86IGZ1bmN0aW9uIGRlbGV0ZUNvbXBhbnlQaG90bygpIHsKICAgICAgdGhpcy5mb3JtLmNvbXBhbnlQaWN0dXJlTGlzdC5zcGxpY2UodGhpcy5jYXJvdXNlbEluZGV4LCAxKTsKICAgIH0sCiAgICBoYW5kbGVjb21wYW55RmlsZVN1Y2Nlc3M6IGZ1bmN0aW9uIGhhbmRsZWNvbXBhbnlGaWxlU3VjY2VzcyhyZXMsIGZpbGUsIGZpbGVMaXN0KSB7CiAgICAgIC8v5q2k5aSE5YaZ5LiK5Lygb3Nz5oiQ5Yqf5LmL5ZCO55qE6YC76L6RCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsKICAgICAgICBpZiAoIXRoaXMuZm9ybS5jb21wYW55TWF0ZXJpYWxMaXN0KSB7CiAgICAgICAgICB0aGlzLmZvcm0uY29tcGFueU1hdGVyaWFsTGlzdCA9IFtdOwogICAgICAgIH0KICAgICAgICB0aGlzLmZvcm0uY29tcGFueU1hdGVyaWFsTGlzdC5wdXNoKHJlcy5kYXRhKTsKICAgICAgfQogICAgfSwKICAgIGhhbmRsZW9tcGFueUZpbGVSZW1vdmU6IGZ1bmN0aW9uIGhhbmRsZW9tcGFueUZpbGVSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy5jb21wYW55Rm9ybS5jb21wYW55Q2FyZExpc3QgPSByZXMuZGF0YTsKICAgIH0sCiAgICAvKiDpgInmi6nnnIHluILljLrooZfpgZMqL2hhbmRsZUFkZHJlc3M6IGZ1bmN0aW9uIGhhbmRsZUFkZHJlc3ModmFsKSB7CiAgICAgIHZhciBkYXRhID0gKDAsIF9pbmRleC5nZXRfYWRkcmVzc19sYWJlbDQpKHZhbCk7CiAgICAgIHRoaXMuZm9ybS5wcm92aW5jZSA9IGRhdGFbMF07CiAgICAgIHRoaXMuZm9ybS5wcm92aW5jZUNvZGUgPSB2YWxbMF07CiAgICAgIHRoaXMuZm9ybS5jaXR5ID0gZGF0YVsxXTsKICAgICAgdGhpcy5mb3JtLmNpdHlDb2RlID0gdmFsWzFdOwogICAgICB0aGlzLmZvcm0ucmVnaW9uID0gZGF0YVsyXTsKICAgICAgdGhpcy5mb3JtLnJlZ2lvbkNvZGUgPSB2YWxbMl07CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_company", "_store", "_address", "_index", "_auth", "name", "dicts", "data", "locationRess", "carouselIndex", "user", "store", "getters", "headers", "Authorization", "getToken", "sourceAddress", "address4", "form", "companyPictureList", "rules", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "accept", "components", "UserMenu", "created", "getDetail", "methods", "_this", "getCompanyDetailByBussinessNo", "bussinessNo", "then", "res", "code", "province", "city", "region", "values", "get_address_values4", "submitForm", "_this2", "editCompany", "_objectSpread2", "default", "$modal", "msgSuccess", "handleBeforeUpload", "file", "type", "size", "typeList", "split", "map", "item", "trim", "toLowerCase", "substr", "dotIndex", "lastIndexOf", "$message", "error", "suffix", "substring", "indexOf", "handlePictureCardPreview", "imageUrl", "url", "imgVisible", "handleSuccess", "response", "fileList", "push", "console", "log", "carouselChange", "now", "prev", "deleteCompanyPhoto", "splice", "handlecompanyFileSuccess", "companyMaterialList", "handleompanyFileRemove", "companyForm", "companyCardList", "handleAddress", "val", "get_address_label4", "provinceCode", "cityCode", "regionCode"], "sources": ["src/views/system/user/companyInfo/index.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-20 13:53:26\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"company-info-container\">\r\n          <div class=\"enterprise-detail-content\">\r\n            <div class=\"enterprise-title\">{{ data.name }}</div>\r\n            <div class=\"enterprise-title-carousel\">\r\n              <el-carousel\r\n                class=\"carousel-content\"\r\n                arrow=\"always\"\r\n                @change=\"carouselChange\"\r\n                :interval=\"5000\"\r\n              >\r\n                <el-carousel-item\r\n                  v-for=\"(item, index) in form.companyPictureList\"\r\n                  :key=\"index\"\r\n                  class=\"carousel-item-content\"\r\n                >\r\n                  <div class=\"carousel-item-box\">\r\n                    <img :src=\"item.url\" alt=\"\" />\r\n                  </div>\r\n                </el-carousel-item>\r\n              </el-carousel>\r\n\r\n              <div class=\"action-container\">\r\n                <el-upload\r\n                  multiple\r\n                  :limit=\"5\"\r\n                  :show-file-list=\"false\"\r\n                  :headers=\"headers\"\r\n                  :action=\"uploadUrl\"\r\n                  :file-list=\"form.companyPictureList\"\r\n                  :accept=\"accept\"\r\n                  :before-upload=\"handleBeforeUpload\"\r\n                  :on-preview=\"handlePictureCardPreview\"\r\n                  :on-success=\"handleSuccess\"\r\n                >\r\n                  <img\r\n                    class=\"button-icon\"\r\n                    src=\"@/assets/user/company_upload.png\"\r\n                    alt=\"\"\r\n                  />\r\n                </el-upload>\r\n                <a @click=\"deleteCompanyPhoto\">\r\n                  <img\r\n                    class=\"button-icon ml_30\"\r\n                    src=\"@/assets/user/company_delete.png\"\r\n                    alt=\"\"\r\n                  />\r\n                </a>\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"enterprise-title-tag\">\r\n              <div\r\n                v-for=\"(item, index) in form.industrialChainValueList\"\r\n                :key=\"index\"\r\n                class=\"title-tag-item\"\r\n              >\r\n                {{ item }}\r\n              </div>\r\n            </div>\r\n            <div class=\"form-class\"></div>\r\n            <el-form\r\n              ref=\"form\"\r\n              :model=\"form\"\r\n              :rules=\"rules\"\r\n              label-width=\"120px\"\r\n            >\r\n              <el-form-item label=\"所属行业：\" prop=\"industry\">\r\n                <el-select\r\n                  v-model=\"form.industry\"\r\n                  placeholder=\"请选择\"\r\n                  clearable\r\n                >\r\n                  <el-option\r\n                    v-for=\"dict in dict.type.company_industry\"\r\n                    :key=\"dict.value\"\r\n                    :label=\"dict.label\"\r\n                    :value=\"dict.value\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"所在地区：\" prop=\"contactsName\">\r\n                    <el-cascader\r\n                      v-model=\"locationRess\"\r\n                      class=\"user-mgt-edit-select\"\r\n                      :options=\"sourceAddress\"\r\n                      :props=\"{\r\n                        label: 'name',\r\n                        value: 'code',\r\n                        checkStrictly: true,\r\n                      }\"\r\n                      @change=\"handleAddress\"\r\n                    >\r\n                    </el-cascader>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"详细地址：\" prop=\"detailAddress\">\r\n                    <el-input\r\n                      v-model=\"form.detailAddress\"\r\n                      placeholder=\"请输入详细地址\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"主营业务：\" prop=\"mainBusiness\">\r\n                    <el-input\r\n                      v-model=\"form.mainBusiness\"\r\n                      type=\"textarea\"\r\n                      :maxlength=\"500\"\r\n                      rows=\"5\"\r\n                      show-word-limit\r\n                      placeholder=\"请输入主营业务\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"企业介绍：\" prop=\"introduce\">\r\n                    <el-input\r\n                      v-model=\"form.introduce\"\r\n                      type=\"textarea\"\r\n                      :maxlength=\"500\"\r\n                      rows=\"5\"\r\n                      show-word-limit\r\n                      placeholder=\"请输入企业介绍\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-form-item label=\"上传文件：\" prop=\"companyMaterialList\">\r\n                <el-upload\r\n                  :file-list=\"form.companyMaterialList\"\r\n                  :headers=\"headers\"\r\n                  :action=\"uploadUrl\"\r\n                  accept=\".pdf, .docx, .xls\"\r\n                  :on-remove=\"handleompanyFileRemove\"\r\n                  :on-success=\"handlecompanyFileSuccess\"\r\n                  :limit=\"10\"\r\n                >\r\n                  <el-button\r\n                    class=\"apathy-upload-btn\"\r\n                    size=\"small\"\r\n                    icon=\"el-icon-upload2\"\r\n                    >上传文件\r\n                  </el-button>\r\n                  <span slot=\"tip\" class=\"el-upload__tip\">\r\n                    仅限doc、pdf、xls格式，且不超过10M\r\n                  </span>\r\n                </el-upload>\r\n              </el-form-item>\r\n            </el-form>\r\n            <div class=\"button-container\">\r\n              <el-button type=\"danger\" @click=\"submitForm()\">保存</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport {\r\n  getCompanyDetailByBussinessNo,\r\n  editCompany,\r\n} from \"@/api/system/company\";\r\nimport store from \"@/store\";\r\nimport { address4 } from \"@/assets/address4\";\r\nimport { get_address_label4, get_address_values4 } from \"@/utils/index\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"CompanyInfo\",\r\n  dicts: [\"company_industry\"],\r\n\r\n  data() {\r\n    return {\r\n      data: {},\r\n      locationRess: \"\",\r\n      carouselIndex: 0,\r\n      user: store.getters.user,\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      sourceAddress: address4, // 省市区街道源\r\n      form: {\r\n        companyPictureList: [],\r\n      },\r\n      rules: {},\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n    };\r\n  },\r\n  components: { UserMenu },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    getDetail() {\r\n      getCompanyDetailByBussinessNo({\r\n        bussinessNo: this.user.bussinessNo,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.form = res.data;\r\n          //初始化地区联动\r\n          let province = res.data.province;\r\n          let city = res.data.city;\r\n          let region = res.data.region;\r\n          let values = get_address_values4([province, city, region]);\r\n          this.locationRess = values;\r\n        }\r\n      });\r\n    },\r\n    submitForm() {\r\n      editCompany({\r\n        ...this.form,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.$modal.msgSuccess(\"保存成功\");\r\n        }\r\n      });\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePictureCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    handleSuccess(response, file, fileList) {\r\n      if (!this.form.companyPictureList) {\r\n        this.form.companyPictureList = [];\r\n      }\r\n      this.form.companyPictureList.push(response.data);\r\n      console.log(this.form.companyPictureList);\r\n    },\r\n    carouselChange(now, prev) {\r\n      this.carouselIndex = now;\r\n    },\r\n    // 删除产品照片\r\n    deleteCompanyPhoto() {\r\n      this.form.companyPictureList.splice(this.carouselIndex, 1);\r\n    },\r\n    handlecompanyFileSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        if (!this.form.companyMaterialList) {\r\n          this.form.companyMaterialList = [];\r\n        }\r\n        this.form.companyMaterialList.push(res.data);\r\n      }\r\n    },\r\n    handleompanyFileRemove(file, fileList) {\r\n      this.companyForm.companyCardList = res.data;\r\n    },\r\n    /* 选择省市区街道*/\r\n    handleAddress(val) {\r\n      let data = get_address_label4(val);\r\n      this.form.province = data[0];\r\n      this.form.provinceCode = val[0];\r\n      this.form.city = data[1];\r\n      this.form.cityCode = val[1];\r\n      this.form.region = data[2];\r\n      this.form.regionCode = val[2];\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .company-info-container {\r\n    .enterprise-detail-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      padding: 60px 116px 58px;\r\n      background: #fff;\r\n      .el-cascader--medium {\r\n        font-size: 14px;\r\n        width: 100%;\r\n        line-height: 36px;\r\n      }\r\n      .enterprise-title {\r\n        font-size: 32px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        text-align: center;\r\n        padding-bottom: 44px;\r\n      }\r\n      .enterprise-title-carousel {\r\n        width: 720px;\r\n        height: 360px;\r\n        position: relative;\r\n        margin: 0 auto;\r\n        .action-container {\r\n          position: absolute;\r\n          right: 80px;\r\n          bottom: 10px;\r\n          display: flex;\r\n          .button-icon {\r\n            width: 44px;\r\n            height: 44px;\r\n          }\r\n          .ml_30 {\r\n            margin-left: 20px;\r\n          }\r\n        }\r\n        .carousel-content {\r\n          width: 100%;\r\n          height: 360px;\r\n          .carousel-item-content {\r\n            width: 100%;\r\n            height: 100%;\r\n            .carousel-item-box {\r\n              margin: 0 auto;\r\n              width: 600px;\r\n              height: 100%;\r\n              img {\r\n                width: 100%;\r\n                height: 100%;\r\n              }\r\n            }\r\n          }\r\n        }\r\n        .carousel-default-img {\r\n          width: 600px;\r\n          height: 100%;\r\n          margin: 0 auto;\r\n          display: block;\r\n        }\r\n      }\r\n      .enterprise-title-tag {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        width: 600px;\r\n        margin: 0 auto 21px;\r\n        .title-tag-item {\r\n          background: rgba(197, 37, 33, 0.1);\r\n          border-radius: 4px;\r\n          padding: 6px 12px;\r\n          font-size: 12px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #21c9b8;\r\n          line-height: 12px;\r\n          margin: 16px 16px 0 0;\r\n        }\r\n      }\r\n\r\n      .enterprise-introduction-box {\r\n        width: 960px;\r\n        padding-top: 61px;\r\n        .enterprise-introduction-info {\r\n          padding-top: 40px;\r\n          font-size: 16px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 28px;\r\n        }\r\n      }\r\n      .enterprise-introduction-title {\r\n        display: flex;\r\n        align-items: center;\r\n        .introduction-line {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .introduction-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n    }\r\n    .el-select {\r\n      display: inline-block;\r\n      position: relative;\r\n      width: 100%;\r\n    }\r\n    .form-class {\r\n      .el-input--medium .el-input__inner {\r\n        height: 36px;\r\n        width: 400px;\r\n        line-height: 36px;\r\n      }\r\n    }\r\n    .button-container {\r\n      width: 100%;\r\n      margin-top: 50px;\r\n      text-align: center;\r\n      .el-button--danger {\r\n        background: #21c9b8;\r\n        color: #fff;\r\n        border-color: #21c9b8;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAwLA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAIA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAM,IAAA;EACAC,KAAA;EAEAC,IAAA,WAAAA,KAAA;IACA;MACAA,IAAA;MACAC,YAAA;MACAC,aAAA;MACAC,IAAA,EAAAC,cAAA,CAAAC,OAAA,CAAAF,IAAA;MACAG,OAAA;QAAAC,aAAA,kBAAAC,cAAA;MAAA;MACAC,aAAA,EAAAC,iBAAA;MAAA;MACAC,IAAA;QACAC,kBAAA;MACA;MACAC,KAAA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,MAAA;IACA;EACA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAD,SAAA,WAAAA,UAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,sCAAA;QACAC,WAAA,OAAAvB,IAAA,CAAAuB;MACA,GAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAL,KAAA,CAAAb,IAAA,GAAAiB,GAAA,CAAA5B,IAAA;UACA;UACA,IAAA8B,QAAA,GAAAF,GAAA,CAAA5B,IAAA,CAAA8B,QAAA;UACA,IAAAC,IAAA,GAAAH,GAAA,CAAA5B,IAAA,CAAA+B,IAAA;UACA,IAAAC,MAAA,GAAAJ,GAAA,CAAA5B,IAAA,CAAAgC,MAAA;UACA,IAAAC,MAAA,OAAAC,0BAAA,GAAAJ,QAAA,EAAAC,IAAA,EAAAC,MAAA;UACAR,KAAA,CAAAvB,YAAA,GAAAgC,MAAA;QACA;MACA;IACA;IACAE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,oBAAA,MAAAC,cAAA,CAAAC,OAAA,MACA,KAAA5B,IAAA,CACA,EAAAgB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAO,MAAA,CAAAI,MAAA,CAAAC,UAAA;QACA;MACA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAA7C,IAAA,GAAA6C,IAAA,CAAA7C,IAAA;QAAA8C,IAAA,GAAAD,IAAA,CAAAC,IAAA;QAAAC,IAAA,GAAAF,IAAA,CAAAE,IAAA;MACA,IAAAC,QAAA,QAAA5B,MAAA,CACA6B,KAAA,MACAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA,GAAAC,WAAA,GAAAC,MAAA;MAAA;MACA,IAAAC,QAAA,GAAAvD,IAAA,CAAAwD,WAAA;MACA;MACA,IAAAD,QAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;QACA,IAAAC,MAAA,GAAA3D,IAAA,CAAA4D,SAAA,CAAAL,QAAA;QACA,IAAAP,QAAA,CAAAa,OAAA,CAAAF,MAAA,CAAAN,WAAA;UACA,KAAAI,QAAA,CAAAC,KAAA;UACA;QACA;MACA;MACA;MACA,IAAAX,IAAA;QACA,KAAAU,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACA;IACAI,wBAAA,WAAAA,yBAAAjB,IAAA;MACA,KAAAkB,QAAA,GAAAlB,IAAA,CAAAmB,GAAA;MACA,KAAAC,UAAA;IACA;IACAC,aAAA,WAAAA,cAAAC,QAAA,EAAAtB,IAAA,EAAAuB,QAAA;MACA,UAAAvD,IAAA,CAAAC,kBAAA;QACA,KAAAD,IAAA,CAAAC,kBAAA;MACA;MACA,KAAAD,IAAA,CAAAC,kBAAA,CAAAuD,IAAA,CAAAF,QAAA,CAAAjE,IAAA;MACAoE,OAAA,CAAAC,GAAA,MAAA1D,IAAA,CAAAC,kBAAA;IACA;IACA0D,cAAA,WAAAA,eAAAC,GAAA,EAAAC,IAAA;MACA,KAAAtE,aAAA,GAAAqE,GAAA;IACA;IACA;IACAE,kBAAA,WAAAA,mBAAA;MACA,KAAA9D,IAAA,CAAAC,kBAAA,CAAA8D,MAAA,MAAAxE,aAAA;IACA;IACAyE,wBAAA,WAAAA,yBAAA/C,GAAA,EAAAe,IAAA,EAAAuB,QAAA;MACA;MACA,IAAAtC,GAAA,CAAAC,IAAA;QACA,UAAAlB,IAAA,CAAAiE,mBAAA;UACA,KAAAjE,IAAA,CAAAiE,mBAAA;QACA;QACA,KAAAjE,IAAA,CAAAiE,mBAAA,CAAAT,IAAA,CAAAvC,GAAA,CAAA5B,IAAA;MACA;IACA;IACA6E,sBAAA,WAAAA,uBAAAlC,IAAA,EAAAuB,QAAA;MACA,KAAAY,WAAA,CAAAC,eAAA,GAAAnD,GAAA,CAAA5B,IAAA;IACA;IACA,YACAgF,aAAA,WAAAA,cAAAC,GAAA;MACA,IAAAjF,IAAA,OAAAkF,yBAAA,EAAAD,GAAA;MACA,KAAAtE,IAAA,CAAAmB,QAAA,GAAA9B,IAAA;MACA,KAAAW,IAAA,CAAAwE,YAAA,GAAAF,GAAA;MACA,KAAAtE,IAAA,CAAAoB,IAAA,GAAA/B,IAAA;MACA,KAAAW,IAAA,CAAAyE,QAAA,GAAAH,GAAA;MACA,KAAAtE,IAAA,CAAAqB,MAAA,GAAAhC,IAAA;MACA,KAAAW,IAAA,CAAA0E,UAAA,GAAAJ,GAAA;IACA;EACA;AACA", "ignoreList": []}]}