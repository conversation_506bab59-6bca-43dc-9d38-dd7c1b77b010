<template>
  <div class="activity-container">
    <div class="activity-banner">
      <img src="../../assets/thinkTank/thinkTankBanner.png" alt="" />
      <div class="bannerTitle">专家智库</div>
      <div class="bannerDesc">
        汇聚行业专家，提供咨询与技术服务，推进其碳中和目标实现
      </div>
    </div>
    <div v-loading="loading">
      <div class="activity-title-content">
        <div class="activity-search-box">
          <el-form ref="form" class="activity-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.keywords"
                placeholder="请输入搜索内容"
                class="activity-search-input"
              >
                <el-button
                  slot="append"
                  class="activity-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="classicCaseType">
          <div class="typeTitle">技术类别</div>
          <div
            v-for="(item, index) in technologyTypeList"
            :key="index"
            class="caseName"
            :class="activeName == item.dictLabel ? 'caseNameHover' : ''"
            @click="getType(item.dictLabel)"
          >
            {{ item.dictLabel }}
          </div>
        </div>
        <div class="expert-library-list">
          <div
            v-for="(item, index) in data"
            :key="index"
            class="list-item-content"
            @click="goExpertLibrary(item.id)"
          >
            <div class="list-item-box">
              <div class="item-headline">
                <div class="item-title">
                  {{ item.expertName }}
                </div>
              </div>
              <div class="expert-library-label">
                <div
                  v-for="(val, index1) in item.techniqueTypeName"
                  :key="index1"
                  class="library-label-item"
                >
                  <span v-if="index1 < 2" class="expert-library-type">{{
                    `#${val}`
                  }}</span>
                  <span v-else>…</span>
                </div>
              </div>
              <div class="expert-library-box">
                {{ item.synopsis }}
              </div>
            </div>
            <div class="list-item-img">
              <img v-if="item.headPortrait" :src="item.headPortrait" alt="" />
              <img
                v-else
                src="../../assets/expertLibrary/defaultImg.png"
                alt=""
              />
            </div>
          </div>
        </div>
        <div class="activity-page-end">
          <el-button class="activity-page-btn" @click="goHome">首页</el-button>
          <el-pagination
            v-if="data && data.length > 0"
            background
            layout="prev, pager, next"
            class="activity-pagination"
            :page-size="pageSize"
            :current-page="pageNum"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import { getActivityList } from "@/api/purchaseSales";
import { getDicts } from "@/api/system/dict/data";
import { getExpertList } from "@/api/purchaseSales";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      fit: "cover",
      loading: false,
      form: {
        keywords: "", //搜索内容
      },
      formInfo: {
        caseType: "", // 案例类型
      },
      caseTypeList: [],
      data: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      solutionTypeList: [
        {
          value: "0",
          label: "全部",
        },
        {
          value: "1",
          label: "节能减排",
        },
        {
          value: "2",
          label: "低碳认证",
        },
        {
          value: "3",
          label: "数据核算",
        },
        {
          value: "4",
          label: "中和服务",
        },
        {
          value: "5",
          label: "星碳培训",
        },
        {
          value: "6",
          label: "绿色会议",
        },
        {
          value: "7",
          label: "数据建模",
        },
        {
          value: "8",
          label: "资产管理",
        },
      ],
      flag: "0",
      technologyTypeList: [
        {
          dictLabel: "全部",
          dictValue: 0,
        },
        {
          dictLabel: "国产化替代",
          dictValue: 1,
        },
        {
          dictLabel: "机器替人",
          dictValue: 2,
        },
        {
          dictLabel: "管理提升",
          dictValue: 3,
        },
        {
          dictLabel: "质量提升",
          dictValue: 4,
        },
        {
          dictLabel: "灭菌消杀",
          dictValue: 5,
        },
        {
          dictLabel: "新材料",
          dictValue: 6,
        },
        {
          dictLabel: "绿色星碳",
          dictValue: 7,
        },
      ],
      activeName: "全部",
    };
  },
  created() {
    this.searchExpert();
    // this.getDictsList("activity_type", "activityTypeList");
    // this.search();
  },
  methods: {
    searchExpert() {
      this.loading = true;
      getExpertList({
        keywords: this.form.name,
        techniqueTypeName: this.activeName == "全部" ? "" : this.activeName,
        pageNum: this.pageNum,
        pageSize: this.pageSize,
      })
        .then((res) => {
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));

          this.loading = false;
          let { rows, total } = res || [];
          this.data = rows;
          this.data.forEach((item) => {
            item.techniqueTypeName = item.techniqueTypeName
              ? item.techniqueTypeName.split(",")
              : [];
          });
          this.total = total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    changeRadio() {
      this.onSearch();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.searchExpert();
    },
    onSearch() {
      this.pageNum = 1;
      this.searchExpert();
    },
    goCaseDetail(id) {
      let routeData = this.$router.resolve({
        path: "/caseDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    goHome() {
      this.$router.push({ path: "/index" });
    },
    getItemData(value) {
      this.flag = value;
    },
    getType(value) {
      this.activeName = value;
      this.searchExpert();
    },
    // 跳转到专家详情页面
    goExpertLibrary(id) {
      let routeData = this.$router.resolve({
        path: "/expertDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
  },
};
</script>

<style lang="scss" scoped>
.activity-container {
  width: 100%;
  background: #ffffff;
  .activity-banner {
    width: 100%;
    height: 500px;
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .bannerTitle {
      position: absolute;
      top: 161px;
      left: 24%;
      font-size: 50px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
    }
    .bannerDesc {
      position: absolute;
      top: 249px;
      left: 24%;
      font-size: 24px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }
  }
  .activity-title-content {
    width: 1192px;
    margin: 40px auto 0;
    background-color: #fff;
    // padding-bottom: 18px;
    .activity-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .activity-title {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .activity-divider {
        width: 48px;
        height: 4px;
        background: #00b085;
      }
    }
    .activity-search-box {
      margin-top: 40px;
      .activity-search-form {
        text-align: center;
        .activity-search-input {
          width: 792px;
          height: 54px;
          .activity-search-btn {
            width: 100px;
          }
        }
      }
    }
    .classicCaseType {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 50px;
      // position: absolute;
      // bottom: -45px;
      // left: calc((100% - 1100px) / 2);
      // width: 1192px;
      // margin: 40px auto
      // height: 90px;
      // background: #ffffff;
      // box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.04);
      // border-radius: 45px;
      // justify-content: center;
      // align-items: center;
      .typeTitle {
        font-size: 18px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #333333;
        margin-right: 20px;
      }
      .caseName {
        width: 100px;
        height: 40px;
        border-radius: 20px;
        margin-left: 15px;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
        font-size: 18px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #979797;
      }
      .caseNameHover {
        background: #00b085;
        color: #ffffff;
      }
      .caseName:nth-child(1) {
        margin-left: 0;
      }
    }
  }
  .activity-page-end {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    padding: 24px 0 60px;
    .activity-page-btn {
      width: 82px;
      height: 32px;
      background: #fff;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #333;
      line-height: 10px;
    }
  }
  .none-class {
    text-align: center;
    padding: 8% 0;
    background: #fff;
    margin-top: 25px;
    .text {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 14px;
    }
  }
}
.expert-library-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  width: 100%;
  .list-item-content {
    display: flex;
    justify-content: space-between;
    width: 578px;
    background: #fff;
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);
    margin-top: 36px;
    padding: 28px 32px;
    min-height: 240px;
    .list-item-box {
      flex: 1;
      .item-headline {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .item-title {
          width: 280px;
          font-size: 32px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333;
          line-height: 32px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          word-wrap: break-word;
        }
      }
      .expert-library-label {
        display: flex;
        flex-wrap: wrap;
        margin: 0 0 16px;
        .library-label-item {
          max-width: 350px;
          padding: 6px 12px;
          background: #f4f5f9;
          border-radius: 4px;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #666;
          line-height: 12px;
          margin: 24px 16px 0 0;
          .expert-library-type {
            word-wrap: break-word;
          }
        }
      }
      .expert-library-box {
        width: 370px;
        font-size: 16px;
        font-family: PingFangSC-Regular, PingFang SC;
        color: #666;
        line-height: 32px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        word-wrap: break-word;
      }
    }
    .list-item-img {
      width: 120px;
      height: 168px;
      margin-left: 24px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    &:hover {
      cursor: pointer;
    }
  }
  .list-item-content:hover {
    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.2);
  }
}
</style>

<style lang="scss">
.activity-container {
  .activity-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #00b085;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #fff;
      line-height: 24px;
    }
  }
  .activity-search-line {
    .el-form-item__label {
      width: 88px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #999;
      padding-right: 32px;
      text-align: left;
    }
    .activity-search-radio {
      width: 1050px;
      margin-top: 11px;
      .el-radio-button {
        padding-bottom: 20px;
        .el-radio-button__inner {
          border: none;
          padding: 0 32px 0 0;
          background: none;
          &:hover {
            color: #00b085;
          }
        }
        &.is-active {
          .el-radio-button__inner {
            color: #00b085;
            background: none;
          }
        }
        .el-radio-button__orig-radio:checked {
          & + .el-radio-button__inner {
            box-shadow: unset;
          }
        }
      }
    }
  }
  .activity-page-end {
    .activity-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #00b085;
              border: 1px solid #00b085;
            }
          }
        }
      }
    }
  }
}
</style>
