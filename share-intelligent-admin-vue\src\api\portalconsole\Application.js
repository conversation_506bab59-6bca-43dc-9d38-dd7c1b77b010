import request from '@/utils/request'

// 查询系统对接列表
export function listApplication(query) {
  return request({
    url: '/portalconsole/Application/list',
    method: 'get',
    params: query
  })
}

// 查询系统对接详细
export function getApplication(applicationId) {
  return request({
    url: '/portalconsole/Application/' + applicationId,
    method: 'get'
  })
}

// 新增系统对接
export function addApplication(data) {
  return request({
    url: '/portalconsole/Application',
    method: 'post',
    data: data
  })
}

// 修改系统对接
export function updateApplication(data) {
  return request({
    url: '/portalconsole/Application',
    method: 'put',
    data: data
  })
}

// 删除系统对接
export function delApplication(applicationId) {
  return request({
    url: '/portalconsole/Application/' + applicationId,
    method: 'delete'
  })
}
