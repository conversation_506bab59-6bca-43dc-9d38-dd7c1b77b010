<template>
  <div class="activity-container">
    <div class="activity-banner">
      <img src="../../assets/appliMarket/appliMarketBanner.png" alt="" />
      <div class="bannerTitle">应用市场</div>
      <div class="bannerDesc">
        助力企业数字化低碳转型升级，提供成熟完善的数字化应用产品
      </div>
    </div>
    <div v-loading="loading">
      <div class="activity-title-content">
        <div class="activity-search-box">
          <el-form ref="form" class="activity-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.keywords"
                placeholder="请输入搜索内容"
                class="activity-search-input"
              >
                <el-button
                  slot="append"
                  class="activity-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="appliType">
        <div
          class="everyType"
          v-for="(item, index) in appliTypeData"
          :key="item.dictValue"
          @click="getappliData(item.dictLabel)"
        >
          <div class="everyImg">
            <img :src="appliTypeImgList[index].url" alt="" />
          </div>
          <div class="everyTitle">{{ item.dictLabel }}</div>
          <div class="everyIcon" v-show="flag === item.dictLabel"></div>
        </div>
      </div>
      <div class="appliContent">
        <div style="display: flex; flex-wrap: wrap">
          <div
            v-loading="loading"
            class="everyContent"
            v-for="item in appliDataList"
            :key="item.id"
            @click="goPurchaseapps(item.id)"
          >
            <div class="title">{{ item.appName }}</div>
            <div class="desc">
              {{ item.briefInto }}
            </div>
            <div
              class="tagStyle"
              v-if="item.appLabel && item.appLabel.length > 0"
            >
              <div
                class="everyTag"
                v-for="itemTag in item.appLabel.split(',')"
                :key="itemTag"
              >
                {{ itemTag }}
              </div>
            </div>
          </div>
        </div>
        <div class="activity-page-end">
          <el-button class="activity-page-btn" @click="goHome">首页</el-button>
          <el-pagination
            v-if="appliDataList && appliDataList.length > 0"
            background
            layout="prev, pager, next"
            class="activity-pagination"
            :page-size="pageSize"
            :current-page="pageNum"
            :total="total"
            @current-change="handleCurrentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { appliType, appliList } from "@/api/appliMarket";
// import { getDicts } from "@/api/system/dict/data";
// import { caseList } from "@/api/classicCase";

export default {
  data() {
    return {
      fit: "cover",
      loading: false,
      form: {
        keywords: "", //搜索内容
      },
      formInfo: {
        caseType: "", // 案例类型
      },
      caseTypeList: [],
      data: [],
      pageNum: 1,
      pageSize: 12,
      total: 0,
      flag: "全部",
      appliTypeData: [
        {
          dictValue: "0",
          dictLabel: "全部",
        },
        {
          dictValue: "1",
          dictLabel: "研发设计",
        },
        {
          dictValue: "2",
          dictLabel: "生产制造",
        },
        {
          dictValue: "3",
          dictLabel: "运营管理",
        },
        {
          dictValue: "4",
          dictLabel: "质量管控",
        },
        {
          dictValue: "5",
          dictLabel: "仓储物流",
        },
        {
          dictValue: "6",
          dictLabel: "安全生产",
        },
        {
          dictValue: "7",
          dictLabel: "节能减排",
        },
        {
          dictValue: "8",
          dictLabel: "运维服务",
        },
      ],
      appliTypeImgList: [
        {
          url: require("../../assets/appliMarket/type1.png"),
        },
        {
          url: require("../../assets/appliMarket/type2.png"),
        },
        {
          url: require("../../assets/appliMarket/type3.png"),
        },
        {
          url: require("../../assets/appliMarket/type4.png"),
        },
        {
          url: require("../../assets/appliMarket/type5.png"),
        },
        {
          url: require("../../assets/appliMarket/type6.png"),
        },
        {
          url: require("../../assets/appliMarket/type7.png"),
        },
        {
          url: require("../../assets/appliMarket/type8.png"),
        },
        {
          url: require("../../assets/appliMarket/type9.png"),
        },
      ],
      appliDataList: [
        {
          id: 1,
          title: "云端研发",
          desc: "促进产学研合作，解决信息孤岛 在创新环节实现提效降本。",
          tag: "案例详情",
        },
        {
          id: 2,
          title: "云端研发",
          desc: "促进产学研合作，解决信息孤岛 在创新环节实现提效降本。",
          tag: "案例详情",
        },
        {
          id: 3,
          title: "云端研发",
          desc: "促进产学研合作，解决信息孤岛 在创新环节实现提效降本。",
          tag: "案例详情",
        },
        {
          id: 4,
          title: "云端研发",
          desc: "促进产学研合作，解决信息孤岛 在创新环节实现提效降本。",
          tag: "案例详情",
        },
        {
          id: 5,
          title: "云端研发",
          desc: "促进产学研合作，解决信息孤岛 在创新环节实现提效降本。",
          tag: "案例详情",
        },
        {
          id: 6,
          title: "云端研发",
          desc: "促进产学研合作，解决信息孤岛 在创新环节实现提效降本。",
          tag: "案例详情",
        },
        {
          id: 7,
          title: "云端研发",
          desc: "促进产学研合作，解决信息孤岛 在创新环节实现提效降本。",
          tag: "案例详情",
        },
        {
          id: 8,
          title: "云端研发",
          desc: "促进产学研合作，解决信息孤岛 在创新环节实现提效降本。",
          tag: "案例详情",
        },
      ],
    };
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      appliType().then((res) => {
        if (res.code === 200) {
          console.log(res.data, "------------");
          this.getListData();
        }
      });
    },
    getListData() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        appName: this.form.keywords,
        appCategory: this.flag == "全部" ? undefined : this.flag,
        appState: 2,
      };
      appliList(params).then((res) => {
        if (res.code === 200) {
          console.log(res, "7777777777777777777");
          this.appliDataList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    changeRadio() {
      this.onSearch();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getListData();
    },
    onSearch() {
      this.pageNum = 1;
      this.getListData();
    },
    goCaseDetail(id) {
      let routeData = this.$router.resolve({
        path: "/caseDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    goHome() {
      this.$router.push({ path: "/index" });
    },
    getappliData(value) {
      this.flag = value;
      this.getListData();
    },
    goPurchaseapps(id) {
      let routeData = this.$router.resolve({
        path: "/purchaseapp",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
  },
};
</script>

<style lang="scss" scoped>
.activity-container {
  width: 100%;
  background: #ffffff;
  .activity-banner {
    width: 100%;
    height: 500px;
    position: relative;
    img {
      width: 100%;
      height: 100%;
    }
    .bannerTitle {
      position: absolute;
      top: 161px;
      left: 24%;
      font-size: 50px;
      font-family: Source Han Sans CN;
      font-weight: bold;
      color: #ffffff;
    }
    .bannerDesc {
      position: absolute;
      top: 249px;
      left: 24%;
      font-size: 24px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      color: #ffffff;
    }
  }
  .activity-title-content {
    width: 100%;
    background-color: #fff;
    // padding-bottom: 18px;
    .activity-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .activity-title {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .activity-divider {
        width: 48px;
        height: 4px;
        background: #21c9b8;
      }
    }
    .activity-search-box {
      margin-top: 40px;
      .activity-search-form {
        text-align: center;
        .activity-search-input {
          width: 792px;
          height: 54px;
          .activity-search-btn {
            width: 100px;
          }
        }
      }
    }
  }
  .appliType {
    width: 1200px;
    margin: 40px auto 0;
    display: flex;
    justify-content: space-between;
    .everyType {
      width: 102px;
      // height: 160px;
      text-align: center;
      cursor: pointer;
      .everyImg {
        width: 63px;
        height: 78px;
        margin-left: calc((100% - 63px) / 2);
        img {
          width: 100%;
          height: 100%;
        }
      }
      .everyTitle {
        font-size: 18px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #979797;
        margin-top: 10px;
      }
      .everyIcon {
        width: 63px;
        height: 4px;
        background: #21c9b8;
        margin-top: 10px;
        margin-left: calc((100% - 63px) / 2);
      }
    }
  }
  .appliContent {
    width: 1200px;
    // height: 500px;
    margin: 0 auto;
    .everyContent {
      width: 280px;
      height: 220px;
      background: #ffffff;
      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      padding: 30px;
      cursor: pointer;
      margin-left: 24px;
      margin-top: 20px;
      .title {
        font-size: 20px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #222222;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        text-overflow: ellipsis;
        word-wrap: break-word;
      }
      .desc {
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        color: #65676a;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        word-wrap: break-word;
        margin-top: 26px;
      }
      .tagStyle {
        display: flex;
        justify-content: space-between;
        margin-top: 30px;
        .everyTag {
          width: 90px;
          height: 42px;
          border: 1px solid #21c9b8;
          border-radius: 21px;
          font-size: 16px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #21c9b8;
          text-align: center;
          line-height: 42px;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          text-overflow: ellipsis;
          word-wrap: break-word;
        }
      }
    }
    .everyContent:hover {
      box-shadow: 0 4px 16px 0 rgba(38, 74, 116, 0.2);
      .title {
        color: #21c9b8;
      }
    }
    .everyContent:nth-child(4n + 1) {
      margin-left: 0;
    }
  }
  .none-class {
    text-align: center;
    padding: 8% 0;
    background: #fff;
    margin-top: 25px;
    .text {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 14px;
    }
  }
  .activity-page-end {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    padding: 24px 0 60px;
    .activity-page-btn {
      width: 82px;
      height: 32px;
      background: #fff;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #333;
      line-height: 10px;
    }
  }
}
</style>

<style lang="scss">
.activity-container {
  .activity-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #21c9b8;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #fff;
      line-height: 24px;
    }
  }
  .activity-search-line {
    .el-form-item__label {
      width: 88px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #999;
      padding-right: 32px;
      text-align: left;
    }
    .activity-search-radio {
      width: 1050px;
      margin-top: 11px;
      .el-radio-button {
        padding-bottom: 20px;
        .el-radio-button__inner {
          border: none;
          padding: 0 32px 0 0;
          background: none;
          &:hover {
            color: #21c9b8;
          }
        }
        &.is-active {
          .el-radio-button__inner {
            color: #21c9b8;
            background: none;
          }
        }
        .el-radio-button__orig-radio:checked {
          & + .el-radio-button__inner {
            box-shadow: unset;
          }
        }
      }
    }
  }
  .activity-page-end {
    .activity-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
}
</style>
