<template>
  <div class="content">
    <div class="card-container cardStyle">
      <!-- 左侧 -->
      <div class="card_left">
        <!-- 上半部分 -->
        <div class="card_left_top">
          <div class="imgStyle">
            <img style="width: 100%; height: 100%" :src="detailsData.images
              ? detailsData.images.split(',')[0]
              : require('@/assets/device/ceshi.png')
              " alt="" />
          </div>
          <!-- <div class="imgContent">
            <div style="cursor: pointer">
              <img src="../../../assets/device/icon_left.png" alt="" />
            </div>
            <div style="display: flex; align-items: center; margin: 0 10px">
              <div
                class="everyImgStyle"
                v-for="(item, index) in imgList"
                :key="index"
              >
                <img
                  style="width: 100%; height: 100%"
                  src="../../../assets/device/ceshi.png"
                  alt=""
                />
              </div>
            </div>
            <div style="cursor: pointer">
              <img src="../../../assets/device/icon_right.png" alt="" />
            </div>
          </div> -->
        </div>
        <!-- 下半部分 -->
        <div class="card_left_bottom">
          <div class="title">{{ detailsData.name }}</div>
          <div class="everyOption">
            <div class="optionName">所属单位：</div>
            <div class="optionValue">{{ detailsData.company }}</div>
          </div>
          <div class="everyOption">
            <div class="optionName">车间地址：</div>
            <div class="optionValue">{{ detailsData.address }}</div>
          </div>
          <div class="everyOption">
            <div class="optionName">车间面积：</div>
            <div class="optionValue">{{ detailsData.area || "--" }}</div>
          </div>
          <div class="everyOption">
            <div class="optionName">参考价格：</div>
            <div class="optionValue" style="color: #cc0a0a">
              {{ detailsData.price || "--" }}
            </div>
          </div>
          <div class="buttonStyle" @click="jumpIntention">申请试用</div>
        </div>
      </div>
      <!-- 中间 -->
      <div class="card_center_line"></div>
      <!-- 右侧 -->
      <div class="card_right">
        <div>
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">车间概况</div>
          </div>
          <div class="content_desc">
            {{ detailsData.description }}
          </div>
        </div>
        <div style="margin-top: 41px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">设备资源</div>
          </div>
          <div class="content_desc">
            {{ detailsData.resources }}
          </div>
        </div>
        <div style="margin-top: 41px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">生产能力</div>
          </div>
          <div class="content_desc">
            {{ detailsData.capability }}
          </div>
        </div>
        <div style="margin-top: 41px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">注意事项</div>
          </div>
          <div class="content_desc">
            {{ detailsData.notes }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { workDetailData } from "@/api/manufacturingSharing";

export default {
  name: "deviceDetail",
  data() {
    return {
      detailsData: {},
      imgList: [
        {
          id: 1,
        },
        {
          id: 2,
        },
        {
          id: 3,
        },
        {
          id: 4,
        },
        {
          id: 5,
        },
      ],
      id: null,
    };
  },
  created() {
    this.id = this.$route.query.id;
    this.getWorkData();
  },
  methods: {
    getWorkData() {
      workDetailData(this.id).then((res) => {
        if (res.code === 200) {
          this.detailsData = res.data;
        }
      });
    },
    jumpIntention() {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => { });
        return;
      } else {
        this.$router.push(`/demandInterested?demandName=${this.detailsData.name}&updateTime=${this.detailsData.updateTime}&intentionType=4&fieldName=车间共享&intentionId=${this.detailsData.id}`);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  background-color: #f2f2f2;
  padding: 30px 0 61px 0;
}

.cardStyle {
  height: 660px;
  background-color: #ffffff;
  padding: 60px 56px 54px 50px;
  display: flex;
}

.card_left {
  .card_left_top {
    .imgStyle {
      width: 330px;
      height: 230px;
      border-radius: 2px;
      margin-left: 10px;
    }

    .imgContent {
      margin-top: 15px;
      display: flex;
      align-items: center;

      .everyImgStyle {
        width: 54px;
        height: 50px;
        margin-left: 10px;
        cursor: pointer;
      }

      .everyImgStyle:nth-child(1) {
        margin-left: 0;
      }
    }
  }

  .card_left_bottom {
    margin-top: 30px;

    .title {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 20px;
      color: #222222;
      margin-bottom: 13px;
    }

    .everyOption {
      display: flex;
      align-items: center;
      margin-top: 12px;

      .optionName {
        min-height: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
        min-width: 75px;
      }

      .optionValue {
        min-height: 16px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }
    }

    .buttonStyle {
      margin-top: 32px;
      margin-left: 55px;
      width: 220px;
      height: 50px;
      background: #21c9b8;
      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);
      border-radius: 2px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
    }
  }
}

.card_center_line {
  width: 1px;
  height: 100%;
  background: #e1e1e1;
  margin-left: 51px;
  margin-right: 61px;
}

.card_right {
  width: 100%;
  overflow-y: auto;

  .content_title {
    display: flex;
    align-items: center;

    .icon {
      width: 4px;
      height: 20px;
      background: #21c9b8;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #030a1a;
      margin-left: 10px;
    }
  }

  .content_desc {
    // width: 631px;
    // height: 159px;
    margin-top: 20px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    line-height: 24px;
  }
}
</style>
