<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.ApplicationMapper">
    
    <resultMap type="Application" id="ApplicationResult">
        <result property="applicationId"    column="application_id"    />
        <result property="applicationIcon"    column="application_icon"    />
        <result property="applicationName"    column="application_name"    />
        <result property="applicationAppId"    column="application_app_id"    />
        <result property="applicationAppUrl"    column="application_app_url"    />
        <result property="applicationStatus"    column="application_status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectApplicationVo">
        select application_id, application_icon, application_name, application_app_id, application_app_url, application_status, del_flag, create_by, create_time, update_by, update_time, remark from application
    </sql>

    <select id="selectApplicationList" parameterType="Application" resultMap="ApplicationResult">
        <include refid="selectApplicationVo"/>
        <where>  
            <if test="applicationIcon != null  and applicationIcon != ''"> and application_icon = #{applicationIcon}</if>
            <if test="applicationName != null  and applicationName != ''"> and application_name like concat('%', #{applicationName}, '%')</if>
            <if test="applicationAppId != null  and applicationAppId != ''"> and application_app_id = #{applicationAppId}</if>
            <if test="applicationAppUrl != null  and applicationAppUrl != ''"> and application_app_url = #{applicationAppUrl}</if>
            <if test="applicationStatus != null  and applicationStatus != ''"> and application_status = #{applicationStatus}</if>
        </where>
    </select>
    
    <select id="selectApplicationByApplicationId" parameterType="Long" resultMap="ApplicationResult">
        <include refid="selectApplicationVo"/>
        where application_id = #{applicationId}
    </select>
        
    <insert id="insertApplication" parameterType="Application" useGeneratedKeys="true" keyProperty="applicationId">
        insert into application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="applicationIcon != null">application_icon,</if>
            <if test="applicationName != null">application_name,</if>
            <if test="applicationAppId != null">application_app_id,</if>
            <if test="applicationAppUrl != null">application_app_url,</if>
            <if test="applicationStatus != null">application_status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="applicationIcon != null">#{applicationIcon},</if>
            <if test="applicationName != null">#{applicationName},</if>
            <if test="applicationAppId != null">#{applicationAppId},</if>
            <if test="applicationAppUrl != null">#{applicationAppUrl},</if>
            <if test="applicationStatus != null">#{applicationStatus},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateApplication" parameterType="Application">
        update application
        <trim prefix="SET" suffixOverrides=",">
            <if test="applicationIcon != null">application_icon = #{applicationIcon},</if>
            <if test="applicationName != null">application_name = #{applicationName},</if>
            <if test="applicationAppId != null">application_app_id = #{applicationAppId},</if>
            <if test="applicationAppUrl != null">application_app_url = #{applicationAppUrl},</if>
            <if test="applicationStatus != null">application_status = #{applicationStatus},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where application_id = #{applicationId}
    </update>

    <delete id="deleteApplicationByApplicationId" parameterType="Long">
        delete from application where application_id = #{applicationId}
    </delete>

    <delete id="deleteApplicationByApplicationIds" parameterType="String">
        delete from application where application_id in 
        <foreach item="applicationId" collection="array" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
    </delete>
    
    <select id="selectApplicationByAppId" parameterType="String" resultMap="ApplicationResult">
        <include refid="selectApplicationVo"/>
        where application_app_id = #{appId}
    </select>
</mapper>