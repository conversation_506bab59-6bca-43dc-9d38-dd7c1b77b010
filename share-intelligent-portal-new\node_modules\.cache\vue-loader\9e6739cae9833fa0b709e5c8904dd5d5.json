{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\index.vue?vue&type=style&index=0&id=61c36e2c&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\index.vue", "mtime": 1750311962979}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/policy/declare", "sourcesContent": ["<template>\r\n  <div class=\"policy-declare-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"policy-declarel-banner\">\r\n      <img src=\"../../../assets/policyDeclare/policyDeclareBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"policy-declarel-title-content\">\r\n        <div class=\"policy-declarel-title-box\">\r\n          <div class=\"policy-declarel-divider\"></div>\r\n          <div class=\"policy-declarel-title\">政策申报</div>\r\n          <div class=\"policy-declarel-divider\"></div>\r\n        </div>\r\n        <div class=\"policy-declarel-search-box\">\r\n          <el-form ref=\"form\" class=\"policy-declarel-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.text\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"policy-declarel-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"policy-declarel-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"policy-declarel-card\">\r\n        <div class=\"policy-declarel-info-content\">\r\n          <div class=\"policy-declarel-search-type-box\">\r\n            <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n              <div class=\"policy-declarel-search-line\">\r\n                <el-form-item\r\n                  label=\"发布单位\"\r\n                  class=\"policy-declarel-search-line-item\"\r\n                  :class=\"{ advanced: !advancedReleaseId }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.releaseId\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in releaseIdList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.id\"\r\n                      >{{ item.name }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"policy-declarel-search-line-btn\"\r\n                  @click=\"toggleReleaseId\"\r\n                  >{{ advancedReleaseId ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n              <div class=\"policy-declarel-search-line\">\r\n                <el-form-item\r\n                  label=\"政策类型\"\r\n                  class=\"policy-declarel-search-line-item\"\r\n                  :class=\"{ advanced: !advancedType }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.type\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in typeList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"policy-declarel-search-line-btn\"\r\n                  @click=\"toggleType\"\r\n                  >{{ advancedType ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n              <div class=\"policy-declarel-search-line\">\r\n                <el-form-item\r\n                  label=\"政策状态\"\r\n                  class=\"policy-declarel-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.policyStatus\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in policyStatusList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.value\"\r\n                      >{{ item.label }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"policy-declarel-list-item\"\r\n            @click=\"goPolicyDeclarelDetail(item)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-headline\">\r\n                <div class=\"item-title\">{{ item.releaseUnitName }}</div>\r\n                <div v-if=\"item.releaseDistrict\" class=\"item-address-tag\">\r\n                  <img\r\n                    src=\"../../../assets/policyDeclare/policyAddressIcon.png\"\r\n                    alt=\"\"\r\n                    class=\"item-address-img\"\r\n                  />\r\n                  <div class=\"item-address-text\">\r\n                    {{ item.releaseDistrict }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"list-item-title\">\r\n                {{ item.title }}\r\n              </div>\r\n              <div class=\"list-item-box\">\r\n                <div class=\"list-item-time\">\r\n                  <div\r\n                    v-if=\"item.policyStatus === 2\"\r\n                    class=\"list-item-time-end\"\r\n                  >\r\n                    申报结束\r\n                  </div>\r\n                  <div v-else class=\"list-item-time-red\">\r\n                    距申报截止还有\r\n                    <div class=\"red-num\">{{ item.dayCount }}</div>\r\n                    天\r\n                  </div>\r\n                </div>\r\n                <div class=\"list-item-money\">\r\n                  <div class=\"list-item-money-title\">最高奖励</div>\r\n                  <span class=\"list-item-money-num\">{{ item.maxReward }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"list-item-status\">\r\n                <!-- 1进行中  2已截止 -->\r\n                <img\r\n                  v-if=\"item.policyStatus === 1\"\r\n                  src=\"../../../assets/policyDeclare/carryOnIcon.png\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../assets/policyDeclare/cutoffIcon.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"policy-declarel-page-end\">\r\n            <el-button class=\"policy-declarel-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            >\r\n            <el-pagination\r\n              v-if=\"data && data.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"policy-declarel-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @size-change=\"handleSizeChange\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getPolicyDeclareList, getListByName } from \"@/api/policyDeclare\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { POLICY_STATUS } from \"@/const/status\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        text: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        releaseId: \"\", //发布单位\r\n        type: \"\", //政策类型\r\n        policyStatus: \"\", //政策状态\r\n        labelCodeList: [], //政策画像code集合\r\n      },\r\n      releaseIdList: [], //发布单位下拉列表\r\n      typeList: [], //政策类型下拉列表\r\n      policyStatusList: POLICY_STATUS,\r\n      advancedReleaseId: false,\r\n      advancedType: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    const { code } = this.$route.params || {};\r\n    console.log(this.$route);\r\n    if (code) {\r\n      this.formInfo.labelCodeList = code;\r\n    }\r\n    this.getDictsList(\"policy_type\", \"typeList\");\r\n    this.getListByName();\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getPolicyDeclareList({\r\n        text: this.form.text,\r\n        type: this.formInfo.type,\r\n        releaseId: this.formInfo.releaseId,\r\n        policyStatus: this.formInfo.policyStatus,\r\n        labelCodeList: this.formInfo.labelCodeList,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 查询发布单位\r\n    getListByName() {\r\n      getListByName().then((res) => {\r\n        this.releaseIdList = res.data || [];\r\n      });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    // 显示更多发布单位\r\n    toggleReleaseId() {\r\n      this.advancedReleaseId = !this.advancedReleaseId;\r\n    },\r\n    // 显示更多政策类型\r\n    toggleType() {\r\n      this.advancedType = !this.advancedType;\r\n    },\r\n    changeRadio() {\r\n      console.log(typeof this.formInfo.releaseId, \"0000\");\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n    // 跳转到政策详情页面\r\n    goPolicyDeclarelDetail(item) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/policyDeclareDetail\",\r\n        query: { id: item.id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n  watch: {},\r\n  computed: {},\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.policy-declare-container {\r\n  width: 100%;\r\n  .policy-declarel-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .policy-declarel-title-content {\r\n    width: 100%;\r\n    padding-bottom: 18px;\r\n    .policy-declarel-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .policy-declarel-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .policy-declarel-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .policy-declarel-search-box {\r\n      .policy-declarel-search-form {\r\n        text-align: center;\r\n        .policy-declarel-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .policy-declarel-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .policy-declarel-card {\r\n    background: #f4f5f9;\r\n    padding-top: 40px;\r\n    .policy-declarel-info-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      .policy-declarel-search-type-box {\r\n        background: #fff;\r\n        margin-bottom: 17px;\r\n        .policy-declarel-search-line {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 14px 24px 4px;\r\n          .policy-declarel-search-line-item {\r\n            flex: 1;\r\n            margin-bottom: 0;\r\n            display: flex;\r\n            &.advanced {\r\n              overflow: hidden;\r\n              height: 45px;\r\n            }\r\n            .more-radio {\r\n              margin-top: 11px;\r\n              flex: 1;\r\n            }\r\n          }\r\n          .policy-declarel-search-line-btn {\r\n            display: inline-block;\r\n            width: 64px;\r\n            height: 24px;\r\n            background: #fff;\r\n            border-radius: 2px;\r\n            border: 1px solid #d9d9d9;\r\n            font-size: 12px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            color: #333;\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 0 16px;\r\n            margin-top: 5px;\r\n            &:hover {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n          & + .policy-declarel-search-line {\r\n            border-top: 1px solid #f5f5f5;\r\n          }\r\n        }\r\n      }\r\n      .policy-declarel-list-item {\r\n        position: relative;\r\n        width: 100%;\r\n        background: #fff;\r\n        border-radius: 12px;\r\n        .list-item-content {\r\n          padding: 27px 24px 24px 24px;\r\n          cursor: pointer;\r\n          .list-item-headline {\r\n            display: flex;\r\n            align-items: center;\r\n            .item-title {\r\n              max-width: 570px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #999;\r\n              line-height: 18px;\r\n              word-break: break-all;\r\n            }\r\n            .item-address-tag {\r\n              display: flex;\r\n              align-items: center;\r\n              border-radius: 6px;\r\n              border: 2px solid #ff8516;\r\n              font-size: 15px;\r\n              line-height: 15px;\r\n              text-align: center;\r\n              margin-left: 12px;\r\n              color: #ff8516;\r\n              .item-address-img {\r\n                width: 19px;\r\n                height: 18px;\r\n                margin-right: 1px;\r\n              }\r\n              .item-address-text {\r\n                max-width: 570px;\r\n                word-break: break-all;\r\n                padding: 3px 5px 2px 0;\r\n              }\r\n            }\r\n          }\r\n          .list-item-title {\r\n            font-size: 24px;\r\n            font-family: PingFangSC-Medium, PingFang SC;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            line-height: 36px;\r\n            word-break: break-all;\r\n            padding-top: 18px;\r\n          }\r\n          .list-item-box {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            margin-top: 20px;\r\n            .list-item-time {\r\n              background: #f5f5f5;\r\n              border-radius: 6px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #999;\r\n              line-height: 18px;\r\n              .list-item-time-end {\r\n                padding: 6px 12px;\r\n              }\r\n              .list-item-time-red {\r\n                display: flex;\r\n                align-items: center;\r\n                padding: 6px 15px 6px 12px;\r\n                .red-num {\r\n                  max-width: 270px;\r\n                  font-size: 24px;\r\n                  font-family: PingFangSC-Medium, PingFang SC;\r\n                  font-weight: 500;\r\n                  color: #cf4140;\r\n                  line-height: 24px;\r\n                  word-wrap: break-word;\r\n                }\r\n              }\r\n            }\r\n            .list-item-money {\r\n              display: flex;\r\n              align-items: flex-end;\r\n              max-width: 570px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              .list-item-money-title {\r\n                font-size: 18px;\r\n                color: #999;\r\n                line-height: 18px;\r\n                margin-right: 6px;\r\n              }\r\n              .list-item-money-num {\r\n                max-width: 270px;\r\n                font-size: 36px;\r\n                font-weight: 500;\r\n                color: #cf4140;\r\n                line-height: 36px;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n          }\r\n        }\r\n        & + .policy-declarel-list-item {\r\n          margin-top: 24px;\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        .list-item-status {\r\n          position: absolute;\r\n          top: 0;\r\n          right: 0;\r\n          img {\r\n            width: 92px;\r\n            height: 71px;\r\n          }\r\n        }\r\n      }\r\n      .policy-declarel-page-end {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        padding: 24px 0 60px;\r\n        .policy-declarel-page-btn {\r\n          width: 82px;\r\n          height: 32px;\r\n          background: #fff;\r\n          border-radius: 4px;\r\n          border: 1px solid #d9d9d9;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.policy-declare-container {\r\n  .policy-declarel-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .el-form-item__label {\r\n    width: 88px;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #999;\r\n    padding-right: 32px;\r\n    text-align: left;\r\n  }\r\n  .policy-declarel-search-line {\r\n    .el-form-item__content {\r\n      width: 970px;\r\n    }\r\n  }\r\n  .el-radio-button {\r\n    padding-bottom: 20px;\r\n    .el-radio-button__inner {\r\n      border: none;\r\n      padding: 0 32px 0 0;\r\n      background: none;\r\n      &:hover {\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        color: #21c9b8;\r\n        background: none;\r\n      }\r\n    }\r\n    .el-radio-button__orig-radio:checked {\r\n      & + .el-radio-button__inner {\r\n        box-shadow: unset;\r\n      }\r\n    }\r\n  }\r\n  .policy-declarel-page-end {\r\n    .policy-declarel-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}