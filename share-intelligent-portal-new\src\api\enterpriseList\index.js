/*
 * @Author: jhy
 * @Date: 2023-01-30 17:58:37
 * @LastEditors: JHY
 * @LastEditTime: 2023-12-09 20:50:00
 */
import request from "@/utils/request";

// 企业详情
export function getCompanyDetail(params) {
  return request({
    url: "/system/company/mag/detailShow",
    method: "get",
    params,
  });
}

// 企业需求
export function getDemandList(params) {
  return request({
    url: "/system/demand/secret/list",
    method: "get",
    params,
  });
}

// 需求大厅
export function gatEwayList(params) {
  return request({
    url: "/system/demand/secret/gatewayList",
    method: "get",
    params,
  });
}

// 企业产品
export function getSupplyList(params) {
  return request({
    url: "/system/supply/secret/list",
    method: "get",
    params,
  });
}
