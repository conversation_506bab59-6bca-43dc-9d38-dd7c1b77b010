{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\mySubscriptions\\detail.vue?vue&type=style&index=0&id=0dc7f6d4&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\mySubscriptions\\detail.vue", "mtime": 1750311963062}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogI2Y0ZjVmOTsNCn0NCi5jb250ZW50IHsNCiAgd2lkdGg6IDEwMCU7DQogIHBhZGRpbmc6IDQwcHg7DQogIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIC5jb250ZW50X3RvcCB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBoZWlnaHQ6IDEyMHB4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgLm9yZGVyU3RhdHVzIHsNCiAgICAgIHdpZHRoOiAzMCU7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAuc3RhdHVzTmFtZSB7DQogICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICAgIH0NCiAgICAgIC5kZXNjIHsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBjb2xvcjogcmdiKDE3MywgMTczLCAxNzMpOw0KICAgICAgICBtYXJnaW4tdG9wOiAzMHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgICAuYW1vdW50TW9uZXkgew0KICAgICAgd2lkdGg6IDQwJTsNCiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICB9DQogICAgLmJ1dHRvbl9jb250ZW50IHsNCiAgICAgIHdpZHRoOiAyMCU7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAuYnV0dG9uU3R5bGUgew0KICAgICAgICBoZWlnaHQ6IDUwcHg7DQogICAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICAgIGxpbmUtaGVpZ2h0OiA1MHB4Ow0KICAgICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICAuY29udGVudF9ib3R0b20gew0KICAgIG1hcmdpbi10b3A6IDIwcHg7DQogICAgcGFkZGluZzogMjBweDsNCiAgICB3aWR0aDogMTAwJTsNCiAgfQ0KICAuYnRuU3R5bGUgew0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/system/user/mySubscriptions", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\">\r\n          <div class=\"content_top\">\r\n            <div class=\"orderStatus\">\r\n              <div class=\"statusName\" v-if=\"info.orderStatus\">\r\n                {{\r\n                  orderForm.filter((item) => item.value == info.orderStatus)[0]\r\n                    .statusName\r\n                }}\r\n              </div>\r\n              <div class=\"desc\" v-if=\"info.orderStatus\">\r\n                {{\r\n                  orderForm.filter((item) => item.value == info.orderStatus)[0]\r\n                    .desc\r\n                }}\r\n              </div>\r\n            </div>\r\n            <div class=\"amountMoney\">\r\n              <span style=\"color: rgb(173, 173, 173)\">订单金额:</span>\r\n              <span style=\"margin-left: 10px\">¥ {{ info.price }}</span>\r\n            </div>\r\n            <!-- 待支付 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 1\">\r\n              <div>\r\n                <div class=\"buttonStyle\">去支付</div>\r\n                <div style=\"margin-top: 10px\">\r\n                  <span\r\n                    style=\"color: #21c9b8; cursor: pointer\"\r\n                    @click=\"cancelOrder(info.id)\"\r\n                    >取消订单</span\r\n                  >\r\n                  <span\r\n                    style=\"margin-left: 20px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"tryout(info)\"\r\n                    >前往试用</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 待发货 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 2\">\r\n              <div>\r\n                <div>\r\n                  <span\r\n                    style=\"margin-left: 20px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"tryout(info)\"\r\n                    >前往试用</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 已发货 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 4\">\r\n              <div>\r\n                <div>\r\n                  <span\r\n                    style=\"color: #21c9b8; cursor: pointer\"\r\n                    @click=\"applyInvoice(info.id)\"\r\n                    >申请开票</span\r\n                  >\r\n                  <span\r\n                    style=\"margin-left: 20px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"confirmReceipt(info.id)\"\r\n                    >确认收货</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 已成交 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 5\">\r\n              <div>\r\n                <div>\r\n                  <span style=\"color: #21c9b8; cursor: pointer\">已开票</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 待续费 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 6\">\r\n              <div>\r\n                <div>\r\n                  <span style=\"color: #21c9b8; cursor: pointer\">去支付</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"content_bottom\">\r\n            <div>\r\n              <el-descriptions title=\"订单信息\" :column=\"2\">\r\n                <el-descriptions-item label=\"订单编号\">{{\r\n                  info.id\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"下单时间\">{{\r\n                  info.orderDate\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"应用提供\">{{\r\n                  info.supply\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"付款时间\">\r\n                  <el-tag size=\"small\">{{\r\n                    info.payTime ? parseTime(info.payTime) : \"--\"\r\n                  }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"应用编号\">{{\r\n                  info.appCode\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"发货时间\">{{\r\n                  info.deliverTime\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"付款方式\">{{\r\n                  info.payWay\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"成交时间\">{{\r\n                  info.makeTime\r\n                }}</el-descriptions-item>\r\n              </el-descriptions>\r\n            </div>\r\n            <div style=\"margin-top: 30px\">\r\n              <el-table :data=\"tableData\" style=\"width: 100%\">\r\n                <el-table-column prop=\"remark\" label=\"产品标题\">\r\n                </el-table-column>\r\n                <el-table-column label=\"产品图片\">\r\n                  <template slot-scope=\"scope\">\r\n                    <img\r\n                      style=\"width: 100px; height: 100px\"\r\n                      :src=\"scope.row.appLogo\"\r\n                      alt=\"\"\r\n                    />\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column prop=\"appCategory\" label=\"产品类型\">\r\n                </el-table-column>\r\n                <el-table-column label=\"规格信息\">\r\n                  <template slot-scope=\"scoped\">\r\n                    <!-- <div>规格: {{ scoped.row.spec }}</div> -->\r\n                    <div>\r\n                      可用时长:\r\n                      {{ scoped.row.validTime == \"1\" ? \"一年\" : \"永久\" }}\r\n                    </div>\r\n                    <div>可用人数: 不限</div>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"有效时间\">\r\n                  <template slot-scope=\"scope\">\r\n                    {{\r\n                      scope.row.expirationTime\r\n                        ? parseTime(scope.row.expirationTime)\r\n                        : \"--\"\r\n                    }}\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </div>\r\n          <div class=\"btnStyle\">\r\n            <el-button @click=\"goBack\">返回</el-button>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      title=\"开票信息\"\r\n      :visible.sync=\"invoiceVisible\"\r\n      width=\"750px\"\r\n      append-to-body\r\n    >\r\n      <el-form :model=\"invoiceData\" label-width=\"80px\">\r\n        <el-form-item label=\"发票类型:\" prop=\"realName\">\r\n          {{ invoiceData.invoiceType == 1 ? \"专票\" : \"普票\" }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司名称:\" prop=\"phonenumber\">\r\n          {{ invoiceData.companyName }}\r\n        </el-form-item>\r\n        <el-form-item label=\"税号:\" prop=\"weixin\">\r\n          {{ invoiceData.dutyParagraph }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司地址:\" prop=\"email\">\r\n          {{ invoiceData.address }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司电话:\" prop=\"email\">\r\n          {{ invoiceData.phone }}\r\n        </el-form-item>\r\n        <el-form-item label=\"开户银行:\" prop=\"email\">\r\n          {{ invoiceData.openAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"银行账号:\" prop=\"email\">\r\n          {{ invoiceData.bankAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱地址:\" prop=\"email\">\r\n          {{ invoiceData.email }}\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  orderDetail,\r\n  cancelOrder,\r\n  invoiceList,\r\n  applyInvoice,\r\n  modifyStatus,\r\n} from \"@/api/system/user\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\n// import { getToken } from \"@/utils/auth\";\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      flag: \"0\",\r\n      tableData: [],\r\n      info: {},\r\n      invoiceData: {},\r\n      orderForm: [\r\n        {\r\n          value: 1,\r\n          statusName: \"待支付\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 2,\r\n          statusName: \"待发货\",\r\n          desc: \"平台将于2023-08-04日前发货，感谢您的支持!如您对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 3,\r\n          statusName: \"支付失败\",\r\n          desc: \"订单支付失败，如您对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 4,\r\n          statusName: \"已发货\",\r\n          desc: \"使用过程中有任何问题，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 5,\r\n          statusName: \"已成交\",\r\n          desc: \"使用过程中有任何问题，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 6,\r\n          statusName: \"待续费\",\r\n          desc: \"请尽快续费，以免影响正常使用\",\r\n        },\r\n        {\r\n          value: 7,\r\n          statusName: \"已关闭\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 8,\r\n          statusName: \"支付中\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 9,\r\n          statusName: \"已取消\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n      ],\r\n      invoiceVisible: false,\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      let id = this.$route.query.id;\r\n      orderDetail(id).then((res) => {\r\n        this.loading = false;\r\n        this.tableData = [];\r\n        if (res.code === 200) {\r\n          this.info = res.data;\r\n          this.tableData.push(res.data);\r\n        }\r\n      });\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    tryout(item) {\r\n      console.log(item);\r\n      if (item.appName == \"云端研发\") {\r\n        let url;\r\n        let hostname;\r\n        var result;\r\n        hostname = \" https://yunduanyanfa.ningmengdou.com/login \";\r\n        result = encodeURIComponent(hostname);\r\n        url = \"https://sso.ningmengdou.com/single/login?returnUrl=\" + result;\r\n        window.open(url, \"_blank\");\r\n      } else if (item.appName == \"檬豆云供应链管理系统\") {\r\n      } else if (item.appName == \"集采平台\") {\r\n        window.open(\"https://mdy.ningmengdou.com\");\r\n      } else if (item.appName == \"云MES\") {\r\n        let userid = \"18660283726\";\r\n        console.log(userid);\r\n        let jsonData = { U: userid, P: \"12a\", A: \"acb\" };\r\n        console.log(jsonData);\r\n        const encodedData = btoa(JSON.stringify(jsonData));\r\n        console.log(encodedData);\r\n        window.open(\r\n          \"http://mes.ningmengdou.com/default.html?parm=\" + encodedData,\r\n          \"_blank\"\r\n        );\r\n      } else {\r\n        window.open(\"//\" + item.webexperienceUrl, \"_blank\");\r\n      }\r\n    },\r\n    cancelOrder(id) {\r\n      this.$confirm(\"订单取消后无法恢复，请谨慎操作!\", \"取消订单\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          cancelOrder(id).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.$router.go(-1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    applyInvoice() {\r\n      this.getInvoiceData();\r\n    },\r\n    getInvoiceData() {\r\n      invoiceList().then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceData = res.data;\r\n          this.invoiceVisible = true;\r\n        }\r\n      });\r\n    },\r\n    confirmReceipt(id) {\r\n      this.$confirm(\"确认后订单状态无法变更，确认收货吗？\", \"确认收货\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          let data = {\r\n            id,\r\n            orderStatus: 5,\r\n          };\r\n          modifyStatus(data).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    submitForm() {\r\n      let data = {\r\n        invoiceMedium: \"1\",\r\n        invoiceType: \"1\",\r\n        issueType: \"1\",\r\n        invoiceHeader: this.invoiceData.companyName,\r\n        dutyParagraph: this.invoiceData.dutyParagraph,\r\n        email: this.invoiceData.email,\r\n        orderId: this.info.id,\r\n        sendTo: this.invoiceData.userId,\r\n      };\r\n      applyInvoice(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceVisible = false;\r\n          this.$message.success(\"操作成功!\");\r\n        }\r\n      });\r\n    },\r\n    cancelDialog() {\r\n      this.invoiceVisible = false;\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  .content_top {\r\n    display: flex;\r\n    width: 100%;\r\n    height: 120px;\r\n    align-items: center;\r\n    .orderStatus {\r\n      width: 30%;\r\n      text-align: center;\r\n      .statusName {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n      }\r\n      .desc {\r\n        font-size: 14px;\r\n        color: rgb(173, 173, 173);\r\n        margin-top: 30px;\r\n      }\r\n    }\r\n    .amountMoney {\r\n      width: 40%;\r\n      text-align: center;\r\n    }\r\n    .button_content {\r\n      width: 20%;\r\n      text-align: center;\r\n      .buttonStyle {\r\n        height: 50px;\r\n        background: #21c9b8;\r\n        line-height: 50px;\r\n        color: #ffffff;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n  .content_bottom {\r\n    margin-top: 20px;\r\n    padding: 20px;\r\n    width: 100%;\r\n  }\r\n  .btnStyle {\r\n    text-align: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}