package com.ruoyi.common.security.sso;

import com.ruoyi.common.core.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * SSO安全过滤器
 * 负责验证SSO令牌和权限
 * 
 * <AUTHOR>
 */
@Component
public class SSOSecurityFilter extends OncePerRequestFilter {
    
    private static final Logger log = LoggerFactory.getLogger(SSOSecurityFilter.class);
    
    @Autowired
    private SSOClient ssoClient;
    
    // 不需要SSO验证的路径
    private static final List<String> EXCLUDE_PATHS = Arrays.asList(
        "/sso/",
        "/login",
        "/logout",
        "/error",
        "/favicon.ico",
        "/static/",
        "/css/",
        "/js/",
        "/images/",
        "/fonts/",
        "/actuator/",
        "/swagger-ui/",
        "/v3/api-docs",
        "/doc.html"
    );
    
    // SSO用户信息在Session中的键名
    private static final String SSO_USER_SESSION_KEY = "SSO_USER_INFO";
    private static final String SSO_TOKEN_SESSION_KEY = "SSO_ACCESS_TOKEN";
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        String requestURI = request.getRequestURI();
        
        // 检查是否需要SSO验证
        if (shouldExclude(requestURI)) {
            filterChain.doFilter(request, response);
            return;
        }
        
        HttpSession session = request.getSession();
        SSOUserInfo ssoUserInfo = (SSOUserInfo) session.getAttribute(SSO_USER_SESSION_KEY);
        String accessToken = (String) session.getAttribute(SSO_TOKEN_SESSION_KEY);
        
        // 如果Session中没有用户信息，尝试从请求头获取Token
        if (ssoUserInfo == null || StringUtils.isEmpty(accessToken)) {
            accessToken = extractTokenFromRequest(request);
            
            if (StringUtils.isNotEmpty(accessToken)) {
                // 验证Token并获取用户信息
                Map<String, Object> userInfo = ssoClient.getUserInfo(accessToken);
                if (userInfo != null) {
                    ssoUserInfo = new SSOUserInfo(userInfo);
                    ssoUserInfo.setAccessToken(accessToken);
                    
                    // 存储到Session
                    session.setAttribute(SSO_USER_SESSION_KEY, ssoUserInfo);
                    session.setAttribute(SSO_TOKEN_SESSION_KEY, accessToken);
                    
                    log.debug("SSO用户验证成功: {}", ssoUserInfo.getUsername());
                } else {
                    log.warn("SSO Token验证失败: {}", accessToken);
                }
            }
        }
        
        // 如果仍然没有用户信息，重定向到SSO登录
        if (ssoUserInfo == null) {
            handleUnauthorized(request, response);
            return;
        }
        
        // 检查用户权限
        if (!checkPermission(ssoUserInfo, requestURI)) {
            handleForbidden(request, response);
            return;
        }
        
        // 将用户信息设置到请求属性中，供后续使用
        request.setAttribute("ssoUserInfo", ssoUserInfo);
        
        filterChain.doFilter(request, response);
    }
    
    /**
     * 检查路径是否需要排除SSO验证
     */
    private boolean shouldExclude(String requestURI) {
        return EXCLUDE_PATHS.stream().anyMatch(requestURI::startsWith);
    }
    
    /**
     * 从请求中提取访问令牌
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        // 从Authorization头获取
        String authHeader = request.getHeader("Authorization");
        if (StringUtils.isNotEmpty(authHeader) && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }
        
        // 从请求参数获取
        String tokenParam = request.getParameter("access_token");
        if (StringUtils.isNotEmpty(tokenParam)) {
            return tokenParam;
        }
        
        return null;
    }
    
    /**
     * 检查用户权限
     */
    private boolean checkPermission(SSOUserInfo userInfo, String requestURI) {
        // 如果用户没有权限，拒绝访问
        if (!Boolean.TRUE.equals(userInfo.getHasPermission())) {
            return false;
        }
        
        // 管理员用户拥有所有权限
        if (userInfo.hasRole("admin")) {
            return true;
        }
        
        // 根据请求路径检查具体权限
        // 这里可以根据实际业务需求进行权限控制
        // 例如：
        if (requestURI.startsWith("/system/user")) {
            return userInfo.hasAnyPermission("system:user:list", "system:user:view");
        } else if (requestURI.startsWith("/system/role")) {
            return userInfo.hasAnyPermission("system:role:list", "system:role:view");
        } else if (requestURI.startsWith("/system/menu")) {
            return userInfo.hasAnyPermission("system:menu:list", "system:menu:view");
        }
        
        // 默认允许访问
        return true;
    }
    
    /**
     * 处理未认证请求
     */
    private void handleUnauthorized(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        String requestURI = request.getRequestURI();
        
        // 如果是AJAX请求，返回JSON响应
        if (isAjaxRequest(request)) {
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":401,\"msg\":\"未登录或登录已过期\",\"data\":null}");
        } else {
            // 重定向到SSO登录页面
            String loginUrl = ssoClient.getLoginUrl(requestURI);
            response.sendRedirect(loginUrl);
        }
    }
    
    /**
     * 处理权限不足请求
     */
    private void handleForbidden(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        // 如果是AJAX请求，返回JSON响应
        if (isAjaxRequest(request)) {
            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":403,\"msg\":\"权限不足\",\"data\":null}");
        } else {
            // 重定向到权限不足页面
            response.sendRedirect("/error/403");
        }
    }
    
    /**
     * 判断是否为AJAX请求
     */
    private boolean isAjaxRequest(HttpServletRequest request) {
        String requestedWith = request.getHeader("X-Requested-With");
        String accept = request.getHeader("Accept");
        String contentType = request.getContentType();
        
        return "XMLHttpRequest".equals(requestedWith) ||
               (accept != null && accept.contains("application/json")) ||
               (contentType != null && contentType.contains("application/json"));
    }
}
