{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\companyTab.vue?vue&type=style&index=0&id=7413bd30&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\companyTab.vue", "mtime": 1750311962929}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["companyTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "companyTab.vue", "sourceRoot": "src/views/components/home/<USER>", "sourcesContent": ["<template>\r\n  <div class=\"company-tab-container\">\r\n    <div v-loading=\"loading\" class=\"tab-main\">\r\n      <el-scrollbar noresize class=\"left\">\r\n        <div class=\"tab-content\">\r\n          <div\r\n            v-for=\"(item, index) in tabs\"\r\n            :key=\"index\"\r\n            :class=\"{ active: tabIndex === index }\"\r\n            class=\"tab-content-item\"\r\n            @click=\"onTabChange(index)\"\r\n          >\r\n            {{ item.dictLabel }}\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n      <el-row class=\"right\" :gutter=\"24\">\r\n        <template v-if=\"items.length > 0\">\r\n          <el-col :span=\"8\" v-for=\"item in items\" :key=\"item.id\">\r\n            <router-link\r\n              target=\"_blank\"\r\n              :to=\"`/enterpriseDetail?id=${item.id}&businessNo=${item.businessNo}`\"\r\n            >\r\n              <div class=\"card\">\r\n                <el-image\r\n                  class=\"card-img\"\r\n                  :src=\"item.url ? item.url : defaultUrl\"\r\n                  fit=\"fill\"\r\n                />\r\n                <div class=\"card-footer\">\r\n                  <div class=\"title\" :title=\"item.company\">\r\n                    {{ item.company }}\r\n                  </div>\r\n                  <div class=\"tag\">{{ item.tag }}</div>\r\n                </div>\r\n              </div>\r\n            </router-link>\r\n          </el-col>\r\n        </template>\r\n        <template v-else>\r\n          <el-empty />\r\n        </template>\r\n      </el-row>\r\n    </div>\r\n    <div class=\"tab-page-end\">\r\n      <!-- <span class=\"demonstration\">完整功能</span> -->\r\n      <el-pagination\r\n        class=\"company-tab-pagination\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"pageNum\"\r\n        :page-sizes=\"[100, 200, 300, 400]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\" prev, pager, next \"\r\n        :total=\"total\"\r\n      >\r\n      </el-pagination>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { head, map } from \"ramda\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { listCompany } from \"@/api/zhm\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  name: \"CompanyTab\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tabs: [],\r\n      tabIndex: 0,\r\n      items: [],\r\n      pageNum: 1,\r\n      pageSize: 6,\r\n      total: 0,\r\n      defaultUrl: require(\"../../../../assets/purchaseSales/companyDefault.png\"),\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      getDicts(\"industrial_chain\").then((res) => {\r\n        const { code, data = [] } = res;\r\n        if (code === 200) {\r\n          this.tabs = data;\r\n          this.tabs.unshift({\r\n            dictLabel: \"全部\",\r\n            dictValue: undefined,\r\n          });\r\n          const item = head(data);\r\n          this.getCompanyData(item.dictValue);\r\n        }\r\n      });\r\n    },\r\n    getCompanyData(type) {\r\n      this.loading = true;\r\n      listCompany({\r\n        industrialChain: type,\r\n        recommendStatus: 1,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          const { code, rows = [] } = res;\r\n          if (code === 200) {\r\n            this.items = map((item) => {\r\n              let url;\r\n              const images = item.companyPictureList || [];\r\n              if (images.length > 0) {\r\n                url = head(images).url;\r\n              }\r\n              return {\r\n                id: item.id,\r\n                company: item.name,\r\n                businessNo: item.businessNo,\r\n                tag: item.category,\r\n                url,\r\n              };\r\n            }, rows);\r\n          }\r\n          this.total = res.total;\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    onTabChange(index) {\r\n      if (index !== this.tabIndex) {\r\n        this.tabIndex = index;\r\n        this.pageNum = 1;\r\n        const item = this.tabs[index] || {};\r\n        this.getCompanyData(item.dictValue);\r\n      }\r\n    },\r\n    handleSizeChange(newSize) {\r\n      // console.log(`每页 ${val} 条`);\r\n      this.pageSize = newSize;\r\n      const item = this.tabs[this.tabIndex] || {};\r\n\r\n      this.getCompanyData(item.dictValue);\r\n    },\r\n    handleCurrentChange(newPage) {\r\n      // console.log(`当前页: ${val}`);\r\n      this.pageNum = newPage;\r\n      const item = this.tabs[this.tabIndex] || {};\r\n      this.getCompanyData(item.dictValue);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n.company-tab-container {\r\n  .tab-main {\r\n    position: relative;\r\n    display: flex;\r\n    flex-shrink: 0;\r\n    width: 100%;\r\n    flex-direction: row;\r\n    ::v-deep .el-scrollbar__wrap {\r\n      overflow-x: hidden;\r\n      overflow-y: auto;\r\n    }\r\n    .left {\r\n      width: 148px;\r\n      height: 580px;\r\n      background: #21c9b8;\r\n      .el-scrollbar__wrap {\r\n        height: 103%;\r\n      }\r\n      // ::-webkit-scrollbar-track-piece {\r\n      //   background-color: #21C9B8 !important;\r\n      // }\r\n      .tab-content {\r\n        padding: 24px 0 24px 18px;\r\n        &-item {\r\n          @include ellipsis;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          flex-shrink: 0;\r\n          height: 40px;\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #ffffff;\r\n          line-height: 14px;\r\n          transition: background, color 0.25ms ease;\r\n          margin-bottom: 12px;\r\n          cursor: pointer;\r\n          &.active {\r\n            color: #21c9b8;\r\n            background: linear-gradient(270deg, #fbfdff 0%, #ffffff 100%);\r\n          }\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .right {\r\n      flex: 1;\r\n      padding-left: 36px;\r\n      .card {\r\n        width: 100%;\r\n        min-height: 300px;\r\n        background: #ffffff;\r\n        box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.1);\r\n        margin-bottom: 24px;\r\n        &-img {\r\n          width: 100%;\r\n          height: 200px;\r\n          background: #ffffff;\r\n        }\r\n        &-footer {\r\n          padding: 16px 20px;\r\n          .title {\r\n            @include ellipsis;\r\n            // @include multiEllipsis(2);\r\n            font-size: 18px;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            line-height: 26px;\r\n            margin-bottom: 10px;\r\n          }\r\n          .tag {\r\n            display: inline-block;\r\n            background: rgba(197, 37, 33, 0.1);\r\n            border-radius: 4px;\r\n            padding: 6px 10px;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #21c9b8;\r\n            line-height: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.company-tab-container {\r\n  .tab-page-end {\r\n    .company-tab-pagination {\r\n      width: 220px;\r\n      margin: 0 auto;\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #ffffff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        margin: 0 6px;\r\n        color: #333;\r\n      }\r\n      .el-pager {\r\n        .number {\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 1px solid #d9d9d9;\r\n          background: #ffffff;\r\n          border-radius: 4px;\r\n          line-height: 32px;\r\n          margin: 0 6px;\r\n          &.active {\r\n            background: #21c9b8;\r\n            border: 1px solid #21c9b8;\r\n            color: #fff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}