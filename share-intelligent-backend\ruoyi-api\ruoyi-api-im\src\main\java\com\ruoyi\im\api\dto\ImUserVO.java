package com.ruoyi.im.api.dto;/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api.dto
 * @ClassName: ImUserVO
 * @Author: ${maguojun}
 * @Description:
 * @Date: 2022/3/17 18:03
 * @Version: 1.0
 */

import com.ruoyi.im.api.domain.ImUser;
import com.ruoyi.im.api.domain.ImUserApply;
import lombok.Data;

/**
 * @program: ruoyi
 *
 * @description:
 *
 * @author: Ma<PERSON><PERSON><PERSON><PERSON>
 *
 * @create: 2022-03-17 18:03
 **/
@Data
public class ImUserVO  {

    private ImUser imUser;

    private int identification;

    private Long friendsId;

    private String remark;//备注

}
