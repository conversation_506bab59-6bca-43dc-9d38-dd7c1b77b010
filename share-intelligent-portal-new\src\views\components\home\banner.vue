<template>
  <div class="banner">
    <div class="banner-body">
      <div class="banner_image">
        <img class="scaleDrawimg" src="../../../assets/images/home/<USER>" alt="" />
        <div class="banner_box">
          <div class="banerText">
            <div class="bannerTitle">易复材共享智造工业互联网平台</div>
            <div class="bannerDesc">
              易复材共享智造工业互联网平台以“4+1+1”模式为核心，通过制造、集采、服务、创新四大共享，整合资源协同生产；依托共享示范工厂推动高端应用落地，以数字化底座赋能设备互联与智能管理，助力复合材料产业向集约化、智能化、绿色化转型升级。
            </div>
          </div>
          <img class="banner_logo" src="@/assets/images/home/<USER>" alt="" />
        </div>
        <!-- <div class="amounts">
          <div class="every_item">
            <div class="every_img">
              <img src="../../../assets/images/home/<USER>" alt="" />
            </div>
            <div style="margin-left: 10px">
              <div class="amountsTotal">
                {{ item.zczx || "0" }}
              </div>
              <div class="amountsName">政策</div>
            </div>
            <div style="margin-top: 38px; margin-left: 10px">条</div>
          </div>
          <div class="every_item">
            <div class="every_img">
              <img src="../../../assets/images/home/<USER>" alt="" />
            </div>
            <div style="margin-left: 10px">
              <div class="amountsTotal">{{ item.zcsb || "0" }}</div>
              <div class="amountsName">申报项目</div>
            </div>
            <div style="margin-top: 38px; margin-left: 10px">项</div>
          </div>
          <div class="every_item">
            <div class="every_img">
              <img src="../../../assets/images/home/<USER>" alt="" />
            </div>
            <div style="margin-left: 10px">
              <div class="amountsTotal">{{ item.sjxq || "0" }}</div>
              <div class="amountsName">需求数量</div>
            </div>
            <div style="margin-top: 38px; margin-left: 10px">条</div>
          </div>
          <div class="every_item">
            <div class="every_img">
              <img src="../../../assets/images/home/<USER>" alt="" />
            </div>
            <div style="margin-left: 10px">
              <div class="amountsTotal">{{ item.gjzy || "0" }}</div>
              <div class="amountsName">资源数量</div>
            </div>
            <div style="margin-top: 38px; margin-left: 10px">条</div>
          </div>
          <div class="every_item">
            <div class="every_img">
              <img src="../../../assets/images/home/<USER>" alt="" />
            </div>
            <div style="margin-left: 10px">
              <div class="amountsTotal">{{ item.zjzk || "0" }}</div>
              <div class="amountsName">专家</div>
            </div>
            <div style="margin-top: 38px; margin-left: 10px">项</div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import { getHomeCountStatics } from "@/api/zhm";
export default {
  name: "CompanyTab",
  data() {
    return {
      item: {
        zczx: 0, // 政策
        sjxq: 0, // 需求数量
        gjzy: 0, // 资源数量
        zjzk: 0, // 专家数量
        zcsb: 0, // 申报项目
      },
    };
  },
  created() {
    // this.initData();
  },
  methods: {
    initData() {
      getHomeCountStatics().then((res) => {
        const { code, data } = res;
        if (code === 200) {
          this.item = data;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.banner {
  &-body {
    position: relative;
    width: 100%;
    height: 450px;

    // background-color: #f5f5f5;
    .banner-image {
      width: 100%;
      height: 500px;
    }

    .banner_image {
      width: 100%;
      height: 450px;
      position: relative;
      background: #ffffff;

      .banner_box {
        position: absolute;
        width: 1200px;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        display: flex;
        justify-content: space-between;
      }

      img {
        width: 100%;
        height: 100%;
      }

      .scaleDrawimg {
        animation-name: scaleDraw;
        animation-timing-function: ease;
        animation-duration: 4s;
        animation-fill-mode: forwards;
      }

      @keyframes scaleDraw {
        0% {
          transform: scale(0.98);
          /*开始为1倍*/
        }

        100% {
          transform: scale(1);
          /*略微放大*/
        }
      }

      .banerText {
        width: 55%;
        padding-top: 50px;
        box-sizing: border-box;

        .bannerTitle {
          width: 100%;
          margin-bottom: 12px;
          font-size: 40px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #000;
        }

        .bannerDesc {
          width: 100%;
          font-size: 24px;
          font-family: Source Han Sans CN;
          font-weight: 400;
          color: #000;
          text-shadow: 0 1px 2px #ccc;
        }
      }

      .banner_logo {
        width: 40%;
        animation: float 5s infinite;
      }

      @keyframes float {
        0% {
          transform: translateY(0);
        }

        50% {
          transform: translateY(20px);
        }

        100% {
          transform: translateY(0);
        }
      }
    }
  }
}

.amounts {
  position: absolute;
  width: 1200px;
  height: 110px;
  left: calc((100% - 1200px) / 2);
  bottom: 20px;
  display: flex;
  justify-content: space-around;

  .every_item {
    width: 18%;
    height: 100%;
    // background: rgb(244, 246, 249);
    display: flex;
    justify-content: center;

    // text-align: center;
    .amountsTotal {
      font-size: 34px;
      font-weight: 500;
      margin-top: 28px;
      text-align: center;
    }

    .amountsName {
      color: #222222;
      font-size: 16px;
      font-weight: 400;
      text-align: center;
    }

    .every_img {
      width: 56px;
      height: 49px;
      margin-top: 38px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
