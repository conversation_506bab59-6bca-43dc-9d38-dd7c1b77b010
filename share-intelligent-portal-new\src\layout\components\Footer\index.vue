<template>
  <div class="footer">
    <div class="footer-content">
      <div class="footer_top">
        <div class="imgStyle">
          <img src="@/assets/images/home/<USER>" alt="" />
        </div>
        <div class="title">易复材共享智造工业互联网平台</div>
      </div>
      <div class="footer_middle">
        <div class="itemStyle" @click="goPage(1)">供需对接</div>
        <div class="itemStyle" @click="goPage(2)">复材商城</div>
        <div class="itemStyle" @click="goPage(3)">制造共享</div>
        <div class="itemStyle" @click="goPage(4)">服务共享</div>
        <div class="itemStyle" @click="goPage(5)">创新共享</div>
        <div class="itemStyle" @click="goPage(6)">解决方案</div>
        <div class="itemStyle" @click="goPage(7)">应用商店</div>
        <div class="itemStyle" @click="goPage(8)">关于我们</div>
      </div>
      <!-- <div class="footer_bottom">
        <div class="item_style">
          Copyright@ 青岛檬豆网络科技有限公司版权所有
        </div>
        <div class="item_style">
          <div class="email_style">
            <img :src="require('@/assets/images/email.png')" alt="" />
          </div>
          <div style="margin-left: 11px"><EMAIL></div>
        </div>
        <div class="item_style">
          <div class="phone_style">
            <img :src="require('@/assets/images/phone.png')" alt="" />
          </div>
          <div style="margin-left: 11px">4008—939—365</div>
        </div>
        <div class="item_style">
          <div class="document_style">
            <img :src="require('@/assets/images/document.png')" alt="" />
          </div>
          <div style="margin-left: 11px">《法律声明及隐私保护》</div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      mobile: "",
      key: "QmRlODJTVGhkNg==",
      type: "cG9saWN5Y2FzaA==",
      base64EncodeChars:
        "ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",
    };
  },
  name: "Footer",
  methods: {
    goPage(flag) {
      if (flag == 1) {
        this.$router.push({
          path: "/supplyDemandDocking",
        });
      }
      if (flag == 2) {
        // this.$router.push({
        //   path: "/classicCase",
        // });
        window.open("http://**************:1001/");
      }
      if (flag == 3) {
        this.$router.push({
          path: "/manufacturingSharing",
        });
      }
      if (flag == 4) {
        this.$router.push({
          path: "/serviceSharing",
        });
      }
      if (flag == 5) {
        this.$router.push({
          path: "/innovationSharing",
        });
      }
      if (flag == 6) {
        this.$router.push({
          path: "/solution",
        });
      }
      if (flag == 7) {
        this.$router.push({
          path: "/appStore",
        });
      }
      if (flag == 8) {
        this.$router.push({
          path: "/aboutUs",
        });
      }
    },
    // toDigital() {
    //   window.open("https://zhenduan.ningmengdou.com/digital-diagosis-web/");
    // },
  },
};
</script>

<style lang="scss" scoped>
.footer {
  width: 100%;
  background: #232323;
  padding: 80px 0;

  &-content {
    max-width: 1200px;
    margin: 0 auto;
    .footer_top {
      display: flex;
      justify-content: center;
      align-items: center;
      .imgStyle {
        width: 47px;
        img {
          width: 100%;
        }
      }
      .title {
        margin-left: 20px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        font-size: 20px;
        color: #ffffff;
        line-height: 20px;
      }
    }
    .footer_middle {
      display: flex;
      justify-content: center;
      margin-top: 58px;
      .itemStyle {
        margin-left: 40px;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #e4dcdc;
        cursor: pointer;
      }
    }
    .footer_bottom {
      display: flex;
      justify-content: center;
      margin-top: 70px;
      .item_style {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        color: #e4dcdc;
        margin-left: 20px;
        .email_style {
          width: 24px;
          height: 18px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .phone_style {
          width: 24px;
          height: 24px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .document_style {
          width: 18px;
          height: 24px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .item_style:nth-child(1) {
        margin-left: 0;
      }
    }
  }
}
</style>
