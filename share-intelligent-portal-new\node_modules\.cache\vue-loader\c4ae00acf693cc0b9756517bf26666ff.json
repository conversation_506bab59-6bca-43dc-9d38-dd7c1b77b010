{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\expertLibrary\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\expertLibrary\\detail.vue", "mtime": 1750311962987}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/purchaseSales/component/expertLibrary", "sourcesContent": ["<template>\r\n  <div class=\"expert-library-detail\">\r\n    <!-- banner图 -->\r\n    <div class=\"expert-detail-banner\">\r\n      <img\r\n        src=\"../../../../assets/expertLibrary/expertLibraryDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"expert-detail-title-box\">\r\n      <div class=\"expert-detail-divider\"></div>\r\n      <div class=\"expert-detail-title\">专家详情</div>\r\n      <div class=\"expert-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"expert-detail-content\">\r\n      <div class=\"expert-detail-headline\">\r\n        <div class=\"expert-detail-headline-img\">\r\n          <img v-if=\"data.headPortrait\" :src=\"data.headPortrait\" alt=\"\" />\r\n          <img\r\n            v-else\r\n            src=\"../../../../assets/expertLibrary/defaultImg.png\"\r\n            alt=\"\"\r\n          />\r\n        </div>\r\n        <div class=\"expert-detail-headline-info\">\r\n          <div class=\"headline-info-title\">{{ data.expertName }}</div>\r\n          <div class=\"headline-info-box\">\r\n            <span class=\"headline-info-laber\">单位：</span>\r\n            <span class=\"headline-info-description\">\r\n              {{ data.workUnit }}\r\n            </span>\r\n          </div>\r\n          <div class=\"headline-info-box\">\r\n            <span class=\"headline-info-laber\">职位：</span>\r\n            <span class=\"headline-info-description\">{{ data.post }}</span>\r\n          </div>\r\n          <div class=\"headline-info-box\">\r\n            <span class=\"headline-info-laber\">研究方向：</span>\r\n            <span class=\"headline-info-description\">{{\r\n              data.researchDirection\r\n            }}</span>\r\n          </div>\r\n          <div class=\"headline-content-btn\">\r\n            <el-button\r\n              v-if=\"showBtn\"\r\n              class=\"headline-btn-style intention-btn\"\r\n              @click=\"goIntention\"\r\n              >我有意向</el-button\r\n            >\r\n            <el-button @click=\"goChat\" icon=\"el-icon-chat-dot-round\"\r\n              >在线沟通</el-button\r\n            >\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"expert-detail-introduce\">\r\n        <div class=\"introduce-content\">\r\n          <div class=\"introduction-line\"></div>\r\n          <div class=\"introduction-title\">专家介绍</div>\r\n        </div>\r\n        <div class=\"introduction-text-content\">\r\n          <div\r\n            v-html=\"data.introduce\"\r\n            class=\"introduction-text ql-editor\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getExpertDetail, getCheckSubmit } from \"@/api/purchaseSales\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { getCompanyInfoByLoginInfo } from \"@/api/apathy\";\r\nimport { mapGetters } from \"vuex\";\r\nimport { getCustomerServicerInfo } from \"@/api/expertLibrary/index\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n      showBtn: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      getExpertDetail({ id: this.$route.query.id })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          if (!this.token) {\r\n            this.showBtn = true;\r\n          } else {\r\n            this.getInfo();\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 判断此资源是不是自己发布的\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (this.data.createById === res.user.userId) {\r\n          this.showBtn = false;\r\n        } else {\r\n          this.showBtn = true;\r\n        }\r\n      });\r\n    },\r\n    goChat() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n\r\n      getCustomerServicerInfo().then((res) => {\r\n        if (res.code == 200) {\r\n          let routeData = this.$router.resolve({\r\n            path: \"/user/im\",\r\n            query: {\r\n              userId: res.data.id,\r\n            },\r\n          });\r\n          window.open(routeData.href, \"_blank\");\r\n        } else {\r\n          this.$message({\r\n            type: \"warning\",\r\n            message: \"获取客服信息失败,请稍后再试\",\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 跳到我有意向页面\r\n    goIntention() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      // 是否加入企业\r\n      this.loading = true;\r\n      getCompanyInfoByLoginInfo()\r\n        .then((res) => {\r\n          if (res.data) {\r\n            // 是否对此资源提交过意向\r\n            getCheckSubmit({\r\n              id: this.$route.query.id,\r\n              resourceType: \"resource_expet\",\r\n            })\r\n              .then((res) => {\r\n                this.loading = false;\r\n                // true 提交过  false未提交过\r\n                if (res.data) {\r\n                  this.$message({\r\n                    type: \"warning\",\r\n                    message: \"已经提交过了哦！\",\r\n                  });\r\n                } else {\r\n                  let routeData = this.$router.resolve({\r\n                    path: \"/addIntention\",\r\n                    query: {\r\n                      id: this.$route.query.id,\r\n                      type: \"resource_expet\",\r\n                      title: this.data.expertName,\r\n                    },\r\n                  });\r\n                  window.open(routeData.href, \"_blank\");\r\n                }\r\n              })\r\n              .catch(() => {\r\n                this.loading = false;\r\n              });\r\n          } else {\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"warning\",\r\n              message: \"必须加入企业才可提交我有意向\",\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.expert-library-detail {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  padding-bottom: 60px;\r\n  .expert-detail-banner {\r\n    width: 100%;\r\n    height: 25.92vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .expert-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .expert-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .expert-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .expert-detail-content {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n    padding: 60px 60px 124px;\r\n    .expert-detail-headline {\r\n      display: flex;\r\n      .expert-detail-headline-img {\r\n        width: 240px;\r\n        height: 240px;\r\n        margin-right: 40px;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .expert-detail-headline-info {\r\n        flex: 1;\r\n        .headline-info-title {\r\n          max-width: 792px;\r\n          font-size: 32px;\r\n          font-family: PingFangSC-Semibold, PingFang SC;\r\n          font-weight: 600;\r\n          color: #333;\r\n          line-height: 32px;\r\n          padding-bottom: 20px;\r\n          word-wrap: break-word;\r\n        }\r\n        .headline-info-box {\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          line-height: 32px;\r\n          .headline-info-laber {\r\n            width: 70px;\r\n            color: #666;\r\n          }\r\n          .headline-info-description {\r\n            flex: 1;\r\n            max-width: 712px;\r\n            color: #333;\r\n            word-wrap: break-word;\r\n          }\r\n        }\r\n        .headline-content-btn {\r\n          padding-top: 52px;\r\n          .headline-btn-style {\r\n            width: 100px;\r\n            height: 32px;\r\n            border-radius: 4px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            padding: 8px 11px;\r\n          }\r\n          .intention-btn {\r\n            background: #21c9b8;\r\n            color: #fff;\r\n          }\r\n          .communication-btn {\r\n            border: 1px solid #21c9b8;\r\n            color: #21c9b8;\r\n            background: transparent;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .expert-detail-introduce {\r\n      padding-top: 60px;\r\n      .introduce-content {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-bottom: 40px;\r\n        .introduction-line {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .introduction-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n      .introduction-text-content {\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.expert-library-detail {\r\n  .introduction-text-content {\r\n    .introduction-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}