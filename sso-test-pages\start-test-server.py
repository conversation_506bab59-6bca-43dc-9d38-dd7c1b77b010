#!/usr/bin/env python3
"""
SSO测试页面HTTP服务器
用于解决CORS跨域问题，提供HTTP服务来托管测试页面
"""

import http.server
import socketserver
import os
import sys
import webbrowser
from pathlib import Path

# 服务器配置
PORT = 8000
HOST = 'localhost'

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """支持CORS的HTTP请求处理器"""
    
    def end_headers(self):
        # 添加CORS头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        # 处理预检请求
        self.send_response(200)
        self.end_headers()

def start_server():
    """启动HTTP服务器"""
    # 切换到当前脚本所在目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print(f"🚀 启动SSO测试页面服务器...")
    print(f"📍 服务地址: http://{HOST}:{PORT}")
    print(f"📁 服务目录: {script_dir}")
    print(f"🔗 检测页面: http://{HOST}:{PORT}/port-detector.html")
    print(f"🔗 主系统测试: http://{HOST}:{PORT}/backend-sso-test.html")
    print(f"🔗 从系统测试: http://{HOST}:{PORT}/market-sso-test.html")
    print()
    
    try:
        with socketserver.TCPServer((HOST, PORT), CORSHTTPRequestHandler) as httpd:
            print(f"✅ 服务器已启动，按 Ctrl+C 停止服务")
            print(f"🌐 正在自动打开浏览器...")
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://{HOST}:{PORT}/port-detector.html')
            except Exception as e:
                print(f"⚠️  无法自动打开浏览器: {e}")
                print(f"请手动访问: http://{HOST}:{PORT}/port-detector.html")
            
            print()
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用，请尝试其他端口")
            print(f"💡 您可以修改脚本中的 PORT 变量")
        else:
            print(f"❌ 启动服务器失败: {e}")
    except Exception as e:
        print(f"❌ 意外错误: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("🔍 SSO测试页面HTTP服务器")
    print("=" * 60)
    start_server()
