/*
 * @Author: jhy
 * @Date: 2023-01-05 10:44:17
 * @LastEditors: zhc
 * @LastEditTime: 2023-02-12 11:02:01
 */
import request from "@/utils/request";

// 查询字典数据列表
export function listData(query) {
  return request({
    url: "/system/dict/data/list",
    method: "get",
    params: query,
  });
}

// 查询字典数据详细
export function getData(dictCode) {
  return request({
    url: "/system/dict/data/" + dictCode,
    method: "get",
  });
}

// 根据字典类型查询字典数据信息
export function getDicts(dictType) {
  return request({
    url: "/system/dict/data/type/" + dictType,
    method: "get",
  });
}
// 查询字典数据列表（一级字典：传dictType；二级字典：传parentCode）
export function getSecondDicts(query) {
  return request({
    url: "/system/dict/data/only-list",
    method: "get",
    params: query,
  });
}

// 新增字典数据
export function addData(data) {
  return request({
    url: "/system/dict/data",
    method: "post",
    data: data,
  });
}

// 修改字典数据
export function updateData(data) {
  return request({
    url: "/system/dict/data",
    method: "put",
    data: data,
  });
}

// 删除字典数据
export function delData(dictCode) {
  return request({
    url: "/system/dict/data/" + dictCode,
    method: "delete",
  });
}

// 查询二级字典数据列表
export function getDataOnlyList(params) {
  return request({
    url: "/system/dict/data/only-list",
    method: "get",
    params,
  });
}
