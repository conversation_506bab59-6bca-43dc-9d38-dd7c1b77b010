<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:38:32
 * @LastEditTime: 2023-03-24 09:19:21
 * @Description: 
 * @LastEditors: zhc
-->
<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-02-03 11:20:59
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div class="app-container">
    <div class="noninductive-record-page">
      <el-row :gutter="20">
        <el-col :span="2.5" :xs="24">
          <user-menu />
        </el-col>
        <el-col :span="18" :xs="24">
          <div>
            <el-radio-group v-model="status" @change="changeType">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="1">审核中</el-radio-button>
              <el-radio-button label="2">审核通过</el-radio-button>
              <el-radio-button label="3">审核驳回</el-radio-button>
              <el-radio-button label="0">草稿箱</el-radio-button>
            </el-radio-group>
          </div>
          <div class="noninductive-pannel">
            <div class="none-class" v-if="!records || records.length == 0">
              <el-image
                style="width: 160px; height: 160px"
                :src="require('@/assets/user/none.png')"
                :fit="fit"
              ></el-image>
              <div class="text">暂无数据</div>
            </div>
            <div
              class="noninductive-item"
              v-for="item in records"
              v-bind:key="item.id"
            >
              <a class="left" @click="goDetail(item.id)">
                <div class="noninductive-title">
                  {{
                    item.itemTypeFirstName || "" + item.itemTypeSecondName || ""
                  }}
                </div>
                <div class="item-info">
                  <div class="company-name">{{ item.companyName }}</div>
                  <div class="company-name ml_150">
                    提交时间：{{ item.updateTime }}
                  </div>
                </div>
              </a>
              <el-image
                class="status-icon"
                v-if="item.status != '0'"
                style="width: 16px; height: 16px"
                :src="
                  require('@/assets/user/noninductive_status_' +
                    item.status +
                    '.png')
                "
              ></el-image>

              <div class="noninductive-status">{{ item.statusName }}</div>
              <a
                @click="doRevocation(item)"
                class="revocation-button"
                v-if="item.status === '1'"
              >
                <div>撤回</div>
              </a>
            </div>
          </div>
          <el-pagination
            v-show="total > 0"
            background
            layout="prev, pager, next"
            :page-size="6"
            :current-page.sync="queryParams.pageNum"
            @current-change="handleCurrentChange"
            :total="total"
          >
          </el-pagination>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import UserMenu from "../components/userMenu.vue";
import {
  listNoninductive,
  revocationNoninductive,
} from "@/api/system/noninductive";
export default {
  name: "Noninductive",
  components: { UserMenu },
  data() {
    return {
      status: "",
      records: [],
      queryParams: {
        pageNum: 1,
        pageSize: 6,
      },
      total: 1,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    changeType(res) {
      this.getList();
    },
    getList() {
      listNoninductive({ ...this.queryParams, status: this.status }).then(
        (response) => {
          this.records = response.rows;
          this.total = response.total;
        }
      );
    },
    doRevocation(item) {
      this.$confirm("是否确认撤回该提报？", { type: "error" })
        .then((_) => {
          revocationNoninductive(item.id).then((response) => {
            this.$message({
              message: "操作成功",
              type: "success",
            });
            this.getList();
          });
        })
        .catch((_) => {});
    },
    goDetail(id) {
      this.$router.push("/user/noninductiveDetail?id=" + id);
    },
    handleCurrentChange(pageNum) {
      this.queryParams.pageNum = pageNum;
      this.getList();
    },
  },
};
</script>

<style lang="scss">
.app-container {
  background: #f4f5f9;
  .noninductive-record-page {
    .noninductive-pannel {
      margin-top: 24px;
      width: 100%;
      height: 600px;
      background: #fff;
      .none-class {
        text-align: center;
        padding: 10% 0;
        .text {
          font-size: 14px;
          font-weight: 400;
          color: #999999;
          line-height: 14px;
        }
      }
      .noninductive-item {
        display: flex;
        padding: 0 20px;
        height: 100px;
        border-bottom: 1px solid #e8e8e8;
        .left {
          width: 73%;
          .noninductive-title {
            font-size: 16px;
            font-weight: 500;
            color: #323233;
            width: 100%;
            line-height: 56px;
            overflow: hidden;
            -webkit-line-clamp: 1;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
          }
          .item-info {
            display: flex;
            width: 800px;
            justify-content: space-between;
            .company-name {
              font-size: 14px;
              font-weight: 400;
              color: #666666;
              line-height: 20px;
            }
            .ml_150 {
              margin-left: 150px;
            }
          }
        }
        .status-icon {
          margin: auto 10px;
          height: 100px;
        }
        .noninductive-status {
          line-height: 100px;
          font-size: 15px;
          width: 180px;
          font-weight: 400;
          color: #333333;
          overflow: hidden;
          -webkit-line-clamp: 1;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
        .revocation-button {
          width: 50px;
          height: 26px;
          border-radius: 4px;
          border: 1px solid #21c9b8;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #21c9b8;
          line-height: 26px;
          text-align: center;
          margin: auto;
        }
      }
    }
    .el-radio-button {
      margin-right: 30px;
    }
    .el-radio-button__inner {
      width: 96px;
      height: 32px;
      background: transparent;
      border-radius: 20px;
      text-align: center;
      color: #333333;
      border: none;
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background: #21c9b8 !important;
      color: #fff;
      box-shadow: none;
    }
    .el-radio-button__inner:hover {
      color: #333333;
    }

    .el-pagination {
      width: 100%;
      margin-top: 20px;
      text-align: center;
    }
    .el-pagination.is-background .el-pager li {
      background-color: #fff;
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active {
      background-color: #21c9b8;
      color: #ffffff;
    }
    .el-pagination.is-background .el-pager li:not(.disabled):hover {
      color: #21c9b8;
    }
  }
}
</style>
