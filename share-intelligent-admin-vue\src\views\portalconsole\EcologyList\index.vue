<template>
  <!-- 生态协作 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="生态类别ID" prop="ecologyCategoryId">
        <el-input
          v-model="queryParams.ecologyCategoryId"
          placeholder="请输入生态类别ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="公司id" prop="companyId">
        <el-input
          v-model="queryParams.companyId"
          placeholder="请输入公司id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="联系人" prop="ecologyContact">
        <el-input
          v-model="queryParams.ecologyContact"
          placeholder="请输入联系人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="ecologyPhone">
        <el-input
          v-model="queryParams.ecologyPhone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:Ecology:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:Ecology:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:Ecology:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:Ecology:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> -->

    <el-table v-loading="loading" :data="EcologyList" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <!-- <el-table-column label="生态ID" align="center" prop="ecologyId" /> -->
      <!-- <el-table-column label="生态类别ID" align="center" prop="ecologyCategoryId" /> -->
      <!-- <el-table-column label="公司id" align="center" prop="companyId" /> -->
      <el-table-column label="公司名称" align="center" prop="companyName" />
      <el-table-column label="生态类别" align="center" prop="ecologyCategoryName" />
      <el-table-column label="合作思路" align="center" prop="ecologyIdeas" />
      <el-table-column label="联系人" align="center" prop="ecologyContact" />
      <el-table-column label="联系电话" align="center" prop="ecologyPhone" />
      <el-table-column label="备注" align="center" prop="remark" />
      <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:Ecology:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:Ecology:remove']"
          >删除</el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改生态协作对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="生态类别ID" prop="ecologyCategoryId">
          <el-input v-model="form.ecologyCategoryId" placeholder="请输入生态类别ID" />
        </el-form-item>
        <el-form-item label="公司id" prop="companyId">
          <el-input v-model="form.companyId" placeholder="请输入公司id" />
        </el-form-item>
        <el-form-item label="合作思路" prop="ecologyIdeas">
          <el-input v-model="form.ecologyIdeas" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="联系人" prop="ecologyContact">
          <el-input v-model="form.ecologyContact" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="ecologyPhone">
          <el-input v-model="form.ecologyPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listEcology, getEcology, delEcology, addEcology, updateEcology } from "@/api/portalconsole/Ecology";

export default {
  name: "EcologyList",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 生态协作表格数据
      EcologyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ecologyCategoryId: null,
        companyId: null,
        ecologyIdeas: null,
        ecologyContact: null,
        ecologyPhone: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        ecologyCategoryId: [
          { required: true, message: "生态类别ID不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询生态协作列表 */
    getList() {
      this.loading = true;
      listEcology(this.queryParams).then(response => {
        this.EcologyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        ecologyId: null,
        ecologyCategoryId: null,
        companyId: null,
        ecologyIdeas: null,
        ecologyContact: null,
        ecologyPhone: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.ecologyId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加生态协作";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const ecologyId = row.ecologyId || this.ids
      getEcology(ecologyId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改生态协作";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.ecologyId != null) {
            updateEcology(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEcology(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ecologyIds = row.ecologyId || this.ids;
      this.$modal.confirm('是否确认删除生态协作编号为"' + ecologyIds + '"的数据项？').then(function() {
        return delEcology(ecologyIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/Ecology/export', {
        ...this.queryParams
      }, `Ecology_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
