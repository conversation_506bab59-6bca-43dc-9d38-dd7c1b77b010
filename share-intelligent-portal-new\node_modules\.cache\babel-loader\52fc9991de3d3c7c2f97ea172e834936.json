{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\approveSetting\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\approveSetting\\index.vue", "mtime": 1750311963043}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_FileUpload", "_store", "_user", "_auth", "_approve", "name", "components", "UserMenu", "FileUpload", "data", "activeName", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "accept", "headers", "Authorization", "getToken", "companyOptions", "isAdmin", "loading", "imgVisible", "personalEdit", "companyEdit", "companyLoading", "personalLoading", "imageUrl", "user", "userId", "store", "getters", "bussinessNo", "phonenumber", "personalForm", "personalCardList", "companyForm", "authMatTerialList", "companyCardList", "personaRules", "companyName", "required", "message", "trigger", "phone", "companyRules", "created", "getApproveDetail", "methods", "handleClick", "tab", "event", "queryParams", "pageNum", "pageSize", "changePersonEdit", "changeCompanyEdit", "handleBeforeUpload", "file", "type", "size", "typeList", "split", "map", "item", "trim", "toLowerCase", "substr", "dotIndex", "lastIndexOf", "$message", "error", "suffix", "substring", "indexOf", "handlePersonalCardPreview", "url", "handleCompanyCardPreview", "handleRemove", "fileList", "handleCompanyCardRemove", "handlecompanyCardSuccess", "res", "code", "push", "handlePersonalCardSuccess", "handleAuthMatTerialSuccess", "getCompanyList", "query", "_this", "getCompanyListByName", "then", "response", "companyChanged", "_this2", "for<PERSON>ach", "id", "creditCode", "tianyanId", "personChanged", "_this3", "handleompanyCardRemove", "submitPersonal", "_this4", "$refs", "validate", "valid", "personalApprove", "_objectSpread2", "default", "$modal", "msgSuccess", "submitCompany", "_this5", "companyApprove", "download", "link", "document", "createElement", "href", "target", "click", "_this6", "_res$data", "_res$data2"], "sources": ["src/views/system/user/approveSetting/index.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-12 10:25:16\r\n * @LastEditTime: 2023-02-27 14:43:13\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-11 11:21:19\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"approve-setting-container\">\r\n          <div class=\"header-small\">\r\n            <div class=\"red-tag\"></div>\r\n            申请认证\r\n          </div>\r\n          <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n            <el-tab-pane label=\"名片认证\" name=\"1\">\r\n              <el-form\r\n                ref=\"personalForm\"\r\n                :model=\"personalForm\"\r\n                :rules=\"personaRules\"\r\n                label-width=\"120px\"\r\n              >\r\n                <el-form-item label=\"真实姓名：\" prop=\"name\">\r\n                  <div v-if=\"!personalEdit\">{{ personalForm.name }}</div>\r\n                  <el-input\r\n                    v-model=\"personalForm.name\"\r\n                    v-else\r\n                    placeholder=\"请输入真实姓名\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式：\" prop=\"phone\">\r\n                  <div v-if=\"!personalEdit\">{{ personalForm.phone }}</div>\r\n                  <el-input\r\n                    v-model=\"personalForm.phone\"\r\n                    v-else\r\n                    placeholder=\"请选择联系方式\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"公司名称：\" prop=\"companyName\">\r\n                  <div v-if=\"!personalEdit\">{{ personalForm.companyName }}</div>\r\n                  <el-select\r\n                    v-model=\"personalForm.companyName\"\r\n                    v-else\r\n                    filterable\r\n                    remote\r\n                    reserve-keyword\r\n                    placeholder=\"请输入关键词\"\r\n                    :remote-method=\"getCompanyList\"\r\n                    :loading=\"personalLoading\"\r\n                    @change=\"personChanged\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in companyOptions\"\r\n                      :key=\"item.id\"\r\n                      :label=\"item.name\"\r\n                      :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"身份证明：\" prop=\"personalCardList\">\r\n                  <div class=\"pic-class\">\r\n                    <el-upload\r\n                      list-type=\"picture-card\"\r\n                      :headers=\"headers\"\r\n                      :action=\"uploadUrl\"\r\n                      :class=\"[personalEdit ? '' : 'hide']\"\r\n                      :disabled=\"!personalEdit\"\r\n                      :file-list=\"personalForm.personalCardList\"\r\n                      :accept=\"accept\"\r\n                      :before-upload=\"handleBeforeUpload\"\r\n                      :on-preview=\"handlePersonalCardPreview\"\r\n                      :on-remove=\"handleRemove\"\r\n                      :on-success=\"handlePersonalCardSuccess\"\r\n                    >\r\n                      <i class=\"el-icon-plus\"></i>\r\n                      <div\r\n                        v-if=\"personalEdit\"\r\n                        slot=\"tip\"\r\n                        class=\"el-upload__tip\"\r\n                      >\r\n                        点击上传\r\n                        <span class=\"red-text\">名片、工作证、工牌</span>\r\n                        等可以认证身份的图片\r\n                      </div>\r\n                    </el-upload>\r\n                  </div>\r\n\r\n                  <el-dialog\r\n                    append-to-body\r\n                    :visible.sync=\"imgVisible\"\r\n                    :close-on-click-modal=\"false\"\r\n                  >\r\n                    <img width=\"100%\" :src=\"imageUrl\" alt=\"\" />\r\n                  </el-dialog>\r\n                </el-form-item>\r\n              </el-form>\r\n              <div class=\"button-container\">\r\n                <el-button\r\n                  v-if=\"!personalEdit\"\r\n                  type=\"danger\"\r\n                  @click=\"changePersonEdit\"\r\n                  >编辑</el-button\r\n                >\r\n                <el-button v-else type=\"danger\" @click=\"submitPersonal()\"\r\n                  >保存</el-button\r\n                >\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane label=\"企业认证\" name=\"2\">\r\n              <div class=\"staff-list\">\r\n                <el-form\r\n                  ref=\"companyForm\"\r\n                  :model=\"companyForm\"\r\n                  :rules=\"companyRules\"\r\n                  label-width=\"120px\"\r\n                >\r\n                  <el-row>\r\n                    <el-col :span=\"24\">\r\n                      <el-form-item label=\"真实姓名：\" prop=\"name\">\r\n                        <div v-if=\"!companyEdit\">\r\n                          {{ companyForm.name }}\r\n                        </div>\r\n                        <el-input\r\n                          v-model=\"companyForm.name\"\r\n                          v-else\r\n                          placeholder=\"请输入真实姓名\"\r\n                        />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n\r\n                  <el-row>\r\n                    <el-col :span=\"24\">\r\n                      <el-form-item label=\"联系方式：\" prop=\"phone\">\r\n                        <div v-if=\"!companyEdit\">\r\n                          {{ companyForm.phone }}\r\n                        </div>\r\n                        <el-input\r\n                          v-model=\"companyForm.phone\"\r\n                          v-else\r\n                          placeholder=\"请选择联系方式\"\r\n                        />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row>\r\n                    <el-col :span=\"24\">\r\n                      <el-form-item label=\"公司名称：\" prop=\"companyName\">\r\n                        <div v-if=\"!companyEdit\">\r\n                          {{ companyForm.companyName }}\r\n                        </div>\r\n                        <el-select\r\n                          v-model=\"companyForm.companyName\"\r\n                          v-else\r\n                          filterable\r\n                          remote\r\n                          reserve-keyword\r\n                          placeholder=\"请输入关键词\"\r\n                          :remote-method=\"getCompanyList\"\r\n                          :loading=\"companyLoading\"\r\n                          @change=\"companyChanged\"\r\n                        >\r\n                          <el-option\r\n                            v-for=\"item in companyOptions\"\r\n                            :key=\"item.id\"\r\n                            :label=\"item.name\"\r\n                            :value=\"item.id\"\r\n                          >\r\n                          </el-option>\r\n                        </el-select>\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-form-item label=\"企业授权书：\">\r\n                    <div class=\"pic-class\">\r\n                      <el-upload\r\n                        list-type=\"picture-card\"\r\n                        :class=\"[companyEdit ? '' : 'hide']\"\r\n                        :headers=\"headers\"\r\n                        :action=\"uploadUrl\"\r\n                        :file-list=\"companyForm.companyCardList\"\r\n                        :accept=\"accept\"\r\n                        :disabled=\"!companyEdit\"\r\n                        :before-upload=\"handleBeforeUpload\"\r\n                        :on-preview=\"handleCompanyCardPreview\"\r\n                        :on-remove=\"handleCompanyCardRemove\"\r\n                        :on-success=\"handlecompanyCardSuccess\"\r\n                      >\r\n                        <i class=\"el-icon-plus\"></i>\r\n                        <div\r\n                          v-if=\"companyEdit\"\r\n                          slot=\"tip\"\r\n                          class=\"el-upload__tip\"\r\n                        >\r\n                          支持jpg、png格式\r\n                          <a class=\"red-text\" @click=\"download\">查看模板>></a>\r\n                        </div>\r\n                      </el-upload>\r\n                    </div>\r\n                    <el-dialog\r\n                      append-to-body\r\n                      :visible.sync=\"imgVisible\"\r\n                      :close-on-click-modal=\"false\"\r\n                    >\r\n                      <img width=\"100%\" :src=\"imageUrl\" alt=\"\" />\r\n                    </el-dialog>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"\" prop=\"companyCardList\">\r\n                    <el-upload\r\n                      :file-list=\"companyForm.authMatTerialList\"\r\n                      :headers=\"headers\"\r\n                      :action=\"uploadUrl\"\r\n                      :disabled=\"!companyEdit\"\r\n                      accept=\".pdf, .docx, .xls\"\r\n                      :on-remove=\"handleompanyCardRemove\"\r\n                      :on-success=\"handleAuthMatTerialSuccess\"\r\n                      :limit=\"10\"\r\n                    >\r\n                      <el-button\r\n                        v-if=\"companyEdit\"\r\n                        class=\"apathy-upload-btn\"\r\n                        size=\"small\"\r\n                        icon=\"el-icon-upload2\"\r\n                        >上传文件\r\n                      </el-button>\r\n                      <span\r\n                        v-if=\"companyEdit\"\r\n                        slot=\"tip\"\r\n                        class=\"el-upload__tip\"\r\n                      >\r\n                        仅限doc、pdf、xls格式\r\n                      </span>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-form>\r\n                <div class=\"button-container\">\r\n                  <el-button\r\n                    v-if=\"!companyEdit\"\r\n                    type=\"danger\"\r\n                    @click=\"changeCompanyEdit\"\r\n                    >编辑</el-button\r\n                  >\r\n                  <el-button v-else type=\"danger\" @click=\"submitCompany()\"\r\n                    >保存</el-button\r\n                  >\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport FileUpload from \"@/components/FileUpload\";\r\nimport store from \"@/store\";\r\nimport { getCompanyListByName } from \"@/api/system/user\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport {\r\n  personalApprove,\r\n  companyApprove,\r\n  getApproveDetail,\r\n} from \"@/api/system/approve.js\";\r\nexport default {\r\n  name: \"ApproveSetting\",\r\n  components: { UserMenu, FileUpload },\r\n  data() {\r\n    return {\r\n      activeName: \"1\",\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      companyOptions: [],\r\n      isAdmin: false,\r\n      loading: false,\r\n      imgVisible: false,\r\n      personalEdit: false,\r\n      companyEdit: false,\r\n      companyLoading: false,\r\n      personalLoading: false,\r\n      imageUrl: \"\",\r\n      user: {\r\n        userId: store.getters.userId,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        phonenumber: store.getters.phonenumber,\r\n      },\r\n      personalForm: {\r\n        personalCardList: [],\r\n      },\r\n      companyForm: {\r\n        authMatTerialList: [],\r\n        companyCardList: [],\r\n      },\r\n      personaRules: {\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        name: [\r\n          { required: true, message: \"真实姓名不能为空\", trigger: \"blur\" },\r\n        ],\r\n        phone: [\r\n          { required: true, message: \"联系方式不能为空\", trigger: \"blur\" },\r\n        ],\r\n        personalCardList: [\r\n          { required: false, message: \"身份证明不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      companyRules: {\r\n        name: [\r\n          { required: true, message: \"真实姓名不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n\r\n        phone: [\r\n          { required: true, message: \"联系方式不能为空\", trigger: \"blur\" },\r\n        ],\r\n        authMatTerialList: [\r\n          { required: false, message: \"授权书不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyCardList: [\r\n          { required: false, message: \"附件不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getApproveDetail();\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      if (this.activeName == \"1\") {\r\n      } else {\r\n      }\r\n      this.getApproveDetail();\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 5,\r\n      };\r\n    },\r\n    changePersonEdit() {\r\n      this.personalEdit = true;\r\n    },\r\n    changeCompanyEdit() {\r\n      this.companyEdit = true;\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePersonalCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    handleCompanyCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.personalForm.personalCardList = fileList;\r\n    },\r\n    // 删除产品照片\r\n    handleCompanyCardRemove(file, fileList) {\r\n      this.companyForm.companyCardList = fileList;\r\n    },\r\n    handlecompanyCardSuccess(res, file, fileList) {\r\n      if (!this.companyForm.companyForm) {\r\n        this.companyForm.companyCardList = [];\r\n      }\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.companyForm.companyCardList.push(res.data);\r\n      }\r\n    },\r\n    handlePersonalCardSuccess(res, file, fileList) {\r\n      if (!this.personalForm.personalCardList) {\r\n        this.personalForm.personalCardList = [];\r\n      }\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.personalForm.personalCardList.push(res.data);\r\n      }\r\n    },\r\n    handleAuthMatTerialSuccess(res, file, fileList) {\r\n      if (!this.companyForm.authMatTerialList) {\r\n        this.companyForm.authMatTerialList = [];\r\n      }\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.companyForm.authMatTerialList.push(res.data);\r\n      }\r\n    },\r\n    getCompanyList(query) {\r\n      if (query !== \"\") {\r\n        getCompanyListByName(query).then((response) => {\r\n          this.companyOptions = response.data;\r\n        });\r\n      }\r\n    },\r\n    companyChanged(res) {\r\n      this.companyOptions.forEach((item) => {\r\n        if (item.id == res) {\r\n          this.companyForm.bussinessNo = item.creditCode;\r\n          this.companyForm.tianyanId = item.id;\r\n          this.companyForm.companyName = item.name;\r\n        }\r\n      });\r\n    },\r\n    personChanged(res) {\r\n      this.companyOptions.forEach((item) => {\r\n        if (item.id == res) {\r\n          this.personalForm.bussinessNo = item.creditCode;\r\n          this.personalForm.tianyanId = item.id;\r\n          this.personalForm.companyName = item.name;\r\n        }\r\n      });\r\n    },\r\n    handleompanyCardRemove(file, fileList) {\r\n      this.companyForm.companyCardList = res.data;\r\n    },\r\n    submitPersonal() {\r\n      this.$refs.personalForm.validate((valid) => {\r\n        if (valid) {\r\n          personalApprove({\r\n            ...this.personalForm,\r\n          }).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.getApproveDetail();\r\n              this.personalEdit = false;\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    submitCompany() {\r\n      // if (!this.companyForm.authMatTerialList) {\r\n      //   this.$modal.msgError(\"请上传授权书\");\r\n      // }\r\n      this.$refs.companyForm.validate((valid) => {\r\n        if (valid) {\r\n          companyApprove({\r\n            ...this.companyForm,\r\n          }).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.getApproveDetail();\r\n              this.companyEdit = false;\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    download() {\r\n      // let url =\r\n      //   \"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230213/1676277801454790.docx\";\r\n      // window.open(url, \"_blank\");\r\n      var link = document.createElement(\"a\");\r\n      link.href =\r\n        \"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230213/1676277801454790.docx\";\r\n      link.download = \"授权书\";\r\n      link.target = \"_blank\";\r\n      link.click();\r\n    },\r\n\r\n    getApproveDetail() {\r\n      getApproveDetail().then((res) => {\r\n        if (res.code == 200) {\r\n          this.personalForm = res.data ?? {};\r\n          this.companyForm = res.data ?? {};\r\n          if (!this.personalForm.personalCardList) {\r\n            this.personalForm.personalCardList = [];\r\n          }\r\n          if (!this.companyForm.authMatTerialList) {\r\n            this.companyForm.authMatTerialList = [];\r\n          }\r\n          if (!this.companyForm.companyCardList) {\r\n            this.companyForm.companyCardList = [];\r\n          }\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .approve-setting-container {\r\n    width: 100%;\r\n    background: #fff;\r\n    min-height: 700px;\r\n    padding: 20px;\r\n    .el-select {\r\n      display: inline-block;\r\n      position: relative;\r\n      width: 100%;\r\n    }\r\n    .el-button--danger {\r\n      background: #21c9b8;\r\n      color: #fff;\r\n      border-color: #21c9b8;\r\n    }\r\n    .header-small {\r\n      text-align: center;\r\n      display: flex;\r\n      font-size: 20px;\r\n      font-weight: 500;\r\n      color: #333333;\r\n      line-height: 20px;\r\n\r\n      .red-tag {\r\n        margin-right: 12px;\r\n        width: 3px;\r\n        height: 22px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .apathy-upload-btn {\r\n      margin-right: 20px;\r\n    }\r\n    .button-container {\r\n      width: 100%;\r\n      margin-top: 50px;\r\n      text-align: center;\r\n    }\r\n\r\n    .el-tabs__nav {\r\n      width: 100%;\r\n      height: 40px;\r\n      padding: 0 43%;\r\n      display: flex;\r\n      // justify-content: space-between;\r\n    }\r\n\r\n    .el-tabs__nav-wrap::after {\r\n      background-color: transparent;\r\n    }\r\n    .el-tabs__active-bar {\r\n      margin-left: 43%;\r\n      background-color: #21c9b8;\r\n    }\r\n    .el-tabs__item.is-active {\r\n      color: #21c9b8;\r\n    }\r\n    .el-tabs__item:hover {\r\n      color: #21c9b8;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n  .pic-class .hide .el-upload--picture-card {\r\n    display: none;\r\n  }\r\n  .el-pagination {\r\n    width: 100%;\r\n    margin-top: 20px;\r\n    text-align: center;\r\n  }\r\n  .el-pagination.is-background .el-pager li {\r\n    background-color: #fff;\r\n  }\r\n  .el-pagination.is-background .el-pager li:not(.disabled).active {\r\n    background-color: #21c9b8;\r\n    color: #ffffff;\r\n  }\r\n  .el-pagination.is-background .el-pager li:not(.disabled):hover {\r\n    color: #21c9b8;\r\n  }\r\n  .red-text {\r\n    color: #21c9b8;\r\n  }\r\n  .trans-form {\r\n    border-radius: 6px;\r\n    background: #ffffff;\r\n    width: 400px;\r\n    padding: 25px 5px 5px 25px;\r\n    .header {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #121620;\r\n      line-height: 18px;\r\n      margin-bottom: 12px;\r\n    }\r\n    .el-input {\r\n      height: 38px;\r\n      input {\r\n        height: 38px;\r\n      }\r\n    }\r\n    .el-select {\r\n      display: inline-block;\r\n      position: relative;\r\n      width: 100%;\r\n    }\r\n    .input-icon {\r\n      height: 39px;\r\n      width: 14px;\r\n      margin-left: 2px;\r\n    }\r\n    .el-form-item--medium .el-form-item__content {\r\n      display: flex;\r\n      line-height: 36px;\r\n    }\r\n    .el-form-item {\r\n      margin-bottom: 12px;\r\n    }\r\n    .el-input__suffix-inner {\r\n      .active-style {\r\n        color: #21c9b8;\r\n        font-size: 14px;\r\n      }\r\n      .disabled-style {\r\n        color: #999;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n  .el-button--primary {\r\n    /* color: #FFFFFF; */\r\n    background-color: #21c9b8 !important;\r\n    border-color: #21c9b8;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AA6QA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAKA;EACAM,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA,iBAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,MAAA;MACAC,OAAA;QAAAC,aAAA,kBAAAC,cAAA;MAAA;MACAC,cAAA;MACAC,OAAA;MACAC,OAAA;MACAC,UAAA;MACAC,YAAA;MACAC,WAAA;MACAC,cAAA;MACAC,eAAA;MACAC,QAAA;MACAC,IAAA;QACAC,MAAA,EAAAC,cAAA,CAAAC,OAAA,CAAAF,MAAA;QACAG,WAAA,EAAAF,cAAA,CAAAC,OAAA,CAAAC,WAAA;QACAC,WAAA,EAAAH,cAAA,CAAAC,OAAA,CAAAE;MACA;MACAC,YAAA;QACAC,gBAAA;MACA;MACAC,WAAA;QACAC,iBAAA;QACAC,eAAA;MACA;MACAC,YAAA;QACAC,WAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAtC,IAAA,GACA;UAAAoC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,KAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,gBAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAE,YAAA;QACAxC,IAAA,GACA;UAAAoC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAH,WAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QAEAC,KAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,iBAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,eAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,OAAA,WAAAA,QAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAAC,GAAA,EAAAC,KAAA;MACA,SAAAzC,UAAA,UACA,QACA;MACA,KAAAqC,gBAAA;MACA,KAAAK,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAhC,YAAA;IACA;IACAiC,iBAAA,WAAAA,kBAAA;MACA,KAAAhC,WAAA;IACA;IACA;IACAiC,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAArD,IAAA,GAAAqD,IAAA,CAAArD,IAAA;QAAAsD,IAAA,GAAAD,IAAA,CAAAC,IAAA;QAAAC,IAAA,GAAAF,IAAA,CAAAE,IAAA;MACA,IAAAC,QAAA,QAAA9C,MAAA,CACA+C,KAAA,MACAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA,GAAAC,WAAA,GAAAC,MAAA;MAAA;MACA,IAAAC,QAAA,GAAA/D,IAAA,CAAAgE,WAAA;MACA;MACA,IAAAD,QAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;QACA,IAAAC,MAAA,GAAAnE,IAAA,CAAAoE,SAAA,CAAAL,QAAA;QACA,IAAAP,QAAA,CAAAa,OAAA,CAAAF,MAAA,CAAAN,WAAA;UACA,KAAAI,QAAA,CAAAC,KAAA;UACA;QACA;MACA;MACA;MACA,IAAAX,IAAA;QACA,KAAAU,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACA;IACAI,yBAAA,WAAAA,0BAAAjB,IAAA;MACA,KAAA/B,QAAA,GAAA+B,IAAA,CAAAkB,GAAA;MACA,KAAAtD,UAAA;IACA;IACAuD,wBAAA,WAAAA,yBAAAnB,IAAA;MACA,KAAA/B,QAAA,GAAA+B,IAAA,CAAAkB,GAAA;MACA,KAAAtD,UAAA;IACA;IACA;IACAwD,YAAA,WAAAA,aAAApB,IAAA,EAAAqB,QAAA;MACA,KAAA7C,YAAA,CAAAC,gBAAA,GAAA4C,QAAA;IACA;IACA;IACAC,uBAAA,WAAAA,wBAAAtB,IAAA,EAAAqB,QAAA;MACA,KAAA3C,WAAA,CAAAE,eAAA,GAAAyC,QAAA;IACA;IACAE,wBAAA,WAAAA,yBAAAC,GAAA,EAAAxB,IAAA,EAAAqB,QAAA;MACA,UAAA3C,WAAA,CAAAA,WAAA;QACA,KAAAA,WAAA,CAAAE,eAAA;MACA;MACA;MACA,IAAA4C,GAAA,CAAAC,IAAA;QACA,KAAA/C,WAAA,CAAAE,eAAA,CAAA8C,IAAA,CAAAF,GAAA,CAAAzE,IAAA;MACA;IACA;IACA4E,yBAAA,WAAAA,0BAAAH,GAAA,EAAAxB,IAAA,EAAAqB,QAAA;MACA,UAAA7C,YAAA,CAAAC,gBAAA;QACA,KAAAD,YAAA,CAAAC,gBAAA;MACA;MACA;MACA,IAAA+C,GAAA,CAAAC,IAAA;QACA,KAAAjD,YAAA,CAAAC,gBAAA,CAAAiD,IAAA,CAAAF,GAAA,CAAAzE,IAAA;MACA;IACA;IACA6E,0BAAA,WAAAA,2BAAAJ,GAAA,EAAAxB,IAAA,EAAAqB,QAAA;MACA,UAAA3C,WAAA,CAAAC,iBAAA;QACA,KAAAD,WAAA,CAAAC,iBAAA;MACA;MACA;MACA,IAAA6C,GAAA,CAAAC,IAAA;QACA,KAAA/C,WAAA,CAAAC,iBAAA,CAAA+C,IAAA,CAAAF,GAAA,CAAAzE,IAAA;MACA;IACA;IACA8E,cAAA,WAAAA,eAAAC,KAAA;MAAA,IAAAC,KAAA;MACA,IAAAD,KAAA;QACA,IAAAE,0BAAA,EAAAF,KAAA,EAAAG,IAAA,WAAAC,QAAA;UACAH,KAAA,CAAAtE,cAAA,GAAAyE,QAAA,CAAAnF,IAAA;QACA;MACA;IACA;IACAoF,cAAA,WAAAA,eAAAX,GAAA;MAAA,IAAAY,MAAA;MACA,KAAA3E,cAAA,CAAA4E,OAAA,WAAA/B,IAAA;QACA,IAAAA,IAAA,CAAAgC,EAAA,IAAAd,GAAA;UACAY,MAAA,CAAA1D,WAAA,CAAAJ,WAAA,GAAAgC,IAAA,CAAAiC,UAAA;UACAH,MAAA,CAAA1D,WAAA,CAAA8D,SAAA,GAAAlC,IAAA,CAAAgC,EAAA;UACAF,MAAA,CAAA1D,WAAA,CAAAI,WAAA,GAAAwB,IAAA,CAAA3D,IAAA;QACA;MACA;IACA;IACA8F,aAAA,WAAAA,cAAAjB,GAAA;MAAA,IAAAkB,MAAA;MACA,KAAAjF,cAAA,CAAA4E,OAAA,WAAA/B,IAAA;QACA,IAAAA,IAAA,CAAAgC,EAAA,IAAAd,GAAA;UACAkB,MAAA,CAAAlE,YAAA,CAAAF,WAAA,GAAAgC,IAAA,CAAAiC,UAAA;UACAG,MAAA,CAAAlE,YAAA,CAAAgE,SAAA,GAAAlC,IAAA,CAAAgC,EAAA;UACAI,MAAA,CAAAlE,YAAA,CAAAM,WAAA,GAAAwB,IAAA,CAAA3D,IAAA;QACA;MACA;IACA;IACAgG,sBAAA,WAAAA,uBAAA3C,IAAA,EAAAqB,QAAA;MACA,KAAA3C,WAAA,CAAAE,eAAA,GAAA4C,GAAA,CAAAzE,IAAA;IACA;IACA6F,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAtE,YAAA,CAAAuE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,wBAAA,MAAAC,cAAA,CAAAC,OAAA,MACAN,MAAA,CAAArE,YAAA,CACA,EAAAyD,IAAA,WAAAT,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACAoB,MAAA,CAAAO,MAAA,CAAAC,UAAA;cACAR,MAAA,CAAAxD,gBAAA;cACAwD,MAAA,CAAAhF,YAAA;YACA;UACA;QACA;MACA;IACA;IACAyF,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA;MACA;MACA;MACA,KAAAT,KAAA,CAAApE,WAAA,CAAAqE,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAQ,uBAAA,MAAAN,cAAA,CAAAC,OAAA,MACAI,MAAA,CAAA7E,WAAA,CACA,EAAAuD,IAAA,WAAAT,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACA8B,MAAA,CAAAH,MAAA,CAAAC,UAAA;cACAE,MAAA,CAAAlE,gBAAA;cACAkE,MAAA,CAAAzF,WAAA;YACA;UACA;QACA;MACA;IACA;IACA2F,QAAA,WAAAA,SAAA;MACA;MACA;MACA;MACA,IAAAC,IAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,IAAA,CAAAG,IAAA,GACA;MACAH,IAAA,CAAAD,QAAA;MACAC,IAAA,CAAAI,MAAA;MACAJ,IAAA,CAAAK,KAAA;IACA;IAEA1E,gBAAA,WAAAA,iBAAA;MAAA,IAAA2E,MAAA;MACA,IAAA3E,yBAAA,IAAA4C,IAAA,WAAAT,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UAAA,IAAAwC,SAAA,EAAAC,UAAA;UACAF,MAAA,CAAAxF,YAAA,IAAAyF,SAAA,GAAAzC,GAAA,CAAAzE,IAAA,cAAAkH,SAAA,cAAAA,SAAA;UACAD,MAAA,CAAAtF,WAAA,IAAAwF,UAAA,GAAA1C,GAAA,CAAAzE,IAAA,cAAAmH,UAAA,cAAAA,UAAA;UACA,KAAAF,MAAA,CAAAxF,YAAA,CAAAC,gBAAA;YACAuF,MAAA,CAAAxF,YAAA,CAAAC,gBAAA;UACA;UACA,KAAAuF,MAAA,CAAAtF,WAAA,CAAAC,iBAAA;YACAqF,MAAA,CAAAtF,WAAA,CAAAC,iBAAA;UACA;UACA,KAAAqF,MAAA,CAAAtF,WAAA,CAAAE,eAAA;YACAoF,MAAA,CAAAtF,WAAA,CAAAE,eAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}