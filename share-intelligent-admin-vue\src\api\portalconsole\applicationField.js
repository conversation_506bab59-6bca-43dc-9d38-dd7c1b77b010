import request from '@/utils/request'

// 查询应用领域列表
export function listApplicationField(query) {
  return request({
    url: '/portalconsole/applicationField/list',
    method: 'get',
    params: query
  })
}

// 查询应用领域详细
export function getApplicationField(applicationFieldId) {
  return request({
    url: '/portalconsole/applicationField/' + applicationFieldId,
    method: 'get'
  })
}

// 新增应用领域
export function addApplicationField(data) {
  return request({
    url: '/portalconsole/applicationField',
    method: 'post',
    data: data
  })
}

// 修改应用领域
export function updateApplicationField(data) {
  return request({
    url: '/portalconsole/applicationField',
    method: 'put',
    data: data
  })
}

// 删除应用领域
export function delApplicationField(applicationFieldId) {
  return request({
    url: '/portalconsole/applicationField/' + applicationFieldId,
    method: 'delete'
  })
}
