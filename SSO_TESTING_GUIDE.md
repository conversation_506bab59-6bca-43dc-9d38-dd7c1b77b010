# 🧪 SSO登录流程完整测试指南

## 📋 测试环境准备

### 1. 确认服务状态

请确认以下服务已正常启动：

| 服务 | 默认端口 | 访问地址 | 状态检查 |
|------|----------|----------|----------|
| 主系统 (Backend) | 9200 | http://localhost:9200 | ✅ 已启动 |
| 从系统 (Market) | 8081 | http://localhost:8081 | ✅ 已启动 |
| SSO认证服务 | 9100 | http://localhost:9100 | ✅ 已启动 |

### 2. 测试页面

我已为您创建了专用的测试页面：

- **主系统测试页面**: `sso-test-pages/backend-sso-test.html`
- **从系统测试页面**: `sso-test-pages/market-sso-test.html`

## 🚀 测试步骤

### 阶段一：基础功能测试

#### 1. 主系统SSO功能测试

**打开测试页面**:
```bash
# 在浏览器中打开
file:///E:/company/nmd/nmdnew/share-intelligent/sso-test-pages/backend-sso-test.html
```

**测试步骤**:
1. ✅ **检查SSO状态** - 验证服务是否正常响应
2. ✅ **获取SSO登录地址** - 测试登录URL生成
3. ⚠️ **SSO登录跳转** - 需要SSO认证服务
4. ✅ **用户信息获取** - 测试用户信息接口
5. ✅ **权限刷新** - 测试权限更新功能
6. ✅ **SSO登出** - 测试登出功能

#### 2. 从系统SSO功能测试

**打开测试页面**:
```bash
# 在浏览器中打开
file:///E:/company/nmd/nmdnew/share-intelligent/sso-test-pages/market-sso-test.html
```

**测试步骤**:
1. ✅ **检查SSO状态** - 验证服务是否正常响应
2. ✅ **获取主系统登录地址** - 测试跳转URL生成
3. ⚠️ **跳转到主系统** - 需要主系统SSO服务
4. ✅ **用户同步测试** - 测试用户信息同步
5. ✅ **SSO登出** - 测试登出功能

### 阶段二：接口直接测试

#### 使用Postman或curl测试

**1. 检查主系统SSO状态**
```bash
curl -X GET "http://localhost:9200/sso/status" \
  -H "Content-Type: application/json"
```

**2. 检查从系统SSO状态**
```bash
curl -X GET "http://localhost:8081/sso/status" \
  -H "Content-Type: application/json"
```

**3. 获取主系统SSO登录地址**
```bash
curl -X GET "http://localhost:9200/sso/loginUrl?redirect=http://localhost:9200/index" \
  -H "Content-Type: application/json"
```

**4. 获取从系统主系统登录地址**
```bash
curl -X GET "http://localhost:8081/sso/loginUrl?redirect=http://localhost:8081/index" \
  -H "Content-Type: application/json"
```

**5. 测试用户同步（从系统）**
```bash
curl -X POST "http://localhost:8081/sso/sync-user" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "test123",
    "username": "testuser",
    "nickName": "测试用户",
    "email": "<EMAIL>",
    "phonenumber": "13800138000"
  }'
```

### 阶段三：完整流程测试（需要SSO认证服务）

#### 1. 完整SSO登录流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Market as 从系统(8081)
    participant Backend as 主系统(9200)
    participant SSO as SSO认证服务(9300)
    
    User->>Market: 1. 访问从系统
    Market->>Market: 2. 检查登录状态
    Market->>User: 3. 跳转到主系统登录
    User->>Backend: 4. 访问主系统
    Backend->>SSO: 5. 跳转到SSO认证
    User->>SSO: 6. 输入用户名密码
    SSO->>Backend: 7. 回调主系统
    Backend->>Backend: 8. 创建主系统会话
    Backend->>Market: 9. 通知从系统用户登录
    Market->>Market: 10. 创建从系统会话
    User->>Market: 11. 访问从系统（已登录）
```

#### 2. 测试场景

**场景1: 从系统发起登录**
1. 用户访问从系统 → 未登录
2. 从系统跳转到主系统登录
3. 主系统跳转到SSO认证
4. 用户在SSO完成认证
5. 回调到主系统，创建会话
6. 主系统通知从系统用户已登录
7. 从系统创建本地会话
8. 用户可以访问两个系统

**场景2: 主系统发起登录**
1. 用户访问主系统 → 未登录
2. 主系统跳转到SSO认证
3. 用户在SSO完成认证
4. 回调到主系统，创建会话
5. 用户访问从系统时自动登录

**场景3: 单点登出**
1. 用户在任一系统点击登出
2. 清除本地会话
3. 通知SSO服务登出
4. SSO通知所有系统清除会话
5. 用户在所有系统都处于登出状态

## 🔧 故障排查

### 常见问题及解决方案

#### 1. 服务无法访问
**症状**: 测试页面显示"请求失败"
**解决方案**:
- 检查服务是否启动
- 确认端口号是否正确
- 检查防火墙设置

#### 2. CORS跨域问题
**症状**: 浏览器控制台显示CORS错误
**解决方案**:
- 在Spring Boot配置中添加CORS支持
- 或使用浏览器插件禁用CORS检查

#### 3. 404错误
**症状**: 接口返回404
**解决方案**:
- 检查Controller路径映射
- 确认接口URL是否正确

#### 4. 500内部错误
**症状**: 接口返回500错误
**解决方案**:
- 查看服务日志
- 检查数据库连接
- 验证Redis连接

## 📊 测试结果记录

### 测试检查清单

#### 主系统 (Backend)
- [ ] SSO状态检查接口正常
- [ ] SSO登录地址生成正常
- [ ] 用户信息接口正常
- [ ] 权限刷新接口正常
- [ ] SSO登出接口正常

#### 从系统 (Market)
- [ ] SSO状态检查接口正常
- [ ] 主系统登录地址生成正常
- [ ] 用户同步接口正常
- [ ] SSO登出接口正常

#### 集成测试（需要SSO服务）
- [ ] 完整登录流程
- [ ] 跨系统会话同步
- [ ] 单点登出功能
- [ ] 权限同步功能

## 🎯 下一步计划

1. **部署SSO认证服务** - 完成完整的SSO架构
2. **配置数据库** - 设置用户和权限数据
3. **前端集成** - 在实际前端页面中集成SSO功能
4. **性能测试** - 测试高并发场景下的SSO性能
5. **安全测试** - 验证Token安全性和会话管理

## 📞 技术支持

如果在测试过程中遇到问题，请：

1. 查看服务日志文件
2. 检查网络连接
3. 验证配置文件
4. 参考故障排查部分

测试愉快！🚀
