<template>
  <div class="content">
    <div class="content_banner">
      制造共享
      <div class="imgContent">
        <div class="imgStyle">
          <img style="width: 100%; height: 100%" src="../../../assets/order/orderStep.png" alt="" />
          <!-- <div class="joinNow" @click="joinNow">立即入驻</div> -->
        </div>
      </div>
    </div>
    <div class="card-container content_card">
      <el-form ref="form" :rules="rules" :model="form" label-position="top">
        <div class="title">基本信息</div>
        <div class="titleLine"></div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="企业名称" prop="companyName">
              <el-autocomplete v-model="form.companyName" placeholder="请输入您公司的完整名称" style="width: 100%;"
                :fetch-suggestions="querySearchTianYanCha" @select="selectAutoDataTianYanCha"></el-autocomplete>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="社会信用代码">
              <el-input v-model="form.socialCreditCode" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="注册资本">
              <el-input v-model="form.registeredCapital" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话">
              <el-input v-model="form.contactPhone" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="行业">
              <el-input v-model="form.industry" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地址">
              <el-input v-model="form.companyAddress" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item prop="technologyType">
          <div slot="label">经营范围</div>
          <el-input v-model="form.businessScope" type="textarea" resize="none" :rows="8" maxlength="500" show-word-limit
            placeholder="请输入" />
        </el-form-item>
        <div class="title">企业能力</div>
        <div class="titleLine"></div>
        <!-- <el-form-item label="">
          <div slot="label">
            <div style="display: flex; width: 1080px">
              <div>业绩情况</div>
              <div class="addStyle">新增行</div>
            </div>
          </div>
          <el-table :data="jobList">
            <el-table-column label="项目名称" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.jobId"></el-input>
              </template>
</el-table-column>
<el-table-column label="联系人" align="center">
  <template slot-scope="scope">
                <el-input v-model="scope.row.jobId"></el-input>
              </template>
</el-table-column>
<el-table-column label="联系电话" align="center">
  <template slot-scope="scope">
                <el-input v-model="scope.row.jobId"></el-input>
              </template>
</el-table-column>
<el-table-column label="附件" align="center">
  <template slot-scope="scope">
                <el-input v-model="scope.row.jobId"></el-input>
              </template>
</el-table-column>
</el-table>
</el-form-item> -->
        <el-form-item label="">
          <div slot="label">
            <div style="display: flex; width: 1080px">
              <div>人员能力</div>
              <div class="addStyle" @click="addPersonnelList">新增行</div>
            </div>
          </div>
          <el-table :data="form.personnelList">
            <el-table-column label="技术人员姓名" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.technicianName"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="专业技术工种" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.technicalType"></el-input>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="技术实力">
          <el-input v-model="form.technicalCapability" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="">
          <div slot="label">
            <div style="display: flex; width: 1080px">
              <div>
                资质证件
                <span style="color: #999999; font-size: 14px; margin-left: 11px">（专利、商标、资质、证书等）</span>
              </div>
              <div class="addStyle" @click="addQualificationList">新增行</div>
            </div>
          </div>
          <el-table :data="form.qualificationList">
            <el-table-column label="资质名称" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.qualificationName"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="附件" align="center">
              <template slot-scope="scope">
                <ImageUpload :limit="1" v-model="scope.row.attachment" />
                <!-- <el-input v-model="scope.row.jobId"></el-input> -->
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label="">
          <div slot="label">
            <div style="display: flex; width: 1080px">
              <div>设备信息</div>
              <div class="addStyle" @click="addEquipmentList">新增行</div>
            </div>
          </div>
          <el-table :data="form.equipmentList">
            <el-table-column label="生产设备" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.equipmentName"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="规格型号" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.specification"></el-input>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item class="footer-submit">
          <el-button type="primary" @click="onSubmit">提交</el-button>
          <el-button style="margin-left: 140px" @click.once="onCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { enteringFactoryAdd } from "@/api/manufacturingSharing";
import { add } from "ramda";
import { searchCompany, getCompanyCodeByName } from "@/api/system/company";

export default {
  data() {
    return {
      form: {
        companyName: "", // 企业名称
        socialCreditCode: "", // 社会信用代码
        registeredCapital: "", // 注册资本
        contactPhone: "", // 联系电话
        industry: "", // 行业
        companyAddress: "", // 地址
        businessScope: "", // 经营范围
        personnelList: [], // 人员能力
        qualificationList: [], // 资质证件
        equipmentList: [], // 设备信息列表
        settledStatus: "0", // 默认传待审核
      },
      rules: {
        companyName: [
          { required: true, message: "请输入企业名称", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          enteringFactoryAdd(this.form).then((res) => {
            if (res.code === 200) {
              this.$message.success("操作成功");
              this.$router.go(-1);
            }
          });
        }
      });
    },
    onCancel() { },
    addPersonnelList() {
      this.form.personnelList.push({
        technicianName: "",
        technicalType: "",
      });
    },
    addQualificationList() {
      this.form.qualificationList.push({
        qualificationName: "",
        attachment: "",
      });
    },
    addEquipmentList() {
      this.form.equipmentList.push({
        equipmentName: "",
        specification: "",
      });
    },
    // 企业名称
    querySearchTianYanCha(queryString, cb) {
      if (queryString) {
        searchCompany({ keywords: queryString }).then(res => {
          let data = res.rows;
          let List = [];
          data.forEach(function (val, index) {
            List.push({
              id: index,
              value: val
            })
          })
          if (data.length > 0) {
            cb(List);
          } else {
            cb([{
              id: '',
              value: '暂无数据'
            }]);
          }
        });
      }
    },
    // 企业名称选择
    selectAutoDataTianYanCha(row) {
      getCompanyCodeByName({ keywords: row.value }).then(res => {
        if (res.code == 200) {
          let data = res.data;
          this.$set(this.form, 'socialCreditCode', data.taxNo)
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.content {
  width: 100%;
  padding-bottom: 60px;
  background-color: #f2f2f2;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  padding-top: 28px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;

  .imgContent {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 10px;

    .imgStyle {
      width: 1256px;
      height: 206px;
      position: relative;
      // .joinNow {
      //   position: absolute;
      //   right: 90px;
      //   top: 75px;
      //   width: 110px;
      //   height: 50px;
      //   background: #f79a47;
      //   border-radius: 2px;
      //   font-family: Source Han Sans CN;
      //   font-weight: 400;
      //   font-size: 18px;
      //   color: #ffffff;
      //   line-height: 50px;
      //   text-align: center;
      //   cursor: pointer;
      // }
    }
  }
}

.content_card {
  // height: 1530px;
  background: #ffffff;
  border-radius: 2px;
  margin-top: 30px;
  padding: 59px 60px 57px 60px;
}

.title {
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 18px;
  color: #21c9b8;
}

.titleLine {
  width: 100%;
  height: 1px;
  background: #21c9b8;
  margin: 20px 0 30px 0;
}

.addStyle {
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #21c9b8;
  margin-left: auto;
  cursor: pointer;
}

.footer-submit {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 60px;

  .el-button {
    width: 140px;
    height: 50px;
  }
}
</style>
