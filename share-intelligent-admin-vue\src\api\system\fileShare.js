import request from '@/utils/request'

// 查询文件共享列表
export function listFileShare(query) {
  return request({
    url: '/system/fileShare/list',
    method: 'get',
    params: query
  })
}

// 查询文件共享详细
export function getFileShare(id) {
  return request({
    url: '/system/fileShare/' + id,
    method: 'get'
  })
}

// 新增文件共享
export function addFileShare(data) {
  return request({
    url: '/system/fileShare',
    method: 'post',
    data: data
  })
}

// 修改文件共享
export function updateFileShare(data) {
  return request({
    url: '/system/fileShare',
    method: 'put',
    data: data
  })
}

// 删除文件共享
export function delFileShare(id) {
  return request({
    url: '/system/fileShare/' + id,
    method: 'delete'
  })
}
