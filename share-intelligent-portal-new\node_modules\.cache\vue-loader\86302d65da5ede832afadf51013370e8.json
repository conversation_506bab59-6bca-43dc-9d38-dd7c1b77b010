{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\im\\components\\video.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\im\\components\\video.vue", "mtime": 1750311963058}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1************}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["video.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA", "file": "video.vue", "sourceRoot": "src/views/system/user/im/components", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-05-31 11:30:14\r\n * @LastEditTime: 2023-05-31 11:51:13\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"video\">\r\n    <!-- 使用组件 -->\r\n    <video-player\r\n      class=\"video-player vjs-custom-skin\"\r\n      ref=\"videoPlayer\"\r\n      :playsinline=\"true\"\r\n      :options=\"playerOptions\"\r\n    ></video-player>\r\n  </div>\r\n</template>\r\n\r\n<script type=\"text/ecmascript-6\">\r\n// 以下三行一定要引入\r\nimport { videoPlayer } from 'vue-video-player'\r\nimport 'video.js/dist/video-js.css'\r\nimport 'vue-video-player/src/custom-theme.css'\r\n// import 'video.js/dist/lang/zh-CN'\r\n\r\nexport default {\r\n// name: 'videoplayer',\r\ncomponents: { // 必需引入\r\n  videoPlayer\r\n},\r\nprops: [ // 接收父组件的数据\r\n  'mp4Pic',\r\n  'mp4Url'\r\n],\r\ndata () {\r\n  return {\r\n    fileAreaHeight: 100,\r\n    fileType: 'mp4', // 资源的类型\r\n  }\r\n},\r\ncomputed: { // 使用计算属性\r\n    playerOptions () {\r\n      const playerOptionsObj = {\r\n        playbackRates: [0.7, 1.0, 1.5, 2.0], //视频播放速度\r\n        autoplay: true, // 如果true，浏览器准备好时开始回放。\r\n        muted: false, // 默认情况下将会消除任何音频。\r\n        loop: false, // 导致视频一结束就重新开始。\r\n        // preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）。\r\n        language: 'zh-CN',\r\n        // aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如\"16:9\"或\"4:3\"）。\r\n        fluid: false, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。\r\n        sources: [{\r\n          type: 'video/' + this.fileType,\t// 资源格式写法：'video/mp4'，否则控制台会出现notSupportedMessage设置的错误。\r\n          src: this.mp4Url // 视频url地址\r\n        }],\r\n        poster: this.mp4Pic, // 视频封面地址\r\n        // width: document.documentElement.clientWidth,\r\n        height: this.fileAreaHeight,\t// 设置高度，fluid需要设置成flase\r\n        notSupportedMessage: '此视频暂无法播放...', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。\r\n        controlBar: {\r\n          timeDivider: true,\r\n          durationDisplay: true,\r\n          remainingTimeDisplay: false,\r\n          fullscreenToggle: true  //全屏按钮\r\n        }\r\n      }\r\n      return playerOptionsObj\r\n    }\r\n  },\r\n  watch: {\r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.video /*可不设置*/ {\r\n  .video-player {\r\n    height: 500px;\r\n  }\r\n  .vjs-custom-skin > .video-js {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  // position:relative;\r\n  // padding:5px;\r\n  // padding-top: 70px;\r\n  //  .vjs-poster /*对画面静止的样式进行设置放大一倍*/\r\n  //  {\r\n  //        transform: scale(2)\r\n  //  }\r\n  //  .vjs-tech /*对画面播放的样式进行设置放大一倍*/\r\n  //  {\r\n  //      transform: scale(2)\r\n  //  }\r\n  //   .video-js .vjs-big-play-button /*对播放按钮的样式进行设置*/\r\n  //   {\r\n  //       width: 80px;\r\n  //         height: 100%;\r\n  //         border-radius: 1em;\r\n  //   }\r\n}\r\n</style>\r\n"]}]}