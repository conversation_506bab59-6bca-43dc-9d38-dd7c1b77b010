{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\demand\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\demand\\index.vue", "mtime": 1750311962949}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXREaWN0cyB9IGZyb20gIkAvYXBpL3N5c3RlbS9kaWN0L2RhdGEiOw0KaW1wb3J0IHsgZ2F0ZXdheURlbWFuZExpc3RTaG93IH0gZnJvbSAiQC9hcGkvcHVyY2hhc2VTYWxlcyI7DQppbXBvcnQgQ3J5cHRvSlMgZnJvbSAiY3J5cHRvLWpzIjsNCmxldCBzZWNyZXRLZXkgPSAiOXpWbjAlYnFtVVlTR3cybiI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgZml0OiAiY292ZXIiLA0KICAgICAgZGVtYW5kTG9hZGluZzogZmFsc2UsDQogICAgICBmb3JtOiB7DQogICAgICAgIGtleXdvcmRzOiAiIiwgLy/mkJzntKLlhoXlrrkNCiAgICAgIH0sDQogICAgICBmb3JtSW5mbzogew0KICAgICAgICBkZW1hbmRUeXBlOiAiIiwgLy/pnIDmsYLnsbvlnosNCiAgICAgIH0sDQogICAgICBkZW1hbmRUeXBlTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgZGljdExhYmVsOiAi5Yib5paw56CU5Y+RIiwNCiAgICAgICAgICBkaWN0VmFsdWU6ICIxIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RMYWJlbDogIueJqeaWmemHh+i0rSIsDQogICAgICAgICAgZGljdFZhbHVlOiAiMiIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0TGFiZWw6ICLmmbrog73liLbpgKAiLA0KICAgICAgICAgIGRpY3RWYWx1ZTogIjMiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdExhYmVsOiAi5pWw5a2X5YyW566h55CGIiwNCiAgICAgICAgICBkaWN0VmFsdWU6ICI0IiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RMYWJlbDogIui9r+S7tuacjeWKoSIsDQogICAgICAgICAgZGljdFZhbHVlOiAiNSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBkaWN0TGFiZWw6ICLkvpvlupTpk77ph5Hono0iLA0KICAgICAgICAgIGRpY3RWYWx1ZTogIjYiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgZGljdExhYmVsOiAi6L+Q6JCl5a6j5LygIiwNCiAgICAgICAgICBkaWN0VmFsdWU6ICI3IiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIGRpY3RMYWJlbDogIuWFtuS7liIsDQogICAgICAgICAgZGljdFZhbHVlOiAiOCIsDQogICAgICAgIH0sDQogICAgICBdLCAvL+a0u+WKqOexu+Wei+WIl+ihqA0KICAgICAgZGF0YTogW10sDQogICAgICBwYWdlTnVtOiAxLA0KICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgdG90YWw6IDAsDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICAvLyB0aGlzLmdldERpY3RzTGlzdCgiYWN0aXZpdHlfdHlwZSIsICJhY3Rpdml0eVR5cGVMaXN0Iik7DQogICAgdGhpcy5nZXREZW1hbmRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDmib7pnIDmsYINCiAgICBnZXREZW1hbmRMaXN0KCkgew0KICAgICAgdGhpcy5kZW1hbmRMb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGdhdGV3YXlEZW1hbmRMaXN0U2hvdyh7DQogICAgICAgIGRlbWFuZFR5cGU6IHRoaXMuZm9ybUluZm8uZGVtYW5kVHlwZSwNCiAgICAgICAgLy8gY2l0eTogIumdkuWym+W4giIsDQogICAgICAgIC8vIHJlZ2lvbjogIuWfjumYs+WMuiIsDQogICAgICAgIGRpc3BsYXlTdGF0dXM6IDEsDQogICAgICAgIGF1ZGl0U3RhdHVzOiAyLA0KICAgICAgICBwYWdlTnVtOiB0aGlzLnBhZ2VOdW0sDQogICAgICAgIHBhZ2VTaXplOiB0aGlzLnBhZ2VTaXplLA0KICAgICAgICBuYW1lOiB0aGlzLmZvcm0ua2V5d29yZHMsDQogICAgICB9KQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgbGV0IGtleSA9IENyeXB0b0pTLmVuYy5VdGY4LnBhcnNlKHNlY3JldEtleSk7DQogICAgICAgICAgbGV0IGRlY3J5cHQgPSBDcnlwdG9KUy5BRVMuZGVjcnlwdChyZXMsIGtleSwgew0KICAgICAgICAgICAgbW9kZTogQ3J5cHRvSlMubW9kZS5FQ0IsDQogICAgICAgICAgICBwYWRkaW5nOiBDcnlwdG9KUy5wYWQuUGtjczcsDQogICAgICAgICAgfSk7DQogICAgICAgICAgcmVzID0gSlNPTi5wYXJzZShDcnlwdG9KUy5lbmMuVXRmOC5zdHJpbmdpZnkoZGVjcnlwdCkpOw0KICAgICAgICAgIHRoaXMuZGVtYW5kTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIGxldCB7IHJvd3MgfSA9IHJlcyB8fCBbXTsNCiAgICAgICAgICB0aGlzLmRhdGEgPSByb3dzOw0KICAgICAgICAgIHRoaXMudG90YWwgPSByZXMudG90YWw7DQogICAgICAgICAgdGhpcy5kYXRhLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgIGlmIChpdGVtLnNjZW5lUGljdHVyZSkgew0KICAgICAgICAgICAgICBpdGVtLnNjZW5lUGljdHVyZSA9IEpTT04ucGFyc2UoaXRlbS5zY2VuZVBpY3R1cmUpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgaWYgKGl0ZW0uYXBwbGljYXRpb25BcmVhKSB7DQogICAgICAgICAgICAgIGl0ZW0uYXBwbGljYXRpb25BcmVhID0gaXRlbS5hcHBsaWNhdGlvbkFyZWEuc3BsaXQoIiwiKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmRlbWFuZExvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmRhdGEgPSBbXTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlrZflhbgNCiAgICBnZXREaWN0c0xpc3QoY29kZSwgcHJvcGVydHlOYW1lKSB7DQogICAgICBnZXREaWN0cyhjb2RlKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpc1twcm9wZXJ0eU5hbWVdID0gcmVzLmRhdGEgfHwgW107DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNoYW5nZVJhZGlvKCkgew0KICAgICAgdGhpcy5vblNlYXJjaCgpOw0KICAgIH0sDQogICAgaGFuZGxlU2l6ZUNoYW5nZShwYWdlU2l6ZSkgew0KICAgICAgdGhpcy5wYWdlU2l6ZSA9IHBhZ2VTaXplOw0KICAgICAgdGhpcy5vblNlYXJjaCgpOw0KICAgIH0sDQogICAgaGFuZGxlQ3VycmVudENoYW5nZShwYWdlTnVtKSB7DQogICAgICB0aGlzLnBhZ2VOdW0gPSBwYWdlTnVtOw0KICAgICAgdGhpcy5nZXREZW1hbmRMaXN0KCk7DQogICAgfSwNCiAgICBvblNlYXJjaCgpIHsNCiAgICAgIHRoaXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldERlbWFuZExpc3QoKTsNCiAgICB9LA0KICAgIC8vIOi3s+i9rOWIsOmcgOaxgumhtemdog0KICAgIGdvRGVtYW5kRGV0YWlsKGlkKSB7DQogICAgICBsZXQgcm91dGVEYXRhID0gdGhpcy4kcm91dGVyLnJlc29sdmUoew0KICAgICAgICBwYXRoOiAiL2RlbWFuZEhhbGxEZXRhaWwiLA0KICAgICAgICBxdWVyeTogeyBpZCB9LA0KICAgICAgfSk7DQogICAgICB3aW5kb3cub3Blbihyb3V0ZURhdGEuaHJlZiwgIl9ibGFuayIpOw0KICAgIH0sDQogICAgZ29Ib21lKCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAiL2luZGV4IiB9KTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsJA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/demand", "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-01-30 11:29:06\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-02-13 11:28:34\r\n-->\r\n<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/demand/demandBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div>\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-title-box\">\r\n          <div class=\"activity-divider\"></div>\r\n          <div class=\"activity-title\">链需求</div>\r\n          <div class=\"activity-divider\"></div>\r\n        </div>\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.keywords\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"activity-info-content\">\r\n        <div class=\"activity-search-type-box\">\r\n          <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n            <div class=\"activity-search-line\">\r\n              <el-form-item label=\"需求类型\" class=\"activity-search-line-item\">\r\n                <el-radio-group\r\n                  v-model=\"formInfo.demandType\"\r\n                  class=\"activity-search-radio\"\r\n                  @input=\"changeRadio\"\r\n                >\r\n                  <el-radio-button label=\"\">全部</el-radio-button>\r\n                  <el-radio-button\r\n                    v-for=\"(item, index) in demandTypeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.dictValue\"\r\n                    >{{ item.dictLabel }}</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n        <div v-loading=\"demandLoading\" v-if=\"data && data.length > 0\">\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"activity-list-item\"\r\n            @click=\"goDemandDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img\r\n                  v-if=\"item.scenePicture && item.scenePicture.length > 0\"\r\n                  :src=\"item.scenePicture[0].url\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../assets/purchaseSales/demandDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"list-item-info\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.demandTitle }}\r\n                </div>\r\n                <div\r\n                  style=\"\r\n                    margin-top: 10px;\r\n                    margin-left: 10px;\r\n                    display: flex;\r\n                    align-items: center;\r\n                  \"\r\n                >\r\n                  <div\r\n                    style=\"\r\n                      height: 30px;\r\n                      line-height: 28px;\r\n                      color: rgb(77, 77, 78);\r\n                    \"\r\n                  >\r\n                    应用领域:\r\n                  </div>\r\n                  <div\r\n                    style=\"margin-left: 10px; height: 30px; line-height: 28px\"\r\n                  >\r\n                    <el-tag\r\n                      class=\"tagStyle\"\r\n                      v-for=\"(areaItem, index) in item.applicationArea\"\r\n                      :key=\"index\"\r\n                      >{{ areaItem }}</el-tag\r\n                    >\r\n                    <!-- <el-tag style=\"margin-left: 20px\">空调外机</el-tag>\r\n                    <el-tag style=\"margin-left: 20px\">语言芯片</el-tag> -->\r\n                  </div>\r\n                </div>\r\n                <!-- <div class=\"list-item-text\">\r\n                {{ item.activityOverview }}\r\n              </div>\r\n              <div class=\"list-item-time\">{{ item.createTimeStr }}</div> -->\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"none-class\" v-else>\r\n          <el-image\r\n            style=\"width: 160px; height: 160px\"\r\n            :src=\"require('@/assets/user/none.png')\"\r\n            :fit=\"fit\"\r\n          ></el-image>\r\n          <div class=\"text\">暂无数据</div>\r\n        </div>\r\n        <div class=\"activity-page-end\">\r\n          <el-button class=\"activity-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"data && data.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { gatewayDemandListShow } from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      demandLoading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        demandType: \"\", //需求类型\r\n      },\r\n      demandTypeList: [\r\n        {\r\n          dictLabel: \"创新研发\",\r\n          dictValue: \"1\",\r\n        },\r\n        {\r\n          dictLabel: \"物料采购\",\r\n          dictValue: \"2\",\r\n        },\r\n        {\r\n          dictLabel: \"智能制造\",\r\n          dictValue: \"3\",\r\n        },\r\n        {\r\n          dictLabel: \"数字化管理\",\r\n          dictValue: \"4\",\r\n        },\r\n        {\r\n          dictLabel: \"软件服务\",\r\n          dictValue: \"5\",\r\n        },\r\n        {\r\n          dictLabel: \"供应链金融\",\r\n          dictValue: \"6\",\r\n        },\r\n        {\r\n          dictLabel: \"运营宣传\",\r\n          dictValue: \"7\",\r\n        },\r\n        {\r\n          dictLabel: \"其他\",\r\n          dictValue: \"8\",\r\n        },\r\n      ], //活动类型列表\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    // this.getDictsList(\"activity_type\", \"activityTypeList\");\r\n    this.getDemandList();\r\n  },\r\n  methods: {\r\n    // 找需求\r\n    getDemandList() {\r\n      this.demandLoading = true;\r\n      gatewayDemandListShow({\r\n        demandType: this.formInfo.demandType,\r\n        // city: \"青岛市\",\r\n        // region: \"城阳区\",\r\n        displayStatus: 1,\r\n        auditStatus: 2,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        name: this.form.keywords,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          this.demandLoading = false;\r\n          let { rows } = res || [];\r\n          this.data = rows;\r\n          this.total = res.total;\r\n          this.data.forEach((item) => {\r\n            if (item.scenePicture) {\r\n              item.scenePicture = JSON.parse(item.scenePicture);\r\n            }\r\n            if (item.applicationArea) {\r\n              item.applicationArea = item.applicationArea.split(\",\");\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.demandLoading = false;\r\n          this.data = [];\r\n        });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getDemandList();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.getDemandList();\r\n    },\r\n    // 跳转到需求页面\r\n    goDemandDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/demandHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-info-content {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    .activity-search-type-box {\r\n      background: #fff;\r\n      margin-bottom: -7px;\r\n      .activity-search-line {\r\n        padding: 14px 24px 4px;\r\n        .activity-search-line-item {\r\n          margin-bottom: 0;\r\n        }\r\n        & + .activity-search-line {\r\n          border-top: 1px solid #f5f5f5;\r\n        }\r\n      }\r\n    }\r\n    .activity-list-item {\r\n      width: 100%;\r\n      background: #fff;\r\n      border-radius: 12px;\r\n      margin-top: 24px;\r\n      .list-item-content {\r\n        display: flex;\r\n        padding: 24px 32px;\r\n        cursor: pointer;\r\n        .list-item-img {\r\n          width: 230px;\r\n          height: 164px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n            border-radius: 5px;\r\n          }\r\n        }\r\n        .list-item-info {\r\n          margin: auto 0;\r\n          padding-left: 24px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          .list-item-title {\r\n            width: 806px;\r\n            // height: 24px;\r\n            text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n            white-space: nowrap; /*让文字不换行*/\r\n            overflow: hidden; /*超出要隐藏*/\r\n            font-size: 24px;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            // line-height: 24px;\r\n            margin: 8px 0 24px;\r\n            word-wrap: break-word;\r\n          }\r\n          .list-item-text {\r\n            width: 806px;\r\n            height: 60px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            text-overflow: ellipsis;\r\n            font-size: 16px;\r\n            color: #666;\r\n            line-height: 30px;\r\n            word-wrap: break-word;\r\n          }\r\n          .list-item-time {\r\n            color: #999;\r\n            line-height: 14px;\r\n            margin-top: 24px;\r\n          }\r\n          .tagStyle {\r\n            margin-left: 20px;\r\n          }\r\n          .tagStyle:nth-child(1) {\r\n            margin-left: 0;\r\n          }\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .activity-page-end {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin: 0 auto;\r\n      padding: 24px 0 60px;\r\n      .activity-page-btn {\r\n        width: 82px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    padding: 8% 0;\r\n    background: #fff;\r\n    margin-top: 25px;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}