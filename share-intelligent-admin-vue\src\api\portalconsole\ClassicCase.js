import request from '@/utils/request'

// 查询典型案例列表
export function listClassicCase(query) {
  return request({
    url: '/portalconsole/ClassicCase/list',
    method: 'get',
    params: query
  })
}

// 查询典型案例详细
export function getClassicCase(classicCaseId) {
  return request({
    url: '/portalconsole/ClassicCase/' + classicCaseId,
    method: 'get'
  })
}

// 新增典型案例
export function addClassicCase(data) {
  return request({
    url: '/portalconsole/ClassicCase',
    method: 'post',
    data: data
  })
}

// 修改典型案例
export function updateClassicCase(data) {
  return request({
    url: '/portalconsole/ClassicCase',
    method: 'put',
    data: data
  })
}

// 删除典型案例
export function delClassicCase(classicCaseId) {
  return request({
    url: '/portalconsole/ClassicCase/' + classicCaseId,
    method: 'delete'
  })
}
//动态资讯查询资讯行业的数据
export function listSolutionType(query) {
  return request({
    url: '/portalconsole/solutionType/list',
    method: 'get',
    params: query
  })
}
