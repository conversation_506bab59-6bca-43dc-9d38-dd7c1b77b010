package com.ruoyi.portalweb.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 政策申报记录对象 policy_submit_record
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
public class PolicySubmitRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 政策申报记录ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long policySubmitRecordId;

    /**
     * 政策申报ID
     */
    @Excel(name = "政策申报ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long policySubmitId;

    /**
     * 公司id
     */
    @Excel(name = "公司id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long companyId;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date policySubmitRecordTime;

    /**
     * 政策申报状态：业务字典
     */
    @Excel(name = "政策申报状态：业务字典")
    private String policySubmitRecordStatus;

    /**
     * 审核人
     */
    @Excel(name = "审核人")
    private String policySubmitRecordAuditor;

    /**
     * 审核时间
     */
    @Excel(name = "审核时间")
    private String policySubmitRecordAudittime;

    /**
     * 上传文件
     */
    @Excel(name = "上传文件")
    private String policySubmitRecordFileUrl;

    /**
     * 联系人
     */
    @Excel(name = "联系人")
    private String contact;

    /**
     * 联系电话
     */
    @Excel(name = "联系电话")
    private String phone;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 用户id
     */
    private Long memberId;

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public void setPolicySubmitRecordId(Long policySubmitRecordId) {
        this.policySubmitRecordId = policySubmitRecordId;
    }

    public Long getPolicySubmitRecordId() {
        return policySubmitRecordId;
    }

    public void setPolicySubmitId(Long policySubmitId) {
        this.policySubmitId = policySubmitId;
    }

    public Long getPolicySubmitId() {
        return policySubmitId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setPolicySubmitRecordTime(Date policySubmitRecordTime) {
        this.policySubmitRecordTime = policySubmitRecordTime;
    }

    public Date getPolicySubmitRecordTime() {
        return policySubmitRecordTime;
    }

    public void setPolicySubmitRecordStatus(String policySubmitRecordStatus) {
        this.policySubmitRecordStatus = policySubmitRecordStatus;
    }

    public String getPolicySubmitRecordStatus() {
        return policySubmitRecordStatus;
    }

    public void setPolicySubmitRecordAuditor(String policySubmitRecordAuditor) {
        this.policySubmitRecordAuditor = policySubmitRecordAuditor;
    }

    public String getPolicySubmitRecordAuditor() {
        return policySubmitRecordAuditor;
    }

    public void setPolicySubmitRecordAudittime(String policySubmitRecordAudittime) {
        this.policySubmitRecordAudittime = policySubmitRecordAudittime;
    }

    public String getPolicySubmitRecordAudittime() {
        return policySubmitRecordAudittime;
    }

    public String getPolicySubmitRecordFileUrl() {
        return policySubmitRecordFileUrl;
    }

    public void setPolicySubmitRecordFileUrl(String policySubmitRecordFileUrl) {
        this.policySubmitRecordFileUrl = policySubmitRecordFileUrl;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDelFlag() {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("policySubmitRecordId", getPolicySubmitRecordId())
                .append("policySubmitId", getPolicySubmitId())
                .append("companyId", getCompanyId())
                .append("policySubmitRecordTime", getPolicySubmitRecordTime())
                .append("policySubmitRecordStatus", getPolicySubmitRecordStatus())
                .append("policySubmitRecordAuditor", getPolicySubmitRecordAuditor())
                .append("policySubmitRecordAudittime", getPolicySubmitRecordAudittime())
                .append("policySubmitRecordFileUrl", getPolicySubmitRecordFileUrl())
                .append("contact", getContact())
                .append("phone", getPhone())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
