{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\activitySquare\\index.vue?vue&type=style&index=0&id=0dcb9c7f&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\activitySquare\\index.vue", "mtime": 1750311962983}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hY3Rpdml0eS1jb250YWluZXIgew0KICB3aWR0aDogMTAwJTsNCiAgYmFja2dyb3VuZDogI2Y0ZjVmOTsNCiAgLmFjdGl2aXR5LWJhbm5lciB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgaGVpZ2h0OiA1MHZoOw0KICAgIGltZyB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGhlaWdodDogMTAwJTsNCiAgICB9DQogIH0NCiAgLmFjdGl2aXR5LXRpdGxlLWNvbnRlbnQgew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogICAgcGFkZGluZy1ib3R0b206IDE4cHg7DQogICAgLmFjdGl2aXR5LXRpdGxlLWJveCB7DQogICAgICB3aWR0aDogMzM2cHg7DQogICAgICBtYXJnaW46IDAgYXV0bzsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgcGFkZGluZzogNjBweCAwIDQwcHg7DQogICAgICAuYWN0aXZpdHktdGl0bGUgew0KICAgICAgICBmb250LXNpemU6IDQwcHg7DQogICAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLU1lZGl1bSwgUGluZ0ZhbmcgU0M7DQogICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgICBsaW5lLWhlaWdodDogNDBweDsNCiAgICAgICAgcGFkZGluZzogMCA0MHB4Ow0KICAgICAgfQ0KICAgICAgLmFjdGl2aXR5LWRpdmlkZXIgew0KICAgICAgICB3aWR0aDogNDhweDsNCiAgICAgICAgaGVpZ2h0OiA0cHg7DQogICAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICB9DQogICAgfQ0KICAgIC5hY3Rpdml0eS1zZWFyY2gtYm94IHsNCiAgICAgIC5hY3Rpdml0eS1zZWFyY2gtZm9ybSB7DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgLmFjdGl2aXR5LXNlYXJjaC1pbnB1dCB7DQogICAgICAgICAgd2lkdGg6IDc5MnB4Ow0KICAgICAgICAgIGhlaWdodDogNTRweDsNCiAgICAgICAgICAuYWN0aXZpdHktc2VhcmNoLWJ0biB7DQogICAgICAgICAgICB3aWR0aDogMTAwcHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQogIC5hY3Rpdml0eS1pbmZvLWNvbnRlbnQgew0KICAgIHdpZHRoOiAxMjAwcHg7DQogICAgbWFyZ2luOiA0MHB4IGF1dG8gMDsNCiAgICAuYWN0aXZpdHktc2VhcmNoLXR5cGUtYm94IHsNCiAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICBtYXJnaW4tYm90dG9tOiAtN3B4Ow0KICAgICAgLmFjdGl2aXR5LXNlYXJjaC1saW5lIHsNCiAgICAgICAgcGFkZGluZzogMTRweCAyNHB4IDRweDsNCiAgICAgICAgLmFjdGl2aXR5LXNlYXJjaC1saW5lLWl0ZW0gew0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDA7DQogICAgICAgIH0NCiAgICAgICAgJiArIC5hY3Rpdml0eS1zZWFyY2gtbGluZSB7DQogICAgICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkICNmNWY1ZjU7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogICAgLmFjdGl2aXR5LWxpc3QtaXRlbSB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICBib3JkZXItcmFkaXVzOiAxMnB4Ow0KICAgICAgbWFyZ2luLXRvcDogMjRweDsNCiAgICAgIC5saXN0LWl0ZW0tY29udGVudCB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIHBhZGRpbmc6IDI0cHggMzJweDsNCiAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgICAubGlzdC1pdGVtLWltZyB7DQogICAgICAgICAgd2lkdGg6IDIzMHB4Ow0KICAgICAgICAgIGhlaWdodDogMTY0cHg7DQogICAgICAgICAgaW1nIHsNCiAgICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNXB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAubGlzdC1pdGVtLWluZm8gew0KICAgICAgICAgIHBhZGRpbmctbGVmdDogMjRweDsNCiAgICAgICAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1SZWd1bGFyLCBQaW5nRmFuZyBTQzsNCiAgICAgICAgICAubGlzdC1pdGVtLXRpdGxlIHsNCiAgICAgICAgICAgIHdpZHRoOiA4MDZweDsNCiAgICAgICAgICAgIGhlaWdodDogMjRweDsNCiAgICAgICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOyAvKuiuqeaIquaWreeahOaWh+Wtl+aYvuekuuS4uueCueeCueOAgui/mOacieS4gOS4quWAvOaYr2NsaXDmhI/miKrmlq3kuI3mmL7npLrngrnngrkqLw0KICAgICAgICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsgLyrorqnmloflrZfkuI3mjaLooYwqLw0KICAgICAgICAgICAgb3ZlcmZsb3c6IGhpZGRlbjsgLyrotoXlh7ropoHpmpDol48qLw0KICAgICAgICAgICAgZm9udC1zaXplOiAyNHB4Ow0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICAgIGNvbG9yOiAjMzIzMjMzOw0KICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgICAgICAgICBtYXJnaW46IDhweCAwIDI0cHg7DQogICAgICAgICAgICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7DQogICAgICAgICAgfQ0KICAgICAgICAgIC5saXN0LWl0ZW0tdGV4dCB7DQogICAgICAgICAgICB3aWR0aDogODA2cHg7DQogICAgICAgICAgICBoZWlnaHQ6IDYwcHg7DQogICAgICAgICAgICBvdmVyZmxvdzogaGlkZGVuOw0KICAgICAgICAgICAgZGlzcGxheTogLXdlYmtpdC1ib3g7DQogICAgICAgICAgICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsOw0KICAgICAgICAgICAgLXdlYmtpdC1saW5lLWNsYW1wOiAyOw0KICAgICAgICAgICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICBjb2xvcjogIzY2NjsNCiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAzMHB4Ow0KICAgICAgICAgICAgd29yZC13cmFwOiBicmVhay13b3JkOw0KICAgICAgICAgIH0NCiAgICAgICAgICAubGlzdC1pdGVtLXRpbWUgew0KICAgICAgICAgICAgY29sb3I6ICM5OTk7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMTRweDsNCiAgICAgICAgICAgIG1hcmdpbi10b3A6IDI0cHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgICY6aG92ZXIgew0KICAgICAgICAgIC5saXN0LWl0ZW0tdGl0bGUgew0KICAgICAgICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICAgIC5hY3Rpdml0eS1wYWdlLWVuZCB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgbWFyZ2luOiAwIGF1dG87DQogICAgICBwYWRkaW5nOiAyNHB4IDAgNjBweDsNCiAgICAgIC5hY3Rpdml0eS1wYWdlLWJ0biB7DQogICAgICAgIHdpZHRoOiA4MnB4Ow0KICAgICAgICBoZWlnaHQ6IDMycHg7DQogICAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtUmVndWxhciwgUGluZ0ZhbmcgU0M7DQogICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgICBsaW5lLWhlaWdodDogMTBweDsNCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLmNvbnRlbnQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgLy8ganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIGZsZXgtd3JhcDogd3JhcDsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBoZWlnaHQ6IDEwMCU7DQogICAgbWFyZ2luLXRvcDogMjBweDsNCiAgICBwYWRkaW5nLWJvdHRvbTogMzBweDsNCiAgICAuY29udGVudEl0ZW0gew0KICAgICAgd2lkdGg6IDMyJTsNCiAgICAgIGhlaWdodDogMzQwcHg7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICBwYWRkaW5nOiAyMHB4IDMwcHg7DQogICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgbWFyZ2luOiAyMHB4IDAgMCAyM3B4Ow0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgaW1nIHsNCiAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgIGhlaWdodDogMjMwcHg7DQogICAgICB9DQogICAgICAuY29udGVudFRpdGxlIHsNCiAgICAgICAgbWFyZ2luLXRvcDogMTVweDsNCiAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICBmb250LWZhbWlseTogU291cmNlIEhhbiBTYW5zIENOOw0KICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICBjb2xvcjogIzIyMjIyMjsNCiAgICAgIH0NCiAgICB9DQogICAgLmNvbnRlbnRJdGVtOm50aC1jaGlsZCgzbiArIDEpIHsNCiAgICAgIG1hcmdpbi1sZWZ0OiAwOw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/purchaseSales/component/activitySquare", "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-01-30 11:29:06\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-02-13 11:28:34\r\n-->\r\n<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img\r\n        src=\"../../../../assets/activitySquare/activitySquareBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-title-box\">\r\n          <div class=\"activity-divider\"></div>\r\n          <div class=\"activity-title\">链活动</div>\r\n          <div class=\"activity-divider\"></div>\r\n        </div>\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.keywords\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"activity-info-content\">\r\n        <div class=\"activity-search-type-box\">\r\n          <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n            <div class=\"activity-search-line\">\r\n              <el-form-item label=\"活动类型\" class=\"activity-search-line-item\">\r\n                <el-radio-group\r\n                  v-model=\"formInfo.activityType\"\r\n                  class=\"activity-search-radio\"\r\n                  @input=\"changeRadio\"\r\n                >\r\n                  <el-radio-button label=\"\">全部</el-radio-button>\r\n                  <el-radio-button\r\n                    v-for=\"(item, index) in activityTypeList\"\r\n                    :key=\"index\"\r\n                    :label=\"item.dictValue\"\r\n                    >{{ item.dictLabel }}</el-radio-button\r\n                  >\r\n                </el-radio-group>\r\n              </el-form-item>\r\n            </div>\r\n          </el-form>\r\n        </div>\r\n        <div class=\"content\">\r\n          <div\r\n            class=\"contentItem\"\r\n            v-for=\"item in data\"\r\n            :key=\"item.id\"\r\n            @click=\"goActivityDetail(item.id)\"\r\n          >\r\n            <div>\r\n              <img :src=\"item.activityPicture\" alt=\"\" />\r\n            </div>\r\n            <div class=\"contentTitle\">{{ item.activityName }}</div>\r\n          </div>\r\n        </div>\r\n        <!-- <div\r\n          v-for=\"(item, index) in data\"\r\n          :key=\"index\"\r\n          class=\"activity-list-item\"\r\n          @click=\"goActivityDetail(item.id)\"\r\n        >\r\n          <div class=\"list-item-content\">\r\n            <div class=\"list-item-img\">\r\n              <img\r\n                v-if=\"item.activityPicture\"\r\n                alt=\"\"\r\n                :src=\"item.activityPicture\"\r\n              />\r\n            </div>\r\n            <div class=\"list-item-info\">\r\n              <div class=\"list-item-title\">\r\n                {{ item.activityName }}\r\n              </div>\r\n              <div class=\"list-item-text\">\r\n                {{ item.activityOverview }}\r\n              </div>\r\n              <div class=\"list-item-time\">{{ item.createTimeStr }}</div>\r\n            </div>\r\n          </div>\r\n        </div> -->\r\n        <div class=\"activity-page-end\">\r\n          <el-button class=\"activity-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"data && data.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getActivityList } from \"@/api/purchaseSales\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        activityType: \"\", //活动类型\r\n      },\r\n      activityTypeList: [], //活动类型列表\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 9,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getDictsList(\"activity_type\", \"activityTypeList\");\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getActivityList({\r\n        ...this.form,\r\n        ...this.formInfo,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n    // 跳转到最新活动页面\r\n    goActivityDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/activityDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-info-content {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    .activity-search-type-box {\r\n      background: #fff;\r\n      margin-bottom: -7px;\r\n      .activity-search-line {\r\n        padding: 14px 24px 4px;\r\n        .activity-search-line-item {\r\n          margin-bottom: 0;\r\n        }\r\n        & + .activity-search-line {\r\n          border-top: 1px solid #f5f5f5;\r\n        }\r\n      }\r\n    }\r\n    .activity-list-item {\r\n      width: 100%;\r\n      background: #fff;\r\n      border-radius: 12px;\r\n      margin-top: 24px;\r\n      .list-item-content {\r\n        display: flex;\r\n        padding: 24px 32px;\r\n        cursor: pointer;\r\n        .list-item-img {\r\n          width: 230px;\r\n          height: 164px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n            border-radius: 5px;\r\n          }\r\n        }\r\n        .list-item-info {\r\n          padding-left: 24px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          .list-item-title {\r\n            width: 806px;\r\n            height: 24px;\r\n            text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n            white-space: nowrap; /*让文字不换行*/\r\n            overflow: hidden; /*超出要隐藏*/\r\n            font-size: 24px;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            line-height: 24px;\r\n            margin: 8px 0 24px;\r\n            word-wrap: break-word;\r\n          }\r\n          .list-item-text {\r\n            width: 806px;\r\n            height: 60px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            text-overflow: ellipsis;\r\n            font-size: 16px;\r\n            color: #666;\r\n            line-height: 30px;\r\n            word-wrap: break-word;\r\n          }\r\n          .list-item-time {\r\n            color: #999;\r\n            line-height: 14px;\r\n            margin-top: 24px;\r\n          }\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .activity-page-end {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin: 0 auto;\r\n      padding: 24px 0 60px;\r\n      .activity-page-btn {\r\n        width: 82px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n  .content {\r\n    display: flex;\r\n    // justify-content: space-between;\r\n    flex-wrap: wrap;\r\n    width: 100%;\r\n    height: 100%;\r\n    margin-top: 20px;\r\n    padding-bottom: 30px;\r\n    .contentItem {\r\n      width: 32%;\r\n      height: 340px;\r\n      text-align: center;\r\n      padding: 20px 30px;\r\n      background: #fff;\r\n      margin: 20px 0 0 23px;\r\n      cursor: pointer;\r\n      img {\r\n        width: 100%;\r\n        height: 230px;\r\n      }\r\n      .contentTitle {\r\n        margin-top: 15px;\r\n        font-size: 18px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #222222;\r\n      }\r\n    }\r\n    .contentItem:nth-child(3n + 1) {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}