{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\im\\index.vue?vue&type=style&index=0&id=ede0c0b6&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\im\\index.vue", "mtime": 1750311963059}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmFwcC1jb250YWluZXIgew0KICBiYWNrZ3JvdW5kOiAjZjRmNWY5Ow0KDQogIC5jb252ZXJzYXRpb24tbGlzdCB7DQogICAgaGVpZ2h0OiA3MDBweDsNCiAgICB3aWR0aDogMzAwcHg7DQogIH0NCiAgLnVzZXItbWVzc2FnZS1jb250YWluZXIgew0KICAgIGhlaWdodDogNzAwcHg7DQogICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICAubm9uZS1jbGFzcyB7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICBwYWRkaW5nOiAyMCUgMDsNCiAgICAgIGhlaWdodDogNzAwcHg7DQoNCiAgICAgIC50ZXh0IHsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICBjb2xvcjogIzk5OTk5OTsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDE0cHg7DQogICAgICB9DQogICAgfQ0KICAgIC5tZXNzYWdlLWJveCB7DQogICAgICBoZWlnaHQ6IDQwMHB4Ow0KICAgICAgLm1lc3NhZ2UtbGlzdCB7DQogICAgICB9DQogICAgICAubm8tZGF0YSB7DQogICAgICB9DQogICAgfQ0KDQogICAgLmVkaXRvci1ib3ggew0KICAgICAgaGVpZ2h0OiAzMDBweDsNCiAgICAgIC5lZGl0b3ItdG9vbC1iYXIgew0KICAgICAgICBoZWlnaHQ6IDllbTsNCiAgICAgIH0NCiAgICAgIC5lZGl0b3ItY29udGVudC13cmFwcGVyIHsNCiAgICAgICAgcGFkZGluZzogMWVtIDRlbTsNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwPA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/im", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-13 19:00:09\r\n * @LastEditTime: 2023-06-06 16:48:26\r\n * @Description:\r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"2.5\">\r\n        <div class=\"conversation-list\">\r\n          <conversation-list ref=\"conversationList\" base-size=\"6.5px\" />\r\n        </div>\r\n      </el-col>\r\n\r\n      <el-col :span=\"17\" :xl=\"17\" :lg=\"15\" :md=\"12\">\r\n        <div class=\"user-message-container\">\r\n          <div class=\"message-box\">\r\n            <message-list\r\n              class=\"message-list\"\r\n              ref=\"messageList\"\r\n              base-size=\"6.5px\"\r\n            ></message-list>\r\n          </div>\r\n          <div class=\"editor-box\">\r\n            <message-editor base-size=\"6.5px\" />\r\n          </div>\r\n          <!-- <div class=\"none-class\">\r\n            <el-image\r\n              style=\"width: 160px; height: 160px\"\r\n              :src=\"require('@/assets/user/none.png')\"\r\n              fit=\"cover\"\r\n            ></el-image>\r\n            <div class=\"text\">暂无数据</div>\r\n          </div> -->\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      :visible.sync=\"videoDialogVisible\"\r\n      :v-if=\"videoDialogVisible\"\r\n      width=\"60%\"\r\n      style=\"height: 650px\"\r\n      :before-close=\"handleVideoDialogClose\"\r\n    >\r\n      <Videoplayer :mp4Url=\"videoUrl\"></Videoplayer>\r\n    </el-dialog>\r\n    <el-dialog\r\n      :visible.sync=\"imageDialogVisible\"\r\n      :v-if=\"imageDialogVisible\"\r\n      width=\"60%\"\r\n      style=\"height: 850px; text-align: center\"\r\n      :before-close=\"handleImageDialogClose\"\r\n    >\r\n      <el-image\r\n        :v-if=\"imageDialogVisible\"\r\n        :src=\"imageUrl\"\r\n        style=\"width: 600px; height: 100%\"\r\n      ></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport FileSaver from \"file-saver\";\r\nimport * as RongIMLib from \"@rongcloud/imlib-next\";\r\nimport {\r\n  imkit,\r\n  CoreEvent,\r\n  DisabledMessageContextMenu,\r\n  DisabledConversationontextMenu,\r\n} from \"@rongcloud/imkit\";\r\nimport store from \"@/store\";\r\nimport { getUserIMToken } from \"@/api/system/user\";\r\nimport { ConversationType } from \"@rongcloud/engine\";\r\nimport Videoplayer from \"./components/video\";\r\n\r\nexport default {\r\n  name: \"IM\",\r\n  components: { UserMenu, Videoplayer },\r\n\r\n  data() {\r\n    return {\r\n      user: {\r\n        id: store.getters.userId,\r\n      },\r\n      editorVisible: false,\r\n      videoDialogVisible: false,\r\n      imageDialogVisible: false,\r\n      videoUrl: \"\",\r\n      lang: \"\",\r\n      langArr: [\r\n        {\r\n          lang: \"zh_CN\",\r\n          value: \"中文\",\r\n        },\r\n        {\r\n          lang: \"en\",\r\n          value: \"英文\",\r\n        },\r\n      ],\r\n      conversationMenuList: [\r\n        {\r\n          value: DisabledConversationontextMenu.Top,\r\n          name: \"置顶\",\r\n        },\r\n        {\r\n          value: DisabledConversationontextMenu.Delete,\r\n          name: \"删除\",\r\n        },\r\n        {\r\n          value: DisabledConversationontextMenu.Notification,\r\n          name: \"免打扰\",\r\n        },\r\n      ],\r\n      disableMenuMessage: [],\r\n      disableMenuConversation: [],\r\n      messageMenuList: [\r\n        {\r\n          value: DisabledMessageContextMenu.Forward,\r\n          name: \"转发\",\r\n        },\r\n        {\r\n          value: DisabledMessageContextMenu.Delete,\r\n          name: \"删除\",\r\n        },\r\n        {\r\n          value: DisabledMessageContextMenu.Reference,\r\n          name: \"引用\",\r\n        },\r\n        {\r\n          value: DisabledMessageContextMenu.Recall,\r\n          name: \"撤回\",\r\n        },\r\n        {\r\n          value: DisabledMessageContextMenu.Copy,\r\n          name: \"复制\",\r\n        },\r\n      ],\r\n      imageUrl: \"\",\r\n      imageUrl: \"\",\r\n      showImage: false,\r\n      switchConversationList: {},\r\n      conversation: null,\r\n      showMessageMenu: false,\r\n      showConversationMenu: false,\r\n    };\r\n  },\r\n  created() {\r\n    ///获取用户token\r\n    if (this.user.id) {\r\n      getUserIMToken({ userId: this.user.id }).then((res) => {\r\n        if (res.code === 200 && res.data.code === 200) {\r\n          window.token = res.data.token;\r\n          RongIMLib.connect(token).then((res) => {\r\n            console.info(\"连接结果打印1：\", res);\r\n            window.imkit = imkit;\r\n            this.lang = imkit.lang;\r\n            // 加载会话列表\r\n            imkit.emit(CoreEvent.CONVERSATION, true);\r\n            if (this.$route.query.userId) {\r\n              imkit.selectConversation({\r\n                conversationType: ConversationType.PRIVATE, // 会话类型\r\n                targetId: this.$route.query.userId,\r\n              });\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    // defineCustomElements();\r\n    const conversationList = this.$refs.conversationList;\r\n    const messageList = this.$refs.messageList;\r\n    //添加点击会话监听\r\n    conversationList.addEventListener(\r\n      \"tapConversation\",\r\n      this.handleTapConversation //回调处理函数\r\n    );\r\n    console.log(\"conversationList\", conversationList);\r\n    //添加删除会话监听\r\n    conversationList.addEventListener(\r\n      \"deleteConversation\",\r\n      this.handleDeleteConversation //回调处理函数\r\n    );\r\n    const disableMenu = [DisabledMessageContextMenu.Reference];\r\n    messageList.disableMenu = disableMenu;\r\n    //添加点击消息触发监听\r\n    messageList.addEventListener(\"tapMessage\", this.handleTapMessage);\r\n  },\r\n  beforeUnmount() {\r\n    // 注意：需要 removeEventListener 防止多次绑定造成异常\r\n    const conversationList = this.$refs.conversationList;\r\n\r\n    conversationList.removeEventListener(\r\n      \"tapConversation\",\r\n      this.handleTapConversation\r\n    );\r\n\r\n    conversationList.removeEventListener(\r\n      \"deleteConversation\",\r\n      this.handleDeleteConversation\r\n    );\r\n  },\r\n  methods: {\r\n    handleVideoDialogClose() {\r\n      this.videoDialogVisible = false;\r\n    },\r\n    handleImageDialogClose() {\r\n      this.imageDialogVisible = false;\r\n    },\r\n    handleTapConversation() {\r\n      //处理点击会话后的操作\r\n      console.info(\"处理点击会话后的操作11111\");\r\n    },\r\n    handleDeleteConversation() {\r\n      //处理删除会话后的操作\r\n      console.info(\"处理点击会话后的操作\");\r\n    },\r\n    handleTapMessage(e) {\r\n      const data = e.detail;\r\n      // 处理点击查看大图或文件消息下载等功能\r\n      console.log(\"点击消息触发监听:\", data);\r\n      if (data.type == \"file\") {\r\n        let url = data.url.replace(\r\n          \"http://rongcloud-file.ronghub.com\",\r\n          \"https://cy.ningmengdou.com/ryim\"\r\n        );\r\n        FileSaver.saveAs(url, data.name);\r\n      } else if (data.type == \"sight\") {\r\n        this.videoUrl = data.url;\r\n        this.videoDialogVisible = true;\r\n      } else if (data.type == \"image\") {\r\n        this.imageUrl = data.url;\r\n        this.imageDialogVisible = true;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n\r\n  .conversation-list {\r\n    height: 700px;\r\n    width: 300px;\r\n  }\r\n  .user-message-container {\r\n    height: 700px;\r\n    background: #fff;\r\n    .none-class {\r\n      text-align: center;\r\n      padding: 20% 0;\r\n      height: 700px;\r\n\r\n      .text {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n        line-height: 14px;\r\n      }\r\n    }\r\n    .message-box {\r\n      height: 400px;\r\n      .message-list {\r\n      }\r\n      .no-data {\r\n      }\r\n    }\r\n\r\n    .editor-box {\r\n      height: 300px;\r\n      .editor-tool-bar {\r\n        height: 9em;\r\n      }\r\n      .editor-content-wrapper {\r\n        padding: 1em 4em;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  .user-message-container {\r\n    .message-box {\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}