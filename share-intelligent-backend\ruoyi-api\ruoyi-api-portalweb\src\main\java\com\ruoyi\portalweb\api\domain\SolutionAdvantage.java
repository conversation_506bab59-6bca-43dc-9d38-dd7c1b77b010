package com.ruoyi.portalweb.api.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 解决方案方案优势对象 solution_advantage
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public class SolutionAdvantage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    public SolutionAdvantage(Long solutionId) {
        this.solutionId = solutionId;
    }

    public SolutionAdvantage() {}

    /** 解决方案方案优势ID */
    private Long solutionAdvantageId;

    /** 方案ID */
    @Excel(name = "方案ID")
    private Long solutionId;

    /** 名称 */
    @Excel(name = "名称")
    private String solutionAdvantageName;

    /** 类型 */
    @Excel(name = "类型")
    private String solutionAdvantageType;

    /** 内容 */
    @Excel(name = "内容")
    private String solutionAdvantageContent;

    /** 优势图片 */
    @Excel(name = "优势图片")
    private String solutionAdvantageImage;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    public void setSolutionAdvantageId(Long solutionAdvantageId) 
    {
        this.solutionAdvantageId = solutionAdvantageId;
    }

    public Long getSolutionAdvantageId() 
    {
        return solutionAdvantageId;
    }
    public void setSolutionId(Long solutionId) 
    {
        this.solutionId = solutionId;
    }

    public Long getSolutionId() 
    {
        return solutionId;
    }
    public void setSolutionAdvantageName(String solutionAdvantageName) 
    {
        this.solutionAdvantageName = solutionAdvantageName;
    }

    public String getSolutionAdvantageName() 
    {
        return solutionAdvantageName;
    }
    public void setSolutionAdvantageType(String solutionAdvantageType) 
    {
        this.solutionAdvantageType = solutionAdvantageType;
    }

    public String getSolutionAdvantageType() 
    {
        return solutionAdvantageType;
    }
    public void setSolutionAdvantageContent(String solutionAdvantageContent) 
    {
        this.solutionAdvantageContent = solutionAdvantageContent;
    }

    public String getSolutionAdvantageContent() 
    {
        return solutionAdvantageContent;
    }

    public String getSolutionAdvantageImage() {
        return solutionAdvantageImage;
    }

    public void setSolutionAdvantageImage(String solutionAdvantageImage) {
        this.solutionAdvantageImage = solutionAdvantageImage;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("solutionAdvantageId", getSolutionAdvantageId())
            .append("solutionId", getSolutionId())
            .append("solutionAdvantageName", getSolutionAdvantageName())
            .append("solutionAdvantageType", getSolutionAdvantageType())
            .append("solutionAdvantageContent", getSolutionAdvantageContent())
            .append("solutionAdvantageImage", getSolutionAdvantageImage())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
