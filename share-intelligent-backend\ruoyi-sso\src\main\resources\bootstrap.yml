# Tomcat
server:
  port: 9100

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-sso
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        file-extension: yaml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  main:
    allow-bean-definition-overriding: true

# SSO服务配置
sso:
  # 服务基本信息
  service:
    name: SSO认证服务
    version: 1.0.0
    description: 统一单点登录认证服务
  
  # 支持的客户端系统
  clients:
    backend:
      name: 复合材料共享智造平台
      url: http://localhost:9200
      callback-url: http://localhost:9200/sso/callback
      database: industry
      secret: "backend_2024#RuoYi@Share$Key!8888"
    market:
      name: 智能市场系统
      url: http://localhost:8081
      callback-url: http://localhost:8081/sso/callback
      database: market
      secret: "market_2024#RuoYi@Share$Key!9999"
  
  # Token配置
  token:
    # SSO Token有效期（分钟）
    expire-minutes: 30
    # 访问Token有效期（分钟）
    access-token-expire-minutes: 480
    # 刷新Token有效期（天）
    refresh-token-expire-days: 7
    # Token签名密钥（建议生产环境使用更复杂的密钥）
    secret: "sso_jwt_2024_RuoYi#Share@Intelligent$Key!9527"
    # Token前缀
    prefix: "sso_token:"
    access-prefix: "access_token:"
    refresh-prefix: "refresh_token:"
  
  # 会话配置
  session:
    # 会话超时时间（分钟）
    timeout-minutes: 480
    # 最大并发会话数
    max-sessions: 1
    # 会话前缀
    prefix: "sso_session:"
  
  # 安全配置
  security:
    # 是否启用CSRF保护
    csrf-enabled: false
    # 允许的跨域来源
    allowed-origins:
      - http://localhost:9200
      - http://localhost:8081
      - http://localhost:80
    # 登录失败最大尝试次数
    max-login-attempts: 5
    # 登录失败锁定时间（分钟）
    lockout-duration-minutes: 15
  
  # 缓存配置
  cache:
    # Redis前缀
    redis-prefix: "sso:"
    # 缓存过期时间（秒）
    default-expire-seconds: 3600
