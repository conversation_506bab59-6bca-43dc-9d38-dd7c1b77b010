{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\publishEquipment\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\publishEquipment\\index.vue", "mtime": 1750311963078}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5rZXlzLmpzIik7CnZhciBfdXNlck1lbnUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4uL2NvbXBvbmVudHMvdXNlck1lbnUudnVlIikpOwp2YXIgX2RhdGEgPSByZXF1aXJlKCJAL2FwaS9zeXN0ZW0vZGljdC9kYXRhIik7CnZhciBfbWFudWZhY3R1cmluZ1NoYXJpbmcgPSByZXF1aXJlKCJAL2FwaS9tYW51ZmFjdHVyaW5nU2hhcmluZyIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogIlVzZXIiLAogIGNvbXBvbmVudHM6IHsKICAgIFVzZXJNZW51OiBfdXNlck1lbnUuZGVmYXVsdAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGZvcm06IHsKICAgICAgICBpZDogbnVsbCwKICAgICAgICBuYW1lOiBudWxsLAogICAgICAgIGNhdGVnb3J5OiBudWxsLAogICAgICAgIHNwZWNpZmljYXRpb25zOiBudWxsLAogICAgICAgIGxvY2F0aW9uOiBudWxsLAogICAgICAgIGRlc2NyaXB0aW9uOiBudWxsLAogICAgICAgIGltYWdlczogbnVsbCwKICAgICAgICB0ZWNobmljYWxQYXJhbXM6IG51bGwsCiAgICAgICAgcmVudE1vZGU6IG51bGwsCiAgICAgICAgcmVudFByaWNlOiBudWxsLAogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwKICAgICAgICBjaGVja1N0YXR1czogbnVsbCwKICAgICAgICBjcmVhdGVCeTogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICBuYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K6+5aSH5ZCN56ew5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGNhdGVnb3J5OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K6+5aSH5YiG57G75LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJjaGFuZ2UiCiAgICAgICAgfV0sCiAgICAgICAgbG9jYXRpb246IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLmiYDlsZ7ljZXkvY0v5L2N572u5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0sCiAgICAgIGRldmljZU1lbnVMaXN0OiBbXQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldERpY3RzKCk7CiAgICBpZiAodGhpcy4kcm91dGUucXVlcnkuaWQpIHsKICAgICAgdGhpcy5nZXREZXRhaWwoKTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6LlrZflhbjmlbDmja7liJfooaggKi9nZXREaWN0czogZnVuY3Rpb24gZ2V0RGljdHMoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgZGljdFR5cGU6ICJkZXZpY2Vfc2hhcmVfdHlwZSIKICAgICAgfTsKICAgICAgKDAsIF9kYXRhLmxpc3REYXRhKShwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICBfdGhpcy5kZXZpY2VNZW51TGlzdCA9IHJlcy5yb3dzOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgZ2V0RGV0YWlsOiBmdW5jdGlvbiBnZXREZXRhaWwoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICAoMCwgX21hbnVmYWN0dXJpbmdTaGFyaW5nLmRldmljZURldGFpbERhdGEpKHRoaXMuJHJvdXRlLnF1ZXJ5LmlkKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7CiAgICAgICAgICBfdGhpczIuZm9ybSA9IHJlcy5kYXRhOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgb25TdWJtaXQ6IGZ1bmN0aW9uIG9uU3VibWl0KCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgaWYgKF90aGlzMy5mb3JtLmlkICE9IG51bGwpIHsKICAgICAgICAgICAgKDAsIF9tYW51ZmFjdHVyaW5nU2hhcmluZy51cGRhdGVEZXZpY2VJbmZvKShfdGhpczMuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzMy4kcm91dGVyLmdvKC0xKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczMuZm9ybS5jaGVja1N0YXR1cyA9IDA7CiAgICAgICAgICAgIHZhciB1c2VyaW5mbyA9IEpTT04ucGFyc2Uod2luZG93LnNlc3Npb25TdG9yYWdlLmdldEl0ZW0oInVzZXJpbmZvIikpOwogICAgICAgICAgICBfdGhpczMuZm9ybS5jcmVhdGVCeSA9IHVzZXJpbmZvLm1lbWJlclBob25lOwogICAgICAgICAgICAoMCwgX21hbnVmYWN0dXJpbmdTaGFyaW5nLmFkZERldmljZUluZm8pKF90aGlzMy5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgICAgIF90aGlzMy4kbW9kYWwubXNnU3VjY2Vzcygi5Y+R5biD5oiQ5YqfLOivt+etieW+heWuoeaguCIpOwogICAgICAgICAgICAgIF90aGlzMy4kcm91dGVyLmdvKC0xKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBvbkNhbmNlbDogZnVuY3Rpb24gb25DYW5jZWwoKSB7CiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_data", "_manufacturingSharing", "name", "components", "UserMenu", "data", "form", "id", "category", "specifications", "location", "description", "images", "technicalParams", "rentMode", "rentPrice", "createTime", "updateTime", "checkStatus", "createBy", "rules", "required", "message", "trigger", "deviceMenuList", "created", "getDicts", "$route", "query", "getDetail", "methods", "_this", "params", "dictType", "listData", "then", "res", "code", "rows", "_this2", "deviceDetailData", "onSubmit", "_this3", "$refs", "validate", "valid", "updateDeviceInfo", "response", "$modal", "msgSuccess", "$router", "go", "userinfo", "JSON", "parse", "window", "sessionStorage", "getItem", "memberPhone", "addDeviceInfo", "onCancel"], "sources": ["src/views/system/user/publishEquipment/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row\r\n      :gutter=\"20\"\r\n      style=\"background: linear-gradient(to right, #e1f7f0, #f4fcfa)\"\r\n    >\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"formStyle\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"设备名称\" prop=\"name\">\r\n              <el-input v-model=\"form.name\" placeholder=\"请输入设备名称\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"设备分类\" prop=\"category\">\r\n              <el-select\r\n                v-model=\"form.category\"\r\n                placeholder=\"请选择设备分类\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in deviceMenuList\"\r\n                  :key=\"dict.dictLabel\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"设备规格\" prop=\"specifications\">\r\n              <el-input\r\n                v-model=\"form.specifications\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"所属单位/位置\" prop=\"location\">\r\n              <el-input\r\n                v-model=\"form.location\"\r\n                placeholder=\"请输入所属单位/位置\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"设备用途描述\" prop=\"description\">\r\n              <el-input\r\n                v-model=\"form.description\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"设备图片\" prop=\"images\">\r\n              <ImageUpload v-model=\"form.images\" resultType=\"string\" :limit=\"1\" :multiple=\"false\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"租用模式\" prop=\"rentMode\">\r\n              <el-input v-model=\"form.rentMode\" placeholder=\"请输入租用模式\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"租用价格\" prop=\"rentPrice\">\r\n              <el-input v-model=\"form.rentPrice\" placeholder=\"请输入租用价格\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"压力(MPa)\" prop=\"pressure\">\r\n              <el-input v-model=\"form.pressure\" placeholder=\"请输入压力(MPa)\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"温度\" prop=\"temperature\">\r\n              <el-input v-model=\"form.temperature\" placeholder=\"请输入温度\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"尺寸\" prop=\"dimension\">\r\n              <el-input v-model=\"form.dimension\" placeholder=\"请输入尺寸\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"规格型号\" prop=\"modelNumber\">\r\n              <el-input\r\n                v-model=\"form.modelNumber\"\r\n                placeholder=\"请输入规格型号\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button type=\"primary\" @click=\"onSubmit\">{{\r\n                form.id ? \"保存\" : \"发布\"\r\n              }}</el-button>\r\n              <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\"\r\n                >取消</el-button\r\n              >\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport {\r\n  addDeviceInfo,\r\n  updateDeviceInfo,\r\n  deviceDetailData,\r\n} from \"@/api/manufacturingSharing\";\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      form: {\r\n        id: null,\r\n        name: null,\r\n        category: null,\r\n        specifications: null,\r\n        location: null,\r\n        description: null,\r\n        images: null,\r\n        technicalParams: null,\r\n        rentMode: null,\r\n        rentPrice: null,\r\n        createTime: null,\r\n        updateTime: null,\r\n        checkStatus: null,\r\n        createBy: null,\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        name: [\r\n          { required: true, message: \"设备名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        category: [\r\n          { required: true, message: \"设备分类不能为空\", trigger: \"change\" },\r\n        ],\r\n        location: [\r\n          { required: true, message: \"所属单位/位置不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      deviceMenuList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getDicts();\r\n    if (this.$route.query.id) {\r\n      this.getDetail();\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询字典数据列表 */\r\n    getDicts() {\r\n      let params = { dictType: \"device_share_type\" };\r\n      listData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.deviceMenuList = res.rows;\r\n        }\r\n      });\r\n    },\r\n    getDetail() {\r\n      deviceDetailData(this.$route.query.id).then((res) => {\r\n        if (res.code == 200) {\r\n          this.form = res.data;\r\n        }\r\n      });\r\n    },\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateDeviceInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.$router.go(-1);\r\n            });\r\n          } else {\r\n            this.form.checkStatus = 0;\r\n            let userinfo = JSON.parse(\r\n              window.sessionStorage.getItem(\"userinfo\")\r\n            );\r\n            this.form.createBy = userinfo.memberPhone;\r\n            addDeviceInfo(this.form).then((response) => {\r\n              this.$modal.msgSuccess(\"发布成功,请等待审核\");\r\n              this.$router.go(-1);\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.formStyle {\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  .footer-submit {\r\n    text-align: center;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAyFA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,qBAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAKA;EACAG,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,EAAA;QACAL,IAAA;QACAM,QAAA;QACAC,cAAA;QACAC,QAAA;QACAC,WAAA;QACAC,MAAA;QACAC,eAAA;QACAC,QAAA;QACAC,SAAA;QACAC,UAAA;QACAC,UAAA;QACAC,WAAA;QACAC,QAAA;MACA;MACA;MACAC,KAAA;QACAlB,IAAA,GACA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAf,QAAA,GACA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,QAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAArB,EAAA;MACA,KAAAsB,SAAA;IACA;EACA;EACAC,OAAA;IACA,eACAJ,QAAA,WAAAA,SAAA;MAAA,IAAAK,KAAA;MACA,IAAAC,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAN,KAAA,CAAAP,cAAA,GAAAY,GAAA,CAAAE,IAAA;QACA;MACA;IACA;IACAT,SAAA,WAAAA,UAAA;MAAA,IAAAU,MAAA;MACA,IAAAC,sCAAA,OAAAb,MAAA,CAAAC,KAAA,CAAArB,EAAA,EAAA4B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAE,MAAA,CAAAjC,IAAA,GAAA8B,GAAA,CAAA/B,IAAA;QACA;MACA;IACA;IACAoC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAApC,IAAA,CAAAC,EAAA;YACA,IAAAuC,sCAAA,EAAAJ,MAAA,CAAApC,IAAA,EAAA6B,IAAA,WAAAY,QAAA;cACAL,MAAA,CAAAM,MAAA,CAAAC,UAAA;cACAP,MAAA,CAAAQ,OAAA,CAAAC,EAAA;YACA;UACA;YACAT,MAAA,CAAApC,IAAA,CAAAY,WAAA;YACA,IAAAkC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CACAC,MAAA,CAAAC,cAAA,CAAAC,OAAA,YACA;YACAf,MAAA,CAAApC,IAAA,CAAAa,QAAA,GAAAiC,QAAA,CAAAM,WAAA;YACA,IAAAC,mCAAA,EAAAjB,MAAA,CAAApC,IAAA,EAAA6B,IAAA,WAAAY,QAAA;cACAL,MAAA,CAAAM,MAAA,CAAAC,UAAA;cACAP,MAAA,CAAAQ,OAAA,CAAAC,EAAA;YACA;UACA;QACA;MACA;IACA;IACAS,QAAA,WAAAA,SAAA;MACA,KAAAV,OAAA,CAAAC,EAAA;IACA;EACA;AACA", "ignoreList": []}]}