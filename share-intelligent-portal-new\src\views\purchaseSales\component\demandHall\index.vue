<!--
 * @Author: jhy
 * @Date: 2023-02-01 17:22:11
 * @LastEditors: JHY
 * @LastEditTime: 2023-12-09 18:52:38
-->
<template>
  <div class="demand-hall-container">
    <!-- banner图 -->
    <div class="purchase-banner">
      <img src="../../../../assets/demandHall/demandHallBanner.png" alt="" />
    </div>
    <div v-loading="loading">
      <div class="demand-hall-title-content">
        <div class="demand-hall-title-box">
          <div class="demand-hall-divider"></div>
          <div class="demand-hall-title">商机需求</div>
          <div class="demand-hall-divider"></div>
        </div>
        <div class="demand-hall-search-box">
          <el-form ref="form" class="demand-hall-search-form" :model="form">
            <el-form-item>
              <el-input
                v-model="form.name"
                placeholder="请输入搜索内容"
                class="demand-hall-search-input"
              >
                <el-button
                  slot="append"
                  class="demand-hall-search-btn"
                  @click="onSearch"
                  >搜索</el-button
                >
              </el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="demand-hall-card">
        <div class="demand-hall-info-content">
          <div class="demand-hall-search-type-box">
            <el-form ref="formInfo" :model="formInfo">
              <div class="demand-hall-search-line">
                <el-form-item
                  label="需求类型"
                  class="demand-hall-search-line-item"
                >
                  <el-radio-group
                    v-model="formInfo.demandType"
                    class="demand-hall-search-radio"
                    @input="changeRadio"
                  >
                    <el-radio-button label="">全部</el-radio-button>
                    <el-radio-button
                      v-for="(item, index) in demandList"
                      :key="index"
                      :label="item.dictValue"
                      >{{ item.dictLabel }}</el-radio-button
                    >
                  </el-radio-group>
                </el-form-item>
              </div>
            </el-form>
          </div>
          <div
            v-for="(item, index) in data"
            :key="index"
            class="demand-hall-list-item"
            @click="goDemandDetail(item.id)"
          >
            <div class="list-item-content">
              <div class="list-item-img">
                <img
                  v-if="item.scenePicture && item.scenePicture.length > 0"
                  alt=""
                  :src="item.scenePicture[0].url"
                />
                <img
                  v-else
                  src="../../../../assets/purchaseSales/demandDefault.png"
                  alt=""
                />
              </div>
              <div class="list-item-info">
                <div class="list-item-title">
                  {{ item.demandTitle }}
                </div>
                <div class="list-item-text">
                  <div class="list-item-label">应用领域：</div>
                  <div class="list-item-tag-box">
                    <div
                      v-for="(val, num) in item.applicationArea"
                      :key="num"
                      class="lilst-item-tag"
                    >
                      {{ val }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="demand-hall-page-end">
            <el-button class="demand-hall-page-btn" @click="goHome"
              >首页</el-button
            >
            <el-pagination
              v-if="data && data.length > 0"
              background
              layout="prev, pager, next"
              class="demand-hall-pagination"
              :page-size="pageSize"
              :current-page="pageNum"
              :total="total"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { gatewayDemendListTen } from "@/api/purchaseSales";
import { getDicts } from "@/api/system/dict/data";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  data() {
    return {
      loading: false,
      form: {
        name: "", //搜索内容
      },
      formInfo: {
        demandType: "", //需求类型
      },
      demandList: [], //资讯类型列表
      data: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  created() {
    this.getDictsList("demand_type", "demandList");
    this.search();
  },
  methods: {
    search() {
      this.loading = true;
      gatewayDemendListTen({
        ...this.form,
        ...this.formInfo,
        displayStatus: 1,
        auditStatus: 2,
        pageNum: this.pageNum,
        // pageSize: this.pageSize,
      })
        .then((res) => {
          console.log(res);
          let key = CryptoJS.enc.Utf8.parse(secretKey);
          let decrypt = CryptoJS.AES.decrypt(res, key, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7,
          });
          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
          console.log(res);
          this.loading = false;
          let { rows, total } = res || [];
          this.data = rows;
          this.data.forEach((item) => {
            item.scenePicture = item.scenePicture
              ? JSON.parse(item.scenePicture)
              : [];
            item.applicationArea = item.applicationArea
              ? item.applicationArea.split(",")
              : "";
          });
          this.total = total;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 字典
    getDictsList(code, propertyName) {
      getDicts(code).then((res) => {
        this[propertyName] = res.data || [];
      });
    },
    changeRadio() {
      this.onSearch();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.onSearch();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.search();
    },
    onSearch() {
      this.pageNum = 1;
      this.search();
    },
    // 跳转到详情页面
    goDemandDetail(id) {
      let routeData = this.$router.resolve({
        path: "/demandHallDetail",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    // 跳转到首页
    goHome() {
      this.$router.push({ path: "/index" });
    },
  },
};
</script>

<style lang="scss" scoped>
.demand-hall-container {
  width: 100%;
  .purchase-banner {
    width: 100%;
    height: 50vh;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .demand-hall-title-content {
    width: 100%;
    padding-bottom: 18px;
    .demand-hall-title-box {
      width: 336px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 60px 0 40px;
      .demand-hall-title {
        font-size: 40px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333;
        line-height: 40px;
        padding: 0 40px;
      }
      .demand-hall-divider {
        width: 48px;
        height: 4px;
        background: #21c9b8;
      }
    }
    .demand-hall-search-box {
      .demand-hall-search-form {
        text-align: center;
        .demand-hall-search-input {
          width: 792px;
          height: 54px;
          .demand-hall-search-btn {
            width: 100px;
          }
        }
      }
    }
  }
  .demand-hall-card {
    background: #f4f5f9;
    padding-top: 40px;
    .demand-hall-info-content {
      width: 1200px;
      margin: 0 auto;
      .demand-hall-search-type-box {
        background: #fff;
        margin-bottom: 17px;
        .demand-hall-search-line {
          padding: 14px 24px 4px;
          .demand-hall-search-line-item {
            margin-bottom: 0;
          }
        }
      }
      .demand-hall-list-item {
        width: 100%;
        background: #fff;
        border-radius: 12px;
        .list-item-content {
          display: flex;
          padding: 24px;
          cursor: pointer;
          .list-item-img {
            width: 180px;
            height: 128px;
            img {
              width: 100%;
              height: 100%;
              border-radius: 7px;
            }
          }
          .list-item-info {
            padding-left: 24px;
            font-family: PingFangSC-Regular, PingFang SC;
            .list-item-title {
              width: 922px;
              height: 24px;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              font-size: 20px;
              font-weight: 500;
              color: #323233;
              line-height: 20px;
              margin-bottom: 26px;
              word-wrap: break-word;
            }
            .list-item-text {
              display: flex;
              align-items: top;
              .list-item-label {
                color: #323233;
                line-height: 14px;
                margin-top: 14px;
              }
              .list-item-tag-box {
                display: flex;
                width: 852px;
                flex-wrap: wrap;
                .lilst-item-tag {
                  padding: 0 12px;
                  max-width: 840px;
                  background: #21c9b8 1a;
                  border-radius: 4px;
                  font-size: 12px;
                  color: #21c9b8;
                  line-height: 24px;
                  text-align: center;
                  margin-right: 16px;
                  margin-top: 10px;
                  word-wrap: break-word;
                  text-align: left;
                }
              }
            }
          }
          &:hover {
            .list-item-title {
              color: #21c9b8;
            }
          }
        }
        & + .demand-hall-list-item {
          margin-top: 24px;
        }
      }
      .demand-hall-page-end {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        padding: 24px 0 60px;
        .demand-hall-page-btn {
          width: 82px;
          height: 32px;
          background: #fff;
          border-radius: 4px;
          border: 1px solid #d9d9d9;
          font-family: PingFangSC-Regular, PingFang SC;
          color: #333;
          line-height: 10px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.demand-hall-container {
  .demand-hall-search-input {
    .el-input__inner {
      height: 54px;
      background: #fff;
      border-radius: 27px 0 0 27px;
      border: 1px solid #d9d9d9;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 24px;
      padding-left: 30px;
    }
    .el-input-group__append {
      border-radius: 0px 100px 100px 0px;
      background: #21c9b8;
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      color: #fff;
      line-height: 24px;
    }
  }
  .demand-hall-search-line {
    .el-form-item__label {
      width: 88px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #999;
      padding-right: 32px;
      text-align: left;
    }
    .demand-hall-search-radio {
      width: 1050px;
      margin-top: 11px;
      .el-radio-button {
        padding-bottom: 20px;
        .el-radio-button__inner {
          border: none;
          padding: 0 32px 0 0;
          background: none;
          &:hover {
            color: #21c9b8;
          }
        }
        &.is-active {
          .el-radio-button__inner {
            color: #21c9b8;
            background: none;
          }
        }
        .el-radio-button__orig-radio:checked {
          & + .el-radio-button__inner {
            box-shadow: unset;
          }
        }
      }
    }
  }
  .demand-hall-page-end {
    .demand-hall-pagination {
      .btn-prev,
      .btn-next,
      .btn-quickprev {
        width: 32px;
        height: 32px;
        background: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        color: #333;
      }
      &.is-background {
        .el-pager {
          .number {
            width: 32px;
            height: 32px;
            border: 1px solid #d9d9d9;
            background: #fff;
            border-radius: 4px;
            line-height: 32px;
            &.active {
              background: #21c9b8;
              border: 1px solid #21c9b8;
            }
          }
        }
      }
    }
  }
}
</style>
