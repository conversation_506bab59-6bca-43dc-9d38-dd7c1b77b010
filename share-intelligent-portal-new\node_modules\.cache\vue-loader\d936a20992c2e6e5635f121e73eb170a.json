{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\noninductive\\detail\\index.vue?vue&type=style&index=0&id=81544d5e&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\noninductive\\detail\\index.vue", "mtime": 1750311963064}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogI2Y0ZjVmOTsNCiAgLm5vdGljZS1yZWNvcmQtZGV0YWlsIHsNCiAgICAuaW5mby1jb250YWluZXIgew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgICBwYWRkaW5nLXRvcDogMTJweDsNCiAgICAgIHBhZGRpbmc6IDEwcHggMzBweDsNCg0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7DQogICAgICAuaGVhZGVyIHsNCiAgICAgICAgbWFyZ2luLWJvdHRvbTogMzBweDsNCiAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgLmVsLWJ1dHRvbiB7DQogICAgICAgICAgaGVpZ2h0OiA0MHB4Ow0KICAgICAgICAgIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7DQogICAgICAgICAgcGFkZGluZzogMTBweCAxMHB4IDEwcHggMjBweDsNCiAgICAgICAgICBmb250LXNpemU6IDIwcHg7DQogICAgICAgICAgY29sb3I6ICMwMDA7DQogICAgICAgIH0NCiAgICAgICAgLmVsLWJ1dHRvbjpob3ZlciB7DQogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7DQogICAgICAgIH0NCiAgICAgICAgLmhlYWRlci10ZXh0IHsNCiAgICAgICAgICBmb250LXNpemU6IDI0cHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgICAgICBsaW5lLWhlaWdodDogMjRweDsNCiAgICAgICAgICBsaW5lLWhlaWdodDogNDBweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLmRldGFpbC1wYWdlIHsNCiAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KDQogICAgICAgIC5oZWFkZXItc21hbGwgew0KICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxNnB4Ow0KICAgICAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQoNCiAgICAgICAgICAucmVkLXRhZyB7DQogICAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDEycHg7DQogICAgICAgICAgICB3aWR0aDogM3B4Ow0KICAgICAgICAgICAgaGVpZ2h0OiAxNnB4Ow0KICAgICAgICAgICAgYmFja2dyb3VuZDogIzIxYzliODsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLmZpbGUtY2xhc3Mgew0KICAgICAgICAgIHdpZHRoOiA3MzNweDsNCiAgICAgICAgICBoZWlnaHQ6IDQwcHg7DQogICAgICAgICAgYmFja2dyb3VuZDogI2Y3ZjhmYTsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgcGFkZGluZzogMCAyMHB4Ow0KICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDQwcHg7DQogICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlOw0KDQogICAgICAgICAgLmVsLWltYWdlIHsNCiAgICAgICAgICAgIG1hcmdpbjogMTJweCA4cHggMCAwOw0KICAgICAgICAgIH0NCiAgICAgICAgICAucHJldml3ZS1jbGFzcyB7DQogICAgICAgICAgICByaWdodDogMjBweDsNCiAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgICAgICAgIG1hcmdpbjogOHB4IDAgMCAwOw0KICAgICAgICAgICAgd2lkdGg6IDcycHg7DQogICAgICAgICAgICBoZWlnaHQ6IDI0cHg7DQogICAgICAgICAgICBib3JkZXItcmFkaXVzOiAxNnB4Ow0KICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzJmNzZlMDsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICAgICAgICBjb2xvcjogIzJmNzZlMDsNCiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAyNHB4Ow0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAuc3RhdHVzX2FwcHJvdmluZyB7DQogICAgICAgICAgdG9wOiAwcHg7DQogICAgICAgICAgcmlnaHQ6IDIwcHg7DQogICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC5lZGl0LXBhZ2Ugew0KICAgICAgICAuZWwtaW5wdXQtLW1lZGl1bSAuZWwtaW5wdXRfX2lubmVyIHsNCiAgICAgICAgICB3aWR0aDogMzAwcHg7DQogICAgICAgICAgaGVpZ2h0OiAzNnB4Ow0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAzNnB4Ow0KICAgICAgICB9DQogICAgICAgIC5lbC1idXR0b24tLXByaW1hcnkgew0KICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICAgICAgYm9yZGVyLWNvbG9yOiAjYmZiZmJmOw0KICAgICAgICB9DQogICAgICAgIC5lbC1idXR0b24tLWRhbmdlciB7DQogICAgICAgICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICAgICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgICAgICBib3JkZXItY29sb3I6ICMyMWM5Yjg7DQogICAgICAgIH0NCiAgICAgICAgLnRpcCB7DQogICAgICAgICAgcGFkZGluZy1sZWZ0OiAxMHB4Ow0KICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgIGNvbG9yOiAjOGM4YzhjOw0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxOHB4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgICAuZWwtZGVzY3JpcHRpb25zLS1tZWRpdW0uaXMtYm9yZGVyZWQgLmVsLWRlc2NyaXB0aW9ucy1pdGVtX19jZWxsIHsNCiAgICAgICAgcGFkZGluZzogMTBweDsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgIH0NCg0KICAgICAgLmVsLWRlc2NyaXB0aW9ucy0tbWVkaXVtLmlzLWJvcmRlcmVkIC5lbC1kZXNjcmlwdGlvbnMtaXRlbV9fbGFiZWwgew0KICAgICAgICBwYWRkaW5nOiAxNXB4Ow0KICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICAgIHdpZHRoOiAyMDBweDsNCiAgICAgIH0NCiAgICAgIC5kZWxldGUtYnRuIHsNCiAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgIG1hcmdpbi10b3A6IDIwcHg7DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgLmVsLWJ1dHRvbiB7DQogICAgICAgICAgcGFkZGluZzogMTJweCA1NXB4Ow0KICAgICAgICB9DQogICAgICAgIC5lbC1idXR0b24tLWRhbmdlciB7DQogICAgICAgICAgbWFyZ2luLWxlZnQ6IDMwcHg7DQogICAgICAgICAgY29sb3I6ICNmZmZmZmY7DQogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzIxYzliODsNCiAgICAgICAgICBib3JkZXItY29sb3I6ICMyMWM5Yjg7DQogICAgICAgIH0NCiAgICAgICAgLmVsLWJ1dHRvbi0tZXJyb3Igew0KICAgICAgICAgIG1hcmdpbi1sZWZ0OiAzMHB4Ow0KICAgICAgICAgIGNvbG9yOiAjMjFjOWI4Ow0KICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7DQogICAgICAgICAgYm9yZGVyLWNvbG9yOiAjMjFjOWI4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyhBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/noninductive/detail", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-03-24 08:55:04\r\n * @Description:\r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"notice-record-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"18\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">兑现详情</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                基本信息\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 所属单位 </template>\r\n                  {{ info.affiliatedUnitName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 资金来源 </template>\r\n                  {{ info.capitalSourceName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 项目名称</template>\r\n                  {{\r\n                    info.itemTypeFirstName || \"\" + info.itemTypeSecondName || \"\"\r\n                  }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 公司名称</template>\r\n                  {{ info.companyName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 所属街道</template>\r\n                  {{ info.affiliatedStreetName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 企业注册地址</template>\r\n                  {{ info.companyAddress }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 企业统一代码 </template>\r\n                  {{ info.companyCreditCode }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 开户银行</template>\r\n                  {{ info.bankName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 银行账号</template>\r\n                  {{ info.bankCode }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系人</template>\r\n                  {{ info.contactsName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系电话 </template>\r\n                  {{ info.contactsPhone }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 申请书 </template>\r\n                  <div v-if=\"!info.application\">--</div>\r\n                  <a\r\n                    v-else\r\n                    class=\"file-class\"\r\n                    @click=\"handleFilePreview(info.application)\"\r\n                  >\r\n                    <el-image\r\n                      style=\"width: 14px; height: 17px\"\r\n                      :src=\"require('@/assets/user/file_pdf.png')\"\r\n                    ></el-image>\r\n                    {{ info.applicationName }}\r\n                    <div class=\"previwe-class\">立即查看</div>\r\n                  </a>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 开户许可证 </template>\r\n                  <div v-if=\"!info.accountLicence\">--</div>\r\n\r\n                  <a\r\n                    class=\"file-class\"\r\n                    v-else\r\n                    @click=\"handleFilePreview(info.accountLicence)\"\r\n                  >\r\n                    <el-image\r\n                      style=\"width: 14px; height: 17px\"\r\n                      :src=\"require('@/assets/user/file_pdf.png')\"\r\n                    ></el-image>\r\n                    {{ info.accountLicenceName }}\r\n                    <div class=\"previwe-class\">立即查看</div>\r\n                  </a>\r\n                  <!-- <el-upload\r\n                    v-if=\"info.accountLicence\"\r\n                    class=\"upload-demo\"\r\n                    :on-preview=\"handleFilePreview(info.accountLicence)\"\r\n                  >\r\n                  </el-upload> -->\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <el-image\r\n                class=\"status_approving\"\r\n                v-if=\"info.status === '1'\"\r\n                style=\"width: 120px; height: 102px\"\r\n                :src=\"require('@/assets/user/status_approving.png')\"\r\n              ></el-image>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"goBack\">返回</el-button>\r\n                <el-button\r\n                  v-if=\"info.status == '0'\"\r\n                  type=\"danger\"\r\n                  @click=\"changeMode\"\r\n                  >编辑</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n            <div class=\"edit-page\" v-else>\r\n              <el-form\r\n                ref=\"form\"\r\n                :model=\"form\"\r\n                :rules=\"rules\"\r\n                label-width=\"120px\"\r\n              >\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"所属单位\" prop=\"affiliatedUnitCode\">\r\n                      <el-select\r\n                        v-model=\"form.affiliatedUnitCode\"\r\n                        placeholder=\"请选择所属单位\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.affiliated_unit\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n                      <el-input\r\n                        v-model=\"form.companyName\"\r\n                        placeholder=\"公司名称\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"企业统一代码\" prop=\"companyCreditCode\">\r\n                      <el-input\r\n                        v-model=\"form.companyCreditCode\"\r\n                        placeholder=\"请输入企业统一代码\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"联系人\" prop=\"contactsName\">\r\n                      <el-input\r\n                        v-model=\"form.contactsName\"\r\n                        placeholder=\"请输入联系人\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"资金来源\" prop=\"capitalSourceCode\">\r\n                      <el-select\r\n                        v-model=\"form.capitalSourceCode\"\r\n                        placeholder=\"请选择资金来源\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.capital_source\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"所属街道\" prop=\"affiliatedStreetCode\">\r\n                      <el-select\r\n                        v-model=\"form.affiliatedStreetCode\"\r\n                        placeholder=\"请选择所属街道\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.affiliated_street\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"开户银行\" prop=\"bankName\">\r\n                      <el-input\r\n                        v-model=\"form.bankName\"\r\n                        placeholder=\"请选择开户银行\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"联系电话\" prop=\"contactsPhone\">\r\n                      <el-input\r\n                        v-model=\"form.contactsPhone\"\r\n                        placeholder=\"请选择联系电话\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"选择项目\" prop=\"itemTypeCode\">\r\n                      <el-cascader\r\n                        ref=\"test\"\r\n                        :props=\"projectProps\"\r\n                        v-model=\"form.itemTypeCode\"\r\n                        placeholder=\"请选择项目\"\r\n                        @change=\"itemTypeChanged\"\r\n                      ></el-cascader>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"企业注册地址\" prop=\"companyAddress\">\r\n                      <el-input\r\n                        v-model=\"form.companyAddress\"\r\n                        placeholder=\"请输入企业注册地址\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"银行账号\" prop=\"bankCode\">\r\n                      <el-input\r\n                        v-model=\"form.bankCode\"\r\n                        placeholder=\"输入请银行账号\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"申请书上传\" prop=\"application\">\r\n                      <el-upload\r\n                        :headers=\"headers\"\r\n                        :action=\"actionUrl\"\r\n                        accept=\".pdf\"\r\n                        :file-list=\"applicationList\"\r\n                        :on-remove=\"handleApplicationRemove\"\r\n                        :on-success=\"handleApplicationSuccess\"\r\n                        :on-exceed=\"handleExceedLicence\"\r\n                        :on-preview=\"handlePreview\"\r\n                        :limit=\"1\"\r\n                      >\r\n                        <div>\r\n                          <el-button\r\n                            size=\"small\"\r\n                            type=\"primary\"\r\n                            icon=\"el-icon-upload2\"\r\n                            >上传文件</el-button\r\n                          >\r\n                          <span class=\"tip\">仅限pdf格式</span>\r\n                        </div>\r\n                      </el-upload>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"开户许可证上传\" prop=\"accountLicence\">\r\n                      <el-upload\r\n                        :headers=\"headers\"\r\n                        :action=\"actionUrl\"\r\n                        accept=\".pdf\"\r\n                        :file-list=\"accountLicenceList\"\r\n                        :on-remove=\"handleAccountRemove\"\r\n                        :on-exceed=\"handleExceedLicence\"\r\n                        :on-success=\"handleAccountSuccess\"\r\n                        :on-preview=\"handlePreview\"\r\n                        :limit=\"1\"\r\n                      >\r\n                        <div>\r\n                          <el-button\r\n                            size=\"small\"\r\n                            type=\"danger\"\r\n                            icon=\"el-icon-upload2\"\r\n                            >上传文件</el-button\r\n                          >\r\n                          <span class=\"tip\">仅限pdf格式</span>\r\n                        </div>\r\n                      </el-upload>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"changeMode\">返回</el-button>\r\n                <el-button type=\"error\" @click=\"changeMode(0)\"\r\n                  >暂存草稿</el-button\r\n                >\r\n                <el-button type=\"danger\" @click=\"submitForm(1)\"\r\n                  >提交审核</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport {\r\n  getNoninductiveDetail,\r\n  editNoninductive,\r\n} from \"@/api/system/noninductive\";\r\nimport { getDicts, getSecondDicts } from \"@/api/system/dict/data.js\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\"affiliated_unit\", \"capital_source\", \"affiliated_street\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      info: {},\r\n      form: {},\r\n      accountLicenceList: [],\r\n      // 表单校验\r\n      rules: {\r\n        affiliatedUnitCode: [\r\n          { required: true, message: \"所属单位不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyCreditCode: [\r\n          { required: true, message: \"企业统一代码不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        capitalSourceCode: [\r\n          { required: true, message: \"资金来源不能为空\", trigger: \"blur\" },\r\n        ],\r\n        affiliatedStreetCode: [\r\n          { required: true, message: \"所属街道不能为空\", trigger: \"blur\" },\r\n        ],\r\n        bankName: [\r\n          { required: true, message: \"开户银行不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsPhone: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n        itemTypeCode: [\r\n          { required: true, message: \"选择项目不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyAddress: [\r\n          { required: true, message: \"企业注册地址不能为空\", trigger: \"blur\" },\r\n        ],\r\n        bankCode: [\r\n          { required: true, message: \"银行账号不能为空\", trigger: \"blur\" },\r\n        ],\r\n        application: [\r\n          { required: true, message: \"申请书不能为空\", trigger: \"blur\" },\r\n        ],\r\n        accountLicence: [\r\n          { required: true, message: \"申请书不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      projectProps: {\r\n        lazy: true,\r\n        lazyLoad(node, resolve) {\r\n          let res = [];\r\n          if (node.level == 0) {\r\n            getDicts(\"item_type\").then((response) => {\r\n              response.data.forEach((element) => {\r\n                res.push({\r\n                  label: element.dictLabel,\r\n                  value: element.dictValue,\r\n                  dictCode: element.dictCode,\r\n                });\r\n              });\r\n              const nodes = res;\r\n              resolve(nodes);\r\n            });\r\n          } else {\r\n            getSecondDicts({ parentCode: node.data.dictCode }).then(\r\n              (response) => {\r\n                response.data.forEach((element) => {\r\n                  res.push({\r\n                    label: element.dictLabel,\r\n                    value: element.dictValue,\r\n                    leaf: true,\r\n                  });\r\n                });\r\n                const nodes = res;\r\n                resolve(nodes);\r\n              }\r\n            );\r\n          }\r\n          // const nodes = Array.from({ length: level + 1 }).map((item) => ({\r\n          //   value: ++id,\r\n          //   label: `选项${id}`,\r\n          //   leaf: level >= 2,\r\n          // }));\r\n          // 通过调用resolve将子节点数据返回，通知组件数据加载完成\r\n        },\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    itemTypeChanged(val) {\r\n      this.form.itemTypeFirstCode = val[0];\r\n      this.form.itemTypeSecondCode = val[1];\r\n    },\r\n    getDetail() {\r\n      let userId = this.$route.query.id;\r\n      getNoninductiveDetail(userId).then((response) => {\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    // 申请书、开户许可证预览\r\n    handlePreview(file) {\r\n      window.open(file.url);\r\n    },\r\n    // 开户许可证上传数量限制\r\n    handleExceedLicence(files, fileList) {\r\n      let num = files.length + fileList.length;\r\n      if (num >= 1) {\r\n        this.$message.error(\"上传数量超过上限\");\r\n        return false;\r\n      }\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n\r\n    changeMode() {\r\n      if (this.isDetail) {\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n        this.form.itemTypeCode = [\r\n          this.info.itemTypeFirstCode,\r\n          this.info.itemTypeSecondCode,\r\n        ];\r\n        this.accountLicenceList = this.info.accountLicence\r\n          ? [\r\n              {\r\n                name: this.info.accountLicenceName,\r\n                url: this.info.accountLicence,\r\n              },\r\n            ]\r\n          : [];\r\n        this.applicationList = this.info.application\r\n          ? [\r\n              {\r\n                name: this.info.applicationName,\r\n                url: this.info.application,\r\n              },\r\n            ]\r\n          : [];\r\n      } else {\r\n        this.isDetail = true;\r\n        this.form = {};\r\n        this.getDetail();\r\n      }\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          editNoninductive({ ...this.form, isSubmit: type }).then(\r\n            (response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            }\r\n          );\r\n        }\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.application = res.data.url;\r\n        this.form.applicationName = res.data.name;\r\n      }\r\n    },\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .notice-record-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 20px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 300px;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n        }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}