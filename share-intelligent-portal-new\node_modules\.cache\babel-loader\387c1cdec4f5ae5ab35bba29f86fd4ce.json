{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\orderSharing.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\orderSharing.vue", "mtime": 1750311962968}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_manufacturingSharing", "require", "name", "data", "currentPage", "pageNum", "pageSize", "total", "leftMenuList", "loading", "orderList", "proProcessesList", "enteringFactoryList", "spanConfig", "deadline", "<PERSON><PERSON><PERSON>", "contactPhone", "productId", "created", "$route", "query", "handleClick", "parseInt", "page", "getOrderList", "methods", "_this", "params", "auditStatus", "orderListData", "then", "res", "code", "rows", "for<PERSON>ach", "item", "order", "replace", "getProProcesses", "_this2", "processListData", "getEnteringFactory", "_this3", "settledStatus", "enteringFactoryListData", "index", "handleSizeChange", "handleCurrentChange", "viewProductDetail", "row", "$router", "push", "id", "$message", "error", "joinNow", "jumpIntention", "_this4", "userInfo", "JSON", "parse", "sessionStorage", "getItem", "memberCompanyName", "$confirm", "confirmButtonText", "cancelButtonText", "type", "cancelButtonClass", "confirmButtonClass", "catch", "concat", "processName", "updateTime", "goFactoryDetail", "isValidRow", "calculateSpans", "_this5", "fields", "field", "Array", "length", "fill", "currentGroupSize", "currentGroupStart", "currentGroupId", "_loop", "i", "j", "objectSpanMethod", "_ref", "column", "rowIndex", "label", "rowspan", "colspan", "watch", "immediate", "handler", "newVal"], "sources": ["src/views/manufacturingSharing/components/orderSharing.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      制造共享\r\n      <!-- <div class=\"imgContent\">\r\n        <div class=\"imgStyle\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../assets/order/orderStep.png\" alt=\"\" />\r\n          <div class=\"joinNow\" @click=\"joinNow\">立即入驻</div>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n    <div class=\"card-container content_card\">\r\n      <div class=\"leftMenu\">\r\n        <div :class=\"currentPage === index ? 'menuItemHover' : 'menuItem'\" v-for=\"(item, index) in leftMenuList\"\r\n          :key=\"index\" @click=\"handleClick(index)\">\r\n          {{ item.name }}\r\n        </div>\r\n      </div>\r\n      <div class=\"tableStyle\">\r\n        <div v-if=\"currentPage === 0\">\r\n          <el-table v-loading=\"loading\" :data=\"orderList\" style=\"width: 910px\" key=\"table1\"\r\n            :span-method=\"objectSpanMethod\">\r\n            <el-table-column label=\"需求截止时间\" align=\"center\" width=\"100\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{\r\n                  scope.row.order ? scope.row.order.deadline : \"\"\r\n                  }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"产品名称\" align=\"center\" prop=\"name\" />\r\n            <el-table-column label=\"规格型号\" align=\"center\" prop=\"modelNumber\" />\r\n            <el-table-column label=\"数量\" align=\"center\" prop=\"quantity\" width=\"60\" />\r\n            <el-table-column label=\"单位\" align=\"center\" prop=\"unit\" width=\"60\" />\r\n            <!-- <el-table-column label=\"可承接量\" align=\"center\" prop=\"capacity\" /> -->\r\n            <el-table-column label=\"联系人\" align=\"center\" width=\"100\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{\r\n                  scope.row.order ? scope.row.order.contactPerson : \"\"\r\n                  }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"联系方式\" align=\"center\" width=\"120\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{\r\n                  scope.row.order ? scope.row.order.contactPhone : \"\"\r\n                  }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <!-- <el-table-column label=\"订单状态\" align=\"center\">\r\n              <template slot-scope=\"scope\" v-if=\"scope.row.order\">\r\n                {{\r\n                  scope.row.order.status == \"0\"\r\n                    ? \"未接单\"\r\n                    : scope.row.order.status == \"1\"\r\n                      ? \"进行中\"\r\n                      : \"已完成\"\r\n                }}\r\n              </template>\r\n            </el-table-column> -->\r\n            <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"80\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"btnStyle\" @click=\"viewProductDetail(scope.row.order)\">\r\n                  查看详情\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <!-- <el-table v-loading=\"loading\" :data=\"orderList\" style=\"width: 910px\" key=\"table1\">\r\n            <el-table-column label=\"需求企业\" align=\"center\" prop=\"demandCompany\" width=\"150\" />\r\n            <el-table-column label=\"需求截止时间\" align=\"center\" prop=\"deadline\" width=\"100\" />\r\n            <el-table-column label=\"托单价格\" align=\"center\" prop=\"price\" />\r\n            <el-table-column label=\"联系电话\" align=\"center\" prop=\"contactPhone\" />\r\n            <el-table-column label=\"交货地址\" align=\"center\" prop=\"deliveryAddress\" width=\"150\" />\r\n            <el-table-column label=\"文件要求\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"text-ellipsis-2\">\r\n                  {{ scope.row.fileRequirement }}\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"btnStyle\" @click=\"viewProductDetail(scope.row)\">\r\n                  查看详情\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table> -->\r\n          <!-- 分页 -->\r\n          <div class=\"pageStyle\">\r\n            <el-pagination v-if=\"orderList && orderList.length > 0\" background layout=\"prev, pager, next\"\r\n              class=\"activity-pagination\" :page-size=\"pageSize\" :current-page=\"pageNum\" :total=\"total\"\r\n              @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n        <div v-if=\"currentPage === 1\">\r\n          <el-table v-loading=\"loading\" :data=\"proProcessesList\" style=\"width: 910px\" key=\"table2\">\r\n            <el-table-column label=\"外协工序名称\" align=\"center\" prop=\"processName\" />\r\n            <el-table-column label=\"外协单位\" align=\"center\" prop=\"companyName\" />\r\n            <el-table-column label=\"联系人\" align=\"center\" prop=\"contactPerson\" />\r\n            <el-table-column label=\"联系方式\" align=\"center\" prop=\"contactPhone\" />\r\n            <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n              <template slot-scope=\"scope\">\r\n                <div class=\"btnStyle\" @click=\"jumpIntention(scope.row)\">\r\n                  我有意向\r\n                </div>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <!-- 分页 -->\r\n          <div class=\"pageStyle\">\r\n            <el-pagination v-if=\"proProcessesList && proProcessesList.length > 0\" background layout=\"prev, pager, next\"\r\n              class=\"activity-pagination\" :page-size=\"pageSize\" :current-page=\"pageNum\" :total=\"total\"\r\n              @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n        <div v-if=\"currentPage == 2\">\r\n          <div class=\"factoryContent\" v-loading=\"loading\">\r\n            <div class=\"factory_item\" v-for=\"(item, index) in enteringFactoryList\" :key=\"index\"\r\n              @click=\"goFactoryDetail(item.id)\">\r\n              <div class=\"companyName\">{{ item.companyName }}</div>\r\n              <div class=\"industry\">{{ item.industry }}</div>\r\n            </div>\r\n            <!-- 分页 -->\r\n            <div class=\"pageStyle\">\r\n              <el-pagination v-if=\"enteringFactoryList && enteringFactoryList.length > 0\" background\r\n                layout=\"prev, pager, next\" class=\"activity-pagination\" :page-size=\"pageSize\" :current-page=\"pageNum\"\r\n                :total=\"total\" @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n              </el-pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {\r\n  orderListData,\r\n  processListData,\r\n  enteringFactoryListData,\r\n} from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"orderSharing\",\r\n  data() {\r\n    return {\r\n      currentPage: 0,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      leftMenuList: [\r\n        {\r\n          name: \"生产订单\",\r\n        },\r\n        {\r\n          name: \"工序外协\",\r\n        },\r\n        {\r\n          name: \"入驻工厂\",\r\n        },\r\n      ],\r\n      loading: false,\r\n      orderList: [],\r\n      proProcessesList: [],\r\n      enteringFactoryList: [],\r\n      spanConfig: {\r\n        // 存储合并配置，所有字段使用相同的合并规则\r\n        deadline: [],\r\n        contactPerson: [],\r\n        contactPhone: []\r\n      },\r\n      productId: \"\",\r\n    };\r\n  },\r\n  created() {\r\n    this.productId = this.$route.query.productId ? this.$route.query.productId : \"\";\r\n    this.handleClick(parseInt(this.$route.query.page) || 0);\r\n    this.getOrderList();\r\n  },\r\n  methods: {\r\n    getOrderList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        auditStatus: \"1\",\r\n      };\r\n      orderListData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.orderList = res.rows;\r\n          // 手机号脱敏处理\r\n          this.orderList.forEach((item) => {\r\n            if (item.order) {\r\n              item.order.contactPhone = item.order.contactPhone.replace(/(\\d{3})\\d{4}(\\d{4})/, \"$1****$2\");\r\n            }\r\n          });\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    getProProcesses() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      };\r\n      processListData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.proProcessesList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    getEnteringFactory() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: 12,\r\n        settledStatus: \"1\",\r\n        productId: this.productId,\r\n      };\r\n      enteringFactoryListData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.enteringFactoryList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    handleClick(index) {\r\n      this.currentPage = index;\r\n      this.pageNum = 1;\r\n      if (index == 0) {\r\n        this.getOrderList();\r\n      } else if (index == 1) {\r\n        this.getProProcesses();\r\n      } else {\r\n        this.getEnteringFactory();\r\n      }\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      if (this.currentPage == 0) {\r\n        this.getOrderList();\r\n      } else if (this.currentPage == 1) {\r\n        this.getProProcesses();\r\n      } else {\r\n        this.getEnteringFactory();\r\n      }\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      if (this.currentPage == 0) {\r\n        this.getOrderList();\r\n      } else if (this.currentPage == 1) {\r\n        this.getProProcesses();\r\n      } else {\r\n        this.getEnteringFactory();\r\n      }\r\n    },\r\n    viewProductDetail(row) {\r\n      if (row) {\r\n        this.$router.push(\"/productOrderDetail?id=\" + row.id);\r\n      } else {\r\n        this.$message.error(\"暂无该订单详情\");\r\n      }\r\n    },\r\n    joinNow() {\r\n      this.$router.push(\"/joinNow\");\r\n    },\r\n    jumpIntention(item) {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(\r\n          `/interested?demandName=${item.processName}&updateTime=${item.updateTime}&intentionType=5&fieldName=工序外协&intentionId=${item.id}`\r\n        );\r\n      }\r\n    },\r\n    goFactoryDetail(id) {\r\n      this.$router.push(\"/factoryDetail?id=\" + id);\r\n    },\r\n\r\n    // 判断一行是否有效（有有效的order.id）\r\n    isValidRow(index) {\r\n      const row = this.orderList[index];\r\n      return row && row.order && row.order.id && row.order.id !== '';\r\n    },\r\n    // 计算合并配置（基于order.id）\r\n    calculateSpans() {\r\n      // 初始化所有字段的合并配置\r\n      const fields = ['deadline', 'contactPerson', 'contactPhone'];\r\n      fields.forEach(field => {\r\n        this.spanConfig[field] = new Array(this.orderList.length).fill(0);\r\n      });\r\n\r\n      if (this.orderList.length === 0) return;\r\n\r\n      let currentGroupSize = 0; // 当前分组的大小\r\n      let currentGroupStart = 0; // 当前分组的起始索引\r\n      let currentGroupId = null; // 当前分组的order.id\r\n\r\n      for (let i = 0; i <= this.orderList.length; i++) {\r\n        // 处理最后一行或分组结束\r\n        if (i === this.orderList.length ||\r\n          !this.isValidRow(i) ||\r\n          (currentGroupId !== null && this.orderList[i].order.id !== currentGroupId)) {\r\n\r\n          // 为当前分组设置合并配置\r\n          if (currentGroupSize > 0) {\r\n            fields.forEach(field => {\r\n              // 分组起始行设置为分组大小\r\n              this.spanConfig[field][currentGroupStart] = currentGroupSize;\r\n              // 分组内其他行设置为0（不显示）\r\n              for (let j = 1; j < currentGroupSize; j++) {\r\n                this.spanConfig[field][currentGroupStart + j] = 0;\r\n              }\r\n            });\r\n          }\r\n\r\n          // 重置分组状态\r\n          currentGroupSize = 0;\r\n          currentGroupId = null;\r\n\r\n          // 处理当前行（如果是无效行）\r\n          if (i < this.orderList.length && !this.isValidRow(i)) {\r\n            fields.forEach(field => {\r\n              this.spanConfig[field][i] = 1; // 无效行单独显示\r\n            });\r\n            continue;\r\n          }\r\n        }\r\n\r\n        // 开始新分组或继续当前分组\r\n        if (i < this.orderList.length) {\r\n          const row = this.orderList[i];\r\n          if (this.isValidRow(i)) {\r\n            if (currentGroupSize === 0) {\r\n              // 开始新分组\r\n              currentGroupStart = i;\r\n              currentGroupId = row.order.id;\r\n              currentGroupSize = 1;\r\n            } else {\r\n              // 继续当前分组\r\n              currentGroupSize++;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    // 表格合并方法\r\n    objectSpanMethod({ row, column, rowIndex }) {\r\n      // 需求截止时间列（第0列）\r\n      if (column.label === '需求截止时间') {\r\n        return {\r\n          rowspan: this.spanConfig.deadline[rowIndex],\r\n          colspan: this.spanConfig.deadline[rowIndex] > 0 ? 1 : 0\r\n        };\r\n      }\r\n      // 联系人列（第5列）\r\n      if (column.label === '联系人') {\r\n        return {\r\n          rowspan: this.spanConfig.contactPerson[rowIndex],\r\n          colspan: this.spanConfig.contactPerson[rowIndex] > 0 ? 1 : 0\r\n        };\r\n      }\r\n      // 联系电话列（第6列）\r\n      if (column.label === '联系方式') {\r\n        return {\r\n          rowspan: this.spanConfig.contactPhone[rowIndex],\r\n          colspan: this.spanConfig.contactPhone[rowIndex] > 0 ? 1 : 0\r\n        };\r\n      }\r\n\r\n      // 其他列不合并\r\n      return {\r\n        rowspan: 1,\r\n        colspan: 1\r\n      };\r\n    },\r\n\r\n  },\r\n  watch: {\r\n    orderList: {\r\n      immediate: true,\r\n      handler(newVal) {\r\n        if (newVal && newVal.length) {\r\n          this.calculateSpans();\r\n        }\r\n      }\r\n    }\r\n  },\r\n\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n  background-color: #f2f2f2;\r\n}\r\n\r\n.text-ellipsis-2 {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  line-height: 1.5;\r\n  /* 建议设置行高 */\r\n  max-height: 3em;\r\n  /* 2行 x 1.5行高 */\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  text-align: center;\r\n  line-height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n\r\n  .imgContent {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-top: 10px;\r\n\r\n    .imgStyle {\r\n      width: 1256px;\r\n      height: 206px;\r\n      position: relative;\r\n\r\n      .joinNow {\r\n        position: absolute;\r\n        right: 90px;\r\n        top: 75px;\r\n        width: 110px;\r\n        height: 50px;\r\n        background: #f79a47;\r\n        border-radius: 2px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 18px;\r\n        color: #ffffff;\r\n        line-height: 50px;\r\n        text-align: center;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.content_card {\r\n  min-height: 436px;\r\n  // max-height: 990px;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: 30px;\r\n  padding: 40px 45px 62px 43px;\r\n  display: flex;\r\n\r\n  .leftMenu {\r\n    width: 160px;\r\n    height: 180px;\r\n    background: #ffffff;\r\n    box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.08);\r\n    border-radius: 2px;\r\n\r\n    .menuItemHover {\r\n      width: 160px;\r\n      height: 60px;\r\n      background: #21c9b8;\r\n      box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.08);\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #ffffff;\r\n      text-align: center;\r\n      line-height: 60px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .menuItem {\r\n      width: 160px;\r\n      height: 60px;\r\n      box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.08);\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #333333;\r\n      text-align: center;\r\n      line-height: 60px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .tableStyle {\r\n    margin-left: 42px;\r\n    width: 100%;\r\n\r\n    .btnStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #21c9b8;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .factoryContent {\r\n      display: flex;\r\n      align-items: center;\r\n      flex-wrap: wrap;\r\n\r\n      .factory_item {\r\n        width: 290px;\r\n        height: 106px;\r\n        background: #ffffff;\r\n        box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.06);\r\n        border-radius: 2px;\r\n        margin-left: 20px;\r\n        padding: 21px;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .factory_item:hover {\r\n        box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);\r\n        scale: 1.01;\r\n      }\r\n\r\n      .factory_item:nth-child(3n + 1) {\r\n        margin-left: 0;\r\n      }\r\n\r\n      .factory_item:nth-child(n + 4) {\r\n        margin-top: 20px;\r\n      }\r\n\r\n      .companyName {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 16px;\r\n        color: #303133;\r\n        height: 16px;\r\n      }\r\n\r\n      .industry {\r\n        width: 90px;\r\n        height: 34px;\r\n        background: #eff9f9;\r\n        border-radius: 2px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 16px;\r\n        color: #21c9b8;\r\n        line-height: 34px;\r\n        text-align: center;\r\n        margin-top: 15px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .pageStyle {\r\n    margin-top: 60px;\r\n    width: 100%;\r\n    text-align: center;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AA2IA,IAAAA,qBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAMA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,YAAA,GACA;QACAN,IAAA;MACA,GACA;QACAA,IAAA;MACA,GACA;QACAA,IAAA;MACA,EACA;MACAO,OAAA;MACAC,SAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,UAAA;QACA;QACAC,QAAA;QACAC,aAAA;QACAC,YAAA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAD,SAAA,QAAAE,MAAA,CAAAC,KAAA,CAAAH,SAAA,QAAAE,MAAA,CAAAC,KAAA,CAAAH,SAAA;IACA,KAAAI,WAAA,CAAAC,QAAA,MAAAH,MAAA,CAAAC,KAAA,CAAAG,IAAA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACAD,YAAA,WAAAA,aAAA;MAAA,IAAAE,KAAA;MACA,KAAAjB,OAAA;MACA,IAAAkB,MAAA;QACAtB,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA,QAAA;QACAsB,WAAA;MACA;MACA,IAAAC,mCAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAN,KAAA,CAAAhB,SAAA,GAAAqB,GAAA,CAAAE,IAAA;UACA;UACAP,KAAA,CAAAhB,SAAA,CAAAwB,OAAA,WAAAC,IAAA;YACA,IAAAA,IAAA,CAAAC,KAAA;cACAD,IAAA,CAAAC,KAAA,CAAApB,YAAA,GAAAmB,IAAA,CAAAC,KAAA,CAAApB,YAAA,CAAAqB,OAAA;YACA;UACA;UACAX,KAAA,CAAAnB,KAAA,GAAAwB,GAAA,CAAAxB,KAAA;UACAmB,KAAA,CAAAjB,OAAA;QACA;MACA;IACA;IACA6B,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAA9B,OAAA;MACA,IAAAkB,MAAA;QACAtB,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA;MACA;MACA,IAAAkC,qCAAA,EAAAb,MAAA,EAAAG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAO,MAAA,CAAA5B,gBAAA,GAAAoB,GAAA,CAAAE,IAAA;UACAM,MAAA,CAAAhC,KAAA,GAAAwB,GAAA,CAAAxB,KAAA;UACAgC,MAAA,CAAA9B,OAAA;QACA;MACA;IACA;IACAgC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,KAAAjC,OAAA;MACA,IAAAkB,MAAA;QACAtB,OAAA,OAAAA,OAAA;QACAC,QAAA;QACAqC,aAAA;QACA1B,SAAA,OAAAA;MACA;MACA,IAAA2B,6CAAA,EAAAjB,MAAA,EAAAG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAU,MAAA,CAAA9B,mBAAA,GAAAmB,GAAA,CAAAE,IAAA;UACAS,MAAA,CAAAnC,KAAA,GAAAwB,GAAA,CAAAxB,KAAA;UACAmC,MAAA,CAAAjC,OAAA;QACA;MACA;IACA;IACAY,WAAA,WAAAA,YAAAwB,KAAA;MACA,KAAAzC,WAAA,GAAAyC,KAAA;MACA,KAAAxC,OAAA;MACA,IAAAwC,KAAA;QACA,KAAArB,YAAA;MACA,WAAAqB,KAAA;QACA,KAAAP,eAAA;MACA;QACA,KAAAG,kBAAA;MACA;IACA;IACAK,gBAAA,WAAAA,iBAAAxC,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,SAAAF,WAAA;QACA,KAAAoB,YAAA;MACA,gBAAApB,WAAA;QACA,KAAAkC,eAAA;MACA;QACA,KAAAG,kBAAA;MACA;IACA;IACAM,mBAAA,WAAAA,oBAAA1C,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,SAAAD,WAAA;QACA,KAAAoB,YAAA;MACA,gBAAApB,WAAA;QACA,KAAAkC,eAAA;MACA;QACA,KAAAG,kBAAA;MACA;IACA;IACAO,iBAAA,WAAAA,kBAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,OAAA,CAAAC,IAAA,6BAAAF,GAAA,CAAAG,EAAA;MACA;QACA,KAAAC,QAAA,CAAAC,KAAA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,KAAAL,OAAA,CAAAC,IAAA;IACA;IACAK,aAAA,WAAAA,cAAArB,IAAA;MAAA,IAAAsB,MAAA;MACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,cAAA,CAAAC,OAAA;MACA,MAAAJ,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAK,iBAAA;QACA,KAAAC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;UACAC,iBAAA;UACAC,kBAAA;QACA,GACAvC,IAAA;UACA2B,MAAA,CAAAP,OAAA,CAAAC,IAAA;QACA,GACAmB,KAAA;QACA;MACA;QACA,KAAApB,OAAA,CAAAC,IAAA,2BAAAoB,MAAA,CACApC,IAAA,CAAAqC,WAAA,kBAAAD,MAAA,CAAApC,IAAA,CAAAsC,UAAA,sEAAAF,MAAA,CAAApC,IAAA,CAAAiB,EAAA,CACA;MACA;IACA;IACAsB,eAAA,WAAAA,gBAAAtB,EAAA;MACA,KAAAF,OAAA,CAAAC,IAAA,wBAAAC,EAAA;IACA;IAEA;IACAuB,UAAA,WAAAA,WAAA9B,KAAA;MACA,IAAAI,GAAA,QAAAvC,SAAA,CAAAmC,KAAA;MACA,OAAAI,GAAA,IAAAA,GAAA,CAAAb,KAAA,IAAAa,GAAA,CAAAb,KAAA,CAAAgB,EAAA,IAAAH,GAAA,CAAAb,KAAA,CAAAgB,EAAA;IACA;IACA;IACAwB,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,MAAA;MACAA,MAAA,CAAA5C,OAAA,WAAA6C,KAAA;QACAF,MAAA,CAAAhE,UAAA,CAAAkE,KAAA,QAAAC,KAAA,CAAAH,MAAA,CAAAnE,SAAA,CAAAuE,MAAA,EAAAC,IAAA;MACA;MAEA,SAAAxE,SAAA,CAAAuE,MAAA;MAEA,IAAAE,gBAAA;MACA,IAAAC,iBAAA;MACA,IAAAC,cAAA;MAAA,IAAAC,KAAA,YAAAA,MAAAC,CAAA,EAEA;QACA;QACA,IAAAA,CAAA,KAAAV,MAAA,CAAAnE,SAAA,CAAAuE,MAAA,IACA,CAAAJ,MAAA,CAAAF,UAAA,CAAAY,CAAA,KACAF,cAAA,aAAAR,MAAA,CAAAnE,SAAA,CAAA6E,CAAA,EAAAnD,KAAA,CAAAgB,EAAA,KAAAiC,cAAA;UAEA;UACA,IAAAF,gBAAA;YACAL,MAAA,CAAA5C,OAAA,WAAA6C,KAAA;cACA;cACAF,MAAA,CAAAhE,UAAA,CAAAkE,KAAA,EAAAK,iBAAA,IAAAD,gBAAA;cACA;cACA,SAAAK,CAAA,MAAAA,CAAA,GAAAL,gBAAA,EAAAK,CAAA;gBACAX,MAAA,CAAAhE,UAAA,CAAAkE,KAAA,EAAAK,iBAAA,GAAAI,CAAA;cACA;YACA;UACA;;UAEA;UACAL,gBAAA;UACAE,cAAA;;UAEA;UACA,IAAAE,CAAA,GAAAV,MAAA,CAAAnE,SAAA,CAAAuE,MAAA,KAAAJ,MAAA,CAAAF,UAAA,CAAAY,CAAA;YACAT,MAAA,CAAA5C,OAAA,WAAA6C,KAAA;cACAF,MAAA,CAAAhE,UAAA,CAAAkE,KAAA,EAAAQ,CAAA;YACA;YAAA;UAEA;QACA;;QAEA;QACA,IAAAA,CAAA,GAAAV,MAAA,CAAAnE,SAAA,CAAAuE,MAAA;UACA,IAAAhC,GAAA,GAAA4B,MAAA,CAAAnE,SAAA,CAAA6E,CAAA;UACA,IAAAV,MAAA,CAAAF,UAAA,CAAAY,CAAA;YACA,IAAAJ,gBAAA;cACA;cACAC,iBAAA,GAAAG,CAAA;cACAF,cAAA,GAAApC,GAAA,CAAAb,KAAA,CAAAgB,EAAA;cACA+B,gBAAA;YACA;cACA;cACAA,gBAAA;YACA;UACA;QACA;MACA;MA9CA,SAAAI,CAAA,MAAAA,CAAA,SAAA7E,SAAA,CAAAuE,MAAA,EAAAM,CAAA;QAAA,IAAAD,KAAA,CAAAC,CAAA,GA2BA;MAAA;IAoBA;IAEA;IACAE,gBAAA,WAAAA,iBAAAC,IAAA;MAAA,IAAAzC,GAAA,GAAAyC,IAAA,CAAAzC,GAAA;QAAA0C,MAAA,GAAAD,IAAA,CAAAC,MAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;MACA;MACA,IAAAD,MAAA,CAAAE,KAAA;QACA;UACAC,OAAA,OAAAjF,UAAA,CAAAC,QAAA,CAAA8E,QAAA;UACAG,OAAA,OAAAlF,UAAA,CAAAC,QAAA,CAAA8E,QAAA;QACA;MACA;MACA;MACA,IAAAD,MAAA,CAAAE,KAAA;QACA;UACAC,OAAA,OAAAjF,UAAA,CAAAE,aAAA,CAAA6E,QAAA;UACAG,OAAA,OAAAlF,UAAA,CAAAE,aAAA,CAAA6E,QAAA;QACA;MACA;MACA;MACA,IAAAD,MAAA,CAAAE,KAAA;QACA;UACAC,OAAA,OAAAjF,UAAA,CAAAG,YAAA,CAAA4E,QAAA;UACAG,OAAA,OAAAlF,UAAA,CAAAG,YAAA,CAAA4E,QAAA;QACA;MACA;;MAEA;MACA;QACAE,OAAA;QACAC,OAAA;MACA;IACA;EAEA;EACAC,KAAA;IACAtF,SAAA;MACAuF,SAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA,IAAAA,MAAA,IAAAA,MAAA,CAAAlB,MAAA;UACA,KAAAL,cAAA;QACA;MACA;IACA;EACA;AAEA", "ignoreList": []}]}