{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Sidebar\\Logo.vue?vue&type=style&index=0&id=6494804b&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Sidebar\\Logo.vue", "mtime": 1750311962855}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnNpZGViYXJMb2dvRmFkZS1lbnRlci1hY3RpdmUgew0KICB0cmFuc2l0aW9uOiBvcGFjaXR5IDEuNXM7DQp9DQoNCi5zaWRlYmFyTG9nb0ZhZGUtZW50ZXIsDQouc2lkZWJhckxvZ29GYWRlLWxlYXZlLXRvIHsNCiAgb3BhY2l0eTogMDsNCn0NCg0KLnNpZGViYXItbG9nby1jb250YWluZXIgew0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQogIHdpZHRoOiAxMDAlOw0KICBoZWlnaHQ6IDUwcHg7DQogIGxpbmUtaGVpZ2h0OiA1MHB4Ow0KICBiYWNrZ3JvdW5kOiAjMmIyZjNhOw0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIG92ZXJmbG93OiBoaWRkZW47DQoNCiAgJiAuc2lkZWJhci1sb2dvLWxpbmsgew0KICAgIGhlaWdodDogMTAwJTsNCiAgICB3aWR0aDogMTAwJTsNCg0KICAgICYgLnNpZGViYXItbG9nbyB7DQogICAgICB3aWR0aDogMzJweDsNCiAgICAgIGhlaWdodDogMzJweDsNCiAgICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7DQogICAgICBtYXJnaW4tcmlnaHQ6IDEycHg7DQogICAgfQ0KDQogICAgJiAuc2lkZWJhci10aXRsZSB7DQogICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgICBtYXJnaW46IDA7DQogICAgICBjb2xvcjogI2ZmZjsNCiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7DQogICAgICBsaW5lLWhlaWdodDogNTBweDsNCiAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgIGZvbnQtZmFtaWx5OiBBdmVuaXIsIEhlbHZldGljYSBOZXVlLCBBcmlhbCwgSGVsdmV0aWNhLCBzYW5zLXNlcmlmOw0KICAgICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsNCiAgICB9DQogIH0NCg0KICAmLmNvbGxhcHNlIHsNCiAgICAuc2lkZWJhci1sb2dvIHsNCiAgICAgIG1hcmdpbi1yaWdodDogMHB4Ow0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["Logo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Logo.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\r\n  <div\r\n    class=\"sidebar-logo-container\"\r\n    :class=\"{ collapse: collapse }\"\r\n    :style=\"{\r\n      backgroundColor:\r\n        sideTheme === 'theme-dark'\r\n          ? variables.menuBackground\r\n          : variables.menuLightBackground,\r\n    }\"\r\n  >\r\n    <transition name=\"sidebarLogoFade\">\r\n      <router-link\r\n        v-if=\"collapse\"\r\n        key=\"collapse\"\r\n        class=\"sidebar-logo-link\"\r\n        to=\"/\"\r\n      >\r\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\" />\r\n        <h1\r\n          v-else\r\n          class=\"sidebar-title\"\r\n          :style=\"{\r\n            color:\r\n              sideTheme === 'theme-dark'\r\n                ? variables.logoTitleColor\r\n                : variables.logoLightTitleColor,\r\n          }\"\r\n        >\r\n          {{ title }}\r\n        </h1>\r\n      </router-link>\r\n      <router-link v-else key=\"expand\" class=\"sidebar-logo-link\" to=\"/\">\r\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\" />\r\n        <h1\r\n          class=\"sidebar-title\"\r\n          :style=\"{\r\n            color:\r\n              sideTheme === 'theme-dark'\r\n                ? variables.logoTitleColor\r\n                : variables.logoLightTitleColor,\r\n          }\"\r\n        >\r\n          {{ title }}\r\n        </h1>\r\n      </router-link>\r\n    </transition>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport logoImg from \"@/assets/logo/logo.png\";\r\nimport variables from \"@/assets/styles/variables.scss\";\r\n\r\nexport default {\r\n  name: \"SidebarLogo\",\r\n  props: {\r\n    collapse: {\r\n      type: Boolean,\r\n      required: true,\r\n    },\r\n  },\r\n  computed: {\r\n    variables() {\r\n      return variables;\r\n    },\r\n    sideTheme() {\r\n      return this.$store.state.settings.sideTheme;\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      title: \"易复材共享智造工业互联网平台\",\r\n      logo: logoImg,\r\n    };\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.sidebarLogoFade-enter-active {\r\n  transition: opacity 1.5s;\r\n}\r\n\r\n.sidebarLogoFade-enter,\r\n.sidebarLogoFade-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.sidebar-logo-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 50px;\r\n  line-height: 50px;\r\n  background: #2b2f3a;\r\n  text-align: center;\r\n  overflow: hidden;\r\n\r\n  & .sidebar-logo-link {\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    & .sidebar-logo {\r\n      width: 32px;\r\n      height: 32px;\r\n      vertical-align: middle;\r\n      margin-right: 12px;\r\n    }\r\n\r\n    & .sidebar-title {\r\n      display: inline-block;\r\n      margin: 0;\r\n      color: #fff;\r\n      font-weight: 600;\r\n      line-height: 50px;\r\n      font-size: 14px;\r\n      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  &.collapse {\r\n    .sidebar-logo {\r\n      margin-right: 0px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}