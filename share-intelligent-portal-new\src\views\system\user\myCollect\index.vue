<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu />
      </el-col>
      <el-col :span="20" :xs="24">
        <div class="content" v-loading="loading">
          <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="appName" label="收藏应用"> </el-table-column>
            <el-table-column prop="appLabel" label="应用标签">
            </el-table-column>
            <el-table-column label="状态"> 已收藏 </el-table-column>
            <el-table-column label="操作" width="150">
              <template slot-scope="scope">
                <el-button
                  @click.native.prevent="viewDetail(scope.row.id)"
                  type="text"
                  size="small"
                >
                  详情
                </el-button>
                <el-button
                  @click.native.prevent="cancelCollect(scope.row.id)"
                  type="text"
                  size="small"
                >
                  取消收藏
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            v-show="total > 0"
            background
            layout="prev, pager, next"
            :page-size="5"
            :current-page.sync="queryParams.pageNum"
            @current-change="handleCurrentChange"
            :total="total"
          >
          </el-pagination>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { collectList, appliCancelCollect } from "@/api/appliMarket";
import UserMenu from "../components/userMenu.vue";
import store from "@/store";

export default {
  name: "Operlog",
  dicts: ["sys_oper_type", "sys_common_status"],
  components: { UserMenu },
  data() {
    return {
      loading: false,
      queryParams: {
        pageNum: 1,
      },
      total: 0,
      tableData: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      collectList().then((res) => {
        this.loading = false;
        if (res.code === 200) {
          this.tableData = res.data;
          // this.total = res.total;
        }
      });
    },
    viewDetail(id) {
      let routeData = this.$router.resolve({
        path: "/purchaseapp",
        query: { id },
      });
      window.open(routeData.href, "_blank");
    },
    cancelCollect(id) {
      let data = {
        id,
        userId: store.getters.userId,
      };
      appliCancelCollect(data).then((res) => {
        if (res.code === 200) {
          this.$message.success("操作成功!");
          this.getList();
        }
      });
    },
    handleCurrentChange(num) {
      this.queryParams.pageNum = num;
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #f4f5f9;
}
.content {
  width: 100%;
  height: calc(100vh - 150px);
  padding: 40px;
  background: #ffffff;
  // background: rgb(242, 248, 255);
}
</style>
