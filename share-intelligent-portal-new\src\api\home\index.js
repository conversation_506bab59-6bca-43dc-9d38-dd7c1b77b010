import request from "@/utils/request";

// 首页-需求对接-需求
export function demandData(params) {
  return request({
    url: "/portalweb/demand/listDesk",
    method: "get",
    params,
  });
}

// 需求详情
export function demandDetailData(params) {
  return request({
    url: "/portalweb/demand/detailDesk",
    method: "get",
    params,
  });
}

// 首页-需求对接-资源
export function supplyData(params) {
  return request({
    url: "/portalweb/supply/listDesk",
    method: "get",
    params,
  });
}

// 供给资源详情
export function supplyDetailData(params) {
  return request({
    url: "/portalweb/supply/detailDesk",
    method: "get",
    params,
  });
}

// 首页-应用市场
export function appData(params) {
  return request({
    url: "/portalweb/AppStore/listDesk",
    method: "get",
    params,
  });
}

// 首页-动态资讯
export function infoData(params) {
  return request({
    url: "/portalweb/NewsInformation/listDesk",
    method: "get",
    params,
  });
}

// 动态资讯-详情
export function infoDetailData(params) {
  return request({
    url: "/portalweb/NewsInformation/detailDesk",
    method: "get",
    params,
  });
}

// 我的需求
export function myDemand(params) {
  return request({
    url: "/portalweb/demand/list",
    method: "get",
    params,
  });
}

// 我的供给
export function mySupply(params) {
  return request({
    url: "/portalweb/supply/list",
    method: "get",
    params,
  });
}

// 提交我有意向
export function submitIntention(params) {
  return request({
    url: "/portalweb/IntentionApply",
    method: "post",
    data: params,
  });
}