{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\demandForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\demandForm.vue", "mtime": 1750311962955}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwp2YXIgX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiRTovY29tcGFueS9ubWQvbm1kbmV3L3NoYXJlLWludGVsbGlnZW50L3NoYXJlLWludGVsbGlnZW50LXBvcnRhbC1uZXcvbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0V2l0aG91dFByb3BlcnRpZXMuanMiKSk7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maWx0ZXIuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LmpvaW4uanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmZ1bmN0aW9uLm5hbWUuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmpzb24uc3RyaW5naWZ5LmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5maWx0ZXIuanMiKTsKdmFyIF96aG0gPSByZXF1aXJlKCJAL2FwaS96aG0iKTsKdmFyIF9jYWNoZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC9wbHVnaW5zL2NhY2hlIikpOwp2YXIgX2V4Y2x1ZGVkID0gWyJkZW1hbmRUeXBlIiwgImtleXdvcmRzIiwgImFwcGxpY2F0aW9uQXJlYSIsICJzY2VuZVBpY3R1cmUiXTsgLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJkZW1hbmRGb3JtIiwKICBkaWN0czogWyJkZW1hbmRfdHlwZSIsICJkaXNwbGF5X3Jlc3RyaWN0aW9ucyIsICJhcHBsaWNhdGlvbl9hcmVhIl0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHZhciB1c2VyID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlcjsKICAgIHJldHVybiB7CiAgICAgIGxvYWRpbmc6IGZhbHNlLAogICAgICBmb3JtOiB7CiAgICAgICAgLy8g6ZyA5rGC5qCH6aKYCiAgICAgICAgZGVtYW5kVGl0bGU6IHVuZGVmaW5lZCwKICAgICAgICAvLyDpnIDmsYLnsbvlnosKICAgICAgICBkZW1hbmRUeXBlOiBbXSwKICAgICAgICAvLyDpnIDmsYLmj4/ov7AKICAgICAgICBzdW1tYXJ5OiB1bmRlZmluZWQsCiAgICAgICAgLy8g5YWz6ZSu6K+NCiAgICAgICAga2V5d29yZHM6IFtdLAogICAgICAgIC8vIOW6lOeUqOmihuWfnwogICAgICAgIGFwcGxpY2F0aW9uQXJlYTogW10sCiAgICAgICAgLy8g5Zy65pmv5Zu+54mHCiAgICAgICAgc2NlbmVQaWN0dXJlOiBbXSwKICAgICAgICAvLyDlsZXnpLrpmZDliLYKICAgICAgICBkaXNwbGF5UmVzdHJpY3Rpb25zOiB1bmRlZmluZWQsCiAgICAgICAgLy8g5YWs5Y+45ZCN56ewCiAgICAgICAgY29tcGFueU5hbWU6IHVzZXIuY29tcGFueU5hbWUsCiAgICAgICAgLy8g6IGU57O75Lq6CiAgICAgICAgY29udGFjdHNOYW1lOiB1c2VyLm5hbWUsCiAgICAgICAgLy8g6IGU57O755S16K+dCiAgICAgICAgY29udGFjdHNNb2JpbGU6IHVzZXIudGVsLAogICAgICAgIGF1ZGl0U3RhdHVzOiAiMSIsCiAgICAgICAgZGlzcGxheVN0YXR1czogIjEiLAogICAgICAgIHB1Ymxpc2hlck5hbWU6IHVzZXIubmFtZSwKICAgICAgICBwdWJsaXNoZXJNb2JpbGU6IHVzZXIudGVsLAogICAgICAgIGJ1c2luZXNzTm86IHVzZXIuYnVzc2luZXNzTm8KICAgICAgfSwKICAgICAgcnVsZXM6IHsKICAgICAgICBkZW1hbmRUaXRsZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuivt+i+k+WFpemcgOaxguagh+mimCIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICBkZW1hbmRUeXBlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup6ZyA5rGC57G75Z6LIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHN1bW1hcnk6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor7fovpPlhaXpnIDmsYLmj4/ov7AiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgYXBwbGljYXRpb25BcmVhOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+36YCJ5oup5bqU55So6aKG5Z+fIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGRpc3BsYXlSZXN0cmljdGlvbnM6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor7fpgInmi6nlsZXnpLrpmZDliLYiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgY29tcGFueU5hbWU6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICLor7fnu7TmiqTlhazlj7jlkI3np7AiLAogICAgICAgICAgdHJpZ2dlcjogImJsdXIiCiAgICAgICAgfV0sCiAgICAgICAgY29udGFjdHNOYW1lOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+357u05oqk6IGU57O75Lq6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIGNvbnRhY3RzTW9iaWxlOiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6K+357u05oqk6IGU57O755S16K+dIiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBpbml0OiBmdW5jdGlvbiBpbml0KCkgewogICAgICB2YXIgZGF0YSA9IF9jYWNoZS5kZWZhdWx0LmxvY2FsLmdldEpTT04oImRlbWFuZF9kYXRhIik7CiAgICAgIGlmIChkYXRhKSB7CiAgICAgICAgdGhpcy5mb3JtID0gZGF0YTsKICAgICAgfQogICAgfSwKICAgIG9uQ2FuY2VsOiBmdW5jdGlvbiBvbkNhbmNlbCgpIHsKICAgICAgdGhpcy4kcm91dGVyLmJhY2soKTsKICAgIH0sCiAgICBvblNhdmU6IGZ1bmN0aW9uIG9uU2F2ZSgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgX2NhY2hlLmRlZmF1bHQubG9jYWwuc2V0SlNPTigiZGVtYW5kX2RhdGEiLCBfdGhpcy5mb3JtKTsKICAgICAgICAgIF90aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaaguWtmOaIkOWKnyIpOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgb25TdWJtaXQ6IGZ1bmN0aW9uIG9uU3VibWl0KHN0YXR1cykgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKGZ1bmN0aW9uICh2YWxpZCkgewogICAgICAgIGlmICh2YWxpZCkgewogICAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSB0cnVlOwogICAgICAgICAgdmFyIF90aGlzMiRmb3JtID0gX3RoaXMyLmZvcm0sCiAgICAgICAgICAgIGRlbWFuZFR5cGUgPSBfdGhpczIkZm9ybS5kZW1hbmRUeXBlLAogICAgICAgICAgICBrZXl3b3JkcyA9IF90aGlzMiRmb3JtLmtleXdvcmRzLAogICAgICAgICAgICBhcHBsaWNhdGlvbkFyZWEgPSBfdGhpczIkZm9ybS5hcHBsaWNhdGlvbkFyZWEsCiAgICAgICAgICAgIHNjZW5lUGljdHVyZSA9IF90aGlzMiRmb3JtLnNjZW5lUGljdHVyZSwKICAgICAgICAgICAgcmVzdCA9ICgwLCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXMyLmRlZmF1bHQpKF90aGlzMiRmb3JtLCBfZXhjbHVkZWQpOwogICAgICAgICAgdmFyIGRhdGEgPSAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCByZXN0KSwge30sIHsKICAgICAgICAgICAgYXVkaXRTdGF0dXM6IHN0YXR1cwogICAgICAgICAgfSk7CiAgICAgICAgICBpZiAoZGVtYW5kVHlwZS5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIGRhdGFbImRlbWFuZFR5cGUiXSA9IGRlbWFuZFR5cGUuam9pbigpOwogICAgICAgICAgfQogICAgICAgICAgaWYgKGtleXdvcmRzLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgZGF0YVsia2V5d29yZHMiXSA9IGtleXdvcmRzLmpvaW4oKTsKICAgICAgICAgIH0KICAgICAgICAgIGlmIChhcHBsaWNhdGlvbkFyZWEubGVuZ3RoID4gMCkgewogICAgICAgICAgICBkYXRhWyJhcHBsaWNhdGlvbkFyZWEiXSA9IGFwcGxpY2F0aW9uQXJlYS5qb2luKCk7CiAgICAgICAgICB9CiAgICAgICAgICBpZiAoc2NlbmVQaWN0dXJlLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgZGF0YVsic2NlbmVQaWN0dXJlIl0gPSBKU09OLnN0cmluZ2lmeShzY2VuZVBpY3R1cmUpOwogICAgICAgICAgfQogICAgICAgICAgKDAsIF96aG0uZGVtYW5kQWRkKShkYXRhKS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICAgICAgdmFyIGNvZGUgPSByZXMuY29kZSwKICAgICAgICAgICAgICBtc2cgPSByZXMubXNnOwogICAgICAgICAgICBpZiAoY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICAgICAgX2NhY2hlLmRlZmF1bHQubG9jYWwucmVtb3ZlKCJkZW1hbmRfZGF0YSIpOwogICAgICAgICAgICAgIF90aGlzMi4kbWVzc2FnZS5zdWNjZXNzKCLlj5HluIPmiJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczIuJHJvdXRlci5iYWNrKCk7CiAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLmVycm9yKG1zZyB8fCAi5Y+R5biD5aSx6LSlIik7CiAgICAgICAgICAgIH0KICAgICAgICAgIH0pLmZpbmFsbHkoZnVuY3Rpb24gKCkgewogICAgICAgICAgICByZXR1cm4gX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIH0pOwogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgaGFuZGxlS2V5d29yZExpc3Q6IGZ1bmN0aW9uIGhhbmRsZUtleXdvcmRMaXN0KCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgdmFyIHN1bW1hcnkgPSB0aGlzLmZvcm0uc3VtbWFyeTsKICAgICAgaWYgKHN1bW1hcnkpIHsKICAgICAgICAoMCwgX3pobS5rZXl3b3JkTGlzdCkoc3VtbWFyeSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICB2YXIgY29kZSA9IHJlcy5jb2RlLAogICAgICAgICAgICBkYXRhID0gcmVzLmRhdGEsCiAgICAgICAgICAgIG1zZyA9IHJlcy5tc2c7CiAgICAgICAgICBpZiAoY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICAgIF90aGlzMy5mb3JtLmtleXdvcmRzID0gZGF0YTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcihtc2cpOwogICAgICAgICAgfQogICAgICAgIH0pOwogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36L6T5YWl6ZyA5rGC5o+P6L+wIik7CiAgICAgIH0KICAgIH0sCiAgICBoYW5kbGVDbG9zZTogZnVuY3Rpb24gaGFuZGxlQ2xvc2UodGFnKSB7CiAgICAgIGNvbnNvbGUubG9nKCJ0YWciLCB0YWcpOwogICAgICB0aGlzLmZvcm0ua2V5d29yZHMgPSB0aGlzLmZvcm0ua2V5d29yZHMuZmlsdGVyKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0gIT09IHRhZzsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_zhm", "require", "_cache", "_interopRequireDefault", "_excluded", "name", "dicts", "data", "user", "$store", "state", "loading", "form", "demandTitle", "undefined", "demandType", "summary", "keywords", "applicationArea", "scenePicture", "displayRestrictions", "companyName", "contactsName", "contactsMobile", "tel", "auditStatus", "displayStatus", "publisherName", "publisherMobile", "businessNo", "bussinessNo", "rules", "required", "message", "trigger", "methods", "init", "cache", "local", "getJSON", "onCancel", "$router", "back", "onSave", "_this", "$refs", "validate", "valid", "setJSON", "$message", "success", "onSubmit", "status", "_this2", "_this2$form", "rest", "_objectWithoutProperties2", "default", "_objectSpread2", "length", "join", "JSON", "stringify", "demandAdd", "then", "res", "code", "msg", "remove", "error", "finally", "handleKeywordList", "_this3", "keywordList", "warning", "handleClose", "tag", "console", "log", "filter", "item"], "sources": ["src/views/form/components/demandForm.vue"], "sourcesContent": ["<template>\r\n  <div class=\"demand-form\">\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item prop=\"demandTitle\" label=\"需求标题\">\r\n        <el-input\r\n          v-model=\"form.demandTitle\"\r\n          maxlength=\"50\"\r\n          show-word-limit\r\n          placeholder=\"请输入标签\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"demandType\">\r\n        <div class=\"label-item\" slot=\"label\">\r\n          <span>需求类型</span>\r\n          <span class=\"extra\">（可按需求产品+应用行业+应用领域进行描述）</span>\r\n        </div>\r\n        <el-checkbox-group\r\n          v-model=\"form.demandType\"\r\n          placeholder=\"请选择\"\r\n          clearable\r\n        >\r\n          <el-checkbox\r\n            v-for=\"dict in dict.type.demand_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.value\"\r\n            :value=\"dict.value\"\r\n            >{{ dict.label }}</el-checkbox\r\n          >\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <el-form-item prop=\"summary\" label=\"需求描述\">\r\n        <el-input\r\n          type=\"textarea\"\r\n          v-model=\"form.summary\"\r\n          maxlength=\"500\"\r\n          rows=\"6\"\r\n          show-word-limit\r\n          placeholder=\"请输入需求描述\"\r\n        ></el-input>\r\n        <div class=\"extra-content\">\r\n          <div class=\"extra-content-header\">\r\n            <el-button @click=\"handleKeywordList\" size=\"small\" type=\"primary\"\r\n              >生成关键词</el-button\r\n            >\r\n            <span class=\"tip\">生成关键词有利于实现精准匹配哦！</span>\r\n          </div>\r\n          <div v-if=\"form.keywords.length > 0\" class=\"extra-content-body\">\r\n            <el-tag\r\n              :key=\"`${tag}_${index}`\"\r\n              v-for=\"(tag, index) in form.keywords\"\r\n              closable\r\n              size=\"small\"\r\n              disable-transitions\r\n              @close=\"handleClose(tag)\"\r\n            >\r\n              {{ tag }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item prop=\"applicationArea\" label=\"应用领域\">\r\n        <el-select\r\n          v-model=\"form.applicationArea\"\r\n          filterable\r\n          multiple\r\n          allow-create\r\n          style=\"width: 100%\"\r\n          placeholder=\"请选择\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in dict.type.application_area\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.label\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品照片\" prop=\"scenePicture\">\r\n        <ImageUpload v-model=\"form.scenePicture\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"展示限制\" prop=\"displayRestrictions\">\r\n        <el-select\r\n          v-model=\"form.displayRestrictions\"\r\n          placeholder=\"请选择\"\r\n          style=\"width: 100%\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.display_restrictions\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.companyName\"\r\n          placeholder=\"请先绑定公司\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系人\" prop=\"contactsName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactsName\"\r\n          placeholder=\"请先维护联系人\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系方式\" prop=\"contactsMobile\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactsMobile\"\r\n          placeholder=\"请先维护联系方式\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button @click.once=\"onCancel\">取消</el-button>\r\n        <el-button @click=\"onSubmit('0')\" type=\"primary\" plain\r\n          >暂存草稿</el-button\r\n        >\r\n        <el-button type=\"primary\" @click=\"onSubmit('1')\">发布</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { demandAdd, keywordList } from \"@/api/zhm\";\r\nimport cache from \"@/plugins/cache\";\r\n\r\nexport default {\r\n  name: \"demandForm\",\r\n  dicts: [\"demand_type\", \"display_restrictions\", \"application_area\"],\r\n  data() {\r\n    const { user } = this.$store.state;\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        // 需求标题\r\n        demandTitle: undefined,\r\n        // 需求类型\r\n        demandType: [],\r\n        // 需求描述\r\n        summary: undefined,\r\n        // 关键词\r\n        keywords: [],\r\n        // 应用领域\r\n        applicationArea: [],\r\n        // 场景图片\r\n        scenePicture: [],\r\n        // 展示限制\r\n        displayRestrictions: undefined,\r\n        // 公司名称\r\n        companyName: user.companyName,\r\n        // 联系人\r\n        contactsName: user.name,\r\n        // 联系电话\r\n        contactsMobile: user.tel,\r\n        auditStatus: \"1\",\r\n        displayStatus: \"1\",\r\n        publisherName: user.name,\r\n        publisherMobile: user.tel,\r\n        businessNo: user.bussinessNo,\r\n      },\r\n      rules: {\r\n        demandTitle: [\r\n          { required: true, message: \"请输入需求标题\", trigger: \"blur\" },\r\n        ],\r\n        demandType: [\r\n          { required: true, message: \"请选择需求类型\", trigger: \"blur\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"请输入需求描述\", trigger: \"blur\" },\r\n        ],\r\n        applicationArea: [\r\n          { required: true, message: \"请选择应用领域\", trigger: \"blur\" },\r\n        ],\r\n        displayRestrictions: [\r\n          { required: true, message: \"请选择展示限制\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"请维护公司名称\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"请维护联系人\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"请维护联系电话\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      const data = cache.local.getJSON(\"demand_data\");\r\n      if (data) {\r\n        this.form = data;\r\n      }\r\n    },\r\n    onCancel() {\r\n      this.$router.back();\r\n    },\r\n    onSave() {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          cache.local.setJSON(\"demand_data\", this.form);\r\n          this.$message.success(\"暂存成功\");\r\n        }\r\n      });\r\n    },\r\n    onSubmit(status) {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          const {\r\n            demandType,\r\n            keywords,\r\n            applicationArea,\r\n            scenePicture,\r\n            ...rest\r\n          } = this.form;\r\n          const data = {\r\n            ...rest,\r\n            auditStatus: status,\r\n          };\r\n          if (demandType.length > 0) {\r\n            data[\"demandType\"] = demandType.join();\r\n          }\r\n          if (keywords.length > 0) {\r\n            data[\"keywords\"] = keywords.join();\r\n          }\r\n          if (applicationArea.length > 0) {\r\n            data[\"applicationArea\"] = applicationArea.join();\r\n          }\r\n          if (scenePicture.length > 0) {\r\n            data[\"scenePicture\"] = JSON.stringify(scenePicture);\r\n          }\r\n\r\n          demandAdd(data)\r\n            .then((res) => {\r\n              const { code, msg } = res;\r\n              if (code === 200) {\r\n                cache.local.remove(\"demand_data\");\r\n                this.$message.success(\"发布成功\");\r\n                this.$router.back();\r\n              } else {\r\n                this.$message.error(msg || \"发布失败\");\r\n              }\r\n            })\r\n            .finally(() => (this.loading = false));\r\n        }\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      const { summary } = this.form;\r\n      if (summary) {\r\n        keywordList(summary).then((res) => {\r\n          const { code, data, msg } = res;\r\n          if (code === 200) {\r\n            this.form.keywords = data;\r\n          } else {\r\n            this.$message.error(msg);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning(\"请输入需求描述\");\r\n      }\r\n    },\r\n\r\n    handleClose(tag) {\r\n      console.log(\"tag\", tag);\r\n      this.form.keywords = this.form.keywords.filter((item) => item !== tag);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.demand-form {\r\n  width: 676px;\r\n  .label-item {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    .title {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #333333;\r\n      line-height: 18px;\r\n    }\r\n    .extra {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .extra-content {\r\n    padding: 12px 0;\r\n    &-header {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      .tip {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n        line-height: 14px;\r\n        margin-left: 12px;\r\n      }\r\n    }\r\n    &-body {\r\n      padding-top: 6px;\r\n      .el-tag {\r\n        margin-right: 12px;\r\n        &:last-child {\r\n          margin-right: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  ::v-deep.el-form-item__label {\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    line-height: 18px;\r\n    margin-bottom: 12px;\r\n    padding: 0;\r\n  }\r\n  .el-checkbox {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #262626;\r\n    line-height: 18px;\r\n    margin-right: 28px;\r\n  }\r\n  .footer-submit {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-top: 40px;\r\n    .el-button {\r\n      width: 160px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAiIA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAA,IAAAG,SAAA,kE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA,IAAAC,IAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,IAAA;IACA;MACAG,OAAA;MACAC,IAAA;QACA;QACAC,WAAA,EAAAC,SAAA;QACA;QACAC,UAAA;QACA;QACAC,OAAA,EAAAF,SAAA;QACA;QACAG,QAAA;QACA;QACAC,eAAA;QACA;QACAC,YAAA;QACA;QACAC,mBAAA,EAAAN,SAAA;QACA;QACAO,WAAA,EAAAb,IAAA,CAAAa,WAAA;QACA;QACAC,YAAA,EAAAd,IAAA,CAAAH,IAAA;QACA;QACAkB,cAAA,EAAAf,IAAA,CAAAgB,GAAA;QACAC,WAAA;QACAC,aAAA;QACAC,aAAA,EAAAnB,IAAA,CAAAH,IAAA;QACAuB,eAAA,EAAApB,IAAA,CAAAgB,GAAA;QACAK,UAAA,EAAArB,IAAA,CAAAsB;MACA;MACAC,KAAA;QACAlB,WAAA,GACA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,UAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,OAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,eAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,mBAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,WAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,YAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,cAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA;IACAC,IAAA,WAAAA,KAAA;MACA,IAAA7B,IAAA,GAAA8B,cAAA,CAAAC,KAAA,CAAAC,OAAA;MACA,IAAAhC,IAAA;QACA,KAAAK,IAAA,GAAAL,IAAA;MACA;IACA;IACAiC,QAAA,WAAAA,SAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAjC,IAAA,CAAAkC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAV,cAAA,CAAAC,KAAA,CAAAU,OAAA,gBAAAJ,KAAA,CAAAhC,IAAA;UACAgC,KAAA,CAAAK,QAAA,CAAAC,OAAA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAAC,MAAA;MAAA,IAAAC,MAAA;MACA,KAAAR,KAAA,CAAAjC,IAAA,CAAAkC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAM,MAAA,CAAA1C,OAAA;UACA,IAAA2C,WAAA,GAMAD,MAAA,CAAAzC,IAAA;YALAG,UAAA,GAAAuC,WAAA,CAAAvC,UAAA;YACAE,QAAA,GAAAqC,WAAA,CAAArC,QAAA;YACAC,eAAA,GAAAoC,WAAA,CAAApC,eAAA;YACAC,YAAA,GAAAmC,WAAA,CAAAnC,YAAA;YACAoC,IAAA,OAAAC,yBAAA,CAAAC,OAAA,EAAAH,WAAA,EAAAlD,SAAA;UAEA,IAAAG,IAAA,OAAAmD,cAAA,CAAAD,OAAA,MAAAC,cAAA,CAAAD,OAAA,MACAF,IAAA;YACA9B,WAAA,EAAA2B;UAAA,EACA;UACA,IAAArC,UAAA,CAAA4C,MAAA;YACApD,IAAA,iBAAAQ,UAAA,CAAA6C,IAAA;UACA;UACA,IAAA3C,QAAA,CAAA0C,MAAA;YACApD,IAAA,eAAAU,QAAA,CAAA2C,IAAA;UACA;UACA,IAAA1C,eAAA,CAAAyC,MAAA;YACApD,IAAA,sBAAAW,eAAA,CAAA0C,IAAA;UACA;UACA,IAAAzC,YAAA,CAAAwC,MAAA;YACApD,IAAA,mBAAAsD,IAAA,CAAAC,SAAA,CAAA3C,YAAA;UACA;UAEA,IAAA4C,cAAA,EAAAxD,IAAA,EACAyD,IAAA,WAAAC,GAAA;YACA,IAAAC,IAAA,GAAAD,GAAA,CAAAC,IAAA;cAAAC,GAAA,GAAAF,GAAA,CAAAE,GAAA;YACA,IAAAD,IAAA;cACA7B,cAAA,CAAAC,KAAA,CAAA8B,MAAA;cACAf,MAAA,CAAAJ,QAAA,CAAAC,OAAA;cACAG,MAAA,CAAAZ,OAAA,CAAAC,IAAA;YACA;cACAW,MAAA,CAAAJ,QAAA,CAAAoB,KAAA,CAAAF,GAAA;YACA;UACA,GACAG,OAAA;YAAA,OAAAjB,MAAA,CAAA1C,OAAA;UAAA;QACA;MACA;IACA;IACA4D,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAAxD,OAAA,QAAAJ,IAAA,CAAAI,OAAA;MACA,IAAAA,OAAA;QACA,IAAAyD,gBAAA,EAAAzD,OAAA,EAAAgD,IAAA,WAAAC,GAAA;UACA,IAAAC,IAAA,GAAAD,GAAA,CAAAC,IAAA;YAAA3D,IAAA,GAAA0D,GAAA,CAAA1D,IAAA;YAAA4D,GAAA,GAAAF,GAAA,CAAAE,GAAA;UACA,IAAAD,IAAA;YACAM,MAAA,CAAA5D,IAAA,CAAAK,QAAA,GAAAV,IAAA;UACA;YACAiE,MAAA,CAAAvB,QAAA,CAAAoB,KAAA,CAAAF,GAAA;UACA;QACA;MACA;QACA,KAAAlB,QAAA,CAAAyB,OAAA;MACA;IACA;IAEAC,WAAA,WAAAA,YAAAC,GAAA;MACAC,OAAA,CAAAC,GAAA,QAAAF,GAAA;MACA,KAAAhE,IAAA,CAAAK,QAAA,QAAAL,IAAA,CAAAK,QAAA,CAAA8D,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,KAAAJ,GAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}