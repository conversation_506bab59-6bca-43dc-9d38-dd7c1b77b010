<template>
  <!-- 典型案例 -->
  <div class="app-container">
    <!-- <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="案例类型" prop="solutionTypeId">
        <el-input
          v-model="queryParams.solutionTypeId"
          placeholder="请输入案例类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="名称" prop="classicCaseName">
        <el-input
          v-model="queryParams.classicCaseName"
          placeholder="请输入名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="企业名称" prop="classicCaseCompany">
        <el-input
          v-model="queryParams.classicCaseCompany"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form> -->

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:ClassicCase:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:ClassicCase:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:ClassicCase:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:ClassicCase:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="ClassicCaseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编码" align="center" prop="classicCaseId" />
      <el-table-column label="方案名称" align="center" prop="classicCaseName" />
      <el-table-column label="企业名称" align="center" prop="classicCaseCompany" />
      <!-- <el-table-column label="案例类型" align="center" prop="solutionTypeId" /> -->
      <!-- <el-table-column label="简介" align="center" prop="classicCaseIntroduction" />
      <el-table-column label="项目背景" align="center" prop="classicCaseBackground" />
      <el-table-column label="实施内容" align="center" prop="classicCaseContent" />
      <el-table-column label="项目效果" align="center" prop="classicCaseEffects" />
      <el-table-column label="备注" align="center" prop="remark" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:ClassicCase:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:ClassicCase:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改典型案例对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="案例类型" prop="solutionArr">
          <!-- <el-input v-model="form.solutionTypeId" placeholder="请输入案例类型" /> -->
          <el-cascader
          clearable
          v-model="solutionArr"
          :options="solutionTypeList"
          :props="propsCascader"
          @change="handleChange">
        </el-cascader>
        </el-form-item>
        <el-form-item label="案例名称" prop="classicCaseName">
          <el-input v-model="form.classicCaseName" placeholder="请输入案例名称" />
        </el-form-item>
        <el-form-item label="企业名称" prop="classicCaseCompany">
          <el-input v-model="form.classicCaseCompany" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="企业简介" prop="classicCaseIntroduction">
          <el-input v-model="form.classicCaseIntroduction" type="textarea" placeholder="请输入企业简介" />
        </el-form-item>
        <el-form-item label="项目背景" prop="classicCaseBackground">
          <el-input v-model="form.classicCaseBackground" type="textarea" placeholder="请输入项目背景" />
        </el-form-item>
        <el-form-item label="实施内容" prop="classicCaseContent">
          <editor v-model="form.classicCaseContent" :min-height="192"/>
        </el-form-item>
        <el-form-item label="项目效果" prop="classicCaseEffects">
          <el-input v-model="form.classicCaseEffects" type="textarea" placeholder="请输入项目效果" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listClassicCase, getClassicCase, delClassicCase, addClassicCase, updateClassicCase,listSolutionType } from "@/api/portalconsole/ClassicCase";

export default {
  name: "ClassicCase",
  data() {
    let validType = (rule, value, callback) => {
	    	// 直接用value 获取不到选中的值
	    	// 所以直接 用HTML中 v-model 绑定的值来判断 是否有值
	      if (this.solutionArr.length == 0) {
	        callback(new Error('请选择案例类型'))
	      } else {
	        callback()
	      }
	    }
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 典型案例表格数据
      ClassicCaseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        solutionTypeId: null,
        classicCaseName: null,
        classicCaseCompany: null,
        classicCaseIntroduction: null,
        classicCaseBackground: null,
        classicCaseContent: null,
        classicCaseEffects: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        classicCaseName: [
          { required: true, message: "案例名称不能为空", trigger: "blur" }
        ],
        classicCaseCompany:[
          { required: true, message: "企业名称不能为空", trigger: "blur" }
        ],
        classicCaseIntroduction:[
          { required: true, message: "企业简介不能为空", trigger: "blur" }
        ],
        classicCaseBackground:[
          { required: true, message: "项目背景不能为空", trigger: "blur" }
        ],
        classicCaseContent:[
          { required: true, message: "实施内容不能为空", trigger: "blur" }
        ],
        classicCaseEffects:[
          { required: true, message: "项目效果不能为空", trigger: "blur" }
        ],
        // solutionTypeId:[
        // { required: true, message: "案例类型不能为空", trigger: "change" }
        // ]
        solutionArr:[
          { required: true,  validator: validType, tirgger: 'blur' },
          { type: 'array', message: '案例类型不能为空' }
        // { required: true, message: "案例类型不能为空", trigger: "blur" }
        ],
      },
      //案例类型
      solutionArr:[],
      solutionTypeList:[],
      propsCascader:{ value: 'value',label: 'label',children: 'children'}
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询典型案例列表 */
    getList() {
      this.loading = true;
      listClassicCase(this.queryParams).then(response => {
        this.ClassicCaseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        classicCaseId: null,
        solutionTypeId: null,
        classicCaseName: null,
        classicCaseCompany: null,
        classicCaseIntroduction: null,
        classicCaseBackground: null,
        classicCaseContent: null,
        classicCaseEffects: null,
        delFlag: null,
      };
      this.solutionArr=[]
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.classicCaseId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.getListSolution()
      this.open = true;
      this.title = "添加典型案例";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const classicCaseId = row.classicCaseId || this.ids
      getClassicCase(classicCaseId).then(response => {
        this.getListSolution()
        this.form = response.data;
        
        setTimeout(() => {
        // 在这里执行你想要延迟执行的函数或代码
        if (response.data.solutionTypeId) {
            this.solutionArr=this.getParentsById(this.solutionTypeList,response.data.solutionTypeId)
            console.log("pid",this.solutionType)
          }
          this.open = true;
          this.title = "修改典型案例";
      }, 1000); // 设置延迟时间，这里是1秒（1000毫秒）
      });
    },
    /** 提交按钮 */
    submitForm() {
      console.log("this",this.form)
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(this.solutionArr != null && this.solutionArr.length > 0) {
            this.form.solutionTypeId = this.solutionArr.slice(-1)[0]
          }
          
          if (this.form.classicCaseId != null) {
            updateClassicCase(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addClassicCase(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const classicCaseIds = row.classicCaseId || this.ids;
      this.$modal.confirm('是否确认删除典型案例编号为"' + classicCaseIds + '"的数据项？').then(function() {
        return delClassicCase(classicCaseIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/ClassicCase/export', {
        ...this.queryParams
      }, `ClassicCase_${new Date().getTime()}.xlsx`)
    },
    //案例类型
     //查询资讯行业数据
     getListSolution() {
        let that = this
        let queryParams={
          pageNum: 1,
          pageSize: 50,
        }
        listSolutionType(queryParams).then(response => {
          let arrList=response.rows
          let data= []
          arrList.forEach(item => {
            data.push({
              value:item.solutionTypeId,
              parentId:item.parentId,
              label:item.solutionTypeName
            })
          });
          console.log('data',data)
          that.solutionTypeList=that.tranListToTreeData(data,null)
          console.log("that.solutionTypeList",that.solutionTypeList)
        });
      },
      //把list整理成树形
      tranListToTreeData(list, parentId=null) {
        const arr = [];
        list.forEach((item) => {
          if (item.parentId === parentId) {
            // 递归调用
            const children =this.tranListToTreeData(list, item.value)
            if (children.length) {
              item.children = children;
            }
            arr.push(item);
          }
        });
        console.log("arr",arr)
        return arr;
      },
      handleChange(value) {
        console.log(value);
        //this.$set(this.form,'solutionTypeId',value.join(','))
      },
      /*
    * el-cascader递归获取父级id
    * @param  list 数据列表
    * @param  id 后端返回的id
    * propsCascader 是el-cascader props属性
    **/
    getParentsById(list, id) {
      for (let i in list) {
        if (list[i][this.propsCascader.value || 'value'] == id) {
          return [list[i][this.propsCascader.value || 'value']]
        }
        if (list[i].children) {
          let node = this.getParentsById(list[i].children, id)
          if (node !== undefined) {
            // 追加父节点
            node.unshift(list[i][this.propsCascader.value || 'value'])
            return node
          }
        }
      }
    },
    
  }
};
</script>
