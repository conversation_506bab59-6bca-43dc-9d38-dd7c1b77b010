{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\thinkTank.vue?vue&type=template&id=455164bb&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\thinkTank.vue", "mtime": 1750311962937}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}