{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Navbar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Navbar.vue", "mtime": 1750311962846}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Navbar.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- <div>您好，欢迎来到复合材料共享智造工业互联网平台!</div> -->\r\n    <div class=\"navbar\">\r\n      <div class=\"left-menu\" @click=\"goIndex\">\r\n        <div>\r\n          <img :src=\"logoImg\" class=\"navbar-logo\" />\r\n        </div>\r\n        <div class=\"platTitle\">易复材共享智造<br />工业互联网平台</div>\r\n      </div>\r\n      <div\r\n        class=\"navbar-body\"\r\n        style=\"display: flex; justify-content: space-around\"\r\n      >\r\n        <top-nav id=\"topmenu-container\" class=\"topmenu-container\" />\r\n        <!-- <div style=\"display: flex; text-align: right\">\r\n        <div class=\"chengyang\">\r\n          <div style=\"margin-top: 10px\">\r\n            <img\r\n              style=\"width: 50px; height: 50px\"\r\n              src=\"../../assets/images/chengyang.jpg\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div style=\"font-size: 12px; color: #ccc\">扫码进入小程序</div>\r\n          <div class=\"chengyangBlock\">\r\n            <img src=\"../../assets/images/chengyang.jpg\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"yunduanyanfa\">\r\n          <div style=\"margin-top: 10px\">\r\n            <img\r\n              style=\"width: 50px; height: 50px\"\r\n              src=\"../../assets/images/yunduanyanfa.jpg\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div style=\"font-size: 12px; color: #ccc\">扫码进入企业端</div>\r\n          <div class=\"yunduanyanfaBlock\">\r\n            <img src=\"../../assets/images/yunduanyanfa.jpg\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n      </div>\r\n      <div class=\"right-menu\">\r\n        <!-- <div class=\"suppot\" @click=\"ifale = !ifale\" v-if=\"token\">技术支持</div> -->\r\n        <!-- v-if=\"token\" -->\r\n        <template v-if=\"token\">\r\n          <el-dropdown\r\n            class=\"avatar-container right-menu-item hover-effect\"\r\n            trigger=\"click\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <span class=\"name\">{{ name }}</span>\r\n              <el-image\r\n                class=\"userAvatar\"\r\n                icon=\"el-icon-user-solid\"\r\n                :size=\"36\"\r\n                :src=\"avatar ? avatar : require('@/assets/images/avatar.png')\"\r\n              ></el-image>\r\n              <i class=\"el-icon-arrow-down\" />\r\n            </div>\r\n\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <router-link to=\"/user/profile\">\r\n                <el-dropdown-item>个人中心</el-dropdown-item>\r\n              </router-link>\r\n              <el-dropdown-item divided @click.native=\"logout\">\r\n                <span>退出登录</span>\r\n              </el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n          <!-- <div class=\"cont\" v-show=\"ifale\">\r\n          <p class=\"sum\">13843272866</p>\r\n          <p class=\"sum\">17685863516</p>\r\n          <p class=\"question\">技术问题请拨打</p>\r\n        </div> -->\r\n        </template>\r\n\r\n        <template v-else>\r\n          <div class=\"login-container\">\r\n            <el-button size=\"small\" type=\"primary\" @click=\"login()\" class=\"dl\"\r\n              >登录</el-button\r\n            >\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from \"vuex\";\r\nimport logoImg from \"@/assets/images/home/<USER>\";\r\nimport Breadcrumb from \"@/components/Breadcrumb\";\r\nimport TopNav from \"@/components/TopNav\";\r\nimport Hamburger from \"@/components/Hamburger\";\r\nimport Screenfull from \"@/components/Screenfull\";\r\nimport SizeSelect from \"@/components/SizeSelect\";\r\nimport Search from \"@/components/HeaderSearch\";\r\nimport RuoYiGit from \"@/components/RuoYi/Git\";\r\nimport RuoYiDoc from \"@/components/RuoYi/Doc\";\r\nimport { getTicket, removeTicket } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  components: {\r\n    Breadcrumb,\r\n    TopNav,\r\n    Hamburger,\r\n    Screenfull,\r\n    SizeSelect,\r\n    Search,\r\n    RuoYiGit,\r\n    RuoYiDoc,\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\", \"sidebar\", \"avatar\", \"name\", \"device\"]),\r\n    setting: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings;\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch(\"settings/changeSetting\", {\r\n          key: \"showSettings\",\r\n          value: val,\r\n        });\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      logoImg,\r\n      mobile: \"\",\r\n      key: \"QmRlODJTVGhkNg==\",\r\n      type: \"cG9saWN5Y2FzaA==\",\r\n      url: \"\",\r\n      text: {},\r\n      wwk: {},\r\n      // visible: false,\r\n      ifale: false,\r\n      base64EncodeChars:\r\n        \"ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n    if (userinfo) {\r\n      this.$store.commit(\"SET_NAME\", userinfo.memberRealName);\r\n      this.$store.commit(\"SET_AVATAR\", userinfo.avatar);\r\n    }\r\n  },\r\n  methods: {\r\n    login() {\r\n      this.$router.push(\"/login\");\r\n    },\r\n    add() {\r\n      if (JSON.parse(localStorage.getItem(\"sessionObj\"))) {\r\n        this.text = JSON.parse(localStorage.getItem(\"sessionObj\"));\r\n        this.wwk = JSON.parse(this.text.data);\r\n        this.mobile = this.wwk.username;\r\n        this.mobile = this.$Base64.encode(this.mobile);\r\n        window.open(\r\n          `https://cyqyfw.com/index/user/login?key=${this.key}&type=${this.type}&mobile=${this.mobile}`\r\n        );\r\n      } else {\r\n        // window.open(\"https://120.221.94.235\");\r\n        window.open(\"https://cyqyfw.com \");\r\n      }\r\n    },\r\n    toggleSideBar() {\r\n      this.$store.dispatch(\"app/toggleSideBar\");\r\n    },\r\n    async logout() {\r\n      this.$confirm(\"确定注销并退出系统吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            if (getTicket()) {\r\n              window.location.href =\r\n                \"https://qyzhfw.chengyang.gov.cn/sso/logout?redirectUrl=https://qyfw.chengyang.gov.cn/index\";\r\n              removeTicket();\r\n            } else {\r\n              location.href = \"/index\";\r\n              localStorage.removeItem(\"sessionObj\");\r\n            }\r\n          });\r\n          sessionStorage.removeItem(\"userinfo\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    goIndex() {\r\n      this.$router.push({\r\n        path: \"/index\",\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cont {\r\n  width: 180px;\r\n  height: 140px;\r\n  text-align: center;\r\n  background-color: #fff;\r\n  border: 1px solid #bbb;\r\n  position: fixed;\r\n  top: 60px;\r\n  right: 80px;\r\n\r\n  .sum {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .question {\r\n    color: rgba(0, 21, 41, 0.678);\r\n  }\r\n}\r\n\r\n.navbar {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  height: 80px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: #fff;\r\n  box-shadow: 0 1px 4px 0 rgba(0, 21, 41, 0.12);\r\n  background: rgb(197, 241, 236);\r\n  background: linear-gradient(\r\n    180deg,\r\n    rgba(197, 241, 236, 1) 34%,\r\n    rgba(245, 255, 254, 1) 99%\r\n  );\r\n  &-body {\r\n    min-width: 1030px;\r\n    // min-width: 1200px;\r\n    // min-width: 66.6%;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n\r\n    .wgdx {\r\n      position: fixed;\r\n      right: 46.5%;\r\n      top: 27.5px;\r\n    }\r\n\r\n    .wgdx:hover {\r\n      color: #c52622;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .errLog-container {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .left-menu {\r\n    display: flex;\r\n    justify-content: center;\r\n    // width: 200px;\r\n    align-items: center;\r\n    margin-left: 20px;\r\n    cursor: pointer;\r\n\r\n    .platTitle {\r\n      width: 128px;\r\n      height: 36px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      font-size: 16px;\r\n      color: #111111;\r\n      line-height: 20px;\r\n      margin-left: 10px;\r\n    }\r\n\r\n    .navbar-logo {\r\n      width: 63px;\r\n      height: 40px;\r\n      object-fit: contain;\r\n    }\r\n  }\r\n\r\n  .right-menu {\r\n    width: 220px;\r\n    height: 100%;\r\n    padding-right: 32px;\r\n    display: flex;\r\n    justify-content: right;\r\n\r\n    .suppot {\r\n      width: 80px;\r\n      height: 40px;\r\n      margin-top: 28px;\r\n      color: #21c9b8;\r\n      border: none;\r\n      background-color: #fff;\r\n    }\r\n\r\n    .suppot:hover {\r\n      cursor: pointer;\r\n    }\r\n\r\n    // .wgdx {\r\n    //   position: fixed;\r\n    //   right: 46.5%;\r\n    //   top: 27.5px;\r\n    // }\r\n    // .wgdx:hover {\r\n    //   color: #c52622;\r\n    //   cursor: pointer;\r\n    // }\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n\r\n    .right-menu-item {\r\n      display: inline-block;\r\n      padding: 0 8px;\r\n      height: 100%;\r\n      font-size: 18px;\r\n      color: #5a5e66;\r\n      vertical-align: text-bottom;\r\n\r\n      &.hover-effect {\r\n        cursor: pointer;\r\n        transition: background 0.3s;\r\n\r\n        &:hover {\r\n          background: rgba(0, 0, 0, 0.025);\r\n        }\r\n      }\r\n    }\r\n\r\n    .avatar-container {\r\n      display: flex;\r\n      align-items: center;\r\n      flex-direction: row;\r\n      position: relative;\r\n\r\n      .avatar-wrapper {\r\n        display: flex;\r\n        align-items: center;\r\n        flex-direction: row;\r\n\r\n        .userAvatar {\r\n          width: 36px;\r\n          height: 36px;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        .name {\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 14px;\r\n          margin-right: 12px;\r\n        }\r\n\r\n        .el-icon-arrow-down {\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          font-weight: bold;\r\n          margin-left: 8px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .login-container {\r\n      .technology {\r\n        color: #21c9b8;\r\n        border: none;\r\n        background-color: #fff;\r\n        width: 120px;\r\n      }\r\n\r\n      .technology:hover {\r\n        cursor: pointer;\r\n      }\r\n\r\n      display: flex;\r\n      // width: 240px;\r\n      height: 80px;\r\n      line-height: 80px;\r\n      align-items: center;\r\n      position: relative;\r\n    }\r\n  }\r\n}\r\n\r\n.chengyang {\r\n  width: 100px;\r\n  height: 83px;\r\n  text-align: center;\r\n}\r\n\r\n.yunduanyanfa {\r\n  width: 100px;\r\n  height: 83px;\r\n  text-align: center;\r\n  margin-left: 20px;\r\n}\r\n\r\n.chengyangBlock {\r\n  position: fixed;\r\n  top: 80px;\r\n  right: calc((100% - 1200px) / 2);\r\n  margin-right: 5%;\r\n  display: none;\r\n}\r\n\r\n.chengyang:hover {\r\n  .chengyangBlock {\r\n    display: block;\r\n  }\r\n}\r\n\r\n.yunduanyanfaBlock {\r\n  position: fixed;\r\n  top: 80px;\r\n  right: calc((100% - 1200px) / 2);\r\n  margin-right: 1%;\r\n  display: none;\r\n}\r\n\r\n.yunduanyanfa:hover {\r\n  .yunduanyanfaBlock {\r\n    display: block;\r\n  }\r\n}\r\n</style>\r\n"]}]}