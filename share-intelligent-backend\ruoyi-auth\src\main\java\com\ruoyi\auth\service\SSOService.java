package com.ruoyi.auth.service;

import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * SSO认证服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SSOService {

    private static final Logger log = LoggerFactory.getLogger(SSOService.class);

    @Autowired
    private RedisService redisService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RestTemplate restTemplate;

    // SSO Token前缀
    private static final String SSO_TOKEN_PREFIX = "sso_token:";
    
    // SSO Token有效期（分钟）
    private static final int SSO_TOKEN_EXPIRE = 30;

    // 系统配置
    @Value("${sso.systems.market.url:http://localhost:8081}")
    private String marketSystemUrl;

    @Value("${sso.systems.market.callback:/sso/login}")
    private String marketCallbackPath;

    /**
     * 生成SSO Token
     * 
     * @param loginUser 登录用户
     * @param targetSystem 目标系统
     * @return SSO Token
     */
    public String generateSSOToken(LoginUser loginUser, String targetSystem) {
        // 生成唯一Token
        String token = IdUtils.fastSimpleUUID();
        
        // 构造Token数据
        Map<String, Object> tokenData = new HashMap<>();
        tokenData.put("userId", loginUser.getUserid());
        tokenData.put("username", loginUser.getUsername());
        tokenData.put("targetSystem", targetSystem);
        tokenData.put("createTime", System.currentTimeMillis());
        
        // 添加用户基本信息
        SysUser user = loginUser.getUser();
        if (user != null) {
            tokenData.put("nickName", user.getNickName());
            tokenData.put("email", user.getEmail());
            tokenData.put("phonenumber", user.getPhonenumber());
            tokenData.put("sex", user.getSex());
            tokenData.put("avatar", user.getAvatar());
            tokenData.put("deptId", user.getDeptId());
            if (user.getDept() != null) {
                tokenData.put("deptName", user.getDept().getDeptName());
            }
        }
        
        // 存储到Redis，设置过期时间
        String redisKey = SSO_TOKEN_PREFIX + token;
        redisService.setCacheObject(redisKey, tokenData, SSO_TOKEN_EXPIRE, TimeUnit.MINUTES);
        
        log.info("生成SSO Token: {} for user: {} target: {}", token, loginUser.getUsername(), targetSystem);
        
        return token;
    }

    /**
     * 验证SSO Token
     * 
     * @param token SSO Token
     * @param system 系统标识
     * @return 用户信息
     */
    public Map<String, Object> verifySSOToken(String token, String system) {
        if (StringUtils.isEmpty(token)) {
            return null;
        }
        
        String redisKey = SSO_TOKEN_PREFIX + token;
        Map<String, Object> tokenData = redisService.getCacheObject(redisKey);
        
        if (tokenData == null) {
            log.warn("SSO Token验证失败，Token不存在或已过期: {}", token);
            return null;
        }
        
        // 验证目标系统
        String targetSystem = (String) tokenData.get("targetSystem");
        if (!system.equals(targetSystem)) {
            log.warn("SSO Token验证失败，系统不匹配: {} != {}", system, targetSystem);
            return null;
        }
        
        // Token验证成功后删除（一次性使用）
        redisService.deleteObject(redisKey);
        
        log.info("SSO Token验证成功: {} for system: {}", token, system);
        
        return tokenData;
    }

    /**
     * 构造目标系统跳转URL
     * 
     * @param targetSystem 目标系统
     * @param ssoToken SSO Token
     * @param redirectUrl 重定向URL
     * @return 跳转URL
     */
    public String buildTargetUrl(String targetSystem, String ssoToken, String redirectUrl) {
        String baseUrl;
        String callbackPath;
        
        switch (targetSystem.toLowerCase()) {
            case "market":
                baseUrl = marketSystemUrl;
                callbackPath = marketCallbackPath;
                break;
            default:
                throw new IllegalArgumentException("不支持的目标系统: " + targetSystem);
        }
        
        StringBuilder url = new StringBuilder();
        url.append(baseUrl).append(callbackPath);
        url.append("?token=").append(ssoToken);
        url.append("&system=").append(targetSystem);
        
        if (StringUtils.isNotEmpty(redirectUrl)) {
            try {
                url.append("&redirect=").append(java.net.URLEncoder.encode(redirectUrl, "UTF-8"));
            } catch (Exception e) {
                log.error("URL编码失败", e);
            }
        }
        
        return url.toString();
    }

    /**
     * 获取用户详细信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    public Map<String, Object> getUserDetailInfo(Long userId) {
        SysUser user = remoteUserService.selectSysUserByUserId(userId);
        if (user == null) {
            return null;
        }
        
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", user.getUserId());
        userInfo.put("username", user.getUserName());
        userInfo.put("nickName", user.getNickName());
        userInfo.put("email", user.getEmail());
        userInfo.put("phonenumber", user.getPhonenumber());
        userInfo.put("sex", user.getSex());
        userInfo.put("avatar", user.getAvatar());
        userInfo.put("status", user.getStatus());
        userInfo.put("deptId", user.getDeptId());
        
        if (user.getDept() != null) {
            userInfo.put("deptName", user.getDept().getDeptName());
        }
        
        return userInfo;
    }

    /**
     * SSO登出
     * 
     * @param token Token
     * @param system 系统标识
     */
    public void ssoLogout(String token, String system) {
        // 清除相关的SSO Token
        String pattern = SSO_TOKEN_PREFIX + "*";
        // 这里可以实现更复杂的登出逻辑，比如通知所有相关系统
        log.info("SSO登出: system={}", system);
    }

    /**
     * 获取系统配置
     * 
     * @param system 系统标识
     * @return 系统配置
     */
    public Map<String, Object> getSystemConfig(String system) {
        Map<String, Object> config = new HashMap<>();
        
        switch (system.toLowerCase()) {
            case "market":
                config.put("systemName", "智能市场系统");
                config.put("systemUrl", marketSystemUrl);
                config.put("callbackPath", marketCallbackPath);
                break;
            default:
                config.put("systemName", "未知系统");
        }
        
        return config;
    }

    /**
     * 同步用户信息到从系统
     * 
     * @param userId 用户ID
     * @param targetSystem 目标系统
     * @return 同步结果
     */
    public boolean syncUserToSystem(Long userId, String targetSystem) {
        try {
            Map<String, Object> userInfo = getUserDetailInfo(userId);
            if (userInfo == null) {
                return false;
            }
            
            // 根据目标系统调用相应的同步接口
            String syncUrl = getSyncUrl(targetSystem);
            if (StringUtils.isEmpty(syncUrl)) {
                return false;
            }
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
            userInfo.forEach(params::add);
            
            HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(params, headers);
            ResponseEntity<Map> response = restTemplate.postForEntity(syncUrl, request, Map.class);
            
            return response.getStatusCode().is2xxSuccessful();
            
        } catch (Exception e) {
            log.error("同步用户信息到系统{}失败", targetSystem, e);
            return false;
        }
    }

    /**
     * 获取同步URL
     * 
     * @param targetSystem 目标系统
     * @return 同步URL
     */
    private String getSyncUrl(String targetSystem) {
        switch (targetSystem.toLowerCase()) {
            case "market":
                return marketSystemUrl + "/sso/sync-user";
            default:
                return null;
        }
    }
}
