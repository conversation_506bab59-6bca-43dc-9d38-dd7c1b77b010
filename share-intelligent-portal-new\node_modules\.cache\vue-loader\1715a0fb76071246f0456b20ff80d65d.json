{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyDemand\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyDemand\\index.vue", "mtime": 1750311963050}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0FA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/companyDemand", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:38:32\r\n * @LastEditTime: 2023-02-20 10:22:25\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-03 11:20:59\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"company-demand-record-page\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"top-row\">\r\n            <el-radio-group v-model=\"status\" @change=\"changeType\">\r\n              <el-radio-button label=\"\">全部</el-radio-button>\r\n              <el-radio-button label=\"1\">审核中</el-radio-button>\r\n              <el-radio-button label=\"2\">审核通过</el-radio-button>\r\n              <el-radio-button label=\"3\">审核驳回</el-radio-button>\r\n              <el-radio-button label=\"0\">草稿箱</el-radio-button>\r\n            </el-radio-group>\r\n            <el-button\r\n              class=\"button-add\"\r\n              icon=\"el-icon-edit-outline\r\n\"\r\n              @click=\"toCreate\"\r\n              >去发布</el-button\r\n            >\r\n          </div>\r\n          <div class=\"company-demand-pannel\">\r\n            <div class=\"none-class\" v-if=\"!records || records.length == 0\">\r\n              <el-image\r\n                style=\"width: 160px; height: 160px\"\r\n                :src=\"require('@/assets/user/none.png')\"\r\n                :fit=\"fit\"\r\n              ></el-image>\r\n              <div class=\"text\">暂无数据</div>\r\n            </div>\r\n            <div\r\n              class=\"company-demand-item\"\r\n              v-for=\"item in records\"\r\n              v-bind:key=\"item.id\"\r\n            >\r\n              <a class=\"left\" @click=\"goDetail(item.id)\">\r\n                <el-image\r\n                  style=\"width: 90px; height: 64px\"\r\n                  :src=\"getUrl(item.scenePicture)\"\r\n                  :fit=\"fit\"\r\n                ></el-image>\r\n                <div class=\"company-demand-title\">\r\n                  {{ item.demandTitle }}\r\n                </div>\r\n              </a>\r\n\r\n              <div\r\n                :class=\"[\r\n                  'company-demand-status',\r\n                  getStatusClass(item.auditStatus),\r\n                ]\"\r\n              >\r\n                {{ getStatusName(item.auditStatus) }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <el-pagination\r\n            v-show=\"total > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            :page-size=\"5\"\r\n            :current-page.sync=\"queryParams.pageNum\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :total=\"total\"\r\n          >\r\n          </el-pagination>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { getDemandList } from \"@/api/system/demand\";\r\nimport store from \"@/store\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  name: \"CompanyDemand\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      userId: store.getters.userId,\r\n      status: \"\",\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 5,\r\n      },\r\n      total: 1,\r\n      fit: \"cover\",\r\n      records: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    changeType() {\r\n      this.getList();\r\n    },\r\n    getUrl(str) {\r\n      var list = str ? JSON.parse(str) : [];\r\n      if (list && list.length > 0) {\r\n        return list[0].url;\r\n      }\r\n      return null;\r\n    },\r\n    getList() {\r\n      getDemandList({\r\n        ...this.queryParams,\r\n        auditStatus: this.status,\r\n        createById: this.userId,\r\n      }).then((response) => {\r\n        let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n        let decrypt = CryptoJS.AES.decrypt(response, key, {\r\n          mode: CryptoJS.mode.ECB,\r\n          padding: CryptoJS.pad.Pkcs7,\r\n        });\r\n        response = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n        this.records = response.rows;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    doRevocation() {\r\n      this.$confirm(\"是否确认撤回该提报？\", { type: \"error\" })\r\n        .then((_) => {\r\n          revocationPolicy({ ids: row.id }).then((response) => {\r\n            this.$message({\r\n              message: \"操作成功\",\r\n              type: \"success\",\r\n            });\r\n          });\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push(\"/user/companyDemandDetail?id=\" + id);\r\n    },\r\n    toCreate(id) {\r\n      this.$router.push(\"/user/companyDemandDetail?type=1\");\r\n    },\r\n    getStatusName(status) {\r\n      switch (status) {\r\n        case 1:\r\n          return \"审核中\";\r\n        case 2:\r\n          return \"审核通过\";\r\n        case 3:\r\n          return \"审核驳回\";\r\n\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    getStatusClass(status) {\r\n      switch (status) {\r\n        case 1:\r\n          return \" orange\";\r\n        case 2:\r\n          return \"green\";\r\n        case 3:\r\n          return \"red\";\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .company-demand-record-page {\r\n    .top-row {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      .button-add {\r\n        width: 100px;\r\n        height: 32px;\r\n        background: #21c9b8;\r\n        border-radius: 4px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n    .company-demand-pannel {\r\n      margin-top: 24px;\r\n      width: 100%;\r\n      height: 600px;\r\n      background: #fff;\r\n      .none-class {\r\n        text-align: center;\r\n        padding: 10% 0;\r\n        .text {\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #999999;\r\n          line-height: 14px;\r\n        }\r\n      }\r\n      .company-demand-item {\r\n        display: flex;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 112px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        .left {\r\n          width: 73%;\r\n          display: flex;\r\n          .company-demand-title {\r\n            margin-left: 24px;\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            width: 85%;\r\n            line-height: 30px;\r\n            overflow: hidden;\r\n            -webkit-line-clamp: 1;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n          }\r\n        }\r\n        .company-demand-status {\r\n          position: absolute;\r\n          right: 30px;\r\n          top: 40px;\r\n          padding: 2px 8px;\r\n          border-radius: 4px;\r\n          font-size: 14px;\r\n          font-size: 15px;\r\n          line-height: 30px;\r\n          text-align: center;\r\n          font-weight: 500;\r\n          overflow: hidden;\r\n          -webkit-line-clamp: 1;\r\n          text-overflow: ellipsis;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n        }\r\n        .orange {\r\n          background: rgba(246, 141, 57, 0.15);\r\n          color: #ff8b2e;\r\n        }\r\n        .green {\r\n          background: rgba(21, 188, 132, 0.15);\r\n          color: #15bc84;\r\n        }\r\n        .red {\r\n          background: rgba(255, 77, 77, 0.15);\r\n          color: #ff4d4d;\r\n        }\r\n      }\r\n    }\r\n    .el-radio-button {\r\n      margin-right: 30px;\r\n    }\r\n    .el-radio-button__inner {\r\n      width: 96px;\r\n      height: 32px;\r\n      background: transparent;\r\n      border-radius: 20px;\r\n      text-align: center;\r\n      color: #333333;\r\n      border: none;\r\n    }\r\n    .el-radio-button__orig-radio:checked + .el-radio-button__inner {\r\n      background: #21c9b8 !important;\r\n      color: #fff;\r\n      box-shadow: none;\r\n    }\r\n    .el-radio-button__inner:hover {\r\n      color: #333333;\r\n    }\r\n\r\n    .el-pagination {\r\n      width: 100%;\r\n      margin-top: 20px;\r\n      text-align: center;\r\n    }\r\n    .el-pagination.is-background .el-pager li {\r\n      background-color: #fff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled).active {\r\n      background-color: #21c9b8;\r\n      color: #ffffff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled):hover {\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}