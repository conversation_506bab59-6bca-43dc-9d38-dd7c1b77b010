<template>
  <!-- 企业信息 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="130px">
      <el-form-item label="公司名称" prop="companyName">
        <el-input
          v-model="queryParams.companyName"
          placeholder="请输入公司名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="公司邮箱" prop="companyEmail">
        <el-input
          v-model="queryParams.companyEmail"
          placeholder="请输入公司邮箱"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="授权书" prop="companyEmpower">
        <el-input
          v-model="queryParams.companyEmpower"
          placeholder="请输入授权书"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> 
      <el-form-item label="会员ID" prop="companyId">
        <el-input
          v-model="queryParams.companyId"
          placeholder="请输入会员ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item label="审核状态" prop="companyStatus">
        <el-select v-model="queryParams.companyStatus" placeholder="请选择审核状态" clearable>
          <el-option
            v-for="dict in dict.type.company_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="社会统一信用代码" prop="socialUnityCreditCode">
        <el-input
          v-model="queryParams.socialUnityCreditCode"
          placeholder="请输入社会统一信用代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="服务行业" prop="serviceIndustry">
        <el-input
          v-model="queryParams.serviceIndustry"
          placeholder="请输入服务行业"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="提交时间" prop="submitTime">
        <el-date-picker clearable
          v-model="queryParams.submitTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择提交时间">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="企业规模" prop="companySize">
        <el-input
          v-model="queryParams.companySize"
          placeholder="请输入企业规模"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系电话" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入联系电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入详细地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="注册资金" prop="registeredCapital">
        <el-input
          v-model="queryParams.registeredCapital"
          placeholder="请输入注册资金"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="创建者" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入创建者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker clearable
          v-model="queryParams.createTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="更新者" prop="updateBy">
        <el-input
          v-model="queryParams.updateBy"
          placeholder="请输入更新者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="更新时间" prop="updateTime">
        <el-date-picker clearable
          v-model="queryParams.updateTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择更新时间">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['portalconsole:Company:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['portalconsole:Company:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['portalconsole:Company:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['portalconsole:Company:export']"
        >导出</el-button>
      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          :disabled="multiple"
          @click="handleCheck"
          v-hasPermi="['portalconsole:Company:add']"
        >批量审核</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row> 

    <el-table v-loading="loading" :data="CompanyList" @selection-change="handleSelectionChange" style="width: 100%">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="编号" align="center" prop="companyId" />
      <el-table-column label="公司名称" align="center" prop="companyName" width="300px" />
      <el-table-column label="公司邮箱" align="center" prop="companyEmail" />
      <el-table-column label="授权书" align="center" prop="" >
        <template slot-scope="scope">
          <a style="color: #1890ff;" @click="downloadAllUrls(scope.row.companyEmpower)">{{ scope.row.companyEmpower }}</a> 
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center" prop="companyStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.company_status" :value="scope.row.companyStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="营业执照" align="center" prop="businessLicenseImageUrl" />
      <el-table-column label="社会统一信用代码" align="center" prop="socialUnityCreditCode" />
      <el-table-column label="服务行业" align="center" prop="serviceIndustry" />
      <el-table-column label="提交时间" align="center" prop="submitTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.submitTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="企业规模" align="center" prop="companySize" />
      <el-table-column label="联系电话" align="center" prop="phone" />
      <el-table-column label="详细地址" align="center" prop="address" />
      <el-table-column label="注册资金" align="center" prop="registeredCapital" />
      <el-table-column label="企业简介" align="center" prop="intrduction" />
      <el-table-column label="创建者" align="center" prop="createBy" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新者" align="center" prop="updateBy" />
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleDetail(scope.row)"
            v-hasPermi="['portalconsole:Company:edit']"
          >详情</el-button>
          <!-- <<el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:Company:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:Company:remove']"
          >删除</el-button>-->
        </template>
      </el-table-column> 
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改企业信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="公司名称" prop="companyName">
          <el-input v-model="form.companyName" placeholder="请输入公司名称" />
        </el-form-item>
        <el-form-item label="公司邮箱" prop="companyEmail">
          <el-input v-model="form.companyEmail" placeholder="请输入公司邮箱" />
        </el-form-item>
        <el-form-item label="授权书" prop="companyEmpower">
          <el-input v-model="form.companyEmpower" placeholder="请输入授权书" />
        </el-form-item>
        <el-form-item label="审核状态" prop="companyStatus">
          <el-select v-model="form.companyStatus" placeholder="请选择审核状态">
            <el-option
              v-for="dict in dict.type.company_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="营业执照" prop="businessLicenseImageUrl">
          <el-input v-model="form.businessLicenseImageUrl" placeholder="请输入营业执照" />
        </el-form-item>
        <el-form-item label="社会统一信用代码" prop="socialUnityCreditCode">
          <el-input v-model="form.socialUnityCreditCode" placeholder="请输入社会统一信用代码" />
        </el-form-item>
        <el-form-item label="服务行业" prop="serviceIndustry">
          <el-input v-model="form.serviceIndustry" placeholder="请输入服务行业" />
        </el-form-item>
        <el-form-item label="提交时间" prop="submitTime">
          <el-date-picker clearable
            v-model="form.submitTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择提交时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="企业规模" prop="companySize">
          <el-input v-model="form.companySize" placeholder="请输入企业规模" />
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.address" placeholder="请输入详细地址" />
        </el-form-item>
        <el-form-item label="注册资金" prop="registeredCapital">
          <el-input v-model="form.registeredCapital" placeholder="请输入注册资金" />
        </el-form-item>
        <el-form-item label="企业简介" prop="intrduction">
          <el-input v-model="form.intrduction" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <detailDialog ref="detailDialog" @submit="submit"></detailDialog>
    <!-- 批量审核弹窗 -->
    <el-dialog
        title="提示"
        :visible.sync="centerDialogVisible"
        width="30%"
        center>
        <span>批量审核选中的数据</span>
        <span slot="footer" class="dialog-footer">
          <el-button type="success" plain @click="edit('3')">认证通过</el-button>
          <el-button type="danger" plain @click="edit('2')">认证拒绝</el-button>
        </span>
    </el-dialog>
  </div>
</template>

<script>
import { listCompany, getCompany, delCompany, addCompany, updateCompany ,auditBatch} from "@/api/portalconsole/Company";
import detailDialog from "./components/detailDialog.vue";
export default {
  name: "Company",
  components:{
    detailDialog
  },
  dicts: ['company_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 企业信息表格数据
      CompanyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        companyId: null,
        companyName: null,
        companyEmail: null,
        companyEmpower: null,
        companyStatus: null,
        socialUnityCreditCode: null,
        serviceIndustry: null,
        submitTime: null,
        companySize: null,
        phone: null,
        address: null,
        registeredCapital: null,
        intrduction: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        companyName: [
          { required: true, message: "公司名称不能为空", trigger: "blur" }
        ],
        businessLicenseImageUrl: [
          { required: true, message: "营业执照不能为空", trigger: "blur" }
        ],
        socialUnityCreditCode: [
          { required: true, message: "社会统一信用代码不能为空", trigger: "blur" }
        ],
        phone: [
          { required: true, message: "联系电话不能为空", trigger: "blur" }
        ],
        address: [
          { required: true, message: "详细地址不能为空", trigger: "blur" }
        ],
        registeredCapital: [
          { required: true, message: "注册资金不能为空", trigger: "blur" }
        ],
      },
      centerDialogVisible:false
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询企业信息列表 */
    getList() {
      this.loading = true;
      listCompany(this.queryParams).then(response => {
        this.CompanyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        companyId: null,
        companyName: null,
        companyEmail: null,
        companyEmpower: null,
        companyStatus: null,
        businessLicenseImageUrl: null,
        socialUnityCreditCode: null,
        serviceIndustry: null,
        submitTime: null,
        companySize: null,
        phone: null,
        address: null,
        registeredCapital: null,
        intrduction: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.companyId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加企业信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const companyId = row.companyId || this.ids
      getCompany(companyId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改企业信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.companyId != null) {
            updateCompany(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCompany(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const companyIds = row.companyId || this.ids;
      this.$modal.confirm('是否确认删除企业信息编号为"' + companyIds + '"的数据项？').then(function() {
        return delCompany(companyIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('portalconsole/Company/export', {
        ...this.queryParams
      }, `Company_${new Date().getTime()}.xlsx`)
    },
    // 通过是2  不通过是3
    handleDetail(row){
      getCompany(row.companyId).then(response => {
        this.$refs.detailDialog.show(response.data)
      });
    },
    //审核完以后刷新列表
    submit(updata){
      if(updata){
        this.getList()
      }
    },
    async downloadAllUrls(url){
      const response = await fetch(url);
      const blob = await response.blob();
      const urlObject = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = urlObject;
      link.setAttribute('download', url.substring(url.lastIndexOf('/') + 1));
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    
    },
    //批量审核
    handleCheck(){
      if(this.ids.length>0){
        this.centerDialogVisible=true
      }else{
        this.$message.warning('请选择要审批的数据')
      }
      
    },
    edit(status){
      const data={
        companyIds:this.ids,
        companyStatus:status
      }
      auditBatch(data).then(res=>{
        this.$message.success('批量审核成功')
        this.centerDialogVisible=false
        this.getList()
      })
    }
  }
};
</script>
<style scoped>
/deep/.el-table .cell{
  height: 26px;
}
</style>