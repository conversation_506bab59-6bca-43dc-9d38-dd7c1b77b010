<template>
  <div>
    <!-- <div>您好，欢迎来到复合材料共享智造工业互联网平台!</div> -->
    <div class="navbar">
      <div class="left-menu" @click="goIndex">
        <div>
          <img :src="logoImg" class="navbar-logo" />
        </div>
        <div class="platTitle">易复材共享智造<br />工业互联网平台</div>
      </div>
      <div
        class="navbar-body"
        style="display: flex; justify-content: space-around"
      >
        <top-nav id="topmenu-container" class="topmenu-container" />
        <!-- <div style="display: flex; text-align: right">
        <div class="chengyang">
          <div style="margin-top: 10px">
            <img
              style="width: 50px; height: 50px"
              src="../../assets/images/chengyang.jpg"
              alt=""
            />
          </div>
          <div style="font-size: 12px; color: #ccc">扫码进入小程序</div>
          <div class="chengyangBlock">
            <img src="../../assets/images/chengyang.jpg" alt="" />
          </div>
        </div>
        <div class="yunduanyanfa">
          <div style="margin-top: 10px">
            <img
              style="width: 50px; height: 50px"
              src="../../assets/images/yunduanyanfa.jpg"
              alt=""
            />
          </div>
          <div style="font-size: 12px; color: #ccc">扫码进入企业端</div>
          <div class="yunduanyanfaBlock">
            <img src="../../assets/images/yunduanyanfa.jpg" alt="" />
          </div>
        </div>
      </div> -->
      </div>
      <div class="right-menu">
        <!-- <div class="suppot" @click="ifale = !ifale" v-if="token">技术支持</div> -->
        <!-- v-if="token" -->
        <template v-if="token">
          <el-dropdown
            class="avatar-container right-menu-item hover-effect"
            trigger="click"
          >
            <div class="avatar-wrapper">
              <span class="name">{{ name }}</span>
              <el-image
                class="userAvatar"
                icon="el-icon-user-solid"
                :size="36"
                :src="avatar ? avatar : require('@/assets/images/avatar.png')"
              ></el-image>
              <i class="el-icon-arrow-down" />
            </div>

            <el-dropdown-menu slot="dropdown">
              <router-link to="/user/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link>
              <el-dropdown-item divided @click.native="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <!-- <div class="cont" v-show="ifale">
          <p class="sum">13843272866</p>
          <p class="sum">17685863516</p>
          <p class="question">技术问题请拨打</p>
        </div> -->
        </template>

        <template v-else>
          <div class="login-container">
            <el-button size="small" type="primary" @click="login()" class="dl"
              >登录</el-button
            >
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import logoImg from "@/assets/images/home/<USER>";
import Breadcrumb from "@/components/Breadcrumb";
import TopNav from "@/components/TopNav";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import Search from "@/components/HeaderSearch";
import RuoYiGit from "@/components/RuoYi/Git";
import RuoYiDoc from "@/components/RuoYi/Doc";
import { getTicket, removeTicket } from "@/utils/auth";

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc,
  },
  computed: {
    ...mapGetters(["token", "sidebar", "avatar", "name", "device"]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "showSettings",
          value: val,
        });
      },
    },
  },
  data() {
    return {
      logoImg,
      mobile: "",
      key: "QmRlODJTVGhkNg==",
      type: "cG9saWN5Y2FzaA==",
      url: "",
      text: {},
      wwk: {},
      // visible: false,
      ifale: false,
      base64EncodeChars:
        "ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",
    };
  },
  created() {
    let userinfo = JSON.parse(sessionStorage.getItem("userinfo"));
    if (userinfo) {
      this.$store.commit("SET_NAME", userinfo.memberRealName);
      this.$store.commit("SET_AVATAR", userinfo.avatar);
    }
  },
  methods: {
    login() {
      this.$router.push("/login");
    },
    add() {
      if (JSON.parse(localStorage.getItem("sessionObj"))) {
        this.text = JSON.parse(localStorage.getItem("sessionObj"));
        this.wwk = JSON.parse(this.text.data);
        this.mobile = this.wwk.username;
        this.mobile = this.$Base64.encode(this.mobile);
        window.open(
          `https://cyqyfw.com/index/user/login?key=${this.key}&type=${this.type}&mobile=${this.mobile}`
        );
      } else {
        // window.open("https://120.221.94.235");
        window.open("https://cyqyfw.com ");
      }
    },
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    async logout() {
      this.$confirm("确定注销并退出系统吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$store.dispatch("LogOut").then(() => {
            if (getTicket()) {
              window.location.href =
                "https://qyzhfw.chengyang.gov.cn/sso/logout?redirectUrl=https://qyfw.chengyang.gov.cn/index";
              removeTicket();
            } else {
              location.href = "/index";
              localStorage.removeItem("sessionObj");
            }
          });
          sessionStorage.removeItem("userinfo");
        })
        .catch(() => {});
    },
    goIndex() {
      this.$router.push({
        path: "/index",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.cont {
  width: 180px;
  height: 140px;
  text-align: center;
  background-color: #fff;
  border: 1px solid #bbb;
  position: fixed;
  top: 60px;
  right: 80px;

  .sum {
    font-size: 20px;
  }

  .question {
    color: rgba(0, 21, 41, 0.678);
  }
}

.navbar {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 80px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px 0 rgba(0, 21, 41, 0.12);
  background: rgb(197, 241, 236);
  background: linear-gradient(
    180deg,
    rgba(197, 241, 236, 1) 34%,
    rgba(245, 255, 254, 1) 99%
  );
  &-body {
    min-width: 1030px;
    // min-width: 1200px;
    // min-width: 66.6%;
    margin-left: auto;
    margin-right: auto;

    .wgdx {
      position: fixed;
      right: 46.5%;
      top: 27.5px;
    }

    .wgdx:hover {
      color: #c52622;
      cursor: pointer;
    }
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .left-menu {
    display: flex;
    justify-content: center;
    // width: 200px;
    align-items: center;
    margin-left: 20px;
    cursor: pointer;

    .platTitle {
      width: 128px;
      height: 36px;
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 16px;
      color: #111111;
      line-height: 20px;
      margin-left: 10px;
    }

    .navbar-logo {
      width: 63px;
      height: 40px;
      object-fit: contain;
    }
  }

  .right-menu {
    width: 220px;
    height: 100%;
    padding-right: 32px;
    display: flex;
    justify-content: right;

    .suppot {
      width: 80px;
      height: 40px;
      margin-top: 28px;
      color: #21c9b8;
      border: none;
      background-color: #fff;
    }

    .suppot:hover {
      cursor: pointer;
    }

    // .wgdx {
    //   position: fixed;
    //   right: 46.5%;
    //   top: 27.5px;
    // }
    // .wgdx:hover {
    //   color: #c52622;
    //   cursor: pointer;
    // }
    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      display: flex;
      align-items: center;
      flex-direction: row;
      position: relative;

      .avatar-wrapper {
        display: flex;
        align-items: center;
        flex-direction: row;

        .userAvatar {
          width: 36px;
          height: 36px;
          border-radius: 50%;
        }

        .name {
          font-size: 14px;
          font-weight: 500;
          color: #333333;
          line-height: 14px;
          margin-right: 12px;
        }

        .el-icon-arrow-down {
          cursor: pointer;
          font-size: 14px;
          color: #999999;
          font-weight: bold;
          margin-left: 8px;
        }
      }
    }

    .login-container {
      .technology {
        color: #21c9b8;
        border: none;
        background-color: #fff;
        width: 120px;
      }

      .technology:hover {
        cursor: pointer;
      }

      display: flex;
      // width: 240px;
      height: 80px;
      line-height: 80px;
      align-items: center;
      position: relative;
    }
  }
}

.chengyang {
  width: 100px;
  height: 83px;
  text-align: center;
}

.yunduanyanfa {
  width: 100px;
  height: 83px;
  text-align: center;
  margin-left: 20px;
}

.chengyangBlock {
  position: fixed;
  top: 80px;
  right: calc((100% - 1200px) / 2);
  margin-right: 5%;
  display: none;
}

.chengyang:hover {
  .chengyangBlock {
    display: block;
  }
}

.yunduanyanfaBlock {
  position: fixed;
  top: 80px;
  right: calc((100% - 1200px) / 2);
  margin-right: 1%;
  display: none;
}

.yunduanyanfa:hover {
  .yunduanyanfaBlock {
    display: block;
  }
}
</style>
