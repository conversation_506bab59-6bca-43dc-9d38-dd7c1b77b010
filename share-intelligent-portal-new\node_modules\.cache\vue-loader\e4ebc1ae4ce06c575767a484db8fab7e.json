{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\profile\\userAvatar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\profile\\userAvatar.vue", "mtime": 1750311963074}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["userAvatar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4FA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "userAvatar.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\r\n  <div>\r\n    <div class=\"user-info-head\" @click=\"editCropper()\">\r\n      <img\r\n        v-bind:src=\"options.img\"\r\n        title=\"点击上传头像\"\r\n        class=\"img-circle img-lg\"\r\n      />\r\n    </div>\r\n    <el-dialog\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"800px\"\r\n      append-to-body\r\n      @opened=\"modalOpened\"\r\n      @close=\"closeDialog\"\r\n    >\r\n      <el-row>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{ height: '350px' }\">\r\n          <vue-cropper\r\n            ref=\"cropper\"\r\n            :img=\"options.img\"\r\n            :info=\"true\"\r\n            :autoCrop=\"options.autoCrop\"\r\n            :autoCropWidth=\"options.autoCropWidth\"\r\n            :autoCropHeight=\"options.autoCropHeight\"\r\n            :fixedBox=\"options.fixedBox\"\r\n            :outputType=\"options.outputType\"\r\n            @realTime=\"realTime\"\r\n            v-if=\"visible\"\r\n          />\r\n        </el-col>\r\n        <el-col :xs=\"24\" :md=\"12\" :style=\"{ height: '350px' }\">\r\n          <div class=\"avatar-upload-preview\">\r\n            <img :src=\"previews.url\" :style=\"previews.img\" />\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <br />\r\n      <el-row>\r\n        <el-col :lg=\"2\" :sm=\"3\" :xs=\"3\">\r\n          <el-upload\r\n            action=\"#\"\r\n            :http-request=\"requestUpload\"\r\n            :show-file-list=\"false\"\r\n            :before-upload=\"beforeUpload\"\r\n          >\r\n            <el-button size=\"small\">\r\n              选择\r\n              <i class=\"el-icon-upload el-icon--right\"></i>\r\n            </el-button>\r\n          </el-upload>\r\n        </el-col>\r\n        <el-col :lg=\"{ span: 1, offset: 2 }\" :sm=\"2\" :xs=\"2\">\r\n          <el-button\r\n            icon=\"el-icon-plus\"\r\n            size=\"small\"\r\n            @click=\"changeScale(1)\"\r\n          ></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{ span: 1, offset: 1 }\" :sm=\"2\" :xs=\"2\">\r\n          <el-button\r\n            icon=\"el-icon-minus\"\r\n            size=\"small\"\r\n            @click=\"changeScale(-1)\"\r\n          ></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{ span: 1, offset: 1 }\" :sm=\"2\" :xs=\"2\">\r\n          <el-button\r\n            icon=\"el-icon-refresh-left\"\r\n            size=\"small\"\r\n            @click=\"rotateLeft()\"\r\n          ></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{ span: 1, offset: 1 }\" :sm=\"2\" :xs=\"2\">\r\n          <el-button\r\n            icon=\"el-icon-refresh-right\"\r\n            size=\"small\"\r\n            @click=\"rotateRight()\"\r\n          ></el-button>\r\n        </el-col>\r\n        <el-col :lg=\"{ span: 2, offset: 6 }\" :sm=\"2\" :xs=\"2\">\r\n          <el-button type=\"primary\" size=\"small\" @click=\"uploadImg()\"\r\n            >提 交</el-button\r\n          >\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport store from \"@/store\";\r\nimport { VueCropper } from \"vue-cropper\";\r\nimport { uploadAvatar } from \"@/api/system/user\";\r\nimport { debounce } from \"@/utils\";\r\n\r\nexport default {\r\n  components: { VueCropper },\r\n  props: {\r\n    user: {\r\n      type: Object,\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示cropper\r\n      visible: false,\r\n      // 弹出层标题\r\n      title: \"修改头像\",\r\n      options: {\r\n        img: store.getters.avatar, //裁剪图片的地址\r\n        autoCrop: true, // 是否默认生成截图框\r\n        autoCropWidth: 200, // 默认生成截图框宽度\r\n        autoCropHeight: 200, // 默认生成截图框高度\r\n        fixedBox: true, // 固定截图框大小 不允许改变\r\n        outputType: \"png\", // 默认生成截图为PNG格式\r\n      },\r\n      previews: {},\r\n      resizeHandler: null,\r\n    };\r\n  },\r\n  methods: {\r\n    // 编辑头像\r\n    editCropper() {\r\n      this.open = true;\r\n    },\r\n    // 打开弹出层结束时的回调\r\n    modalOpened() {\r\n      this.visible = true;\r\n      if (!this.resizeHandler) {\r\n        this.resizeHandler = debounce(() => {\r\n          this.refresh();\r\n        }, 100);\r\n      }\r\n      window.addEventListener(\"resize\", this.resizeHandler);\r\n    },\r\n    // 刷新组件\r\n    refresh() {\r\n      this.$refs.cropper.refresh();\r\n    },\r\n    // 覆盖默认的上传行为\r\n    requestUpload() {},\r\n    // 向左旋转\r\n    rotateLeft() {\r\n      this.$refs.cropper.rotateLeft();\r\n    },\r\n    // 向右旋转\r\n    rotateRight() {\r\n      this.$refs.cropper.rotateRight();\r\n    },\r\n    // 图片缩放\r\n    changeScale(num) {\r\n      num = num || 1;\r\n      this.$refs.cropper.changeScale(num);\r\n    },\r\n    // 上传预处理\r\n    beforeUpload(file) {\r\n      if (file.type.indexOf(\"image/\") == -1) {\r\n        this.$modal.msgError(\r\n          \"文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。\"\r\n        );\r\n      } else {\r\n        const reader = new FileReader();\r\n        reader.readAsDataURL(file);\r\n        reader.onload = () => {\r\n          this.options.img = reader.result;\r\n        };\r\n      }\r\n    },\r\n    // 上传图片\r\n    uploadImg() {\r\n      this.$refs.cropper.getCropBlob((data) => {\r\n        let formData = new FormData();\r\n        formData.append(\"avatarfile\", data);\r\n        uploadAvatar(formData).then((response) => {\r\n          this.open = false;\r\n          this.options.img = response.imgUrl;\r\n          store.commit(\"SET_AVATAR\", this.options.img);\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n          this.visible = false;\r\n        });\r\n      });\r\n    },\r\n    // 实时预览\r\n    realTime(data) {\r\n      this.previews = data;\r\n    },\r\n    // 关闭窗口\r\n    closeDialog() {\r\n      this.options.img = store.getters.avatar;\r\n      this.visible = false;\r\n      window.removeEventListener(\"resize\", this.resizeHandler);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.user-info-head {\r\n  position: relative;\r\n  display: inline-block;\r\n  height: 120px;\r\n}\r\n\r\n.user-info-head:hover:after {\r\n  content: \"+\";\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  color: #eee;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  font-size: 24px;\r\n  font-style: normal;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  cursor: pointer;\r\n  line-height: 110px;\r\n  border-radius: 50%;\r\n}\r\n</style>\r\n"]}]}