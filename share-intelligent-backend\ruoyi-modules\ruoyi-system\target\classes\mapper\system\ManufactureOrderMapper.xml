<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.ManufactureOrderMapper">

    <resultMap type="ManufactureOrder" id="ManufactureOrderResult">
        <result property="id"    column="id"    />
        <result property="deadline"    column="deadline"    />
        <result property="status"    column="status"    />
        <result property="orderType"    column="order_type"    />
        <result property="price"    column="price"    />
        <result property="demandCompany"    column="demand_company"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="deliveryAddress"    column="delivery_address"    />
        <result property="fileRequirement"    column="file_requirement"    />
        <result property="bankName"    column="bank_name"    />
        <result property="paymentAccount"    column="payment_account"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="contactPerson"    column="contact_person"    />
    </resultMap>

    <sql id="selectManufactureOrderVo">
        select id, deadline, status, order_type, price, demand_company, contact_phone, delivery_address, file_requirement, bank_name, payment_account, audit_status, create_time, update_time, contact_person from manufacture_order
    </sql>

    <select id="selectManufactureOrderList" parameterType="ManufactureOrder" resultMap="ManufactureOrderResult">
        <include refid="selectManufactureOrderVo"/>
        <where>
            <if test="deadline != null "> and deadline = #{deadline}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="orderType != null  and orderType != ''"> and order_type = #{orderType}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="demandCompany != null  and demandCompany != ''"> and demand_company = #{demandCompany}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="deliveryAddress != null  and deliveryAddress != ''"> and delivery_address = #{deliveryAddress}</if>
            <if test="fileRequirement != null  and fileRequirement != ''"> and file_requirement = #{fileRequirement}</if>
            <if test="bankName != null  and bankName != ''"> and bank_name like concat('%', #{bankName}, '%')</if>
            <if test="paymentAccount != null  and paymentAccount != ''"> and payment_account = #{paymentAccount}</if>
            <if test="auditStatus != null  and auditStatus != ''"> and audit_status = #{auditStatus}</if>
        </where>
    </select>

    <select id="selectManufactureOrderById" parameterType="Long" resultMap="ManufactureOrderResult">
        <include refid="selectManufactureOrderVo"/>
        where id = #{id}
    </select>

    <insert id="insertManufactureOrder" parameterType="ManufactureOrder" useGeneratedKeys="true" keyProperty="id">
        insert into manufacture_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deadline != null">deadline,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="orderType != null and orderType != ''">order_type,</if>
            <if test="price != null">price,</if>
            <if test="demandCompany != null">demand_company,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="deliveryAddress != null">delivery_address,</if>
            <if test="fileRequirement != null">file_requirement,</if>
            <if test="bankName != null">bank_name,</if>
            <if test="paymentAccount != null">payment_account,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="contactPerson != null">contact_person,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deadline != null">#{deadline},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="orderType != null and orderType != ''">#{orderType},</if>
            <if test="price != null">#{price},</if>
            <if test="demandCompany != null">#{demandCompany},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="deliveryAddress != null">#{deliveryAddress},</if>
            <if test="fileRequirement != null">#{fileRequirement},</if>
            <if test="bankName != null">#{bankName},</if>
            <if test="paymentAccount != null">#{paymentAccount},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
        </trim>
    </insert>

    <update id="updateManufactureOrder" parameterType="ManufactureOrder">
        update manufacture_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="deadline != null">deadline = #{deadline},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="orderType != null and orderType != ''">order_type = #{orderType},</if>
            <if test="price != null">price = #{price},</if>
            <if test="demandCompany != null">demand_company = #{demandCompany},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="deliveryAddress != null">delivery_address = #{deliveryAddress},</if>
            <if test="fileRequirement != null">file_requirement = #{fileRequirement},</if>
            <if test="bankName != null">bank_name = #{bankName},</if>
            <if test="paymentAccount != null">payment_account = #{paymentAccount},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteManufactureOrderById" parameterType="Long">
        delete from manufacture_order where id = #{id}
    </delete>

    <delete id="deleteManufactureOrderByIds" parameterType="String">
        delete from manufacture_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
