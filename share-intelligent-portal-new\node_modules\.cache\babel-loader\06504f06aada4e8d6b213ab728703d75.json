{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\serviceAgency.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\serviceAgency.vue", "mtime": 1750311962936}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_purchaseSales", "require", "_cryptoJs", "_interopRequireDefault", "secret<PERSON>ey", "_default", "exports", "default", "data", "loading", "pageNum", "pageSize", "total", "created", "methods", "search", "_this", "getCompanyHomeList", "recommendStatus", "then", "res", "key", "CryptoJS", "enc", "Utf8", "parse", "decrypt", "AES", "mode", "ECB", "padding", "pad", "Pkcs7", "JSON", "stringify", "_ref", "rows", "catch", "goEnterpriseDetail", "item", "routeData", "$router", "resolve", "path", "query", "id", "businessNo", "window", "open", "href", "goEnterprise"], "sources": ["src/views/components/home/<USER>"], "sourcesContent": ["<template>\r\n  <div\r\n    class=\"serviceBg wow animate__animated animate__fadeInUp\"\r\n    data-wow-duration=\"1s\"\r\n  >\r\n    <div class=\"serviceImg\"></div>\r\n    <div class=\"card-container\">\r\n      <div class=\"enterpriseTitle\">\r\n        <div>服务机构</div>\r\n        <div class=\"allEnterprise\" @click=\"goEnterprise\">查看全部>></div>\r\n      </div>\r\n      <div class=\"content\">\r\n        <div\r\n          class=\"contentItem\"\r\n          v-for=\"(item, index) in data\"\r\n          :key=\"index\"\r\n          @click=\"goEnterpriseDetail(item)\"\r\n          :title=\"item.name\"\r\n        >\r\n          <div\r\n            v-if=\"item.companyPictureList && item.companyPictureList.length > 0\"\r\n          >\r\n            <img :src=\"item.companyPictureList[0].url\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCompanyHomeList } from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 15,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    // this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getCompanyHomeList({\r\n        recommendStatus: 1,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 跳转到企业名录详情页面\r\n    goEnterpriseDetail(item) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/enterpriseDetail\",\r\n        query: { id: item.id, businessNo: item.businessNo },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goEnterprise() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/enterpriseList\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.serviceBg {\r\n  width: 100%;\r\n  height: 340px;\r\n  position: relative;\r\n  .serviceImg {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    width: calc((100% - 1200px) / 2);\r\n    height: 500px;\r\n    background-image: url(\"../../../assets/images/home/<USER>\");\r\n    background-size: 100% 100%;\r\n  }\r\n}\r\n.enterpriseTitle {\r\n  width: 100%;\r\n  font-size: 36px;\r\n  text-align: center;\r\n  margin: 10px 0 60px 0;\r\n  position: relative;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  color: #000000;\r\n  .allEnterprise {\r\n    position: absolute;\r\n    top: 8 px;\r\n    right: 0;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    line-height: 26px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n  flex-wrap: wrap;\r\n  // height: 280px;\r\n  .contentItem {\r\n    width: 220px;\r\n    height: 90px;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    background: #ffffff;\r\n    box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);\r\n    border-radius: 4px;\r\n    img {\r\n      width: 100%;\r\n      height: 90px;\r\n    }\r\n  }\r\n  .contentItem:nth-child(n + 6) {\r\n    margin-top: 25px;\r\n  }\r\n  .contentItem:hover {\r\n    transform: translateY(-10px);\r\n    transition: 1s;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AA+BA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAG,SAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAD,IAAA;MACAE,OAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,KAAA;MACA,KAAAP,OAAA;MACA,IAAAQ,iCAAA;QACAC,eAAA;QACAR,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA;MACA,GACAQ,IAAA,WAAAC,GAAA;QACAJ,KAAA,CAAAP,OAAA;QACA,IAAAY,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAArB,SAAA;QACA,IAAAsB,OAAA,GAAAJ,iBAAA,CAAAK,GAAA,CAAAD,OAAA,CAAAN,GAAA,EAAAC,GAAA;UACAO,IAAA,EAAAN,iBAAA,CAAAM,IAAA,CAAAC,GAAA;UACAC,OAAA,EAAAR,iBAAA,CAAAS,GAAA,CAAAC;QACA;QACAZ,GAAA,GAAAa,IAAA,CAAAR,KAAA,CAAAH,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAU,SAAA,CAAAR,OAAA;QACA,IAAAS,IAAA,GAAAf,GAAA;UAAAgB,IAAA,GAAAD,IAAA,CAAAC,IAAA;UAAAxB,KAAA,GAAAuB,IAAA,CAAAvB,KAAA;QACAI,KAAA,CAAAR,IAAA,GAAA4B,IAAA;QACApB,KAAA,CAAAJ,KAAA,GAAAA,KAAA;MACA,GACAyB,KAAA;QACArB,KAAA,CAAAP,OAAA;MACA;IACA;IACA;IACA6B,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAC,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACAC,KAAA;UAAAC,EAAA,EAAAN,IAAA,CAAAM,EAAA;UAAAC,UAAA,EAAAP,IAAA,CAAAO;QAAA;MACA;MACAC,MAAA,CAAAC,IAAA,CAAAR,SAAA,CAAAS,IAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,IAAAV,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;MACA;MACAI,MAAA,CAAAC,IAAA,CAAAR,SAAA,CAAAS,IAAA;IACA;EACA;AACA", "ignoreList": []}]}