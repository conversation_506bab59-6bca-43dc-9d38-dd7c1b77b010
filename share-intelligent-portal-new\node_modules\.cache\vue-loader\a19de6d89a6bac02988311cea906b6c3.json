{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\TopNav\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\TopNav\\index.vue", "mtime": 1750311962827}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBmaW5kLCBwcm9wRXEsIHJlcGxhY2UsIGZpbHRlciwgbWFwIH0gZnJvbSAicmFtZGEiOw0KDQppbXBvcnQgeyBjb25zdGFudFJvdXRlcyB9IGZyb20gIkAvcm91dGVyIjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICBjb25zdCBtYWluTWVudSA9IGZpbmQocHJvcEVxKCJuYW1lIiwgIm1haW4iKSwgY29uc3RhbnRSb3V0ZXMpIHx8IHt9Ow0KICAgIGNvbnN0IG1lbnVzID0gZmlsdGVyKChpdGVtKSA9PiAhaXRlbS5oaWRkZW4sIG1haW5NZW51LmNoaWxkcmVuIHx8IFtdKTsNCiAgICBjb25zdCAkbWVudXMgPSBtYXAoKGl0ZW0pID0+IGl0ZW0ucGF0aCwgbWVudXMpOw0KICAgIGNvbnN0ICRwYXRoID0gcmVwbGFjZSgiLyIsICIiLCB0aGlzLiRyb3V0ZS5wYXRoKTsNCiAgICByZXR1cm4gew0KICAgICAgYWN0aXZlTWVudTogJG1lbnVzLmluY2x1ZGVzKCRwYXRoKSA/ICRwYXRoIDogImluZGV4IiwNCiAgICAgIG1lbnVzLA0KICAgICAgcGF0aHM6ICRtZW51cywNCiAgICAgIC8vIOmhtumDqOagj+WIneWni+aVsA0KICAgICAgdmlzaWJsZU51bWJlcjogNiwNCiAgICAgIC8vIOW9k+WJjea/gOa0u+iPnOWNleeahCBpbmRleA0KICAgICAgY3VycmVudEluZGV4OiB1bmRlZmluZWQsDQogICAgICBtb2JpbGU6ICIiLA0KICAgICAga2V5OiAiUW1SbE9ESlRWR2hrTmc9PSIsDQogICAgICB0eXBlOiAiY0c5eWRISmhhWFE9IiwNCiAgICAgIGJhc2U2NEVuY29kZUNoYXJzOg0KICAgICAgICAiQUJDREVGR0hJSktMTU5PUE9SU1RVV1hZWmFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6MDEyMzQ1Njc4OSsvIiwNCiAgICAgIHRleHQ6IHt9LA0KICAgICAgd3drOiB7fSwNCiAgICAgIGNhc2hUeXBlOiAiY0c5c2FXTjVZMkZ6YUE9PSIsDQogICAgICBzdXBwbHlEZW1hbmREb2NraW5nOiBbDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIumcgOaxguWkp+WOhSIsDQogICAgICAgICAgZGVzYzogIuS8geS4muWPkeW4g+mcgOaxgiIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIuS+m+e7meWkp+WOhSIsDQogICAgICAgICAgZGVzYzogIuS8geS4muWPkeW4g+S+m+e7mSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIuino+WGs+aWueahiCIsDQogICAgICAgICAgZGVzYzogIuaPkOS+m+ihjOS4mumXrumimOino+WGs+etlueVpeS4juahiOS+iyIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIuW6lOeUqOWVhuW6lyIsDQogICAgICAgICAgZGVzYzogIuaxh+iBmuW3peS4muW3peWFt+S4juW6lOeUqOeahOS4i+i9veW5s+WPsCIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogIuWkjeadkOWxleWOhSIsDQogICAgICAgICAgZGVzYzogIuWxleekuuWkjeWQiOadkOaWmeS6p+WTgeS4juaKgOacr+eahOW5s+WPsCIsDQogICAgICAgIH0sDQogICAgICBdLA0KICAgICAgbWFudWZhY3R1cmluZ1NoYXJpbmc6IFsNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAi6K6+5aSH5YWx5LqrIiwNCiAgICAgICAgICBkZXNjOiAi57q/5LiK5o+Q5L6b5qih5YW344CB5bel5Lia6K6+5aSH56ef6LWB77yM5LyB5Lia5Zyo57q/55Sz6K+3IiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAi6L2m6Ze05YWx5LqrIiwNCiAgICAgICAgICBkZXNjOiAi5o+Q5L6b6L2m6Ze056ef6LWB77yM5LyB5Lia5Zyo57q/55Sz6K+344CCIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAi6K6i5Y2V5YWx5LqrIiwNCiAgICAgICAgICBkZXNjOiAi5LyB5Lia5Y+R5biD55Sf5Lqn6K6i5Y2V5Y2P5ZCM6ZyA5rGC77yM5bel5Y6C5o6l5Y2V5ZON5bqU55Sf5Lqn5Lqk5LuY44CCIiwNCiAgICAgICAgfSwNCiAgICAgIF0sDQogICAgICBzZXJ2aWNlU2hhcmluZzogWw0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICLkurrmiY3mnI3liqEiLA0KICAgICAgICAgIGRlc2M6ICLkvIHkuJrmi5vogZjjgIHkuKrkurrnroDljobmiJbog73lipvkv6Hmga/vvIzkurrmiY3kvpvpnIDlr7nmjqXjgIIiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICLkvIHkuJrnlKjlt6UiLA0KICAgICAgICAgIGRlc2M6ICLlirPliqHnlKjlt6Xkv6Hmga/msYfpm4bvvIzmtbfph4/pq5jolqrogYzkvY3nrYnkvaDmnaXpgInjgIIiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICLmo4Dpqozmo4DmtYsiLA0KICAgICAgICAgIGRlc2M6ICLkuJPkuJrnrKzkuInmlrnmo4DmtYvmnLrmnoTmj5Dkvpvmo4Dpqozmo4DmtYvmnI3liqHvvIzmo4DmtYvpobnnm67kuIDplK7nlLPor7fjgIIiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICLlrp7pqozlrqTlhbHkuqsiLA0KICAgICAgICAgIGRlc2M6ICLlrp7pqozlrqTotYTmupDlhbHkuqvvvIzkvY7miJDmnKzlrp7njrDliJvmlrDjgIIiLA0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgaW5ub3ZhdGlvblNoYXJpbmc6IFsNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAi5Yib5Lia5a215YyWIiwNCiAgICAgICAgICBkZXNjOiAi5o+Q5L6b5Yib5Lia5Z+65Zyw57uZ5Yid5Yib5LyB5Lia77yM5LqG6Kej5Yqg5YWl5YWx5Lqr5Yib5paw5Zut5Yy644CCIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAi5paH5Lu25YWx5LqrIiwNCiAgICAgICAgICBkZXNjOiAi5LiT5Yip44CB5qCH5YeG44CB5ZWG5qCH562J55+l6K+G5bqT5L+h5oGv5byA5pS+44CCIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAi5LyX562556eR56CUIiwNCiAgICAgICAgICBkZXNjOiAi5rGH6IGa56eR56CU5LyX5pm65LyX56256LWE6YeR77yM5pCt5bu65oiQ5p6c5a215YyW44CB6LWE5rqQ5YWx5Lqr55qE5Yib5paw5Y2P5L2c5bmz5Y+wIiwNCiAgICAgICAgfSwNCiAgICAgIF0sDQogICAgICBhYm91dFVzTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICLlubPlj7Dku4vnu40iLA0KICAgICAgICAgIGRlc2M6ICLku6XigJzlhbHkuqvmmbrpgKDigJ3otYvog73nibnoibLkuqfkuJrpm4bnvqTvvIzlrp7njrDotYTmupDliKnnlKjnmoTmnIDlpKfljJbjgIIiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICLliqjmgIHotYTorq8iLA0KICAgICAgICAgIGRlc2M6ICLmj5DkvpvmnIDmlrDnmoTmlrDpl7votYTorq/jgIHorqnmgqjov4Xmjozmj6HkuqfkuJrliqjmgIHjgIIiLA0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICB9Ow0KICB9LA0KICB3YXRjaDogew0KICAgICRyb3V0ZShyb3V0ZSkgew0KICAgICAgY29uc3QgeyBwYXRoIH0gPSByb3V0ZTsNCiAgICAgIGNvbnN0ICRwYXRoID0gcmVwbGFjZSgiLyIsICIiLCBwYXRoKTsNCiAgICAgIGlmICh0aGlzLnBhdGhzLmluY2x1ZGVzKCRwYXRoKSkgew0KICAgICAgICBpZiAoJHBhdGggIT09IHRoaXMuYWN0aXZlTWVudSkgew0KICAgICAgICAgIHRoaXMuYWN0aXZlTWVudSA9ICRwYXRoOw0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICB0aGVtZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy50aGVtZTsNCiAgICB9LA0KICB9LA0KDQogIG1ldGhvZHM6IHsNCiAgICBnb1N1cHBseURlbWFuZERvY2tpbmcoaW5kZXgpIHsNCiAgICAgIGlmIChpbmRleCA9PSAyKSB7DQogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgICBwYXRoOiAiL3NvbHV0aW9uIiwNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSBpZiAoaW5kZXggPT0gMykgew0KICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgICAgcGF0aDogIi9hcHBTdG9yZSIsDQogICAgICAgIH0pDQogICAgICB9IGVsc2UgaWYgKGluZGV4ID09IDQpIHsNCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgIHBhdGg6ICIvY29tcG9zaXRlRXhoaWJpdGlvbkhhbGwiLA0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9zdXBwbHlEZW1hbmREb2NraW5nP2luZGV4PSIgKyBpbmRleCk7DQogICAgICB9DQogICAgfSwNCiAgICBnb01hbnVmYWN0dXJpbmdTaGFyaW5nKGluZGV4KSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL21hbnVmYWN0dXJpbmdTaGFyaW5nP2luZGV4PSIgKyBpbmRleCk7DQogICAgfSwNCiAgICBnb1NlcnZpY2VTaGFyaW5nKGluZGV4KSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL3NlcnZpY2VTaGFyaW5nP2luZGV4PSIgKyBpbmRleCk7DQogICAgfSwNCg0KICAgIGdvSW5ub3ZhdGlvblNoYXJpbmcoaW5kZXgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCIvaW5ub3ZhdGlvblNoYXJpbmc/aW5kZXg9IiArIGluZGV4KTsNCiAgICB9LA0KDQogICAgZ29BYm91dFVzKGluZGV4KSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL2Fib3V0VXM/aW5kZXg9IiArIGluZGV4KTsNCiAgICB9LA0KDQogICAgLy8gYWRkKCkgew0KICAgIC8vICAgaWYgKEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oInNlc3Npb25PYmoiKSkpIHsNCiAgICAvLyAgICAgdGhpcy50ZXh0ID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgic2Vzc2lvbk9iaiIpKTsNCiAgICAvLyAgICAgdGhpcy53d2sgPSBKU09OLnBhcnNlKHRoaXMudGV4dC5kYXRhKTsNCiAgICAvLyAgICAgdGhpcy5tb2JpbGUgPSB0aGlzLnd3ay51c2VybmFtZTsNCiAgICAvLyAgICAgdGhpcy5tb2JpbGUgPSB0aGlzLiRCYXNlNjQuZW5jb2RlKHRoaXMubW9iaWxlKTsNCiAgICAvLyAgICAgLy8gdGhpcy50eXBlID0gdGhpcy4kQmFzZTY0LmVuY29kZSh0aGlzLnR5cGUpOw0KICAgIC8vICAgICB3aW5kb3cub3BlbigNCiAgICAvLyAgICAgICBgaHR0cHM6Ly9jeXF5ZncuY29tL2luZGV4L3VzZXIvbG9naW4/a2V5PSR7dGhpcy5rZXl9JnR5cGU9JHt0aGlzLnR5cGV9Jm1vYmlsZT0ke3RoaXMubW9iaWxlfWANCiAgICAvLyAgICAgKTsNCiAgICAvLyAgIH0gZWxzZSB7DQogICAgLy8gICAgIC8vIHdpbmRvdy5vcGVuKCdodHRwczovLzEyMC4yMjEuOTQuMjM1L2luZGV4L3BvbGljeS9wb3J0cmFpdC5odG1sJykNCiAgICAvLyAgICAgLy8gd2luZG93Lm9wZW4oJ2h0dHBzOi8vY3lxeWZ3LmNvbSAnKQ0KICAgIC8vICAgICB3aW5kb3cub3BlbigiaHR0cHM6Ly9jeXF5ZncuY29tL2luZGV4L3BvbGljeS9wb3J0cmFpdC5odG1sIik7DQogICAgLy8gICB9DQogICAgLy8gfSwNCiAgICAvLyBzZW5zZWxlc3NDYXNoaW5nKCkgew0KICAgIC8vICAgaWYgKEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oInNlc3Npb25PYmoiKSkpIHsNCiAgICAvLyAgICAgdGhpcy50ZXh0ID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgic2Vzc2lvbk9iaiIpKTsNCiAgICAvLyAgICAgdGhpcy53d2sgPSBKU09OLnBhcnNlKHRoaXMudGV4dC5kYXRhKTsNCiAgICAvLyAgICAgdGhpcy5tb2JpbGUgPSB0aGlzLnd3ay51c2VybmFtZTsNCiAgICAvLyAgICAgdGhpcy5tb2JpbGUgPSB0aGlzLiRCYXNlNjQuZW5jb2RlKHRoaXMubW9iaWxlKTsNCiAgICAvLyAgICAgd2luZG93Lm9wZW4oDQogICAgLy8gICAgICAgYGh0dHBzOi8vY3lxeWZ3LmNvbS9pbmRleC91c2VyL2xvZ2luP2tleT0ke3RoaXMua2V5fSZ0eXBlPSR7dGhpcy5jYXNoVHlwZX0mbW9iaWxlPSR7dGhpcy5tb2JpbGV9YA0KICAgIC8vICAgICApOw0KICAgIC8vICAgfSBlbHNlIHsNCiAgICAvLyAgICAgLy8gd2luZG93Lm9wZW4oImh0dHBzOi8vMTIwLjIyMS45NC4yMzUiKTsNCiAgICAvLyAgICAgd2luZG93Lm9wZW4oImh0dHBzOi8vY3lxeWZ3LmNvbSAiKTsNCiAgICAvLyAgIH0NCiAgICAvLyB9LA0KICAgIGhhbmRsZVNlbGVjdChpbmRleCkgew0KICAgICAgY29uc29sZS5sb2coaW5kZXgsICItLS0tLS0tLS0tIik7DQogICAgICBpZiAoaW5kZXggJiYgaW5kZXggIT09ICJzaG9wcGluZyIpIHsNCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goYC8ke2luZGV4fWApOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgd2luZG93Lm9wZW4oImh0dHA6Ly82MS4yNDAuMTQ1LjEwMDoxMDAxLyIpOw0KICAgICAgfQ0KICAgICAgLy8gIGVsc2Ugew0KICAgICAgLy8gICB0aGlzLnNlbnNlbGVzc0Nhc2hpbmcoKTsNCiAgICAgIC8vIH0NCiAgICB9LA0KICAgIC8vIC8vIOi3s+i9rOWIsOmcgOaxguWkp+WOhQ0KICAgIC8vIGdvRGVtYW5kSGFsbCgpIHsNCiAgICAvLyAgIHRoaXMuJHJvdXRlci5wdXNoKCIvZGVtYW5kSGFsbCIpOw0KICAgIC8vIH0sDQogICAgLy8gLy8g6Lez6L2s5Yiw6LWE5rqQ5aSn5Y6FDQogICAgLy8gZ29TdXBwbHkoKSB7DQogICAgLy8gICB0aGlzLiRyb3V0ZXIucHVzaCgiL3Jlc291cmNlSGFsbCIpOw0KICAgIC8vIH0sDQogICAgLy8gLy8g6Lez6L2s5Yiw5LyB5Lia5ZCN5b2VDQogICAgLy8gZ29Db21wYW55KCkgew0KICAgIC8vICAgdGhpcy4kcm91dGVyLnB1c2goIi9lbnRlcnByaXNlTGlzdCIpOw0KICAgIC8vIH0sDQogICAgLy8gLy8g6Lez6L2s5Yiw5LiT5a625pm65bqTDQogICAgLy8gZ29FeHBlcnRMaWJyYXJ5KCkgew0KICAgIC8vICAgdGhpcy4kcm91dGVyLnB1c2goIi9leHBlcnRMaWJyYXJ5Iik7DQogICAgLy8gfSwNCiAgICAvLyAvLyDot7PovazliLDmtLvliqjlub/lnLoNCiAgICAvLyBnb0FjdGl2aXR5KCkgew0KICAgIC8vICAgdGhpcy4kcm91dGVyLnB1c2goIi9hY3Rpdml0eVNxdWFyZSIpOw0KICAgIC8vIH0sDQogICAgLy8gLy8g6Lez6L2s5pS/562W6LWE6K6vDQogICAgLy8gZ29Qb2xpY3lJbmZvcm1hdGlvbigpIHsNCiAgICAvLyAgIHRoaXMuJHJvdXRlci5wdXNoKCIvcG9saWN5P3NwZWNpYWxMb2M9cG9saWN5SW5mbyIpOw0KICAgIC8vIH0sDQogICAgLy8gLy8g6Lez6L2s5pS/562W55S75YOPDQogICAgLy8gZ29Qb2xpY3lQb3J0cmFpdCgpIHsNCiAgICAvLyAgIHRoaXMuJHJvdXRlci5wdXNoKCIvcG9saWN5P3NwZWNpYWxMb2M9cG9saWN5UHJvdHJhaXQiKTsNCiAgICAvLyB9LA0KICAgIC8vIC8vIOi3s+i9rOaUv+etlueUs+aKpQ0KICAgIC8vIGdvUG9saWN5QXBwbHkoKSB7DQogICAgLy8gICB0aGlzLiRyb3V0ZXIucHVzaCgiL3BvbGljeT9zcGVjaWFsTG9jPWFwcGx5UG9saWN5Iik7DQogICAgLy8gfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/TopNav", "sourcesContent": ["<template>\r\n  <el-menu :default-active=\"activeMenu\" menu-trigger=\"hover\" mode=\"horizontal\" background-color=\"transparent\"\r\n    @select=\"handleSelect\">\r\n    <template v-for=\"(item, index) in menus\">\r\n      <el-menu-item :style=\"{ '--theme': '#45c9b8' }\" :index=\"item.path\" class=\"nav_span\" :key=\"index\">\r\n        {{ item.meta.title }}\r\n      </el-menu-item>\r\n    </template>\r\n    <!-- 弹窗---- -->\r\n    <!-- 供需对接 -->\r\n    <div class=\"supplyDemandDocking\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in supplyDemandDocking\" :key=\"index\"\r\n          @click=\"goSupplyDemandDocking(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 制造共享 -->\r\n    <div class=\"manufacturingShare\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in manufacturingSharing\" :key=\"index\"\r\n          @click=\"goManufacturingSharing(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 服务共享 -->\r\n    <div class=\"serviceShare\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in serviceSharing\" :key=\"index\"\r\n          @click=\"goServiceSharing(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 创新共享 -->\r\n    <div class=\"innovationShare\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in innovationSharing\" :key=\"index\"\r\n          @click=\"goInnovationSharing(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 关于我们 -->\r\n    <div class=\"aboutUs\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"content_item head_title_line\" v-for=\"(item, index) in aboutUsList\" :key=\"index\"\r\n          @click=\"goAboutUs(index)\">\r\n          <div class=\"title\">{{ item.title }}</div>\r\n          <div class=\"titleLine\"></div>\r\n          <div class=\"desc\">{{ item.desc }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 弹窗 -->\r\n\r\n    <!-- <div class=\"sub-purchase-popper\">\r\n      <div class=\"sub-purchase-content\">\r\n        <div class=\"sub-purchase-left\">\r\n          <div class=\"sub-left-img\">\r\n            <img src=\"../../assets/purchaseSales/purchaseNav.png\" alt=\"\" />\r\n          </div>\r\n          <div class=\"sub-left-title\">采销互联</div>\r\n          <div class=\"sub-left-info\">\r\n            利用平台实现区域互采互销，支持产业链上下游企业间的供需对接，切实推进本地企业产品互采互用，实现区域内企业互利共赢，共同发展。\r\n          </div>\r\n        </div>\r\n        <div class=\"sub-purchase-right\">\r\n          <div class=\"sub-right-each\">\r\n            <div class=\"sub-right-item\" @click=\"goDemandHall\">\r\n              <div class=\"sub-right-title\">\r\n                需求大厅\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">\r\n                汇集企业发展的瓶颈和难题，谁有能力谁来揭榜\r\n              </div>\r\n            </div>\r\n            <div class=\"sub-right-item\" @click=\"goSupply\">\r\n              <div class=\"sub-right-title\">\r\n                供给大厅\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">\r\n                整合产业链资源，工业信息互联供需精准对接\r\n              </div>\r\n            </div>\r\n            <div class=\"sub-right-item\" @click=\"goCompany\">\r\n              <div class=\"sub-right-title\">\r\n                企业名录\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">\r\n                企业黄页大全，产业链上下游企业精准筛选\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"sub-right-each\">\r\n            <div class=\"sub-right-item\" @click=\"goExpertLibrary\">\r\n              <div class=\"sub-right-title\">\r\n                专家智库\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">\r\n                开放科研院所、行业专家资源、解决企业卡脖子难题\r\n              </div>\r\n            </div>\r\n            <div class=\"sub-right-item\" @click=\"goActivity\">\r\n              <div class=\"sub-right-title\">\r\n                活动广场\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">参与最新、最全的线上、线下活动</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div> -->\r\n    <!-- <div class=\"sub-purchase-left\">\r\n          <div class=\"sub-left-img\">\r\n            <img src=\"../../assets/policyDeclare/policyNav.png\" alt=\"\" />\r\n          </div>\r\n          <div class=\"sub-left-title\">政策大厅</div>\r\n          <div class=\"sub-left-info\" style=\"text-align: center\">\r\n            中央到镇街五级政府政策，一键查询\r\n          </div>\r\n        </div>\r\n        <div class=\"sub-purchase-right\">\r\n          <div class=\"sub-right-each\">\r\n            <div class=\"sub-right-item\" @click=\"goPolicyInformation\">\r\n              <div class=\"sub-right-title\">\r\n                政策资讯\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">最新政策 在线查看</div>\r\n            </div>\r\n            <div class=\"sub-right-item\">\r\n              <div class=\"sub-right-title\" @click=\"add\">\r\n                政策画像\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">权威便捷、高效查询</div>\r\n            </div>\r\n            <div class=\"sub-right-item\" @click=\"goPolicyApply\">\r\n              <div class=\"sub-right-title\">\r\n                政策申报\r\n                <span class=\"sub-right-arrow\">>></span>\r\n              </div>\r\n              <div class=\"sub-right-info\">精准查询 申报无忧</div>\r\n            </div>\r\n          </div>\r\n        </div> -->\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { find, propEq, replace, filter, map } from \"ramda\";\r\n\r\nimport { constantRoutes } from \"@/router\";\r\nexport default {\r\n  data() {\r\n    const mainMenu = find(propEq(\"name\", \"main\"), constantRoutes) || {};\r\n    const menus = filter((item) => !item.hidden, mainMenu.children || []);\r\n    const $menus = map((item) => item.path, menus);\r\n    const $path = replace(\"/\", \"\", this.$route.path);\r\n    return {\r\n      activeMenu: $menus.includes($path) ? $path : \"index\",\r\n      menus,\r\n      paths: $menus,\r\n      // 顶部栏初始数\r\n      visibleNumber: 6,\r\n      // 当前激活菜单的 index\r\n      currentIndex: undefined,\r\n      mobile: \"\",\r\n      key: \"QmRlODJTVGhkNg==\",\r\n      type: \"cG9ydHJhaXQ=\",\r\n      base64EncodeChars:\r\n        \"ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",\r\n      text: {},\r\n      wwk: {},\r\n      cashType: \"cG9saWN5Y2FzaA==\",\r\n      supplyDemandDocking: [\r\n        {\r\n          title: \"需求大厅\",\r\n          desc: \"企业发布需求\",\r\n        },\r\n        {\r\n          title: \"供给大厅\",\r\n          desc: \"企业发布供给\",\r\n        },\r\n        {\r\n          title: \"解决方案\",\r\n          desc: \"提供行业问题解决策略与案例\",\r\n        },\r\n        {\r\n          title: \"应用商店\",\r\n          desc: \"汇聚工业工具与应用的下载平台\",\r\n        },\r\n        {\r\n          title: \"复材展厅\",\r\n          desc: \"展示复合材料产品与技术的平台\",\r\n        },\r\n      ],\r\n      manufacturingSharing: [\r\n        {\r\n          title: \"设备共享\",\r\n          desc: \"线上提供模具、工业设备租赁，企业在线申请\",\r\n        },\r\n        {\r\n          title: \"车间共享\",\r\n          desc: \"提供车间租赁，企业在线申请。\",\r\n        },\r\n        {\r\n          title: \"订单共享\",\r\n          desc: \"企业发布生产订单协同需求，工厂接单响应生产交付。\",\r\n        },\r\n      ],\r\n      serviceSharing: [\r\n        {\r\n          title: \"人才服务\",\r\n          desc: \"企业招聘、个人简历或能力信息，人才供需对接。\",\r\n        },\r\n        {\r\n          title: \"企业用工\",\r\n          desc: \"劳务用工信息汇集，海量高薪职位等你来选。\",\r\n        },\r\n        {\r\n          title: \"检验检测\",\r\n          desc: \"专业第三方检测机构提供检验检测服务，检测项目一键申请。\",\r\n        },\r\n        {\r\n          title: \"实验室共享\",\r\n          desc: \"实验室资源共享，低成本实现创新。\",\r\n        }\r\n      ],\r\n      innovationSharing: [\r\n        {\r\n          title: \"创业孵化\",\r\n          desc: \"提供创业基地给初创企业，了解加入共享创新园区。\",\r\n        },\r\n        {\r\n          title: \"文件共享\",\r\n          desc: \"专利、标准、商标等知识库信息开放。\",\r\n        },\r\n        {\r\n          title: \"众筹科研\",\r\n          desc: \"汇聚科研众智众筹资金，搭建成果孵化、资源共享的创新协作平台\",\r\n        },\r\n      ],\r\n      aboutUsList: [\r\n        {\r\n          title: \"平台介绍\",\r\n          desc: \"以“共享智造”赋能特色产业集群，实现资源利用的最大化。\",\r\n        },\r\n        {\r\n          title: \"动态资讯\",\r\n          desc: \"提供最新的新闻资讯、让您迅掌握产业动态。\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  watch: {\r\n    $route(route) {\r\n      const { path } = route;\r\n      const $path = replace(\"/\", \"\", path);\r\n      if (this.paths.includes($path)) {\r\n        if ($path !== this.activeMenu) {\r\n          this.activeMenu = $path;\r\n        }\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    },\r\n  },\r\n\r\n  methods: {\r\n    goSupplyDemandDocking(index) {\r\n      if (index == 2) {\r\n        this.$router.push({\r\n          path: \"/solution\",\r\n        })\r\n      } else if (index == 3) {\r\n        this.$router.push({\r\n          path: \"/appStore\",\r\n        })\r\n      } else if (index == 4) {\r\n        this.$router.push({\r\n          path: \"/compositeExhibitionHall\",\r\n        })\r\n      } else {\r\n        this.$router.push(\"/supplyDemandDocking?index=\" + index);\r\n      }\r\n    },\r\n    goManufacturingSharing(index) {\r\n      this.$router.push(\"/manufacturingSharing?index=\" + index);\r\n    },\r\n    goServiceSharing(index) {\r\n      this.$router.push(\"/serviceSharing?index=\" + index);\r\n    },\r\n\r\n    goInnovationSharing(index) {\r\n      this.$router.push(\"/innovationSharing?index=\" + index);\r\n    },\r\n\r\n    goAboutUs(index) {\r\n      this.$router.push(\"/aboutUs?index=\" + index);\r\n    },\r\n\r\n    // add() {\r\n    //   if (JSON.parse(localStorage.getItem(\"sessionObj\"))) {\r\n    //     this.text = JSON.parse(localStorage.getItem(\"sessionObj\"));\r\n    //     this.wwk = JSON.parse(this.text.data);\r\n    //     this.mobile = this.wwk.username;\r\n    //     this.mobile = this.$Base64.encode(this.mobile);\r\n    //     // this.type = this.$Base64.encode(this.type);\r\n    //     window.open(\r\n    //       `https://cyqyfw.com/index/user/login?key=${this.key}&type=${this.type}&mobile=${this.mobile}`\r\n    //     );\r\n    //   } else {\r\n    //     // window.open('https://120.221.94.235/index/policy/portrait.html')\r\n    //     // window.open('https://cyqyfw.com ')\r\n    //     window.open(\"https://cyqyfw.com/index/policy/portrait.html\");\r\n    //   }\r\n    // },\r\n    // senselessCashing() {\r\n    //   if (JSON.parse(localStorage.getItem(\"sessionObj\"))) {\r\n    //     this.text = JSON.parse(localStorage.getItem(\"sessionObj\"));\r\n    //     this.wwk = JSON.parse(this.text.data);\r\n    //     this.mobile = this.wwk.username;\r\n    //     this.mobile = this.$Base64.encode(this.mobile);\r\n    //     window.open(\r\n    //       `https://cyqyfw.com/index/user/login?key=${this.key}&type=${this.cashType}&mobile=${this.mobile}`\r\n    //     );\r\n    //   } else {\r\n    //     // window.open(\"https://120.221.94.235\");\r\n    //     window.open(\"https://cyqyfw.com \");\r\n    //   }\r\n    // },\r\n    handleSelect(index) {\r\n      console.log(index, \"----------\");\r\n      if (index && index !== \"shopping\") {\r\n        this.$router.push(`/${index}`);\r\n      } else {\r\n        window.open(\"http://61.240.145.100:1001/\");\r\n      }\r\n      //  else {\r\n      //   this.senselessCashing();\r\n      // }\r\n    },\r\n    // // 跳转到需求大厅\r\n    // goDemandHall() {\r\n    //   this.$router.push(\"/demandHall\");\r\n    // },\r\n    // // 跳转到资源大厅\r\n    // goSupply() {\r\n    //   this.$router.push(\"/resourceHall\");\r\n    // },\r\n    // // 跳转到企业名录\r\n    // goCompany() {\r\n    //   this.$router.push(\"/enterpriseList\");\r\n    // },\r\n    // // 跳转到专家智库\r\n    // goExpertLibrary() {\r\n    //   this.$router.push(\"/expertLibrary\");\r\n    // },\r\n    // // 跳转到活动广场\r\n    // goActivity() {\r\n    //   this.$router.push(\"/activitySquare\");\r\n    // },\r\n    // // 跳转政策资讯\r\n    // goPolicyInformation() {\r\n    //   this.$router.push(\"/policy?specialLoc=policyInfo\");\r\n    // },\r\n    // // 跳转政策画像\r\n    // goPolicyPortrait() {\r\n    //   this.$router.push(\"/policy?specialLoc=policyProtrait\");\r\n    // },\r\n    // // 跳转政策申报\r\n    // goPolicyApply() {\r\n    //   this.$router.push(\"/policy?specialLoc=applyPolicy\");\r\n    // },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.is-active {\r\n  background-color: #fff !important;\r\n}\r\n\r\n.head_title_line {\r\n  .titleLine {\r\n    position: relative;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      bottom: 0;\r\n      width: 0%;\r\n      width: 0%;\r\n      height: 2px;\r\n      background-color: #37c9b8;\r\n      transition: all 0.35s ease-in;\r\n      left: 0;\r\n      z-index: 1;\r\n    }\r\n  }\r\n\r\n  &:hover {\r\n    .titleLine::after {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.nav_span {\r\n  position: relative;\r\n\r\n  &::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    left: 50%;\r\n    bottom: -3px;\r\n    width: 100%;\r\n    height: 4px;\r\n    background-color: #37c9b8;\r\n    transform-origin: center;\r\n    transform: translate(-50%, 0) scaleX(0);\r\n    transition: transform 0.3s ease-in;\r\n  }\r\n\r\n  &:hover {\r\n    background-color: #fff !important;\r\n\r\n    &::before {\r\n      transform: translate(-50%, 0) scaleX(1);\r\n    }\r\n  }\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item {\r\n  height: 80px !important;\r\n  line-height: 80px !important;\r\n  color: #333333 !important;\r\n  font-size: 16px !important;\r\n  font-weight: 500 !important;\r\n  padding: 0 10px !important;\r\n  margin: 0 10px !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:hover {\r\n  color: #{\"var(--theme)\"} !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item.is-active,\r\n.el-menu--horizontal>.el-submenu.is-active .el-submenu__title {\r\n  color: #21c9b8 !important;\r\n  border-bottom: 4px solid #21c9b8 !important;\r\n  // color: #fff !important;\r\n  // background-color: #{\"var(--theme)\"} !important;\r\n  // border-bottom: none !important;\r\n}\r\n\r\n/* submenu item */\r\n.topmenu-container.el-menu--horizontal>.el-submenu .el-submenu__title {\r\n  height: 80px !important;\r\n  line-height: 80px !important;\r\n  color: #333333 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n  font-size: 16px !important;\r\n  font-weight: 500 !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(6):hover~.supplyDemandDocking {\r\n  display: block;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(3):hover~.manufacturingShare {\r\n  display: block;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(4):hover~.serviceShare {\r\n  display: block;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(5):hover~.innovationShare {\r\n  display: block;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal>.el-menu-item:nth-child(7):hover~.aboutUs {\r\n  display: block;\r\n}\r\n\r\n.supplyDemandDocking,\r\n.manufacturingShare,\r\n.serviceShare,\r\n.innovationShare,\r\n.aboutUs {\r\n  position: fixed;\r\n  display: none;\r\n  left: 0 !important;\r\n  top: 80px !important;\r\n  width: 100vw;\r\n  // height: 246px;\r\n  margin-top: 0;\r\n  padding: 0;\r\n  background-color: #fff;\r\n\r\n  .sub-purchase-content {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    // margin-left: 28.96vw;\r\n    // justify-content: center;\r\n    padding: 50px;\r\n\r\n    .content_item {\r\n      width: 15%;\r\n      margin-left: 5%;\r\n      cursor: pointer;\r\n\r\n      .title {\r\n        font-weight: 700;\r\n        font-size: 20px;\r\n        position: relative;\r\n        transition: all 0.2s ease-in;\r\n      }\r\n\r\n      .titleLine {\r\n        height: 2px;\r\n        width: 100%;\r\n        background-color: #999;\r\n        margin: 20px 0;\r\n      }\r\n\r\n      .desc {\r\n        font-size: 14px;\r\n        color: #999;\r\n      }\r\n    }\r\n\r\n    .content_item:hover .title {\r\n      color: #21c9b8;\r\n      // transition: all 0.6s ease-in-out;\r\n    }\r\n\r\n    .content_item:hover .titleLine {\r\n      // background-color: #21c9b8;\r\n    }\r\n\r\n    .content_item:nth-child(5n + 1) {\r\n      margin-left: 0;\r\n    }\r\n\r\n    .content_item:nth-child(n + 6) {\r\n      margin-top: 50px;\r\n    }\r\n\r\n    // .sub-purchase-left {\r\n    //   margin: 4px 80px 0 0;\r\n    //   width: 400px;\r\n    //   font-family: PingFangSC-Regular, PingFang SC;\r\n    //   .sub-left-img {\r\n    //     width: 64px;\r\n    //     height: 64px;\r\n    //     margin: 0 auto;\r\n    //     img {\r\n    //       width: 100%;\r\n    //       height: 100%;\r\n    //     }\r\n    //   }\r\n    //   .sub-left-title {\r\n    //     font-weight: 500;\r\n    //     color: #333;\r\n    //     line-height: 26px;\r\n    //     padding: 18px 0 8px 0;\r\n    //     text-align: center;\r\n    //   }\r\n    //   .sub-left-info {\r\n    //     color: #666;\r\n    //     line-height: 26px;\r\n    //     white-space: normal;\r\n    //   }\r\n    // }\r\n    // .sub-purchase-right {\r\n    //   display: flex;\r\n    //   .sub-right-each {\r\n    //     .sub-right-item {\r\n    //       cursor: pointer;\r\n    //       font-family: PingFangSC-Regular, PingFang SC;\r\n    //       .sub-right-title {\r\n    //         display: flex;\r\n    //         align-items: center;\r\n    //         font-size: 16px;\r\n    //         font-weight: 500;\r\n    //         color: #333;\r\n    //         line-height: 16px;\r\n    //       }\r\n    //       .sub-right-arrow {\r\n    //         display: none;\r\n    //       }\r\n    //       .sub-right-info {\r\n    //         color: #999999b3;\r\n    //         line-height: 14px;\r\n    //         padding-top: 12px;\r\n    //       }\r\n    //       &:hover {\r\n    //         .sub-right-title {\r\n    //           color: #21c9b8;\r\n    //         }\r\n    //         .sub-right-arrow {\r\n    //           display: block;\r\n    //         }\r\n    //       }\r\n    //       & + .sub-right-item {\r\n    //         padding-top: 32px;\r\n    //       }\r\n    //     }\r\n    //     & + .sub-right-each {\r\n    //       margin-left: 80px;\r\n    //     }\r\n    //   }\r\n    // }\r\n  }\r\n\r\n  &:hover {\r\n    display: block;\r\n  }\r\n}\r\n\r\n.show-content {\r\n  .sub-purchase-popper {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.el-menu--horizontal>.el-submenu {\r\n  &:hover {\r\n    .sub-purchase-title {\r\n      color: #21c9b8 !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}