package com.ruoyi.auth.controller;

import com.ruoyi.auth.config.QWTSmsConfig;
import com.ruoyi.auth.model.QWTSmsResponse;
import com.ruoyi.auth.util.QWTSendUtils;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.redis.service.RedisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

/**
 * 短信验证码控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sms")
public class SmsController {

    private static final Logger log = LoggerFactory.getLogger(SmsController.class);

    @Autowired
    private QWTSmsConfig qwtSmsConfig;

    @Autowired
    private RedisService redisService;

    /**
     * 发送短信验证码
     * 
     * @param phone 手机号
     * @return 发送结果
     */
    @PostMapping("/sendCode")
    public AjaxResult sendSmsCode(@RequestParam("phone") String phone) {
        try {
            // 验证手机号格式
            if (!isValidPhone(phone)) {
                return AjaxResult.error("手机号格式不正确");
            }

            // 检查发送频率限制（1分钟内只能发送一次）
            String rateKey = "sms:rate:" + phone;
            if (redisService.hasKey(rateKey)) {
                return AjaxResult.error("发送过于频繁，请稍后再试");
            }

            // 生成6位验证码
            String code = QWTSendUtils.getRandomNum(6);
            
            // 构造短信内容
            String content = "您的验证码是：" + code + "，5分钟内有效，请勿泄露给他人。";
            
            // 发送短信
            QWTSmsResponse response = QWTSendUtils.sendQWTSms(phone, content, qwtSmsConfig);
            
            if ("OK".equals(response.getCode())) {
                // 发送成功，存储验证码到Redis，5分钟过期
                String codeKey = "sms:code:" + phone;
                redisService.setCacheObject(codeKey, code, 5L, TimeUnit.MINUTES);

                // 设置发送频率限制，1分钟过期
                redisService.setCacheObject(rateKey, "1", 1L, TimeUnit.MINUTES);
                
                log.info("短信验证码发送成功: phone={}, messageId={}", phone, response.getMessageId());
                return AjaxResult.success("验证码发送成功");
            } else {
                log.error("短信验证码发送失败: phone={}, error={}", phone, response.getMessage());
                return AjaxResult.error("验证码发送失败：" + response.getMessage());
            }
            
        } catch (Exception e) {
            log.error("发送短信验证码异常: phone={}", phone, e);
            return AjaxResult.error("验证码发送失败，请稍后重试");
        }
    }

    /**
     * 验证短信验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 验证结果
     */
    @PostMapping("/verifyCode")
    public AjaxResult verifySmsCode(@RequestParam("phone") String phone, 
                                   @RequestParam("code") String code) {
        try {
            // 验证手机号格式
            if (!isValidPhone(phone)) {
                return AjaxResult.error("手机号格式不正确");
            }

            // 验证码不能为空
            if (code == null || code.trim().isEmpty()) {
                return AjaxResult.error("验证码不能为空");
            }

            // 从Redis获取验证码
            String codeKey = "sms:code:" + phone;
            String storedCode = redisService.getCacheObject(codeKey);
            
            if (storedCode == null) {
                return AjaxResult.error("验证码已过期或不存在");
            }
            
            if (!code.trim().equals(storedCode)) {
                return AjaxResult.error("验证码错误");
            }
            
            // 验证成功，删除验证码
            redisService.deleteObject(codeKey);
            
            log.info("短信验证码验证成功: phone={}", phone);
            return AjaxResult.success("验证码验证成功");
            
        } catch (Exception e) {
            log.error("验证短信验证码异常: phone={}, code={}", phone, code, e);
            return AjaxResult.error("验证码验证失败，请稍后重试");
        }
    }

    /**
     * 查询短信余额
     * 
     * @return 余额信息
     */
    @GetMapping("/balance")
    public AjaxResult getSmsBalance() {
        try {
            // 查询B套餐余额
            String balance = QWTSendUtils.getBalance("1", qwtSmsConfig);
            
            log.info("查询短信余额: {}", balance);
            return AjaxResult.success("查询成功", balance);
            
        } catch (Exception e) {
            log.error("查询短信余额异常", e);
            return AjaxResult.error("查询余额失败，请稍后重试");
        }
    }

    /**
     * 验证手机号格式
     * 
     * @param phone 手机号
     * @return 是否有效
     */
    private boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        
        // 简单的手机号验证（11位数字，以1开头）
        return phone.matches("^1[3-9]\\d{9}$");
    }
}
