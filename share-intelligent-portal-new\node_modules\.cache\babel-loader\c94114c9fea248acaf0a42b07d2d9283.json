{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\aboutUs\\components\\dynamicInfoDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\aboutUs\\components\\dynamicInfoDetail.vue", "mtime": 1750311962914}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfaG9tZSA9IHJlcXVpcmUoIkAvYXBpL2hvbWUiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gewogIG5hbWU6ICJuZXdzIiwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGRldGFpbDoge30sCiAgICAgIG5ld3NMaXN0OiBbXSwKICAgICAgbmV3c0xpc3QyOiBbXSwKICAgICAgbmV3c0xpc3QzOiBbXSwKICAgICAgbmV3c0xpc3Q0OiBbXSwKICAgICAgbmV3c0xpc3Q1OiBbXSwKICAgICAgbmV3c0xpc3Q2OiBbXSwKICAgICAgbmV3c0xpc3Q3OiBbXSwKICAgICAgbmV3c0xpc3Q4OiBbXSwKICAgICAgbmV3c0xpc3Q5OiBbXSwKICAgICAgbmV3c0xpc3QxMDogW10sCiAgICAgIGlkOiBudWxsLAogICAgICBjbXNMaXN0OiBbXQogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7CiAgICB0aGlzLmdldERhdGEoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGdldERhdGE6IGZ1bmN0aW9uIGdldERhdGEoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgbmV3c0luZm9ybWF0aW9uSWQ6IHRoaXMuaWQKICAgICAgfTsKICAgICAgKDAsIF9ob21lLmluZm9EZXRhaWxEYXRhKShwYXJhbXMpLnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7CiAgICAgICAgICBfdGhpcy5kZXRhaWwgPSByZXMuZGF0YTsKICAgICAgICAgIF90aGlzLmRldGFpbC5uZXdzSW5mb3JtYXRpb25Db250ZW50ID0gZGVjb2RlVVJJQ29tcG9uZW50KF90aGlzLmRldGFpbC5uZXdzSW5mb3JtYXRpb25Db250ZW50KTsKICAgICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["_home", "require", "name", "data", "loading", "detail", "newsList", "newsList2", "newsList3", "newsList4", "newsList5", "newsList6", "newsList7", "newsList8", "newsList9", "newsList10", "id", "cmsList", "created", "$route", "query", "getData", "methods", "_this", "params", "newsInformationId", "infoDetailData", "then", "res", "code", "newsInformationContent", "decodeURIComponent"], "sources": ["src/views/aboutUs/components/dynamicInfoDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"main\" v-loading=\"loading\">\r\n    <div class=\"main_l\">\r\n      <div class=\"zhuanjia_title\">{{ detail.newsInformationName || \"\" }}</div>\r\n      <div class=\"laiyuan\">\r\n        {{ detail.newsInformationDate }} 作者：{{\r\n          detail.newsInformationAuthor ? detail.newsInformationAuthor : \"\"\r\n        }}\r\n      </div>\r\n      <div class=\"news_c\" v-html=\"detail.newsInformationContent\"></div>\r\n    </div>\r\n    <div class=\"main_r\">\r\n      <div class=\"ad\">\r\n        <div style=\"color: white\">更多新闻动态</div>\r\n      </div>\r\n      <!-- <div class=\"ad_exr\">\r\n        <ul class=\"news_exr_list2\">\r\n          <li v-for=\"(item, index) in cmsList\" :key=\"index\">\r\n            <a\r\n              :href=\"'newsInformationDetail.html?id=' + item.newsInformationId\"\r\n            >\r\n              <div class=\"news_exr_l\">\r\n                <img :src=\"item.newsInformationImg\" />\r\n              </div>\r\n              <div class=\"news_exr_r\">\r\n                <p>{{ item.newsInformationName || \"\" }}</p>\r\n                <div class=\"time\">{{ item.newsInformationDate }}</div>\r\n              </div>\r\n            </a>\r\n          </li>\r\n        </ul>\r\n      </div> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { infoDetailData, infoData } from \"@/api/home\";\r\n\r\nexport default {\r\n  name: \"news\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      detail: {},\r\n      newsList: [],\r\n      newsList2: [],\r\n      newsList3: [],\r\n      newsList4: [],\r\n      newsList5: [],\r\n      newsList6: [],\r\n      newsList7: [],\r\n      newsList8: [],\r\n      newsList9: [],\r\n      newsList10: [],\r\n      id: null,\r\n      cmsList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getData();\r\n  },\r\n  methods: {\r\n    getData() {\r\n      let params = {\r\n        newsInformationId: this.id,\r\n      };\r\n      infoDetailData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.detail = res.data;\r\n          this.detail.newsInformationContent = decodeURIComponent(\r\n            this.detail.newsInformationContent\r\n          );\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.main {\r\n  width: 1200px;\r\n  display: flex;\r\n  flex-flow: row wrap;\r\n  justify-content: space-between;\r\n  margin-top: 0px;\r\n  margin-right: auto;\r\n  margin-bottom: 0;\r\n  margin-left: auto;\r\n  font-size: 14px;\r\n  padding-top: 40px;\r\n  padding-right: 0px;\r\n  padding-bottom: 40px;\r\n  padding-left: 0px;\r\n}\r\n.main_l {\r\n  width: 855px;\r\n}\r\n\r\n.main_r {\r\n  width: 320px;\r\n}\r\n.zhuanjia_title {\r\n  font-size: 30px;\r\n  color: #000000;\r\n  line-height: 50px;\r\n}\r\n\r\n.news_c {\r\n  padding-top: 20px;\r\n  padding-bottom: 20px;\r\n  width: 100%;\r\n\r\n  ::v-deep .ql-align-center{\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n.ad {\r\n  height: 86px;\r\n  background-size: cover;\r\n  border-radius: 4px;\r\n  line-height: 86px;\r\n  font-size: 20px;\r\n  text-align: center;\r\n  background-color: #21c9b8;\r\n  cursor: pointer;\r\n}\r\n\r\n.ad_exr {\r\n  padding-left: 20px;\r\n  padding-top: 20px;\r\n  background-color: #fff;\r\n}\r\n\r\nul.news_exr_list2 {\r\n  margin: 0px;\r\n  padding-top: 10px;\r\n  padding-right: 0px;\r\n  padding-bottom: 0px;\r\n  padding-left: 0px;\r\n}\r\n\r\nul.news_exr_list2 li {\r\n  padding: 12px;\r\n  box-shadow: 0 2px 5px #eee;\r\n  border-radius: 5px;\r\n  margin-top: 15px;\r\n  margin-right: 0px;\r\n  margin-bottom: 0px;\r\n  margin-left: 0px;\r\n  border-top-width: 1px;\r\n  border-right-width: 1px;\r\n  border-bottom-width: 0px;\r\n  border-left-width: 1px;\r\n  border-top-style: solid;\r\n  border-right-style: solid;\r\n  border-bottom-style: solid;\r\n  border-left-style: solid;\r\n  border-top-color: #eee;\r\n  border-right-color: #eee;\r\n  border-bottom-color: #eee;\r\n  border-left-color: #eee;\r\n}\r\n\r\nul.news_exr_list2 li a {\r\n  display: flex;\r\n  width: 100%;\r\n  flex-flow: row wrap;\r\n  justify-content: space-between;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAoCA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,MAAA;MACAC,QAAA;MACAC,SAAA;MACAC,SAAA;MACAC,SAAA;MACAC,SAAA;MACAC,SAAA;MACAC,SAAA;MACAC,SAAA;MACAC,SAAA;MACAC,UAAA;MACAC,EAAA;MACAC,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAF,EAAA,QAAAG,MAAA,CAAAC,KAAA,CAAAJ,EAAA;IACA,KAAAK,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,MAAA;QACAC,iBAAA,OAAAT;MACA;MACA,IAAAU,oBAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAN,KAAA,CAAAlB,MAAA,GAAAuB,GAAA,CAAAzB,IAAA;UACAoB,KAAA,CAAAlB,MAAA,CAAAyB,sBAAA,GAAAC,kBAAA,CACAR,KAAA,CAAAlB,MAAA,CAAAyB,sBACA;UACAP,KAAA,CAAAnB,OAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}