{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\newsCenter\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\newsCenter\\detail.vue", "mtime": 1750311962976}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_newsCenter", "require", "name", "data", "created", "init", "methods", "_this", "id", "$route", "query", "loading", "newsDetail", "then", "res", "code", "console", "log", "attachment", "JSON", "parse", "catch", "downLoadFile", "file", "dataSource", "for<PERSON>ach", "item", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "style", "display", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click"], "sources": ["src/views/newsCenter/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"notice-detail-container\">\r\n    <div class=\"notice-detail-banner\">\r\n      <img src=\"../../assets/notice/noticeDetailBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div class=\"notice-detail-title-box\">\r\n      <div class=\"notice-divider\"></div>\r\n      <div class=\"notice-detail-title\">新闻详情</div>\r\n      <div class=\"notice-divider\"></div>\r\n    </div>\r\n    <div class=\"notice-detail-content\">\r\n      <div class=\"notice-detail-box\">\r\n        <div class=\"notice-info-title\">\r\n          {{ data.title }}\r\n        </div>\r\n        <div class=\"notice-info-time\">\r\n          {{ data.updateTime }}\r\n          <span style=\"margin-left: 10px\">作者:{{ data.createBy }}</span>\r\n        </div>\r\n        <div\r\n          v-if=\"data.attachment && data.attachment.length > 0\"\r\n          style=\"margin: 10px 0; cursor: pointer\"\r\n          @click=\"downLoadFile(data.attachment)\"\r\n        >\r\n          <i class=\"el-icon-download\">下载附件</i>\r\n        </div>\r\n        <div class=\"notice-info-divider\"></div>\r\n        <div class=\"notice-info-box\">\r\n          <div\r\n            v-html=\"data.content\"\r\n            class=\"notice-info-content ql-editor\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { newsDetail } from \"@/api/newsCenter\";\r\n\r\nexport default {\r\n  name: \"policyPage\",\r\n  data() {\r\n    return {\r\n      data: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      let id = this.$route.query.id;\r\n      this.loading = true;\r\n      newsDetail({ id: id })\r\n        .then((res) => {\r\n          if (res.code === 200) {\r\n            console.log(res, \"-----------------\");\r\n            // res.data.attachment = JSON.parse(res.data.attachment);\r\n            this.data = res.data;\r\n            this.data.attachment = JSON.parse(res.data.attachment);\r\n            this.loading = false;\r\n          }\r\n          // this.data = res.data || {};\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    downLoadFile(file) {\r\n      const dataSource = file; // 这里面是需要批量下载的文件url列表，根据自己的项目获取\r\n      dataSource.forEach((item) => {\r\n        let url = window.URL.createObjectURL(new Blob([item.url]));\r\n        let link = document.createElement(\"a\");\r\n        link.style.display = \"none\";\r\n        link.href = url;\r\n        link.setAttribute(\"download\", item.name);\r\n        document.body.appendChild(link);\r\n        link.click();\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.notice-detail-container {\r\n  width: 100%;\r\n  padding: 0 0 100px;\r\n  background: #f4f5f9;\r\n  .notice-detail-banner {\r\n    width: 100%;\r\n    height: 26vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .notice-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .notice-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .notice-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .notice-detail-content {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n    .notice-detail-box {\r\n      padding: 60px 116px 100px;\r\n      font-family: PingFangSC-Semibold, PingFang SC;\r\n      .notice-info-title {\r\n        width: 960px;\r\n        font-size: 32px;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n        white-space: nowrap; /*让文字不换行*/\r\n        overflow: hidden; /*超出要隐藏*/\r\n        word-wrap: break-word;\r\n      }\r\n      .notice-info-time {\r\n        font-size: 14px;\r\n        color: #999;\r\n        line-height: 12px;\r\n        padding-top: 40px;\r\n      }\r\n      .notice-info-divider {\r\n        width: 100%;\r\n        height: 1px;\r\n        background: #e8e8e8;\r\n        margin-top: 10px;\r\n      }\r\n      .notice-info-box {\r\n        padding-top: 40px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.notice-detail-container {\r\n  .notice-info-content {\r\n    word-break: break-all;\r\n    font-size: 16px;\r\n    line-height: 28px;\r\n    color: #333;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    img {\r\n      max-width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAuCA,IAAAA,WAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAA,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,IAAA,WAAAA,KAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,EAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,EAAA;MACA,KAAAG,OAAA;MACA,IAAAC,sBAAA;QAAAJ,EAAA,EAAAA;MAAA,GACAK,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAC,OAAA,CAAAC,GAAA,CAAAH,GAAA;UACA;UACAP,KAAA,CAAAJ,IAAA,GAAAW,GAAA,CAAAX,IAAA;UACAI,KAAA,CAAAJ,IAAA,CAAAe,UAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAN,GAAA,CAAAX,IAAA,CAAAe,UAAA;UACAX,KAAA,CAAAI,OAAA;QACA;QACA;MACA,GACAU,KAAA;QACAd,KAAA,CAAAI,OAAA;MACA;IACA;IACAW,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,UAAA,GAAAD,IAAA;MACAC,UAAA,CAAAC,OAAA,WAAAC,IAAA;QACA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,KAAAC,IAAA,EAAAL,IAAA,CAAAC,GAAA;QACA,IAAAK,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,KAAA,CAAAC,OAAA;QACAJ,IAAA,CAAAK,IAAA,GAAAV,GAAA;QACAK,IAAA,CAAAM,YAAA,aAAAZ,IAAA,CAAAxB,IAAA;QACA+B,QAAA,CAAAM,IAAA,CAAAC,WAAA,CAAAR,IAAA;QACAA,IAAA,CAAAS,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}