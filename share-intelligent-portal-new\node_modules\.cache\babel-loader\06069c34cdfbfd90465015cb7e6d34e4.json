{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\interested.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\interested.vue", "mtime": 1750311962967}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ramda", "require", "_home", "data", "form", "title", "updateTime", "rules", "completionDate", "required", "message", "trigger", "quantity", "created", "userinfo", "JSON", "parse", "sessionStorage", "getItem", "companyName", "memberCompanyName", "linkMan", "companyRealName", "linkTel", "memberPhone", "$route", "query", "demandName", "intentionType", "parseInt", "fieldName", "intentionId", "methods", "onSubmit", "_this", "$refs", "validate", "valid", "sum", "submitIntention", "then", "res", "code", "$message", "success", "cancel", "error", "msg", "goDetail", "$router", "push", "go"], "sources": ["src/views/manufacturingSharing/components/interested.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div style=\"height: 37px\">我有意向</div>\r\n      <div style=\"height: 33px; margin-top: 21px\">I have intentions</div>\r\n    </div>\r\n    <div class=\"card-container card-content\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <div class=\"imgStyle\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../assets/device/ceshi.png\" alt=\"\" />\r\n        </div>\r\n        <div class=\"title\">{{ form.title }}</div>\r\n        <div style=\"display: flex; align-items: center; margin-top: 15px\">\r\n          <div class=\"publishTimeStyle\">发布时间：{{ updateTime }}</div>\r\n          <!-- <div class=\"detailStyle\" @click=\"goDetail\">查看详情 >></div> -->\r\n        </div>\r\n      </div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <span class=\"resourceType\">资源类型：</span>\r\n          <span class=\"resourceValue\">{{ form.fieldName }}</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <span class=\"resourceType\">资源名称：</span>\r\n          <span class=\"resourceValue\">{{ form.title }}</span>\r\n        </div>\r\n        <div style=\"margin-top: 20px\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"意向描述：\">\r\n              <el-input v-model=\"form.intentionContent\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\"\r\n                show-word-limit placeholder=\"请输入\" />\r\n            </el-form-item>\r\n            <el-row>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"完工日期\" prop=\"completionDate\" style=\"width: 80%\">\r\n                  <el-date-picker v-model=\"form.completionDate\" type=\"date\" placeholder=\"选择日期\" value-format=\"yyyy-MM-dd\"\r\n                    style=\"width: 100%\" />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"加工数量\" prop=\"quantity\" style=\"width: 80%\">\r\n                  <el-input v-model=\"form.quantity\" placeholder=\"请输入\"></el-input>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n            <el-form-item label=\"意向企业：\" prop=\"companyName\">\r\n              <el-input disabled v-model=\"form.companyName\" placeholder=\"自动带出\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系人：\" prop=\"linkMan\">\r\n              <el-input disabled v-model=\"form.linkMan\" placeholder=\"请先维护联系人\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系电话：\" prop=\"linkTel\">\r\n              <el-input disabled v-model=\"form.linkTel\" placeholder=\"请先维护联系方式\"></el-input>\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button style=\"width: 100%; height: 50px\" type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n        <div class=\"promptStyle\">温馨提示</div>\r\n        <div class=\"desc\" style=\"margin-top: 20px\">\r\n          1、我们会在最快的时间和您取得联系（工作时间周一至周五8:00-18:00）\r\n        </div>\r\n        <div class=\"desc\" style=\"margin-top: 13px\">\r\n          2、紧急问题请拨打：15512688882\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { update } from 'ramda';\r\nimport { submitIntention } from \"@/api/home\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        title: \"\",\r\n      },\r\n      updateTime: \"\",\r\n      rules: {\r\n        completionDate: [\r\n          { required: true, message: \"请选择完工日期\", trigger: \"change\" },\r\n        ],\r\n        quantity: [\r\n          { required: true, message: \"请输入加工数量\", trigger: \"blur\" },\r\n        ],\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n    if (userinfo) {\r\n      this.form.companyName = userinfo.memberCompanyName;\r\n      this.form.linkMan = userinfo.companyRealName;\r\n      this.form.linkTel = userinfo.memberPhone;\r\n    }\r\n    if (this.$route.query.demandName && this.$route.query.demandName != 'null') {\r\n      this.form.title = this.$route.query.demandName\r\n    }\r\n    if (this.$route.query.intentionType) {\r\n      this.form.intentionType = parseInt(this.$route.query.intentionType)\r\n    }\r\n    if (this.$route.query.fieldName) {\r\n      this.form.fieldName = this.$route.query.fieldName\r\n    }\r\n    if (this.$route.query.intentionId) {\r\n      this.form.intentionId = this.$route.query.intentionId\r\n    }\r\n    if (this.$route.query.updateTime && this.$route.query.updateTime != 'null') {\r\n      this.updateTime = this.$route.query.updateTime\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          this.form.sum = this.sum\r\n          submitIntention(this.form).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$message.success(\"提交成功\")\r\n              this.cancel()\r\n            } else {\r\n              this.$message.error(res.msg)\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    goDetail() {\r\n      this.$router.push(\"/productOrderDetail\");\r\n    },\r\n    cancel(){\r\n      this.$router.go(-1)\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: rgb(242, 242, 242);\r\n  padding-bottom: 60px;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 71px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n}\r\n\r\n.card-content {\r\n  display: flex;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: -70px;\r\n  padding: 60px 59px 62px 60px;\r\n\r\n  .card_left {\r\n    .imgStyle {\r\n      width: 330px;\r\n      height: 230px;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #333333;\r\n      margin-top: 23px;\r\n    }\r\n\r\n    .publishTimeStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n    }\r\n\r\n    .detailStyle {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #21c9b8;\r\n      margin-left: auto;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .card_right {\r\n    margin-left: 40px;\r\n    width: 100%;\r\n\r\n    .resourceType {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .resourceValue {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #666666;\r\n    }\r\n\r\n    .footer-submit {\r\n      margin-top: 40px;\r\n    }\r\n\r\n    .promptStyle {\r\n      margin-top: 30px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #030a1a;\r\n    }\r\n\r\n    .desc {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #999999;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAyEA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,KAAA;MACA;MACAC,UAAA;MACAC,KAAA;QACAC,cAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,cAAA,CAAAC,OAAA;IACA,IAAAJ,QAAA;MACA,KAAAV,IAAA,CAAAe,WAAA,GAAAL,QAAA,CAAAM,iBAAA;MACA,KAAAhB,IAAA,CAAAiB,OAAA,GAAAP,QAAA,CAAAQ,eAAA;MACA,KAAAlB,IAAA,CAAAmB,OAAA,GAAAT,QAAA,CAAAU,WAAA;IACA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,UAAA,SAAAF,MAAA,CAAAC,KAAA,CAAAC,UAAA;MACA,KAAAvB,IAAA,CAAAC,KAAA,QAAAoB,MAAA,CAAAC,KAAA,CAAAC,UAAA;IACA;IACA,SAAAF,MAAA,CAAAC,KAAA,CAAAE,aAAA;MACA,KAAAxB,IAAA,CAAAwB,aAAA,GAAAC,QAAA,MAAAJ,MAAA,CAAAC,KAAA,CAAAE,aAAA;IACA;IACA,SAAAH,MAAA,CAAAC,KAAA,CAAAI,SAAA;MACA,KAAA1B,IAAA,CAAA0B,SAAA,QAAAL,MAAA,CAAAC,KAAA,CAAAI,SAAA;IACA;IACA,SAAAL,MAAA,CAAAC,KAAA,CAAAK,WAAA;MACA,KAAA3B,IAAA,CAAA2B,WAAA,QAAAN,MAAA,CAAAC,KAAA,CAAAK,WAAA;IACA;IACA,SAAAN,MAAA,CAAAC,KAAA,CAAApB,UAAA,SAAAmB,MAAA,CAAAC,KAAA,CAAApB,UAAA;MACA,KAAAA,UAAA,QAAAmB,MAAA,CAAAC,KAAA,CAAApB,UAAA;IACA;EACA;EACA0B,OAAA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,KAAA,CAAA9B,IAAA,CAAAkC,GAAA,GAAAJ,KAAA,CAAAI,GAAA;UACA,IAAAC,qBAAA,EAAAL,KAAA,CAAA9B,IAAA,EAAAoC,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACAR,KAAA,CAAAS,QAAA,CAAAC,OAAA;cACAV,KAAA,CAAAW,MAAA;YACA;cACAX,KAAA,CAAAS,QAAA,CAAAG,KAAA,CAAAL,GAAA,CAAAM,GAAA;YACA;UACA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACAL,MAAA,WAAAA,OAAA;MACA,KAAAI,OAAA,CAAAE,EAAA;IACA;EACA;AACA", "ignoreList": []}]}