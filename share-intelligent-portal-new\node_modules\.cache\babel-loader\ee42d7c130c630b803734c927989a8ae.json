{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\joinNow.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\joinNow.vue", "mtime": 1750311962967}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_manufacturingSharing", "require", "_ramda", "_company", "data", "form", "companyName", "socialCreditCode", "registeredCapital", "contactPhone", "industry", "companyAddress", "businessScope", "personnelList", "qualificationList", "equipmentList", "settledStatus", "rules", "required", "message", "trigger", "methods", "onSubmit", "_this", "$refs", "validate", "valid", "enteringFactoryAdd", "then", "res", "code", "$message", "success", "$router", "go", "onCancel", "addPersonnelList", "push", "<PERSON><PERSON><PERSON>", "technicalType", "addQualificationList", "qualificationName", "attachment", "addEquipmentList", "equipmentName", "specification", "querySearchTianYanCha", "queryString", "cb", "searchCompany", "keywords", "rows", "List", "for<PERSON>ach", "val", "index", "id", "value", "length", "selectAutoDataTianYanCha", "row", "_this2", "getCompanyCodeByName", "$set", "taxNo"], "sources": ["src/views/manufacturingSharing/components/joinNow.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      制造共享\r\n      <div class=\"imgContent\">\r\n        <div class=\"imgStyle\">\r\n          <img style=\"width: 100%; height: 100%\" src=\"../../../assets/order/orderStep.png\" alt=\"\" />\r\n          <!-- <div class=\"joinNow\" @click=\"joinNow\">立即入驻</div> -->\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container content_card\">\r\n      <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n        <div class=\"title\">基本信息</div>\r\n        <div class=\"titleLine\"></div>\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"企业名称\" prop=\"companyName\">\r\n              <el-autocomplete v-model=\"form.companyName\" placeholder=\"请输入您公司的完整名称\" style=\"width: 100%;\"\r\n                :fetch-suggestions=\"querySearchTianYanCha\" @select=\"selectAutoDataTianYanCha\"></el-autocomplete>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"社会信用代码\">\r\n              <el-input v-model=\"form.socialCreditCode\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"注册资本\">\r\n              <el-input v-model=\"form.registeredCapital\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"联系电话\">\r\n              <el-input v-model=\"form.contactPhone\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"行业\">\r\n              <el-input v-model=\"form.industry\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"地址\">\r\n              <el-input v-model=\"form.companyAddress\" placeholder=\"请输入\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item prop=\"technologyType\">\r\n          <div slot=\"label\">经营范围</div>\r\n          <el-input v-model=\"form.businessScope\" type=\"textarea\" resize=\"none\" :rows=\"8\" maxlength=\"500\" show-word-limit\r\n            placeholder=\"请输入\" />\r\n        </el-form-item>\r\n        <div class=\"title\">企业能力</div>\r\n        <div class=\"titleLine\"></div>\r\n        <!-- <el-form-item label=\"\">\r\n          <div slot=\"label\">\r\n            <div style=\"display: flex; width: 1080px\">\r\n              <div>业绩情况</div>\r\n              <div class=\"addStyle\">新增行</div>\r\n            </div>\r\n          </div>\r\n          <el-table :data=\"jobList\">\r\n            <el-table-column label=\"项目名称\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.jobId\"></el-input>\r\n              </template>\r\n</el-table-column>\r\n<el-table-column label=\"联系人\" align=\"center\">\r\n  <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.jobId\"></el-input>\r\n              </template>\r\n</el-table-column>\r\n<el-table-column label=\"联系电话\" align=\"center\">\r\n  <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.jobId\"></el-input>\r\n              </template>\r\n</el-table-column>\r\n<el-table-column label=\"附件\" align=\"center\">\r\n  <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.jobId\"></el-input>\r\n              </template>\r\n</el-table-column>\r\n</el-table>\r\n</el-form-item> -->\r\n        <el-form-item label=\"\">\r\n          <div slot=\"label\">\r\n            <div style=\"display: flex; width: 1080px\">\r\n              <div>人员能力</div>\r\n              <div class=\"addStyle\" @click=\"addPersonnelList\">新增行</div>\r\n            </div>\r\n          </div>\r\n          <el-table :data=\"form.personnelList\">\r\n            <el-table-column label=\"技术人员姓名\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.technicianName\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"专业技术工种\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.technicalType\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-form-item>\r\n        <el-form-item label=\"技术实力\">\r\n          <el-input v-model=\"form.technicalCapability\" placeholder=\"请输入\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"\">\r\n          <div slot=\"label\">\r\n            <div style=\"display: flex; width: 1080px\">\r\n              <div>\r\n                资质证件\r\n                <span style=\"color: #999999; font-size: 14px; margin-left: 11px\">（专利、商标、资质、证书等）</span>\r\n              </div>\r\n              <div class=\"addStyle\" @click=\"addQualificationList\">新增行</div>\r\n            </div>\r\n          </div>\r\n          <el-table :data=\"form.qualificationList\">\r\n            <el-table-column label=\"资质名称\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.qualificationName\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"附件\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <ImageUpload :limit=\"1\" v-model=\"scope.row.attachment\" />\r\n                <!-- <el-input v-model=\"scope.row.jobId\"></el-input> -->\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-form-item>\r\n        <el-form-item label=\"\">\r\n          <div slot=\"label\">\r\n            <div style=\"display: flex; width: 1080px\">\r\n              <div>设备信息</div>\r\n              <div class=\"addStyle\" @click=\"addEquipmentList\">新增行</div>\r\n            </div>\r\n          </div>\r\n          <el-table :data=\"form.equipmentList\">\r\n            <el-table-column label=\"生产设备\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.equipmentName\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"规格型号\" align=\"center\">\r\n              <template slot-scope=\"scope\">\r\n                <el-input v-model=\"scope.row.specification\"></el-input>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-form-item>\r\n        <el-form-item class=\"footer-submit\">\r\n          <el-button type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n          <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { enteringFactoryAdd } from \"@/api/manufacturingSharing\";\r\nimport { add } from \"ramda\";\r\nimport { searchCompany, getCompanyCodeByName } from \"@/api/system/company\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      form: {\r\n        companyName: \"\", // 企业名称\r\n        socialCreditCode: \"\", // 社会信用代码\r\n        registeredCapital: \"\", // 注册资本\r\n        contactPhone: \"\", // 联系电话\r\n        industry: \"\", // 行业\r\n        companyAddress: \"\", // 地址\r\n        businessScope: \"\", // 经营范围\r\n        personnelList: [], // 人员能力\r\n        qualificationList: [], // 资质证件\r\n        equipmentList: [], // 设备信息列表\r\n        settledStatus: \"0\", // 默认传待审核\r\n      },\r\n      rules: {\r\n        companyName: [\r\n          { required: true, message: \"请输入企业名称\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    onSubmit() {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          enteringFactoryAdd(this.form).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功\");\r\n              this.$router.go(-1);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    onCancel() { },\r\n    addPersonnelList() {\r\n      this.form.personnelList.push({\r\n        technicianName: \"\",\r\n        technicalType: \"\",\r\n      });\r\n    },\r\n    addQualificationList() {\r\n      this.form.qualificationList.push({\r\n        qualificationName: \"\",\r\n        attachment: \"\",\r\n      });\r\n    },\r\n    addEquipmentList() {\r\n      this.form.equipmentList.push({\r\n        equipmentName: \"\",\r\n        specification: \"\",\r\n      });\r\n    },\r\n    // 企业名称\r\n    querySearchTianYanCha(queryString, cb) {\r\n      if (queryString) {\r\n        searchCompany({ keywords: queryString }).then(res => {\r\n          let data = res.rows;\r\n          let List = [];\r\n          data.forEach(function (val, index) {\r\n            List.push({\r\n              id: index,\r\n              value: val\r\n            })\r\n          })\r\n          if (data.length > 0) {\r\n            cb(List);\r\n          } else {\r\n            cb([{\r\n              id: '',\r\n              value: '暂无数据'\r\n            }]);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 企业名称选择\r\n    selectAutoDataTianYanCha(row) {\r\n      getCompanyCodeByName({ keywords: row.value }).then(res => {\r\n        if (res.code == 200) {\r\n          let data = res.data;\r\n          this.$set(this.form, 'socialCreditCode', data.taxNo)\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n  background-color: #f2f2f2;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 28px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n\r\n  .imgContent {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-top: 10px;\r\n\r\n    .imgStyle {\r\n      width: 1256px;\r\n      height: 206px;\r\n      position: relative;\r\n      // .joinNow {\r\n      //   position: absolute;\r\n      //   right: 90px;\r\n      //   top: 75px;\r\n      //   width: 110px;\r\n      //   height: 50px;\r\n      //   background: #f79a47;\r\n      //   border-radius: 2px;\r\n      //   font-family: Source Han Sans CN;\r\n      //   font-weight: 400;\r\n      //   font-size: 18px;\r\n      //   color: #ffffff;\r\n      //   line-height: 50px;\r\n      //   text-align: center;\r\n      //   cursor: pointer;\r\n      // }\r\n    }\r\n  }\r\n}\r\n\r\n.content_card {\r\n  // height: 1530px;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: 30px;\r\n  padding: 59px 60px 57px 60px;\r\n}\r\n\r\n.title {\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 18px;\r\n  color: #21c9b8;\r\n}\r\n\r\n.titleLine {\r\n  width: 100%;\r\n  height: 1px;\r\n  background: #21c9b8;\r\n  margin: 20px 0 30px 0;\r\n}\r\n\r\n.addStyle {\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 400;\r\n  font-size: 14px;\r\n  color: #21c9b8;\r\n  margin-left: auto;\r\n  cursor: pointer;\r\n}\r\n\r\n.footer-submit {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 60px;\r\n\r\n  .el-button {\r\n    width: 140px;\r\n    height: 50px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;AAiKA,IAAAA,qBAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,WAAA;QAAA;QACAC,gBAAA;QAAA;QACAC,iBAAA;QAAA;QACAC,YAAA;QAAA;QACAC,QAAA;QAAA;QACAC,cAAA;QAAA;QACAC,aAAA;QAAA;QACAC,aAAA;QAAA;QACAC,iBAAA;QAAA;QACAC,aAAA;QAAA;QACAC,aAAA;MACA;MACAC,KAAA;QACAX,WAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,wCAAA,EAAAJ,KAAA,CAAAlB,IAAA,EAAAuB,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACAP,KAAA,CAAAQ,QAAA,CAAAC,OAAA;cACAT,KAAA,CAAAU,OAAA,CAAAC,EAAA;YACA;UACA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAA/B,IAAA,CAAAQ,aAAA,CAAAwB,IAAA;QACAC,cAAA;QACAC,aAAA;MACA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MACA,KAAAnC,IAAA,CAAAS,iBAAA,CAAAuB,IAAA;QACAI,iBAAA;QACAC,UAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAtC,IAAA,CAAAU,aAAA,CAAAsB,IAAA;QACAO,aAAA;QACAC,aAAA;MACA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,WAAA,EAAAC,EAAA;MACA,IAAAD,WAAA;QACA,IAAAE,sBAAA;UAAAC,QAAA,EAAAH;QAAA,GAAAnB,IAAA,WAAAC,GAAA;UACA,IAAAzB,IAAA,GAAAyB,GAAA,CAAAsB,IAAA;UACA,IAAAC,IAAA;UACAhD,IAAA,CAAAiD,OAAA,WAAAC,GAAA,EAAAC,KAAA;YACAH,IAAA,CAAAf,IAAA;cACAmB,EAAA,EAAAD,KAAA;cACAE,KAAA,EAAAH;YACA;UACA;UACA,IAAAlD,IAAA,CAAAsD,MAAA;YACAV,EAAA,CAAAI,IAAA;UACA;YACAJ,EAAA;cACAQ,EAAA;cACAC,KAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAE,wBAAA,WAAAA,yBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,6BAAA;QAAAZ,QAAA,EAAAU,GAAA,CAAAH;MAAA,GAAA7B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,IAAA1B,IAAA,GAAAyB,GAAA,CAAAzB,IAAA;UACAyD,MAAA,CAAAE,IAAA,CAAAF,MAAA,CAAAxD,IAAA,sBAAAD,IAAA,CAAA4D,KAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}