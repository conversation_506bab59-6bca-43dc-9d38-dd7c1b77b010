{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\deviceDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\deviceDetail.vue", "mtime": 1750311962965}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["deviceDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "deviceDetail.vue", "sourceRoot": "src/views/manufacturingSharing/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"card-container cardStyle\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <!-- 上半部分 -->\r\n        <div class=\"card_left_top\">\r\n          <div class=\"imgStyle\">\r\n            <img style=\"width: 100%; height: 100%\" :src=\"detailsData.images\r\n              ? detailsData.images.split(',')[0]\r\n              : require('@/assets/device/ceshi.png')\r\n              \" alt=\"\" />\r\n          </div>\r\n          <!-- <div class=\"imgContent\">\r\n            <div style=\"cursor: pointer\">\r\n              <img src=\"../../../assets/device/icon_left.png\" alt=\"\" />\r\n            </div>\r\n            <div style=\"display: flex; align-items: center; margin: 0 10px\">\r\n              <div\r\n                class=\"everyImgStyle\"\r\n                v-for=\"(item, index) in imgList\"\r\n                :key=\"index\"\r\n              >\r\n                <img\r\n                  style=\"width: 100%; height: 100%\"\r\n                  src=\"../../../assets/device/ceshi.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n            </div>\r\n            <div style=\"cursor: pointer\">\r\n              <img src=\"../../../assets/device/icon_right.png\" alt=\"\" />\r\n            </div>\r\n          </div> -->\r\n        </div>\r\n        <!-- 下半部分 -->\r\n        <div class=\"card_left_bottom\">\r\n          <div class=\"title\">{{ detailsData.name }}</div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">设备类别：</div>\r\n            <div class=\"optionValue\">\r\n              {{ deviceType }}\r\n            </div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">所属单位：</div>\r\n            <div class=\"optionValue\">{{ detailsData.location }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">租赁模式：</div>\r\n            <div class=\"optionValue\">{{ detailsData.rentMode }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">参考价格：</div>\r\n            <div class=\"optionValue\" style=\"color: #cc0a0a\">\r\n              {{ detailsData.rentPrice }}\r\n            </div>\r\n          </div>\r\n          <div class=\"buttonStyle\" @click=\"jumpIntention\">我有意向</div>\r\n        </div>\r\n      </div>\r\n      <!-- 中间 -->\r\n      <div class=\"card_center_line\"></div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">设备用途</div>\r\n          </div>\r\n          <div class=\"content_desc\">\r\n            {{ detailsData.description }}\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">技术指标</div>\r\n          </div>\r\n          <div style=\"margin-top: 22px\">\r\n            <el-descriptions class=\"margin-top\" title=\"\" :column=\"2\" border>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 采购日期 </template>\r\n                {{ detailsData.createTime }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 厂商品牌 </template>\r\n                {{ detailsData.brand }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 规格型号 </template>\r\n                {{ detailsData.modelNumber }}\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { deviceDetailData } from \"@/api/manufacturingSharing\";\r\nexport default {\r\n  name: \"deviceDetail\",\r\n  data() {\r\n    return {\r\n      detailsData: {},\r\n      deviceType: \"\",\r\n      imgList: [\r\n        {\r\n          id: 1,\r\n        },\r\n        {\r\n          id: 2,\r\n        },\r\n        {\r\n          id: 3,\r\n        },\r\n        {\r\n          id: 4,\r\n        },\r\n        {\r\n          id: 5,\r\n        },\r\n      ],\r\n      deviceMenuList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getDeviceDict();\r\n  },\r\n  methods: {\r\n    /** 查询字典数据列表 */\r\n    getDeviceDict() {\r\n      let params = { dictType: \"device_share_type\" };\r\n      listData(params).then((response) => {\r\n        this.deviceMenuList = response.rows;\r\n        this.getDetailData();\r\n      });\r\n    },\r\n    getDetailData() {\r\n      deviceDetailData(this.id).then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res.data, \"----------\");\r\n          this.detailsData = res.data;\r\n          this.deviceMenuList.forEach((item) => {\r\n            if (item.dictValue == this.detailsData.category) {\r\n              this.deviceType = item.dictLabel;\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    jumpIntention() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(`/demandInterested?demandName=${this.detailsData.name}&updateTime=${this.detailsData.updateTime}&intentionType=3&fieldName=设备共享&intentionId=${this.detailsData.id}`);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: #f2f2f2;\r\n  padding: 30px 0 61px 0;\r\n}\r\n\r\n.cardStyle {\r\n  height: 660px;\r\n  background-color: #ffffff;\r\n  padding: 60px 56px 54px 50px;\r\n  display: flex;\r\n}\r\n\r\n.card_left {\r\n  .card_left_top {\r\n    .imgStyle {\r\n      width: 330px;\r\n      height: 230px;\r\n      border-radius: 2px;\r\n      margin-left: 10px;\r\n    }\r\n\r\n    .imgContent {\r\n      margin-top: 15px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .everyImgStyle {\r\n        width: 54px;\r\n        height: 50px;\r\n        margin-left: 10px;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .everyImgStyle:nth-child(1) {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .card_left_bottom {\r\n    margin-top: 30px;\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      font-size: 20px;\r\n      color: #222222;\r\n      margin-bottom: 13px;\r\n    }\r\n\r\n    .everyOption {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 12px;\r\n\r\n      .optionName {\r\n        min-height: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #999999;\r\n        min-width: 75px;\r\n      }\r\n\r\n      .optionValue {\r\n        min-height: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #333333;\r\n      }\r\n    }\r\n\r\n    .buttonStyle {\r\n      margin-top: 32px;\r\n      margin-left: 55px;\r\n      width: 220px;\r\n      height: 50px;\r\n      background: #21c9b8;\r\n      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);\r\n      border-radius: 2px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      text-align: center;\r\n      line-height: 50px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.card_center_line {\r\n  width: 1px;\r\n  height: 100%;\r\n  background: #e1e1e1;\r\n  margin-left: 51px;\r\n  margin-right: 61px;\r\n}\r\n\r\n.card_right {\r\n  width: 100%;\r\n  overflow-y: auto;\r\n\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n\r\n  .content_desc {\r\n    // width: 631px;\r\n    // height: 159px;\r\n    margin-top: 20px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #666666;\r\n    line-height: 24px;\r\n  }\r\n}\r\n</style>\r\n"]}]}