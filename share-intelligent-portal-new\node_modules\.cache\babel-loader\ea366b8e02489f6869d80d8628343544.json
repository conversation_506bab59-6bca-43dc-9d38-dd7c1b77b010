{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\thinkTank.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\thinkTank.vue", "mtime": 1750311962937}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_purchaseSales", "require", "_cryptoJs", "_interopRequireDefault", "secret<PERSON>ey", "_default", "exports", "default", "data", "loading", "pageNum", "total", "created", "methods", "gothinkTank", "routeData", "$router", "resolve", "path", "window", "open", "href", "searchExpert", "_this", "getExpertList", "pageSize", "then", "res", "key", "CryptoJS", "enc", "Utf8", "parse", "decrypt", "AES", "mode", "ECB", "padding", "pad", "Pkcs7", "JSON", "stringify", "_ref", "rows", "slice", "for<PERSON>ach", "item", "techniqueTypeName", "split", "catch", "goExpertLibrary", "id", "query"], "sources": ["src/views/components/home/<USER>"], "sourcesContent": ["<template>\r\n  <div\r\n    class=\"cardBg wow animate__animated animate__fadeInUp\"\r\n    data-wow-duration=\"1s\"\r\n  >\r\n    <div class=\"card-container\">\r\n      <div class=\"enterpriseTitle\">\r\n        <div>专家智库</div>\r\n        <div class=\"allEnterprise\" @click=\"gothinkTank\">查看全部>></div>\r\n      </div>\r\n      <div class=\"expert-library-list\">\r\n        <div\r\n          v-for=\"(item, index) in data\"\r\n          :key=\"index\"\r\n          class=\"list-item-content\"\r\n          @click=\"goExpertLibrary(item.id)\"\r\n        >\r\n          <div class=\"list-item-box\">\r\n            <div class=\"item-headline\">\r\n              <div class=\"item-title\">\r\n                {{ item.expertName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"expert-library-label\">\r\n              <div\r\n                v-for=\"(val, index1) in item.techniqueTypeName\"\r\n                :key=\"index1\"\r\n                class=\"library-label-item\"\r\n              >\r\n                <span v-if=\"index1 < 2\" class=\"expert-library-type\">{{\r\n                  `#${val}`\r\n                }}</span>\r\n                <span v-else>…</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"expert-library-box\">\r\n              {{ item.synopsis }}\r\n            </div>\r\n          </div>\r\n          <div class=\"list-item-img\">\r\n            <img v-if=\"item.headPortrait\" :src=\"item.headPortrait\" alt=\"\" />\r\n            <img\r\n              v-else\r\n              src=\"../../../assets/expertLibrary/defaultImg.png\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getExpertList } from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    // this.searchExpert();\r\n  },\r\n  methods: {\r\n    gothinkTank() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/thinkTank\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    searchExpert() {\r\n      this.loading = true;\r\n      getExpertList({\r\n        pageSize: 4,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows.slice(0, 4);\r\n          this.data.forEach((item) => {\r\n            item.techniqueTypeName = item.techniqueTypeName\r\n              ? item.techniqueTypeName.split(\",\")\r\n              : [];\r\n          });\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 跳转到专家详情页面\r\n    goExpertLibrary(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/expertDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cardBg {\r\n  width: 100%;\r\n  height: 740px;\r\n  background-image: url(\"../../../assets/images/home/<USER>\");\r\n  background-size: 100% 100%;\r\n}\r\n.enterpriseTitle {\r\n  width: 100%;\r\n  font-size: 36px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  color: #000000;\r\n  text-align: center;\r\n  margin: 60px 0 20px 0;\r\n  padding-top: 70px;\r\n  position: relative;\r\n  .allEnterprise {\r\n    position: absolute;\r\n    top: 8 px;\r\n    right: 0;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    line-height: 26px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n  height: 280px;\r\n  .contentItem {\r\n    width: 23%;\r\n    height: 100%;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    background: #ffffff;\r\n    box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);\r\n    border-radius: 4px;\r\n    img {\r\n      width: 100%;\r\n      height: 230px;\r\n    }\r\n  }\r\n}\r\n.expert-library-list {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  width: 100%;\r\n  .list-item-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    width: 578px;\r\n    background: #fff;\r\n    margin-top: 31px;\r\n    padding: 28px 32px;\r\n    min-height: 240px;\r\n    .list-item-box {\r\n      flex: 1;\r\n      .item-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        .item-title {\r\n          width: 280px;\r\n          font-size: 32px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 32px;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          word-wrap: break-word;\r\n        }\r\n      }\r\n      .expert-library-label {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        margin: 0 0 16px;\r\n        .library-label-item {\r\n          max-width: 350px;\r\n          padding: 6px 12px;\r\n          background: #f4f5f9;\r\n          border-radius: 4px;\r\n          font-size: 12px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #666;\r\n          line-height: 12px;\r\n          margin: 24px 16px 0 0;\r\n          .expert-library-type {\r\n            word-wrap: break-word;\r\n          }\r\n        }\r\n      }\r\n      .expert-library-box {\r\n        width: 370px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #666;\r\n        line-height: 32px;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n    }\r\n    .list-item-img {\r\n      width: 120px;\r\n      height: 168px;\r\n      margin-left: 24px;\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n    &:hover {\r\n      cursor: pointer;\r\n    }\r\n  }\r\n  .list-item-content:hover {\r\n    box-shadow: 0px 2px 20px 0px rgba(13, 230, 96, 0.3);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;AAsDA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAG,SAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAD,IAAA;MACAE,OAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAA;MACA,IAAAC,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;MACA;MACAC,MAAA,CAAAC,IAAA,CAAAL,SAAA,CAAAM,IAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,KAAA;MACA,KAAAd,OAAA;MACA,IAAAe,4BAAA;QACAC,QAAA;MACA,GACAC,IAAA,WAAAC,GAAA;QACA,IAAAC,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAA5B,SAAA;QACA,IAAA6B,OAAA,GAAAJ,iBAAA,CAAAK,GAAA,CAAAD,OAAA,CAAAN,GAAA,EAAAC,GAAA;UACAO,IAAA,EAAAN,iBAAA,CAAAM,IAAA,CAAAC,GAAA;UACAC,OAAA,EAAAR,iBAAA,CAAAS,GAAA,CAAAC;QACA;QACAZ,GAAA,GAAAa,IAAA,CAAAR,KAAA,CAAAH,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAU,SAAA,CAAAR,OAAA;QAEAV,KAAA,CAAAd,OAAA;QACA,IAAAiC,IAAA,GAAAf,GAAA;UAAAgB,IAAA,GAAAD,IAAA,CAAAC,IAAA;UAAAhC,KAAA,GAAA+B,IAAA,CAAA/B,KAAA;QACAY,KAAA,CAAAf,IAAA,GAAAmC,IAAA,CAAAC,KAAA;QACArB,KAAA,CAAAf,IAAA,CAAAqC,OAAA,WAAAC,IAAA;UACAA,IAAA,CAAAC,iBAAA,GAAAD,IAAA,CAAAC,iBAAA,GACAD,IAAA,CAAAC,iBAAA,CAAAC,KAAA,QACA;QACA;QACAzB,KAAA,CAAAZ,KAAA,GAAAA,KAAA;MACA,GACAsC,KAAA;QACA1B,KAAA,CAAAd,OAAA;MACA;IACA;IACA;IACAyC,eAAA,WAAAA,gBAAAC,EAAA;MACA,IAAApC,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACAkC,KAAA;UAAAD,EAAA,EAAAA;QAAA;MACA;MACAhC,MAAA,CAAAC,IAAA,CAAAL,SAAA,CAAAM,IAAA;IACA;EACA;AACA", "ignoreList": []}]}