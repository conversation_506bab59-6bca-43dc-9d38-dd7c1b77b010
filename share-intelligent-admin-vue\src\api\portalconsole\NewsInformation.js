import request from '@/utils/request'

// 查询动态资讯列表
export function listNewsInformation(query) {
  return request({
    url: '/portalconsole/NewsInformation/list',
    method: 'get',
    params: query
  })
}

// 查询动态资讯详细
export function getNewsInformation(newsInformationId) {
  return request({
    url: '/portalconsole/NewsInformation/' + newsInformationId,
    method: 'get'
  })
}

// 新增动态资讯
export function addNewsInformation(data) {
  return request({
    url: '/portalconsole/NewsInformation',
    method: 'post',
    data: data
  })
}

// 修改动态资讯
export function updateNewsInformation(data) {
  return request({
    url: '/portalconsole/NewsInformation',
    method: 'put',
    data: data
  })
}

// 删除动态资讯
export function delNewsInformation(newsInformationId) {
  return request({
    url: '/portalconsole/NewsInformation/' + newsInformationId,
    method: 'delete'
  })
}
//动态资讯查询资讯行业的数据
export function listSolutionType(query) {
  return request({
    url: '/portalconsole/solutionType/list',
    method: 'get',
    params: query
  })
}