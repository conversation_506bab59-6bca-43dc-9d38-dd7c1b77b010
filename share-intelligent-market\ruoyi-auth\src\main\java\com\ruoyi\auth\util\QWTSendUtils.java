package com.ruoyi.auth.util;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.ruoyi.auth.config.QWTSmsConfig;
import com.ruoyi.auth.config.SmsConfig;
import com.ruoyi.auth.model.QWTSmsResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Random;

public class QWTSendUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(QWTSendUtils.class);

    /**
     * 使用阿里云发送短信
     * @param telephone  手机号
     * @param templateParam 阿里云模板参数
     * @param smsConfig 阿里云配置参数
     * @return
     */
    public static SendSmsResponse sendSms(String telephone, String templateParam, SmsConfig smsConfig) {

        // 可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");

        // 初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", smsConfig.getAccessKeyId(), smsConfig.getAccessKeySecret());
        try {
            DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", smsConfig.getProduct(), smsConfig.getDomain());
        } catch (ClientException e) {
            e.printStackTrace();
        }
        IAcsClient acsClient = new DefaultAcsClient(profile);

        // 组装请求对象-具体描述见控制台-文档部分内容
        SendSmsRequest request = new SendSmsRequest();
        // 必填:待发送手机号
        request.setPhoneNumbers(telephone);
        // 必填:短信签名-可在短信控制台中找到
        request.setSignName(smsConfig.getSign()); // 签名
        // 必填:短信模板-可在短信控制台中找到
        request.setTemplateCode(smsConfig.getTemplate());  // 验证码模板
        // 可选:模板中的变量替换JSON串,如模板内容为"亲爱的用户,您的验证码为${code}"时,此处的值为
        request.setTemplateParam(templateParam);
        // 选填-上行短信扩展码(无特殊需求用户请忽略此字段)
        // request.setSmsUpExtendCode("90997");
        // 可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
        request.setOutId("yourOutId");
        // hint 此处可能会抛出异常，注意catch
        SendSmsResponse sendSmsResponse = null;
        try {
            sendSmsResponse = acsClient.getAcsResponse(request);
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                //记录日志
                LOGGER.info("短信发送成功！signName: {}, telephone: {}, templateCode: {}, templateParam: {}", telephone,templateParam);
            } else {
                // 记录日志
                LOGGER.info("短信发送失败！ {}。 signName: {}, telephone: {}, templateCode: {}, templateParam: {}", sendSmsResponse.getMessage(), telephone, templateParam);
            }
        } catch (ClientException e) {
            e.printStackTrace();
            LOGGER.error(e.getMessage(), e);
        }

        return sendSmsResponse;
    }

    /**
     * 使用全网智能通讯平台发送短信
     * @param telephone 手机号
     * @param content 短信内容
     * @param qwtSmsConfig 全网智能通讯平台配置参数
     * @return
     */
    public static QWTSmsResponse sendQWTSms(String telephone, String content, QWTSmsConfig qwtSmsConfig) {
        QWTSmsResponse response = new QWTSmsResponse();

        try {
            // 构建请求URL
            StringBuilder urlBuilder = new StringBuilder(qwtSmsConfig.getApiUrl());
            urlBuilder.append("/SendSms?");
            urlBuilder.append("LoginName=").append(URLEncoder.encode(qwtSmsConfig.getLoginName(), "UTF-8"));
            urlBuilder.append("&Pwd=").append(URLEncoder.encode(qwtSmsConfig.getPassword(), "UTF-8"));
            urlBuilder.append("&FeeType=").append(URLEncoder.encode(qwtSmsConfig.getFeeType(), "UTF-8"));
            urlBuilder.append("&Mobile=").append(URLEncoder.encode(telephone, "UTF-8"));
            urlBuilder.append("&Content=").append(URLEncoder.encode(content, "UTF-8"));

            // 如果有签名，添加签名
            if (qwtSmsConfig.getSignName() != null && !qwtSmsConfig.getSignName().isEmpty()) {
                urlBuilder.append("&SignName=").append(URLEncoder.encode(qwtSmsConfig.getSignName(), "UTF-8"));
            }

            // 创建HTTP连接
            URL url = new URL(urlBuilder.toString());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);

            // 获取响应
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
                String inputLine;
                StringBuilder responseContent = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    responseContent.append(inputLine);
                }
                in.close();

                String responseStr = responseContent.toString();
                LOGGER.info("全网智能通讯平台短信发送响应: {}", responseStr);

                // 解析响应
                if (responseStr.startsWith("OK|")) {
                    // 成功响应格式：OK|消息编码 或 OK|消息编码|进入审核
                    String[] parts = responseStr.split("\\|");
                    response.setCode("OK");
                    response.setMessage("短信发送成功");
                    if (parts.length > 1) {
                        response.setMessageId(parts[1]);
                    }
                    LOGGER.info("短信发送成功！telephone: {}, content: {}", telephone, content);
                } else {
                    // 失败响应
                    response.setCode("ERROR");
                    response.setMessage("短信发送失败: " + responseStr);
                    LOGGER.info("短信发送失败！ {}。 telephone: {}, content: {}", responseStr, telephone, content);
                }
            } else {
                response.setCode("ERROR");
                response.setMessage("HTTP请求失败，状态码: " + responseCode);
                LOGGER.error("HTTP请求失败，状态码: {}", responseCode);
            }

            connection.disconnect();
        } catch (Exception e) {
            response.setCode("ERROR");
            response.setMessage("发送短信异常: " + e.getMessage());
            LOGGER.error("发送短信异常", e);
        }

        return response;
    }

    /**
     * 生成随机数，去掉0和1
     *
     * @param length 长度
     * @return
     */
    public static String getRandomNum(int length) {
        char[] codeSeq = {'2', '3', '4', '5', '6', '7', '8', '9'};
        Random random = new Random();
        StringBuilder s = new StringBuilder();
        for (int i = 0; i < length; i++) {
            String r = String.valueOf(codeSeq[random.nextInt(codeSeq.length)]);
            s.append(r);
        }
        return s.toString();
    }

    /**
     * 获取短信报告
     * @param messageId 消息ID
     * @param qwtSmsConfig 全网智能通讯平台配置参数
     * @return 短信报告内容
     */
    public static String getSmsReport(String messageId, QWTSmsConfig qwtSmsConfig) {
        try {
            // 构建请求URL
            StringBuilder urlBuilder = new StringBuilder(qwtSmsConfig.getApiUrl());
            urlBuilder.append("/GetSmsReport?");
            urlBuilder.append("LoginName=").append(URLEncoder.encode(qwtSmsConfig.getLoginName(), "UTF-8"));
            urlBuilder.append("&Pwd=").append(URLEncoder.encode(qwtSmsConfig.getPassword(), "UTF-8"));
            urlBuilder.append("&MessageID=").append(URLEncoder.encode(messageId, "UTF-8"));

            // 创建HTTP连接
            URL url = new URL(urlBuilder.toString());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);

            // 获取响应
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
                String inputLine;
                StringBuilder responseContent = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    responseContent.append(inputLine);
                }
                in.close();

                String responseStr = responseContent.toString();
                LOGGER.info("获取短信报告响应: {}", responseStr);

                connection.disconnect();
                return responseStr;
            } else {
                LOGGER.error("获取短信报告HTTP请求失败，状态码: {}", responseCode);
                return "ERROR: HTTP请求失败，状态码: " + responseCode;
            }
        } catch (Exception e) {
            LOGGER.error("获取短信报告异常", e);
            return "ERROR: " + e.getMessage();
        }
    }

    /**
     * 获取短信回复
     * @param messageId 消息ID
     * @param qwtSmsConfig 全网智能通讯平台配置参数
     * @return 短信回复内容
     */
    public static String getSmsReply(String messageId, QWTSmsConfig qwtSmsConfig) {
        try {
            // 构建请求URL
            StringBuilder urlBuilder = new StringBuilder(qwtSmsConfig.getApiUrl());
            urlBuilder.append("/GetSmsReply?");
            urlBuilder.append("LoginName=").append(URLEncoder.encode(qwtSmsConfig.getLoginName(), "UTF-8"));
            urlBuilder.append("&Pwd=").append(URLEncoder.encode(qwtSmsConfig.getPassword(), "UTF-8"));
            urlBuilder.append("&MessageID=").append(URLEncoder.encode(messageId, "UTF-8"));

            // 创建HTTP连接
            URL url = new URL(urlBuilder.toString());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);

            // 获取响应
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
                String inputLine;
                StringBuilder responseContent = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    responseContent.append(inputLine);
                }
                in.close();

                String responseStr = responseContent.toString();
                LOGGER.info("获取短信回复响应: {}", responseStr);

                connection.disconnect();
                return responseStr;
            } else {
                LOGGER.error("获取短信回复HTTP请求失败，状态码: {}", responseCode);
                return "ERROR: HTTP请求失败，状态码: " + responseCode;
            }
        } catch (Exception e) {
            LOGGER.error("获取短信回复异常", e);
            return "ERROR: " + e.getMessage();
        }
    }

    /**
     * 查询余额
     * @param getType 查询类型：1（B套餐）、2（A套餐）、3（彩信）、4（语音）
     * @param qwtSmsConfig 全网智能通讯平台配置参数
     * @return 余额信息
     */
    public static String getBalance(String getType, QWTSmsConfig qwtSmsConfig) {
        try {
            // 构建请求URL
            StringBuilder urlBuilder = new StringBuilder(qwtSmsConfig.getApiUrl());
            urlBuilder.append("/GetBalance?");
            urlBuilder.append("LoginName=").append(URLEncoder.encode(qwtSmsConfig.getLoginName(), "UTF-8"));
            urlBuilder.append("&Pwd=").append(URLEncoder.encode(qwtSmsConfig.getPassword(), "UTF-8"));
            urlBuilder.append("&GetType=").append(URLEncoder.encode(getType, "UTF-8"));

            // 创建HTTP连接
            URL url = new URL(urlBuilder.toString());
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);

            // 获取响应
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
                String inputLine;
                StringBuilder responseContent = new StringBuilder();

                while ((inputLine = in.readLine()) != null) {
                    responseContent.append(inputLine);
                }
                in.close();

                String responseStr = responseContent.toString();
                LOGGER.info("查询余额响应: {}", responseStr);

                connection.disconnect();
                return responseStr;
            } else {
                LOGGER.error("查询余额HTTP请求失败，状态码: {}", responseCode);
                return "ERROR: HTTP请求失败，状态码: " + responseCode;
            }
        } catch (Exception e) {
            LOGGER.error("查询余额异常", e);
            return "ERROR: " + e.getMessage();
        }
    }
}
