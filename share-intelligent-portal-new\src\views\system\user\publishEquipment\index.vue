<template>
  <div class="app-container">
    <el-row
      :gutter="20"
      style="background: linear-gradient(to right, #e1f7f0, #f4fcfa)"
    >
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
        <div class="formStyle">
          <el-form ref="form" :rules="rules" :model="form" label-position="top">
            <el-form-item label="设备名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入设备名称" />
            </el-form-item>
            <el-form-item label="设备分类" prop="category">
              <el-select
                v-model="form.category"
                placeholder="请选择设备分类"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="dict in deviceMenuList"
                  :key="dict.dictLabel"
                  :label="dict.dictLabel"
                  :value="dict.dictValue"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="设备规格" prop="specifications">
              <el-input
                v-model="form.specifications"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
            <el-form-item label="所属单位/位置" prop="location">
              <el-input
                v-model="form.location"
                placeholder="请输入所属单位/位置"
              />
            </el-form-item>
            <el-form-item label="设备用途描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                placeholder="请输入内容"
              />
            </el-form-item>
            <el-form-item label="设备图片" prop="images">
              <ImageUpload v-model="form.images" resultType="string" :limit="1" :multiple="false" />
            </el-form-item>
            <el-form-item label="租用模式" prop="rentMode">
              <el-input v-model="form.rentMode" placeholder="请输入租用模式" />
            </el-form-item>
            <el-form-item label="租用价格" prop="rentPrice">
              <el-input v-model="form.rentPrice" placeholder="请输入租用价格" />
            </el-form-item>
            <el-form-item label="压力(MPa)" prop="pressure">
              <el-input v-model="form.pressure" placeholder="请输入压力(MPa)" />
            </el-form-item>
            <el-form-item label="温度" prop="temperature">
              <el-input v-model="form.temperature" placeholder="请输入温度" />
            </el-form-item>
            <el-form-item label="尺寸" prop="dimension">
              <el-input v-model="form.dimension" placeholder="请输入尺寸" />
            </el-form-item>
            <el-form-item label="规格型号" prop="modelNumber">
              <el-input
                v-model="form.modelNumber"
                placeholder="请输入规格型号"
              />
            </el-form-item>
            <el-form-item class="footer-submit">
              <el-button type="primary" @click="onSubmit">{{
                form.id ? "保存" : "发布"
              }}</el-button>
              <el-button style="margin-left: 140px" @click.once="onCancel"
                >取消</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import UserMenu from "../components/userMenu.vue";
import { listData } from "@/api/system/dict/data";
import {
  addDeviceInfo,
  updateDeviceInfo,
  deviceDetailData,
} from "@/api/manufacturingSharing";
export default {
  name: "User",
  components: { UserMenu },
  data() {
    return {
      form: {
        id: null,
        name: null,
        category: null,
        specifications: null,
        location: null,
        description: null,
        images: null,
        technicalParams: null,
        rentMode: null,
        rentPrice: null,
        createTime: null,
        updateTime: null,
        checkStatus: null,
        createBy: null,
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: "设备名称不能为空", trigger: "blur" },
        ],
        category: [
          { required: true, message: "设备分类不能为空", trigger: "change" },
        ],
        location: [
          { required: true, message: "所属单位/位置不能为空", trigger: "blur" },
        ],
      },
      deviceMenuList: [],
    };
  },
  created() {
    this.getDicts();
    if (this.$route.query.id) {
      this.getDetail();
    }
  },
  methods: {
    /** 查询字典数据列表 */
    getDicts() {
      let params = { dictType: "device_share_type" };
      listData(params).then((res) => {
        if (res.code === 200) {
          this.deviceMenuList = res.rows;
        }
      });
    },
    getDetail() {
      deviceDetailData(this.$route.query.id).then((res) => {
        if (res.code == 200) {
          this.form = res.data;
        }
      });
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateDeviceInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.$router.go(-1);
            });
          } else {
            this.form.checkStatus = 0;
            let userinfo = JSON.parse(
              window.sessionStorage.getItem("userinfo")
            );
            this.form.createBy = userinfo.memberPhone;
            addDeviceInfo(this.form).then((response) => {
              this.$modal.msgSuccess("发布成功,请等待审核");
              this.$router.go(-1);
            });
          }
        }
      });
    },
    onCancel() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));
  height: 100vh;
}

.formStyle {
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  .footer-submit {
    text-align: center;
  }
}
</style>
