<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="2.5" :xs="24">
                <user-menu activeIndex="1" />
            </el-col>
            <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
                <div class="top">
                    <div class="content_title">
                        <div class="icon"></div>
                        <div class="title">我的对接记录</div>
                        <div class="buttonStyle" @click="cancel">返回</div>
                    </div>
                </div>
                <div class="content">
                    <p>意向提交时间：{{ this.content.createTime }}</p>
                    <p>资源类型：{{ this.content.intentionTypeName }}</p>
                    <p v-if="this.content.title">标题：{{ this.content.title }}</p>
                    <p>意向描述：{{ this.content.intentionContent }}</p>
                    <p v-if="this.content.completionDate">完成日期：{{ this.content.completionDate }}</p>
                    <p v-if="this.content.quantity">承接量：{{ this.content.quantity }}</p>
                    <p v-if="this.content.price">含税单价：{{ this.content.price }}</p>
                    <p v-if="this.content.rate">税率：{{ this.content.rate }}</p>
                    <p v-if="this.content.shippingFee">运费：{{ this.content.shippingFee }}</p>
                    <p v-if="this.content.sum">含税合计：{{ this.content.sum }}</p>
                    <p>联系人：{{ this.userinfo.memberRealName }}</p>
                    <p>联系电话：{{ this.userinfo.memberPhone }}</p>
                </div>
            </el-col>
        </el-row>
    </div>
</template>
<script>
import UserMenu from "../components/userMenu.vue";

export default {
    name: "User",
    components: { UserMenu },
    data() {
        return {
            id: 1,
            content: {},
            userinfo: {}
        };
    },
    created() {
        if (this.$route.query.id) {
            this.id = this.$route.query.id;
            this.content = this.$route.query;
            console.log(this.content)
        }
        this.userinfo = JSON.parse(sessionStorage.getItem("userinfo"));
    },
    methods: {
        cancel() {
            this.$router.go(-1);
        },
    },
};
</script>
<style lang="scss" scoped>
.app-container {
    background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));
    height: 1000px;
}

.top {
    padding: 20px;
    background: #fff;
    border-radius: 10px;
    margin-top: 20px;
    display: flex;
    justify-content: space-between;

    .content_title {
        display: flex;
        align-items: center;
        width: 100%;

        .icon {
            width: 4px;
            height: 20px;
            background: #21c9b8;
        }

        .title {
            font-family: Source Han Sans CN;
            font-weight: 400;
            font-size: 18px;
            color: #030a1a;
            margin-left: 10px;
        }

        .buttonStyle {
            padding: 10px 20px;
            background: #21c9b8;
            color: #fff;
            text-align: center;
            cursor: pointer;
            border-radius: 10px;
            margin-left: auto;
        }
    }
}

.queryForm {
    padding: 20px;
}

.content {
    background-color: #fff;
    padding: 20px;
    margin-top: 20px;
    border-radius: 10px;

    p {
        margin: 10px 0;
    }
}
</style>