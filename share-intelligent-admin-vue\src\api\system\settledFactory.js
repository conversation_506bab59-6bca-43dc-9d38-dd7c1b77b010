import request from '@/utils/request'

// 查询入驻工厂列表
export function listSettledFactory(query) {
  return request({
    url: '/system/settledFactory/list',
    method: 'get',
    params: query
  })
}

// 查询入驻工厂详细
export function getSettledFactory(id) {
  return request({
    url: '/system/settledFactory/' + id,
    method: 'get'
  })
}

// 新增入驻工厂
export function addSettledFactory(data) {
  return request({
    url: '/system/settledFactory',
    method: 'post',
    data: data
  })
}

// 修改入驻工厂
export function updateSettledFactory(data) {
  return request({
    url: '/system/settledFactory',
    method: 'put',
    data: data
  })
}

// 删除入驻工厂
export function delSettledFactory(id) {
  return request({
    url: '/system/settledFactory/' + id,
    method: 'delete'
  })
}
