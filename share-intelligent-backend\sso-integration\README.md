# 主系统SSO集成说明

## 概述

本目录包含主系统（share-intelligent-backend）的SSO客户端集成代码和配置文件。

## 文件结构

```
sso-integration/
├── README.md                    # 本说明文件
├── application-sso.yml          # SSO配置文件
└── ../ruoyi-auth/src/main/java/com/ruoyi/auth/
    ├── controller/
    │   └── SSOClientController.java    # SSO客户端控制器
    ├── service/
    │   └── SSOClientService.java       # SSO客户端服务
    └── config/
        └── SSOClientConfig.java        # SSO客户端配置
```

## 主要功能

### 1. SSO客户端控制器 (SSOClientController)

- **SSO登录跳转**: `/sso/login` - 跳转到SSO认证服务
- **SSO登录回调**: `/sso/callback` - 处理SSO登录回调
- **SSO登出**: `/sso/logout` - 处理SSO登出
- **状态检查**: `/sso/status` - 检查SSO状态
- **获取登录URL**: `/sso/loginUrl` - 获取SSO登录地址
- **用户信息**: `/sso/userinfo` - 获取当前用户信息
- **权限刷新**: `/sso/refresh` - 刷新用户权限信息

### 2. SSO客户端服务 (SSOClientService)

- **令牌交换**: 使用授权码换取访问令牌
- **会话管理**: 创建和管理本地用户会话
- **用户同步**: 从SSO获取用户信息并同步到本地
- **权限映射**: 将SSO权限映射到本地权限体系
- **状态检查**: 检查SSO服务状态

### 3. SSO客户端配置 (SSOClientConfig)

- **服务器配置**: SSO服务器地址和接口路径
- **客户端配置**: 客户端ID、密钥、回调地址等
- **RestTemplate配置**: HTTP客户端配置

## 配置说明

### application-sso.yml 配置项

```yaml
sso:
  server:
    url: http://localhost:9300          # SSO服务器地址
  client:
    id: backend                         # 客户端ID
    secret: "backend_2024#RuoYi@Share$Key!8888"  # 客户端密钥
    callback-url: http://localhost:9200/sso/callback  # 回调地址
```

## 集成步骤

### 1. 配置文件集成

将 `application-sso.yml` 的内容添加到主系统的配置文件中，或者通过 `@PropertySource` 注解引入。

### 2. 依赖检查

确保以下依赖已添加到项目中：
- Spring Web
- Spring Security
- Redis
- RestTemplate

### 3. 启动类配置

在启动类中添加组件扫描：

```java
@SpringBootApplication
@ComponentScan(basePackages = {"com.ruoyi"})
public class RuoyiAuthApplication {
    // ...
}
```

### 4. 安全配置

在Spring Security配置中添加SSO相关的路径排除：

```java
@Override
public void configure(WebSecurity web) throws Exception {
    web.ignoring().antMatchers("/sso/**");
}
```

## 使用方式

### 1. 前端跳转到SSO登录

```javascript
// 获取SSO登录地址
fetch('/sso/loginUrl?redirect=' + encodeURIComponent(window.location.href))
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      window.location.href = data.data.loginUrl;
    }
  });
```

### 2. 检查用户登录状态

```javascript
// 检查SSO状态
fetch('/sso/status')
  .then(response => response.json())
  .then(data => {
    if (data.code === 200 && data.data.isLogin) {
      console.log('用户已登录:', data.data.username);
    }
  });
```

### 3. 用户登出

```javascript
// SSO登出
fetch('/sso/logout', { method: 'POST' })
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      window.location.href = '/login';
    }
  });
```

## 权限映射

主系统支持以下权限映射：

### 管理员权限
- 系统用户管理：增删改查
- 角色管理：增删改查
- 菜单管理：增删改查
- 部门管理：增删改查

### 普通用户权限
- 系统用户查看
- 基础功能访问

## 注意事项

1. **客户端密钥安全**: 确保客户端密钥的安全性，不要在前端暴露
2. **HTTPS部署**: 生产环境建议使用HTTPS协议
3. **Token过期处理**: 实现Token过期自动刷新机制
4. **错误处理**: 完善SSO登录失败的错误处理逻辑
5. **日志记录**: 记录关键操作的日志用于审计

## 故障排查

### 常见问题

1. **回调地址不匹配**: 检查配置文件中的回调地址是否正确
2. **客户端密钥错误**: 验证客户端密钥是否与SSO服务配置一致
3. **网络连接问题**: 检查与SSO服务的网络连通性
4. **Token验证失败**: 检查Token格式和有效期

### 调试方法

1. 开启DEBUG日志级别查看详细信息
2. 使用浏览器开发者工具检查网络请求
3. 检查Redis中的缓存数据
4. 验证SSO服务的状态和配置

## 更新日志

- v1.0.0: 初始版本，支持基本的SSO登录和权限映射功能
