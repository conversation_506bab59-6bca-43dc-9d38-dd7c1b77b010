{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyDemand\\detail\\index1.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyDemand\\detail\\index1.vue", "mtime": 1750311963050}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpsZXQgaWQgPSAwOw0KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsgZ2V0RGVtYW5kRGV0YWlsLCBjcmVhdGVEZW1hbmQsIGVkaXREZW1hbmQgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGVtYW5kIjsNCmltcG9ydCB7IHVwbG9hZFVybCB9IGZyb20gIkAvYXBpL29zcyI7DQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7DQppbXBvcnQgc3RvcmUgZnJvbSAiQC9zdG9yZSI7DQppbXBvcnQgeyBrZXl3b3JkTGlzdDEgfSBmcm9tICJAL2FwaS96aG0iOw0KaW1wb3J0IENyeXB0b0pTIGZyb20gImNyeXB0by1qcyI7DQpsZXQgc2VjcmV0S2V5ID0gIjl6Vm4wJWJxbVVZU0d3Mm4iOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJOb3RpY2UiLA0KICBkaWN0czogWw0KICAgICJhZmZpbGlhdGVkX3VuaXQiLA0KICAgICJjYXBpdGFsX3NvdXJjZSIsDQogICAgImFmZmlsaWF0ZWRfc3RyZWV0IiwNCiAgICAiZGlzcGxheV9yZXN0cmljdGlvbnMiLA0KICBdLA0KICBjb21wb25lbnRzOiB7IFVzZXJNZW51IH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGlzRGV0YWlsOiB0cnVlLA0KICAgICAgdGl0bGU6ICLpnIDmsYLor6bmg4UiLA0KICAgICAgaW1hZ2VVcmw6ICIiLA0KICAgICAgYWN0aW9uVXJsOiB1cGxvYWRVcmwoKSwNCiAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKSB9LA0KICAgICAgdXBsb2FkVXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9maWxlL3VwbG9hZCIsIC8v5LiK5Lyg5Zyw5Z2ADQogICAgICBhY2NlcHQ6ICIuanBnLCAuanBlZywgLnBuZywgLmJtcCIsDQogICAgICBpc0NyZWF0ZTogZmFsc2UsDQogICAgICBpbWdWaXNpYmxlOiBmYWxzZSwNCiAgICAgIHVzZXI6IHsNCiAgICAgICAgdGVsOiBzdG9yZS5nZXR0ZXJzLnRlbCwNCiAgICAgICAgbmFtZTogc3RvcmUuZ2V0dGVycy5uYW1lLA0KICAgICAgICBjb21wYW55TmFtZTogc3RvcmUuZ2V0dGVycy5jb21wYW55TmFtZSwNCiAgICAgICAgYnVzc2luZXNzTm86IHN0b3JlLmdldHRlcnMuYnVzc2luZXNzTm8sDQogICAgICAgIHBob25lbnVtYmVyOiBzdG9yZS5nZXR0ZXJzLnBob25lbnVtYmVyLA0KICAgICAgfSwNCiAgICAgIGtleXdvcmRzOiBbXSwNCiAgICAgIGFwcGxpY2F0aW9uc0lucHV0OiAiIiwNCiAgICAgIGluZm86IHt9LA0KICAgICAgLy8g5bGV56S66ZmQ5Yi2DQogICAgICBkaXNwbGF5UmVzdHJpY3Rpb25zOiB1bmRlZmluZWQsDQogICAgICBmb3JtOiB7fSwNCiAgICAgIGFjY291bnRMaWNlbmNlTGlzdDogW10sDQogICAgICBsaXN0OiB7fSwNCiAgICAgIHRhYmxlRGF0YTogW10sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGRlbWFuZFRpdGxlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumcgOaxguagh+mimOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBkaXNwbGF5UmVzdHJpY3Rpb25zOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeWxleekuuaAp+i0qCIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBzdW1tYXJ5OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumcgOaxguaPj+i/sOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBjb250YWN0c05hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6IGU57O75Lq65LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvbXBhbnlOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWFrOWPuOWQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBjb250YWN0c01vYmlsZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLogZTns7vnlLXor53kuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmhhbmRsZUtleXdvcmRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBoYW5kbGVEZWxldGUoZGF0YSkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9kZW1hbmRIYWxsRGV0YWlsP2lkPSIgKyBkYXRhLmlkKTsNCiAgICB9LA0KICAgIGluaXRGb3JtKCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBhcHBsaWNhdGlvbkFyZWE6IFtdLA0KICAgICAgICBzY2VuZVBpY3R1cmU6IFtdLA0KICAgICAgICBhcHBsaWNhdGlvbkFyZWFMaXN0OiBbXSwNCiAgICAgICAgc2NlbmVQaWN0dXJlTGlzdDogW10sDQogICAgICAgIGtleXdvcmRzOiBbXSwNCiAgICAgICAgYXVkaXRTdGF0dXM6ICIxIiwNCiAgICAgICAgZGlzcGxheVN0YXR1czogIjIiLA0KICAgICAgICBwdWJsaXNoZXJOYW1lOiB0aGlzLnVzZXIubmFtZSwNCiAgICAgICAgcHVibGlzaGVyTW9iaWxlOiB0aGlzLnVzZXIudGVsLA0KICAgICAgfTsNCiAgICB9LA0KICAgIGdldERldGFpbCgpIHsNCiAgICAgIGxldCBpZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkOw0KICAgICAgZ2V0RGVtYW5kRGV0YWlsKGlkKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICBsZXQga2V5ID0gQ3J5cHRvSlMuZW5jLlV0ZjgucGFyc2Uoc2VjcmV0S2V5KTsNCiAgICAgICAgbGV0IGRlY3J5cHQgPSBDcnlwdG9KUy5BRVMuZGVjcnlwdChyZXNwb25zZSwga2V5LCB7DQogICAgICAgICAgbW9kZTogQ3J5cHRvSlMubW9kZS5FQ0IsDQogICAgICAgICAgcGFkZGluZzogQ3J5cHRvSlMucGFkLlBrY3M3LA0KICAgICAgICB9KTsNCiAgICAgICAgcmVzID0gSlNPTi5wYXJzZShDcnlwdG9KUy5lbmMuVXRmOC5zdHJpbmdpZnkoZGVjcnlwdCkpOw0KICAgICAgICB0aGlzLmluZm8gPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdvQmFjaygpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5nbygtMSk7DQogICAgfSwNCiAgICBnZXRVcmwoc3RyKSB7DQogICAgICBpZiAoc3RyICYmIHN0ciAhPSBudWxsKSB7DQogICAgICAgIHZhciBsaXN0ID0gSlNPTi5wYXJzZShzdHIpOw0KICAgICAgICBpZiAobGlzdCAmJiBsaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICByZXR1cm4gbGlzdFswXS51cmw7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIG51bGw7DQogICAgfSwNCiAgICAvLyDlupTnlKjpoobln5/mlrDlop4NCiAgICBoYW5kbGVJbnB1dENvbmZpcm0oKSB7DQogICAgICBsZXQgdmFsID0gdGhpcy5hcHBsaWNhdGlvbnNJbnB1dDsNCiAgICAgIGlmICh2YWwpIHsNCiAgICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uQXJlYUxpc3QucHVzaCh2YWwpOw0KICAgICAgfQ0KICAgICAgdGhpcy5hcHBsaWNhdGlvbnNJbnB1dCA9ICIiOw0KICAgIH0sDQogICAgLy8g5bqU55So6aKG5Z+f56e76ZmkDQogICAgaGFuZGxlQ2xvc2UodGFnKSB7DQogICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb25BcmVhTGlzdC5zcGxpY2UoDQogICAgICAgIHRoaXMuZm9ybS5hcHBsaWNhdGlvbkFyZWFMaXN0LmluZGV4T2YodGFnKSwNCiAgICAgICAgMQ0KICAgICAgKTsNCiAgICB9LA0KICAgIGhhbmRsZVN1bW1hcnlDbG9zZSh0YWcpIHsNCiAgICAgIHRoaXMuZm9ybS5rZXl3b3Jkcy5zcGxpY2UodGhpcy5mb3JtLmtleXdvcmRzLmluZGV4T2YodGFnKSwgMSk7DQogICAgfSwNCiAgICAvLyDkuqflk4HnhafniYfkuIrkvKDkuYvliY3nmoTpkqnlrZANCiAgICBoYW5kbGVCZWZvcmVVcGxvYWQoZmlsZSkgew0KICAgICAgbGV0IHsgbmFtZSwgdHlwZSwgc2l6ZSB9ID0gZmlsZTsNCiAgICAgIGxldCB0eXBlTGlzdCA9IHRoaXMuYWNjZXB0DQogICAgICAgIC5zcGxpdCgiLCIpDQogICAgICAgIC5tYXAoKGl0ZW0pID0+IGl0ZW0udHJpbSgpLnRvTG93ZXJDYXNlKCkuc3Vic3RyKDEpKTsNCiAgICAgIGxldCBkb3RJbmRleCA9IG5hbWUubGFzdEluZGV4T2YoIi4iKTsNCiAgICAgIC8vIOaWh+S7tuexu+Wei+agoemqjA0KICAgICAgaWYgKGRvdEluZGV4ID09PSAtMSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7fkuIrkvKDmraPnoa7moLzlvI/nmoTmlofku7YiKTsNCiAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgbGV0IHN1ZmZpeCA9IG5hbWUuc3Vic3RyaW5nKGRvdEluZGV4ICsgMSk7DQogICAgICAgIGlmICh0eXBlTGlzdC5pbmRleE9mKHN1ZmZpeC50b0xvd2VyQ2FzZSgpKSA9PT0gLTEpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7fkuIrkvKDmraPnoa7moLzlvI/nmoTmlofku7YiKTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC8vIOaWh+S7tuS4iuS8oOWkp+Wwj+mZkOWItg0KICAgICAgaWYgKHNpemUgPiAxMDQ4NTc2ICogMjApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5paH5Lu25aSn5bCP5LiN6IO96LaF6L+HMjBN77yBIik7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOeCueWHu+S6p+WTgeeFp+eJhw0KICAgIGhhbmRsZVBpY3R1cmVDYXJkUHJldmlldyhmaWxlKSB7DQogICAgICB0aGlzLmltYWdlVXJsID0gZmlsZS51cmw7DQogICAgICB0aGlzLmltZ1Zpc2libGUgPSB0cnVlOw0KICAgIH0sDQogICAgLy8g5Yig6Zmk5Lqn5ZOB54Wn54mHDQogICAgaGFuZGxlUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLmZvcm0uc2NlbmVQaWN0dXJlTGlzdCA9IGZpbGVMaXN0Ow0KICAgIH0sDQogICAgaGFuZGxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSkgew0KICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMjAwKSB7DQogICAgICAgIGlmICh0aGlzLmZvcm0uc2NlbmVQaWN0dXJlTGlzdCA9PSBudWxsKSB7DQogICAgICAgICAgdGhpcy5mb3JtLnNjZW5lUGljdHVyZUxpc3QgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmZvcm0uc2NlbmVQaWN0dXJlTGlzdC5wdXNoKHJlc3BvbnNlLmRhdGEpOw0KICAgICAgfQ0KICAgIH0sDQogICAgY2hhbmdlTW9kZSgpIHsNCiAgICAgIGlmICh0aGlzLmlzQ3JlYXRlKSB7DQogICAgICAgIHRoaXMuZ29CYWNrKCk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmlzRGV0YWlsKSB7DQogICAgICAgIHRoaXMudGl0bGUgPSAi57yW6L6R6ZyA5rGCIjsNCiAgICAgICAgdGhpcy5pc0RldGFpbCA9IGZhbHNlOw0KICAgICAgICB0aGlzLmZvcm0gPSB0aGlzLmluZm87DQogICAgICAgIGlmICh0aGlzLmluZm8uYXBwbGljYXRpb25BcmVhKSB7DQogICAgICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uQXJlYUxpc3QgPSB0aGlzLmluZm8uYXBwbGljYXRpb25BcmVhLnNwbGl0KCIsIik7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uQXJlYUxpc3QgPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5pbmZvLmtleXdvcmRzKSB7DQogICAgICAgICAgdGhpcy5mb3JtLmtleXdvcmRzID0gdGhpcy5pbmZvLmtleXdvcmRzLnNwbGl0KCIsIik7DQogICAgICAgIH0NCg0KICAgICAgICBpZiAodGhpcy5pbmZvLnNjZW5lUGljdHVyZSAmJiB0aGlzLmluZm8uc2NlbmVQaWN0dXJlICE9ICJudWxsIikgew0KICAgICAgICAgIHRoaXMuZm9ybS5zY2VuZVBpY3R1cmVMaXN0ID0gSlNPTi5wYXJzZSh0aGlzLmluZm8uc2NlbmVQaWN0dXJlKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmZvcm0uc2NlbmVQaWN0dXJlTGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmlzRGV0YWlsID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLpnIDmsYLor6bmg4UiOw0KICAgICAgICB0aGlzLmluaXRGb3JtKCk7DQogICAgICAgIHRoaXMuZ2V0RGV0YWlsKCk7DQogICAgICB9DQogICAgfSwNCiAgICBnb0NyZWF0ZSgpIHsNCiAgICAgIHRoaXMudGl0bGUgPSAi5paw5aKe6ZyA5rGCIjsNCiAgICAgIHRoaXMuaXNEZXRhaWwgPSBmYWxzZTsNCiAgICAgIHRoaXMuaW5pdEZvcm0oKTsNCiAgICAgIHRoaXMuZm9ybS5jb21wYW55TmFtZSA9IHRoaXMudXNlci5jb21wYW55TmFtZTsNCiAgICAgIHRoaXMuZm9ybS5jb250YWN0c05hbWUgPSB0aGlzLnVzZXIubmFtZTsNCiAgICAgIHRoaXMuZm9ybS5jb250YWN0c01vYmlsZSA9IHRoaXMudXNlci5waG9uZW51bWJlcjsNCiAgICAgIHRoaXMuZm9ybS5wdWJsaXNoZXJOYW1lID0gdGhpcy51c2VyLm5hbWU7DQogICAgICB0aGlzLmZvcm0ucHVibGlzaGVyTW9iaWxlID0gdGhpcy51c2VyLnBob25lbnVtYmVyOw0KICAgICAgdGhpcy5mb3JtLmJ1c2luZXNzTm8gPSB0aGlzLnVzZXIuYnVzc2luZXNzTm87DQogICAgfSwNCiAgICBoYW5kbGVGaWxlUHJldmlldyhmaWxlKSB7DQogICAgICB3aW5kb3cub3BlbihmaWxlKTsNCiAgICB9LA0KICAgIGRpc3BsYXlSZXN0cmljdGlvbkNoYW5nZWQocmVzKSB7DQogICAgICB0aGlzLmRpY3QudHlwZS5kaXNwbGF5X3Jlc3RyaWN0aW9ucy5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGlmIChpdGVtLmxhYmVsID09IHJlcykgew0KICAgICAgICAgIHRoaXMuZm9ybS5kaXNwbGF5UmVzdHJpY3Rpb25zID0gaXRlbS52YWx1ZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzdWJtaXRGb3JtKHR5cGUpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKA0KICAgICAgICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uQXJlYSAmJg0KICAgICAgICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uQXJlYS5sZW5ndGggPiAwDQogICAgICAgICAgKSB7DQogICAgICAgICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb25BcmVhID0gdGhpcy5mb3JtLmFwcGxpY2F0aW9uQXJlYUxpc3Quam9pbigiLCIpOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb25BcmVhID0gIiI7DQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuZm9ybS5zY2VuZVBpY3R1cmUgPSBKU09OLnN0cmluZ2lmeSh0aGlzLmZvcm0uc2NlbmVQaWN0dXJlTGlzdCk7DQogICAgICAgICAgdGhpcy5mb3JtLmJ1c2luZXNzTm8gPSB0aGlzLnVzZXIuYnVzc2luZXNzTm87DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5rZXl3b3JkcyAmJiB0aGlzLmZvcm0ua2V5d29yZHMubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtLmtleXdvcmRzID0gdGhpcy5mb3JtLmtleXdvcmRzLmpvaW4oIiwiKTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgdGhpcy5mb3JtLmtleXdvcmRzID0gIiI7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmICh0aGlzLmlzQ3JlYXRlKSB7DQogICAgICAgICAgICBjcmVhdGVEZW1hbmQoeyAuLi50aGlzLmZvcm0sIGlzU3VibWl0OiB0eXBlIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLmNoYW5nZU1vZGUoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLmZvcm0uYXVkaXRTdGF0dXMgPSAxOw0KICAgICAgICAgICAgZWRpdERlbWFuZCh7IC4uLnRoaXMuZm9ybSwgaXNTdWJtaXQ6IHR5cGUgfSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMuY2hhbmdlTW9kZSgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZUtleXdvcmRMaXN0KCkgew0KICAgICAgY29uc29sZS5sb2codGhpcy4kcm91dGUucXVlcnkua2V5KTsNCiAgICAgIGxldCBpbmZvID0gSlNPTi5wYXJzZSh0aGlzLiRyb3V0ZS5xdWVyeS5rZXkpOw0KICAgICAgdGhpcy5saXN0LmRlbWFuZFR5cGVOYW1lID0gaW5mby5kZW1hbmRUeXBlTmFtZTsNCiAgICAgIHRoaXMubGlzdC5zdW1tYXJ5ID0gaW5mby5zdW1tYXJ5Ow0KICAgICAgdGhpcy5saXN0LmFwcGxpY2F0aW9uQXJlYSA9IGluZm8uYXBwbGljYXRpb25BcmVhOw0KICAgICAga2V5d29yZExpc3QxKHRoaXMubGlzdCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIHRoaXMudGFibGVEYXRhID0gcmVzLnJvd3M7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZUFwcGxpY2F0aW9uUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb24gPSAiIjsNCiAgICB9LA0KICAgIGhhbmRsZUFwcGxpY2F0aW9uU3VjY2VzcyhyZXMsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICAvL+atpOWkhOWGmeS4iuS8oG9zc+aIkOWKn+S5i+WQjueahOmAu+i+kQ0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb24gPSByZXMuZGF0YS51cmw7DQogICAgICAgIHRoaXMuZm9ybS5hcHBsaWNhdGlvbk5hbWUgPSByZXMuZGF0YS5uYW1lOw0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlQWNjb3VudFJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5mb3JtLmFjY291bnRMaWNlbmNlID0gIiI7DQogICAgfSwNCiAgICBoYW5kbGVBY2NvdW50U3VjY2VzcyhyZXMsIGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICAvL+atpOWkhOWGmeS4iuS8oG9zc+aIkOWKn+S5i+WQjueahOmAu+i+kQ0KICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICB0aGlzLmZvcm0uYWNjb3VudExpY2VuY2UgPSByZXMuZGF0YS51cmw7DQogICAgICAgIHRoaXMuZm9ybS5hY2NvdW50TGljZW5jZU5hbWUgPSByZXMuZGF0YS5uYW1lOw0KICAgICAgfQ0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index1.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index1.vue", "sourceRoot": "src/views/system/user/companyDemand/detail", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-11 15:20:15\r\n * @LastEditTime: 2023-02-28 08:48:59\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-11 15:18:41\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"company-demand-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">匹配资源列表</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                资源列表\r\n              </div>\r\n            </div>\r\n            <el-table :data=\"tableData\" style=\"width: 100%\">\r\n              <el-table-column prop=\"companyName\" label=\"公司名称\" width=\"240\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"applicationArea\"\r\n                label=\"应用领域\"\r\n                width=\"180\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"summary\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  -webkit-line-clamp: 2;\r\n                  line-clamp: 2;\r\n                \"\r\n                label=\"描述\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"danger\"\r\n                    @click=\"handleDelete(scope.row)\"\r\n                    >查看详情</el-button\r\n                  >\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport { getDemandDetail, createDemand, editDemand } from \"@/api/system/demand\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport store from \"@/store\";\r\nimport { keywordList1 } from \"@/api/zhm\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\r\n    \"affiliated_unit\",\r\n    \"capital_source\",\r\n    \"affiliated_street\",\r\n    \"display_restrictions\",\r\n  ],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      title: \"需求详情\",\r\n      imageUrl: \"\",\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      isCreate: false,\r\n      imgVisible: false,\r\n      user: {\r\n        tel: store.getters.tel,\r\n        name: store.getters.name,\r\n        companyName: store.getters.companyName,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        phonenumber: store.getters.phonenumber,\r\n      },\r\n      keywords: [],\r\n      applicationsInput: \"\",\r\n      info: {},\r\n      // 展示限制\r\n      displayRestrictions: undefined,\r\n      form: {},\r\n      accountLicenceList: [],\r\n      list: {},\r\n      tableData: [],\r\n      // 表单校验\r\n      rules: {\r\n        demandTitle: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" },\r\n        ],\r\n        displayRestrictions: [\r\n          { required: true, message: \"请选择展示性质\", trigger: \"blur\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"需求描述不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.handleKeywordList();\r\n  },\r\n  methods: {\r\n    handleDelete(data) {\r\n      this.$router.push(\"/demandHallDetail?id=\" + data.id);\r\n    },\r\n    initForm() {\r\n      this.form = {\r\n        applicationArea: [],\r\n        scenePicture: [],\r\n        applicationAreaList: [],\r\n        scenePictureList: [],\r\n        keywords: [],\r\n        auditStatus: \"1\",\r\n        displayStatus: \"2\",\r\n        publisherName: this.user.name,\r\n        publisherMobile: this.user.tel,\r\n      };\r\n    },\r\n    getDetail() {\r\n      let id = this.$route.query.id;\r\n      getDemandDetail(id).then((response) => {\r\n        let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n        let decrypt = CryptoJS.AES.decrypt(response, key, {\r\n          mode: CryptoJS.mode.ECB,\r\n          padding: CryptoJS.pad.Pkcs7,\r\n        });\r\n        res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    getUrl(str) {\r\n      if (str && str != null) {\r\n        var list = JSON.parse(str);\r\n        if (list && list.length > 0) {\r\n          return list[0].url;\r\n        }\r\n      }\r\n\r\n      return null;\r\n    },\r\n    // 应用领域新增\r\n    handleInputConfirm() {\r\n      let val = this.applicationsInput;\r\n      if (val) {\r\n        this.form.applicationAreaList.push(val);\r\n      }\r\n      this.applicationsInput = \"\";\r\n    },\r\n    // 应用领域移除\r\n    handleClose(tag) {\r\n      this.form.applicationAreaList.splice(\r\n        this.form.applicationAreaList.indexOf(tag),\r\n        1\r\n      );\r\n    },\r\n    handleSummaryClose(tag) {\r\n      this.form.keywords.splice(this.form.keywords.indexOf(tag), 1);\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePictureCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.form.scenePictureList = fileList;\r\n    },\r\n    handleSuccess(response, file) {\r\n      if (response.code == 200) {\r\n        if (this.form.scenePictureList == null) {\r\n          this.form.scenePictureList = [];\r\n        }\r\n        this.form.scenePictureList.push(response.data);\r\n      }\r\n    },\r\n    changeMode() {\r\n      if (this.isCreate) {\r\n        this.goBack();\r\n        return;\r\n      }\r\n      if (this.isDetail) {\r\n        this.title = \"编辑需求\";\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n        if (this.info.applicationArea) {\r\n          this.form.applicationAreaList = this.info.applicationArea.split(\",\");\r\n        } else {\r\n          this.form.applicationAreaList = [];\r\n        }\r\n        if (this.info.keywords) {\r\n          this.form.keywords = this.info.keywords.split(\",\");\r\n        }\r\n\r\n        if (this.info.scenePicture && this.info.scenePicture != \"null\") {\r\n          this.form.scenePictureList = JSON.parse(this.info.scenePicture);\r\n        } else {\r\n          this.form.scenePictureList = [];\r\n        }\r\n      } else {\r\n        this.isDetail = true;\r\n        this.title = \"需求详情\";\r\n        this.initForm();\r\n        this.getDetail();\r\n      }\r\n    },\r\n    goCreate() {\r\n      this.title = \"新增需求\";\r\n      this.isDetail = false;\r\n      this.initForm();\r\n      this.form.companyName = this.user.companyName;\r\n      this.form.contactsName = this.user.name;\r\n      this.form.contactsMobile = this.user.phonenumber;\r\n      this.form.publisherName = this.user.name;\r\n      this.form.publisherMobile = this.user.phonenumber;\r\n      this.form.businessNo = this.user.bussinessNo;\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    displayRestrictionChanged(res) {\r\n      this.dict.type.display_restrictions.forEach((item) => {\r\n        if (item.label == res) {\r\n          this.form.displayRestrictions = item.value;\r\n        }\r\n      });\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (\r\n            this.form.applicationArea &&\r\n            this.form.applicationArea.length > 0\r\n          ) {\r\n            this.form.applicationArea = this.form.applicationAreaList.join(\",\");\r\n          } else {\r\n            this.form.applicationArea = \"\";\r\n          }\r\n          this.form.scenePicture = JSON.stringify(this.form.scenePictureList);\r\n          this.form.businessNo = this.user.bussinessNo;\r\n          if (this.form.keywords && this.form.keywords.length > 0) {\r\n            this.form.keywords = this.form.keywords.join(\",\");\r\n          } else {\r\n            this.form.keywords = \"\";\r\n          }\r\n          if (this.isCreate) {\r\n            createDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          } else {\r\n            this.form.auditStatus = 1;\r\n            editDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      console.log(this.$route.query.key);\r\n      let info = JSON.parse(this.$route.query.key);\r\n      this.list.demandTypeName = info.demandTypeName;\r\n      this.list.summary = info.summary;\r\n      this.list.applicationArea = info.applicationArea;\r\n      keywordList1(this.list).then((res) => {\r\n        this.tableData = res.rows;\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.application = res.data.url;\r\n        this.form.applicationName = res.data.name;\r\n      }\r\n    },\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.el-table .cell {\r\n  display: -webkit-box; /* 将元素设为一个弹性伸缩盒子 */\r\n  -webkit-box-orient: vertical; /* 设定元素为垂直方向 */\r\n  overflow: hidden; /* 隐藏超出部分 */\r\n  text-overflow: ellipsis; /* 超出部分用省略号(...)表示 */\r\n  -webkit-line-clamp: 2; /* 限制文本显示2行 */\r\n  line-clamp: 2;\r\n}\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .company-demand-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 20px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .mt_40 {\r\n          margin-top: 40px;\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 90%;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-textarea__inner {\r\n          width: 90%;\r\n        }\r\n        .add-demand-tag {\r\n          margin-right: 10px;\r\n          height: 32px;\r\n          line-height: 32px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n          color: #333;\r\n        }\r\n        .el-button:hover,\r\n        .el-button:focus {\r\n          border-color: #d9d9d9;\r\n          background-color: #fff;\r\n        }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8 !important;\r\n          border-color: #21c9b8 !important;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}