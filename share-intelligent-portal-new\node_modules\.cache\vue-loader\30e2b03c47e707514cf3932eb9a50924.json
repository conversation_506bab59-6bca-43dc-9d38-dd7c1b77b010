{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\BackTopper\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\BackTopper\\index.vue", "mtime": 1750311962788}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgdGhyb3R0bGUgZnJvbSAidGhyb3R0bGUtZGVib3VuY2UvdGhyb3R0bGUiOw0KDQpjb25zdCBjdWJpYyA9ICh2YWx1ZSkgPT4gTWF0aC5wb3codmFsdWUsIDMpOw0KY29uc3QgZWFzZUluT3V0Q3ViaWMgPSAodmFsdWUpID0+DQogIHZhbHVlIDwgMC41ID8gY3ViaWModmFsdWUgKiAyKSAvIDIgOiAxIC0gY3ViaWMoKDEgLSB2YWx1ZSkgKiAyKSAvIDI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkVsQmFja3RvcCIsDQoNCiAgcHJvcHM6IHsNCiAgICB2aXNpYmlsaXR5SGVpZ2h0OiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAyMDAsDQogICAgfSwNCiAgICB0YXJnZXQ6IFtTdHJpbmddLA0KICB9LA0KDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGVsOiBudWxsLA0KICAgICAgY29udGFpbmVyOiBudWxsLA0KICAgICAgdmlzaWJsZTogdHJ1ZSwNCiAgICB9Ow0KICB9LA0KDQogIG1vdW50ZWQoKSB7DQogICAgdGhpcy5pbml0KCk7DQogICAgLy8gdGhpcy50aHJvdHRsZWRTY3JvbGxIYW5kbGVyID0gdGhyb3R0bGUoMzAwLCB0aGlzLm9uU2Nyb2xsKTsNCiAgICAvLyB0aGlzLmNvbnRhaW5lci5hZGRFdmVudExpc3RlbmVyKCJzY3JvbGwiLCB0aGlzLnRocm90dGxlZFNjcm9sbEhhbmRsZXIpOw0KICB9LA0KDQogIG1ldGhvZHM6IHsNCiAgICBpbml0KCkgew0KICAgICAgdGhpcy5jb250YWluZXIgPSBkb2N1bWVudDsNCiAgICAgIHRoaXMuZWwgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQ7DQogICAgICBpZiAodGhpcy50YXJnZXQpIHsNCiAgICAgICAgdGhpcy5lbCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IodGhpcy50YXJnZXQpOw0KICAgICAgICBpZiAoIXRoaXMuZWwpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYHRhcmdldCBpcyBub3QgZXhpc3RlZDogJHt0aGlzLnRhcmdldH1gKTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmNvbnRhaW5lciA9IHRoaXMuZWw7DQogICAgICB9DQogICAgfSwNCiAgICBvblNjcm9sbCgpIHsNCiAgICAgIGNvbnN0IHNjcm9sbFRvcCA9IHRoaXMuZWwuc2Nyb2xsVG9wOw0KICAgICAgdGhpcy52aXNpYmxlID0gc2Nyb2xsVG9wID49IHRoaXMudmlzaWJpbGl0eUhlaWdodDsNCiAgICB9LA0KICAgIGhhbmRsZUNsaWNrKGUpIHsNCiAgICAgIHRoaXMuc2Nyb2xsVG9Ub3AoKTsNCiAgICAgIHRoaXMuJGVtaXQoImNsaWNrIiwgZSk7DQogICAgfSwNCiAgICBzY3JvbGxUb1RvcCgpIHsNCiAgICAgIGNvbnN0IGVsID0gdGhpcy5lbDsNCiAgICAgIGNvbnN0IGJlZ2luVGltZSA9IERhdGUubm93KCk7DQogICAgICBjb25zdCBiZWdpblZhbHVlID0gZWwuc2Nyb2xsVG9wOw0KICAgICAgY29uc3QgckFGID0NCiAgICAgICAgd2luZG93LnJlcXVlc3RBbmltYXRpb25GcmFtZSB8fCAoKGZ1bmMpID0+IHNldFRpbWVvdXQoZnVuYywgMTYpKTsNCiAgICAgIGNvbnN0IGZyYW1lRnVuYyA9ICgpID0+IHsNCiAgICAgICAgY29uc3QgcHJvZ3Jlc3MgPSAoRGF0ZS5ub3coKSAtIGJlZ2luVGltZSkgLyA1MDA7DQogICAgICAgIGlmIChwcm9ncmVzcyA8IDEpIHsNCiAgICAgICAgICBlbC5zY3JvbGxUb3AgPSBiZWdpblZhbHVlICogKDEgLSBlYXNlSW5PdXRDdWJpYyhwcm9ncmVzcykpOw0KICAgICAgICAgIHJBRihmcmFtZUZ1bmMpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGVsLnNjcm9sbFRvcCA9IDA7DQogICAgICAgIH0NCiAgICAgIH07DQogICAgICByQUYoZnJhbWVGdW5jKTsNCiAgICB9LA0KICAgIGdvUHVibGlzaCgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgcGF0aDogIi9yZWxlYXNlIiwNCiAgICAgIH0pOw0KICAgIH0sDQogIH0sDQoNCiAgYmVmb3JlRGVzdHJveSgpIHsNCiAgICB0aGlzLmNvbnRhaW5lci5yZW1vdmVFdmVudExpc3RlbmVyKCJzY3JvbGwiLCB0aGlzLnRocm90dGxlZFNjcm9sbEhhbmRsZXIpOw0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyQA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/BackTopper", "sourcesContent": ["<template>\r\n  <transition name=\"el-fade-in\">\r\n    <div v-show=\"visible\" class=\"left-bar\">\r\n      <div class=\"left-bar-item create\" @click=\"goPublish\">\r\n        <svg\r\n          class=\"icon\"\r\n          viewBox=\"0 0 16 15\"\r\n          version=\"1.1\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          xmlns:xlink=\"http://www.w3.org/1999/xlink\"\r\n        >\r\n          <title>发布需求</title>\r\n          <g\r\n            id=\"Web\"\r\n            stroke=\"none\"\r\n            stroke-width=\"1\"\r\n            fill=\"none\"\r\n            fill-rule=\"evenodd\"\r\n          >\r\n            <g\r\n              id=\"首页-1备份-5\"\r\n              transform=\"translate(-1872.000000, -894.000000)\"\r\n              fill=\"#666666\"\r\n              fill-rule=\"nonzero\"\r\n            >\r\n              <g id=\"编组-14\" transform=\"translate(1840.000000, 792.000000)\">\r\n                <g id=\"发布供需0\" transform=\"translate(32.000000, 102.000000)\">\r\n                  <path\r\n                    d=\"M8.38940592,15 L2.05523449,15 C0.903672664,14.9764217 -0.0133456064,14.0529692 0.000147765112,12.9304939 L0.000147765112,2.07342565 C-0.0133756596,0.950280288 0.903032382,0.0256945182 2.05523449,0 L12.6805557,0 C13.8412914,0.0150609322 14.7709522,0.94213234 14.7598724,2.07342565 L14.7598724,6.2241965 C14.7597162,6.54886123 14.4896466,6.81202487 14.1565182,6.81202487 C13.8233899,6.81202487 13.5533202,6.54886123 13.5532638,6.2241965 L13.5532638,2.07342565 C13.5597503,1.84158899 13.4714289,1.61674452 13.3077462,1.44839883 C13.1440635,1.28005313 12.9184407,1.18200966 12.6805557,1.17585576 L2.05523449,1.17585576 C1.56938668,1.20118186 1.19287132,1.59943505 1.2066566,2.07342565 L1.2066566,12.9304939 C1.20016967,13.1612911 1.28806726,13.3851388 1.4509958,13.5527496 C1.61392435,13.7203603 1.83852445,13.8179907 2.07534297,13.8241442 L8.38940592,13.8241442 C8.72257412,13.8241443 8.99266031,14.0873685 8.99266031,14.4120721 C8.99266031,14.7367757 8.72257412,15 8.38940592,15 Z\"\r\n                    id=\"路径\"\r\n                  ></path>\r\n                  <path\r\n                    d=\"M11.260897,14.8510642 C10.9996538,14.8520458 10.7811726,14.6578533 10.758185,14.4042331 C10.6335124,12.8991377 10.6335124,12.8991377 10.7260114,12.7462765 L13.629676,8.04285341 C13.6968635,7.93237473 13.8071512,7.85317475 13.9353249,7.82336033 C14.0780496,7.78639325 14.2297299,7.80173375 14.3616247,7.86647504 L15.7531316,8.67781552 C15.8682548,8.74408854 15.9509475,8.85295812 15.9823683,8.9796185 L15.9823683,9.0031356 C16.017972,9.1354369 15.999215,9.27605555 15.9300862,9.39508754 L13.0304433,14.0985106 C12.9299009,14.2592109 12.9299009,14.2592109 11.445895,14.8157826 C11.3872384,14.839378 11.3243528,14.8513691 11.260897,14.8510642 L11.260897,14.8510642 Z M11.6630666,13.1264698 C11.6630666,13.2479749 11.6630666,13.4439509 11.7032836,13.6673635 L12.2462125,13.459629 L14.8200981,9.29709955 L14.2409738,8.95610138 L11.6630666,13.1264698 Z M10.6576425,5.09537497 L4.07814765,5.09537497 C3.74497943,5.09537497 3.47489323,4.83215069 3.47489323,4.50744709 C3.47489323,4.18274349 3.74497943,3.91951921 4.07814765,3.91951921 L10.6576425,3.91951921 C10.9908108,3.91951921 11.260897,4.18274349 11.260897,4.50744709 C11.260897,4.83215069 10.9908108,5.09537497 10.6576425,5.09537497 Z M9.10124615,7.96446302 L4.07814765,7.96446302 C3.74497943,7.96446302 3.47489323,7.70123874 3.47489323,7.37653514 C3.47489323,7.05183153 3.74497943,6.78860726 4.07814765,6.78860726 L9.10124615,6.78860726 C9.43441437,6.78860726 9.70450057,7.05183153 9.70450057,7.37653514 C9.70450057,7.70123874 9.43441437,7.96446302 9.10124615,7.96446302 Z M7.64137045,10.8296316 L4.07814765,10.8296316 C3.74497943,10.8296316 3.47489323,10.5664073 3.47489323,10.2417037 C3.47489323,9.91700008 3.74497943,9.65377581 4.07814765,9.65377581 L7.64137045,9.65377581 C7.97453867,9.65377581 8.24462487,9.91700008 8.24462487,10.2417037 C8.24462487,10.5664073 7.97453867,10.8296316 7.64137045,10.8296316 L7.64137045,10.8296316 Z\"\r\n                    id=\"形状\"\r\n                  ></path>\r\n                </g>\r\n              </g>\r\n            </g>\r\n          </g>\r\n        </svg>\r\n        <span class=\"text\">需求定制</span>\r\n        <!-- <div class=\"add-menu\">\r\n          <router-link to=\"/addSource?type=demand\" class=\"add-menu-item\">\r\n            <svg\r\n              class=\"add-menu-item-icon\"\r\n              viewBox=\"0 0 18 18\"\r\n              version=\"1.1\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              xmlns:xlink=\"http://www.w3.org/1999/xlink\"\r\n            >\r\n              <title>发布需求</title>\r\n              <g\r\n                id=\"Web\"\r\n                stroke=\"none\"\r\n                stroke-width=\"1\"\r\n                fill=\"none\"\r\n                fill-rule=\"evenodd\"\r\n              >\r\n                <g\r\n                  id=\"首页-1备份-5\"\r\n                  transform=\"translate(-1744.000000, -886.000000)\"\r\n                  fill=\"#666\"\r\n                  fill-rule=\"nonzero\"\r\n                >\r\n                  <g\r\n                    id=\"编组-14备份\"\r\n                    transform=\"translate(1732.000000, 872.000000)\"\r\n                  >\r\n                    <g\r\n                      id=\"thunderbolt\"\r\n                      transform=\"translate(12.000000, 14.000000)\"\r\n                    >\r\n                      <rect\r\n                        id=\"矩形\"\r\n                        fill=\"#000000\"\r\n                        opacity=\"0\"\r\n                        x=\"0\"\r\n                        y=\"0\"\r\n                        width=\"18\"\r\n                        height=\"18\"\r\n                      ></rect>\r\n                      <path\r\n                        d=\"M15.5625,6.01757813 L11.2597656,6.01757813 L15.1289063,1.12890625 C15.2089844,1.02539062 15.1367188,0.875 15.0058594,0.875 L7.515625,0.875 C7.4609375,0.875 7.40820313,0.904296875 7.38085938,0.953125 L2.3203125,9.69335938 C2.25976563,9.796875 2.33398438,9.92773438 2.45507813,9.92773438 L5.86132813,9.92773438 L4.11523438,16.9121094 C4.078125,17.0644531 4.26171875,17.171875 4.375,17.0625 L15.6699219,6.28515625 C15.7714844,6.18945313 15.703125,6.01757813 15.5625,6.01757813 Z M6.38671875,13.3066406 L7.56445312,8.59960938 L4.49023438,8.59960938 L8.19335938,2.20507812 L12.5800781,2.20507812 L8.51171875,7.34765625 L12.6328125,7.34765625 L6.38671875,13.3066406 Z\"\r\n                        id=\"形状\"\r\n                      ></path>\r\n                    </g>\r\n                  </g>\r\n                </g>\r\n              </g>\r\n            </svg>\r\n            <span class=\"add-menu-item-text\">发布需求</span>\r\n          </router-link>\r\n          <router-link to=\"/addSource?type=supply\" class=\"add-menu-item\">\r\n            <svg\r\n              class=\"add-menu-item-icon\"\r\n              viewBox=\"0 0 18 18\"\r\n              version=\"1.1\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n              xmlns:xlink=\"http://www.w3.org/1999/xlink\"\r\n            >\r\n              <title>发布资源</title>\r\n              <g\r\n                id=\"Web\"\r\n                stroke=\"none\"\r\n                stroke-width=\"1\"\r\n                fill=\"none\"\r\n                fill-rule=\"evenodd\"\r\n              >\r\n                <g\r\n                  id=\"首页-1备份-5\"\r\n                  transform=\"translate(-1744.000000, -928.000000)\"\r\n                  fill-rule=\"nonzero\"\r\n                >\r\n                  <g\r\n                    id=\"编组-14备份\"\r\n                    transform=\"translate(1732.000000, 872.000000)\"\r\n                  >\r\n                    <g\r\n                      id=\"experiment\"\r\n                      transform=\"translate(12.000000, 56.000000)\"\r\n                    >\r\n                      <rect\r\n                        id=\"矩形\"\r\n                        fill=\"#000000\"\r\n                        opacity=\"0\"\r\n                        x=\"0\"\r\n                        y=\"0\"\r\n                        width=\"18\"\r\n                        height=\"18\"\r\n                      ></rect>\r\n                      <path\r\n                        d=\"M9,8.21875 C9,8.65022246 9.34977754,9 9.78125,9 C10.2127225,9 10.5625,8.65022246 10.5625,8.21875 C10.5625,7.78727754 10.2127225,7.4375 9.78125,7.4375 C9.34977754,7.4375 9,7.78727754 9,8.21875 Z M16.1679688,15.1113281 L12.5996094,5.875 L12.5996094,2.4765625 L14,2.4765625 L14,1.1484375 L4,1.1484375 L4,2.4765625 L5.40039062,2.4765625 L5.40039062,5.875 L1.83203125,15.1113281 C1.77734375,15.2558594 1.74804688,15.4082031 1.74804688,15.5625 C1.74804688,16.2519531 2.30859375,16.8125 2.99804688,16.8125 L15.0019531,16.8125 C15.15625,16.8125 15.3085937,16.7832031 15.453125,16.7285156 C16.0976562,16.4804688 16.4179687,15.7558594 16.1679688,15.1113281 Z M6.72851562,6.12304688 L6.72851562,2.515625 L11.2714844,2.515625 L11.2714844,6.12304688 L13.046875,10.71875 C12.6425781,10.6152344 12.2246094,10.5625 11.7988281,10.5625 C10.6035156,10.5625 9.47070312,10.9824219 8.5703125,11.734375 C7.90820312,12.2851563 7.08007813,12.5917969 6.20117188,12.5917969 C5.5625,12.5917969 4.94921875,12.4296875 4.40820312,12.1289062 L6.72851562,6.12304688 Z M3.11132812,15.484375 L3.92578125,13.3789062 C4.62304688,13.7324219 5.3984375,13.921875 6.203125,13.921875 C7.3984375,13.921875 8.53125,13.5019531 9.43164062,12.75 C10.09375,12.1992187 10.921875,11.8925781 11.8007812,11.8925781 C12.484375,11.8925781 13.1367187,12.078125 13.7070312,12.421875 L14.8886719,15.484375 L3.11132812,15.484375 Z\"\r\n                        id=\"形状\"\r\n                        fill=\"#666666\"\r\n                      ></path>\r\n                    </g>\r\n                  </g>\r\n                </g>\r\n              </g>\r\n            </svg>\r\n            <span class=\"add-menu-item-text\">发布资源</span>\r\n          </router-link>\r\n        </div> -->\r\n      </div>\r\n      <div @click.stop=\"handleClick\" class=\"left-bar-item\">\r\n        <svg\r\n          class=\"icon\"\r\n          viewBox=\"0 0 16 16\"\r\n          version=\"1.1\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          xmlns:xlink=\"http://www.w3.org/1999/xlink\"\r\n        >\r\n          <title>返回顶部</title>\r\n          <g\r\n            id=\"Web\"\r\n            stroke=\"none\"\r\n            stroke-width=\"1\"\r\n            fill=\"none\"\r\n            fill-rule=\"evenodd\"\r\n          >\r\n            <g\r\n              id=\"首页-1备份-5\"\r\n              transform=\"translate(-1872.000000, -813.000000)\"\r\n              fill=\"#666666\"\r\n              fill-rule=\"nonzero\"\r\n            >\r\n              <g id=\"编组-14\" transform=\"translate(1840.000000, 792.000000)\">\r\n                <g id=\"返回顶部\" transform=\"translate(32.000000, 21.000000)\">\r\n                  <polygon\r\n                    id=\"路径\"\r\n                    points=\"0 0 16 0 16 1.23105499 4.72056094e-16 1.23105499\"\r\n                  ></polygon>\r\n                  <polygon\r\n                    id=\"路径\"\r\n                    points=\"8.87019131 2.694432 8.0000085 1.824032 7.12980871 2.694432 1.47296613 8.35257601 2.34314893 9.222976 7.38461408 4.180351 7.38461408 16 8.61538594 16 8.61538594 4.180351 13.6568511 9.222976 14.5270509 8.35257601\"\r\n                  ></polygon>\r\n                </g>\r\n              </g>\r\n            </g>\r\n          </g>\r\n        </svg>\r\n        <span class=\"text\">返回顶部</span>\r\n      </div>\r\n      <!-- <div class=\"left-bar-item qrcode\">\r\n        <svg\r\n          class=\"icon\"\r\n          width=\"14px\"\r\n          height=\"16px\"\r\n          viewBox=\"0 0 14 16\"\r\n          version=\"1.1\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n          xmlns:xlink=\"http://www.w3.org/1999/xlink\"\r\n        >\r\n          <title>产品入口1</title>\r\n          <g\r\n            id=\"Web\"\r\n            stroke=\"none\"\r\n            stroke-width=\"1\"\r\n            fill=\"none\"\r\n            fill-rule=\"evenodd\"\r\n          >\r\n            <g\r\n              id=\"首页-1备份-5\"\r\n              transform=\"translate(-1873.000000, -973.000000)\"\r\n              fill=\"#666666\"\r\n              fill-rule=\"nonzero\"\r\n            >\r\n              <g id=\"编组-14\" transform=\"translate(1840.000000, 792.000000)\">\r\n                <g id=\"产品入口1\" transform=\"translate(33.125000, 181.187500)\">\r\n                  <path\r\n                    d=\"M13.125,3.90625 L10.46875,3.90625 L10.46875,3.59375 C10.46875,1.609375 8.859375,0 6.875,0 C4.890625,0 3.28125,1.609375 3.28125,3.59375 L3.28125,3.90625 L0.625,3.90625 C0.279296875,3.90625 0,4.18554688 0,4.53125 L0,15 C0,15.3457031 0.279296875,15.625 0.625,15.625 L13.125,15.625 C13.4707031,15.625 13.75,15.3457031 13.75,15 L13.75,4.53125 C13.75,4.18554688 13.4707031,3.90625 13.125,3.90625 Z M4.6875,3.59375 C4.6875,2.38476562 5.66601562,1.40625 6.875,1.40625 C8.08398438,1.40625 9.0625,2.38476562 9.0625,3.59375 L9.0625,3.90625 L4.6875,3.90625 L4.6875,3.59375 Z M12.34375,14.21875 L1.40625,14.21875 L1.40625,5.3125 L3.28125,5.3125 L3.28125,7.03125 C3.28125,7.1171875 3.3515625,7.1875 3.4375,7.1875 L4.53125,7.1875 C4.6171875,7.1875 4.6875,7.1171875 4.6875,7.03125 L4.6875,5.3125 L9.0625,5.3125 L9.0625,7.03125 C9.0625,7.1171875 9.1328125,7.1875 9.21875,7.1875 L10.3125,7.1875 C10.3984375,7.1875 10.46875,7.1171875 10.46875,7.03125 L10.46875,5.3125 L12.34375,5.3125 L12.34375,14.21875 Z\"\r\n                    id=\"形状\"\r\n                  ></path>\r\n                </g>\r\n              </g>\r\n            </g>\r\n          </g>\r\n        </svg>\r\n        <span class=\"text\">产品入口</span>\r\n        <div class=\"qrcode-menu\">\r\n          <div class=\"qrcode-menu-content\">\r\n            <div class=\"qrcode-el\">\r\n              <el-image\r\n                style=\"width: 80px; height: 80px\"\r\n                :src=\"require('@/assets/user/wait.png')\"\r\n                fit=\"fill\"\r\n              />\r\n              <div class=\"qrcode-el-title\">门户端</div>\r\n              <div class=\"qrcode-el-desc\">政策服务/申报/数据掌握</div>\r\n            </div>\r\n            <div class=\"qrcode-el\">\r\n              <el-image\r\n                style=\"width: 80px; height: 80px\"\r\n                :src=\"require('@/assets/user/company_mini.png')\"\r\n                fit=\"fill\"\r\n              />\r\n              <div class=\"qrcode-el-title\">企业端-云端研发</div>\r\n              <div class=\"qrcode-el-desc\">研发/采购/销售/政策/服务</div>\r\n            </div>\r\n            <div class=\"qrcode-el\">\r\n              <el-image\r\n                style=\"width: 80px; height: 80px\"\r\n                :src=\"require('@/assets/user/resource_mini.png')\"\r\n                fit=\"fill\"\r\n              />\r\n              <div class=\"qrcode-el-title\">资源端</div>\r\n              <div class=\"qrcode-el-desc\">供应商/服务商/专家</div>\r\n            </div>\r\n            <div class=\"qrcode-el\">\r\n              <el-image\r\n                style=\"width: 80px; height: 80px\"\r\n                :src=\"require('@/assets/user/gov_mini.png')\"\r\n                fit=\"fill\"\r\n              />\r\n              <div class=\"qrcode-el-title\">政府端</div>\r\n              <div class=\"qrcode-el-desc\">政策服务/申报/数据掌握</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n  </transition>\r\n</template>\r\n\r\n<script>\r\nimport throttle from \"throttle-debounce/throttle\";\r\n\r\nconst cubic = (value) => Math.pow(value, 3);\r\nconst easeInOutCubic = (value) =>\r\n  value < 0.5 ? cubic(value * 2) / 2 : 1 - cubic((1 - value) * 2) / 2;\r\n\r\nexport default {\r\n  name: \"ElBacktop\",\r\n\r\n  props: {\r\n    visibilityHeight: {\r\n      type: Number,\r\n      default: 200,\r\n    },\r\n    target: [String],\r\n  },\r\n\r\n  data() {\r\n    return {\r\n      el: null,\r\n      container: null,\r\n      visible: true,\r\n    };\r\n  },\r\n\r\n  mounted() {\r\n    this.init();\r\n    // this.throttledScrollHandler = throttle(300, this.onScroll);\r\n    // this.container.addEventListener(\"scroll\", this.throttledScrollHandler);\r\n  },\r\n\r\n  methods: {\r\n    init() {\r\n      this.container = document;\r\n      this.el = document.documentElement;\r\n      if (this.target) {\r\n        this.el = document.querySelector(this.target);\r\n        if (!this.el) {\r\n          throw new Error(`target is not existed: ${this.target}`);\r\n        }\r\n        this.container = this.el;\r\n      }\r\n    },\r\n    onScroll() {\r\n      const scrollTop = this.el.scrollTop;\r\n      this.visible = scrollTop >= this.visibilityHeight;\r\n    },\r\n    handleClick(e) {\r\n      this.scrollToTop();\r\n      this.$emit(\"click\", e);\r\n    },\r\n    scrollToTop() {\r\n      const el = this.el;\r\n      const beginTime = Date.now();\r\n      const beginValue = el.scrollTop;\r\n      const rAF =\r\n        window.requestAnimationFrame || ((func) => setTimeout(func, 16));\r\n      const frameFunc = () => {\r\n        const progress = (Date.now() - beginTime) / 500;\r\n        if (progress < 1) {\r\n          el.scrollTop = beginValue * (1 - easeInOutCubic(progress));\r\n          rAF(frameFunc);\r\n        } else {\r\n          el.scrollTop = 0;\r\n        }\r\n      };\r\n      rAF(frameFunc);\r\n    },\r\n    goPublish() {\r\n      this.$router.push({\r\n        path: \"/release\",\r\n      });\r\n    },\r\n  },\r\n\r\n  beforeDestroy() {\r\n    this.container.removeEventListener(\"scroll\", this.throttledScrollHandler);\r\n  },\r\n};\r\n</script>\r\n<style scoped lang=\"scss\">\r\n@import \"~@/assets/styles/mixin.scss\";\r\n.left-bar {\r\n  position: fixed;\r\n  right: 0;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  width: 80px;\r\n  background: #ffffff;\r\n  box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.2);\r\n  z-index: 999;\r\n  &-item {\r\n    @include flexCenter;\r\n    flex-direction: column;\r\n    position: relative;\r\n    width: 100%;\r\n    height: 80px;\r\n    cursor: pointer;\r\n    background-color: #ffffff;\r\n    .icon {\r\n      width: 16px;\r\n      height: 16px;\r\n      object-fit: contain;\r\n      margin-bottom: 8px;\r\n    }\r\n    .text {\r\n      height: 14px;\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #666666;\r\n      line-height: 14px;\r\n    }\r\n    .add-menu {\r\n      display: none;\r\n      position: absolute;\r\n      right: 80px;\r\n      top: 2.5px;\r\n      width: 108px;\r\n      box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.2);\r\n      z-index: 10;\r\n      padding-right: 4px;\r\n      &-item {\r\n        @include flexCenter;\r\n        flex-shrink: 0;\r\n        width: 100%;\r\n        height: 38px;\r\n        background: #ffffff;\r\n        &-icon {\r\n          width: 18px;\r\n          height: 18px;\r\n          margin-right: 6px;\r\n        }\r\n        &-text {\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #666666;\r\n          line-height: 14px;\r\n        }\r\n        &:hover {\r\n          background-color: #21c9b8;\r\n          .add-menu-item-icon {\r\n            path {\r\n              fill: #ffffff;\r\n            }\r\n          }\r\n          .add-menu-item-text {\r\n            color: #ffffff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .qrcode-menu {\r\n      display: none;\r\n      position: absolute;\r\n      right: 80px;\r\n      top: -160px;\r\n      width: 160px;\r\n      box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.2);\r\n      z-index: 10;\r\n      padding-right: 4px;\r\n      &-content {\r\n        @include flexCenter;\r\n        flex-direction: column;\r\n        flex-shrink: 0;\r\n        width: 100%;\r\n        background: #ffffff;\r\n        padding: 12px 5px;\r\n        .qrcode-el {\r\n          @include flexCenter;\r\n          flex-direction: column;\r\n          margin-bottom: 20px;\r\n          &-title {\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            line-height: 12px;\r\n            padding: 6px 0 8px 0;\r\n          }\r\n          &-desc {\r\n            font-size: 10px;\r\n            font-weight: 400;\r\n            color: #999999;\r\n            line-height: 10px;\r\n          }\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    &.create {\r\n      &:hover {\r\n        .add-menu {\r\n          display: block;\r\n        }\r\n      }\r\n    }\r\n    &.qrcode {\r\n      &:hover {\r\n        .qrcode-menu {\r\n          display: block;\r\n        }\r\n      }\r\n    }\r\n    &:hover {\r\n      .icon {\r\n        path {\r\n          fill: #ffffff;\r\n        }\r\n      }\r\n      background-color: #21c9b8;\r\n      .text {\r\n        color: #ffffff;\r\n      }\r\n      .icon {\r\n        fill: green;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}