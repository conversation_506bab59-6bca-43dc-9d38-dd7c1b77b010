{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\Breadcrumb\\index.vue?vue&type=style&index=0&id=b50ef614&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\Breadcrumb\\index.vue", "mtime": 1750311962789}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWJyZWFkY3J1bWIuZWwtYnJlYWRjcnVtYiB7DQogIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgZm9udC1zaXplOiAxNHB4Ow0KICBsaW5lLWhlaWdodDogNTBweDsNCiAgbWFyZ2luLWxlZnQ6IDhweDsNCg0KICAubm8tcmVkaXJlY3Qgew0KICAgIGNvbG9yOiAjOTdhOGJlOw0KICAgIGN1cnNvcjogdGV4dDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Breadcrumb", "sourcesContent": ["<template>\r\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\r\n    <transition-group name=\"breadcrumb\">\r\n      <el-breadcrumb-item v-for=\"(item, index) in levelList\" :key=\"item.path\">\r\n        <span\r\n          v-if=\"item.redirect === 'noRedirect' || index == levelList.length - 1\"\r\n          class=\"no-redirect\"\r\n          >{{ item.meta.title }}</span\r\n        >\r\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null,\r\n    };\r\n  },\r\n  watch: {\r\n    $route(route) {\r\n      // if you go to the redirect page, do not update the breadcrumbs\r\n      if (route.path.startsWith(\"/redirect/\")) {\r\n        return;\r\n      }\r\n      this.getBreadcrumb();\r\n    },\r\n  },\r\n  created() {\r\n    this.getBreadcrumb();\r\n  },\r\n  methods: {\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let matched = this.$route.matched.filter(\r\n        (item) => item.meta && item.meta.title\r\n      );\r\n      const first = matched[0];\r\n\r\n      if (!this.isDashboard(first)) {\r\n        matched = [{ path: \"/index\", meta: { title: \"首页\" } }].concat(matched);\r\n      }\r\n\r\n      this.levelList = matched.filter(\r\n        (item) => item.meta && item.meta.title && item.meta.breadcrumb !== false\r\n      );\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name;\r\n      if (!name) {\r\n        return false;\r\n      }\r\n      return name.trim() === \"Index\";\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item;\r\n      if (redirect) {\r\n        this.$router.push(redirect);\r\n        return;\r\n      }\r\n      this.$router.push(path);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-breadcrumb.el-breadcrumb {\r\n  display: inline-block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n  margin-left: 8px;\r\n\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n"]}]}