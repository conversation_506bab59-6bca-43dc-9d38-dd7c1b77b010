# Tomcat
server:
  port: 9700

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-auth
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: ca6f4dcd-0826-4ad7-9eff-9180f9a832e1
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
        namespace: ca6f4dcd-0826-4ad7-9eff-9180f9a832e1
        file-extension: yaml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  main:
    allow-bean-definition-overriding: true

# SSO单点登录配置
sso:
  # 主系统配置
  main-system:
    url: http://localhost:9200
    verify-token-url: /sso/verify
    login-url: /sso/login
    logout-url: /sso/logout
    status-url: /sso/status
  # 当前系统配置
  current-system:
    system-id: market
    name: 智能市场系统
    callback-url: http://localhost:8081/sso/login
    database: market
  # Token配置
  token:
    expire-minutes: 480
    prefix: local_token:
  # 缓存配置
  cache:
    redis-prefix: sso_client:
