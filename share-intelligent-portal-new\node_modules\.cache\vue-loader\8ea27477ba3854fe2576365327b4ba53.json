{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\authRole.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\authRole.vue", "mtime": 1750311963044}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["authRole.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgDA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "authRole.vue", "sourceRoot": "src/views/system/user", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <h4 class=\"form-header h4\">基本信息</h4>\r\n    <el-form ref=\"form\" :model=\"form\" label-width=\"80px\">\r\n      <el-row>\r\n        <el-col :span=\"8\" :offset=\"2\">\r\n          <el-form-item label=\"用户昵称\" prop=\"nickName\">\r\n            <el-input v-model=\"form.nickName\" disabled />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\" :offset=\"2\">\r\n          <el-form-item label=\"登录账号\" prop=\"userName\">\r\n            <el-input  v-model=\"form.userName\" disabled />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <h4 class=\"form-header h4\">角色信息</h4>\r\n    <el-table v-loading=\"loading\" :row-key=\"getRowKey\" @row-click=\"clickRow\" ref=\"table\" @selection-change=\"handleSelectionChange\" :data=\"roles.slice((pageNum-1)*pageSize,pageNum*pageSize)\">\r\n      <el-table-column label=\"序号\" type=\"index\" align=\"center\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column type=\"selection\" :reserve-selection=\"true\" width=\"55\"></el-table-column>\r\n      <el-table-column label=\"角色编号\" align=\"center\" prop=\"roleId\" />\r\n      <el-table-column label=\"角色名称\" align=\"center\" prop=\"roleName\" />\r\n      <el-table-column label=\"权限字符\" align=\"center\" prop=\"roleKey\" />\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\"> \r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination v-show=\"total>0\" :total=\"total\" :page.sync=\"pageNum\" :limit.sync=\"pageSize\" />\r\n\r\n    <el-form label-width=\"100px\">\r\n      <el-form-item style=\"text-align: center;margin-left:-120px;margin-top:30px;\">\r\n        <el-button type=\"primary\" @click=\"submitForm()\">提交</el-button>\r\n        <el-button @click=\"close()\">返回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getAuthRole, updateAuthRole } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"AuthRole\",\r\n  data() {\r\n    return {\r\n       // 遮罩层\r\n      loading: true,\r\n      // 分页信息\r\n      total: 0,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      // 选中角色编号\r\n      roleIds:[],\r\n      // 角色信息\r\n      roles: [],\r\n      // 用户信息\r\n      form: {}\r\n    };\r\n  },\r\n  created() {\r\n    const userId = this.$route.params && this.$route.params.userId;\r\n    if (userId) {\r\n      this.loading = true;\r\n      getAuthRole(userId).then((response) => {\r\n        this.form = response.user;\r\n        this.roles = response.roles;\r\n        this.total = this.roles.length;\r\n        this.$nextTick(() => {\r\n          this.roles.forEach((row) => {\r\n            if (row.flag) {\r\n              this.$refs.table.toggleRowSelection(row);\r\n            }\r\n          });\r\n        });\r\n        this.loading = false;\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    /** 单击选中行数据 */\r\n    clickRow(row) {\r\n      this.$refs.table.toggleRowSelection(row);\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.roleIds = selection.map((item) => item.roleId);\r\n    },\r\n    // 保存选中的数据编号\r\n    getRowKey(row) {\r\n      return row.roleId;\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      const userId = this.form.userId;\r\n      const roleIds = this.roleIds.join(\",\");\r\n      updateAuthRole({ userId: userId, roleIds: roleIds }).then((response) => {\r\n        this.$modal.msgSuccess(\"授权成功\");\r\n        this.close();\r\n      });\r\n    },\r\n    /** 关闭按钮 */\r\n    close() {\r\n      const obj = { path: \"/system/user\" };\r\n      this.$tab.closeOpenPage(obj);\r\n    },\r\n  },\r\n};\r\n</script>"]}]}