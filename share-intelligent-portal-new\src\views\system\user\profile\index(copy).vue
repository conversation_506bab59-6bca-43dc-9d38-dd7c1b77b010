<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="user-info-card">
              <div style="display: flex; align-items: center">
                <el-image
                  style="width: 18px; height: 18px"
                  :src="require('@/assets/user/infoIcon.png')"
                ></el-image>
                <div style="margin-left: 10px">我的信息</div>
              </div>
              <div style="display: flex; margin-top: 34px">
                <el-image
                  style="width: 100px; height: 100px; border-radius: 50%"
                  :fit="fit"
                  :src="user.avatar"
                ></el-image>
                <div class="left-info-box">
                  <div class="user-name">{{ user.realName || "--" }}</div>
                  <div class="tag-group">
                    <div
                      v-if="this.companyStatus == '0'"
                      class="label-container"
                    >
                      <el-image
                        style="width: 12px; height: 12px"
                        :src="require('@/assets/user/authentication.png')"
                      ></el-image>
                      <span>未认证</span>
                    </div>
                    <div v-else class="certification">服务商认证</div>
                  </div>
                </div>
                <div class="divider"></div>
                <div class="right-info-box">
                  <div class="phone-class">
                    <el-image
                      class="iamge"
                      style="width: 14px; height: 20px; margin-left: 4px"
                      :src="require('@/assets/user/icon_phone.png')"
                    ></el-image>
                    <div class="phone-number">{{ user.phonenumber }}</div>
                  </div>
                  <div class="phone-class">
                    <el-image
                      class="iamge"
                      style="width: 22px; height: 20px"
                      :src="require('@/assets/user/icon_company.png')"
                    ></el-image>
                    <div class="phone-number" v-if="user.bussinessNo">
                      {{ user.companyName }}
                    </div>
                    <div class="phone-number grey" v-else>
                      您还未关联企业，<a
                        @click="jumpToApprove"
                        style="color: #21c9b8"
                        >请先关联</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="notify-card">
              <div style="display: flex; align-items: center">
                <el-image
                  style="width: 14px; height: 20px"
                  :src="require('@/assets/user/notifyIcon.png')"
                ></el-image>
                <div style="margin-left: 10px">消息通知</div>
                <div class="viewStyle" @click="viewMessage">查看更多>></div>
              </div>
              <div class="driver"></div>
              <div class="notify_content">
                <div
                  class="everyItem"
                  v-for="item in notifyList"
                  :key="item.id"
                >
                  <div class="item_icon">
                    <div class="icon_small"></div>
                  </div>
                  <div class="desc">
                    {{ item.remark || item.describeInfo || "--" }}
                  </div>
                  <div class="item_time">
                    {{ item.createTime.slice(0, 10) }}
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="subscribe-card">
              <div style="display: flex; align-items: center">
                <el-image
                  style="width: 14px; height: 20px"
                  :src="require('@/assets/user/notifyIcon.png')"
                ></el-image>
                <div style="margin-left: 10px">我的订阅</div>
                <div class="viewStyle" @click="viewSubscribe">查看更多>></div>
              </div>
              <div class="subscribe_content">
                <div
                  class="everyItem"
                  v-for="item in subscribeList"
                  :key="item.id"
                >
                  <div style="display: flex">
                    <div class="item_img">
                      <img :src="item.appLogo" alt="" />
                      <!-- <img :src="require('@/assets/user/wait.png')" alt="" /> -->
                    </div>
                    <div class="item_desc">
                      <div class="title">{{ item.remark }}</div>
                      <div style="font-size: 14px; margin-top: 10px">
                        <span style="color: #999999">规格:</span>
                        <span style="margin-left: 5px">{{
                          item.specs == "1" ? "基础版" : "高级版"
                        }}</span>
                      </div>
                      <div style="font-size: 14px; margin-top: 10px">
                        <span style="color: #999999">可用时长:</span>
                        <span style="margin-left: 5px">{{
                          item.validTime == "1" ? "一年" : "永久"
                        }}</span>
                      </div>
                      <div style="font-size: 14px; margin-top: 10px">
                        <span style="color: #999999">可用人数:</span>
                        <span style="margin-left: 5px">不限</span>
                        <!-- <span style="margin-left: 5px">{{ item.number }}</span> -->
                      </div>
                    </div>
                    <div class="item_amounts">
                      <div
                        style="
                          color: #999999;
                          font-size: 14px;
                          margin-top: 34px;
                        "
                      >
                        订单金额
                      </div>
                      <div
                        style="
                          margin-top: 21px;
                          color: #e10c02;
                          font-weight: 400;
                        "
                      >
                        ￥{{ item.price }}
                      </div>
                    </div>
                  </div>
                  <div class="driver" v-show="item.id == 1"></div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="ordermanage-card">
              <div style="display: flex; align-items: center">
                <el-image
                  style="width: 16px; height: 19px"
                  :src="require('@/assets/user/order.png')"
                ></el-image>
                <div style="margin-left: 10px">订单管理</div>
                <div class="viewStyle">查看更多>></div>
              </div>
              <div class="ordermanage_content">
                <div
                  class="everyItem"
                  v-for="item in ordermanageList"
                  :key="item.id"
                >
                  <div class="item_company">
                    {{ item.supply }}
                  </div>
                  <div class="item_content">
                    <div class="item_img">
                      <img :src="item.appLogo" alt="" />
                      <!-- <img :src="require('@/assets/user/wait.png')" alt="" /> -->
                    </div>
                    <div class="item_middle">
                      <div class="title">{{ item.appName }}</div>
                      <!-- <div class="desc">{{ item.desc }}</div> -->
                    </div>
                    <div class="item_right">
                      <div
                        style="
                          font-size: 14px;
                          font-family: Microsoft YaHei;
                          font-weight: 400;
                          color: #666666;
                          margin: 10px 0;
                        "
                      >
                        ￥{{ item.price }}
                      </div>
                      <div
                        class="payType"
                        :style="
                          item.status == '待支付'
                            ? 'color:#E10C02'
                            : item.status == '待发货'
                            ? 'color:#FBAC14'
                            : 'color:#19C582'
                        "
                      >
                        {{ getStatus(item.orderStatus) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="dataPanel-card">
              <div style="display: flex; align-items: center">
                <el-image
                  style="width: 18px; height: 20px"
                  :src="require('@/assets/user/dataPanel.png')"
                ></el-image>
                <div style="margin-left: 10px">数据面板</div>
              </div>
              <div class="dataPanel_content">
                <div class="dataPanel_left">
                  <div class="dataPanelItem">
                    <div class="itemType">应用</div>
                    <div class="typeNum">{{ appOrderData.appNumber }}</div>
                    <div class="addStyle">
                      <div class="addText">今日新增</div>
                      <div style="color: #f46768; margin-left: 10%">
                        {{ appOrderData.todayAppNumber }}
                      </div>
                      <div style="margin-left: 10%">
                        <img
                          :src="require('@/assets/user/add.png')"
                          alt=""
                          style="width: 12px; height: 16px"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="dataPanelItem">
                    <div class="itemType">订单</div>
                    <div class="typeNum">{{ appOrderData.orderNumber }}</div>
                    <div class="addStyle">
                      <div class="addText">今日新增</div>
                      <div style="color: #f46768; margin-left: 10%">
                        {{ appOrderData.todayOrderNumber }}
                      </div>
                      <div style="margin-left: 10%">
                        <img
                          :src="require('@/assets/user/add.png')"
                          alt=""
                          style="width: 12px; height: 16px"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="dataPanelItem">
                    <div class="itemType">开票</div>
                    <div class="typeNum">{{ appOrderData.invoiceNumber }}</div>
                    <div class="addStyle">
                      <div class="addText">今日新增</div>
                      <div style="color: #f46768; margin-left: 10%">
                        {{ appOrderData.todayInvoiceNumber }}
                      </div>
                      <div style="margin-left: 10%">
                        <img
                          :src="require('@/assets/user/add.png')"
                          alt=""
                          style="width: 12px; height: 16px"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="dataPanel_right">
                  <div style="display: flex; margin-top: 18px">
                    <div>
                      年份:
                      <el-date-picker
                        v-model="valueYear"
                        type="year"
                        placeholder="选择年"
                        style="width: 70%"
                        format="yyyy"
                        value-format="yyyy"
                        @change="getSubData"
                      >
                      </el-date-picker>
                    </div>
                    <div>
                      月份:
                      <el-date-picker
                        v-model="valueMonth"
                        type="month"
                        placeholder="选择月"
                        style="width: 70%"
                        format="MM"
                        value-format="MM"
                        @change="getSubData"
                        @focus="timeEditable"
                      >
                      </el-date-picker>
                    </div>
                  </div>
                  <div id="dataPanel" style="width: 100%; height: 200px"></div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="application-card">
              <div style="display: flex; align-items: center">
                <el-image
                  style="width: 16px; height: 19px"
                  :src="require('@/assets/user/application.png')"
                ></el-image>
                <div style="margin-left: 10px">应用管理</div>
                <div class="viewStyle">查看更多>></div>
              </div>
              <div class="application-content">
                <div
                  class="everyItem"
                  v-for="item in applicationList"
                  :key="item.id"
                >
                  <div class="item_img">
                    <!-- <img :src="require('@/assets/user/wait.png')" alt="" /> -->
                    <img :src="item.appLogo" alt="" />
                  </div>
                  <div class="title">{{ item.appName }}</div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
      <!-- <el-col :span="3" :xs="24">
        <div class="code-box">
          <div class="code-item">
            <el-image
              style="width: 100px; height: 100px"
              :src="require('@/assets/user/more.png')"
              :fit="fit"
            ></el-image>
            <div class="title">门户端</div>
            <div class="hint">政策服务/申报/数据掌握</div>
          </div>
          <div class="code-item">
            <el-image
              style="width: 100px; height: 100px"
              :src="require('@/assets/user/company_mini.png')"
              :fit="fit"
            ></el-image>
            <div class="title">企业端-云端研发</div>
            <div class="hint">研发/采购/销售/政策/服务</div>
          </div>
          <div class="code-item">
            <el-image
              style="width: 100px; height: 100px"
              :src="require('@/assets/user/resource_mini.png')"
              :fit="fit"
            ></el-image>
            <div class="title">资源端</div>
            <div class="hint">供应商/服务商/专家</div>
          </div>
          <div class="code-item">
            <el-image
              style="width: 100px; height: 100px"
              :src="require('@/assets/user/gov_mini.png')"
              :fit="fit"
            ></el-image>
            <div class="title">政府端</div>
            <div class="hint">政策服务/申报/数据掌握</div>
          </div>
        </div>
      </el-col> -->
    </el-row>
  </div>
</template>

<script>
import store from "@/store";
import userAvatar from "./userAvatar";
import userInfo from "./userInfo";
import resetPwd from "./resetPwd";
// import { listInfo } from "@/api/system/info";
import { getAbutmentList } from "@/api/system/abutment";

import {
  getUserInfo,
  checkAuthStatus,
  sublist,
  appOrderNum,
  subStatistics,
} from "@/api/system/user";
import UserMenu from "../components/userMenu.vue";
import * as RongIMLib from "@rongcloud/imlib-next";
import { getUserIMToken } from "@/api/system/user";
import { getUserListByIds } from "@/api/im.js";
import * as echarts from "echarts";
import { listInfo } from "@/api/system/info";
import { appliList } from "@/api/appliMarket";
import { orderList } from "@/api/system/user";

export default {
  name: "Profile",
  components: { userAvatar, userInfo, resetPwd, UserMenu },
  data() {
    return {
      user: {
        name: store.getters.name,
        avatar: store.getters.avatar,
        bussinessNo: store.getters.bussinessNo,
        id: store.getters.userId,
      },
      roleGroup: {},
      userInfo: {},
      postGroup: {},
      fit: "cover",
      responseList: [],
      responseList: [],
      abutmentList: [],
      conversationList: [],
      companyStatus: "",
      activeTab: "userinfo",
      url: "https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg",
      notifyList: [],
      subscribeList: [],
      ordermanageList: [],
      valueYear: "",
      valueMonth: "",
      applicationList: [],
      queryParams: {
        pageNum: 1,
      },
      orderStatusList: [
        {
          dictValue: 1,
          dictLabel: "待支付",
        },
        {
          dictValue: 2,
          dictLabel: "待发货",
        },
        {
          dictValue: 3,
          dictLabel: "支付失败",
        },
        {
          dictValue: 4,
          dictLabel: "已发货",
        },
        {
          dictValue: 5,
          dictLabel: "已成交",
        },
        {
          dictValue: 6,
          dictLabel: "待续费",
        },
        {
          dictValue: 7,
          dictLabel: "已关闭",
        },
        {
          dictValue: 8,
          dictLabel: "支付中",
        },
        {
          dictValue: 9,
          dictLabel: "已取消",
        },
      ],
      appOrderData: {},
      subStatisticsData: [],
    };
  },
  created() {
    this.getMessage();
    this.getSublist();
    this.getAppliList();
    this.getOrderList();
    this.getAppOrderNum();
    this.getSubStatistics();
    this.getUser();
    // this.getResponseList();
    // this.getAbutmentList();
    // this.getImMessage();
  },
  // mounted() {
  //   this.getInitEcharts();
  // },
  methods: {
    getMessage() {
      let data = {
        pageNum: 1,
        pageSize: 4,
        type: 2,
      };
      listInfo(data).then((response) => {
        this.notifyList = response.rows.slice(0, 4);
        console.log(this.notifyList, "----------");
      });
    },
    getSublist() {
      sublist().then((res) => {
        if (res.code === 200) {
          this.subscribeList = res.rows.slice(0, 2);
        }
      });
    },
    getAppliList() {
      let params = {
        createBy: this.$store.state.user.userId,
        pageNum: this.queryParams.pageNum,
        pageSize: 6,
      };
      appliList(params).then((res) => {
        if (res.code === 200) {
          this.applicationList = res.rows.slice(0, 6);
        }
      });
    },
    getOrderList() {
      let params = {
        createBy: this.$store.state.user.userId,
        pageNum: this.queryParams.pageNum,
        pageSize: 3,
      };
      orderList(params).then((res) => {
        if (res.code === 200) {
          this.ordermanageList = res.rows;
          console.log(this.ordermanageList, "----------------");
        }
      });
    },
    getAppOrderNum() {
      appOrderNum().then((res) => {
        if (res.code === 200) {
          this.appOrderData = res.data;
        }
      });
    },
    getSubStatistics() {
      let params = {
        year: this.valueYear,
        month: this.valueMonth,
      };
      console.log(params, "------------");
      subStatistics(params).then((res) => {
        if (res.code === 200) {
          this.subStatisticsData = res.data;
          this.getInitEcharts();
          console.log(res, "------------");
        }
      });
    },
    getImMessage() {
      if (this.user.id) {
        getUserIMToken({ userId: this.user.id }).then((res) => {
          console.log(res);
          if (res.code === 200 && res.data.code === 200) {
            window.token = res.data.token;
            RongIMLib.connect(token).then((res) => {
              console.info("连接结果打印1：", res);
              const startTime = 0;
              const count = 10;
              const order = 0;

              RongIMLib.getConversationList({
                count: count,
                startTime: startTime,
                order: order,
              }).then((res) => {
                if (res.code === 0) {
                  this.conversationList = res.data;
                  let ids = [];
                  this.conversationList.forEach((item) => {
                    ids.push(item.targetId);
                  });
                  this.getUserListByIds(ids);
                } else {
                  console.log(res.code, res.msg);
                }
              });
            });
          }
        });
      }
    },
    getUserListByIds(ids) {
      getUserListByIds(ids).then((res) => {
        if (res.code == 200) {
          let list = [];
          this.conversationList.forEach((item) => {
            let index = res.data.findIndex(
              (element) => element.id == item.targetId
            );
            if (index != -1) {
              this.conversationList[index].realName = res.data[index].realName;
              this.conversationList[index].userPortrait =
                res.data[index].userPortrait;
              // list.push({ ...item, ...res.data[index] });
            }
          });
          // this.conversationList = list;
          this.conversationList = this.sliceArray(this.conversationList, 3);
        }
      });
    },
    getUser() {
      getUserInfo(this.user.id).then((response) => {
        this.user = response.data;
        this.roleGroup = response.roleGroup;
        this.postGroup = response.postGroup;
      });
      checkAuthStatus().then((response) => {
        this.companyStatus = response.data.companyStatus;
      });
    },
    goIM(info) {
      let routeData;
      if (info) {
        routeData = this.$router.resolve({
          path: "/user/im",
          query: {
            userId: info.targetId,
          },
        });
      } else {
        routeData = this.$router.resolve({
          path: "/user/im",
        });
      }
      window.open(routeData.href, "_blank");
    },
    getResponseList() {
      listInfo({ pageNum: 1, pageSize: 10 }).then((response) => {
        this.responseList = this.sliceArray(response.rows, 2);
      });
    },
    getAbutmentList() {
      getAbutmentList({ pageNum: 1, pageSize: 9 }).then((response) => {
        this.abutmentList = this.sliceArray(response.rows, 3);
      });
    },
    getApplyStatusName(key) {
      switch (key) {
        case 1:
          return "审核中";
        case 2:
          return "审核通过";
        default:
        case 3:
          return "审核驳回";
      }
    },
    jumpToApprove() {
      this.$router.push(`/user/userInfo?relevanceCompany=1`);
    },
    getUrl(str) {
      if (!str) {
        return "";
      }

      var list = JSON.parse(str);
      if (list.length > 0) {
        return list[0].url;
      }
    },

    sliceArray(array, number) {
      var result = [];
      for (var x = 0; x < Math.ceil(array.length / number); x++) {
        var start = x * number;
        var end = start + number;
        result.push(array.slice(start, end));
      }
      console.log(result);
      return result;
    },
    jumpToDemand() {
      this.$router.push("/user/companyDemand");
    },
    jumpToApply() {
      this.$router.push("/user/companyApply");
    },
    jumpToMessage() {
      this.$router.push("/user/notice");
    },
    jumpToAbutment() {
      this.$router.push("/user/abutmentRecord");
    },
    getInitEcharts() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(document.getElementById("dataPanel"));
      // 绘制图表
      myChart.setOption({
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          right: 0,
          top: 40,
          // top: "5%",
          // left: "center",
        },
        series: [
          {
            type: "pie",
            radius: ["40%", "70%"],
            avoidLabelOverlap: false,
            center: ["30%", "50%"],
            label: {
              show: false,
              position: "center",
            },
            labelLine: {
              show: false,
            },
            data: this.subStatisticsData,
          },
        ],
      });
    },
    viewMessage() {
      this.$router.push({
        path: "/user/notice",
      });
    },
    viewSubscribe() {
      this.$router.push({
        path: "/user/mySubscriptions",
      });
    },
    getStatus(status) {
      let orderStatus;
      this.orderStatusList.forEach((item) => {
        if (item.dictValue == status) {
          orderStatus = item.dictLabel;
        }
      });
      return orderStatus;
    },
    getSubData() {
      this.getSubStatistics();
    },
    timeEditable() {
      this.$nextTick(() => {
        let els = document.querySelectorAll(
          ".el-date-picker__header--bordered"
        );
        for (var i = 0; i <= els.length - 1; i++) {
          els[1].style.display = "none";
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  background: #f4f5f9;
  .user-type-card {
    background: rgba(33, 77, 197, 0.1);
    margin-left: 20px;
    margin-top: 14px;
    color: #214dc5;
    font-size: 12px;
    border-radius: 4px;
    font-weight: 400;
    padding: 4px 12px 4px 12px;
  }
  .user-info-card {
    background-color: #fff;
    padding: 30px;
    height: 240px;
    border-radius: 4px;
    .left-info-box {
      width: 30%;
      text-align: center;
      .user-name {
        font-size: 28px;
        font-weight: 500;
        // margin-left: 20px;
        color: #333333;
        line-height: 50px;
      }
      .tag-group {
        display: flex;
        justify-content: center;
        .certification {
          width: 110px;
          height: 40px;
          line-height: 40px;
          background: rgb(229, 247, 243);
          border-radius: 4px;
          font-size: 16px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #21c9b8;
        }
        .label-container {
          padding: 4px 6px;
          margin-top: 6px;
          margin-right: 6px;
          height: 24px;
          background: #f0f1f4;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          color: #8a8c94;
          line-height: 12px;
          .el-image {
            margin: 2px 4px 0 0;
          }
        }
        .orange {
          background: rgba(255, 191, 69, 0.2);
          color: #ff8a27;
        }
        .red {
          background: rgba(197, 37, 33, 0.1);
          color: #21c9b8;
        }
      }
    }
    .divider {
      width: 1px;
      height: 80px;
      margin: auto 0;
      background: #e8e8e8;
    }
    .right-info-box {
      width: 50%;
      padding-left: 40px;
      padding-top: 12px;
      height: 100px;
      .phone-class {
        display: flex;
        margin-top: 14px;
      }
      .phone-number {
        margin-left: 12px;
      }
      .grey {
        color: #999999;
      }
    }
  }
  .notify-card {
    background-color: #fff;
    padding: 30px;
    height: 240px;
    border-radius: 4px;
    .driver {
      width: 65%;
      height: 1px;
      background: #f7f8f9;
      margin-top: 15px;
    }
    .notify_content {
      margin-top: 15px;
      .everyItem {
        display: flex;
        align-items: center;
        margin-top: 22px;
        .item_icon {
          width: 10px;
          height: 10px;
          border: 1px solid #21c9b8;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          .icon_small {
            width: 4px;
            height: 4px;
            background: #21c9b8;
            border-radius: 50%;
          }
        }
        .desc {
          width: 80%;
          overflow: hidden;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #333333;
          margin-left: 10px;
        }
        .item_time {
          width: 20%;
          text-align: right;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #999999;
        }
      }
      .everyItem:nth-child(1) {
        margin-top: 0;
      }
    }
  }
  .subscribe-card {
    background-color: #fff;
    padding: 30px;
    height: 410px;
    margin-top: 21px;
    border-radius: 4px;
    .subscribe_content {
      margin-top: 20px;
      .everyItem {
        width: 100%;
        margin-top: 30px;
        .item_img {
          width: 17%;
          height: 120px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .item_desc {
          margin-left: 20px;
          width: 58%;
          .title {
            font-size: 16px;
            font-family: Source Han Sans CN;
            font-weight: 500;
            color: #333333;
            margin-top: 11px;
          }
        }
        .item_amounts {
          width: 20%;
          text-align: center;
        }
      }
      .everyItem:nth-child(1) {
        margin-top: 0;
      }
      .driver {
        width: 100%;
        height: 1px;
        background: #f7f8f9;
        margin: 30px 0;
      }
    }
  }
  .ordermanage-card {
    background-color: #fff;
    padding: 30px;
    height: 410px;
    margin-top: 21px;
    border-radius: 4px;
    .ordermanage_content {
      .everyItem {
        width: 100%;
        border-bottom: 1px solid #f7f8f9;
        padding: 10px 0;
        .item_company {
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #666666;
        }
        .item_company:nth-child(1) {
          margin-top: 0;
        }
        .item_content {
          display: flex;
          margin-top: 10px;
          .item_img {
            width: 11%;
            height: 70px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .item_middle {
            width: 70%;
            margin-left: 20px;
            .title {
              font-size: 16px;
              font-family: Source Han Sans CN;
              font-weight: 500;
              color: #333333;
              margin: 10px 0;
            }
            .desc {
              font-size: 14px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #999999;
            }
          }
          .item_right {
            width: 30%;
            text-align: right;
            .payType {
              font-size: 14px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #e10c02;
            }
          }
        }
      }
      .everyItem:nth-child(3) {
        border-bottom: none;
      }
    }
  }
  .dataPanel-card {
    height: 290px;
    padding: 30px;
    margin-top: 21px;
    background: #ffffff;
    border-radius: 4px;
    .dataPanel_content {
      width: 100%;
      display: flex;
      .dataPanel_left {
        width: 45%;
        .dataPanelItem {
          width: 100%;
          height: 56px;
          background: rgb(245, 252, 250);
          line-height: 56px;
          display: flex;
          margin-top: 18px;
          .itemType {
            font-size: 16px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #333333;
            margin-left: 5%;
          }
          .typeNum {
            font-size: 30px;
            font-family: SimHei;
            font-weight: 400;
            color: #21c9b8;
            margin-left: 9%;
          }
          .addStyle {
            margin-left: 9%;
            display: flex;
            width: 45%;
            .addText {
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: 400;
              color: #999999;
            }
          }
        }
      }
      .dataPanel_right {
        width: 55%;
        padding-left: 10px;
      }
    }
  }
  .application-card {
    height: 290px;
    padding: 30px;
    margin-top: 21px;
    background: #ffffff;
    border-radius: 4px;
    .application-content {
      display: flex;
      flex-wrap: wrap;
      margin-top: 20px;
      .everyItem {
        display: flex;
        width: 32%;
        margin-left: 2%;
        .item_img {
          width: 48%;
          height: 95px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .title {
          font-size: 16px;
          font-family: Source Han Sans CN;
          font-weight: 500;
          color: #333333;
          margin-left: 9%;
          margin-top: 27px;
        }
      }
      .everyItem:nth-child(n + 4) {
        margin-top: 20px;
      }
      .everyItem:nth-child(3n + 1) {
        margin-left: 0;
      }
    }
  }
  .viewStyle {
    width: calc(100% - 90px);
    text-align: right;
    font-size: 16px;
    font-family: Source Han Sans CN;
    font-weight: 500;
    color: #21c9b8;
    cursor: pointer;
  }
  .demand-card {
    margin-top: 18px;
    background-color: #fff;
    height: 340px;
    z-index: -1;
    .demand-header {
      line-height: 60px;
      padding: 0 20px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #e8e8e8;

      .header {
        font-size: 18px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333333;
      }
      .more {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #21c9b8;
      }
    }
    .demmand-carousel {
      width: 100%;
      height: 260px;
      padding: 12px 20px;
      .demand-message-item {
        display: flex;
        padding: 16px 0 16px 0;
        .title {
          width: 65%;
          height: 50px;
          margin-left: 12px;
          font-size: 16px;
          line-height: 26px;
          font-weight: 500;
          color: #333333;
          overflow: hidden;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
        .status-card {
          background: rgba(21, 188, 132, 0.15);
          color: #15bc84;
          margin: auto;
          font-size: 12px;
          border-radius: 4px;
          padding: 0 12px;
          font-weight: 400;
          height: 30px;
          line-height: 30px;
        }
      }
    }
  }
  .small-card {
    margin-top: 18px;
    height: 300px;
    padding-bottom: 12px;
    background-color: #fff;
    .small-card-header {
      line-height: 60px;
      width: 100%;
      display: flex;
      padding: 0 20px;
      justify-content: space-between;
      border-bottom: 1px solid #e8e8e8;
      .header {
        font-size: 18px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333333;
      }
      .more {
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #21c9b8;
      }
    }
    .message-carousel {
      width: 100%;
      height: 260px;
      .message-item {
        padding: 18px 0 18px 0;
        margin: 4px 20px;
        border-bottom: 1px solid #e8e8e8;
        .title {
          font-size: 16px;
          font-weight: 500;
          color: #333333;
          line-height: 24px;
        }
        .content {
          margin-top: 8px;
          font-size: 12px;
          font-weight: 400;
          color: #666666;
          line-height: 23px;
          overflow: hidden;
          -webkit-line-clamp: 2;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
      }
    }
    .apply-carousel {
      width: 100%;
      height: 230px;
      .apply-item {
        padding: 18px 0 10px 0;
        margin: 0 20px;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        .item-left {
          width: 100%;
          .title {
            font-size: 16px;
            font-weight: 500;
            color: #333333;
            // width: 300px;
            line-height: 16px;
            overflow: hidden;
            -webkit-line-clamp: 1;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
          }
          .content {
            margin-top: 8px;
            font-size: 12px;
            font-weight: 400;
            color: #666666;
            line-height: 20px;
            overflow: hidden;
            -webkit-line-clamp: 1;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
          }
        }
        .item-right {
          padding: 10px 0;
          .button {
            text-align: center;
            width: 72px;
            padding: 6px 0;
            border-radius: 4px;
            border: 1px solid #214dc5;
            font-size: 12px;
            font-weight: 400;
            color: #214dc5;
            line-height: 12px;
          }
        }
      }
    }
    .conversation-box {
      width: 100%;
      height: 230px;
      .conversaion-item {
        padding: 12px 0 10px 0;
        margin: 0 20px;
        border-bottom: 1px solid #e8e8e8;
        display: flex;
        &-avatar {
          margin-right: 16px;
          .el-avatar {
            background: transparent;
          }
        }
        &-center {
          width: 300px;
          &-content {
            margin-top: 2px;
            font-size: 14px;
            font-weight: 400;
            color: #666666;
            line-height: 20px;
            overflow: hidden;
            -webkit-line-clamp: 1;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
          }
        }

        &-right {
          padding: 10px 0;
          .button {
            text-align: center;
            width: 72px;
            padding: 6px 0;
            border-radius: 4px;
            border: 1px solid #214dc5;
            font-size: 12px;
            font-weight: 400;
            color: #214dc5;
            line-height: 12px;
          }
        }
      }
    }
  }
  .none-class {
    text-align: center;
    margin: 8% 0;
    .text {
      font-size: 14px;
      font-weight: 400;
      color: #999999;
      line-height: 14px;
    }
  }
  .code-box {
    background-color: #fff;
    border-radius: 8px;
    text-align: center;
    .code-item {
      padding: 16px 0;
      .title {
        margin-top: 10px;
        font-size: 12px;
        font-weight: 500;
        color: #333333;
        line-height: 12px;
      }
      .hint {
        margin-top: 10px;
        margin-bottom: 6px;
        font-size: 10px;
        font-weight: 400;
        color: #999999;
        line-height: 10px;
      }
    }
  }
}
</style>
