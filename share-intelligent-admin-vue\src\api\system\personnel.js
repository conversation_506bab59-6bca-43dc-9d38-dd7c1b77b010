import request from '@/utils/request'

// 查询工厂人员能力列表
export function listPersonnel(query) {
  return request({
    url: '/system/personnel/list',
    method: 'get',
    params: query
  })
}

// 查询工厂人员能力详细
export function getPersonnel(id) {
  return request({
    url: '/system/personnel/' + id,
    method: 'get'
  })
}

// 新增工厂人员能力
export function addPersonnel(data) {
  return request({
    url: '/system/personnel',
    method: 'post',
    data: data
  })
}

// 修改工厂人员能力
export function updatePersonnel(data) {
  return request({
    url: '/system/personnel',
    method: 'put',
    data: data
  })
}

// 删除工厂人员能力
export function delPersonnel(id) {
  return request({
    url: '/system/personnel/' + id,
    method: 'delete'
  })
}
