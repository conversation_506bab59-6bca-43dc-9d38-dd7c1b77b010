<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FactoryEquipmentMapper">
    
    <resultMap type="FactoryEquipment" id="FactoryEquipmentResult">
        <result property="id"    column="id"    />
        <result property="factoryId"    column="factory_id"    />
        <result property="equipmentName"    column="equipment_name"    />
        <result property="specification"    column="specification"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectFactoryEquipmentVo">
        select id, factory_id, equipment_name, specification, create_time, update_time from factory_equipment
    </sql>

    <select id="selectFactoryEquipmentList" parameterType="FactoryEquipment" resultMap="FactoryEquipmentResult">
        <include refid="selectFactoryEquipmentVo"/>
        <where>  
            <if test="factoryId != null "> and factory_id = #{factoryId}</if>
            <if test="equipmentName != null  and equipmentName != ''"> and equipment_name like concat('%', #{equipmentName}, '%')</if>
            <if test="specification != null  and specification != ''"> and specification = #{specification}</if>
        </where>
    </select>
    
    <select id="selectFactoryEquipmentById" parameterType="Long" resultMap="FactoryEquipmentResult">
        <include refid="selectFactoryEquipmentVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFactoryEquipment" parameterType="FactoryEquipment" useGeneratedKeys="true" keyProperty="id">
        insert into factory_equipment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="factoryId != null">factory_id,</if>
            <if test="equipmentName != null and equipmentName != ''">equipment_name,</if>
            <if test="specification != null">specification,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="factoryId != null">#{factoryId},</if>
            <if test="equipmentName != null and equipmentName != ''">#{equipmentName},</if>
            <if test="specification != null">#{specification},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateFactoryEquipment" parameterType="FactoryEquipment">
        update factory_equipment
        <trim prefix="SET" suffixOverrides=",">
            <if test="factoryId != null">factory_id = #{factoryId},</if>
            <if test="equipmentName != null and equipmentName != ''">equipment_name = #{equipmentName},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFactoryEquipmentById" parameterType="Long">
        delete from factory_equipment where id = #{id}
    </delete>

    <delete id="deleteFactoryEquipmentByIds" parameterType="String">
        delete from factory_equipment where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>