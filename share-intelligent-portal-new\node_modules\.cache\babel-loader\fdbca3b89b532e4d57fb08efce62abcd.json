{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\scene\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\scene\\detail.vue", "mtime": 1750311963002}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfc2NlbmUgPSByZXF1aXJlKCJAL2FwaS9zY2VuZSIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogInNjZW5lRGV0YWlsUGFnZSIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRhdGE6IHt9CiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuaW5pdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgaW5pdDogZnVuY3Rpb24gaW5pdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdmFyIGlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICgwLCBfc2NlbmUuZ2V0SW5mb0RldGFpbCkoewogICAgICAgIGlkOiBpZAogICAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXMpIHsKICAgICAgICBfdGhpcy5sb2FkaW5nID0gZmFsc2U7CiAgICAgICAgX3RoaXMuZGF0YSA9IHJlcy5kYXRhIHx8IHt9OwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_scene", "require", "name", "data", "created", "init", "methods", "_this", "id", "$route", "query", "loading", "getInfoDetail", "then", "res", "catch"], "sources": ["src/views/scene/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"scene-detail-container\">\r\n    <div class=\"scene-detail-banner\">\r\n      <img\r\n        src=\"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/167601983615484.webp\"\r\n        alt=\"场景详情\"\r\n      />\r\n    </div>\r\n    <div class=\"scene-detail-title-box\">\r\n      <div class=\"scene-divider\"></div>\r\n      <div class=\"scene-detail-title\">场景详情</div>\r\n      <div class=\"scene-divider\"></div>\r\n    </div>\r\n    <div class=\"scene-detail-content\">\r\n      <template v-if=\"data.id\">\r\n        <div class=\"scene-detail-box\">\r\n          <div class=\"scene-info-title\">\r\n            {{ data.title }}\r\n          </div>\r\n          <div class=\"scene-info-time\">{{ data.updateTime }}</div>\r\n          <div class=\"scene-info-divider\"></div>\r\n          <div class=\"scene-info-box\">\r\n            <div\r\n              v-html=\"data.content\"\r\n              class=\"scene-info-content ql-editor\"\r\n            ></div>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      <template v-else>\r\n        <el-empty />\r\n      </template>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getInfoDetail } from \"@/api/scene\";\r\n\r\nexport default {\r\n  name: \"sceneDetailPage\",\r\n  data() {\r\n    return {\r\n      data: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      let id = this.$route.query.id;\r\n      this.loading = true;\r\n      getInfoDetail({ id: id })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.scene-detail-container {\r\n  width: 100%;\r\n  padding: 0 0 100px;\r\n  background: #f4f5f9;\r\n  .scene-detail-banner {\r\n    width: 100%;\r\n    height: 280px;\r\n    img {\r\n      width: 100%;\r\n      height: 280px;\r\n      object-fit: fill;\r\n    }\r\n  }\r\n  .scene-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .scene-detail-title {\r\n      font-size: 40px;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .scene-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .scene-detail-content {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n    .scene-detail-box {\r\n      padding: 60px 116px 100px;\r\n      .scene-info-title {\r\n        width: 960px;\r\n        font-size: 32px;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n        white-space: nowrap; /*让文字不换行*/\r\n        overflow: hidden; /*超出要隐藏*/\r\n      }\r\n      .scene-info-time {\r\n        font-size: 12px;\r\n        color: #999;\r\n        line-height: 12px;\r\n        padding-top: 40px;\r\n      }\r\n      .scene-info-divider {\r\n        width: 100%;\r\n        height: 1px;\r\n        background: #e8e8e8;\r\n        margin-top: 10px;\r\n      }\r\n      .scene-info-box {\r\n        padding-top: 40px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.scene-detail-container {\r\n  .scene-info-content {\r\n    word-break: break-all;\r\n    font-size: 16px;\r\n    line-height: 28px;\r\n    color: #333;\r\n    img {\r\n      max-width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAqCA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAA,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,IAAA,WAAAA,KAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,EAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,EAAA;MACA,KAAAG,OAAA;MACA,IAAAC,oBAAA;QAAAJ,EAAA,EAAAA;MAAA,GACAK,IAAA,WAAAC,GAAA;QACAP,KAAA,CAAAI,OAAA;QACAJ,KAAA,CAAAJ,IAAA,GAAAW,GAAA,CAAAX,IAAA;MACA,GACAY,KAAA;QACAR,KAAA,CAAAI,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}