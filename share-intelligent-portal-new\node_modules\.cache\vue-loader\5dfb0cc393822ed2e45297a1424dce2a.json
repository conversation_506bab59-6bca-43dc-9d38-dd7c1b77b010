{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\im\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\im\\index.vue", "mtime": 1750311963059}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/im", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-13 19:00:09\r\n * @LastEditTime: 2023-06-06 16:48:26\r\n * @Description:\r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"2.5\">\r\n        <div class=\"conversation-list\">\r\n          <conversation-list ref=\"conversationList\" base-size=\"6.5px\" />\r\n        </div>\r\n      </el-col>\r\n\r\n      <el-col :span=\"17\" :xl=\"17\" :lg=\"15\" :md=\"12\">\r\n        <div class=\"user-message-container\">\r\n          <div class=\"message-box\">\r\n            <message-list\r\n              class=\"message-list\"\r\n              ref=\"messageList\"\r\n              base-size=\"6.5px\"\r\n            ></message-list>\r\n          </div>\r\n          <div class=\"editor-box\">\r\n            <message-editor base-size=\"6.5px\" />\r\n          </div>\r\n          <!-- <div class=\"none-class\">\r\n            <el-image\r\n              style=\"width: 160px; height: 160px\"\r\n              :src=\"require('@/assets/user/none.png')\"\r\n              fit=\"cover\"\r\n            ></el-image>\r\n            <div class=\"text\">暂无数据</div>\r\n          </div> -->\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      :visible.sync=\"videoDialogVisible\"\r\n      :v-if=\"videoDialogVisible\"\r\n      width=\"60%\"\r\n      style=\"height: 650px\"\r\n      :before-close=\"handleVideoDialogClose\"\r\n    >\r\n      <Videoplayer :mp4Url=\"videoUrl\"></Videoplayer>\r\n    </el-dialog>\r\n    <el-dialog\r\n      :visible.sync=\"imageDialogVisible\"\r\n      :v-if=\"imageDialogVisible\"\r\n      width=\"60%\"\r\n      style=\"height: 850px; text-align: center\"\r\n      :before-close=\"handleImageDialogClose\"\r\n    >\r\n      <el-image\r\n        :v-if=\"imageDialogVisible\"\r\n        :src=\"imageUrl\"\r\n        style=\"width: 600px; height: 100%\"\r\n      ></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport FileSaver from \"file-saver\";\r\nimport * as RongIMLib from \"@rongcloud/imlib-next\";\r\nimport {\r\n  imkit,\r\n  CoreEvent,\r\n  DisabledMessageContextMenu,\r\n  DisabledConversationontextMenu,\r\n} from \"@rongcloud/imkit\";\r\nimport store from \"@/store\";\r\nimport { getUserIMToken } from \"@/api/system/user\";\r\nimport { ConversationType } from \"@rongcloud/engine\";\r\nimport Videoplayer from \"./components/video\";\r\n\r\nexport default {\r\n  name: \"IM\",\r\n  components: { UserMenu, Videoplayer },\r\n\r\n  data() {\r\n    return {\r\n      user: {\r\n        id: store.getters.userId,\r\n      },\r\n      editorVisible: false,\r\n      videoDialogVisible: false,\r\n      imageDialogVisible: false,\r\n      videoUrl: \"\",\r\n      lang: \"\",\r\n      langArr: [\r\n        {\r\n          lang: \"zh_CN\",\r\n          value: \"中文\",\r\n        },\r\n        {\r\n          lang: \"en\",\r\n          value: \"英文\",\r\n        },\r\n      ],\r\n      conversationMenuList: [\r\n        {\r\n          value: DisabledConversationontextMenu.Top,\r\n          name: \"置顶\",\r\n        },\r\n        {\r\n          value: DisabledConversationontextMenu.Delete,\r\n          name: \"删除\",\r\n        },\r\n        {\r\n          value: DisabledConversationontextMenu.Notification,\r\n          name: \"免打扰\",\r\n        },\r\n      ],\r\n      disableMenuMessage: [],\r\n      disableMenuConversation: [],\r\n      messageMenuList: [\r\n        {\r\n          value: DisabledMessageContextMenu.Forward,\r\n          name: \"转发\",\r\n        },\r\n        {\r\n          value: DisabledMessageContextMenu.Delete,\r\n          name: \"删除\",\r\n        },\r\n        {\r\n          value: DisabledMessageContextMenu.Reference,\r\n          name: \"引用\",\r\n        },\r\n        {\r\n          value: DisabledMessageContextMenu.Recall,\r\n          name: \"撤回\",\r\n        },\r\n        {\r\n          value: DisabledMessageContextMenu.Copy,\r\n          name: \"复制\",\r\n        },\r\n      ],\r\n      imageUrl: \"\",\r\n      imageUrl: \"\",\r\n      showImage: false,\r\n      switchConversationList: {},\r\n      conversation: null,\r\n      showMessageMenu: false,\r\n      showConversationMenu: false,\r\n    };\r\n  },\r\n  created() {\r\n    ///获取用户token\r\n    if (this.user.id) {\r\n      getUserIMToken({ userId: this.user.id }).then((res) => {\r\n        if (res.code === 200 && res.data.code === 200) {\r\n          window.token = res.data.token;\r\n          RongIMLib.connect(token).then((res) => {\r\n            console.info(\"连接结果打印1：\", res);\r\n            window.imkit = imkit;\r\n            this.lang = imkit.lang;\r\n            // 加载会话列表\r\n            imkit.emit(CoreEvent.CONVERSATION, true);\r\n            if (this.$route.query.userId) {\r\n              imkit.selectConversation({\r\n                conversationType: ConversationType.PRIVATE, // 会话类型\r\n                targetId: this.$route.query.userId,\r\n              });\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    // defineCustomElements();\r\n    const conversationList = this.$refs.conversationList;\r\n    const messageList = this.$refs.messageList;\r\n    //添加点击会话监听\r\n    conversationList.addEventListener(\r\n      \"tapConversation\",\r\n      this.handleTapConversation //回调处理函数\r\n    );\r\n    console.log(\"conversationList\", conversationList);\r\n    //添加删除会话监听\r\n    conversationList.addEventListener(\r\n      \"deleteConversation\",\r\n      this.handleDeleteConversation //回调处理函数\r\n    );\r\n    const disableMenu = [DisabledMessageContextMenu.Reference];\r\n    messageList.disableMenu = disableMenu;\r\n    //添加点击消息触发监听\r\n    messageList.addEventListener(\"tapMessage\", this.handleTapMessage);\r\n  },\r\n  beforeUnmount() {\r\n    // 注意：需要 removeEventListener 防止多次绑定造成异常\r\n    const conversationList = this.$refs.conversationList;\r\n\r\n    conversationList.removeEventListener(\r\n      \"tapConversation\",\r\n      this.handleTapConversation\r\n    );\r\n\r\n    conversationList.removeEventListener(\r\n      \"deleteConversation\",\r\n      this.handleDeleteConversation\r\n    );\r\n  },\r\n  methods: {\r\n    handleVideoDialogClose() {\r\n      this.videoDialogVisible = false;\r\n    },\r\n    handleImageDialogClose() {\r\n      this.imageDialogVisible = false;\r\n    },\r\n    handleTapConversation() {\r\n      //处理点击会话后的操作\r\n      console.info(\"处理点击会话后的操作11111\");\r\n    },\r\n    handleDeleteConversation() {\r\n      //处理删除会话后的操作\r\n      console.info(\"处理点击会话后的操作\");\r\n    },\r\n    handleTapMessage(e) {\r\n      const data = e.detail;\r\n      // 处理点击查看大图或文件消息下载等功能\r\n      console.log(\"点击消息触发监听:\", data);\r\n      if (data.type == \"file\") {\r\n        let url = data.url.replace(\r\n          \"http://rongcloud-file.ronghub.com\",\r\n          \"https://cy.ningmengdou.com/ryim\"\r\n        );\r\n        FileSaver.saveAs(url, data.name);\r\n      } else if (data.type == \"sight\") {\r\n        this.videoUrl = data.url;\r\n        this.videoDialogVisible = true;\r\n      } else if (data.type == \"image\") {\r\n        this.imageUrl = data.url;\r\n        this.imageDialogVisible = true;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n\r\n  .conversation-list {\r\n    height: 700px;\r\n    width: 300px;\r\n  }\r\n  .user-message-container {\r\n    height: 700px;\r\n    background: #fff;\r\n    .none-class {\r\n      text-align: center;\r\n      padding: 20% 0;\r\n      height: 700px;\r\n\r\n      .text {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n        line-height: 14px;\r\n      }\r\n    }\r\n    .message-box {\r\n      height: 400px;\r\n      .message-list {\r\n      }\r\n      .no-data {\r\n      }\r\n    }\r\n\r\n    .editor-box {\r\n      height: 300px;\r\n      .editor-tool-bar {\r\n        height: 9em;\r\n      }\r\n      .editor-content-wrapper {\r\n        padding: 1em 4em;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  .user-message-container {\r\n    .message-box {\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}