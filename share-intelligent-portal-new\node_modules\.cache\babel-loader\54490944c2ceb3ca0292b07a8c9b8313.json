{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\application\\publish.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\application\\publish.vue", "mtime": 1750311963042}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_auth", "_appliMarket", "_store", "_default", "exports", "default", "name", "dicts", "components", "UserMenu", "data", "active", "ruleForm", "appName", "appCategory", "applicaServer", "delivery", "briefInto", "content", "appLogo", "supply", "linkman", "phone", "serverIp", "webUrl", "webexperienceUrl", "serverexamineUrl", "developmentPeople", "spec", "userNumber", "validTime", "orderCode", "price", "promotionPrice", "commissionRatio", "rules", "required", "message", "trigger", "consultingTelephone", "operationTelephone", "personalCardList", "tableData", "date", "address", "shopData", "id", "type", "dialogVisible", "dialogpriceVisible", "uploadUrl", "process", "env", "VUE_APP_BASE_API", "accept", "headers", "Authorization", "getToken", "appliTypeData", "dict<PERSON><PERSON>ue", "dict<PERSON><PERSON>l", "created", "getDetail", "methods", "_this", "$route", "query", "params", "userId", "store", "getters", "appliDetail", "then", "res", "code", "url", "typeFormat", "row", "column", "selectDictLabel", "dict", "sys_oper_type", "businessType", "handleView", "open", "form", "handleDelete", "_this2", "operIds", "operId", "ids", "$modal", "confirm", "delOperlog", "getList", "msgSuccess", "catch", "handleCurrentChange", "pageNum", "queryParams", "prevStep", "nextStep", "_this3", "$refs", "appliForm", "validate", "valid", "console", "log", "appliForm1", "addSpecification", "handleClose", "addPrice", "handlepriceClose", "handleBeforeUpload", "file", "size", "typeList", "split", "map", "item", "trim", "toLowerCase", "substr", "dotIndex", "lastIndexOf", "$message", "error", "suffix", "substring", "indexOf", "handlePersonalCardPreview", "imageUrl", "imgVisible", "handleRemove", "fileList", "handlePersonalCardSuccess", "push", "handelExceed", "warning", "addShopData", "length", "delShopData", "_this4", "for<PERSON>ach", "index", "splice", "submitData", "_this5", "appliForm2", "appliEdit", "success", "$router", "path", "appliAdd"], "sources": ["src/views/system/user/application/publish.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"container\">\r\n          <div class=\"content\">\r\n            <!-- 步骤条 -->\r\n            <div class=\"stepsStyle\">\r\n              <el-steps :active=\"active\">\r\n                <el-step title=\"创建应用\" description=\"去创建\"></el-step>\r\n                <el-step title=\"配置开发管理\" description=\"地址配置\"></el-step>\r\n                <!-- <el-step title=\"应用测试\" description=\"查看调试文档\"></el-step> -->\r\n                <el-step title=\"上架应用\" description=\"去上架\"></el-step>\r\n              </el-steps>\r\n            </div>\r\n            <div class=\"currentContent\">\r\n              <div v-show=\"active == 0\">\r\n                <div class=\"title\">基本信息</div>\r\n                <el-form\r\n                  :model=\"ruleForm\"\r\n                  :rules=\"rules\"\r\n                  ref=\"appliForm\"\r\n                  label-width=\"100px\"\r\n                  class=\"demo-ruleForm\"\r\n                >\r\n                  <el-form-item label=\"应用名称\" prop=\"appName\">\r\n                    <el-input v-model=\"ruleForm.appName\"></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用类型\" prop=\"appCategory\">\r\n                    <el-select\r\n                      v-model=\"ruleForm.appCategory\"\r\n                      placeholder=\"请选择应用类型\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"(item, index) in appliTypeData\"\r\n                        :key=\"index\"\r\n                        :label=\"item.dictLabel\"\r\n                        :value=\"item.dictLabel\"\r\n                      ></el-option>\r\n                    </el-select>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用服务端\" prop=\"applicaServer\">\r\n                    <el-radio-group v-model=\"ruleForm.applicaServer\">\r\n                      <el-radio label=\"0\">APP端</el-radio>\r\n                      <el-radio label=\"1\">web端</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"交付方式\" prop=\"delivery\">\r\n                    <el-radio-group v-model=\"ruleForm.delivery\">\r\n                      <el-radio label=\"0\">Saas服务</el-radio>\r\n                      <el-radio label=\"1\">本地部署</el-radio>\r\n                    </el-radio-group>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用简介\" prop=\"briefInto\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.briefInto\"\r\n                      type=\"textarea\"\r\n                      :rows=\"3\"\r\n                      resize=\"none\"\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用详情\" prop=\"content\">\r\n                    <editor v-model=\"ruleForm.content\" :min-height=\"192\" />\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用封面\" prop=\"appLogo\">\r\n                    <el-upload\r\n                      :limit=\"1\"\r\n                      list-type=\"picture-card\"\r\n                      :headers=\"headers\"\r\n                      :action=\"uploadUrl\"\r\n                      :file-list=\"personalCardList\"\r\n                      :accept=\"accept\"\r\n                      :before-upload=\"handleBeforeUpload\"\r\n                      :on-preview=\"handlePersonalCardPreview\"\r\n                      :on-remove=\"handleRemove\"\r\n                      :on-success=\"handlePersonalCardSuccess\"\r\n                      :on-exceed=\"handelExceed\"\r\n                    >\r\n                      <i class=\"el-icon-plus\"></i>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"应用提供\" prop=\"supply\">\r\n                    <el-input v-model=\"ruleForm.supply\"></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"联系人\" prop=\"linkman\">\r\n                    <el-input v-model=\"ruleForm.linkman\"></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"联系方式\" prop=\"phone\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.phone\"\r\n                      @input=\"\r\n                        ruleForm.phone = ruleForm.phone.replace(/[^0-9.]/g, '')\r\n                      \"\r\n                      maxlength=\"11\"\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                </el-form>\r\n              </div>\r\n              <div v-show=\"active == 1\">\r\n                <div class=\"title\">开发管理配置</div>\r\n                <el-form\r\n                  :model=\"ruleForm\"\r\n                  :rules=\"rules\"\r\n                  ref=\"appliForm1\"\r\n                  label-width=\"160px\"\r\n                  class=\"demo-ruleForm\"\r\n                >\r\n                  <el-form-item label=\"服务器出口IP\">\r\n                    <el-input\r\n                      v-model=\"ruleForm.serverIp\"\r\n                      @input=\"\r\n                        ruleForm.serverIp = ruleForm.serverIp.replace(\r\n                          /[^0-9.]/g,\r\n                          ''\r\n                        )\r\n                      \"\r\n                    ></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"网页端(Web)应用地址\" prop=\"webUrl\">\r\n                    <el-input v-model=\"ruleForm.webUrl\"></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item\r\n                    label=\"网页端(Web)体验地址\"\r\n                    prop=\"webexperienceUrl\"\r\n                  >\r\n                    <el-input v-model=\"ruleForm.webexperienceUrl\"></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item\r\n                    label=\"健康检查服务端地址\"\r\n                    prop=\"serverexamineUrl\"\r\n                  >\r\n                    <el-input v-model=\"ruleForm.serverexamineUrl\"></el-input>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"开发联系人\" prop=\"developmentPeople\">\r\n                    <el-input v-model=\"ruleForm.developmentPeople\"></el-input>\r\n                  </el-form-item>\r\n                  <!-- <el-form-item label=\"获取测试token\">\r\n                    <div>点此获取</div>\r\n                  </el-form-item> -->\r\n                </el-form>\r\n              </div>\r\n              <div v-show=\"active == 2\">\r\n                <div class=\"title\">商品规格信息</div>\r\n                <div>\r\n                  <el-form\r\n                    :model=\"ruleForm\"\r\n                    :rules=\"rules\"\r\n                    ref=\"appliForm2\"\r\n                    label-width=\"180px\"\r\n                    class=\"demo-ruleForm\"\r\n                  >\r\n                    <el-form-item label=\"规格\" prop=\"spec\">\r\n                      <el-input v-model=\"ruleForm.spec\"></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"使用用户数\" prop=\"userNumber\">\r\n                      <el-input\r\n                        v-model=\"ruleForm.userNumber\"\r\n                        @input=\"\r\n                          ruleForm.userNumber = ruleForm.userNumber.replace(\r\n                            /[^0-9.]/g,\r\n                            ''\r\n                          )\r\n                        \"\r\n                      ></el-input>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"有效时间\" prop=\"validTime\">\r\n                      <el-date-picker\r\n                        v-model=\"ruleForm.validTime\"\r\n                        type=\"datetime\"\r\n                        placeholder=\"请选择有效时间\"\r\n                      >\r\n                      </el-date-picker>\r\n                    </el-form-item>\r\n                    <div style=\"margin-top: 20px\">\r\n                      <div class=\"title\">商品价格信息</div>\r\n                      <div style=\"margin-top: 20px\">\r\n                        <el-form-item label=\"订货编码\" prop=\"orderCode\">\r\n                          <el-input\r\n                            v-model=\"ruleForm.orderCode\"\r\n                            @input=\"\r\n                              ruleForm.orderCode = ruleForm.orderCode.replace(\r\n                                /[^\\w\\.\\/]/g,\r\n                                ''\r\n                              )\r\n                            \"\r\n                          ></el-input>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"商品原价（元）\" prop=\"price\">\r\n                          <el-input\r\n                            v-model=\"ruleForm.price\"\r\n                            @input=\"\r\n                              ruleForm.price = ruleForm.price.replace(\r\n                                /[^0-9.]/g,\r\n                                ''\r\n                              )\r\n                            \"\r\n                          ></el-input>\r\n                        </el-form-item>\r\n                        <el-form-item\r\n                          label=\"商品促销价（元）\"\r\n                          prop=\"promotionPrice\"\r\n                        >\r\n                          <el-input\r\n                            v-model=\"ruleForm.promotionPrice\"\r\n                            @input=\"\r\n                              ruleForm.promotionPrice =\r\n                                ruleForm.promotionPrice.replace(/[^0-9.]/g, '')\r\n                            \"\r\n                          ></el-input>\r\n                        </el-form-item>\r\n                        <el-form-item label=\"商品分佣比例（%）\">\r\n                          <el-input\r\n                            v-model=\"ruleForm.commissionRatio\"\r\n                            @input=\"\r\n                              ruleForm.commissionRatio =\r\n                                ruleForm.commissionRatio.replace(/[^0-9.]/g, '')\r\n                            \"\r\n                          ></el-input>\r\n                        </el-form-item>\r\n                        <!-- <div class=\"buttonStyle\" @click=\"addPrice\">新增价格</div>\r\n                          <div style=\"margin-top: 20px\">\r\n                            <el-table\r\n                              :data=\"tableData\"\r\n                              style=\"width: 100%; mni-height: 200px\"\r\n                            >\r\n                              <el-table-column prop=\"date\" label=\"规格\">\r\n                              </el-table-column>\r\n                              <el-table-column prop=\"name\" label=\"订货编码\">\r\n                              </el-table-column>\r\n                              <el-table-column prop=\"name\" label=\"商品原价（元）\">\r\n                              </el-table-column>\r\n                              <el-table-column prop=\"name\" label=\"商品促销价（元）\">\r\n                              </el-table-column>\r\n                              <el-table-column prop=\"name\" label=\"商品分佣比例（%）\">\r\n                              </el-table-column>\r\n                              <el-table-column label=\"操作\">\r\n                                <template>\r\n                                  <div>删除</div>\r\n                                </template>\r\n                              </el-table-column>\r\n                            </el-table>\r\n                          </div> -->\r\n                      </div>\r\n                    </div>\r\n                    <div style=\"margin-top: 20px\">\r\n                      <div class=\"title\">商品参数介绍</div>\r\n                      <div style=\"margin-top: 20px\">\r\n                        <el-form-item\r\n                          label=\"服务咨询电话\"\r\n                          prop=\"consultingTelephone\"\r\n                        >\r\n                          <el-input\r\n                            v-model=\"ruleForm.consultingTelephone\"\r\n                            @input=\"\r\n                              ruleForm.consultingTelephone =\r\n                                ruleForm.consultingTelephone.replace(\r\n                                  /[^0-9.]/g,\r\n                                  ''\r\n                                )\r\n                            \"\r\n                          ></el-input>\r\n                        </el-form-item>\r\n                        <el-form-item\r\n                          label=\"产品运营联系人手机号\"\r\n                          prop=\"operationTelephone\"\r\n                        >\r\n                          <el-input\r\n                            v-model=\"ruleForm.operationTelephone\"\r\n                            @input=\"\r\n                              ruleForm.operationTelephone =\r\n                                ruleForm.operationTelephone.replace(\r\n                                  /[^0-9.]/g,\r\n                                  ''\r\n                                )\r\n                            \"\r\n                          ></el-input>\r\n                        </el-form-item>\r\n                        <!-- <el-table\r\n                      :data=\"shopData\"\r\n                      style=\"width: 100%; mni-height: 200px\"\r\n                    >\r\n                      <el-table-column label=\"名称\">\r\n                        <template slot-scope=\"scoped\">\r\n                          <div v-if=\"scoped.row.type == 1\">\r\n                            {{ scoped.row.name }}\r\n                          </div>\r\n                          <div v-if=\"scoped.row.type == 2\">\r\n                            <el-input\r\n                              v-model=\"scoped.row.name\"\r\n                              placeholder=\"请输入\"\r\n                            ></el-input>\r\n                          </div>\r\n                        </template>\r\n                      </el-table-column>\r\n                      <el-table-column label=\"内容\">\r\n                        <template slot-scope=\"scoped\">\r\n                          <el-input\r\n                            v-model=\"scoped.row.content\"\r\n                            placeholder=\"请输入\"\r\n                          ></el-input>\r\n                        </template>\r\n                      </el-table-column>\r\n                      <el-table-column label=\"操作\">\r\n                        <template slot-scope=\"scoped\">\r\n                          <div v-if=\"scoped.row.type == 1\">-</div>\r\n                          <div\r\n                            style=\"color: #21C9B8; cursor: pointer\"\r\n                            v-if=\"scoped.row.type == 2\"\r\n                            @click=\"delShopData(scoped.row.id)\"\r\n                          >\r\n                            删除\r\n                          </div>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                    <div\r\n                      style=\"color: #21C9B8; margin-top: 20px; cursor: pointer\"\r\n                      @click=\"addShopData\"\r\n                    >\r\n                      新增参数\r\n                    </div> -->\r\n                      </div>\r\n                    </div>\r\n                  </el-form>\r\n                  <!-- <div class=\"buttonStyle\" @click=\"addSpecification\">\r\n                    新增规格\r\n                  </div>\r\n                  <div style=\"margin-top: 20px\">\r\n                    <el-table\r\n                      :data=\"tableData\"\r\n                      style=\"width: 100%; mni-height: 200px\"\r\n                    >\r\n                      <el-table-column prop=\"date\" label=\"规格\" width=\"180\">\r\n                      </el-table-column>\r\n                      <el-table-column prop=\"name\" label=\"规格信息\" width=\"180\">\r\n                      </el-table-column>\r\n                      <el-table-column label=\"操作\">\r\n                        <template>\r\n                          <div>删除</div>\r\n                        </template>\r\n                      </el-table-column>\r\n                    </el-table>\r\n                  </div> -->\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div class=\"submitStyle\">\r\n              <div v-show=\"active !== 0\" class=\"buttonStyle\" @click=\"prevStep\">\r\n                上一步\r\n              </div>\r\n              <div v-show=\"active !== 2\" class=\"buttonStyle\" @click=\"nextStep\">\r\n                下一步\r\n              </div>\r\n              <div\r\n                v-show=\"active === 2\"\r\n                class=\"buttonStyle\"\r\n                @click=\"submitData\"\r\n              >\r\n                提交\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      title=\"新增规格\"\r\n      :visible.sync=\"dialogVisible\"\r\n      width=\"30%\"\r\n      :before-close=\"handleClose\"\r\n    >\r\n      <div>\r\n        <el-form\r\n          :model=\"ruleForm\"\r\n          :rules=\"rules\"\r\n          ref=\"ruleForm\"\r\n          label-width=\"80px\"\r\n          class=\"demo-ruleForm\"\r\n        >\r\n          <el-form-item label=\"规格\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"规格信息\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"dialogVisible = false\"\r\n          >确 定</el-button\r\n        >\r\n      </span>\r\n    </el-dialog>\r\n    <el-dialog\r\n      title=\"新增价格\"\r\n      :visible.sync=\"dialogpriceVisible\"\r\n      width=\"30%\"\r\n      :before-close=\"handlepriceClose\"\r\n    >\r\n      <div>\r\n        <el-form\r\n          :model=\"ruleForm\"\r\n          :rules=\"rules\"\r\n          ref=\"ruleForm\"\r\n          label-width=\"150px\"\r\n          class=\"demo-ruleForm\"\r\n        >\r\n          <el-form-item label=\"规格\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"订货编码\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"商品原价（元）\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"商品促销价（元）\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"商品分佣比例（%）\" prop=\"name\">\r\n            <el-input v-model=\"ruleForm.name\"></el-input>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogpriceVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"dialogpriceVisible = false\"\r\n          >确 定</el-button\r\n        >\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import { list, delOperlog, cleanOperlog } from \"@/api/system/operlog\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { appliAdd, appliEdit, appliDetail } from \"@/api/appliMarket\";\r\nimport store from \"@/store\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      active: 0,\r\n      ruleForm: {\r\n        appName: \"\",\r\n        appCategory: \"\",\r\n        applicaServer: \"\",\r\n        delivery: \"\",\r\n        briefInto: \"\",\r\n        content: \"\",\r\n        appLogo: \"\",\r\n        supply: \"\",\r\n        linkman: \"\",\r\n        phone: \"\",\r\n        serverIp: \"\",\r\n        webUrl: \"\",\r\n        webexperienceUrl: \"\",\r\n        serverexamineUrl: \"\",\r\n        developmentPeople: \"\",\r\n        spec: \"\",\r\n        userNumber: \"\",\r\n        validTime: \"\",\r\n        orderCode: \"\",\r\n        price: \"\",\r\n        promotionPrice: \"\",\r\n        commissionRatio: \"\",\r\n      },\r\n      rules: {\r\n        appName: [\r\n          { required: true, message: \"请输入应用名称\", trigger: \"blur\" },\r\n        ],\r\n        appCategory: [\r\n          { required: true, message: \"请选择应用类型\", trigger: \"change\" },\r\n        ],\r\n        applicaServer: [\r\n          { required: true, message: \"请选择应用服务端\", trigger: \"change\" },\r\n        ],\r\n        delivery: [\r\n          { required: true, message: \"请选择交付方式\", trigger: \"change\" },\r\n        ],\r\n        briefInto: [\r\n          { required: true, message: \"请输入应用简介\", trigger: \"blur\" },\r\n        ],\r\n        content: [\r\n          { required: true, message: \"请输入应用详情\", trigger: \"blur\" },\r\n        ],\r\n        appLogo: [\r\n          { required: true, message: \"请上传应用封面图片\", trigger: \"change\" },\r\n        ],\r\n        supply: [\r\n          { required: true, message: \"请输入应用提供\", trigger: \"blur\" },\r\n        ],\r\n        linkman: [{ required: true, message: \"请输入联系人\", trigger: \"blur\" }],\r\n        phone: [{ required: true, message: \"请输入联系方式\", trigger: \"blur\" }],\r\n        webUrl: [\r\n          {\r\n            required: true,\r\n            message: \"请输入网页端(Web)应用地址\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        webexperienceUrl: [\r\n          {\r\n            required: true,\r\n            message: \"请输入网页端(Web)体验地址\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        serverexamineUrl: [\r\n          {\r\n            required: true,\r\n            message: \"请输入健康检查服务端地址\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        developmentPeople: [\r\n          {\r\n            required: true,\r\n            message: \"请输入开发联系人\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n\r\n        spec: [\r\n          {\r\n            required: true,\r\n            message: \"请输入规格\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        userNumber: [\r\n          {\r\n            required: true,\r\n            message: \"请输入使用用户数\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        validTime: [\r\n          {\r\n            required: true,\r\n            message: \"请选择有效时间\",\r\n            trigger: \"change\",\r\n          },\r\n        ],\r\n        orderCode: [\r\n          {\r\n            required: true,\r\n            message: \"请输入订货编码\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        price: [\r\n          {\r\n            required: true,\r\n            message: \"请输入商品原价\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        promotionPrice: [\r\n          {\r\n            required: true,\r\n            message: \"请输入商品促销价\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        consultingTelephone: [\r\n          {\r\n            required: true,\r\n            message: \"请输入服务咨询电话\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        operationTelephone: [\r\n          {\r\n            required: true,\r\n            message: \"请输入产品运营联系人手机号\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        // date1: [\r\n        //   {\r\n        //     type: \"date\",\r\n        //     required: true,\r\n        //     message: \"请选择日期\",\r\n        //     trigger: \"change\",\r\n        //   },\r\n        // ],\r\n        // date2: [\r\n        //   {\r\n        //     type: \"date\",\r\n        //     required: true,\r\n        //     message: \"请选择时间\",\r\n        //     trigger: \"change\",\r\n        //   },\r\n        // ],\r\n      },\r\n      personalCardList: [],\r\n      tableData: [\r\n        {\r\n          date: \"2016-05-02\",\r\n          name: \"王小虎\",\r\n          address: \"上海市普陀区金沙江路 1518 弄\",\r\n        },\r\n        {\r\n          date: \"2016-05-04\",\r\n          name: \"王小虎\",\r\n          address: \"上海市普陀区金沙江路 1517 弄\",\r\n        },\r\n        {\r\n          date: \"2016-05-01\",\r\n          name: \"王小虎\",\r\n          address: \"上海市普陀区金沙江路 1519 弄\",\r\n        },\r\n        {\r\n          date: \"2016-05-03\",\r\n          name: \"王小虎\",\r\n          address: \"上海市普陀区金沙江路 1516 弄\",\r\n        },\r\n      ],\r\n      shopData: [\r\n        {\r\n          id: 1,\r\n          name: \"服务咨询电话\",\r\n          content: \"\",\r\n          type: 1,\r\n        },\r\n        {\r\n          id: 2,\r\n          name: \"产品运营联系人手机号\",\r\n          content: \"\",\r\n          type: 1,\r\n        },\r\n      ],\r\n      dialogVisible: false,\r\n      dialogpriceVisible: false,\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      appliTypeData: [\r\n        {\r\n          dictValue: \"1\",\r\n          dictLabel: \"研发设计\",\r\n        },\r\n        {\r\n          dictValue: \"2\",\r\n          dictLabel: \"生产制造\",\r\n        },\r\n        {\r\n          dictValue: \"3\",\r\n          dictLabel: \"运营管理\",\r\n        },\r\n        {\r\n          dictValue: \"4\",\r\n          dictLabel: \"质量管控\",\r\n        },\r\n        {\r\n          dictValue: \"5\",\r\n          dictLabel: \"仓储物流\",\r\n        },\r\n        {\r\n          dictValue: \"6\",\r\n          dictLabel: \"安全生产\",\r\n        },\r\n        {\r\n          dictValue: \"7\",\r\n          dictLabel: \"节能减排\",\r\n        },\r\n        {\r\n          dictValue: \"8\",\r\n          dictLabel: \"运维服务\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    getDetail() {\r\n      let id = this.$route.query.id;\r\n      if (id) {\r\n        let params = {\r\n          id,\r\n          userId: store.getters.userId,\r\n        };\r\n        appliDetail(params).then((res) => {\r\n          if (res.code === 200) {\r\n            this.ruleForm = res.data;\r\n            this.personalCardList = [{ url: this.ruleForm.appLogo }];\r\n          }\r\n        });\r\n      }\r\n    },\r\n    // 操作日志类型字典翻译\r\n    typeFormat(row, column) {\r\n      return this.selectDictLabel(\r\n        this.dict.type.sys_oper_type,\r\n        row.businessType\r\n      );\r\n    },\r\n    /** 详细按钮操作 */\r\n    handleView(row) {\r\n      this.open = true;\r\n      this.form = row;\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const operIds = row.operId || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除日志编号为\"' + operIds + '\"的数据项？')\r\n        .then(function () {\r\n          return delOperlog(operIds);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    prevStep() {\r\n      this.active--;\r\n    },\r\n    nextStep() {\r\n      if (this.active == 0) {\r\n        this.$refs.appliForm.validate((valid) => {\r\n          if (valid) {\r\n            console.log(this.active, \"执行了吗1----------\");\r\n            this.active++;\r\n            // this.$nextTick(() => {\r\n            //   this.$refs.appliForm1.clearValidate(); // 只清除清除验证\r\n            // });\r\n          } else {\r\n            console.log(\"error submit!!\");\r\n            return false;\r\n          }\r\n        });\r\n      } else if (this.active == 1) {\r\n        this.$refs.appliForm1.validate((valid) => {\r\n          if (valid) {\r\n            console.log(this.active, \"执行了吗2----------\");\r\n            this.active++;\r\n          } else {\r\n            console.log(\"error submit!!\");\r\n            return false;\r\n          }\r\n        });\r\n      }\r\n    },\r\n    addSpecification() {\r\n      this.dialogVisible = true;\r\n    },\r\n    handleClose() {\r\n      this.dialogVisible = false;\r\n    },\r\n    addPrice() {\r\n      this.dialogpriceVisible = true;\r\n    },\r\n    handlepriceClose() {\r\n      this.dialogpriceVisible = false;\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePersonalCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.personalCardList = [];\r\n      this.ruleForm.appLogo = \"\";\r\n    },\r\n    handlePersonalCardSuccess(res, file, fileList) {\r\n      if (!this.personalCardList) {\r\n        this.personalCardList = [];\r\n        this.ruleForm.appLogo = \"\";\r\n      }\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.personalCardList.push(res.data);\r\n        this.ruleForm.appLogo = res.data.url;\r\n      }\r\n    },\r\n    handelExceed(file, fileList) {\r\n      this.$message.warning(\"超出图片个数限制\");\r\n    },\r\n    addShopData() {\r\n      let id = this.shopData.length + 1;\r\n      let data = {\r\n        id,\r\n        name: \"\",\r\n        content: \"\",\r\n        type: 2,\r\n      };\r\n      this.shopData.push(data);\r\n    },\r\n    delShopData(id) {\r\n      this.shopData.forEach((item, index) => {\r\n        if (item.id === id) {\r\n          this.shopData.splice(index, 1);\r\n        }\r\n      });\r\n    },\r\n    submitData() {\r\n      this.$refs.appliForm2.validate((valid) => {\r\n        if (valid) {\r\n          if (this.$route.query.id) {\r\n            appliEdit(this.ruleForm).then((res) => {\r\n              if (res.code === 200) {\r\n                this.$message.success(\"操作成功!\");\r\n                this.$router.push({\r\n                  path: \"/user/application\",\r\n                });\r\n              }\r\n            });\r\n          } else {\r\n            appliAdd(this.ruleForm).then((res) => {\r\n              if (res.code === 200) {\r\n                this.$message.success(\"操作成功!\");\r\n                this.$router.push({\r\n                  path: \"/user/application\",\r\n                });\r\n              }\r\n            });\r\n          }\r\n        } else {\r\n          console.log(\"error submit!!\");\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.container {\r\n  width: 100%;\r\n  background: #ffffff;\r\n  padding: 40px;\r\n}\r\n.content {\r\n  width: 100%;\r\n  // background: #ffffff;\r\n  .stepsStyle {\r\n    padding: 0 13%;\r\n  }\r\n  .currentContent {\r\n    margin-top: 30px;\r\n    padding-left: 5%;\r\n    padding-right: 40%;\r\n  }\r\n  .title {\r\n    font-size: 22px;\r\n    font-weight: 600;\r\n    margin-bottom: 30px;\r\n    margin-left: 20px;\r\n  }\r\n  .submitStyle {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: right;\r\n  }\r\n  .buttonStyle {\r\n    width: 100px;\r\n    padding: 10px;\r\n    background: #21c9b8;\r\n    color: #ffffff;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    border-radius: 4px;\r\n    margin-right: 20px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAwbA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAJ,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAJA;AAAA,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMA;EACAC,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,QAAA;QACAC,OAAA;QACAC,WAAA;QACAC,aAAA;QACAC,QAAA;QACAC,SAAA;QACAC,OAAA;QACAC,OAAA;QACAC,MAAA;QACAC,OAAA;QACAC,KAAA;QACAC,QAAA;QACAC,MAAA;QACAC,gBAAA;QACAC,gBAAA;QACAC,iBAAA;QACAC,IAAA;QACAC,UAAA;QACAC,SAAA;QACAC,SAAA;QACAC,KAAA;QACAC,cAAA;QACAC,eAAA;MACA;MACAC,KAAA;QACAtB,OAAA,GACA;UAAAuB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAxB,WAAA,GACA;UAAAsB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAvB,aAAA,GACA;UAAAqB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAtB,QAAA,GACA;UAAAoB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACArB,SAAA,GACA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACApB,OAAA,GACA;UAAAkB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,OAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAlB,MAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAjB,OAAA;UAAAe,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAhB,KAAA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAd,MAAA,GACA;UACAY,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAb,gBAAA,GACA;UACAW,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAZ,gBAAA,GACA;UACAU,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAX,iBAAA,GACA;UACAS,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QAEAV,IAAA,GACA;UACAQ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAT,UAAA,GACA;UACAO,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAR,SAAA,GACA;UACAM,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAP,SAAA,GACA;UACAK,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAN,KAAA,GACA;UACAI,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAL,cAAA,GACA;UACAG,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,mBAAA,GACA;UACAH,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAE,kBAAA,GACA;UACAJ,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAG,gBAAA;MACAC,SAAA,GACA;QACAC,IAAA;QACArC,IAAA;QACAsC,OAAA;MACA,GACA;QACAD,IAAA;QACArC,IAAA;QACAsC,OAAA;MACA,GACA;QACAD,IAAA;QACArC,IAAA;QACAsC,OAAA;MACA,GACA;QACAD,IAAA;QACArC,IAAA;QACAsC,OAAA;MACA,EACA;MACAC,QAAA,GACA;QACAC,EAAA;QACAxC,IAAA;QACAY,OAAA;QACA6B,IAAA;MACA,GACA;QACAD,EAAA;QACAxC,IAAA;QACAY,OAAA;QACA6B,IAAA;MACA,EACA;MACAC,aAAA;MACAC,kBAAA;MACAC,SAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,MAAA;MACAC,OAAA;QAAAC,aAAA,kBAAAC,cAAA;MAAA;MACAC,aAAA,GACA;QACAC,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAD,SAAA,WAAAA,UAAA;MAAA,IAAAE,KAAA;MACA,IAAAlB,EAAA,QAAAmB,MAAA,CAAAC,KAAA,CAAApB,EAAA;MACA,IAAAA,EAAA;QACA,IAAAqB,MAAA;UACArB,EAAA,EAAAA,EAAA;UACAsB,MAAA,EAAAC,cAAA,CAAAC,OAAA,CAAAF;QACA;QACA,IAAAG,wBAAA,EAAAJ,MAAA,EAAAK,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACAV,KAAA,CAAApD,QAAA,GAAA6D,GAAA,CAAA/D,IAAA;YACAsD,KAAA,CAAAvB,gBAAA;cAAAkC,GAAA,EAAAX,KAAA,CAAApD,QAAA,CAAAO;YAAA;UACA;QACA;MACA;IACA;IACA;IACAyD,UAAA,WAAAA,WAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,CACA,KAAAC,IAAA,CAAAjC,IAAA,CAAAkC,aAAA,EACAJ,GAAA,CAAAK,YACA;IACA;IACA,aACAC,UAAA,WAAAA,WAAAN,GAAA;MACA,KAAAO,IAAA;MACA,KAAAC,IAAA,GAAAR,GAAA;IACA;IACA,aACAS,YAAA,WAAAA,aAAAT,GAAA;MAAA,IAAAU,MAAA;MACA,IAAAC,OAAA,GAAAX,GAAA,CAAAY,MAAA,SAAAC,GAAA;MACA,KAAAC,MAAA,CACAC,OAAA,kBAAAJ,OAAA,aACAhB,IAAA;QACA,OAAAqB,UAAA,CAAAL,OAAA;MACA,GACAhB,IAAA;QACAe,MAAA,CAAAO,OAAA;QACAP,MAAA,CAAAI,MAAA,CAAAI,UAAA;MACA,GACAC,KAAA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,OAAA;MACA,KAAAC,WAAA,CAAAD,OAAA,GAAAA,OAAA;MACA,KAAAJ,OAAA;IACA;IACAM,QAAA,WAAAA,SAAA;MACA,KAAAzF,MAAA;IACA;IACA0F,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,SAAA3F,MAAA;QACA,KAAA4F,KAAA,CAAAC,SAAA,CAAAC,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAC,OAAA,CAAAC,GAAA,CAAAN,MAAA,CAAA3F,MAAA;YACA2F,MAAA,CAAA3F,MAAA;YACA;YACA;YACA;UACA;YACAgG,OAAA,CAAAC,GAAA;YACA;UACA;QACA;MACA,gBAAAjG,MAAA;QACA,KAAA4F,KAAA,CAAAM,UAAA,CAAAJ,QAAA,WAAAC,KAAA;UACA,IAAAA,KAAA;YACAC,OAAA,CAAAC,GAAA,CAAAN,MAAA,CAAA3F,MAAA;YACA2F,MAAA,CAAA3F,MAAA;UACA;YACAgG,OAAA,CAAAC,GAAA;YACA;UACA;QACA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAA;MACA,KAAA9D,aAAA;IACA;IACA+D,WAAA,WAAAA,YAAA;MACA,KAAA/D,aAAA;IACA;IACAgE,QAAA,WAAAA,SAAA;MACA,KAAA/D,kBAAA;IACA;IACAgE,gBAAA,WAAAA,iBAAA;MACA,KAAAhE,kBAAA;IACA;IACA;IACAiE,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAA7G,IAAA,GAAA6G,IAAA,CAAA7G,IAAA;QAAAyC,IAAA,GAAAoE,IAAA,CAAApE,IAAA;QAAAqE,IAAA,GAAAD,IAAA,CAAAC,IAAA;MACA,IAAAC,QAAA,QAAA/D,MAAA,CACAgE,KAAA,MACAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA,GAAAC,WAAA,GAAAC,MAAA;MAAA;MACA,IAAAC,QAAA,GAAAtH,IAAA,CAAAuH,WAAA;MACA;MACA,IAAAD,QAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;QACA,IAAAC,MAAA,GAAA1H,IAAA,CAAA2H,SAAA,CAAAL,QAAA;QACA,IAAAP,QAAA,CAAAa,OAAA,CAAAF,MAAA,CAAAN,WAAA;UACA,KAAAI,QAAA,CAAAC,KAAA;UACA;QACA;MACA;MACA;MACA,IAAAX,IAAA;QACA,KAAAU,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACA;IACAI,yBAAA,WAAAA,0BAAAhB,IAAA;MACA,KAAAiB,QAAA,GAAAjB,IAAA,CAAAxC,GAAA;MACA,KAAA0D,UAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAAnB,IAAA,EAAAoB,QAAA;MACA,KAAA9F,gBAAA;MACA,KAAA7B,QAAA,CAAAO,OAAA;IACA;IACAqH,yBAAA,WAAAA,0BAAA/D,GAAA,EAAA0C,IAAA,EAAAoB,QAAA;MACA,UAAA9F,gBAAA;QACA,KAAAA,gBAAA;QACA,KAAA7B,QAAA,CAAAO,OAAA;MACA;MACA;MACA,IAAAsD,GAAA,CAAAC,IAAA;QACA,KAAAjC,gBAAA,CAAAgG,IAAA,CAAAhE,GAAA,CAAA/D,IAAA;QACA,KAAAE,QAAA,CAAAO,OAAA,GAAAsD,GAAA,CAAA/D,IAAA,CAAAiE,GAAA;MACA;IACA;IACA+D,YAAA,WAAAA,aAAAvB,IAAA,EAAAoB,QAAA;MACA,KAAAT,QAAA,CAAAa,OAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA,IAAA9F,EAAA,QAAAD,QAAA,CAAAgG,MAAA;MACA,IAAAnI,IAAA;QACAoC,EAAA,EAAAA,EAAA;QACAxC,IAAA;QACAY,OAAA;QACA6B,IAAA;MACA;MACA,KAAAF,QAAA,CAAA4F,IAAA,CAAA/H,IAAA;IACA;IACAoI,WAAA,WAAAA,YAAAhG,EAAA;MAAA,IAAAiG,MAAA;MACA,KAAAlG,QAAA,CAAAmG,OAAA,WAAAxB,IAAA,EAAAyB,KAAA;QACA,IAAAzB,IAAA,CAAA1E,EAAA,KAAAA,EAAA;UACAiG,MAAA,CAAAlG,QAAA,CAAAqG,MAAA,CAAAD,KAAA;QACA;MACA;IACA;IACAE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA7C,KAAA,CAAA8C,UAAA,CAAA5C,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAA0C,MAAA,CAAAnF,MAAA,CAAAC,KAAA,CAAApB,EAAA;YACA,IAAAwG,sBAAA,EAAAF,MAAA,CAAAxI,QAAA,EAAA4D,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA,CAAAC,IAAA;gBACA0E,MAAA,CAAAtB,QAAA,CAAAyB,OAAA;gBACAH,MAAA,CAAAI,OAAA,CAAAf,IAAA;kBACAgB,IAAA;gBACA;cACA;YACA;UACA;YACA,IAAAC,qBAAA,EAAAN,MAAA,CAAAxI,QAAA,EAAA4D,IAAA,WAAAC,GAAA;cACA,IAAAA,GAAA,CAAAC,IAAA;gBACA0E,MAAA,CAAAtB,QAAA,CAAAyB,OAAA;gBACAH,MAAA,CAAAI,OAAA,CAAAf,IAAA;kBACAgB,IAAA;gBACA;cACA;YACA;UACA;QACA;UACA9C,OAAA,CAAAC,GAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}