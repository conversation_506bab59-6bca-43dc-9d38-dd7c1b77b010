{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\newsCenter.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\newsCenter.vue", "mtime": 1750311962934}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBuZXdzTGlzdCB9IGZyb20gIkAvYXBpL25ld3NDZW50ZXIiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgZGF0YTogW10sDQogICAgICBwYWdlTnVtOiAxLA0KICAgICAgcGFnZVNpemU6IDQsDQogICAgICB0b3RhbDogMCwNCiAgICAgIGZsYWc6ICIxIiwNCiAgICAgIG5ld3NGaXJzdERhdGE6IHt9LA0KICAgICAgbmV3c1JpZ2h0TGlzdDogW10sDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICAvLyB0aGlzLmluaXREYXRhKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBpbml0RGF0YSgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICBwYWdlTnVtOiB0aGlzLnBhZ2VOdW0sDQogICAgICAgIHBhZ2VTaXplOiB0aGlzLnBhZ2VTaXplLA0KICAgICAgICBraW5kOiAiMCIsDQogICAgICAgIHR5cGVUb3A6IHRoaXMuZmxhZywNCiAgICAgIH07DQogICAgICBuZXdzTGlzdChwYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMubmV3c0ZpcnN0RGF0YSA9IHJlcy5yb3dzWzBdOw0KICAgICAgICAgIHRoaXMubmV3c1JpZ2h0TGlzdCA9IHJlcy5yb3dzLnNsaWNlKDEsIDQpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICAgIC8vIG5ld3NUeXBlKCkudGhlbigocmVzKSA9PiB7DQogICAgICAvLyAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAvLyAgICAgY29uc29sZS5sb2cocmVzLCAiLS0tLS0tLS0tLS0tLS0tLS0iKTsNCiAgICAgIC8vICAgfQ0KICAgICAgLy8gfSk7DQogICAgICAvLyBnZXREaWN0cygiY2FzZV9pbmR1c3RyeSIpLnRoZW4oKHJlcykgPT4gew0KICAgICAgLy8gICBjb25zdCB7IGNvZGUsIGRhdGEgPSBbXSB9ID0gcmVzOw0KICAgICAgLy8gICBpZiAoY29kZSA9PT0gMjAwKSB7DQogICAgICAvLyAgICAgdGhpcy5jYXNlVHlwZUxpc3QgPSBkYXRhOw0KICAgICAgLy8gICAgIHRoaXMuZ2V0Q2FzZUxpc3QoKTsNCiAgICAgIC8vICAgfQ0KICAgICAgLy8gfSk7DQogICAgfSwNCiAgICAvLyDot7PovazliLDor6bmg4XpobXpnaINCiAgICBnb0RldGFpbChpZCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICBwYXRoOiAiL25ld3NEZXRhaWwiLA0KICAgICAgICBxdWVyeTogew0KICAgICAgICAgIGlkLA0KICAgICAgICB9LA0KICAgICAgfSk7DQogICAgfSwNCiAgICBnb05ld3NDZW50ZXIoKSB7DQogICAgICBsZXQgcm91dGVEYXRhID0gdGhpcy4kcm91dGVyLnJlc29sdmUoew0KICAgICAgICBwYXRoOiAiL25ld3NDZW50ZXIiLA0KICAgICAgfSk7DQogICAgICB3aW5kb3cub3Blbihyb3V0ZURhdGEuaHJlZiwgIl9ibGFuayIpOw0KICAgIH0sDQogICAgZ2V0RmxhZyh2YWx1ZSkgew0KICAgICAgdGhpcy5mbGFnID0gdmFsdWU7DQogICAgICB0aGlzLmluaXREYXRhKCk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["newsCenter.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsHA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "newsCenter.vue", "sourceRoot": "src/views/components/home", "sourcesContent": ["<template>\r\n  <div\r\n    class=\"card-container wow animate__animated animate__fadeInUp\"\r\n    data-wow-duration=\"1s\"\r\n  >\r\n    <div class=\"enterpriseTitle\">\r\n      <div>新闻中心</div>\r\n      <div class=\"allEnterprise\" @click=\"goNewsCenter\">查看全部>></div>\r\n    </div>\r\n    <div class=\"content\" v-loading=\"loading\">\r\n      <div class=\"content_left\">\r\n        <div\r\n          class=\"platDynamics\"\r\n          :class=\"flag === '1' ? 'platDyHover' : ''\"\r\n          @click=\"getFlag('1')\"\r\n        >\r\n          <div class=\"platImg\">\r\n            <img\r\n              v-show=\"flag !== '1'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-show=\"flag === '1'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div class=\"platTitle\">平台动态</div>\r\n        </div>\r\n        <div\r\n          class=\"platDynamics\"\r\n          :class=\"flag === '2' ? 'platDyHover' : ''\"\r\n          @click=\"getFlag('2')\"\r\n        >\r\n          <div class=\"platImg\">\r\n            <img\r\n              v-show=\"flag !== '2'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-show=\"flag === '2'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div class=\"platTitle\">行业动态</div>\r\n        </div>\r\n        <div\r\n          class=\"platDynamics\"\r\n          :class=\"flag === '3' ? 'platDyHover' : ''\"\r\n          @click=\"getFlag('3')\"\r\n        >\r\n          <div class=\"platImg\">\r\n            <img\r\n              v-show=\"flag !== '3'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-show=\"flag === '3'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div class=\"platTitle\">政策法规</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content_middle\">\r\n        <div class=\"newsImg\">\r\n          <img src=\"../../../assets/images/home/<USER>\" alt=\"\" />\r\n        </div>\r\n        <div\r\n          class=\"newsContent\"\r\n          v-if=\"newsFirstData.updateTime\"\r\n          @click=\"goDetail(newsFirstData.id)\"\r\n        >\r\n          <div class=\"news_left\">\r\n            <div class=\"news_year\">\r\n              {{ newsFirstData.updateTime.slice(0, 7) }}\r\n            </div>\r\n            <div class=\"news_day\">\r\n              {{ newsFirstData.updateTime.slice(8, 10) }}\r\n            </div>\r\n          </div>\r\n          <div class=\"news_right\">\r\n            <div class=\"title\">{{ newsFirstData.title }}</div>\r\n            <div class=\"desc\">\r\n              {{ newsFirstData.brief }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content_right\">\r\n        <div\r\n          class=\"newsContent\"\r\n          v-for=\"item in newsRightList\"\r\n          :key=\"item.id\"\r\n          @click=\"goDetail(item.id)\"\r\n        >\r\n          <div class=\"news_left\">\r\n            <div class=\"news_year\">{{ item.updateTime.slice(0, 7) }}</div>\r\n            <div class=\"news_day\">{{ item.updateTime.slice(8, 10) }}</div>\r\n          </div>\r\n          <div class=\"news_right\">\r\n            <div class=\"title\">{{ item.title }}</div>\r\n            <div class=\"desc\">\r\n              {{ item.brief }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { newsList } from \"@/api/newsCenter\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 4,\r\n      total: 0,\r\n      flag: \"1\",\r\n      newsFirstData: {},\r\n      newsRightList: [],\r\n    };\r\n  },\r\n  created() {\r\n    // this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        kind: \"0\",\r\n        typeTop: this.flag,\r\n      };\r\n      newsList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.loading = false;\r\n          this.newsFirstData = res.rows[0];\r\n          this.newsRightList = res.rows.slice(1, 4);\r\n        }\r\n      });\r\n      // newsType().then((res) => {\r\n      //   if (res.code === 200) {\r\n      //     console.log(res, \"-----------------\");\r\n      //   }\r\n      // });\r\n      // getDicts(\"case_industry\").then((res) => {\r\n      //   const { code, data = [] } = res;\r\n      //   if (code === 200) {\r\n      //     this.caseTypeList = data;\r\n      //     this.getCaseList();\r\n      //   }\r\n      // });\r\n    },\r\n    // 跳转到详情页面\r\n    goDetail(id) {\r\n      this.$router.push({\r\n        path: \"/newsDetail\",\r\n        query: {\r\n          id,\r\n        },\r\n      });\r\n    },\r\n    goNewsCenter() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/newsCenter\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    getFlag(value) {\r\n      this.flag = value;\r\n      this.initData();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.enterpriseTitle {\r\n  width: 100%;\r\n  font-size: 36px;\r\n  text-align: center;\r\n  margin: 60px 0;\r\n  position: relative;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  color: #000000;\r\n  .allEnterprise {\r\n    position: absolute;\r\n    top: 8 px;\r\n    right: 0;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    line-height: 26px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.content {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 330px;\r\n  margin-bottom: 86px;\r\n  .content_left {\r\n    width: 300px;\r\n    height: 100%;\r\n    .platDynamics {\r\n      width: 100%;\r\n      height: 96px;\r\n      background-color: rgb(229, 247, 243);\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin-top: 21px;\r\n      cursor: pointer;\r\n      .platImg {\r\n        width: 42px;\r\n        height: 40px;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .platTitle {\r\n        font-size: 20px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #21c9b8;\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n    .platDynamics:nth-child(1) {\r\n      margin-top: 0;\r\n    }\r\n    .platDyHover {\r\n      background: #21c9b8;\r\n      .platTitle {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n  .content_middle {\r\n    width: 460px;\r\n    height: 100%;\r\n    margin: 0 20px 0 30px;\r\n  }\r\n  .content_right {\r\n    width: 390px;\r\n    height: 100%;\r\n  }\r\n  .newsImg {\r\n    width: 100%;\r\n    height: 220px;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .newsContent {\r\n    width: 100%;\r\n    height: 110px;\r\n    background: #f9f9f9;\r\n    padding: 22px 26px;\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    .news_left {\r\n      width: 100px;\r\n      font-size: 16px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #666666;\r\n      margin-right: 20px;\r\n      text-align: center;\r\n      .news_year {\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #666666;\r\n      }\r\n      .news_day {\r\n        font-size: 30px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #222222;\r\n        line-height: 26px;\r\n        margin-top: 10px;\r\n      }\r\n    }\r\n    .news_right {\r\n      width: calc(100% - 100px);\r\n      .title {\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #222222;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 1;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n      .desc {\r\n        margin-top: 13px;\r\n        font-size: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #666666;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n    }\r\n  }\r\n  .newsContent:hover {\r\n    background: rgb(235, 252, 240);\r\n    .title {\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}