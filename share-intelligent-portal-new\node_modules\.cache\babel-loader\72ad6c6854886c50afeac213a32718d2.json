{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\im\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\im\\index.vue", "mtime": 1750311963059}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_fileSaver", "RongIMLib", "_interopRequireWildcard", "_imkit", "_store", "_user", "_engine", "_video", "name", "components", "UserMenu", "Videoplayer", "data", "_defineProperty2", "default", "user", "id", "store", "getters", "userId", "editorVisible", "videoDialogVisible", "imageDialogVisible", "videoUrl", "lang", "langArr", "value", "conversationMenuList", "DisabledConversationontextMenu", "Top", "Delete", "Notification", "disableMenuMessage", "disableMenuConversation", "messageMenuList", "DisabledMessageContextMenu", "Forward", "Reference", "Recall", "Copy", "imageUrl", "created", "_this", "getUserIMToken", "then", "res", "code", "window", "token", "connect", "console", "info", "imkit", "emit", "CoreEvent", "CONVERSATION", "$route", "query", "selectConversation", "conversationType", "ConversationType", "PRIVATE", "targetId", "mounted", "conversationList", "$refs", "messageList", "addEventListener", "handleTapConversation", "log", "handleDeleteConversation", "disable<PERSON><PERSON><PERSON>", "handleTapMessage", "beforeUnmount", "removeEventListener", "methods", "handleVideoDialogClose", "handleImageDialogClose", "e", "detail", "type", "url", "replace", "FileSaver", "saveAs"], "sources": ["src/views/system/user/im/index.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-13 19:00:09\r\n * @LastEditTime: 2023-06-06 16:48:26\r\n * @Description:\r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"2.5\">\r\n        <div class=\"conversation-list\">\r\n          <conversation-list ref=\"conversationList\" base-size=\"6.5px\" />\r\n        </div>\r\n      </el-col>\r\n\r\n      <el-col :span=\"17\" :xl=\"17\" :lg=\"15\" :md=\"12\">\r\n        <div class=\"user-message-container\">\r\n          <div class=\"message-box\">\r\n            <message-list\r\n              class=\"message-list\"\r\n              ref=\"messageList\"\r\n              base-size=\"6.5px\"\r\n            ></message-list>\r\n          </div>\r\n          <div class=\"editor-box\">\r\n            <message-editor base-size=\"6.5px\" />\r\n          </div>\r\n          <!-- <div class=\"none-class\">\r\n            <el-image\r\n              style=\"width: 160px; height: 160px\"\r\n              :src=\"require('@/assets/user/none.png')\"\r\n              fit=\"cover\"\r\n            ></el-image>\r\n            <div class=\"text\">暂无数据</div>\r\n          </div> -->\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      :visible.sync=\"videoDialogVisible\"\r\n      :v-if=\"videoDialogVisible\"\r\n      width=\"60%\"\r\n      style=\"height: 650px\"\r\n      :before-close=\"handleVideoDialogClose\"\r\n    >\r\n      <Videoplayer :mp4Url=\"videoUrl\"></Videoplayer>\r\n    </el-dialog>\r\n    <el-dialog\r\n      :visible.sync=\"imageDialogVisible\"\r\n      :v-if=\"imageDialogVisible\"\r\n      width=\"60%\"\r\n      style=\"height: 850px; text-align: center\"\r\n      :before-close=\"handleImageDialogClose\"\r\n    >\r\n      <el-image\r\n        :v-if=\"imageDialogVisible\"\r\n        :src=\"imageUrl\"\r\n        style=\"width: 600px; height: 100%\"\r\n      ></el-image>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport FileSaver from \"file-saver\";\r\nimport * as RongIMLib from \"@rongcloud/imlib-next\";\r\nimport {\r\n  imkit,\r\n  CoreEvent,\r\n  DisabledMessageContextMenu,\r\n  DisabledConversationontextMenu,\r\n} from \"@rongcloud/imkit\";\r\nimport store from \"@/store\";\r\nimport { getUserIMToken } from \"@/api/system/user\";\r\nimport { ConversationType } from \"@rongcloud/engine\";\r\nimport Videoplayer from \"./components/video\";\r\n\r\nexport default {\r\n  name: \"IM\",\r\n  components: { UserMenu, Videoplayer },\r\n\r\n  data() {\r\n    return {\r\n      user: {\r\n        id: store.getters.userId,\r\n      },\r\n      editorVisible: false,\r\n      videoDialogVisible: false,\r\n      imageDialogVisible: false,\r\n      videoUrl: \"\",\r\n      lang: \"\",\r\n      langArr: [\r\n        {\r\n          lang: \"zh_CN\",\r\n          value: \"中文\",\r\n        },\r\n        {\r\n          lang: \"en\",\r\n          value: \"英文\",\r\n        },\r\n      ],\r\n      conversationMenuList: [\r\n        {\r\n          value: DisabledConversationontextMenu.Top,\r\n          name: \"置顶\",\r\n        },\r\n        {\r\n          value: DisabledConversationontextMenu.Delete,\r\n          name: \"删除\",\r\n        },\r\n        {\r\n          value: DisabledConversationontextMenu.Notification,\r\n          name: \"免打扰\",\r\n        },\r\n      ],\r\n      disableMenuMessage: [],\r\n      disableMenuConversation: [],\r\n      messageMenuList: [\r\n        {\r\n          value: DisabledMessageContextMenu.Forward,\r\n          name: \"转发\",\r\n        },\r\n        {\r\n          value: DisabledMessageContextMenu.Delete,\r\n          name: \"删除\",\r\n        },\r\n        {\r\n          value: DisabledMessageContextMenu.Reference,\r\n          name: \"引用\",\r\n        },\r\n        {\r\n          value: DisabledMessageContextMenu.Recall,\r\n          name: \"撤回\",\r\n        },\r\n        {\r\n          value: DisabledMessageContextMenu.Copy,\r\n          name: \"复制\",\r\n        },\r\n      ],\r\n      imageUrl: \"\",\r\n      imageUrl: \"\",\r\n      showImage: false,\r\n      switchConversationList: {},\r\n      conversation: null,\r\n      showMessageMenu: false,\r\n      showConversationMenu: false,\r\n    };\r\n  },\r\n  created() {\r\n    ///获取用户token\r\n    if (this.user.id) {\r\n      getUserIMToken({ userId: this.user.id }).then((res) => {\r\n        if (res.code === 200 && res.data.code === 200) {\r\n          window.token = res.data.token;\r\n          RongIMLib.connect(token).then((res) => {\r\n            console.info(\"连接结果打印1：\", res);\r\n            window.imkit = imkit;\r\n            this.lang = imkit.lang;\r\n            // 加载会话列表\r\n            imkit.emit(CoreEvent.CONVERSATION, true);\r\n            if (this.$route.query.userId) {\r\n              imkit.selectConversation({\r\n                conversationType: ConversationType.PRIVATE, // 会话类型\r\n                targetId: this.$route.query.userId,\r\n              });\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  },\r\n  mounted() {\r\n    // defineCustomElements();\r\n    const conversationList = this.$refs.conversationList;\r\n    const messageList = this.$refs.messageList;\r\n    //添加点击会话监听\r\n    conversationList.addEventListener(\r\n      \"tapConversation\",\r\n      this.handleTapConversation //回调处理函数\r\n    );\r\n    console.log(\"conversationList\", conversationList);\r\n    //添加删除会话监听\r\n    conversationList.addEventListener(\r\n      \"deleteConversation\",\r\n      this.handleDeleteConversation //回调处理函数\r\n    );\r\n    const disableMenu = [DisabledMessageContextMenu.Reference];\r\n    messageList.disableMenu = disableMenu;\r\n    //添加点击消息触发监听\r\n    messageList.addEventListener(\"tapMessage\", this.handleTapMessage);\r\n  },\r\n  beforeUnmount() {\r\n    // 注意：需要 removeEventListener 防止多次绑定造成异常\r\n    const conversationList = this.$refs.conversationList;\r\n\r\n    conversationList.removeEventListener(\r\n      \"tapConversation\",\r\n      this.handleTapConversation\r\n    );\r\n\r\n    conversationList.removeEventListener(\r\n      \"deleteConversation\",\r\n      this.handleDeleteConversation\r\n    );\r\n  },\r\n  methods: {\r\n    handleVideoDialogClose() {\r\n      this.videoDialogVisible = false;\r\n    },\r\n    handleImageDialogClose() {\r\n      this.imageDialogVisible = false;\r\n    },\r\n    handleTapConversation() {\r\n      //处理点击会话后的操作\r\n      console.info(\"处理点击会话后的操作11111\");\r\n    },\r\n    handleDeleteConversation() {\r\n      //处理删除会话后的操作\r\n      console.info(\"处理点击会话后的操作\");\r\n    },\r\n    handleTapMessage(e) {\r\n      const data = e.detail;\r\n      // 处理点击查看大图或文件消息下载等功能\r\n      console.log(\"点击消息触发监听:\", data);\r\n      if (data.type == \"file\") {\r\n        let url = data.url.replace(\r\n          \"http://rongcloud-file.ronghub.com\",\r\n          \"https://cy.ningmengdou.com/ryim\"\r\n        );\r\n        FileSaver.saveAs(url, data.name);\r\n      } else if (data.type == \"sight\") {\r\n        this.videoUrl = data.url;\r\n        this.videoDialogVisible = true;\r\n      } else if (data.type == \"image\") {\r\n        this.imageUrl = data.url;\r\n        this.imageDialogVisible = true;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n\r\n  .conversation-list {\r\n    height: 700px;\r\n    width: 300px;\r\n  }\r\n  .user-message-container {\r\n    height: 700px;\r\n    background: #fff;\r\n    .none-class {\r\n      text-align: center;\r\n      padding: 20% 0;\r\n      height: 700px;\r\n\r\n      .text {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n        line-height: 14px;\r\n      }\r\n    }\r\n    .message-box {\r\n      height: 400px;\r\n      .message-list {\r\n      }\r\n      .no-data {\r\n      }\r\n    }\r\n\r\n    .editor-box {\r\n      height: 300px;\r\n      .editor-tool-bar {\r\n        height: 9em;\r\n      }\r\n      .editor-content-wrapper {\r\n        padding: 1em 4em;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  .user-message-container {\r\n    .message-box {\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AAoEA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,SAAA,GAAAC,uBAAA,CAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAMA,IAAAK,MAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,KAAA,GAAAN,OAAA;AACA,IAAAO,OAAA,GAAAP,OAAA;AACA,IAAAQ,MAAA,GAAAT,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAS,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA,iBAAA;IAAAC,WAAA,EAAAA;EAAA;EAEAC,IAAA,WAAAA,KAAA;IACA,WAAAC,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA,MAAAD,gBAAA,CAAAC,OAAA;MACAC,IAAA;QACAC,EAAA,EAAAC,cAAA,CAAAC,OAAA,CAAAC;MACA;MACAC,aAAA;MACAC,kBAAA;MACAC,kBAAA;MACAC,QAAA;MACAC,IAAA;MACAC,OAAA,GACA;QACAD,IAAA;QACAE,KAAA;MACA,GACA;QACAF,IAAA;QACAE,KAAA;MACA,EACA;MACAC,oBAAA,GACA;QACAD,KAAA,EAAAE,qCAAA,CAAAC,GAAA;QACArB,IAAA;MACA,GACA;QACAkB,KAAA,EAAAE,qCAAA,CAAAE,MAAA;QACAtB,IAAA;MACA,GACA;QACAkB,KAAA,EAAAE,qCAAA,CAAAG,YAAA;QACAvB,IAAA;MACA,EACA;MACAwB,kBAAA;MACAC,uBAAA;MACAC,eAAA,GACA;QACAR,KAAA,EAAAS,iCAAA,CAAAC,OAAA;QACA5B,IAAA;MACA,GACA;QACAkB,KAAA,EAAAS,iCAAA,CAAAL,MAAA;QACAtB,IAAA;MACA,GACA;QACAkB,KAAA,EAAAS,iCAAA,CAAAE,SAAA;QACA7B,IAAA;MACA,GACA;QACAkB,KAAA,EAAAS,iCAAA,CAAAG,MAAA;QACA9B,IAAA;MACA,GACA;QACAkB,KAAA,EAAAS,iCAAA,CAAAI,IAAA;QACA/B,IAAA;MACA,EACA;MACAgC,QAAA;IAAA,eACA,kBACA,kCACA,qBACA,0BACA,gCACA;EAEA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA;IACA,SAAA3B,IAAA,CAAAC,EAAA;MACA,IAAA2B,oBAAA;QAAAxB,MAAA,OAAAJ,IAAA,CAAAC;MAAA,GAAA4B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA,YAAAD,GAAA,CAAAjC,IAAA,CAAAkC,IAAA;UACAC,MAAA,CAAAC,KAAA,GAAAH,GAAA,CAAAjC,IAAA,CAAAoC,KAAA;UACA/C,SAAA,CAAAgD,OAAA,CAAAD,KAAA,EAAAJ,IAAA,WAAAC,GAAA;YACAK,OAAA,CAAAC,IAAA,aAAAN,GAAA;YACAE,MAAA,CAAAK,KAAA,GAAAA,YAAA;YACAV,KAAA,CAAAlB,IAAA,GAAA4B,YAAA,CAAA5B,IAAA;YACA;YACA4B,YAAA,CAAAC,IAAA,CAAAC,gBAAA,CAAAC,YAAA;YACA,IAAAb,KAAA,CAAAc,MAAA,CAAAC,KAAA,CAAAtC,MAAA;cACAiC,YAAA,CAAAM,kBAAA;gBACAC,gBAAA,EAAAC,wBAAA,CAAAC,OAAA;gBAAA;gBACAC,QAAA,EAAApB,KAAA,CAAAc,MAAA,CAAAC,KAAA,CAAAtC;cACA;YACA;UACA;QACA;MACA;IACA;EACA;EACA4C,OAAA,WAAAA,QAAA;IACA;IACA,IAAAC,gBAAA,QAAAC,KAAA,CAAAD,gBAAA;IACA,IAAAE,WAAA,QAAAD,KAAA,CAAAC,WAAA;IACA;IACAF,gBAAA,CAAAG,gBAAA,CACA,mBACA,KAAAC,qBAAA;IACA;IACAlB,OAAA,CAAAmB,GAAA,qBAAAL,gBAAA;IACA;IACAA,gBAAA,CAAAG,gBAAA,CACA,sBACA,KAAAG,wBAAA;IACA;IACA,IAAAC,WAAA,IAAApC,iCAAA,CAAAE,SAAA;IACA6B,WAAA,CAAAK,WAAA,GAAAA,WAAA;IACA;IACAL,WAAA,CAAAC,gBAAA,oBAAAK,gBAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA;IACA,IAAAT,gBAAA,QAAAC,KAAA,CAAAD,gBAAA;IAEAA,gBAAA,CAAAU,mBAAA,CACA,mBACA,KAAAN,qBACA;IAEAJ,gBAAA,CAAAU,mBAAA,CACA,sBACA,KAAAJ,wBACA;EACA;EACAK,OAAA;IACAC,sBAAA,WAAAA,uBAAA;MACA,KAAAvD,kBAAA;IACA;IACAwD,sBAAA,WAAAA,uBAAA;MACA,KAAAvD,kBAAA;IACA;IACA8C,qBAAA,WAAAA,sBAAA;MACA;MACAlB,OAAA,CAAAC,IAAA;IACA;IACAmB,wBAAA,WAAAA,yBAAA;MACA;MACApB,OAAA,CAAAC,IAAA;IACA;IACAqB,gBAAA,WAAAA,iBAAAM,CAAA;MACA,IAAAlE,IAAA,GAAAkE,CAAA,CAAAC,MAAA;MACA;MACA7B,OAAA,CAAAmB,GAAA,cAAAzD,IAAA;MACA,IAAAA,IAAA,CAAAoE,IAAA;QACA,IAAAC,GAAA,GAAArE,IAAA,CAAAqE,GAAA,CAAAC,OAAA,CACA,qCACA,iCACA;QACAC,kBAAA,CAAAC,MAAA,CAAAH,GAAA,EAAArE,IAAA,CAAAJ,IAAA;MACA,WAAAI,IAAA,CAAAoE,IAAA;QACA,KAAAzD,QAAA,GAAAX,IAAA,CAAAqE,GAAA;QACA,KAAA5D,kBAAA;MACA,WAAAT,IAAA,CAAAoE,IAAA;QACA,KAAAxC,QAAA,GAAA5B,IAAA,CAAAqE,GAAA;QACA,KAAA3D,kBAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}