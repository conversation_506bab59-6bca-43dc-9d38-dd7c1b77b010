<!--
 * @Author: zhc
 * @Date: 2023-02-11 15:20:15
 * @LastEditTime: 2023-02-28 08:48:59
 * @Description: 
 * @LastEditors: zhc
-->
<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-02-11 15:18:41
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div class="app-container">
    <div class="company-demand-detail">
      <el-row :gutter="20">
        <el-col :span="2.5" :xs="24">
          <user-menu />
        </el-col>
        <el-col :span="20" :xs="24">
          <div class="info-container">
            <div class="header">
              <div class="header-text">{{ this.title }}</div>
            </div>
            <div class="detail-page" v-if="isDetail">
              <div class="header-small">
                <div class="red-tag"></div>
                基本信息
                <el-button
                  plain
                  type="primary"
                  style="position: absolute; right: 0"
                  @click="toZiyuan"
                  >查看平台匹配资源</el-button
                >
              </div>

              <el-descriptions class="margin-top" :column="1" border>
                <el-descriptions-item>
                  <template slot="label"> 需求标题 </template>
                  {{ info.demandTitle }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 需求描述 </template>
                  {{ info.summary }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 应用领域</template>
                  {{ info.applicationArea }}
                </el-descriptions-item>

                <el-descriptions-item>
                  <template slot="label"> 需求封面 </template>
                  <el-image
                    style="width: 90px; height: 64px"
                    :src="getUrl(info.scenePicture)"
                  ></el-image>
                </el-descriptions-item>
              </el-descriptions>
              <div class="header-small mt_40">
                <div class="red-tag"></div>
                联系信息
              </div>

              <el-descriptions class="margin-top" :column="1" border>
                <el-descriptions-item>
                  <template slot="label"> 公司名称 </template>
                  {{ info.companyName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 联系人 </template>
                  {{ info.contactsName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 联系电话</template>
                  {{ info.contactsMobile }}
                </el-descriptions-item>
              </el-descriptions>
              <el-image
                class="status_approving"
                v-if="info.status == '1'"
                style="width: 120px; height: 102px"
                :src="require('@/assets/user/status_approving.png')"
              ></el-image>
              <div class="delete-btn">
                <el-button @click="goBack">返回</el-button>
                <el-button type="danger" @click="changeMode">编辑</el-button>
              </div>
            </div>
            <div class="edit-page" v-else>
              <el-form
                ref="form"
                :model="form"
                :rules="rules"
                label-position="top"
              >
                <el-form-item label="需求标题" prop="demandTitle">
                  <el-input v-model="form.demandTitle" placeholder="请输入" />
                </el-form-item>
                <el-form-item prop="demandType">
                  <div class="label-item" slot="label">
                    <span>需求类型</span>
                    <span class="extra"
                      >（可按需求产品+应用行业+应用领域进行描述）</span
                    >
                  </div>
                  <el-checkbox-group
                    v-model="form.demandType"
                    placeholder="请选择"
                    clearable
                  >
                    <el-checkbox
                      v-for="dict in dict.type.demand_type"
                      :key="dict.value"
                      :label="dict.value"
                      :value="dict.value"
                      >{{ dict.label }}</el-checkbox
                    >
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="需求描述" prop="summary">
                  <el-input
                    v-model="form.summary"
                    type="textarea"
                    :rows="2"
                    :maxlength="500"
                    placeholder="请输入"
                  />
                  <div class="extra-content">
                    <div class="extra-content-header">
                      <el-button
                        @click="handleKeywordList"
                        size="small"
                        type="primary"
                        >生成关键词</el-button
                      >
                      <span class="tip">生成关键词有利于实现精准匹配哦！</span>
                    </div>
                    <div
                      v-if="form.keywords && form.keywords.length > 0"
                      class="extra-content-body"
                    >
                      <el-tag
                        :key="`${tag}_${index}`"
                        v-for="(tag, index) in form.keywords"
                        closable
                        size="small"
                        disable-transitions
                        @close="handleSummaryClose(tag)"
                      >
                        {{ tag }}
                      </el-tag>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="应用领域" prop="applicationArea">
                  <el-select
                    v-model="form.applicationArea"
                    filterable
                    multiple
                    allow-create
                    style="width: 100%"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in dict.type.application_area"
                      :key="item.value"
                      :label="item.label"
                      :value="item.label"
                    >
                    </el-option>
                  </el-select>
                  <!-- <el-tag
                    v-for="tag in form.applicationAreaList"
                    closable
                    class="add-demand-tag"
                    :key="tag"
                    :disable-transitions="false"
                    @close="handleClose(tag)"
                  >
                    {{ tag }}
                  </el-tag>
                  <el-input v-model="applicationsInput" :maxlength="255">
                  </el-input>
                  <el-button
                    size="small"
                    icon="el-icon-plus"
                    class="add-demand-btn-tag"
                    @click="handleInputConfirm"
                    >新增</el-button
                  > -->
                </el-form-item>
                <el-form-item label="产品图片">
                  <el-upload
                    list-type="picture-card"
                    :headers="headers"
                    :action="uploadUrl"
                    :file-list="form.scenePictureList"
                    :accept="accept"
                    :before-upload="handleBeforeUpload"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemove"
                    :on-success="handleSuccess"
                  >
                    <i class="el-icon-plus"></i>
                  </el-upload>
                  <el-dialog
                    append-to-body
                    :visible.sync="imgVisible"
                    :close-on-click-modal="false"
                  >
                    <img v-if="imageUrl" width="100%" :src="imageUrl" alt="" />
                  </el-dialog>
                </el-form-item>
                <el-form-item label="展示限制" prop="displayRestrictions">
                  <el-select
                    v-model="form.displayRestrictions"
                    placeholder="请选择"
                    style="width: 100%"
                    clearable
                  >
                    <el-option
                      v-for="dict in dict.type.display_restrictions"
                      :key="dict.value"
                      :label="dict.label"
                      :value="dict.value"
                    />
                  </el-select>
                </el-form-item>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="公司名称" prop="companyName">
                      <el-input
                        disabled
                        v-model="form.companyName"
                        placeholder="请输入公司名称"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="联系人" prop="contactsName">
                      <el-input
                        disabled
                        v-model="form.contactsName"
                        placeholder="请输入联系人"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="24">
                    <el-form-item label="联系电话" prop="contactsMobile">
                      <el-input
                        disabled
                        v-model="form.contactsMobile"
                        placeholder="请输入联系电话"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <div class="delete-btn">
                <el-button @click="changeMode">返回</el-button>
                <!-- <el-button type="error" @click="changeMode(0)"
                  >暂存草稿</el-button
                > -->
                <el-button type="danger" @click="submitForm(1)"
                  >提交审核</el-button
                >
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
let id = 0;

import UserMenu from "../../components/userMenu.vue";
import { getDemandDetail, createDemand, editDemand } from "@/api/system/demand";
import { uploadUrl } from "@/api/oss";
import { getToken } from "@/utils/auth";
import store from "@/store";
import { demandAdd, keywordList } from "@/api/zhm";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  name: "Notice",
  dicts: [
    "demand_type",
    "affiliated_unit",
    "capital_source",
    "affiliated_street",
    "display_restrictions",
    "application_area",
  ],
  components: { UserMenu },
  data() {
    return {
      isDetail: true,
      title: "需求详情",
      imageUrl: "",
      actionUrl: uploadUrl(),
      headers: { Authorization: "Bearer " + getToken() },
      uploadUrl: process.env.VUE_APP_BASE_API + "/file/upload", //上传地址
      accept: ".jpg, .jpeg, .png, .bmp",
      isCreate: false,
      imgVisible: false,
      user: {
        tel: store.getters.tel,
        name: store.getters.name,
        companyName: store.getters.companyName,
        bussinessNo: store.getters.bussinessNo,
        phonenumber: store.getters.phonenumber,
      },
      keywords: [],
      applicationsInput: "",
      info: {},

      form: {},
      accountLicenceList: [],

      // 表单校验
      rules: {
        demandTitle: [
          { required: true, message: "需求标题不能为空", trigger: "blur" },
        ],
        demandType: [
          { required: true, message: "请选择需求类型", trigger: "change" },
        ],
        applicationArea: [
          { required: true, message: "请选择应用领域", trigger: "change" },
        ],
        displayRestrictions: [
          { required: true, message: "请选择展示限制", trigger: "change" },
        ],
        summary: [
          { required: true, message: "需求描述不能为空", trigger: "blur" },
        ],
        contactsName: [
          { required: true, message: "联系人不能为空", trigger: "blur" },
        ],
        companyName: [
          { required: true, message: "公司名称不能为空", trigger: "blur" },
        ],
        contactsMobile: [
          { required: true, message: "联系电话不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.isCreate = this.$route.query.type == 1;
    if (this.isCreate) {
      this.goCreate();
    } else {
      this.getDetail();
    }
  },
  methods: {
    initForm() {
      this.form = {
        demandType: [],
        applicationArea: [],
        scenePicture: [],
        // applicationAreaList: [],
        scenePictureList: [],
        keywords: [],
        auditStatus: "1",
        displayStatus: "2",
        publisherName: this.user.name,
        publisherMobile: this.user.tel,
        // 展示限制
        displayRestrictions: undefined,
      };
    },
    getDetail() {
      let id = this.$route.query.id;
      getDemandDetail(id).then((response) => {
        let key = CryptoJS.enc.Utf8.parse(secretKey);
        let decrypt = CryptoJS.AES.decrypt(response, key, {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7,
        });
        response = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
        this.info = response.data;
        this.total = response.total;
      });
    },
    goBack() {
      this.$router.go(-1);
    },
    getUrl(str) {
      if (str && str != null) {
        var list = JSON.parse(str);
        if (list && list.length > 0) {
          return list[0].url;
        }
      }

      return null;
    },
    // // 应用领域新增
    // handleInputConfirm() {
    //   let val = this.applicationsInput;
    //   if (val) {
    //     this.form.applicationAreaList.push(val);
    //   }
    //   this.applicationsInput = "";
    // },
    // // 应用领域移除
    // handleClose(tag) {
    //   this.form.applicationAreaList.splice(
    //     this.form.applicationAreaList.indexOf(tag),
    //     1
    //   );
    // },
    handleSummaryClose(tag) {
      this.form.keywords.splice(this.form.keywords.indexOf(tag), 1);
    },
    // 产品照片上传之前的钩子
    handleBeforeUpload(file) {
      let { name, type, size } = file;
      let typeList = this.accept
        .split(",")
        .map((item) => item.trim().toLowerCase().substr(1));
      let dotIndex = name.lastIndexOf(".");
      // 文件类型校验
      if (dotIndex === -1) {
        this.$message.error("请上传正确格式的文件");
        return false;
      } else {
        let suffix = name.substring(dotIndex + 1);
        if (typeList.indexOf(suffix.toLowerCase()) === -1) {
          this.$message.error("请上传正确格式的文件");
          return false;
        }
      }
      // 文件上传大小限制
      if (size > 1048576 * 20) {
        this.$message.error("文件大小不能超过20M！");
        return false;
      }
    },
    // 点击产品照片
    handlePictureCardPreview(file) {
      this.imageUrl = file.url;
      this.imgVisible = true;
    },
    // 删除产品照片
    handleRemove(file, fileList) {
      this.form.scenePictureList = fileList;
    },
    handleSuccess(response, file) {
      if (response.code == 200) {
        if (this.form.scenePictureList == null) {
          this.form.scenePictureList = [];
        }
        this.form.scenePictureList.push(response.data);
      }
    },
    changeMode() {
      if (this.isCreate) {
        this.goBack();
        return;
      }
      if (this.isDetail) {
        this.title = "编辑需求";
        this.isDetail = false;
        this.form = this.info;
        if (this.info.applicationArea) {
          this.form.applicationArea = this.info.applicationArea.split(",");
        } else {
          this.form.applicationArea = [];
        }
        if (this.info.displayRestrictions) {
          this.form.displayRestrictions =
            this.info.displayRestrictions.toString();
        }
        if (this.info.keywords) {
          this.form.keywords = this.info.keywords.split(",");
        }
        if (this.info.demandType) {
          this.form.demandType = this.info.demandType.split(",");
        }
        if (this.info.scenePicture && this.info.scenePicture != "null") {
          this.form.scenePictureList = JSON.parse(this.info.scenePicture);
        } else {
          this.form.scenePictureList = [];
        }
      } else {
        this.isDetail = true;
        this.title = "需求详情";
        this.initForm();
        this.getDetail();
      }
    },
    goCreate() {
      this.title = "新增需求";
      this.isDetail = false;
      this.initForm();
      this.form.companyName = this.user.companyName;
      this.form.contactsName = this.user.name;
      this.form.contactsMobile = this.user.phonenumber;
      this.form.publisherName = this.user.name;
      this.form.publisherMobile = this.user.phonenumber;
      this.form.businessNo = this.user.bussinessNo;
    },
    handleFilePreview(file) {
      window.open(file);
    },
    displayRestrictionChanged(res) {
      this.dict.type.display_restrictions.forEach((item) => {
        if (item.label == res) {
          this.form.displayRestrictions = item.value;
        }
      });
    },
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // if (
          //   this.form.applicationAreaList &&
          //   this.form.applicationAreaList.length > 0
          // ) {
          //   this.form.applicationArea = this.form.applicationAreaList.join(",");
          // } else {
          //   this.form.applicationArea = "";
          // }
          if (this.form.applicationArea.length > 0) {
            this.form.applicationArea = this.form.applicationArea.join();
          }
          this.form.scenePicture = JSON.stringify(this.form.scenePictureList);
          this.form.businessNo = this.user.bussinessNo;
          if (this.form.keywords && this.form.keywords.length > 0) {
            this.form.keywords = this.form.keywords.join(",");
          } else {
            this.form.keywords = "";
          }
          if (this.form.demandType.length > 0) {
            this.form.demandType = this.form.demandType.join();
          }
          if (this.isCreate) {
            createDemand({ ...this.form, isSubmit: type }).then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.changeMode();
            });
          } else {
            this.form.auditStatus = 1;
            editDemand({ ...this.form, isSubmit: type }).then((response) => {
              this.$modal.msgSuccess("操作成功");
              this.changeMode();
            });
          }
        }
      });
    },
    toZiyuan() {
      this.$router.push({
        path: "/user/companyApplyDetail1",
        query: { key: JSON.stringify(this.info) },
      });
    },
    handleKeywordList() {
      const { summary } = this.form;
      if (summary) {
        keywordList(summary).then((res) => {
          const { code, data, msg } = res;
          if (code === 200) {
            this.form.keywords = data;
          } else {
            this.$message.error(msg);
          }
        });
      } else {
        this.$message.warning("请输入需求描述");
      }
    },
    handleApplicationRemove(file, fileList) {
      this.form.application = "";
    },
    handleApplicationSuccess(res, file, fileList) {
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        this.form.application = res.data.url;
        this.form.applicationName = res.data.name;
      }
    },
    handleAccountRemove(file, fileList) {
      this.form.accountLicence = "";
    },
    handleAccountSuccess(res, file, fileList) {
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        this.form.accountLicence = res.data.url;
        this.form.accountLicenceName = res.data.name;
      }
    },
  },
};
</script>

<style lang="scss">
.app-container {
  background: #f4f5f9;
  .company-demand-detail {
    .info-container {
      width: 100%;
      padding-top: 12px;
      padding: 10px 30px;

      background-color: white;
      .header {
        margin-bottom: 30px;
        width: 100%;
        text-align: center;
        .el-button {
          height: 40px;
          border-color: transparent;
          padding: 10px 10px 10px 20px;
          font-size: 20px;
          color: #000;
        }
        .el-button:hover {
          background-color: white;
        }
        .header-text {
          font-size: 24px;
          font-weight: 500;
          color: #333333;
          line-height: 24px;
          line-height: 40px;
        }
      }
      .detail-page {
        position: relative;

        .header-small {
          text-align: center;
          display: flex;
          font-size: 16px;
          font-weight: 500;
          color: #333333;
          line-height: 16px;
          margin-bottom: 36px;

          .red-tag {
            margin-right: 12px;
            width: 3px;
            height: 16px;
            background: #21c9b8;
          }
        }
        .mt_40 {
          margin-top: 40px;
        }
        .file-class {
          width: 733px;
          height: 40px;
          background: #f7f8fa;
          border-radius: 4px;
          padding: 0 20px;
          display: flex;
          font-size: 14px;
          font-weight: 400;
          color: #333333;
          line-height: 40px;
          position: relative;

          .el-image {
            margin: 12px 8px 0 0;
          }
          .previwe-class {
            right: 20px;
            position: absolute;
            margin: 8px 0 0 0;
            width: 72px;
            height: 24px;
            border-radius: 16px;
            text-align: center;
            border: 1px solid #2f76e0;
            font-size: 12px;
            font-weight: 400;
            color: #2f76e0;
            line-height: 24px;
          }
        }
        .status_approving {
          top: 0px;
          right: 20px;
          position: absolute;
        }
      }

      .edit-page {
        .el-input--medium .el-input__inner {
          width: 90%;
          height: 36px;
          line-height: 36px;
        }
        .el-textarea__inner {
          width: 90%;
        }
        .add-demand-tag {
          margin-right: 10px;
          height: 32px;
          line-height: 32px;
        }
        .el-button--primary {
          background: #fff;
          color: #333;
          border-color: #bfbfbf;
        }
        .el-button--danger {
          background: #fff;
          color: #21c9b8;
          border-color: #21c9b8;
        }
        .tip {
          padding-left: 10px;
          font-size: 12px;
          font-weight: 400;
          color: #8c8c8c;
          line-height: 18px;
        }
      }
      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {
        padding: 10px;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
      }

      .el-descriptions--medium.is-bordered .el-descriptions-item__label {
        padding: 15px;
        text-align: center;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        width: 200px;
      }
      .delete-btn {
        width: 100%;
        margin-top: 20px;
        text-align: center;
        .el-button {
          padding: 12px 55px;
          color: #333;
        }
        .el-button:hover,
        .el-button:focus {
          border-color: #d9d9d9;
          background-color: #fff;
        }
        .el-button--danger {
          margin-left: 30px;
          color: #ffffff;
          background-color: #21c9b8 !important;
          border-color: #21c9b8 !important;
        }
        .el-button--error {
          margin-left: 30px;
          color: #21c9b8;
          background-color: #ffffff;
          border-color: #21c9b8;
        }
      }
    }
  }
}
</style>
<style lang="scss" scoped>
::v-deep .el-input__suffix {
  position: absolute;
  top: 0;
  right: 11%;
}
.el-checkbox {
  font-size: 14px;
  font-weight: 400;
  color: #262626;
  line-height: 18px;
  margin-right: 28px;
}
.edit-page {
  padding-left: 50px;
}
</style>
