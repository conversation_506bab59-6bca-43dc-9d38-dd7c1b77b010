{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\aboutUs\\components\\platIntroduction.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\aboutUs\\components\\platIntroduction.vue", "mtime": 1750311962916}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_wow", "_interopRequireDefault", "require", "name", "data", "activeIndex", "list", "time", "text", "methods", "handleMouseEnter", "index", "handleMouseLeave", "initWow", "_this", "wow", "WOW", "boxClass", "animateClass", "offset", "mobile", "live", "callback", "e", "_loop", "i", "setTimeout", "dom", "$refs", "concat", "console", "log", "classList", "add", "style", "opacity", "length", "scrollContainer", "resetAnimation", "init", "computed", "created", "mounted", "components", "watch", "mixins"], "sources": ["src/views/aboutUs/components/platIntroduction.vue"], "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      <div class=\"content_bannerTitle\">易复材共享智造工业互联网平台</div>\r\n      <div class=\"content_bannerBox\"></div>\r\n      <div class=\"content_bannerContent\">创国内引领的行业级工业互联网平台</div>\r\n    </div>\r\n    <!-- <div class=\"aboutUsBody\">\r\n      <img src=\"@/assets/aboutUs/aboutUsBody.png\" alt=\"\" />\r\n    </div> -->\r\n    <div class=\"company\">\r\n      <div class=\"card-container\">\r\n        <div class=\"company-top\">\r\n          <img src=\"@/assets/aboutUs/company.png\" alt=\"\">\r\n          <div class=\"company-message\">\r\n            <div class=\"company-title\">易复材共享智造工业互联网平台</div>\r\n            <div class=\"company-content\">\r\n              易复材共享智造工业互联网平台以“4+1+1”模式为核心，通过制造、集采、服务、创新四大共享，\r\n              整合资源协同生产；依托共享示范工厂推动高端应用落地, 以数字化底座赋能设备互联与智能管理，\r\n              助力复合材料产业向集约化、智能化、绿色化转型升级\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"description\">\r\n          <ul class=\"list\">\r\n            <li>\r\n              <div class=\"imagebox\">\r\n                为集群赋能，创共享生态\r\n                <!-- <img src=\"@/assets/aboutUs/company.jpg\" alt=\"\" /> -->\r\n              </div>\r\n              <div class=\"listR\">\r\n                <p>\r\n                  易复材共享智造工业互联网平台以“共享、开放、协同、创新”为核心理念，依托枣强复合材料产业\r\n                  集群的深厚产业基础，整合恒润集团20余年制造经验与河北省复合材料产业技术研究院的科研服务能\r\n                  力，打造 “4+1+1” 共享智造创新平台。\r\n                </p>\r\n                <p>\r\n                  通过制造共享、集采共享、服务共享、创新共享四大模块，构建资源高效协同的产业生态，赋能复合\r\n                  材料产业向高端化、智能化、绿色化转型。\r\n                  制造共享整合集群设备与订单资源，实现分布式协同生产，\r\n                  盘活闲置产能，助力企业轻资产运营；集采共享通过“集采+仓储+物流+金融”一体化模式，\r\n                  降低采 购成本， 并为企业提供高效资金支持。\r\n                </p>\r\n                <p>\r\n                  服务共享联合第三方机构，覆盖人才培育、技能培训、技术咨询等全周期需求，赋能企业高效成长；\r\n                  创新共享推动产学研深度融合，加速技术攻关与成果转化。\r\n                  平台依托共享示范工厂引入先进工艺设备，\r\n                  承接高端应用领域订单，并通过数字化底座实现设备互联与数据互通，构建安全高效的智能管理体系。\r\n                  以资源开放共享与技术赋能为驱动，助力复合材料产业迈向集约化、数字化、生态化发展新阶段，为\r\n                  区域经济高质量发展注入核心动能。\r\n                </p>\r\n                <!--  <p>\r\n                  易复材共享智造工业互联网平台以“共享、开放、协同、创新”为核心理念，依托枣强复合材料产业集群的深厚产业基础，整合恒润集团20余年制造经验与河北省复合材料产业技术研究院的科研服务能力，打造“4+1+1”共享智造创新平台。通过制造共享、集采共享、服务共享、创新共享四大模块，构建资源高效协同的产业生态，赋能复合材料产业向高端化、智能化、绿色化转型。\r\n                  制造共享整合集群设备与订单资源，实现分布式协同生产，盘活闲置产能，助力企业轻资产运营；集采共享通过“集采+仓储+物流+金融”一体化模式，降低采购成本，并为企业提供高效资金支持；服务共享联合第三方机构，覆盖人才培育、技能培训、技术咨询等全周期需求，赋能企业高效成长；创新共享推动产学研深度融合，加速技术攻关与成果转化。\r\n                  平台依托共享示范工厂引入先进工艺设备，承接高端应用领域订单，并通过数字化底座实现设备互联与数据互通，构建安全高效的智能管理体系。以资源开放共享与技术赋能为驱动，助力复合材料产业迈向集约化、数字化、生态化发展新阶段，为区域经济高质量发展注入核心动能。\r\n                </p> -->\r\n              </div>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- <div class=\"course_box\">\r\n      <div class=\"aboutMengdou\">发展历程</div>\r\n      <ul class=\"course_ul wow\">\r\n        <li v-for=\"item, i of list\" :ref=\"`list_item${i + 1}`\" class=\"animate__animated\" :key=\"i\">\r\n          <div class=\"course_ul_l\" :class=\"[i % 2 != 0 ? 'course_ul_la' : 'course_ul_lb']\">\r\n            <span>{{ item.time }}</span>\r\n          </div>\r\n          <div class=\"course_ul_r\">\r\n            <p>{{ item.text }}</p>\r\n          </div>\r\n        </li>\r\n      </ul>\r\n    </div> -->\r\n    <div class=\"flexFooter \">\r\n      <div class=\"card-container\">\r\n        <div class=\"process-title\">平台发展历程</div>\r\n        <div class=\"process\">\r\n          <img class=\"line\" src=\"@/assets/aboutUs/process-line.png\" alt=\"\">\r\n          <div class=\"process-2021\">\r\n            <div class=\"process-time\">2021年</div>\r\n            <div class=\"process-content\">\r\n              寻共需\r\n            </div>\r\n          </div>\r\n          <div class=\"process-2022\">\r\n            <div class=\"process-content\">\r\n              促共享\r\n            </div>\r\n            <div class=\"process-time\">2022年</div>\r\n          </div>\r\n          <div class=\"process-2023\">\r\n            <div class=\"process-time\">2023年</div>\r\n            <div class=\"process-content\">\r\n              谋发展\r\n            </div>\r\n          </div>\r\n          <div class=\"process-2025\">\r\n            <div class=\"process-content\">\r\n              创未来\r\n            </div>\r\n            <div class=\"process-time\">2025年</div>\r\n          </div>\r\n        </div>\r\n\r\n        <video class=\"videoRef\" src=\"@/assets/video/aboutUs.mp4\" loop isMuted=\"true\" controls autoplay\r\n          playsinline></video>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  // padding-bottom: 60px;\r\n}\r\n\r\n.aboutUsBody {\r\n  width: 1200px;\r\n  height: 650px;\r\n  margin: 30px auto;\r\n}\r\n\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #ffffff;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .content_bannerBox {\r\n    height: 11px;\r\n  }\r\n\r\n  .content_bannerContent {\r\n    font-size: 22px;\r\n    color: #222222;\r\n  }\r\n\r\n  .content_bannerTitle {\r\n    font-size: 40px;\r\n    color: #222222;\r\n  }\r\n}\r\n\r\n.flexFooter {\r\n  padding-bottom: 80px;\r\n  box-sizing: border-box;\r\n  background: url(\"../../../assets/aboutUs/aboutUs-footer.png\");\r\n  background-size: 100% auto;\r\n  background-position: bottom;\r\n  background-repeat: no-repeat;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  text-align: center;\r\n\r\n  .process-title {\r\n    font-weight: 500;\r\n    font-size: 34px;\r\n    color: #000000;\r\n  }\r\n\r\n  .process {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 473px;\r\n    background-image: url(\"../../../assets/aboutUs/process.png\");\r\n    background-size: 100% 100%;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n\r\n    .line {\r\n      position: absolute;\r\n      top: 50%;\r\n      left: 50%;\r\n      transform: translate(-50%, -50%);\r\n      width: 100%;\r\n    }\r\n\r\n    .process-2021 {\r\n      position: absolute;\r\n      left: 12%;\r\n      top: 65%;\r\n    }\r\n\r\n    .process-2022 {\r\n      position: absolute;\r\n      left: 33%;\r\n      top: 45%;\r\n    }\r\n\r\n    .process-2023 {\r\n      position: absolute;\r\n      left: 53%;\r\n      top: 50%;\r\n    }\r\n\r\n    .process-2025 {\r\n      position: absolute;\r\n      left: 75%;\r\n      top: 30%;\r\n    }\r\n\r\n    .process-time {\r\n      font-weight: 500;\r\n      font-size: 18px;\r\n      color: #000000;\r\n    }\r\n\r\n    .process-content {\r\n      font-weight: 400;\r\n      font-size: 22px;\r\n      color: #3E3D3D;\r\n    }\r\n  }\r\n\r\n  .videoRef {\r\n    width: 1200px;\r\n  }\r\n\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\" scoped>\r\n.company {\r\n  width: 100%;\r\n  height: 890px;\r\n  background-image: url(\"../../../assets/aboutUs/company-bg.png\");\r\n  background-size: 100%;\r\n  /* 背景图片覆盖整个元素 */\r\n  background-repeat: no-repeat;\r\n  /* 背景图片不重复 */\r\n  background-position: 0 0;\r\n  /* 背景图片居中 */\r\n  padding-top: 75px;\r\n  box-sizing: border-box;\r\n\r\n  .company-top {\r\n    width: 100%;\r\n    height: 300px;\r\n    display: flex;\r\n\r\n    img {\r\n      height: 100%;\r\n    }\r\n\r\n    .company-message {\r\n      height: 100%;\r\n      background-color: #F5F6F6;\r\n      padding: 0 60px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      justify-content: center;\r\n\r\n      .company-title {\r\n        font-size: 34px;\r\n        color: #000000;\r\n        font-weight: 500;\r\n        padding-bottom: 45px;\r\n      }\r\n\r\n      .company-content {\r\n        font-weight: 400;\r\n        font-size: 16px;\r\n        color: #3E3D3D;\r\n        line-height: 28px;\r\n        opacity: 0.94;\r\n      }\r\n    }\r\n  }\r\n\r\n  .description {\r\n    width: 100%;\r\n    margin: 0 auto;\r\n\r\n    .list {\r\n      width: 100%;\r\n      padding: 0;\r\n      // margin: 0 auto;\r\n      padding: 65px 0;\r\n\r\n      li {\r\n        display: flex;\r\n        justify-content: space-between;\r\n\r\n        .listR {\r\n          width: 713px;\r\n          height: 362px;\r\n          display: flex;\r\n          flex-direction: column;\r\n          justify-content: space-around;\r\n\r\n          p {\r\n            margin: 0;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #3E3D3D;\r\n            line-height: 26px;\r\n          }\r\n        }\r\n\r\n        .imagebox {\r\n          flex: 1;\r\n          display: flex;\r\n          align-items: center;\r\n          // text-align: center;\r\n          font-size: 34px;\r\n          color: #000000;\r\n          // img {\r\n          //   width: 100%;\r\n          // }\r\n        }\r\n\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// .swiper-pagination-bullet {\r\n//   background: #fff;\r\n// }\r\n\r\n// .swiper-wrapper {\r\n//   position: relative;\r\n// }\r\n\r\n// .swiper-container {\r\n//   width: 100%;\r\n// }\r\n\r\n// .swiper-container2 {\r\n//   width: 100%;\r\n//   overflow: hidden;\r\n// }\r\n\r\n.container {\r\n  width: 100%;\r\n}\r\n\r\n.banner {\r\n  width: 100%;\r\n  height: 400px;\r\n}\r\n</style>\r\n<style scoped>\r\n.course_box {\r\n  background: url(\"../../../assets/aboutUs/enterBgc.png\") no-repeat center center;\r\n  background-size: 100% 100%;\r\n  height: 920px;\r\n  overflow: hidden;\r\n}\r\n\r\n.course_box .course_ul {\r\n  width: 1226px;\r\n  margin: 0px auto 50px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.course_box .course_ul li {\r\n  display: flex;\r\n  margin-top: 20px;\r\n  opacity: 0;\r\n}\r\n\r\n.course_box .course_ul li .course_ul_la::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  width: 14px;\r\n  height: 14px;\r\n  border: 1px solid #21c9b8;\r\n  border-radius: 50%;\r\n  background-color: #fbfbfa;\r\n}\r\n\r\n.course_box .course_ul li .course_ul_lb::after {\r\n  content: \"\";\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  width: 14px;\r\n  height: 14px;\r\n  background: #21c9b8;\r\n  border-radius: 50%;\r\n}\r\n\r\n.course_box .course_ul li .course_ul_l {\r\n  position: relative;\r\n  width: 120px;\r\n  position: relative;\r\n  margin-right: 20px;\r\n}\r\n\r\n.course_box .course_ul li .course_ul_l::before {\r\n  content: \"\";\r\n  position: absolute;\r\n  right: 6px;\r\n  top: 0;\r\n  width: 2px;\r\n  height: 50px;\r\n  background: linear-gradient(0deg, #f6f6f6, #21c9b8);\r\n}\r\n\r\n.course_box .course_ul li .course_ul_l span {\r\n  color: #333333;\r\n  font-size: 26px;\r\n}\r\n\r\n.course_box .course_ul li .course_ul_r {\r\n  flex: 1;\r\n  height: 60px;\r\n  border: 1px solid #ffffff;\r\n  background-color: rgba(247, 247, 247, 0.8);\r\n  padding: 0 20px;\r\n  line-height: 16px;\r\n}\r\n\r\n.course_box .course_ul li .course_ul_r p {\r\n  font-size: 16px;\r\n  color: #5a5a5a;\r\n}\r\n\r\n.aboutMengdou {\r\n  width: 100%;\r\n  text-align: center;\r\n  padding: 50px 0 50px;\r\n  box-sizing: border-box;\r\n  font-size: 30px;\r\n  color: #333333;\r\n}\r\n</style>\r\n\r\n<script>\r\nimport WOW from 'wow.js';\r\nimport 'animate.css';\r\n\r\nexport default {\r\n  name: \"Com\",\r\n  data() {\r\n    return {\r\n      activeIndex: 0,\r\n      list: [\r\n        {\r\n          time: '2023',\r\n          text: \"荣获国家级跨领域跨行业工业互联网平台\"\r\n        },\r\n        {\r\n          time: '2022',\r\n          text: \"建立云端研发新模式，成长为瞪羚企业，获评山东省双跨工业互联网平台、国家特色专业型工业互联网平台\"\r\n        },\r\n        {\r\n          time: '2021',\r\n          text: \"建立上百条优势物料资源线，上线事业合伙人模式，打破发展瓶颈，建立青岛市中小企业供应链场景实验室\"\r\n        },\r\n        {\r\n          time: '2020',\r\n          text: \"智能制造模式上线，平台发展扩展至电子制造、冷库冷藏仪器仪表、新零售畜牧业养殖五大行业营收破2亿\"\r\n        },\r\n        {\r\n          time: '2019',\r\n          text: \"完成A轮融资，玺品直播上线，企业级在线支付工具-楼豆宝上线，获批国家商务部认定的线上线下融合数字商务企业\"\r\n        }, {\r\n          time: '2018',\r\n          text: \"玺品创新模式上线，智能采购模式行业扩展\"\r\n        }, {\r\n          time: '2017',\r\n          text: \"总部落地青岛，首个智能采购机器人--豆小秘上线，智能采购模式上线，颠覆式赋能\"\r\n        }, {\r\n          time: '2016',\r\n          text: \"首创上下游裂变式上线模式，檬豆云实现5次迭代，探索智能采购模式\"\r\n        },\r\n        {\r\n          time: '2015',\r\n          text: \"公司成立于上海，完成天使轮融资檬豆云上线\"\r\n        },\r\n      ],\r\n    };\r\n  },\r\n\r\n  methods: {\r\n    handleMouseEnter(index) {\r\n      this.activeIndex = index;\r\n    },\r\n    handleMouseLeave() {\r\n      // 鼠标离开后恢复默认(第一个保持黑色)\r\n      this.activeIndex = 0;\r\n    },\r\n    initWow() {\r\n      var wow = new WOW({\r\n        boxClass: \"wow\",\r\n        animateClass: \"animated\",\r\n        offset: 200,\r\n        mobile: true,\r\n        live: true,\r\n        callback: (e) => {\r\n          // console.log(this.$refs.list_item1);\r\n          for (let i = 0; i < this.list.length; i++) {\r\n            setTimeout(() => {\r\n              var dom = this.$refs[`list_item${i + 1}`][0]\r\n              console.log(this.$refs[`list_item${i + 1}`][0]);\r\n              dom.classList.add('animate__fadeInUpBig');\r\n              dom.style.opacity = 1;\r\n            }, i * 220);\r\n          }\r\n        },\r\n        scrollContainer: null,\r\n        resetAnimation: true,\r\n      });\r\n      wow.init();\r\n    }\r\n  },\r\n\r\n  computed: {},\r\n\r\n  created() { },\r\n\r\n  mounted() {\r\n    this.initWow();\r\n  },\r\n\r\n\r\n  components: {},\r\n\r\n  watch: {},\r\n  mixins: [],\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;AAicA,IAAAA,IAAA,GAAAC,sBAAA,CAAAC,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,IAAA,GACA;QACAC,IAAA;QACAC,IAAA;MACA,GACA;QACAD,IAAA;QACAC,IAAA;MACA,GACA;QACAD,IAAA;QACAC,IAAA;MACA,GACA;QACAD,IAAA;QACAC,IAAA;MACA,GACA;QACAD,IAAA;QACAC,IAAA;MACA;QACAD,IAAA;QACAC,IAAA;MACA;QACAD,IAAA;QACAC,IAAA;MACA;QACAD,IAAA;QACAC,IAAA;MACA,GACA;QACAD,IAAA;QACAC,IAAA;MACA;IAEA;EACA;EAEAC,OAAA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MACA,KAAAN,WAAA,GAAAM,KAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA;MACA,KAAAP,WAAA;IACA;IACAQ,OAAA,WAAAA,QAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,GAAA,OAAAC,YAAA;QACAC,QAAA;QACAC,YAAA;QACAC,MAAA;QACAC,MAAA;QACAC,IAAA;QACAC,QAAA,WAAAA,SAAAC,CAAA;UAAA,IAAAC,KAAA,YAAAA,MAAAC,CAAA,EAEA;YACAC,UAAA;cACA,IAAAC,GAAA,GAAAb,KAAA,CAAAc,KAAA,aAAAC,MAAA,CAAAJ,CAAA;cACAK,OAAA,CAAAC,GAAA,CAAAjB,KAAA,CAAAc,KAAA,aAAAC,MAAA,CAAAJ,CAAA;cACAE,GAAA,CAAAK,SAAA,CAAAC,GAAA;cACAN,GAAA,CAAAO,KAAA,CAAAC,OAAA;YACA,GAAAV,CAAA;UACA;UARA;UACA,SAAAA,CAAA,MAAAA,CAAA,GAAAX,KAAA,CAAAR,IAAA,CAAA8B,MAAA,EAAAX,CAAA;YAAAD,KAAA,CAAAC,CAAA;UAAA;QAQA;QACAY,eAAA;QACAC,cAAA;MACA;MACAvB,GAAA,CAAAwB,IAAA;IACA;EACA;EAEAC,QAAA;EAEAC,OAAA,WAAAA,QAAA;EAEAC,OAAA,WAAAA,QAAA;IACA,KAAA7B,OAAA;EACA;EAGA8B,UAAA;EAEAC,KAAA;EACAC,MAAA;AACA", "ignoreList": []}]}