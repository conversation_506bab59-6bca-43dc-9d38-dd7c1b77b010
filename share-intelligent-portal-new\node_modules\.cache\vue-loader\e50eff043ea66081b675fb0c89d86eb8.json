{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\appliMarket\\purchaseapp.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\appliMarket\\purchaseapp.vue", "mtime": 1750311962923}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhcHBsaURldGFpbCwNCiAgYXBwbGlPcmRlciwNCiAgYXBwbGlDb2xsZWN0LA0KICBhcHBsaUNhbmNlbENvbGxlY3QsDQp9IGZyb20gIkAvYXBpL2FwcGxpTWFya2V0IjsNCmltcG9ydCBzdG9yZSBmcm9tICJAL3N0b3JlIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAicHVyY2hhc2VhcHAiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBpbmZvOiB7fSwNCiAgICAgIGRldGFpbDogW10sDQogICAgICBzaG93R206IGZhbHNlLA0KICAgICAgdGVhbTogIiIsDQogICAgICBzcGVjRmxhZzogIjEiLA0KICAgICAgZHVGbGFnOiAiMSIsDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldERldGFpbCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0RGV0YWlsKCkgew0KICAgICAgbGV0IGlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7DQogICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICBpZCwNCiAgICAgICAgdXNlcklkOiBzdG9yZS5nZXR0ZXJzLnVzZXJJZCwNCiAgICAgIH07DQogICAgICBhcHBsaURldGFpbChwYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuaW5mbyA9IHJlcy5kYXRhOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdvdW1haSgpIHsNCiAgICAgIHRoaXMuc2hvd0dtID0gdHJ1ZTsNCiAgICAgIC8vIGlmICghd2luZG93LnNlc3Npb25TdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJykpIHsNCiAgICAgIC8vIAlhbGVydCgn6K+35YWI55m75b2VJykNCiAgICAgIC8vIAlsZXQgdXJsOw0KICAgICAgLy8gCXZhciBzdHIgPSB3aW5kb3cubG9jYXRpb24uaHJlZjsNCiAgICAgIC8vIAl2YXIgcmVzdWx0ID0gZW5jb2RlVVJJQ29tcG9uZW50KHN0cikNCiAgICAgIC8vIAlpZiAod2luZG93LmxvY2F0aW9uLmhvc3QgPT0gJ3Rlc3QubmluZ21lbmdkb3UuY29tJykgew0KICAgICAgLy8gCQkvLyB1cmwgPSAiaHR0cHM6Ly9zc290ZXN0Lm5pbmdtZW5nZG91LmNvbS9zaW5nbGUvbG9naW4/cmV0dXJuVXJsPSIgKyByZXN1bHQNCiAgICAgIC8vIAkJdXJsID0gImh0dHBzOi8vc3NvLm5pbmdtZW5nZG91LmNvbS9zaW5nbGUvbmV3TG9naW4/cmV0dXJuVXJsPSIgKyByZXN1bHQNCiAgICAgIC8vIAl9IGVsc2UgaWYgKHdpbmRvdy5sb2NhdGlvbi5ob3N0ID09ICd3d3cubmluZ21lbmdkb3UuY29tJykgew0KICAgICAgLy8gCQkvLyB1cmwgPSAiaHR0cHM6Ly9zc28ubmluZ21lbmdkb3UuY29tL3NpbmdsZS9sb2dpbj9yZXR1cm5Vcmw9IiArIHJlc3VsdA0KICAgICAgLy8gCQl1cmwgPSAiaHR0cHM6Ly9zc28ubmluZ21lbmdkb3UuY29tL3NpbmdsZS9uZXdMb2dpbj9yZXR1cm5Vcmw9IiArIHJlc3VsdA0KICAgICAgLy8gCX0NCiAgICAgIC8vIAl3aW5kb3cubG9jYXRpb24uaHJlZiA9IHVybA0KICAgICAgLy8gCXJldHVybg0KICAgICAgLy8gfQ0KICAgICAgLy8gdGhpcy5zaG93R20gPSB0cnVlDQogICAgfSwNCiAgICBjb2xsZWN0KCkgew0KICAgICAgbGV0IGRhdGEgPSB7DQogICAgICAgIGlkOiB0aGlzLmluZm8uaWQsDQogICAgICAgIHVzZXJJZDogc3RvcmUuZ2V0dGVycy51c2VySWQsDQogICAgICB9Ow0KICAgICAgYXBwbGlDb2xsZWN0KGRhdGEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfISIpOw0KICAgICAgICAgIHRoaXMuZ2V0RGV0YWlsKCk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgY2FuY2VsQ29sbGVjdCgpIHsNCiAgICAgIGxldCBkYXRhID0gew0KICAgICAgICBpZDogdGhpcy5pbmZvLmlkLA0KICAgICAgICB1c2VySWQ6IHN0b3JlLmdldHRlcnMudXNlcklkLA0KICAgICAgfTsNCiAgICAgIGFwcGxpQ2FuY2VsQ29sbGVjdChkYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyEiKTsNCiAgICAgICAgICB0aGlzLmdldERldGFpbCgpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldFNwZWModmFsdWUpIHsNCiAgICAgIGNvbnNvbGUubG9nKHZhbHVlKTsNCiAgICAgIHRoaXMuc3BlY0ZsYWcgPSB2YWx1ZTsNCiAgICB9LA0KICAgIG9yZGVyKCkgew0KICAgICAgbGV0IGRhdGEgPSB7DQogICAgICAgIGFwcElkOiB0aGlzLmluZm8uaWQsDQogICAgICAgIHByaWNlOiB0aGlzLmluZm8ucHJpY2UsDQogICAgICAgIHRvdGFsQW1vdW50OiB0aGlzLmluZm8ucHJpY2UsDQogICAgICAgIHNwZWM6IHRoaXMuc3BlY0ZsYWcsDQogICAgICAgIHZhbGlkVGltZTogdGhpcy5kdUZsYWcsDQogICAgICAgIHBob25lOiB0aGlzLmluZm8ucGhvbmUsDQogICAgICAgIHJlbWFyazogdGhpcy5pbmZvLmFwcE5hbWUsDQogICAgICB9Ow0KICAgICAgYXBwbGlPcmRlcihkYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLnNob3dHbSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5LiL5Y2V5oiQ5YqfISIpOw0KICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgICAgIHBhdGg6ICIvcGF5bWVudCIsDQogICAgICAgICAgICBxdWVyeTogew0KICAgICAgICAgICAgICBpZDogcmVzLmRhdGEuaWQsDQogICAgICAgICAgICB9LA0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldER1cmF0aW9uKHZhbHVlKSB7DQogICAgICB0aGlzLmR1RmxhZyA9IHZhbHVlOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["purchaseapp.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4KA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "purchaseapp.vue", "sourceRoot": "src/views/appliMarket", "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div>\r\n      <div style=\"background-color: #fff\">\r\n        <div class=\"info-top\">\r\n          <div style=\"width: 1128px; margin: 0px auto\">\r\n            <div\r\n              style=\"width: 797px; display: inline-block; vertical-align: top\"\r\n            >\r\n              <div style=\"margin-top: 60px; width: 100%\">\r\n                <span\r\n                  style=\"\r\n                    color: rgba(51, 51, 51, 1);\r\n                    font-size: 42px;\r\n                    line-height: 66px;\r\n                  \"\r\n                  >{{ info.appName }}</span\r\n                >\r\n                <span\r\n                  style=\"\r\n                    line-height: 34px;\r\n                    border-radius: 2px;\r\n                    background-color: #21c9b8;\r\n                    color: #fff;\r\n                    font-size: 14px;\r\n                    margin: 16px 30px;\r\n                    padding: 0px 10px;\r\n                  \"\r\n                  >{{ info.appLabel }}</span\r\n                >\r\n              </div>\r\n              <div\r\n                style=\"\r\n                  color: #21c9b8;\r\n                  font-size: 16px;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 2;\r\n                \"\r\n              >\r\n                {{ info.briefInto }}\r\n              </div>\r\n              <div\r\n                style=\"\r\n                  line-height: 36px;\r\n                  color: #21c9b8;\r\n                  font-size: 20px;\r\n                  margin: 20px 0;\r\n                \"\r\n              >\r\n                {{ info.price }}元起 / 年\r\n              </div>\r\n              <div style=\"margin-top: 30px\">\r\n                <span\r\n                  class=\"btn\"\r\n                  style=\"background: #21c9b8; color: #fff\"\r\n                  @click=\"goumai\"\r\n                  >立即订阅</span\r\n                >\r\n                <span class=\"btn\" @click=\"collect\" v-if=\"info.issub == 0\"\r\n                  >立即收藏</span\r\n                >\r\n                <span class=\"btn\" @click=\"cancelCollect\" v-else>已收藏</span>\r\n                <!--\t\t\t\t\t\t\t<span class=\"btn\" @click=\"getUrl()\">跳转使用</span>-->\r\n              </div>\r\n            </div>\r\n\r\n            <div class=\"right-info\" v-show=\"info.supply !== ''\">\r\n              <div>\r\n                <p style=\"padding-top: 30px\">应用提供：{{ info.supply }}</p>\r\n                <p>联系人：{{ info.linkman }}</p>\r\n                <p>联系电话：{{ info.phone }}</p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div style=\"width: 1226px; margin: 60px auto\">\r\n          <h3\r\n            style=\"\r\n              line-height: 30px;\r\n              color: #333;\r\n              font-size: 20px;\r\n              font-weight: 400;\r\n              padding-bottom: 20px;\r\n            \"\r\n          >\r\n            <span\r\n              style=\"\r\n                display: inline-block;\r\n                vertical-align: top;\r\n                height: 20px;\r\n                width: 3px;\r\n                background-color: rgba(247, 154, 71, 100);\r\n                border-radius: 3px;\r\n                margin: 5px 18px 5px 0px;\r\n              \"\r\n            ></span>\r\n            应用介绍\r\n          </h3>\r\n          <div class=\"appliDetail\" v-html=\"info.content\"></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"tishi_bg\" v-if=\"showGm\">\r\n      <div class=\"goumai_bg\">\r\n        <div class=\"goumai_t\">\r\n          <span\r\n            ><a href=\"javascript:void(0)\" @click=\"showGm = false\"\r\n              ><img src=\"../../assets/images/close2.png\" /></a></span\r\n          >获取企业研发平台\r\n        </div>\r\n        <div class=\"goumai_c\">\r\n          <div class=\"appLabel\">\r\n            <a href=\"#\">{{ info.appLabel }}</a>\r\n          </div>\r\n          <div class=\"briefInto\">\r\n            <p>应用编号：{{ info.appCode }}</p>\r\n            <p>应用提供：{{ info.supply }}</p>\r\n            <!-- <p>联系电话：{{ detail.phone }}</p>\r\n            <p>联系人：{{ detail.linkman }}</p> -->\r\n          </div>\r\n          <div class=\"goumai_cont\">\r\n            <!-- <p>\r\n              选择规格:\r\n              <span @click=\"getSpec('1')\" style=\"cursor: pointer\">\r\n                <el-tag :type=\"specFlag == '1' ? 'success' : 'info'\"\r\n                  >基础版</el-tag\r\n                ></span\r\n              >\r\n              <span\r\n                @click=\"getSpec('2')\"\r\n                style=\"margin-left: 10px; cursor: pointer\"\r\n              >\r\n                <el-tag :type=\"specFlag == '2' ? 'success' : 'info'\"\r\n                  >高级版</el-tag\r\n                ></span\r\n              >\r\n            </p> -->\r\n            <p>\r\n              可用时长:\r\n              <span @click=\"getDuration('1')\" style=\"cursor: pointer\">\r\n                <el-tag :type=\"duFlag == '1' ? 'success' : 'info'\">一年</el-tag>\r\n              </span>\r\n              <span\r\n                @click=\"getDuration('2')\"\r\n                style=\"margin-left: 10px; cursor: pointer\"\r\n              >\r\n                <el-tag :type=\"duFlag == '2' ? 'success' : 'info'\">永久</el-tag>\r\n              </span>\r\n            </p>\r\n            <p>可用人数: 不限</p>\r\n          </div>\r\n          <div class=\"goumai_total\">\r\n            <div class=\"goumai_total_l\">\r\n              <p>\r\n                总价:<strong>￥{{ info.price }}</strong>\r\n              </p>\r\n            </div>\r\n            <div class=\"goumai_total_r\">\r\n              <a href=\"javascript:void(0)\" @click=\"showGm = false\">取消</a\r\n              ><a href=\"javascript:void(0)\" @click=\"order\">立即下单</a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  appliDetail,\r\n  appliOrder,\r\n  appliCollect,\r\n  appliCancelCollect,\r\n} from \"@/api/appliMarket\";\r\nimport store from \"@/store\";\r\n\r\nexport default {\r\n  name: \"purchaseapp\",\r\n  data() {\r\n    return {\r\n      info: {},\r\n      detail: [],\r\n      showGm: false,\r\n      team: \"\",\r\n      specFlag: \"1\",\r\n      duFlag: \"1\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    getDetail() {\r\n      let id = this.$route.query.id;\r\n      let params = {\r\n        id,\r\n        userId: store.getters.userId,\r\n      };\r\n      appliDetail(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.info = res.data;\r\n        }\r\n      });\r\n    },\r\n    goumai() {\r\n      this.showGm = true;\r\n      // if (!window.sessionStorage.getItem('token')) {\r\n      // \talert('请先登录')\r\n      // \tlet url;\r\n      // \tvar str = window.location.href;\r\n      // \tvar result = encodeURIComponent(str)\r\n      // \tif (window.location.host == 'test.ningmengdou.com') {\r\n      // \t\t// url = \"https://ssotest.ningmengdou.com/single/login?returnUrl=\" + result\r\n      // \t\turl = \"https://sso.ningmengdou.com/single/newLogin?returnUrl=\" + result\r\n      // \t} else if (window.location.host == 'www.ningmengdou.com') {\r\n      // \t\t// url = \"https://sso.ningmengdou.com/single/login?returnUrl=\" + result\r\n      // \t\turl = \"https://sso.ningmengdou.com/single/newLogin?returnUrl=\" + result\r\n      // \t}\r\n      // \twindow.location.href = url\r\n      // \treturn\r\n      // }\r\n      // this.showGm = true\r\n    },\r\n    collect() {\r\n      let data = {\r\n        id: this.info.id,\r\n        userId: store.getters.userId,\r\n      };\r\n      appliCollect(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message.success(\"操作成功!\");\r\n          this.getDetail();\r\n        }\r\n      });\r\n    },\r\n    cancelCollect() {\r\n      let data = {\r\n        id: this.info.id,\r\n        userId: store.getters.userId,\r\n      };\r\n      appliCancelCollect(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message.success(\"操作成功!\");\r\n          this.getDetail();\r\n        }\r\n      });\r\n    },\r\n    getSpec(value) {\r\n      console.log(value);\r\n      this.specFlag = value;\r\n    },\r\n    order() {\r\n      let data = {\r\n        appId: this.info.id,\r\n        price: this.info.price,\r\n        totalAmount: this.info.price,\r\n        spec: this.specFlag,\r\n        validTime: this.duFlag,\r\n        phone: this.info.phone,\r\n        remark: this.info.appName,\r\n      };\r\n      appliOrder(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.showGm = false;\r\n          this.$message.success(\"下单成功!\");\r\n          this.$router.push({\r\n            path: \"/payment\",\r\n            query: {\r\n              id: res.data.id,\r\n            },\r\n          });\r\n        }\r\n      });\r\n    },\r\n    getDuration(value) {\r\n      this.duFlag = value;\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.info-top {\r\n  background-image: url(\"../../assets/images/appbanner.png\");\r\n  height: 360px;\r\n  background-size: 100%;\r\n}\r\n.info-top span {\r\n  display: inline-block;\r\n  text-align: center;\r\n  vertical-align: top;\r\n}\r\n.info-top .right-info {\r\n  display: inline-block;\r\n  vertical-align: top;\r\n  width: 331px;\r\n  height: 326px;\r\n  background-image: url(\"../../assets/images/appbannersub.png\");\r\n  background-size: 100%;\r\n  margin-top: 8px;\r\n  float: right;\r\n}\r\n.info-top .right-info > div {\r\n  width: 304px;\r\n  height: 184px;\r\n  margin-top: 80px;\r\n  margin-left: 20px;\r\n  opacity: 0.75;\r\n  border-radius: 2px;\r\n  background-color: rgba(255, 255, 255, 1);\r\n}\r\n.info-top .right-info > div p {\r\n  line-height: 36px;\r\n  padding-left: 30px;\r\n  padding-bottom: 8px;\r\n  color: rgba(102, 102, 102, 1);\r\n  font-size: 14px;\r\n  text-align: left;\r\n}\r\n.info-top span.btn {\r\n  cursor: pointer;\r\n  width: 104px;\r\n  height: 36px;\r\n  line-height: 36px;\r\n  border-radius: 4px;\r\n  color: #21c9b8;\r\n  font-size: 16px;\r\n  border: 1px solid #21c9b8;\r\n  margin-right: 20px;\r\n}\r\n.tishi_bg {\r\n  position: fixed;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(255, 255, 255, 0.6);\r\n  z-index: 100;\r\n  top: 0;\r\n  left: 0;\r\n}\r\n.goumai_bg {\r\n  width: 600px;\r\n  position: relative;\r\n  margin-top: 8%;\r\n  margin-right: auto;\r\n  margin-bottom: 0;\r\n  margin-left: auto;\r\n  background-color: #fff;\r\n  border-radius: 10px;\r\n  box-shadow: 0px 0px 4px 0px rgb(51 51 51 / 20%);\r\n  padding: 0px;\r\n  overflow: hidden;\r\n}\r\n.goumai_t {\r\n  background-color: #eee;\r\n  color: #333;\r\n  font-size: 18px;\r\n  line-height: 50px;\r\n  padding-right: 30px;\r\n  padding-left: 30px;\r\n}\r\n\r\n.goumai_t span {\r\n  float: right;\r\n}\r\n\r\n.goumai_t span img {\r\n  width: 25px;\r\n  margin-top: 12px;\r\n}\r\n\r\n.goumai_c {\r\n  padding: 30px;\r\n}\r\n.appLabel a {\r\n  color: #de791b;\r\n}\r\n.goumai_cont {\r\n  border-top-width: 0px;\r\n  border-right-width: 0px;\r\n  border-bottom-width: 1px;\r\n  border-left-width: 0px;\r\n  border-top-style: solid;\r\n  border-right-style: solid;\r\n  border-bottom-style: solid;\r\n  border-left-style: solid;\r\n  border-top-color: #ccc;\r\n  border-right-color: #ccc;\r\n  border-bottom-color: #ccc;\r\n  border-left-color: #ccc;\r\n  padding-top: 10px;\r\n  padding-bottom: 10px;\r\n}\r\n\r\n.goumai_cont p {\r\n  padding-top: 5px;\r\n  padding-bottom: 5px;\r\n}\r\n.goumai_total {\r\n  padding-top: 15px;\r\n  display: flex;\r\n  flex-flow: row wrap;\r\n  justify-content: space-between;\r\n}\r\n\r\n.goumai_total_l {\r\n  width: 60%;\r\n}\r\n\r\n.goumai_total_l strong {\r\n  color: #f00;\r\n  font-size: 24px;\r\n}\r\n\r\n.goumai_total_r {\r\n  width: 40%;\r\n  text-align: right;\r\n}\r\n\r\n.goumai_total_r a {\r\n  display: inline-block;\r\n  line-height: 35px;\r\n  text-align: center;\r\n  border: 1px solid #ccc;\r\n  width: 40%;\r\n  margin-top: 0px;\r\n  margin-right: 0px;\r\n  margin-bottom: 0px;\r\n  margin-left: 2%;\r\n}\r\n\r\n.goumai_total_r a:hover {\r\n  color: #fff;\r\n  background-color: #21c9b8;\r\n  border: 1px solid #21c9b8;\r\n}\r\n::v-deep .appliDetail img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n}\r\n</style>\r\n"]}]}