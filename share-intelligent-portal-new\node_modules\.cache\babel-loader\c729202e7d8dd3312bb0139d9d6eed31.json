{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\components\\userMenu.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\components\\userMenu.vue", "mtime": 1750311963053}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_user", "require", "name", "props", "data", "activeIndex", "$route", "path", "companyStatus", "mobile", "key", "type", "base64EncodeChars", "created", "methods", "getUser", "_this", "checkShowMenuRole", "then", "response", "code", "handleSelect", "keyP<PERSON>", "console", "log", "add", "window", "open"], "sources": ["src/views/system/user/components/userMenu.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 10:58:54\r\n * @LastEditTime: 2023-02-13 19:08:08\r\n * @Description:\r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <el-menu\r\n    :default-active=\"this.$route.path\"\r\n    class=\"user-center-menu\"\r\n    @select=\"handleSelect\"\r\n    :router=\"true\"\r\n  >\r\n    <el-menu-item index=\"/user/profile\" route=\"/user/profile\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-menu\"></i>\r\n        <span class=\"menu-text\" slot=\"title\">首页</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/supplyDemand\" route=\"/user/supplyDemand\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">供需管理</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/reApplication\" route=\"/user/reApplication\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">入驻申请</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/sharedOrders\" route=\"/user/sharedOrders\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">共享订单</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/emInformation\" route=\"/user/emInformation\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">用工信息</span>\r\n      </div>\r\n    </el-menu-item>\r\n      <el-menu-item index=\"/user/equipmentManagement\" route=\"/user/equipmentManagement\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">设备管理</span>\r\n      </div>\r\n    </el-menu-item>\r\n      <el-menu-item index=\"/user/workshopManagement\" route=\"/user/workshopManagement\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">车间管理</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/dockingRecords\" route=\"/user/dockingRecords\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">对接记录</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/userCenter\" route=\"/user/userCenter\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-user\"></i>\r\n        <span slot=\"title\">用户中心</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/spCertification\" route=\"/user/spCertification\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">认证设置</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <!-- <el-menu-item index=\"/user/userInfo\" route=\"/user/userInfo\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-user\"></i>\r\n        <span slot=\"title\">个人资料</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/invoiceInfo\" route=\"/user/invoiceInfo\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-tickets\"></i>\r\n        <span slot=\"title\">发票信息</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n    <!-- <el-menu-item\r\n      index=\"/user/companyInfo\"\r\n      route=\"/user/companyInfo\"\r\n      v-show=\"this.companyStatus\"\r\n    >\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">公司信息</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n    <!-- <el-menu-item index=\"/user/spCertification\" route=\"/user/spCertification\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">认证设置</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/myCollect\" route=\"/user/myCollect\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-star-off\"></i>\r\n        <span slot=\"title\">我的收藏</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/mySubscriptions\" route=\"/user/mySubscriptions\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-finished\"></i>\r\n        <span slot=\"title\">我的订阅</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/application\" route=\"/user/application\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-news\"></i>\r\n        <span slot=\"title\">应用管理</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/orderManage\" route=\"/user/orderManage\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-c-scale-to-original\"></i>\r\n        <span slot=\"title\">订单管理</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n    <!-- <el-menu-item index=\"/user/policyDeclare\" route=\"/user/policyDeclare\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">政策申报</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n    <!-- <el-menu-item index=\"/user/noninductive\" route=\"/user/noninductive\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\" @click=\"add\">无感兑现</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n    <!-- <el-menu-item index=\"/user/commercial\" route=\"/user/commercial\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">商机推荐</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item>\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\" @click=\"add\">数字化诊断</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/companyDemand\" route=\"/user/companyDemand\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">企业需求</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/companyApply\" route=\"/user/companyApply\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">企业资源</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/abutmentRecord\" route=\"/user/abutmentRecord\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">对接记录</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/im\" route=\"/user/im\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\" route=\"/user/im\"></i>\r\n        <span slot=\"title\">即时沟通</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item index=\"/user/approveSetting\" route=\"/user/approveSetting\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">认证设置</span>\r\n      </div>\r\n    </el-menu-item>\r\n    <el-menu-item\r\n      index=\"/user/teamManage\"\r\n      route=\"/user/teamManage\"\r\n      v-show=\"this.companyStatus\"\r\n    >\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-setting\"></i>\r\n        <span slot=\"title\">团队管理</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n    <!-- <el-menu-item index=\"/user/notice\" route=\"/user/notice\">\r\n      <div class=\"item-hover\">\r\n        <i class=\"menu-icon el-icon-chat-dot-round\"></i>\r\n        <span slot=\"title\">通知中心</span>\r\n      </div>\r\n    </el-menu-item> -->\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport { checkShowMenuRole } from \"@/api/system/user\";\r\nexport default {\r\n  name: \"UserMenu\",\r\n  props: {},\r\n  data() {\r\n    return {\r\n      activeIndex: this.$route.path,\r\n      companyStatus: false,\r\n      mobile: \"\",\r\n      key: \"QmRlODJTVGhkNg==\",\r\n      type: \"cG9saWN5Y2FzaA==\",\r\n      base64EncodeChars:\r\n        \"ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",\r\n    };\r\n  },\r\n  created() {\r\n    // this.getUser();\r\n  },\r\n  methods: {\r\n    getUser() {\r\n      // getUserInfo(this.user.id).then((response) => {\r\n      //   this.user = response.data;\r\n      //   this.roleGroup = response.roleGroup;\r\n      //   this.postGroup = response.postGroup;\r\n      // });\r\n      checkShowMenuRole().then((response) => {\r\n        if (response.code == 200) {\r\n          this.companyStatus = true;\r\n        }\r\n      });\r\n    },\r\n    handleSelect(key, keyPath) {\r\n      console.log(key, keyPath);\r\n      console.log(this.$route.path);\r\n    },\r\n    add() {\r\n      window.open(\"https://zhenduan.ningmengdou.com/digital-diagosis-web/\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.user-center-menu {\r\n  width: 160px;\r\n  border-right: #fff;\r\n  padding: 20px 0 20px 0;\r\n  background-color: rgb(239, 251, 247);\r\n  .menu-icon {\r\n    color: #333333;\r\n    margin-right: 8px;\r\n    margin-left: 16px;\r\n  }\r\n  .el-menu-item {\r\n    color: #333333;\r\n    font-weight: 500;\r\n  }\r\n  .item-hover:hover {\r\n    color: #21c9b8;\r\n    .menu-icon {\r\n      color: #21c9b8 !important;\r\n    }\r\n  }\r\n  .el-menu-item:hover {\r\n    outline: 0 !important;\r\n    color: #21c9b8 !important;\r\n    background: #fff !important;\r\n  }\r\n  .el-menu-item.is-active {\r\n    color: #fff !important;\r\n    background-color: #21c9b8 !important;\r\n    .menu-icon {\r\n      color: #fff !important;\r\n    }\r\n  }\r\n  .el-menu-item.is-active :hover {\r\n    color: #fff !important;\r\n    .menu-icon {\r\n      color: #fff !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AAwMA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA,OAAAC,MAAA,CAAAC,IAAA;MACAC,aAAA;MACAC,MAAA;MACAC,GAAA;MACAC,IAAA;MACAC,iBAAA,EACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,OAAA;IACAC,OAAA,WAAAA,QAAA;MAAA,IAAAC,KAAA;MACA;MACA;MACA;MACA;MACA;MACA,IAAAC,uBAAA,IAAAC,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAR,aAAA;QACA;MACA;IACA;IACAa,YAAA,WAAAA,aAAAX,GAAA,EAAAY,OAAA;MACAC,OAAA,CAAAC,GAAA,CAAAd,GAAA,EAAAY,OAAA;MACAC,OAAA,CAAAC,GAAA,MAAAlB,MAAA,CAAAC,IAAA;IACA;IACAkB,GAAA,WAAAA,IAAA;MACAC,MAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}