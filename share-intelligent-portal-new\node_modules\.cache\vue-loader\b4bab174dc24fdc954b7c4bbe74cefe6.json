{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyDemand\\detail\\index.vue?vue&type=style&index=0&id=61587f19&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyDemand\\detail\\index.vue", "mtime": 1750311963050}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogI2Y0ZjVmOTsNCiAgLmNvbXBhbnktZGVtYW5kLWRldGFpbCB7DQogICAgLmluZm8tY29udGFpbmVyIHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgcGFkZGluZy10b3A6IDEycHg7DQogICAgICBwYWRkaW5nOiAxMHB4IDMwcHg7DQoNCiAgICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlOw0KICAgICAgLmhlYWRlciB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDMwcHg7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgIC5lbC1idXR0b24gew0KICAgICAgICAgIGhlaWdodDogNDBweDsNCiAgICAgICAgICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50Ow0KICAgICAgICAgIHBhZGRpbmc6IDEwcHggMTBweCAxMHB4IDIwcHg7DQogICAgICAgICAgZm9udC1zaXplOiAyMHB4Ow0KICAgICAgICAgIGNvbG9yOiAjMDAwOw0KICAgICAgICB9DQogICAgICAgIC5lbC1idXR0b246aG92ZXIgew0KICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlOw0KICAgICAgICB9DQogICAgICAgIC5oZWFkZXItdGV4dCB7DQogICAgICAgICAgZm9udC1zaXplOiAyNHB4Ow0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDQwcHg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC5kZXRhaWwtcGFnZSB7DQogICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAgICAgICAuaGVhZGVyLXNtYWxsIHsNCiAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgICAgICBsaW5lLWhlaWdodDogMTZweDsNCiAgICAgICAgICBtYXJnaW4tYm90dG9tOiAzNnB4Ow0KDQogICAgICAgICAgLnJlZC10YWcgew0KICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMnB4Ow0KICAgICAgICAgICAgd2lkdGg6IDNweDsNCiAgICAgICAgICAgIGhlaWdodDogMTZweDsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC5tdF80MCB7DQogICAgICAgICAgbWFyZ2luLXRvcDogNDBweDsNCiAgICAgICAgfQ0KICAgICAgICAuZmlsZS1jbGFzcyB7DQogICAgICAgICAgd2lkdGg6IDczM3B4Ow0KICAgICAgICAgIGhlaWdodDogNDBweDsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjdmOGZhOw0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCiAgICAgICAgICBwYWRkaW5nOiAwIDIwcHg7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgICAgICBsaW5lLWhlaWdodDogNDBweDsNCiAgICAgICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQoNCiAgICAgICAgICAuZWwtaW1hZ2Ugew0KICAgICAgICAgICAgbWFyZ2luOiAxMnB4IDhweCAwIDA7DQogICAgICAgICAgfQ0KICAgICAgICAgIC5wcmV2aXdlLWNsYXNzIHsNCiAgICAgICAgICAgIHJpZ2h0OiAyMHB4Ow0KICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgICAgICAgbWFyZ2luOiA4cHggMCAwIDA7DQogICAgICAgICAgICB3aWR0aDogNzJweDsNCiAgICAgICAgICAgIGhlaWdodDogMjRweDsNCiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDE2cHg7DQogICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjMmY3NmUwOw0KICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgICAgIGNvbG9yOiAjMmY3NmUwOw0KICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC5zdGF0dXNfYXBwcm92aW5nIHsNCiAgICAgICAgICB0b3A6IDBweDsNCiAgICAgICAgICByaWdodDogMjBweDsNCiAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLmVkaXQtcGFnZSB7DQogICAgICAgIC5lbC1pbnB1dC0tbWVkaXVtIC5lbC1pbnB1dF9faW5uZXIgew0KICAgICAgICAgIHdpZHRoOiA5MCU7DQogICAgICAgICAgaGVpZ2h0OiAzNnB4Ow0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAzNnB4Ow0KICAgICAgICB9DQogICAgICAgIC5lbC10ZXh0YXJlYV9faW5uZXIgew0KICAgICAgICAgIHdpZHRoOiA5MCU7DQogICAgICAgIH0NCiAgICAgICAgLmFkZC1kZW1hbmQtdGFnIHsNCiAgICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogICAgICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAzMnB4Ow0KICAgICAgICB9DQogICAgICAgIC5lbC1idXR0b24tLXByaW1hcnkgew0KICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICAgICAgYm9yZGVyLWNvbG9yOiAjYmZiZmJmOw0KICAgICAgICB9DQogICAgICAgIC5lbC1idXR0b24tLWRhbmdlciB7DQogICAgICAgICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICAgICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgICAgICBib3JkZXItY29sb3I6ICMyMWM5Yjg7DQogICAgICAgIH0NCiAgICAgICAgLnRpcCB7DQogICAgICAgICAgcGFkZGluZy1sZWZ0OiAxMHB4Ow0KICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgIGNvbG9yOiAjOGM4YzhjOw0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxOHB4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgICAuZWwtZGVzY3JpcHRpb25zLS1tZWRpdW0uaXMtYm9yZGVyZWQgLmVsLWRlc2NyaXB0aW9ucy1pdGVtX19jZWxsIHsNCiAgICAgICAgcGFkZGluZzogMTBweDsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgIH0NCg0KICAgICAgLmVsLWRlc2NyaXB0aW9ucy0tbWVkaXVtLmlzLWJvcmRlcmVkIC5lbC1kZXNjcmlwdGlvbnMtaXRlbV9fbGFiZWwgew0KICAgICAgICBwYWRkaW5nOiAxNXB4Ow0KICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgIGZvbnQtc2l6ZTogMTRweDsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICAgIHdpZHRoOiAyMDBweDsNCiAgICAgIH0NCiAgICAgIC5kZWxldGUtYnRuIHsNCiAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgIG1hcmdpbi10b3A6IDIwcHg7DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgLmVsLWJ1dHRvbiB7DQogICAgICAgICAgcGFkZGluZzogMTJweCA1NXB4Ow0KICAgICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgICB9DQogICAgICAgIC5lbC1idXR0b246aG92ZXIsDQogICAgICAgIC5lbC1idXR0b246Zm9jdXMgew0KICAgICAgICAgIGJvcmRlci1jb2xvcjogI2Q5ZDlkOTsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICAgICAgICB9DQogICAgICAgIC5lbC1idXR0b24tLWRhbmdlciB7DQogICAgICAgICAgbWFyZ2luLWxlZnQ6IDMwcHg7DQogICAgICAgICAgY29sb3I6ICNmZmZmZmY7DQogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzIxYzliOCAhaW1wb3J0YW50Ow0KICAgICAgICAgIGJvcmRlci1jb2xvcjogIzIxYzliOCAhaW1wb3J0YW50Ow0KICAgICAgICB9DQogICAgICAgIC5lbC1idXR0b24tLWVycm9yIHsNCiAgICAgICAgICBtYXJnaW4tbGVmdDogMzBweDsNCiAgICAgICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmZmZmOw0KICAgICAgICAgIGJvcmRlci1jb2xvcjogIzIxYzliODsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAomBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/companyDemand/detail", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-11 15:20:15\r\n * @LastEditTime: 2023-02-28 08:48:59\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-11 15:18:41\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"company-demand-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">{{ this.title }}</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                基本信息\r\n                <el-button\r\n                  plain\r\n                  type=\"primary\"\r\n                  style=\"position: absolute; right: 0\"\r\n                  @click=\"toZiyuan\"\r\n                  >查看平台匹配资源</el-button\r\n                >\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 需求标题 </template>\r\n                  {{ info.demandTitle }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 需求描述 </template>\r\n                  {{ info.summary }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 应用领域</template>\r\n                  {{ info.applicationArea }}\r\n                </el-descriptions-item>\r\n\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 需求封面 </template>\r\n                  <el-image\r\n                    style=\"width: 90px; height: 64px\"\r\n                    :src=\"getUrl(info.scenePicture)\"\r\n                  ></el-image>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <div class=\"header-small mt_40\">\r\n                <div class=\"red-tag\"></div>\r\n                联系信息\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 公司名称 </template>\r\n                  {{ info.companyName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系人 </template>\r\n                  {{ info.contactsName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系电话</template>\r\n                  {{ info.contactsMobile }}\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <el-image\r\n                class=\"status_approving\"\r\n                v-if=\"info.status == '1'\"\r\n                style=\"width: 120px; height: 102px\"\r\n                :src=\"require('@/assets/user/status_approving.png')\"\r\n              ></el-image>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"goBack\">返回</el-button>\r\n                <el-button type=\"danger\" @click=\"changeMode\">编辑</el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"edit-page\" v-else>\r\n              <el-form\r\n                ref=\"form\"\r\n                :model=\"form\"\r\n                :rules=\"rules\"\r\n                label-position=\"top\"\r\n              >\r\n                <el-form-item label=\"需求标题\" prop=\"demandTitle\">\r\n                  <el-input v-model=\"form.demandTitle\" placeholder=\"请输入\" />\r\n                </el-form-item>\r\n                <el-form-item prop=\"demandType\">\r\n                  <div class=\"label-item\" slot=\"label\">\r\n                    <span>需求类型</span>\r\n                    <span class=\"extra\"\r\n                      >（可按需求产品+应用行业+应用领域进行描述）</span\r\n                    >\r\n                  </div>\r\n                  <el-checkbox-group\r\n                    v-model=\"form.demandType\"\r\n                    placeholder=\"请选择\"\r\n                    clearable\r\n                  >\r\n                    <el-checkbox\r\n                      v-for=\"dict in dict.type.demand_type\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.value\"\r\n                      :value=\"dict.value\"\r\n                      >{{ dict.label }}</el-checkbox\r\n                    >\r\n                  </el-checkbox-group>\r\n                </el-form-item>\r\n                <el-form-item label=\"需求描述\" prop=\"summary\">\r\n                  <el-input\r\n                    v-model=\"form.summary\"\r\n                    type=\"textarea\"\r\n                    :rows=\"2\"\r\n                    :maxlength=\"500\"\r\n                    placeholder=\"请输入\"\r\n                  />\r\n                  <div class=\"extra-content\">\r\n                    <div class=\"extra-content-header\">\r\n                      <el-button\r\n                        @click=\"handleKeywordList\"\r\n                        size=\"small\"\r\n                        type=\"primary\"\r\n                        >生成关键词</el-button\r\n                      >\r\n                      <span class=\"tip\">生成关键词有利于实现精准匹配哦！</span>\r\n                    </div>\r\n                    <div\r\n                      v-if=\"form.keywords && form.keywords.length > 0\"\r\n                      class=\"extra-content-body\"\r\n                    >\r\n                      <el-tag\r\n                        :key=\"`${tag}_${index}`\"\r\n                        v-for=\"(tag, index) in form.keywords\"\r\n                        closable\r\n                        size=\"small\"\r\n                        disable-transitions\r\n                        @close=\"handleSummaryClose(tag)\"\r\n                      >\r\n                        {{ tag }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n                <el-form-item label=\"应用领域\" prop=\"applicationArea\">\r\n                  <el-select\r\n                    v-model=\"form.applicationArea\"\r\n                    filterable\r\n                    multiple\r\n                    allow-create\r\n                    style=\"width: 100%\"\r\n                    placeholder=\"请选择\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in dict.type.application_area\"\r\n                      :key=\"item.value\"\r\n                      :label=\"item.label\"\r\n                      :value=\"item.label\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                  <!-- <el-tag\r\n                    v-for=\"tag in form.applicationAreaList\"\r\n                    closable\r\n                    class=\"add-demand-tag\"\r\n                    :key=\"tag\"\r\n                    :disable-transitions=\"false\"\r\n                    @close=\"handleClose(tag)\"\r\n                  >\r\n                    {{ tag }}\r\n                  </el-tag>\r\n                  <el-input v-model=\"applicationsInput\" :maxlength=\"255\">\r\n                  </el-input>\r\n                  <el-button\r\n                    size=\"small\"\r\n                    icon=\"el-icon-plus\"\r\n                    class=\"add-demand-btn-tag\"\r\n                    @click=\"handleInputConfirm\"\r\n                    >新增</el-button\r\n                  > -->\r\n                </el-form-item>\r\n                <el-form-item label=\"产品图片\">\r\n                  <el-upload\r\n                    list-type=\"picture-card\"\r\n                    :headers=\"headers\"\r\n                    :action=\"uploadUrl\"\r\n                    :file-list=\"form.scenePictureList\"\r\n                    :accept=\"accept\"\r\n                    :before-upload=\"handleBeforeUpload\"\r\n                    :on-preview=\"handlePictureCardPreview\"\r\n                    :on-remove=\"handleRemove\"\r\n                    :on-success=\"handleSuccess\"\r\n                  >\r\n                    <i class=\"el-icon-plus\"></i>\r\n                  </el-upload>\r\n                  <el-dialog\r\n                    append-to-body\r\n                    :visible.sync=\"imgVisible\"\r\n                    :close-on-click-modal=\"false\"\r\n                  >\r\n                    <img v-if=\"imageUrl\" width=\"100%\" :src=\"imageUrl\" alt=\"\" />\r\n                  </el-dialog>\r\n                </el-form-item>\r\n                <el-form-item label=\"展示限制\" prop=\"displayRestrictions\">\r\n                  <el-select\r\n                    v-model=\"form.displayRestrictions\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100%\"\r\n                    clearable\r\n                  >\r\n                    <el-option\r\n                      v-for=\"dict in dict.type.display_restrictions\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.label\"\r\n                      :value=\"dict.value\"\r\n                    />\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n                      <el-input\r\n                        disabled\r\n                        v-model=\"form.companyName\"\r\n                        placeholder=\"请输入公司名称\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"联系人\" prop=\"contactsName\">\r\n                      <el-input\r\n                        disabled\r\n                        v-model=\"form.contactsName\"\r\n                        placeholder=\"请输入联系人\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"联系电话\" prop=\"contactsMobile\">\r\n                      <el-input\r\n                        disabled\r\n                        v-model=\"form.contactsMobile\"\r\n                        placeholder=\"请输入联系电话\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"changeMode\">返回</el-button>\r\n                <!-- <el-button type=\"error\" @click=\"changeMode(0)\"\r\n                  >暂存草稿</el-button\r\n                > -->\r\n                <el-button type=\"danger\" @click=\"submitForm(1)\"\r\n                  >提交审核</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport { getDemandDetail, createDemand, editDemand } from \"@/api/system/demand\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport store from \"@/store\";\r\nimport { demandAdd, keywordList } from \"@/api/zhm\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\r\n    \"demand_type\",\r\n    \"affiliated_unit\",\r\n    \"capital_source\",\r\n    \"affiliated_street\",\r\n    \"display_restrictions\",\r\n    \"application_area\",\r\n  ],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      title: \"需求详情\",\r\n      imageUrl: \"\",\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      isCreate: false,\r\n      imgVisible: false,\r\n      user: {\r\n        tel: store.getters.tel,\r\n        name: store.getters.name,\r\n        companyName: store.getters.companyName,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        phonenumber: store.getters.phonenumber,\r\n      },\r\n      keywords: [],\r\n      applicationsInput: \"\",\r\n      info: {},\r\n\r\n      form: {},\r\n      accountLicenceList: [],\r\n\r\n      // 表单校验\r\n      rules: {\r\n        demandTitle: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" },\r\n        ],\r\n        demandType: [\r\n          { required: true, message: \"请选择需求类型\", trigger: \"change\" },\r\n        ],\r\n        applicationArea: [\r\n          { required: true, message: \"请选择应用领域\", trigger: \"change\" },\r\n        ],\r\n        displayRestrictions: [\r\n          { required: true, message: \"请选择展示限制\", trigger: \"change\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"需求描述不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.isCreate = this.$route.query.type == 1;\r\n    if (this.isCreate) {\r\n      this.goCreate();\r\n    } else {\r\n      this.getDetail();\r\n    }\r\n  },\r\n  methods: {\r\n    initForm() {\r\n      this.form = {\r\n        demandType: [],\r\n        applicationArea: [],\r\n        scenePicture: [],\r\n        // applicationAreaList: [],\r\n        scenePictureList: [],\r\n        keywords: [],\r\n        auditStatus: \"1\",\r\n        displayStatus: \"2\",\r\n        publisherName: this.user.name,\r\n        publisherMobile: this.user.tel,\r\n        // 展示限制\r\n        displayRestrictions: undefined,\r\n      };\r\n    },\r\n    getDetail() {\r\n      let id = this.$route.query.id;\r\n      getDemandDetail(id).then((response) => {\r\n        let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n        let decrypt = CryptoJS.AES.decrypt(response, key, {\r\n          mode: CryptoJS.mode.ECB,\r\n          padding: CryptoJS.pad.Pkcs7,\r\n        });\r\n        response = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    getUrl(str) {\r\n      if (str && str != null) {\r\n        var list = JSON.parse(str);\r\n        if (list && list.length > 0) {\r\n          return list[0].url;\r\n        }\r\n      }\r\n\r\n      return null;\r\n    },\r\n    // // 应用领域新增\r\n    // handleInputConfirm() {\r\n    //   let val = this.applicationsInput;\r\n    //   if (val) {\r\n    //     this.form.applicationAreaList.push(val);\r\n    //   }\r\n    //   this.applicationsInput = \"\";\r\n    // },\r\n    // // 应用领域移除\r\n    // handleClose(tag) {\r\n    //   this.form.applicationAreaList.splice(\r\n    //     this.form.applicationAreaList.indexOf(tag),\r\n    //     1\r\n    //   );\r\n    // },\r\n    handleSummaryClose(tag) {\r\n      this.form.keywords.splice(this.form.keywords.indexOf(tag), 1);\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePictureCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.form.scenePictureList = fileList;\r\n    },\r\n    handleSuccess(response, file) {\r\n      if (response.code == 200) {\r\n        if (this.form.scenePictureList == null) {\r\n          this.form.scenePictureList = [];\r\n        }\r\n        this.form.scenePictureList.push(response.data);\r\n      }\r\n    },\r\n    changeMode() {\r\n      if (this.isCreate) {\r\n        this.goBack();\r\n        return;\r\n      }\r\n      if (this.isDetail) {\r\n        this.title = \"编辑需求\";\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n        if (this.info.applicationArea) {\r\n          this.form.applicationArea = this.info.applicationArea.split(\",\");\r\n        } else {\r\n          this.form.applicationArea = [];\r\n        }\r\n        if (this.info.displayRestrictions) {\r\n          this.form.displayRestrictions =\r\n            this.info.displayRestrictions.toString();\r\n        }\r\n        if (this.info.keywords) {\r\n          this.form.keywords = this.info.keywords.split(\",\");\r\n        }\r\n        if (this.info.demandType) {\r\n          this.form.demandType = this.info.demandType.split(\",\");\r\n        }\r\n        if (this.info.scenePicture && this.info.scenePicture != \"null\") {\r\n          this.form.scenePictureList = JSON.parse(this.info.scenePicture);\r\n        } else {\r\n          this.form.scenePictureList = [];\r\n        }\r\n      } else {\r\n        this.isDetail = true;\r\n        this.title = \"需求详情\";\r\n        this.initForm();\r\n        this.getDetail();\r\n      }\r\n    },\r\n    goCreate() {\r\n      this.title = \"新增需求\";\r\n      this.isDetail = false;\r\n      this.initForm();\r\n      this.form.companyName = this.user.companyName;\r\n      this.form.contactsName = this.user.name;\r\n      this.form.contactsMobile = this.user.phonenumber;\r\n      this.form.publisherName = this.user.name;\r\n      this.form.publisherMobile = this.user.phonenumber;\r\n      this.form.businessNo = this.user.bussinessNo;\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    displayRestrictionChanged(res) {\r\n      this.dict.type.display_restrictions.forEach((item) => {\r\n        if (item.label == res) {\r\n          this.form.displayRestrictions = item.value;\r\n        }\r\n      });\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          // if (\r\n          //   this.form.applicationAreaList &&\r\n          //   this.form.applicationAreaList.length > 0\r\n          // ) {\r\n          //   this.form.applicationArea = this.form.applicationAreaList.join(\",\");\r\n          // } else {\r\n          //   this.form.applicationArea = \"\";\r\n          // }\r\n          if (this.form.applicationArea.length > 0) {\r\n            this.form.applicationArea = this.form.applicationArea.join();\r\n          }\r\n          this.form.scenePicture = JSON.stringify(this.form.scenePictureList);\r\n          this.form.businessNo = this.user.bussinessNo;\r\n          if (this.form.keywords && this.form.keywords.length > 0) {\r\n            this.form.keywords = this.form.keywords.join(\",\");\r\n          } else {\r\n            this.form.keywords = \"\";\r\n          }\r\n          if (this.form.demandType.length > 0) {\r\n            this.form.demandType = this.form.demandType.join();\r\n          }\r\n          if (this.isCreate) {\r\n            createDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          } else {\r\n            this.form.auditStatus = 1;\r\n            editDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    toZiyuan() {\r\n      this.$router.push({\r\n        path: \"/user/companyApplyDetail1\",\r\n        query: { key: JSON.stringify(this.info) },\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      const { summary } = this.form;\r\n      if (summary) {\r\n        keywordList(summary).then((res) => {\r\n          const { code, data, msg } = res;\r\n          if (code === 200) {\r\n            this.form.keywords = data;\r\n          } else {\r\n            this.$message.error(msg);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning(\"请输入需求描述\");\r\n      }\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.application = res.data.url;\r\n        this.form.applicationName = res.data.name;\r\n      }\r\n    },\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .company-demand-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 36px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .mt_40 {\r\n          margin-top: 40px;\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 90%;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-textarea__inner {\r\n          width: 90%;\r\n        }\r\n        .add-demand-tag {\r\n          margin-right: 10px;\r\n          height: 32px;\r\n          line-height: 32px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n          color: #333;\r\n        }\r\n        .el-button:hover,\r\n        .el-button:focus {\r\n          border-color: #d9d9d9;\r\n          background-color: #fff;\r\n        }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8 !important;\r\n          border-color: #21c9b8 !important;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .el-input__suffix {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 11%;\r\n}\r\n.el-checkbox {\r\n  font-size: 14px;\r\n  font-weight: 400;\r\n  color: #262626;\r\n  line-height: 18px;\r\n  margin-right: 28px;\r\n}\r\n.edit-page {\r\n  padding-left: 50px;\r\n}\r\n</style>\r\n"]}]}