{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\policyDeclare\\index.vue?vue&type=style&index=0&id=02c91e66&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\policyDeclare\\index.vue", "mtime": 1750311963071}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/policyDeclare", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:38:32\r\n * @LastEditTime: 2023-02-21 12:15:31\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-03 11:20:59\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"noninductive-record-page\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div>\r\n            <el-radio-group v-model=\"status\" @change=\"changeType\">\r\n              <el-radio-button label=\"\">全部</el-radio-button>\r\n              <el-radio-button label=\"2\">审核中</el-radio-button>\r\n              <el-radio-button label=\"3\">审核通过</el-radio-button>\r\n              <el-radio-button label=\"4\">审核驳回</el-radio-button>\r\n              <el-radio-button label=\"1\">草稿箱</el-radio-button>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"noninductive-pannel\">\r\n            <div class=\"none-class\" v-if=\"!records || records.length == 0\">\r\n              <el-image\r\n                style=\"width: 160px; height: 160px\"\r\n                :src=\"require('@/assets/user/none.png')\"\r\n                :fit=\"fit\"\r\n              ></el-image>\r\n              <div class=\"text\">暂无数据</div>\r\n            </div>\r\n            <div\r\n              class=\"noninductive-item\"\r\n              v-for=\"item in records\"\r\n              v-bind:key=\"item.id\"\r\n            >\r\n              <a class=\"left\" @click=\"goDetail(item.id)\">\r\n                <div class=\"noninductive-title\">\r\n                  {{ item.policyName }}\r\n                </div>\r\n                <div class=\"item-info\">\r\n                  <div class=\"company-name\">\r\n                    提交时间：{{ item.updateTime }}\r\n                  </div>\r\n                </div>\r\n              </a>\r\n              <el-image\r\n                class=\"status-icon\"\r\n                v-if=\"item.status != '1'\"\r\n                style=\"width: 16px; height: 16px\"\r\n                :src=\"\r\n                  require('@/assets/user/noninductive_status_' +\r\n                    getItemStatus(item.status) +\r\n                    '.png')\r\n                \"\r\n              ></el-image>\r\n\r\n              <div class=\"noninductive-status\">{{ item.statusName }}</div>\r\n              <a\r\n                @click=\"doRevocation(item)\"\r\n                class=\"revocation-button\"\r\n                v-if=\"item.status == '2'\"\r\n              >\r\n                <div>撤回</div>\r\n              </a>\r\n            </div>\r\n          </div>\r\n          <el-pagination\r\n            v-show=\"total > 0\"\r\n            background\r\n            :page-size=\"6\"\r\n            layout=\"prev, pager, next\"\r\n            :current-page.sync=\"queryParams.pageNum\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :total=\"total\"\r\n          >\r\n          </el-pagination>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listPolicy, revocationPolicy } from \"@/api/system/policy\";\r\nexport default {\r\n  name: \"PolicyDeclare\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      status: \"\",\r\n      fit: \"cover\",\r\n      records: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 6,\r\n      },\r\n      total: 1,\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    changeType(res) {\r\n      this.getList();\r\n    },\r\n    getList() {\r\n      listPolicy({ ...this.queryParams, status: this.status }).then(\r\n        (response) => {\r\n          this.records = response.rows;\r\n          this.total = response.total;\r\n        }\r\n      );\r\n    },\r\n    doRevocation(item) {\r\n      this.$confirm(\"是否确认撤回该提报？\", { type: \"error\" })\r\n        .then((_) => {\r\n          revocationPolicy(item.id).then((response) => {\r\n            this.$message({\r\n              message: \"操作成功\",\r\n              type: \"success\",\r\n            });\r\n            this.getList();\r\n          });\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push(\"/user/policyDeclareDetail?id=\" + id);\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    getItemStatus(status) {\r\n      switch (status) {\r\n        case \"2\":\r\n          return \"1\";\r\n          break;\r\n        case \"3\":\r\n          return \"2\";\r\n          break;\r\n        case \"4\":\r\n          return \"3\";\r\n          break;\r\n        default:\r\n          return \"1\";\r\n          break;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .noninductive-record-page {\r\n    .noninductive-pannel {\r\n      margin-top: 24px;\r\n      width: 100%;\r\n      height: 600px;\r\n      background: #fff;\r\n      .none-class {\r\n        text-align: center;\r\n        padding: 10% 0;\r\n        .text {\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #999999;\r\n          line-height: 14px;\r\n        }\r\n      }\r\n      .noninductive-item {\r\n        display: flex;\r\n        padding: 0 20px;\r\n        height: 100px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        .left {\r\n          width: 53%;\r\n          .noninductive-title {\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            width: 100%;\r\n            line-height: 56px;\r\n            overflow: hidden;\r\n            -webkit-line-clamp: 1;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n          }\r\n          .item-info {\r\n            display: flex;\r\n            .company-name {\r\n              font-size: 14px;\r\n              font-weight: 400;\r\n              color: #666666;\r\n              line-height: 20px;\r\n            }\r\n            .ml_150 {\r\n              margin-left: 150px;\r\n            }\r\n          }\r\n        }\r\n        .status-icon {\r\n          margin: auto 10px;\r\n          height: 100px;\r\n        }\r\n        .noninductive-status {\r\n          line-height: 100px;\r\n          font-size: 15px;\r\n          width: 350px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          overflow: hidden;\r\n          -webkit-line-clamp: 2;\r\n          text-overflow: ellipsis;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n        }\r\n        .revocation-button {\r\n          width: 50px;\r\n          height: 26px;\r\n          border-radius: 4px;\r\n          border: 1px solid #21c9b8;\r\n          font-size: 12px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          font-weight: 400;\r\n          color: #21c9b8;\r\n          line-height: 26px;\r\n          text-align: center;\r\n          margin: auto;\r\n        }\r\n      }\r\n    }\r\n    .el-radio-button {\r\n      margin-right: 30px;\r\n    }\r\n    .el-radio-button__inner {\r\n      width: 96px;\r\n      height: 32px;\r\n      background: transparent;\r\n      border-radius: 20px;\r\n      text-align: center;\r\n      color: #333333;\r\n      border: none;\r\n    }\r\n    .el-radio-button__orig-radio:checked + .el-radio-button__inner {\r\n      background: #21c9b8 !important;\r\n      color: #fff;\r\n      box-shadow: none;\r\n    }\r\n    .el-radio-button__inner:hover {\r\n      color: #333333;\r\n    }\r\n\r\n    .el-pagination {\r\n      width: 100%;\r\n      margin-top: 20px;\r\n      text-align: center;\r\n    }\r\n    .el-pagination.is-background .el-pager li {\r\n      background-color: #fff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled).active {\r\n      background-color: #21c9b8;\r\n      color: #ffffff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled):hover {\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}