{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\mySubscriptions\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\mySubscriptions\\detail.vue", "mtime": 1750311963062}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_user", "require", "_userMenu", "_interopRequireDefault", "_default", "exports", "default", "name", "dicts", "components", "UserMenu", "data", "queryParams", "pageNum", "total", "flag", "tableData", "info", "invoiceData", "orderForm", "value", "statusName", "desc", "invoiceVisible", "created", "getList", "methods", "_this", "loading", "id", "$route", "query", "orderDetail", "then", "res", "code", "push", "handleCurrentChange", "tryout", "item", "console", "log", "appName", "url", "hostname", "result", "encodeURIComponent", "window", "open", "userid", "jsonData", "U", "P", "A", "encodedData", "btoa", "JSON", "stringify", "webexperienceUrl", "cancelOrder", "_this2", "$confirm", "confirmButtonText", "cancelButtonText", "type", "$message", "success", "$router", "go", "catch", "applyInvoice", "getInvoiceData", "_this3", "invoiceList", "confirmReceipt", "_this4", "orderStatus", "modifyStatus", "submitForm", "_this5", "invoiceMedium", "invoiceType", "issueType", "invoiceHeader", "companyName", "dutyParagraph", "email", "orderId", "sendTo", "userId", "cancelDialog", "goBack"], "sources": ["src/views/system/user/mySubscriptions/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\">\r\n          <div class=\"content_top\">\r\n            <div class=\"orderStatus\">\r\n              <div class=\"statusName\" v-if=\"info.orderStatus\">\r\n                {{\r\n                  orderForm.filter((item) => item.value == info.orderStatus)[0]\r\n                    .statusName\r\n                }}\r\n              </div>\r\n              <div class=\"desc\" v-if=\"info.orderStatus\">\r\n                {{\r\n                  orderForm.filter((item) => item.value == info.orderStatus)[0]\r\n                    .desc\r\n                }}\r\n              </div>\r\n            </div>\r\n            <div class=\"amountMoney\">\r\n              <span style=\"color: rgb(173, 173, 173)\">订单金额:</span>\r\n              <span style=\"margin-left: 10px\">¥ {{ info.price }}</span>\r\n            </div>\r\n            <!-- 待支付 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 1\">\r\n              <div>\r\n                <div class=\"buttonStyle\">去支付</div>\r\n                <div style=\"margin-top: 10px\">\r\n                  <span\r\n                    style=\"color: #21c9b8; cursor: pointer\"\r\n                    @click=\"cancelOrder(info.id)\"\r\n                    >取消订单</span\r\n                  >\r\n                  <span\r\n                    style=\"margin-left: 20px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"tryout(info)\"\r\n                    >前往试用</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 待发货 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 2\">\r\n              <div>\r\n                <div>\r\n                  <span\r\n                    style=\"margin-left: 20px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"tryout(info)\"\r\n                    >前往试用</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 已发货 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 4\">\r\n              <div>\r\n                <div>\r\n                  <span\r\n                    style=\"color: #21c9b8; cursor: pointer\"\r\n                    @click=\"applyInvoice(info.id)\"\r\n                    >申请开票</span\r\n                  >\r\n                  <span\r\n                    style=\"margin-left: 20px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"confirmReceipt(info.id)\"\r\n                    >确认收货</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 已成交 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 5\">\r\n              <div>\r\n                <div>\r\n                  <span style=\"color: #21c9b8; cursor: pointer\">已开票</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 待续费 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 6\">\r\n              <div>\r\n                <div>\r\n                  <span style=\"color: #21c9b8; cursor: pointer\">去支付</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"content_bottom\">\r\n            <div>\r\n              <el-descriptions title=\"订单信息\" :column=\"2\">\r\n                <el-descriptions-item label=\"订单编号\">{{\r\n                  info.id\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"下单时间\">{{\r\n                  info.orderDate\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"应用提供\">{{\r\n                  info.supply\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"付款时间\">\r\n                  <el-tag size=\"small\">{{\r\n                    info.payTime ? parseTime(info.payTime) : \"--\"\r\n                  }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"应用编号\">{{\r\n                  info.appCode\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"发货时间\">{{\r\n                  info.deliverTime\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"付款方式\">{{\r\n                  info.payWay\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"成交时间\">{{\r\n                  info.makeTime\r\n                }}</el-descriptions-item>\r\n              </el-descriptions>\r\n            </div>\r\n            <div style=\"margin-top: 30px\">\r\n              <el-table :data=\"tableData\" style=\"width: 100%\">\r\n                <el-table-column prop=\"remark\" label=\"产品标题\">\r\n                </el-table-column>\r\n                <el-table-column label=\"产品图片\">\r\n                  <template slot-scope=\"scope\">\r\n                    <img\r\n                      style=\"width: 100px; height: 100px\"\r\n                      :src=\"scope.row.appLogo\"\r\n                      alt=\"\"\r\n                    />\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column prop=\"appCategory\" label=\"产品类型\">\r\n                </el-table-column>\r\n                <el-table-column label=\"规格信息\">\r\n                  <template slot-scope=\"scoped\">\r\n                    <!-- <div>规格: {{ scoped.row.spec }}</div> -->\r\n                    <div>\r\n                      可用时长:\r\n                      {{ scoped.row.validTime == \"1\" ? \"一年\" : \"永久\" }}\r\n                    </div>\r\n                    <div>可用人数: 不限</div>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"有效时间\">\r\n                  <template slot-scope=\"scope\">\r\n                    {{\r\n                      scope.row.expirationTime\r\n                        ? parseTime(scope.row.expirationTime)\r\n                        : \"--\"\r\n                    }}\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </div>\r\n          <div class=\"btnStyle\">\r\n            <el-button @click=\"goBack\">返回</el-button>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      title=\"开票信息\"\r\n      :visible.sync=\"invoiceVisible\"\r\n      width=\"750px\"\r\n      append-to-body\r\n    >\r\n      <el-form :model=\"invoiceData\" label-width=\"80px\">\r\n        <el-form-item label=\"发票类型:\" prop=\"realName\">\r\n          {{ invoiceData.invoiceType == 1 ? \"专票\" : \"普票\" }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司名称:\" prop=\"phonenumber\">\r\n          {{ invoiceData.companyName }}\r\n        </el-form-item>\r\n        <el-form-item label=\"税号:\" prop=\"weixin\">\r\n          {{ invoiceData.dutyParagraph }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司地址:\" prop=\"email\">\r\n          {{ invoiceData.address }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司电话:\" prop=\"email\">\r\n          {{ invoiceData.phone }}\r\n        </el-form-item>\r\n        <el-form-item label=\"开户银行:\" prop=\"email\">\r\n          {{ invoiceData.openAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"银行账号:\" prop=\"email\">\r\n          {{ invoiceData.bankAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱地址:\" prop=\"email\">\r\n          {{ invoiceData.email }}\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  orderDetail,\r\n  cancelOrder,\r\n  invoiceList,\r\n  applyInvoice,\r\n  modifyStatus,\r\n} from \"@/api/system/user\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\n// import { getToken } from \"@/utils/auth\";\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      flag: \"0\",\r\n      tableData: [],\r\n      info: {},\r\n      invoiceData: {},\r\n      orderForm: [\r\n        {\r\n          value: 1,\r\n          statusName: \"待支付\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 2,\r\n          statusName: \"待发货\",\r\n          desc: \"平台将于2023-08-04日前发货，感谢您的支持!如您对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 3,\r\n          statusName: \"支付失败\",\r\n          desc: \"订单支付失败，如您对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 4,\r\n          statusName: \"已发货\",\r\n          desc: \"使用过程中有任何问题，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 5,\r\n          statusName: \"已成交\",\r\n          desc: \"使用过程中有任何问题，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 6,\r\n          statusName: \"待续费\",\r\n          desc: \"请尽快续费，以免影响正常使用\",\r\n        },\r\n        {\r\n          value: 7,\r\n          statusName: \"已关闭\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 8,\r\n          statusName: \"支付中\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 9,\r\n          statusName: \"已取消\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n      ],\r\n      invoiceVisible: false,\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      let id = this.$route.query.id;\r\n      orderDetail(id).then((res) => {\r\n        this.loading = false;\r\n        this.tableData = [];\r\n        if (res.code === 200) {\r\n          this.info = res.data;\r\n          this.tableData.push(res.data);\r\n        }\r\n      });\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    tryout(item) {\r\n      console.log(item);\r\n      if (item.appName == \"云端研发\") {\r\n        let url;\r\n        let hostname;\r\n        var result;\r\n        hostname = \" https://yunduanyanfa.ningmengdou.com/login \";\r\n        result = encodeURIComponent(hostname);\r\n        url = \"https://sso.ningmengdou.com/single/login?returnUrl=\" + result;\r\n        window.open(url, \"_blank\");\r\n      } else if (item.appName == \"檬豆云供应链管理系统\") {\r\n      } else if (item.appName == \"集采平台\") {\r\n        window.open(\"https://mdy.ningmengdou.com\");\r\n      } else if (item.appName == \"云MES\") {\r\n        let userid = \"18660283726\";\r\n        console.log(userid);\r\n        let jsonData = { U: userid, P: \"12a\", A: \"acb\" };\r\n        console.log(jsonData);\r\n        const encodedData = btoa(JSON.stringify(jsonData));\r\n        console.log(encodedData);\r\n        window.open(\r\n          \"http://mes.ningmengdou.com/default.html?parm=\" + encodedData,\r\n          \"_blank\"\r\n        );\r\n      } else {\r\n        window.open(\"//\" + item.webexperienceUrl, \"_blank\");\r\n      }\r\n    },\r\n    cancelOrder(id) {\r\n      this.$confirm(\"订单取消后无法恢复，请谨慎操作!\", \"取消订单\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          cancelOrder(id).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.$router.go(-1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    applyInvoice() {\r\n      this.getInvoiceData();\r\n    },\r\n    getInvoiceData() {\r\n      invoiceList().then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceData = res.data;\r\n          this.invoiceVisible = true;\r\n        }\r\n      });\r\n    },\r\n    confirmReceipt(id) {\r\n      this.$confirm(\"确认后订单状态无法变更，确认收货吗？\", \"确认收货\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          let data = {\r\n            id,\r\n            orderStatus: 5,\r\n          };\r\n          modifyStatus(data).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    submitForm() {\r\n      let data = {\r\n        invoiceMedium: \"1\",\r\n        invoiceType: \"1\",\r\n        issueType: \"1\",\r\n        invoiceHeader: this.invoiceData.companyName,\r\n        dutyParagraph: this.invoiceData.dutyParagraph,\r\n        email: this.invoiceData.email,\r\n        orderId: this.info.id,\r\n        sendTo: this.invoiceData.userId,\r\n      };\r\n      applyInvoice(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceVisible = false;\r\n          this.$message.success(\"操作成功!\");\r\n        }\r\n      });\r\n    },\r\n    cancelDialog() {\r\n      this.invoiceVisible = false;\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  .content_top {\r\n    display: flex;\r\n    width: 100%;\r\n    height: 120px;\r\n    align-items: center;\r\n    .orderStatus {\r\n      width: 30%;\r\n      text-align: center;\r\n      .statusName {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n      }\r\n      .desc {\r\n        font-size: 14px;\r\n        color: rgb(173, 173, 173);\r\n        margin-top: 30px;\r\n      }\r\n    }\r\n    .amountMoney {\r\n      width: 40%;\r\n      text-align: center;\r\n    }\r\n    .button_content {\r\n      width: 20%;\r\n      text-align: center;\r\n      .buttonStyle {\r\n        height: 50px;\r\n        background: #21c9b8;\r\n        line-height: 50px;\r\n        color: #ffffff;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n  .content_bottom {\r\n    margin-top: 20px;\r\n    padding: 20px;\r\n    width: 100%;\r\n  }\r\n  .btnStyle {\r\n    text-align: center;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;AA8MA,IAAAA,KAAA,GAAAC,OAAA;AAOA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GACA;EACAC,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;QACAC,OAAA;MACA;MACAC,KAAA;MACAC,IAAA;MACAC,SAAA;MACAC,IAAA;MACAC,WAAA;MACAC,SAAA,GACA;QACAC,KAAA;QACAC,UAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,UAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,UAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,UAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,UAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,UAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,UAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,UAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,UAAA;QACAC,IAAA;MACA,EACA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAC,OAAA;MACA,IAAAC,EAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,EAAA;MACA,IAAAG,iBAAA,EAAAH,EAAA,EAAAI,IAAA,WAAAC,GAAA;QACAP,KAAA,CAAAC,OAAA;QACAD,KAAA,CAAAX,SAAA;QACA,IAAAkB,GAAA,CAAAC,IAAA;UACAR,KAAA,CAAAV,IAAA,GAAAiB,GAAA,CAAAvB,IAAA;UACAgB,KAAA,CAAAX,SAAA,CAAAoB,IAAA,CAAAF,GAAA,CAAAvB,IAAA;QACA;MACA;IACA;IACA0B,mBAAA,WAAAA,oBAAAxB,OAAA;MACA,KAAAD,WAAA,CAAAC,OAAA,GAAAA,OAAA;MACA,KAAAY,OAAA;IACA;IACAa,MAAA,WAAAA,OAAAC,IAAA;MACAC,OAAA,CAAAC,GAAA,CAAAF,IAAA;MACA,IAAAA,IAAA,CAAAG,OAAA;QACA,IAAAC,GAAA;QACA,IAAAC,QAAA;QACA,IAAAC,MAAA;QACAD,QAAA;QACAC,MAAA,GAAAC,kBAAA,CAAAF,QAAA;QACAD,GAAA,2DAAAE,MAAA;QACAE,MAAA,CAAAC,IAAA,CAAAL,GAAA;MACA,WAAAJ,IAAA,CAAAG,OAAA,mBACA,WAAAH,IAAA,CAAAG,OAAA;QACAK,MAAA,CAAAC,IAAA;MACA,WAAAT,IAAA,CAAAG,OAAA;QACA,IAAAO,MAAA;QACAT,OAAA,CAAAC,GAAA,CAAAQ,MAAA;QACA,IAAAC,QAAA;UAAAC,CAAA,EAAAF,MAAA;UAAAG,CAAA;UAAAC,CAAA;QAAA;QACAb,OAAA,CAAAC,GAAA,CAAAS,QAAA;QACA,IAAAI,WAAA,GAAAC,IAAA,CAAAC,IAAA,CAAAC,SAAA,CAAAP,QAAA;QACAV,OAAA,CAAAC,GAAA,CAAAa,WAAA;QACAP,MAAA,CAAAC,IAAA,CACA,kDAAAM,WAAA,EACA,QACA;MACA;QACAP,MAAA,CAAAC,IAAA,QAAAT,IAAA,CAAAmB,gBAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA9B,EAAA;MAAA,IAAA+B,MAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACA/B,IAAA;QACA,IAAA0B,iBAAA,EAAA9B,EAAA,EAAAI,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACAyB,MAAA,CAAAK,QAAA,CAAAC,OAAA;YACAN,MAAA,CAAAO,OAAA,CAAAC,EAAA;UACA;QACA;MACA,GACAC,KAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,cAAA;IACA;IACAA,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,iBAAA,IAAAxC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAqC,MAAA,CAAAtD,WAAA,GAAAgB,GAAA,CAAAvB,IAAA;UACA6D,MAAA,CAAAjD,cAAA;QACA;MACA;IACA;IACAmD,cAAA,WAAAA,eAAA7C,EAAA;MAAA,IAAA8C,MAAA;MACA,KAAAd,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GACA/B,IAAA;QACA,IAAAtB,IAAA;UACAkB,EAAA,EAAAA,EAAA;UACA+C,WAAA;QACA;QACA,IAAAC,kBAAA,EAAAlE,IAAA,EAAAsB,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACAwC,MAAA,CAAAV,QAAA,CAAAC,OAAA;YACAS,MAAA,CAAAlD,OAAA;UACA;QACA;MACA,GACA4C,KAAA;IACA;IACAS,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,IAAApE,IAAA;QACAqE,aAAA;QACAC,WAAA;QACAC,SAAA;QACAC,aAAA,OAAAjE,WAAA,CAAAkE,WAAA;QACAC,aAAA,OAAAnE,WAAA,CAAAmE,aAAA;QACAC,KAAA,OAAApE,WAAA,CAAAoE,KAAA;QACAC,OAAA,OAAAtE,IAAA,CAAAY,EAAA;QACA2D,MAAA,OAAAtE,WAAA,CAAAuE;MACA;MACA,IAAAnB,kBAAA,EAAA3D,IAAA,EAAAsB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA4C,MAAA,CAAAxD,cAAA;UACAwD,MAAA,CAAAd,QAAA,CAAAC,OAAA;QACA;MACA;IACA;IACAwB,YAAA,WAAAA,aAAA;MACA,KAAAnE,cAAA;IACA;IACAoE,MAAA,WAAAA,OAAA;MACA,KAAAxB,OAAA,CAAAC,EAAA;IACA;EACA;AACA", "ignoreList": []}]}