{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\newsCenter\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\newsCenter\\detail.vue", "mtime": 1750311962976}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBuZXdzRGV0YWlsIH0gZnJvbSAiQC9hcGkvbmV3c0NlbnRlciI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogInBvbGljeVBhZ2UiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBkYXRhOiB7fSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuaW5pdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaW5pdCgpIHsNCiAgICAgIGxldCBpZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkOw0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIG5ld3NEZXRhaWwoeyBpZDogaWQgfSkNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICBjb25zb2xlLmxvZyhyZXMsICItLS0tLS0tLS0tLS0tLS0tLSIpOw0KICAgICAgICAgICAgLy8gcmVzLmRhdGEuYXR0YWNobWVudCA9IEpTT04ucGFyc2UocmVzLmRhdGEuYXR0YWNobWVudCk7DQogICAgICAgICAgICB0aGlzLmRhdGEgPSByZXMuZGF0YTsNCiAgICAgICAgICAgIHRoaXMuZGF0YS5hdHRhY2htZW50ID0gSlNPTi5wYXJzZShyZXMuZGF0YS5hdHRhY2htZW50KTsNCiAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIH0NCiAgICAgICAgICAvLyB0aGlzLmRhdGEgPSByZXMuZGF0YSB8fCB7fTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICBkb3duTG9hZEZpbGUoZmlsZSkgew0KICAgICAgY29uc3QgZGF0YVNvdXJjZSA9IGZpbGU7IC8vIOi/memHjOmdouaYr+mcgOimgeaJuemHj+S4i+i9veeahOaWh+S7tnVybOWIl+ihqO+8jOagueaNruiHquW3seeahOmhueebruiOt+WPlg0KICAgICAgZGF0YVNvdXJjZS5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGxldCB1cmwgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTChuZXcgQmxvYihbaXRlbS51cmxdKSk7DQogICAgICAgIGxldCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpOw0KICAgICAgICBsaW5rLnN0eWxlLmRpc3BsYXkgPSAibm9uZSI7DQogICAgICAgIGxpbmsuaHJlZiA9IHVybDsNCiAgICAgICAgbGluay5zZXRBdHRyaWJ1dGUoImRvd25sb2FkIiwgaXRlbS5uYW1lKTsNCiAgICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTsNCiAgICAgICAgbGluay5jbGljaygpOw0KICAgICAgfSk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/newsCenter", "sourcesContent": ["<template>\r\n  <div class=\"notice-detail-container\">\r\n    <div class=\"notice-detail-banner\">\r\n      <img src=\"../../assets/notice/noticeDetailBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div class=\"notice-detail-title-box\">\r\n      <div class=\"notice-divider\"></div>\r\n      <div class=\"notice-detail-title\">新闻详情</div>\r\n      <div class=\"notice-divider\"></div>\r\n    </div>\r\n    <div class=\"notice-detail-content\">\r\n      <div class=\"notice-detail-box\">\r\n        <div class=\"notice-info-title\">\r\n          {{ data.title }}\r\n        </div>\r\n        <div class=\"notice-info-time\">\r\n          {{ data.updateTime }}\r\n          <span style=\"margin-left: 10px\">作者:{{ data.createBy }}</span>\r\n        </div>\r\n        <div\r\n          v-if=\"data.attachment && data.attachment.length > 0\"\r\n          style=\"margin: 10px 0; cursor: pointer\"\r\n          @click=\"downLoadFile(data.attachment)\"\r\n        >\r\n          <i class=\"el-icon-download\">下载附件</i>\r\n        </div>\r\n        <div class=\"notice-info-divider\"></div>\r\n        <div class=\"notice-info-box\">\r\n          <div\r\n            v-html=\"data.content\"\r\n            class=\"notice-info-content ql-editor\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { newsDetail } from \"@/api/newsCenter\";\r\n\r\nexport default {\r\n  name: \"policyPage\",\r\n  data() {\r\n    return {\r\n      data: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      let id = this.$route.query.id;\r\n      this.loading = true;\r\n      newsDetail({ id: id })\r\n        .then((res) => {\r\n          if (res.code === 200) {\r\n            console.log(res, \"-----------------\");\r\n            // res.data.attachment = JSON.parse(res.data.attachment);\r\n            this.data = res.data;\r\n            this.data.attachment = JSON.parse(res.data.attachment);\r\n            this.loading = false;\r\n          }\r\n          // this.data = res.data || {};\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    downLoadFile(file) {\r\n      const dataSource = file; // 这里面是需要批量下载的文件url列表，根据自己的项目获取\r\n      dataSource.forEach((item) => {\r\n        let url = window.URL.createObjectURL(new Blob([item.url]));\r\n        let link = document.createElement(\"a\");\r\n        link.style.display = \"none\";\r\n        link.href = url;\r\n        link.setAttribute(\"download\", item.name);\r\n        document.body.appendChild(link);\r\n        link.click();\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.notice-detail-container {\r\n  width: 100%;\r\n  padding: 0 0 100px;\r\n  background: #f4f5f9;\r\n  .notice-detail-banner {\r\n    width: 100%;\r\n    height: 26vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .notice-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .notice-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .notice-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .notice-detail-content {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n    .notice-detail-box {\r\n      padding: 60px 116px 100px;\r\n      font-family: PingFangSC-Semibold, PingFang SC;\r\n      .notice-info-title {\r\n        width: 960px;\r\n        font-size: 32px;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n        white-space: nowrap; /*让文字不换行*/\r\n        overflow: hidden; /*超出要隐藏*/\r\n        word-wrap: break-word;\r\n      }\r\n      .notice-info-time {\r\n        font-size: 14px;\r\n        color: #999;\r\n        line-height: 12px;\r\n        padding-top: 40px;\r\n      }\r\n      .notice-info-divider {\r\n        width: 100%;\r\n        height: 1px;\r\n        background: #e8e8e8;\r\n        margin-top: 10px;\r\n      }\r\n      .notice-info-box {\r\n        padding-top: 40px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.notice-detail-container {\r\n  .notice-info-content {\r\n    word-break: break-all;\r\n    font-size: 16px;\r\n    line-height: 28px;\r\n    color: #333;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    img {\r\n      max-width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}