<template>
  <div class="app-container">
    <el-form ref="requireForm" :model="form" label-width="80px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="详细描述" prop="detail">
            <el-radio-group v-model="form.detail">
              <el-radio :label='1'>是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预算明确" prop="budget">
            <el-radio-group v-model="form.budget">
              <el-radio :label='1'>是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="一手需求" prop="initial">
            <el-radio-group v-model="form.initial">
              <el-radio :label='1'>是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="持续" prop="continued">
            <el-radio-group v-model="form.continued">
              <el-radio :label='1'>是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="大规模" prop="scale">
            <el-radio-group v-model="form.scale">
              <el-radio :label='1'>是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitRequire">确 定</el-button>
    </div>
  </div>
</template>
<style>
</style>

<script>
  // import {
  //   gradeRequire
  // } from "@/api/sso/service";
  export default {
    props: {
      id: String
    },
    name: "GradeContect",
    data() {
      return {
        form:{}
      };
    },
    created() {
      this.form.id = this.id
    },
    methods: {
      /* 需求评级-提交 */
      submitRequire() {
        // gradeRequire(this.form).then(res => {
        //   this.$modal.msgSuccess("提交成功");
        // })
      },
    }
  };
</script>
