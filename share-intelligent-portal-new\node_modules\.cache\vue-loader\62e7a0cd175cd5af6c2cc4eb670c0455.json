{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\thinkTank\\index.vue?vue&type=style&index=0&id=90b5c156&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\thinkTank\\index.vue", "mtime": 1750311963092}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYWN0aXZpdHktY29udGFpbmVyIHsNCiAgd2lkdGg6IDEwMCU7DQogIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIC5hY3Rpdml0eS1iYW5uZXIgew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGhlaWdodDogNTAwcHg7DQogICAgcG9zaXRpb246IHJlbGF0aXZlOw0KICAgIGltZyB7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIGhlaWdodDogMTAwJTsNCiAgICB9DQogICAgLmJhbm5lclRpdGxlIHsNCiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgIHRvcDogMTYxcHg7DQogICAgICBsZWZ0OiAyNCU7DQogICAgICBmb250LXNpemU6IDUwcHg7DQogICAgICBmb250LWZhbWlseTogU291cmNlIEhhbiBTYW5zIENOOw0KICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICBjb2xvcjogI2ZmZmZmZjsNCiAgICB9DQogICAgLmJhbm5lckRlc2Mgew0KICAgICAgcG9zaXRpb246IGFic29sdXRlOw0KICAgICAgdG9wOiAyNDlweDsNCiAgICAgIGxlZnQ6IDI0JTsNCiAgICAgIGZvbnQtc2l6ZTogMjRweDsNCiAgICAgIGZvbnQtZmFtaWx5OiBTb3VyY2UgSGFuIFNhbnMgQ047DQogICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgY29sb3I6ICNmZmZmZmY7DQogICAgfQ0KICB9DQogIC5hY3Rpdml0eS10aXRsZS1jb250ZW50IHsNCiAgICB3aWR0aDogMTE5MnB4Ow0KICAgIG1hcmdpbjogNDBweCBhdXRvIDA7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZjsNCiAgICAvLyBwYWRkaW5nLWJvdHRvbTogMThweDsNCiAgICAuYWN0aXZpdHktdGl0bGUtYm94IHsNCiAgICAgIHdpZHRoOiAzMzZweDsNCiAgICAgIG1hcmdpbjogMCBhdXRvOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBwYWRkaW5nOiA2MHB4IDAgNDBweDsNCiAgICAgIC5hY3Rpdml0eS10aXRsZSB7DQogICAgICAgIGZvbnQtc2l6ZTogNDBweDsNCiAgICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtTWVkaXVtLCBQaW5nRmFuZyBTQzsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgY29sb3I6ICMzMzM7DQogICAgICAgIGxpbmUtaGVpZ2h0OiA0MHB4Ow0KICAgICAgICBwYWRkaW5nOiAwIDQwcHg7DQogICAgICB9DQogICAgICAuYWN0aXZpdHktZGl2aWRlciB7DQogICAgICAgIHdpZHRoOiA0OHB4Ow0KICAgICAgICBoZWlnaHQ6IDRweDsNCiAgICAgICAgYmFja2dyb3VuZDogIzAwYjA4NTsNCiAgICAgIH0NCiAgICB9DQogICAgLmFjdGl2aXR5LXNlYXJjaC1ib3ggew0KICAgICAgbWFyZ2luLXRvcDogNDBweDsNCiAgICAgIC5hY3Rpdml0eS1zZWFyY2gtZm9ybSB7DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgLmFjdGl2aXR5LXNlYXJjaC1pbnB1dCB7DQogICAgICAgICAgd2lkdGg6IDc5MnB4Ow0KICAgICAgICAgIGhlaWdodDogNTRweDsNCiAgICAgICAgICAuYWN0aXZpdHktc2VhcmNoLWJ0biB7DQogICAgICAgICAgICB3aWR0aDogMTAwcHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICAgIC5jbGFzc2ljQ2FzZVR5cGUgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgIG1hcmdpbi10b3A6IDUwcHg7DQogICAgICAvLyBwb3NpdGlvbjogYWJzb2x1dGU7DQogICAgICAvLyBib3R0b206IC00NXB4Ow0KICAgICAgLy8gbGVmdDogY2FsYygoMTAwJSAtIDExMDBweCkgLyAyKTsNCiAgICAgIC8vIHdpZHRoOiAxMTkycHg7DQogICAgICAvLyBtYXJnaW46IDQwcHggYXV0bw0KICAgICAgLy8gaGVpZ2h0OiA5MHB4Ow0KICAgICAgLy8gYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgICAgIC8vIGJveC1zaGFkb3c6IDBweCA0cHggMjBweCAwcHggcmdiYSgwLCAwLCAwLCAwLjA0KTsNCiAgICAgIC8vIGJvcmRlci1yYWRpdXM6IDQ1cHg7DQogICAgICAvLyBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgIC8vIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAudHlwZVRpdGxlIHsNCiAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICBmb250LWZhbWlseTogU291cmNlIEhhbiBTYW5zIENOOw0KICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICBjb2xvcjogIzMzMzMzMzsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KICAgICAgfQ0KICAgICAgLmNhc2VOYW1lIHsNCiAgICAgICAgd2lkdGg6IDEwMHB4Ow0KICAgICAgICBoZWlnaHQ6IDQwcHg7DQogICAgICAgIGJvcmRlci1yYWRpdXM6IDIwcHg7DQogICAgICAgIG1hcmdpbi1sZWZ0OiAxNXB4Ow0KICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgIGxpbmUtaGVpZ2h0OiA0MHB4Ow0KICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgZm9udC1mYW1pbHk6IFNvdXJjZSBIYW4gU2FucyBDTjsNCiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgY29sb3I6ICM5Nzk3OTc7DQogICAgICB9DQogICAgICAuY2FzZU5hbWVIb3ZlciB7DQogICAgICAgIGJhY2tncm91bmQ6ICMwMGIwODU7DQogICAgICAgIGNvbG9yOiAjZmZmZmZmOw0KICAgICAgfQ0KICAgICAgLmNhc2VOYW1lOm50aC1jaGlsZCgxKSB7DQogICAgICAgIG1hcmdpbi1sZWZ0OiAwOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICAuYWN0aXZpdHktcGFnZS1lbmQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBtYXJnaW46IDAgYXV0bzsNCiAgICBwYWRkaW5nOiAyNHB4IDAgNjBweDsNCiAgICAuYWN0aXZpdHktcGFnZS1idG4gew0KICAgICAgd2lkdGg6IDgycHg7DQogICAgICBoZWlnaHQ6IDMycHg7DQogICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLVJlZ3VsYXIsIFBpbmdGYW5nIFNDOw0KICAgICAgY29sb3I6ICMzMzM7DQogICAgICBsaW5lLWhlaWdodDogMTBweDsNCiAgICB9DQogIH0NCiAgLm5vbmUtY2xhc3Mgew0KICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICBwYWRkaW5nOiA4JSAwOw0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgbWFyZ2luLXRvcDogMjVweDsNCiAgICAudGV4dCB7DQogICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgY29sb3I6ICM5OTk5OTk7DQogICAgICBsaW5lLWhlaWdodDogMTRweDsNCiAgICB9DQogIH0NCn0NCi5leHBlcnQtbGlicmFyeS1saXN0IHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBmbGV4LXdyYXA6IHdyYXA7DQogIHdpZHRoOiAxMDAlOw0KICAubGlzdC1pdGVtLWNvbnRlbnQgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIHdpZHRoOiA1NzhweDsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgIGJveC1zaGFkb3c6IDBweCA0cHggMjBweCAwcHggcmdiYSgwLCAwLCAwLCAwLjA2KTsNCiAgICBtYXJnaW4tdG9wOiAzNnB4Ow0KICAgIHBhZGRpbmc6IDI4cHggMzJweDsNCiAgICBtaW4taGVpZ2h0OiAyNDBweDsNCiAgICAubGlzdC1pdGVtLWJveCB7DQogICAgICBmbGV4OiAxOw0KICAgICAgLml0ZW0taGVhZGxpbmUgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIC5pdGVtLXRpdGxlIHsNCiAgICAgICAgICB3aWR0aDogMjgwcHg7DQogICAgICAgICAgZm9udC1zaXplOiAzMnB4Ow0KICAgICAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLU1lZGl1bSwgUGluZ0ZhbmcgU0M7DQogICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICBjb2xvcjogIzMzMzsNCiAgICAgICAgICBsaW5lLWhlaWdodDogMzJweDsNCiAgICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwOw0KICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgICAgd29yZC13cmFwOiBicmVhay13b3JkOw0KICAgICAgICB9DQogICAgICB9DQogICAgICAuZXhwZXJ0LWxpYnJhcnktbGFiZWwgew0KICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICBmbGV4LXdyYXA6IHdyYXA7DQogICAgICAgIG1hcmdpbjogMCAwIDE2cHg7DQogICAgICAgIC5saWJyYXJ5LWxhYmVsLWl0ZW0gew0KICAgICAgICAgIG1heC13aWR0aDogMzUwcHg7DQogICAgICAgICAgcGFkZGluZzogNnB4IDEycHg7DQogICAgICAgICAgYmFja2dyb3VuZDogI2Y0ZjVmOTsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLVJlZ3VsYXIsIFBpbmdGYW5nIFNDOw0KICAgICAgICAgIGNvbG9yOiAjNjY2Ow0KICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxMnB4Ow0KICAgICAgICAgIG1hcmdpbjogMjRweCAxNnB4IDAgMDsNCiAgICAgICAgICAuZXhwZXJ0LWxpYnJhcnktdHlwZSB7DQogICAgICAgICAgICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICAuZXhwZXJ0LWxpYnJhcnktYm94IHsNCiAgICAgICAgd2lkdGg6IDM3MHB4Ow0KICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLVJlZ3VsYXIsIFBpbmdGYW5nIFNDOw0KICAgICAgICBjb2xvcjogIzY2NjsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDMycHg7DQogICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgIGRpc3BsYXk6IC13ZWJraXQtYm94Ow0KICAgICAgICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsOw0KICAgICAgICAtd2Via2l0LWxpbmUtY2xhbXA6IDI7DQogICAgICAgIHRleHQtb3ZlcmZsb3c6IGVsbGlwc2lzOw0KICAgICAgICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQ7DQogICAgICB9DQogICAgfQ0KICAgIC5saXN0LWl0ZW0taW1nIHsNCiAgICAgIHdpZHRoOiAxMjBweDsNCiAgICAgIGhlaWdodDogMTY4cHg7DQogICAgICBtYXJnaW4tbGVmdDogMjRweDsNCiAgICAgIGltZyB7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICB9DQogICAgfQ0KICAgICY6aG92ZXIgew0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIH0NCiAgfQ0KICAubGlzdC1pdGVtLWNvbnRlbnQ6aG92ZXIgew0KICAgIGJveC1zaGFkb3c6IDBweCA0cHggMjBweCAwcHggcmdiYSgwLCAwLCAwLCAwLjIpOw0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/thinkTank", "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/thinkTank/thinkTankBanner.png\" alt=\"\" />\r\n      <div class=\"bannerTitle\">专家智库</div>\r\n      <div class=\"bannerDesc\">\r\n        汇聚行业专家，提供咨询与技术服务，推进其碳中和目标实现\r\n      </div>\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.keywords\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n        <div class=\"classicCaseType\">\r\n          <div class=\"typeTitle\">技术类别</div>\r\n          <div\r\n            v-for=\"(item, index) in technologyTypeList\"\r\n            :key=\"index\"\r\n            class=\"caseName\"\r\n            :class=\"activeName == item.dictLabel ? 'caseNameHover' : ''\"\r\n            @click=\"getType(item.dictLabel)\"\r\n          >\r\n            {{ item.dictLabel }}\r\n          </div>\r\n        </div>\r\n        <div class=\"expert-library-list\">\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"list-item-content\"\r\n            @click=\"goExpertLibrary(item.id)\"\r\n          >\r\n            <div class=\"list-item-box\">\r\n              <div class=\"item-headline\">\r\n                <div class=\"item-title\">\r\n                  {{ item.expertName }}\r\n                </div>\r\n              </div>\r\n              <div class=\"expert-library-label\">\r\n                <div\r\n                  v-for=\"(val, index1) in item.techniqueTypeName\"\r\n                  :key=\"index1\"\r\n                  class=\"library-label-item\"\r\n                >\r\n                  <span v-if=\"index1 < 2\" class=\"expert-library-type\">{{\r\n                    `#${val}`\r\n                  }}</span>\r\n                  <span v-else>…</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"expert-library-box\">\r\n                {{ item.synopsis }}\r\n              </div>\r\n            </div>\r\n            <div class=\"list-item-img\">\r\n              <img v-if=\"item.headPortrait\" :src=\"item.headPortrait\" alt=\"\" />\r\n              <img\r\n                v-else\r\n                src=\"../../assets/expertLibrary/defaultImg.png\"\r\n                alt=\"\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"activity-page-end\">\r\n          <el-button class=\"activity-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"data && data.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import { getActivityList } from \"@/api/purchaseSales\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { getExpertList } from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      loading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        caseType: \"\", // 案例类型\r\n      },\r\n      caseTypeList: [],\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      solutionTypeList: [\r\n        {\r\n          value: \"0\",\r\n          label: \"全部\",\r\n        },\r\n        {\r\n          value: \"1\",\r\n          label: \"节能减排\",\r\n        },\r\n        {\r\n          value: \"2\",\r\n          label: \"低碳认证\",\r\n        },\r\n        {\r\n          value: \"3\",\r\n          label: \"数据核算\",\r\n        },\r\n        {\r\n          value: \"4\",\r\n          label: \"中和服务\",\r\n        },\r\n        {\r\n          value: \"5\",\r\n          label: \"星碳培训\",\r\n        },\r\n        {\r\n          value: \"6\",\r\n          label: \"绿色会议\",\r\n        },\r\n        {\r\n          value: \"7\",\r\n          label: \"数据建模\",\r\n        },\r\n        {\r\n          value: \"8\",\r\n          label: \"资产管理\",\r\n        },\r\n      ],\r\n      flag: \"0\",\r\n      technologyTypeList: [\r\n        {\r\n          dictLabel: \"全部\",\r\n          dictValue: 0,\r\n        },\r\n        {\r\n          dictLabel: \"国产化替代\",\r\n          dictValue: 1,\r\n        },\r\n        {\r\n          dictLabel: \"机器替人\",\r\n          dictValue: 2,\r\n        },\r\n        {\r\n          dictLabel: \"管理提升\",\r\n          dictValue: 3,\r\n        },\r\n        {\r\n          dictLabel: \"质量提升\",\r\n          dictValue: 4,\r\n        },\r\n        {\r\n          dictLabel: \"灭菌消杀\",\r\n          dictValue: 5,\r\n        },\r\n        {\r\n          dictLabel: \"新材料\",\r\n          dictValue: 6,\r\n        },\r\n        {\r\n          dictLabel: \"绿色星碳\",\r\n          dictValue: 7,\r\n        },\r\n      ],\r\n      activeName: \"全部\",\r\n    };\r\n  },\r\n  created() {\r\n    this.searchExpert();\r\n    // this.getDictsList(\"activity_type\", \"activityTypeList\");\r\n    // this.search();\r\n  },\r\n  methods: {\r\n    searchExpert() {\r\n      this.loading = true;\r\n      getExpertList({\r\n        keywords: this.form.name,\r\n        techniqueTypeName: this.activeName == \"全部\" ? \"\" : this.activeName,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.data.forEach((item) => {\r\n            item.techniqueTypeName = item.techniqueTypeName\r\n              ? item.techniqueTypeName.split(\",\")\r\n              : [];\r\n          });\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.searchExpert();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.searchExpert();\r\n    },\r\n    goCaseDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/caseDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    getItemData(value) {\r\n      this.flag = value;\r\n    },\r\n    getType(value) {\r\n      this.activeName = value;\r\n      this.searchExpert();\r\n    },\r\n    // 跳转到专家详情页面\r\n    goExpertLibrary(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/expertDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #ffffff;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 500px;\r\n    position: relative;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    .bannerTitle {\r\n      position: absolute;\r\n      top: 161px;\r\n      left: 24%;\r\n      font-size: 50px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n    }\r\n    .bannerDesc {\r\n      position: absolute;\r\n      top: 249px;\r\n      left: 24%;\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 1192px;\r\n    margin: 40px auto 0;\r\n    background-color: #fff;\r\n    // padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #00b085;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      margin-top: 40px;\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .classicCaseType {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin-top: 50px;\r\n      // position: absolute;\r\n      // bottom: -45px;\r\n      // left: calc((100% - 1100px) / 2);\r\n      // width: 1192px;\r\n      // margin: 40px auto\r\n      // height: 90px;\r\n      // background: #ffffff;\r\n      // box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.04);\r\n      // border-radius: 45px;\r\n      // justify-content: center;\r\n      // align-items: center;\r\n      .typeTitle {\r\n        font-size: 18px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        margin-right: 20px;\r\n      }\r\n      .caseName {\r\n        width: 100px;\r\n        height: 40px;\r\n        border-radius: 20px;\r\n        margin-left: 15px;\r\n        text-align: center;\r\n        line-height: 40px;\r\n        cursor: pointer;\r\n        font-size: 18px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #979797;\r\n      }\r\n      .caseNameHover {\r\n        background: #00b085;\r\n        color: #ffffff;\r\n      }\r\n      .caseName:nth-child(1) {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    padding: 24px 0 60px;\r\n    .activity-page-btn {\r\n      width: 82px;\r\n      height: 32px;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      border: 1px solid #d9d9d9;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #333;\r\n      line-height: 10px;\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    padding: 8% 0;\r\n    background: #fff;\r\n    margin-top: 25px;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n}\r\n.expert-library-list {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  width: 100%;\r\n  .list-item-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    width: 578px;\r\n    background: #fff;\r\n    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n    margin-top: 36px;\r\n    padding: 28px 32px;\r\n    min-height: 240px;\r\n    .list-item-box {\r\n      flex: 1;\r\n      .item-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        .item-title {\r\n          width: 280px;\r\n          font-size: 32px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 32px;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          word-wrap: break-word;\r\n        }\r\n      }\r\n      .expert-library-label {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        margin: 0 0 16px;\r\n        .library-label-item {\r\n          max-width: 350px;\r\n          padding: 6px 12px;\r\n          background: #f4f5f9;\r\n          border-radius: 4px;\r\n          font-size: 12px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #666;\r\n          line-height: 12px;\r\n          margin: 24px 16px 0 0;\r\n          .expert-library-type {\r\n            word-wrap: break-word;\r\n          }\r\n        }\r\n      }\r\n      .expert-library-box {\r\n        width: 370px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #666;\r\n        line-height: 32px;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n    }\r\n    .list-item-img {\r\n      width: 120px;\r\n      height: 168px;\r\n      margin-left: 24px;\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n    &:hover {\r\n      cursor: pointer;\r\n    }\r\n  }\r\n  .list-item-content:hover {\r\n    box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.2);\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #00b085;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #00b085;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #00b085;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #00b085;\r\n              border: 1px solid #00b085;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}