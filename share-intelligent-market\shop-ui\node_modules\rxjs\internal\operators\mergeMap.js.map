{"version": 3, "file": "mergeMap.js", "sources": ["../../src/internal/operators/mergeMap.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAKA,6BAA4B;AAC5B,2CAA0C;AAC1C,oDAAiG;AAgEjG,SAAgB,QAAQ,CACtB,OAAuC,EACvC,cAAwH,EACxH,UAA6C;IAA7C,2BAAA,EAAA,aAAqB,MAAM,CAAC,iBAAiB;IAE7C,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;QAExC,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAC3C,QAAQ,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,WAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CACzC,SAAG,CAAC,UAAC,CAAM,EAAE,EAAU,IAAK,OAAA,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAA3B,CAA2B,CAAC,CACzD,EAFkB,CAElB,EAAE,UAAU,CAAC,CACf,EAJiC,CAIjC,CAAC;KACH;SAAM,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;QAC7C,UAAU,GAAG,cAAc,CAAC;KAC7B;IACD,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,EAAtD,CAAsD,CAAC;AAC3F,CAAC;AAhBD,4BAgBC;AAED;IACE,0BAAoB,OAAwD,EACxD,UAA6C;QAA7C,2BAAA,EAAA,aAAqB,MAAM,CAAC,iBAAiB;QAD7C,YAAO,GAAP,OAAO,CAAiD;QACxD,eAAU,GAAV,UAAU,CAAmC;IACjE,CAAC;IAED,+BAAI,GAAJ,UAAK,QAAuB,EAAE,MAAW;QACvC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,kBAAkB,CAC5C,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CACxC,CAAC,CAAC;IACL,CAAC;IACH,uBAAC;AAAD,CAAC,AAVD,IAUC;AAVY,4CAAgB;AAiB7B;IAA8C,sCAA2B;IAMvE,4BAAY,WAA0B,EAClB,OAAwD,EACxD,UAA6C;QAA7C,2BAAA,EAAA,aAAqB,MAAM,CAAC,iBAAiB;QAFjE,YAGE,kBAAM,WAAW,CAAC,SACnB;QAHmB,aAAO,GAAP,OAAO,CAAiD;QACxD,gBAAU,GAAV,UAAU,CAAmC;QAPzD,kBAAY,GAAY,KAAK,CAAC;QAC9B,YAAM,GAAQ,EAAE,CAAC;QACjB,YAAM,GAAW,CAAC,CAAC;QACjB,WAAK,GAAW,CAAC,CAAC;;IAM5B,CAAC;IAES,kCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;YACjC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACzB;IACH,CAAC;IAES,qCAAQ,GAAlB,UAAmB,KAAQ;QACzB,IAAI,MAA0B,CAAC;QAC/B,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI;YACF,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACrC;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC,KAAM,CAAC,GAAG,CAAC,CAAC;YAC7B,OAAO;SACR;QACD,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACzB,CAAC;IAEO,sCAAS,GAAjB,UAAkB,GAAuB;QACvC,IAAM,eAAe,GAAG,IAAI,sCAAqB,CAAC,IAAI,CAAC,CAAC;QACxD,IAAM,WAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;QACrD,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACjC,IAAM,iBAAiB,GAAG,+BAAc,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QAI/D,IAAI,iBAAiB,KAAK,eAAe,EAAE;YACzC,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;SACpC;IACH,CAAC;IAES,sCAAS,GAAnB;QACE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACjD,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,uCAAU,GAAV,UAAW,UAAa;QACtB,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,UAAU,CAAC,CAAC;IACrC,CAAC;IAED,2CAAc,GAAd;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAG,CAAC,CAAC;SAC7B;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE;YACjD,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;IACH,CAAC;IACH,yBAAC;AAAD,CAAC,AAnED,CAA8C,sCAAqB,GAmElE;AAnEY,gDAAkB;AAwElB,QAAA,OAAO,GAAG,QAAQ,CAAC"}