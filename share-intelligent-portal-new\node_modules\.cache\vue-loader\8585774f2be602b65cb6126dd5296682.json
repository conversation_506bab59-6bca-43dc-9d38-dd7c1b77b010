{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\workshopManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\workshopManagement\\index.vue", "mtime": 1750311963092}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/workshopManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"top\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">车间管理</div>\r\n          </div>\r\n          <el-button class=\"btn\" type=\"primary\" plain @click=\"handleAdd\">发布车间信息</el-button>\r\n        </div>\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\"\r\n          style=\"margin-top: 20px\">\r\n          <el-form-item label=\"车间名称\" prop=\"name\">\r\n            <el-input v-model=\"queryParams.name\" placeholder=\"请输入车间名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属单位\" prop=\"company\">\r\n            <el-input v-model=\"queryParams.company\" placeholder=\"请输入所属单位\" clearable @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"车间面积\" prop=\"area\">\r\n            <el-input v-model=\"queryParams.area\" placeholder=\"请输入车间面积\" clearable @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"参考价格\" prop=\"price\">\r\n            <el-input v-model=\"queryParams.price\" placeholder=\"请输入参考价格\" clearable @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div class=\"table\">\r\n          <div style=\"width: 100%\">\r\n            <el-table :data=\"tableData\" style=\"width: 100%\" v-loading=\"loading\">\r\n              <el-table-column label=\"车间ID\" align=\"center\" prop=\"id\" />\r\n              <el-table-column label=\"车间名称\" align=\"center\" prop=\"name\" />\r\n              <el-table-column label=\"车间类型\" align=\"center\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ getWorkShopType(scope.row.type) }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"所属单位\" align=\"center\" prop=\"company\" />\r\n              <el-table-column label=\"车间地址\" align=\"center\" prop=\"address\" />\r\n              <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\">修改</el-button>\r\n                  <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\"\r\n                    @click=\"handleDelete(scope.row)\">删除</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n          <!-- 分页 -->\r\n          <div class=\"pageStyle\">\r\n            <el-pagination v-if=\"tableData && tableData.length > 0\" background layout=\"prev, pager, next\"\r\n              class=\"activity-pagination\" :page-size=\"queryParams.pageSize\" :current-page=\"queryParams.pageNum\"\r\n              :total=\"total\" @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { workUserListData, delWorkInfo } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      total: 0,\r\n      tableData: [],\r\n      workShopTypeList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        name: null,\r\n        company: null,\r\n        area: null,\r\n        price: null,\r\n        checkStatus: null,\r\n        createBy: null,\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getDicts();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 查询字典数据列表 */\r\n    getDicts() {\r\n      let params = { dictType: \"workshop_type\" };\r\n      listData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.workShopTypeList = res.rows;\r\n        }\r\n      });\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      let userinfo = JSON.parse(window.sessionStorage.getItem(\"userinfo\"));\r\n      this.queryParams.createBy = userinfo.memberPhone;\r\n      workUserListData(this.queryParams).then((res) => {\r\n        if (res.code == 200) {\r\n          this.tableData = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    handleAdd() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(\"/publishWorkshop\");\r\n      }\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.queryParams.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.$router.push(\"/publishWorkshop?id=\" + row.id);\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除车间信息编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delWorkInfo(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => { });\r\n    },\r\n    getWorkShopType(type) {\r\n      const wrokShopType = this.workShopTypeList.find(\r\n        (item) => item.dictValue == type\r\n      );\r\n      return wrokShopType ? wrokShopType.dictLabel : \"\";\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.top {\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  // margin-top: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.table {\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  padding: 20px;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  justify-content: space-around;\r\n}\r\n\r\n.pageStyle {\r\n  width: 100%;\r\n  margin-top: 61px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n</style>\r\n"]}]}