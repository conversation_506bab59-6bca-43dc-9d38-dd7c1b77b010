<template>
  <div class="">
    <el-row>
      <el-table v-loading="loading" :data="data">
        <el-table-column label="日期" align="center" prop="date" />
        <el-table-column label="内容" align="center" prop="remark" />
        <el-table-column label="运营" align="center" prop="principal" />
      </el-table>
      <pagination :total="total" :page.sync="page" :limit.sync="limit" @pagination="getFollow" />
    </el-row>
    <el-form style='margin-top: 10px;' ref="followForm" :model="followForm" :rules='rules' label-width="80px">
      <el-row>
        <el-form-item label='日期' prop='date'>
          <el-date-picker v-model="followForm.date" value-format='yyyy-MM-dd' type="date" placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label='内容' prop='remark'>
          <el-input type='textarea' v-model='followForm.remark'></el-input>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label='负责人' prop='principal'>
          <el-input disabled v-model='followForm.principal'></el-input>
        </el-form-item>
      </el-row>
      <!-- <el-row>
        <el-form-item label='是否同步' prop='sync'>
          <el-form-item label="是否同步">
            <el-switch v-model='followForm.sync'></el-switch>
          </el-form-item>
        </el-form-item>
      </el-row> -->
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: center;">
      <el-button type="primary" @click="submitFollow">确 定</el-button>
    </div>
  </div>
</template>

<script>
  // import {
  //   listRequirefollow,
  //   addRequirefollow
  // } from "@/api/sso/service";
  // import {
  //   mapGetters
  // } from "vuex";
  // import Pagination from "@/components/Pagination";
  export default {
    components: {
      // Pagination
    },
    name: "DemandFollow",
    props: {
      form: Object
    },
    data() {
      return {
        loading: false,
        total: 0,
        page: 1,
        limit: 10,
        data: [],
        followForm: {
          principal:this.$store.state.user.name
        },
        rules: {
          date: [{
            required: true,
            message: "请选择日期",
            trigger: "change"
          }],
          remark: [{
            required: true,
            message: "内容不能为空",
            trigger: "blur"
          }],
          principal: [{
            required: true,
            message: "负责人不能为空",
            trigger: "blur"
          }]
        }
      }
    },
    created() {
      this.followForm.requireId = this.form.id;
      this.getFollow()
    },
    methods: {
      // 表单重置
      reset() {
        this.followForm = {
          principal:this.$store.state.user.name,
          remark:null,
          date:null
        };
        this.resetForm("followForm");
      },
      /* 获取需求跟进*/
      getFollow() {
        // let data = {
        //   requireId: this.form.id,
        //   page: this.page,
        //   limit: this.limit
        // }
        // listRequirefollow(data).then(res => {
        //   this.data = res.data.rows;
        //   this.total = res.data.total;
        //   this.loading = false;
        // })
      },
      submitFollow() {
        // this.$refs["followForm"].validate(valid => {
        //   if (valid) {
        //     addRequirefollow(this.followForm).then(res => {
        //       this.$modal.msgSuccess("提交成功");
        //       this.getFollow()
        //     })
        //   }
        // })
      },
    }
  };
</script>
