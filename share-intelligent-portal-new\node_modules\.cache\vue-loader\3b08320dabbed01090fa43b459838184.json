{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\register.vue?vue&type=style&index=0&id=77453986&rel=stylesheet%2Fscss&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\register.vue", "mtime": 1750311962994}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnJlZ2lzdGVyIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGhlaWdodDogMTAwJTsNCiAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCIuLi9hc3NldHMvaW1hZ2VzL2xvZ2luLWJhY2tncm91bmQuanBnIik7DQogIGJhY2tncm91bmQtc2l6ZTogY292ZXI7DQp9DQoudGl0bGUgew0KICBtYXJnaW46IDBweCBhdXRvIDMwcHggYXV0bzsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjb2xvcjogIzcwNzA3MDsNCn0NCg0KLnJlZ2lzdGVyLWZvcm0gew0KICBib3JkZXItcmFkaXVzOiA2cHg7DQogIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIHdpZHRoOiA0MDBweDsNCiAgcGFkZGluZzogMjVweCAyNXB4IDVweCAyNXB4Ow0KICAuZWwtaW5wdXQgew0KICAgIGhlaWdodDogMzhweDsNCiAgICBpbnB1dCB7DQogICAgICBoZWlnaHQ6IDM4cHg7DQogICAgfQ0KICB9DQogIC5pbnB1dC1pY29uIHsNCiAgICBoZWlnaHQ6IDM5cHg7DQogICAgd2lkdGg6IDE0cHg7DQogICAgbWFyZ2luLWxlZnQ6IDJweDsNCiAgfQ0KfQ0KLnJlZ2lzdGVyLXRpcCB7DQogIGZvbnQtc2l6ZTogMTNweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjb2xvcjogI2JmYmZiZjsNCn0NCi5yZWdpc3Rlci1jb2RlIHsNCiAgd2lkdGg6IDMzJTsNCiAgaGVpZ2h0OiAzOHB4Ow0KICBmbG9hdDogcmlnaHQ7DQogIGltZyB7DQogICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7DQogIH0NCn0NCi5lbC1yZWdpc3Rlci1mb290ZXIgew0KICBoZWlnaHQ6IDQwcHg7DQogIGxpbmUtaGVpZ2h0OiA0MHB4Ow0KICBwb3NpdGlvbjogZml4ZWQ7DQogIGJvdHRvbTogMDsNCiAgd2lkdGg6IDEwMCU7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgY29sb3I6ICNmZmY7DQogIGZvbnQtZmFtaWx5OiBBcmlhbDsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICBsZXR0ZXItc3BhY2luZzogMXB4Ow0KfQ0KLnJlZ2lzdGVyLWNvZGUtaW1nIHsNCiAgaGVpZ2h0OiAzOHB4Ow0KfQ0K"}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"register\">\r\n    <el-form\r\n      ref=\"registerForm\"\r\n      :model=\"registerForm\"\r\n      :rules=\"registerRules\"\r\n      class=\"register-form\"\r\n    >\r\n      <h3 class=\"title\">若依后台管理系统</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"registerForm.username\"\r\n          type=\"text\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"user\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"registerForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"password\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"confirmPassword\">\r\n        <el-input\r\n          v-model=\"registerForm.confirmPassword\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"确认密码\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"password\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\r\n        <el-input\r\n          v-model=\"registerForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"validCode\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n        <div class=\"register-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\" />\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item style=\"width: 100%\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width: 100%\"\r\n          @click.native.prevent=\"handleRegister\"\r\n        >\r\n          <span v-if=\"!loading\">注 册</span>\r\n          <span v-else>注 册 中...</span>\r\n        </el-button>\r\n        <div style=\"float: right\">\r\n          <router-link class=\"link-type\" :to=\"'/login'\"\r\n            >使用已有账户登录</router-link\r\n          >\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-register-footer\">\r\n      <span>Copyright © 2018-2022 ruoyi.vip All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg, register } from \"@/api/login\";\r\n\r\nexport default {\r\n  name: \"Register\",\r\n  data() {\r\n    const equalToPassword = (rule, value, callback) => {\r\n      if (this.registerForm.password !== value) {\r\n        callback(new Error(\"两次输入的密码不一致\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      codeUrl: \"\",\r\n      registerForm: {\r\n        username: \"\",\r\n        password: \"\",\r\n        confirmPassword: \"\",\r\n        code: \"\",\r\n        uuid: \"\",\r\n      },\r\n      registerRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\r\n          {\r\n            min: 2,\r\n            max: 20,\r\n            message: \"用户账号长度必须介于 2 和 20 之间\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\r\n          {\r\n            min: 5,\r\n            max: 20,\r\n            message: \"用户密码长度必须介于 5 和 20 之间\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        confirmPassword: [\r\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\r\n          { required: true, validator: equalToPassword, trigger: \"blur\" },\r\n        ],\r\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }],\r\n      },\r\n      loading: false,\r\n      captchaEnabled: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.getCode();\r\n  },\r\n  methods: {\r\n    getCode() {\r\n      getCodeImg().then((res) => {\r\n        this.captchaEnabled =\r\n          res.captchaEnabled === undefined ? true : res.captchaEnabled;\r\n        if (this.captchaEnabled) {\r\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n          this.registerForm.uuid = res.uuid;\r\n        }\r\n      });\r\n    },\r\n    handleRegister() {\r\n      this.$refs.registerForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          register(this.registerForm)\r\n            .then((res) => {\r\n              const username = this.registerForm.username;\r\n              this.$alert(\r\n                \"<font color='red'>恭喜你，您的账号 \" +\r\n                  username +\r\n                  \" 注册成功！</font>\",\r\n                \"系统提示\",\r\n                {\r\n                  dangerouslyUseHTMLString: true,\r\n                  type: \"success\",\r\n                }\r\n              )\r\n                .then(() => {\r\n                  this.$router.push(\"/login\");\r\n                })\r\n                .catch(() => {});\r\n            })\r\n            .catch(() => {\r\n              this.loading = false;\r\n              if (this.captchaEnabled) {\r\n                this.getCode();\r\n              }\r\n            });\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.register {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.register-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.register-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.register-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-register-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.register-code-img {\r\n  height: 38px;\r\n}\r\n</style>\r\n"]}]}