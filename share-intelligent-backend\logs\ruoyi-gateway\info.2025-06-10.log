13:59:08.031 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
13:59:08.091 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:59:08.470 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:59:08.471 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:59:10.643 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
13:59:15.287 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
13:59:16.264 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
13:59:16.812 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:59:16.812 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:59:17.728 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
13:59:17.730 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
13:59:17.971 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
13:59:18.151 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
13:59:18.189 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 11.002 seconds (JVM running for 12.095)
13:59:18.196 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
13:59:18.196 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
13:59:18.197 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
14:03:54.264 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
14:03:54.265 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:03:55.675 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/user/getInfo
14:03:55.675 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:03:56.920 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/getRouters
14:03:56.920 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:04:01.144 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /code/gen/list
14:04:01.144 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:04:03.949 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /code/gen/list
14:04:03.949 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:04:07.895 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /code/gen/preview/59
14:04:07.895 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:05:02.447 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/user/getInfo
14:05:02.447 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:05:02.668 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/getRouters
14:05:02.669 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:05:03.918 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /code/gen/list
14:05:03.918 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:05:05.858 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /code/gen/list
14:05:05.858 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:05:15.442 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /code/gen/preview/59
14:05:15.442 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:06:22.528 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /code/gen/preview/60
14:06:22.529 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:07:03.616 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/user/getInfo
14:07:03.616 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:07:04.033 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/getRouters
14:07:04.033 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:07:05.487 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /code/gen/list
14:07:05.487 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:07:09.122 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /code/gen/list
14:07:09.123 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:07:13.048 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /code/gen/preview/60
14:07:13.049 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:07:47.056 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/type/sys_show_hide
14:07:47.057 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:07:47.366 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/list
14:07:47.366 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:07:47.413 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/type/sys_normal_disable
14:07:47.414 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:08:29.711 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/list
14:08:29.712 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:09:08.238 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu
14:09:08.239 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:09:08.815 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/list
14:09:08.816 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:09:15.378 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/2199
14:09:15.379 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:09:15.695 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/list
14:09:15.696 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:09:22.991 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu
14:09:22.991 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:09:23.273 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/list
14:09:23.273 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:09:30.947 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/list
14:09:30.948 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:10:28.360 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu
14:10:28.360 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:10:28.619 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/list
14:10:28.620 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:11:00.383 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/user/getInfo
14:11:00.384 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:11:00.570 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/getRouters
14:11:00.571 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:11:01.659 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/type/sys_show_hide
14:11:01.660 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:11:01.942 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/type/sys_normal_disable
14:11:01.943 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:11:01.948 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/list
14:11:01.948 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:11:04.900 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:11:04.901 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:14:04.494 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/user/getInfo
14:14:04.494 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:14:04.666 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/getRouters
14:14:04.667 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:14:05.788 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:14:05.788 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:15:34.898 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:15:34.898 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:15:37.757 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:15:37.758 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:15:59.419 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:15:59.419 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:16:00.973 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:16:00.974 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:16:05.518 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:16:05.519 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:16:06.534 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:16:06.534 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:18:24.678 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:18:24.679 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:18:39.268 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/1
14:18:39.268 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:19:37.589 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:19:37.590 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:19:59.958 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement
14:19:59.959 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:20:00.457 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:20:00.457 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:20:03.538 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/4
14:20:03.539 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:20:04.188 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:20:04.189 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:20:41.688 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
14:20:41.688 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:29:30.612 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/type/sys_normal_disable
14:29:30.612 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:29:30.615 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/type/list
14:29:30.617 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:30:37.727 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/type
14:30:37.727 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:30:38.001 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/type/list
14:30:38.001 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:30:46.992 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/type/list
14:30:46.993 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:30:57.281 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/type/optionselect
14:30:57.282 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:30:57.285 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/type/171
14:30:57.285 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:30:57.539 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
14:30:57.540 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:31:19.799 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data
14:31:19.800 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:31:20.321 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
14:31:20.322 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:31:26.504 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data
14:31:26.505 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:31:27.002 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
14:31:27.002 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:31:39.502 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
14:31:39.502 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:32:15.491 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/type/sys_supply_type
14:32:15.491 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:32:15.491 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
14:32:15.491 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:33:18.061 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
14:33:18.062 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:33:35.346 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/1
14:33:35.346 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:33:38.122 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/3
14:33:38.123 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:33:40.966 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo
14:33:40.966 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:33:41.450 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
14:33:41.451 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:34:03.490 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
14:34:03.490 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:34:19.162 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/user/getInfo
14:34:19.162 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:34:19.358 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/getRouters
14:34:19.358 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:34:20.591 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
14:34:20.591 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:34:20.593 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/type/sys_supply_type
14:34:20.593 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:34:24.107 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/1
14:34:24.108 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:34:26.413 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo
14:34:26.414 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:34:26.663 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
14:34:26.663 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:35:02.973 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/user/getInfo
14:35:02.975 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:35:03.196 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/getRouters
14:35:03.197 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:35:04.237 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/type/sys_supply_type
14:35:04.237 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:35:04.513 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
14:35:04.514 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:35:16.339 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/1
14:35:16.339 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:35:26.675 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo
14:35:26.675 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:36:11.010 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/1
14:36:11.010 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:36:12.418 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo
14:36:12.418 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:36:12.897 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
14:36:12.898 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:37:46.330 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo
14:37:46.331 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:37:46.591 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
14:37:46.591 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:37:55.509 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/4
14:37:55.509 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:37:56.006 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
14:37:56.006 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
14:53:13.732 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
14:53:13.733 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list]
15:05:53.761 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
15:05:53.817 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:05:54.271 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:05:54.272 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:06:04.318 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
15:06:08.036 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:06:08.616 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:06:09.185 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:06:09.185 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:06:17.333 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:06:17.334 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:09:36.411 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
15:09:36.460 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:09:36.890 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:09:36.890 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:09:39.170 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
15:09:43.219 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:09:43.806 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:09:44.194 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:09:44.195 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:09:45.106 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:09:45.106 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:09:45.330 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
15:09:45.495 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
15:09:45.531 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 9.872 seconds (JVM running for 10.978)
15:09:45.537 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
15:09:45.538 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
15:09:45.540 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
15:25:34.378 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
15:25:34.381 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
15:25:39.930 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
15:25:39.993 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:25:40.434 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:25:40.434 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:25:42.911 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
15:25:48.688 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:25:49.796 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:25:50.636 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:25:50.636 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:25:51.818 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:25:51.819 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:25:52.049 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
15:25:52.387 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
15:25:52.450 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 13.161 seconds (JVM running for 14.622)
15:25:52.457 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
15:25:52.458 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
15:25:52.460 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
15:50:22.363 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
15:50:22.364 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
15:50:28.567 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
15:50:28.633 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:50:29.089 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:50:29.090 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:50:31.451 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
15:50:38.248 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
15:50:39.587 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
15:50:40.593 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:50:40.593 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:50:42.235 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
15:50:42.236 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
15:50:42.506 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8080 register finished
15:50:42.963 [main] INFO  c.a.c.n.d.GatewayLocatorHeartBeatPublisher - [start,64] - Start nacos gateway locator heartBeat task scheduler.
15:50:43.009 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 15.094 seconds (JVM running for 16.81)
15:50:43.017 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
15:50:43.018 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway.yml, group=DEFAULT_GROUP
15:50:43.018 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-gateway-prod.yml, group=DEFAULT_GROUP
15:54:29.967 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/AppStore/listDesk
15:54:29.967 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/demand/listDesk
15:54:29.967 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /portalweb/NewsInformation/listDesk
15:54:29.973 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
15:54:29.973 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
15:54:29.973 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
15:54:37.150 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/materialInfo/listWithOrder
15:54:37.150 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
15:54:39.152 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/materialInfo/listWithOrder
15:54:39.154 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
15:54:44.976 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
15:54:44.977 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
15:55:28.530 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
15:55:28.531 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
15:55:38.036 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
15:55:38.036 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
15:56:04.830 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
15:56:04.830 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
15:56:13.211 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
15:56:13.211 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
15:59:09.211 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /auth/login
15:59:09.212 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
15:59:10.953 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
15:59:10.954 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:00:24.882 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:00:24.882 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:03:32.651 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:03:32.651 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:04:28.450 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:04:28.450 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:04:31.671 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:04:31.671 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:04:40.458 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:04:40.458 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:04:42.462 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:04:42.462 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:04:56.515 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:04:56.516 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:05:19.201 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:05:19.202 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:05:23.073 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:05:23.073 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:13:13.624 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:13:13.624 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:13:13.932 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:13:13.932 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:13:13.932 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:13:13.933 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:13:43.001 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:13:43.002 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:13:43.006 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:13:43.006 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:14:17.974 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:14:17.974 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:14:21.338 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:14:21.338 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:14:21.343 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:14:21.343 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:16:11.834 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:16:11.835 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:16:32.848 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:16:32.848 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:17:36.677 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:17:36.678 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:17:40.090 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:17:40.090 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:17:43.338 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:17:43.338 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:18:52.257 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:18:52.257 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:19:30.643 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:19:30.644 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:19:31.411 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:19:31.411 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:21:03.901 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:21:03.901 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:22:48.248 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:22:48.248 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:31:06.670 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:31:06.671 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:31:15.847 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:31:15.847 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:33:57.555 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:33:57.556 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:39:25.105 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:39:25.105 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:41:02.757 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:41:02.758 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:41:53.370 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:41:53.370 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:42:09.773 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:42:09.773 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:43:01.702 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:43:01.702 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:45:38.818 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:45:38.818 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:45:59.339 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo
16:45:59.340 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:46:00.359 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:46:00.359 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:46:15.915 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/user/getInfo
16:46:15.915 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:46:16.210 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/getRouters
16:46:16.210 [reactor-http-nio-12] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:46:17.616 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/type/sys_supply_type
16:46:17.616 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:46:17.931 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
16:46:17.931 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:46:21.731 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/5
16:46:21.731 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:48:29.549 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
16:48:29.549 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:48:31.283 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/5
16:48:31.283 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:48:36.384 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/1
16:48:36.384 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:48:40.807 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo
16:48:40.807 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:48:41.318 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
16:48:41.318 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:48:42.254 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/1
16:48:42.254 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:48:46.482 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo
16:48:46.483 [reactor-http-nio-7] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:48:46.742 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
16:48:46.742 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:48:47.638 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/1
16:48:47.639 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:49:16.469 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/5
16:49:16.470 [reactor-http-nio-10] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:51:26.203 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/user/getInfo
16:51:26.203 [reactor-http-nio-11] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:51:26.395 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/menu/getRouters
16:51:26.395 [reactor-http-nio-13] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:51:27.508 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/type/sys_supply_type
16:51:27.509 [reactor-http-nio-14] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:51:27.788 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
16:51:27.788 [reactor-http-nio-15] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:51:53.074 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:51:53.074 [reactor-http-nio-16] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:52:50.299 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/dict/data/list
16:52:50.299 [reactor-http-nio-1] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:53:00.623 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo
16:53:00.623 [reactor-http-nio-2] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:53:01.352 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
16:53:01.352 [reactor-http-nio-3] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:53:04.378 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
16:53:04.379 [reactor-http-nio-4] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
16:53:11.038 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/6
16:53:11.038 [reactor-http-nio-5] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
17:02:49.616 [lettuce-nioEventLoop-5-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:254)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
17:02:49.616 [lettuce-nioEventLoop-5-2] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:254)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
17:02:49.647 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /127.0.0.1:6379
17:02:49.647 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /127.0.0.1:6379
17:02:55.945 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:02:55.945 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:03:02.447 [lettuce-eventExecutorLoop-3-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:03:02.447 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:03:09.543 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:03:09.746 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:03:16.740 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:03:16.942 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:03:22.943 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:03:23.146 [lettuce-eventExecutorLoop-3-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:03:33.245 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:03:33.447 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:03:51.741 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:03:51.943 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:04:23.844 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:04:24.044 [lettuce-eventExecutorLoop-3-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:04:55.938 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:04:56.143 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:04:59.340 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
17:04:59.340 [reactor-http-nio-6] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
17:05:28.040 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:05:28.242 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:06:00.143 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:06:00.341 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:06:32.241 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:06:32.444 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:06:59.898 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysSupplierInfo/list
17:06:59.898 [reactor-http-nio-8] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
17:07:00.193 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,53] - 当前请求URL: /system/SysTechRequirement/list
17:07:00.193 [reactor-http-nio-9] INFO  c.r.g.f.AuthFilter - [filter,54] - 白名单列表: [/auth/logout, /auth/login, /auth/register, /*/v2/api-docs, /csrf, /auth/portallogout, /auth/portallogin, /auth/loginBySmsCode, /auth/portalregister, /auth/portalpassword, /auth/casLogin, /auth/TokenLogout, /system/dict/data/*, /auth/login, /cascenter/user/info, /portalweb/demand/listDesk, /portalweb/supply/listDesk, /portalweb/solution/listDesk, /portalweb/solutionType/listDesk, /portalweb/ClassicCase/listDesk, /portalweb/AppStore/listDesk, /portalweb/NewsInformation/listDesk, /portalweb/Company/listDesk, /portalweb/EcologyCategory/listDesk, /portalweb/ExpertDatabase/listDesk, /portalweb/demand/detailDesk, /portalweb/supply/detailDesk, /portalweb/AppStore/detailDesk, /portalweb/ClassicCase/detailDesk, /portalweb/NewsInformation/detailDesk, /portalweb/ExpertDatabase/detailDesk, /portalweb/Company/detailDesk, /portalweb/solution/detailDesk, /portalweb/Company/searchByKeywords, /portalweb/solutionType/listClassicCase, /portalweb/solutionType/listSolution, /auth/single/util/get_common_code, /auth/single/util/get_qwt_code, /portalweb/solution/solutionDeskList, /portalweb/PolicyInformation/listDesk, /portalweb/PolicySubmit/listDesk, /portalweb/PolicySubmit/detailDesk, /portalweb/PolicySubmit/company, /portalweb/policyLabel/allDesk, /portalweb/PolicyInformation/detailDesk, /portalweb/classicCaseIndustry/listClassicCase, /portalweb/companyRelated/listDesk, /portalweb/companyRelated/detailDesk, /portalweb/demand/listDesk/CompanyRelated, /portalweb/supply/listDesk/CompanyRelated, /portalweb/solution/listDesk/CompanyRelated, /portalweb/recommendation, /portalweb/applicationField/listDesk, /system/materialInfo/**, /system/testingItem/**, /system/laboratoryInfo/**, /system/manufactureOrder/**, /system/compositeMaterial/**, /system/deviceInfo/**, /system/workInfo/**, /system/materialInfo/listWithOrder, /system/jobInfo/**, /system/testingItem/leftJoinLabs, /system/laboratoryInfo/**, /system/certificate/**, /system/settledFactory/**, /system/incubationInfo/**, /system/info/**, /system/processOutsourcing/**, /system/testingItem/**, /system/fileShare/**, /system/outsourcingRequirement/list, /system/SysProduct/**, /system/SysProduct/list, /system/SysTechRequirement/list]
17:07:04.341 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:07:04.545 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:07:36.438 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:07:36.641 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:08:08.542 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:08:08.745 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:08:40.641 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:08:40.844 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:09:12.735 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:09:12.939 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:09:44.841 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:09:45.044 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
17:09:48.059 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
17:09:48.064 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
