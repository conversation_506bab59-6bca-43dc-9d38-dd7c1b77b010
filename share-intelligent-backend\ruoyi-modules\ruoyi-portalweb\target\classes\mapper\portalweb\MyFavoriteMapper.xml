<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.MyFavoriteMapper">
    
    <resultMap type="MyFavoriteVO" id="MyFavoriteResult">
        <result property="myFavoriteId"    column="my_favorite_id"    />
        <result property="favoriteType"    column="favorite_type"    />
        <result property="memberId"    column="member_id"    />
        <result property="issueId"    column="issue_id"    />
        <result property="mainContent"    column="main_content"    />
        <result property="companyName"    column="company_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectMyFavoriteVo">
        select my_favorite_id, favorite_type, member_id, issue_id, main_content, company_name, status, create_by, create_time, update_by, update_time, remark from my_favorite
    </sql>

    <select id="selectMyFavoriteList" parameterType="MyFavorite" resultMap="MyFavoriteResult">
        <include refid="selectMyFavoriteVo"/>
        <where>
            <if test="favoriteType != null  and favoriteType != ''"> and favorite_type = #{favoriteType}</if>
            <if test="memberId != null "> and member_id = #{memberId}</if>
            <if test="issueId != null "> and issue_id = #{issueId}</if>
            <if test="mainContent != null  and mainContent != ''"> and main_content like concat('%', #{mainContent}, '%')</if>
        </where>
    </select>
    
    <select id="selectMyFavoriteByMyFavoriteId" parameterType="Long" resultMap="MyFavoriteResult">
        <include refid="selectMyFavoriteVo"/>
        where my_favorite_id = #{myFavoriteId}
    </select>
    <select id="selectMyFavoriteByIssueId"
            parameterType="MyFavorite" resultMap="MyFavoriteResult">

        <include refid="selectMyFavoriteVo"></include>
        where favorite_type = #{favoriteType} and member_id = #{memberId} and issue_id = #{issueId}
    </select>

    <insert id="insertMyFavorite" parameterType="MyFavorite">
        insert into my_favorite
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="myFavoriteId != null">my_favorite_id,</if>
            <if test="favoriteType != null">favorite_type,</if>
            <if test="memberId != null">member_id,</if>
            <if test="issueId != null">issue_id,</if>
            <if test="mainContent != null">main_content,</if>
            <if test="companyName != null">company_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="myFavoriteId != null">#{myFavoriteId},</if>
            <if test="favoriteType != null">#{favoriteType},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="issueId != null">#{issueId},</if>
            <if test="mainContent != null">#{mainContent},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateMyFavorite" parameterType="MyFavorite">
        update my_favorite
        <trim prefix="SET" suffixOverrides=",">
            <if test="favoriteType != null">favorite_type = #{favoriteType},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="issueId != null">issue_id = #{issueId},</if>
            <if test="mainContent != null">main_content = #{mainContent},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where my_favorite_id = #{myFavoriteId}
    </update>
    <update id="updateMyFavoriteStatus" >
        update my_favorite set status = #{status}
        <where>
           favorite_type = #{favoriteType} and issue_id = #{issueId}
        </where>
    </update>

    <delete id="deleteMyFavoriteByMyFavoriteId" parameterType="Long">
        delete from my_favorite where my_favorite_id = #{myFavoriteId}
    </delete>

    <delete id="deleteMyFavoriteByMyFavoriteIds" parameterType="String">
        delete from my_favorite where my_favorite_id in 
        <foreach item="myFavoriteId" collection="array" open="(" separator="," close=")">
            #{myFavoriteId}
        </foreach>
    </delete>
</mapper>