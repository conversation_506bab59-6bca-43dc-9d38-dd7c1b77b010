17:43:37.338 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
17:43:39.548 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4725e567-7325-41db-8b9b-75cafb350938_config-0
17:43:39.696 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 84 ms to scan 1 urls, producing 3 keys and 6 values 
17:43:39.765 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 4 keys and 9 values 
17:43:39.791 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 3 keys and 10 values 
17:43:40.154 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 358 ms to scan 207 urls, producing 0 keys and 0 values 
17:43:40.178 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 1 keys and 5 values 
17:43:40.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 1 keys and 7 values 
17:43:40.274 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
17:43:40.601 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 317 ms to scan 207 urls, producing 0 keys and 0 values 
17:43:40.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4725e567-7325-41db-8b9b-75cafb350938_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:43:40.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4725e567-7325-41db-8b9b-75cafb350938_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/320861946
17:43:40.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4725e567-7325-41db-8b9b-75cafb350938_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/1135870515
17:43:40.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4725e567-7325-41db-8b9b-75cafb350938_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:43:40.613 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4725e567-7325-41db-8b9b-75cafb350938_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:43:40.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4725e567-7325-41db-8b9b-75cafb350938_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:43:43.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4725e567-7325-41db-8b9b-75cafb350938_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153422731_127.0.0.1_49585
17:43:43.116 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4725e567-7325-41db-8b9b-75cafb350938_config-0] Notify connected event to listeners.
17:43:43.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4725e567-7325-41db-8b9b-75cafb350938_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:43:43.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4725e567-7325-41db-8b9b-75cafb350938_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1563699545
17:43:43.361 [main] INFO  c.r.a.RuoYiAuthApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
17:43:48.661 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
17:43:48.662 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:43:48.662 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
17:43:49.231 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:43:54.684 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:43:55.956 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 790899e2-378a-4abd-af33-199629fc4b87
17:43:55.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] RpcClient init label, labels = {module=naming, source=sdk}
17:43:55.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:43:55.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:43:55.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:43:55.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:43:56.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750153435983_127.0.0.1_49789
17:43:56.099 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] Notify connected event to listeners.
17:43:56.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:43:56.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/1563699545
17:43:59.767 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
17:43:59.896 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-auth 192.168.0.68:9700 register finished
17:44:00.307 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] Receive server push request, request = NotifySubscriberRequest, requestId = 3
17:44:00.314 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] Ack server push request, request = NotifySubscriberRequest, requestId = 3
17:44:00.448 [main] INFO  c.r.a.RuoYiAuthApplication - [logStarted,61] - Started RuoYiAuthApplication in 24.379 seconds (JVM running for 26.429)
17:44:00.617 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth-dev.yaml, group=ruoyi
17:44:00.618 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth, group=ruoyi
17:44:00.619 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-auth.yaml, group=ruoyi
17:44:01.425 [RMI TCP Connection(10)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:35:14.656 [lettuce-nioEventLoop-4-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:258)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:722)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:658)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:584)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:496)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:986)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
18:35:14.824 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was localhost/127.0.0.1:6379
18:35:14.914 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4725e567-7325-41db-8b9b-75cafb350938_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:35:14.916 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:35:19.194 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790899e2-378a-4abd-af33-199629fc4b87] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
18:35:19.194 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4725e567-7325-41db-8b9b-75cafb350938_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
