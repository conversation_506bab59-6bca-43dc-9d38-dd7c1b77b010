<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.IncubationInfoMapper">

    <resultMap type="IncubationInfo" id="IncubationInfoResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="type"    column="type"    />
        <result property="images"    column="images"    />
        <result property="content"    column="content"    />
        <result property="sort"    column="sort"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectIncubationInfoVo">
        select id, title, type, images, content, sort, status, create_by, create_time, update_by, update_time, remark from incubation_info
    </sql>

    <select id="selectIncubationInfoList" parameterType="IncubationInfo" resultMap="IncubationInfoResult">
        <include refid="selectIncubationInfoVo"/>
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="images != null  and images != ''"> and images = #{images}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectIncubationInfoById" parameterType="Long" resultMap="IncubationInfoResult">
        <include refid="selectIncubationInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertIncubationInfo" parameterType="IncubationInfo" useGeneratedKeys="true" keyProperty="id">
        insert into incubation_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="images != null">images,</if>
            <if test="content != null">content,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="images != null">#{images},</if>
            <if test="content != null">#{content},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateIncubationInfo" parameterType="IncubationInfo">
        update incubation_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="images != null">images = #{images},</if>
            <if test="content != null">content = #{content},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteIncubationInfoById" parameterType="Long">
        delete from incubation_info where id = #{id}
    </delete>

    <delete id="deleteIncubationInfoByIds" parameterType="String">
        delete from incubation_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
