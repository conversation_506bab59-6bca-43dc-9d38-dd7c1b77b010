/**
 * THIS FILE IS AUTO-GENERATED
 * DON'T MAKE CHANGES HERE
 */
import { DenseMatrixDependencies } from './dependenciesDenseMatrixClass.generated.js';
import { divideScalarDependencies } from './dependenciesDivideScalar.generated.js';
import { equalScalarDependencies } from './dependenciesEqualScalar.generated.js';
import { matrixDependencies } from './dependenciesMatrix.generated.js';
import { multiplyScalarDependencies } from './dependenciesMultiplyScalar.generated.js';
import { subtractDependencies } from './dependenciesSubtract.generated.js';
import { typedDependencies } from './dependenciesTyped.generated.js';
import { createUsolve } from '../../factoriesAny.js';
export var usolveDependencies = {
  DenseMatrixDependencies,
  divideScalarDependencies,
  equalScalarDependencies,
  matrixDependencies,
  multiplyScalarDependencies,
  subtractDependencies,
  typedDependencies,
  createUsolve
};