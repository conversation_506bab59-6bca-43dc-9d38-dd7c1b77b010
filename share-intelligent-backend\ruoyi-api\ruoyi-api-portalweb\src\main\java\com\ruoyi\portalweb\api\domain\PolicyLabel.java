package com.ruoyi.portalweb.api.domain;

import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 政策标签对象 policy_label
 * 
 * <AUTHOR>
 * @date 2024-05-30
 */
public class PolicyLabel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 政策标签ID */
    private Long policyLabelId;

    /** 政策标签名称 */
    @Excel(name = "政策标签名称")
    private String policyLabelValue;

    /** 标签分组 */
    @Excel(name = "标签分组")
    private String policyLabelGroup;

    public void setPolicyLabelId(Long policyLabelId) 
    {
        this.policyLabelId = policyLabelId;
    }

    public Long getPolicyLabelId() 
    {
        return policyLabelId;
    }
    public void setPolicyLabelValue(String policyLabelValue) 
    {
        this.policyLabelValue = policyLabelValue;
    }

    public String getPolicyLabelValue() 
    {
        return policyLabelValue;
    }
    public void setPolicyLabelGroup(String policyLabelGroup) 
    {
        this.policyLabelGroup = policyLabelGroup;
    }

    public String getPolicyLabelGroup() 
    {
        return policyLabelGroup;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("policyLabelId", getPolicyLabelId())
            .append("policyLabelValue", getPolicyLabelValue())
            .append("policyLabelGroup", getPolicyLabelGroup())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
