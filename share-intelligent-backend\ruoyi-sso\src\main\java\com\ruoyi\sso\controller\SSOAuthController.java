package com.ruoyi.sso.controller;

import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.common.core.utils.uuid.IdUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.sso.domain.dto.LoginRequest;
import com.ruoyi.sso.domain.dto.TokenValidateRequest;
import com.ruoyi.sso.domain.vo.LoginResponse;
import com.ruoyi.sso.domain.vo.TokenValidateResponse;
import com.ruoyi.sso.service.SSOAuthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * SSO认证控制器
 * 
 * <AUTHOR>
 */
@Api(tags = "SSO认证服务")
@RestController
@RequestMapping("/sso")
public class SSOAuthController {

    private static final Logger log = LoggerFactory.getLogger(SSOAuthController.class);

    @Autowired
    private SSOAuthService ssoAuthService;

    @Autowired
    private RedisService redisService;

    /**
     * 获取验证码
     *
     * @return 验证码信息
     */
    @ApiOperation("获取验证码")
    @GetMapping("/captcha")
    public AjaxResult getCaptcha() {
        try {
            // 生成验证码UUID
            String uuid = IdUtils.simpleUUID();
            String verifyKey = "sso:captcha:" + uuid;

            // 生成4位数字验证码
            String captchaCode = String.valueOf((int)((Math.random() * 9 + 1) * 1000));

            // 存储到Redis，5分钟过期
            redisService.setCacheObject(verifyKey, captchaCode, 300);

            Map<String, Object> result = new HashMap<>();
            result.put("uuid", uuid);
            result.put("captchaEnabled", true);

            log.info("生成验证码成功: uuid={}", uuid);
            return AjaxResult.success("验证码生成成功", result);

        } catch (Exception e) {
            log.error("生成验证码异常", e);
            return AjaxResult.error("验证码生成失败: " + e.getMessage());
        }
    }

    /**
     * SSO登录页面
     *
     * @param clientId 客户端ID
     * @param redirectUri 回调地址
     * @param state 状态参数
     * @param response HTTP响应
     */
    @ApiOperation("SSO登录页面")
    @GetMapping("/login")
    public void loginPage(@RequestParam("client_id") String clientId,
                         @RequestParam("redirect_uri") String redirectUri,
                         @RequestParam(value = "state", required = false) String state,
                         HttpServletResponse response) throws IOException {
        
        log.info("SSO登录请求: clientId={}, redirectUri={}, state={}", clientId, redirectUri, state);
        
        // 检查客户端是否有效
        if (!ssoAuthService.isValidClient(clientId)) {
            response.sendError(HttpServletResponse.SC_BAD_REQUEST, "无效的客户端ID");
            return;
        }
        
        // 检查是否已登录
        String existingToken = ssoAuthService.getCurrentUserToken();
        if (existingToken != null) {
            // 已登录，直接生成授权码并跳转
            String authCode = ssoAuthService.generateAuthCode(existingToken, clientId, redirectUri);
            String callbackUrl = buildCallbackUrl(redirectUri, authCode, state);
            response.sendRedirect(callbackUrl);
            return;
        }
        
        // 未登录，跳转到登录页面
        String loginPageUrl = "/login.html?client_id=" + clientId + 
                             "&redirect_uri=" + java.net.URLEncoder.encode(redirectUri, "UTF-8");
        if (state != null) {
            loginPageUrl += "&state=" + java.net.URLEncoder.encode(state, "UTF-8");
        }
        
        response.sendRedirect(loginPageUrl);
    }

    /**
     * 用户登录认证
     * 
     * @param loginRequest 登录请求
     * @return 登录结果
     */
    @ApiOperation("用户登录认证")
    @PostMapping("/authenticate")
    public AjaxResult authenticate(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            log.info("用户登录认证: username={}, clientId={}", 
                    loginRequest.getUsername(), loginRequest.getClientId());
            
            LoginResponse response = ssoAuthService.authenticate(loginRequest);
            
            if (response.isSuccess()) {
                log.info("用户 {} 登录成功", loginRequest.getUsername());
                return AjaxResult.success("登录成功", response);
            } else {
                log.warn("用户 {} 登录失败: {}", loginRequest.getUsername(), response.getMessage());
                return AjaxResult.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("登录认证异常", e);
            return AjaxResult.error("登录认证失败: " + e.getMessage());
        }
    }

    /**
     * 授权码换取访问令牌
     * 
     * @param authCode 授权码
     * @param clientId 客户端ID
     * @param clientSecret 客户端密钥
     * @return 访问令牌
     */
    @ApiOperation("授权码换取访问令牌")
    @PostMapping("/token")
    public AjaxResult getToken(@RequestParam("code") String authCode,
                              @RequestParam("client_id") String clientId,
                              @RequestParam("client_secret") String clientSecret) {
        try {
            log.info("授权码换取令牌: authCode={}, clientId={}", authCode, clientId);
            
            Map<String, Object> tokenInfo = ssoAuthService.exchangeToken(authCode, clientId, clientSecret);
            
            if (tokenInfo != null) {
                log.info("令牌换取成功: clientId={}", clientId);
                return AjaxResult.success("令牌获取成功", tokenInfo);
            } else {
                log.warn("令牌换取失败: authCode={}, clientId={}", authCode, clientId);
                return AjaxResult.error("无效的授权码或客户端信息");
            }
        } catch (Exception e) {
            log.error("令牌换取异常", e);
            return AjaxResult.error("令牌获取失败: " + e.getMessage());
        }
    }

    /**
     * 验证访问令牌
     * 
     * @param validateRequest 验证请求
     * @return 验证结果
     */
    @ApiOperation("验证访问令牌")
    @PostMapping("/validate")
    public AjaxResult validateToken(@Valid @RequestBody TokenValidateRequest validateRequest) {
        try {
            log.info("令牌验证请求: clientId={}", validateRequest.getClientId());
            
            TokenValidateResponse response = ssoAuthService.validateToken(validateRequest);
            
            if (response.isValid()) {
                log.info("令牌验证成功: clientId={}, userId={}", 
                        validateRequest.getClientId(), response.getUserId());
                return AjaxResult.success("令牌验证成功", response);
            } else {
                log.warn("令牌验证失败: clientId={}, reason={}", 
                        validateRequest.getClientId(), response.getMessage());
                return AjaxResult.error(response.getMessage());
            }
        } catch (Exception e) {
            log.error("令牌验证异常", e);
            return AjaxResult.error("令牌验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     * 
     * @param accessToken 访问令牌
     * @return 用户信息
     */
    @ApiOperation("获取用户信息")
    @GetMapping("/userinfo")
    public AjaxResult getUserInfo(@RequestParam("access_token") String accessToken) {
        try {
            log.info("获取用户信息请求: accessToken={}", accessToken.substring(0, 10) + "...");
            
            Map<String, Object> userInfo = ssoAuthService.getUserInfo(accessToken);
            
            if (userInfo != null) {
                log.info("用户信息获取成功: userId={}", userInfo.get("userId"));
                return AjaxResult.success("用户信息获取成功", userInfo);
            } else {
                log.warn("用户信息获取失败: 无效的访问令牌");
                return AjaxResult.error("无效的访问令牌");
            }
        } catch (Exception e) {
            log.error("获取用户信息异常", e);
            return AjaxResult.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * SSO登出
     * 
     * @param accessToken 访问令牌
     * @param request HTTP请求
     * @return 登出结果
     */
    @ApiOperation("SSO登出")
    @PostMapping("/logout")
    public AjaxResult logout(@RequestParam(value = "access_token", required = false) String accessToken,
                            HttpServletRequest request) {
        try {
            log.info("SSO登出请求");
            
            boolean result = ssoAuthService.logout(accessToken);
            
            if (result) {
                log.info("SSO登出成功");
                return AjaxResult.success("登出成功");
            } else {
                log.warn("SSO登出失败");
                return AjaxResult.error("登出失败");
            }
        } catch (Exception e) {
            log.error("SSO登出异常", e);
            return AjaxResult.error("登出失败: " + e.getMessage());
        }
    }

    /**
     * 检查SSO状态
     * 
     * @param request HTTP请求
     * @return SSO状态
     */
    @ApiOperation("检查SSO状态")
    @GetMapping("/status")
    public AjaxResult checkStatus(HttpServletRequest request) {
        try {
            Map<String, Object> status = ssoAuthService.getStatus();
            return AjaxResult.success("状态获取成功", status);
        } catch (Exception e) {
            log.error("获取SSO状态异常", e);
            return AjaxResult.error("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 构造回调URL
     * 
     * @param redirectUri 重定向URI
     * @param authCode 授权码
     * @param state 状态参数
     * @return 回调URL
     */
    private String buildCallbackUrl(String redirectUri, String authCode, String state) {
        StringBuilder url = new StringBuilder(redirectUri);
        
        if (redirectUri.contains("?")) {
            url.append("&");
        } else {
            url.append("?");
        }
        
        url.append("code=").append(authCode);
        
        if (state != null) {
            url.append("&state=").append(state);
        }
        
        return url.toString();
    }
}
