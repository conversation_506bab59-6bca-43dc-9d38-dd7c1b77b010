import request from '@/utils/request'

// 查询生态类别列表
export function listEcologyCategory(query) {
  return request({
    url: '/portalconsole/EcologyCategory/list',
    method: 'get',
    params: query
  })
}

// 查询生态类别详细
export function getEcologyCategory(ecologyCategoryId) {
  return request({
    url: '/portalconsole/EcologyCategory/' + ecologyCategoryId,
    method: 'get'
  })
}

// 新增生态类别
export function addEcologyCategory(data) {
  return request({
    url: '/portalconsole/EcologyCategory',
    method: 'post',
    data: data
  })
}

// 修改生态类别
export function updateEcologyCategory(data) {
  return request({
    url: '/portalconsole/EcologyCategory',
    method: 'put',
    data: data
  })
}

// 删除生态类别
export function delEcologyCategory(ecologyCategoryId) {
  return request({
    url: '/portalconsole/EcologyCategory/' + ecologyCategoryId,
    method: 'delete'
  })
}
