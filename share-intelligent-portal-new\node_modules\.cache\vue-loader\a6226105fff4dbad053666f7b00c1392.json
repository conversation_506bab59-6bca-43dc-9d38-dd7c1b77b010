{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\menu\\index.vue?vue&type=template&id=0304e458", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\menu\\index.vue", "mtime": 1750311963032}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}