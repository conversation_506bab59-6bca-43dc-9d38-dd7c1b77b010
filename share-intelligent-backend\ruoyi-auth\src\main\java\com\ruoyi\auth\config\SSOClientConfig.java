package com.ruoyi.auth.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * SSO客户端配置
 *
 * <AUTHOR>
 */
@Configuration
public class SSOClientConfig {

    /**
     * SSO客户端属性配置
     */
    @ConfigurationProperties(prefix = "sso.client")
    @Bean
    public SSOClientProperties ssoClientProperties() {
        return new SSOClientProperties();
    }

    /**
     * RestTemplate配置
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(5000);
        factory.setReadTimeout(10000);
        return new RestTemplate(factory);
    }

    /**
     * SSO客户端属性类
     */
    public static class SSOClientProperties {

        private String serverUrl = "http://localhost:9300";
        private String clientId = "backend";
        private String clientSecret = "backend_2024#RuoYi@Share$Key!8888";
        private String callbackUrl = "http://localhost:9200/sso/callback";
        private String loginUrl = "/sso/login";
        private String tokenUrl = "/sso/token";
        private String validateUrl = "/sso/validate";
        private String userInfoUrl = "/sso/userinfo";
        private String logoutUrl = "/sso/logout";

        // Getters and Setters
        public String getServerUrl() {
            return serverUrl;
        }

        public void setServerUrl(String serverUrl) {
            this.serverUrl = serverUrl;
        }

        public String getClientId() {
            return clientId;
        }

        public void setClientId(String clientId) {
            this.clientId = clientId;
        }

        public String getClientSecret() {
            return clientSecret;
        }

        public void setClientSecret(String clientSecret) {
            this.clientSecret = clientSecret;
        }

        public String getCallbackUrl() {
            return callbackUrl;
        }

        public void setCallbackUrl(String callbackUrl) {
            this.callbackUrl = callbackUrl;
        }

        public String getLoginUrl() {
            return loginUrl;
        }

        public void setLoginUrl(String loginUrl) {
            this.loginUrl = loginUrl;
        }

        public String getTokenUrl() {
            return tokenUrl;
        }

        public void setTokenUrl(String tokenUrl) {
            this.tokenUrl = tokenUrl;
        }

        public String getValidateUrl() {
            return validateUrl;
        }

        public void setValidateUrl(String validateUrl) {
            this.validateUrl = validateUrl;
        }

        public String getUserInfoUrl() {
            return userInfoUrl;
        }

        public void setUserInfoUrl(String userInfoUrl) {
            this.userInfoUrl = userInfoUrl;
        }

        public String getLogoutUrl() {
            return logoutUrl;
        }

        public void setLogoutUrl(String logoutUrl) {
            this.logoutUrl = logoutUrl;
        }

        /**
         * 获取完整的SSO登录URL
         */
        public String getFullLoginUrl() {
            return serverUrl + loginUrl;
        }

        /**
         * 获取完整的Token URL
         */
        public String getFullTokenUrl() {
            return serverUrl + tokenUrl;
        }

        /**
         * 获取完整的验证URL
         */
        public String getFullValidateUrl() {
            return serverUrl + validateUrl;
        }

        /**
         * 获取完整的用户信息URL
         */
        public String getFullUserInfoUrl() {
            return serverUrl + userInfoUrl;
        }

        /**
         * 获取完整的登出URL
         */
        public String getFullLogoutUrl() {
            return serverUrl + logoutUrl;
        }
    }
}
