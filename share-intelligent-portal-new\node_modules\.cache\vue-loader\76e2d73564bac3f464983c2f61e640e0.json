{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\resource\\index.vue?vue&type=template&id=104058aa&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\resource\\index.vue", "mtime": 1750311963001}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFjdGl2aXR5LWNvbnRhaW5lciI+CiAgPGRpdiBjbGFzcz0iYWN0aXZpdHktYmFubmVyIj4KICAgIDxpbWcgc3JjPSIuLi8uLi9hc3NldHMvcmVzb3VyY2UvcmVzb3VyY2VCYW5uZXIucG5nIiBhbHQ9IiIgLz4KICA8L2Rpdj4KICA8ZGl2IHYtbG9hZGluZz0ibG9hZGluZyI+CiAgICA8ZGl2IGNsYXNzPSJhY3Rpdml0eS10aXRsZS1jb250ZW50Ij4KICAgICAgPGRpdiBjbGFzcz0iYWN0aXZpdHktdGl0bGUtYm94Ij4KICAgICAgICA8ZGl2IGNsYXNzPSJhY3Rpdml0eS1kaXZpZGVyIj48L2Rpdj4KICAgICAgICA8ZGl2IGNsYXNzPSJhY3Rpdml0eS10aXRsZSI+6ZO+6LWE5rqQPC9kaXY+CiAgICAgICAgPGRpdiBjbGFzcz0iYWN0aXZpdHktZGl2aWRlciI+PC9kaXY+CiAgICAgIDwvZGl2PgogICAgICA8ZGl2IGNsYXNzPSJhY3Rpdml0eS1zZWFyY2gtYm94Ij4KICAgICAgICA8ZWwtZm9ybSByZWY9ImZvcm0iIGNsYXNzPSJhY3Rpdml0eS1zZWFyY2gtZm9ybSIgOm1vZGVsPSJmb3JtIj4KICAgICAgICAgIDxlbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm0ubmFtZSIKICAgICAgICAgICAgICBwbGFjZWhvbGRlcj0i6K+36L6T5YWl5pCc57Si5YaF5a65IgogICAgICAgICAgICAgIGNsYXNzPSJhY3Rpdml0eS1zZWFyY2gtaW5wdXQiCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8ZWwtYnV0dG9uCiAgICAgICAgICAgICAgICBzbG90PSJhcHBlbmQiCiAgICAgICAgICAgICAgICBjbGFzcz0iYWN0aXZpdHktc2VhcmNoLWJ0biIKICAgICAgICAgICAgICAgIEBjbGljaz0ib25TZWFyY2giCiAgICAgICAgICAgICAgICA+5pCc57SiPC9lbC1idXR0b24KICAgICAgICAgICAgICA+CiAgICAgICAgICAgIDwvZWwtaW5wdXQ+CiAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICA8L2VsLWZvcm0+CiAgICAgIDwvZGl2PgogICAgPC9kaXY+CiAgICA8ZGl2IGNsYXNzPSJhY3Rpdml0eS1pbmZvLWNvbnRlbnQiPgogICAgICA8ZGl2IGNsYXNzPSJhY3Rpdml0eS1zZWFyY2gtdHlwZS1ib3giPgogICAgICAgIDxlbC1mb3JtIHJlZj0iZm9ybUluZm8iIDptb2RlbD0iZm9ybUluZm8iPgogICAgICAgICAgPGRpdiBjbGFzcz0iYWN0aXZpdHktc2VhcmNoLWxpbmUiPgogICAgICAgICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLotYTmupDnsbvlnosiIGNsYXNzPSJhY3Rpdml0eS1zZWFyY2gtbGluZS1pdGVtIj4KICAgICAgICAgICAgICA8ZWwtcmFkaW8tZ3JvdXAKICAgICAgICAgICAgICAgIHYtbW9kZWw9ImZvcm1JbmZvLnN1cHBseVR5cGUiCiAgICAgICAgICAgICAgICBjbGFzcz0iYWN0aXZpdHktc2VhcmNoLXJhZGlvIgogICAgICAgICAgICAgICAgQGlucHV0PSJjaGFuZ2VSYWRpbyIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZWwtcmFkaW8tYnV0dG9uIGxhYmVsPSIiPuWFqOmDqDwvZWwtcmFkaW8tYnV0dG9uPgogICAgICAgICAgICAgICAgPGVsLXJhZGlvLWJ1dHRvbgogICAgICAgICAgICAgICAgICB2LWZvcj0iKGl0ZW0sIGluZGV4KSBpbiByZXNvdXJjZVR5cGVMaXN0IgogICAgICAgICAgICAgICAgICA6a2V5PSJpbmRleCIKICAgICAgICAgICAgICAgICAgOmxhYmVsPSJpdGVtLmRpY3RWYWx1ZSIKICAgICAgICAgICAgICAgICAgPnt7IGl0ZW0uZGljdExhYmVsIH19PC9lbC1yYWRpby1idXR0b24KICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8L2VsLXJhZGlvLWdyb3VwPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbQogICAgICAgICAgICAgIHYtaWY9IgogICAgICAgICAgICAgICAgZm9ybUluZm8uc3VwcGx5VHlwZSAhPT0gNCAmJgogICAgICAgICAgICAgICAgZm9ybUluZm8uc3VwcGx5VHlwZSAhPT0gNiAmJgogICAgICAgICAgICAgICAgZm9ybUluZm8uc3VwcGx5VHlwZSAhPT0gNwogICAgICAgICAgICAgICIKICAgICAgICAgICAgICBsYWJlbD0i5oqA5pyv57G75YirIgogICAgICAgICAgICAgIGNsYXNzPSJhY3Rpdml0eS1zZWFyY2gtbGluZS1pdGVtIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPGVsLXJhZGlvLWdyb3VwCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtSW5mby50ZWNobm9sb2d5VHlwZSIKICAgICAgICAgICAgICAgIGNsYXNzPSJhY3Rpdml0eS1zZWFyY2gtcmFkaW8iCiAgICAgICAgICAgICAgICBAaW5wdXQ9ImNoYW5nZVJhZGlvIgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxlbC1yYWRpby1idXR0b24gbGFiZWw9IiI+5YWo6YOoPC9lbC1yYWRpby1idXR0b24+CiAgICAgICAgICAgICAgICA8ZWwtcmFkaW8tYnV0dG9uCiAgICAgICAgICAgICAgICAgIHYtZm9yPSIoaXRlbSwgaW5kZXgpIGluIHRlY2hub2xvZ3lUeXBlTGlzdCIKICAgICAgICAgICAgICAgICAgOmtleT0iaW5kZXgiCiAgICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5kaWN0TGFiZWwiCiAgICAgICAgICAgICAgICAgID57eyBpdGVtLmRpY3RMYWJlbCB9fTwvZWwtcmFkaW8tYnV0dG9uCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9lbC1yYWRpby1ncm91cD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0KICAgICAgICAgICAgICB2LWlmPSJmb3JtSW5mby5zdXBwbHlUeXBlID09PSA0IgogICAgICAgICAgICAgIGxhYmVsPSLmioDmnK/nsbvliKsiCiAgICAgICAgICAgICAgY2xhc3M9ImFjdGl2aXR5LXNlYXJjaC1saW5lLWl0ZW0iCiAgICAgICAgICAgID4KICAgICAgICAgICAgICA8ZWwtcmFkaW8tZ3JvdXAKICAgICAgICAgICAgICAgIHYtbW9kZWw9InRlY2huaXF1ZVR5cGVOYW1lIgogICAgICAgICAgICAgICAgY2xhc3M9ImFjdGl2aXR5LXNlYXJjaC1yYWRpbyIKICAgICAgICAgICAgICAgIEBpbnB1dD0iY2hhbmdlUmFkaW8iCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPGVsLXJhZGlvLWJ1dHRvbiBsYWJlbD0iIj7lhajpg6g8L2VsLXJhZGlvLWJ1dHRvbj4KICAgICAgICAgICAgICAgIDxlbC1yYWRpby1idXR0b24KICAgICAgICAgICAgICAgICAgdi1mb3I9IihpdGVtLCBpbmRleCkgaW4gdGVjaG5vbG9neVR5cGVMaXN0MiIKICAgICAgICAgICAgICAgICAgOmtleT0iaW5kZXgiCiAgICAgICAgICAgICAgICAgIDpsYWJlbD0iaXRlbS5kaWN0TGFiZWwiCiAgICAgICAgICAgICAgICAgID57eyBpdGVtLmRpY3RMYWJlbCB9fTwvZWwtcmFkaW8tYnV0dG9uCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgPC9lbC1yYWRpby1ncm91cD4KICAgICAgICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgICAgICAgIDxlbC1mb3JtLWl0ZW0KICAgICAgICAgICAgICBsYWJlbD0i5oiQ5p6c6Zi25q61IgogICAgICAgICAgICAgIGNsYXNzPSJhY3Rpdml0eS1zZWFyY2gtbGluZS1pdGVtIgogICAgICAgICAgICAgIHYtaWY9IgogICAgICAgICAgICAgICAgZm9ybUluZm8uc3VwcGx5VHlwZSAhPT0gNCAmJgogICAgICAgICAgICAgICAgZm9ybUluZm8uc3VwcGx5VHlwZSAhPT0gNiAmJgogICAgICAgICAgICAgICAgZm9ybUluZm8uc3VwcGx5VHlwZSAhPT0gNwogICAgICAgICAgICAgICIKICAgICAgICAgICAgPgogICAgICAgICAgICAgIDxlbC1yYWRpby1ncm91cAogICAgICAgICAgICAgICAgdi1tb2RlbD0iZm9ybUluZm8ucHJvZHVjdFN0YWdlIgogICAgICAgICAgICAgICAgY2xhc3M9ImFjdGl2aXR5LXNlYXJjaC1yYWRpbyIKICAgICAgICAgICAgICAgIEBpbnB1dD0iY2hhbmdlUmFkaW8iCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPGVsLXJhZGlvLWJ1dHRvbiBsYWJlbD0iIj7lhajpg6g8L2VsLXJhZGlvLWJ1dHRvbj4KICAgICAgICAgICAgICAgIDxlbC1yYWRpby1idXR0b24KICAgICAgICAgICAgICAgICAgdi1mb3I9IihpdGVtLCBpbmRleCkgaW4gYWNoaWV2ZW1lbnRMaXN0IgogICAgICAgICAgICAgICAgICA6a2V5PSJpbmRleCIKICAgICAgICAgICAgICAgICAgOmxhYmVsPSJpdGVtLmRpY3RWYWx1ZSIKICAgICAgICAgICAgICAgICAgPnt7IGl0ZW0uZGljdExhYmVsIH19PC9lbC1yYWRpby1idXR0b24KICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8L2VsLXJhZGlvLWdyb3VwPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgICAgPGVsLWZvcm0taXRlbQogICAgICAgICAgICAgIGxhYmVsPSLlkIjkvZzmlrnlvI8iCiAgICAgICAgICAgICAgY2xhc3M9ImFjdGl2aXR5LXNlYXJjaC1saW5lLWl0ZW0iCiAgICAgICAgICAgICAgdi1pZj0iCiAgICAgICAgICAgICAgICBmb3JtSW5mby5zdXBwbHlUeXBlICE9PSA0ICYmCiAgICAgICAgICAgICAgICBmb3JtSW5mby5zdXBwbHlUeXBlICE9PSA2ICYmCiAgICAgICAgICAgICAgICBmb3JtSW5mby5zdXBwbHlUeXBlICE9PSA3CiAgICAgICAgICAgICAgIgogICAgICAgICAgICA+CiAgICAgICAgICAgICAgPGVsLXJhZGlvLWdyb3VwCiAgICAgICAgICAgICAgICB2LW1vZGVsPSJmb3JtSW5mby5jb29wZXJhdGlvbk1vZGUiCiAgICAgICAgICAgICAgICBjbGFzcz0iYWN0aXZpdHktc2VhcmNoLXJhZGlvIgogICAgICAgICAgICAgICAgQGlucHV0PSJjaGFuZ2VSYWRpbyIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZWwtcmFkaW8tYnV0dG9uIGxhYmVsPSIiPuWFqOmDqDwvZWwtcmFkaW8tYnV0dG9uPgogICAgICAgICAgICAgICAgPGVsLXJhZGlvLWJ1dHRvbgogICAgICAgICAgICAgICAgICB2LWZvcj0iKGl0ZW0sIGluZGV4KSBpbiBjb29wZXJhdGlvbk1vZGVMaXN0IgogICAgICAgICAgICAgICAgICA6a2V5PSJpbmRleCIKICAgICAgICAgICAgICAgICAgOmxhYmVsPSJpdGVtLmRpY3RWYWx1ZSIKICAgICAgICAgICAgICAgICAgPnt7IGl0ZW0uZGljdExhYmVsIH19PC9lbC1yYWRpby1idXR0b24KICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICA8L2VsLXJhZGlvLWdyb3VwPgogICAgICAgICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZWwtZm9ybT4KICAgICAgPC9kaXY+CiAgICAgIDxkaXYKICAgICAgICB2LWlmPSIKICAgICAgICAgIGZvcm1JbmZvLnN1cHBseVR5cGUgIT09IDQgJiYKICAgICAgICAgIGZvcm1JbmZvLnN1cHBseVR5cGUgIT09IDYgJiYKICAgICAgICAgIGZvcm1JbmZvLnN1cHBseVR5cGUgIT09IDcKICAgICAgICAiCiAgICAgID4KICAgICAgICA8ZGl2CiAgICAgICAgICB2LWZvcj0iKGl0ZW0sIGluZGV4KSBpbiBkYXRhIgogICAgICAgICAgOmtleT0iaW5kZXgiCiAgICAgICAgICBjbGFzcz0iYWN0aXZpdHktbGlzdC1pdGVtIgogICAgICAgICAgQGNsaWNrPSJnb1Jlc291cmNlRGV0YWlsKGl0ZW0uaWQpIgogICAgICAgID4KICAgICAgICAgIDxkaXYgY2xhc3M9Imxpc3QtaXRlbS1jb250ZW50Ij4KICAgICAgICAgICAgPGRpdiBjbGFzcz0ibGlzdC1pdGVtLWltZyI+CiAgICAgICAgICAgICAgPGltZwogICAgICAgICAgICAgICAgdi1pZj0iCiAgICAgICAgICAgICAgICAgIGl0ZW0ucHJvZHVjdFBob3RvICYmCiAgICAgICAgICAgICAgICAgIGl0ZW0ucHJvZHVjdFBob3RvLmxlbmd0aCA+IDAgJiYKICAgICAgICAgICAgICAgICAgaXRlbS5wcm9kdWN0UGhvdG9bMF0udXJsCiAgICAgICAgICAgICAgICAiCiAgICAgICAgICAgICAgICA6c3JjPSJpdGVtLnByb2R1Y3RQaG90b1swXS51cmwiCiAgICAgICAgICAgICAgICBhbHQ9IiIKICAgICAgICAgICAgICAvPgogICAgICAgICAgICAgIDxpbWcKICAgICAgICAgICAgICAgIHYtZWxzZQogICAgICAgICAgICAgICAgc3JjPSIuLi8uLi9hc3NldHMvcHVyY2hhc2VTYWxlcy9yZXNvdXJjZURlZmF1bHQucG5nIgogICAgICAgICAgICAgICAgYWx0PSIiCiAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9Imxpc3QtaXRlbS1pbmZvIiBzdHlsZT0ibWFyZ2luOiBhdXRvIDAiPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Imxpc3QtaXRlbS10aXRsZSI+CiAgICAgICAgICAgICAgICB7eyBpdGVtLnN1cHBseU5hbWUgfX0KICAgICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgICAgICA8ZGl2CiAgICAgICAgICAgICAgICBzdHlsZT0iCiAgICAgICAgICAgICAgICAgIG1hcmdpbi10b3A6IDEwcHg7CiAgICAgICAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4OwogICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAgICAgICAgIgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImNvbG9yOiByZ2IoNzcsIDc3LCA3OCkiPuW6lOeUqOmihuWfnzo8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYKICAgICAgICAgICAgICAgICAgc3R5bGU9Im1hcmdpbi1sZWZ0OiAxMHB4IgogICAgICAgICAgICAgICAgICB2LWlmPSIKICAgICAgICAgICAgICAgICAgICBpdGVtLmFwcGxpY2F0aW9uQXJlYSAmJiBpdGVtLmFwcGxpY2F0aW9uQXJlYS5sZW5ndGggPiAwCiAgICAgICAgICAgICAgICAgICIKICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgICAgPGVsLXRhZwogICAgICAgICAgICAgICAgICAgIGNsYXNzPSJ0YWdTdHlsZSIKICAgICAgICAgICAgICAgICAgICB2LWZvcj0iKHZhbCwgbnVtKSBpbiBpdGVtLmFwcGxpY2F0aW9uQXJlYSIKICAgICAgICAgICAgICAgICAgICA6a2V5PSJudW0iCiAgICAgICAgICAgICAgICAgICAgPnt7IHZhbCB9fTwvZWwtdGFnCiAgICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiB2LWVsc2Ugc3R5bGU9Im1hcmdpbi1sZWZ0OiAxMHB4Ij4KICAgICAgICAgICAgICAgICAgPGVsLXRhZz7mmoLml6A8L2VsLXRhZz4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDxkaXYKICAgICAgICAgICAgICAgIHN0eWxlPSIKICAgICAgICAgICAgICAgICAgbWFyZ2luLXRvcDogMTBweDsKICAgICAgICAgICAgICAgICAgbWFyZ2luLWxlZnQ6IDEwcHg7CiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgICAgICAgICAiCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iY29sb3I6IHJnYig3NywgNzcsIDc4KSI+5oqA5pyv57G75YirOjwvZGl2PgogICAgICAgICAgICAgICAgPGRpdgogICAgICAgICAgICAgICAgICBzdHlsZT0ibWFyZ2luLWxlZnQ6IDEwcHgiCiAgICAgICAgICAgICAgICAgIHYtaWY9Iml0ZW0udGVjaG5vbG9neVR5cGUgJiYgaXRlbS50ZWNobm9sb2d5VHlwZS5sZW5ndGggPiAwIgogICAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgICA8ZWwtdGFnCiAgICAgICAgICAgICAgICAgICAgY2xhc3M9InRhZ1N0eWxlIgogICAgICAgICAgICAgICAgICAgIHR5cGU9InN1Y2Nlc3MiCiAgICAgICAgICAgICAgICAgICAgdi1mb3I9Iih2YWwsIG51bSkgaW4gaXRlbS50ZWNobm9sb2d5VHlwZSIKICAgICAgICAgICAgICAgICAgICA6a2V5PSJudW0iCiAgICAgICAgICAgICAgICAgICAgPnt7IHZhbCB9fTwvZWwtdGFnCiAgICAgICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiB2LWVsc2Ugc3R5bGU9Im1hcmdpbi1sZWZ0OiAxMHB4Ij4KICAgICAgICAgICAgICAgICAgPGVsLXRhZyB0eXBlPSJzdWNjZXNzIj7mmoLml6A8L2VsLXRhZz4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDxkaXYKICAgICAgICAgICAgICAgIHYtaWY9ImZvcm1JbmZvLnN1cHBseVR5cGUgPT09IDYiCiAgICAgICAgICAgICAgICBzdHlsZT0iCiAgICAgICAgICAgICAgICAgIG1hcmdpbi10b3A6IDEwcHg7CiAgICAgICAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4OwogICAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICAgICAgICAgICAgIgogICAgICAgICAgICAgID4KICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9ImNvbG9yOiByZ2IoNzcsIDc3LCA3OCkiPuinhOagvOWei+WPtzo8L2Rpdj4KICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9Im1hcmdpbi1sZWZ0OiAxMHB4IiB2LWlmPSJpdGVtLnNwZWNpZmljYXRpb24iPgogICAgICAgICAgICAgICAgICA8ZWwtdGFnIGNsYXNzPSJ0YWdTdHlsZSIgdHlwZT0ic3VjY2VzcyI+e3sKICAgICAgICAgICAgICAgICAgICBpdGVtLnNwZWNpZmljYXRpb24KICAgICAgICAgICAgICAgICAgfX08L2VsLXRhZz4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiB2LWVsc2Ugc3R5bGU9Im1hcmdpbi1sZWZ0OiAxMHB4Ij4KICAgICAgICAgICAgICAgICAgPGVsLXRhZyB0eXBlPSJzdWNjZXNzIj7mmoLml6A8L2VsLXRhZz4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgICAgPGRpdiB2LWlmPSJmb3JtSW5mby5zdXBwbHlUeXBlID09PSA2Ij4KICAgICAgICA8ZGl2CiAgICAgICAgICB2LWZvcj0iKGl0ZW0sIGluZGV4KSBpbiBkYXRhIgogICAgICAgICAgOmtleT0iaW5kZXgiCiAgICAgICAgICBjbGFzcz0iYWN0aXZpdHktbGlzdC1pdGVtIgogICAgICAgICAgQGNsaWNrPSJnb0luc0RldGFpbChpdGVtLmlkKSIKICAgICAgICA+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJsaXN0LWl0ZW0tY29udGVudCI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9Imxpc3QtaXRlbS1pbWciPgogICAgICAgICAgICAgIDxpbWcgdi1pZj0iaXRlbS5waWNVcmwiIDpzcmM9Iml0ZW0ucGljVXJsIiBhbHQ9IiIgLz4KICAgICAgICAgICAgICA8IS0tIDxpbWcKICAgICAgICAgICAgICAgIHYtZWxzZQogICAgICAgICAgICAgICAgc3JjPSIuLi8uLi9hc3NldHMvcHVyY2hhc2VTYWxlcy9yZXNvdXJjZURlZmF1bHQucG5nIgogICAgICAgICAgICAgICAgYWx0PSIiCiAgICAgICAgICAgICAgLz4gLS0+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJsaXN0LWl0ZW0taW5mbyI+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ibGlzdC1pdGVtLXRpdGxlIj4KICAgICAgICAgICAgICAgIHt7IGl0ZW0ubmFtZSB9fQogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDxkaXYKICAgICAgICAgICAgICAgIHN0eWxlPSIKICAgICAgICAgICAgICAgICAgbWFyZ2luLXRvcDogMTBweDsKICAgICAgICAgICAgICAgICAgbWFyZ2luLWxlZnQ6IDEwcHg7CiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgICAgICAgICAiCiAgICAgICAgICAgICAgPgogICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0iY29sb3I6IHJnYig3NywgNzcsIDc4KSI+6KeE5qC85Z6L5Y+3OjwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiBzdHlsZT0ibWFyZ2luLWxlZnQ6IDEwcHgiIHYtaWY9Iml0ZW0uc3BlY2lmaWNhdGlvbiI+CiAgICAgICAgICAgICAgICAgIDxlbC10YWcgY2xhc3M9InRhZ1N0eWxlIiB0eXBlPSJzdWNjZXNzIj57ewogICAgICAgICAgICAgICAgICAgIGl0ZW0uc3BlY2lmaWNhdGlvbgogICAgICAgICAgICAgICAgICB9fTwvZWwtdGFnPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2IHYtZWxzZSBzdHlsZT0ibWFyZ2luLWxlZnQ6IDEwcHgiPgogICAgICAgICAgICAgICAgICA8ZWwtdGFnIHR5cGU9InN1Y2Nlc3MiPuaaguaXoDwvZWwtdGFnPgogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPCEtLSA8ZGl2IGNsYXNzPSJsaXN0LWl0ZW0tdGV4dCI+CiAgICAgICAgICAgICAge3sgaXRlbS5hY3Rpdml0eU92ZXJ2aWV3IH19CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJsaXN0LWl0ZW0tdGltZSI+e3sgaXRlbS5jcmVhdGVUaW1lU3RyIH19PC9kaXY+IC0tPgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgICAgPGRpdiB2LWlmPSJmb3JtSW5mby5zdXBwbHlUeXBlID09PSA3Ij4KICAgICAgICA8ZGl2CiAgICAgICAgICB2LWZvcj0iKGl0ZW0sIGluZGV4KSBpbiBkYXRhIgogICAgICAgICAgOmtleT0iaW5kZXgiCiAgICAgICAgICBjbGFzcz0iYWN0aXZpdHktbGlzdC1pdGVtIgogICAgICAgICAgQGNsaWNrPSJnb0xhYkRldGFpbChpdGVtLmlkKSIKICAgICAgICA+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJsaXN0LWl0ZW0tY29udGVudCI+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9Imxpc3QtaXRlbS1pbWciPgogICAgICAgICAgICAgIDxpbWcgdi1pZj0iaXRlbS5waWNVcmwiIDpzcmM9Iml0ZW0ucGljVXJsIiBhbHQ9IiIgLz4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9Imxpc3QtaXRlbS1pbmZvIj4KICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJsaXN0LWl0ZW0tdGl0bGUiPgogICAgICAgICAgICAgICAge3sgaXRlbS5uYW1lIH19CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPGRpdgogICAgICAgICAgICAgICAgc3R5bGU9IgogICAgICAgICAgICAgICAgICBtYXJnaW4tdG9wOiAxMHB4OwogICAgICAgICAgICAgICAgICBtYXJnaW4tbGVmdDogMTBweDsKICAgICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICAgICAgICAgICIKICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJjb2xvcjogcmdiKDc3LCA3NywgNzgpIj7ooYzkuJrpoobln586PC9kaXY+CiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPSJtYXJnaW4tbGVmdDogMTBweCIgdi1pZj0iaXRlbS5pbmR1c3RyeSI+CiAgICAgICAgICAgICAgICAgIDxlbC10YWcgY2xhc3M9InRhZ1N0eWxlIiB0eXBlPSJzdWNjZXNzIj57ewogICAgICAgICAgICAgICAgICAgIGl0ZW0uaW5kdXN0cnkKICAgICAgICAgICAgICAgICAgfX08L2VsLXRhZz4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgICAgPGRpdiB2LWVsc2Ugc3R5bGU9Im1hcmdpbi1sZWZ0OiAxMHB4Ij4KICAgICAgICAgICAgICAgICAgPGVsLXRhZyB0eXBlPSJzdWNjZXNzIj7mmoLml6A8L2VsLXRhZz4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvZGl2PgogICAgICA8L2Rpdj4KICAgICAgPGRpdiB2LWlmPSJmb3JtSW5mby5zdXBwbHlUeXBlID09PSA0Ij4KICAgICAgICA8ZGl2IGNsYXNzPSJleHBlcnQtbGlicmFyeS1saXN0Ij4KICAgICAgICAgIDxkaXYKICAgICAgICAgICAgdi1mb3I9IihpdGVtLCBpbmRleCkgaW4gZGF0YSIKICAgICAgICAgICAgOmtleT0iaW5kZXgiCiAgICAgICAgICAgIGNsYXNzPSJsaXN0LWl0ZW0tY29udGVudCIKICAgICAgICAgICAgQGNsaWNrPSJnb0V4cGVydExpYnJhcnkoaXRlbS5pZCkiCiAgICAgICAgICA+CiAgICAgICAgICAgIDxkaXYgY2xhc3M9Imxpc3QtaXRlbS1ib3giPgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9Iml0ZW0taGVhZGxpbmUiPgogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaXRlbS10aXRsZSI+CiAgICAgICAgICAgICAgICAgIHt7IGl0ZW0uZXhwZXJ0TmFtZSB9fQogICAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iZXhwZXJ0LWxpYnJhcnktbGFiZWwiPgogICAgICAgICAgICAgICAgPGRpdgogICAgICAgICAgICAgICAgICB2LWZvcj0iKHZhbCwgaW5kZXgxKSBpbiBpdGVtLnRlY2huaXF1ZVR5cGVOYW1lIgogICAgICAgICAgICAgICAgICA6a2V5PSJpbmRleDEiCiAgICAgICAgICAgICAgICAgIGNsYXNzPSJsaWJyYXJ5LWxhYmVsLWl0ZW0iCiAgICAgICAgICAgICAgICA+CiAgICAgICAgICAgICAgICAgIDxzcGFuIHYtaWY9ImluZGV4MSA8IDIiIGNsYXNzPSJleHBlcnQtbGlicmFyeS10eXBlIj57ewogICAgICAgICAgICAgICAgICAgIGAjJHt2YWx9YAogICAgICAgICAgICAgICAgICB9fTwvc3Bhbj4KICAgICAgICAgICAgICAgICAgPHNwYW4gdi1lbHNlPuKApjwvc3Bhbj4KICAgICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImV4cGVydC1saWJyYXJ5LWJveCI+CiAgICAgICAgICAgICAgICB7eyBpdGVtLnN5bm9wc2lzIH19CiAgICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDwvZGl2PgogICAgICAgICAgICA8ZGl2IGNsYXNzPSJsaXN0LWl0ZW0taW1nIj4KICAgICAgICAgICAgICA8aW1nIHYtaWY9Iml0ZW0uaGVhZFBvcnRyYWl0IiA6c3JjPSJpdGVtLmhlYWRQb3J0cmFpdCIgYWx0PSIiIC8+CiAgICAgICAgICAgICAgPGltZwogICAgICAgICAgICAgICAgdi1lbHNlCiAgICAgICAgICAgICAgICBzcmM9Ii4uLy4uL2Fzc2V0cy9leHBlcnRMaWJyYXJ5L2RlZmF1bHRJbWcucG5nIgogICAgICAgICAgICAgICAgYWx0PSIiCiAgICAgICAgICAgICAgLz4KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2Rpdj4KICAgICAgPC9kaXY+CiAgICAgIDxkaXYgY2xhc3M9ImFjdGl2aXR5LXBhZ2UtZW5kIj4KICAgICAgICA8ZWwtYnV0dG9uIGNsYXNzPSJhY3Rpdml0eS1wYWdlLWJ0biIgQGNsaWNrPSJnb0hvbWUiPummlumhtTwvZWwtYnV0dG9uPgogICAgICAgIDxlbC1wYWdpbmF0aW9uCiAgICAgICAgICB2LWlmPSJkYXRhICYmIGRhdGEubGVuZ3RoID4gMCIKICAgICAgICAgIGJhY2tncm91bmQKICAgICAgICAgIGxheW91dD0icHJldiwgcGFnZXIsIG5leHQiCiAgICAgICAgICBjbGFzcz0iYWN0aXZpdHktcGFnaW5hdGlvbiIKICAgICAgICAgIDpwYWdlLXNpemU9InBhZ2VTaXplIgogICAgICAgICAgOmN1cnJlbnQtcGFnZT0icGFnZU51bSIKICAgICAgICAgIDp0b3RhbD0idG90YWwiCiAgICAgICAgICBAc2l6ZS1jaGFuZ2U9ImhhbmRsZVNpemVDaGFuZ2UiCiAgICAgICAgICBAY3VycmVudC1jaGFuZ2U9ImhhbmRsZUN1cnJlbnRDaGFuZ2UiCiAgICAgICAgPgogICAgICAgIDwvZWwtcGFnaW5hdGlvbj4KICAgICAgPC9kaXY+CiAgICA8L2Rpdj4KICA8L2Rpdj4KPC9kaXY+Cg=="}, null]}