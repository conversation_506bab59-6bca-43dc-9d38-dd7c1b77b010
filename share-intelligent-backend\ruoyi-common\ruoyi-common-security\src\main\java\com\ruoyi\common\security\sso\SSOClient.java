package com.ruoyi.common.security.sso;

import com.ruoyi.common.core.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * SSO客户端
 * 负责与SSO服务进行交互
 * 
 * <AUTHOR>
 */
@Component
public class SSOClient {
    
    private static final Logger log = LoggerFactory.getLogger(SSOClient.class);
    
    @Value("${sso.server.url:http://localhost:9300}")
    private String ssoServerUrl;
    
    @Value("${sso.client.id:backend}")
    private String clientId;
    
    @Value("${sso.client.secret:backend_2024#RuoYi@Share$Key!8888}")
    private String clientSecret;
    
    @Value("${sso.client.callback-url:http://localhost:9200/sso/callback}")
    private String callbackUrl;
    
    private final RestTemplate restTemplate = new RestTemplate();
    
    /**
     * 获取SSO登录URL
     */
    public String getLoginUrl(String redirectUrl) {
        StringBuilder loginUrl = new StringBuilder();
        loginUrl.append(ssoServerUrl).append("/sso/login");
        loginUrl.append("?client_id=").append(clientId);
        loginUrl.append("&redirect_uri=").append(callbackUrl);
        
        if (StringUtils.isNotEmpty(redirectUrl)) {
            loginUrl.append("&state=").append(redirectUrl);
        }
        
        return loginUrl.toString();
    }
    
    /**
     * 使用授权码换取访问令牌
     */
    public Map<String, Object> exchangeToken(String authCode) {
        if (StringUtils.isEmpty(authCode)) {
            return null;
        }
        
        try {
            String url = ssoServerUrl + "/sso/token";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("grant_type", "authorization_code");
            params.add("code", authCode);
            params.add("client_id", clientId);
            params.add("client_secret", clientSecret);
            params.add("redirect_uri", callbackUrl);
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                // 检查响应格式，SSO服务返回的是AjaxResult格式
                if (result.get("code") != null && "200".equals(result.get("code").toString())) {
                    log.info("授权码换取令牌成功");
                    return (Map<String, Object>) result.get("data");
                } else {
                    log.warn("授权码换取令牌失败: {}", result.get("msg"));
                    return null;
                }
            } else {
                log.warn("授权码换取令牌失败: {}", response.getStatusCode());
                return null;
            }
            
        } catch (Exception e) {
            log.error("授权码换取令牌异常", e);
            return null;
        }
    }
    
    /**
     * 验证访问令牌
     */
    public Map<String, Object> validateToken(String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            return null;
        }
        
        try {
            String url = ssoServerUrl + "/sso/validate";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("access_token", accessToken);
            requestBody.put("client_id", clientId);
            
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                Boolean valid = (Boolean) result.get("valid");
                if (Boolean.TRUE.equals(valid)) {
                    return result;
                }
            }
            
            return null;
            
        } catch (Exception e) {
            log.error("验证访问令牌异常", e);
            return null;
        }
    }
    
    /**
     * 获取用户信息
     */
    public Map<String, Object> getUserInfo(String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            return null;
        }
        
        try {
            String url = ssoServerUrl + "/sso/userinfo";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            
            HttpEntity<String> request = new HttpEntity<>(headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                log.debug("获取用户信息成功");
                return response.getBody();
            } else {
                log.warn("获取用户信息失败: {}", response.getStatusCode());
                return null;
            }
            
        } catch (Exception e) {
            log.error("获取用户信息异常", e);
            return null;
        }
    }
    
    /**
     * 登出
     */
    public boolean logout(String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            return false;
        }
        
        try {
            String url = ssoServerUrl + "/sso/logout";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            
            HttpEntity<String> request = new HttpEntity<>(headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("SSO登出成功");
                return true;
            } else {
                log.warn("SSO登出失败: {}", response.getStatusCode());
                return false;
            }
            
        } catch (Exception e) {
            log.error("SSO登出异常", e);
            return false;
        }
    }
    
    /**
     * 检查SSO服务状态
     */
    public boolean checkSSOStatus() {
        try {
            String url = ssoServerUrl + "/sso/status";
            
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            
            return response.getStatusCode() == HttpStatus.OK;
            
        } catch (Exception e) {
            log.error("检查SSO服务状态异常", e);
            return false;
        }
    }
    
    // Getters
    public String getSsoServerUrl() {
        return ssoServerUrl;
    }
    
    public String getClientId() {
        return clientId;
    }
    
    public String getCallbackUrl() {
        return callbackUrl;
    }
}
