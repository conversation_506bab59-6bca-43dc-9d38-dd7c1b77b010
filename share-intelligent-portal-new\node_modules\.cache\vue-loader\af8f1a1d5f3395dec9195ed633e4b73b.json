{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\classicCase\\index.vue?vue&type=style&index=1&id=6f030a3b&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\classicCase\\index.vue", "mtime": 1750311962925}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYWN0aXZpdHktY29udGFpbmVyIHsNCiAgLmFjdGl2aXR5LXNlYXJjaC1pbnB1dCB7DQogICAgLmVsLWlucHV0X19pbm5lciB7DQogICAgICBoZWlnaHQ6IDU0cHg7DQogICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgYm9yZGVyLXJhZGl1czogMjdweCAwIDAgMjdweDsNCiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1SZWd1bGFyLCBQaW5nRmFuZyBTQzsNCiAgICAgIGxpbmUtaGVpZ2h0OiAyNHB4Ow0KICAgICAgcGFkZGluZy1sZWZ0OiAzMHB4Ow0KICAgIH0NCiAgICAuZWwtaW5wdXQtZ3JvdXBfX2FwcGVuZCB7DQogICAgICBib3JkZXItcmFkaXVzOiAwcHggMTAwcHggMTAwcHggMHB4Ow0KICAgICAgYmFja2dyb3VuZDogIzIxYzliODsNCiAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLVJlZ3VsYXIsIFBpbmdGYW5nIFNDOw0KICAgICAgY29sb3I6ICNmZmY7DQogICAgICBsaW5lLWhlaWdodDogMjRweDsNCiAgICB9DQogIH0NCiAgLmFjdGl2aXR5LXNlYXJjaC1saW5lIHsNCiAgICAuZWwtZm9ybS1pdGVtX19sYWJlbCB7DQogICAgICB3aWR0aDogODhweDsNCiAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLU1lZGl1bSwgUGluZ0ZhbmcgU0M7DQogICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgY29sb3I6ICM5OTk7DQogICAgICBwYWRkaW5nLXJpZ2h0OiAzMnB4Ow0KICAgICAgdGV4dC1hbGlnbjogbGVmdDsNCiAgICB9DQogICAgLmFjdGl2aXR5LXNlYXJjaC1yYWRpbyB7DQogICAgICB3aWR0aDogMTA1MHB4Ow0KICAgICAgbWFyZ2luLXRvcDogMTFweDsNCiAgICAgIC5lbC1yYWRpby1idXR0b24gew0KICAgICAgICBwYWRkaW5nLWJvdHRvbTogMjBweDsNCiAgICAgICAgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgew0KICAgICAgICAgIGJvcmRlcjogbm9uZTsNCiAgICAgICAgICBwYWRkaW5nOiAwIDMycHggMCAwOw0KICAgICAgICAgIGJhY2tncm91bmQ6IG5vbmU7DQogICAgICAgICAgJjpob3ZlciB7DQogICAgICAgICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgJi5pcy1hY3RpdmUgew0KICAgICAgICAgIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsNCiAgICAgICAgICAgIGNvbG9yOiAjMjFjOWI4Ow0KICAgICAgICAgICAgYmFja2dyb3VuZDogbm9uZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLmVsLXJhZGlvLWJ1dHRvbl9fb3JpZy1yYWRpbzpjaGVja2VkIHsNCiAgICAgICAgICAmICsgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgew0KICAgICAgICAgICAgYm94LXNoYWRvdzogdW5zZXQ7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQogIC5hY3Rpdml0eS1wYWdlLWVuZCB7DQogICAgLmFjdGl2aXR5LXBhZ2luYXRpb24gew0KICAgICAgLmJ0bi1wcmV2LA0KICAgICAgLmJ0bi1uZXh0LA0KICAgICAgLmJ0bi1xdWlja3ByZXYgew0KICAgICAgICB3aWR0aDogMzJweDsNCiAgICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgfQ0KICAgICAgJi5pcy1iYWNrZ3JvdW5kIHsNCiAgICAgICAgLmVsLXBhZ2VyIHsNCiAgICAgICAgICAubnVtYmVyIHsNCiAgICAgICAgICAgIHdpZHRoOiAzMnB4Ow0KICAgICAgICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMzJweDsNCiAgICAgICAgICAgICYuYWN0aXZlIHsNCiAgICAgICAgICAgICAgYmFja2dyb3VuZDogIzIxYzliODsNCiAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzIxYzliODsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAycA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/classicCase", "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/classicCase/classicCaseBanner.png\" alt=\"\" />\r\n      <div class=\"bannerTitle\">典型案例</div>\r\n      <div class=\"bannerDesc\">\r\n        积累众多优秀典型案例，提供适用于不同行业、领域的低碳转型项目案例\r\n      </div>\r\n      <div class=\"classicCaseType\">\r\n        <div\r\n          v-for=\"item in caseTypeList\"\r\n          :key=\"item.dictValue\"\r\n          class=\"caseName\"\r\n          :class=\"activeName == item.dictValue ? 'caseNameHover' : ''\"\r\n          @click=\"getCaseType(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <!-- <div class=\"activity-title-content\">\r\n        <div class=\"activity-title-box\">\r\n          <div class=\"activity-divider\"></div>\r\n          <div class=\"activity-title\">典型案例</div>\r\n          <div class=\"activity-divider\"></div>\r\n        </div>\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.keywords\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div> -->\r\n      <div class=\"activity-info-content\">\r\n        <div v-if=\"data && data.length > 0\" class=\"activityList\">\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"activity-list-item\"\r\n            @click=\"goCaseDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img v-if=\"item.coverPicUrl\" alt=\"\" :src=\"item.coverPicUrl\" />\r\n              </div>\r\n              <div class=\"list-item-title\">\r\n                {{ item.name }}\r\n              </div>\r\n              <div class=\"list-item-icon\"></div>\r\n              <div v-html=\"item.detail\" class=\"detailContent\"></div>\r\n              <div class=\"itemButton\">案例详情</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"none-class\" v-else>\r\n          <el-image\r\n            style=\"width: 160px; height: 160px\"\r\n            :src=\"require('@/assets/user/none.png')\"\r\n            :fit=\"fit\"\r\n          ></el-image>\r\n          <div class=\"text\">暂无数据</div>\r\n        </div>\r\n        <div class=\"activity-page-end\">\r\n          <el-button class=\"activity-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"data && data.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { caseList } from \"@/api/classicCase\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      loading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        caseType: \"\", // 案例类型\r\n      },\r\n      caseTypeList: [],\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 9,\r\n      total: 0,\r\n      activeName: \"\",\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n    // this.getDictsList(\"activity_type\", \"activityTypeList\");\r\n    // this.search();\r\n  },\r\n  methods: {\r\n    getCaseList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        industry: this.formInfo.caseType,\r\n        name: this.form.keywords,\r\n      };\r\n      caseList(params).then((response) => {\r\n        this.data = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getCaseList();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.getCaseList();\r\n    },\r\n    goCaseDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/caseDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    initData() {\r\n      getDicts(\"case_industry\").then((res) => {\r\n        const { code, data = [] } = res;\r\n        if (code === 200) {\r\n          this.caseTypeList = data;\r\n          // console.log(this.caseTypeList, \"9999999999999\");\r\n          this.caseTypeList.unshift({\r\n            dictValue: \"\",\r\n            dictLabel: \"全部\",\r\n          });\r\n          this.getCaseList();\r\n        }\r\n      });\r\n    },\r\n    getCaseType(value) {\r\n      this.activeName = value;\r\n      this.formInfo.caseType = this.activeName;\r\n      this.getCaseList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  // background: #f4f5f9;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 500px;\r\n    position: relative;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    .bannerTitle {\r\n      position: absolute;\r\n      top: 161px;\r\n      left: 24%;\r\n      font-size: 50px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n    }\r\n    .bannerDesc {\r\n      position: absolute;\r\n      top: 249px;\r\n      left: 24%;\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n    .classicCaseType {\r\n      position: absolute;\r\n      bottom: -45px;\r\n      left: calc((100% - 1100px) / 2);\r\n      width: 1100px;\r\n      height: 90px;\r\n      background: #ffffff;\r\n      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.04);\r\n      border-radius: 45px;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      .caseName {\r\n        width: 100px;\r\n        height: 40px;\r\n        border-radius: 20px;\r\n        margin-left: 15px;\r\n        text-align: center;\r\n        line-height: 40px;\r\n        cursor: pointer;\r\n        font-size: 18px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #979797;\r\n      }\r\n      .caseNameHover {\r\n        background: #21c9b8;\r\n        color: #ffffff;\r\n      }\r\n      .caseName:nth-child(1) {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-info-content {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    background: rgb(253, 253, 253);\r\n    .activity-search-type-box {\r\n      background: #fff;\r\n      margin-bottom: -7px;\r\n      .activity-search-line {\r\n        padding: 14px 24px;\r\n        .activity-search-line-item {\r\n          margin-bottom: 0;\r\n        }\r\n        & + .activity-search-line {\r\n          border-top: 1px solid #f5f5f5;\r\n        }\r\n      }\r\n    }\r\n    .activityList {\r\n      display: flex;\r\n      width: 100%;\r\n      flex-wrap: wrap;\r\n    }\r\n    .activity-list-item {\r\n      width: 380px;\r\n      height: 420px;\r\n      background: #ffffff;\r\n      border-radius: 12px;\r\n      margin-top: 24px;\r\n      margin-left: 26px;\r\n      .list-item-content {\r\n        padding: 20px;\r\n        cursor: pointer;\r\n        .list-item-img {\r\n          width: 340px;\r\n          height: 200px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n            border-radius: 5px;\r\n          }\r\n        }\r\n        .list-item-title {\r\n          margin-top: 20px;\r\n          font-size: 20px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #333333;\r\n        }\r\n        .list-item-icon {\r\n          width: 38px;\r\n          height: 4px;\r\n          background: #21c9b8;\r\n          border-radius: 2px;\r\n          margin-top: 14px;\r\n        }\r\n        .detailContent {\r\n          color: #666;\r\n          overflow: hidden;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n          -webkit-line-clamp: 2;\r\n          text-overflow: ellipsis;\r\n        }\r\n        .itemButton {\r\n          width: 140px;\r\n          height: 44px;\r\n          line-height: 44px;\r\n          border: 1px solid #21c9b8;\r\n          border-radius: 22px;\r\n          margin: 15px auto;\r\n          text-align: center;\r\n          font-size: 18px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #21c9b8;\r\n        }\r\n        .list-item-info {\r\n          width: 700px;\r\n          padding-left: 24px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          margin: auto;\r\n          .cusPoints {\r\n            position: relative;\r\n            font-size: 20px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            color: #222222;\r\n            line-height: 30px;\r\n            margin-top: 20px;\r\n            .rectangle {\r\n              position: absolute;\r\n              top: 12px;\r\n              left: 0;\r\n            }\r\n          }\r\n          .list-item-text {\r\n            // height: 40px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            text-overflow: ellipsis;\r\n            font-size: 16px;\r\n            color: #666;\r\n            // line-height: 30px;\r\n            word-wrap: break-word;\r\n            margin-top: 15px;\r\n          }\r\n          .list-item-time {\r\n            color: #999;\r\n            line-height: 14px;\r\n            margin-top: 24px;\r\n          }\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .activity-list-item:nth-child(3n + 1) {\r\n      margin-left: 0;\r\n    }\r\n    .activity-list-item:hover {\r\n      background-color: #ffffff;\r\n      box-shadow: 0 4px 16px 0 rgba(38, 74, 116, 0.1);\r\n      .list-item-title {\r\n        color: #21c9b8;\r\n      }\r\n      .itemButton {\r\n        background: #21c9b8;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n    .activity-page-end {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin: 0 auto;\r\n      padding: 24px 0 60px;\r\n      .activity-page-btn {\r\n        width: 82px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    padding: 8% 0;\r\n    background: #fff;\r\n    margin-top: 25px;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}