{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\index.vue", "mtime": 1750311962979}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRQb2xpY3lEZWNsYXJlTGlzdCwgZ2V0TGlzdEJ5TmFtZSB9IGZyb20gIkAvYXBpL3BvbGljeURlY2xhcmUiOw0KaW1wb3J0IHsgZ2V0RGljdHMgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGljdC9kYXRhIjsNCmltcG9ydCB7IFBPTElDWV9TVEFUVVMgfSBmcm9tICJAL2NvbnN0L3N0YXR1cyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBmb3JtOiB7DQogICAgICAgIHRleHQ6ICIiLCAvL+aQnOe0ouWGheWuuQ0KICAgICAgfSwNCiAgICAgIGZvcm1JbmZvOiB7DQogICAgICAgIHJlbGVhc2VJZDogIiIsIC8v5Y+R5biD5Y2V5L2NDQogICAgICAgIHR5cGU6ICIiLCAvL+aUv+etluexu+Weiw0KICAgICAgICBwb2xpY3lTdGF0dXM6ICIiLCAvL+aUv+etlueKtuaAgQ0KICAgICAgICBsYWJlbENvZGVMaXN0OiBbXSwgLy/mlL/nrZbnlLvlg49jb2Rl6ZuG5ZCIDQogICAgICB9LA0KICAgICAgcmVsZWFzZUlkTGlzdDogW10sIC8v5Y+R5biD5Y2V5L2N5LiL5ouJ5YiX6KGoDQogICAgICB0eXBlTGlzdDogW10sIC8v5pS/562W57G75Z6L5LiL5ouJ5YiX6KGoDQogICAgICBwb2xpY3lTdGF0dXNMaXN0OiBQT0xJQ1lfU1RBVFVTLA0KICAgICAgYWR2YW5jZWRSZWxlYXNlSWQ6IGZhbHNlLA0KICAgICAgYWR2YW5jZWRUeXBlOiBmYWxzZSwNCiAgICAgIGRhdGE6IFtdLA0KICAgICAgcGFnZU51bTogMSwNCiAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgIHRvdGFsOiAwLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgY29uc3QgeyBjb2RlIH0gPSB0aGlzLiRyb3V0ZS5wYXJhbXMgfHwge307DQogICAgY29uc29sZS5sb2codGhpcy4kcm91dGUpOw0KICAgIGlmIChjb2RlKSB7DQogICAgICB0aGlzLmZvcm1JbmZvLmxhYmVsQ29kZUxpc3QgPSBjb2RlOw0KICAgIH0NCiAgICB0aGlzLmdldERpY3RzTGlzdCgicG9saWN5X3R5cGUiLCAidHlwZUxpc3QiKTsNCiAgICB0aGlzLmdldExpc3RCeU5hbWUoKTsNCiAgICB0aGlzLnNlYXJjaCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgc2VhcmNoKCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGdldFBvbGljeURlY2xhcmVMaXN0KHsNCiAgICAgICAgdGV4dDogdGhpcy5mb3JtLnRleHQsDQogICAgICAgIHR5cGU6IHRoaXMuZm9ybUluZm8udHlwZSwNCiAgICAgICAgcmVsZWFzZUlkOiB0aGlzLmZvcm1JbmZvLnJlbGVhc2VJZCwNCiAgICAgICAgcG9saWN5U3RhdHVzOiB0aGlzLmZvcm1JbmZvLnBvbGljeVN0YXR1cywNCiAgICAgICAgbGFiZWxDb2RlTGlzdDogdGhpcy5mb3JtSW5mby5sYWJlbENvZGVMaXN0LA0KICAgICAgICBwYWdlTnVtOiB0aGlzLnBhZ2VOdW0sDQogICAgICAgIHBhZ2VTaXplOiB0aGlzLnBhZ2VTaXplLA0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIGxldCB7IHJvd3MsIHRvdGFsIH0gPSByZXMgfHwgW107DQogICAgICAgICAgdGhpcy5kYXRhID0gcm93czsNCiAgICAgICAgICB0aGlzLnRvdGFsID0gdG90YWw7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5p+l6K+i5Y+R5biD5Y2V5L2NDQogICAgZ2V0TGlzdEJ5TmFtZSgpIHsNCiAgICAgIGdldExpc3RCeU5hbWUoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5yZWxlYXNlSWRMaXN0ID0gcmVzLmRhdGEgfHwgW107DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWtl+WFuA0KICAgIGdldERpY3RzTGlzdChjb2RlLCBwcm9wZXJ0eU5hbWUpIHsNCiAgICAgIGdldERpY3RzKGNvZGUpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzW3Byb3BlcnR5TmFtZV0gPSByZXMuZGF0YSB8fCBbXTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5pi+56S65pu05aSa5Y+R5biD5Y2V5L2NDQogICAgdG9nZ2xlUmVsZWFzZUlkKCkgew0KICAgICAgdGhpcy5hZHZhbmNlZFJlbGVhc2VJZCA9ICF0aGlzLmFkdmFuY2VkUmVsZWFzZUlkOw0KICAgIH0sDQogICAgLy8g5pi+56S65pu05aSa5pS/562W57G75Z6LDQogICAgdG9nZ2xlVHlwZSgpIHsNCiAgICAgIHRoaXMuYWR2YW5jZWRUeXBlID0gIXRoaXMuYWR2YW5jZWRUeXBlOw0KICAgIH0sDQogICAgY2hhbmdlUmFkaW8oKSB7DQogICAgICBjb25zb2xlLmxvZyh0eXBlb2YgdGhpcy5mb3JtSW5mby5yZWxlYXNlSWQsICIwMDAwIik7DQogICAgICB0aGlzLm9uU2VhcmNoKCk7DQogICAgfSwNCiAgICBoYW5kbGVTaXplQ2hhbmdlKHBhZ2VTaXplKSB7DQogICAgICB0aGlzLnBhZ2VTaXplID0gcGFnZVNpemU7DQogICAgICB0aGlzLm9uU2VhcmNoKCk7DQogICAgfSwNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKHBhZ2VOdW0pIHsNCiAgICAgIHRoaXMucGFnZU51bSA9IHBhZ2VOdW07DQogICAgICB0aGlzLnNlYXJjaCgpOw0KICAgIH0sDQogICAgb25TZWFyY2goKSB7DQogICAgICB0aGlzLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5zZWFyY2goKTsNCiAgICB9LA0KICAgIC8vIOi3s+i9rOWIsOaUv+etluivpuaDhemhtemdog0KICAgIGdvUG9saWN5RGVjbGFyZWxEZXRhaWwoaXRlbSkgew0KICAgICAgbGV0IHJvdXRlRGF0YSA9IHRoaXMuJHJvdXRlci5yZXNvbHZlKHsNCiAgICAgICAgcGF0aDogIi9wb2xpY3lEZWNsYXJlRGV0YWlsIiwNCiAgICAgICAgcXVlcnk6IHsgaWQ6IGl0ZW0uaWQgfSwNCiAgICAgIH0pOw0KICAgICAgd2luZG93Lm9wZW4ocm91dGVEYXRhLmhyZWYsICJfYmxhbmsiKTsNCiAgICB9LA0KICAgIC8vIOi3s+i9rOWIsOmmlumhtQ0KICAgIGdvSG9tZSgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogIi9pbmRleCIgfSk7DQogICAgfSwNCiAgfSwNCiAgd2F0Y2g6IHt9LA0KICBjb21wdXRlZDoge30sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiMA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/policy/declare", "sourcesContent": ["<template>\r\n  <div class=\"policy-declare-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"policy-declarel-banner\">\r\n      <img src=\"../../../assets/policyDeclare/policyDeclareBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"policy-declarel-title-content\">\r\n        <div class=\"policy-declarel-title-box\">\r\n          <div class=\"policy-declarel-divider\"></div>\r\n          <div class=\"policy-declarel-title\">政策申报</div>\r\n          <div class=\"policy-declarel-divider\"></div>\r\n        </div>\r\n        <div class=\"policy-declarel-search-box\">\r\n          <el-form ref=\"form\" class=\"policy-declarel-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.text\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"policy-declarel-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"policy-declarel-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"policy-declarel-card\">\r\n        <div class=\"policy-declarel-info-content\">\r\n          <div class=\"policy-declarel-search-type-box\">\r\n            <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n              <div class=\"policy-declarel-search-line\">\r\n                <el-form-item\r\n                  label=\"发布单位\"\r\n                  class=\"policy-declarel-search-line-item\"\r\n                  :class=\"{ advanced: !advancedReleaseId }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.releaseId\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in releaseIdList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.id\"\r\n                      >{{ item.name }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"policy-declarel-search-line-btn\"\r\n                  @click=\"toggleReleaseId\"\r\n                  >{{ advancedReleaseId ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n              <div class=\"policy-declarel-search-line\">\r\n                <el-form-item\r\n                  label=\"政策类型\"\r\n                  class=\"policy-declarel-search-line-item\"\r\n                  :class=\"{ advanced: !advancedType }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.type\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in typeList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"policy-declarel-search-line-btn\"\r\n                  @click=\"toggleType\"\r\n                  >{{ advancedType ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n              <div class=\"policy-declarel-search-line\">\r\n                <el-form-item\r\n                  label=\"政策状态\"\r\n                  class=\"policy-declarel-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.policyStatus\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in policyStatusList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.value\"\r\n                      >{{ item.label }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"policy-declarel-list-item\"\r\n            @click=\"goPolicyDeclarelDetail(item)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-headline\">\r\n                <div class=\"item-title\">{{ item.releaseUnitName }}</div>\r\n                <div v-if=\"item.releaseDistrict\" class=\"item-address-tag\">\r\n                  <img\r\n                    src=\"../../../assets/policyDeclare/policyAddressIcon.png\"\r\n                    alt=\"\"\r\n                    class=\"item-address-img\"\r\n                  />\r\n                  <div class=\"item-address-text\">\r\n                    {{ item.releaseDistrict }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"list-item-title\">\r\n                {{ item.title }}\r\n              </div>\r\n              <div class=\"list-item-box\">\r\n                <div class=\"list-item-time\">\r\n                  <div\r\n                    v-if=\"item.policyStatus === 2\"\r\n                    class=\"list-item-time-end\"\r\n                  >\r\n                    申报结束\r\n                  </div>\r\n                  <div v-else class=\"list-item-time-red\">\r\n                    距申报截止还有\r\n                    <div class=\"red-num\">{{ item.dayCount }}</div>\r\n                    天\r\n                  </div>\r\n                </div>\r\n                <div class=\"list-item-money\">\r\n                  <div class=\"list-item-money-title\">最高奖励</div>\r\n                  <span class=\"list-item-money-num\">{{ item.maxReward }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"list-item-status\">\r\n                <!-- 1进行中  2已截止 -->\r\n                <img\r\n                  v-if=\"item.policyStatus === 1\"\r\n                  src=\"../../../assets/policyDeclare/carryOnIcon.png\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../assets/policyDeclare/cutoffIcon.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"policy-declarel-page-end\">\r\n            <el-button class=\"policy-declarel-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            >\r\n            <el-pagination\r\n              v-if=\"data && data.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"policy-declarel-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @size-change=\"handleSizeChange\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getPolicyDeclareList, getListByName } from \"@/api/policyDeclare\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { POLICY_STATUS } from \"@/const/status\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        text: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        releaseId: \"\", //发布单位\r\n        type: \"\", //政策类型\r\n        policyStatus: \"\", //政策状态\r\n        labelCodeList: [], //政策画像code集合\r\n      },\r\n      releaseIdList: [], //发布单位下拉列表\r\n      typeList: [], //政策类型下拉列表\r\n      policyStatusList: POLICY_STATUS,\r\n      advancedReleaseId: false,\r\n      advancedType: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    const { code } = this.$route.params || {};\r\n    console.log(this.$route);\r\n    if (code) {\r\n      this.formInfo.labelCodeList = code;\r\n    }\r\n    this.getDictsList(\"policy_type\", \"typeList\");\r\n    this.getListByName();\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getPolicyDeclareList({\r\n        text: this.form.text,\r\n        type: this.formInfo.type,\r\n        releaseId: this.formInfo.releaseId,\r\n        policyStatus: this.formInfo.policyStatus,\r\n        labelCodeList: this.formInfo.labelCodeList,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 查询发布单位\r\n    getListByName() {\r\n      getListByName().then((res) => {\r\n        this.releaseIdList = res.data || [];\r\n      });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    // 显示更多发布单位\r\n    toggleReleaseId() {\r\n      this.advancedReleaseId = !this.advancedReleaseId;\r\n    },\r\n    // 显示更多政策类型\r\n    toggleType() {\r\n      this.advancedType = !this.advancedType;\r\n    },\r\n    changeRadio() {\r\n      console.log(typeof this.formInfo.releaseId, \"0000\");\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n    // 跳转到政策详情页面\r\n    goPolicyDeclarelDetail(item) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/policyDeclareDetail\",\r\n        query: { id: item.id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n  watch: {},\r\n  computed: {},\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.policy-declare-container {\r\n  width: 100%;\r\n  .policy-declarel-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .policy-declarel-title-content {\r\n    width: 100%;\r\n    padding-bottom: 18px;\r\n    .policy-declarel-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .policy-declarel-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .policy-declarel-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .policy-declarel-search-box {\r\n      .policy-declarel-search-form {\r\n        text-align: center;\r\n        .policy-declarel-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .policy-declarel-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .policy-declarel-card {\r\n    background: #f4f5f9;\r\n    padding-top: 40px;\r\n    .policy-declarel-info-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      .policy-declarel-search-type-box {\r\n        background: #fff;\r\n        margin-bottom: 17px;\r\n        .policy-declarel-search-line {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 14px 24px 4px;\r\n          .policy-declarel-search-line-item {\r\n            flex: 1;\r\n            margin-bottom: 0;\r\n            display: flex;\r\n            &.advanced {\r\n              overflow: hidden;\r\n              height: 45px;\r\n            }\r\n            .more-radio {\r\n              margin-top: 11px;\r\n              flex: 1;\r\n            }\r\n          }\r\n          .policy-declarel-search-line-btn {\r\n            display: inline-block;\r\n            width: 64px;\r\n            height: 24px;\r\n            background: #fff;\r\n            border-radius: 2px;\r\n            border: 1px solid #d9d9d9;\r\n            font-size: 12px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            color: #333;\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 0 16px;\r\n            margin-top: 5px;\r\n            &:hover {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n          & + .policy-declarel-search-line {\r\n            border-top: 1px solid #f5f5f5;\r\n          }\r\n        }\r\n      }\r\n      .policy-declarel-list-item {\r\n        position: relative;\r\n        width: 100%;\r\n        background: #fff;\r\n        border-radius: 12px;\r\n        .list-item-content {\r\n          padding: 27px 24px 24px 24px;\r\n          cursor: pointer;\r\n          .list-item-headline {\r\n            display: flex;\r\n            align-items: center;\r\n            .item-title {\r\n              max-width: 570px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #999;\r\n              line-height: 18px;\r\n              word-break: break-all;\r\n            }\r\n            .item-address-tag {\r\n              display: flex;\r\n              align-items: center;\r\n              border-radius: 6px;\r\n              border: 2px solid #ff8516;\r\n              font-size: 15px;\r\n              line-height: 15px;\r\n              text-align: center;\r\n              margin-left: 12px;\r\n              color: #ff8516;\r\n              .item-address-img {\r\n                width: 19px;\r\n                height: 18px;\r\n                margin-right: 1px;\r\n              }\r\n              .item-address-text {\r\n                max-width: 570px;\r\n                word-break: break-all;\r\n                padding: 3px 5px 2px 0;\r\n              }\r\n            }\r\n          }\r\n          .list-item-title {\r\n            font-size: 24px;\r\n            font-family: PingFangSC-Medium, PingFang SC;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            line-height: 36px;\r\n            word-break: break-all;\r\n            padding-top: 18px;\r\n          }\r\n          .list-item-box {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            margin-top: 20px;\r\n            .list-item-time {\r\n              background: #f5f5f5;\r\n              border-radius: 6px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #999;\r\n              line-height: 18px;\r\n              .list-item-time-end {\r\n                padding: 6px 12px;\r\n              }\r\n              .list-item-time-red {\r\n                display: flex;\r\n                align-items: center;\r\n                padding: 6px 15px 6px 12px;\r\n                .red-num {\r\n                  max-width: 270px;\r\n                  font-size: 24px;\r\n                  font-family: PingFangSC-Medium, PingFang SC;\r\n                  font-weight: 500;\r\n                  color: #cf4140;\r\n                  line-height: 24px;\r\n                  word-wrap: break-word;\r\n                }\r\n              }\r\n            }\r\n            .list-item-money {\r\n              display: flex;\r\n              align-items: flex-end;\r\n              max-width: 570px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              .list-item-money-title {\r\n                font-size: 18px;\r\n                color: #999;\r\n                line-height: 18px;\r\n                margin-right: 6px;\r\n              }\r\n              .list-item-money-num {\r\n                max-width: 270px;\r\n                font-size: 36px;\r\n                font-weight: 500;\r\n                color: #cf4140;\r\n                line-height: 36px;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n          }\r\n        }\r\n        & + .policy-declarel-list-item {\r\n          margin-top: 24px;\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        .list-item-status {\r\n          position: absolute;\r\n          top: 0;\r\n          right: 0;\r\n          img {\r\n            width: 92px;\r\n            height: 71px;\r\n          }\r\n        }\r\n      }\r\n      .policy-declarel-page-end {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        padding: 24px 0 60px;\r\n        .policy-declarel-page-btn {\r\n          width: 82px;\r\n          height: 32px;\r\n          background: #fff;\r\n          border-radius: 4px;\r\n          border: 1px solid #d9d9d9;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.policy-declare-container {\r\n  .policy-declarel-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .el-form-item__label {\r\n    width: 88px;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #999;\r\n    padding-right: 32px;\r\n    text-align: left;\r\n  }\r\n  .policy-declarel-search-line {\r\n    .el-form-item__content {\r\n      width: 970px;\r\n    }\r\n  }\r\n  .el-radio-button {\r\n    padding-bottom: 20px;\r\n    .el-radio-button__inner {\r\n      border: none;\r\n      padding: 0 32px 0 0;\r\n      background: none;\r\n      &:hover {\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        color: #21c9b8;\r\n        background: none;\r\n      }\r\n    }\r\n    .el-radio-button__orig-radio:checked {\r\n      & + .el-radio-button__inner {\r\n        box-shadow: unset;\r\n      }\r\n    }\r\n  }\r\n  .policy-declarel-page-end {\r\n    .policy-declarel-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}