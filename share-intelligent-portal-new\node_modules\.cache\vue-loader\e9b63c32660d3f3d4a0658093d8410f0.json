{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\serviceAgency.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\serviceAgency.vue", "mtime": 1750311962936}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBnZXRDb21wYW55SG9tZUxpc3QgfSBmcm9tICJAL2FwaS9wdXJjaGFzZVNhbGVzIjsNCmltcG9ydCBDcnlwdG9KUyBmcm9tICJjcnlwdG8tanMiOw0KbGV0IHNlY3JldEtleSA9ICI5elZuMCVicW1VWVNHdzJuIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIGRhdGE6IFtdLA0KICAgICAgcGFnZU51bTogMSwNCiAgICAgIHBhZ2VTaXplOiAxNSwNCiAgICAgIHRvdGFsOiAwLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgLy8gdGhpcy5zZWFyY2goKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIHNlYXJjaCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBnZXRDb21wYW55SG9tZUxpc3Qoew0KICAgICAgICByZWNvbW1lbmRTdGF0dXM6IDEsDQogICAgICAgIHBhZ2VOdW06IHRoaXMucGFnZU51bSwNCiAgICAgICAgcGFnZVNpemU6IHRoaXMucGFnZVNpemUsDQogICAgICB9KQ0KICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgbGV0IGtleSA9IENyeXB0b0pTLmVuYy5VdGY4LnBhcnNlKHNlY3JldEtleSk7DQogICAgICAgICAgbGV0IGRlY3J5cHQgPSBDcnlwdG9KUy5BRVMuZGVjcnlwdChyZXMsIGtleSwgew0KICAgICAgICAgICAgbW9kZTogQ3J5cHRvSlMubW9kZS5FQ0IsDQogICAgICAgICAgICBwYWRkaW5nOiBDcnlwdG9KUy5wYWQuUGtjczcsDQogICAgICAgICAgfSk7DQogICAgICAgICAgcmVzID0gSlNPTi5wYXJzZShDcnlwdG9KUy5lbmMuVXRmOC5zdHJpbmdpZnkoZGVjcnlwdCkpOw0KICAgICAgICAgIGxldCB7IHJvd3MsIHRvdGFsIH0gPSByZXMgfHwgW107DQogICAgICAgICAgdGhpcy5kYXRhID0gcm93czsNCiAgICAgICAgICB0aGlzLnRvdGFsID0gdG90YWw7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g6Lez6L2s5Yiw5LyB5Lia5ZCN5b2V6K+m5oOF6aG16Z2iDQogICAgZ29FbnRlcnByaXNlRGV0YWlsKGl0ZW0pIHsNCiAgICAgIGxldCByb3V0ZURhdGEgPSB0aGlzLiRyb3V0ZXIucmVzb2x2ZSh7DQogICAgICAgIHBhdGg6ICIvZW50ZXJwcmlzZURldGFpbCIsDQogICAgICAgIHF1ZXJ5OiB7IGlkOiBpdGVtLmlkLCBidXNpbmVzc05vOiBpdGVtLmJ1c2luZXNzTm8gfSwNCiAgICAgIH0pOw0KICAgICAgd2luZG93Lm9wZW4ocm91dGVEYXRhLmhyZWYsICJfYmxhbmsiKTsNCiAgICB9LA0KICAgIGdvRW50ZXJwcmlzZSgpIHsNCiAgICAgIGxldCByb3V0ZURhdGEgPSB0aGlzLiRyb3V0ZXIucmVzb2x2ZSh7DQogICAgICAgIHBhdGg6ICIvZW50ZXJwcmlzZUxpc3QiLA0KICAgICAgfSk7DQogICAgICB3aW5kb3cub3Blbihyb3V0ZURhdGEuaHJlZiwgIl9ibGFuayIpOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["serviceAgency.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "serviceAgency.vue", "sourceRoot": "src/views/components/home", "sourcesContent": ["<template>\r\n  <div\r\n    class=\"serviceBg wow animate__animated animate__fadeInUp\"\r\n    data-wow-duration=\"1s\"\r\n  >\r\n    <div class=\"serviceImg\"></div>\r\n    <div class=\"card-container\">\r\n      <div class=\"enterpriseTitle\">\r\n        <div>服务机构</div>\r\n        <div class=\"allEnterprise\" @click=\"goEnterprise\">查看全部>></div>\r\n      </div>\r\n      <div class=\"content\">\r\n        <div\r\n          class=\"contentItem\"\r\n          v-for=\"(item, index) in data\"\r\n          :key=\"index\"\r\n          @click=\"goEnterpriseDetail(item)\"\r\n          :title=\"item.name\"\r\n        >\r\n          <div\r\n            v-if=\"item.companyPictureList && item.companyPictureList.length > 0\"\r\n          >\r\n            <img :src=\"item.companyPictureList[0].url\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCompanyHomeList } from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 15,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    // this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getCompanyHomeList({\r\n        recommendStatus: 1,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 跳转到企业名录详情页面\r\n    goEnterpriseDetail(item) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/enterpriseDetail\",\r\n        query: { id: item.id, businessNo: item.businessNo },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goEnterprise() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/enterpriseList\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.serviceBg {\r\n  width: 100%;\r\n  height: 340px;\r\n  position: relative;\r\n  .serviceImg {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    width: calc((100% - 1200px) / 2);\r\n    height: 500px;\r\n    background-image: url(\"../../../assets/images/home/<USER>\");\r\n    background-size: 100% 100%;\r\n  }\r\n}\r\n.enterpriseTitle {\r\n  width: 100%;\r\n  font-size: 36px;\r\n  text-align: center;\r\n  margin: 10px 0 60px 0;\r\n  position: relative;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  color: #000000;\r\n  .allEnterprise {\r\n    position: absolute;\r\n    top: 8 px;\r\n    right: 0;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    line-height: 26px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n  flex-wrap: wrap;\r\n  // height: 280px;\r\n  .contentItem {\r\n    width: 220px;\r\n    height: 90px;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    background: #ffffff;\r\n    box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);\r\n    border-radius: 4px;\r\n    img {\r\n      width: 100%;\r\n      height: 90px;\r\n    }\r\n  }\r\n  .contentItem:nth-child(n + 6) {\r\n    margin-top: 25px;\r\n  }\r\n  .contentItem:hover {\r\n    transform: translateY(-10px);\r\n    transition: 1s;\r\n  }\r\n}\r\n</style>\r\n"]}]}