{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\enterpriseList\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\enterpriseList\\index.vue", "mtime": 1750311962987}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_data", "require", "_status", "_purchaseSales", "_cryptoJs", "_interopRequireDefault", "secret<PERSON>ey", "_default", "exports", "default", "data", "loading", "form", "queryParam", "formInfo", "companyLabel", "industrialChain", "industry", "companyLabelList", "ENTERPRISE_TYPE", "industrialChainList", "industryList", "advancedIndustrial", "advancedIndustry", "pageNum", "pageSize", "total", "created", "getDictsList", "search", "methods", "_this", "getCompanyHomeList", "_objectSpread2", "recommendStatus", "then", "res", "key", "CryptoJS", "enc", "Utf8", "parse", "decrypt", "AES", "mode", "ECB", "padding", "pad", "Pkcs7", "JSON", "stringify", "_ref", "rows", "catch", "code", "propertyName", "_this2", "getDicts", "toggleIndustry", "toggleTrade", "changeRadio", "onSearch", "goEnterpriseDetail", "item", "routeData", "$router", "resolve", "path", "query", "id", "businessNo", "window", "open", "href", "goHome", "push", "handleSizeChange", "handleCurrentChange"], "sources": ["src/views/purchaseSales/component/enterpriseList/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"enterprise-list-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"enterprise-list-banner\">\r\n      <img src=\"../../../../assets/enterprise/enterpriseBanner.png\" alt=\"\" />\r\n      <div class=\"bannerTitle\">服务机构</div>\r\n      <div class=\"bannerDesc\">\r\n        凝聚行业顶尖服务机构，提供全方面多领域的星碳服务\r\n      </div>\r\n    </div>\r\n    <div v-loading=\"loading\" style=\"margin-top: 40px\">\r\n      <div class=\"enterprise-list-title-content\">\r\n        <div class=\"enterprise-list-search-box\">\r\n          <el-form ref=\"form\" class=\"enterprise-list-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.queryParam\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"enterprise-list-search-input\"\r\n                :maxlength=\"255\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"enterprise-list-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"enterprise-list-card\">\r\n        <div class=\"enterprise-list-content\">\r\n          <div class=\"enterprise-list-search-type-box\">\r\n            <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n              <div class=\"enterprise-list-search-line\">\r\n                <el-form-item\r\n                  label=\"企业分类\"\r\n                  class=\"enterprise-list-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.companyLabel\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in companyLabelList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.value\"\r\n                      >{{ item.label }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"enterprise-list-search-line\">\r\n                <el-form-item\r\n                  label=\"产业链\"\r\n                  class=\"enterprise-list-search-line-item\"\r\n                  :class=\"{ advanced: !advancedIndustrial }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.industrialChain\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in industrialChainList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"enterprise-list-search-line-btn\"\r\n                  @click=\"toggleIndustry\"\r\n                  >{{ advancedIndustrial ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n              <div class=\"enterprise-list-search-line\">\r\n                <el-form-item\r\n                  label=\"行业领域\"\r\n                  class=\"enterprise-list-search-line-item\"\r\n                  :class=\"{ advanced: !advancedIndustry }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.industry\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in industryList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"enterprise-list-search-line-btn\"\r\n                  @click=\"toggleTrade\"\r\n                  >{{ advancedIndustry ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"enterprise-list-list-item\"\r\n            @click=\"goEnterpriseDetail(item)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img\r\n                  v-if=\"\r\n                    item.companyPictureList &&\r\n                    item.companyPictureList.length > 0\r\n                  \"\r\n                  alt=\"\"\r\n                  :src=\"item.companyPictureList[0].url\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/companyDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"list-item-box\">\r\n                <div class=\"item-title\">{{ item.name }}</div>\r\n                <div class=\"item-info-box\">\r\n                  {{ item.introduce }}\r\n                </div>\r\n                <div class=\"item-tag-content\">\r\n                  <div class=\"item-tag-box\">\r\n                    <div class=\"item-tag\" v-show=\"item.category\">\r\n                      {{ item.category }}\r\n                    </div>\r\n                    <div v-if=\"item.address\" class=\"item-address-tag\">\r\n                      <i class=\"el-icon-location item-address-img\"></i>\r\n                      <!-- <img\r\n                        src=\"../../../../assets/enterprise/addressIcon.png\"\r\n                        alt=\"\"\r\n                        class=\"item-address-img\"\r\n                      /> -->\r\n                      <div class=\"item-address-text\">{{ item.address }}</div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <!-- <div class=\"list-item-img\">\r\n                <img\r\n                  v-if=\"\r\n                    item.companyPictureList &&\r\n                    item.companyPictureList.length > 0\r\n                  \"\r\n                  alt=\"\"\r\n                  :src=\"item.companyPictureList[0].url\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/companyDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div> -->\r\n            </div>\r\n          </div>\r\n          <div class=\"enterprise-list-page-end\">\r\n            <el-button class=\"enterprise-list-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            >\r\n            <el-pagination\r\n              v-if=\"data && data.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"enterprise-list-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @size-change=\"handleSizeChange\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { ENTERPRISE_TYPE } from \"@/const/status\";\r\nimport { getCompanyHomeList } from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        queryParam: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        companyLabel: \"\", //企业分类\r\n        industrialChain: \"\", //产业链\r\n        industry: \"\", //行业领域\r\n      },\r\n      companyLabelList: ENTERPRISE_TYPE, //企业分类列表\r\n      industrialChainList: [], //产业链字典列表\r\n      industryList: [], //行业领域字典列表\r\n      advancedIndustrial: false,\r\n      advancedIndustry: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getDictsList(\"industrial_chain\", \"industrialChainList\");\r\n    this.getDictsList(\"company_industry\", \"industryList\");\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getCompanyHomeList({\r\n        ...this.form,\r\n        ...this.formInfo,\r\n        recommendStatus: 1,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    // 显示更多产业链\r\n    toggleIndustry() {\r\n      this.advancedIndustrial = !this.advancedIndustrial;\r\n    },\r\n    // 显示更多行业领域\r\n    toggleTrade() {\r\n      this.advancedIndustry = !this.advancedIndustry;\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    // 跳转到企业名录详情页面\r\n    goEnterpriseDetail(item) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/enterpriseDetail\",\r\n        query: { id: item.id, businessNo: item.businessNo },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.enterprise-list-container {\r\n  width: 100%;\r\n  .enterprise-list-banner {\r\n    width: 100%;\r\n    height: 500px;\r\n    position: relative;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    .bannerTitle {\r\n      position: absolute;\r\n      top: 161px;\r\n      left: 24%;\r\n      font-size: 50px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n    }\r\n    .bannerDesc {\r\n      position: absolute;\r\n      top: 249px;\r\n      left: 24%;\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .enterprise-list-title-content {\r\n    width: 100%;\r\n    .enterprise-list-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .enterprise-list-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .enterprise-list-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .enterprise-list-search-box {\r\n      .enterprise-list-search-form {\r\n        text-align: center;\r\n        .enterprise-list-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .enterprise-list-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .enterprise-list-card {\r\n    background: #ffffff;\r\n    .enterprise-list-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      .enterprise-list-search-type-box {\r\n        background: #fff;\r\n        margin-bottom: 17px;\r\n        .enterprise-list-search-line {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 14px 24px 4px;\r\n          .enterprise-list-search-line-item {\r\n            flex: 1;\r\n            margin-bottom: 0;\r\n            display: flex;\r\n            &.advanced {\r\n              overflow: hidden;\r\n              height: 45px;\r\n            }\r\n            .more-radio {\r\n              margin-top: 11px;\r\n              flex: 1;\r\n            }\r\n          }\r\n          .enterprise-list-search-line-btn {\r\n            display: inline-block;\r\n            width: 64px;\r\n            height: 24px;\r\n            background: #fff;\r\n            border-radius: 2px;\r\n            border: 1px solid #d9d9d9;\r\n            font-size: 12px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            color: #333;\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 0 16px;\r\n            margin-top: 5px;\r\n            &:hover {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n          & + .enterprise-list-search-line {\r\n            border-top: 1px solid #f5f5f5;\r\n          }\r\n        }\r\n      }\r\n      .enterprise-list-list-item {\r\n        width: 100%;\r\n        height: 220px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n        .list-item-content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 24px 24px 24px 32px;\r\n          cursor: pointer;\r\n          .list-item-box {\r\n            width: 880px;\r\n            // margin-left: 32px;\r\n            // padding-top: 8px;\r\n            .item-title {\r\n              width: 806px;\r\n              height: 24px;\r\n              font-size: 22px;\r\n              font-family: Source Han Sans CN;\r\n              font-weight: 500;\r\n              color: #222222;\r\n              line-height: 24px;\r\n              margin-bottom: 12px;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              word-break: break-all;\r\n            }\r\n            .item-tag-content {\r\n              display: flex;\r\n              align-items: top;\r\n              .item-tag-box {\r\n                display: flex;\r\n                width: 852px;\r\n                flex-wrap: wrap;\r\n                .item-tag {\r\n                  height: 38px;\r\n                  background: rgb(242, 245, 255);\r\n                  border-radius: 4px;\r\n                  padding: 0 12px;\r\n                  max-width: 860px;\r\n                  line-height: 38px;\r\n                  margin-right: 16px;\r\n                  margin-top: 12px;\r\n                  color: #21c9b8;\r\n                  word-break: break-all;\r\n                }\r\n                .item-address-tag {\r\n                  display: flex;\r\n                  align-items: center;\r\n                  padding: 0 12px 0 5px;\r\n                  line-height: 24px;\r\n                  text-align: center;\r\n                  margin-right: 16px;\r\n                  margin-top: 12px;\r\n                  background: rgb(240, 249, 247);\r\n                  border-radius: 2px;\r\n                  .item-address-img {\r\n                    width: 14px;\r\n                    height: 18px;\r\n                    line-height: 18px;\r\n                    margin-right: 4px;\r\n                    color: #039477;\r\n                  }\r\n                  .item-address-text {\r\n                    color: #039477;\r\n                    max-width: 860px;\r\n                    word-break: break-all;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n            .item-info-box {\r\n              width: 806px;\r\n              height: 78px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #65676a;\r\n              line-height: 26px;\r\n              margin-top: 24px;\r\n              overflow: hidden;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n              -webkit-line-clamp: 3;\r\n              text-overflow: ellipsis;\r\n              word-break: break-all;\r\n            }\r\n          }\r\n          .list-item-img {\r\n            width: 220px;\r\n            height: 160px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n              border-radius: 7px;\r\n            }\r\n          }\r\n          &:hover {\r\n            .list-item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        & + .enterprise-list-list-item {\r\n          margin-top: 24px;\r\n        }\r\n      }\r\n      .enterprise-list-list-item:hover {\r\n        box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.2);\r\n      }\r\n      .enterprise-list-page-end {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        padding: 24px 0 60px;\r\n        .enterprise-list-page-btn {\r\n          width: 82px;\r\n          height: 32px;\r\n          background: #fff;\r\n          border-radius: 4px;\r\n          border: 1px solid #d9d9d9;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.enterprise-list-container {\r\n  .enterprise-list-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .el-form-item__label {\r\n    width: 88px;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #999;\r\n    padding-right: 32px;\r\n    text-align: left;\r\n  }\r\n  .enterprise-list-search-line {\r\n    .el-form-item__content {\r\n      width: 970px;\r\n    }\r\n  }\r\n  .el-radio-button {\r\n    padding-bottom: 20px;\r\n    .el-radio-button__inner {\r\n      border: none;\r\n      padding: 0 32px 0 0;\r\n      background: none;\r\n      &:hover {\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        color: #21c9b8;\r\n        background: none;\r\n      }\r\n    }\r\n    .el-radio-button__orig-radio:checked {\r\n      & + .el-radio-button__inner {\r\n        box-shadow: unset;\r\n      }\r\n    }\r\n  }\r\n  .enterprise-list-page-end {\r\n    .enterprise-list-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AAqMA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAC,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAK,SAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;QACAC,UAAA;MACA;MACAC,QAAA;QACAC,YAAA;QAAA;QACAC,eAAA;QAAA;QACAC,QAAA;MACA;MACAC,gBAAA,EAAAC,uBAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,YAAA;MAAA;MACAC,kBAAA;MACAC,gBAAA;MACAb,IAAA;MACAc,OAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAA,YAAA;IACA,KAAAC,MAAA;EACA;EACAC,OAAA;IACAD,MAAA,WAAAA,OAAA;MAAA,IAAAE,KAAA;MACA,KAAApB,OAAA;MACA,IAAAqB,iCAAA,MAAAC,cAAA,CAAAxB,OAAA,MAAAwB,cAAA,CAAAxB,OAAA,MAAAwB,cAAA,CAAAxB,OAAA,MACA,KAAAG,IAAA,GACA,KAAAE,QAAA;QACAoB,eAAA;QACAV,OAAA,OAAAA;QACA;MAAA,EACA,EACAW,IAAA,WAAAC,GAAA;QACAL,KAAA,CAAApB,OAAA;QACA,IAAA0B,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAnC,SAAA;QACA,IAAAoC,OAAA,GAAAJ,iBAAA,CAAAK,GAAA,CAAAD,OAAA,CAAAN,GAAA,EAAAC,GAAA;UACAO,IAAA,EAAAN,iBAAA,CAAAM,IAAA,CAAAC,GAAA;UACAC,OAAA,EAAAR,iBAAA,CAAAS,GAAA,CAAAC;QACA;QACAZ,GAAA,GAAAa,IAAA,CAAAR,KAAA,CAAAH,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAU,SAAA,CAAAR,OAAA;QACA,IAAAS,IAAA,GAAAf,GAAA;UAAAgB,IAAA,GAAAD,IAAA,CAAAC,IAAA;UAAA1B,KAAA,GAAAyB,IAAA,CAAAzB,KAAA;QACAK,KAAA,CAAArB,IAAA,GAAA0C,IAAA;QACArB,KAAA,CAAAL,KAAA,GAAAA,KAAA;MACA,GACA2B,KAAA;QACAtB,KAAA,CAAApB,OAAA;MACA;IACA;IACA;IACAiB,YAAA,WAAAA,aAAA0B,IAAA,EAAAC,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,cAAA,EAAAH,IAAA,EAAAnB,IAAA,WAAAC,GAAA;QACAoB,MAAA,CAAAD,YAAA,IAAAnB,GAAA,CAAA1B,IAAA;MACA;IACA;IACA;IACAgD,cAAA,WAAAA,eAAA;MACA,KAAApC,kBAAA,SAAAA,kBAAA;IACA;IACA;IACAqC,WAAA,WAAAA,YAAA;MACA,KAAApC,gBAAA,SAAAA,gBAAA;IACA;IACAqC,WAAA,WAAAA,YAAA;MACA,KAAAC,QAAA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAAC,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACAC,KAAA;UAAAC,EAAA,EAAAN,IAAA,CAAAM,EAAA;UAAAC,UAAA,EAAAP,IAAA,CAAAO;QAAA;MACA;MACAC,MAAA,CAAAC,IAAA,CAAAR,SAAA,CAAAS,IAAA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAT,OAAA,CAAAU,IAAA;QAAAR,IAAA;MAAA;IACA;IACAS,gBAAA,WAAAA,iBAAAnD,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAoC,QAAA;IACA;IACAgB,mBAAA,WAAAA,oBAAArD,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAK,MAAA;IACA;IACAgC,QAAA,WAAAA,SAAA;MACA,KAAArC,OAAA;MACA,KAAAK,MAAA;IACA;EACA;AACA", "ignoreList": []}]}