{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\demandHall\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\demandHall\\index.vue", "mtime": 1750311962985}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_purchaseSales", "require", "_data", "_cryptoJs", "_interopRequireDefault", "secret<PERSON>ey", "_default", "exports", "default", "data", "loading", "form", "name", "formInfo", "demandType", "demandList", "pageNum", "pageSize", "total", "created", "getDictsList", "search", "methods", "_this", "gatewayDemendListTen", "_objectSpread2", "displayStatus", "auditStatus", "then", "res", "console", "log", "key", "CryptoJS", "enc", "Utf8", "parse", "decrypt", "AES", "mode", "ECB", "padding", "pad", "Pkcs7", "JSON", "stringify", "_ref", "rows", "for<PERSON>ach", "item", "scenePicture", "applicationArea", "split", "catch", "code", "propertyName", "_this2", "getDicts", "changeRadio", "onSearch", "handleSizeChange", "handleCurrentChange", "goDemandDetail", "id", "routeData", "$router", "resolve", "path", "query", "window", "open", "href", "goHome", "push"], "sources": ["src/views/purchaseSales/component/demandHall/index.vue"], "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-02-01 17:22:11\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-12-09 18:52:38\r\n-->\r\n<template>\r\n  <div class=\"demand-hall-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"purchase-banner\">\r\n      <img src=\"../../../../assets/demandHall/demandHallBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"demand-hall-title-content\">\r\n        <div class=\"demand-hall-title-box\">\r\n          <div class=\"demand-hall-divider\"></div>\r\n          <div class=\"demand-hall-title\">商机需求</div>\r\n          <div class=\"demand-hall-divider\"></div>\r\n        </div>\r\n        <div class=\"demand-hall-search-box\">\r\n          <el-form ref=\"form\" class=\"demand-hall-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.name\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"demand-hall-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"demand-hall-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"demand-hall-card\">\r\n        <div class=\"demand-hall-info-content\">\r\n          <div class=\"demand-hall-search-type-box\">\r\n            <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n              <div class=\"demand-hall-search-line\">\r\n                <el-form-item\r\n                  label=\"需求类型\"\r\n                  class=\"demand-hall-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.demandType\"\r\n                    class=\"demand-hall-search-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in demandList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"demand-hall-list-item\"\r\n            @click=\"goDemandDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img\r\n                  v-if=\"item.scenePicture && item.scenePicture.length > 0\"\r\n                  alt=\"\"\r\n                  :src=\"item.scenePicture[0].url\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/demandDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"list-item-info\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.demandTitle }}\r\n                </div>\r\n                <div class=\"list-item-text\">\r\n                  <div class=\"list-item-label\">应用领域：</div>\r\n                  <div class=\"list-item-tag-box\">\r\n                    <div\r\n                      v-for=\"(val, num) in item.applicationArea\"\r\n                      :key=\"num\"\r\n                      class=\"lilst-item-tag\"\r\n                    >\r\n                      {{ val }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"demand-hall-page-end\">\r\n            <el-button class=\"demand-hall-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            >\r\n            <el-pagination\r\n              v-if=\"data && data.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"demand-hall-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @size-change=\"handleSizeChange\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { gatewayDemendListTen } from \"@/api/purchaseSales\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        name: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        demandType: \"\", //需求类型\r\n      },\r\n      demandList: [], //资讯类型列表\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getDictsList(\"demand_type\", \"demandList\");\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      gatewayDemendListTen({\r\n        ...this.form,\r\n        ...this.formInfo,\r\n        displayStatus: 1,\r\n        auditStatus: 2,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          console.log(res);\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          console.log(res);\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.data.forEach((item) => {\r\n            item.scenePicture = item.scenePicture\r\n              ? JSON.parse(item.scenePicture)\r\n              : [];\r\n            item.applicationArea = item.applicationArea\r\n              ? item.applicationArea.split(\",\")\r\n              : \"\";\r\n          });\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n    // 跳转到详情页面\r\n    goDemandDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/demandHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.demand-hall-container {\r\n  width: 100%;\r\n  .purchase-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .demand-hall-title-content {\r\n    width: 100%;\r\n    padding-bottom: 18px;\r\n    .demand-hall-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .demand-hall-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .demand-hall-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .demand-hall-search-box {\r\n      .demand-hall-search-form {\r\n        text-align: center;\r\n        .demand-hall-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .demand-hall-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .demand-hall-card {\r\n    background: #f4f5f9;\r\n    padding-top: 40px;\r\n    .demand-hall-info-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      .demand-hall-search-type-box {\r\n        background: #fff;\r\n        margin-bottom: 17px;\r\n        .demand-hall-search-line {\r\n          padding: 14px 24px 4px;\r\n          .demand-hall-search-line-item {\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n      }\r\n      .demand-hall-list-item {\r\n        width: 100%;\r\n        background: #fff;\r\n        border-radius: 12px;\r\n        .list-item-content {\r\n          display: flex;\r\n          padding: 24px;\r\n          cursor: pointer;\r\n          .list-item-img {\r\n            width: 180px;\r\n            height: 128px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n              border-radius: 7px;\r\n            }\r\n          }\r\n          .list-item-info {\r\n            padding-left: 24px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            .list-item-title {\r\n              width: 922px;\r\n              height: 24px;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              font-size: 20px;\r\n              font-weight: 500;\r\n              color: #323233;\r\n              line-height: 20px;\r\n              margin-bottom: 26px;\r\n              word-wrap: break-word;\r\n            }\r\n            .list-item-text {\r\n              display: flex;\r\n              align-items: top;\r\n              .list-item-label {\r\n                color: #323233;\r\n                line-height: 14px;\r\n                margin-top: 14px;\r\n              }\r\n              .list-item-tag-box {\r\n                display: flex;\r\n                width: 852px;\r\n                flex-wrap: wrap;\r\n                .lilst-item-tag {\r\n                  padding: 0 12px;\r\n                  max-width: 840px;\r\n                  background: #21c9b8 1a;\r\n                  border-radius: 4px;\r\n                  font-size: 12px;\r\n                  color: #21c9b8;\r\n                  line-height: 24px;\r\n                  text-align: center;\r\n                  margin-right: 16px;\r\n                  margin-top: 10px;\r\n                  word-wrap: break-word;\r\n                  text-align: left;\r\n                }\r\n              }\r\n            }\r\n          }\r\n          &:hover {\r\n            .list-item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        & + .demand-hall-list-item {\r\n          margin-top: 24px;\r\n        }\r\n      }\r\n      .demand-hall-page-end {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        padding: 24px 0 60px;\r\n        .demand-hall-page-btn {\r\n          width: 82px;\r\n          height: 32px;\r\n          background: #fff;\r\n          border-radius: 4px;\r\n          border: 1px solid #d9d9d9;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.demand-hall-container {\r\n  .demand-hall-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .demand-hall-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .demand-hall-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .demand-hall-page-end {\r\n    .demand-hall-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AA8HA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAC,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAI,SAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;QACAC,IAAA;MACA;MACAC,QAAA;QACAC,UAAA;MACA;MACAC,UAAA;MAAA;MACAN,IAAA;MACAO,OAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,YAAA;IACA,KAAAC,MAAA;EACA;EACAC,OAAA;IACAD,MAAA,WAAAA,OAAA;MAAA,IAAAE,KAAA;MACA,KAAAb,OAAA;MACA,IAAAc,mCAAA,MAAAC,cAAA,CAAAjB,OAAA,MAAAiB,cAAA,CAAAjB,OAAA,MAAAiB,cAAA,CAAAjB,OAAA,MACA,KAAAG,IAAA,GACA,KAAAE,QAAA;QACAa,aAAA;QACAC,WAAA;QACAX,OAAA,OAAAA;QACA;MAAA,EACA,EACAY,IAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACA,IAAAG,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAA/B,SAAA;QACA,IAAAgC,OAAA,GAAAJ,iBAAA,CAAAK,GAAA,CAAAD,OAAA,CAAAR,GAAA,EAAAG,GAAA;UACAO,IAAA,EAAAN,iBAAA,CAAAM,IAAA,CAAAC,GAAA;UACAC,OAAA,EAAAR,iBAAA,CAAAS,GAAA,CAAAC;QACA;QACAd,GAAA,GAAAe,IAAA,CAAAR,KAAA,CAAAH,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAU,SAAA,CAAAR,OAAA;QACAP,OAAA,CAAAC,GAAA,CAAAF,GAAA;QACAN,KAAA,CAAAb,OAAA;QACA,IAAAoC,IAAA,GAAAjB,GAAA;UAAAkB,IAAA,GAAAD,IAAA,CAAAC,IAAA;UAAA7B,KAAA,GAAA4B,IAAA,CAAA5B,KAAA;QACAK,KAAA,CAAAd,IAAA,GAAAsC,IAAA;QACAxB,KAAA,CAAAd,IAAA,CAAAuC,OAAA,WAAAC,IAAA;UACAA,IAAA,CAAAC,YAAA,GAAAD,IAAA,CAAAC,YAAA,GACAN,IAAA,CAAAR,KAAA,CAAAa,IAAA,CAAAC,YAAA,IACA;UACAD,IAAA,CAAAE,eAAA,GAAAF,IAAA,CAAAE,eAAA,GACAF,IAAA,CAAAE,eAAA,CAAAC,KAAA,QACA;QACA;QACA7B,KAAA,CAAAL,KAAA,GAAAA,KAAA;MACA,GACAmC,KAAA;QACA9B,KAAA,CAAAb,OAAA;MACA;IACA;IACA;IACAU,YAAA,WAAAA,aAAAkC,IAAA,EAAAC,YAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,cAAA,EAAAH,IAAA,EAAA1B,IAAA,WAAAC,GAAA;QACA2B,MAAA,CAAAD,YAAA,IAAA1B,GAAA,CAAApB,IAAA;MACA;IACA;IACAiD,WAAA,WAAAA,YAAA;MACA,KAAAC,QAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA3C,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAA0C,QAAA;IACA;IACAE,mBAAA,WAAAA,oBAAA7C,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAK,MAAA;IACA;IACAsC,QAAA,WAAAA,SAAA;MACA,KAAA3C,OAAA;MACA,KAAAK,MAAA;IACA;IACA;IACAyC,cAAA,WAAAA,eAAAC,EAAA;MACA,IAAAC,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACAC,KAAA;UAAAL,EAAA,EAAAA;QAAA;MACA;MACAM,MAAA,CAAAC,IAAA,CAAAN,SAAA,CAAAO,IAAA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAP,OAAA,CAAAQ,IAAA;QAAAN,IAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}