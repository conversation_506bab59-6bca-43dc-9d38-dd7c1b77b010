<template>
  <div class="content">
    <div class="content_banner">
      人才库
      <div class="imgContent">
        <div class="imgStyle">
          <img style="width: 100%; height: 100%" src="../../../../assets/order/orderStep.png" alt="" />
        </div>
      </div>
    </div>
    <div class="card-container content_card">
      <el-form ref="form" :rules="rules" :model="form" label-position="top">
        <el-row :gutter="50">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出生年月">
              <el-date-picker v-model="form.birthDate" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在地">
              <el-input v-model="form.location" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="毕业院校">
              <el-input v-model="form.graduateSchool" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最高学历" prop="education">
              <el-select v-model="form.education" placeholder="请选择最高学历" clearable style="width: 100%">
                <el-option v-for="dict in educationList" :key="dict.dictLabel" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所在单位">
              <el-input v-model="form.currentCompany" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作状态" prop='workStatus'>
              <el-radio-group v-model="form.workStatus">
                <el-radio v-for="dict in workStatusList" :key="dict.dictValue" :label="dict.dictValue"
                  :value="dict.dictValue">{{ dict.dictLabel }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职务">
              <el-input v-model="form.position" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职称" prop="jobTitle">
              <el-select v-model="form.jobTitle" placeholder="请选择职称" clearable style="width: 100%">
                <el-option v-for="dict in jobTitleList" :key="dict.dictLabel" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="技术领域">
              <el-input v-model="form.skills" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位分类" prop="positionType">
              <el-select v-model="form.positionType" placeholder="请选择岗位分类" clearable style="width: 100%">
                <el-option v-for="dict in positionTypeList" :key="dict.dictLabel" :label="dict.dictLabel"
                  :value="dict.dictValue" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="个人简介">
              <el-input v-model="form.workExperience" type="textarea" resize="none" :rows="8" maxlength="500"
                show-word-limit placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="个人照片">
              <ImageUpload :limit="1" v-model="photoList" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="附件">
              <FileUpload v-model="form.resumeFile" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="footer-submit">
              <el-button type="primary" @click="onSubmit">提交</el-button>
              <el-button style="margin-left: 140px" @click.once="onCancel">取消</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { talentAdd, talentEdit, talentDetailData } from "@/api/serviceSharing";
import { getInfo } from "@/api/login";

export default {
  data() {
    return {
      id: "",
      photoList: [],
      form: {
        name: "",
        contactPhone: "",
        birthDate: "",
        location: "",
        graduateSchool: "",
        education: "",
        currentCompany: "",
        workStatus: "",
        position: "",
        jobTitle: "",
        skills: "",
        positionType: "", // 岗位分类
        workExperience: "", // 个人简介
        photo: "", // 照片
        resumeFile: "", // 简历附件
        settledStatus: "0",
      },
      rules: {
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        positionType: [
          { required: true, message: "岗位分类不能为空", trigger: "change" },
        ],
        education: [
          { required: true, message: "最高学历不能为空", trigger: "blur" },
        ],
        jobTitle: [
          { required: true, message: "职称不能为空", trigger: "blur" },
        ],
        workStatus: [
          { required: true, message: "工作状态不能为空", trigger: "change" },
        ],
      },
      positionTypeList: [], // 岗位分类
      educationList: [], // 最高学历
      jobTitleList: [], // 职称
      workStatusList: [], // 工作状态
      talentInfo: {},
    };
  },
  created() {
    this.getPositionType();
    this.getEducation();
    this.getJobTitle();
    this.getWorkStatus();
    this.getInfo()
  },
  methods: {
    // 获取用户信息
    getInfo() {
      getInfo().then((res) => {
        if (res.code == 200) {
          this.talentInfo = res.talentInfo;
          if (this.talentInfo.id) {
            this.id = this.talentInfo.id;
            this.getTalentInfo()
          }
        }
      });
    },
    getTalentInfo() {
      talentDetailData(this.id).then((res) => {
        this.form = res.data;
        this.photoList = res.data.photo ? [res.data.photo] : [];
      })
    },
    // 岗位分类
    getPositionType() {
      let params = { dictType: "position_type" };
      listData(params).then((response) => {
        this.positionTypeList = response.rows;
      });
    },
    // 最高学历
    getEducation() {
      let params = { dictType: "education" };
      listData(params).then((response) => {
        this.educationList = response.rows;
      });
    },
    // 职称
    getJobTitle() {
      let params = { dictType: "job_title" };
      listData(params).then((response) => {
        this.jobTitleList = response.rows;
      });
    },
    // 工作状态
    getWorkStatus() {
      let params = { dictType: "work_status" };
      listData(params).then((response) => {
        this.workStatusList = response.rows;
      });
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.photo =
            this.photoList && this.photoList.length > 0
              ? this.photoList[0].url
              : "";
          // 每次提交将审核设置为待审核
          this.form.settledStatus = 0;
          if (this.id) {
            talentEdit(this.form).then((res) => {
              if (res.code === 200) {
                this.$message.success("操作成功");
                this.$router.go(-1);
              }
            });
          } else {
            talentAdd(this.form).then((res) => {
              if (res.code === 200) {
                this.$message.success("操作成功");
                this.$router.go(-1);
              }
            });
          }
        }
      });
    },
    onCancel() { },
  },
};
</script>
<style scoped lang="scss">
.content {
  width: 100%;
  padding-bottom: 60px;
  background-color: #f2f2f2;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  padding-top: 28px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;

  .imgContent {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 10px;

    .imgStyle {
      width: 1256px;
      height: 206px;
      position: relative;
    }
  }
}

.content_card {
  // height: 1530px;
  background: #ffffff;
  border-radius: 2px;
  margin-top: 30px;
  padding: 59px 60px 57px 60px;
}

.addStyle {
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #21c9b8;
  margin-left: auto;
  cursor: pointer;
}

.footer-submit {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 60px;

  .el-button {
    width: 140px;
    height: 50px;
  }
}
</style>
