{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\register.vue", "mtime": 1750311962994}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"register\">\r\n    <el-form\r\n      ref=\"registerForm\"\r\n      :model=\"registerForm\"\r\n      :rules=\"registerRules\"\r\n      class=\"register-form\"\r\n    >\r\n      <h3 class=\"title\">若依后台管理系统</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"registerForm.username\"\r\n          type=\"text\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"user\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"registerForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"password\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"confirmPassword\">\r\n        <el-input\r\n          v-model=\"registerForm.confirmPassword\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"确认密码\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"password\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\r\n        <el-input\r\n          v-model=\"registerForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"validCode\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n        <div class=\"register-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\" />\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item style=\"width: 100%\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width: 100%\"\r\n          @click.native.prevent=\"handleRegister\"\r\n        >\r\n          <span v-if=\"!loading\">注 册</span>\r\n          <span v-else>注 册 中...</span>\r\n        </el-button>\r\n        <div style=\"float: right\">\r\n          <router-link class=\"link-type\" :to=\"'/login'\"\r\n            >使用已有账户登录</router-link\r\n          >\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-register-footer\">\r\n      <span>Copyright © 2018-2022 ruoyi.vip All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg, register } from \"@/api/login\";\r\n\r\nexport default {\r\n  name: \"Register\",\r\n  data() {\r\n    const equalToPassword = (rule, value, callback) => {\r\n      if (this.registerForm.password !== value) {\r\n        callback(new Error(\"两次输入的密码不一致\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      codeUrl: \"\",\r\n      registerForm: {\r\n        username: \"\",\r\n        password: \"\",\r\n        confirmPassword: \"\",\r\n        code: \"\",\r\n        uuid: \"\",\r\n      },\r\n      registerRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\r\n          {\r\n            min: 2,\r\n            max: 20,\r\n            message: \"用户账号长度必须介于 2 和 20 之间\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\r\n          {\r\n            min: 5,\r\n            max: 20,\r\n            message: \"用户密码长度必须介于 5 和 20 之间\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        confirmPassword: [\r\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\r\n          { required: true, validator: equalToPassword, trigger: \"blur\" },\r\n        ],\r\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }],\r\n      },\r\n      loading: false,\r\n      captchaEnabled: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.getCode();\r\n  },\r\n  methods: {\r\n    getCode() {\r\n      getCodeImg().then((res) => {\r\n        this.captchaEnabled =\r\n          res.captchaEnabled === undefined ? true : res.captchaEnabled;\r\n        if (this.captchaEnabled) {\r\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n          this.registerForm.uuid = res.uuid;\r\n        }\r\n      });\r\n    },\r\n    handleRegister() {\r\n      this.$refs.registerForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          register(this.registerForm)\r\n            .then((res) => {\r\n              const username = this.registerForm.username;\r\n              this.$alert(\r\n                \"<font color='red'>恭喜你，您的账号 \" +\r\n                  username +\r\n                  \" 注册成功！</font>\",\r\n                \"系统提示\",\r\n                {\r\n                  dangerouslyUseHTMLString: true,\r\n                  type: \"success\",\r\n                }\r\n              )\r\n                .then(() => {\r\n                  this.$router.push(\"/login\");\r\n                })\r\n                .catch(() => {});\r\n            })\r\n            .catch(() => {\r\n              this.loading = false;\r\n              if (this.captchaEnabled) {\r\n                this.getCode();\r\n              }\r\n            });\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.register {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.register-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.register-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.register-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-register-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.register-code-img {\r\n  height: 38px;\r\n}\r\n</style>\r\n"]}]}