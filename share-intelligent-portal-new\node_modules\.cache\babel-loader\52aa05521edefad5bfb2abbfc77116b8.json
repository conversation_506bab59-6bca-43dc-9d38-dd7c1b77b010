{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\scene\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\scene\\index.vue", "mtime": 1750311963004}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ramda", "require", "_scene", "name", "data", "loading", "form", "title", "undefined", "items", "pageNum", "pageSize", "total", "created", "search", "methods", "_this", "getListByText", "_objectSpread2", "default", "then", "res", "_ref", "rows", "console", "log", "map", "_ref2", "id", "simpleContent", "updateTime", "_ref2$coverPictureLis", "coverPictureList", "image", "head", "content", "src", "url", "finally", "handleSizeChange", "onSearch", "handleCurrentChange", "goNoticDetail", "routeData", "$router", "resolve", "path", "query", "window", "open", "href", "goHome", "push"], "sources": ["src/views/scene/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"scene-container\">\r\n    <div class=\"scene-banner\">\r\n      <img\r\n        src=\"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/1676017632639145.webp\"\r\n        alt=\"场景发布\"\r\n      />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"scene-title-content\">\r\n        <div class=\"scene-title-box\">\r\n          <div class=\"scene-divider\"></div>\r\n          <div class=\"scene-title\">场景发布</div>\r\n          <div class=\"scene-divider\"></div>\r\n        </div>\r\n        <div class=\"scene-search-box\">\r\n          <el-form ref=\"form\" class=\"scene-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.title\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"scene-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"scene-search-btn\"\r\n                  @click=\"search\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"scene-info-content\">\r\n        <template v-if=\"items.length > 0\">\r\n          <div\r\n            v-for=\"(item, index) in items\"\r\n            :key=\"index\"\r\n            class=\"scene-list-item\"\r\n            @click=\"goNoticDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <!-- <el-image class=\"list-item-img\" fit=\"fill\" :alt=\"item.title\" :src=\"item.src\" /> -->\r\n              <div class=\"list-item-info\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.title }}\r\n                </div>\r\n                <div class=\"list-item-text\">\r\n                  {{ item.content }}\r\n                </div>\r\n                <div class=\"list-item-time\">{{ item.updateTime }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-else>\r\n          <el-empty />\r\n        </template>\r\n        <div class=\"scene-page-end\">\r\n          <el-button class=\"scene-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"items && items.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"scene-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { head, map } from \"ramda\";\r\nimport { getListByText } from \"@/api/scene\";\r\n\r\nexport default {\r\n  name: \"ScenePage\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        title: undefined, //搜索内容\r\n      },\r\n      items: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getListByText({\r\n        ...this.form,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          console.log(\"rows\", rows);\r\n          this.items = map(\r\n            ({\r\n              id,\r\n              title,\r\n              simpleContent,\r\n              updateTime,\r\n              coverPictureList = [],\r\n            }) => {\r\n              const image = head(coverPictureList || []) || {};\r\n              return {\r\n                id,\r\n                title,\r\n                content: simpleContent,\r\n                updateTime,\r\n                src: image.url,\r\n              };\r\n            },\r\n            rows\r\n          );\r\n          this.total = total;\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.pageNum = 1;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    // 跳转到详情页面\r\n    goNoticDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/scenarioDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.scene-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .scene-banner {\r\n    width: 100%;\r\n    height: 540px;\r\n    img {\r\n      width: 100%;\r\n      height: 540px;\r\n      object-fit: fill;\r\n    }\r\n  }\r\n  .scene-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    padding-bottom: 18px;\r\n    .scene-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .scene-title {\r\n        font-size: 40px;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .scene-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .scene-search-box {\r\n      .scene-search-form {\r\n        text-align: center;\r\n        .scene-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .scene-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .scene-info-content {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    .scene-list-item {\r\n      width: 100%;\r\n      background: #fff;\r\n      border-radius: 12px;\r\n      margin-top: 24px;\r\n      .list-item-content {\r\n        display: flex;\r\n        padding: 24px 32px;\r\n        cursor: pointer;\r\n        .list-item-img {\r\n          width: 298px;\r\n          height: 212px;\r\n          border-radius: 7px;\r\n          overflow: hidden;\r\n        }\r\n        .list-item-info {\r\n          padding-left: 24px;\r\n          .list-item-title {\r\n            width: 806px;\r\n            height: 24px;\r\n            text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n            white-space: nowrap; /*让文字不换行*/\r\n            overflow: hidden; /*超出要隐藏*/\r\n            font-size: 24px;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            line-height: 24px;\r\n            margin: 8px 0 24px;\r\n          }\r\n          .list-item-text {\r\n            width: 806px;\r\n            height: 90px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 3;\r\n            text-overflow: ellipsis;\r\n            font-size: 16px;\r\n            color: #666;\r\n            line-height: 30px;\r\n          }\r\n          .list-item-time {\r\n            color: #999;\r\n            line-height: 14px;\r\n            margin-top: 52px;\r\n          }\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .scene-page-end {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin: 0 auto;\r\n      padding: 24px 0 60px;\r\n      .scene-page-btn {\r\n        width: 82px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        color: #333;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.scene-container {\r\n  .scene-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .scene-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .scene-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .scene-page-end {\r\n    .scene-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;AAgFA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;QACAC,KAAA,EAAAC,SAAA;MACA;MACAC,KAAA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,MAAA;EACA;EACAC,OAAA;IACAD,MAAA,WAAAA,OAAA;MAAA,IAAAE,KAAA;MACA,KAAAX,OAAA;MACA,IAAAY,oBAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,KAAAb,IAAA;QACAI,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA;MAAA,EACA,EACAS,IAAA,WAAAC,GAAA;QACAL,KAAA,CAAAX,OAAA;QACA,IAAAiB,IAAA,GAAAD,GAAA;UAAAE,IAAA,GAAAD,IAAA,CAAAC,IAAA;UAAAX,KAAA,GAAAU,IAAA,CAAAV,KAAA;QACAY,OAAA,CAAAC,GAAA,SAAAF,IAAA;QACAP,KAAA,CAAAP,KAAA,OAAAiB,UAAA,EACA,UAAAC,KAAA,EAMA;UAAA,IALAC,EAAA,GAAAD,KAAA,CAAAC,EAAA;YACArB,KAAA,GAAAoB,KAAA,CAAApB,KAAA;YACAsB,aAAA,GAAAF,KAAA,CAAAE,aAAA;YACAC,UAAA,GAAAH,KAAA,CAAAG,UAAA;YAAAC,qBAAA,GAAAJ,KAAA,CACAK,gBAAA;YAAAA,gBAAA,GAAAD,qBAAA,mBAAAA,qBAAA;UAEA,IAAAE,KAAA,OAAAC,WAAA,EAAAF,gBAAA;UACA;YACAJ,EAAA,EAAAA,EAAA;YACArB,KAAA,EAAAA,KAAA;YACA4B,OAAA,EAAAN,aAAA;YACAC,UAAA,EAAAA,UAAA;YACAM,GAAA,EAAAH,KAAA,CAAAI;UACA;QACA,GACAd,IACA;QACAP,KAAA,CAAAJ,KAAA,GAAAA,KAAA;MACA,GACA0B,OAAA;QACAtB,KAAA,CAAAX,OAAA;MACA;IACA;IACAkC,gBAAA,WAAAA,iBAAA5B,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAD,OAAA;MACA,KAAA8B,QAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA/B,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAI,MAAA;IACA;IACA;IACA4B,aAAA,WAAAA,cAAAd,EAAA;MACA,IAAAe,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACAC,KAAA;UAAAnB,EAAA,EAAAA;QAAA;MACA;MACAoB,MAAA,CAAAC,IAAA,CAAAN,SAAA,CAAAO,IAAA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAP,OAAA,CAAAQ,IAAA;QAAAN,IAAA;MAAA;IACA;EACA;AACA", "ignoreList": []}]}