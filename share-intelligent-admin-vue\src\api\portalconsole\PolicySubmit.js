import request from '@/utils/request'

// 查询政策申报列表
export function listPolicySubmit(query) {
  return request({
    url: '/portalconsole/PolicySubmit/list',
    method: 'get',
    params: query
  })
}

// 查询政策申报详细
export function getPolicySubmit(policySubmitId) {
  return request({
    url: '/portalconsole/PolicySubmit/' + policySubmitId,
    method: 'get'
  })
}

// 新增政策申报
export function addPolicySubmit(data) {
  return request({
    url: '/portalconsole/PolicySubmit',
    method: 'post',
    data: data
  })
}

// 修改政策申报
export function updatePolicySubmit(data) {
  return request({
    url: '/portalconsole/PolicySubmit/audit',
    method: 'put',
    data: data
  })
}

// 删除政策申报
export function delPolicySubmit(policySubmitId) {
  return request({
    url: '/portalconsole/PolicySubmit/' + policySubmitId,
    method: 'delete'
  })
}
//查询详情页面的提报信息
export function getInfo(query) {
  return request({
    url: '/portalconsole/PolicySubmitRecord/list',
    method: 'get',
    params: query
  })
}

