{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\resource\\labDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\resource\\labDetail.vue", "mtime": 1750311963002}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_purchaseSales", "require", "_login", "_apathy", "_vuex", "data", "loading", "showBtn", "created", "init", "methods", "_this", "laboratoryDetail", "$route", "query", "id", "then", "res", "token", "getInfo", "catch", "_this2", "createById", "user", "userId", "goChat", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "$store", "dispatch", "location", "href", "routeData", "$router", "resolve", "path", "createImById", "window", "open", "goIntention", "_this4", "getCompanyInfoByLoginInfo", "getCheckSubmit", "resourceType", "$message", "message", "title", "supplyName", "computed", "_objectSpread2", "default", "mapGetters"], "sources": ["src/views/resource/labDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"resource-hall-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"resource-hall-detail-banner\">\r\n      <img\r\n        src=\"../../assets/resourceHall/resourceHallDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"resource-hall-detail-title-box\">\r\n      <div class=\"resource-hall-detail-divider\"></div>\r\n      <div class=\"resource-hall-detail-title\">实验室详情</div>\r\n      <div class=\"resource-hall-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"resource-hall-detail-content\">\r\n      <div class=\"resource-hall-detail-box\">\r\n        <div class=\"resource-hall-detail-box-title\">\r\n          {{ data.name }}\r\n        </div>\r\n        <div class=\"resource-hall-detail-headline\">\r\n          <div class=\"headline-content\">\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">所属单位：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.institution }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">行业领域：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.industry }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">所属区域：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.province }}{{ data.city ? \" \" + data.city : \"\"\r\n                }}{{ data.district ? \" \" + data.district : \"\" }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-btn\">\r\n              <el-button\r\n                v-if=\"showBtn\"\r\n                class=\"headline-btn-style intention-btn\"\r\n                @click=\"goIntention\"\r\n                >我有意向</el-button\r\n              >\r\n              <el-button\r\n                @click=\"goChat\"\r\n                class=\"headline-btn-style communication-btn\"\r\n                icon=\"el-icon-chat-dot-round\"\r\n                >在线沟通</el-button\r\n              >\r\n            </div>\r\n          </div>\r\n          <div class=\"headline-img\">\r\n            <img v-if=\"data.picUrl\" :src=\"data.picUrl\" alt=\"\" />\r\n            <img\r\n              v-else\r\n              src=\"../../assets/resourceHall/resourceHallDetailBanner.png\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"resource-hall-detail-description\">\r\n          <div class=\"description-title-box\">\r\n            <div class=\"description-divider\"></div>\r\n            <div class=\"description-title\">成果描述</div>\r\n          </div>\r\n          <div class=\"description-content\">\r\n            <div v-html=\"data.detail\" class=\"description-text ql-editor\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { laboratoryDetail, getCheckSubmit } from \"@/api/purchaseSales\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { getCompanyInfoByLoginInfo } from \"@/api/apathy\";\r\nimport { mapGetters } from \"vuex\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n      showBtn: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      laboratoryDetail(this.$route.query.id)\r\n        .then((res) => {\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          if (!this.token) {\r\n            this.showBtn = true;\r\n          } else {\r\n            this.getInfo();\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 判断此资源是不是自己发布的\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (this.data.createById === res.user.userId) {\r\n          this.showBtn = false;\r\n        } else {\r\n          this.showBtn = true;\r\n        }\r\n      });\r\n    },\r\n    goChat() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      let routeData = this.$router.resolve({\r\n        path: \"/user/im\",\r\n        query: {\r\n          userId: this.data.createImById,\r\n        },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳到我有意向页面\r\n    goIntention() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      // 是否加入企业\r\n      getCompanyInfoByLoginInfo()\r\n        .then((res) => {\r\n          if (res.data) {\r\n            // 是否对此资源提交过意向\r\n            getCheckSubmit({\r\n              id: this.$route.query.id,\r\n              resourceType: \"resource_supply\",\r\n            })\r\n              .then((res) => {\r\n                this.loading = false;\r\n                // true 提交过  false未提交过\r\n                if (res.data) {\r\n                  this.$message({\r\n                    type: \"warning\",\r\n                    message: \"已经提交过了哦！\",\r\n                  });\r\n                } else {\r\n                  let routeData = this.$router.resolve({\r\n                    path: \"/addIntention\",\r\n                    query: {\r\n                      id: this.$route.query.id,\r\n                      type: \"resource_supply\",\r\n                      title: this.data.supplyName,\r\n                    },\r\n                  });\r\n                  window.open(routeData.href, \"_blank\");\r\n                }\r\n              })\r\n              .catch(() => {\r\n                this.loading = false;\r\n              });\r\n          } else {\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"warning\",\r\n              message: \"必须加入企业才可提交我有意向\",\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.resource-hall-detail-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .resource-hall-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .resource-hall-detail-title-box {\r\n    width: 380px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .resource-hall-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .resource-hall-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .resource-hall-detail-content {\r\n    background: #f4f5f9;\r\n    padding-bottom: 70px;\r\n    .resource-hall-detail-box {\r\n      width: 1200px;\r\n      background: #fff;\r\n      margin: 0 auto;\r\n      padding: 60px 60px 192px;\r\n      .resource-hall-detail-box-title {\r\n        width: 100%;\r\n        font-size: 32px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        word-wrap: break-word;\r\n      }\r\n      .resource-hall-detail-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-top: 40px;\r\n        padding-bottom: 40px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        .headline-content {\r\n          flex: 1;\r\n          .headline-content-item {\r\n            display: flex;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            line-height: 32px;\r\n            .item-title {\r\n              width: 80px;\r\n              color: #666;\r\n            }\r\n            .item-content {\r\n              flex: 1;\r\n              max-width: 560px;\r\n              color: #333;\r\n              word-wrap: break-word;\r\n            }\r\n          }\r\n          .headline-content-btn {\r\n            padding-top: 112px;\r\n            .headline-btn-style {\r\n              width: 100px;\r\n              height: 32px;\r\n              border-radius: 4px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              padding: 8px 11px;\r\n            }\r\n            .intention-btn {\r\n              background: #21c9b8;\r\n              color: #fff;\r\n            }\r\n            .communication-btn {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        .headline-img {\r\n          width: 400px;\r\n          height: 240px;\r\n          margin-left: 20px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .resource-hall-detail-description {\r\n      padding-top: 39px;\r\n      .description-title-box {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-bottom: 40px;\r\n        .description-divider {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .description-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n      .description-content {\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.resource-hall-detail-container {\r\n  .description-content {\r\n    .description-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AA+EA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAD,IAAA;MACAE,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,IAAA,WAAAA,KAAA;MAAA,IAAAE,KAAA;MACA,KAAAL,OAAA;MACA,IAAAM,+BAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,EAAA,EACAC,IAAA,WAAAC,GAAA;QACAN,KAAA,CAAAL,OAAA;QACAK,KAAA,CAAAN,IAAA,GAAAY,GAAA,CAAAZ,IAAA;QACA,KAAAM,KAAA,CAAAO,KAAA;UACAP,KAAA,CAAAJ,OAAA;QACA;UACAI,KAAA,CAAAQ,OAAA;QACA;MACA,GACAC,KAAA;QACAT,KAAA,CAAAL,OAAA;MACA;IACA;IACA;IACAa,OAAA,WAAAA,QAAA;MAAA,IAAAE,MAAA;MACA,IAAAF,cAAA,IAAAH,IAAA,WAAAC,GAAA;QACA,IAAAI,MAAA,CAAAhB,IAAA,CAAAiB,UAAA,KAAAL,GAAA,CAAAM,IAAA,CAAAC,MAAA;UACAH,MAAA,CAAAd,OAAA;QACA;UACAc,MAAA,CAAAd,OAAA;QACA;MACA;IACA;IACAkB,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAR,KAAA;QACA,KAAAS,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAAd,IAAA;UACAU,MAAA,CAAAK,MAAA,CAAAC,QAAA,WAAAhB,IAAA;YACAiB,QAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;MACA,IAAAC,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACAxB,KAAA;UACAU,MAAA,OAAAnB,IAAA,CAAAkC;QACA;MACA;MACAC,MAAA,CAAAC,IAAA,CAAAN,SAAA,CAAAD,IAAA;IACA;IACA;IACAQ,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAzB,KAAA;QACA,KAAAS,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAAd,IAAA;UACA2B,MAAA,CAAAZ,MAAA,CAAAC,QAAA,WAAAhB,IAAA;YACAiB,QAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;MACA,KAAA5B,OAAA;MACA;MACA,IAAAsC,iCAAA,IACA5B,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAZ,IAAA;UACA;UACA,IAAAwC,6BAAA;YACA9B,EAAA,EAAA4B,MAAA,CAAA9B,MAAA,CAAAC,KAAA,CAAAC,EAAA;YACA+B,YAAA;UACA,GACA9B,IAAA,WAAAC,GAAA;YACA0B,MAAA,CAAArC,OAAA;YACA;YACA,IAAAW,GAAA,CAAAZ,IAAA;cACAsC,MAAA,CAAAI,QAAA;gBACAjB,IAAA;gBACAkB,OAAA;cACA;YACA;cACA,IAAAb,SAAA,GAAAQ,MAAA,CAAAP,OAAA,CAAAC,OAAA;gBACAC,IAAA;gBACAxB,KAAA;kBACAC,EAAA,EAAA4B,MAAA,CAAA9B,MAAA,CAAAC,KAAA,CAAAC,EAAA;kBACAe,IAAA;kBACAmB,KAAA,EAAAN,MAAA,CAAAtC,IAAA,CAAA6C;gBACA;cACA;cACAV,MAAA,CAAAC,IAAA,CAAAN,SAAA,CAAAD,IAAA;YACA;UACA,GACAd,KAAA;YACAuB,MAAA,CAAArC,OAAA;UACA;QACA;UACAqC,MAAA,CAAArC,OAAA;UACAqC,MAAA,CAAAI,QAAA;YACAjB,IAAA;YACAkB,OAAA;UACA;QACA;MACA,GACA5B,KAAA;QACAuB,MAAA,CAAArC,OAAA;MACA;IACA;EACA;EACA6C,QAAA,MAAAC,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA;AAEA", "ignoreList": []}]}