{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\dockingRecords\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\dockingRecords\\detail.vue", "mtime": 1750311963054}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLm9iamVjdC5rZXlzLmpzIik7CnZhciBfdXNlck1lbnUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4uL2NvbXBvbmVudHMvdXNlck1lbnUudnVlIikpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogIlVzZXIiLAogIGNvbXBvbmVudHM6IHsKICAgIFVzZXJNZW51OiBfdXNlck1lbnUuZGVmYXVsdAogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGlkOiAxLAogICAgICBjb250ZW50OiB7fSwKICAgICAgdXNlcmluZm86IHt9CiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5pZCkgewogICAgICB0aGlzLmlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7CiAgICAgIHRoaXMuY29udGVudCA9IHRoaXMuJHJvdXRlLnF1ZXJ5OwogICAgICBjb25zb2xlLmxvZyh0aGlzLmNvbnRlbnQpOwogICAgfQogICAgdGhpcy51c2VyaW5mbyA9IEpTT04ucGFyc2Uoc2Vzc2lvblN0b3JhZ2UuZ2V0SXRlbSgidXNlcmluZm8iKSk7CiAgfSwKICBtZXRob2RzOiB7CiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "name", "components", "UserMenu", "data", "id", "content", "userinfo", "created", "$route", "query", "console", "log", "JSON", "parse", "sessionStorage", "getItem", "methods", "cancel", "$router", "go"], "sources": ["src/views/system/user/dockingRecords/detail.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-row :gutter=\"20\">\r\n            <el-col :span=\"2.5\" :xs=\"24\">\r\n                <user-menu activeIndex=\"1\" />\r\n            </el-col>\r\n            <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n                <div class=\"top\">\r\n                    <div class=\"content_title\">\r\n                        <div class=\"icon\"></div>\r\n                        <div class=\"title\">我的对接记录</div>\r\n                        <div class=\"buttonStyle\" @click=\"cancel\">返回</div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"content\">\r\n                    <p>意向提交时间：{{ this.content.createTime }}</p>\r\n                    <p>资源类型：{{ this.content.intentionTypeName }}</p>\r\n                    <p v-if=\"this.content.title\">标题：{{ this.content.title }}</p>\r\n                    <p>意向描述：{{ this.content.intentionContent }}</p>\r\n                    <p v-if=\"this.content.completionDate\">完成日期：{{ this.content.completionDate }}</p>\r\n                    <p v-if=\"this.content.quantity\">承接量：{{ this.content.quantity }}</p>\r\n                    <p v-if=\"this.content.price\">含税单价：{{ this.content.price }}</p>\r\n                    <p v-if=\"this.content.rate\">税率：{{ this.content.rate }}</p>\r\n                    <p v-if=\"this.content.shippingFee\">运费：{{ this.content.shippingFee }}</p>\r\n                    <p v-if=\"this.content.sum\">含税合计：{{ this.content.sum }}</p>\r\n                    <p>联系人：{{ this.userinfo.memberRealName }}</p>\r\n                    <p>联系电话：{{ this.userinfo.memberPhone }}</p>\r\n                </div>\r\n            </el-col>\r\n        </el-row>\r\n    </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\n\r\nexport default {\r\n    name: \"User\",\r\n    components: { UserMenu },\r\n    data() {\r\n        return {\r\n            id: 1,\r\n            content: {},\r\n            userinfo: {}\r\n        };\r\n    },\r\n    created() {\r\n        if (this.$route.query.id) {\r\n            this.id = this.$route.query.id;\r\n            this.content = this.$route.query;\r\n            console.log(this.content)\r\n        }\r\n        this.userinfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n    },\r\n    methods: {\r\n        cancel() {\r\n            this.$router.go(-1);\r\n        },\r\n    },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n    background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n    height: 1000px;\r\n}\r\n\r\n.top {\r\n    padding: 20px;\r\n    background: #fff;\r\n    border-radius: 10px;\r\n    margin-top: 20px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .content_title {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n\r\n        .icon {\r\n            width: 4px;\r\n            height: 20px;\r\n            background: #21c9b8;\r\n        }\r\n\r\n        .title {\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 18px;\r\n            color: #030a1a;\r\n            margin-left: 10px;\r\n        }\r\n\r\n        .buttonStyle {\r\n            padding: 10px 20px;\r\n            background: #21c9b8;\r\n            color: #fff;\r\n            text-align: center;\r\n            cursor: pointer;\r\n            border-radius: 10px;\r\n            margin-left: auto;\r\n        }\r\n    }\r\n}\r\n\r\n.queryForm {\r\n    padding: 20px;\r\n}\r\n\r\n.content {\r\n    background-color: #fff;\r\n    padding: 20px;\r\n    margin-top: 20px;\r\n    border-radius: 10px;\r\n\r\n    p {\r\n        margin: 10px 0;\r\n    }\r\n}\r\n</style>"], "mappings": ";;;;;;;;AAiCA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,EAAA;MACAC,OAAA;MACAC,QAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAL,EAAA;MACA,KAAAA,EAAA,QAAAI,MAAA,CAAAC,KAAA,CAAAL,EAAA;MACA,KAAAC,OAAA,QAAAG,MAAA,CAAAC,KAAA;MACAC,OAAA,CAAAC,GAAA,MAAAN,OAAA;IACA;IACA,KAAAC,QAAA,GAAAM,IAAA,CAAAC,KAAA,CAAAC,cAAA,CAAAC,OAAA;EACA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;EACA;AACA", "ignoreList": []}]}