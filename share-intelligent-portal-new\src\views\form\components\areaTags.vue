<template>
  <div class="application-area">
    <div class="action">
      <el-input
        class="input-new-tag"
        v-if="inputVisible"
        v-model.trim="inputValue"
        ref="saveTagInput"
        size="small"
        @keyup.enter.native="handleInputConfirm"
        @blur="handleInputConfirm"
      >
      </el-input>
      <div v-else class="button-new-tag" @click="showInput">
        <i class="el-icon-plus"></i>
      </div>
    </div>
    <el-tag
      v-for="tag in dynamicTags"
      :key="`tag${tag}`"
      closable
      :disable-transitions="false"
      @close="handleClose(tag)">
      {{tag}}
    </el-tag>
  </div>
</template>

<script>
import { includes } from 'ramda'
import { getDicts } from '@/api/system/dict/data'

export default {
  name: 'ApplicationAreaDicts',
  data() {
    return {
      dynamicTags: [],
      inputVisible: false,
      inputValue: ''
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.dynamicTags =  Array.isArray(val) ? val : this.value.split(',');
        } else {
          this.dynamicTags = [];
          return [];
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      getDicts('application_area').then(res => {
        const { data = [] } = res;
        if (data && data.length > 0) {
          this.dynamicTags = data.map(item => item.dictLabel)
          this.$emit("input", this.dynamicTags);
        }
      })
    },
    handleClose(tag) {
      this.dynamicTags = this.dynamicTags.filter(item => item !== tag);
      this.$nextTick(() => {
        this.$emit("input", this.formatValue(this.dynamicTags));
      })
    },

    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },

    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue && !this.dynamicTags.includes(inputValue)) {
        this.dynamicTags = [inputValue, ...this.dynamicTags];
        this.$nextTick(() => {
          this.$emit("input", this.dynamicTags);
        })
      } else {
        this.$message.warning("应用领域已存在");
      }
      this.inputVisible = false;
      this.inputValue = '';

    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/mixin.scss";
.application-area {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-shrink: 0;
  flex-wrap: wrap;
  .el-tag {
    margin: 6px;
    &:last-child {
      margin-right: 0;
    }
  }
  .button-new-tag {
    @include flexCenter;
    width: 24px;
    height: 24px;
    border: 1px dotted #999999;
    border-radius: 100%;
    margin-right: 6px;
    cursor: pointer;
    i {
      color: #999999;
      font-size: 16px;
      font-weight: bold;
    }
  }
  .input-new-tag {
    width: 90px;
    margin-right: 6px;
    vertical-align: bottom;
  }
}

</style>
