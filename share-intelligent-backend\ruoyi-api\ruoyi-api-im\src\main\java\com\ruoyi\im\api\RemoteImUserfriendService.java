package com.ruoyi.im.api;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.im.api.domain.ImUserFriend;
import com.ruoyi.im.api.dto.ImUserVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api.util
 * @ClassName: RemoteImUserfriendService
 * @Author: ${maguojun}
 * @Description: ${description}
 * @Date: 2022/3/16 13:49
 * @Version: 1.0
 */
@FeignClient(contextId = "remoteImUserfriendService", value = ServiceNameConstants.IM_SERVICE)
public interface RemoteImUserfriendService {
    /**
    * @Description:
    * @Param:
    * @return:
    * @Author: 查看通讯录好友列表，根据首字母排序
    * @Date:
    */
    @PostMapping(value = "/im/userfriend/list")
    R<Map<String, List<JSONObject>>> userfriendList(@RequestBody ImUserFriend imUserFriend);
    /**
    * @Description:
    * @Param:
    * @return:
    * @Author: 根据手机号和昵称全局搜索用户
    * @Date:
    */
    @PostMapping(value = "/im/userfriend/search")
    R<List<ImUserVO>> findList(@RequestBody(required = false) ImUserFriend imUserFriend, @RequestParam(value = "fields") String fields);
    /**
    * @Description:
    * @Param:
    * @return:
    * @Author: 删除好友
    * @Date:
    */
    @PostMapping(value = "/im/userfriend/delete")
    R<Boolean> delete(@RequestBody ImUserFriend imUserFriend);


    /***
     * 修改ImUserFriend数据
     * @param imUserFriend
     * @return
     */
    @PostMapping(value = "/im/userfriend/update")
    public R<Boolean> update(@RequestBody ImUserFriend imUserFriend);





}
