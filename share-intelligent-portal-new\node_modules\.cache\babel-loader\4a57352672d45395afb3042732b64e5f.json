{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\newsCenter.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\newsCenter.vue", "mtime": 1750311962934}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zbGljZS5qcyIpOwp2YXIgX25ld3NDZW50ZXIgPSByZXF1aXJlKCJAL2FwaS9uZXdzQ2VudGVyIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGRhdGE6IFtdLAogICAgICBwYWdlTnVtOiAxLAogICAgICBwYWdlU2l6ZTogNCwKICAgICAgdG90YWw6IDAsCiAgICAgIGZsYWc6ICIxIiwKICAgICAgbmV3c0ZpcnN0RGF0YToge30sCiAgICAgIG5ld3NSaWdodExpc3Q6IFtdCiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIC8vIHRoaXMuaW5pdERhdGEoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIGluaXREYXRhOiBmdW5jdGlvbiBpbml0RGF0YSgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgdmFyIHBhcmFtcyA9IHsKICAgICAgICBwYWdlTnVtOiB0aGlzLnBhZ2VOdW0sCiAgICAgICAgcGFnZVNpemU6IHRoaXMucGFnZVNpemUsCiAgICAgICAga2luZDogIjAiLAogICAgICAgIHR5cGVUb3A6IHRoaXMuZmxhZwogICAgICB9OwogICAgICAoMCwgX25ld3NDZW50ZXIubmV3c0xpc3QpKHBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsKICAgICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgICAgIF90aGlzLm5ld3NGaXJzdERhdGEgPSByZXMucm93c1swXTsKICAgICAgICAgIF90aGlzLm5ld3NSaWdodExpc3QgPSByZXMucm93cy5zbGljZSgxLCA0KTsKICAgICAgICB9CiAgICAgIH0pOwogICAgICAvLyBuZXdzVHlwZSgpLnRoZW4oKHJlcykgPT4gewogICAgICAvLyAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7CiAgICAgIC8vICAgICBjb25zb2xlLmxvZyhyZXMsICItLS0tLS0tLS0tLS0tLS0tLSIpOwogICAgICAvLyAgIH0KICAgICAgLy8gfSk7CiAgICAgIC8vIGdldERpY3RzKCJjYXNlX2luZHVzdHJ5IikudGhlbigocmVzKSA9PiB7CiAgICAgIC8vICAgY29uc3QgeyBjb2RlLCBkYXRhID0gW10gfSA9IHJlczsKICAgICAgLy8gICBpZiAoY29kZSA9PT0gMjAwKSB7CiAgICAgIC8vICAgICB0aGlzLmNhc2VUeXBlTGlzdCA9IGRhdGE7CiAgICAgIC8vICAgICB0aGlzLmdldENhc2VMaXN0KCk7CiAgICAgIC8vICAgfQogICAgICAvLyB9KTsKICAgIH0sCiAgICAvLyDot7PovazliLDor6bmg4XpobXpnaIKICAgIGdvRGV0YWlsOiBmdW5jdGlvbiBnb0RldGFpbChpZCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgcGF0aDogIi9uZXdzRGV0YWlsIiwKICAgICAgICBxdWVyeTogewogICAgICAgICAgaWQ6IGlkCiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICBnb05ld3NDZW50ZXI6IGZ1bmN0aW9uIGdvTmV3c0NlbnRlcigpIHsKICAgICAgdmFyIHJvdXRlRGF0YSA9IHRoaXMuJHJvdXRlci5yZXNvbHZlKHsKICAgICAgICBwYXRoOiAiL25ld3NDZW50ZXIiCiAgICAgIH0pOwogICAgICB3aW5kb3cub3Blbihyb3V0ZURhdGEuaHJlZiwgIl9ibGFuayIpOwogICAgfSwKICAgIGdldEZsYWc6IGZ1bmN0aW9uIGdldEZsYWcodmFsdWUpIHsKICAgICAgdGhpcy5mbGFnID0gdmFsdWU7CiAgICAgIHRoaXMuaW5pdERhdGEoKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_newsCenter", "require", "data", "loading", "pageNum", "pageSize", "total", "flag", "newsFirstData", "newsRightList", "created", "methods", "initData", "_this", "params", "kind", "typeTop", "newsList", "then", "res", "code", "rows", "slice", "goDetail", "id", "$router", "push", "path", "query", "goNewsCenter", "routeData", "resolve", "window", "open", "href", "getFlag", "value"], "sources": ["src/views/components/home/<USER>"], "sourcesContent": ["<template>\r\n  <div\r\n    class=\"card-container wow animate__animated animate__fadeInUp\"\r\n    data-wow-duration=\"1s\"\r\n  >\r\n    <div class=\"enterpriseTitle\">\r\n      <div>新闻中心</div>\r\n      <div class=\"allEnterprise\" @click=\"goNewsCenter\">查看全部>></div>\r\n    </div>\r\n    <div class=\"content\" v-loading=\"loading\">\r\n      <div class=\"content_left\">\r\n        <div\r\n          class=\"platDynamics\"\r\n          :class=\"flag === '1' ? 'platDyHover' : ''\"\r\n          @click=\"getFlag('1')\"\r\n        >\r\n          <div class=\"platImg\">\r\n            <img\r\n              v-show=\"flag !== '1'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-show=\"flag === '1'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div class=\"platTitle\">平台动态</div>\r\n        </div>\r\n        <div\r\n          class=\"platDynamics\"\r\n          :class=\"flag === '2' ? 'platDyHover' : ''\"\r\n          @click=\"getFlag('2')\"\r\n        >\r\n          <div class=\"platImg\">\r\n            <img\r\n              v-show=\"flag !== '2'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-show=\"flag === '2'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div class=\"platTitle\">行业动态</div>\r\n        </div>\r\n        <div\r\n          class=\"platDynamics\"\r\n          :class=\"flag === '3' ? 'platDyHover' : ''\"\r\n          @click=\"getFlag('3')\"\r\n        >\r\n          <div class=\"platImg\">\r\n            <img\r\n              v-show=\"flag !== '3'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-show=\"flag === '3'\"\r\n              src=\"../../../assets/images/home/<USER>\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div class=\"platTitle\">政策法规</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content_middle\">\r\n        <div class=\"newsImg\">\r\n          <img src=\"../../../assets/images/home/<USER>\" alt=\"\" />\r\n        </div>\r\n        <div\r\n          class=\"newsContent\"\r\n          v-if=\"newsFirstData.updateTime\"\r\n          @click=\"goDetail(newsFirstData.id)\"\r\n        >\r\n          <div class=\"news_left\">\r\n            <div class=\"news_year\">\r\n              {{ newsFirstData.updateTime.slice(0, 7) }}\r\n            </div>\r\n            <div class=\"news_day\">\r\n              {{ newsFirstData.updateTime.slice(8, 10) }}\r\n            </div>\r\n          </div>\r\n          <div class=\"news_right\">\r\n            <div class=\"title\">{{ newsFirstData.title }}</div>\r\n            <div class=\"desc\">\r\n              {{ newsFirstData.brief }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"content_right\">\r\n        <div\r\n          class=\"newsContent\"\r\n          v-for=\"item in newsRightList\"\r\n          :key=\"item.id\"\r\n          @click=\"goDetail(item.id)\"\r\n        >\r\n          <div class=\"news_left\">\r\n            <div class=\"news_year\">{{ item.updateTime.slice(0, 7) }}</div>\r\n            <div class=\"news_day\">{{ item.updateTime.slice(8, 10) }}</div>\r\n          </div>\r\n          <div class=\"news_right\">\r\n            <div class=\"title\">{{ item.title }}</div>\r\n            <div class=\"desc\">\r\n              {{ item.brief }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { newsList } from \"@/api/newsCenter\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 4,\r\n      total: 0,\r\n      flag: \"1\",\r\n      newsFirstData: {},\r\n      newsRightList: [],\r\n    };\r\n  },\r\n  created() {\r\n    // this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        kind: \"0\",\r\n        typeTop: this.flag,\r\n      };\r\n      newsList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.loading = false;\r\n          this.newsFirstData = res.rows[0];\r\n          this.newsRightList = res.rows.slice(1, 4);\r\n        }\r\n      });\r\n      // newsType().then((res) => {\r\n      //   if (res.code === 200) {\r\n      //     console.log(res, \"-----------------\");\r\n      //   }\r\n      // });\r\n      // getDicts(\"case_industry\").then((res) => {\r\n      //   const { code, data = [] } = res;\r\n      //   if (code === 200) {\r\n      //     this.caseTypeList = data;\r\n      //     this.getCaseList();\r\n      //   }\r\n      // });\r\n    },\r\n    // 跳转到详情页面\r\n    goDetail(id) {\r\n      this.$router.push({\r\n        path: \"/newsDetail\",\r\n        query: {\r\n          id,\r\n        },\r\n      });\r\n    },\r\n    goNewsCenter() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/newsCenter\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    getFlag(value) {\r\n      this.flag = value;\r\n      this.initData();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.enterpriseTitle {\r\n  width: 100%;\r\n  font-size: 36px;\r\n  text-align: center;\r\n  margin: 60px 0;\r\n  position: relative;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  color: #000000;\r\n  .allEnterprise {\r\n    position: absolute;\r\n    top: 8 px;\r\n    right: 0;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    line-height: 26px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.content {\r\n  display: flex;\r\n  width: 100%;\r\n  height: 330px;\r\n  margin-bottom: 86px;\r\n  .content_left {\r\n    width: 300px;\r\n    height: 100%;\r\n    .platDynamics {\r\n      width: 100%;\r\n      height: 96px;\r\n      background-color: rgb(229, 247, 243);\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin-top: 21px;\r\n      cursor: pointer;\r\n      .platImg {\r\n        width: 42px;\r\n        height: 40px;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .platTitle {\r\n        font-size: 20px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #21c9b8;\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n    .platDynamics:nth-child(1) {\r\n      margin-top: 0;\r\n    }\r\n    .platDyHover {\r\n      background: #21c9b8;\r\n      .platTitle {\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n  .content_middle {\r\n    width: 460px;\r\n    height: 100%;\r\n    margin: 0 20px 0 30px;\r\n  }\r\n  .content_right {\r\n    width: 390px;\r\n    height: 100%;\r\n  }\r\n  .newsImg {\r\n    width: 100%;\r\n    height: 220px;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .newsContent {\r\n    width: 100%;\r\n    height: 110px;\r\n    background: #f9f9f9;\r\n    padding: 22px 26px;\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    .news_left {\r\n      width: 100px;\r\n      font-size: 16px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #666666;\r\n      margin-right: 20px;\r\n      text-align: center;\r\n      .news_year {\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #666666;\r\n      }\r\n      .news_day {\r\n        font-size: 30px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #222222;\r\n        line-height: 26px;\r\n        margin-top: 10px;\r\n      }\r\n    }\r\n    .news_right {\r\n      width: calc(100% - 100px);\r\n      .title {\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #222222;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 1;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n      .desc {\r\n        margin-top: 13px;\r\n        font-size: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #666666;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n    }\r\n  }\r\n  .newsContent:hover {\r\n    background: rgb(235, 252, 240);\r\n    .title {\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAsHA,IAAAA,WAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAD,IAAA;MACAE,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,IAAA;MACAC,aAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,KAAAV,OAAA;MACA,IAAAW,MAAA;QACAV,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA,QAAA;QACAU,IAAA;QACAC,OAAA,OAAAT;MACA;MACA,IAAAU,oBAAA,EAAAH,MAAA,EAAAI,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAP,KAAA,CAAAV,OAAA;UACAU,KAAA,CAAAL,aAAA,GAAAW,GAAA,CAAAE,IAAA;UACAR,KAAA,CAAAJ,aAAA,GAAAU,GAAA,CAAAE,IAAA,CAAAC,KAAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC,QAAA,WAAAA,SAAAC,EAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAC,KAAA;UACAJ,EAAA,EAAAA;QACA;MACA;IACA;IACAK,YAAA,WAAAA,aAAA;MACA,IAAAC,SAAA,QAAAL,OAAA,CAAAM,OAAA;QACAJ,IAAA;MACA;MACAK,MAAA,CAAAC,IAAA,CAAAH,SAAA,CAAAI,IAAA;IACA;IACAC,OAAA,WAAAA,QAAAC,KAAA;MACA,KAAA7B,IAAA,GAAA6B,KAAA;MACA,KAAAxB,QAAA;IACA;EACA;AACA", "ignoreList": []}]}