{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\solution\\detail.vue", "mtime": 1750311963017}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_solution", "require", "name", "data", "showLogin", "userinfo", "token", "productList", "cmsList", "cmsData", "demandList", "input", "requireList", "queryIndex", "total", "pageSize", "pageNum", "titleContent", "requirementTypeCodeArray", "form", "imageUrl", "aaa", "dataList", "painList", "advantageList", "advantageListHover", "benefitList", "caseList", "caseIndex", "gjImgdefault", "created", "$route", "query", "id", "getDemandList", "methods", "_this", "_asyncToGenerator2", "default", "_regenerator2", "m", "_callee", "res", "w", "_context", "n", "solutionDetail", "solutionId", "v", "code", "alSolutionPainVOs", "alSolutionCaseVOs", "alSolutionAdvantageVOs", "a", "getDicts", "_this2", "_callee2", "_context2", "solutionDicts", "currentChange", "val", "changeSolve", "changeadvantageListHover", "lastStep", "$refs", "carousel", "setActiveItem", "nextStep", "changeCaseIndex", "index", "getUrl<PERSON>ey", "decodeURIComponent", "RegExp", "exec", "location", "href", "replace"], "sources": ["src/views/solution/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"notice-detail-container\">\r\n    <div class=\"soDetailBanner\" :style=\"{ 'background-image': `url(${form.solutionBanner})` }\">\r\n      <div class=\"bannerSolutionFlex\" style=\"height: 100%\">\r\n        <div>\r\n          <p class=\"solutionTitle\" style=\"color:#333;\">{{ form.solutionName }}</p>\r\n          <p class=\"solutionEng\" style=\"color:#333;\">{{ form.solutionIntroduction }}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"schemeBox\" v-if=\"form.solutionOverview\">\r\n      <div class=\"serveContent\">\r\n        <div class=\"newsHeader\">\r\n          <div class=\"titleAll\">方案概述</div>\r\n        </div>\r\n        <div class=\"serveBottom\">\r\n          <div class=\"sketchLeft\">\r\n            <p style=\"display: inline-block;position: absolute; width: 505px;height: 297px;background: #21c9b8;\"></p>\r\n            <!-- <img src=\"@/assets/solution/sketchBg.png\" class=\"sketchBg\"> -->\r\n            <img width=\"506\" v-if='form.solutionImg' height=\"296\" :src=\"form.solutionImg\" class=\"sketch\">\r\n            <img width=\"506\" v-else height=\"296\" src=\"@/assets/solution/default.jpg\" class=\"sketch\">\r\n          </div>\r\n          <div class=\"sketchRight\">\r\n            <div style=\"text-align: end;height: 58px;\">\r\n              <img src=\"@/assets/solution/comma.png\">\r\n            </div>\r\n            {{ form.solutionOverview }}\r\n            <div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"painSpotBox\" v-if=\"painList != null && painList.length != 0\">\r\n      <div class=\"serveContent\">\r\n        <div class=\"newsHeader\">\r\n          <div class=\"titleAll\">行业痛点</div>\r\n        </div>\r\n        <div class=\"painContent\">\r\n          <div v-for=\"(item, index) in painList\" :key=\"index\" class=\"painDiv\">\r\n            <!-- <div><img :src=\"`../images/icon/fa0${index + 1}.svg`\" style=\"width: 40px\"></div> -->\r\n            <div class=\"painDivTitle\">{{ item.solutionPainName }}</div>\r\n            <div class=\"textOverflow3\">{{ item.solutionPainContent }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"advantageBox\" v-if=\"advantageList != null && advantageList.length != 0\">\r\n      <div class=\"serveContent\">\r\n        <div class=\"newsHeader\">\r\n          <div class=\"titleAll\">方案优势</div>\r\n        </div>\r\n        <div>\r\n          <el-carousel ref=\"carousel\" :autoplay=\"false\" indicator-position=\"none\" @change=\"changeadvantageListHover\">\r\n            <el-carousel-item v-for=\"(item, index) in advantageList\" :key=\"index\">\r\n              <div class=\"serveBottom\">\r\n                <div class=\"advantageLeft\">\r\n                  <div class=\"advantageTitleBox\">\r\n                    <img src=\"@/assets/solution/advantageIcon.svg\">\r\n                    <div class=\"advantageTitle\">{{ item.solutionAdvantageName }}</div>\r\n                  </div>\r\n                  <div class=\"advsubtitle\">{{ item.solutionAdvantageType }}</div>\r\n                  <div>{{ item.solutionAdvantageContent }}</div>\r\n                </div>\r\n                <div style=\"width: 552px;height: 358px\">\r\n                  <img :src=\"item.solutionAdvantageImage ? item.solutionAdvantageImage : gjImgdefault\"\r\n                    style=\"width: 100%;height: 100%\">\r\n                </div>\r\n              </div>\r\n            </el-carousel-item>\r\n          </el-carousel>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"caseBoxdetail\" v-if=\"caseList != null && caseList.length != 0\">\r\n      <div class=\"serveContent\">\r\n        <div class=\"newsHeader\">\r\n          <div class=\"titleAll\">实施案例</div>\r\n        </div>\r\n      </div>\r\n      <div class=\"casecontent\" v-if=\"caseList.length > 0\">\r\n        <div class=\"serveContent serveBottom\">\r\n          <div class=\"caseLeft\">\r\n            <div v-for=\"(item, index) in caseList\" :key=\"index\"\r\n              :class=\"['caseLeftbtn', caseIndex == index ? 'caseLeftbtnhover' : '']\" @click=\"changeCaseIndex(index)\"\r\n              style=\"display: flex;align-items: center;\">\r\n              <img style=\"margin-left: 10px;width: 17px\" src=\"@/assets/solution/caseicon.png\">\r\n              <span style=\"margin-left: 20px;\" class=\"textOverflow1\">{{ item.solutionCaseName }}</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"caseRight\">\r\n            <div class=\"caseRightTitle\">{{ caseList[caseIndex].solutionCaseName }}</div>\r\n            <div class=\"caseRightContent\">{{ caseList[caseIndex].solutionCaseContent }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { solutionDetail, solutionDicts } from \"@/api/solution\";\r\nimport \"@/assets/styles/index.css\";\r\n\r\nexport default {\r\n  name: \"policyPage\",\r\n  data() {\r\n    return {\r\n      showLogin: false,\r\n      userinfo: [],\r\n      token: '',\r\n      productList: [],\r\n      cmsList: [],\r\n      cmsData: [],\r\n      demandList: [],\r\n      input: '',\r\n      requireList: [],\r\n      queryIndex: 0,\r\n      total: 0,\r\n      pageSize: 10,\r\n      pageNum: 1,\r\n      titleContent: '',\r\n      requirementTypeCodeArray: [],\r\n      form: {},\r\n      imageUrl: '',\r\n      aaa: '1',\r\n      dataList: [],\r\n      painList: [],\r\n      advantageList: [],\r\n      advantageListHover: 0,\r\n      benefitList: [],\r\n      caseList: [],\r\n      caseIndex: 0,\r\n      gjImgdefault: '@/assets/solution/gjimgdefault.png'\r\n    };\r\n  },\r\n  created() {\r\n    if (this.$route.query.id) {\r\n      this.id = this.$route.query.id\r\n      this.getDemandList()\r\n    }\r\n  },\r\n  methods: {\r\n    async getDemandList() {\r\n      let res = await solutionDetail({ solutionId: this.id });\r\n      if (res.code == 200) {\r\n        this.form = res.data;\r\n        this.painList = res.data.alSolutionPainVOs;\r\n        this.caseList = res.data.alSolutionCaseVOs;\r\n        this.advantageList = res.data.alSolutionAdvantageVOs;\r\n      }\r\n    },\r\n    async getDicts() {\r\n      let res = await solutionDicts();\r\n      if (res.code == 200) {\r\n        this.requireList = res.data;\r\n      }\r\n    },\r\n    currentChange(val) {\r\n      this.pageNum = val\r\n      this.getDemandList()\r\n    },\r\n    changeSolve(val) {\r\n      this.aaa = val\r\n    },\r\n    changeadvantageListHover(val) {\r\n      this.advantageListHover = val\r\n    },\r\n    lastStep() {\r\n      this.$refs.carousel.setActiveItem(this.advantageListHover - 1)\r\n    },\r\n    nextStep() {\r\n      this.$refs.carousel.setActiveItem(this.advantageListHover + 1)\r\n    },\r\n    changeCaseIndex(index) {\r\n      this.caseIndex = index\r\n    },\r\n    getUrlKey: function (name) {\r\n      return decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(\r\n        location.href) || [, \"\"])[\r\n        1].replace(/\\+/g, '%20')) || null;\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.notice-detail-container {\r\n  width: 100%;\r\n  padding: 0 0 100px;\r\n  background: #f4f5f9;\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.notice-detail-container {\r\n  .notice-info-content {\r\n    word-break: break-all;\r\n    font-size: 16px;\r\n    line-height: 28px;\r\n    color: #333;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n\r\n    img {\r\n      max-width: 100%;\r\n    }\r\n  }\r\n}\r\n\r\n.swiper-pagination-bullet {\r\n  background: #fff;\r\n}\r\n\r\n.swiper-wrapper {\r\n  position: relative;\r\n}\r\n\r\n.swiper-container {\r\n  width: 100%;\r\n}\r\n\r\n.swiper-container2 {\r\n  width: 100%;\r\n  overflow: hidden;\r\n}\r\n\r\n.soDetailBanner {\r\n  background-repeat: no-repeat;\r\n  background-size: 100%;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;AAqGA,IAAAA,SAAA,GAAAC,OAAA;AACAA,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,QAAA;MACAC,KAAA;MACAC,WAAA;MACAC,OAAA;MACAC,OAAA;MACAC,UAAA;MACAC,KAAA;MACAC,WAAA;MACAC,UAAA;MACAC,KAAA;MACAC,QAAA;MACAC,OAAA;MACAC,YAAA;MACAC,wBAAA;MACAC,IAAA;MACAC,QAAA;MACAC,GAAA;MACAC,QAAA;MACAC,QAAA;MACAC,aAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,QAAA;MACAC,SAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,EAAA;MACA,KAAAA,EAAA,QAAAF,MAAA,CAAAC,KAAA,CAAAC,EAAA;MACA,KAAAC,aAAA;IACA;EACA;EACAC,OAAA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAC,QAAA;QAAA,IAAAC,GAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAC,CAAA;cAAA,OACA,IAAAC,wBAAA;gBAAAC,UAAA,EAAAX,KAAA,CAAAH;cAAA;YAAA;cAAAS,GAAA,GAAAE,QAAA,CAAAI,CAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAb,KAAA,CAAAjB,IAAA,GAAAuB,GAAA,CAAAvC,IAAA;gBACAiC,KAAA,CAAAb,QAAA,GAAAmB,GAAA,CAAAvC,IAAA,CAAA+C,iBAAA;gBACAd,KAAA,CAAAT,QAAA,GAAAe,GAAA,CAAAvC,IAAA,CAAAgD,iBAAA;gBACAf,KAAA,CAAAZ,aAAA,GAAAkB,GAAA,CAAAvC,IAAA,CAAAiD,sBAAA;cACA;YAAA;cAAA,OAAAR,QAAA,CAAAS,CAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IACA;IACAa,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MAAA,WAAAlB,kBAAA,CAAAC,OAAA,mBAAAC,aAAA,CAAAD,OAAA,IAAAE,CAAA,UAAAgB,SAAA;QAAA,IAAAd,GAAA;QAAA,WAAAH,aAAA,CAAAD,OAAA,IAAAK,CAAA,WAAAc,SAAA;UAAA,kBAAAA,SAAA,CAAAZ,CAAA;YAAA;cAAAY,SAAA,CAAAZ,CAAA;cAAA,OACA,IAAAa,uBAAA;YAAA;cAAAhB,GAAA,GAAAe,SAAA,CAAAT,CAAA;cACA,IAAAN,GAAA,CAAAO,IAAA;gBACAM,MAAA,CAAA3C,WAAA,GAAA8B,GAAA,CAAAvC,IAAA;cACA;YAAA;cAAA,OAAAsD,SAAA,CAAAJ,CAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IACA;IACAG,aAAA,WAAAA,cAAAC,GAAA;MACA,KAAA5C,OAAA,GAAA4C,GAAA;MACA,KAAA1B,aAAA;IACA;IACA2B,WAAA,WAAAA,YAAAD,GAAA;MACA,KAAAvC,GAAA,GAAAuC,GAAA;IACA;IACAE,wBAAA,WAAAA,yBAAAF,GAAA;MACA,KAAAnC,kBAAA,GAAAmC,GAAA;IACA;IACAG,QAAA,WAAAA,SAAA;MACA,KAAAC,KAAA,CAAAC,QAAA,CAAAC,aAAA,MAAAzC,kBAAA;IACA;IACA0C,QAAA,WAAAA,SAAA;MACA,KAAAH,KAAA,CAAAC,QAAA,CAAAC,aAAA,MAAAzC,kBAAA;IACA;IACA2C,eAAA,WAAAA,gBAAAC,KAAA;MACA,KAAAzC,SAAA,GAAAyC,KAAA;IACA;IACAC,SAAA,WAAAA,UAAApE,IAAA;MACA,OAAAqE,kBAAA,MAAAC,MAAA,WAAAtE,IAAA,+BAAAuE,IAAA,CACAC,QAAA,CAAAC,IAAA,aACA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}