{"name": "ssri", "version": "7.1.1", "description": "Standard Subresource Integrity library -- parses, serializes, generates, and verifies integrity metadata according to the SRI spec.", "main": "index.js", "files": ["*.js"], "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --coverage test/*.js", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": "https://github.com/npm/ssri", "keywords": ["w3c", "web", "security", "integrity", "checksum", "hashing", "subresource integrity", "sri", "sri hash", "sri string", "sri generator", "html"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "twitter": "maybekatz"}, "license": "ISC", "dependencies": {"figgy-pudding": "^3.5.1", "minipass": "^3.1.1"}, "devDependencies": {"standard": "^14.3.0", "standard-version": "^7.0.0", "tap": "^14.8.2", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "tap": {"check-coverage": true}, "engines": {"node": ">= 8"}}