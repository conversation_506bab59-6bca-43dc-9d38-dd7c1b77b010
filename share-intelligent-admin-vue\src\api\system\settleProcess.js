import request from '@/utils/request'

// 查询入驻申请列表
export function listSettleProcess(query) {
  return request({
    url: '/system/settleProcess/list',
    method: 'get',
    params: query
  })
}

// 查询入驻申请详细
export function getSettleProcess(id) {
  return request({
    url: '/system/settleProcess/' + id,
    method: 'get'
  })
}

// 新增入驻申请
export function addSettleProcess(data) {
  return request({
    url: '/system/settleProcess',
    method: 'post',
    data: data
  })
}

// 修改入驻申请
export function updateSettleProcess(data) {
  return request({
    url: '/system/settleProcess',
    method: 'put',
    data: data
  })
}

// 删除入驻申请
export function delSettleProcess(id) {
  return request({
    url: '/system/settleProcess/' + id,
    method: 'delete'
  })
}
