{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\HeaderSearch\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\HeaderSearch\\index.vue", "mtime": 1750311962802}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAoBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/HeaderSearch", "sourcesContent": ["<template>\r\n  <div :class=\"{'show':show}\" class=\"header-search\">\r\n    <svg-icon class-name=\"search-icon\" icon-class=\"search\" @click.stop=\"click\" />\r\n    <el-select\r\n      ref=\"headerSearchSelect\"\r\n      v-model=\"search\"\r\n      :remote-method=\"querySearch\"\r\n      filterable\r\n      default-first-option\r\n      remote\r\n      placeholder=\"Search\"\r\n      class=\"header-search-select\"\r\n      @change=\"change\"\r\n    >\r\n      <el-option v-for=\"option in options\" :key=\"option.item.path\" :value=\"option.item\" :label=\"option.item.title.join(' > ')\" />\r\n    </el-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// fuse is a lightweight fuzzy-search module\r\n// make search results more in line with expectations\r\nimport Fuse from 'fuse.js/dist/fuse.min.js'\r\nimport path from 'path'\r\n\r\nexport default {\r\n  name: 'HeaderSearch',\r\n  data() {\r\n    return {\r\n      search: '',\r\n      options: [],\r\n      searchPool: [],\r\n      show: false,\r\n      fuse: undefined\r\n    }\r\n  },\r\n  computed: {\r\n    routes() {\r\n      return this.$store.getters.permission_routes\r\n    }\r\n  },\r\n  watch: {\r\n    routes() {\r\n      this.searchPool = this.generateRoutes(this.routes)\r\n    },\r\n    searchPool(list) {\r\n      this.initFuse(list)\r\n    },\r\n    show(value) {\r\n      if (value) {\r\n        document.body.addEventListener('click', this.close)\r\n      } else {\r\n        document.body.removeEventListener('click', this.close)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.searchPool = this.generateRoutes(this.routes)\r\n  },\r\n  methods: {\r\n    click() {\r\n      this.show = !this.show\r\n      if (this.show) {\r\n        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()\r\n      }\r\n    },\r\n    close() {\r\n      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()\r\n      this.options = []\r\n      this.show = false\r\n    },\r\n    change(val) {\r\n      const path = val.path;\r\n      if(this.ishttp(val.path)) {\r\n        // http(s):// 路径新窗口打开\r\n        const pindex = path.indexOf(\"http\");\r\n        window.open(path.substr(pindex, path.length), \"_blank\");\r\n      } else {\r\n        this.$router.push(val.path)\r\n      }\r\n      this.search = ''\r\n      this.options = []\r\n      this.$nextTick(() => {\r\n        this.show = false\r\n      })\r\n    },\r\n    initFuse(list) {\r\n      this.fuse = new Fuse(list, {\r\n        shouldSort: true,\r\n        threshold: 0.4,\r\n        location: 0,\r\n        distance: 100,\r\n        minMatchCharLength: 1,\r\n        keys: [{\r\n          name: 'title',\r\n          weight: 0.7\r\n        }, {\r\n          name: 'path',\r\n          weight: 0.3\r\n        }]\r\n      })\r\n    },\r\n    // Filter out the routes that can be displayed in the sidebar\r\n    // And generate the internationalized title\r\n    generateRoutes(routes, basePath = '/', prefixTitle = []) {\r\n      let res = []\r\n\r\n      for (const router of routes) {\r\n        // skip hidden router\r\n        if (router.hidden) { continue }\r\n\r\n        const data = {\r\n          path: !this.ishttp(router.path) ? path.resolve(basePath, router.path) : router.path,\r\n          title: [...prefixTitle]\r\n        }\r\n\r\n        if (router.meta && router.meta.title) {\r\n          data.title = [...data.title, router.meta.title]\r\n\r\n          if (router.redirect !== 'noRedirect') {\r\n            // only push the routes with title\r\n            // special case: need to exclude parent router without redirect\r\n            res.push(data)\r\n          }\r\n        }\r\n\r\n        // recursive child routes\r\n        if (router.children) {\r\n          const tempRoutes = this.generateRoutes(router.children, data.path, data.title)\r\n          if (tempRoutes.length >= 1) {\r\n            res = [...res, ...tempRoutes]\r\n          }\r\n        }\r\n      }\r\n      return res\r\n    },\r\n    querySearch(query) {\r\n      if (query !== '') {\r\n        this.options = this.fuse.search(query)\r\n      } else {\r\n        this.options = []\r\n      }\r\n    },\r\n    ishttp(url) {\r\n      return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.header-search {\r\n  font-size: 0 !important;\r\n\r\n  .search-icon {\r\n    cursor: pointer;\r\n    font-size: 18px;\r\n    vertical-align: middle;\r\n  }\r\n\r\n  .header-search-select {\r\n    font-size: 18px;\r\n    transition: width 0.2s;\r\n    width: 0;\r\n    overflow: hidden;\r\n    background: transparent;\r\n    border-radius: 0;\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n\r\n    ::v-deep .el-input__inner {\r\n      border-radius: 0;\r\n      border: 0;\r\n      padding-left: 0;\r\n      padding-right: 0;\r\n      box-shadow: none !important;\r\n      border-bottom: 1px solid #d9d9d9;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  &.show {\r\n    .header-search-select {\r\n      width: 210px;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}