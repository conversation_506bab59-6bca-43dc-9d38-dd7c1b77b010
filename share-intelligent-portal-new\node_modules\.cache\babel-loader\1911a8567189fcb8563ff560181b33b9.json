{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\policyDeclare\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\policyDeclare\\index.vue", "mtime": 1750311963071}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_policy", "name", "components", "UserMenu", "data", "status", "fit", "records", "queryParams", "pageNum", "pageSize", "total", "created", "getList", "methods", "changeType", "res", "_this", "listPolicy", "_objectSpread2", "default", "then", "response", "rows", "doRevocation", "item", "_this2", "$confirm", "type", "_", "revocationPolicy", "id", "$message", "message", "catch", "goDetail", "$router", "push", "handleCurrentChange", "getItemStatus"], "sources": ["src/views/system/user/policyDeclare/index.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:38:32\r\n * @LastEditTime: 2023-02-21 12:15:31\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-03 11:20:59\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"noninductive-record-page\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div>\r\n            <el-radio-group v-model=\"status\" @change=\"changeType\">\r\n              <el-radio-button label=\"\">全部</el-radio-button>\r\n              <el-radio-button label=\"2\">审核中</el-radio-button>\r\n              <el-radio-button label=\"3\">审核通过</el-radio-button>\r\n              <el-radio-button label=\"4\">审核驳回</el-radio-button>\r\n              <el-radio-button label=\"1\">草稿箱</el-radio-button>\r\n            </el-radio-group>\r\n          </div>\r\n          <div class=\"noninductive-pannel\">\r\n            <div class=\"none-class\" v-if=\"!records || records.length == 0\">\r\n              <el-image\r\n                style=\"width: 160px; height: 160px\"\r\n                :src=\"require('@/assets/user/none.png')\"\r\n                :fit=\"fit\"\r\n              ></el-image>\r\n              <div class=\"text\">暂无数据</div>\r\n            </div>\r\n            <div\r\n              class=\"noninductive-item\"\r\n              v-for=\"item in records\"\r\n              v-bind:key=\"item.id\"\r\n            >\r\n              <a class=\"left\" @click=\"goDetail(item.id)\">\r\n                <div class=\"noninductive-title\">\r\n                  {{ item.policyName }}\r\n                </div>\r\n                <div class=\"item-info\">\r\n                  <div class=\"company-name\">\r\n                    提交时间：{{ item.updateTime }}\r\n                  </div>\r\n                </div>\r\n              </a>\r\n              <el-image\r\n                class=\"status-icon\"\r\n                v-if=\"item.status != '1'\"\r\n                style=\"width: 16px; height: 16px\"\r\n                :src=\"\r\n                  require('@/assets/user/noninductive_status_' +\r\n                    getItemStatus(item.status) +\r\n                    '.png')\r\n                \"\r\n              ></el-image>\r\n\r\n              <div class=\"noninductive-status\">{{ item.statusName }}</div>\r\n              <a\r\n                @click=\"doRevocation(item)\"\r\n                class=\"revocation-button\"\r\n                v-if=\"item.status == '2'\"\r\n              >\r\n                <div>撤回</div>\r\n              </a>\r\n            </div>\r\n          </div>\r\n          <el-pagination\r\n            v-show=\"total > 0\"\r\n            background\r\n            :page-size=\"6\"\r\n            layout=\"prev, pager, next\"\r\n            :current-page.sync=\"queryParams.pageNum\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :total=\"total\"\r\n          >\r\n          </el-pagination>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listPolicy, revocationPolicy } from \"@/api/system/policy\";\r\nexport default {\r\n  name: \"PolicyDeclare\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      status: \"\",\r\n      fit: \"cover\",\r\n      records: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 6,\r\n      },\r\n      total: 1,\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    changeType(res) {\r\n      this.getList();\r\n    },\r\n    getList() {\r\n      listPolicy({ ...this.queryParams, status: this.status }).then(\r\n        (response) => {\r\n          this.records = response.rows;\r\n          this.total = response.total;\r\n        }\r\n      );\r\n    },\r\n    doRevocation(item) {\r\n      this.$confirm(\"是否确认撤回该提报？\", { type: \"error\" })\r\n        .then((_) => {\r\n          revocationPolicy(item.id).then((response) => {\r\n            this.$message({\r\n              message: \"操作成功\",\r\n              type: \"success\",\r\n            });\r\n            this.getList();\r\n          });\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push(\"/user/policyDeclareDetail?id=\" + id);\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    getItemStatus(status) {\r\n      switch (status) {\r\n        case \"2\":\r\n          return \"1\";\r\n          break;\r\n        case \"3\":\r\n          return \"2\";\r\n          break;\r\n        case \"4\":\r\n          return \"3\";\r\n          break;\r\n        default:\r\n          return \"1\";\r\n          break;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .noninductive-record-page {\r\n    .noninductive-pannel {\r\n      margin-top: 24px;\r\n      width: 100%;\r\n      height: 600px;\r\n      background: #fff;\r\n      .none-class {\r\n        text-align: center;\r\n        padding: 10% 0;\r\n        .text {\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #999999;\r\n          line-height: 14px;\r\n        }\r\n      }\r\n      .noninductive-item {\r\n        display: flex;\r\n        padding: 0 20px;\r\n        height: 100px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        .left {\r\n          width: 53%;\r\n          .noninductive-title {\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            width: 100%;\r\n            line-height: 56px;\r\n            overflow: hidden;\r\n            -webkit-line-clamp: 1;\r\n            text-overflow: ellipsis;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n          }\r\n          .item-info {\r\n            display: flex;\r\n            .company-name {\r\n              font-size: 14px;\r\n              font-weight: 400;\r\n              color: #666666;\r\n              line-height: 20px;\r\n            }\r\n            .ml_150 {\r\n              margin-left: 150px;\r\n            }\r\n          }\r\n        }\r\n        .status-icon {\r\n          margin: auto 10px;\r\n          height: 100px;\r\n        }\r\n        .noninductive-status {\r\n          line-height: 100px;\r\n          font-size: 15px;\r\n          width: 350px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          overflow: hidden;\r\n          -webkit-line-clamp: 2;\r\n          text-overflow: ellipsis;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n        }\r\n        .revocation-button {\r\n          width: 50px;\r\n          height: 26px;\r\n          border-radius: 4px;\r\n          border: 1px solid #21c9b8;\r\n          font-size: 12px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          font-weight: 400;\r\n          color: #21c9b8;\r\n          line-height: 26px;\r\n          text-align: center;\r\n          margin: auto;\r\n        }\r\n      }\r\n    }\r\n    .el-radio-button {\r\n      margin-right: 30px;\r\n    }\r\n    .el-radio-button__inner {\r\n      width: 96px;\r\n      height: 32px;\r\n      background: transparent;\r\n      border-radius: 20px;\r\n      text-align: center;\r\n      color: #333333;\r\n      border: none;\r\n    }\r\n    .el-radio-button__orig-radio:checked + .el-radio-button__inner {\r\n      background: #21c9b8 !important;\r\n      color: #fff;\r\n      box-shadow: none;\r\n    }\r\n    .el-radio-button__inner:hover {\r\n      color: #333333;\r\n    }\r\n\r\n    .el-pagination {\r\n      width: 100%;\r\n      margin-top: 20px;\r\n      text-align: center;\r\n    }\r\n    .el-pagination.is-background .el-pager li {\r\n      background-color: #fff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled).active {\r\n      background-color: #21c9b8;\r\n      color: #ffffff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled):hover {\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AA6FA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAE,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA;MACAC,GAAA;MACAC,OAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,KAAAH,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MAAA,IAAAI,KAAA;MACA,IAAAC,kBAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,WAAAZ,WAAA;QAAAH,MAAA,OAAAA;MAAA,IAAAgB,IAAA,CACA,UAAAC,QAAA;QACAL,KAAA,CAAAV,OAAA,GAAAe,QAAA,CAAAC,IAAA;QACAN,KAAA,CAAAN,KAAA,GAAAW,QAAA,CAAAX,KAAA;MACA,CACA;IACA;IACAa,YAAA,WAAAA,aAAAC,IAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QAAAC,IAAA;MAAA,GACAP,IAAA,WAAAQ,CAAA;QACA,IAAAC,wBAAA,EAAAL,IAAA,CAAAM,EAAA,EAAAV,IAAA,WAAAC,QAAA;UACAI,MAAA,CAAAM,QAAA;YACAC,OAAA;YACAL,IAAA;UACA;UACAF,MAAA,CAAAb,OAAA;QACA;MACA,GACAqB,KAAA,WAAAL,CAAA;IACA;IACAM,QAAA,WAAAA,SAAAJ,EAAA;MACA,KAAAK,OAAA,CAAAC,IAAA,mCAAAN,EAAA;IACA;IACAO,mBAAA,WAAAA,oBAAA7B,OAAA;MACA,KAAAD,WAAA,CAAAC,OAAA,GAAAA,OAAA;MACA,KAAAI,OAAA;IACA;IACA0B,aAAA,WAAAA,cAAAlC,MAAA;MACA,QAAAA,MAAA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}