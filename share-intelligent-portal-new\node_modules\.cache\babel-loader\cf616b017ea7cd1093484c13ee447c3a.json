{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\activitySquare\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\activitySquare\\detail.vue", "mtime": 1750311962983}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_purchaseSales", "require", "_vuex", "_login", "data", "loading", "created", "init", "methods", "token", "getInfo", "getActivityDetail", "id", "_this", "userId", "$route", "query", "then", "res", "expertList", "length", "for<PERSON>ach", "item", "techniqueTypeName", "split", "demandList", "scenePicture", "JSON", "parse", "applicationArea", "supplyList", "productPhoto", "catch", "_this2", "user", "getTime", "info", "startTime", "endTime", "concat", "goExpertLibraryDetail", "routeData", "$router", "resolve", "path", "window", "open", "href", "goDemandDetail", "goResourceDetail", "signUp", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "$store", "dispatch", "location", "activityName", "addActivityEnroll", "activityId", "$message", "success", "computed", "_objectSpread2", "default", "mapGetters"], "sources": ["src/views/purchaseSales/component/activitySquare/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"activity-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"activity-detail-banner\">\r\n      <img\r\n        src=\"../../../../assets/activitySquare/activityDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"activity-detail-title-box\">\r\n      <div class=\"activity-detail-divider\"></div>\r\n      <div class=\"activity-detail-title\">活动详情</div>\r\n      <div class=\"activity-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"activity-detail-card\">\r\n      <div class=\"activity-detail-content\">\r\n        <div class=\"activity-detail-headline\">\r\n          <div class=\"headline-title\">\r\n            {{ data.activityName }}\r\n          </div>\r\n          <div class=\"headline-time\">\r\n            {{ getTime() }}\r\n          </div>\r\n        </div>\r\n        <!-- 活动详情标题 -->\r\n        <div class=\"activity-detail-caption\" v-if=\"data.activityContent\">\r\n          <div class=\"caption-line\"></div>\r\n          <div class=\"caption-title\">活动详情</div>\r\n        </div>\r\n        <!-- 活动详情 -->\r\n        <div class=\"activity-detail-img\">\r\n          <img v-if=\"data.activityContent\" :src=\"data.activityContent\" alt=\"\" />\r\n        </div>\r\n        <!-- 相关专家标题 -->\r\n        <div\r\n          class=\"activity-detail-caption\"\r\n          v-if=\"data.expertList && data.expertList.length > 0\"\r\n        >\r\n          <div class=\"caption-line\"></div>\r\n          <div class=\"caption-title\">相关专家</div>\r\n        </div>\r\n        <!-- 相关专家 -->\r\n        <div class=\"activity-detai-list\">\r\n          <div\r\n            v-for=\"(item, index) in data.expertList\"\r\n            :key=\"index\"\r\n            class=\"list-item-content\"\r\n            @click=\"goExpertLibraryDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-box\">\r\n              <div class=\"item-headline\">\r\n                <div class=\"item-title\">\r\n                  {{ item.expertName }}\r\n                </div>\r\n              </div>\r\n              <div class=\"activity-detai-label\">\r\n                <div\r\n                  v-for=\"(val, index1) in item.techniqueTypeName\"\r\n                  :key=\"index1\"\r\n                  class=\"activity-label-item\"\r\n                >\r\n                  <span v-if=\"index1 < 2\" class=\"activity-label-type\">{{\r\n                    `#${val}`\r\n                  }}</span>\r\n                  <span v-else>…</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"activity-detai-box\">\r\n                {{ item.synopsis }}\r\n              </div>\r\n            </div>\r\n            <div class=\"list-item-img\">\r\n              <img v-if=\"item.headPortrait\" :src=\"item.headPortrait\" alt=\"\" />\r\n              <img\r\n                v-else\r\n                src=\"../../../../assets/expertLibrary/defaultImg.png\"\r\n                alt=\"\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 相关需求标题 -->\r\n        <div\r\n          class=\"activity-detail-caption\"\r\n          v-if=\"data.demandList && data.demandList.length > 0\"\r\n        >\r\n          <div class=\"caption-line\"></div>\r\n          <div class=\"caption-title\">相关需求</div>\r\n        </div>\r\n        <!-- 相关需求内容 -->\r\n        <div class=\"activity-demand-info\">\r\n          <div\r\n            v-for=\"(item, index) in data.demandList\"\r\n            :key=\"index\"\r\n            class=\"activity-demand-item\"\r\n            @click=\"goDemandDetail(item.id)\"\r\n          >\r\n            <div class=\"activity-item-img\">\r\n              <img\r\n                v-if=\"item.scenePicture && item.scenePicture.length > 0\"\r\n                :src=\"item.scenePicture[0].url\"\r\n                alt=\"\"\r\n              />\r\n              <img\r\n                v-else\r\n                src=\"../../../../assets/purchaseSales/demandDefault.png\"\r\n                alt=\"\"\r\n              />\r\n            </div>\r\n            <div class=\"activity-item-content\">\r\n              <div class=\"activity-item-title\">\r\n                {{ item.demandTitle }}\r\n              </div>\r\n              <div class=\"activity-item-content-tag\">\r\n                <div\r\n                  v-for=\"(val, num) in item.applicationArea\"\r\n                  :key=\"num\"\r\n                  class=\"activity-item-tag\"\r\n                >\r\n                  {{ val }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 相关供给标题 -->\r\n        <div\r\n          class=\"activity-detail-caption related-supply-title\"\r\n          v-if=\"data.supplyList && data.supplyList.length > 0\"\r\n        >\r\n          <div class=\"caption-line\"></div>\r\n          <div class=\"caption-title\">相关供给</div>\r\n        </div>\r\n        <!-- 相关供给内容 -->\r\n        <div class=\"activity-demand-info\">\r\n          <div\r\n            v-for=\"(item, index) in data.supplyList\"\r\n            :key=\"index\"\r\n            class=\"activity-demand-item\"\r\n            @click=\"goResourceDetail(item.id)\"\r\n          >\r\n            <div class=\"activity-item-img\">\r\n              <img\r\n                v-if=\"item.productPhoto && item.productPhoto.length > 0\"\r\n                :src=\"item.productPhoto[0].url\"\r\n                alt=\"\"\r\n              />\r\n              <img\r\n                v-else\r\n                src=\"../../../../assets/purchaseSales/resourceDefault.png\"\r\n                alt=\"\"\r\n              />\r\n            </div>\r\n            <div class=\"activity-item-content\">\r\n              <div class=\"activity-item-title\">\r\n                {{ item.supplyName }}\r\n              </div>\r\n              <div class=\"activity-item-content-tag\">\r\n                <div\r\n                  v-for=\"(val, num) in item.applicationArea\"\r\n                  :key=\"num\"\r\n                  class=\"activity-item-tag\"\r\n                >\r\n                  {{ val }}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- 立即报名按钮 -->\r\n        <div class=\"activity-area-btn\">\r\n          <el-button\r\n            v-if=\"data.isEnroll === 1\"\r\n            disabled\r\n            type=\"info\"\r\n            class=\"activity-disabled-btn\"\r\n            >您已报名</el-button\r\n          >\r\n          <el-button v-else class=\"activity-sign-up\" @click=\"signUp\"\r\n            >立即报名</el-button\r\n          >\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getActivityDetail, addActivityEnroll } from \"@/api/purchaseSales\";\r\nimport { mapGetters } from \"vuex\";\r\nimport { getInfo } from \"@/api/login\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.token ? this.getInfo() : this.getActivityDetail();\r\n    },\r\n    // 详情接口\r\n    getActivityDetail(id) {\r\n      this.loading = true;\r\n      let userId = id ? id : null;\r\n      getActivityDetail({ id: this.$route.query.id, userId: userId })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          if (this.data.expertList && this.data.expertList.length > 0) {\r\n            this.data.expertList.forEach((item) => {\r\n              item.techniqueTypeName = item.techniqueTypeName.split(\",\");\r\n            });\r\n          }\r\n          if (this.data.demandList && this.data.demandList.length > 0) {\r\n            this.data.demandList.forEach((item) => {\r\n              item.scenePicture = JSON.parse(item.scenePicture);\r\n              item.applicationArea = item.applicationArea.split(\",\");\r\n            });\r\n          }\r\n          if (this.data.supplyList && this.data.supplyList.length > 0) {\r\n            this.data.supplyList.forEach((item) => {\r\n              item.productPhoto = JSON.parse(item.productPhoto);\r\n              item.applicationArea = item.applicationArea.split(\",\");\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        let id = res.user.userId || null;\r\n        this.getActivityDetail(id);\r\n      });\r\n    },\r\n    // 时间展示\r\n    getTime() {\r\n      let info = \"--\";\r\n      if (this.data.startTime && this.data.endTime) {\r\n        info = `${this.data.startTime}至${this.data.endTime}`;\r\n      } else if (this.data.startTime || this.data.endTime) {\r\n        info = this.data.startTime || this.data.endTime;\r\n      }\r\n      return info;\r\n    },\r\n    // 跳转到专家详情页面\r\n    goExpertLibraryDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/expertDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到需求详情页面\r\n    goDemandDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/demandHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到资源详情页面\r\n    goResourceDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/resourceHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 立即报名\r\n    signUp() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.$confirm(\r\n        `您参加的是${this.data.activityName},请确认。报名成功后，平台客服会与您对接`,\r\n        \"提示\",\r\n        {\r\n          confirmButtonText: \"我要参加\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }\r\n      ).then(() => {\r\n        this.loading = true;\r\n        addActivityEnroll({ activityId: this.$route.query.id })\r\n          .then(() => {\r\n            this.loading = false;\r\n            this.$message.success(\"报名成功\");\r\n            this.init();\r\n          })\r\n          .catch(() => {\r\n            this.loading = false;\r\n          });\r\n      });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-detail-container {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n  background: #f4f5f9;\r\n  .activity-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .activity-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .activity-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .activity-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .activity-detail-card {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n    .activity-detail-content {\r\n      padding: 52px 116px 60px;\r\n      .activity-detail-headline {\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        padding-bottom: 32px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        margin-bottom: 40px;\r\n        .headline-title {\r\n          font-size: 32px;\r\n          font-weight: 600;\r\n          color: #333;\r\n          line-height: 48px;\r\n          word-wrap: break-word;\r\n        }\r\n        .headline-time {\r\n          color: #333;\r\n          line-height: 14px;\r\n          padding-top: 16px;\r\n        }\r\n      }\r\n      .activity-detail-caption {\r\n        display: flex;\r\n        align-items: center;\r\n        .caption-line {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .caption-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n      .related-supply-title {\r\n        margin-top: 60px;\r\n      }\r\n      .activity-demand-info {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        .activity-demand-item {\r\n          width: 222px;\r\n          margin: 40px 18px 0 0;\r\n          background: #f8f9fb;\r\n          .activity-item-img {\r\n            width: 100%;\r\n            height: 160px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          .activity-item-content {\r\n            padding: 16px 16px 14px;\r\n            .activity-item-title {\r\n              width: 190px;\r\n              height: 52px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Medium, PingFang SC;\r\n              font-weight: 500;\r\n              color: #333;\r\n              line-height: 26px;\r\n              overflow: hidden;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n              -webkit-line-clamp: 2;\r\n              text-overflow: ellipsis;\r\n            }\r\n            .activity-item-content-tag {\r\n              display: flex;\r\n              flex-wrap: wrap;\r\n              .activity-item-tag {\r\n                max-width: 190px;\r\n                padding: 6px 12px;\r\n                font-size: 12px;\r\n                font-family: PingFangSC-Regular, PingFang SC;\r\n                color: #214dc5;\r\n                line-height: 12px;\r\n                background: rgba(33, 77, 197, 0.1);\r\n                border-radius: 4px;\r\n                margin: 12px 12px 0 0;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n          }\r\n          &:hover {\r\n            cursor: pointer;\r\n            .activity-item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .activity-detail-img {\r\n        width: 960px;\r\n        // height: 1160px;\r\n        height: 100%;\r\n        margin: 40px 0 60px;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .activity-detai-list {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        flex-wrap: wrap;\r\n        width: 100%;\r\n        margin-bottom: 60px;\r\n        .list-item-content {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          width: 462px;\r\n          background: #fff;\r\n          box-shadow: 0px 8px 32px 0px rgba(38, 74, 116, 0.1);\r\n          margin-top: 40px;\r\n          padding: 22px 25px 23px 26px;\r\n          min-height: 179px;\r\n          .list-item-box {\r\n            flex: 1;\r\n            .item-headline {\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              .item-title {\r\n                width: 200px;\r\n                font-size: 26px;\r\n                font-family: PingFangSC-Medium, PingFang SC;\r\n                font-weight: 500;\r\n                color: #333;\r\n                line-height: 26px;\r\n                text-overflow: ellipsis;\r\n                white-space: nowrap;\r\n                overflow: hidden;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n            .activity-detai-label {\r\n              display: flex;\r\n              flex-wrap: wrap;\r\n              margin: 0 0 15px;\r\n              .activity-label-item {\r\n                max-width: 280px;\r\n                padding: 5px 12px;\r\n                background: #f4f5f9;\r\n                border-radius: 4px;\r\n                font-size: 10px;\r\n                font-family: PingFangSC-Regular, PingFang SC;\r\n                color: #666;\r\n                line-height: 10px;\r\n                margin: 19px 12px 0 0;\r\n                .activity-label-type {\r\n                  word-wrap: break-word;\r\n                }\r\n              }\r\n            }\r\n            .activity-detai-box {\r\n              width: 296px;\r\n              font-size: 12px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #666;\r\n              line-height: 24px;\r\n              overflow: hidden;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n              -webkit-line-clamp: 2;\r\n              text-overflow: ellipsis;\r\n              word-wrap: break-word;\r\n            }\r\n          }\r\n          .list-item-img {\r\n            width: 96px;\r\n            height: 134px;\r\n            margin-left: 19px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          &:hover {\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n      .activity-area-btn {\r\n        text-align: center;\r\n        margin-top: 60px;\r\n        .activity-sign-up {\r\n          width: 400px;\r\n          height: 50px;\r\n          background: #21c9b8;\r\n          border-radius: 4px;\r\n          font-size: 20px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #fff;\r\n          line-height: 20px;\r\n        }\r\n      }\r\n      .activity-disabled-btn {\r\n        width: 400px;\r\n        height: 50px;\r\n        border-radius: 4px;\r\n        font-size: 20px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        line-height: 20px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AA4LA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAD,IAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,IAAA,WAAAA,KAAA;MACA,KAAAE,KAAA,QAAAC,OAAA,UAAAC,iBAAA;IACA;IACA;IACAA,iBAAA,WAAAA,kBAAAC,EAAA;MAAA,IAAAC,KAAA;MACA,KAAAR,OAAA;MACA,IAAAS,MAAA,GAAAF,EAAA,GAAAA,EAAA;MACA,IAAAD,gCAAA;QAAAC,EAAA,OAAAG,MAAA,CAAAC,KAAA,CAAAJ,EAAA;QAAAE,MAAA,EAAAA;MAAA,GACAG,IAAA,WAAAC,GAAA;QACAL,KAAA,CAAAR,OAAA;QACAQ,KAAA,CAAAT,IAAA,GAAAc,GAAA,CAAAd,IAAA;QACA,IAAAS,KAAA,CAAAT,IAAA,CAAAe,UAAA,IAAAN,KAAA,CAAAT,IAAA,CAAAe,UAAA,CAAAC,MAAA;UACAP,KAAA,CAAAT,IAAA,CAAAe,UAAA,CAAAE,OAAA,WAAAC,IAAA;YACAA,IAAA,CAAAC,iBAAA,GAAAD,IAAA,CAAAC,iBAAA,CAAAC,KAAA;UACA;QACA;QACA,IAAAX,KAAA,CAAAT,IAAA,CAAAqB,UAAA,IAAAZ,KAAA,CAAAT,IAAA,CAAAqB,UAAA,CAAAL,MAAA;UACAP,KAAA,CAAAT,IAAA,CAAAqB,UAAA,CAAAJ,OAAA,WAAAC,IAAA;YACAA,IAAA,CAAAI,YAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAN,IAAA,CAAAI,YAAA;YACAJ,IAAA,CAAAO,eAAA,GAAAP,IAAA,CAAAO,eAAA,CAAAL,KAAA;UACA;QACA;QACA,IAAAX,KAAA,CAAAT,IAAA,CAAA0B,UAAA,IAAAjB,KAAA,CAAAT,IAAA,CAAA0B,UAAA,CAAAV,MAAA;UACAP,KAAA,CAAAT,IAAA,CAAA0B,UAAA,CAAAT,OAAA,WAAAC,IAAA;YACAA,IAAA,CAAAS,YAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAN,IAAA,CAAAS,YAAA;YACAT,IAAA,CAAAO,eAAA,GAAAP,IAAA,CAAAO,eAAA,CAAAL,KAAA;UACA;QACA;MACA,GACAQ,KAAA;QACAnB,KAAA,CAAAR,OAAA;MACA;IACA;IACAK,OAAA,WAAAA,QAAA;MAAA,IAAAuB,MAAA;MACA,IAAAvB,cAAA,IAAAO,IAAA,WAAAC,GAAA;QACA,IAAAN,EAAA,GAAAM,GAAA,CAAAgB,IAAA,CAAApB,MAAA;QACAmB,MAAA,CAAAtB,iBAAA,CAAAC,EAAA;MACA;IACA;IACA;IACAuB,OAAA,WAAAA,QAAA;MACA,IAAAC,IAAA;MACA,SAAAhC,IAAA,CAAAiC,SAAA,SAAAjC,IAAA,CAAAkC,OAAA;QACAF,IAAA,MAAAG,MAAA,MAAAnC,IAAA,CAAAiC,SAAA,YAAAE,MAAA,MAAAnC,IAAA,CAAAkC,OAAA;MACA,gBAAAlC,IAAA,CAAAiC,SAAA,SAAAjC,IAAA,CAAAkC,OAAA;QACAF,IAAA,QAAAhC,IAAA,CAAAiC,SAAA,SAAAjC,IAAA,CAAAkC,OAAA;MACA;MACA,OAAAF,IAAA;IACA;IACA;IACAI,qBAAA,WAAAA,sBAAA5B,EAAA;MACA,IAAA6B,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACA5B,KAAA;UAAAJ,EAAA,EAAAA;QAAA;MACA;MACAiC,MAAA,CAAAC,IAAA,CAAAL,SAAA,CAAAM,IAAA;IACA;IACA;IACAC,cAAA,WAAAA,eAAApC,EAAA;MACA,IAAA6B,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACA5B,KAAA;UAAAJ,EAAA,EAAAA;QAAA;MACA;MACAiC,MAAA,CAAAC,IAAA,CAAAL,SAAA,CAAAM,IAAA;IACA;IACA;IACAE,gBAAA,WAAAA,iBAAArC,EAAA;MACA,IAAA6B,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACA5B,KAAA;UAAAJ,EAAA,EAAAA;QAAA;MACA;MACAiC,MAAA,CAAAC,IAAA,CAAAL,SAAA,CAAAM,IAAA;IACA;IACA;IACAG,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA1C,KAAA;QACA,KAAA2C,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAAtC,IAAA;UACAkC,MAAA,CAAAK,MAAA,CAAAC,QAAA,WAAAxC,IAAA;YACAyC,QAAA,CAAAX,IAAA;UACA;QACA;QACA;MACA;MACA,KAAAK,QAAA,kCAAAb,MAAA,CACA,KAAAnC,IAAA,CAAAuD,YAAA,0HACA,MACA;QACAN,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,CACA,EAAAtC,IAAA;QACAkC,MAAA,CAAA9C,OAAA;QACA,IAAAuD,gCAAA;UAAAC,UAAA,EAAAV,MAAA,CAAApC,MAAA,CAAAC,KAAA,CAAAJ;QAAA,GACAK,IAAA;UACAkC,MAAA,CAAA9C,OAAA;UACA8C,MAAA,CAAAW,QAAA,CAAAC,OAAA;UACAZ,MAAA,CAAA5C,IAAA;QACA,GACAyB,KAAA;UACAmB,MAAA,CAAA9C,OAAA;QACA;MACA;IACA;EACA;EACA2D,QAAA,MAAAC,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA;AAEA", "ignoreList": []}]}