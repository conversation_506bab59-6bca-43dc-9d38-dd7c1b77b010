package com.ruoyi.portalweb.api.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 我的收藏对象 my_favorite
 * 
 * <AUTHOR>
 * @date 2024-06-25
 */
public class MyFavorite extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 我的收藏ID */
    private Long myFavoriteId;

    /** 收藏分类 */
    @Excel(name = "收藏分类")
    private String favoriteType;

    /** 会员ID */
    @Excel(name = "会员ID")
    private Long memberId;

    /** 收藏条目ID */
    @Excel(name = "收藏条目ID")
    private Long issueId;

    /** 主要内容 */
    @Excel(name = "主要内容")
    private String mainContent;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String companyName;

    /** 主要内容 */
    @Excel(name = "状态")
    private String status;

    public String getCompanyName() {return companyName;}

    public void setCompanyName(String companyName) {this.companyName = companyName;}

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setMyFavoriteId(Long myFavoriteId)
    {
        this.myFavoriteId = myFavoriteId;
    }

    public Long getMyFavoriteId() 
    {
        return myFavoriteId;
    }
    public void setFavoriteType(String favoriteType) 
    {
        this.favoriteType = favoriteType;
    }

    public String getFavoriteType() 
    {
        return favoriteType;
    }
    public void setMemberId(Long memberId) 
    {
        this.memberId = memberId;
    }

    public Long getMemberId() 
    {
        return memberId;
    }
    public void setIssueId(Long issueId) 
    {
        this.issueId = issueId;
    }

    public Long getIssueId() 
    {
        return issueId;
    }
    public void setMainContent(String mainContent) 
    {
        this.mainContent = mainContent;
    }

    public String getMainContent() 
    {
        return mainContent;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("myFavoriteId", getMyFavoriteId())
            .append("favoriteType", getFavoriteType())
            .append("memberId", getMemberId())
            .append("issueId", getIssueId())
            .append("mainContent", getMainContent())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("status", getStatus())
            .toString();
    }
}
