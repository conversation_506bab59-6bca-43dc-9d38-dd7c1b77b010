{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\scene\\index.vue?vue&type=style&index=0&id=5fdb2436&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\scene\\index.vue", "mtime": 1750311963004}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/scene", "sourcesContent": ["<template>\r\n  <div class=\"scene-container\">\r\n    <div class=\"scene-banner\">\r\n      <img\r\n        src=\"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/1676017632639145.webp\"\r\n        alt=\"场景发布\"\r\n      />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"scene-title-content\">\r\n        <div class=\"scene-title-box\">\r\n          <div class=\"scene-divider\"></div>\r\n          <div class=\"scene-title\">场景发布</div>\r\n          <div class=\"scene-divider\"></div>\r\n        </div>\r\n        <div class=\"scene-search-box\">\r\n          <el-form ref=\"form\" class=\"scene-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.title\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"scene-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"scene-search-btn\"\r\n                  @click=\"search\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"scene-info-content\">\r\n        <template v-if=\"items.length > 0\">\r\n          <div\r\n            v-for=\"(item, index) in items\"\r\n            :key=\"index\"\r\n            class=\"scene-list-item\"\r\n            @click=\"goNoticDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <!-- <el-image class=\"list-item-img\" fit=\"fill\" :alt=\"item.title\" :src=\"item.src\" /> -->\r\n              <div class=\"list-item-info\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.title }}\r\n                </div>\r\n                <div class=\"list-item-text\">\r\n                  {{ item.content }}\r\n                </div>\r\n                <div class=\"list-item-time\">{{ item.updateTime }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </template>\r\n        <template v-else>\r\n          <el-empty />\r\n        </template>\r\n        <div class=\"scene-page-end\">\r\n          <el-button class=\"scene-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"items && items.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"scene-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { head, map } from \"ramda\";\r\nimport { getListByText } from \"@/api/scene\";\r\n\r\nexport default {\r\n  name: \"ScenePage\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        title: undefined, //搜索内容\r\n      },\r\n      items: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getListByText({\r\n        ...this.form,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          console.log(\"rows\", rows);\r\n          this.items = map(\r\n            ({\r\n              id,\r\n              title,\r\n              simpleContent,\r\n              updateTime,\r\n              coverPictureList = [],\r\n            }) => {\r\n              const image = head(coverPictureList || []) || {};\r\n              return {\r\n                id,\r\n                title,\r\n                content: simpleContent,\r\n                updateTime,\r\n                src: image.url,\r\n              };\r\n            },\r\n            rows\r\n          );\r\n          this.total = total;\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.pageNum = 1;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    // 跳转到详情页面\r\n    goNoticDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/scenarioDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.scene-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .scene-banner {\r\n    width: 100%;\r\n    height: 540px;\r\n    img {\r\n      width: 100%;\r\n      height: 540px;\r\n      object-fit: fill;\r\n    }\r\n  }\r\n  .scene-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    padding-bottom: 18px;\r\n    .scene-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .scene-title {\r\n        font-size: 40px;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .scene-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .scene-search-box {\r\n      .scene-search-form {\r\n        text-align: center;\r\n        .scene-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .scene-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .scene-info-content {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    .scene-list-item {\r\n      width: 100%;\r\n      background: #fff;\r\n      border-radius: 12px;\r\n      margin-top: 24px;\r\n      .list-item-content {\r\n        display: flex;\r\n        padding: 24px 32px;\r\n        cursor: pointer;\r\n        .list-item-img {\r\n          width: 298px;\r\n          height: 212px;\r\n          border-radius: 7px;\r\n          overflow: hidden;\r\n        }\r\n        .list-item-info {\r\n          padding-left: 24px;\r\n          .list-item-title {\r\n            width: 806px;\r\n            height: 24px;\r\n            text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n            white-space: nowrap; /*让文字不换行*/\r\n            overflow: hidden; /*超出要隐藏*/\r\n            font-size: 24px;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            line-height: 24px;\r\n            margin: 8px 0 24px;\r\n          }\r\n          .list-item-text {\r\n            width: 806px;\r\n            height: 90px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 3;\r\n            text-overflow: ellipsis;\r\n            font-size: 16px;\r\n            color: #666;\r\n            line-height: 30px;\r\n          }\r\n          .list-item-time {\r\n            color: #999;\r\n            line-height: 14px;\r\n            margin-top: 52px;\r\n          }\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .scene-page-end {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      margin: 0 auto;\r\n      padding: 24px 0 60px;\r\n      .scene-page-btn {\r\n        width: 82px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border-radius: 4px;\r\n        border: 1px solid #d9d9d9;\r\n        color: #333;\r\n        line-height: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.scene-container {\r\n  .scene-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .scene-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .scene-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .scene-page-end {\r\n    .scene-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}