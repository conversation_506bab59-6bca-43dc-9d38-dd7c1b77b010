"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@rongcloud/engine"),t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},t(e,n)};
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */function n(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}var r=function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},r.apply(this,arguments)};function i(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{_(r.next(e))}catch(e){o(e)}}function s(e){try{_(r.throw(e))}catch(e){o(e)}}function _(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}_((r=r.apply(e,t||[])).next())}))}function o(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}function a(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)a.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return a}function s(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}function _(t){var n=t.conversationType,r=t.channelId,i=t.messageType,o=t.content,a=t.senderUserId,s=t.targetId,_=t.sentTime,d=t.receivedTime,u=t.messageUId,c=t.messageDirection,E=t.isPersited,l=t.isCounted,T=t.isOffLineMessage,A=t.canIncludeExpansion,R=t.expansion,S=t.receivedStatus,I=t.receivedStatusInfo,f=t.disableNotification,v=t.isMentioned,g=t.isStatusMessage,O=t.readReceiptInfo,N=t.pushConfig,C=t.messageId,p=t.isInterrupt,m=t.isModifyMessage,U=t.channelType,h=t.sentStatus,M=t.directedUserIds,G=t.disableUpdateLastMessage,P=t.needReceipt,y=t.sendReceipt;return S||(S=e.ReceivedStatus.UNREAD),{messageType:i,channelId:r||"",content:o,senderUserId:a,targetId:s,conversationType:n,sentTime:_,receivedTime:d,messageUId:u,messageDirection:c,isPersited:E,isCounted:l,isMentioned:v,disableNotification:f,isStatusMessage:g,canIncludeExpansion:A,expansion:R,receivedStatus:S,receivedStatusInfo:I,readReceiptInfo:O,pushConfig:N,messageId:C,isInterrupt:p,isModifyMessage:m,sentStatus:h,isOffLineMessage:T,channelType:U,directedUserIds:M,disableUpdateLastMessage:G,needReceipt:P,sendReceipt:y}}function d(t){var n=t.conversationType,r=t.targetId,i=t.latestMessage,o=t.unreadMessageCount,a=t.hasMentioned,s=t.mentionedInfo,d=t.lastUnreadTime,u=t.notificationStatus,c=t.notificationLevel,E=t.isTop,l=t.channelId,T=t.unreadMentionedCount,A=t.matchCount,R=t.operationTime,S=t.draft,I={conversationType:n,targetId:r,latestMessage:i&&_(i),unreadMessageCount:o,hasMentioned:a,mentionedInfo:a?{type:null==s?void 0:s.type,userIdList:null==s?void 0:s.userIdList}:void 0,lastUnreadTime:d,notificationStatus:u,notificationLevel:c,isTop:E,channelId:l,unreadMentionedCount:T,matchCount:A,operationTime:R,draft:S||""};return I.conversationType===e.ConversationType.ULTRA_GROUP&&(I.firstUnreadMessage=t.firstUnreadMessage,I.channelType=t.channelType,I.unreadMentionedMeCount=t.unreadMentionedMeCount),I}function u(e,t){null===console||void 0===console||console.warn("Method '".concat(e,"' is deprecated, please use '").concat(t,"' instead"))}var c,E={groupName:"name",portraitUri:"portraitUrl",introduction:"introduction",notice:"announcement",joinPermission:"joinPerm",removeMemberPermission:"removePerm",invitePermission:"memInvitePerm",inviteHandlePermission:"invitePerm",groupInfoEditPermission:"profilePerm",memberInfoEditPermission:"memProfilePerm"};exports.Events=void 0,(c=exports.Events||(exports.Events={})).CONNECTING="CONNECTING",c.CONNECTED="CONNECTED",c.DISCONNECT="DISCONNECT",c.SUSPEND="SUSPEND",c.MESSAGES="MESSAGES",c.READ_RECEIPT_RECEIVED="READ_RECEIPT_RECEIVED",c.MESSAGE_RECEIPT_REQUEST="MESSAGE_RECEIPT_REQUEST",c.MESSAGE_RECEIPT_RESPONSE="MESSAGE_RECEIPT_RESPONSE",c.MESSAGE_READ_RECEIPT_V4="MESSAGE_READ_RECEIPT_V4",c.SYNC_READ_STATUS="SYNC_READ_STATUS",c.CONVERSATION="CONVERSATION",c.CHATROOM="CHATROOM",c.EXPANSION="EXPANSION",c.PULL_OFFLINE_MESSAGE_FINISHED="PULL_OFFLINE_MESSAGE_FINISHED",c.TAG="TAG",c.CONVERSATION_TAG="CONVERSATION_TAG",c.TYPING_STATUS="TYPING_STATUS",c.MESSAGE_BLOCKED="MESSAGE_BLOCKED",c.ULTRA_GROUP_ENABLE="ULTRA_GROUP_ENABLE",c.CONVERSATIONS_SYNCED="CONVERSATIONS_SYNCED",c.OPERATE_STATUS="OPERATE_STATUS",c.ULTRA_GROUP_MESSAGE_EXPANSION_UPDATED="ULTRA_GROUP_MESSAGE_EXPANSION_UPDATED",c.ULTRA_GROUP_MESSAGE_MODIFIED="ULTRA_GROUP_MESSAGE_MODIFIED",c.ULTRA_GROUP_MESSAGE_RECALLED="ULTRA_GROUP_MESSAGE_RECALLED",c.ULTRA_GROUP_CHANNEL_TYPE_CHANGE="ULTRA_GROUP_CHANNEL_TYPE_CHANGE",c.ULTRA_GROUP_CHANNEL_DELETE="ULTRA_GROUP_CHANNEL_DELETE",c.ULTRA_GROUP_CHANNEL_USER_KICKED="ULTRA_GROUP_CHANNEL_USER_KICKED",c.PRIVATE_MESSAGE_DELIVERED="PRIVATE_MESSAGE_DELIVERED",c.GROUP_MESSAGE_DELIVERED="GROUP_MESSAGE_DELIVERED",c.USER_GROUP_STATUS="USER_GROUP_STATUS",c.SUBSCRIBED_USER_STATUS_CHANGE="SUBSCRIBED_USER_STATUS_CHANGE",c.SUBSCRIBED_RELATION_CHANGE="SUBSCRIBED_RELATION_CHANGE",c.SYNC_SUBSCRIBED_USER_STATUS_FINISHED="SYNC_SUBSCRIBED_USER_STATUS_FINISHED",c.OWN_USER_PROFILE_CHANGED="OWN_USER_PROFILE_CHANGED",c.GROUP_OPERATION="GROUP_OPERATION",c.GROUP_INFO_CHANGED="GROUP_INFO_CHANGED",c.GROUP_MEMBER_INFO_CHANGED="GROUP_MEMBER_INFO_CHANGED",c.GROUP_APPLICATION_EVENT="GROUP_APPLICATION_EVENT",c.GROUP_REMARK_CHANGED_SYNC="GROUP_REMARK_CHANGED_SYNC",c.GROUP_FOLLOWS_CHANGED_SYNC="GROUP_FOLLOWS_CHANGED_SYNC",c.FRIEND_ADDED="FRIEND_ADDED",c.FRIEND_DELETE="FRIEND_DELETE",c.FRIEND_APPLICATION_STATUS_CHANGED="FRIEND_APPLICATION_STATUS_CHANGED",c.FRIEND_CLEARED="FRIEND_CLEARED",c.FRIEND_INFO_CHANGED_SYNC="FRIEND_INFO_CHANGED_SYNC",c.DATABASE_UPGRADE_WILL_START="DATABASE_UPGRADE_WILL_START",c.DATABASE_UPGRADING="DATABASE_UPGRADING",c.DATABASE_UPGRADE_DID_COMPLETE="DATABASE_UPGRADE_DID_COMPLETE",c.STREAM_MESSAGE_RESPONSE="STREAM_MESSAGE_RESPONSE",c.MESSAGE_RECEIPT_RESPONSE_V5="MESSAGE_RECEIPT_RESPONSE_V5";var l,T={A_ADD_TO_BLACKLIST_T:"A-add_to_blacklist-T",A_ADD_TO_BLACKLIST_R:"A-add_to_blacklist-R",A_REMOVE_FROM_BLACKLIST_T:"A-remove_from_blacklist-T",A_REMOVE_FROM_BLACKLIST_R:"A-remove_from_blacklist-R",A_GET_BLACKLIST_T:"A-get_blacklist-T",A_GET_BLACKLIST_R:"A-get_blacklist-R",A_GET_BLACKLIST_STATUS_T:"A-get_blacklist_status-T",A_GET_BLACKLIST_STATUS_R:"A-get_blacklist_status-R",A_GET_CHATROOM_INFO_T:"A-get_chatroom_info-T",A_GET_CHATROOM_INFO_R:"A-get_chatroom_info-R",A_SET_CHATROOM_ENTRY_T:"A-set_chatroom_entry-T",A_SET_CHATROOM_ENTRY_R:"A-set_chatroom_entry-R",A_SET_CHATROOM_ENTRIES_T:"A-set_chatroom_entries-T",A_SET_CHATROOM_ENTRIES_R:"A-set_chatroom_entries-R",A_FORCE_SET_CHATROOM_ENTRY_T:"A-force_set_chatroom_entry-T",A_FORCE_SET_CHATROOM_ENTRY_R:"A-force_set_chatroom_entry-R",A_REMOVE_CHATROOM_ENTRY_T:"A-remove_chatroom_entry-T",A_REMOVE_CHATROOM_ENTRY_R:"A-remove_chatroom_entry-R",A_REMOVE_CHATROOM_ENTRIES_T:"A-remove_chatroom_entries-T",A_REMOVE_CHATROOM_ENTRIES_R:"A-remove_chatroom_entries-R",A_FORCE_REMOVE_CHATROOM_ENTRY_T:"A-force_remove_chatroom_entry-T",A_FORCE_REMOVE_CHATROOM_ENTRY_R:"A-force_remove_chatroom_entry-R",A_GET_CHATROOM_ENTRY_T:"A-get_chatroom_entry-T",A_GET_CHATROOM_ENTRY_R:"A-get_chatroom_entry-R",A_GET_ALL_CHATROOM_ENTRIES_T:"A-get_all_chatroom_entries-T",A_GET_ALL_CHATROOM_ENTRIES_R:"A-get_all_chatroom_entries-R",A_GET_CHATROOM_HISTORY_MESSAGES_T:"A-get_chatroom_history_messages-T",A_GET_CHATROOM_HISTORY_MESSAGES_R:"A-get_chatroom_history_messages-R",A_BIND_RTC_ROOM_FOR_CHATROOM_T:"A-bind_rtc_room_for_chatroom-T",A_BIND_RTC_ROOM_FOR_CHATROOM_R:"A-bind_rtc_room_for_chatroom-R",A_GET_CONVERSATION_MESSAGE_DRAFT_T:"A-get_conversation_message_draft-T",A_GET_CONVERSATION_MESSAGE_DRAFT_R:"A-get_conversation_message_draft-R",A_SAVE_CONVERSATION_MESSAGE_DRAFT_T:"A-save_conversation_message_draft-T",A_SAVE_CONVERSATION_MESSAGE_DRAFT_R:"A-save_conversation_message_draft-R",A_CLEAR_CONVERSATION_MESSAGE_DRAFT_T:"A-clear_conversation_message_draft-T",A_CLEAR_CONVERSATION_MESSAGE_DRAFT_R:"A-clear_conversation_message_draft-R",A_GET_TOTAL_UNREAD_COUNT_BY_LEVELS_T:"A-get_total_unread_count_by_levels-T",A_GET_TOTAL_UNREAD_COUNT_BY_LEVELS_R:"A-get_total_unread_count_by_levels-R",A_GET_UNREAD_COUNT_T:"A-get_unread_count-T",A_GET_UNREAD_COUNT_R:"A-get_unread_count-R",A_CLEAR_UNREAD_COUNT_T:"A-clear_unread_count-T",A_CLEAR_UNREAD_COUNT_R:"A-clear_unread_count-R",A_GET_UNREAD_MENTIONED_COUNT_T:"A-get_unread_mentioned_count-T",A_GET_UNREAD_MENTIONED_COUNT_R:"A-get_unread_mentioned_count-R",A_GET_ALL_UNREAD_MENTIONED_COUNT_T:"A-get_all_unread_mentioned_count-T",A_GET_ALL_UNREAD_MENTIONED_COUNT_R:"A-get_all_unread_mentioned_count-R",A_GET_CONVERSATIONS_T:"A-get_conversations-T",A_GET_CONVERSATIONS_R:"A-get_conversations-R",A_GET_REALTIME_CON_TOTAL_UNREAD_COUNT_T:"A-get_realtime_con_total_unread_count-T",A_GET_REALTIME_CON_TOTAL_UNREAD_COUNT_R:"A-get_realtime_con_total_unread_count-R",A_CLEAR_REALTIME_CON_UNREAD_COUNT_T:"A-clear_realtime_con_unread_count-T",A_CLEAR_REALTIME_CON_UNREAD_COUNT_R:"A-clear_realtime_con_unread_count-R",A_REMOVE_REALTIME_CONVERSATIONS_T:"A-remove_realtime_conversations-T",A_REMOVE_REALTIME_CONVERSATIONS_R:"A-remove_realtime_conversations-R",A_GET_REALTIME_CONVERSATIONS_T:"A-get_realtime_conversations-T",A_GET_REALTIME_CONVERSATIONS_R:"A-get_realtime_conversations-R",A_E_SEARCH_MESSAGES_T:"A-e_search_messages-T",A_E_SEARCH_MESSAGES_R:"A-e_search_messages-R",A_E_SEARCH_MESSAGE_IN_TIME_RANGE_T:"A-e_search_message_in_time_range-T",A_E_SEARCH_MESSAGE_IN_TIME_RANGE_R:"A-e_search_message_in_time_range-R",A_E_SEARCH_MESSAGES_BY_USER_T:"A-e_search_messages_by_user-T",A_E_SEARCH_MESSAGES_BY_USER_R:"A-e_search_messages_by_user-R",A_E_GET_HISTORY_MESSAGES_BY_MESSAGE_TYPES_T:"A-e_get_history_messages_by_message_types-T",A_E_GET_HISTORY_MESSAGES_BY_MESSAGE_TYPES_R:"A-e_get_history_messages_by_message_types-R",A_E_GET_CONTINUOUS_MESSAGES_T:"A-e_get_continuous_messages-T",A_E_GET_CONTINUOUS_MESSAGES_R:"A-e_get_continuous_messages-R",A_E_SET_MESSAGE_STATUS_TO_READ_T:"A-e_set_message_status_to_read-T",A_E_SET_MESSAGE_STATUS_TO_READ_R:"A-e_set_message_status_to_read-R",A_E_SET_MESSAGE_RECEIVED_STATUS_T:"A-e_set_message_received_status-T",A_E_SET_MESSAGE_RECEIVED_STATUS_R:"A-e_set_message_received_status-R",A_E_SET_MESSAGE_SENT_STATUS_T:"A-e_set_message_sent_status-T",A_E_SET_MESSAGE_SENT_STATUS_R:"A-e_set_message_sent_status-R",A_E_SET_MESSAGE_SENT_STATUS_INFO_T:"A-e_set_message_sent_status_info-T",A_E_SET_MESSAGE_SENT_STATUS_INFO_R:"A-e_set_message_sent_status_info-R",A_E_SET_MESSAGE_CONTENT_T:"A-e_set_message_content-T",A_E_SET_MESSAGE_CONTENT_R:"A-e_set_message_content-R",A_E_DELETE_MESSAGES_T:"A-e_delete_messages-T",A_E_DELETE_MESSAGES_R:"A-e_delete_messages-R",A_E_CLEAR_MESSAGES_T:"A-e_clear_messages-T",A_E_CLEAR_MESSAGES_R:"A-e_clear_messages-R",A_E_DELETE_MESSAGES_BY_TIMESTAMP_T:"A-e_delete_messages_by_timestamp-T",A_E_DELETE_MESSAGES_BY_TIMESTAMP_R:"A-e_delete_messages_by_timestamp-R",A_E_INSERT_MESSAGE_T:"A-e_insert_message-T",A_E_INSERT_MESSAGE_R:"A-e_insert_message-R",A_E_BATCH_INSERT_MESSAGE_T:"A-e_batch_insert_message-T",A_E_BATCH_INSERT_MESSAGE_R:"A-e_batch_insert_message-R",A_E_GET_MESSAGE_COUNT_T:"A-e_get_message_count-T",A_E_GET_MESSAGE_COUNT_R:"A-e_get_message_count-R",A_E_GET_PRIVATE_MESSAGE_DELIVER_TIME_T:"A-e_get_private_message_deliver_time-T",A_E_GET_PRIVATE_MESSAGE_DELIVER_TIME_R:"A-e_get_private_message_deliver_time-R",A_E_GET_GROUP_MESSAGE_DELIVER_LIST_T:"A-e_get_group_message_deliver_list-T",A_E_GET_GROUP_MESSAGE_DELIVER_LIST_R:"A-e_get_group_message_deliver_list-R",A_E_SET_CHECK_DUPLICATE_MESSAGE_T:"A-e_set_check_duplicate_message-T",A_E_SET_CHECK_DUPLICATE_MESSAGE_R:"A-e_set_check_duplicate_message-R",A_E_SET_CHECK_CHAT_ROOM_DUPLICATE_MESSAGE_T:"A-e_set_check_duplicate_message-T",A_E_SET_CHECK_CHAT_ROOM_DUPLICATE_MESSAGE_R:"A-e_set_check_duplicate_message-R",A_E_CLEAR_LOCAL_DATA_T:"A-e_clear_local_data-T",A_E_CLEAR_LOCAL_DATA_R:"A-e_clear_local_data-R",A_BATCH_REMOVE_CONVERSATION_T:"A-batch_Remove_conversation-T",A_BATCH_REMOVE_CONVERSATION_R:"A-batch_Remove_conversation-R",A_BATCH_SET_CON_NOTIFI_LEVEL_T:"A-batch_set_con_notifi_level-T",A_BATCH_SET_CON_NOTIFI_LEVEL_R:"A-batch_set_con_notifi_level-R",A_BATCH_SET_CON_TO_TOP_T:"A-batch_set_con_to_top-T",A_BATCH_SET_CON_TO_TOP_R:"A-batch_set_con_to_top-R",A_BATCH_CLEAR_MSG_T:"A-batch_clear_msg-T",A_BATCH_CLEAR_MSG_R:"A-batch_clear_msg-R",A_GET_REALTIME_CON_UNREAD_COUNTS_T:"A-get_realtime_con_unread_counts-T",A_GET_REALTIME_CON_UNREAD_COUNTS_R:"A-get_realtime_con_unread_counts-R",A_GET_REMOTE_HISTORY_MESSAGES_T:"A-get_remote_history_messages-T",A_GET_REMOTE_HISTORY_MESSAGES_R:"A-get_remote_history_messages-R",A_RECALL_MESSAGE_T:"A-recall_message-T",A_RECALL_MESSAGE_R:"A-recall_message-R",A_UPDATE_MESSAGE_EXPANSION_T:"A-update_message_expansion-T",A_UPDATE_MESSAGE_EXPANSION_R:"A-update_message_expansion-R",A_REMOVE_MESSAGE_EXPANSION_FOR_KEY_T:"A-remove_message_expansion_for_key-T",A_REMOVE_MESSAGE_EXPANSION_FOR_KEY_R:"A-remove_message_expansion_for_key-R",A_GET_MESSAGE_READER_T:"A-get_message_reader-T",A_GET_MESSAGE_READER_R:"A-get_message_reader-R",A_GET_FIRST_UNREAD_MESSAGE_T:"A-get_first_unread_message-T",A_GET_FIRST_UNREAD_MESSAGE_R:"A-get_first_unread_message-R",A_GET_FIRST_UNREAD_MESSAGE_INFO_T:"A-get_first_unread_message_info-T",A_GET_FIRST_UNREAD_MESSAGE_INFO_R:"A-get_first_unread_message_info-R",A_INSERT_MESSAGE_T:"A-insert_message-T",A_INSERT_MESSAGE_R:"A-insert_message-R",A_GET_UNREAD_MENTIONED_MESSAGES_T:"A-get_unread_mentioned_messages-T",A_GET_UNREAD_MENTIONED_MESSAGES_R:"A-get_unread_mentioned_messages-R",A_SEARCH_CONVERSATION_BY_CONTENT_T:"A-search_conversation_by_content-T",A_SEARCH_CONVERSATION_BY_CONTENT_R:"A-search_conversation_by_content-R",A_CLEAR_UNREAD_COUNT_BY_TIMESTAMP_T:"A-clear_unread_count_by_timestamp-T",A_CLEAR_UNREAD_COUNT_BY_TIMESTAMP_R:"A-clear_unread_count_by_timestamp-R",A_SET_MESSAGE_RECEIVED_STATUS_T:"A-set_message_received_status-T",A_SET_MESSAGE_RECEIVED_STATUS_R:"A-set_message_received_status-R",A_SET_PROXY_T:"A-set_proxy-T",A_SET_PROXY_R:"A-set_proxy-R",A_GET_PROXY_T:"A-get_proxy-T",A_GET_PROXY_R:"A-get_proxy-R",A_TEST_PROXY_T:"A-test_proxy-T",A_TEST_PROXY_R:"A-test_proxy-R",A_ADD_TAG_T:"A-add_tag-T",A_ADD_TAG_R:"A-add_tag-R",A_REMOVE_TAG_T:"A-remove_tag-T",A_REMOVE_TAG_R:"A-remove_tag-R",A_UPDATE_TAG_T:"A-update_tag-T",A_UPDATE_TAG_R:"A-update_tag-R",A_GET_TAGS_T:"A-get_tags-T",A_GET_TAGS_R:"A-get_tags-R",A_GET_TAGS_FOR_CONVERSATION_T:"A-get_tags_for_conversation-T",A_GET_TAGS_FOR_CONVERSATION_R:"A-get_tags_for_conversation-R",A_ADD_TAG_FOR_CONVERSATIONS_T:"A-add_tag_for_conversations-T",A_ADD_TAG_FOR_CONVERSATIONS_R:"A-add_tag_for_conversations-R",A_REMOVE_CONVERSATIONS_FROM_TAG_T:"A-remove_conversations_from_tag-T",A_REMOVE_CONVERSATIONS_FROM_TAG_R:"A-remove_conversations_from_tag-R",A_REMOVE_TAGS_FROM_CONVERSATION_T:"A-remove_tags_from_conversation-T",A_REMOVE_TAGS_FROM_CONVERSATION_R:"A-remove_tags_from_conversation-R",A_REMOVE_TAG_FROM_CONVERSATIONS_T:"A-remove_tag_from_conversations-T",A_REMOVE_TAG_FROM_CONVERSATIONS_R:"A-remove_tag_from_conversations-R",A_GET_CONVERSATIONS_FROM_TAG_BY_PAGE_T:"A-get_conversations_from_tag_by_page-T",A_GET_CONVERSATIONS_FROM_TAG_BY_PAGE_R:"A-get_conversations_from_tag_by_page-R",A_GET_UNREAD_COUNT_BY_TAG_T:"A-get_unread_count_by_tag-T",A_GET_UNREAD_COUNT_BY_TAG_R:"A-get_unread_count_by_tag-R",A_SET_CONVERSATION_STATUS_IN_TAG_T:"A-set_conversation_status_in_tag-T",A_SET_CONVERSATION_STATUS_IN_TAG_R:"A-set_conversation_status_in_tag-R",A_GET_ULTRA_GROUP_LIST_T:"A-get_ultra_group_list-T",A_GET_ULTRA_GROUP_LIST_R:"A-get_ultra_group_list-R",A_GET_BLOCK_ULTRA_GROUP_LIST_T:"A-get_block_ultra_group_list-T",A_GET_BLOCK_ULTRA_GROUP_LIST_R:"A-get_block_ultra_group_list-R",A_SEND_ULTRA_GROUP_TYPING_STATUS_T:"A-send_ultra_group_typing_status-T",A_SEND_ULTRA_GROUP_TYPING_STATUS_R:"A-send_ultra_group_typing_status-R",A_GET_ULTRA_GROUP_MESSAGE_LIST_BY_MESSAGE_UID_T:"A-get_ultra_group_message_list_by_message_uid-T",A_GET_ULTRA_GROUP_MESSAGE_LIST_BY_MESSAGE_UID_R:"A-get_ultra_group_message_list_by_message_uid-R",A_UPDATE_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_T:"A-update_expansion_for_ultra_group_message-T",A_UPDATE_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_R:"A-update_expansion_for_ultra_group_message-R",A_REMOVE_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_T:"A-remove_expansion_for_ultra_group_message-T",A_REMOVE_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_R:"A-remove_expansion_for_ultra_group_message-R",A_REMOVE_ALL_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_T:"A-remove_all_expansion_for_ultra_group_message-T",A_REMOVE_ALL_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_R:"A-remove_all_expansion_for_ultra_group_message-R",A_MODIFY_MESSAGE_T:"A-modify_message-T",A_MODIFY_MESSAGE_R:"A-modify_message-R",A_GET_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_BY_TARGET_ID_T:"A-get_ultra_group_unread_mentioned_count_by_target_id-T",A_GET_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_BY_TARGET_ID_R:"A-get_ultra_group_unread_mentioned_count_by_target_id-R",A_GET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_T:"A-get_ultra_group_default_notification_level-T",A_GET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_R:"A-get_ultra_group_default_notification_level-R",A_SET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_T:"A-set_ultra_group_default_notification_level-T",A_SET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_R:"A-set_ultra_group_default_notification_level-R",A_GET_ULTRA_GROUP_UNREAD_COUNT_BY_TARGET_ID_T:"A-get_ultra_group_unread_count_by_target_id-T",A_GET_ULTRA_GROUP_UNREAD_COUNT_BY_TARGET_ID_R:"A-get_ultra_group_unread_count_by_target_id-R",A_GET_ALL_ULTRA_GROUP_UNREAD_COUNT_T:"A-get_all_ultra_group_unread_count-T",A_GET_ALL_ULTRA_GROUP_UNREAD_COUNT_R:"A-get_all_ultra_group_unread_count-R",A_GET_ALL_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_T:"A-get_all_ultra_group_unread_mentioned_count-T",A_GET_ALL_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_R:"A-get_all_ultra_group_unread_mentioned_count-R",A_GET_ULTRA_GROUP_UNREAD_MENTIONED_MESSAGES_T:"A-get_ultra_group_unread_mentioned_messages-T",A_GET_ULTRA_GROUP_UNREAD_MENTIONED_MESSAGES_R:"A-get_ultra_group_unread_mentioned_messages-R",A_GET_ULTRA_GROUP_FIRST_UNREAD_MESSAGE_TIMESTAMP_T:"A-get_ultra_group_first_unread_message_timestamp-T",A_GET_ULTRA_GROUP_FIRST_UNREAD_MESSAGE_TIMESTAMP_R:"A-get_ultra_group_first_unread_message_timestamp-R",A_GET_ULTRA_GROUP_UNREAD_INFO_LIST_T:"A-get_ultra_group_unread_info_list-T",A_GET_ULTRA_GROUP_UNREAD_INFO_LIST_R:"A-get_ultra_group_unread_info_list-R",A_SUBSCRIBE_USER_STATUS_T:"A-subscribe_user_status-T",A_SUBSCRIBE_USER_STATUS_R:"A-subscribe_user_status-R",A_UNSUBSCRIBE_USER_STATUS_T:"A-unsubscribe_user_status-T",A_UNSUBSCRIBE_USER_STATUS_R:"A-unsubscribe_user_status-R",A_GET_SUBSCRIBE_USER_STATUS_T:"A-get_subscribe_user_status-T",A_GET_SUBSCRIBE_USER_STATUS_R:"A-get_subscribe_user_status-R",A_GET_SUBSCRIBE_USER_STATUS_LIST_T:"A-get_subscribe_user_status_list-T",A_GET_SUBSCRIBE_USER_STATUS_LIST_R:"A-get_subscribe_user_status_list-R",A_GET_MESSAGE_BY_TIMESTAMP_T:"A-get_message_by_timestamp-T",A_GET_MESSAGE_BY_TIMESTAMP_R:"A-get_message_by_timestamp-R",A_UPDATE_MY_USER_PROFILE_T:"A-update_my_user_profile-T",A_UPDATE_MY_USER_PROFILE_R:"A-update_my_user_profile-R",A_GET_USER_PROFILES_T:"A-get_user_profiles-T",A_GET_USER_PROFILES_R:"A-get_user_profiles-R",A_GET_MY_USER_PROFILE_T:"A-get_my_user_profile-T",A_GET_MY_USER_PROFILE_R:"A-get_my_user_profile-R",A_UPDATE_MY_USER_PROFILE_VISIBILITY_T:"A-update_my_user_profile_visibility-T",A_UPDATE_MY_USER_PROFILE_VISIBILITY_R:"A-update_my_user_profile_visibility-R",A_GET_MY_USER_PROFILE_VISIBILITY_T:"A-get_my_user_profile_visibility-T",A_GET_MY_USER_PROFILE_VISIBILITY_R:"A-get_my_user_profile_visibility-R",A_SEARCH_USER_PROFILE_BY_UNIQUE_ID_T:"A-search_user_profile_by_unique_id-T",A_SEARCH_USER_PROFILE_BY_UNIQUE_ID_R:"A-search_user_profile_by_unique_id-R",A_CREATE_GROUP_T:"A-create_group-T",A_CREATE_GROUP_R:"A-create_group-R",A_UPDATE_GROUP_INFO_T:"A-update_group_info-T",A_UPDATE_GROUP_INFO_R:"A-update_group_info-R",A_GET_GROUPS_INFO_T:"A-get_groups_info-T",A_GET_GROUPS_INFO_R:"A-get_groups_info-R",A_KICK_GROUP_MEMBERS_T:"A-kick_group_members-T",A_KICK_GROUP_MEMBERS_R:"A-kick_group_members-R",A_QUIT_GROUP_T:"A-quit_group-T",A_QUIT_GROUP_R:"A-quit_group-R",A_DISMISS_GROUP_T:"A-dismiss_group-T",A_DISMISS_GROUP_R:"A-dismiss_group-R",A_TRANSFER_GROUP_OWNER_T:"A-transfer_group_owner-T",A_TRANSFER_GROUP_OWNER_R:"A-transfer_group_owner-R",A_ADD_GROUP_MANAGERS_T:"A-add_group_managers-T",A_ADD_GROUP_MANAGERS_R:"A-add_group_managers-R",A_REMOVE_GROUP_MANAGERS_T:"A-remove_group_managers-T",A_REMOVE_GROUP_MANAGERS_R:"A-remove_group_managers-R",A_GET_GROUP_MEMBERS_BY_ROLES_T:"A-get_group_members_by_roles-T",A_GET_GROUP_MEMBERS_BY_ROLES_R:"A-get_group_members_by_roles-R",A_GET_GROUP_MEMBERS_T:"A-get_group_members-T",A_GET_GROUP_MEMBERS_R:"A-get_group_members-R",A_SET_GROUP_MEMBER_INFO_T:"A-set_group_member_info-T",A_SET_GROUP_MEMBER_INFO_R:"A-set_group_member_info-R",A_SEARCH_GROUP_MEMBERS_T:"A-search_group_members-T",A_SEARCH_GROUP_MEMBERS_R:"A-search_group_members-R",A_JOIN_GROUP_T:"A-join_group-T",A_JOIN_GROUP_R:"A-join_group-R",A_INVITE_USERS_TO_GROUP_T:"A-invite_users_to_group-T",A_INVITE_USERS_TO_GROUP_R:"A-invite_users_to_group-R",A_ACCEPT_GROUP_INVITE_T:"A-accept_group_invite-T",A_ACCEPT_GROUP_INVITE_R:"A-accept_group_invite-R",A_REFUSE_GROUP_INVITE_T:"A-refuse_group_invite-T",A_REFUSE_GROUP_INVITE_R:"A-refuse_group_invite-R",A_ACCEPT_GROUP_APPLICATION_T:"A-accept_group_application-T",A_ACCEPT_GROUP_APPLICATION_R:"A-accept_group_application-R",A_REFUSE_GROUP_APPLICATION_T:"A-refuse_group_application-T",A_REFUSE_GROUP_APPLICATION_R:"A-refuse_group_application-R",A_GET_GROUP_APPLICATIONS_T:"A-get_group_applications-T",A_GET_GROUP_APPLICATIONS_R:"A-get_group_applications-R",A_GET_JOINED_GROUPS_BY_ROLES_T:"A-get_joined_groups_by_roles-T",A_GET_JOINED_GROUPS_BY_ROLES_R:"A-get_joined_groups_by_roles-R",A_SEARCH_JOINED_GROUPS_T:"A-search_joined_groups-T",A_SEARCH_JOINED_GROUPS_R:"A-search_joined_groups-R",A_GET_JOINED_GROUPS_T:"A-get_joined_groups-T",A_GET_JOINED_GROUPS_R:"A-get_joined_groups-R",A_SET_GROUP_REMARK_T:"A-set_group_remark-T",A_SET_GROUP_REMARK_R:"A-set_group_remark-R",A_GET_GROUP_REMARK_T:"A-get_group_remark-T",A_GET_GROUP_REMARK_R:"A-get_group_remark-R",A_ADD_GROUP_FOLLOWS_T:"A-add_group_follows-T",A_ADD_GROUP_FOLLOWS_R:"A-add_group_follows-R",A_REMOVE_GROUP_FOLLOWS_T:"A-remove_group_follows-T",A_REMOVE_GROUP_FOLLOWS_R:"A-remove_group_follows-R",A_GET_GROUP_FOLLOWS_T:"A-get_group_follows-T",A_GET_GROUP_FOLLOWS_R:"A-get_group_follows-R",A_ADD_FRIEND_T:"A-add_friend-T",A_ADD_FRIEND_R:"A-add_friend-R",A_DELETE_FRIENDS_T:"A-delete_friends-T",A_DELETE_FRIENDS_R:"A-delete_friends-R",A_ACCEPT_FRIEND_APPLICATION_T:"A-accept_friend_application-T",A_ACCEPT_FRIEND_APPLICATION_R:"A-accept_friend_application-R",A_REFUSE_FRIEND_APPLICATION_T:"A-refuse_friend_application-T",A_REFUSE_FRIEND_APPLICATION_R:"A-refuse_friend_application-R",A_SET_FRIEND_INFO_T:"A-set_friend_info-T",A_SET_FRIEND_INFO_R:"A-set_friend_info-R",A_CHECK_FRIENDS_T:"A-check_friends-T",A_CHECK_FRIENDS_R:"A-check_friends-R",A_GET_FRIENDS_T:"A-get_friends-T",A_GET_FRIENDS_R:"A-get_friends-R",A_GET_FRIEND_APPLICATIONS_T:"A-get_friend_applications-T",A_GET_FRIEND_APPLICATIONS_R:"A-get_friend_applications-R",A_DELETE_FRIEND_APPLICATION_T:"A-delete_friend_application-T",A_DELETE_FRIEND_APPLICATION_R:"A-delete_friend_application-R",A_GET_FRIENDS_INFO_T:"A-get_friends_info-T",A_GET_FRIENDS_INFO_R:"A-get_friends_info-R",A_SEARCH_FRIENDS_INFO_T:"A-search_friends_info-T",A_SEARCH_FRIENDS_INFO_R:"A-search_friends_info-R",A_SET_ADD_FRIEND_PERMISSION_T:"A-set_add_friend_permission-T",A_SET_ADD_FRIEND_PERMISSION_R:"A-set_add_friend_permission-R",A_GET_ADD_FRIEND_PERMISSION_T:"A-get_add_friend_permission-T",A_GET_ADD_FRIEND_PERMISSION_R:"A-get_add_friend_permission-R"};exports.MessageAuditType=void 0,(l=exports.MessageAuditType||(exports.MessageAuditType={}))[l.DISALLOW=0]="DISALLOW",l[l.ALLOW=1]="ALLOW";var A=new e.EventEmitter,R={connection:function(t,n){switch(t){case e.RCConnectionStatus.CONNECTED:A.emit(exports.Events.CONNECTED);break;case e.RCConnectionStatus.CONNECTING:A.emit(exports.Events.CONNECTING);break;case e.RCConnectionStatus.SUSPENDED:A.emit(exports.Events.SUSPEND,n);break;case e.RCConnectionStatus.DISCONNECTED:A.emit(exports.Events.DISCONNECT,n)}},batchMessage:function(e){A.emit(exports.Events.MESSAGES,{messages:e.map((function(e){return _(e)}))})},typingState:function(e){A.emit(exports.Events.TYPING_STATUS,{status:e})},readReceiptReceived:function(e,t,n,r){A.emit(exports.Events.READ_RECEIPT_RECEIVED,{conversation:e,messageUId:t,sentTime:n,sendUserId:r})},messageReceiptRequest:function(e,t,n){A.emit(exports.Events.MESSAGE_RECEIPT_REQUEST,{conversation:e,messageUId:t,senderUserId:n})},messageReceiptResponse:function(e,t,n){A.emit(exports.Events.MESSAGE_RECEIPT_RESPONSE,{conversation:e,receivedUserId:t,messageUIdList:n})},conversationState:function(t){var n=t.map((function(t){var n=t.conversationType,r={conversationType:n,latestMessage:t.latestMessage?_(t.latestMessage):null,targetId:t.targetId,unreadMessageCount:t.unreadMessageCount,unreadMentionedCount:t.unreadMentionedCount,notificationStatus:t.notificationStatus,notificationLevel:t.notificationLevel,lastUnreadTime:t.lastUnreadTime,channelId:t.channelId};return n===e.ConversationType.ULTRA_GROUP?(r.versionTime=t.versionTime,r.unreadMessageCount=S(t.unreadMessageCount||0),r.unreadMentionedCount=S(t.unreadMentionedCount||0),r.unreadMentionedMeCount=S(t.unreadMentionedMeCount||0),{conversation:r}):(r.hasMentioned=t.hasMentioned,r.mentionedInfo=t.mentionedInfo,r.isTop=t.isTop,{conversation:r,updatedItems:t.updatedItems})}));A.emit(exports.Events.CONVERSATION,{conversationList:n})},chatroomState:function(e){A.emit(exports.Events.CHATROOM,e)},expansion:function(e){A.emit(exports.Events.EXPANSION,e)},pullFinished:function(){A.emit(exports.Events.PULL_OFFLINE_MESSAGE_FINISHED)},tag:function(){A.emit(exports.Events.TAG)},conversationTagChanged:function(){A.emit(exports.Events.CONVERSATION_TAG)},messageBlocked:function(e){A.emit(exports.Events.MESSAGE_BLOCKED,e)},messageDelivered:function(e){A.emit(exports.Events.PRIVATE_MESSAGE_DELIVERED,e)},groupMessageDeliveredStatus:function(e){A.emit(exports.Events.GROUP_MESSAGE_DELIVERED,e)},ultraGroupEnable:function(){A.emit(exports.Events.ULTRA_GROUP_ENABLE)},sgRelationsSynced:function(){A.emit(exports.Events.CONVERSATIONS_SYNCED)},operateStatus:function(e){A.emit(exports.Events.OPERATE_STATUS,e)},ultraGroupMessageExpansionUpdated:function(e){A.emit(exports.Events.ULTRA_GROUP_MESSAGE_EXPANSION_UPDATED,e.map((function(e){return _(e)})))},ultraGroupMessageModified:function(e){A.emit(exports.Events.ULTRA_GROUP_MESSAGE_MODIFIED,e.map((function(e){return _(e)})))},ultraGroupMessageRecalled:function(e){A.emit(exports.Events.ULTRA_GROUP_MESSAGE_RECALLED,e.map((function(e){return _(e)})))},ultraGroupChannelChangeType:function(e){A.emit(exports.Events.ULTRA_GROUP_CHANNEL_TYPE_CHANGE,e)},ultraGroupChannelUserKicked:function(e){A.emit(exports.Events.ULTRA_GROUP_CHANNEL_USER_KICKED,e)},ultraGroupChannelDelete:function(e){A.emit(exports.Events.ULTRA_GROUP_CHANNEL_DELETE,e)},userGroupStatus:function(e){A.emit(exports.Events.USER_GROUP_STATUS,e)},messageReadReceipt:function(e){A.emit(exports.Events.MESSAGE_READ_RECEIPT_V4,e)},syncReadStatus:function(e){A.emit(exports.Events.SYNC_READ_STATUS,e)},subscribedUserStatusChange:function(e){A.emit(exports.Events.SUBSCRIBED_USER_STATUS_CHANGE,e)},subscribedRelationChange:function(e){A.emit(exports.Events.SUBSCRIBED_RELATION_CHANGE,e)},syncSubscribedUserStatusFinished:function(e){A.emit(exports.Events.SYNC_SUBSCRIBED_USER_STATUS_FINISHED,e)},ownUserProfileChanged:function(e){A.emit(exports.Events.OWN_USER_PROFILE_CHANGED,e)},groupOperation:function(e){A.emit(exports.Events.GROUP_OPERATION,e)},groupInfoChanged:function(e){A.emit(exports.Events.GROUP_INFO_CHANGED,e)},groupMemberInfoChanged:function(e){A.emit(exports.Events.GROUP_MEMBER_INFO_CHANGED,e)},groupApplicationEvent:function(e){A.emit(exports.Events.GROUP_APPLICATION_EVENT,e)},groupRemarkChangedSync:function(e){A.emit(exports.Events.GROUP_REMARK_CHANGED_SYNC,e)},groupFollowsChangedSync:function(e){A.emit(exports.Events.GROUP_FOLLOWS_CHANGED_SYNC,e)},friendAdd:function(e){A.emit(exports.Events.FRIEND_ADDED,e)},friendDelete:function(e){A.emit(exports.Events.FRIEND_DELETE,e)},friendApplicationStatusChanged:function(e){A.emit(exports.Events.FRIEND_APPLICATION_STATUS_CHANGED,e)},friendCleared:function(e){A.emit(exports.Events.FRIEND_CLEARED,e)},friendInfoChangedSync:function(e){A.emit(exports.Events.FRIEND_INFO_CHANGED_SYNC,e)},databaseUpgradeWillStart:function(){A.emit(exports.Events.DATABASE_UPGRADE_WILL_START)},databaseUpgrading:function(e){A.emit(exports.Events.DATABASE_UPGRADING,e)},databaseUpgradeDidComplete:function(e){A.emit(exports.Events.DATABASE_UPGRADE_DID_COMPLETE,e)},readReceiptResponseV5:function(e){A.emit(exports.Events.MESSAGE_RECEIPT_RESPONSE_V5,e)}};function S(e){return e>999?999:e}e.VersionManage.add("imlib-next","5.20.2");var I=null,f=null,v=null;function g(){return i(this,void 0,void 0,(function(){return o(this,(function(t){switch(t.label){case 0:return I?[4,e.APIContext.destroy()]:[2];case 1:return t.sent(),f=null,I=null,v=null,[2]}}))}))}function O(e){if(!I)throw new Error("Not initialized yet!");return e(I,v,f)}function N(t){return i(this,void 0,void 0,(function(){return o(this,(function(n){return I?[2,t(I,v,f)]:[2,e.fail(e.ErrorCode.BIZ_ERROR_CLIENT_NOT_INIT)]}))}))}function C(){return I?e.ok(I):e.fail(e.ErrorCode.BIZ_ERROR_CLIENT_NOT_INIT)}function p(){A.clear()}var m={TIMEOUT:{code:-1,msg:"Network timeout"},REJECTED_BY_BLACKLIST:{code:405,msg:"Blacklisted by the other party"},SEND_TOO_FAST:{code:20604,msg:"Sending messages too quickly"},NOT_IN_GROUP:{code:22406,msg:"Not in group"},ULTRA_GROUP_USER_NOT_IN_PRIVATE_CHANNEL:{code:e.ErrorCode.ULTRA_GROUP_USER_NOT_IN_PRIVATE_CHANNEL,msg:"ultra group user not in private channel"},FORBIDDEN_IN_GROUP:{code:22408,msg:"Forbbiden from speaking in the group"},NOT_IN_CHATROOM:{code:23406,msg:"Not in chatRoom"},FORBIDDEN_IN_CHATROOM:{code:23408,msg:"Forbbiden from speaking in the chatRoom"},KICKED_FROM_CHATROOM:{code:23409,msg:"Kicked out and forbbiden from joining the chatRoom"},CHATROOM_NOT_EXIST:{code:23410,msg:"ChatRoom does not exist"},CHATROOM_IS_FULL:{code:23411,msg:"ChatRoom members exceeded"},PARAMETER_INVALID_CHATROOM:{code:23412,msg:"Invalid chatRoom parameters"},ROAMING_SERVICE_UNAVAILABLE_CHATROOM:{code:23414,msg:"ChatRoom message roaming service is not open, Please go to the developer to open this service"},RECALLMESSAGE_PARAMETER_INVALID:{code:25101,msg:"Invalid recall message parameter"},ROAMING_SERVICE_UNAVAILABLE_MESSAGE:{code:25102,msg:"Single group chat roaming service is not open, Please go to the developer to open this service"},PUSHSETTING_PARAMETER_INVALID:{code:26001,msg:"Invalid push parameter"},OPERATION_BLOCKED:{code:20605,msg:"Operation is blocked"},OPERATION_NOT_SUPPORT:{code:20606,msg:"Operation is not supported"},MSG_BLOCKED_SENSITIVE_WORD:{code:21501,msg:"The sent message contains sensitive words"},REPLACED_SENSITIVE_WORD:{code:21502,msg:"Sensitive words in the message have been replaced"},NOT_CONNECTED:{code:30001,msg:"Please connect successfully first"},NAVI_REQUEST_ERROR:{code:30007,msg:"Navigation http request failed"},CMP_REQUEST_ERROR:{code:30010,msg:"CMP sniff http request failed"},CONN_APPKEY_FAKE:{code:31002,msg:"Your appkey is fake"},CONN_MINI_SERVICE_NOT_OPEN:{code:31003,msg:"Mini program service is not open, Please go to the developer to open this service"},CONN_ACK_TIMEOUT:{code:31e3,msg:"Connection ACK timeout"},CONN_TOKEN_INCORRECT:{code:31004,msg:"Your token is not valid or expired"},CONN_NOT_AUTHRORIZED:{code:31005,msg:"AppKey and Token do not match"},CONN_REDIRECTED:{code:31006,msg:"Connection redirection"},CONN_APP_BLOCKED_OR_DELETED:{code:31008,msg:"AppKey is banned or deleted"},CONN_USER_BLOCKED:{code:31009,msg:"User blocked"},CONN_DOMAIN_INCORRECT:{code:31012,msg:"Connect domain error, Please check the set security domain"},ROAMING_SERVICE_UNAVAILABLE:{code:33007,msg:"Roaming service cloud is not open, Please go to the developer to open this service"},RC_CONNECTION_EXIST:{code:34001,msg:"Connection already exists"},CHATROOM_KV_EXCEED:{code:23423,msg:"ChatRoom KV setting exceeds maximum"},CHATROOM_KV_OVERWRITE_INVALID:{code:23424,msg:"ChatRoom KV already exists"},CHATROOM_KV_STORE_NOT_OPEN:{code:23426,msg:"ChatRoom KV storage service is not open, Please go to the developer to open this service"},CHATROOM_KEY_NOT_EXIST:{code:23427,msg:"ChatRoom key does not exist"},MSG_KV_NOT_SUPPORT:{code:34008,msg:"The message cannot be extended"},SEND_MESSAGE_KV_FAIL:{code:34009,msg:"Sending RC expansion message fail"},EXPANSION_LIMIT_EXCEET:{code:34010,msg:"The message expansion size is beyond the limit"},ILLGAL_PARAMS:{code:33003,msg:"Incorrect parameters passed in while calling the interface"},UPLOAD_FILE_FAILED:{code:35020,msg:"File upload failed"},CHATROOM_KV_STORE_NOT_ALL_SUCCESS:{code:23428,msg:"Chatroom kv store not all success"},CHATROOM_KV_STORE_OUT_LIMIT:{code:23429,msg:"Chatroom kv's length is out of limit"},TAG_EXISTS:{code:33101,msg:"The tag already exists"},TAG_NOT_EXIST:{code:33100,msg:"The tag does not exist"},NOT_SUPPORT:{code:e.ErrorCode.NOT_SUPPORT,msg:"The method is not supported in a browser!"}},U={},h={};for(var M in m){var G=m[M],P=G.code;U[P]=M,h[P]=G}m.ROAMING_SERVICE_UNAVAILABLE.code;var y=0,L=new Map;function D(e,t){var n=function(){for(var e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];var i=[],o=[];s([],a(L.values()),!1).forEach((function(e){var t=a(e,3),r=t[0],s=t[1],_=t[2];r===n&&(o.push(s),_&&i.push(_))})),i.forEach((function(t){e=t(e)}));var _=t.apply(void 0,s([],a(e),!1));return o.forEach((function(t){t(e,_)})),_};return Object.defineProperty(n,"original",{value:t,writable:!1,enumerable:!1,configurable:!1}),Object.defineProperty(n,"name",{value:e,writable:!1,enumerable:!1,configurable:!1}),Object.defineProperty(n,"monitorable",{value:!0,writable:!1,enumerable:!1,configurable:!1}),n}var b=D("disconnect",(function(t,n){return O((function(r,i){return i.info(e.LogTagId.A_DISCONNECT_O),r.disconnect(t,n)}))}));var V=D("removeConversation",(function(e){return i(void 0,void 0,void 0,(function(){return o(this,(function(t){return[2,C().next((function(t){return t.removeConversation(e)}))]}))}))}));var w=D("clearMessagesUnreadStatus",(function(t){return i(void 0,void 0,void 0,(function(){return o(this,(function(n){return[2,new e.ValidatorManage(T.A_CLEAR_UNREAD_COUNT_T,T.A_CLEAR_UNREAD_COUNT_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(n,r){return i(void 0,void 0,void 0,(function(){var i,a,s,_,d,u;return o(this,(function(o){switch(o.label){case 0:return i=t.conversationType,a=t.targetId,s=t.channelId,_=r.createTraceId(),r.info(T.A_CLEAR_UNREAD_COUNT_T,JSON.stringify({conversationType:i,targetId:a,channelId:s}),_),[4,n.clearUnreadCount(i,a,s)];case 1:return d=o.sent(),u=d===e.ErrorCode.SUCCESS?"info":"warn",r[u](T.A_CLEAR_UNREAD_COUNT_R,JSON.stringify({code:d}),_),d!==e.ErrorCode.SUCCESS?[2,{code:d,msg:U[d]}]:[2,{code:d}]}}))}))}))]}))}))}));var x=D("setConversationNotificationLevel",(function(e,t){return i(void 0,void 0,void 0,(function(){return o(this,(function(n){return[2,C().next((function(n){return n.batchSetConversationNotificationLevel([e],t)}))]}))}))}));var F=D("setConversationToTop",(function(e,t,n,r){return void 0===t&&(t=!0),void 0===n&&(n=!0),void 0===r&&(r=!1),i(void 0,void 0,void 0,(function(){return o(this,(function(i){return[2,C().next((function(i){return i.batchSetConversationToTop([e],!!t,n,r)}))]}))}))}));var B=2048;var J=function(e,t,n,r){void 0===n&&(n=!0),void 0===r&&(r=!0),Object.defineProperty(this,"messageType",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"content",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"isPersited",{enumerable:!0,configurable:!0,writable:!0,value:n}),Object.defineProperty(this,"isCounted",{enumerable:!0,configurable:!0,writable:!0,value:r})},H=function(e){function t(t,n,r){return e.call(this,t,r,n.isPersited,n.isCounted)||this}return n(t,e),t}(J);var Y,k=function(t){function r(n){return t.call(this,e.MessageType.COMBINE,e.getMessageTypeDescription(e.MessageType.COMBINE),n)||this}return n(r,t),r}(H),j=function(t){function r(n){return t.call(this,e.MessageType.COMBINE_V2,e.getMessageTypeDescription(e.MessageType.COMBINE_V2),n)||this}return n(r,t),r}(H),K=function(t){function r(n){return t.call(this,"RC:CmdMsg",e.getMessageTypeDescription("RC:CmdMsg"),n)||this}return n(r,t),r}(H),X=function(t){function r(n){return t.call(this,e.MessageType.FILE,e.getMessageTypeDescription(e.MessageType.FILE),n)||this}return n(r,t),r}(H),Q=function(t){function r(n){return t.call(this,e.MessageType.GIF,e.getMessageTypeDescription(e.MessageType.GIF),n)||this}return n(r,t),r}(H),W=function(t){function r(n){return t.call(this,"RC:GrpNtf",e.getMessageTypeDescription("RC:GrpNtf"),n)||this}return n(r,t),r}(H),q=function(t){function r(n){return t.call(this,e.MessageType.HQ_VOICE,e.getMessageTypeDescription(e.MessageType.HQ_VOICE),n)||this}return n(r,t),r}(H),z=function(t){function r(n){return t.call(this,e.MessageType.IMAGE,e.getMessageTypeDescription(e.MessageType.IMAGE),n)||this}return n(r,t),r}(H),Z=function(t){function r(n){return t.call(this,"RC:InfoNtf",e.getMessageTypeDescription("RC:InfoNtf"),n)||this}return n(r,t),r}(H),$=function(t){function r(n){return t.call(this,e.MessageType.LOCATION,e.getMessageTypeDescription(e.MessageType.LOCATION),n)||this}return n(r,t),r}(H),ee=function(t){function r(n){return t.call(this,e.MessageType.REFERENCE,e.getMessageTypeDescription(e.MessageType.REFERENCE),n)||this}return n(r,t),r}(H),te=function(t){function r(n){return t.call(this,e.MessageType.RICH_CONTENT,e.getMessageTypeDescription(e.MessageType.RICH_CONTENT),n)||this}return n(r,t),r}(H),ne=function(t){function r(n){return t.call(this,e.MessageType.SIGHT,e.getMessageTypeDescription(e.MessageType.SIGHT),n)||this}return n(r,t),r}(H),re=function(t){function r(n){return t.call(this,e.MessageType.STREAM_MESSAGE,e.getMessageTypeDescription(e.MessageType.STREAM_MESSAGE),n)||this}return n(r,t),r}(H),ie=function(t){function r(n){return t.call(this,e.MessageType.TextMessage,e.getMessageTypeDescription(e.MessageType.TextMessage),n)||this}return n(r,t),r}(H),oe=function(t){function r(n){return t.call(this,e.MessageType.VOICE,e.getMessageTypeDescription(e.MessageType.VOICE),n)||this}return n(r,t),r}(H);!function(e){e.ALIYUN="aliyun",e.S3="s3",e.MINIO="minio",e.QINIU="qiniu",e.RCX="rcx"}(Y||(Y={}));var ae=function(){function t(e,t,n){Object.defineProperty(this,"_url",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"_authInfo",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"_getFileToken",{enumerable:!0,configurable:!0,writable:!0,value:n}),Object.defineProperty(this,"multipartUpload",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_isAbort",{enumerable:!0,configurable:!0,writable:!0,value:!1})}return Object.defineProperty(t.prototype,"isAbort",{enumerable:!1,configurable:!0,writable:!0,value:function(){return this._isAbort}}),Object.defineProperty(t.prototype,"abort",{enumerable:!1,configurable:!0,writable:!0,value:function(){this._isAbort=!0,this._abortHandler()}}),Object.defineProperty(t.prototype,"upload",{enumerable:!1,configurable:!0,writable:!0,value:function(t){return t.file.size>e.MAX_UPLOAD_FILE_SIZE?Promise.resolve({code:e.ErrorCode.UPLOAD_FAIL,msg:"the file size is over 5GB!"}):t.file.size>e.UPLOAD_FILE_CHUNK_SIZE&&this.multipartUpload?this.multipartUpload(t):this.commonUpload(t)}}),t}(),se=function(t){function r(){var e=null!==t&&t.apply(this,arguments)||this;return Object.defineProperty(e,"_promise",{enumerable:!0,configurable:!0,writable:!0,value:null}),e}return n(r,t),Object.defineProperty(r.prototype,"commonUpload",{enumerable:!1,configurable:!0,writable:!0,value:function(t){return i(this,void 0,void 0,(function(){var n,r,i,a,s,_;return o(this,(function(o){switch(o.label){case 0:return n=t.file,r=t.onProgress,i=this._authInfo,a={},(s=new FormData).set("token",i.token),s.set("key",i.fileName),s.set("file",n),this._promise=e.httpRequest({url:e.fixUrlProtocol(this._url),headers:a,onProgress:function(e){return null==r?void 0:r(e.loaded,e.total)},method:"POST",body:s,timeout:0}),[4,this._promise.result()];case 1:return _=o.sent().status,this._promise=null,200===_||204===_?[2,{code:e.ErrorCode.SUCCESS,data:{uniqueName:i.fileName,uploadMethod:e.UploadMethod.QINIU}}]:[2,{code:e.ErrorCode.UPLOAD_FAIL,msg:_.toString()}]}}))}))}}),Object.defineProperty(r.prototype,"_abortHandler",{enumerable:!1,configurable:!0,writable:!0,value:function(){var e;null===(e=this._promise)||void 0===e||e.abort()}}),r}(ae),_e=function(t){function r(){var e=null!==t&&t.apply(this,arguments)||this;return Object.defineProperty(e,"_promise",{enumerable:!0,configurable:!0,writable:!0,value:null}),e}return n(r,t),Object.defineProperty(r.prototype,"commonUpload",{enumerable:!1,configurable:!0,writable:!0,value:function(t){return i(this,void 0,void 0,(function(){var n,r,i,a,s,_;return o(this,(function(o){switch(o.label){case 0:return n=t.file,r=t.onProgress,i=this._authInfo,a=e.fixUrlProtocol("".concat(i.ossBucketName,".").concat(this._url)),(s=new FormData).set("token",i.token),s.set("OSSAccessKeyId",i.osskeyId),s.set("policy",i.ossPolicy),s.set("Signature",i.ossSign),s.set("success_action_status","200"),s.set("key",i.fileName),s.set("file",n),this._promise=e.httpRequest({url:a,onProgress:function(e){return null==r?void 0:r(e.loaded,e.total)},method:"POST",body:s,timeout:0}),[4,this._promise.result()];case 1:return _=o.sent().status,this._promise=null,200===_||204===_?[2,{code:e.ErrorCode.SUCCESS,data:{uniqueName:i.fileName,uploadMethod:e.UploadMethod.ALI}}]:[2,{code:e.ErrorCode.UPLOAD_FAIL,msg:_.toString()}]}}))}))}}),Object.defineProperty(r.prototype,"_abortHandler",{enumerable:!1,configurable:!0,writable:!0,value:function(){var e;null===(e=this._promise)||void 0===e||e.abort()}}),r}(ae);function de(e){return Math.ceil(e/1024)||0}var ue,ce=function(t){function r(){var n=null!==t&&t.apply(this,arguments)||this;return Object.defineProperty(n,"_promise",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(n,"isSupportMultipart",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(n,"multipartUpload",{enumerable:!0,configurable:!0,writable:!0,value:function(t){return i(n,void 0,void 0,(function(){var n,r,i,a,s,_,d,u,c,E,l,T,A,R,S,I,f;return o(this,(function(o){switch(o.label){case 0:n=t.file,r=t.fileType,o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this._getFileToken(r,n.name,"POST","uploads",void 0,de(n.size))];case 2:return i=o.sent(),[3,4];case 3:return[2,{code:o.sent()}];case 4:return a=e.fixUrlProtocol("".concat(this._url,"/").concat(i.minioBucketName,"/").concat(i.fileName)),s=this.getBaseHeaders(n,i,t),this._promise=e.httpRequest({url:"".concat(a,"?uploads"),headers:s,method:"POST"}),[4,this._promise.result()];case 5:return _=o.sent(),d=_.status,u=_.data,this._promise=null,c=null===(f=null==u?void 0:u.match(/(?:<UploadId>)(\S*?)(?:<\/UploadId>)/))||void 0===f?void 0:f[1],200!==d&&204!==d||!c?[2,{code:e.ErrorCode.UPLOAD_FAIL,msg:"start upload fail"}]:(E=[],[4,this.requestUploadPart(a,n,c,i.fileName,t,E)]);case 6:if(l=o.sent(),T=l.code,A=l.msg,T!==e.ErrorCode.SUCCESS)return[2,{code:T,msg:A}];o.label=7;case 7:return o.trys.push([7,9,,10]),[4,this._getFileToken(r,i.fileName,"POST","uploadId=".concat(c),void 0,de(n.size))];case 8:return i=o.sent(),[3,10];case 9:return[2,{code:o.sent()}];case 10:return R=E.map((function(e,t){return"<Part><PartNumber>".concat(t+1,"</PartNumber><ETag>").concat(e,"</ETag></Part>")})).join(""),R="<CompleteMultipartUpload xmlns='http://s3.amazonaws.com/doc/2006-03-01/'>".concat(R,"</CompleteMultipartUpload>"),S=this.getBaseHeaders(n,i,t),this._promise=e.httpRequest({url:"".concat(a,"?uploadId=").concat(c),headers:S,method:"POST",body:R,timeout:0}),[4,this._promise.result()];case 11:return I=o.sent().status,this._promise=null,200===I||204===I?[2,{code:e.ErrorCode.SUCCESS,data:{uniqueName:i.fileName,uploadMethod:e.UploadMethod.MINIO}}]:[2,{code:e.ErrorCode.UPLOAD_FAIL,msg:"end upload fail"}]}}))}))}}),n}return n(r,t),Object.defineProperty(r.prototype,"commonUpload",{enumerable:!1,configurable:!0,writable:!0,value:function(t){return i(this,void 0,void 0,(function(){var n,r,i,a,s,_;return o(this,(function(o){switch(o.label){case 0:return n=t.file,r=t.onProgress,i=this._authInfo,a=e.fixUrlProtocol("".concat(this._url,"/").concat(i.minioBucketName,"/").concat(i.fileName)),s=this.getBaseHeaders(n,i,t),this._promise=e.httpRequest({url:a,headers:s,onProgress:function(e){return null==r?void 0:r(e.loaded,e.total)},method:"PUT",body:n,timeout:0}),[4,this._promise.result()];case 1:return _=o.sent().status,this._promise=null,200===_||204===_?[2,{code:e.ErrorCode.SUCCESS,data:{uniqueName:i.fileName,uploadMethod:e.UploadMethod.MINIO}}]:[2,{code:e.ErrorCode.UPLOAD_FAIL,msg:_.toString()}]}}))}))}}),Object.defineProperty(r.prototype,"requestUploadPart",{enumerable:!1,configurable:!0,writable:!0,value:function(t,n,r,a,s,_,d,u){return void 0===d&&(d=1),void 0===u&&(u=0),i(this,void 0,void 0,(function(){var i,c,E,l,T,A,R,S,I;return o(this,(function(o){switch(o.label){case 0:if(u>=5)return[2,{code:e.ErrorCode.UPLOAD_FAIL,msg:"Upload retry times exceeded 5. Procedure"}];i=(d-1)*e.UPLOAD_FILE_CHUNK_SIZE,c=Math.min(e.UPLOAD_FILE_CHUNK_SIZE,n.size-i),E="partNumber=".concat(d,"&uploadId=").concat(r),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,this._getFileToken(s.fileType,a,"PUT",E,void 0,de(n.size))];case 2:return l=o.sent(),[3,4];case 3:return[2,{code:o.sent()}];case 4:return T=n.slice(i,d*e.UPLOAD_FILE_CHUNK_SIZE),A=this.getBaseHeaders(n,l,s),this._promise=e.httpRequest({url:"".concat(t,"?").concat(E),headers:A,method:"PUT",body:T,timeout:0,onProgress:function(e){var t;return null===(t=null==s?void 0:s.onProgress)||void 0===t?void 0:t.call(s,i+e.loaded,n.size)}}),[4,this._promise.result()];case 5:return R=o.sent(),S=R.status,I=R.xhr,this._promise=null,200===S||204===S?(_.push((null==I?void 0:I.getResponseHeader("ETag"))||""),i+c<n.size?[2,this.requestUploadPart(t,n,r,a,s,_,d+1,0)]:[2,{code:e.ErrorCode.SUCCESS}]):[2,this.requestUploadPart(t,n,r,a,s,_,d,u+1)]}}))}))}}),Object.defineProperty(r.prototype,"getBaseHeaders",{enumerable:!1,configurable:!0,writable:!0,value:function(e,t,n){var r=n.contentDisposition||("text/html"===e.type?"inline":"attachment");return{"Content-Disposition":"".concat(r,"; filename=").concat(encodeURIComponent(e.name),";"),Authorization:t.minioAuthorization,"x-amz-content-sha256":t.minioContentSha256,"x-amz-date":t.minioDate,"Content-Type":e.type||"text/plain"}}}),Object.defineProperty(r.prototype,"_abortHandler",{enumerable:!1,configurable:!0,writable:!0,value:function(){var e;null===(e=this._promise)||void 0===e||e.abort()}}),r}(ae),Ee=function(t){function r(){var n=null!==t&&t.apply(this,arguments)||this;return Object.defineProperty(n,"isSupportMultipart",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(n,"_promise",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(n,"multipartUpload",{enumerable:!0,configurable:!0,writable:!0,value:function(t){return i(n,void 0,void 0,(function(){var n,r,i,a;return o(this,(function(o){return n=t.file,r=t.onProgress,i=e.UUId.gen(),a=this._authInfo,[2,this._requestUploadPart(i,n,a.token,r)]}))}))}}),n}return n(r,t),Object.defineProperty(r.prototype,"commonUpload",{enumerable:!1,configurable:!0,writable:!0,value:function(t){return i(this,void 0,void 0,(function(){var n,r,i,a,s,_,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:return n=t.file,r=t.onProgress,i=this._authInfo,a={},(s=new FormData).set("token",i.token),s.set("key",i.fileName),s.set("file",n),_=e.fixUrlProtocol(this._url),this._promise=e.httpRequest({url:_,headers:a,onProgress:function(e){return null==r?void 0:r(e.loaded,e.total)},method:"POST",body:s,timeout:0}),[4,this._promise.result()];case 1:return d=o.sent(),u=d.status,c=d.data,this._promise=null,200!==u&&204!==u||!c?[2,{code:e.ErrorCode.UPLOAD_FAIL,msg:u.toString()}]:(E=JSON.parse(c),[2,{code:e.ErrorCode.SUCCESS,data:{uniqueName:E.rc_url.file_name,downloadUrl:"".concat(_,"/").concat(E.rc_url.path),uploadMethod:e.UploadMethod.QINIU}}])}}))}))}}),Object.defineProperty(r.prototype,"_requestUploadPart",{enumerable:!1,configurable:!0,writable:!0,value:function(t,n,r,a,s){return void 0===s&&(s=0),i(this,void 0,void 0,(function(){var i,_,d,u,c,E,l,T,A,R;return o(this,(function(o){switch(o.label){case 0:return i=Math.min(e.UPLOAD_FILE_CHUNK_SIZE,n.size-s),_=t+n.name.substring(n.name.lastIndexOf(".")),d={Range:"bytes=".concat(s,"-").concat(s+i),"X-File-TransactionId":_,"X-File-Total-Size":n.size.toString()},u=n.slice(s,s+i),(c=new FormData).set("file",u,n.name),c.set("key",_),c.set("name",_),c.set("token",r),E=e.fixUrlProtocol(this._url),this._promise=e.httpRequest({url:E,headers:d,onProgress:function(e){return null==a?void 0:a(s+e.loaded,n.size)},method:"POST",body:c,timeout:0}),[4,this._promise.result()];case 1:return l=o.sent(),T=l.status,A=l.data,this._promise=null,200===T||204===T?s+i<n.size?[2,this._requestUploadPart(t,n,r,a,s+i)]:(R=JSON.parse(A),[2,{code:e.ErrorCode.SUCCESS,data:{uniqueName:R.rc_url.file_name,downloadUrl:"".concat(E,"/").concat(R.rc_url.path),uploadMethod:e.UploadMethod.QINIU}}]):[2,{code:e.ErrorCode.UPLOAD_FAIL,msg:T.toString()}]}}))}))}}),Object.defineProperty(r.prototype,"_abortHandler",{enumerable:!1,configurable:!0,writable:!0,value:function(){var e;null===(e=this._promise)||void 0===e||e.abort()}}),r}(ae),le=function(t){function r(){var e=null!==t&&t.apply(this,arguments)||this;return Object.defineProperty(e,"_promise",{enumerable:!0,configurable:!0,writable:!0,value:null}),e}return n(r,t),Object.defineProperty(r.prototype,"commonUpload",{enumerable:!1,configurable:!0,writable:!0,value:function(t){return i(this,void 0,void 0,(function(){var n,r,i,a,s,_,d,u,c;return o(this,(function(o){switch(o.label){case 0:return n=t.file,r=t.onProgress,i=t.contentDisposition,a=this._authInfo,s={},_=e.fixUrlProtocol("".concat(a.s3BucketName,".").concat(this._url)),d=new FormData,i?d.set("Content-Disposition",i):d.set("Content-Disposition","text/html"===n.type?"inline;":"attachment;"),d.set("Content-Type",n.type),d.set("x-amz-credential",a.s3Credential),d.set("x-amz-algorithm",a.s3Algorithm),d.set("x-amz-date",a.s3Date),d.set("policy",a.s3Policy),d.set("x-amz-signature",a.s3Signature),d.set("key",a.fileName),d.set("file",n),this._promise=e.httpRequest({url:_,headers:s,onProgress:function(e){return null==r?void 0:r(e.loaded,e.total)},method:"POST",body:d,timeout:0}),[4,this._promise.result()];case 1:return u=o.sent(),c=u.status,this._promise=null,200===c||204===c?[2,{code:e.ErrorCode.SUCCESS,data:{uniqueName:a.fileName,uploadMethod:e.UploadMethod.AWS}}]:[2,{code:e.ErrorCode.UPLOAD_FAIL,msg:c.toString()}]}}))}))}}),Object.defineProperty(r.prototype,"_abortHandler",{enumerable:!1,configurable:!0,writable:!0,value:function(){var e;null===(e=this._promise)||void 0===e||e.abort()}}),r}(ae);var Te=((ue={})[Y.QINIU]=se,ue[Y.ALIYUN]=_e,ue[Y.S3]=le,ue[Y.MINIO]=ce,ue[Y.RCX]=Ee,ue);function Ae(t,n,i,o){var a=document.createElement("canvas"),s=a.getContext("2d"),_=new Image;_.onload=function(n){var d=o||{},u=d.maxHeight,c=void 0===u?160:u,E=d.maxWidth,l=void 0===E?160:E,T=d.quality,A=void 0===T?1:T,R=d.scale,S=void 0===R?1:R,I=Se(_.width,_.height,{maxHeight:c,maxWidth:l,scale:S});a.width=Math.min(I.w,l),a.height=Math.min(I.h,c),s.drawImage(_,I.x,I.y,I.w,I.h);var f=Re(a,A);f=f.replace(/data:image\/[^;]+;base64,/,""),t({code:e.ErrorCode.SUCCESS,data:r(r({},i),{thumbnail:f,width:Math.ceil(I.w),height:Math.ceil(I.h)})})},_.onerror=function(n){t({code:e.ErrorCode.UPLOAD_FAIL,msg:"get image info fail:".concat(JSON.stringify(n))})},_.src=(window.URL||window.webkitURL).createObjectURL(n)}function Re(t,n){var r=t.toDataURL("image/jpeg",n);return r.length>e.MAX_MESSAGE_CONTENT_BYTES-10240?Re(t,n-.1):r}var Se=function(e,t,n){var r,i,o=n.maxHeight,a=void 0===o?160:o,s=n.maxWidth,_=void 0===s?160:s,d=n.scale,u=e<t,c=0,E=0;return(u?t/e:e/t)>(void 0===d?1:d)?(u?(r=160,(i=t/(e/160))>a&&(E=(i-a)/2)):(i=160,(r=e/(t/160))>_&&(c=(r-_)/2)),{w:r,h:i,x:-c,y:-E}):(u?(i=a,r=e/(t/a)):(r=_,i=t/(e/_)),{w:r,h:i,x:-c,y:-E})},Ie=0,fe=function(){function t(e,t){Object.defineProperty(this,"_promise",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_resolve",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_client",{enumerable:!0,configurable:!0,writable:!0,value:null}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:++Ie}),this._promise=this._handleUpload(e,t)}return Object.defineProperty(t.prototype,"abort",{enumerable:!1,configurable:!0,writable:!0,value:function(){var t;this._resolve({code:e.ErrorCode.UPLOAD_FAIL,msg:"upload abort"}),null===(t=this._client)||void 0===t||t.abort()}}),Object.defineProperty(t.prototype,"_uploadInLoop",{enumerable:!1,configurable:!0,writable:!0,value:function(t,n,r,a,s){return void 0===s&&(s=[]),i(this,void 0,void 0,(function(){var i,_,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:i=0,o.label=1;case 1:return i<t.length?(_=t[i],this._client=new Te[_.type](_.url,r,a),[4,this._client.upload(n)]):[3,4];case 2:if(d=o.sent(),u=d.code,c=d.data,E=d.msg,u===e.ErrorCode.SUCCESS)return[2,{code:u,data:c}];if(u===e.ErrorCode.RC_FILE_SIZE_EXCEED_LIMIT)return[2,{code:u,msg:"File size exceeds the limit"}];if(u===e.ErrorCode.INVALID_PARAMETER_SIZE_NOT_FOUND)return[2,{code:u,msg:"File size not found"}];if(this._client.isAbort())return this._client=null,[2,{code:u,data:c,msg:E}];this._client=null,s.push({type:_.type,url:_.url,code:u,msg:E}),o.label=3;case 3:return i++,[3,1];case 4:return[2,{code:e.ErrorCode.UPLOAD_FAIL,msg:"Failed to upload all channels, msg: ".concat(JSON.stringify(s))}]}}))}))}}),Object.defineProperty(t.prototype,"_handleUpload",{enumerable:!1,configurable:!0,writable:!0,value:function(t,n){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new Promise((function(s){return i(a,void 0,void 0,(function(){var i,a,_,d,u,c,E,l,T,A,R,S,I,f,v,g,O,N,C;return o(this,(function(o){switch(o.label){case 0:return this._resolve=s,i=n.getFileToken,a=n.customDomain,_=n.uploadServer,d=n.ossConfig,u=t.file,c=t.fileType,E=t.thumbnailConfig,[4,i(c,u.name,void 0,void 0,void 0,de(u.size))];case 1:return l=o.sent(),T=function(e,t,n){if(t)return[{type:Y.RCX,url:t,p:"1"}];if(e)return JSON.parse(e).map((function(e){var t={type:Y.QINIU,url:"",p:""};return Object.keys(e).forEach((function(n){"p"===n?t.p=e[n]:(t.type=n,t.url=e[n])})),t})).sort((function(e,t){return e.p-t.p}));var r=[];return n&&r.push({type:Y.QINIU,url:n,p:"1"}),r}(l.ossConfig||d,a,_),0===T.length?(s({code:e.ErrorCode.INVALID_PARAMETER_MEDIA_URL,msg:"uploadUrlList is empty"}),[2]):[4,this._uploadInLoop(T,t,l,i)];case 2:if(A=o.sent(),R=A.code,S=A.data,I=A.msg,R!==e.ErrorCode.SUCCESS||!S)return s({code:R,msg:I}),[2];if(f=S.downloadUrl,v=S.uploadMethod,f)return[3,6];o.label=3;case 3:return o.trys.push([3,5,,6]),[4,n.getFileUrl(c,S.uniqueName,v,u.name)];case 4:return g=o.sent(),f=g.downloadUrl,[3,6];case 5:return O=o.sent(),s({code:O,msg:"Get download url fail"}),[3,6];case 6:return f?(N={downloadUrl:f,uniqueName:(null==S?void 0:S.uniqueName)||"",name:u.name,size:u.size,type:u.type},c!==e.FileType.AUDIO?[3,8]:[4,u.arrayBuffer()]):(s({code:e.ErrorCode.UPLOAD_FAIL,msg:"Get download url fail"}),[2]);case 7:return C=o.sent(),function(t,n,i){(new AudioContext).decodeAudioData(n,(function(n){t({code:e.ErrorCode.SUCCESS,data:r(r({},i),{duration:n.duration,length:n.length})})}),(function(n){t({code:e.ErrorCode.UPLOAD_FAIL,msg:"get audio info fail:".concat(JSON.stringify(n))})}))}(s,C,N),[2];case 8:return c===e.FileType.IMAGE?(Ae(s,u,N,E),[2]):(s({code:R,data:N}),[2])}}))}))}))]}))}))}}),Object.defineProperty(t.prototype,"awaitResult",{enumerable:!1,configurable:!0,writable:!0,value:function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){return[2,this._promise]}))}))}}),t}(),ve={};function ge(e,t){var n=new fe(e,t);return function(e){i(this,void 0,void 0,(function(){return o(this,(function(t){switch(t.label){case 0:return ve[e.id]=e,[4,e.awaitResult()];case 1:return t.sent(),delete ve[e.id],[2]}}))}))}(n),n}function Oe(e){var t=ve[e];return!!t&&(t.abort(),!0)}var Ne=function(e,t){var n=e.getNaviInfoFromCache();return{getFileToken:e.getFileToken.bind(e),getFileUrl:e.getFileUrl.bind(e),customDomain:t||"",ossConfig:null==n?void 0:n.ossConfig,uploadServer:null==n?void 0:n.uploadServer}};function Ce(t){return i(this,void 0,void 0,(function(){var n,r,a;return o(this,(function(s){return n=t.contentDisposition,r=t.fileType,a=t.file,[2,new e.ValidatorManage("","").validate("contentDisposition",n,(function(e){return"inline"===e||"attachment"===e}),!1).validate("fileType",r,(function(t){return!!e.FileType[t]}),!0).validate("file",a,(function(t){return t instanceof File&&(!(r===e.FileType.IMAGE&&!t.type.match(/^image\//))&&!(r===e.FileType.AUDIO&&!t.type.match(/^audio\//)))}),!0).ifReady((function(e,n,r){return function(e,t){return i(this,void 0,void 0,(function(){return o(this,(function(n){return[2,new fe(e,t).awaitResult()]}))}))}(t,Ne(e,r.uploadDomain))}))]}))}))}var pe=He,me=D("sendMessage",(function(t,n,a){return i(void 0,void 0,void 0,(function(){return o(this,(function(s){return[2,new e.ValidatorManage(e.LogTagId.A_SEND_MSG_T,e.LogTagId.A_SEND_MSG_R).validate("message",n,(function(){return n instanceof J}),!0).validate("message",n,(function(){if(!e.isObject(n.content))return!1;if(!(null==a?void 0:a.isMentioned))return!0;var t=n.content.mentionedInfo||{type:null==a?void 0:a.mentionedType,userIdList:null==a?void 0:a.mentionedUserIdList};if(!e.isObject(t))return!1;var r=t.type,i=t.userIdList,o=void 0===i?[]:i,s=t.mentionedContent;return!![e.MentionedType.ALL,e.MentionedType.SINGAL].includes(r)&&(("string"==typeof s||void 0===s)&&(e.isArray(o)&&o.every((function(t){return e.isValidTargetId(t)}))))}),!0).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("isStatusMessage",null==a?void 0:a.isStatusMessage,e.AssertRules.BOOLEAN).validate("disableNotification",null==a?void 0:a.disableNotification,e.AssertRules.BOOLEAN).validate("pushContent",null==a?void 0:a.pushContent,e.AssertRules.STRING).validate("pushData",null==a?void 0:a.pushData,e.AssertRules.STRING).validate("isMentioned",null==a?void 0:a.isMentioned,e.AssertRules.BOOLEAN).validate("mentionedType",null==a?void 0:a.mentionedType,e.AssertRules.NUMBER).validate("mentionedUserIdList",null==a?void 0:a.mentionedUserIdList,e.isLimitedArray({itemValidator:e.isValidTargetId})).validate("directionalUserIdList",null==a?void 0:a.directionalUserIdList,e.isLimitedArray({maxLength:300,itemValidator:e.isValidTargetId})).validate("isVoipPush",null==a?void 0:a.isVoipPush,e.AssertRules.BOOLEAN).validate("canIncludeExpansion",null==a?void 0:a.canIncludeExpansion,e.AssertRules.BOOLEAN).validate("expansion",null==a?void 0:a.expansion,e.AssertRules.OBJECT).validate("pushConfig",null==a?void 0:a.pushConfig,e.AssertRules.OBJECT).validate("messageId",null==a?void 0:a.messageId,e.AssertRules.NUMBER).validate("disableUpdateLastMessage",null==a?void 0:a.disableUpdateLastMessage,e.AssertRules.BOOLEAN).ifReady((function(s,d){return i(void 0,void 0,void 0,(function(){var i,u,c,E,l,T,A,R,S,I,f,v,g,O,N,C,p,m,h,M,G,P;return o(this,(function(o){switch(o.label){case 0:return u=(i=a||{}).disableNotification,c=i.isMentioned,E=i.directionalUserIdList,l=i.canIncludeExpansion,T=i.onSendBefore,A=i.expansion,R=i.pushConfig,S=i.isStatusMessage,I=i.disableUpdateLastMessage,f=d.createTraceId(),v=t.conversationType,g=t.targetId,O=t.channelId,E&&E.length>0&&![3,7,8,10].includes(v)?[2,{code:e.ErrorCode.RC_DIRECTIONAL_MESSAGE_INVALID_CONVERSATION_TYPE}]:I&&v===e.ConversationType.ULTRA_GROUP?[2,{code:e.ErrorCode.INVALID_DISABLE_UPDATE_LAST_MESSAGE}]:(N=function(e,t,n){var i=e.isStatusMessage,o=e.disableNotification,a=e.pushContent,s=e.pushData,_=e.isMentioned,d=e.mentionedType,u=e.mentionedUserIdList,c=e.directionalUserIdList,E=e.isVoipPush,l=e.canIncludeExpansion,T=e.expansion,A=e.isFilerWhiteBlacklist,R=e.pushConfig,S=e.messageId,I=e.disableUpdateLastMessage,f=e.needReceipt,v=n.channelId;return r({isStatusMessage:i,disableNotification:o,pushContent:a,pushData:s,isMentioned:_,mentionedType:d,mentionedUserIdList:u,directionalUserIdList:c,isVoipPush:E,canIncludeExpansion:l,expansion:T,isFilerWhiteBlacklist:A,pushConfig:R,channelId:v||"",messageId:S,disableUpdateLastMessage:I,needReceipt:f},t)}(a||{},n,t),C={isMentioned:!!c,content:n.content,messageType:n.messageType,isPersited:!!n.isPersited,isCounted:!!n.isCounted,disableNotification:!!u,canIncludeExpansion:!!l,expansion:A||null,conversationType:t.conversationType,targetId:t.targetId,channelId:t.channelId,senderUserId:s.getCurrentUserId(),messageUId:"",messageDirection:e.MessageDirection.SEND,receivedTime:0,isStatusMessage:!!S,receivedStatus:e.ReceivedStatus.UNREAD,isOffLineMessage:!1,pushConfig:R,sentStatus:e.SentStatus.SENDING,sentTime:0,directedUserIds:E||[],disableUpdateLastMessage:I},v!==e.ConversationType.ULTRA_GROUP&&(C.isOffLineMessage=!1),p=function(t){C.messageId=t;try{T&&T(_(C))}catch(t){d.error(e.LogTagId.A_SEND_MSG_T,'Callback method "onSendBefore" execution error.\n'.concat(null==t?void 0:t.stack),f)}},d.info(e.LogTagId.A_SEND_MSG_T,JSON.stringify({messageType:n.messageType,conversationType:v,targetId:g,channelId:O}),f),[4,s.sendMessage(v,g,N,p,f)]);case 1:return m=o.sent(),h=m.code,M=m.data,G=m.msg,h===e.ErrorCode.SUCCESS?(d.info(e.LogTagId.A_SEND_MSG_R,JSON.stringify({code:h,messageUId:M.messageUId,messageId:M.messageId}),f),P=_(M),[2,{code:h,data:P}]):(d.warn(e.LogTagId.A_SEND_MSG_R,JSON.stringify({code:h}),f),C.sentTime=(null==M?void 0:M.sentTime)||0,C.sentStatus=null==M?void 0:M.sentStatus,[2,{code:h,msg:G||U[h],data:_(C)}])}}))}))}))]}))}))}));function Ue(t,n){return function(i,o,a,s){var _;if(!e.validate("sendOptions.contentDisposition",null==s?void 0:s.contentDisposition,(function(e){return["inline","attachment"].includes(e)})))return Promise.resolve({code:e.ErrorCode.INVALID_PARAMETER_SENDOPTIONS});var d={contentDisposition:null==s?void 0:s.contentDisposition};if(s&&"thumbnailConfig"in s){var u=null===(_=s)||void 0===_?void 0:_.thumbnailConfig;if(!(e.validate("sendOptions.maxHeight",null==u?void 0:u.maxHeight,e.AssertRules.NUMBER)&&e.validate("sendOptions.maxWidth",null==u?void 0:u.maxWidth,e.AssertRules.NUMBER)&&e.validate("sendOptions.quality",null==u?void 0:u.quality,e.AssertRules.NUMBER)&&e.validate("sendOptions.scale",null==u?void 0:u.scale,e.AssertRules.NUMBER)))return Promise.resolve({code:e.ErrorCode.INVALID_PARAMETER_SENDOPTIONS});d.thumbnailConfig=u}return N((function(_,u,c){return new Promise((function(_){var c=u.createTraceId();u.info(e.LogTagId.L_MEDIA_UPLOAD_T,JSON.stringify({filesize:o.file.size,type:t}),c),Ce({file:o.file,fileType:t,onProgress:function(e,t){var n;null===(n=null==a?void 0:a.onProgress)||void 0===n||n.call(a,Math.floor(e/t*100))},contentDisposition:d.contentDisposition,thumbnailConfig:d.thumbnailConfig}).then((function(t){var d,E=t.code,l=t.data,T=t.msg;if(E===e.ErrorCode.SUCCESS&&l){var A=(null===(d=null==a?void 0:a.onComplete)||void 0===d?void 0:d.call(a,r({url:l.downloadUrl},l)))||n(l,o);return u.info(e.LogTagId.L_MEDIA_UPLOAD_R,JSON.stringify({downurl:l.downloadUrl,code:e.ErrorCode.SUCCESS}),c),void me(i,A,s).then(_)}_({code:E,msg:T})}))}))}))}}var he=Ue(e.FileType.FILE,(function(e,t){var n,r,i={name:e.name,size:t.file.size,type:(n=e.name,r=n.match(/\.(\w+)$/),r?r[1]:"bin"),fileUrl:e.downloadUrl};return t.user&&(i.user=t.user),t.extra&&(i.extra=t.extra),t.mentionedInfo&&(i.mentionedInfo=t.mentionedInfo),new X(i)})),Me=Ue(e.FileType.IMAGE,(function(e,t){var n={content:e.thumbnail,imageUri:e.downloadUrl,thumWidth:e.width,thumHeight:e.height};return t.user&&(n.user=t.user),t.extra&&(n.extra=t.extra),t.mentionedInfo&&(n.mentionedInfo=t.mentionedInfo),t.audit&&(n.audit=t.audit),new z(n)})),Ge=Ue(e.FileType.IMAGE,(function(e,t){var n={gifDataSize:e.size,remoteUrl:e.downloadUrl,width:e.width,height:e.height};return t.user&&(n.user=t.user),t.extra&&(n.extra=t.extra),t.mentionedInfo&&(n.mentionedInfo=t.mentionedInfo),t.audit&&(n.audit=t.audit),new Q(n)})),Pe=Ue(e.FileType.AUDIO,(function(e,t){var n={remoteUrl:e.downloadUrl,duration:e.duration,type:e.type};return t.user&&(n.user=t.user),t.extra&&(n.extra=t.extra),t.mentionedInfo&&(n.mentionedInfo=t.mentionedInfo),t.audit&&(n.audit=t.audit),new q(n)})),ye=Ue(e.FileType.SIGHT,(function(e,t){var n={sightUrl:e.downloadUrl,content:t.thumbnail,duration:t.duration,size:t.file.size||e.size,name:t.name||e.name};return t.user&&(n.user=t.user),t.extra&&(n.extra=t.extra),t.mentionedInfo&&(n.mentionedInfo=t.mentionedInfo),t.audit&&(n.audit=t.audit),new ne(n)}));Ue(e.FileType.COMBINE_HTML,(function(e,t){var n={remoteUrl:e.downloadUrl,nameList:t.nameList,summaryList:t.summaryList,conversationType:t.conversationType};return t.user&&(n.user=t.user),t.extra&&(n.extra=t.extra),t.mentionedInfo&&(n.mentionedInfo=t.mentionedInfo),new k(n)}));var Le=function(t,n,r,a){return i(void 0,void 0,void 0,(function(){var i,s;return o(this,(function(o){switch(o.label){case 0:return!e.validate("messageList",a,e.AssertRules.OBJECT,!0)||Object.keys(a).some((function(t){return!e.validate(t,a[t],e.AssertRules.ARRAY)}))?[2,Promise.resolve({code:e.ErrorCode.MSG_LIMIT_ERROR})]:(i=[],Object.keys(a).forEach((function(e){a[e].forEach((function(e){-1===i.indexOf(e)&&i.push(e)}))})),i.length?[4,t.sendReadReceiptMessage(r.targetId,i,r.channelId)]:(n.warn(e.LogTagId.A_SEND_MSG_R,JSON.stringify({code:e.ErrorCode.MSG_LIMIT_ERROR,msg:"Error in parameter messageList."})),[2,Promise.resolve({code:e.ErrorCode.MSG_LIMIT_ERROR})]));case 1:return s=o.sent().code,[2,Promise.resolve({code:s})]}}))}))};var De=D("recallMessage",(function(e,t){return i(void 0,void 0,void 0,(function(){return o(this,(function(n){return[2,C().next((function(n){return i(void 0,void 0,void 0,(function(){return o(this,(function(r){switch(r.label){case 0:return[4,n.recallMessage(e,t)];case 1:return[2,r.sent().trans((function(e){return _(e)}))]}}))}))}))]}))}))}));var be={TEXT:e.MessageType.TextMessage,VOICE:e.MessageType.VOICE,HQ_VOICE:e.MessageType.HQ_VOICE,IMAGE:e.MessageType.IMAGE,GIF:e.MessageType.GIF,RICH_CONTENT:e.MessageType.RICH_CONTENT,LOCATION:e.MessageType.LOCATION,FILE:e.MessageType.FILE,SIGHT:e.MessageType.SIGHT,COMBINE:e.MessageType.COMBINE,COMBINE_V2:e.MessageType.COMBINE_V2,CHRM_KV_NOTIFY:e.MessageType.CHRM_KV_NOTIFY,LOG_COMMAND:e.MessageType.LOG_COMMAND,EXPANSION_NOTIFY:e.MessageType.EXPANSION_NOTIFY,REFERENCE:e.MessageType.REFERENCE,RECALL_MESSAGE_TYPE:e.MessageType.RECALL,STREAM_MESSAGE:e.MessageType.STREAM_MESSAGE};function Ve(e,t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,C().next((function(s){return i(a,void 0,void 0,(function(){return o(this,(function(i){switch(i.label){case 0:return[4,s.searchConversationByContent(e,t,n,r)];case 1:return[2,i.sent().trans((function(e){return e.map((function(e){return d(e)}))}))]}}))}))}))]}))}))}function we(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_CLEAR_MESSAGES_T,T.A_E_CLEAR_MESSAGES_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(e,r){return i(n,void 0,void 0,(function(){var n,i,a,s,_;return o(this,(function(o){switch(o.label){case 0:return n=t.conversationType,i=t.targetId,a=t.channelId,s=r.createTraceId(),r.info(T.A_E_CLEAR_MESSAGES_T,JSON.stringify({conversationType:n,targetId:i,channelId:a}),s),[4,e.clearMessages(n,i,a)];case 1:return _=o.sent(),r.info(T.A_E_CLEAR_MESSAGES_R,JSON.stringify({code:_}),s),[2,{code:_}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))}function xe(t,n,r){return i(this,void 0,void 0,(function(){var a,s,_,d=this;return o(this,(function(u){return e.usingCppEngine()?(a=t.conversationType,s=t.targetId,_=t.channelId,[2,new e.ValidatorManage(T.A_E_DELETE_MESSAGES_BY_TIMESTAMP_T,T.A_E_DELETE_MESSAGES_BY_TIMESTAMP_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("timestamp",n,e.AssertRules.NUMBER,!0).validate("cleanSpace",r,e.AssertRules.BOOLEAN).ifReady((function(t,u){return i(d,void 0,void 0,(function(){var i,d,c;return o(this,(function(o){switch(o.label){case 0:return i=u.createTraceId(),u.info(T.A_E_DELETE_MESSAGES_BY_TIMESTAMP_T,JSON.stringify({conversationType:a,targetId:s,channelId:_,timestamp:n,cleanSpace:r}),i),[4,t.deleteMessagesByTimestamp(a,s,n,r,_)];case 1:return d=o.sent(),c=d===e.ErrorCode.SUCCESS?"info":"warn",u[c](T.A_E_DELETE_MESSAGES_BY_TIMESTAMP_R,JSON.stringify({code:d}),i),[2,{code:d}]}}))}))}))]):[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))}var Fe=function(t,n){var r,i;if(!e.validate("options",t,e.AssertRules.ARRAY,!0)||!e.validate("options",t,(function(e){return!(t.length>20||0===t.length)||(n.error(T.A_BATCH_CLEAR_MSG_R,"conversations length should not be greater than 20 and not be equal to 0"),!1)}),!0))return e.ErrorCode.INVALID_PARAMETER_CLEAR_MESSAGE_OPTION_LIST;try{for(var o=function(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(t),a=o.next();!a.done;a=o.next()){var s=a.value;if(!(e.validate("options",s,e.AssertRules.OBJECT,!0)&&e.validate("options.targetId",s.targetId,e.AssertRules.STRING,!0)&&e.validate("options.conversationType",s.conversationType,(function(t){var r=[e.ConversationType.PRIVATE,e.ConversationType.GROUP,e.ConversationType.CHATROOM,e.ConversationType.SYSTEM].includes(t);return r||n.error(T.A_BATCH_CLEAR_MSG_R,"conversations.conversationType is invalid, It should be one of the following values:\n        ConversationType.PRIVATE,\n        ConversationType.GROUP,\n        ConversationType.CHATROOM,\n        ConversationType.SYSTEM,\n      "),r}),!0)&&e.validate("options.timestamp",s.timestamp,e.AssertRules.NUMBER,!0)&&e.validate("options.channelId",s.channelId,e.AssertRules.ONLY_STRING)))return e.ErrorCode.INVALID_PARAMETER_CLEAR_MESSAGE_OPTION_LIST}}catch(e){r={error:e}}finally{try{a&&!a.done&&(i=o.return)&&i.call(o)}finally{if(r)throw r.error}}};function Be(t,n,r){return void 0===r&&(r={}),i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(T.A_E_INSERT_MESSAGE_T,T.A_E_INSERT_MESSAGE_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("userId",n,(function(t){return e.validate("message.senderUserId",null==t?void 0:t.senderUserId,e.AssertRules.STRING,!0)}),!0).validate("messageType",null==n?void 0:n.messageType,e.AssertRules.STRING,!0).validate("content",null==n?void 0:n.content,e.AssertRules.OBJECT,!0).validate("messageDirection",n.messageDirection,e.AssertRules.NUMBER,!0).validate("canIncludeExpansion",n.canIncludeExpansion,e.AssertRules.BOOLEAN).validate("expansion",n.expansion,e.AssertRules.OBJECT).validate("disableNotification",n.disableNotification,e.AssertRules.BOOLEAN).validate("sentTime",n.sentTime,e.AssertRules.NUMBER).validate("sentStatus",n.sentStatus,e.AssertRules.NUMBER).validate("receivedStatus",n.receivedStatus,e.AssertRules.NUMBER).validate("receivedStatusInfo",n.receivedStatusInfo,e.AssertRules.OBJECT).validate("directedUserIds",null==n?void 0:n.directedUserIds,e.isLimitedArray({maxLength:300,itemValidator:e.notEmptyString})).validate("searchContent",null==r?void 0:r.searchContent,e.AssertRules.STRING).validate("disableUpdateLastMessage",null==r?void 0:r.disableUpdateLastMessage,e.AssertRules.BOOLEAN).ifReady((function(s,d){return i(a,void 0,void 0,(function(){var i,a,u,c,E,l,A,R,S,I,f,v,g,O,N,C,p,m,h,M,G,P;return o(this,(function(o){switch(o.label){case 0:return i=n.messageType,a=n.content,u=n.senderUserId,c=n.messageDirection,E=n.canIncludeExpansion,l=n.expansion,A=n.disableNotification,R=n.sentTime,S=n.sentStatus,I=n.receivedStatus,f=n.receivedStatusInfo,v=n.directedUserIds,g=n.disableUpdateLastMessage,O=n.conversationType,g&&O===e.ConversationType.ULTRA_GROUP?[2,{code:e.ErrorCode.INVALID_DISABLE_UPDATE_LAST_MESSAGE}]:(N=d.createTraceId(),d.info(T.A_E_INSERT_MESSAGE_T,JSON.stringify({conversationType:t.conversationType,targetId:t.targetId,messageType:i,sentTime:R}),N),C=r.isUnread,p=r.searchContent,m=I,f&&(m=e.transformReceivedStatusFlag(f)),h={senderUserId:u,messageType:i,content:a,messageDirection:c,sentTime:R,sentStatus:S,searchContent:p,isUnread:!!C,messageUId:"",disableNotification:A,canIncludeExpansion:E,expansionMsg:JSON.stringify(l),channelId:t.channelId||"",readStatus:m,directedUserIds:v,disableUpdateLastMessage:g||r.disableUpdateLastMessage},[4,s.insertMessage(t.conversationType,t.targetId,h)]);case 1:return M=o.sent(),G=M.code,P=M.data,G===e.ErrorCode.SUCCESS?(d.info(T.A_E_INSERT_MESSAGE_R,JSON.stringify({code:G,messageId:null==P?void 0:P.messageId}),N),[2,{code:G,data:_(P)}]):(d.warn(T.A_E_INSERT_MESSAGE_R,JSON.stringify({code:G}),N),[2,{code:G,msg:U[G]}])}}))}))}))]}))}))}function Je(t,n,r){return void 0===r&&(r={}),i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(T.A_E_INSERT_MESSAGE_T,T.A_E_INSERT_MESSAGE_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("messageType",null==n?void 0:n.messageType,e.AssertRules.STRING,!0).validate("content",null==n?void 0:n.content,e.AssertRules.OBJECT,!0).validate("searchContent",null==r?void 0:r.searchContent,e.AssertRules.STRING).validate("disableUpdateLastMessage",null==r?void 0:r.disableUpdateLastMessage,e.AssertRules.BOOLEAN).ifReady((function(s,d){return i(a,void 0,void 0,(function(){var i,a,u,c,E,l,A,R,S,I,f;return o(this,(function(o){switch(o.label){case 0:return i=n.messageType,a=n.content,u=d.createTraceId(),d.info(T.A_E_INSERT_MESSAGE_T,JSON.stringify({conversationType:t.conversationType,targetId:t.targetId,messageType:i}),u),c=r.isUnread,E=r.searchContent,(l=r.disableUpdateLastMessage)&&t.conversationType===e.ConversationType.ULTRA_GROUP?[2,{code:e.ErrorCode.INVALID_DISABLE_UPDATE_LAST_MESSAGE}]:(A={senderUserId:"",messageType:i,content:a,searchContent:E,messageDirection:1,isUnread:!!c,messageUId:"",channelId:null!==(f=t.channelId)&&void 0!==f?f:"",disableUpdateLastMessage:l},[4,s.insertMessage(t.conversationType,t.targetId,A)]);case 1:return R=o.sent(),S=R.code,I=R.data,S===e.ErrorCode.SUCCESS?(d.info(T.A_E_INSERT_MESSAGE_R,JSON.stringify({code:S,messageId:null==I?void 0:I.messageId}),u),[2,{code:S,data:_(I)}]):(d.warn(T.A_E_INSERT_MESSAGE_R,JSON.stringify({code:S}),u),[2,{code:S,msg:U[S]}])}}))}))}))]}))}))}function He(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){switch(t.label){case 0:return[4,C().next((function(t){return t.getLocalMessage(e)}))];case 1:return[2,t.sent().trans((function(e){return _(e)}))]}}))}))}var Ye=Object.freeze({__proto__:null,getAllConversationList:function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,C().next((function(r){return i(n,void 0,void 0,(function(){var n;return o(this,(function(i){switch(i.label){case 0:return n={type:e.ConversationListFilterType.ALL,params:{channelId:t}},[4,r.getConversationListByFilter(n)];case 1:return[2,i.sent().trans((function(e){return e.map((function(e){return d(e)}))}))]}}))}))}))]}))}))},getConversationList:function(t,n,r,a){return i(this,void 0,void 0,(function(){var s=this;return o(this,(function(_){return[2,C().next((function(_){return i(s,void 0,void 0,(function(){var i;return o(this,(function(o){switch(o.label){case 0:return i={type:e.ConversationListFilterType.TIMESTAMP,params:{startTime:t,count:n,channelId:r,topPriority:a}},[4,_.getConversationListByFilter(i)];case 1:return[2,o.sent().trans((function(e){return e.map((function(e){return d(e)}))}))]}}))}))}))]}))}))},searchConversationByContent:Ve,getConversations:function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_GET_CONVERSATIONS_T,T.A_GET_CONVERSATIONS_R).validate("conversationList",t,e.isLimitedArray({maxLength:100,minLength:1,itemValidator:function(t){return e.isValidConversation(t,[e.ConversationType.PRIVATE,e.ConversationType.GROUP,e.ConversationType.SYSTEM])}}),!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,u;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_CONVERSATIONS_T,JSON.stringify({conversations:t}),n),[4,r.getConversations(t)];case 1:return i=o.sent(),s=i.code,_=i.data,s===e.ErrorCode.SUCCESS&&_?(u=_.map((function(e){return d(e)})),a.info(T.A_GET_CONVERSATIONS_R,JSON.stringify({code:s,length:u.length}),n),[2,{code:s,data:u}]):(a.warn(T.A_GET_CONVERSATIONS_R,JSON.stringify({code:s}),n),[2,{code:s,msg:U[s]}])}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},searchMessages:function(t,n,r,a,s){return i(this,void 0,void 0,(function(){var d=this;return o(this,(function(u){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_SEARCH_MESSAGES_T,T.A_E_SEARCH_MESSAGES_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("keyword",n,e.AssertRules.STRING,!0).validate("startTime",r,e.AssertRules.NUMBER,!0).validate("count",a,e.AssertRules.NUMBER,!0).validate("messageTypes",s,e.isLimitedArray({itemValidator:e.notEmptyString})).ifReady((function(u,c){return i(d,void 0,void 0,(function(){var i,d,E,l,A,R,S,I,f,v,g;return o(this,(function(o){switch(o.label){case 0:return i=t.targetId,d=t.conversationType,E=t.channelId,l=c.createTraceId(),c.info(T.A_E_SEARCH_MESSAGES_T,JSON.stringify({targetId:i,conversationType:d,channelId:E,keyword:n,startTime:r,count:a}),l),s?[4,u.searchMessageByContentAndMessageType(d,i,n,r,a,s,E)]:[3,2];case 1:return A=o.sent(),[3,6];case 2:return e.isUndefined(E)?[4,u.searchMessageByContentWithAllChannel(d,i,n,r,a)]:[3,4];case 3:return A=o.sent(),[3,6];case 4:return[4,u.searchMessageByContent(d,i,n,r,a,0,E)];case 5:A=o.sent(),o.label=6;case 6:return R=A.code,S=A.data,R===e.ErrorCode.SUCCESS&&S?(I=S.messages,f=S.count,v=[],g=I.map((function(e){return v.push(e.messageUId),_(e)})),c.info(T.A_E_SEARCH_MESSAGES_R,JSON.stringify({code:R,list:v}),l),[2,{code:R,data:{messages:g,count:f}}]):(c.warn(T.A_E_SEARCH_MESSAGES_R,JSON.stringify({code:R}),l),[2,{code:R,msg:U[R]}])}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},searchMessageInTimeRange:function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_SEARCH_MESSAGE_IN_TIME_RANGE_T,T.A_E_SEARCH_MESSAGE_IN_TIME_RANGE_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("startTime",null==n?void 0:n.startTime,e.AssertRules.NUMBER,!0).validate("endTime",null==n?void 0:n.endTime,e.AssertRules.NUMBER,!0).validate("offset",null==n?void 0:n.offset,e.AssertRules.NUMBER).validate("limit",null==n?void 0:n.limit,e.AssertRules.NUMBER).validate("keyword",null==n?void 0:n.keyword,e.AssertRules.STRING,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,d,u,c,E,l,A,R,S,I;return o(this,(function(o){switch(o.label){case 0:return r=t.conversationType,i=t.targetId,d=n.keyword,u=n.startTime,c=n.endTime,E=n.offset,l=n.limit,E=E||0,l=l||5,A=s.createTraceId(),s.info(T.A_E_SEARCH_MESSAGE_IN_TIME_RANGE_T,JSON.stringify({targetId:i,conversationType:r,keyword:d,startTime:u,endTime:c,offset:E,limit:l}),A),[4,a.searchMessageByContentInTimeRangeWithAllChannel(r,i,d,u,c,E,l)];case 1:return R=o.sent(),S=R.code,I=R.data,S===e.ErrorCode.SUCCESS&&I?[2,{code:S,data:{messages:I.messages.map((function(e){return _(e)}))}}]:(s.warn(T.A_E_SEARCH_MESSAGE_IN_TIME_RANGE_R,JSON.stringify({code:S}),A),[2,{code:S}])}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},searchMessagesByUser:function(t,n,r,a){return i(this,void 0,void 0,(function(){var s=this;return o(this,(function(d){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_SEARCH_MESSAGES_BY_USER_T,T.A_E_SEARCH_MESSAGES_BY_USER_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("userId",n,e.AssertRules.STRING,!0).validate("startTime",r,e.AssertRules.NUMBER,!0).validate("count",a,e.AssertRules.NUMBER,!0).ifReady((function(d,u){return i(s,void 0,void 0,(function(){var i,s,c,E,l,A,R;return o(this,(function(o){switch(o.label){case 0:return i=t.conversationType,s=t.targetId,c=t.channelId,E=u.createTraceId(),u.info(T.A_E_SEARCH_MESSAGES_BY_USER_T,JSON.stringify({targetId:s,conversationType:i,channelId:c,startTime:r,userId:n,count:a}),E),[4,d.searchMessagesByUser(i,s,n,r,a,c)];case 1:return l=o.sent(),A=l.code,R=l.data,A===e.ErrorCode.SUCCESS&&R?[2,{code:A,data:{messages:R.messages.map((function(e){return _(e)}))}}]:(u.warn(T.A_E_SEARCH_MESSAGES_BY_USER_R,JSON.stringify({code:A})),[2,{code:A}])}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},getHistoryMessagesByMessageTypes:function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_GET_HISTORY_MESSAGES_BY_MESSAGE_TYPES_T,T.A_E_GET_HISTORY_MESSAGES_BY_MESSAGE_TYPES_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("count",null==n?void 0:n.count,e.AssertRules.NUMBER).validate("count",null==n?void 0:n.count,(function(e){var t=e>0&&e<=100;return t||null===console||void 0===console||console.error("option.count is invalid, It should be greater than 0 and less than 100"),t})).validate("timestamp",null==n?void 0:n.timestamp,e.AssertRules.NUMBER).validate("order",null==n?void 0:n.order,(function(e){return[0,1].includes(e)})).validate("messageTypes",null==n?void 0:n.messageTypes,(function(t){return!!e.isArray(t)&&!t.some((function(t){return!e.notEmptyString(t)}))}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,d,u,c,E,l,A,R,S,I,f,v,g,O,N,C,p,m;return o(this,(function(o){switch(o.label){case 0:return r=n.count,i=void 0===r?20:r,d=n.timestamp,u=void 0===d?0:d,c=n.order,E=void 0===c?0:c,l=n.messageTypes,A=t.conversationType,R=t.targetId,S=t.channelId,I=void 0===S?"":S,f=s.createTraceId(),s.info(T.A_E_GET_HISTORY_MESSAGES_BY_MESSAGE_TYPES_T,JSON.stringify({targetId:R,conversationType:A,channelId:I,count:i,timestamp:u,messageTypes:l}),f),[4,a.getHistoryMessagesByObjectNames(A,R,u,i,l,E,I)];case 1:return v=o.sent(),g=v.code,O=v.data,g===e.ErrorCode.SUCCESS?(C=(N=O).list,p=N.hasMore,m=C.map((function(e){return _(e)})),[2,{code:g,data:{messages:m,hasMore:p,list:m}}]):(s.warn(T.A_E_GET_HISTORY_MESSAGES_BY_MESSAGE_TYPES_R,JSON.stringify({code:g})),[2,{code:g,msg:U[g]}])}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},getContinuousMessages:function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_GET_CONTINUOUS_MESSAGES_T,T.A_E_GET_CONTINUOUS_MESSAGES_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("timestamp",null==n?void 0:n.timestamp,e.AssertRules.NUMBER).validate("count",null==n?void 0:n.count,e.AssertRules.NUMBER).validate("count",null==n?void 0:n.count,(function(e){var t=e>0&&e<=100;return t||null===console||void 0===console||console.error('"options.count" is invalid, It should be greater than 0 and less than 100'),t})).validate("order",null==n?void 0:n.order,(function(e){return 0===e||1===e})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,d,u,c,E,l,A,R,S,I,f,v,g,O;return o(this,(function(o){switch(o.label){case 0:return r=n.count,i=n.timestamp,d=n.order,u=t.conversationType,c=t.targetId,E=t.channelId,l=s.createTraceId(),s.info(T.A_E_GET_CONTINUOUS_MESSAGES_T,JSON.stringify({targetId:c,conversationType:u,channelId:E,count:r,timestamp:i}),l),(a.getNaviInfoFromCache()||{}).historyMsg?[4,a.getContinuousMessages(u,c,i||0,r||20,d||0,E||"")]:[2,{code:e.ErrorCode.MESSAGE_STORAGE_SERVICE_UNAVAILABLE}];case 1:return A=o.sent(),R=A.code,S=A.data,R===e.ErrorCode.SUCCESS?(f=(I=S).list,v=I.timestamp,g=I.hasMore,O=f.map((function(e){return _(e)})),[2,{code:R,data:{list:O,timestamp:v,hasMore:g}}]):(s.warn(T.A_E_GET_CONTINUOUS_MESSAGES_R,JSON.stringify({code:R})),[2,{code:R,msg:U[R]}])}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},setMessageStatusToRead:function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_SET_MESSAGE_STATUS_TO_READ_T,T.A_E_SET_MESSAGE_STATUS_TO_READ_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("timestamp",n,e.AssertRules.NUMBER,!0).ifReady((function(e,a){return i(r,void 0,void 0,(function(){var r,i,s,_,d;return o(this,(function(o){switch(o.label){case 0:return r=t.conversationType,i=t.targetId,s=t.channelId,_=a.createTraceId(),a.info(T.A_E_SET_MESSAGE_STATUS_TO_READ_T,JSON.stringify({targetId:i,conversationType:r,channelId:s,timestamp:n}),_),[4,e.setMessageStatusToRead(r,i,n,s)];case 1:return d=o.sent(),a.info(T.A_E_SET_MESSAGE_STATUS_TO_READ_R,JSON.stringify(d),_),[2,d]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},setMessageReceivedStatus:function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return console.warn("`setMessageReceivedStatus` has being deprecated, use `setMessageReceivedStatusInfo` instead."),e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_SET_MESSAGE_RECEIVED_STATUS_T,T.A_E_SET_MESSAGE_RECEIVED_STATUS_R).validate("messageId",t,e.AssertRules.NUMBER,!0).validate("receivedStatus",n,(function(t){return!e.isUndefined(e.ReceivedStatus[t])}),!0).ifReady((function(e,a){return i(r,void 0,void 0,(function(){var r,i;return o(this,(function(o){switch(o.label){case 0:return r=a.createTraceId(),a.info(T.A_E_SET_MESSAGE_RECEIVED_STATUS_T,JSON.stringify({messageId:t,receivedStatus:n}),r),[4,e.setMessageReceivedStatus(t,n)];case 1:return i=o.sent(),a.info(T.A_E_SET_MESSAGE_RECEIVED_STATUS_R,JSON.stringify({code:i}),r),[2,{code:i}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},setMessageReceivedStatusInfo:function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_SET_MESSAGE_SENT_STATUS_INFO_T,T.A_E_SET_MESSAGE_SENT_STATUS_INFO_R).validate("messageId",t,e.AssertRules.NUMBER,!0).validate("receivedStatusInfo",n,e.AssertRules.OBJECT,!0).validate("receivedStatusInfo",null==n?void 0:n.isRead,e.AssertRules.BOOLEAN).validate("receivedStatusInfo",null==n?void 0:n.isListened,e.AssertRules.BOOLEAN).validate("receivedStatusInfo",null==n?void 0:n.isDownload,e.AssertRules.BOOLEAN).validate("receivedStatusInfo",null==n?void 0:n.isRetrieved,e.AssertRules.BOOLEAN).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=e.transformReceivedStatusFlag(n),i=s.createTraceId(),s.info(T.A_E_SET_MESSAGE_SENT_STATUS_INFO_T,JSON.stringify({messageId:t,sentStatus:r}),i),[4,a.setMessageReceivedStatus(t,r)];case 1:return _=o.sent(),s.info(T.A_E_SET_MESSAGE_SENT_STATUS_INFO_R,JSON.stringify({code:_}),i),[2,{code:_}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},setMessageSentStatus:function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_SET_MESSAGE_SENT_STATUS_T,T.A_E_SET_MESSAGE_SENT_STATUS_T).validate("messageId",t,e.AssertRules.NUMBER,!0).validate("sentStatus",n,(function(t){return!e.isUndefined(e.SentStatus[t])}),!0).ifReady((function(e,a){return i(r,void 0,void 0,(function(){var r,i;return o(this,(function(o){switch(o.label){case 0:return r=a.createTraceId(),a.info(T.A_E_SET_MESSAGE_SENT_STATUS_T,JSON.stringify({messageId:t,sentStatus:n}),r),[4,e.setMessageSentStatus(t,n)];case 1:return i=o.sent(),a.info(T.A_E_SET_MESSAGE_SENT_STATUS_R,JSON.stringify({code:i}),r),[2,{code:i}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},setMessageContent:function(t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_SET_MESSAGE_CONTENT_T,T.A_E_SET_MESSAGE_CONTENT_R).validate("messageId",t,e.AssertRules.NUMBER,!0).validate("content",n,e.AssertRules.OBJECT,!0).validate("messageType",r,e.AssertRules.STRING,!0).ifReady((function(e,s){return i(a,void 0,void 0,(function(){var i,a;return o(this,(function(o){switch(o.label){case 0:return i=s.createTraceId(),s.info(T.A_E_SET_MESSAGE_CONTENT_T,JSON.stringify({messageId:t,messageType:r}),i),[4,e.setMessageContent(t,n,r)];case 1:return a=o.sent(),s.info(T.A_E_SET_MESSAGE_CONTENT_R,JSON.stringify({code:a}),i),[2,{code:a}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},deleteMessages:function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_DELETE_MESSAGES_T,T.A_E_DELETE_MESSAGES_R).validate("messageIds",t,(function(t){return!!e.isArray(t)&&!t.some((function(t){return!e.isNumber(t)}))}),!0).ifReady((function(e,r){return i(n,void 0,void 0,(function(){var n,i;return o(this,(function(o){switch(o.label){case 0:return n=r.createTraceId(),r.info(T.A_E_DELETE_MESSAGES_T,JSON.stringify({messageIds:t}),n),[4,e.deleteMessages(t)];case 1:return i=o.sent(),r.info(T.A_E_DELETE_MESSAGES_R,JSON.stringify({code:i}),n),[2,{code:i}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},clearMessages:we,deleteMessagesByTimestamp:xe,batchClearMessagesByTimestamp:function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_BATCH_CLEAR_MSG_T,T.A_BATCH_CLEAR_MSG_R).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return Fe(t,a)?[2,{code:e.ErrorCode.INVALID_PARAMETER_CLEAR_MESSAGE_OPTION_LIST}]:(n=t.map((function(e){return{conversationType:e.conversationType,targetId:e.targetId,timestamp:e.timestamp,channelId:e.channelId||""}})),i=a.createTraceId(),a.info(T.A_BATCH_CLEAR_MSG_T,"options: ".concat(JSON.stringify(n)),i),[4,r.batchClearMessagesByTimestamp(n)]);case 1:return s=o.sent(),a[e.ErrorCode.SUCCESS===s?"info":"warn"](T.A_BATCH_CLEAR_MSG_R,"code: ".concat(s),i),[2,{code:s}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},insertMessage:function(t,n,r){return void 0===r&&(r={}),i(this,void 0,void 0,(function(){return o(this,(function(i){return e.usingCppEngine()?n instanceof J?[2,Je(t,n,r)]:[2,Be(t,n,r)]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},batchInsertMessage:function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_BATCH_INSERT_MESSAGE_R,T.A_E_BATCH_INSERT_MESSAGE_R).validate("messages",t,(function(t){var n,r,i,o;if(!e.validate("messages",t,e.AssertRules.ARRAY,!0))return!1;if(t.length<=0||t.length>=500)return!1;for(var a in t)if(!e.validate("message",t[a],e.AssertRules.OBJECT,!0)||!e.validate("message.conversationType",t[a].conversationType,(function(t){return[e.ConversationType.PRIVATE,e.ConversationType.DISCUSSION,e.ConversationType.GROUP,e.ConversationType.CUSTOMER_SERVICE,e.ConversationType.SYSTEM,e.ConversationType.APP_PUBLIC_SERVICE,e.ConversationType.PUBLIC_SERVICE].includes(t)}),!0)||!e.validate("message.targetId",t[a].targetId,e.AssertRules.STRING,!0)||!e.validate("message.senderUserId",t[a].senderUserId,e.AssertRules.STRING,!0)||!e.validate("message.messageType",t[a].messageType,e.AssertRules.STRING,!0)||!e.validate("message.content",t[a].content,e.AssertRules.OBJECT,!0)||!e.validate("message.messageDirection",t[a].messageDirection,e.AssertRules.NUMBER,!0)||!e.validate("message.canIncludeExpansion",t[a].canIncludeExpansion,e.AssertRules.BOOLEAN)||!e.validate("message.expansion",t[a].expansion,e.AssertRules.OBJECT)||!e.validate("message.disableNotification",t[a].disableNotification,e.AssertRules.BOOLEAN)||!e.validate("message.sentTime",t[a].sentTime,e.AssertRules.NUMBER)||!e.validate("message.sentStatus",t[a].sentStatus,e.AssertRules.NUMBER)||!e.validate("message.disableUpdateLastMessage",t[a].disableUpdateLastMessage,e.AssertRules.BOOLEAN)||!e.validate("options.isUnread",null===(n=t[a])||void 0===n?void 0:n.isUnread,e.AssertRules.BOOLEAN)||!e.validate("options.searchContent",null===(r=t[a])||void 0===r?void 0:r.searchContent,e.AssertRules.STRING)||!e.validate("options.receivedStatus",null===(i=t[a])||void 0===i?void 0:i.receivedStatus,e.AssertRules.NUMBER)||!e.validate("options.receivedStatusInfo",null===(o=t[a])||void 0===o?void 0:o.receivedStatusInfo,e.AssertRules.OBJECT)||t[a].directedUserIds&&t[a].directedUserIds.some((function(t){return!e.validate("userId",t,e.AssertRules.STRING,!0)}))&&t[a].directedUserIds.length<300)return console.warn("then ".concat(a," index message validation failed")),!1;return!0}),!0).validate("checkDuplicate",n,e.AssertRules.BOOLEAN).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_E_BATCH_INSERT_MESSAGE_T,JSON.stringify({checkDuplicate:n,length:t.length}),r),[4,a.batchInsertMessage(t,n)];case 1:return i=o.sent(),_=i.code,d=i.data,_===e.ErrorCode.SUCCESS?(s.info(T.A_E_BATCH_INSERT_MESSAGE_R,JSON.stringify({code:_,data:d}),r),[2,{code:_,data:d}]):(s.warn(T.A_E_BATCH_INSERT_MESSAGE_R,JSON.stringify({code:_}),r),[2,{code:_,msg:U[_]}])}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},getMessageCount:function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_GET_MESSAGE_COUNT_T,T.A_E_GET_MESSAGE_COUNT_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(e,r){return i(n,void 0,void 0,(function(){var n,i,a,s,_,d,u;return o(this,(function(o){switch(o.label){case 0:return n=t.conversationType,i=t.targetId,a=t.channelId,s=r.createTraceId(),r.info(T.A_E_GET_MESSAGE_COUNT_T,JSON.stringify({conversationType:n,targetId:i,channelId:a}),s),[4,e.getMessageCount(n,i,a)];case 1:return _=o.sent(),d=_.code,u=_.data,r.info(T.A_E_GET_MESSAGE_COUNT_R,JSON.stringify({code:d,data:u}),s),[2,{code:d,data:u}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},getPrivateMessageDeliverTime:function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_GET_PRIVATE_MESSAGE_DELIVER_TIME_T,T.A_E_GET_PRIVATE_MESSAGE_DELIVER_TIME_R).validate("messageUId",t,e.AssertRules.STRING,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_E_GET_PRIVATE_MESSAGE_DELIVER_TIME_T,JSON.stringify({messageUId:t}),n),[4,r.getPrivateMessageDeliverTime(t)];case 1:return i=o.sent(),s=i.code,_=i.data,s===e.ErrorCode.SUCCESS?(a.info(T.A_E_GET_PRIVATE_MESSAGE_DELIVER_TIME_R,JSON.stringify({code:s,data:_}),n),[2,{code:s,data:_}]):(a.warn(T.A_E_GET_PRIVATE_MESSAGE_DELIVER_TIME_R,JSON.stringify({code:s}),n),[2,{code:s,msg:U[s]}])}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},getGroupMessageDeliverList:function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_GET_GROUP_MESSAGE_DELIVER_LIST_T,T.A_E_GET_GROUP_MESSAGE_DELIVER_LIST_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("messageUId",n,e.AssertRules.STRING,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_E_GET_GROUP_MESSAGE_DELIVER_LIST_T,JSON.stringify({targetId:t,messageUId:n}),r),[4,a.getGroupMessageDeliverList(t,n)];case 1:return i=o.sent(),_=i.code,d=i.data,u=_===e.ErrorCode.SUCCESS?"info":"warn",s[u](T.A_E_GET_GROUP_MESSAGE_DELIVER_LIST_R,JSON.stringify({code:_}),r),_===e.ErrorCode.SUCCESS?[2,{code:_,data:d}]:[2,{code:_,msg:U[_]}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},setCheckDuplicateMessage:function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_SET_CHECK_DUPLICATE_MESSAGE_T,T.A_E_SET_CHECK_DUPLICATE_MESSAGE_R).validate("checkDuplicate",t,e.AssertRules.BOOLEAN).ifReady((function(e,r){return i(n,void 0,void 0,(function(){var n,i,a,s;return o(this,(function(o){switch(o.label){case 0:return n=r.createTraceId(),r.info(T.A_E_SET_CHECK_DUPLICATE_MESSAGE_T,JSON.stringify({checkDuplicate:t}),n),[4,e.setCheckDuplicateMessage(!!t)];case 1:return i=o.sent(),a=i.code,s=i.data,r.info(T.A_E_SET_CHECK_DUPLICATE_MESSAGE_R,JSON.stringify({code:a,data:s}),n),[2,{code:a}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},setCheckChatRoomDuplicateMessage:function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_E_SET_CHECK_CHAT_ROOM_DUPLICATE_MESSAGE_T,T.A_E_SET_CHECK_CHAT_ROOM_DUPLICATE_MESSAGE_R).validate("checkDuplicate",t,e.AssertRules.BOOLEAN).ifReady((function(e,r){return i(n,void 0,void 0,(function(){var n,i,a,s;return o(this,(function(o){switch(o.label){case 0:return n=r.createTraceId(),r.info(T.A_E_SET_CHECK_CHAT_ROOM_DUPLICATE_MESSAGE_T,JSON.stringify({checkDuplicate:t}),n),[4,e.setCheckChatRoomDuplicateMessage(!!t)];case 1:return i=o.sent(),a=i.code,s=i.data,r.info(T.A_E_SET_CHECK_CHAT_ROOM_DUPLICATE_MESSAGE_R,JSON.stringify({code:a,data:s}),n),[2,{code:a}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},getMessagesAroundTimestamp:function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_GET_MESSAGE_BY_TIMESTAMP_T,T.A_GET_MESSAGE_BY_TIMESTAMP_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.isValidTargetId,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("timestamp",null==n?void 0:n.timestamp,e.isInteger({min:0}),!0).validate("beforeCount",null==n?void 0:n.beforeCount,e.isInteger({min:0}),!0).validate("afterCount",null==n?void 0:n.afterCount,e.isInteger({min:0}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,d,u,c,E,l;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_MESSAGE_BY_TIMESTAMP_T,JSON.stringify({option:n,conversation:t}),r),i=n.timestamp,d=n.beforeCount,u=n.afterCount,[4,a.getMessagesByTimestamp(t,i,d,u)];case 1:return c=o.sent(),E=c.code,l=c.data,E===e.ErrorCode.SUCCESS?(s.info(T.A_GET_MESSAGE_BY_TIMESTAMP_R,JSON.stringify({code:E,data:l}),r),[2,{code:E,data:null==l?void 0:l.map((function(e){return _(e)}))}]):(s.warn(T.A_GET_MESSAGE_BY_TIMESTAMP_R,JSON.stringify({code:E}),r),[2,{code:E,msg:U[E]}])}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},getLocalMessage:He,clearLocalData:function(){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return e.usingCppEngine()?[2,N((function(n,r){return i(t,void 0,void 0,(function(){var t,i,a,s,_;return o(this,(function(o){switch(o.label){case 0:return t=r.createTraceId(),r.info(T.A_E_SET_CHECK_DUPLICATE_MESSAGE_T,"clearLocalData ->",t),[4,n.clearData()];case 1:return i=o.sent(),a=i.code,s=i.data,_=a===e.ErrorCode.SUCCESS?"info":"warn",r[_](T.A_E_SET_CHECK_DUPLICATE_MESSAGE_R,JSON.stringify({code:a}),t),[2,{code:a,data:s}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},enable:function(){return e.usingCppEngine()}});var ke=function(e){var t=e.count,n=e.pageToken,r=void 0===n?"":n,i=e.order;return{count:t,pageToken:r,order:void 0!==i&&i}};Object.defineProperty(exports,"AreaCode",{enumerable:!0,get:function(){return e.AreaCode}}),Object.defineProperty(exports,"ChatroomEntryType",{enumerable:!0,get:function(){return e.ChatroomEntryType}}),Object.defineProperty(exports,"ChatroomSyncStatusReason",{enumerable:!0,get:function(){return e.ChatroomSyncStatusReason}}),Object.defineProperty(exports,"ChatroomUserChangeType",{enumerable:!0,get:function(){return e.ChatroomUserChangeType}}),Object.defineProperty(exports,"ChrmMemBanType",{enumerable:!0,get:function(){return e.ChrmMemBanType}}),Object.defineProperty(exports,"ChrmMemOperateType",{enumerable:!0,get:function(){return e.ChrmMemOperateType}}),Object.defineProperty(exports,"ChrmSyncStatus",{enumerable:!0,get:function(){return e.ChrmSyncStatus}}),Object.defineProperty(exports,"ConnectionStatus",{enumerable:!0,get:function(){return e.ConnectionStatus}}),Object.defineProperty(exports,"ConversationType",{enumerable:!0,get:function(){return e.ConversationType}}),Object.defineProperty(exports,"DirectionType",{enumerable:!0,get:function(){return e.DirectionType}}),Object.defineProperty(exports,"ErrorCode",{enumerable:!0,get:function(){return e.ErrorCode}}),Object.defineProperty(exports,"FileType",{enumerable:!0,get:function(){return e.FileType}}),Object.defineProperty(exports,"FriendAddPermission",{enumerable:!0,get:function(){return e.FriendAddPermission}}),Object.defineProperty(exports,"FriendApplicationStatus",{enumerable:!0,get:function(){return e.FriendApplicationStatus}}),Object.defineProperty(exports,"FriendApplicationType",{enumerable:!0,get:function(){return e.FriendApplicationType}}),Object.defineProperty(exports,"FriendRelationType",{enumerable:!0,get:function(){return e.FriendRelationType}}),Object.defineProperty(exports,"GroupApplicationDirection",{enumerable:!0,get:function(){return e.GroupApplicationDirection}}),Object.defineProperty(exports,"GroupApplicationStatus",{enumerable:!0,get:function(){return e.GroupApplicationStatus}}),Object.defineProperty(exports,"GroupApplicationType",{enumerable:!0,get:function(){return e.GroupApplicationType}}),Object.defineProperty(exports,"GroupInviteHandlePermission",{enumerable:!0,get:function(){return e.GroupInviteHandlePermission}}),Object.defineProperty(exports,"GroupJoinPermission",{enumerable:!0,get:function(){return e.GroupJoinPermission}}),Object.defineProperty(exports,"GroupMemberInfoEditPermission",{enumerable:!0,get:function(){return e.GroupMemberInfoEditPermission}}),Object.defineProperty(exports,"GroupMemberRole",{enumerable:!0,get:function(){return e.GroupMemberRole}}),Object.defineProperty(exports,"GroupOperation",{enumerable:!0,get:function(){return e.GroupOperation}}),Object.defineProperty(exports,"GroupOperationPermission",{enumerable:!0,get:function(){return e.GroupOperationPermission}}),Object.defineProperty(exports,"GroupOperationType",{enumerable:!0,get:function(){return e.GroupOperationType}}),Object.defineProperty(exports,"InterruptionLevel",{enumerable:!0,get:function(){return e.InterruptionLevel}}),Object.defineProperty(exports,"LogL",{enumerable:!0,get:function(){return e.LogL}}),Object.defineProperty(exports,"LogLevel",{enumerable:!0,get:function(){return e.LogLevel}}),Object.defineProperty(exports,"MentionedType",{enumerable:!0,get:function(){return e.MentionedType}}),Object.defineProperty(exports,"MessageBlockSourceType",{enumerable:!0,get:function(){return e.MessageBlockSourceType}}),Object.defineProperty(exports,"MessageBlockType",{enumerable:!0,get:function(){return e.MessageBlockType}}),Object.defineProperty(exports,"MessageDirection",{enumerable:!0,get:function(){return e.MessageDirection}}),Object.defineProperty(exports,"NotificationLevel",{enumerable:!0,get:function(){return e.NotificationLevel}}),Object.defineProperty(exports,"NotificationStatus",{enumerable:!0,get:function(){return e.NotificationStatus}}),Object.defineProperty(exports,"OnlineStatus",{enumerable:!0,get:function(){return e.OnlineStatus}}),Object.defineProperty(exports,"OperateStatus",{enumerable:!0,get:function(){return e.OperateStatus}}),Object.defineProperty(exports,"PlatformInfo",{enumerable:!0,get:function(){return e.PlatformInfo}}),Object.defineProperty(exports,"PushImportanceHonor",{enumerable:!0,get:function(){return e.PushImportanceHonor}}),Object.defineProperty(exports,"PushNotificationQuietHoursLevel",{enumerable:!0,get:function(){return e.PushNotificationQuietHoursLevel}}),Object.defineProperty(exports,"QueryFriendsDirectionType",{enumerable:!0,get:function(){return e.QueryFriendsDirectionType}}),Object.defineProperty(exports,"RCConnectionStatus",{enumerable:!0,get:function(){return e.RCConnectionStatus}}),Object.defineProperty(exports,"ReceivedStatus",{enumerable:!0,get:function(){return e.ReceivedStatus}}),Object.defineProperty(exports,"SentStatus",{enumerable:!0,get:function(){return e.SentStatus}}),Object.defineProperty(exports,"StreamMessageResponseEventType",{enumerable:!0,get:function(){return e.StreamMessageResponseEventType}}),Object.defineProperty(exports,"SubscribeOperationType",{enumerable:!0,get:function(){return e.SubscribeOperationType}}),Object.defineProperty(exports,"SubscribeType",{enumerable:!0,get:function(){return e.SubscribeType}}),Object.defineProperty(exports,"UltraGroupChannelChangeType",{enumerable:!0,get:function(){return e.UltraGroupChannelChangeType}}),Object.defineProperty(exports,"UltraGroupChannelType",{enumerable:!0,get:function(){return e.UltraGroupChannelType}}),Object.defineProperty(exports,"UploadMethod",{enumerable:!0,get:function(){return e.UploadMethod}}),Object.defineProperty(exports,"UserProfileVisibility",{enumerable:!0,get:function(){return e.UserProfileVisibility}}),Object.defineProperty(exports,"fail",{enumerable:!0,get:function(){return e.fail}}),Object.defineProperty(exports,"ok",{enumerable:!0,get:function(){return e.ok}}),exports.BaseMessage=J,exports.CombineMessage=k,exports.CombineV2Message=j,exports.CommandMessage=K,exports.ConnectType={COMET:"comet",WEBSOCKET:"websocket"},exports.FileMessage=X,exports.GIFMessage=Q,exports.GroupNotificationMessage=W,exports.HQVoiceMessage=q,exports.ImageMessage=z,exports.InformationNotificationMessage=Z,exports.LocationMessage=$,exports.MessageType=be,exports.ReferenceMessage=ee,exports.RichContentMessage=te,exports.SightMessage=ne,exports.StreamMessage=re,exports.TextMessage=ie,exports.UploadTask=fe,exports.VoiceMessage=oe,exports.__addSDKVersion=function(t,n){e.VersionManage.add(t,n)},exports.acceptFriendApplication=function(t){var n=this;return new e.ValidatorManage(T.A_ACCEPT_FRIEND_APPLICATION_T,T.A_ACCEPT_FRIEND_APPLICATION_R).validateParameters("userId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_USER_ID,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n;return o(this,(function(i){switch(i.label){case 0:return a.info(T.A_ACCEPT_FRIEND_APPLICATION_T,t),[4,r.acceptFriendApplication(t)];case 1:return n=i.sent().code,a[n===e.ErrorCode.SUCCESS?"info":"warn"](T.A_ACCEPT_FRIEND_APPLICATION_R,n),[2,{code:n}]}}))}))}))},exports.acceptGroupApplication=function(t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(T.A_ACCEPT_GROUP_APPLICATION_T,T.A_ACCEPT_GROUP_APPLICATION_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("applicantId",n,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_USER_ID,!0).validateParameters("inviterId",r,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_USER_ID).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d,u,c;return o(this,(function(o){switch(o.label){case 0:return i=_.createTraceId(),_.info(T.A_ACCEPT_GROUP_APPLICATION_T,JSON.stringify({groupId:t,inviterId:r,applicantId:n}),i),[4,s.acceptGroupApplication(t,n,null!=r?r:"")];case 1:return a=o.sent(),d=a.code,u=a.data,c=d===e.ErrorCode.SUCCESS?"info":"warn",_[c](T.A_ACCEPT_GROUP_APPLICATION_R,JSON.stringify({code:d}),i),d!==e.ErrorCode.SUCCESS?[2,{code:d}]:[2,{code:d,data:u}]}}))}))}))]}))}))},exports.acceptGroupInvite=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_ACCEPT_GROUP_INVITE_T,T.A_ACCEPT_GROUP_INVITE_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("inviterId",n,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_USER_ID,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_ACCEPT_GROUP_INVITE_T,JSON.stringify({groupId:t,inviterId:n}),r),[4,a.acceptGroupInvite(t,n)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_ACCEPT_GROUP_INVITE_R,JSON.stringify({code:i}),r),[2,{code:i}]}}))}))}))]}))}))},exports.addConversationsToTag=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_ADD_TAG_FOR_CONVERSATIONS_T,T.A_ADD_TAG_FOR_CONVERSATIONS_R).validate("tagId",t,e.AssertRules.STRING,!0).validate("conversations",n,(function(t){return!(!e.validate("conversations",t,e.AssertRules.ARRAY,!0)||t.length<=0)&&!t.some((function(t){return!e.validate("conversation",t,e.AssertRules.CONVERSATION,!0)}))}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_ADD_TAG_FOR_CONVERSATIONS_T,JSON.stringify({conversations:n.map((function(e){return{conversationType:e.conversationType,targetId:e.targetId}})),tagId:t}),r),[4,a.addTagForConversations(t,n)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_ADD_TAG_FOR_CONVERSATIONS_R,JSON.stringify({code:i}),r),i===e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,msg:U[i]}]}}))}))}))]}))}))},exports.addEventListener=function(e,t,n){A.on(e,t,n)},exports.addFriend=function(t,n,r){var a=this;return new e.ValidatorManage(T.A_ADD_FRIEND_T,T.A_ADD_FRIEND_R).validateParameters("userId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_USER_ID,!0).validateParameters("directionType",n,(function(t){return e.isValidEnum({value:t,type:e.DirectionType})}),e.ErrorCode.INVALID_PARAMETER_DIRECTION_TYPE,!0).validateParameters("extra",r,e.isLimitedString({maxLength:128}),e.ErrorCode.INVALID_PARAMETER_EXTRA).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d;return o(this,(function(o){switch(o.label){case 0:return _.info(T.A_ADD_FRIEND_T,JSON.stringify({userId:t,directionType:n,extra:r})),[4,s.addFriend(t,n,r||"")];case 1:return i=o.sent(),a=i.code,d=i.data,_[a===e.ErrorCode.SUCCESS?"info":"warn"](T.A_ADD_FRIEND_R,a),a!==e.ErrorCode.SUCCESS?[2,{code:a}]:[2,{code:a,data:d}]}}))}))}))},exports.addGroupFollows=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_ADD_GROUP_FOLLOWS_T,T.A_ADD_GROUP_FOLLOWS_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("userIds",n,e.isLimitedArray({minLength:1,maxLength:100,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_USERIDLIST,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_ADD_GROUP_FOLLOWS_T,JSON.stringify({groupId:t,userIds:n}),r),[4,a.addGroupFollows(t,n)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_ADD_GROUP_FOLLOWS_R,JSON.stringify({code:i}),r),[2,{code:i}]}}))}))}))]}))}))},exports.addGroupManagers=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_ADD_GROUP_MANAGERS_T,T.A_ADD_GROUP_MANAGERS_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("userIds",n,e.isLimitedArray({minLength:1,maxLength:10,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_USERIDLIST,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_ADD_GROUP_MANAGERS_T,JSON.stringify({groupId:t,userIds:n}),r),[4,a.setGroupManagers(1,t,n)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_ADD_GROUP_MANAGERS_R,JSON.stringify({code:i}),r),[2,{code:i}]}}))}))}))]}))}))},exports.addMonitor=function(e,t,n){if(!e.monitorable)throw new Error("Not monitorable API: ".concat(e.name));var r=++y;return L.set(r,[e,t,n]),r},exports.addTag=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_ADD_TAG_T,T.A_ADD_TAG_R).validate("tagId",null==t?void 0:t.tagId,e.AssertRules.STRING,!0).validate("tagId",null==t?void 0:t.tagId,(function(e){return!!e&&e.length<=10})).validate("tagName",null==t?void 0:t.tagName,e.AssertRules.STRING,!0).validate("tagName",null==t?void 0:t.tagName,(function(e){return!!e&&e.length<=15})).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_ADD_TAG_T,JSON.stringify({tagId:t.tagId,tagName:t.tagName}),n),[4,r.createTag(t)];case 1:return i=o.sent().code,s=i===e.ErrorCode.SUCCESS?"info":"warn",a[s](T.A_ADD_TAG_R,JSON.stringify({code:i}),n),i===e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,msg:U[i]}]}}))}))}))]}))}))},exports.addToBlacklist=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_ADD_TO_BLACKLIST_T,T.A_ADD_TO_BLACKLIST_R).validate("userId",t,e.AssertRules.STRING,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_ADD_TO_BLACKLIST_T,JSON.stringify({userId:t}),n),[4,r.addToBlacklist(t)];case 1:return i=o.sent(),s=i===e.ErrorCode.SUCCESS?"info":"warn",a[s](T.A_ADD_TO_BLACKLIST_R,JSON.stringify({code:i}),n),[2,{code:i}]}}))}))}))]}))}))},exports.batchClearRemoteHistoryMessages=function(e,t){return i(this,void 0,void 0,(function(){return o(this,(function(n){return[2,C().next((function(n){return n.batchClearRemoteHistoryMessages(e,t)}))]}))}))},exports.batchGetMessageReadReceiptInfoV4=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(e.LogTagId.A_BATCH_GET_MESSAGE_READ_RECEIPT_INFO_V4_T,e.LogTagId.A_BATCH_GET_MESSAGE_READ_RECEIPT_INFO_V4_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,(function(t){return t===e.ConversationType.GROUP}),!0,"It should be ConversationType.GROUP").validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("messageUId",n,(function(t){return!!e.validate("messageUIds",t,e.AssertRules.ARRAY,!0)&&!t.some((function(t){return!e.isString(t)}))}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:return r="conversationType:".concat(t.conversationType,",targetId:").concat(t.targetId,",\n      channelId: ").concat(t.channelId,", messageUid:").concat(n.toString()),i=s.createTraceId(),s.info(e.LogTagId.A_BATCH_GET_MESSAGE_READ_RECEIPT_INFO_V4_T,r,i),3!==(null==(_=a.getNaviInfoFromCache())?void 0:_.grpRRVer)?[2,{code:e.ErrorCode.READ_RECEIPT_V4_NOT_SUPPORT}]:[4,a.batchGetMessageReadReceiptInfoV4(t,n)];case 1:return d=o.sent(),u=d.code,c=d.msg,E=d.data,s[u===e.ErrorCode.SUCCESS?"info":"warn"](e.LogTagId.A_BATCH_GET_MESSAGE_READ_RECEIPT_INFO_V4_R,"code: ".concat(u,", msg: ").concat(c),i),u===e.ErrorCode.SUCCESS?[2,{code:u,data:E}]:[2,{code:u,msg:c}]}}))}))}))]}))}))},exports.batchRemoveConversation=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_BATCH_REMOVE_CONVERSATION_T,T.A_BATCH_REMOVE_CONVERSATION_R).validate("conversationList",t,e.isLimitedArray({minLength:1,maxLength:20,itemValidator:function(t){return e.isValidConversation(t,[e.ConversationType.PRIVATE,e.ConversationType.GROUP,e.ConversationType.SYSTEM])}}),!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return n=t.map((function(e){var t;return{conversationType:e.conversationType,targetId:e.targetId,channelId:null!==(t=e.channelId)&&void 0!==t?t:""}})),i=a.createTraceId(),a.info(T.A_BATCH_REMOVE_CONVERSATION_T,"conversations: ".concat(JSON.stringify(n)),i),[4,r.batchRemoveConversation(n)];case 1:return s=o.sent(),a[e.ErrorCode.SUCCESS===s?"info":"warn"](T.A_BATCH_REMOVE_CONVERSATION_R,"code: ".concat(s),i),[2,{code:s}]}}))}))}))]}))}))},exports.batchSetConversationNotificationLevel=function(e,t){return i(this,void 0,void 0,(function(){return o(this,(function(n){return[2,C().next((function(n){return n.batchSetConversationNotificationLevel(e,t)}))]}))}))},exports.batchSetConversationToTop=function(e,t,n,r){return void 0===n&&(n=!0),void 0===r&&(r=!0),i(this,void 0,void 0,(function(){return o(this,(function(i){return[2,C().next((function(i){return i.batchSetConversationToTop(e,!!t,r,n)}))]}))}))},exports.bindRTCRoomForChatroom=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_BIND_RTC_ROOM_FOR_CHATROOM_T,T.A_BIND_RTC_ROOM_FOR_CHATROOM_R).validate("targetId",null==t?void 0:t.chatRoomId,e.AssertRules.STRING,!0).validate("rtcRoomId",null==t?void 0:t.rtcRoomId,e.AssertRules.STRING,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_BIND_RTC_ROOM_FOR_CHATROOM_T,JSON.stringify({chatRoomId:t.chatRoomId,rtcRoomId:t.rtcRoomId}),n),[4,r.bindRTCRoomForChatroom(t)];case 1:return i=o.sent(),s=i===e.ErrorCode.SUCCESS?"info":"warn",a[s](T.A_BIND_RTC_ROOM_FOR_CHATROOM_R,JSON.stringify({code:i}),n),[2,{code:i,msg:U[i]}]}}))}))}))]}))}))},exports.checkFriends=function(t,n){var r=this;return new e.ValidatorManage(T.A_CHECK_FRIENDS_T,T.A_CHECK_FRIENDS_R).validateParameters("userIds",t,e.isLimitedArray({minLength:1,maxLength:20,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_USERIDLIST,!0).validateParameters("directionType",n,(function(t){return e.isValidEnum({value:t,type:e.DirectionType})}),e.ErrorCode.INVALID_PARAMETER_DIRECTION_TYPE,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return s.info(T.A_CHECK_FRIENDS_T,JSON.stringify({userIds:t,directionType:n})),[4,a.checkFriends(t,n)];case 1:return r=o.sent(),i=r.code,_=r.data,s[i===e.ErrorCode.SUCCESS?"info":"warn"](T.A_CHECK_FRIENDS_R,JSON.stringify({code:i,data:_})),i!==e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,data:_}]}}))}))}))},exports.clearAllMessagesUnreadStatus=function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){return[2,C().next((function(e){return e.clearAllUnreadCount()}))]}))}))},exports.clearEventListeners=p,exports.clearHistoryMessages=function(e,t){return i(this,void 0,void 0,(function(){return o(this,(function(n){return[2,C().next((function(n){return n.deleteRemoteMessageByTimestamp(e,t)}))]}))}))},exports.clearMessages=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){return u("RongIMLib.clearMessages","RongIMLib.electronExtension.clearMessages"),[2,we(e)]}))}))},exports.clearMessagesUnreadStatus=w,exports.clearRealtimeConUnreadCount=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_CLEAR_REALTIME_CON_UNREAD_COUNT_T,T.A_CLEAR_REALTIME_CON_UNREAD_COUNT_R).validate("conversation",t,e.isValidConversation,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_CLEAR_REALTIME_CON_UNREAD_COUNT_T,JSON.stringify(t),n),[4,r.clearRealtimeConUnreadCount(t)];case 1:return i=o.sent(),s=i.code,_=i.data,a[e.ErrorCode.SUCCESS===s?"info":"warn"](T.A_CLEAR_REALTIME_CON_UNREAD_COUNT_R,"code: ".concat(s),n),[2,{code:s,data:_}]}}))}))}))]}))}))},exports.clearTextMessageDraft=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_CLEAR_CONVERSATION_MESSAGE_DRAFT_T,T.A_CLEAR_CONVERSATION_MESSAGE_DRAFT_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d,u;return o(this,(function(o){switch(o.label){case 0:return n=t.conversationType,i=t.targetId,s=t.channelId,_=a.createTraceId(),a.info(T.A_CLEAR_CONVERSATION_MESSAGE_DRAFT_T,JSON.stringify({conversationType:n,targetId:i,channelId:s}),_),[4,r.clearConversationMessageDraft(n,i,s)];case 1:return d=o.sent(),u=d===e.ErrorCode.SUCCESS?"info":"warn",a[u](T.A_CLEAR_CONVERSATION_MESSAGE_DRAFT_R,JSON.stringify({code:d}),_),d===e.ErrorCode.SUCCESS?[2,{code:d}]:[2,{code:d,msg:U[d]}]}}))}))}))]}))}))},exports.clearUnreadCountByTimestamp=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_CLEAR_UNREAD_COUNT_BY_TIMESTAMP_T,T.A_CLEAR_UNREAD_COUNT_BY_TIMESTAMP_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("timestamp",n,e.AssertRules.NUMBER).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:return r=t.conversationType,i=t.targetId,_=t.channelId,d=void 0===_?"":_,u=s.createTraceId(),s.info(T.A_CLEAR_UNREAD_COUNT_BY_TIMESTAMP_T,JSON.stringify({conversationType:r,targetId:i,channelId:d,timestamp:n}),u),[4,a.clearUnreadCountByTimestamp(r,i,n,d)];case 1:return c=o.sent(),E=c===e.ErrorCode.SUCCESS?"info":"warn",s[E](T.A_CLEAR_UNREAD_COUNT_BY_TIMESTAMP_R,JSON.stringify({code:c}),u),c===e.ErrorCode.SUCCESS?[2,{code:c}]:[2,{code:c,msg:U[c]}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},exports.connect=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(e.LogTagId.A_CONNECT_T,e.LogTagId.A_CONNECT_R).validate("token",t,e.AssertRules.STRING,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(e.LogTagId.A_CONNECT_T,JSON.stringify({token:t,reconnectKickEnable:n}),r),[4,a.connect(t,!!n,r)];case 1:return i=o.sent(),_=i.code===e.ErrorCode.SUCCESS?"info":"warn",s[_](e.LogTagId.A_CONNECT_R,JSON.stringify({code:i.code,userId:i.userId}),r),i.code===e.ErrorCode.SUCCESS?[2,{code:i.code,data:{userId:i.userId}}]:[2,{code:i.code,msg:U[i.code]}]}}))}))}))]}))}))},exports.createGroup=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_CREATE_GROUP_T,T.A_CREATE_GROUP_R).validateParameters("groupId",t.groupId,e.isValidGroupId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("groupName",t.groupName,e.isLimitedString({isTrim:!0,minLength:1,maxLength:64}),e.ErrorCode.INVALID_PARAMETER_GROUP_NAME,!0).validateParameters("groupInfo.portraitUri",null==t?void 0:t.portraitUri,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.introduction",null==t?void 0:t.introduction,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.notice",null==t?void 0:t.notice,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.extProfile",null==t?void 0:t.extProfile,e.AssertRules.OBJECT,e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.extProfile",null==t?void 0:t.extProfile,(function(e){for(var t in e)if("string"!=typeof e[t])return!1;return!0}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.joinPermission",null==t?void 0:t.joinPermission,(function(t){return e.isValidEnum({value:t,type:e.GroupJoinPermission})}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.removeMemberPermission",null==t?void 0:t.removeMemberPermission,(function(t){return e.isValidEnum({value:t,type:e.GroupOperationPermission})}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.invitePermission",null==t?void 0:t.invitePermission,(function(t){return e.isValidEnum({value:t,type:e.GroupOperationPermission})}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.inviteHandlePermission",null==t?void 0:t.inviteHandlePermission,(function(t){return e.isValidEnum({value:t,type:e.GroupInviteHandlePermission})}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.groupInfoEditPermission",null==t?void 0:t.groupInfoEditPermission,(function(t){return e.isValidEnum({value:t,type:e.GroupOperationPermission})}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.memberInfoEditPermission",null==t?void 0:t.memberInfoEditPermission,(function(t){return e.isValidEnum({value:t,type:e.GroupMemberInfoEditPermission})}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("inviteeUserIds",n,e.isLimitedArray({minLength:0,maxLength:30,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_USERIDLIST).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u,c,l,A,R,S,I;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_CREATE_GROUP_T,JSON.stringify({groupInfo:t,inviteeUserIds:n}),r),i=t.groupId,_=t.extProfile,d={},u=Object.keys(E),Object.keys(t).forEach((function(e){u.includes(e)&&(d[E[e]]=t[e])})),[4,a.createGroup(i,d,_,n)];case 1:return c=o.sent(),l=c.code,A=c.msg,R=void 0===A?"":A,S=c.data,I=l===e.ErrorCode.SUCCESS?"info":"warn",s[I](T.A_CREATE_GROUP_R,JSON.stringify({code:l}),r),l!==e.ErrorCode.SUCCESS?[2,{code:l,msg:R,data:S}]:[2,{code:l,data:S}]}}))}))}))]}))}))},exports.createUploadTask=function(t){return i(this,void 0,void 0,(function(){var n,r,i;return o(this,(function(o){return n=t.contentDisposition,r=t.fileType,i=t.file,[2,new e.ValidatorManage("","").validate("contentDisposition",n,(function(e){return"inline"===e||"attachment"===e}),!1).validate("fileType",r,(function(t){return!!e.FileType[t]}),!0).validate("file",i,(function(t){return t instanceof File&&(!(r===e.FileType.IMAGE&&!t.type.match(/^image\//))&&!(r===e.FileType.AUDIO&&!t.type.match(/^audio\//)))}),!0).ifReadySync((function(n,r,i){var o=ge(t,Ne(n,i.uploadDomain));return{code:e.ErrorCode.SUCCESS,data:o}}))]}))}))},exports.deleteFriends=function(t,n){var r=this;return new e.ValidatorManage(T.A_DELETE_FRIENDS_T,T.A_DELETE_FRIENDS_R).validateParameters("userIds",t,e.isLimitedArray({minLength:1,maxLength:100,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_USERIDLIST,!0).validateParameters("directionType",n,(function(t){return e.isValidEnum({value:t,type:e.DirectionType})}),e.ErrorCode.INVALID_PARAMETER_DIRECTION_TYPE,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r;return o(this,(function(i){switch(i.label){case 0:return s.info(T.A_DELETE_FRIENDS_T,JSON.stringify({userIds:t,directionType:n})),[4,a.deleteFriends(t,n)];case 1:return r=i.sent().code,s[r===e.ErrorCode.SUCCESS?"info":"warn"](T.A_DELETE_FRIENDS_R,r),[2,{code:r}]}}))}))}))},exports.deleteLocalMessagesByTimestamp=function(e,t,n){return i(this,void 0,void 0,(function(){return o(this,(function(r){return u("RongIMLib.deleteLocalMessagesByTimestamp","RongIMLib.electronExtension.deleteMessagesByTimestamp"),[2,xe(e,t,n)]}))}))},exports.deleteMessages=function(e,t){return i(this,void 0,void 0,(function(){return o(this,(function(n){return[2,C().next((function(n){return n.deleteRemoteMessage(e,t)}))]}))}))},exports.destroy=function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){return p(),[2,g()]}))}))},exports.disconnect=b,exports.dismissGroup=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_DISMISS_GROUP_T,T.A_DISMISS_GROUP_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_DISMISS_GROUP_T,JSON.stringify({groupId:t}),n),[4,r.dismissGroup(t)];case 1:return i=o.sent().code,s=i===e.ErrorCode.SUCCESS?"info":"warn",a[s](T.A_DISMISS_GROUP_R,JSON.stringify({code:i}),n),[2,{code:i}]}}))}))}))]}))}))},exports.electronExtension=Ye,exports.forceRemoveChatRoomEntry=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_FORCE_REMOVE_CHATROOM_ENTRY_T,T.A_FORCE_REMOVE_CHATROOM_ENTRY_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("key",null==n?void 0:n.key,e.AssertRules.STRING,!0).validate("isSendNotification",null==n?void 0:n.isSendNotification,e.AssertRules.BOOLEAN).validate("notificationExtra",null==n?void 0:n.notificationExtra,e.isLimitedString({maxLength:B})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_FORCE_REMOVE_CHATROOM_ENTRY_T,JSON.stringify({chatroomId:t,key:n.key,isSendNotification:n.isSendNotification,notificationExtra:n.notificationExtra}),r),[4,a.forceRemoveChatroomEntry(t,n)];case 1:return i=o.sent(),_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_FORCE_REMOVE_CHATROOM_ENTRY_R,JSON.stringify({code:i}),r),i!==e.ErrorCode.SUCCESS?[2,{code:i,msg:U[i]}]:[2,{code:i}]}}))}))}))]}))}))},exports.forceSetChatRoomEntry=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_FORCE_SET_CHATROOM_ENTRY_T,T.A_FORCE_SET_CHATROOM_ENTRY_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("key",null==n?void 0:n.key,e.AssertRules.STRING,!0).validate("value",null==n?void 0:n.value,e.AssertRules.STRING,!0).validate("isAutoDelete",null==n?void 0:n.isAutoDelete,e.AssertRules.BOOLEAN).validate("isSendNotification",null==n?void 0:n.isSendNotification,e.AssertRules.BOOLEAN).validate("notificationExtra",null==n?void 0:n.notificationExtra,e.isLimitedString({maxLength:B})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_FORCE_SET_CHATROOM_ENTRY_T,JSON.stringify({chatroomId:t,key:n.key,value:n.value,isSendNotification:n.isSendNotification,isAutoDelete:n.isAutoDelete,notificationExtra:n.notificationExtra}),r),[4,a.forceSetChatroomEntry(t,n)];case 1:return i=o.sent(),_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_FORCE_SET_CHATROOM_ENTRY_R,JSON.stringify({code:i}),r),i!==e.ErrorCode.SUCCESS?[2,{code:i,msg:U[i]}]:[2,{code:i}]}}))}))}))]}))}))},exports.getAllChatRoomEntries=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_ALL_CHATROOM_ENTRIES_T,T.A_GET_ALL_CHATROOM_ENTRIES_R).validate("targetId",t,e.AssertRules.STRING,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_ALL_CHATROOM_ENTRIES_T,JSON.stringify({chatroomId:t}),n),[4,r.getAllChatroomEntries(t)];case 1:return i=o.sent(),s=i.code,_=i.data,s===e.ErrorCode.SUCCESS&&_?(a.info(T.A_GET_ALL_CHATROOM_ENTRIES_R,JSON.stringify({code:s,data:_}),n),[2,{code:s,data:_}]):(a.warn(T.A_GET_ALL_CHATROOM_ENTRIES_R,JSON.stringify({code:s}),n),[2,{code:s,msg:U[s]}])}}))}))}))]}))}))},exports.getAllUltraGroupUnreadCount=function(){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return[2,new e.ValidatorManage(T.A_GET_ALL_ULTRA_GROUP_UNREAD_COUNT_T,T.A_GET_ALL_ULTRA_GROUP_UNREAD_COUNT_R).ifReady((function(n,r){return i(t,void 0,void 0,(function(){var t,i,a,s,_;return o(this,(function(o){switch(o.label){case 0:return t=r.createTraceId(),r.info(T.A_GET_ALL_ULTRA_GROUP_UNREAD_COUNT_T,"getAllUltraGroupUnreadCount",t),[4,n.getAllUltraGroupUnreadCount()];case 1:return i=o.sent(),a=i.code,s=i.data,_=a===e.ErrorCode.SUCCESS?"info":"warn",r[_](T.A_GET_ALL_ULTRA_GROUP_UNREAD_COUNT_R,JSON.stringify({code:a,data:s}),t),a===e.ErrorCode.SUCCESS?[2,{code:a,data:s}]:[2,{code:a,msg:U[a]}]}}))}))}))]}))}))},exports.getAllUltraGroupUnreadMentionedCount=function(){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return[2,new e.ValidatorManage(T.A_GET_ALL_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_T,T.A_GET_ALL_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_R).ifReady((function(n,r){return i(t,void 0,void 0,(function(){var t,i,a,s,_;return o(this,(function(o){switch(o.label){case 0:return t=r.createTraceId(),r.info(T.A_GET_ALL_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_T,"getAllUltraGroupUnreadCount",t),[4,n.getAllUltraGroupUnreadCount(true)];case 1:return i=o.sent(),a=i.code,s=i.data,_=a===e.ErrorCode.SUCCESS?"info":"warn",r[_](T.A_GET_ALL_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_R,JSON.stringify({code:a,data:s}),t),a===e.ErrorCode.SUCCESS?[2,{code:a,data:s}]:[2,{code:a,msg:U[a]}]}}))}))}))]}))}))},exports.getAllUnreadMentionedCount=function(){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return[2,new e.ValidatorManage("","").ifReady((function(n,r){return i(t,void 0,void 0,(function(){var t,i,a,s;return o(this,(function(o){switch(o.label){case 0:return t=r.createTraceId(),r.info(T.A_GET_ALL_UNREAD_MENTIONED_COUNT_T,"",t),[4,n.getAllUnreadMentionedCount()];case 1:return i=o.sent(),a=i.code,s=i.data,a===e.ErrorCode.SUCCESS?(r.info(T.A_GET_ALL_UNREAD_MENTIONED_COUNT_R,JSON.stringify({code:a,data:s}),t),[2,{code:a,data:s}]):(r.warn(T.A_GET_ALL_UNREAD_MENTIONED_COUNT_R,JSON.stringify({code:a}),t),[2,{code:a}])}}))}))}))]}))}))},exports.getBlacklist=function(){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return[2,new e.ValidatorManage(T.A_GET_BLACKLIST_T,T.A_GET_BLACKLIST_R).ifReady((function(n,r){return i(t,void 0,void 0,(function(){var t,i,a,s;return o(this,(function(o){switch(o.label){case 0:return t=r.createTraceId(),r.info(T.A_GET_BLACKLIST_T,"getBlacklist -> ",t),[4,n.getBlacklist()];case 1:return i=o.sent(),a=i.code,s=i.data,a===e.ErrorCode.SUCCESS&&s?(r.info(T.A_GET_BLACKLIST_R,JSON.stringify({code:a,data:s}),t),[2,{code:a,data:s}]):(r.warn(T.A_GET_BLACKLIST_R,JSON.stringify({code:a}),t),[2,{code:a}])}}))}))}))]}))}))},exports.getBlacklistStatus=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_BLACKLIST_STATUS_T,T.A_GET_BLACKLIST_STATUS_R).validate("userId",t,e.AssertRules.STRING,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_BLACKLIST_STATUS_T,JSON.stringify({userId:t}),n),[4,r.getBlacklistStatus(t)];case 1:return i=o.sent(),s=i.code,_=i.data,s===e.ErrorCode.SUCCESS?(a.info(T.A_GET_BLACKLIST_STATUS_R,JSON.stringify({code:s,data:0===_}),n),[2,{code:s,data:0===_}]):(a.warn(T.A_GET_BLACKLIST_STATUS_R,JSON.stringify({code:s}),n),[2,{code:s}])}}))}))}))]}))}))},exports.getBlockUltraGroupList=function(){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return[2,new e.ValidatorManage("","").ifReady((function(n,r){return i(t,void 0,void 0,(function(){var t,i,a,s;return o(this,(function(o){switch(o.label){case 0:return t=r.createTraceId(),r.info(T.A_GET_BLOCK_ULTRA_GROUP_LIST_T,"",t),[4,n.getBlockUltraGroupList()];case 1:return i=o.sent(),a=i.code,s=i.data,a===e.ErrorCode.SUCCESS?(r.info(T.A_GET_BLOCK_ULTRA_GROUP_LIST_R,JSON.stringify({code:a,length:null==s?void 0:s.length}),t),[2,{code:e.ErrorCode.SUCCESS,data:s}]):(r.warn(T.A_GET_BLOCK_ULTRA_GROUP_LIST_R,JSON.stringify({code:a}),t),[2,{code:a,msg:U[a]}])}}))}))}))]}))}))},exports.getBlockedConversationList=function(){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return[2,C().next((function(n){return i(t,void 0,void 0,(function(){var t;return o(this,(function(r){switch(r.label){case 0:return t={type:e.ConversationListFilterType.BLOCKED},[4,n.getConversationListByFilter(t)];case 1:return[2,r.sent().trans((function(e){return e.map(d)}))]}}))}))}))]}))}))},exports.getChatRoomEntry=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_GET_CHATROOM_ENTRY_T,T.A_GET_CHATROOM_ENTRY_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("key",n,(function(t){return e.isString(t)&&/[\w+=-]+/.test(t)&&t.length<=128}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_CHATROOM_ENTRY_T,JSON.stringify({chatroomId:t,key:n}),r),[4,a.getChatroomEntry(t,n)];case 1:return i=o.sent(),_=i.code,d=i.data,_===e.ErrorCode.SUCCESS&&d?(s.info(T.A_GET_CHATROOM_ENTRY_R,JSON.stringify({code:_,data:d}),r),[2,{code:_,data:d}]):(s.warn(T.A_GET_CHATROOM_ENTRY_R,JSON.stringify({code:_}),r),[2,{code:_,msg:U[_]}])}}))}))}))]}))}))},exports.getChatRoomInfo=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(e.LogTagId.A_QUIT_CHATROOM_T,e.LogTagId.A_QUIT_CHATROOM_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("count",null==n?void 0:n.count,e.AssertRules.NUMBER).validate("order",null==n?void 0:n.order,(function(e){return[0,1,2].includes(e)})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_CHATROOM_INFO_T,JSON.stringify({chatroomId:t,count:null==n?void 0:n.count,order:null==n?void 0:n.order}),r),[4,a.getChatroomInfo(t,null==n?void 0:n.count,null==n?void 0:n.order)];case 1:return i=o.sent(),_=i.code,d=i.data,u=_===e.ErrorCode.SUCCESS?"info":"warn",s[u](T.A_GET_CHATROOM_INFO_R,JSON.stringify({code:_}),r),_===e.ErrorCode.SUCCESS&&d?[2,{code:_,data:d}]:[2,{code:_,msg:U[_]}]}}))}))}))]}))}))},exports.getChatroomHistoryMessages=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_GET_CHATROOM_HISTORY_MESSAGES_T,T.A_GET_CHATROOM_HISTORY_MESSAGES_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("timestamp",null==n?void 0:n.timestamp,e.AssertRules.NUMBER).validate("count",null==n?void 0:n.count,e.AssertRules.NUMBER).validate("count",null==n?void 0:n.count,(function(e){var t=e>0&&e<=100;return t||null===console||void 0===console||console.error('"options.count" is invalid, It should be greater than 0 and less than 100'),t})).validate("order",null==n?void 0:n.order,(function(e){return 0===e||1===e})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_CHATROOM_HISTORY_MESSAGES_T,JSON.stringify({chatroomId:t,timestamp:n.timestamp,count:n.count,order:n.order}),r),[4,a.getChatRoomHistoryMessages(t,n.count,n.order,n.timestamp)];case 1:return i=o.sent(),d=i.code,u=i.data,d===e.ErrorCode.SUCCESS&&u?(c=[],E=u.list.map((function(e){return c.push(e.messageUId),_(e)})),s.info(T.A_GET_CHATROOM_HISTORY_MESSAGES_R,JSON.stringify({code:d,data:c}),r),[2,{code:d,data:{list:E,hasMore:!!u.hasMore}}]):(s.warn(T.A_GET_CHATROOM_HISTORY_MESSAGES_R,JSON.stringify({code:d}),r),[2,{code:d,msg:U[d]}])}}))}))}))]}))}))},exports.getConnectionStatus=function(){return O((function(e){return e.getConnectionStatus()}))},exports.getConversation=function(e){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return[2,C().next((function(n){return i(t,void 0,void 0,(function(){return o(this,(function(t){switch(t.label){case 0:return[4,n.getConversation(e)];case 1:return[2,t.sent().trans((function(e){return d(e)}))]}}))}))}))]}))}))},exports.getConversationList=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,C().next((function(a){return i(r,void 0,void 0,(function(){var r;return o(this,(function(i){switch(i.label){case 0:return r={type:e.ConversationListFilterType.TIMESTAMP,params:{count:(null==t?void 0:t.count)||300,startTime:(null==t?void 0:t.startTime)||0,order:null==t?void 0:t.order,channelId:n}},[4,a.getConversationListByFilter(r)];case 1:return[2,i.sent().trans((function(e){return e.map(d)}))]}}))}))}))]}))}))},exports.getConversationNotificationLevel=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){return[2,C().next((function(t){return t.getConversationNotificationLevel(e)}))]}))}))},exports.getConversationNotificationStatus=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,C().next((function(r){return i(n,void 0,void 0,(function(){return o(this,(function(n){switch(n.label){case 0:return[4,r.getConversationNotificationLevel(t)];case 1:return[2,n.sent().trans((function(t){return e.trans2NotificationStatus(t)}))]}}))}))}))]}))}))},exports.getConversationTypeNotificationLevel=function(e){return i(this,void 0,void 0,(function(){return o(this,(function(t){return[2,C().next((function(t){return t.getConversationTypeNotificationLevel(e)}))]}))}))},exports.getConversationsFromTagByPage=function(t,n,a){return i(this,void 0,void 0,(function(){var s=this;return o(this,(function(_){return[2,new e.ValidatorManage(T.A_GET_CONVERSATIONS_FROM_TAG_BY_PAGE_T,T.A_GET_CONVERSATIONS_FROM_TAG_BY_PAGE_R).validate("tagId",t,e.AssertRules.STRING,!0).validate("count",n,e.AssertRules.NUMBER,!0).validate("startTime",a,e.AssertRules.NUMBER,!0).ifReady((function(_,u){return i(s,void 0,void 0,(function(){var i,s,c,E,l;return o(this,(function(o){switch(o.label){case 0:return i=u.createTraceId(),u.info(T.A_GET_CONVERSATIONS_FROM_TAG_BY_PAGE_T,JSON.stringify({tagId:t,count:n,startTime:a}),i),[4,_.getConversationListByTag(t,a,n)];case 1:return s=o.sent(),c=s.code,E=s.data,c===e.ErrorCode.SUCCESS?(l=null==E?void 0:E.map((function(e){return function(e){return r(r({},d(e)),{isTopInTag:e.isTopInTag})}(e)})),u.info(T.A_GET_CONVERSATIONS_FROM_TAG_BY_PAGE_R,JSON.stringify({code:c,length:null==l?void 0:l.length}),i),[2,{code:c,data:l}]):(u.warn(T.A_GET_CONVERSATIONS_FROM_TAG_BY_PAGE_R,JSON.stringify({code:c}),i),[2,{code:c,msg:U[c]}])}}))}))}))]}))}))},exports.getCurrentUserId=function(){return O((function(e){return e.getCurrentUserId()}))},exports.getDeviceId=function(){return O((function(e){return e.getDeviceId()}))},exports.getDownloadAuth=function(t){return new e.ValidatorManage("","").validate("mediaUrl",t,e.AssertRules.STRING,!0).ifReady((function(n){return new Promise((function(r){n.getFileToken(1,void 0,void 0,void 0,t).then((function(t){var n=t.token,i=t.downloadAuthInfo,o=t.deadline;r({code:e.ErrorCode.SUCCESS,data:{token:n,downloadAuthInfo:i,deadline:o}})})).catch((function(e){r({code:e,msg:U[e]})}))}))}))},exports.getFileToken=function(t,n,r,i){return N((function(o){return new Promise((function(a){o.getFileToken(t,n,r,i).then((function(t){a({code:e.ErrorCode.SUCCESS,data:t})})).catch((function(e){a({code:e,msg:U[e]})}))}))}))},exports.getFileUrl=function(t,n,r,i,o){return(null==i?void 0:i.isBosRes)?Promise.resolve({code:e.ErrorCode.SUCCESS,data:i}):new e.ValidatorManage("","").validate("fileType",t,e.isValidFileType,!0).validate("filename",n,e.AssertRules.STRING,!0).validate("saveName",r,e.AssertRules.STRING).validate("serverType",o,e.AssertRules.NUMBER).ifReady((function(i){return new Promise((function(a){i.getFileUrl(t,n,o||e.UploadMethod.QINIU,r).then((function(t){a({code:e.ErrorCode.SUCCESS,data:t})})).catch((function(e){a({code:e})}))}))}))},exports.getFirstUnreadMessage=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return e.usingCppEngine()?[2,new e.ValidatorManage(T.A_GET_FIRST_UNREAD_MESSAGE_T,T.A_GET_FIRST_UNREAD_MESSAGE_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,d,u,c,E,l;return o(this,(function(o){switch(o.label){case 0:return n=t.conversationType,i=t.targetId,s=t.channelId,d=a.createTraceId(),a.info(T.A_GET_FIRST_UNREAD_MESSAGE_T,JSON.stringify({conversationType:n,targetId:i,channelId:s}),d),[4,r.getFirstUnreadMessage(n,i,s)];case 1:return u=o.sent(),c=u.code,E=u.data,c===e.ErrorCode.SUCCESS?(l=E&&_(E)||null,a.info(T.A_GET_FIRST_UNREAD_MESSAGE_R,JSON.stringify({code:c,messageUId:null==E?void 0:E.messageUId}),d),[2,{code:c,data:l}]):(a.warn(T.A_GET_FIRST_UNREAD_MESSAGE_R,JSON.stringify({code:c}),d),[2,{code:c,msg:U[c]}])}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},exports.getFirstUnreadMessageInfo=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_FIRST_UNREAD_MESSAGE_INFO_T,T.A_GET_FIRST_UNREAD_MESSAGE_INFO_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d,u,c;return o(this,(function(o){switch(o.label){case 0:return n=t.conversationType,i=t.targetId,s=t.channelId,_=a.createTraceId(),a.info(T.A_GET_FIRST_UNREAD_MESSAGE_INFO_T,JSON.stringify({conversationType:n,targetId:i,channelId:s}),_),[4,r.getFirstUnreadMessageInfo(n,i,s||"")];case 1:return d=o.sent(),u=d.code,c=d.data,u===e.ErrorCode.SUCCESS?(a.info(T.A_GET_FIRST_UNREAD_MESSAGE_INFO_R,JSON.stringify({code:u,messageUId:null==c?void 0:c.messageUId,sentTime:null==c?void 0:c.sentTime}),_),[2,{code:u,data:c}]):(a.warn(T.A_GET_FIRST_UNREAD_MESSAGE_INFO_R,JSON.stringify({code:u}),_),[2,{code:u,msg:U[u]}])}}))}))}))]}))}))},exports.getFriendAddPermission=function(){var t=this;return new e.ValidatorManage(T.A_GET_ADD_FRIEND_PERMISSION_T,T.A_GET_ADD_FRIEND_PERMISSION_R).ifReady((function(n,r){return i(t,void 0,void 0,(function(){var t,i,a;return o(this,(function(o){switch(o.label){case 0:return r.info(T.A_GET_ADD_FRIEND_PERMISSION_T),[4,n.getFriendAddPermission()];case 1:return t=o.sent(),i=t.code,a=t.data,r[i===e.ErrorCode.SUCCESS?"info":"warn"](T.A_GET_ADD_FRIEND_PERMISSION_R,JSON.stringify({code:i,data:a})),i!==e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,data:a}]}}))}))}))},exports.getFriendApplications=function(t,n,r){var a=this;return void 0===t&&(t={count:50,pageToken:"",order:!1}),new e.ValidatorManage(T.A_GET_FRIEND_APPLICATIONS_T,T.A_GET_FRIEND_APPLICATIONS_R).validateParameters("types",n,e.isLimitedArray({itemValidator:function(t){return e.isValidEnum({value:t,type:e.FriendApplicationType})}}),e.ErrorCode.INVALID_PARAMETER_FRIEND_APPLICATION_TYPE).validateParameters("option.count",t.count,e.isInteger({min:1,max:100}),e.ErrorCode.INVALID_PARAMETER_COUNT,!0).validateParameters("option.pageToken",t.pageToken,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).validateParameters("option.order",t.order,e.AssertRules.BOOLEAN,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).validateParameters("status",r,e.isLimitedArray({itemValidator:function(t){return e.isValidEnum({value:t,type:e.FriendApplicationStatus})}}),e.ErrorCode.INVALID_PARAMETER_FRIEND_APPLICATION_STATUS).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d,u,c;return o(this,(function(o){switch(o.label){case 0:return _.info(T.A_GET_FRIEND_APPLICATIONS_T,JSON.stringify({types:n,status:r,option:t})),[4,s.getFriendApplications({count:t.count,pageToken:null!==(u=null==t?void 0:t.pageToken)&&void 0!==u?u:"",order:null!==(c=null==t?void 0:t.order)&&void 0!==c&&c},n,r)];case 1:return i=o.sent(),a=i.code,d=i.data,_[a===e.ErrorCode.SUCCESS?"info":"warn"](T.A_GET_FRIEND_APPLICATIONS_R,JSON.stringify({code:a,data:d})),a!==e.ErrorCode.SUCCESS?[2,{code:a}]:[2,{code:a,data:d}]}}))}))}))},exports.getFriends=function(t,n){var r=this;return void 0===n&&(n={count:50,pageToken:"",order:!1}),new e.ValidatorManage(T.A_GET_FRIENDS_T,T.A_GET_FRIENDS_R).validateParameters("directionType",t,(function(t){return e.isValidEnum({value:t,type:e.QueryFriendsDirectionType})}),e.ErrorCode.INVALID_PARAMETER_QUERY_FRIENDS_DIRECTION_TYPE,!0).validateParameters("option.count",null==n?void 0:n.count,e.isInteger({min:1,max:100}),e.ErrorCode.INVALID_PARAMETER_COUNT,!e.usingCppEngine()).validateParameters("option.pageToken",null==n?void 0:n.pageToken,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).validateParameters("option.order",null==n?void 0:n.order,e.AssertRules.BOOLEAN,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return s.info(T.A_GET_FRIENDS_T,JSON.stringify({directionType:t,option:n})),[4,a.getFriends(t,n)];case 1:return r=o.sent(),i=r.code,_=r.data,s[i===e.ErrorCode.SUCCESS?"info":"warn"](T.A_GET_FRIENDS_R,JSON.stringify({code:i,data:_})),i!==e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,data:_}]}}))}))}))},exports.getFriendsInfo=function(t){var n=this;return new e.ValidatorManage(T.A_GET_FRIENDS_INFO_T,T.A_GET_FRIENDS_INFO_R).validateParameters("userIds",t,e.isLimitedArray({minLength:1,maxLength:100,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_USERIDLIST,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return a.info(T.A_GET_FRIENDS_INFO_T,JSON.stringify({userIds:t})),[4,r.getFriendsInfo(t)];case 1:return n=o.sent(),i=n.code,s=n.data,a[i===e.ErrorCode.SUCCESS?"info":"warn"](T.A_GET_FRIENDS_INFO_R,JSON.stringify({code:i,data:s})),i!==e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,data:s}]}}))}))}))},exports.getGroupApplications=function(t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(T.A_GET_GROUP_APPLICATIONS_T,T.A_GET_GROUP_APPLICATIONS_R).validateParameters("option",t,e.AssertRules.OBJECT,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION,!0).validateParameters("option.count",t.count,e.isInteger({min:1,max:200}),e.ErrorCode.INVALID_PARAMETER_COUNT,!0).validateParameters("option.pageToken",null==t?void 0:t.pageToken,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).validateParameters("option.order",null==t?void 0:t.order,e.AssertRules.BOOLEAN,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).validateParameters("directions",n,e.isLimitedArray({minLength:1,itemValidator:function(t){return e.isValidEnum({value:t,type:e.GroupApplicationDirection})}}),e.ErrorCode.INVALID_PARAMETER_GROUP_APPLICATION_DIRECTION,e.usingCppEngine()).validateParameters("status",r,e.isLimitedArray({minLength:1,itemValidator:function(t){return e.isValidEnum({value:t,type:e.GroupApplicationStatus})}}),e.ErrorCode.INVALID_PARAMETER_GROUP_APPLICATION_STATUS,e.usingCppEngine()).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d,u,c;return o(this,(function(o){switch(o.label){case 0:return i=_.createTraceId(),_.info(T.A_GET_GROUP_APPLICATIONS_T,JSON.stringify({status:r,option:t,formatOption:ke(t)}),i),[4,s.getGroupApplications(ke(t),n,r)];case 1:return a=o.sent(),d=a.code,u=a.data,c=d===e.ErrorCode.SUCCESS?"info":"warn",_[c](T.A_GET_GROUP_APPLICATIONS_R,JSON.stringify({code:d,data:u}),i),d!==e.ErrorCode.SUCCESS?[2,{code:d}]:[2,{code:d,data:u}]}}))}))}))]}))}))},exports.getGroupFollows=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_GROUP_FOLLOWS_T,T.A_GET_GROUP_FOLLOWS_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_GROUP_FOLLOWS_T,JSON.stringify({groupId:t}),n),[4,r.getGroupFollows(t)];case 1:return i=o.sent(),s=i.code,_=i.data,d=s===e.ErrorCode.SUCCESS?"info":"warn",a[d](T.A_GET_GROUP_FOLLOWS_R,JSON.stringify({code:s}),n),s!==e.ErrorCode.SUCCESS?[2,{code:s}]:[2,{code:s,data:_}]}}))}))}))]}))}))},exports.getGroupMembers=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_GET_GROUP_MEMBERS_T,T.A_GET_GROUP_MEMBERS_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("userIds",n,e.isLimitedArray({minLength:1,maxLength:100,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_USERIDLIST,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_GROUP_MEMBERS_T,JSON.stringify({groupId:t,userIds:n}),r),[4,a.getGroupMembers(t,n)];case 1:return i=o.sent(),_=i.code,d=i.data,u=_===e.ErrorCode.SUCCESS?"info":"warn",s[u](T.A_GET_GROUP_MEMBERS_R,JSON.stringify({code:_,data:d}),r),_!==e.ErrorCode.SUCCESS?[2,{code:_}]:[2,{code:_,data:d}]}}))}))}))]}))}))},exports.getGroupMembersByRole=function(t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(T.A_GET_GROUP_MEMBERS_BY_ROLES_T,T.A_GET_GROUP_MEMBERS_BY_ROLES_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("role",n,(function(t){return e.isValidEnum({value:t,type:e.GroupMemberRole})}),e.ErrorCode.INVALID_PARAMETER_GROUP_MEMBER_ROLE,!0).validateParameters("option",r,e.AssertRules.OBJECT,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION,!0).validateParameters("option.count",r.count,e.isInteger({min:1,max:100}),e.ErrorCode.INVALID_PARAMETER_COUNT,!0).validateParameters("option.pageToken",null==r?void 0:r.pageToken,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).validateParameters("option.order",null==r?void 0:r.order,e.AssertRules.BOOLEAN,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d,u,c;return o(this,(function(o){switch(o.label){case 0:return i=_.createTraceId(),_.info(T.A_GET_GROUP_MEMBERS_BY_ROLES_T,JSON.stringify({groupId:t,role:n,option:r,formatOption:ke(r)}),i),[4,s.getGroupMembersByRole(t,n,ke(r))];case 1:return a=o.sent(),d=a.code,u=a.data,c=d===e.ErrorCode.SUCCESS?"info":"warn",_[c](T.A_GET_GROUP_MEMBERS_BY_ROLES_R,JSON.stringify({code:d,data:u}),i),d!==e.ErrorCode.SUCCESS?[2,{code:d}]:[2,{code:d,data:u}]}}))}))}))]}))}))},exports.getGroupsInfo=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_GROUPS_INFO_T,T.A_GET_GROUPS_INFO_R).validateParameters("groupIds",t,e.isLimitedArray({minLength:1,maxLength:20,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_GROUP_IDS,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_GROUPS_INFO_T,JSON.stringify({groupIds:t}),n),[4,r.getGroupsInfo(t)];case 1:return i=o.sent(),s=i.code,_=i.data,d=s===e.ErrorCode.SUCCESS?"info":"warn",a[d](T.A_GET_GROUPS_INFO_R,JSON.stringify({code:s,data:_}),n),s!==e.ErrorCode.SUCCESS?[2,{code:s}]:[2,{code:s,data:_}]}}))}))}))]}))}))},exports.getHistoryMessages=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(e.LogTagId.A_GET_HISTORY_MSG_T,e.LogTagId.A_GET_HISTORY_MSG_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("timestamp",null==n?void 0:n.timestamp,e.AssertRules.NUMBER).validate("count",null==n?void 0:n.count,e.AssertRules.NUMBER).validate("count",null==n?void 0:n.count,(function(t){if(e.usingCppEngine())return!0;var n=t>0&&t<=100;return n||null===console||void 0===console||console.error('"options.count" is invalid, It should be greater than 0 and less than 100'),n})).validate("order",null==n?void 0:n.order,(function(e){return 0===e||1===e})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(e.LogTagId.A_GET_HISTORY_MSG_T,JSON.stringify({targetId:t.targetId,conversationType:t.conversationType,channelId:t.channelId,timestamp:null==n?void 0:n.timestamp,count:null==n?void 0:n.count}),r),[4,a.getHistoryMessage(t.conversationType,t.targetId,null==n?void 0:n.timestamp,null==n?void 0:n.count,null==n?void 0:n.order,null==t?void 0:t.channelId,"",r)];case 1:return i=o.sent(),d=i.code,u=i.data,d===e.ErrorCode.SUCCESS&&u?(c=[],E=u.list.map((function(e){return c.push(e.messageUId),_(e)})),s.info(e.LogTagId.A_GET_HISTORY_MSG_R,JSON.stringify({code:d,messageUIds:c}),r),[2,{code:d,data:{list:E,hasMore:u.hasMore}}]):(s.warn(e.LogTagId.A_GET_HISTORY_MSG_R,JSON.stringify({code:d,messageUIds:""}),r),[2,{code:d,msg:U[d]}])}}))}))}))]}))}))},exports.getJoinedGroups=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_JOINED_GROUPS_T,T.A_GET_JOINED_GROUPS_R).validateParameters("groupIds",t,e.isLimitedArray({minLength:1,maxLength:20,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_GROUP_IDS,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_JOINED_GROUPS_T,JSON.stringify({groupIds:t}),n),[4,r.getJoinedGroups(t)];case 1:return i=o.sent(),s=i.code,_=i.data,d=s===e.ErrorCode.SUCCESS?"info":"warn",a[d](T.A_GET_JOINED_GROUPS_R,JSON.stringify({code:s}),n),s!==e.ErrorCode.SUCCESS?[2,{code:s}]:[2,{code:s,data:_}]}}))}))}))]}))}))},exports.getJoinedGroupsByRole=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_GET_JOINED_GROUPS_BY_ROLES_T,T.A_GET_JOINED_GROUPS_BY_ROLES_R).validateParameters("option",t,e.AssertRules.OBJECT,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION,!0).validateParameters("option.count",t.count,e.isInteger({min:1,max:100}),e.ErrorCode.INVALID_PARAMETER_COUNT,!0).validateParameters("option.pageToken",null==t?void 0:t.pageToken,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).validateParameters("option.order",null==t?void 0:t.order,e.AssertRules.BOOLEAN,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).validateParameters("role",n,(function(t){return e.isValidEnum({value:t,type:e.GroupMemberRole})}),e.ErrorCode.INVALID_PARAMETER_GROUP_MEMBER_ROLE).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_JOINED_GROUPS_BY_ROLES_T,JSON.stringify({role:n,option:t,formatOption:ke(t)}),r),[4,a.getJoinedGroupsByRole(n,ke(t))];case 1:return i=o.sent(),_=i.code,d=i.data,u=_===e.ErrorCode.SUCCESS?"info":"warn",s[u](T.A_GET_JOINED_GROUPS_BY_ROLES_R,JSON.stringify({code:_,data:d}),r),_!==e.ErrorCode.SUCCESS?[2,{code:_}]:[2,{code:_,data:d}]}}))}))}))]}))}))},exports.getMessage=pe,exports.getMessageReadReceiptInfoV5=function(e,t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,C().next((function(r){return i(n,void 0,void 0,(function(){return o(this,(function(n){return[2,r.getMessageReadReceiptInfoV5(e,t)]}))}))}))]}))}))},exports.getMessageReadReceiptV4=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(e.LogTagId.A_GET_MESSAGE_READ_RECEIPT_V4_T,e.LogTagId.A_GET_MESSAGE_READ_RECEIPT_V4_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,(function(t){return t===e.ConversationType.GROUP}),!0,"It should be ConversationType.GROUP").validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("messageUId",n,e.AssertRules.STRING,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:return r="conversationType:".concat(t.conversationType,",targetId:").concat(t.targetId,",\n      channelId: ").concat(t.channelId,", messageUid:").concat(n),i=s.createTraceId(),s.info(e.LogTagId.A_GET_MESSAGE_READ_RECEIPT_V4_T,r,i),3!==(null==(_=a.getNaviInfoFromCache())?void 0:_.grpRRVer)?[2,{code:e.ErrorCode.READ_RECEIPT_V4_NOT_SUPPORT}]:[4,a.getMessageReadReceiptV4(t,n)];case 1:return d=o.sent(),u=d.code,c=d.msg,E=d.data,s[u===e.ErrorCode.SUCCESS?"info":"warn"](e.LogTagId.A_GET_MESSAGE_READ_RECEIPT_V4_R,"code: ".concat(u,", msg: ").concat(c),i),u===e.ErrorCode.SUCCESS&&E?[2,{code:u,data:E}]:[2,{code:u,msg:c}]}}))}))}))]}))}))},exports.getMessageReader=function(t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(T.A_GET_MESSAGE_READER_T,T.A_GET_MESSAGE_READER_R).validate("messageUId",n,e.AssertRules.STRING,!0).validate("targetId",t,e.AssertRules.STRING,!0).validate("channelId",r,e.AssertRules.CHANNEL_ID).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d,u;return o(this,(function(o){switch(o.label){case 0:return i=_.createTraceId(),_.info(T.A_GET_MESSAGE_READER_T,JSON.stringify({targetId:t,messageUId:n,channelId:r}),i),[4,s.getMessageReader(t,n,r)];case 1:return a=o.sent(),d=a.code,u=a.data,d===e.ErrorCode.SUCCESS?(_.info(T.A_GET_MESSAGE_READER_R,JSON.stringify({code:d,totalMemberCount:null==u?void 0:u.totalMemberCount}),i),[2,{code:d,data:u}]):(_.warn(T.A_GET_MESSAGE_READER_R,JSON.stringify({code:d})),[2,{code:d,msg:U[d]}])}}))}))}))]}))}))},exports.getMessagesReadReceiptByUsersV5=function(e,t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,C().next((function(a){return i(r,void 0,void 0,(function(){return o(this,(function(r){return[2,a.getMessagesReadReceiptByUsersV5(e,t,n)]}))}))}))]}))}))},exports.getMessagesReadReceiptUsersByPageV5=function(e,t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,C().next((function(a){return i(r,void 0,void 0,(function(){return o(this,(function(r){return[2,a.getMessagesReadReceiptUsersByPageV5(e,t,n)]}))}))}))]}))}))},exports.getMyUserProfile=function(){var t=this;return new e.ValidatorManage(T.A_GET_MY_USER_PROFILE_T,T.A_GET_MY_USER_PROFILE_R).ifReady((function(n,r){return i(t,void 0,void 0,(function(){var t,i,a,s,_;return o(this,(function(o){switch(o.label){case 0:return t=r.createTraceId(),r.info(T.A_GET_MY_USER_PROFILE_T,"",t),[4,n.getMyUserProfile()];case 1:return i=o.sent(),a=i.code,s=i.data,_=a===e.ErrorCode.SUCCESS?"info":"warn",r[_](T.A_GET_MY_USER_PROFILE_R,JSON.stringify({code:a,data:s}),t),[2,{code:a,data:s}]}}))}))}))},exports.getMyUserProfileVisibility=function(){var t=this;return new e.ValidatorManage(T.A_GET_MY_USER_PROFILE_VISIBILITY_T,T.A_GET_MY_USER_PROFILE_VISIBILITY_R).ifReady((function(n,r){return i(t,void 0,void 0,(function(){var t,i,a,s,_;return o(this,(function(o){switch(o.label){case 0:return t=r.createTraceId(),r.info(T.A_GET_MY_USER_PROFILE_VISIBILITY_T,"",t),[4,n.getMyUserProfileVisibility()];case 1:return i=o.sent(),a=i.code,s=i.data,_=a===e.ErrorCode.SUCCESS?"info":"warn",r[_](T.A_GET_MY_USER_PROFILE_VISIBILITY_R,JSON.stringify({code:a,data:s}),t),[2,{code:a,data:s}]}}))}))}))},exports.getNotificationQuietHoursSetting=function(){return C().next((function(e){return e.getNotificationQuietHoursSetting()}))},exports.getProxy=function(){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return[2,new e.ValidatorManage(T.A_GET_PROXY_T,T.A_GET_PROXY_R).ifReady((function(n,r){return i(t,void 0,void 0,(function(){var t,i,a,s;return o(this,(function(o){switch(o.label){case 0:return t=r.createTraceId(),r.info(T.A_GET_PROXY_T,"",t),[4,n.getProxy()];case 1:return i=o.sent(),a=i.code,s=i.data,a===e.ErrorCode.SUCCESS?(r.info(T.A_GET_PROXY_R,JSON.stringify({code:a,data:s}),t),[2,{code:a,data:s}]):(r.warn(T.A_GET_PROXY_R,JSON.stringify({code:a}),t),[2,{code:a,msg:U[a]}])}}))}))}))]}))}))},exports.getRealtimeConTotalUnreadCount=function(){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return[2,new e.ValidatorManage(T.A_GET_REALTIME_CON_TOTAL_UNREAD_COUNT_T,T.A_GET_REALTIME_CON_TOTAL_UNREAD_COUNT_R).ifReady((function(n,r){return i(t,void 0,void 0,(function(){var t,i,a,s;return o(this,(function(o){switch(o.label){case 0:return t=r.createTraceId(),r.info(T.A_GET_REALTIME_CON_TOTAL_UNREAD_COUNT_T,"",t),[4,n.getRealtimeConTotalUnreadCount()];case 1:return i=o.sent(),a=i.code,s=i.data,r[e.ErrorCode.SUCCESS===a?"info":"warn"](T.A_GET_REALTIME_CON_TOTAL_UNREAD_COUNT_R,"code: ".concat(a,", data: ").concat(s),t),[2,{code:a,data:s}]}}))}))}))]}))}))},exports.getRealtimeConUnreadCounts=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_REALTIME_CON_UNREAD_COUNTS_T,T.A_GET_REALTIME_CON_UNREAD_COUNTS_R).validate("conversationList",t,e.isLimitedArray({minLength:1,maxLength:10,itemValidator:function(t){return e.isValidConversation(t)}}),!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_REALTIME_CON_UNREAD_COUNTS_T,"cons: ".concat(JSON.stringify(t)),n),[4,r.getRealtimeConUnreadCounts(t)];case 1:return i=o.sent(),s=i.code,_=i.data,a[e.ErrorCode.SUCCESS===s?"info":"warn"](T.A_GET_REALTIME_CON_UNREAD_COUNTS_R,"code: ".concat(s),n),[2,{code:s,data:_}]}}))}))}))]}))}))},exports.getRealtimeConversations=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_REALTIME_CONVERSATIONS_T,T.A_GET_REALTIME_CONVERSATIONS_R).validate("count",null==t?void 0:t.count,e.isInteger({min:1,max:50})).validate("startTime",null==t?void 0:t.startTime,e.isInteger({min:0})).validate("order",null==t?void 0:t.order,(function(e){return[0,1].includes(e)})).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,u,c,E,l;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_REALTIME_CONVERSATIONS_T,JSON.stringify({count:null==t?void 0:t.count,startTime:null==t?void 0:t.startTime,order:null==t?void 0:t.order}),n),[4,r.getRealtimeConversations(null!==(c=null==t?void 0:t.count)&&void 0!==c?c:30,null!==(E=null==t?void 0:t.startTime)&&void 0!==E?E:0,null!==(l=null==t?void 0:t.order)&&void 0!==l?l:0)];case 1:return i=o.sent(),s=i.code,_=i.data,s===e.ErrorCode.SUCCESS&&_?(u=_.map((function(e){return d(e)})),a.info(T.A_GET_REALTIME_CONVERSATIONS_R,JSON.stringify({code:s,list:u.length}),n),[2,{code:0,data:u}]):(a.warn(T.A_GET_REALTIME_CONVERSATIONS_R,JSON.stringify({code:s})),[2,{code:s,msg:U[s]}])}}))}))}))]}))}))},exports.getRemoteHistoryMessages=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_GET_REMOTE_HISTORY_MESSAGES_T,T.A_GET_REMOTE_HISTORY_MESSAGES_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("timestamp",null==n?void 0:n.timestamp,e.AssertRules.NUMBER).validate("count",null==n?void 0:n.count,e.AssertRules.NUMBER).validate("count",null==n?void 0:n.count,(function(e){return e>0&&e<=100})).validate("order",null==n?void 0:n.order,(function(e){return 0===e||1===e})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_REMOTE_HISTORY_MESSAGES_T,JSON.stringify({targetId:t.targetId,conversationType:t.conversationType,channelId:t.channelId,timestamp:null==n?void 0:n.timestamp,count:null==n?void 0:n.count,order:null==n?void 0:n.order}),r),[4,a.getRemoteHistoryMessages(t.conversationType,t.targetId,(null==n?void 0:n.timestamp)||0,(null==n?void 0:n.count)||20,(null==n?void 0:n.order)||0,t.channelId||"")];case 1:return i=o.sent(),d=i.code,u=i.data,d===e.ErrorCode.SUCCESS&&u?(c=[],E=u.list.map((function(e){return c.push(e.messageUId),_(e)})),s.info(T.A_GET_REMOTE_HISTORY_MESSAGES_R,JSON.stringify({code:d,messageUIds:c})),[2,{code:d,data:{list:E,hasMore:u.hasMore}}]):(s.warn(T.A_GET_REMOTE_HISTORY_MESSAGES_R,JSON.stringify({code:d})),[2,{code:d,msg:U[d]}])}}))}))}))]}))}))},exports.getServerTime=function(){return O((function(e){return e.getServerTime()}))},exports.getSubscribeUserList=function(t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(T.A_GET_SUBSCRIBE_USER_STATUS_LIST_T,T.A_GET_SUBSCRIBE_USER_STATUS_LIST_R).validate("subscribeType",t,(function(t){return[e.SubscribeType.ONLINE_STATUS,e.SubscribeType.USER_PROFILE].includes(t)}),!0).validate("subscribePageSize",n,e.isInteger({min:1,max:200}),!0).validate("offset",r,e.isInteger({min:0}),!0).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d,u,c;return o(this,(function(o){switch(o.label){case 0:return i=_.createTraceId(),_.info(T.A_GET_SUBSCRIBE_USER_STATUS_LIST_T,JSON.stringify({pageSize:n,offset:r}),i),[4,s.getSubscribeUserList(t,n,r)];case 1:return a=o.sent(),d=a.code,u=a.data,c=d===e.ErrorCode.SUCCESS?"info":"warn",_[c](T.A_GET_SUBSCRIBE_USER_STATUS_LIST_R,JSON.stringify({code:d,data:u}),i),d===e.ErrorCode.SUCCESS?[2,{code:d,data:u}]:[2,{code:d}]}}))}))}))]}))}))},exports.getSubscribeUserStatus=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_UNSUBSCRIBE_USER_STATUS_T,T.A_UNSUBSCRIBE_USER_STATUS_R).validate("subscribeType",t,(function(t){return[e.SubscribeType.ONLINE_STATUS,e.SubscribeType.USER_PROFILE].includes(t)}),!0).validate("subscribeUserIds",n,e.isLimitedArray({maxLength:200,minLength:1,itemValidator:e.isValidTargetId}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_SUBSCRIBE_USER_STATUS_T,JSON.stringify({userIds:n}),r),[4,a.getSubscribeUserStatus(t,n)];case 1:return i=o.sent(),_=i.code,d=i.data,u=_===e.ErrorCode.SUCCESS?"info":"warn",s[u](T.A_GET_SUBSCRIBE_USER_STATUS_R,JSON.stringify({code:_}),r),_===e.ErrorCode.SUCCESS?[2,{code:_,data:d}]:[2,{code:_}]}}))}))}))]}))}))},exports.getTags=function(){return i(this,void 0,void 0,(function(){var t=this;return o(this,(function(n){return[2,new e.ValidatorManage("","").ifReady((function(n,r){return i(t,void 0,void 0,(function(){var t,i,a,s,_;return o(this,(function(o){switch(o.label){case 0:return t=r.createTraceId(),r.info(T.A_GET_TAGS_T,"",t),[4,n.getTagList()];case 1:return i=o.sent(),a=i.code,s=i.data,_=a===e.ErrorCode.SUCCESS?"info":"warn",r[_](T.A_GET_TAGS_R,JSON.stringify({code:a}),t),[2,{code:e.ErrorCode.SUCCESS,data:s}]}}))}))}))]}))}))},exports.getTagsFromConversation=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_TAGS_FOR_CONVERSATION_T,T.A_GET_TAGS_FOR_CONVERSATION_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_TAGS_FOR_CONVERSATION_T,JSON.stringify({conversationType:t.conversationType,targetId:t.targetId,channelId:t.channelId}),n),[4,r.getTagsForConversation(t)];case 1:return i=o.sent(),s=i.code,_=i.data,d=s===e.ErrorCode.SUCCESS?"info":"warn",a[d](T.A_GET_TAGS_FOR_CONVERSATION_R,JSON.stringify({code:s}),n),s===e.ErrorCode.SUCCESS?[2,{code:s,data:_}]:[2,{code:s,msg:U[s]}]}}))}))}))]}))}))},exports.getTextMessageDraft=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_CONVERSATION_MESSAGE_DRAFT_T,T.A_GET_CONVERSATION_MESSAGE_DRAFT_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:return n=t.conversationType,i=t.targetId,s=t.channelId,_=a.createTraceId(),a.info(T.A_GET_CONVERSATION_MESSAGE_DRAFT_T,JSON.stringify({conversationType:n,targetId:i,channelId:s}),_),[4,r.getConversationMessageDraft(n,i,s)];case 1:return d=o.sent(),u=d.code,c=d.data,E=u===e.ErrorCode.SUCCESS?"info":"warn",a[E](T.A_GET_CONVERSATION_MESSAGE_DRAFT_R,JSON.stringify({code:u,data:c}),_),u===e.ErrorCode.SUCCESS?[2,{code:u,data:c||""}]:[2,{code:u,msg:U[u]}]}}))}))}))]}))}))},exports.getTopConversationList=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,C().next((function(a){return i(r,void 0,void 0,(function(){var r;return o(this,(function(i){switch(i.label){case 0:return r={type:e.ConversationListFilterType.TOP,params:{conversationTypes:t,channelId:n}},[4,a.getConversationListByFilter(r)];case 1:return[2,i.sent().trans((function(e){return e.map(d)}))]}}))}))}))]}))}))},exports.getTotalUnreadCount=function(e,t){return i(this,void 0,void 0,(function(){return o(this,(function(n){return[2,C().next((function(n){return n.getTotalUnreadCount("",t,e)}))]}))}))},exports.getTotalUnreadCountByLevels=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_GET_TOTAL_UNREAD_COUNT_BY_LEVELS_T,T.A_GET_TOTAL_UNREAD_COUNT_BY_LEVELS_R).validate("conversationTypes",t,(function(t){return!!e.isArray(t)&&!t.some((function(t){return!e.isValidConversationType(t)}))}),!0).validate("levels",n,(function(t){return!!e.isArray(n)&&!t.some((function(t){return!e.isValidNotificationLevel(t)}))}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_TOTAL_UNREAD_COUNT_BY_LEVELS_T,JSON.stringify({conversationTypes:t,levels:n,isMentioned:!1}),r),[4,a.getTotalUnreadCountByLevels(t,n)];case 1:return i=o.sent(),_=i.code,d=i.data,_===e.ErrorCode.SUCCESS?(s.info(T.A_GET_TOTAL_UNREAD_COUNT_BY_LEVELS_R,JSON.stringify({code:_,data:d}),r),[2,{code:_,data:d}]):(s.warn(T.A_GET_TOTAL_UNREAD_COUNT_BY_LEVELS_R,JSON.stringify({code:_}),r),[2,{code:_,msg:U[_]}])}}))}))}))]}))}))},exports.getTotalUnreadMentionedCountByLevels=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_GET_TOTAL_UNREAD_COUNT_BY_LEVELS_T,T.A_GET_TOTAL_UNREAD_COUNT_BY_LEVELS_R).validate("conversationTypes",t,(function(t){return!!e.isArray(t)&&!t.some((function(t){return!e.isValidConversationType(t)}))}),!0).validate("levels",n,(function(t){return!!e.isArray(t)&&!t.some((function(t){return!e.isValidNotificationLevel(t)}))}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_TOTAL_UNREAD_COUNT_BY_LEVELS_T,JSON.stringify({conversationTypes:t,levels:n,isMentioned:!0}),r),[4,a.getTotalUnreadCountByLevels(t,n,!0)];case 1:return i=o.sent(),_=i.code,d=i.data,_===e.ErrorCode.SUCCESS?(s.info(T.A_GET_TOTAL_UNREAD_COUNT_BY_LEVELS_R,JSON.stringify({code:_,data:d}),r),[2,{code:_,data:d}]):(s.warn(T.A_GET_TOTAL_UNREAD_COUNT_BY_LEVELS_R,JSON.stringify({code:_}),r),[2,{code:_,msg:U[_]}])}}))}))}))]}))}))},exports.getUltraGroupDefaultNotificationLevel=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_T,T.A_GET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_R).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_T,JSON.stringify({targetId:t.targetId,channelId:t.channelId}),n),[4,r.getUltraGroupDefaultNotificationLevel(t.targetId,t.channelId)];case 1:return i=o.sent(),s=i.code,_=i.data,s===e.ErrorCode.SUCCESS?(a.info(T.A_GET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_R,JSON.stringify({code:s,data:_}),n),[2,{code:s,data:_}]):(a.warn(T.A_GET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_R,JSON.stringify({code:s}),n),[2,{code:s,msg:U[s]}])}}))}))}))]}))}))},exports.getUltraGroupFirstUnreadMessageTimestamp=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_ULTRA_GROUP_UNREAD_MENTIONED_MESSAGES_T,T.A_GET_ULTRA_GROUP_UNREAD_MENTIONED_MESSAGES_R).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d,u,c;return o(this,(function(o){switch(o.label){case 0:return n=t.targetId,i=t.channelId,s={targetId:n,channelId:i},_=a.createTraceId(),a.info(T.A_GET_ULTRA_GROUP_FIRST_UNREAD_MESSAGE_TIMESTAMP_T,JSON.stringify(s),_),[4,r.getUltraGroupFirstUnreadMessageTimestamp(s)];case 1:return d=o.sent(),u=d.code,c=d.data,u===e.ErrorCode.SUCCESS?(a.info(T.A_GET_ULTRA_GROUP_FIRST_UNREAD_MESSAGE_TIMESTAMP_R,JSON.stringify({code:u,sentTime:null==c?void 0:c.sentTime}),_),[2,{code:u,data:c}]):(a.warn(T.A_GET_ULTRA_GROUP_FIRST_UNREAD_MESSAGE_TIMESTAMP_R,JSON.stringify({code:u}),_),[2,{code:u,msg:U[u]}])}}))}))}))]}))}))},exports.getUltraGroupList=function(t){return void 0===t&&(t={}),i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_ULTRA_GROUP_LIST_T,T.A_GET_ULTRA_GROUP_LIST_R).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!1).validate("channelType",null==t?void 0:t.channelType,(function(t){return void 0!==e.UltraGroupChannelType[t]}),!1).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_ULTRA_GROUP_LIST_T,JSON.stringify({targetId:t.targetId,channelType:t.channelType}),n),[4,r.getUltraGroupList(t)];case 1:return i=o.sent(),s=i.code,_=i.data,s===e.ErrorCode.SUCCESS?(a.info(T.A_GET_ULTRA_GROUP_LIST_R,JSON.stringify({code:s,length:null==_?void 0:_.length}),n),[2,{code:e.ErrorCode.SUCCESS,data:_}]):(a.warn(T.A_GET_ULTRA_GROUP_LIST_R,JSON.stringify({code:s}),n),[2,{code:s,msg:U[s]}])}}))}))}))]}))}))},exports.getUltraGroupMessageListByMessageUId=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_GET_ULTRA_GROUP_MESSAGE_LIST_BY_MESSAGE_UID_T,T.A_GET_ULTRA_GROUP_MESSAGE_LIST_BY_MESSAGE_UID_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("messages",n,e.AssertRules.ARRAY,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,d,u,c,E,l,A,R,S,I,f;return o(this,(function(o){switch(o.label){case 0:return r=t.targetId,i=t.conversationType,d=t.channelId,u=[],n.forEach((function(e){var t=e.sendTime,n=e.messageUId;u.push({sendTime:t,messageUId:n,channelId:d})})),c={targetId:r,conversationType:i,messages:u},E=s.createTraceId(),s.info(T.A_GET_ULTRA_GROUP_MESSAGE_LIST_BY_MESSAGE_UID_T,JSON.stringify(c),E),[4,a.getUltraGroupMessageListByMessageUId(c)];case 1:if(l=o.sent(),A=l.code,R=l.data,A===e.ErrorCode.SUCCESS){for(f in S={},I=[],R)I.push(f),S[f]=R[f]?_(R[f]):void 0;return s.info(T.A_GET_ULTRA_GROUP_MESSAGE_LIST_BY_MESSAGE_UID_R,JSON.stringify({code:A,messageUIds:I}),E),[2,{code:e.ErrorCode.SUCCESS,data:S}]}return s.warn(T.A_GET_ULTRA_GROUP_MESSAGE_LIST_BY_MESSAGE_UID_R,JSON.stringify({code:A}),E),[2,{code:A,msg:U[A]}]}}))}))}))]}))}))},exports.getUltraGroupUnreadCountByTargetId=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_GET_ULTRA_GROUP_UNREAD_COUNT_BY_TARGET_ID_T,T.A_GET_ULTRA_GROUP_UNREAD_COUNT_BY_TARGET_ID_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("notificationLevels",n,e.AssertRules.ARRAY).validate("notificationLevels",n,(function(t){return!(null==n?void 0:n.some((function(t){return!e.isValidNotificationLevel(t)})))})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_ULTRA_GROUP_UNREAD_COUNT_BY_TARGET_ID_T,JSON.stringify({targetId:t,notificationLevels:n}),r),[4,a.getUltraGroupUnreadCountByTargetId(t,n)];case 1:return i=o.sent(),_=i.code,d=i.data,u=_===e.ErrorCode.SUCCESS?"info":"warn",s[u](T.A_GET_ULTRA_GROUP_UNREAD_COUNT_BY_TARGET_ID_R,JSON.stringify({code:_,data:d}),r),[2,{code:_,data:d}]}}))}))}))]}))}))},exports.getUltraGroupUnreadInfoList=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_ULTRA_GROUP_UNREAD_INFO_LIST_T,T.A_GET_ULTRA_GROUP_UNREAD_INFO_LIST_R).validate("targetIds",t,(function(n){return!!e.validate("targetIds",t,e.AssertRules.ARRAY,!0)&&(!(n.length<=0||n.length>20)&&!t.some((function(t){return!e.isString(t)})))})).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d,u;return o(this,(function(o){switch(o.label){case 0:return n=t.filter((function(e,n){return t.indexOf(e)===n})),i=a.createTraceId(),a.info(T.A_GET_ULTRA_GROUP_UNREAD_INFO_LIST_T,JSON.stringify({targetIds:t}),i),[4,r.getUltraGroupUnreadInfoList(n)];case 1:return s=o.sent(),_=s.code,d=s.data,u=_===e.ErrorCode.SUCCESS?"info":"warn",a[u](T.A_GET_ULTRA_GROUP_UNREAD_INFO_LIST_R,JSON.stringify({code:_}),i),_===e.ErrorCode.SUCCESS?[2,{code:_,data:d}]:[2,{code:_,msg:U[_]}]}}))}))}))]}))}))},exports.getUltraGroupUnreadMentionedCountByTargetId=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_GET_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_BY_TARGET_ID_T,T.A_GET_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_BY_TARGET_ID_T).validate("targetId",t,e.AssertRules.STRING,!0).validate("notificationLevels",n,e.AssertRules.ARRAY).validate("notificationLevels",n,(function(t){return!(null==n?void 0:n.some((function(t){return!e.isValidNotificationLevel(t)})))})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_BY_TARGET_ID_T,JSON.stringify({targetId:t,notificationLevels:n}),r),[4,a.getUltraGroupUnreadMentionedCountByTargetId(t,n)];case 1:return i=o.sent(),_=i.code,d=i.data,_===e.ErrorCode.SUCCESS?(s.info(T.A_GET_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_BY_TARGET_ID_R,JSON.stringify({code:_,data:d}),t),[2,{code:e.ErrorCode.SUCCESS,data:d}]):(s.warn(T.A_GET_ULTRA_GROUP_UNREAD_MENTIONED_COUNT_BY_TARGET_ID_R,JSON.stringify({code:_}),r),[2,{code:_,msg:U[_]}])}}))}))}))]}))}))},exports.getUltraGroupUnreadMentionedMessages=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return t.channelId=t.channelId||"",[2,new e.ValidatorManage(T.A_GET_ULTRA_GROUP_UNREAD_MENTIONED_MESSAGES_T,T.A_GET_ULTRA_GROUP_UNREAD_MENTIONED_MESSAGES_R).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("sentTime",null==t?void 0:t.sentTime,e.AssertRules.NUMBER,!0).validate("count",null==t?void 0:t.count,(function(t){return e.isNumber(t)&&t>0&&t<=50}),!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d,u,c,E,l;return o(this,(function(o){switch(o.label){case 0:return n=t.targetId,i=t.channelId,s=t.sentTime,_=t.count,d={targetId:n,channelId:i,sentTime:s,count:_},u=a.createTraceId(),a.info(T.A_GET_ULTRA_GROUP_UNREAD_MENTIONED_MESSAGES_T,JSON.stringify(d),u),[4,r.getUltraGroupUnreadMentionedMessages(d)];case 1:return c=o.sent(),E=c.code,l=c.data,E===e.ErrorCode.SUCCESS?(a.info(T.A_GET_ULTRA_GROUP_UNREAD_MENTIONED_MESSAGES_R,JSON.stringify({code:E,data:null==l?void 0:l.map((function(e){return e.messageUId}))}),u),[2,{code:E,data:l}]):(a.warn(T.A_GET_ULTRA_GROUP_UNREAD_MENTIONED_MESSAGES_R,JSON.stringify({code:E}),u),[2,{code:E,msg:U[E]}])}}))}))}))]}))}))},exports.getUnreadConversationList=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,C().next((function(r){return i(n,void 0,void 0,(function(){var n;return o(this,(function(i){switch(i.label){case 0:return n={type:e.ConversationListFilterType.UNREAD,params:{conversationTypes:t}},[4,r.getConversationListByFilter(n)];case 1:return[2,i.sent().trans((function(e){return e.map(d)}))]}}))}))}))]}))}))},exports.getUnreadCount=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_UNREAD_COUNT_T,T.A_GET_UNREAD_COUNT_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d,u,c;return o(this,(function(o){switch(o.label){case 0:return n=t.conversationType,i=t.targetId,s=t.channelId,_=a.createTraceId(),a.info(T.A_GET_UNREAD_COUNT_T,JSON.stringify({conversationType:n,targetId:i,channelId:s}),_),[4,r.getUnreadCount(n,i,s)];case 1:return d=o.sent(),u=d.code,c=d.data,u===e.ErrorCode.SUCCESS?(a.info(T.A_GET_UNREAD_COUNT_R,JSON.stringify({code:u,data:c})),[2,{code:u,data:c}]):(a.warn(T.A_GET_UNREAD_COUNT_R,JSON.stringify({code:u})),[2,{code:u,msg:U[u]}])}}))}))}))]}))}))},exports.getUnreadCountByTag=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_GET_UNREAD_COUNT_BY_TAG_T,T.A_GET_UNREAD_COUNT_BY_TAG_R).validate("tagId",t,e.AssertRules.STRING,!0).validate("containMuted",n,e.AssertRules.BOOLEAN,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_GET_UNREAD_COUNT_BY_TAG_T,JSON.stringify({tagId:t,containMuted:n}),r),[4,a.getUnreadCountByTag(t,n)];case 1:return i=o.sent(),_=i.code,d=i.data,u=_===e.ErrorCode.SUCCESS?"info":"warn",s[u](T.A_GET_UNREAD_COUNT_BY_TAG_T,JSON.stringify({code:_,data:d}),r),[2,{code:_,data:d}]}}))}))}))]}))}))},exports.getUnreadMentionedCount=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_GET_UNREAD_MENTIONED_COUNT_T,T.A_GET_UNREAD_MENTIONED_COUNT_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_UNREAD_MENTIONED_COUNT_T,JSON.stringify({conversationType:t.conversationType,targetId:t.targetId,channelId:t.channelId}),n),[4,r.getUnreadMentionedCount(t)];case 1:return i=o.sent(),s=i.code,_=i.data,s===e.ErrorCode.SUCCESS?(a.info(T.A_GET_UNREAD_MENTIONED_COUNT_R,JSON.stringify({code:s,data:_}),n),[2,{code:s,data:_}]):(a.warn(T.A_GET_UNREAD_MENTIONED_COUNT_R,JSON.stringify({code:s}),n),[2,{code:s}])}}))}))}))]}))}))},exports.getUnreadMentionedMessages=function(t){return e.usingCppEngine()?new e.ValidatorManage(T.A_GET_UNREAD_MENTIONED_MESSAGES_T,T.A_GET_UNREAD_MENTIONED_MESSAGES_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReadySync((function(n,r){var i=t.conversationType,o=t.targetId,a=t.channelId,s=r.createTraceId();r.info(T.A_GET_UNREAD_MENTIONED_MESSAGES_T,JSON.stringify({conversationType:i,targetId:o,channelId:a}),s);var d=n.getUnreadMentionedMessages(i,o,a),u=[];return d&&d.length&&d.forEach((function(e){return u.push(_(e))})),r.info(T.A_GET_UNREAD_MENTIONED_MESSAGES_R,JSON.stringify({code:e.ErrorCode.SUCCESS,count:u.length}),s),{code:e.ErrorCode.SUCCESS,data:u}})):{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}},exports.getUserProfiles=function(t){var n=this;return new e.ValidatorManage(T.A_GET_USER_PROFILES_T,T.A_GET_USER_PROFILES_R).validate("userId",t,e.isLimitedArray({minLength:1,itemValidator:e.notEmptyString}),!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_GET_USER_PROFILES_T,JSON.stringify({userIds:t}),n),[4,r.getUserProfiles(t)];case 1:return i=o.sent(),s=i.code,_=i.data,d=s===e.ErrorCode.SUCCESS?"info":"warn",a[d](T.A_GET_USER_PROFILES_R,JSON.stringify({code:s,data:_}),n),[2,{code:s,data:_}]}}))}))}))},exports.init=function(t){!function(t){var n;I?v.warn(e.LogTagId.A_INIT_REPEAT_O):(e.assert("options.appkey",t.appkey,e.AssertRules.STRING,!0),e.assert("options.navigators",t.navigators,(function(t){return e.isArray(t)&&(0===t.length||t.every(e.isHttpUrl))})),f=t,I=e.APIContext.init({appkey:t.appkey,navigators:t.navigators||[],miniCMPProxy:t.customCMP||[],logOutputLevel:t.logOutputLevel,httpInMainProcess:!!t.httpInMainProcess,logExpireTime:t.logExpireTime,areaCode:t.areaCode,logServerUrl:t.logServerUrl,uploadDomain:t.uploadDomain,environment:null!==(n=t.environment)&&void 0!==n?n:"default",eventLoopBlockingCheck:t.eventLoopBlockingCheck}),v=I.logger,I.assignWatcher(R),e.logger.init(v),I.sse.on("message",(function(e,t){var n=r(r({},t),{message:t.message?_(t.message):void 0});A.emit(exports.Events.STREAM_MESSAGE_RESPONSE,n)})))}(t)},exports.insertMessage=function(t,n,r){return void 0===r&&(r={}),i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return u("RongIMLib.insertMessage","RongIMLib.ElectronExtension.insertMessage"),e.usingCppEngine()?[2,new e.ValidatorManage(T.A_INSERT_MESSAGE_T,T.A_INSERT_MESSAGE_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("searchContent",null==r?void 0:r.searchContent,e.AssertRules.STRING).validate("senderUserId",null==n?void 0:n.senderUserId,e.AssertRules.STRING,!0).validate("messageType",null==n?void 0:n.messageType,e.AssertRules.STRING,!0).validate("content",null==n?void 0:n.content,e.AssertRules.OBJECT,!0).validate("messageDirection",null==n?void 0:n.messageDirection,e.AssertRules.NUMBER,!0).validate("canIncludeExpansion",null==n?void 0:n.canIncludeExpansion,e.AssertRules.BOOLEAN).validate("expansion",null==n?void 0:n.expansion,e.AssertRules.OBJECT).validate("disableNotification",null==n?void 0:n.disableNotification,e.AssertRules.BOOLEAN).validate("sentTime",null==n?void 0:n.sentTime,e.AssertRules.NUMBER).validate("sentStatus",null==n?void 0:n.sentStatus,e.AssertRules.NUMBER).ifReady((function(s,d){return i(a,void 0,void 0,(function(){var i,a,u,c,E,l,A,R,S,I,f,v,g,O,N,C,p;return o(this,(function(o){switch(o.label){case 0:return i=n.senderUserId,a=n.messageType,u=n.content,c=n.messageDirection,E=n.messageUId,l=n.canIncludeExpansion,A=n.expansion,R=n.disableNotification,S=n.sentTime,I=n.sentStatus,f=d.createTraceId(),d.info(T.A_INSERT_MESSAGE_T,JSON.stringify({conversationType:t.conversationType,targetId:t.targetId,channelId:t.channelId}),f),v=r.isUnread,g=r.searchContent,O={senderUserId:i,messageType:a,content:u,messageDirection:c,sentTime:S,sentStatus:I,searchContent:g,isUnread:!!v,messageUId:E,disableNotification:R,canIncludeExpansion:l,expansionMsg:JSON.stringify(A),channelId:t.channelId||""},[4,s.insertMessage(t.conversationType,t.targetId,O)];case 1:return N=o.sent(),C=N.code,p=N.data,C===e.ErrorCode.SUCCESS?(d.info(T.A_INSERT_MESSAGE_R,JSON.stringify({code:C,messageId:null==p?void 0:p.messageId}),f),[2,{code:C,data:_(p)}]):(d.warn(T.A_INSERT_MESSAGE_R,JSON.stringify({code:C}),f),[2,{code:C,msg:U[C]}])}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},exports.installPlugin=function(e,t){return O((function(n){return n.install(e,t)}))},exports.inviteUsersToGroup=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_INVITE_USERS_TO_GROUP_T,T.A_INVITE_USERS_TO_GROUP_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("userIds",n,e.isLimitedArray({minLength:1,maxLength:30,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_USERIDLIST).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_INVITE_USERS_TO_GROUP_T,JSON.stringify({groupId:t,userIds:n}),r),[4,a.inviteUsersToGroup(t,n)];case 1:return i=o.sent(),_=i.code,d=i.data,u=_===e.ErrorCode.SUCCESS?"info":"warn",s[u](T.A_INVITE_USERS_TO_GROUP_R,JSON.stringify({code:_}),r),_!==e.ErrorCode.SUCCESS?[2,{code:_}]:[2,{code:_,data:d}]}}))}))}))]}))}))},exports.joinChatRoom=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(e.LogTagId.A_JOIN_CHATROOM_T,e.LogTagId.A_JOIN_CHATROOM_R).validate("targetId",t,e.isValidTargetId,!0).validate("count",n,e.AssertRules.OBJECT,!0).validate("count",null==n?void 0:n.count,e.AssertRules.NUMBER,!0).validate("extra",null==n?void 0:n.extra,e.AssertRules.ONLY_STRING,!1).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(e.LogTagId.A_JOIN_CHATROOM_T,JSON.stringify({chatroomId:t,count:n.count,joinExist:!1}),r),[4,a.joinChatroom(t,n.count,r,n.extra)];case 1:return i=o.sent(),_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](e.LogTagId.A_JOIN_CHATROOM_R,JSON.stringify({code:i,timestamp:(new Date).getTime()}),r),i!==e.ErrorCode.SUCCESS?[2,{code:i,msg:U[i]}]:[2,{code:i}]}}))}))}))]}))}))},exports.joinExistChatRoom=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(e.LogTagId.A_JOIN_CHATROOM_T,e.LogTagId.A_JOIN_CHATROOM_R).validate("targetId",t,e.isValidTargetId,!0).validate("count",null==n?void 0:n.count,e.AssertRules.NUMBER,!0).validate("extra",null==n?void 0:n.extra,e.AssertRules.ONLY_STRING,!1).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(e.LogTagId.A_JOIN_CHATROOM_T,JSON.stringify({chatroomId:t,count:n.count,joinExist:!0}),r),[4,a.joinExistChatroom(t,n.count,r,n.extra)];case 1:return i=o.sent(),_=i.code,d=i.data,u=_===e.ErrorCode.SUCCESS?"info":"warn",s[u](e.LogTagId.A_JOIN_CHATROOM_R,JSON.stringify({code:_,timestamp:(new Date).getTime()}),r),_!==e.ErrorCode.SUCCESS?[2,{code:_,msg:U[_]}]:[2,{code:_,data:d}]}}))}))}))]}))}))},exports.joinGroup=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_JOIN_GROUP_T,T.A_JOIN_GROUP_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_JOIN_GROUP_T,JSON.stringify({groupId:t}),n),[4,r.joinGroup(t)];case 1:return i=o.sent(),s=i.code,_=i.data,d=s===e.ErrorCode.SUCCESS?"info":"warn",a[d](T.A_JOIN_GROUP_R,JSON.stringify({code:s}),n),s!==e.ErrorCode.SUCCESS?[2,{code:s}]:[2,{code:s,data:_}]}}))}))}))]}))}))},exports.kickGroupMembers=function(t,n,r){return void 0===r&&(r={removeMuteStatus:!0,removeWhiteList:!0,removeFollow:!0}),i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(T.A_KICK_GROUP_MEMBERS_T,T.A_KICK_GROUP_MEMBERS_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("userIds",n,e.isLimitedArray({minLength:1,maxLength:100,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_USERIDLIST,!0).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d;return o(this,(function(o){switch(o.label){case 0:return i=_.createTraceId(),_.info(T.A_KICK_GROUP_MEMBERS_T,JSON.stringify({groupId:t,userIds:n,config:r}),i),[4,s.kickGroupMembers(t,n,r)];case 1:return a=o.sent().code,d=a===e.ErrorCode.SUCCESS?"info":"warn",_[d](T.A_KICK_GROUP_MEMBERS_R,JSON.stringify({code:a}),i),[2,{code:a}]}}))}))}))]}))}))},exports.modifyMessage=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_MODIFY_MESSAGE_R,T.A_MODIFY_MESSAGE_R).validate("content",t,e.AssertRules.OBJECT,!0).validate("message",n,e.AssertRules.OBJECT,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u,c,E,l,A,R,S;return o(this,(function(o){switch(o.label){case 0:return r=n.conversationType,i=n.targetId,_=n.messageUId,d=n.senderUserId,u=n.sentTime,c=n.canIncludeExpansion,E=n.channelId,l=s.createTraceId(),s.info(T.A_MODIFY_MESSAGE_T,JSON.stringify({conversationType:r,targetId:i,messageUId:_,channelId:E}),l),A={targetId:i,channelId:E,conversationType:r,fromUserId:d,sendTime:u,canIncludeExpansion:c,messageUId:_,content:t},[4,a.modifyMessage(A)];case 1:return R=o.sent().code,S=R===e.ErrorCode.SUCCESS?"info":"warn",s[S](T.A_MODIFY_MESSAGE_R,JSON.stringify({code:R}),l),R===e.ErrorCode.SUCCESS?[2,{code:e.ErrorCode.SUCCESS}]:[2,{code:R,msg:U[R]}]}}))}))}))]}))}))},exports.onceEventListener=function(e,t,n){A.once(e,t,n)},exports.quitChatRoom=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(e.LogTagId.A_QUIT_CHATROOM_T,e.LogTagId.A_QUIT_CHATROOM_R).validate("targetId",t,e.AssertRules.STRING,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(e.LogTagId.A_QUIT_CHATROOM_T,JSON.stringify({chatroomId:t}),n),[4,r.quitChatroom(t)];case 1:return i=o.sent(),s=i===e.ErrorCode.SUCCESS?"info":"warn",a[s](e.LogTagId.A_QUIT_CHATROOM_R,JSON.stringify({code:i}),n),i!==e.ErrorCode.SUCCESS?[2,{code:i,msg:U[i]}]:[2,{code:i}]}}))}))}))]}))}))},exports.quitGroup=function(t,n){return void 0===n&&(n={removeMuteStatus:!0,removeWhiteList:!0,removeFollow:!0}),i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_QUIT_GROUP_T,T.A_QUIT_GROUP_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_QUIT_GROUP_T,JSON.stringify({groupId:t,config:n}),r),[4,a.quitGroup(t,n)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_QUIT_GROUP_R,JSON.stringify({code:i}),r),[2,{code:i}]}}))}))}))]}))}))},exports.recallMessage=De,exports.refuseFriendApplication=function(t){var n=this;return new e.ValidatorManage(T.A_REFUSE_FRIEND_APPLICATION_T,T.A_REFUSE_FRIEND_APPLICATION_R).validateParameters("userId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_USER_ID,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n;return o(this,(function(i){switch(i.label){case 0:return a.info(T.A_REFUSE_FRIEND_APPLICATION_T,JSON.stringify({userId:t})),[4,r.refuseFriendApplication(t,"")];case 1:return n=i.sent().code,a[n===e.ErrorCode.SUCCESS?"info":"warn"](T.A_REFUSE_FRIEND_APPLICATION_R,n),[2,{code:n}]}}))}))}))},exports.refuseGroupApplication=function(t,n,r,a){return i(this,void 0,void 0,(function(){var s=this;return o(this,(function(_){return[2,new e.ValidatorManage(T.A_REFUSE_GROUP_APPLICATION_T,T.A_REFUSE_GROUP_APPLICATION_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("applicantId",n,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_USER_ID,!0).validateParameters("inviterId",r,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_USER_ID).validateParameters("reason",a,e.isLimitedString({maxLength:128}),e.ErrorCode.INVALID_PARAMETER_REASON).ifReady((function(_,d){return i(s,void 0,void 0,(function(){var i,s,u;return o(this,(function(o){switch(o.label){case 0:return i=d.createTraceId(),d.info(T.A_REFUSE_GROUP_APPLICATION_T,JSON.stringify({groupId:t,applicantId:n,reason:a}),i),[4,_.refuseGroupApplication(t,n,null!=r?r:"",null!=a?a:"")];case 1:return s=o.sent().code,u=s===e.ErrorCode.SUCCESS?"info":"warn",d[u](T.A_REFUSE_GROUP_APPLICATION_R,JSON.stringify({code:s}),i),[2,{code:s}]}}))}))}))]}))}))},exports.refuseGroupInvite=function(t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(T.A_REFUSE_GROUP_INVITE_T,T.A_REFUSE_GROUP_INVITE_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("inviterId",n,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_USER_ID,!0).validateParameters("reason",r,e.isLimitedString({maxLength:128}),e.ErrorCode.INVALID_PARAMETER_REASON).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d;return o(this,(function(o){switch(o.label){case 0:return i=_.createTraceId(),_.info(T.A_REFUSE_GROUP_INVITE_T,JSON.stringify({groupId:t,inviterId:n,reason:r}),i),[4,s.refuseGroupInvite(t,n,null!=r?r:"")];case 1:return a=o.sent().code,d=a===e.ErrorCode.SUCCESS?"info":"warn",_[d](T.A_REFUSE_GROUP_INVITE_R,JSON.stringify({code:a}),i),[2,{code:a}]}}))}))}))]}))}))},exports.registerMessageType=function(t,n,r,i,o){return O((function(a,s){return e.assert("messageType",t,e.AssertRules.STRING,!0),e.assert("isPersited",n,e.AssertRules.BOOLEAN,!0),e.assert("isCounted",r,e.AssertRules.BOOLEAN,!0),e.assert("isStatusMessage",o,e.AssertRules.BOOLEAN,!1),e.assert("searchProps",i,e.AssertRules.ARRAY),s.info(e.LogTagId.A_REGTYP_O,JSON.stringify({messageType:t,isCounted:r,isStatusMessage:o,isPersited:n})),a.registerMessageType(t,n,r,i,o),function(e,t,n){return void 0===t&&(t=!0),void 0===n&&(n=!0),function(r){return new J(e,r,t,n)}}(t,n,r)}))},exports.removeAllExpansionForUltraGroupMessage=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_REMOVE_ALL_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_T,T.A_REMOVE_ALL_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_R).validate("message",t,e.AssertRules.OBJECT,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d,u,c,E,l,A;return o(this,(function(o){switch(o.label){case 0:return n=t.conversationType,i=t.targetId,s=t.messageUId,_=t.sentTime,d=t.canIncludeExpansion,u=t.channelId,c=a.createTraceId(),a.info(T.A_REMOVE_ALL_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_T,JSON.stringify({conversationType:n,targetId:i,messageUId:s,channelId:u}),c),E={targetId:i,channelId:u,conversationType:n,sendTime:_,canIncludeExpansion:d,messageUId:s,removeAll:!0},[4,r.expandUltraMessage(E)];case 1:return l=o.sent().code,A=l===e.ErrorCode.SUCCESS?"info":"warn",a[A](T.A_REMOVE_ALL_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_R,JSON.stringify({code:l}),c),l===e.ErrorCode.SUCCESS?[2,{code:e.ErrorCode.SUCCESS}]:[2,{code:l,msg:U[l]}]}}))}))}))]}))}))},exports.removeChatRoomEntries=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_REMOVE_CHATROOM_ENTRIES_T,T.A_REMOVE_CHATROOM_ENTRIES_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("entries",null==n?void 0:n.entries,(function(t){return!!e.validate("entry.key",t,e.AssertRules.ARRAY,!0)&&n.entries.every((function(t){return e.validate("key",t,e.AssertRules.STRING,!0)}))}),!0).validate("notificationExtra",null==n?void 0:n.notificationExtra,e.isLimitedString({maxLength:B})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u,c;return o(this,(function(o){switch(o.label){case 0:return r=n.entries.map((function(e){return{key:e}})),i=s.createTraceId(),s.info(T.A_REMOVE_CHATROOM_ENTRIES_T,JSON.stringify({chatroomId:t,notificationExtra:n.notificationExtra,entries:r}),i),[4,a.removeChatroomEntries(t,{entries:r,notificationExtra:n.notificationExtra})];case 1:return _=o.sent(),d=_.code,u=_.data,c=d===e.ErrorCode.SUCCESS?"info":"warn",s[c](T.A_REMOVE_CHATROOM_ENTRIES_R,JSON.stringify({code:d}),i),d!==e.ErrorCode.SUCCESS?[2,{code:d,msg:U[d],data:u}]:[2,{code:d}]}}))}))}))]}))}))},exports.removeChatRoomEntry=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_REMOVE_CHATROOM_ENTRY_T,T.A_REMOVE_CHATROOM_ENTRY_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("key",null==n?void 0:n.key,e.AssertRules.STRING,!0).validate("isSendNotification",null==n?void 0:n.isSendNotification,e.AssertRules.BOOLEAN).validate("notificationExtra",null==n?void 0:n.notificationExtra,e.isLimitedString({maxLength:B})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_REMOVE_CHATROOM_ENTRY_T,JSON.stringify({chatroomId:t,key:n.key,isSendNotification:n.isSendNotification,notificationExtra:n.notificationExtra}),r),[4,a.removeChatroomEntry(t,n)];case 1:return i=o.sent(),_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_REMOVE_CHATROOM_ENTRY_R,JSON.stringify({code:i}),r),i!==e.ErrorCode.SUCCESS?[2,{code:i,msg:U[i]}]:[2,{code:i}]}}))}))}))]}))}))},exports.removeConversation=V,exports.removeConversationsFromTag=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_REMOVE_CONVERSATIONS_FROM_TAG_T,T.A_REMOVE_CONVERSATIONS_FROM_TAG_R).validate("tagId",t,e.AssertRules.STRING,!0).validate("conversations",n,(function(t){return!(!e.validate("conversations",t,e.AssertRules.ARRAY,!0)||t.length<=0)&&!t.some((function(t){return!e.validate("conversation",t,e.AssertRules.CONVERSATION,!0)}))}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_REMOVE_CONVERSATIONS_FROM_TAG_T,JSON.stringify({conversations:n.map((function(e){return{conversationType:e.conversationType,targetId:e.targetId}})),tagId:t}),r),[4,a.removeTagForConversations(t,n)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_REMOVE_CONVERSATIONS_FROM_TAG_R,JSON.stringify({code:i}),r),i===e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,msg:U[i]}]}}))}))}))]}))}))},exports.removeEventListener=function(e,t,n){A.off(e,t,n)},exports.removeEventListeners=function(e){A.removeAll(e)},exports.removeExpansionForUltraGroupMessage=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_REMOVE_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_T,T.A_REMOVE_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_R).validate("expansion",t,e.AssertRules.ARRAY,!0).validate("message",n,e.AssertRules.OBJECT,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u,c,E,l,A,R;return o(this,(function(o){switch(o.label){case 0:return r=n.conversationType,i=n.targetId,_=n.messageUId,d=n.sentTime,u=n.canIncludeExpansion,c=n.channelId,E=s.createTraceId(),s.info(T.A_REMOVE_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_T,JSON.stringify({conversationType:r,targetId:i,messageUId:_,channelId:c}),E),l={targetId:i,channelId:c,conversationType:r,sendTime:d,canIncludeExpansion:u,messageUId:_,keys:t},[4,a.expandUltraMessage(l)];case 1:return A=o.sent().code,R=A===e.ErrorCode.SUCCESS?"info":"warn",s[R](T.A_REMOVE_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_R,JSON.stringify({code:A}),E),A===e.ErrorCode.SUCCESS?[2,{code:e.ErrorCode.SUCCESS}]:[2,{code:A,msg:U[A]}]}}))}))}))]}))}))},exports.removeFromBlacklist=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_REMOVE_FROM_BLACKLIST_T,T.A_REMOVE_FROM_BLACKLIST_R).validate("userId",t,e.AssertRules.STRING,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_REMOVE_FROM_BLACKLIST_T,JSON.stringify({userId:t}),n),[4,r.removeFromBlacklist(t)];case 1:return i=o.sent(),s=i===e.ErrorCode.SUCCESS?"info":"warn",a[s](T.A_REMOVE_FROM_BLACKLIST_R,JSON.stringify({code:i}),n),[2,{code:i}]}}))}))}))]}))}))},exports.removeGroupFollows=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_REMOVE_GROUP_FOLLOWS_T,T.A_REMOVE_GROUP_FOLLOWS_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("userIds",n,e.isLimitedArray({minLength:1,maxLength:100,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_USERIDLIST,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_REMOVE_GROUP_FOLLOWS_T,JSON.stringify({groupId:t,userIds:n}),r),[4,a.removeGroupFollows(t,n)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_REMOVE_GROUP_FOLLOWS_R,JSON.stringify({code:i}),r),[2,{code:i}]}}))}))}))]}))}))},exports.removeGroupManagers=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_REMOVE_GROUP_MANAGERS_T,T.A_REMOVE_GROUP_MANAGERS_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("userIds",n,e.isLimitedArray({minLength:1,maxLength:10,itemValidator:e.isValidTargetId}),e.ErrorCode.INVALID_PARAMETER_USERIDLIST,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_REMOVE_GROUP_MANAGERS_T,JSON.stringify({groupId:t,userIds:n}),r),[4,a.setGroupManagers(2,t,n)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_REMOVE_GROUP_MANAGERS_R,JSON.stringify({code:i}),r),[2,{code:i}]}}))}))}))]}))}))},exports.removeMessageExpansionForKey=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_REMOVE_MESSAGE_EXPANSION_FOR_KEY_T,T.A_REMOVE_MESSAGE_EXPANSION_FOR_KEY_R).validate("keys",t,e.AssertRules.ARRAY,!0).validate("message",n,e.AssertRules.OBJECT,!0).validate("conversationType",null==n?void 0:n.conversationType,e.AssertRules.NUMBER,!0).validate("messageType",null==n?void 0:n.messageType,e.AssertRules.STRING).validate("targetId",null==n?void 0:n.targetId,e.AssertRules.STRING,!0).validate("channelId",null==n?void 0:n.channelId,e.AssertRules.CHANNEL_ID).validate("messageUId",null==n?void 0:n.messageUId,e.AssertRules.STRING,!0).validate("canIncludeExpansion",null==n?void 0:n.canIncludeExpansion,e.AssertRules.BOOLEAN,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u,c,E,l,A;return o(this,(function(o){switch(o.label){case 0:return r=n.conversationType,i=n.targetId,_=n.messageUId,d=n.canIncludeExpansion,u=n.channelId,c=n.messageType,E=s.createTraceId(),s.info(T.A_REMOVE_MESSAGE_EXPANSION_FOR_KEY_T,JSON.stringify({targetId:i,channelId:u,conversationType:r,messageUId:_,canIncludeExpansion:d}),E),[4,a.sendExpansionMessage({conversationType:r,messageType:c,targetId:i,messageUId:_,canIncludeExpansion:d,keys:t,channelId:u})];case 1:return l=o.sent().code,A=l===e.ErrorCode.SUCCESS?"info":"warn",s[A](T.A_REMOVE_MESSAGE_EXPANSION_FOR_KEY_R,JSON.stringify({code:l}),E),l!==e.ErrorCode.SUCCESS?[2,{code:l,msg:U[l]}]:[2,{code:l}]}}))}))}))]}))}))},exports.removeMonitor=function(e){L.delete(e)},exports.removeNotificationQuietHoursSetting=function(){return C().next((function(e){return e.removeNotificationQuietHoursSetting()}))},exports.removeRealtimeConversations=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_REMOVE_REALTIME_CONVERSATIONS_T,T.A_REMOVE_REALTIME_CONVERSATIONS_R).validate("conversationList",t,e.isLimitedArray({minLength:1,maxLength:20,itemValidator:function(t){return e.isValidConversation(t)}}),!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_REMOVE_REALTIME_CONVERSATIONS_T,JSON.stringify(t),n),[4,r.removeRealtimeConversations(t)];case 1:return i=o.sent(),s=i.code,_=i.data,a[e.ErrorCode.SUCCESS===s?"info":"warn"](T.A_REMOVE_REALTIME_CONVERSATIONS_R,"code: ".concat(s),n),[2,{code:s,data:_}]}}))}))}))]}))}))},exports.removeTag=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_REMOVE_TAG_T,T.A_REMOVE_TAG_R).validate("tagId",t,e.AssertRules.STRING,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_REMOVE_TAG_T,JSON.stringify({tagId:t}),n),[4,r.removeTag(t)];case 1:return i=o.sent().code,s=i===e.ErrorCode.SUCCESS?"info":"warn",a[s](T.A_REMOVE_TAG_R,JSON.stringify({code:i}),n),i===e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,msg:U[i]}]}}))}))}))]}))}))},exports.removeTagFromConversations=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_REMOVE_TAG_FROM_CONVERSATIONS_T,T.A_REMOVE_TAG_FROM_CONVERSATIONS_R).validate("tagId",t,e.AssertRules.STRING,!0).validate("conversations",n,(function(t){return!(!e.validate("conversations",t,e.AssertRules.ARRAY,!0)||t.length<=0)&&!t.some((function(t){return!e.validate("conversation",t,e.AssertRules.CONVERSATION,!0)}))}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_REMOVE_TAG_FROM_CONVERSATIONS_T,JSON.stringify({conversations:n.map((function(e){return{conversationType:e.conversationType,targetId:e.targetId}})),tagId:t}),r),[4,a.removeTagForConversations(t,n)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_REMOVE_TAG_FROM_CONVERSATIONS_R,JSON.stringify({code:i}),r),i===e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,msg:U[i]}]}}))}))}))]}))}))},exports.removeTagsFromConversation=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_REMOVE_TAGS_FROM_CONVERSATION_T,T.A_REMOVE_TAGS_FROM_CONVERSATION_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("tagIds",n,(function(t){return!!e.validate("tagIds",t,e.AssertRules.ARRAY,!0)&&!t.some((function(t){return!e.isString(t)}))}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_REMOVE_TAGS_FROM_CONVERSATION_T,JSON.stringify({tagIds:n,targetId:t.targetId,conversationType:t.conversationType}),r),[4,a.removeTagsForConversation(t,n)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_REMOVE_TAGS_FROM_CONVERSATION_R,JSON.stringify({code:i}),r),i===e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,msg:U[i]}]}}))}))}))]}))}))},exports.requestStreamMessageContent=function(e){return C().next((function(t){return t.sse.requestStreamMessageContent(e)}))},exports.saveTextMessageDraft=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_SAVE_CONVERSATION_MESSAGE_DRAFT_T,T.A_SAVE_CONVERSATION_MESSAGE_DRAFT_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("draft",n,e.AssertRules.STRING,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_SAVE_CONVERSATION_MESSAGE_DRAFT_T,JSON.stringify({conversationType:t.conversationType,targetId:t.targetId,channelId:t.channelId,draft:n}),r),[4,a.saveConversationMessageDraft(t.conversationType,t.targetId,n,t.channelId)];case 1:return i=o.sent(),_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_SAVE_CONVERSATION_MESSAGE_DRAFT_R,JSON.stringify({code:i}),r),i===e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,msg:U[i]}]}}))}))}))]}))}))},exports.searchConversationByContent=function(t,n,r,a){return i(this,void 0,void 0,(function(){return o(this,(function(i){return u("RongIMLib.searchConversationByContent","RongIMLib.ElectronExtension.searchConversationByContent"),[2,Ve(t,null!=r?r:[e.MessageType.TextMessage,e.MessageType.FILE],a,n)]}))}))},exports.searchFriendsInfo=function(t){var n=this;return new e.ValidatorManage(T.A_SEARCH_FRIENDS_INFO_T,T.A_SEARCH_FRIENDS_INFO_R).validateParameters("name",t,e.isLimitedString({isTrim:!0,minLength:1,maxLength:64}),e.ErrorCode.INVALID_PARAMETER_NICK_NAME,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return a.info(T.A_SEARCH_FRIENDS_INFO_T,JSON.stringify({name:t})),[4,r.searchFriendsInfo(t)];case 1:return n=o.sent(),i=n.code,s=n.data,a[i===e.ErrorCode.SUCCESS?"info":"warn"](T.A_SEARCH_FRIENDS_INFO_R,JSON.stringify({code:i,data:s})),[2,{code:i,data:s}]}}))}))}))},exports.searchGroupMembers=function(t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(T.A_SEARCH_GROUP_MEMBERS_T,T.A_SEARCH_GROUP_MEMBERS_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("name",n,e.isLimitedString({isTrim:!0,minLength:1,maxLength:64}),e.ErrorCode.INVALID_PARAMETER_NICK_NAME,!0).validateParameters("option",r,e.AssertRules.OBJECT,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION,!0).validateParameters("option.count",r.count,e.isInteger({min:1,max:200}),e.ErrorCode.INVALID_PARAMETER_COUNT,!0).validateParameters("option.pageToken",null==r?void 0:r.pageToken,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).validateParameters("option.order",null==r?void 0:r.order,e.AssertRules.BOOLEAN,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d,u,c;return o(this,(function(o){switch(o.label){case 0:return i=_.createTraceId(),_.info(T.A_SEARCH_GROUP_MEMBERS_T,JSON.stringify({groupId:t,name:n,option:r,formatOption:ke(r)}),i),[4,s.searchGroupMembers(t,n,ke(r))];case 1:return a=o.sent(),d=a.code,u=a.data,c=d===e.ErrorCode.SUCCESS?"info":"warn",_[c](T.A_SEARCH_GROUP_MEMBERS_R,JSON.stringify({code:d,data:u}),i),d!==e.ErrorCode.SUCCESS?[2,{code:d}]:[2,{code:d,data:u}]}}))}))}))]}))}))},exports.searchJoinedGroups=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_SEARCH_JOINED_GROUPS_T,T.A_SEARCH_JOINED_GROUPS_R).validateParameters("groupName",t,e.isLimitedString({isTrim:!0,minLength:1,maxLength:64}),e.ErrorCode.INVALID_PARAMETER_GROUP_NAME,!0).validateParameters("option",n,e.AssertRules.OBJECT,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION,!0).validateParameters("option.count",n.count,e.isInteger({min:1,max:200}),e.ErrorCode.INVALID_PARAMETER_COUNT,!0).validateParameters("option.pageToken",null==n?void 0:n.pageToken,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).validateParameters("option.order",null==n?void 0:n.order,e.AssertRules.BOOLEAN,e.ErrorCode.INVALID_PARAMETER_PAGING_QUERY_OPTION).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_SEARCH_JOINED_GROUPS_T,JSON.stringify({groupName:t,option:n,formatOption:ke(n)}),r),[4,a.searchJoinedGroups(t,ke(n))];case 1:return i=o.sent(),_=i.code,d=i.data,u=_===e.ErrorCode.SUCCESS?"info":"warn",s[u](T.A_SEARCH_JOINED_GROUPS_R,JSON.stringify({code:_}),r),_!==e.ErrorCode.SUCCESS?[2,{code:_}]:[2,{code:_,data:d}]}}))}))}))]}))}))},exports.searchMessages=function(t,n,r,a){return i(this,void 0,void 0,(function(){var s=this;return o(this,(function(d){return u("RongIMLib.searchMessages","RongIMLib.ElectronExtension.searchMessages"),e.usingCppEngine()?[2,new e.ValidatorManage("A-search_messages-T","A-search_messages-R").validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("keyword",n,e.AssertRules.STRING,!0).validate("timestamp",r,e.AssertRules.NUMBER).validate("count",a,e.AssertRules.NUMBER).ifReady((function(d,u){return i(s,void 0,void 0,(function(){var i,s,c,E;return o(this,(function(o){switch(o.label){case 0:return[4,d.searchMessageByContent(t.conversationType,t.targetId,n,r,a,1,t.channelId)];case 1:return i=o.sent(),s=i.code,c=i.data,s===e.ErrorCode.SUCCESS?(E=[],(null==c?void 0:c.messages)&&c.messages.length&&c.messages.forEach((function(e){return E.push(_(e))})),[2,{code:s,data:{messages:E,count:null==c?void 0:c.count}}]):(u.warn(e.LogTagId.O,"searchMessages ->code:".concat(s,",targetId:").concat(t.targetId)),[2,{code:s,msg:U[s]}])}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},exports.searchUserProfileByUniqueId=function(t){var n=this;return new e.ValidatorManage(T.A_SEARCH_USER_PROFILE_BY_UNIQUE_ID_T,T.A_SEARCH_USER_PROFILE_BY_UNIQUE_ID_R).validate("uniqueId",t,e.AssertRules.STRING,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_SEARCH_USER_PROFILE_BY_UNIQUE_ID_T,t,n),[4,r.searchUserProfileByUniqueId(t)];case 1:return i=o.sent(),s=i.code,_=i.data,d=s===e.ErrorCode.SUCCESS?"info":"warn",a[d](T.A_SEARCH_USER_PROFILE_BY_UNIQUE_ID_R,JSON.stringify({code:s,data:_}),n),[2,{code:s,data:_}]}}))}))}))},exports.sendFileMessage=he,exports.sendGIFMessage=Ge,exports.sendHQVoiceMessage=Pe,exports.sendImageMessage=Me,exports.sendMessage=me,exports.sendReadReceiptMessage=function(t,n,r,a){return i(this,void 0,void 0,(function(){var s=this;return o(this,(function(_){return[2,new e.ValidatorManage(e.LogTagId.A_SEND_MSG_T,e.LogTagId.A_SEND_MSG_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("messageUId",n,e.AssertRules.STRING,!0).validate("timestamp",r,e.AssertRules.NUMBER,!0).validate("channelId",a,e.AssertRules.CHANNEL_ID).ifReady((function(_){return i(s,void 0,void 0,(function(){var i,s,_,d,u;return o(this,(function(o){switch(o.label){case 0:return i={targetId:t,conversationType:e.ConversationType.PRIVATE,channelId:a},s=new J("RC:ReadNtf",{messageUId:n,lastMessageSendTime:r,type:1}),[4,me(i,s)];case 1:return _=o.sent(),d=_.code,u=_.msg,0===d?[2,{code:d}]:[2,{code:d,msg:u}]}}))}))}))]}))}))},exports.sendReadReceiptMessageV4=function(t,n,r){return void 0===r&&(r=""),i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(e.LogTagId.A_SEND_MESSAGE_READ_RECEIPT_V4_T,e.LogTagId.A_SEND_MESSAGE_READ_RECEIPT_V4_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,(function(t){return t===e.ConversationType.GROUP||t===e.ConversationType.PRIVATE}),!0,"It should be ConversationType.GROUP or ConversationType.PRIVATE").validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("startMsgUId",r,e.AssertRules.ONLY_STRING,!0).validate("endMsgUId",n,e.AssertRules.STRING,!0).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:return i=_.createTraceId(),a="conversationType:".concat(t.conversationType,",targetId:").concat(t.targetId,",\n      channelId: ").concat(t.channelId,", startMsgUId:").concat(r,", endMsgUId: ").concat(n),_.info(e.LogTagId.A_SEND_MESSAGE_READ_RECEIPT_V4_T,a,i),3!==(null==(d=s.getNaviInfoFromCache())?void 0:d.grpRRVer)?[2,{code:e.ErrorCode.READ_RECEIPT_V4_NOT_SUPPORT}]:[4,s.sendReadReceiptMessageV4(t,r,n)];case 1:return u=o.sent(),c=u.code,E=u.msg,_[c===e.ErrorCode.SUCCESS?"info":"warn"](e.LogTagId.A_SEND_MESSAGE_READ_RECEIPT_V4_R,"code: ".concat(c,", msg: ").concat(E),i),[2,{code:c,msg:E}]}}))}))}))]}))}))},exports.sendReadReceiptRequest=function(t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(e.LogTagId.A_SEND_MSG_T,e.LogTagId.A_SEND_MSG_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("messageUId",n,e.AssertRules.STRING,!0).validate("channelId",r,e.AssertRules.CHANNEL_ID).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,s,d,u,c;return o(this,(function(o){switch(o.label){case 0:return i="messageUId:".concat(n,",targetId:").concat(t),_.debug(e.LogTagId.A_SEND_MSG_T,"send read receipt message ->".concat(i)),a=new J("RC:RRReqMsg",{messageUId:n}),s={targetId:t,conversationType:e.ConversationType.GROUP,channelId:r},[4,me(s,a)];case 1:return d=o.sent(),u=d.code,c=d.msg,0===u?[2,{code:u}]:[2,{code:u,msg:c}]}}))}))}))]}))}))},exports.sendReadReceiptResponse=function(e,t,n){return i(this,void 0,void 0,(function(){return o(this,(function(e){throw new Error("This method is deprecated, please use method sendReadReceiptResponseV2.")}))}))},exports.sendReadReceiptResponseV2=function(t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(e.LogTagId.A_SEND_MSG_T,e.LogTagId.A_SEND_MSG_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("channelId",r,e.AssertRules.CHANNEL_ID).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d,u,c;return o(this,(function(o){switch(o.label){case 0:return i={targetId:t,conversationType:e.ConversationType.GROUP,channelId:r},d=s.getNaviInfoFromCache(),1!==(null==d?void 0:d.grpRRVer)?[3,2]:[4,Le(s,_,i,n)];case 1:return a=o.sent(),[3,4];case 2:return!e.validate("messageList",n,e.AssertRules.OBJECT,!1)||n&&Object.keys(n).some((function(t){return!e.validate(t,n[t],e.AssertRules.ARRAY)}))?[2,{code:e.ErrorCode.MSG_LIMIT_ERROR}]:(u=new J("RC:RRRspMsg",{receiptMessageDic:n}),[4,me(i,u)]);case 3:a=o.sent(),o.label=4;case 4:return(c=a.code)===e.ErrorCode.SUCCESS?[2,{code:c}]:[2,{code:c,msg:U[c]}]}}))}))}))]}))}))},exports.sendReadReceiptResponseV5=function(e,t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,C().next((function(r){return i(n,void 0,void 0,(function(){return o(this,(function(n){return[2,r.sendReadReceiptResponseV5(e,t)]}))}))}))]}))}))},exports.sendSightMessage=ye,exports.sendSyncReadStatusMessage=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(e.LogTagId.A_SYNC_READ_STATUS_T,e.LogTagId.A_SYNC_READ_STATUS_R).validate("timestamp",n,e.AssertRules.NUMBER,!0).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,(function(t){return[e.ConversationType.GROUP,e.ConversationType.PRIVATE,e.ConversationType.APP_PUBLIC_SERVICE,e.ConversationType.PUBLIC_SERVICE,e.ConversationType.SYSTEM].includes(t)}),!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:return r="conversationType:".concat(t.conversationType,",targetId:").concat(t.targetId,",\n      channelId: ").concat(t.channelId,", timestamp:").concat(n),i=s.createTraceId(),s.info(e.LogTagId.A_SYNC_READ_STATUS_T,r,i),1!==(null==(_=a.getNaviInfoFromCache())?void 0:_.sesDriven)?[3,2]:[4,a.updateConversationReadTime(t,n)];case 1:return d=o.sent(),[3,4];case 2:return u=new J(e.MessageType.SYNC_READ_STATUS,{lastMessageSendTime:n}),[4,me(t,u)];case 3:d=o.sent(),o.label=4;case 4:return c=d.code,E=d.msg,s[c===e.ErrorCode.SUCCESS?"info":"warn"](e.LogTagId.A_SYNC_READ_STATUS_R,"code: ".concat(c,", msg: ").concat(E),i),[2,{code:c,msg:E}]}}))}))}))]}))}))},exports.sendTextMessage=function(e,t,n){var r=new ie(t);return me(e,r,n)},exports.sendTypingStatusMessage=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(e.LogTagId.A_SEND_MSG_T,e.LogTagId.A_SEND_MSG_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("messageType",n,e.AssertRules.STRING,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:return r={messageType:"RC:TypSts",content:{typingContentType:n},isStatusMessage:!0,channelId:t.channelId},i=s.createTraceId(),s.info(e.LogTagId.A_SEND_MSG_T,JSON.stringify({messageType:r.messageType,conversationType:t.conversationType,targetId:t.targetId,channelId:t.channelId}),i),[4,a.sendMessage(t.conversationType,t.targetId,r,void 0,i)];case 1:return d=o.sent(),u=d.code,c=d.data,E=u===e.ErrorCode.SUCCESS?"info":"warn",s[E](e.LogTagId.A_SEND_MSG_R,JSON.stringify({code:u,messageUId:c?c.messageUId:"",messageId:c?c.messageId:""}),i),u===e.ErrorCode.SUCCESS?[2,{code:u,data:_(c)}]:[2,{code:u,msg:U[u]}]}}))}))}))]}))}))},exports.sendUltraGroupTypingStatus=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_SEND_ULTRA_GROUP_TYPING_STATUS_T,T.A_SEND_ULTRA_GROUP_TYPING_STATUS_R).validate("conversation",t,e.AssertRules.OBJECT,!0).validate("conversationType",null==t?void 0:t.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_SEND_ULTRA_GROUP_TYPING_STATUS_T,JSON.stringify({targetId:t.targetId,channelId:t.channelId}),n),[4,r.sendUltraGroupTypingStatus(t)];case 1:return i=o.sent().code,s=i===e.ErrorCode.SUCCESS?"info":"warn",a[s](T.A_SEND_ULTRA_GROUP_TYPING_STATUS_R,JSON.stringify({code:i}),n),i===e.ErrorCode.SUCCESS?[2,{code:e.ErrorCode.SUCCESS}]:[2,{code:i,msg:U[i]}]}}))}))}))]}))}))},exports.setChatRoomEntries=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_SET_CHATROOM_ENTRIES_T,T.A_SET_CHATROOM_ENTRIES_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("entries",null==n?void 0:n.entries,(function(t){return!!e.validate("entry.key",t,e.AssertRules.ARRAY,!0)&&n.entries.every((function(t){return e.validate("entry.key",t.key,e.AssertRules.STRING,!0)&&e.validate("entry.value",t.value,e.AssertRules.STRING,!0)}))}),!0).validate("isAutoDelete",null==n?void 0:n.isAutoDelete,e.AssertRules.BOOLEAN).validate("isForce",null==n?void 0:n.isForce,e.AssertRules.BOOLEAN).validate("notificationExtra",null==n?void 0:n.notificationExtra,e.isLimitedString({maxLength:B})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u;return o(this,(function(o){switch(o.label){case 0:return n.entries.length>10?[2,m.CHATROOM_KV_STORE_OUT_LIMIT]:(r=s.createTraceId(),s.info(T.A_SET_CHATROOM_ENTRIES_T,JSON.stringify({chatroomId:t,entries:n.entries.map((function(e){return{key:e.key,value:e.value}})),isAutoDelete:n.isAutoDelete,notificationExtra:n.notificationExtra,isForce:n.isForce}),r),[4,a.setChatroomEntries(t,n)]);case 1:return i=o.sent(),_=i.code,d=i.data,u=_===e.ErrorCode.SUCCESS?"info":"warn",s[u](T.A_SET_CHATROOM_ENTRIES_R,JSON.stringify({code:_}),r),_!==e.ErrorCode.SUCCESS?[2,{code:_,msg:U[_],data:d}]:[2,{code:_}]}}))}))}))]}))}))},exports.setChatRoomEntry=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_SET_CHATROOM_ENTRY_T,T.A_SET_CHATROOM_ENTRY_R).validate("targetId",t,e.AssertRules.STRING,!0).validate("key",null==n?void 0:n.key,e.AssertRules.STRING,!0).validate("value",null==n?void 0:n.value,e.AssertRules.STRING,!0).validate("isAutoDelete",null==n?void 0:n.isAutoDelete,e.AssertRules.BOOLEAN).validate("isSendNotification",null==n?void 0:n.isSendNotification,e.AssertRules.BOOLEAN).validate("notificationExtra",null==n?void 0:n.notificationExtra,e.isLimitedString({maxLength:B})).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_SET_CHATROOM_ENTRY_T,JSON.stringify({chatroomId:t,key:n.key,value:n.value,isSendNotification:n.isSendNotification,isAutoDelete:n.isAutoDelete,notificationExtra:n.notificationExtra}),r),[4,a.setChatroomEntry(t,n)];case 1:return i=o.sent(),_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_SET_CHATROOM_ENTRY_R,JSON.stringify({code:i}),r),i!==e.ErrorCode.SUCCESS?[2,{code:i,msg:U[i]}]:[2,{code:i}]}}))}))}))]}))}))},exports.setConversationNotificationLevel=x,exports.setConversationNotificationStatus=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,C().next((function(a){return i(r,void 0,void 0,(function(){var r;return o(this,(function(i){return n!==e.NotificationStatus.CLOSE&&n!==e.NotificationStatus.OPEN?[2,e.fail(e.ErrorCode.INVALID_PARAMETER_NOTIFICATION_LEVEL)]:(r=e.trans2NotificationLevel(n),[2,a.batchSetConversationNotificationLevel([t],r)])}))}))}))]}))}))},exports.setConversationToTop=F,exports.setConversationToTopInTag=function(t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(T.A_GET_UNREAD_COUNT_BY_TAG_T,T.A_GET_UNREAD_COUNT_BY_TAG_R).validate("tagId",t,e.AssertRules.STRING,!0).validate("isTop",r,e.AssertRules.BOOLEAN,!0).validate("conversationType",null==n?void 0:n.conversationType,e.AssertRules.CONVERSATION_TYPE,!0).validate("targetId",null==n?void 0:n.targetId,e.AssertRules.STRING,!0).validate("channelId",null==n?void 0:n.channelId,e.AssertRules.CHANNEL_ID).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d;return o(this,(function(o){switch(o.label){case 0:return i=_.createTraceId(),_.info(T.A_GET_UNREAD_COUNT_BY_TAG_T,JSON.stringify({tagId:t,targetId:n.targetId,conversationType:n.conversationType,isTop:r}),i),[4,s.setConversationStatusInTag(t,n,{isTop:r})];case 1:return a=o.sent().code,d=a===e.ErrorCode.SUCCESS?"info":"warn",_[d](T.A_SET_CONVERSATION_STATUS_IN_TAG_R,JSON.stringify({code:a}),i),a===e.ErrorCode.SUCCESS?[2,{code:a}]:[2,{code:a,msg:U[a]}]}}))}))}))]}))}))},exports.setConversationTypeNotificationLevel=function(e,t){return i(this,void 0,void 0,(function(){return o(this,(function(n){return[2,C().next((function(n){return n.setConversationTypeNotificationLevel(e,t)}))]}))}))},exports.setFriendAddPermission=function(t){var n=this;return new e.ValidatorManage(T.A_SET_ADD_FRIEND_PERMISSION_T,T.A_SET_ADD_FRIEND_PERMISSION_R).validateParameters("permission",t,(function(t){return e.isValidEnum({value:t,type:e.FriendAddPermission})}),e.ErrorCode.INVALID_PARAMETER_FRIEND_ADD_PERMISSION,!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return a.info(T.A_SET_ADD_FRIEND_PERMISSION_T,JSON.stringify(t)),[4,r.setFriendAddPermission(t)];case 1:return n=o.sent(),i=n.code,s=n.data,a[i===e.ErrorCode.SUCCESS?"info":"warn"](T.A_SET_ADD_FRIEND_PERMISSION_R,JSON.stringify({code:i,data:s})),[2,{code:i}]}}))}))}))},exports.setFriendInfo=function(t,n,r){var a=this;return void 0===n&&(n=""),void 0===r&&(r={}),new e.ValidatorManage(T.A_SET_FRIEND_INFO_T,T.A_SET_FRIEND_INFO_R).validateParameters("userId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_USER_ID,!0).validateParameters("remark",n,e.isLimitedString({isTrim:!0,maxLength:64}),e.ErrorCode.INVALID_PARAMETER_REMARK).validateParameters("extProfile",r,e.AssertRules.OBJECT,e.ErrorCode.INVALID_PARAMETER_FRIEND_INFO_EXT_FIELDS).validateParameters("extProfile",r,(function(t){for(var n in t)if(!e.isString(t[n]))return!1;return!0}),e.ErrorCode.INVALID_PARAMETER_FRIEND_INFO_EXT_FIELDS).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d;return o(this,(function(o){switch(o.label){case 0:return _.info(T.A_SET_FRIEND_INFO_T,JSON.stringify({userId:t,remark:n,extProfile:r})),[4,s.setFriendInfo(t,n,r)];case 1:return i=o.sent(),a=i.code,d=i.data,_[a===e.ErrorCode.SUCCESS?"info":"warn"](T.A_SET_FRIEND_INFO_R,a),[2,{code:a,data:d}]}}))}))}))},exports.setGroupMemberInfo=function(t,n,r,a){return i(this,void 0,void 0,(function(){var s=this;return o(this,(function(_){return[2,new e.ValidatorManage(T.A_SET_GROUP_MEMBER_INFO_T,T.A_SET_GROUP_MEMBER_INFO_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("userId",n,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_USER_ID,!0).validateParameters("nickname",r,e.isLimitedString({isTrim:!0,maxLength:64}),e.ErrorCode.INVALID_PARAMETER_NICK_NAME,!0).validateParameters("extra",a,e.isLimitedString({maxLength:128}),e.ErrorCode.INVALID_PARAMETER_EXTRA).ifReady((function(_,d){return i(s,void 0,void 0,(function(){var i,s,u,c,E;return o(this,(function(o){switch(o.label){case 0:return i=d.createTraceId(),d.info(T.A_SET_GROUP_MEMBER_INFO_T,JSON.stringify({groupId:t,userId:n,nickname:r,extra:a}),i),[4,_.setGroupMemberInfo(t,n,r,a)];case 1:return s=o.sent(),u=s.code,c=s.data,E=u===e.ErrorCode.SUCCESS?"info":"warn",d[E](T.A_SET_GROUP_MEMBER_INFO_R,JSON.stringify({code:u}),i),u===e.ErrorCode.SUCCESS?[2,{code:u}]:[2,{code:u,data:c}]}}))}))}))]}))}))},exports.setGroupRemark=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_SET_GROUP_REMARK_T,T.A_SET_GROUP_REMARK_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("remark",n,e.isLimitedString({isTrim:!0,maxLength:64}),e.ErrorCode.INVALID_PARAMETER_REMARK,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_SET_GROUP_REMARK_T,JSON.stringify({groupId:t,remark:n}),r),[4,a.setGroupRemark(t,n)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_SET_GROUP_REMARK_R,JSON.stringify({code:i}),r),[2,{code:i}]}}))}))}))]}))}))},exports.setMessageReceivedStatus=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return u("RongIMLib.setMessageReceivedStatus","RongIMLib.ElectronExtension.setMessageReceivedStatus"),e.usingCppEngine()?[2,new e.ValidatorManage(T.A_SET_MESSAGE_RECEIVED_STATUS_T,T.A_SET_MESSAGE_RECEIVED_STATUS_R).validate("messageId",t,e.AssertRules.NUMBER,!0).validate("receivedStatus",n,e.AssertRules.NUMBER,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_SET_MESSAGE_RECEIVED_STATUS_T,JSON.stringify({messageId:t,receivedStatus:n}),r),[4,a.setMessageReceivedStatus(t,n)];case 1:return i=o.sent(),_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_SET_MESSAGE_RECEIVED_STATUS_R,JSON.stringify({code:i}),r),i===e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,msg:U[i]}]}}))}))}))]:[2,{code:e.ErrorCode.NOT_SUPPORT,msg:U[e.ErrorCode.NOT_SUPPORT]}]}))}))},exports.setNotificationQuietHoursWithSetting=function(e){return C().next((function(t){return t.setNotificationQuietHoursWithSetting(e)}))},exports.setProxy=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_SET_PROXY_T,T.A_SET_PROXY_R).validate("set.proxy",t,(function(n){return!!(!t||e.validate("proxy.socksHost",t.socksHost,e.AssertRules.STRING,!0)&&e.validate("proxy.socksPort",t.socksPort,e.AssertRules.NUMBER,!0))})).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_SET_PROXY_T,JSON.stringify({socksHost:null==t?void 0:t.socksHost,socksPort:null==t?void 0:t.socksPort}),n),[4,r.setProxy(t)];case 1:return i=o.sent().code,s=i===e.ErrorCode.SUCCESS?"info":"warn",a[s](T.A_SET_PROXY_R,JSON.stringify({code:i}),n),i===e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,msg:U[i]}]}}))}))}))]}))}))},exports.setUltraGroupDefaultNotificationLevel=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return t.channelId=t.channelId||"",[2,new e.ValidatorManage(T.A_SET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_T,T.A_SET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_R).validate("targetId",null==t?void 0:t.targetId,e.AssertRules.STRING,!0).validate("channelId",null==t?void 0:t.channelId,e.AssertRules.CHANNEL_ID).validate("notificationLevel",n,(function(t){return void 0!==e.NotificationLevel[t]}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_SET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_T,JSON.stringify({targetId:t.targetId,channelId:t.channelId,notificationLevel:n}),r),[4,a.setUltraGroupDefaultNotificationLevel(t.targetId,n,t.channelId)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_SET_ULTRA_GROUP_DEFAULT_NOTIFICATION_LEVEL_R,JSON.stringify({code:i}),r),i===e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,msg:U[i]}]}}))}))}))]}))}))},exports.stopUploadTask=function(t){return i(this,void 0,void 0,(function(){return o(this,(function(n){return[2,{code:e.ErrorCode.SUCCESS,data:Oe(t)}]}))}))},exports.subscribeUserStatus=function(t,n,r){return i(this,void 0,void 0,(function(){var a=this;return o(this,(function(s){return[2,new e.ValidatorManage(T.A_SUBSCRIBE_USER_STATUS_T,T.A_SUBSCRIBE_USER_STATUS_R).validate("subscribeUserIds",t,e.isLimitedArray({maxLength:200,minLength:1,itemValidator:e.isValidTargetId}),!0).validate("subscribeType",n,(function(t){return[e.SubscribeType.ONLINE_STATUS,e.SubscribeType.USER_PROFILE].includes(t)}),!0).validate("expiry",r,e.isInteger({min:60,max:2592e3}),!0).ifReady((function(s,_){return i(a,void 0,void 0,(function(){var i,a,d,u,c;return o(this,(function(o){switch(o.label){case 0:return i=_.createTraceId(),_.info(T.A_SUBSCRIBE_USER_STATUS_T,JSON.stringify({userIds:t,subscribeType:n,expiry:r}),i),[4,s.subscribeUser(t,n,r)];case 1:return a=o.sent(),d=a.code,u=a.data,c=d===e.ErrorCode.SUCCESS?"info":"warn",_[c](T.A_SUBSCRIBE_USER_STATUS_R,JSON.stringify({code:d,data:u}),i),[2,{code:d,data:u}]}}))}))}))]}))}))},exports.testProxy=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_TEST_PROXY_T,T.A_TEST_PROXY_R).validate("test.proxy",t,e.AssertRules.OBJECT,!0).validate("test.proxy.socksHost",null==t?void 0:t.socksHost,e.AssertRules.STRING,!0).validate("test.proxy.socksPort",null==t?void 0:t.socksPort,e.AssertRules.NUMBER,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_TEST_PROXY_T,JSON.stringify({socksHost:null==t?void 0:t.socksHost,socksPort:null==t?void 0:t.socksPort}),r),[4,a.testProxy(t,n)];case 1:return i=o.sent(),_=i.status,d=i.data,_===e.ErrorCode.SUCCESS?[2,{code:_,msg:d}]:(s.warn(T.A_TEST_PROXY_R,JSON.stringify({status:_}),r),[2,{code:_,msg:U[_]}])}}))}))}))]}))}))},exports.transferGroupOwner=function(t,n,r,a){return void 0===r&&(r=!1),void 0===a&&(a={removeMuteStatus:!0,removeWhiteList:!0,removeFollow:!0}),i(this,void 0,void 0,(function(){var s=this;return o(this,(function(_){return[2,new e.ValidatorManage(T.A_TRANSFER_GROUP_OWNER_T,T.A_TRANSFER_GROUP_OWNER_R).validateParameters("groupId",t,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("newOwnerId",n,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_USER_ID,!0).ifReady((function(_,d){return i(s,void 0,void 0,(function(){var i,s,u;return o(this,(function(o){switch(o.label){case 0:return i=d.createTraceId(),d.info(T.A_TRANSFER_GROUP_OWNER_T,JSON.stringify({groupId:t,newOwnerId:n,quitGroup:r,config:a}),i),[4,_.transferGroupOwner(t,n,a,r)];case 1:return s=o.sent().code,u=s===e.ErrorCode.SUCCESS?"info":"warn",d[u](T.A_TRANSFER_GROUP_OWNER_R,JSON.stringify({code:s}),i),[2,{code:s}]}}))}))}))]}))}))},exports.unSubscribeUserStatus=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_UNSUBSCRIBE_USER_STATUS_T,T.A_UNSUBSCRIBE_USER_STATUS_R).validate("subscribeUserIds",t,e.isLimitedArray({maxLength:200,minLength:1,itemValidator:e.isValidTargetId}),!0).validate("subscribeType",n,(function(t){return[e.SubscribeType.ONLINE_STATUS,e.SubscribeType.USER_PROFILE].includes(t)}),!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_;return o(this,(function(o){switch(o.label){case 0:return r=s.createTraceId(),s.info(T.A_UNSUBSCRIBE_USER_STATUS_T,JSON.stringify({userIds:t,subscribeType:n}),r),[4,a.unSubscribeUser(t,n)];case 1:return i=o.sent().code,_=i===e.ErrorCode.SUCCESS?"info":"warn",s[_](T.A_UNSUBSCRIBE_USER_STATUS_R,JSON.stringify({code:i}),r),[2,{code:i}]}}))}))}))]}))}))},exports.updateExpansionForUltraGroupMessage=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_UPDATE_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_T,T.A_UPDATE_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_R).validate("expansion",t,e.AssertRules.OBJECT,!0).validate("message",n,e.AssertRules.OBJECT,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u,c,E,l,A,R;return o(this,(function(o){switch(o.label){case 0:return r=n.conversationType,i=n.targetId,_=n.messageUId,d=n.sentTime,u=n.canIncludeExpansion,c=n.channelId,E=s.createTraceId(),s.info(T.A_UPDATE_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_T,JSON.stringify({conversationType:r,targetId:i,messageUId:_,channelId:c}),E),l={targetId:i,channelId:c,conversationType:r,sendTime:d,canIncludeExpansion:u,messageUId:_,expansion:t},[4,a.expandUltraMessage(l)];case 1:return A=o.sent().code,R=A===e.ErrorCode.SUCCESS?"info":"warn",s[R](T.A_UPDATE_EXPANSION_FOR_ULTRA_GROUP_MESSAGE_R,JSON.stringify({code:A}),E),A===e.ErrorCode.SUCCESS?[2,{code:e.ErrorCode.SUCCESS}]:[2,{code:A,msg:U[A]}]}}))}))}))]}))}))},exports.updateGroupInfo=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_UPDATE_GROUP_INFO_T,T.A_UPDATE_GROUP_INFO_R).validateParameters("groupId",t.groupId,e.isValidTargetId,e.ErrorCode.INVALID_PARAMETER_GROUP_ID,!0).validateParameters("groupName",t.groupName,e.isLimitedString({isTrim:!0,maxLength:64}),e.ErrorCode.INVALID_PARAMETER_GROUP_NAME).validateParameters("groupInfo.portraitUri",null==t?void 0:t.portraitUri,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.introduction",null==t?void 0:t.introduction,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.notice",null==t?void 0:t.notice,e.AssertRules.ONLY_STRING,e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.extProfile",null==t?void 0:t.extProfile,e.AssertRules.OBJECT,e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.extProfile",null==t?void 0:t.extProfile,(function(e){for(var t in e)if("string"!=typeof e[t])return!1;return!0}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.joinPermission",null==t?void 0:t.joinPermission,(function(t){return e.isValidEnum({value:t,type:e.GroupJoinPermission})}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.removeMemberPermission",null==t?void 0:t.removeMemberPermission,(function(t){return e.isValidEnum({value:t,type:e.GroupOperationPermission})}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.invitePermission",null==t?void 0:t.invitePermission,(function(t){return e.isValidEnum({value:t,type:e.GroupOperationPermission})}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.inviteHandlePermission",null==t?void 0:t.inviteHandlePermission,(function(t){return e.isValidEnum({value:t,type:e.GroupInviteHandlePermission})}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.groupInfoEditPermission",null==t?void 0:t.groupInfoEditPermission,(function(t){return e.isValidEnum({value:t,type:e.GroupOperationPermission})}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).validateParameters("groupInfo.memberInfoEditPermission",null==t?void 0:t.memberInfoEditPermission,(function(t){return e.isValidEnum({value:t,type:e.GroupMemberInfoEditPermission})}),e.ErrorCode.INVALID_PARAMETER_GROUP_INFO).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d,u,c,l,A,R,S;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_UPDATE_GROUP_INFO_T,JSON.stringify({groupInfo:t}),n),i=t.groupId,s=t.extProfile,_={},d=Object.keys(E),Object.keys(t).forEach((function(e){d.includes(e)&&(_[E[e]]=t[e])})),[4,r.updateGroupInfo(i,_,s)];case 1:return u=o.sent(),c=u.code,l=u.msg,A=void 0===l?"":l,R=u.data,S=c===e.ErrorCode.SUCCESS?"info":"warn",a[S](T.A_UPDATE_GROUP_INFO_R,JSON.stringify({code:c}),n),c!==e.ErrorCode.SUCCESS?[2,{code:c,msg:A,data:R}]:[2,{code:c}]}}))}))}))]}))}))},exports.updateMessageExpansion=function(t,n){return i(this,void 0,void 0,(function(){var r=this;return o(this,(function(a){return[2,new e.ValidatorManage(T.A_UPDATE_MESSAGE_EXPANSION_T,T.A_UPDATE_MESSAGE_EXPANSION_R).validate("expansion",t,e.AssertRules.OBJECT,!0).validate("message",n,e.AssertRules.OBJECT,!0).validate("conversationType",null==n?void 0:n.conversationType,e.AssertRules.NUMBER,!0).validate("targetId",null==n?void 0:n.targetId,e.AssertRules.STRING,!0).validate("channelId",null==n?void 0:n.channelId,e.AssertRules.CHANNEL_ID).validate("messageType",null==n?void 0:n.messageType,e.AssertRules.STRING).validate("messageUId",null==n?void 0:n.messageUId,e.AssertRules.STRING,!0).validate("expansion",null==n?void 0:n.expansion,e.AssertRules.OBJECT).validate("canIncludeExpansion",null==n?void 0:n.canIncludeExpansion,e.AssertRules.BOOLEAN,!0).ifReady((function(a,s){return i(r,void 0,void 0,(function(){var r,i,_,d,u,c,E,l,A,R;return o(this,(function(o){switch(o.label){case 0:return r=n.conversationType,i=n.targetId,_=n.messageUId,d=n.canIncludeExpansion,u=n.expansion,c=n.channelId,E=n.messageType,l=s.createTraceId(),s.info(T.A_UPDATE_MESSAGE_EXPANSION_T,JSON.stringify({targetId:i,channelId:c,conversationType:r,messageUId:_,canIncludeExpansion:d}),l),[4,a.sendExpansionMessage({messageType:E,conversationType:r,targetId:i,messageUId:_,expansion:t,canIncludeExpansion:d,originExpansion:u,channelId:c})];case 1:return A=o.sent().code,R=A===e.ErrorCode.SUCCESS?"info":"warn",s[R](T.A_UPDATE_MESSAGE_EXPANSION_R,JSON.stringify({code:A}),l),A!==e.ErrorCode.SUCCESS?[2,{code:A,msg:U[A]}]:[2,{code:A}]}}))}))}))]}))}))},exports.updateMyUserProfile=function(t){var n=this;return new e.ValidatorManage(T.A_UPDATE_MY_USER_PROFILE_T,T.A_UPDATE_MY_USER_PROFILE_R).validate("profile",t,e.AssertRules.OBJECT,!0).validate("profile",t.name,e.AssertRules.ONLY_STRING).validate("profile",t.portraitUri,e.AssertRules.ONLY_STRING).validate("profile",t.email,e.AssertRules.ONLY_STRING).validate("profile",t.birthday,e.AssertRules.ONLY_STRING).validate("profile",t.gender,e.AssertRules.NUMBER).validate("profile",t.location,e.AssertRules.ONLY_STRING).validate("profile",t.role,e.AssertRules.NUMBER).validate("profile",t.level,e.AssertRules.NUMBER).validate("profile",t.extraProfile,e.AssertRules.OBJECT).validate("profile",t.extraProfile,(function(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&"string"!=typeof e[t])return!1;return!0})).ifReady((function(a,s){return i(n,void 0,void 0,(function(){var n,i,_,d,u,c,E;return o(this,(function(o){switch(o.label){case 0:return n=s.createTraceId(),s.info(T.A_UPDATE_MY_USER_PROFILE_T,JSON.stringify({profile:t}),n),i=t.extraProfile,_={},["name","portraitUri","email","birthday","gender","location","role","level"].forEach((function(n){e.isUndefined(t[n])||(_[n]=t[n])})),e.isUndefined(i)||(_.extraProfile=r({},i)),[4,a.updateMyUserProfile(_)];case 1:return d=o.sent(),u=d.code,c=d.data,E=u===e.ErrorCode.SUCCESS?"info":"warn",s[E](T.A_UPDATE_MY_USER_PROFILE_R,JSON.stringify({code:u,data:c}),n),[2,{code:u,data:c}]}}))}))}))},exports.updateMyUserProfileVisibility=function(t){var n=this;return new e.ValidatorManage(T.A_UPDATE_MY_USER_PROFILE_VISIBILITY_T,T.A_UPDATE_MY_USER_PROFILE_VISIBILITY_R).validate("userProfileVisibility",t,(function(t){return e.isValidEnum({value:t,type:e.UserProfileVisibility})}),!0).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s,_,d;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_UPDATE_MY_USER_PROFILE_VISIBILITY_T,t,n),[4,r.updateMyUserProfileVisibility(t)];case 1:return i=o.sent(),s=i.code,_=i.data,d=s===e.ErrorCode.SUCCESS?"info":"warn",a[d](T.A_UPDATE_MY_USER_PROFILE_VISIBILITY_R,JSON.stringify({code:s,data:_}),n),[2,{code:s,data:_}]}}))}))}))},exports.updateTag=function(t){return i(this,void 0,void 0,(function(){var n=this;return o(this,(function(r){return[2,new e.ValidatorManage(T.A_UPDATE_TAG_T,T.A_UPDATE_TAG_R).validate("tagId",null==t?void 0:t.tagId,e.AssertRules.STRING,!0).validate("tagName",null==t?void 0:t.tagName,e.AssertRules.STRING,!0).validate("tagName",null==t?void 0:t.tagName,(function(e){return!!e&&e.length<=15})).ifReady((function(r,a){return i(n,void 0,void 0,(function(){var n,i,s;return o(this,(function(o){switch(o.label){case 0:return n=a.createTraceId(),a.info(T.A_UPDATE_TAG_T,JSON.stringify({tagId:t.tagId,tagName:t.tagName}),n),[4,r.updateTag(t)];case 1:return i=o.sent().code,s=i===e.ErrorCode.SUCCESS?"info":"warn",a[s](T.A_UPDATE_TAG_R,JSON.stringify({code:i}),n),i===e.ErrorCode.SUCCESS?[2,{code:i}]:[2,{code:i,msg:U[i]}]}}))}))}))]}))}))},exports.upload=Ce;
