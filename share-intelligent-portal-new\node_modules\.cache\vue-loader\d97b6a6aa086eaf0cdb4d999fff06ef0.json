{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\productOrderDetail.vue?vue&type=style&index=0&id=5252311c&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\productOrderDetail.vue", "mtime": 1750311962969}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["productOrderDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkMA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "productOrderDetail.vue", "sourceRoot": "src/views/manufacturingSharing/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"card-container cardStyle\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <div class=\"card_left_bottom\">\r\n          <div class=\"title\">{{ detailsData.demandCompany }}</div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">联系人：</div>\r\n            <div class=\"optionValue\">{{ detailsData.contactPerson }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">联系电话：</div>\r\n            <div class=\"optionValue\">{{ detailsData.contactPhone }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">交货地址：</div>\r\n            <div class=\"optionValue\">{{ detailsData.deliveryAddress }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">需求截至时间：</div>\r\n            <div class=\"optionValue\">{{ detailsData.deadline }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">订单状态：</div>\r\n            <div class=\"optionValue\">\r\n              {{\r\n                detailsData.status == \"0\"\r\n                  ? \"未接单\"\r\n                  : detailsData.status == \"1\"\r\n                    ? \"进行中\"\r\n                    : \"已完成\"\r\n              }}\r\n            </div>\r\n          </div>\r\n          <div class=\"buttonStyle\" @click=\"jumpIntention\">我要接单</div>\r\n        </div>\r\n      </div>\r\n      <!-- 中间 -->\r\n      <div class=\"card_center_line\"></div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">需求物料</div>\r\n          </div>\r\n          <div style=\"margin-top: 22px\">\r\n            <el-table :data=\"detailsData.materials\" style=\"margin-top: 20px\">\r\n              <el-table-column label=\"物料名称\" align=\"center\" prop=\"name\">\r\n              </el-table-column>\r\n              <el-table-column label=\"规格型号\" align=\"center\" prop=\"modelNumber\">\r\n              </el-table-column>\r\n              <el-table-column label=\"数量\" align=\"center\" prop=\"quantity\">\r\n              </el-table-column>\r\n              <el-table-column label=\"单位\" align=\"center\" prop=\"unit\">\r\n              </el-table-column>\r\n              <el-table-column label=\"可承接量\" align=\"center\" prop=\"capacity\">\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">交付要求</div>\r\n          </div>\r\n          <div style=\"margin-top: 22px\">\r\n            <el-descriptions class=\"margin-top\" title=\"\" :column=\"2\" border>\r\n              <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 压力 </template>\r\n60Mpa\r\n</el-descriptions-item>\r\n<el-descriptions-item>\r\n  <template slot=\"label\"> 温度 </template>\r\n  18100000000\r\n</el-descriptions-item>\r\n<el-descriptions-item>\r\n  <template slot=\"label\"> 尺寸 </template>\r\n  3.5m\r\n</el-descriptions-item> -->\r\n              <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 规格型号 </template>\r\n                T-565487\r\n              </el-descriptions-item> -->\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 拦标价 </template>\r\n                <!-- {{ detailsData.deliveryAddress }} -->\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 是否允许接单 </template>\r\n                是\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 交付时间 </template>\r\n                <!-- 2025-12-05 -->\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 需求截至时间： </template>\r\n                {{ detailsData.deadline }}\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">基本信息</div>\r\n          </div>\r\n          <div style=\"margin-top: 22px\">\r\n            <el-descriptions class=\"margin-top\" title=\"\" :column=\"2\" border>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 需求企业 </template>\r\n                {{ detailsData.demandCompany }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 联系人 </template>\r\n                {{ detailsData.contactPerson }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 联系电话 </template>\r\n                {{ detailsData.contactPhone }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 交货地址 </template>\r\n                {{ detailsData.deliveryAddress }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item :span=\"2\">\r\n                <template slot=\"label\"> 交货要求 </template>\r\n                {{ detailsData.fileRequirement }}\r\n              </el-descriptions-item>\r\n              <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 开户行 </template>\r\n                {{ detailsData.bankName }}\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 付款账号 </template>\r\n                {{ detailsData.paymentAccount }}\r\n              </el-descriptions-item> -->\r\n            </el-descriptions>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { orderDetailData } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"deviceDetail\",\r\n  data() {\r\n    return {\r\n      detailsData: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getDetailData();\r\n  },\r\n  methods: {\r\n    getDetailData() {\r\n      orderDetailData(this.id).then((res) => {\r\n        if (res.code === 200) {\r\n          this.detailsData = res.data;\r\n        }\r\n      });\r\n    },\r\n    takeOrder(id) {\r\n      this.$router.push(\"/receiveOrder\"); // 传id\r\n    },\r\n    jumpIntention() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(`/receiveOrder?demandName=${this.detailsData.demandCompany}&updateTime=${this.detailsData.updateTime}&intentionType=12&fieldName=生成订单&intentionId=${this.detailsData.id}`);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: #f2f2f2;\r\n  padding: 30px 0 61px 0;\r\n}\r\n\r\n.cardStyle {\r\n  height: 100%;\r\n  // height: 660px;\r\n  background-color: #ffffff;\r\n  padding: 60px 56px 54px 50px;\r\n  display: flex;\r\n}\r\n\r\n.card_left {\r\n  .card_left_bottom {\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      font-size: 20px;\r\n      color: #222222;\r\n      margin-bottom: 25px;\r\n    }\r\n\r\n    .everyOption {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 12px;\r\n\r\n      .optionName {\r\n        // height: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #999999;\r\n      }\r\n\r\n      .optionValue {\r\n        // height: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #333333;\r\n      }\r\n    }\r\n\r\n    .buttonStyle {\r\n      margin-top: 32px;\r\n      // margin-left: 55px;\r\n      width: 220px;\r\n      height: 50px;\r\n      background: #21c9b8;\r\n      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);\r\n      border-radius: 2px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      text-align: center;\r\n      line-height: 50px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.card_center_line {\r\n  width: 1px;\r\n  height: 660px;\r\n  background: #e1e1e1;\r\n  margin-left: 60px;\r\n  margin-right: 61px;\r\n}\r\n\r\n.card_right {\r\n  width: 100%;\r\n\r\n  // overflow-y: auto;\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}