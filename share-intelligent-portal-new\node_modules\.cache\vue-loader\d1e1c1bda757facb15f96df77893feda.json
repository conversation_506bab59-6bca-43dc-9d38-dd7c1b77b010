{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\index_v1.vue?vue&type=style&index=0&id=572c3b22&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\index_v1.vue", "mtime": 1750311962957}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmRhc2hib2FyZC1lZGl0b3ItY29udGFpbmVyIHsNCiAgcGFkZGluZzogMzJweDsNCiAgYmFja2dyb3VuZC1jb2xvcjogcmdiKDI0MCwgMjQyLCAyNDUpOw0KICBwb3NpdGlvbjogcmVsYXRpdmU7DQoNCiAgLmNoYXJ0LXdyYXBwZXIgew0KICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgcGFkZGluZzogMTZweCAxNnB4IDA7DQogICAgbWFyZ2luLWJvdHRvbTogMzJweDsNCiAgfQ0KfQ0KDQpAbWVkaWEgKG1heC13aWR0aDoxMDI0cHgpIHsNCiAgLmNoYXJ0LXdyYXBwZXIgew0KICAgIHBhZGRpbmc6IDhweDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index_v1.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index_v1.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-editor-container\">\r\n\r\n    <panel-group @handleSetLineChartData=\"handleSetLineChartData\" />\r\n\r\n    <el-row style=\"background:#fff;padding:16px 16px 0;margin-bottom:32px;\">\r\n      <line-chart :chart-data=\"lineChartData\" />\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"32\">\r\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\r\n        <div class=\"chart-wrapper\">\r\n          <raddar-chart />\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\r\n        <div class=\"chart-wrapper\">\r\n          <pie-chart />\r\n        </div>\r\n      </el-col>\r\n      <el-col :xs=\"24\" :sm=\"24\" :lg=\"8\">\r\n        <div class=\"chart-wrapper\">\r\n          <bar-chart />\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    \r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport PanelGroup from './dashboard/PanelGroup'\r\nimport LineChart from './dashboard/LineChart'\r\nimport RaddarChart from './dashboard/Raddar<PERSON>hart'\r\nimport PieChart from './dashboard/PieChart'\r\nimport BarChart from './dashboard/BarChart'\r\n\r\nconst lineChartData = {\r\n  newVisitis: {\r\n    expectedData: [100, 120, 161, 134, 105, 160, 165],\r\n    actualData: [120, 82, 91, 154, 162, 140, 145]\r\n  },\r\n  messages: {\r\n    expectedData: [200, 192, 120, 144, 160, 130, 140],\r\n    actualData: [180, 160, 151, 106, 145, 150, 130]\r\n  },\r\n  purchases: {\r\n    expectedData: [80, 100, 121, 104, 105, 90, 100],\r\n    actualData: [120, 90, 100, 138, 142, 130, 130]\r\n  },\r\n  shoppings: {\r\n    expectedData: [130, 140, 141, 142, 145, 150, 160],\r\n    actualData: [120, 82, 91, 154, 162, 140, 130]\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: 'Index',\r\n  components: {\r\n    PanelGroup,\r\n    LineChart,\r\n    RaddarChart,\r\n    PieChart,\r\n    BarChart\r\n  },\r\n  data() {\r\n    return {\r\n      lineChartData: lineChartData.newVisitis\r\n    }\r\n  },\r\n  methods: {\r\n    handleSetLineChartData(type) {\r\n      this.lineChartData = lineChartData[type]\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dashboard-editor-container {\r\n  padding: 32px;\r\n  background-color: rgb(240, 242, 245);\r\n  position: relative;\r\n\r\n  .chart-wrapper {\r\n    background: #fff;\r\n    padding: 16px 16px 0;\r\n    margin-bottom: 32px;\r\n  }\r\n}\r\n\r\n@media (max-width:1024px) {\r\n  .chart-wrapper {\r\n    padding: 8px;\r\n  }\r\n}\r\n</style>\r\n"]}]}