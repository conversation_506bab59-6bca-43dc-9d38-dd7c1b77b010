<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.DemandMapper">

    <resultMap type="com.ruoyi.portalweb.vo.DemandVO" id="DemandResult">
        <result property="id" column="id"/>
        <result property="companyName" column="company_name"/>
        <result property="contact" column="contact"/>
        <result property="phone" column="phone"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="description" column="description"/>
        <result property="applicationArea" column="application_area"/>
        <result property="imageUrl" column="image_url"/>
<!--        <result property="bidType" column="bid_type"/>-->
<!--        <result property="targetCompany" column="target_company"/>-->
<!--        <result property="bidStartTime" column="bid_start_time"/>-->
<!--        <result property="bidEndTime" column="bid_end_time"/>-->
<!--        <result property="bidDeadline" column="bid_deadline"/>-->
<!--        <result property="attachment" column="attachment"/>-->
<!--        <result property="publisher" column="publisher"/>-->
        <result property="auditStatus" column="audit_status"/>
        <result property="visible" column="visible"/>
        <result property="recommend" column="recommend"/>
        <result property="processStatus" column="process_status"/>
        <result property="onShow" column="on_show"/>
<!--        <result property="prospectiveCorp" column="prospective_corp"/>-->
<!--        <result property="autoPublish" column="auto_publish"/>-->
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberId" column="member_id"/>
        <result property="viewCount" column="view_count"/>

        <result property="typeName" column="type_name"/>
        <result property="imageUrlPath" column="image_url_path"/>
        <result property="applicationAreaName" column="application_area_name"/>
        <result property="auditStatusName" column="audit_status_name"/>

    </resultMap>

    <sql id="selectDemandVo">
        select id, company_name, contact, phone, title, type, description, application_area, image_url, bid_type,
        target_company, bid_start_time, bid_end_time, bid_deadline, attachment, publisher, view_count,
        audit_status, visible, recommend, process_status, on_show, prospective_corp, auto_publish,
        create_by, create_time, update_by, update_time, remark
        from demand
    </sql>

    <sql id="Base_Column_List">
		a.*,c.file_full_path AS image_url_path, b.dict_label as type_name, d.application_field_name AS application_area_name
        ,e.dict_label as audit_status_name
	</sql>

	<sql id="Base_Table_List">
		FROM demand a
        LEFT JOIN sys_dict_data b ON a.type = b.dict_value AND b.dict_type = 'demand_type'
        LEFT JOIN sys_file_info c ON a.image_url = c.file_id
        LEFT JOIN (
            SELECT de.id as did, GROUP_CONCAT(sy.application_field_name) application_field_name
            FROM demand de
            JOIN application_field sy ON FIND_IN_SET(sy.application_field_id,de.application_area)
            GROUP BY de.id
        ) d on a.id = d.did
        LEFT JOIN sys_dict_data e ON a.audit_status = e.dict_value AND e.dict_type = 'demand_status'
	</sql>

    <select id="selectDemandList" parameterType="DemandVO" resultMap="DemandResult">
        <!--<include refid="selectDemandVo"/>-->
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        <where>
            <if test="companyName != null  and companyName != ''">
                and a.company_name like concat('%', #{companyName}, '%')
            </if>
            <if test="contact != null  and contact != ''">and a.contact = #{contact}</if>
            <if test="phone != null  and phone != ''">and a.phone = #{phone}</if>
            <if test="title != null  and title != ''">and a.title like concat('%', #{title}, '%')</if>
            <if test="type != null  and type != ''">and a.type like concat('%', #{type},'%')</if>
            <if test="applicationArea != null  and applicationArea != ''">
                and a.application_area like concat('%', #{applicationArea}, '%')
            </if>
<!--            <if test="bidType != null  and bidType != ''">and a.bid_type = #{bidType}</if>-->
<!--            <if test="bidStartTime != null ">and a.bid_start_time = #{bidStartTime}</if>-->
<!--            <if test="bidEndTime != null ">and a.bid_end_time = #{bidEndTime}</if>-->
<!--            <if test="bidDeadline != null ">and a.bid_deadline = #{bidDeadline}</if>-->
<!--            <if test="publisher != null  and publisher != ''">and a.publisher = #{publisher}</if>-->
            <if test="auditStatus != null and auditStatus != '' ">and a.audit_status = #{auditStatus}</if>
            <if test="processStatus != null ">and a.process_status = #{processStatus}</if>
            <if test="onShow != null ">and a.on_show = #{onShow}</if>
<!--            <if test="autoPublish != null ">and a.auto_publish = #{autoPublish}</if>-->
            <if test="memberId != null ">and a.member_id = #{memberId}</if>
            <if test="viewCount != null ">and a.view_count = #{viewCount}</if>
            <if test="startTime != null ">and a.create_time &gt;= #{startTime}</if>
            <if test="endTime != null ">and a.create_time &lt;= #{endTime}</if>
            <if test="keyword != null ">and ( a.title like concat('%', #{keyword}, '%') or a.company_name like concat('%', #{keyword}, '%') or  a.description like concat('%', #{keyword}, '%') or a.application_area like concat('%', #{keyword}, '%'))</if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <select id="selectDemandById" parameterType="Long" resultMap="DemandResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        where a.id = #{id}
    </select>
    <select id="selectDemandListByMemberIds" resultMap="DemandResult"
            parameterType="List">
        SELECT
        <include refid="Base_Column_List" />
        <include refid="Base_Table_List" />
            <where>
            a.on_show = 0 AND a.audit_status = 2 AND
            a.member_id IN
                <foreach collection="list" open="(" close=")" separator="," item="memberId">
                    #{memberId}
                </foreach>
            </where>
    </select>
    <select id="selectDemandListByDemandIds" resultMap="DemandResult" parameterType="java.util.List">
        <include refid="selectDemandVo"/>
        <where>
            id IN
            <foreach collection="list" open="(" separator="," close=")" item="id">#{id}</foreach>
        </where>
    </select>

    <insert id="insertDemand" parameterType="Demand" useGeneratedKeys="true" keyProperty="id">
        insert into demand
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="contact != null and contact != ''">contact,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="type != null">type,</if>
            <if test="description != null">description,</if>
            <if test="applicationArea != null">application_area,</if>
            <if test="imageUrl != null">image_url,</if>
<!--            <if test="bidType != null">bid_type,</if>-->
<!--            <if test="targetCompany != null">target_company,</if>-->
<!--            <if test="bidStartTime != null">bid_start_time,</if>-->
<!--            <if test="bidEndTime != null">bid_end_time,</if>-->
<!--            <if test="bidDeadline != null">bid_deadline,</if>-->
<!--            <if test="attachment != null">attachment,</if>-->
<!--            <if test="publisher != null">publisher,</if>-->
            <if test="auditStatus != null">audit_status,</if>
            <if test="visible != null and visible != ''">visible,</if>
            <if test="recommend != null">recommend,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="onShow != null">on_show,</if>
<!--            <if test="prospectiveCorp != null">prospective_corp,</if>-->
<!--            <if test="autoPublish != null">auto_publish,</if>-->
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="memberId != null">member_id,</if>
            <if test="viewCount != null">view_count,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="contact != null and contact != ''">#{contact},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="type != null">#{type},</if>
            <if test="description != null">#{description},</if>
            <if test="applicationArea != null">#{applicationArea},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
<!--            <if test="bidType != null">#{bidType},</if>-->
<!--            <if test="targetCompany != null">#{targetCompany},</if>-->
<!--            <if test="bidStartTime != null">#{bidStartTime},</if>-->
<!--            <if test="bidEndTime != null">#{bidEndTime},</if>-->
<!--            <if test="bidDeadline != null">#{bidDeadline},</if>-->
<!--            <if test="attachment != null">#{attachment},</if>-->
<!--            <if test="publisher != null">#{publisher},</if>-->
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="visible != null and visible != ''">#{visible},</if>
            <if test="recommend != null">#{recommend},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="onShow != null">#{onShow},</if>
<!--            <if test="prospectiveCorp != null">#{prospectiveCorp},</if>-->
<!--            <if test="autoPublish != null">#{autoPublish},</if>-->
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="viewCount != null">#{viewCount},</if>
        </trim>
    </insert>
    <update id="addDemandViewCount">
        update demand set view_count = view_count+1 where id = #{id}
    </update>

    <update id="updateDemand" parameterType="Demand">
        update demand
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="contact != null and contact != ''">contact = #{contact},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="type != null">type = #{type},</if>
            <if test="description != null">description = #{description},</if>
            <if test="applicationArea != null">application_area = #{applicationArea},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
<!--            <if test="bidType != null">bid_type = #{bidType},</if>-->
<!--            <if test="targetCompany != null">target_company = #{targetCompany},</if>-->
<!--            <if test="bidStartTime != null">bid_start_time = #{bidStartTime},</if>-->
<!--            <if test="bidEndTime != null">bid_end_time = #{bidEndTime},</if>-->
<!--            <if test="bidDeadline != null">bid_deadline = #{bidDeadline},</if>-->
<!--            <if test="attachment != null">attachment = #{attachment},</if>-->
<!--            <if test="publisher != null">publisher = #{publisher},</if>-->
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="visible != null and visible != ''">visible = #{visible},</if>
            <if test="recommend != null">recommend = #{recommend},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="onShow != null">on_show = #{onShow},</if>
<!--            <if test="prospectiveCorp != null">prospective_corp = #{prospectiveCorp},</if>-->
<!--            <if test="autoPublish != null">auto_publish = #{autoPublish},</if>-->
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDemandById" parameterType="Long">
        delete from demand where id = #{id}
    </delete>

    <delete id="deleteDemandByIds" parameterType="String">
        delete from demand where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>