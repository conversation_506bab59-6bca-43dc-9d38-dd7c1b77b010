{"version": 3, "file": "fetch.js", "sources": ["../../../../src/internal/observable/dom/fetch.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,IAAI,EAAE,MAAM,uBAAuB,CAAC;AA8F7C,MAAM,UAAU,SAAS,CACvB,KAAuB,EACvB,gBAEM;IAFN,iCAAA,EAAA,qBAEM;IAEE,IAAA,oCAAQ,EAAE,qDAAO,CAAsB;IAC/C,OAAO,IAAI,UAAU,CAAe,UAAA,UAAU;QAC5C,IAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;QACzC,IAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;QACjC,IAAI,SAAS,GAAG,IAAI,CAAC;QACrB,IAAI,YAAY,GAAG,KAAK,CAAC;QAEzB,IAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACxC,YAAY,CAAC,GAAG,CAAC;YACf,YAAY,GAAG,IAAI,CAAC;YACpB,IAAI,SAAS,EAAE;gBACb,UAAU,CAAC,KAAK,EAAE,CAAC;aACpB;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,iBAA8B,CAAC;QACnC,IAAI,IAAI,EAAE;YAER,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;oBACvB,UAAU,CAAC,KAAK,EAAE,CAAC;iBACpB;qBAAM;oBACL,IAAM,aAAW,GAAG,IAAI,CAAC,MAAM,CAAC;oBAChC,IAAM,oBAAkB,GAAG;wBACzB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;4BACnB,UAAU,CAAC,KAAK,EAAE,CAAC;yBACpB;oBACH,CAAC,CAAC;oBACF,aAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,oBAAkB,CAAC,CAAC;oBAC1D,YAAY,CAAC,GAAG,CAAC,cAAM,OAAA,aAAW,CAAC,mBAAmB,CAAC,OAAO,EAAE,oBAAkB,CAAC,EAA5D,CAA4D,CAAC,CAAC;iBACtF;aACF;YAGD,iBAAiB,wBAAQ,IAAI,IAAE,MAAM,QAAA,GAAE,CAAC;SACzC;aAAM;YACL,iBAAiB,GAAG,EAAE,MAAM,QAAA,EAAE,CAAC;SAChC;QAED,KAAK,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,UAAA,QAAQ;YAC3C,IAAI,QAAQ,EAAE;gBACZ,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CACjD,UAAA,KAAK,IAAI,OAAA,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,EAAtB,CAAsB,EAC/B,UAAA,GAAG;oBACD,SAAS,GAAG,KAAK,CAAC;oBAClB,IAAI,CAAC,YAAY,EAAE;wBAEjB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;qBACvB;gBACH,CAAC,EACD;oBACE,SAAS,GAAG,KAAK,CAAC;oBAClB,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACxB,CAAC,CACF,CAAC,CAAC;aACJ;iBAAM;gBACL,SAAS,GAAG,KAAK,CAAC;gBAClB,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1B,UAAU,CAAC,QAAQ,EAAE,CAAC;aACvB;QACH,CAAC,CAAC,CAAC,KAAK,CAAC,UAAA,GAAG;YACV,SAAS,GAAG,KAAK,CAAC;YAClB,IAAI,CAAC,YAAY,EAAE;gBAEjB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACvB;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,YAAY,CAAC;IACtB,CAAC,CAAC,CAAC;AACL,CAAC"}