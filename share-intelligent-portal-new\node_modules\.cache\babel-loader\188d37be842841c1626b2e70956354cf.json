{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\detail.vue", "mtime": 1750311962980}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBfbm90aWNlID0gcmVxdWlyZSgiQC9hcGkvbm90aWNlIik7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHsKICBuYW1lOiAicG9saWN5UGFnZSIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGRhdGE6IHt9CiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHRoaXMuaW5pdCgpOwogIH0sCiAgbWV0aG9kczogewogICAgaW5pdDogZnVuY3Rpb24gaW5pdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdmFyIGlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7CiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7CiAgICAgICgwLCBfbm90aWNlLmdldEluZm9EZXRhaWwpKHsKICAgICAgICBpZDogaWQKICAgICAgfSkudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIF90aGlzLmRhdGEgPSByZXMuZGF0YSB8fCB7fTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_notice", "require", "name", "data", "created", "init", "methods", "_this", "id", "$route", "query", "loading", "getInfoDetail", "then", "res", "catch"], "sources": ["src/views/policy/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"notice-detail-container\">\r\n    <div class=\"notice-detail-banner\">\r\n      <img src=\"../../assets/notice/noticeDetailBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div class=\"notice-detail-title-box\">\r\n      <div class=\"notice-divider\"></div>\r\n      <div class=\"notice-detail-title\">政策详情</div>\r\n      <div class=\"notice-divider\"></div>\r\n    </div>\r\n    <div class=\"notice-detail-content\">\r\n      <div class=\"notice-detail-box\">\r\n        <div class=\"notice-info-title\">\r\n          {{ data.title }}\r\n        </div>\r\n        <div class=\"notice-info-time\">{{ data.updateTime }}</div>\r\n        <div class=\"notice-info-divider\"></div>\r\n        <div class=\"notice-info-box\">\r\n          <div\r\n            v-html=\"data.content\"\r\n            class=\"notice-info-content ql-editor\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getInfoDetail } from \"@/api/notice\";\r\n\r\nexport default {\r\n  name: \"policyPage\",\r\n  data() {\r\n    return {\r\n      data: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      let id = this.$route.query.id;\r\n      this.loading = true;\r\n      getInfoDetail({ id: id })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.notice-detail-container {\r\n  width: 100%;\r\n  padding: 0 0 100px;\r\n  background: #f4f5f9;\r\n  .notice-detail-banner {\r\n    width: 100%;\r\n    height: 26vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .notice-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .notice-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .notice-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .notice-detail-content {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n    .notice-detail-box {\r\n      padding: 60px 116px 100px;\r\n      font-family: PingFangSC-Semibold, PingFang SC;\r\n      .notice-info-title {\r\n        width: 960px;\r\n        font-size: 32px;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n        white-space: nowrap; /*让文字不换行*/\r\n        overflow: hidden; /*超出要隐藏*/\r\n        word-wrap: break-word;\r\n      }\r\n      .notice-info-time {\r\n        font-size: 12px;\r\n        color: #999;\r\n        line-height: 12px;\r\n        padding-top: 40px;\r\n      }\r\n      .notice-info-divider {\r\n        width: 100%;\r\n        height: 1px;\r\n        background: #e8e8e8;\r\n        margin-top: 10px;\r\n      }\r\n      .notice-info-box {\r\n        padding-top: 40px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.notice-detail-container {\r\n  .notice-info-content {\r\n    word-break: break-all;\r\n    font-size: 16px;\r\n    line-height: 28px;\r\n    color: #333;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    img {\r\n      max-width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;AA6BA,IAAAA,OAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAA,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,IAAA,WAAAA,KAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,EAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,EAAA;MACA,KAAAG,OAAA;MACA,IAAAC,qBAAA;QAAAJ,EAAA,EAAAA;MAAA,GACAK,IAAA,WAAAC,GAAA;QACAP,KAAA,CAAAI,OAAA;QACAJ,KAAA,CAAAJ,IAAA,GAAAW,GAAA,CAAAX,IAAA;MACA,GACAY,KAAA;QACAR,KAAA,CAAAI,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}