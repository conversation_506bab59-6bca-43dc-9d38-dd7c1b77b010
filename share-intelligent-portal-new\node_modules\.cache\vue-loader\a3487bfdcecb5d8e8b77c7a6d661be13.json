{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Sidebar\\Item.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Sidebar\\Item.vue", "mtime": 1750311962851}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICdNZW51SXRlbScsDQogIGZ1bmN0aW9uYWw6IHRydWUsDQogIHByb3BzOiB7DQogICAgaWNvbjogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogJycNCiAgICB9LA0KICAgIHRpdGxlOiB7DQogICAgICB0eXBlOiBTdHJpbmcsDQogICAgICBkZWZhdWx0OiAnJw0KICAgIH0NCiAgfSwNCiAgcmVuZGVyKGgsIGNvbnRleHQpIHsNCiAgICBjb25zdCB7IGljb24sIHRpdGxlIH0gPSBjb250ZXh0LnByb3BzDQogICAgY29uc3Qgdm5vZGVzID0gW10NCg0KICAgIGlmIChpY29uKSB7DQogICAgICB2bm9kZXMucHVzaCg8c3ZnLWljb24gaWNvbi1jbGFzcz17aWNvbn0vPikNCiAgICB9DQoNCiAgICBpZiAodGl0bGUpIHsNCiAgICAgIGlmICh0aXRsZS5sZW5ndGggPiA1KSB7DQogICAgICAgIHZub2Rlcy5wdXNoKDxzcGFuIHNsb3Q9J3RpdGxlJyB0aXRsZT17KHRpdGxlKX0+eyh0aXRsZSl9PC9zcGFuPikNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHZub2Rlcy5wdXNoKDxzcGFuIHNsb3Q9J3RpdGxlJz57KHRpdGxlKX08L3NwYW4+KQ0KICAgICAgfQ0KICAgIH0NCiAgICByZXR1cm4gdm5vZGVzDQogIH0NCn0NCg=="}, {"version": 3, "sources": ["Item.vue"], "names": [], "mappings": ";AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Item.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'MenuItem',\r\n  functional: true,\r\n  props: {\r\n    icon: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  render(h, context) {\r\n    const { icon, title } = context.props\r\n    const vnodes = []\r\n\r\n    if (icon) {\r\n      vnodes.push(<svg-icon icon-class={icon}/>)\r\n    }\r\n\r\n    if (title) {\r\n      if (title.length > 5) {\r\n        vnodes.push(<span slot='title' title={(title)}>{(title)}</span>)\r\n      } else {\r\n        vnodes.push(<span slot='title'>{(title)}</span>)\r\n      }\r\n    }\r\n    return vnodes\r\n  }\r\n}\r\n</script>\r\n"]}]}