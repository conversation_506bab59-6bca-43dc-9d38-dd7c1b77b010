<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.NewsInformationMapper">
    
    <resultMap type="NewsInformation" id="NewsInformationResult">
        <result property="newsInformationId"    column="news_information_id"    />
        <result property="newsInformationPlateId"    column="news_information_plate_id"    />
        <result property="solutionTypeId"    column="solution_type_id"    />
        <result property="newsInformationSource"    column="news_information_source"    />
        <result property="newsInformationAuthor"    column="news_information_author"    />
        <result property="newsInformationName"    column="news_information_name"    />
        <result property="newsInformationIntroduction"    column="news_information_introduction"    />
        <result property="newsInformationImg"    column="news_information_img"    />
        <result property="newsInformationContent"    column="news_information_content"    />
        <result property="newsInformationFrequency"    column="news_information_frequency"    />
        <result property="newsInformationDate"    column="news_information_date"    />
        <result property="top"    column="top"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectNewsInformationVo">
        select news_information_id, news_information_plate_id, solution_type_id, news_information_source, news_information_author, news_information_name, news_information_introduction, news_information_img, news_information_content, news_information_frequency, news_information_date, top, del_flag, create_by, create_time, update_by, update_time, remark from news_information
    </sql>

    <select id="selectNewsInformationList" parameterType="NewsInformation" resultMap="NewsInformationResult">
        <include refid="selectNewsInformationVo"/>
        <where>  
            <if test="newsInformationPlateId != null "> and news_information_plate_id = #{newsInformationPlateId}</if>
            <if test="solutionTypeId != null "> and solution_type_id = #{solutionTypeId}</if>
            <if test="newsInformationSource != null  and newsInformationSource != ''"> and news_information_source = #{newsInformationSource}</if>
            <if test="newsInformationAuthor != null  and newsInformationAuthor != ''"> and news_information_author = #{newsInformationAuthor}</if>
            <if test="newsInformationName != null  and newsInformationName != ''"> and news_information_name like concat('%', #{newsInformationName}, '%')</if>
            <if test="newsInformationIntroduction != null  and newsInformationIntroduction != ''"> and news_information_introduction = #{newsInformationIntroduction}</if>
            <if test="newsInformationImg != null  and newsInformationImg != ''"> and news_information_img = #{newsInformationImg}</if>
            <if test="newsInformationContent != null  and newsInformationContent != ''"> and news_information_content = #{newsInformationContent}</if>
            <if test="newsInformationFrequency != null "> and news_information_frequency = #{newsInformationFrequency}</if>
            <if test="newsInformationDate != null "> and news_information_date = #{newsInformationDate}</if>
            <if test="top != null "> and top = #{top}</if>
        </where>
    </select>
    
    <select id="selectNewsInformationByNewsInformationId" parameterType="Long" resultMap="NewsInformationResult">
        <include refid="selectNewsInformationVo"/>
        where news_information_id = #{newsInformationId}
    </select>
        
    <insert id="insertNewsInformation" parameterType="NewsInformation" useGeneratedKeys="true" keyProperty="newsInformationId">
        insert into news_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="newsInformationPlateId != null">news_information_plate_id,</if>
            <if test="solutionTypeId != null">solution_type_id,</if>
            <if test="newsInformationSource != null">news_information_source,</if>
            <if test="newsInformationAuthor != null">news_information_author,</if>
            <if test="newsInformationName != null">news_information_name,</if>
            <if test="newsInformationIntroduction != null">news_information_introduction,</if>
            <if test="newsInformationImg != null">news_information_img,</if>
            <if test="newsInformationContent != null">news_information_content,</if>
            <if test="newsInformationFrequency != null">news_information_frequency,</if>
            <if test="newsInformationDate != null">news_information_date,</if>
            <if test="top != null">top,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="newsInformationPlateId != null">#{newsInformationPlateId},</if>
            <if test="solutionTypeId != null">#{solutionTypeId},</if>
            <if test="newsInformationSource != null">#{newsInformationSource},</if>
            <if test="newsInformationAuthor != null">#{newsInformationAuthor},</if>
            <if test="newsInformationName != null">#{newsInformationName},</if>
            <if test="newsInformationIntroduction != null">#{newsInformationIntroduction},</if>
            <if test="newsInformationImg != null">#{newsInformationImg},</if>
            <if test="newsInformationContent != null">#{newsInformationContent},</if>
            <if test="newsInformationFrequency != null">#{newsInformationFrequency},</if>
            <if test="newsInformationDate != null">#{newsInformationDate},</if>
            <if test="top != null">#{top},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateNewsInformation" parameterType="NewsInformation">
        update news_information
        <trim prefix="SET" suffixOverrides=",">
            <if test="newsInformationPlateId != null">news_information_plate_id = #{newsInformationPlateId},</if>
            <if test="solutionTypeId != null">solution_type_id = #{solutionTypeId},</if>
            <if test="newsInformationSource != null">news_information_source = #{newsInformationSource},</if>
            <if test="newsInformationAuthor != null">news_information_author = #{newsInformationAuthor},</if>
            <if test="newsInformationName != null">news_information_name = #{newsInformationName},</if>
            <if test="newsInformationIntroduction != null">news_information_introduction = #{newsInformationIntroduction},</if>
            <if test="newsInformationImg != null">news_information_img = #{newsInformationImg},</if>
            <if test="newsInformationContent != null">news_information_content = #{newsInformationContent},</if>
            <if test="newsInformationFrequency != null">news_information_frequency = #{newsInformationFrequency},</if>
            <if test="newsInformationDate != null">news_information_date = #{newsInformationDate},</if>
            <if test="top != null">top = #{top},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where news_information_id = #{newsInformationId}
    </update>

    <delete id="deleteNewsInformationByNewsInformationId" parameterType="Long">
        delete from news_information where news_information_id = #{newsInformationId}
    </delete>

    <delete id="deleteNewsInformationByNewsInformationIds" parameterType="String">
        delete from news_information where news_information_id in 
        <foreach item="newsInformationId" collection="array" open="(" separator="," close=")">
            #{newsInformationId}
        </foreach>
    </delete>
</mapper>