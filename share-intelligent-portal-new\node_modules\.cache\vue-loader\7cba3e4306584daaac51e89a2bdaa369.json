{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\RightPanel\\index.vue?vue&type=style&index=0&id=1e488bfb&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\RightPanel\\index.vue", "mtime": 1750311962813}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5yaWdodFBhbmVsLWJhY2tncm91bmQgew0KICBwb3NpdGlvbjogZml4ZWQ7DQogIHRvcDogMDsNCiAgbGVmdDogMDsNCiAgb3BhY2l0eTogMDsNCiAgdHJhbnNpdGlvbjogb3BhY2l0eSAuM3MgY3ViaWMtYmV6aWVyKC43LCAuMywgLjEsIDEpOw0KICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIC4yKTsNCiAgei1pbmRleDogLTE7DQp9DQoNCi5yaWdodFBhbmVsIHsNCiAgd2lkdGg6IDEwMCU7DQogIG1heC13aWR0aDogMjYwcHg7DQogIGhlaWdodDogMTAwdmg7DQogIHBvc2l0aW9uOiBmaXhlZDsNCiAgdG9wOiAwOw0KICByaWdodDogMDsNCiAgYm94LXNoYWRvdzogMHB4IDBweCAxNXB4IDBweCByZ2JhKDAsIDAsIDAsIC4wNSk7DQogIHRyYW5zaXRpb246IGFsbCAuMjVzIGN1YmljLWJlemllciguNywgLjMsIC4xLCAxKTsNCiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoMTAwJSk7DQogIGJhY2tncm91bmQ6ICNmZmY7DQogIHotaW5kZXg6IDQwMDAwOw0KfQ0KDQouaGFuZGxlLWJ1dHRvbiB7DQogIHdpZHRoOiA0OHB4Ow0KICBoZWlnaHQ6IDQ4cHg7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgbGVmdDogLTQ4cHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgZm9udC1zaXplOiAyNHB4Ow0KICBib3JkZXItcmFkaXVzOiA2cHggMCAwIDZweCAhaW1wb3J0YW50Ow0KICB6LWluZGV4OiAwOw0KICBwb2ludGVyLWV2ZW50czogYXV0bzsNCiAgY3Vyc29yOiBwb2ludGVyOw0KICBjb2xvcjogI2ZmZjsNCiAgbGluZS1oZWlnaHQ6IDQ4cHg7DQogIGkgew0KICAgIGZvbnQtc2l6ZTogMjRweDsNCiAgICBsaW5lLWhlaWdodDogNDhweDsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/RightPanel", "sourcesContent": ["<template>\r\n  <div ref=\"rightPanel\" class=\"rightPanel-container\">\r\n    <div class=\"rightPanel-background\" />\r\n    <div class=\"rightPanel\">\r\n      <div class=\"rightPanel-items\">\r\n        <slot />\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'RightPanel',\r\n  props: {\r\n    clickNotClose: {\r\n      default: false,\r\n      type: Boolean\r\n    }\r\n  },\r\n  computed: {\r\n    show: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'showSettings',\r\n          value: val\r\n        })\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    show(value) {\r\n      if (value && !this.clickNotClose) {\r\n        this.addEventClick()\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.addEventClick()\r\n  },\r\n  beforeDestroy() {\r\n    const elx = this.$refs.rightPanel\r\n    elx.remove()\r\n  },\r\n  methods: {\r\n    addEventClick() {\r\n      window.addEventListener('click', this.closeSidebar)\r\n    },\r\n    closeSidebar(evt) {\r\n      const parent = evt.target.closest('.el-drawer__body')\r\n      if (!parent) {\r\n        this.show = false\r\n        window.removeEventListener('click', this.closeSidebar)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.rightPanel-background {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  opacity: 0;\r\n  transition: opacity .3s cubic-bezier(.7, .3, .1, 1);\r\n  background: rgba(0, 0, 0, .2);\r\n  z-index: -1;\r\n}\r\n\r\n.rightPanel {\r\n  width: 100%;\r\n  max-width: 260px;\r\n  height: 100vh;\r\n  position: fixed;\r\n  top: 0;\r\n  right: 0;\r\n  box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, .05);\r\n  transition: all .25s cubic-bezier(.7, .3, .1, 1);\r\n  transform: translate(100%);\r\n  background: #fff;\r\n  z-index: 40000;\r\n}\r\n\r\n.handle-button {\r\n  width: 48px;\r\n  height: 48px;\r\n  position: absolute;\r\n  left: -48px;\r\n  text-align: center;\r\n  font-size: 24px;\r\n  border-radius: 6px 0 0 6px !important;\r\n  z-index: 0;\r\n  pointer-events: auto;\r\n  cursor: pointer;\r\n  color: #fff;\r\n  line-height: 48px;\r\n  i {\r\n    font-size: 24px;\r\n    line-height: 48px;\r\n  }\r\n}\r\n</style>\r\n"]}]}