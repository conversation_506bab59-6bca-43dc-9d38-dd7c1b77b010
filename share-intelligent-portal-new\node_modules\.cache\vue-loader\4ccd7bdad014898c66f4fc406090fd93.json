{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\index.vue", "mtime": 1750311963059}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0VXNlciwgZ2V0VXNlciwgZGVsVXNlciwgYWRkVXNlciwgdXBkYXRlVXNlciwgcmVzZXRVc2VyUHdkLCBjaGFuZ2VVc2VyU3RhdHVzLCBkZXB0VHJlZVNlbGVjdCB9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyIjsNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsNCmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0IjsNCmltcG9ydCAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QvZGlzdC92dWUtdHJlZXNlbGVjdC5jc3MiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJVc2VyIiwNCiAgZGljdHM6IFsnc3lzX25vcm1hbF9kaXNhYmxlJywgJ3N5c191c2VyX3NleCddLA0KICBjb21wb25lbnRzOiB7IFRyZWVzZWxlY3QgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOeUqOaIt+ihqOagvOaVsOaNrg0KICAgICAgdXNlckxpc3Q6IG51bGwsDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOmDqOmXqOagkemAiemhuQ0KICAgICAgZGVwdE9wdGlvbnM6IHVuZGVmaW5lZCwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDpg6jpl6jlkI3np7ANCiAgICAgIGRlcHROYW1lOiB1bmRlZmluZWQsDQogICAgICAvLyDpu5jorqTlr4bnoIENCiAgICAgIGluaXRQYXNzd29yZDogdW5kZWZpbmVkLA0KICAgICAgLy8g5pel5pyf6IyD5Zu0DQogICAgICBkYXRlUmFuZ2U6IFtdLA0KICAgICAgLy8g5bKX5L2N6YCJ6aG5DQogICAgICBwb3N0T3B0aW9uczogW10sDQogICAgICAvLyDop5LoibLpgInpobkNCiAgICAgIHJvbGVPcHRpb25zOiBbXSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICBkZWZhdWx0UHJvcHM6IHsNCiAgICAgICAgY2hpbGRyZW46ICJjaGlsZHJlbiIsDQogICAgICAgIGxhYmVsOiAibGFiZWwiDQogICAgICB9LA0KICAgICAgLy8g55So5oi35a+85YWl5Y+C5pWwDQogICAgICB1cGxvYWQ6IHsNCiAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGC77yI55So5oi35a+85YWl77yJDQogICAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgICAvLyDlvLnlh7rlsYLmoIfpopjvvIjnlKjmiLflr7zlhaXvvIkNCiAgICAgICAgdGl0bGU6ICIiLA0KICAgICAgICAvLyDmmK/lkKbnpoHnlKjkuIrkvKANCiAgICAgICAgaXNVcGxvYWRpbmc6IGZhbHNlLA0KICAgICAgICAvLyDmmK/lkKbmm7TmlrDlt7Lnu4/lrZjlnKjnmoTnlKjmiLfmlbDmja4NCiAgICAgICAgdXBkYXRlU3VwcG9ydDogMCwNCiAgICAgICAgLy8g6K6+572u5LiK5Lyg55qE6K+35rGC5aS06YOoDQogICAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKSB9LA0KICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYANCiAgICAgICAgdXJsOiBwcm9jZXNzLmVudi5WVUVfQVBQX0JBU0VfQVBJICsgIi9zeXN0ZW0vdXNlci9pbXBvcnREYXRhIg0KICAgICAgfSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwLA0KICAgICAgICB1c2VyTmFtZTogdW5kZWZpbmVkLA0KICAgICAgICBwaG9uZW51bWJlcjogdW5kZWZpbmVkLA0KICAgICAgICBzdGF0dXM6IHVuZGVmaW5lZCwNCiAgICAgICAgZGVwdElkOiB1bmRlZmluZWQNCiAgICAgIH0sDQogICAgICAvLyDliJfkv6Hmga8NCiAgICAgIGNvbHVtbnM6IFsNCiAgICAgICAgeyBrZXk6IDAsIGxhYmVsOiBg55So5oi357yW5Y+3YCwgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogMSwgbGFiZWw6IGDnlKjmiLflkI3np7BgLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiAyLCBsYWJlbDogYOeUqOaIt+aYteensGAsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDMsIGxhYmVsOiBg6YOo6ZeoYCwgdmlzaWJsZTogdHJ1ZSB9LA0KICAgICAgICB7IGtleTogNCwgbGFiZWw6IGDmiYvmnLrlj7fnoIFgLCB2aXNpYmxlOiB0cnVlIH0sDQogICAgICAgIHsga2V5OiA1LCBsYWJlbDogYOeKtuaAgWAsIHZpc2libGU6IHRydWUgfSwNCiAgICAgICAgeyBrZXk6IDYsIGxhYmVsOiBg5Yib5bu65pe26Ze0YCwgdmlzaWJsZTogdHJ1ZSB9DQogICAgICBdLA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICB1c2VyTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlKjmiLflkI3np7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IG1pbjogMiwgbWF4OiAyMCwgbWVzc2FnZTogJ+eUqOaIt+WQjeensOmVv+W6puW/hemhu+S7i+S6jiAyIOWSjCAyMCDkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBuaWNrTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlKjmiLfmmLXnp7DkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBwYXNzd29yZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLnlKjmiLflr4bnoIHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgICB7IG1pbjogNSwgbWF4OiAyMCwgbWVzc2FnZTogJ+eUqOaIt+WvhueggemVv+W6puW/hemhu+S7i+S6jiA1IOWSjCAyMCDkuYvpl7QnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBlbWFpbDogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHR5cGU6ICJlbWFpbCIsDQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE6YKu566x5Zyw5Z2AIiwNCiAgICAgICAgICAgIHRyaWdnZXI6IFsiYmx1ciIsICJjaGFuZ2UiXQ0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgcGhvbmVudW1iZXI6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBwYXR0ZXJuOiAvXjFbM3w0fDV8Nnw3fDh8OV1bMC05XVxkezh9JC8sDQogICAgICAgICAgICBtZXNzYWdlOiAi6K+36L6T5YWl5q2j56Gu55qE5omL5py65Y+356CBIiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIg0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfQ0KICAgIH07DQogIH0sDQogIHdhdGNoOiB7DQogICAgLy8g5qC55o2u5ZCN56ew562b6YCJ6YOo6Zeo5qCRDQogICAgZGVwdE5hbWUodmFsKSB7DQogICAgICB0aGlzLiRyZWZzLnRyZWUuZmlsdGVyKHZhbCk7DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIHRoaXMuZ2V0RGVwdFRyZWUoKTsNCiAgICB0aGlzLmdldENvbmZpZ0tleSgic3lzLnVzZXIuaW5pdFBhc3N3b3JkIikudGhlbihyZXNwb25zZSA9PiB7DQogICAgICB0aGlzLmluaXRQYXNzd29yZCA9IHJlc3BvbnNlLm1zZzsNCiAgICB9KTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6LnlKjmiLfliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RVc2VyKHRoaXMuYWRkRGF0ZVJhbmdlKHRoaXMucXVlcnlQYXJhbXMsIHRoaXMuZGF0ZVJhbmdlKSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICApOw0KICAgIH0sDQogICAgLyoqIOafpeivoumDqOmXqOS4i+aLieagkee7k+aehCAqLw0KICAgIGdldERlcHRUcmVlKCkgew0KICAgICAgZGVwdFRyZWVTZWxlY3QoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5kZXB0T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOetm+mAieiKgueCuQ0KICAgIGZpbHRlck5vZGUodmFsdWUsIGRhdGEpIHsNCiAgICAgIGlmICghdmFsdWUpIHJldHVybiB0cnVlOw0KICAgICAgcmV0dXJuIGRhdGEubGFiZWwuaW5kZXhPZih2YWx1ZSkgIT09IC0xOw0KICAgIH0sDQogICAgLy8g6IqC54K55Y2V5Ye75LqL5Lu2DQogICAgaGFuZGxlTm9kZUNsaWNrKGRhdGEpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuZGVwdElkID0gZGF0YS5pZDsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIC8vIOeUqOaIt+eKtuaAgeS/ruaUuQ0KICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsNCiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIjsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICciIicgKyByb3cudXNlck5hbWUgKyAnIueUqOaIt+WQl++8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBjaGFuZ2VVc2VyU3RhdHVzKHJvdy51c2VySWQsIHJvdy5zdGF0dXMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uKCkgew0KICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIjEiIDogIjAiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICB1c2VySWQ6IHVuZGVmaW5lZCwNCiAgICAgICAgZGVwdElkOiB1bmRlZmluZWQsDQogICAgICAgIHVzZXJOYW1lOiB1bmRlZmluZWQsDQogICAgICAgIG5pY2tOYW1lOiB1bmRlZmluZWQsDQogICAgICAgIHBhc3N3b3JkOiB1bmRlZmluZWQsDQogICAgICAgIHBob25lbnVtYmVyOiB1bmRlZmluZWQsDQogICAgICAgIGVtYWlsOiB1bmRlZmluZWQsDQogICAgICAgIHNleDogdW5kZWZpbmVkLA0KICAgICAgICBzdGF0dXM6ICIwIiwNCiAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQsDQogICAgICAgIHBvc3RJZHM6IFtdLA0KICAgICAgICByb2xlSWRzOiBbXQ0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMuZGF0ZVJhbmdlID0gW107DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZCA9IHVuZGVmaW5lZDsNCiAgICAgIHRoaXMuJHJlZnMudHJlZS5zZXRDdXJyZW50S2V5KG51bGwpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS51c2VySWQpOw0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7DQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7DQogICAgfSwNCiAgICAvLyDmm7TlpJrmk43kvZzop6blj5ENCiAgICBoYW5kbGVDb21tYW5kKGNvbW1hbmQsIHJvdykgew0KICAgICAgc3dpdGNoIChjb21tYW5kKSB7DQogICAgICAgIGNhc2UgImhhbmRsZVJlc2V0UHdkIjoNCiAgICAgICAgICB0aGlzLmhhbmRsZVJlc2V0UHdkKHJvdyk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgImhhbmRsZUF1dGhSb2xlIjoNCiAgICAgICAgICB0aGlzLmhhbmRsZUF1dGhSb2xlKHJvdyk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgYnJlYWs7DQogICAgICB9DQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgZ2V0VXNlcigpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnBvc3RPcHRpb25zID0gcmVzcG9uc2UucG9zdHM7DQogICAgICAgIHRoaXMucm9sZU9wdGlvbnMgPSByZXNwb25zZS5yb2xlczsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDnlKjmiLciOw0KICAgICAgICB0aGlzLmZvcm0ucGFzc3dvcmQgPSB0aGlzLmluaXRQYXNzd29yZDsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IHVzZXJJZCA9IHJvdy51c2VySWQgfHwgdGhpcy5pZHM7DQogICAgICBnZXRVc2VyKHVzZXJJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMucG9zdE9wdGlvbnMgPSByZXNwb25zZS5wb3N0czsNCiAgICAgICAgdGhpcy5yb2xlT3B0aW9ucyA9IHJlc3BvbnNlLnJvbGVzOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAicG9zdElkcyIsIHJlc3BvbnNlLnBvc3RJZHMpOw0KICAgICAgICB0aGlzLiRzZXQodGhpcy5mb3JtLCAicm9sZUlkcyIsIHJlc3BvbnNlLnJvbGVJZHMpOw0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueeUqOaItyI7DQogICAgICAgIHRoaXMuZm9ybS5wYXNzd29yZCA9ICIiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog6YeN572u5a+G56CB5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUmVzZXRQd2Qocm93KSB7DQogICAgICB0aGlzLiRwcm9tcHQoJ+ivt+i+k+WFpSInICsgcm93LnVzZXJOYW1lICsgJyLnmoTmlrDlr4bnoIEnLCAi5o+Q56S6Iiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICBjbG9zZU9uQ2xpY2tNb2RhbDogZmFsc2UsDQogICAgICAgIGlucHV0UGF0dGVybjogL14uezUsMjB9JC8sDQogICAgICAgIGlucHV0RXJyb3JNZXNzYWdlOiAi55So5oi35a+G56CB6ZW/5bqm5b+F6aG75LuL5LqOIDUg5ZKMIDIwIOS5i+mXtCINCiAgICAgIH0pLnRoZW4oKHsgdmFsdWUgfSkgPT4gew0KICAgICAgICAgIHJlc2V0VXNlclB3ZChyb3cudXNlcklkLCB2YWx1ZSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip/vvIzmlrDlr4bnoIHmmK/vvJoiICsgdmFsdWUpOw0KICAgICAgICAgIH0pOw0KICAgICAgICB9KS5jYXRjaCgoKSA9PiB7fSk7DQogICAgfSwNCiAgICAvKiog5YiG6YWN6KeS6Imy5pON5L2cICovDQogICAgaGFuZGxlQXV0aFJvbGU6IGZ1bmN0aW9uKHJvdykgew0KICAgICAgY29uc3QgdXNlcklkID0gcm93LnVzZXJJZDsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCIvc3lzdGVtL3VzZXItYXV0aC9yb2xlLyIgKyB1c2VySWQpOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm06IGZ1bmN0aW9uKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS51c2VySWQgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICB1cGRhdGVVc2VyKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkVXNlcih0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCB1c2VySWRzID0gcm93LnVzZXJJZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOeUqOaIt+e8luWPt+S4uiInICsgdXNlcklkcyArICci55qE5pWw5o2u6aG577yfJykudGhlbihmdW5jdGlvbigpIHsNCiAgICAgICAgcmV0dXJuIGRlbFVzZXIodXNlcklkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ3N5c3RlbS91c2VyL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYHVzZXJfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICAvKiog5a+85YWl5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlSW1wb3J0KCkgew0KICAgICAgdGhpcy51cGxvYWQudGl0bGUgPSAi55So5oi35a+85YWlIjsNCiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSB0cnVlOw0KICAgIH0sDQogICAgLyoqIOS4i+i9veaooeadv+aTjeS9nCAqLw0KICAgIGltcG9ydFRlbXBsYXRlKCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnc3lzdGVtL3VzZXIvaW1wb3J0VGVtcGxhdGUnLCB7DQogICAgICB9LCBgdXNlcl90ZW1wbGF0ZV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9LA0KICAgIC8vIOaWh+S7tuS4iuS8oOS4reWkhOeQhg0KICAgIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzcyhldmVudCwgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8vIOaWh+S7tuS4iuS8oOaIkOWKn+WkhOeQhg0KICAgIGhhbmRsZUZpbGVTdWNjZXNzKHJlc3BvbnNlLCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy51cGxvYWQub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLmNsZWFyRmlsZXMoKTsNCiAgICAgIHRoaXMuJGFsZXJ0KCI8ZGl2IHN0eWxlPSdvdmVyZmxvdzogYXV0bztvdmVyZmxvdy14OiBoaWRkZW47bWF4LWhlaWdodDogNzB2aDtwYWRkaW5nOiAxMHB4IDIwcHggMDsnPiIgKyByZXNwb25zZS5tc2cgKyAiPC9kaXY+IiwgIuWvvOWFpee7k+aenCIsIHsgZGFuZ2Vyb3VzbHlVc2VIVE1MU3RyaW5nOiB0cnVlIH0pOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvLyDmj5DkuqTkuIrkvKDmlofku7YNCiAgICBzdWJtaXRGaWxlRm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnMudXBsb2FkLnN1Ym1pdCgpOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuVA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <!--部门数据-->\r\n      <el-col :span=\"4\" :xs=\"24\">\r\n        <div class=\"head-container\">\r\n          <el-input\r\n            v-model=\"deptName\"\r\n            placeholder=\"请输入部门名称\"\r\n            clearable\r\n            size=\"small\"\r\n            prefix-icon=\"el-icon-search\"\r\n            style=\"margin-bottom: 20px\"\r\n          />\r\n        </div>\r\n        <div class=\"head-container\">\r\n          <el-tree\r\n            :data=\"deptOptions\"\r\n            :props=\"defaultProps\"\r\n            :expand-on-click-node=\"false\"\r\n            :filter-node-method=\"filterNode\"\r\n            ref=\"tree\"\r\n            node-key=\"id\"\r\n            default-expand-all\r\n            highlight-current\r\n            @node-click=\"handleNodeClick\"\r\n          />\r\n        </div>\r\n      </el-col>\r\n      <!--用户数据-->\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"用户名称\" prop=\"userName\">\r\n            <el-input\r\n              v-model=\"queryParams.userName\" \r\n              placeholder=\"请输入用户名称\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n            <el-input\r\n              v-model=\"queryParams.phonenumber\"\r\n              placeholder=\"请输入手机号码\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"状态\" prop=\"status\">\r\n            <el-select\r\n              v-model=\"queryParams.status\"\r\n              placeholder=\"用户状态\"\r\n              clearable\r\n              style=\"width: 240px\"\r\n            >\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_normal_disable\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"创建时间\">\r\n            <el-date-picker\r\n              v-model=\"dateRange\"\r\n              style=\"width: 240px\"\r\n              value-format=\"yyyy-MM-dd\"\r\n              type=\"daterange\"\r\n              range-separator=\"-\"\r\n              start-placeholder=\"开始日期\"\r\n              end-placeholder=\"结束日期\"\r\n            ></el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:user:add']\"\r\n            >新增</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              v-hasPermi=\"['system:user:edit']\"\r\n            >修改</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:user:remove']\"\r\n            >删除</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"info\"\r\n              plain\r\n              icon=\"el-icon-upload2\"\r\n              size=\"mini\"\r\n              @click=\"handleImport\"\r\n              v-hasPermi=\"['system:user:import']\"\r\n            >导入</el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['system:user:export']\"\r\n            >导出</el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\" :columns=\"columns\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"userList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"50\" align=\"center\" />\r\n          <el-table-column label=\"用户编号\" align=\"center\" key=\"userId\" prop=\"userId\" v-if=\"columns[0].visible\" />\r\n          <el-table-column label=\"用户名称\" align=\"center\" key=\"userName\" prop=\"userName\" v-if=\"columns[1].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"用户昵称\" align=\"center\" key=\"nickName\" prop=\"nickName\" v-if=\"columns[2].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"部门\" align=\"center\" key=\"deptName\" prop=\"dept.deptName\" v-if=\"columns[3].visible\" :show-overflow-tooltip=\"true\" />\r\n          <el-table-column label=\"手机号码\" align=\"center\" key=\"phonenumber\" prop=\"phonenumber\" v-if=\"columns[4].visible\" width=\"120\" />\r\n          <el-table-column label=\"状态\" align=\"center\" key=\"status\" v-if=\"columns[5].visible\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" v-if=\"columns[6].visible\" width=\"160\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createTime) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"操作\"\r\n            align=\"center\"\r\n            width=\"160\"\r\n            class-name=\"small-padding fixed-width\"\r\n          >\r\n            <template slot-scope=\"scope\" v-if=\"scope.row.userId !== 1\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['system:user:edit']\"\r\n              >修改</el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"text\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:user:remove']\"\r\n              >删除</el-button>\r\n              <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['system:user:resetPwd', 'system:user:edit']\">\r\n                <el-button size=\"mini\" type=\"text\" icon=\"el-icon-d-arrow-right\">更多</el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item command=\"handleResetPwd\" icon=\"el-icon-key\"\r\n                    v-hasPermi=\"['system:user:resetPwd']\">重置密码</el-dropdown-item>\r\n                  <el-dropdown-item command=\"handleAuthRole\" icon=\"el-icon-circle-check\"\r\n                    v-hasPermi=\"['system:user:edit']\">分配角色</el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改用户配置对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"600px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"用户昵称\" prop=\"nickName\">\r\n              <el-input v-model=\"form.nickName\" placeholder=\"请输入用户昵称\" maxlength=\"30\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"归属部门\" prop=\"deptId\">\r\n              <treeselect v-model=\"form.deptId\" :options=\"deptOptions\" :show-count=\"true\" placeholder=\"请选择归属部门\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"手机号码\" prop=\"phonenumber\">\r\n              <el-input v-model=\"form.phonenumber\" placeholder=\"请输入手机号码\" maxlength=\"11\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"邮箱\" prop=\"email\">\r\n              <el-input v-model=\"form.email\" placeholder=\"请输入邮箱\" maxlength=\"50\" />\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item v-if=\"form.userId == undefined\" label=\"用户名称\" prop=\"userName\">\r\n              <el-input v-model=\"form.userName\" placeholder=\"请输入用户名称\" maxlength=\"30\" />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item v-if=\"form.userId == undefined\" label=\"用户密码\" prop=\"password\">\r\n              <el-input v-model=\"form.password\" placeholder=\"请输入用户密码\" type=\"password\" maxlength=\"20\" show-password/>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"用户性别\">\r\n              <el-select v-model=\"form.sex\" placeholder=\"请选择性别\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_user_sex\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{dict.label}}</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"岗位\">\r\n              <el-select v-model=\"form.postIds\" multiple placeholder=\"请选择岗位\">\r\n                <el-option\r\n                  v-for=\"item in postOptions\"\r\n                  :key=\"item.postId\"\r\n                  :label=\"item.postName\"\r\n                  :value=\"item.postId\"\r\n                  :disabled=\"item.status == 1\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"角色\">\r\n              <el-select v-model=\"form.roleIds\" multiple placeholder=\"请选择角色\">\r\n                <el-option\r\n                  v-for=\"item in roleOptions\"\r\n                  :key=\"item.roleId\"\r\n                  :label=\"item.roleName\"\r\n                  :value=\"item.roleId\"\r\n                  :disabled=\"item.status == 1\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"备注\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 用户导入对话框 -->\r\n    <el-dialog :title=\"upload.title\" :visible.sync=\"upload.open\" width=\"400px\" append-to-body>\r\n      <el-upload\r\n        ref=\"upload\"\r\n        :limit=\"1\"\r\n        accept=\".xlsx, .xls\"\r\n        :headers=\"upload.headers\"\r\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\r\n        :disabled=\"upload.isUploading\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleFileSuccess\"\r\n        :auto-upload=\"false\"\r\n        drag\r\n      >\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div class=\"el-upload__tip text-center\" slot=\"tip\">\r\n          <div class=\"el-upload__tip\" slot=\"tip\">\r\n            <el-checkbox v-model=\"upload.updateSupport\" /> 是否更新已经存在的用户数据\r\n          </div>\r\n          <span>仅允许导入xls、xlsx格式文件。</span>\r\n          <el-link type=\"primary\" :underline=\"false\" style=\"font-size:12px;vertical-align: baseline;\" @click=\"importTemplate\">下载模板</el-link>\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus, deptTreeSelect } from \"@/api/system/user\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  dicts: ['sys_normal_disable', 'sys_user_sex'],\r\n  components: { Treeselect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 用户表格数据\r\n      userList: null,\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 部门树选项\r\n      deptOptions: undefined,\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 部门名称\r\n      deptName: undefined,\r\n      // 默认密码\r\n      initPassword: undefined,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 岗位选项\r\n      postOptions: [],\r\n      // 角色选项\r\n      roleOptions: [],\r\n      // 表单参数\r\n      form: {},\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\"\r\n      },\r\n      // 用户导入参数\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 是否更新已经存在的用户数据\r\n        updateSupport: 0,\r\n        // 设置上传的请求头部\r\n        headers: { Authorization: \"Bearer \" + getToken() },\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/system/user/importData\"\r\n      },\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        userName: undefined,\r\n        phonenumber: undefined,\r\n        status: undefined,\r\n        deptId: undefined\r\n      },\r\n      // 列信息\r\n      columns: [\r\n        { key: 0, label: `用户编号`, visible: true },\r\n        { key: 1, label: `用户名称`, visible: true },\r\n        { key: 2, label: `用户昵称`, visible: true },\r\n        { key: 3, label: `部门`, visible: true },\r\n        { key: 4, label: `手机号码`, visible: true },\r\n        { key: 5, label: `状态`, visible: true },\r\n        { key: 6, label: `创建时间`, visible: true }\r\n      ],\r\n      // 表单校验\r\n      rules: {\r\n        userName: [\r\n          { required: true, message: \"用户名称不能为空\", trigger: \"blur\" },\r\n          { min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }\r\n        ],\r\n        nickName: [\r\n          { required: true, message: \"用户昵称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        password: [\r\n          { required: true, message: \"用户密码不能为空\", trigger: \"blur\" },\r\n          { min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }\r\n        ],\r\n        email: [\r\n          {\r\n            type: \"email\",\r\n            message: \"请输入正确的邮箱地址\",\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        phonenumber: [\r\n          {\r\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\r\n            message: \"请输入正确的手机号码\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    // 根据名称筛选部门树\r\n    deptName(val) {\r\n      this.$refs.tree.filter(val);\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getDeptTree();\r\n    this.getConfigKey(\"sys.user.initPassword\").then(response => {\r\n      this.initPassword = response.msg;\r\n    });\r\n  },\r\n  methods: {\r\n    /** 查询用户列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listUser(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n          this.userList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    /** 查询部门下拉树结构 */\r\n    getDeptTree() {\r\n      deptTreeSelect().then(response => {\r\n        this.deptOptions = response.data;\r\n      });\r\n    },\r\n    // 筛选节点\r\n    filterNode(value, data) {\r\n      if (!value) return true;\r\n      return data.label.indexOf(value) !== -1;\r\n    },\r\n    // 节点单击事件\r\n    handleNodeClick(data) {\r\n      this.queryParams.deptId = data.id;\r\n      this.handleQuery();\r\n    },\r\n    // 用户状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.userName + '\"用户吗？').then(function() {\r\n        return changeUserStatus(row.userId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function() {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        userId: undefined,\r\n        deptId: undefined,\r\n        userName: undefined,\r\n        nickName: undefined,\r\n        password: undefined,\r\n        phonenumber: undefined,\r\n        email: undefined,\r\n        sex: undefined,\r\n        status: \"0\",\r\n        remark: undefined,\r\n        postIds: [],\r\n        roleIds: []\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.queryParams.deptId = undefined;\r\n      this.$refs.tree.setCurrentKey(null);\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.userId);\r\n      this.single = selection.length != 1;\r\n      this.multiple = !selection.length;\r\n    },\r\n    // 更多操作触发\r\n    handleCommand(command, row) {\r\n      switch (command) {\r\n        case \"handleResetPwd\":\r\n          this.handleResetPwd(row);\r\n          break;\r\n        case \"handleAuthRole\":\r\n          this.handleAuthRole(row);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      getUser().then(response => {\r\n        this.postOptions = response.posts;\r\n        this.roleOptions = response.roles;\r\n        this.open = true;\r\n        this.title = \"添加用户\";\r\n        this.form.password = this.initPassword;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const userId = row.userId || this.ids;\r\n      getUser(userId).then(response => {\r\n        this.form = response.data;\r\n        this.postOptions = response.posts;\r\n        this.roleOptions = response.roles;\r\n        this.$set(this.form, \"postIds\", response.postIds);\r\n        this.$set(this.form, \"roleIds\", response.roleIds);\r\n        this.open = true;\r\n        this.title = \"修改用户\";\r\n        this.form.password = \"\";\r\n      });\r\n    },\r\n    /** 重置密码按钮操作 */\r\n    handleResetPwd(row) {\r\n      this.$prompt('请输入\"' + row.userName + '\"的新密码', \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        closeOnClickModal: false,\r\n        inputPattern: /^.{5,20}$/,\r\n        inputErrorMessage: \"用户密码长度必须介于 5 和 20 之间\"\r\n      }).then(({ value }) => {\r\n          resetUserPwd(row.userId, value).then(response => {\r\n            this.$modal.msgSuccess(\"修改成功，新密码是：\" + value);\r\n          });\r\n        }).catch(() => {});\r\n    },\r\n    /** 分配角色操作 */\r\n    handleAuthRole: function(row) {\r\n      const userId = row.userId;\r\n      this.$router.push(\"/system/user-auth/role/\" + userId);\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.userId != undefined) {\r\n            updateUser(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addUser(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const userIds = row.userId || this.ids;\r\n      this.$modal.confirm('是否确认删除用户编号为\"' + userIds + '\"的数据项？').then(function() {\r\n        return delUser(userIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/user/export', {\r\n        ...this.queryParams\r\n      }, `user_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 导入按钮操作 */\r\n    handleImport() {\r\n      this.upload.title = \"用户导入\";\r\n      this.upload.open = true;\r\n    },\r\n    /** 下载模板操作 */\r\n    importTemplate() {\r\n      this.download('system/user/importTemplate', {\r\n      }, `user_template_${new Date().getTime()}.xlsx`)\r\n    },\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true;\r\n    },\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      this.upload.open = false;\r\n      this.upload.isUploading = false;\r\n      this.$refs.upload.clearFiles();\r\n      this.$alert(\"<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>\" + response.msg + \"</div>\", \"导入结果\", { dangerouslyUseHTMLString: true });\r\n      this.getList();\r\n    },\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit();\r\n    }\r\n  }\r\n};\r\n</script>"]}]}