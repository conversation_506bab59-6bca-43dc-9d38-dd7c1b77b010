<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.LabTestingRelationMapper">
    
    <resultMap type="LabTestingRelation" id="LabTestingRelationResult">
        <result property="id"    column="id"    />
        <result property="labId"    column="lab_id"    />
        <result property="testingId"    column="testing_id"    />
        <result property="price"    column="price"    />
        <result property="cycle"    column="cycle"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectLabTestingRelationVo">
        select id, lab_id, testing_id, price, cycle, remark, create_time, update_time from lab_testing_relation
    </sql>

    <select id="selectLabTestingRelationList" parameterType="LabTestingRelation" resultMap="LabTestingRelationResult">
        <include refid="selectLabTestingRelationVo"/>
        <where>  
            <if test="labId != null "> and lab_id = #{labId}</if>
            <if test="testingId != null "> and testing_id = #{testingId}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="cycle != null "> and cycle = #{cycle}</if>
        </where>
    </select>
    
    <select id="selectLabTestingRelationById" parameterType="Long" resultMap="LabTestingRelationResult">
        <include refid="selectLabTestingRelationVo"/>
        where id = #{id}
    </select>
    
    <select id="selectLabTestingRelationByLabIdAndTestingId" resultMap="LabTestingRelationResult">
        <include refid="selectLabTestingRelationVo"/>
        where lab_id = #{labId} and testing_id = #{testingId}
    </select>
    
    <select id="selectLabTestingRelationByLabId" parameterType="Long" resultMap="LabTestingRelationResult">
        <include refid="selectLabTestingRelationVo"/>
        where lab_id = #{labId}
    </select>
    
    <select id="selectLabTestingRelationByTestingId" parameterType="Long" resultMap="LabTestingRelationResult">
        <include refid="selectLabTestingRelationVo"/>
        where testing_id = #{testingId}
    </select>
        
    <insert id="insertLabTestingRelation" parameterType="LabTestingRelation" useGeneratedKeys="true" keyProperty="id">
        insert into lab_testing_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="labId != null">lab_id,</if>
            <if test="testingId != null">testing_id,</if>
            <if test="price != null">price,</if>
            <if test="cycle != null">cycle,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="labId != null">#{labId},</if>
            <if test="testingId != null">#{testingId},</if>
            <if test="price != null">#{price},</if>
            <if test="cycle != null">#{cycle},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
    
    <insert id="batchInsertLabTestingRelation" parameterType="java.util.List">
        insert into lab_testing_relation (lab_id, testing_id, price, cycle, remark, create_time, update_time) values
        <foreach collection="list" item="item" separator=",">
            (#{item.labId}, #{item.testingId}, #{item.price}, #{item.cycle}, #{item.remark}, 
            <if test="item.createTime != null">#{item.createTime}</if>
            <if test="item.createTime == null">now()</if>, 
            <if test="item.updateTime != null">#{item.updateTime}</if>
            <if test="item.updateTime == null">now()</if>)
        </foreach>
    </insert>

    <update id="updateLabTestingRelation" parameterType="LabTestingRelation">
        update lab_testing_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="labId != null">lab_id = #{labId},</if>
            <if test="testingId != null">testing_id = #{testingId},</if>
            <if test="price != null">price = #{price},</if>
            <if test="cycle != null">cycle = #{cycle},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLabTestingRelationById" parameterType="Long">
        delete from lab_testing_relation where id = #{id}
    </delete>

    <delete id="deleteLabTestingRelationByIds" parameterType="String">
        delete from lab_testing_relation where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteLabTestingRelationByLabId" parameterType="Long">
        delete from lab_testing_relation where lab_id = #{labId}
    </delete>
    
    <delete id="deleteLabTestingRelationByTestingId" parameterType="Long">
        delete from lab_testing_relation where testing_id = #{testingId}
    </delete>
</mapper>