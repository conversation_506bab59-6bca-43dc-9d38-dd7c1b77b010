{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\dashboard\\LineChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\dashboard\\LineChart.vue", "mtime": 1750311962946}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_resize", "_interopRequireDefault", "_default", "exports", "default", "mixins", "resize", "props", "className", "type", "String", "width", "height", "autoResize", "Boolean", "chartData", "Object", "required", "data", "chart", "watch", "deep", "handler", "val", "setOptions", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "init", "$el", "_ref", "arguments", "length", "undefined", "expectedData", "actualData", "setOption", "xAxis", "boundaryGap", "axisTick", "show", "grid", "left", "right", "bottom", "top", "containLabel", "tooltip", "trigger", "axisPointer", "padding", "yAxis", "legend", "series", "name", "itemStyle", "normal", "color", "lineStyle", "smooth", "animationDuration", "animationEasing", "areaStyle"], "sources": ["src/views/dashboard/LineChart.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\" />\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nrequire('echarts/theme/macarons') // echarts theme\r\nimport resize from './mixins/resize'\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '350px'\r\n    },\r\n    autoResize: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chartData: {\r\n      type: Object,\r\n      required: true\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  watch: {\r\n    chartData: {\r\n      deep: true,\r\n      handler(val) {\r\n        this.setOptions(val)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n      this.setOptions(this.chartData)\r\n    },\r\n    setOptions({ expectedData, actualData } = {}) {\r\n      this.chart.setOption({\r\n        xAxis: {\r\n          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],\r\n          boundaryGap: false,\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        grid: {\r\n          left: 10,\r\n          right: 10,\r\n          bottom: 20,\r\n          top: 30,\r\n          containLabel: true\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'cross'\r\n          },\r\n          padding: [5, 10]\r\n        },\r\n        yAxis: {\r\n          axisTick: {\r\n            show: false\r\n          }\r\n        },\r\n        legend: {\r\n          data: ['expected', 'actual']\r\n        },\r\n        series: [{\r\n          name: 'expected', itemStyle: {\r\n            normal: {\r\n              color: '#FF005A',\r\n              lineStyle: {\r\n                color: '#FF005A',\r\n                width: 2\r\n              }\r\n            }\r\n          },\r\n          smooth: true,\r\n          type: 'line',\r\n          data: expectedData,\r\n          animationDuration: 2800,\r\n          animationEasing: 'cubicInOut'\r\n        },\r\n        {\r\n          name: 'actual',\r\n          smooth: true,\r\n          type: 'line',\r\n          itemStyle: {\r\n            normal: {\r\n              color: '#3888fa',\r\n              lineStyle: {\r\n                color: '#3888fa',\r\n                width: 2\r\n              },\r\n              areaStyle: {\r\n                color: '#f3f8ff'\r\n              }\r\n            }\r\n          },\r\n          data: actualData,\r\n          animationDuration: 2800,\r\n          animationEasing: 'quadraticOut'\r\n        }]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;AAKA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;AADAA,OAAA;AAAA,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAGA;EACAC,MAAA,GAAAC,eAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAO,KAAA;MACAF,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAQ,MAAA;MACAH,IAAA,EAAAC,MAAA;MACAN,OAAA;IACA;IACAS,UAAA;MACAJ,IAAA,EAAAK,OAAA;MACAV,OAAA;IACA;IACAW,SAAA;MACAN,IAAA,EAAAO,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAL,SAAA;MACAM,IAAA;MACAC,OAAA,WAAAA,QAAAC,GAAA;QACA,KAAAC,UAAA,CAAAD,GAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,SAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,UAAAV,KAAA;MACA;IACA;IACA,KAAAA,KAAA,CAAAW,OAAA;IACA,KAAAX,KAAA;EACA;EACAY,OAAA;IACAH,SAAA,WAAAA,UAAA;MACA,KAAAT,KAAA,GAAAtB,OAAA,CAAAmC,IAAA,MAAAC,GAAA;MACA,KAAAT,UAAA,MAAAT,SAAA;IACA;IACAS,UAAA,WAAAA,WAAA;MAAA,IAAAU,IAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;QAAAG,YAAA,GAAAJ,IAAA,CAAAI,YAAA;QAAAC,UAAA,GAAAL,IAAA,CAAAK,UAAA;MACA,KAAApB,KAAA,CAAAqB,SAAA;QACAC,KAAA;UACAvB,IAAA;UACAwB,WAAA;UACAC,QAAA;YACAC,IAAA;UACA;QACA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAC,GAAA;UACAC,YAAA;QACA;QACAC,OAAA;UACAC,OAAA;UACAC,WAAA;YACA5C,IAAA;UACA;UACA6C,OAAA;QACA;QACAC,KAAA;UACAZ,QAAA;YACAC,IAAA;UACA;QACA;QACAY,MAAA;UACAtC,IAAA;QACA;QACAuC,MAAA;UACAC,IAAA;UAAAC,SAAA;YACAC,MAAA;cACAC,KAAA;cACAC,SAAA;gBACAD,KAAA;gBACAlD,KAAA;cACA;YACA;UACA;UACAoD,MAAA;UACAtD,IAAA;UACAS,IAAA,EAAAoB,YAAA;UACA0B,iBAAA;UACAC,eAAA;QACA,GACA;UACAP,IAAA;UACAK,MAAA;UACAtD,IAAA;UACAkD,SAAA;YACAC,MAAA;cACAC,KAAA;cACAC,SAAA;gBACAD,KAAA;gBACAlD,KAAA;cACA;cACAuD,SAAA;gBACAL,KAAA;cACA;YACA;UACA;UACA3C,IAAA,EAAAqB,UAAA;UACAyB,iBAAA;UACAC,eAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}