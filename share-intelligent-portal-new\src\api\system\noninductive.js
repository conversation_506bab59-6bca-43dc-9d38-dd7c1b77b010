/*
 * @Author: zhc
 * @Date: 2023-02-07 09:59:21
 * @LastEditTime: 2023-02-16 15:01:45
 * @Description:
 * @LastEditors: zhc
 */
/*
 * @Author: zhc
 * @Date: 2023-02-06 14:52:03
 * @LastEditTime: 2023-02-06 17:54:33
 * @Description:
 *
 * @LastEditors: zhc
 */
import request from "@/utils/request";

// 个人中心-查询列表
export function listNoninductive(params) {
  return request({
    url: "/system/insentience-cash/my-list",
    method: "get",
    params: params,
  });
}

// 查询公告详细
export function getNoninductiveDetail(id) {
  return request({
    url: "/system/insentience-cash/my-detail",
    method: "get",
    params: { id: id },
  });
}
// 撤回
export function revocationNoninductive(id) {
  return request({
    url: "/system/insentience-cash/revocation",
    method: "post",
    data: { id: id },
  });
}
// 修改
export function editNoninductive(params) {
  return request({
    url: "/system/insentience-cash/edit",
    method: "post",
    data: params,
  });
}

// 删除消息通知
export function deleteInfo(data) {
  return request({
    url: "/system/info/remove",
    method: "post",
    data: data,
  });
}

// 修改公告
export function updateNotice(data) {
  return request({
    url: "/system/notice",
    method: "put",
    data: data,
  });
}

// 删除公告
export function delNotice(noticeId) {
  return request({
    url: "/system/notice/" + noticeId,
    method: "delete",
  });
}
