{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\expertLibrary\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\expertLibrary\\detail.vue", "mtime": 1750311962987}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_purchaseSales", "require", "_login", "_apathy", "_vuex", "_index", "_cryptoJs", "_interopRequireDefault", "secret<PERSON>ey", "_default", "exports", "default", "data", "loading", "showBtn", "created", "init", "methods", "_this", "getExpertDetail", "id", "$route", "query", "then", "res", "key", "CryptoJS", "enc", "Utf8", "parse", "decrypt", "AES", "mode", "ECB", "padding", "pad", "Pkcs7", "JSON", "stringify", "token", "getInfo", "catch", "_this2", "createById", "user", "userId", "goChat", "_this3", "$confirm", "confirmButtonText", "cancelButtonText", "type", "$store", "dispatch", "location", "href", "getCustomerServicerInfo", "code", "routeData", "$router", "resolve", "path", "window", "open", "$message", "message", "goIntention", "_this4", "getCompanyInfoByLoginInfo", "getCheckSubmit", "resourceType", "title", "expertName", "computed", "_objectSpread2", "mapGetters"], "sources": ["src/views/purchaseSales/component/expertLibrary/detail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"expert-library-detail\">\r\n    <!-- banner图 -->\r\n    <div class=\"expert-detail-banner\">\r\n      <img\r\n        src=\"../../../../assets/expertLibrary/expertLibraryDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"expert-detail-title-box\">\r\n      <div class=\"expert-detail-divider\"></div>\r\n      <div class=\"expert-detail-title\">专家详情</div>\r\n      <div class=\"expert-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"expert-detail-content\">\r\n      <div class=\"expert-detail-headline\">\r\n        <div class=\"expert-detail-headline-img\">\r\n          <img v-if=\"data.headPortrait\" :src=\"data.headPortrait\" alt=\"\" />\r\n          <img\r\n            v-else\r\n            src=\"../../../../assets/expertLibrary/defaultImg.png\"\r\n            alt=\"\"\r\n          />\r\n        </div>\r\n        <div class=\"expert-detail-headline-info\">\r\n          <div class=\"headline-info-title\">{{ data.expertName }}</div>\r\n          <div class=\"headline-info-box\">\r\n            <span class=\"headline-info-laber\">单位：</span>\r\n            <span class=\"headline-info-description\">\r\n              {{ data.workUnit }}\r\n            </span>\r\n          </div>\r\n          <div class=\"headline-info-box\">\r\n            <span class=\"headline-info-laber\">职位：</span>\r\n            <span class=\"headline-info-description\">{{ data.post }}</span>\r\n          </div>\r\n          <div class=\"headline-info-box\">\r\n            <span class=\"headline-info-laber\">研究方向：</span>\r\n            <span class=\"headline-info-description\">{{\r\n              data.researchDirection\r\n            }}</span>\r\n          </div>\r\n          <div class=\"headline-content-btn\">\r\n            <el-button\r\n              v-if=\"showBtn\"\r\n              class=\"headline-btn-style intention-btn\"\r\n              @click=\"goIntention\"\r\n              >我有意向</el-button\r\n            >\r\n            <el-button @click=\"goChat\" icon=\"el-icon-chat-dot-round\"\r\n              >在线沟通</el-button\r\n            >\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"expert-detail-introduce\">\r\n        <div class=\"introduce-content\">\r\n          <div class=\"introduction-line\"></div>\r\n          <div class=\"introduction-title\">专家介绍</div>\r\n        </div>\r\n        <div class=\"introduction-text-content\">\r\n          <div\r\n            v-html=\"data.introduce\"\r\n            class=\"introduction-text ql-editor\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getExpertDetail, getCheckSubmit } from \"@/api/purchaseSales\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { getCompanyInfoByLoginInfo } from \"@/api/apathy\";\r\nimport { mapGetters } from \"vuex\";\r\nimport { getCustomerServicerInfo } from \"@/api/expertLibrary/index\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n      showBtn: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      getExpertDetail({ id: this.$route.query.id })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          if (!this.token) {\r\n            this.showBtn = true;\r\n          } else {\r\n            this.getInfo();\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 判断此资源是不是自己发布的\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (this.data.createById === res.user.userId) {\r\n          this.showBtn = false;\r\n        } else {\r\n          this.showBtn = true;\r\n        }\r\n      });\r\n    },\r\n    goChat() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n\r\n      getCustomerServicerInfo().then((res) => {\r\n        if (res.code == 200) {\r\n          let routeData = this.$router.resolve({\r\n            path: \"/user/im\",\r\n            query: {\r\n              userId: res.data.id,\r\n            },\r\n          });\r\n          window.open(routeData.href, \"_blank\");\r\n        } else {\r\n          this.$message({\r\n            type: \"warning\",\r\n            message: \"获取客服信息失败,请稍后再试\",\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 跳到我有意向页面\r\n    goIntention() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      // 是否加入企业\r\n      this.loading = true;\r\n      getCompanyInfoByLoginInfo()\r\n        .then((res) => {\r\n          if (res.data) {\r\n            // 是否对此资源提交过意向\r\n            getCheckSubmit({\r\n              id: this.$route.query.id,\r\n              resourceType: \"resource_expet\",\r\n            })\r\n              .then((res) => {\r\n                this.loading = false;\r\n                // true 提交过  false未提交过\r\n                if (res.data) {\r\n                  this.$message({\r\n                    type: \"warning\",\r\n                    message: \"已经提交过了哦！\",\r\n                  });\r\n                } else {\r\n                  let routeData = this.$router.resolve({\r\n                    path: \"/addIntention\",\r\n                    query: {\r\n                      id: this.$route.query.id,\r\n                      type: \"resource_expet\",\r\n                      title: this.data.expertName,\r\n                    },\r\n                  });\r\n                  window.open(routeData.href, \"_blank\");\r\n                }\r\n              })\r\n              .catch(() => {\r\n                this.loading = false;\r\n              });\r\n          } else {\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"warning\",\r\n              message: \"必须加入企业才可提交我有意向\",\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.expert-library-detail {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  padding-bottom: 60px;\r\n  .expert-detail-banner {\r\n    width: 100%;\r\n    height: 25.92vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .expert-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .expert-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .expert-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .expert-detail-content {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n    padding: 60px 60px 124px;\r\n    .expert-detail-headline {\r\n      display: flex;\r\n      .expert-detail-headline-img {\r\n        width: 240px;\r\n        height: 240px;\r\n        margin-right: 40px;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .expert-detail-headline-info {\r\n        flex: 1;\r\n        .headline-info-title {\r\n          max-width: 792px;\r\n          font-size: 32px;\r\n          font-family: PingFangSC-Semibold, PingFang SC;\r\n          font-weight: 600;\r\n          color: #333;\r\n          line-height: 32px;\r\n          padding-bottom: 20px;\r\n          word-wrap: break-word;\r\n        }\r\n        .headline-info-box {\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          line-height: 32px;\r\n          .headline-info-laber {\r\n            width: 70px;\r\n            color: #666;\r\n          }\r\n          .headline-info-description {\r\n            flex: 1;\r\n            max-width: 712px;\r\n            color: #333;\r\n            word-wrap: break-word;\r\n          }\r\n        }\r\n        .headline-content-btn {\r\n          padding-top: 52px;\r\n          .headline-btn-style {\r\n            width: 100px;\r\n            height: 32px;\r\n            border-radius: 4px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            padding: 8px 11px;\r\n          }\r\n          .intention-btn {\r\n            background: #21c9b8;\r\n            color: #fff;\r\n          }\r\n          .communication-btn {\r\n            border: 1px solid #21c9b8;\r\n            color: #21c9b8;\r\n            background: transparent;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .expert-detail-introduce {\r\n      padding-top: 60px;\r\n      .introduce-content {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-bottom: 40px;\r\n        .introduction-line {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .introduction-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n      .introduction-text-content {\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.expert-library-detail {\r\n  .introduction-text-content {\r\n    .introduction-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AAwEA,IAAAA,cAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAC,sBAAA,CAAAN,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAO,SAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAD,IAAA;MACAE,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,IAAA,WAAAA,KAAA;MAAA,IAAAE,KAAA;MACA,KAAAL,OAAA;MACA,IAAAM,8BAAA;QAAAC,EAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAF;MAAA,GACAG,IAAA,WAAAC,GAAA;QACA,IAAAC,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAArB,SAAA;QACA,IAAAsB,OAAA,GAAAJ,iBAAA,CAAAK,GAAA,CAAAD,OAAA,CAAAN,GAAA,EAAAC,GAAA;UACAO,IAAA,EAAAN,iBAAA,CAAAM,IAAA,CAAAC,GAAA;UACAC,OAAA,EAAAR,iBAAA,CAAAS,GAAA,CAAAC;QACA;QACAZ,GAAA,GAAAa,IAAA,CAAAR,KAAA,CAAAH,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAU,SAAA,CAAAR,OAAA;QAEAZ,KAAA,CAAAL,OAAA;QACAK,KAAA,CAAAN,IAAA,GAAAY,GAAA,CAAAZ,IAAA;QACA,KAAAM,KAAA,CAAAqB,KAAA;UACArB,KAAA,CAAAJ,OAAA;QACA;UACAI,KAAA,CAAAsB,OAAA;QACA;MACA,GACAC,KAAA;QACAvB,KAAA,CAAAL,OAAA;MACA;IACA;IACA;IACA2B,OAAA,WAAAA,QAAA;MAAA,IAAAE,MAAA;MACA,IAAAF,cAAA,IAAAjB,IAAA,WAAAC,GAAA;QACA,IAAAkB,MAAA,CAAA9B,IAAA,CAAA+B,UAAA,KAAAnB,GAAA,CAAAoB,IAAA,CAAAC,MAAA;UACAH,MAAA,CAAA5B,OAAA;QACA;UACA4B,MAAA,CAAA5B,OAAA;QACA;MACA;IACA;IACAgC,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAAR,KAAA;QACA,KAAAS,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAA5B,IAAA;UACAwB,MAAA,CAAAK,MAAA,CAAAC,QAAA,WAAA9B,IAAA;YACA+B,QAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;MAEA,IAAAC,8BAAA,IAAAjC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAiC,IAAA;UACA,IAAAC,SAAA,GAAAX,MAAA,CAAAY,OAAA,CAAAC,OAAA;YACAC,IAAA;YACAvC,KAAA;cACAuB,MAAA,EAAArB,GAAA,CAAAZ,IAAA,CAAAQ;YACA;UACA;UACA0C,MAAA,CAAAC,IAAA,CAAAL,SAAA,CAAAH,IAAA;QACA;UACAR,MAAA,CAAAiB,QAAA;YACAb,IAAA;YACAc,OAAA;UACA;QACA;MACA;IACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA;MACA,UAAA5B,KAAA;QACA,KAAAS,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAA5B,IAAA;UACA4C,MAAA,CAAAf,MAAA,CAAAC,QAAA,WAAA9B,IAAA;YACA+B,QAAA,CAAAC,IAAA;UACA;QACA;QACA;MACA;MACA;MACA,KAAA1C,OAAA;MACA,IAAAuD,iCAAA,IACA7C,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAZ,IAAA;UACA;UACA,IAAAyD,6BAAA;YACAjD,EAAA,EAAA+C,MAAA,CAAA9C,MAAA,CAAAC,KAAA,CAAAF,EAAA;YACAkD,YAAA;UACA,GACA/C,IAAA,WAAAC,GAAA;YACA2C,MAAA,CAAAtD,OAAA;YACA;YACA,IAAAW,GAAA,CAAAZ,IAAA;cACAuD,MAAA,CAAAH,QAAA;gBACAb,IAAA;gBACAc,OAAA;cACA;YACA;cACA,IAAAP,SAAA,GAAAS,MAAA,CAAAR,OAAA,CAAAC,OAAA;gBACAC,IAAA;gBACAvC,KAAA;kBACAF,EAAA,EAAA+C,MAAA,CAAA9C,MAAA,CAAAC,KAAA,CAAAF,EAAA;kBACA+B,IAAA;kBACAoB,KAAA,EAAAJ,MAAA,CAAAvD,IAAA,CAAA4D;gBACA;cACA;cACAV,MAAA,CAAAC,IAAA,CAAAL,SAAA,CAAAH,IAAA;YACA;UACA,GACAd,KAAA;YACA0B,MAAA,CAAAtD,OAAA;UACA;QACA;UACAsD,MAAA,CAAAtD,OAAA;UACAsD,MAAA,CAAAH,QAAA;YACAb,IAAA;YACAc,OAAA;UACA;QACA;MACA,GACAxB,KAAA;QACA0B,MAAA,CAAAtD,OAAA;MACA;IACA;EACA;EACA4D,QAAA,MAAAC,cAAA,CAAA/D,OAAA,MACA,IAAAgE,gBAAA;AAEA", "ignoreList": []}]}