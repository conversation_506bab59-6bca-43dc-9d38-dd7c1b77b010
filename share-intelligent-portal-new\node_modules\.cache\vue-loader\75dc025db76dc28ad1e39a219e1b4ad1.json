{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\Breadcrumb\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\components\\Breadcrumb\\index.vue", "mtime": 1750311962789}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxldmVsTGlzdDogbnVsbCwNCiAgICB9Ow0KICB9LA0KICB3YXRjaDogew0KICAgICRyb3V0ZShyb3V0ZSkgew0KICAgICAgLy8gaWYgeW91IGdvIHRvIHRoZSByZWRpcmVjdCBwYWdlLCBkbyBub3QgdXBkYXRlIHRoZSBicmVhZGNydW1icw0KICAgICAgaWYgKHJvdXRlLnBhdGguc3RhcnRzV2l0aCgiL3JlZGlyZWN0LyIpKSB7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCiAgICAgIHRoaXMuZ2V0QnJlYWRjcnVtYigpOw0KICAgIH0sDQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRCcmVhZGNydW1iKCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXRCcmVhZGNydW1iKCkgew0KICAgICAgLy8gb25seSBzaG93IHJvdXRlcyB3aXRoIG1ldGEudGl0bGUNCiAgICAgIGxldCBtYXRjaGVkID0gdGhpcy4kcm91dGUubWF0Y2hlZC5maWx0ZXIoDQogICAgICAgIChpdGVtKSA9PiBpdGVtLm1ldGEgJiYgaXRlbS5tZXRhLnRpdGxlDQogICAgICApOw0KICAgICAgY29uc3QgZmlyc3QgPSBtYXRjaGVkWzBdOw0KDQogICAgICBpZiAoIXRoaXMuaXNEYXNoYm9hcmQoZmlyc3QpKSB7DQogICAgICAgIG1hdGNoZWQgPSBbeyBwYXRoOiAiL2luZGV4IiwgbWV0YTogeyB0aXRsZTogIummlumhtSIgfSB9XS5jb25jYXQobWF0Y2hlZCk7DQogICAgICB9DQoNCiAgICAgIHRoaXMubGV2ZWxMaXN0ID0gbWF0Y2hlZC5maWx0ZXIoDQogICAgICAgIChpdGVtKSA9PiBpdGVtLm1ldGEgJiYgaXRlbS5tZXRhLnRpdGxlICYmIGl0ZW0ubWV0YS5icmVhZGNydW1iICE9PSBmYWxzZQ0KICAgICAgKTsNCiAgICB9LA0KICAgIGlzRGFzaGJvYXJkKHJvdXRlKSB7DQogICAgICBjb25zdCBuYW1lID0gcm91dGUgJiYgcm91dGUubmFtZTsNCiAgICAgIGlmICghbmFtZSkgew0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgICByZXR1cm4gbmFtZS50cmltKCkgPT09ICJJbmRleCI7DQogICAgfSwNCiAgICBoYW5kbGVMaW5rKGl0ZW0pIHsNCiAgICAgIGNvbnN0IHsgcmVkaXJlY3QsIHBhdGggfSA9IGl0ZW07DQogICAgICBpZiAocmVkaXJlY3QpIHsNCiAgICAgICAgdGhpcy4kcm91dGVyLnB1c2gocmVkaXJlY3QpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaChwYXRoKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Breadcrumb", "sourcesContent": ["<template>\r\n  <el-breadcrumb class=\"app-breadcrumb\" separator=\"/\">\r\n    <transition-group name=\"breadcrumb\">\r\n      <el-breadcrumb-item v-for=\"(item, index) in levelList\" :key=\"item.path\">\r\n        <span\r\n          v-if=\"item.redirect === 'noRedirect' || index == levelList.length - 1\"\r\n          class=\"no-redirect\"\r\n          >{{ item.meta.title }}</span\r\n        >\r\n        <a v-else @click.prevent=\"handleLink(item)\">{{ item.meta.title }}</a>\r\n      </el-breadcrumb-item>\r\n    </transition-group>\r\n  </el-breadcrumb>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      levelList: null,\r\n    };\r\n  },\r\n  watch: {\r\n    $route(route) {\r\n      // if you go to the redirect page, do not update the breadcrumbs\r\n      if (route.path.startsWith(\"/redirect/\")) {\r\n        return;\r\n      }\r\n      this.getBreadcrumb();\r\n    },\r\n  },\r\n  created() {\r\n    this.getBreadcrumb();\r\n  },\r\n  methods: {\r\n    getBreadcrumb() {\r\n      // only show routes with meta.title\r\n      let matched = this.$route.matched.filter(\r\n        (item) => item.meta && item.meta.title\r\n      );\r\n      const first = matched[0];\r\n\r\n      if (!this.isDashboard(first)) {\r\n        matched = [{ path: \"/index\", meta: { title: \"首页\" } }].concat(matched);\r\n      }\r\n\r\n      this.levelList = matched.filter(\r\n        (item) => item.meta && item.meta.title && item.meta.breadcrumb !== false\r\n      );\r\n    },\r\n    isDashboard(route) {\r\n      const name = route && route.name;\r\n      if (!name) {\r\n        return false;\r\n      }\r\n      return name.trim() === \"Index\";\r\n    },\r\n    handleLink(item) {\r\n      const { redirect, path } = item;\r\n      if (redirect) {\r\n        this.$router.push(redirect);\r\n        return;\r\n      }\r\n      this.$router.push(path);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-breadcrumb.el-breadcrumb {\r\n  display: inline-block;\r\n  font-size: 14px;\r\n  line-height: 50px;\r\n  margin-left: 8px;\r\n\r\n  .no-redirect {\r\n    color: #97a8be;\r\n    cursor: text;\r\n  }\r\n}\r\n</style>\r\n"]}]}