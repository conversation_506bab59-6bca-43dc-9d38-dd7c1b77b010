<template>
  <div class="content">
    <div class="content_banner">车间共享</div>
    <div class="card-container">
      <div class="content_top" v-if="deviceMenuList && deviceMenuList.length > 0">
        <div class="content_top_item tr2" :class="currentMenu == item.dictValue ? 'active' : ''"
          v-for="(item, index) in deviceMenuList" :key="index" @click="handleClick(item.dictValue)">
          <div>
            <div style="display: flex; justify-content: center">
              <div class="imgStyle">
                <img :src="currentMenu == item.dictValue
                    ? deviceMenuIconList[index].hoverUrl
                    : deviceMenuIconList[index].url
                  " alt="" />
              </div>
            </div>
            <div :class="currentMenu == item.dictValue ? 'titleActive' : 'title'">
              {{ item.dictLabel }}
            </div>
          </div>
        </div>
      </div>
      <div class="content_bottom" v-loading="loading" v-if="deviceDataList && deviceDataList.length > 0">
        <div class="content_bottom_item" v-for="(item, index) in deviceDataList" :key="index"
          @click="workShopDetail(item.id)">
          <span class="bottom"></span>
          <span class="right"></span>
          <span class="top"></span>
          <span class="left"></span>
          <div class="imgStyle">
            <img style="width: 100%; height: 100%" :src="item.images
              ? item.images.split(',')[0]
              : require('../../../assets/device/ceshi.png')
              " alt="" />
          </div>
          <div class="title">
            {{ item.name }}
          </div>
          <div class="companyStyle">
            <img src="../../../assets/device/company_icon.png" alt="" />
            <div class="companyName">{{ item.company }}</div>
          </div>
        </div>
      </div>
      <div class="none-class" v-else>
        <el-image style="width: 160px; height: 160px" :src="require('@/assets/user/none.png')" :fit="fit"></el-image>
        <div class="text">暂无数据</div>
      </div>
      <!-- 分页 -->
      <div class="pageStyle">
        <el-pagination v-if="deviceDataList && deviceDataList.length > 0" background layout="prev, pager, next"
          class="activity-pagination" :page-size="pageSize" :current-page="pageNum" :total="total"
          @size-change="handleSizeChange" @current-change="handleCurrentChange">
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { workListData } from "@/api/manufacturingSharing";
import { listData } from "@/api/system/dict/data";

export default {
  name: "deviceSharing",
  data() {
    return {
      fit: "cover",
      loading: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      currentMenu: '',
      deviceMenuList: [],
      deviceDataList: [
        {
          url: require("../../../assets/device/ceshi.png"),
          title: "共享制造车间",
          companyName: "北京融创科技有限公司",
        },
        {
          url: require("../../../assets/device/ceshi.png"),
          title: "共享制造车间",
          companyName: "北京融创科技有限公司",
        },
        {
          url: require("../../../assets/device/ceshi.png"),
          title: "共享制造车间",
          companyName: "北京融创科技有限公司",
        },
        {
          url: require("../../../assets/device/ceshi.png"),
          title: "共享制造车间",
          companyName: "北京融创科技有限公司",
        },
        {
          url: require("../../../assets/device/ceshi.png"),
          title: "共享制造车间",
          companyName: "北京融创科技有限公司",
        },
        {
          url: require("../../../assets/device/ceshi.png"),
          title: "共享制造车间",
          companyName: "北京融创科技有限公司",
        },
        {
          url: require("../../../assets/device/ceshi.png"),
          title: "共享制造车间",
          companyName: "北京融创科技有限公司",
        },
        {
          url: require("../../../assets/device/ceshi.png"),
          title: "共享制造车间",
          companyName: "北京融创科技有限公司",
        },
      ],
      deviceMenuIconList: [
        {
          url: require("../../../assets/device/device1.png"),
          hoverUrl: require("../../../assets/device/deviceHover1.png"),
        },
        {
          url: require("../../../assets/device/workShop1.png"),
          hoverUrl: require("../../../assets/device/workShopHover1.png"),
        },
        {
          url: require("../../../assets/device/workShop2.png"),
          hoverUrl: require("../../../assets/device/workShopHover2.png"),
        },
        {
          url: require("../../../assets/device/device3.png"),
          hoverUrl: require("../../../assets/device/deviceHover3.png"),
        },
        {
          url: require("../../../assets/device/device8.png"),
          hoverUrl: require("../../../assets/device/deviceHover8.png"),
        },
        {
          url: require("../../../assets/device/device2.png"),
          hoverUrl: require("../../../assets/device/deviceHover2.png"),
        },
        {
          url: require("../../../assets/device/device5.png"),
          hoverUrl: require("../../../assets/device/deviceHover5.png"),
        },
        {
          url: require("../../../assets/device/device4.png"),
          hoverUrl: require("../../../assets/device/deviceHover4.png"),
        },
        {
          url: require("../../../assets/device/device2.png"),
          hoverUrl: require("../../../assets/device/deviceHover2.png"),
        },
        {
          url: require("../../../assets/device/device2.png"),
          hoverUrl: require("../../../assets/device/deviceHover2.png"),
        },
      ],
    };
  },
  created() {
    this.getList();
    this.getDicts();
  },
  methods: {
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        type: this.currentMenu,
      };
      workListData(params).then((response) => {
        this.deviceDataList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleClick(index) {
      this.currentMenu = index;
      this.pageNum = 1;
      this.getList();
    },
    workShopDetail(id) {
      this.$router.push("/workShopDetail?id=" + id);
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    getDicts() {
      let params = { dictType: "workshop_type" };
      listData(params).then((response) => {
        this.deviceMenuList = response.rows;
        this.deviceMenuList.unshift({ dictValue: "", dictLabel: "全部" });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  padding-bottom: 60px;
}

.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../assets/release/banner.png");
  background-size: 100% 100%;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
  text-align: center;
  line-height: 300px;
}

.content_top {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 40px;

  .content_top_item {
    width: 110px;
    // width: 160px;
    height: 88px;
    background: #ffffff;
    box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .imgStyle {
      width: 27px;
      height: 27px;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #333333;
      margin-top: 10px;
    }

    .titleActive {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #ffffff;
      margin-top: 10px;
    }
  }

  .active {
    background-color: #0cad9d;
  }
}

.content_bottom {
  margin-top: 40px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  // justify-content: space-around;
  .content_bottom_item {
    width: 280px;
    height: 290px;
    background: #f8f8fa;
    box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.06);
    border-radius: 4px;
    border: 2px solid #ffffff;
    padding: 20px;
    cursor: pointer;
    margin-left: 25px;
    transition: all 0.35s ease-in;
    position: relative;

    span {
      position: absolute;
      z-index: 1;
      background-color: #37c9b8;
      transition: transform 0.5s ease;
    }

    .bottom,
    .top {
      height: 2px;
      left: -1px;
      right: -1px;
      transform: scaleX(0);
    }

    .left,
    .right {
      width: 2px;
      top: -1px;
      bottom: -1px;
      transform: scaleY(0);
    }

    .bottom {
      bottom: -1px;
      transform-origin: bottom right;
    }

    .right {
      right: -1px;
      transform-origin: top right;
    }

    .top {
      top: -1px;
      transform-origin: top left;
    }

    .left {
      left: -1px;
      transform-origin: bottom left;
    }

    .title {
      height: 18px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #000000;
      margin-top: 17px;
      margin-bottom: 14px;
    }

    .companyStyle {
      display: flex;
      align-items: center;
    }

    .companyName {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #7e7e7e;
      margin-left: 10px;
    }

    .imgStyle {
      width: 236px;
      height: 157px;
    }
  }

  .content_bottom_item:nth-child(4n + 1) {
    margin-left: 0;
  }

  .content_bottom_item:nth-child(n + 5) {
    margin-top: 26px;
  }

  .content_bottom_item:hover {
    background-color: #ffffff;
    box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.1);

    .top {
      transform-origin: top right;
      transform: scaleX(1);
    }

    .left {
      transform-origin: top left;
      transform: scaleY(1);
    }

    .bottom {
      transform-origin: bottom left;
      transform: scaleX(1);
    }

    .right {
      transform-origin: bottom right;
      transform: scaleY(1);
    }
  }
}

.none-class {
  text-align: center;
  padding: 8% 0;

  .text {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
  }
}

.pageStyle {
  margin-top: 61px;
  text-align: center;
}
</style>
