<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalconsole.mapper.SupplyMapper">

    <resultMap type="com.ruoyi.portalconsole.domain.vo.SupplyVO" id="SupplyResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="type"    column="type"    />
        <result property="applicationArea"    column="application_area"    />
        <result property="technologyCategory"    column="technology_category"    />
        <result property="description"    column="description"    />
        <result property="matchDemand"    column="match_demand"    />
        <result property="process"    column="process"    />
        <result property="productType"    column="product_type"    />
        <result property="cooperationType"    column="cooperation_type"    />
        <result property="organization"    column="organization"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="attachment"    column="attachment"    />
        <result property="publisher"    column="publisher"    />
        <result property="phone"    column="phone"    />
        <result property="contact"    column="contact"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="recommend"    column="recommend"    />
        <result property="onShow"    column="on_show"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="memberId" column="member_id"/>
        <result property="viewCount" column="view_count"/>

        <result property="imageUrlPath"    column="image_url_path"    />
        <result property="applicationAreaName" column="application_area_name"/>
    </resultMap>

    <sql id="selectSupplyVo">
        select a.*,b.file_full_path as image_url_path, c.application_area_name as application_area_name
        from supply a
        left join sys_file_info b on a.image_url = b.file_id
        LEFT JOIN (
            SELECT s.id as sid, GROUP_CONCAT(af.application_field_name) application_area_name  FROM supply s LEFT JOIN application_field af ON FIND_IN_SET(af.application_field_id, s.application_area) GROUP BY s.id
        ) c ON c.sid = a.id
    </sql>

    <select id="selectSupplyList" parameterType="Supply" resultMap="SupplyResult">
        <include refid="selectSupplyVo"/>
        <where>
            <if test="title != null  and title != ''"> and title like concat('%', #{title}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="applicationArea != null  and applicationArea != ''"> and application_area like concat('%', #{applicationArea}, '%')</if>
            <if test="technologyCategory != null  and technologyCategory != ''"> and technology_category like concat('%', #{technologyCategory}, '%')</if>
            <if test="matchDemand != null  and matchDemand != ''"> and match_demand = #{matchDemand}</if>
            <if test="process != null  and process != ''"> and process = #{process}</if>
            <if test="productType != null  and productType != ''"> and product_type = #{productType}</if>
            <if test="cooperationType != null  and cooperationType != ''"> and cooperation_type = #{cooperationType}</if>
            <if test="organization != null  and organization != ''"> and organization = #{organization}</if>
            <if test="publisher != null  and publisher != ''"> and publisher = #{publisher}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="auditStatus != null "> and audit_status = #{auditStatus}</if>
            <if test="onShow != null "> and on_show = #{onShow}</if>
        </where>
    </select>

    <select id="selectSupplyById" parameterType="Long" resultMap="SupplyResult">
        <include refid="selectSupplyVo"/>
        where id = #{id}
    </select>

    <insert id="insertSupply" parameterType="Supply" useGeneratedKeys="true" keyProperty="id">
        insert into supply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="type != null">type,</if>
            <if test="applicationArea != null">application_area,</if>
            <if test="technologyCategory != null">technology_category,</if>
            <if test="description != null">description,</if>
            <if test="matchDemand != null">match_demand,</if>
            <if test="process != null">process,</if>
            <if test="productType != null">product_type,</if>
            <if test="cooperationType != null">cooperation_type,</if>
            <if test="organization != null">organization,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="attachment != null">attachment,</if>
            <if test="publisher != null">publisher,</if>
            <if test="phone != null">phone,</if>
            <if test="contact != null">contact,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="recommend != null">recommend,</if>
            <if test="onShow != null">on_show,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="type != null">#{type},</if>
            <if test="applicationArea != null">#{applicationArea},</if>
            <if test="technologyCategory != null">#{technologyCategory},</if>
            <if test="description != null">#{description},</if>
            <if test="matchDemand != null">#{matchDemand},</if>
            <if test="process != null">#{process},</if>
            <if test="productType != null">#{productType},</if>
            <if test="cooperationType != null">#{cooperationType},</if>
            <if test="organization != null">#{organization},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="attachment != null">#{attachment},</if>
            <if test="publisher != null">#{publisher},</if>
            <if test="phone != null">#{phone},</if>
            <if test="contact != null">#{contact},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="recommend != null">#{recommend},</if>
            <if test="onShow != null">#{onShow},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSupply" parameterType="Supply">
        update supply
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="type != null">type = #{type},</if>
            <if test="applicationArea != null">application_area = #{applicationArea},</if>
            <if test="technologyCategory != null">technology_category = #{technologyCategory},</if>
            <if test="description != null">description = #{description},</if>
            <if test="matchDemand != null">match_demand = #{matchDemand},</if>
            <if test="process != null">process = #{process},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="cooperationType != null">cooperation_type = #{cooperationType},</if>
            <if test="organization != null">organization = #{organization},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="attachment != null">attachment = #{attachment},</if>
            <if test="publisher != null">publisher = #{publisher},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="contact != null">contact = #{contact},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="recommend != null">recommend = #{recommend},</if>
            <if test="onShow != null">on_show = #{onShow},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSupplyById" parameterType="Long">
        delete from supply where id = #{id}
    </delete>

    <delete id="deleteSupplyByIds" parameterType="String">
        delete from supply where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>