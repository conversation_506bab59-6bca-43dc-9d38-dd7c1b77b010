package com.ruoyi.im.api;

import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.im.api.domain.ImChatroomMsg;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @ProjectName: ruoyi
 * @Package: com.ruoyi.im.api
 * @ClassName: RemoteImMessageService
 * @Author: ${maguojun}
 * @Description: ${description}
 * @Date: 2022/3/16 13:52
 * @Version: 1.0
 */
@FeignClient(contextId = "remoteImMessageService", value = ServiceNameConstants.IM_SERVICE)
public interface RemoteImMessageService {

    /***
     * 发送消息
     * @param imChatroomMsg
     * @return
     */
    @PostMapping(value="im/chatroommsg/send")
    public R<Long> add(@RequestBody ImChatroomMsg imChatroomMsg);
}
