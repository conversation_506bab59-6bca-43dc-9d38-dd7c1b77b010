package com.ruoyi.sso.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.sso.service.UserMappingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户映射管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sso/user-mapping")
public class UserMappingController {
    
    private static final Logger log = LoggerFactory.getLogger(UserMappingController.class);
    
    @Autowired
    private UserMappingService userMappingService;
    
    /**
     * 获取用户权限信息
     */
    @GetMapping("/permissions")
    public R<Map<String, Object>> getUserPermissions(
            @RequestParam String username,
            @RequestParam String clientId) {
        
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(clientId)) {
            return R.fail("用户名和客户端ID不能为空");
        }
        
        try {
            Map<String, Object> permissions = userMappingService.getUserPermissions(username, clientId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("username", username);
            result.put("clientId", clientId);
            result.put("permissions", permissions);
            result.put("roles", userMappingService.getUserRoles(username, clientId));
            result.put("permissionCodes", userMappingService.getUserPermissionCodes(username, clientId));
            result.put("hasPermission", userMappingService.hasPermission(username, clientId));
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("获取用户权限失败", e);
            return R.fail("获取用户权限失败");
        }
    }
    
    /**
     * 创建用户映射
     */
    @PostMapping("/create")
    public R<String> createUserMapping(@RequestBody Map<String, Object> request) {
        
        String username = (String) request.get("username");
        String clientId = (String) request.get("clientId");
        String systemUserId = (String) request.get("systemUserId");
        
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(clientId)) {
            return R.fail("用户名和客户端ID不能为空");
        }
        
        try {
            // 构造权限信息
            Map<String, Object> permissions = new HashMap<>();
            
            Object roles = request.get("roles");
            if (roles != null) {
                permissions.put("roles", roles);
            }
            
            Object permissionCodes = request.get("permissionCodes");
            if (permissionCodes != null) {
                permissions.put("permissionCodes", permissionCodes);
            }
            
            boolean success = userMappingService.createUserMapping(username, clientId, systemUserId, permissions);
            
            if (success) {
                return R.ok("用户映射创建成功");
            } else {
                return R.fail("用户映射创建失败");
            }
            
        } catch (Exception e) {
            log.error("创建用户映射失败", e);
            return R.fail("创建用户映射失败");
        }
    }
    
    /**
     * 更新用户映射
     */
    @PutMapping("/update")
    public R<String> updateUserMapping(@RequestBody Map<String, Object> request) {
        
        String username = (String) request.get("username");
        String clientId = (String) request.get("clientId");
        
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(clientId)) {
            return R.fail("用户名和客户端ID不能为空");
        }
        
        try {
            // 构造权限信息
            Map<String, Object> permissions = new HashMap<>();
            
            Object roles = request.get("roles");
            if (roles != null) {
                permissions.put("roles", roles);
            }
            
            Object permissionCodes = request.get("permissionCodes");
            if (permissionCodes != null) {
                permissions.put("permissionCodes", permissionCodes);
            }
            
            boolean success = userMappingService.updateUserMapping(username, clientId, permissions);
            
            if (success) {
                return R.ok("用户映射更新成功");
            } else {
                return R.fail("用户映射更新失败");
            }
            
        } catch (Exception e) {
            log.error("更新用户映射失败", e);
            return R.fail("更新用户映射失败");
        }
    }
    
    /**
     * 删除用户映射
     */
    @DeleteMapping("/delete")
    public R<String> deleteUserMapping(
            @RequestParam String username,
            @RequestParam String clientId) {
        
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(clientId)) {
            return R.fail("用户名和客户端ID不能为空");
        }
        
        try {
            boolean success = userMappingService.deleteUserMapping(username, clientId);
            
            if (success) {
                return R.ok("用户映射删除成功");
            } else {
                return R.fail("用户映射删除失败");
            }
            
        } catch (Exception e) {
            log.error("删除用户映射失败", e);
            return R.fail("删除用户映射失败");
        }
    }
    
    /**
     * 批量设置用户权限
     */
    @PostMapping("/batch-permissions")
    public R<String> batchSetPermissions(@RequestBody Map<String, Object> request) {
        
        String clientId = (String) request.get("clientId");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> userPermissions = (List<Map<String, Object>>) request.get("userPermissions");
        
        if (StringUtils.isEmpty(clientId) || userPermissions == null || userPermissions.isEmpty()) {
            return R.fail("客户端ID和用户权限列表不能为空");
        }
        
        try {
            int successCount = 0;
            int totalCount = userPermissions.size();
            
            for (Map<String, Object> userPermission : userPermissions) {
                String username = (String) userPermission.get("username");
                String systemUserId = (String) userPermission.get("systemUserId");
                
                if (StringUtils.isNotEmpty(username)) {
                    Map<String, Object> permissions = new HashMap<>();
                    permissions.put("roles", userPermission.get("roles"));
                    permissions.put("permissionCodes", userPermission.get("permissionCodes"));
                    
                    boolean success = userMappingService.createUserMapping(username, clientId, systemUserId, permissions);
                    if (success) {
                        successCount++;
                    }
                }
            }
            
            return R.ok(String.format("批量设置完成，成功: %d/%d", successCount, totalCount));
            
        } catch (Exception e) {
            log.error("批量设置用户权限失败", e);
            return R.fail("批量设置用户权限失败");
        }
    }
    
    /**
     * 获取支持的权限代码列表
     */
    @GetMapping("/permission-codes/{clientId}")
    public R<Map<String, Object>> getPermissionCodes(@PathVariable String clientId) {
        
        Map<String, Object> result = new HashMap<>();
        
        if ("backend".equals(clientId)) {
            result.put("roles", Arrays.asList("admin", "system_manager", "user"));
            result.put("permissionCodes", Arrays.asList(
                "system:user:list", "system:user:add", "system:user:edit", "system:user:remove", "system:user:view",
                "system:role:list", "system:role:add", "system:role:edit", "system:role:remove",
                "system:menu:list", "system:menu:add", "system:menu:edit", "system:menu:remove",
                "system:dept:list", "system:dept:add", "system:dept:edit", "system:dept:remove"
            ));
        } else if ("market".equals(clientId)) {
            result.put("roles", Arrays.asList("admin", "market_manager", "customer"));
            result.put("permissionCodes", Arrays.asList(
                "market:product:list", "market:product:add", "market:product:edit", "market:product:remove", "market:product:view",
                "market:order:list", "market:order:create", "market:order:process", "market:order:cancel",
                "market:user:list", "market:user:manage",
                "market:category:list", "market:category:add", "market:category:edit", "market:category:remove"
            ));
        } else {
            result.put("roles", Arrays.asList());
            result.put("permissionCodes", Arrays.asList());
        }
        
        return R.ok(result);
    }
}
