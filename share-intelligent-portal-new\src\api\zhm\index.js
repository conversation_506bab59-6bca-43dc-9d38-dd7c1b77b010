import request from '@/utils/request'

// 首页统计
export function getHomeCountStatics() {
  return request({
    url: '/system/company/apply/getHomecountStatics',
    method: 'get',
  })
}

// 查询公司列表
export function listCompany(industrialChain) {
  return request({
    // url: '/system/company/mag/getCompanylist_home',
    url: "/system/company/mag/secret/getCompanylist_cxhl_find",
    method: 'get',
    params: industrialChain
  })
}

// 查询场景列表
export function listScene() {
  return request({
    url: '/system/scene/info/list',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 9,
    }
  })
}

// 查询场景列表
export function listPolicy() {
  return request({
    url: '/system/policy/list-recommend',
    method: 'get',
    params: {
      pageNum: 1,
      pageSize: 9,
    }
  })
}

// 查询活动列表
export function listActivity() {
  return request({
    url: '/system/activity-portal/list',
    method: 'get',
    params: {
      isHot: 1,
      pageNum: 1,
      pageSize: 6,
    }
  })
}

// 查询公告列表
export function listNotice() {
  return request({
    url: '/system/information/listByText',
    method: 'get',
    params: {
      // topStatus: 1,
      pageNum: 1,
      pageSize: 4,
      kind: 1
    }
  })
}

// 查询活动列表
export function listPolicyByType(type) {
  return request({
    url: '/system/policy/listByType',
    method: 'get',
    params: {
      type
    }
  })
}

// 画像列表
export function listByTypePolicy(type) {
  return request({
    url: '/system/labelInfo/listByTypePolicy',
    method: 'get',
  })
}

// 添加意向
export function interactRecordAdd(data) {
  return request({
    url: '/system/interactRecord/add',
    method: 'post',
    data,
  })
}

// 添加需求
export function demandAdd(data) {
  return request({
    url: '/system/demand',
    method: 'post',
    data,
  })
}

// 添加供给
export function supplyAdd(data) {
  return request({
    url: '/system/supply',
    method: 'post',
    data,
  })
}

// 添加供给
export function keywordList(text) {
  return request({
    url: '/system/common/keyword-list',
    method: 'post',
    data: {
      text
    },
  })
}
// 推荐
export function keywordList1(params) {
  return request({
    url: '/system/demand/listRecommend',
    method: 'get',
    params
  })
}
export function keywordList2(params) {
  return request({
    url: '/system/supply/listRecommend',
    method: 'get',
    params
  })
}
// 跳转
export function jump(data) {
  return request({
    url: '/index/user/login',
    // url:'http://cyqyfw.com/index/user/login',
    method: 'post',
    data,
  })
}
