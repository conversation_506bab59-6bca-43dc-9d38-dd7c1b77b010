package com.ruoyi.portalweb.api.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 政策意见反馈对象 policy_information_feedback
 * 
 * <AUTHOR>
 * @date 2024-06-26
 */
public class PolicyInformationFeedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 反馈ID */
    private Long policyInformationFeedbackId;

    /** 政策资讯ID */
    @Excel(name = "政策资讯ID")
    private Long policyInformationId;

    /** 反馈内容 */
    @Excel(name = "反馈内容")
    private String content;

    public void setPolicyInformationFeedbackId(Long policyInformationFeedbackId) 
    {
        this.policyInformationFeedbackId = policyInformationFeedbackId;
    }

    public Long getPolicyInformationFeedbackId() 
    {
        return policyInformationFeedbackId;
    }
    public void setPolicyInformationId(Long policyInformationId) 
    {
        this.policyInformationId = policyInformationId;
    }

    public Long getPolicyInformationId() 
    {
        return policyInformationId;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("policyInformationFeedbackId", getPolicyInformationFeedbackId())
            .append("policyInformationId", getPolicyInformationId())
            .append("content", getContent())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
