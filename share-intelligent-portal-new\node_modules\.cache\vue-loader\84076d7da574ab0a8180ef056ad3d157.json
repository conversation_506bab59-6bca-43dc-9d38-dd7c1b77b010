{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\dockingRecords\\index.vue?vue&type=style&index=0&id=51c9a598&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\dockingRecords\\index.vue", "mtime": 1750311963055}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCByZ2IoMjI1LCAyNDcsIDI0MCksIHJnYigyNDQsIDI1MiwgMjUwKSk7DQogIGhlaWdodDogMTAwdmg7DQp9DQoNCi5tYWluLWNvbnRlbnQgew0KICBwYWRkaW5nOiAyMHB4Ow0KDQogIC50YWJsZSB7DQogICAgbWFyZ2luLXRvcDogMjBweDsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7DQogICAgcGFkZGluZzogMjBweDsNCiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBmbGV4LXdyYXA6IHdyYXA7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1hcm91bmQ7DQoNCiAgICAucGFnZVN0eWxlIHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgbWFyZ2luLXRvcDogNjFweDsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqZA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/dockingRecords", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"main-content\">\r\n          <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n            <el-tab-pane label=\"我申请的\" name=\"0\">\r\n              <el-form\r\n                class=\"queryForm\"\r\n                :model=\"queryParams\"\r\n                ref=\"queryForm\"\r\n                size=\"small\"\r\n                :inline=\"true\"\r\n              >\r\n                <el-form-item label=\"企业名称\" prop=\"companyName\">\r\n                  <el-input\r\n                    v-model=\"queryParams.companyName\"\r\n                    placeholder=\"请输入企业名称\"\r\n                    clearable\r\n                  >\r\n                  </el-input>\r\n                </el-form-item>\r\n                <!-- <el-form-item label=\"状态\" prop=\"intentionStatus\">\r\n                  <el-select v-model=\"queryParams.intentionStatus\" placeholder=\"请选择\" clearable>\r\n                    <el-option v-for=\"dict in intentionStatus\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n                      :value=\"dict.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item> -->\r\n                <el-form-item>\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                  >\r\n                  <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                  >\r\n                </el-form-item>\r\n              </el-form>\r\n              <div class=\"table\">\r\n                <div style=\"width: 100%\">\r\n                  <el-table\r\n                    :data=\"cooperationList\"\r\n                    style=\"width: 100%\"\r\n                    :v-loading=\"loading\"\r\n                  >\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"intentionTypeName\"\r\n                      label=\"资源类型\"\r\n                      width=\"180\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"title\"\r\n                      label=\"资源名称\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"intentionContent\"\r\n                      label=\"申请内容\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"companyName\"\r\n                      label=\"申请公司\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"createTime\"\r\n                      label=\"申请时间\"\r\n                    >\r\n                    </el-table-column>\r\n                    <!-- <el-table-column align=\"center\" prop=\"intentionStatus\" label=\"状态\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '0'\" type=\"danger\">待回复</el-tag>\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '1'\" type=\"warning\">已申请</el-tag>\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '2'\" type=\"primary\">对接中</el-tag>\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '3'\" type=\"info\">已完结</el-tag>\r\n                      </template>\r\n                    </el-table-column> -->\r\n                    <!-- <el-table-column align=\"center\" label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button type=\"text\" size=\"small\" @click=\"handleView(scope.row)\">查看</el-button>\r\n                        <el-button type=\"text\" size=\"small\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n                      </template>\r\n                    </el-table-column> -->\r\n                  </el-table>\r\n                </div>\r\n                <!-- 分页 -->\r\n                <div class=\"pageStyle\">\r\n                  <el-pagination\r\n                    v-if=\"tableData && tableData.length > 0\"\r\n                    background\r\n                    layout=\"prev, pager, next\"\r\n                    class=\"activity-pagination\"\r\n                    :page-size=\"pageSize\"\r\n                    :current-page=\"pageNum\"\r\n                    :total=\"total\"\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                  >\r\n                  </el-pagination>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane label=\"被申请的\" name=\"1\">\r\n              <el-form\r\n                class=\"queryForm\"\r\n                :model=\"queryParams1\"\r\n                ref=\"queryForm\"\r\n                size=\"small\"\r\n                :inline=\"true\"\r\n              >\r\n                <el-form-item label=\"企业名称\" prop=\"companyName\">\r\n                  <el-input\r\n                    v-model=\"queryParams1.intentionCompanyName\"\r\n                    placeholder=\"请输入企业名称\"\r\n                    clearable\r\n                  >\r\n                  </el-input>\r\n                </el-form-item>\r\n                <!-- <el-form-item label=\"状态\" prop=\"intentionStatus\">\r\n                  <el-select v-model=\"queryParams1.intentionStatus\" placeholder=\"请选择\" clearable>\r\n                    <el-option v-for=\"dict in intentionStatus\" :key=\"dict.dictValue\" :label=\"dict.dictLabel\"\r\n                      :value=\"dict.dictValue\" />\r\n                  </el-select>\r\n                </el-form-item> -->\r\n                <el-form-item>\r\n                  <el-button\r\n                    type=\"primary\"\r\n                    icon=\"el-icon-search\"\r\n                    size=\"mini\"\r\n                    @click=\"handleQuery\"\r\n                    >搜索</el-button\r\n                  >\r\n                  <el-button\r\n                    icon=\"el-icon-refresh\"\r\n                    size=\"mini\"\r\n                    @click=\"resetQuery\"\r\n                    >重置</el-button\r\n                  >\r\n                </el-form-item>\r\n              </el-form>\r\n              <div class=\"table\">\r\n                <div style=\"width: 100%\">\r\n                  <el-table\r\n                    :data=\"cooperationList\"\r\n                    style=\"width: 100%\"\r\n                    :v-loading=\"loading\"\r\n                  >\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"intentionTypeName\"\r\n                      label=\"资源类型\"\r\n                      width=\"180\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"title\"\r\n                      label=\"资源名称\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"intentionContent\"\r\n                      label=\"申请内容\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"companyName\"\r\n                      label=\"供需发布公司\"\r\n                    >\r\n                    </el-table-column>\r\n                    <el-table-column\r\n                      align=\"center\"\r\n                      prop=\"createTime\"\r\n                      label=\"申请时间\"\r\n                    >\r\n                    </el-table-column>\r\n                    <!-- <el-table-column align=\"center\" prop=\"intentionStatus\" label=\"状态\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '0'\" type=\"danger\">待回复</el-tag>\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '1'\" type=\"warning\">已申请</el-tag>\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '2'\" type=\"primary\">对接中</el-tag>\r\n                        <el-tag v-if=\"scope.row.intentionStatus === '3'\" type=\"info\">已完结</el-tag>\r\n                      </template>\r\n                    </el-table-column> -->\r\n                    <el-table-column align=\"center\" label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button\r\n                          type=\"text\"\r\n                          size=\"small\"\r\n                          @click=\"handleView(scope.row)\"\r\n                          >查看</el-button\r\n                        >\r\n                        <el-button\r\n                          type=\"text\"\r\n                          size=\"small\"\r\n                          @click=\"handleDelete(scope.row)\"\r\n                          >删除</el-button\r\n                        >\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                </div>\r\n                <!-- 分页 -->\r\n                <div class=\"pageStyle\">\r\n                  <el-pagination\r\n                    v-if=\"tableData && tableData.length > 0\"\r\n                    background\r\n                    layout=\"prev, pager, next\"\r\n                    class=\"activity-pagination\"\r\n                    :page-size=\"pageSize\"\r\n                    :current-page=\"pageNum\"\r\n                    :total=\"total\"\r\n                    @size-change=\"handleSizeChange\"\r\n                    @current-change=\"handleCurrentChange\"\r\n                  >\r\n                  </el-pagination>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { dockingList } from \"@/api/system/user\";\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tableData: [\r\n        {\r\n          category: \"供给\",\r\n          name: \"玻璃钢格栅\",\r\n          content: \"申请内容\",\r\n          company: \"恒润集团有限公司\",\r\n          time: \"2025-12-05\",\r\n          status: \"待回复\",\r\n        },\r\n        {\r\n          category: \"供给\",\r\n          name: \"玻璃钢格栅\",\r\n          content: \"申请内容\",\r\n          company: \"恒润集团有限公司\",\r\n          time: \"2025-12-05\",\r\n          status: \"对接中\",\r\n        },\r\n        {\r\n          category: \"供给\",\r\n          name: \"玻璃钢格栅\",\r\n          content: \"申请内容\",\r\n          company: \"恒润集团有限公司\",\r\n          time: \"2025-12-05\",\r\n          status: \"已完结\",\r\n        },\r\n        {\r\n          category: \"供给\",\r\n          name: \"玻璃钢格栅\",\r\n          content: \"申请内容\",\r\n          company: \"恒润集团有限公司\",\r\n          time: \"2025-12-05\",\r\n          status: \"已申请\",\r\n        },\r\n      ],\r\n      showLogin: false,\r\n      userinfo: [],\r\n      token: \"\",\r\n      cooperationList: [],\r\n      partnerList: [],\r\n      total: 1,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      activeName: \"0\",\r\n      queryType: \"my\",\r\n      intentionStatus: [],\r\n      queryParams: {\r\n        companyName: \"\",\r\n        intentionStatus: \"\",\r\n      },\r\n      queryParams1: {\r\n        intentionCompanyName: \"\",\r\n        intentionStatus: \"\",\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getDicts();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      dockingList({\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        queryType: this.queryType,\r\n        companyName: this.queryParams.companyName,\r\n        intentionStatus: this.queryParams.intentionStatus,\r\n        intentionCompanyName: this.queryParams1.intentionCompanyName,\r\n      }).then((res) => {\r\n        this.cooperationList = res.rows;\r\n        this.total = res.total;\r\n      });\r\n    },\r\n    handleClick(tab, event) {\r\n      if (this.activeName === \"0\") {\r\n        this.queryType = \"my\";\r\n      } else {\r\n        this.queryType = \"\";\r\n      }\r\n      this.getList();\r\n    },\r\n    handleQuery() {\r\n      this.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams = {\r\n        companyName: \"\",\r\n        intentionStatus: \"\",\r\n      };\r\n      this.queryParams1 = {\r\n        intentionCompanyName: \"\",\r\n        intentionStatus: \"\",\r\n      };\r\n      this.getList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    getDicts() {\r\n      let params = { dictType: \"intention_status\" };\r\n      listData(params).then((response) => {\r\n        if (response.code == 200) {\r\n          this.intentionStatus = response.rows;\r\n        }\r\n      });\r\n    },\r\n    handleDelete(row) {\r\n      this.$confirm(\"是否确认删除该记录?\", \"警告\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.deleteDocking(row.id);\r\n        })\r\n        .catch(function () {});\r\n      // let delurl = '/portalweb/IntentionApply/' + id\r\n      // YS.deleteJsonFetch(delurl, {}).then(res => {\r\n      //   alert('删除成功')\r\n      //   this.getCooperationList()\r\n      // });\r\n    },\r\n    handleView(row) {\r\n      console.log(row);\r\n      this.$router.push({\r\n        path: \"dockingRecordsDetail\",\r\n        query: {\r\n          id: row.id,\r\n          createTime: row.createTime ? row.createTime : \"\",\r\n          intentionTypeName: row.intentionTypeName ? row.intentionTypeName : \"\",\r\n          intentionContent: row.intentionContent ? row.intentionContent : \"\",\r\n          completionDate: row.completionDate ? row.completionDate : \"\",\r\n          quantity: row.quantity ? row.quantity : \"\",\r\n          price: row.price ? row.price : \"\",\r\n          rate: row.rate ? row.rate : \"\",\r\n          shippingFee: row.shippingFee ? row.shippingFee : \"\",\r\n          sum: row.sum ? row.sum : \"\",\r\n          term: row.term ? row.term : \"\",\r\n          title: row.title ? row.title : \"\",\r\n        },\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.main-content {\r\n  padding: 20px;\r\n\r\n  .table {\r\n    margin-top: 20px;\r\n    background: #fff;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    box-sizing: border-box;\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    justify-content: space-around;\r\n\r\n    .pageStyle {\r\n      width: 100%;\r\n      margin-top: 61px;\r\n      display: flex;\r\n      justify-content: center;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}