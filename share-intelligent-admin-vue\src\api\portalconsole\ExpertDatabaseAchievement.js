import request from '@/utils/request'

// 查询专家成果列表
export function listExpertDatabaseAchievement(query) {
  return request({
    url: '/portalconsole/ExpertDatabaseAchievement/list',
    method: 'get',
    params: query
  })
}

// 查询专家成果详细
export function getExpertDatabaseAchievement(expertDatabaseAchievementId) {
  return request({
    url: '/portalconsole/ExpertDatabaseAchievement/' + expertDatabaseAchievementId,
    method: 'get'
  })
}

// 新增专家成果
export function addExpertDatabaseAchievement(data) {
  return request({
    url: '/portalconsole/ExpertDatabaseAchievement',
    method: 'post',
    data: data
  })
}

// 修改专家成果
export function updateExpertDatabaseAchievement(data) {
  return request({
    url: '/portalconsole/ExpertDatabaseAchievement',
    method: 'put',
    data: data
  })
}

// 删除专家成果
export function delExpertDatabaseAchievement(expertDatabaseAchievementId) {
  return request({
    url: '/portalconsole/ExpertDatabaseAchievement/' + expertDatabaseAchievementId,
    method: 'delete'
  })
}
