package com.ruoyi.auth.controller;

import com.ruoyi.auth.service.SSOClientService;
import com.ruoyi.common.core.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * SSO回调控制器
 * 专门处理SSO登录回调
 * 
 * <AUTHOR>
 */
@Controller
@RequestMapping("/sso")
public class SSOCallbackController {

    private static final Logger log = LoggerFactory.getLogger(SSOCallbackController.class);

    @Autowired
    private SSOClientService ssoClientService;

    /**
     * SSO登录回调处理
     * SSO服务重定向到此接口进行登录处理
     *
     * @param code 授权码
     * @param state 状态参数（原始重定向URL）
     * @param error 错误信息
     * @param response HTTP响应
     */
    @GetMapping("/callback")
    public void ssoCallback(@RequestParam(required = false) String code,
                           @RequestParam(value = "state", required = false) String state,
                           @RequestParam(required = false) String error,
                           HttpServletResponse response) throws IOException {

        if (StringUtils.isNotEmpty(error)) {
            log.error("SSO登录失败: {}", error);
            response.sendRedirect("/login?error=" + error);
            return;
        }

        if (StringUtils.isEmpty(code)) {
            log.error("SSO回调缺少授权码");
            response.sendRedirect("/login?error=missing_code");
            return;
        }

        log.info("SSO登录回调，授权码: {}, 状态: {}", code, state);

        try {
            // 使用授权码换取访问令牌
            Map<String, Object> tokenInfo = ssoClientService.exchangeToken(code);

            if (tokenInfo != null && tokenInfo.get("access_token") != null) {
                String accessToken = (String) tokenInfo.get("access_token");

                // 获取用户信息并创建本地会话
                boolean loginSuccess = ssoClientService.createLocalSession(accessToken);

                if (loginSuccess) {
                    // 登录成功，跳转到目标页面
                    String redirectUrl = StringUtils.isNotEmpty(state) ? state : "/index";
                    log.info("SSO登录成功，跳转到: {}", redirectUrl);
                    response.sendRedirect(redirectUrl);
                } else {
                    log.error("创建本地会话失败");
                    response.sendRedirect("/login?error=session_create_failed");
                }
            } else {
                log.error("获取访问令牌失败");
                response.sendRedirect("/login?error=token_exchange_failed");
            }
        } catch (Exception e) {
            log.error("SSO回调处理异常", e);
            response.sendRedirect("/login?error=callback_error");
        }
    }
}
