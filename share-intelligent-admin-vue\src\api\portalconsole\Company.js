import request from '@/utils/request'

// 查询企业信息列表
export function listCompany(query) {
  return request({
    url: '/portalconsole/Company/list',
    method: 'get',
    params: query
  })
}

// 查询企业信息详细
export function getCompany(companyId) {
  return request({
    url: '/portalconsole/Company/' + companyId,
    method: 'get'
  })
}

// 新增企业信息
export function addCompany(data) {
  return request({
    url: '/portalconsole/Company',
    method: 'post',
    data: data
  })
}

// 修改企业信息
export function updateCompany(data) {
  return request({
    url: '/portalconsole/Company/audit',
    method: 'put',
    data: data
  })
}

// 删除企业信息
export function delCompany(companyId) {
  return request({
    url: '/portalconsole/Company/' + companyId,
    method: 'delete'
  })
}

//批量审核
export function auditBatch(data) {
  return request({
    url: '/portalconsole/Company/auditBatch',
    method: 'put',
    data: data
  })
}


