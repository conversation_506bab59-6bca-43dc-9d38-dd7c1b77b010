{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\expertLibrary\\detail.vue?vue&type=style&index=0&id=31dee942&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\expertLibrary\\detail.vue", "mtime": 1750311962987}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/purchaseSales/component/expertLibrary", "sourcesContent": ["<template>\r\n  <div class=\"expert-library-detail\">\r\n    <!-- banner图 -->\r\n    <div class=\"expert-detail-banner\">\r\n      <img\r\n        src=\"../../../../assets/expertLibrary/expertLibraryDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"expert-detail-title-box\">\r\n      <div class=\"expert-detail-divider\"></div>\r\n      <div class=\"expert-detail-title\">专家详情</div>\r\n      <div class=\"expert-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"expert-detail-content\">\r\n      <div class=\"expert-detail-headline\">\r\n        <div class=\"expert-detail-headline-img\">\r\n          <img v-if=\"data.headPortrait\" :src=\"data.headPortrait\" alt=\"\" />\r\n          <img\r\n            v-else\r\n            src=\"../../../../assets/expertLibrary/defaultImg.png\"\r\n            alt=\"\"\r\n          />\r\n        </div>\r\n        <div class=\"expert-detail-headline-info\">\r\n          <div class=\"headline-info-title\">{{ data.expertName }}</div>\r\n          <div class=\"headline-info-box\">\r\n            <span class=\"headline-info-laber\">单位：</span>\r\n            <span class=\"headline-info-description\">\r\n              {{ data.workUnit }}\r\n            </span>\r\n          </div>\r\n          <div class=\"headline-info-box\">\r\n            <span class=\"headline-info-laber\">职位：</span>\r\n            <span class=\"headline-info-description\">{{ data.post }}</span>\r\n          </div>\r\n          <div class=\"headline-info-box\">\r\n            <span class=\"headline-info-laber\">研究方向：</span>\r\n            <span class=\"headline-info-description\">{{\r\n              data.researchDirection\r\n            }}</span>\r\n          </div>\r\n          <div class=\"headline-content-btn\">\r\n            <el-button\r\n              v-if=\"showBtn\"\r\n              class=\"headline-btn-style intention-btn\"\r\n              @click=\"goIntention\"\r\n              >我有意向</el-button\r\n            >\r\n            <el-button @click=\"goChat\" icon=\"el-icon-chat-dot-round\"\r\n              >在线沟通</el-button\r\n            >\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"expert-detail-introduce\">\r\n        <div class=\"introduce-content\">\r\n          <div class=\"introduction-line\"></div>\r\n          <div class=\"introduction-title\">专家介绍</div>\r\n        </div>\r\n        <div class=\"introduction-text-content\">\r\n          <div\r\n            v-html=\"data.introduce\"\r\n            class=\"introduction-text ql-editor\"\r\n          ></div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getExpertDetail, getCheckSubmit } from \"@/api/purchaseSales\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { getCompanyInfoByLoginInfo } from \"@/api/apathy\";\r\nimport { mapGetters } from \"vuex\";\r\nimport { getCustomerServicerInfo } from \"@/api/expertLibrary/index\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n      showBtn: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      getExpertDetail({ id: this.$route.query.id })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          if (!this.token) {\r\n            this.showBtn = true;\r\n          } else {\r\n            this.getInfo();\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 判断此资源是不是自己发布的\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (this.data.createById === res.user.userId) {\r\n          this.showBtn = false;\r\n        } else {\r\n          this.showBtn = true;\r\n        }\r\n      });\r\n    },\r\n    goChat() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n\r\n      getCustomerServicerInfo().then((res) => {\r\n        if (res.code == 200) {\r\n          let routeData = this.$router.resolve({\r\n            path: \"/user/im\",\r\n            query: {\r\n              userId: res.data.id,\r\n            },\r\n          });\r\n          window.open(routeData.href, \"_blank\");\r\n        } else {\r\n          this.$message({\r\n            type: \"warning\",\r\n            message: \"获取客服信息失败,请稍后再试\",\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 跳到我有意向页面\r\n    goIntention() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      // 是否加入企业\r\n      this.loading = true;\r\n      getCompanyInfoByLoginInfo()\r\n        .then((res) => {\r\n          if (res.data) {\r\n            // 是否对此资源提交过意向\r\n            getCheckSubmit({\r\n              id: this.$route.query.id,\r\n              resourceType: \"resource_expet\",\r\n            })\r\n              .then((res) => {\r\n                this.loading = false;\r\n                // true 提交过  false未提交过\r\n                if (res.data) {\r\n                  this.$message({\r\n                    type: \"warning\",\r\n                    message: \"已经提交过了哦！\",\r\n                  });\r\n                } else {\r\n                  let routeData = this.$router.resolve({\r\n                    path: \"/addIntention\",\r\n                    query: {\r\n                      id: this.$route.query.id,\r\n                      type: \"resource_expet\",\r\n                      title: this.data.expertName,\r\n                    },\r\n                  });\r\n                  window.open(routeData.href, \"_blank\");\r\n                }\r\n              })\r\n              .catch(() => {\r\n                this.loading = false;\r\n              });\r\n          } else {\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"warning\",\r\n              message: \"必须加入企业才可提交我有意向\",\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.expert-library-detail {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  padding-bottom: 60px;\r\n  .expert-detail-banner {\r\n    width: 100%;\r\n    height: 25.92vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .expert-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .expert-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .expert-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .expert-detail-content {\r\n    width: 1200px;\r\n    background: #fff;\r\n    margin: 0 auto;\r\n    padding: 60px 60px 124px;\r\n    .expert-detail-headline {\r\n      display: flex;\r\n      .expert-detail-headline-img {\r\n        width: 240px;\r\n        height: 240px;\r\n        margin-right: 40px;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .expert-detail-headline-info {\r\n        flex: 1;\r\n        .headline-info-title {\r\n          max-width: 792px;\r\n          font-size: 32px;\r\n          font-family: PingFangSC-Semibold, PingFang SC;\r\n          font-weight: 600;\r\n          color: #333;\r\n          line-height: 32px;\r\n          padding-bottom: 20px;\r\n          word-wrap: break-word;\r\n        }\r\n        .headline-info-box {\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          line-height: 32px;\r\n          .headline-info-laber {\r\n            width: 70px;\r\n            color: #666;\r\n          }\r\n          .headline-info-description {\r\n            flex: 1;\r\n            max-width: 712px;\r\n            color: #333;\r\n            word-wrap: break-word;\r\n          }\r\n        }\r\n        .headline-content-btn {\r\n          padding-top: 52px;\r\n          .headline-btn-style {\r\n            width: 100px;\r\n            height: 32px;\r\n            border-radius: 4px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            padding: 8px 11px;\r\n          }\r\n          .intention-btn {\r\n            background: #21c9b8;\r\n            color: #fff;\r\n          }\r\n          .communication-btn {\r\n            border: 1px solid #21c9b8;\r\n            color: #21c9b8;\r\n            background: transparent;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .expert-detail-introduce {\r\n      padding-top: 60px;\r\n      .introduce-content {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-bottom: 40px;\r\n        .introduction-line {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .introduction-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n      .introduction-text-content {\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.expert-library-detail {\r\n  .introduction-text-content {\r\n    .introduction-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}