<!--
 * @Author: zhc
 * @Date: 2023-04-25 16:15:11
 * @LastEditTime: 2023-05-19 08:55:53
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div class="home">
    <home-banner />
    <home-industrial />
    <home-require />
    <home-dynamic-info />
    <home-market />
    <!-- <home-think />
    <home-news /> -->
  </div>
</template>
<script src="https://cdn.ronghub.com/RongIMLib-5.7.4.prod.js"></script>
<script>
import HomeBanner from "./components/home/<USER>";
import HomeIndustrial from "./components/home/<USER>";
import HomeRequire from "./components/home/<USER>";
import HomeMarket from "./components/home/<USER>";
import HomeDynamicInfo from "./components/home/<USER>";
// import HomeThink from "./components/home/<USER>";
// import HomeNews from "./components/home/<USER>";

export default {
  name: "Index",
  components: {
    HomeBanner,
    HomeIndustrial,
    HomeRequire,
    HomeMarket,
    HomeDynamicInfo,
    // HomeThink,
    // HomeNews,
  },
  data() {
    return {
      // 版本号
      version: "3.6.1",
    };
  },
  methods: {
    goTarget(href) {
      window.open(href, "_blank");
    },
  },
};
</script>

<style scoped lang="scss">
@import "~@/assets/styles/mixin.scss";

.home {
  position: relative;
  padding: 0;
  margin: 0;
  background-color: #ffffff;
}
</style>
