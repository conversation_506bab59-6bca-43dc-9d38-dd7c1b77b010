{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\application\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\application\\index.vue", "mtime": 1750311963040}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_appliMarket", "require", "_userMenu", "_interopRequireDefault", "_user", "name", "dicts", "components", "UserMenu", "data", "appliList", "queryParams", "pageNum", "total", "loading", "shipNum", "invoiceNum", "companyStatus", "created", "getUser", "getOrderStatusNum", "getList", "methods", "_this", "orderStatusNum", "then", "res", "code", "waitSendNum", "wait<PERSON>ake<PERSON>um", "_this2", "params", "createBy", "$store", "state", "user", "userId", "pageSize", "rows", "typeFormat", "row", "column", "selectDictLabel", "dict", "type", "sys_oper_type", "businessType", "handleDelete", "id", "_this3", "$modal", "confirm", "delAppli", "msgSuccess", "catch", "handleCurrentChange", "publishAppli", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "$router", "push", "path", "goDetail", "query", "goEdit", "status", "$message", "warning", "appliGround", "appState", "_this5", "appliGroundOff", "success", "offShelf", "_this6", "_this7", "checkAuthStatus"], "sources": ["src/views/system/user/application/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\">\r\n          <div class=\"applicationDesc\">\r\n            <div class=\"desc_left\">\r\n              <div>\r\n                商品发布说明:\r\n                SaaS、API商品发布前需先进行接入调试，接入流程请查看商品接入指南。\r\n              </div>\r\n              <div>\r\n                商品修改说明:\r\n                只有在销售商品才能进行修改操作，提交修改申请后，需要等待运营审核后才能生效。\r\n              </div>\r\n              <div>\r\n                商品升级说明:\r\n                SaaS商品上架后，如需支持用户升级已购买的规格，请在操作中设置升级规则。\r\n              </div>\r\n              <div>\r\n                商品下架说明:\r\n                为保障用户正常访问，商品下架需由云商店审核通过后方可下架。下架全部商品规格后，商品进入“已停售”状态，停售后将不会在商店中呈现和售卖，但不影响已购用户的使用和续订。\r\n              </div>\r\n            </div>\r\n            <div class=\"driver\"></div>\r\n            <div class=\"desc_right\">\r\n              <div class=\"statistics\">\r\n                <div>待发货 {{ shipNum }}</div>\r\n                <div class=\"statisticsItem\">待开票 {{ invoiceNum }}</div>\r\n              </div>\r\n              <div class=\"submitStyle\">\r\n                <div class=\"buttonStyle\" @click=\"publishAppli\">发布应用</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"appliTitle\">我的应用</div>\r\n          <div class=\"appliPart\" v-loading=\"loading\">\r\n            <div class=\"everyItem\" v-for=\"item in appliList\" :key=\"item.id\">\r\n              <div class=\"item_img\">\r\n                <img :src=\"item.appLogo\" alt=\"\" />\r\n              </div>\r\n              <div class=\"item_text\">\r\n                <div class=\"title\">{{ item.appName }}</div>\r\n                <div class=\"desc\">{{ item.briefInto }}</div>\r\n              </div>\r\n              <div class=\"timeStyle\">\r\n                <div class=\"tabHeader\">交付方式</div>\r\n                <div style=\"margin-top: 10px\">\r\n                  {{ item.delivery == \"0\" ? \"Saas服务\" : \"本地部署\" }}\r\n                </div>\r\n              </div>\r\n              <div class=\"timeStyle\">\r\n                <div class=\"tabHeader\">创建时间</div>\r\n                <div style=\"margin-top: 10px\">{{ item.createTime }}</div>\r\n              </div>\r\n              <div class=\"timeStyle\">\r\n                <div class=\"tabHeader\">应用状态</div>\r\n                <div style=\"margin-top: 10px\">\r\n                  {{\r\n                    item.appState == 0\r\n                      ? \"待配置\"\r\n                      : item.appState == 1\r\n                      ? \"待上架\"\r\n                      : item.appState == 2\r\n                      ? \"已上架\"\r\n                      : \"已下架\"\r\n                  }}\r\n                </div>\r\n              </div>\r\n              <div class=\"option\">\r\n                <div style=\"display: flex\">\r\n                  <div\r\n                    class=\"buttonStyle\"\r\n                    @click=\"goEdit(item.id, item.appState)\"\r\n                  >\r\n                    编辑\r\n                  </div>\r\n                  <div\r\n                    v-if=\"item.appState == 2\"\r\n                    class=\"buttonStyle\"\r\n                    @click=\"offShelf(item.id, item.appState)\"\r\n                  >\r\n                    下架\r\n                  </div>\r\n                  <div\r\n                    v-if=\"item.appState == 3\"\r\n                    class=\"buttonStyle\"\r\n                    @click=\"appliGround(item.id, item.appState)\"\r\n                  >\r\n                    上架\r\n                  </div>\r\n                </div>\r\n                <div style=\"display: flex; margin-top: 10px\">\r\n                  <div class=\"buttonStyle\" @click=\"goDetail(item.id)\">详情</div>\r\n                  <div class=\"buttonStyle\" @click=\"handleDelete(item.id)\">\r\n                    删除\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div style=\"text-align: center; margin-top: 45px\">\r\n              <el-pagination\r\n                v-show=\"total > 0\"\r\n                background\r\n                layout=\"prev, pager, next\"\r\n                :page-size=\"5\"\r\n                :current-page.sync=\"queryParams.pageNum\"\r\n                @current-change=\"handleCurrentChange\"\r\n                :total=\"total\"\r\n              >\r\n              </el-pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  appliList,\r\n  delAppli,\r\n  orderStatusNum,\r\n  appliGroundOff,\r\n} from \"@/api/appliMarket\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { checkAuthStatus } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      appliList: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      loading: false,\r\n      shipNum: 0,\r\n      invoiceNum: 0,\r\n      companyStatus: \"0\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getUser();\r\n    this.getOrderStatusNum();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getOrderStatusNum() {\r\n      orderStatusNum().then((res) => {\r\n        if (res.code === 200) {\r\n          (this.shipNum = res.data.waitSendNum),\r\n            (this.invoiceNum = res.data.waitMakeNum);\r\n        }\r\n      });\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        createBy: this.$store.state.user.userId,\r\n        pageNum: this.queryParams.pageNum,\r\n        pageSize: 5,\r\n      };\r\n      appliList(params).then((res) => {\r\n        this.appliList = res.rows;\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 操作日志类型字典翻译\r\n    typeFormat(row, column) {\r\n      return this.selectDictLabel(\r\n        this.dict.type.sys_oper_type,\r\n        row.businessType\r\n      );\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(id) {\r\n      this.$modal\r\n        .confirm(\"是否确认删除该数据项？\")\r\n        .then(function () {\r\n          return delAppli(id);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"操作成功!\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    publishAppli() {\r\n      if (this.companyStatus == \"0\") {\r\n        this.$confirm(\"当前用户未完成服务商认证，请认证后再进行操作。\", \"\", {\r\n          confirmButtonText: \"去认证\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push({\r\n              path: \"/user/spCertification\",\r\n            });\r\n          })\r\n          .catch(() => {});\r\n      } else {\r\n        this.$router.push({\r\n          path: \"/user/publishAppli\",\r\n        });\r\n      }\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push({\r\n        path: \"/user/appliDetail\",\r\n        query: {\r\n          id,\r\n        },\r\n      });\r\n    },\r\n    goEdit(id, status) {\r\n      if (status == 2) {\r\n        this.$message.warning(\"当前应用已上架，请下架后再进行编辑！\");\r\n      } else {\r\n        this.$router.push({\r\n          path: \"/user/publishAppli\",\r\n          query: {\r\n            id,\r\n          },\r\n        });\r\n      }\r\n    },\r\n    // 上架\r\n    appliGround(id, appState) {\r\n      let data = {\r\n        id,\r\n        appState,\r\n      };\r\n      appliGroundOff(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message.success(\"操作成功!\");\r\n          this.getList();\r\n        }\r\n      });\r\n    },\r\n    // 下架\r\n    offShelf(id, appState) {\r\n      let data = {\r\n        id,\r\n        appState,\r\n      };\r\n      appliGroundOff(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message.success(\"操作成功!\");\r\n          this.getList();\r\n        }\r\n      });\r\n      // this.$message.warning(\"当前应用存在进行中订单，无法下架！\");\r\n    },\r\n    getUser() {\r\n      checkAuthStatus().then((res) => {\r\n        if (res.code === 200) {\r\n          this.companyStatus = res.data.companyStatus;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  // background: rgb(242, 248, 255);\r\n  .applicationDesc {\r\n    width: 100%;\r\n    height: 150px;\r\n    display: flex;\r\n    padding: 20px;\r\n    .desc_left {\r\n      width: 80%;\r\n    }\r\n    .driver {\r\n      width: 2px;\r\n      height: 100%;\r\n      background: #ccc;\r\n      margin: 0 5%;\r\n    }\r\n    .desc_right {\r\n      width: 30%;\r\n      display: flex;\r\n      align-items: center;\r\n      .statistics {\r\n        margin-left: 10px;\r\n        width: 50%;\r\n        .statisticsItem {\r\n          margin-top: 12px;\r\n        }\r\n      }\r\n      .submitStyle {\r\n        width: 50%;\r\n        display: flex;\r\n        justify-content: right;\r\n        .buttonStyle {\r\n          width: 100px;\r\n          padding: 10px;\r\n          background: #21c9b8;\r\n          color: #ffffff;\r\n          text-align: center;\r\n          cursor: pointer;\r\n          border-radius: 4px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .appliTitle {\r\n    font-size: 22px;\r\n    margin: 30px;\r\n    font-weight: 600;\r\n  }\r\n  .appliPart {\r\n    .everyItem {\r\n      width: 100%;\r\n      height: 150px;\r\n      background: #ffffff;\r\n      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n      display: flex;\r\n      padding: 20px;\r\n      align-items: center;\r\n      margin-top: 20px;\r\n      .item_img {\r\n        width: 17%;\r\n        height: 100%;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .item_text {\r\n        width: 20%;\r\n        margin-left: 2%;\r\n        .title {\r\n          font-size: 16px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #333333;\r\n        }\r\n        .desc {\r\n          margin-top: 10px;\r\n          font-size: 14px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          color: #999999;\r\n        }\r\n      }\r\n      // .delivery {\r\n      //   margin-left: 2%;\r\n      // }\r\n      .timeStyle {\r\n        width: 15%;\r\n        text-align: center;\r\n      }\r\n      .option {\r\n        // display: flex;\r\n        // justify-content: center;\r\n        width: 18%;\r\n        .buttonStyle {\r\n          color: #21c9b8;\r\n          cursor: pointer;\r\n          margin-left: 20%;\r\n        }\r\n      }\r\n      .tabHeader {\r\n        font-family: Microsoft YaHei;\r\n        font-weight: 400;\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AA2HA,IAAAA,YAAA,GAAAC,OAAA;AAMA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;MACAC,WAAA;QACAC,OAAA;MACA;MACAC,KAAA;MACAC,OAAA;MACAC,OAAA;MACAC,UAAA;MACAC,aAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAF,iBAAA,WAAAA,kBAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,2BAAA,IAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAR,OAAA,GAAAW,GAAA,CAAAjB,IAAA,CAAAmB,WAAA,EACAL,KAAA,CAAAP,UAAA,GAAAU,GAAA,CAAAjB,IAAA,CAAAoB,WAAA;QACA;MACA;IACA;IACAR,OAAA,WAAAA,QAAA;MAAA,IAAAS,MAAA;MACA,KAAAhB,OAAA;MACA,IAAAiB,MAAA;QACAC,QAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,MAAA;QACAxB,OAAA,OAAAD,WAAA,CAAAC,OAAA;QACAyB,QAAA;MACA;MACA,IAAA3B,sBAAA,EAAAqB,MAAA,EAAAN,IAAA,WAAAC,GAAA;QACAI,MAAA,CAAApB,SAAA,GAAAgB,GAAA,CAAAY,IAAA;QACAR,MAAA,CAAAjB,KAAA,GAAAa,GAAA,CAAAb,KAAA;QACAiB,MAAA,CAAAhB,OAAA;MACA;IACA;IACA;IACAyB,UAAA,WAAAA,WAAAC,GAAA,EAAAC,MAAA;MACA,YAAAC,eAAA,CACA,KAAAC,IAAA,CAAAC,IAAA,CAAAC,aAAA,EACAL,GAAA,CAAAM,YACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAC,EAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,MAAA,CACAC,OAAA,gBACA1B,IAAA;QACA,WAAA2B,qBAAA,EAAAJ,EAAA;MACA,GACAvB,IAAA;QACAwB,MAAA,CAAA5B,OAAA;QACA4B,MAAA,CAAAC,MAAA,CAAAG,UAAA;MACA,GACAC,KAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA3C,OAAA;MACA,KAAAD,WAAA,CAAAC,OAAA,GAAAA,OAAA;MACA,KAAAS,OAAA;IACA;IACAmC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,SAAAxC,aAAA;QACA,KAAAyC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAhB,IAAA;QACA,GACAnB,IAAA;UACAgC,MAAA,CAAAI,OAAA,CAAAC,IAAA;YACAC,IAAA;UACA;QACA,GACAT,KAAA;MACA;QACA,KAAAO,OAAA,CAAAC,IAAA;UACAC,IAAA;QACA;MACA;IACA;IACAC,QAAA,WAAAA,SAAAhB,EAAA;MACA,KAAAa,OAAA,CAAAC,IAAA;QACAC,IAAA;QACAE,KAAA;UACAjB,EAAA,EAAAA;QACA;MACA;IACA;IACAkB,MAAA,WAAAA,OAAAlB,EAAA,EAAAmB,MAAA;MACA,IAAAA,MAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;MACA;QACA,KAAAR,OAAA,CAAAC,IAAA;UACAC,IAAA;UACAE,KAAA;YACAjB,EAAA,EAAAA;UACA;QACA;MACA;IACA;IACA;IACAsB,WAAA,WAAAA,YAAAtB,EAAA,EAAAuB,QAAA;MAAA,IAAAC,MAAA;MACA,IAAA/D,IAAA;QACAuC,EAAA,EAAAA,EAAA;QACAuB,QAAA,EAAAA;MACA;MACA,IAAAE,2BAAA,EAAAhE,IAAA,EAAAgB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA6C,MAAA,CAAAJ,QAAA,CAAAM,OAAA;UACAF,MAAA,CAAAnD,OAAA;QACA;MACA;IACA;IACA;IACAsD,QAAA,WAAAA,SAAA3B,EAAA,EAAAuB,QAAA;MAAA,IAAAK,MAAA;MACA,IAAAnE,IAAA;QACAuC,EAAA,EAAAA,EAAA;QACAuB,QAAA,EAAAA;MACA;MACA,IAAAE,2BAAA,EAAAhE,IAAA,EAAAgB,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAiD,MAAA,CAAAR,QAAA,CAAAM,OAAA;UACAE,MAAA,CAAAvD,OAAA;QACA;MACA;MACA;IACA;IACAF,OAAA,WAAAA,QAAA;MAAA,IAAA0D,MAAA;MACA,IAAAC,qBAAA,IAAArD,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAkD,MAAA,CAAA5D,aAAA,GAAAS,GAAA,CAAAjB,IAAA,CAAAQ,aAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}