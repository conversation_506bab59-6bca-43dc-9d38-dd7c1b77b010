{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\demandTab.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\demandTab.vue", "mtime": 1750311962930}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ramda", "require", "_data", "_demand", "_utils", "_cryptoJs", "_interopRequireDefault", "secret<PERSON>ey", "AESUtil", "exports", "aesEncrypt", "content", "key", "CryptoJS", "enc", "Utf8", "parse", "srcs", "encrypted", "AES", "encrypt", "mode", "ECB", "padding", "pad", "Pkcs7", "toString", "aesDecrypt", "encryptStr", "decrypt", "stringify", "_default", "default", "name", "data", "loading", "tabs", "tabIndex", "items", "pageNum", "pageSize", "total", "defaultUrl", "created", "initData", "mounted", "getDemandData", "methods", "_this", "getDicts", "then", "res", "code", "_res$data", "unshift", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "undefined", "dictSort", "dictType", "item", "head", "type", "_this2", "listDemand", "demandType", "displayStatus", "JSON", "_res", "_res$rows", "rows", "map", "url", "images", "scenePicture", "length", "id", "title", "demandTitle", "company", "displayRestrictions", "formatCompanyName", "companyName", "finally", "onTabChange", "index", "handleSizeChange", "handleCurrentChange"], "sources": ["src/views/components/home/<USER>/demandTab.vue"], "sourcesContent": ["<template>\r\n  <div class=\"damand-tab-container\">\r\n    <div v-loading=\"loading\" class=\"tab-main\">\r\n      <el-scrollbar noresize class=\"left\">\r\n        <div class=\"tab-content\">\r\n          <div\r\n            v-for=\"(item, index) in tabs\"\r\n            :key=\"index\"\r\n            :class=\"{ active: tabIndex === index }\"\r\n            class=\"tab-content-item\"\r\n            @click=\"onTabChange(index)\"\r\n          >\r\n            {{ item.dictLabel }}\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n      <el-row class=\"right\" :gutter=\"24\">\r\n        <template v-if=\"items.length > 0\">\r\n          <el-col :span=\"8\" v-for=\"item in items\" :key=\"item.id\">\r\n            <router-link\r\n              target=\"_blank\"\r\n              :to=\"`/demandHallDetail?id=${item.id}`\"\r\n            >\r\n              <div class=\"card\">\r\n                <el-image\r\n                  class=\"card-img\"\r\n                  :src=\"item.url ? item.url : defaultUrl\"\r\n                  fit=\"fill\"\r\n                />\r\n                <div class=\"card-footer\">\r\n                  <div class=\"title\" :title=\"item.title\">{{ item.title }}</div>\r\n                  <div class=\"subtitle\">{{ item.company }}</div>\r\n                </div>\r\n              </div>\r\n            </router-link>\r\n          </el-col>\r\n        </template>\r\n        <template v-else>\r\n          <el-empty />\r\n        </template>\r\n      </el-row>\r\n    </div>\r\n    <div class=\"tab-page-end\">\r\n      <!-- <span class=\"demonstration\">完整功能</span> -->\r\n      <el-pagination\r\n        class=\"company-tab-pagination\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"pageNum\"\r\n        :page-sizes=\"[100, 200, 300, 400]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\" prev, pager, next \"\r\n        :total=\"total\"\r\n      >\r\n      </el-pagination>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { head, map } from \"ramda\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { listDemand } from \"@/api/zhm/demand\";\r\nimport { formatCompanyName } from \"@/utils\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\nexport const AESUtil = {\r\n  /**\r\n   * AES加密方法\r\n   * @param content 要加密的字符串\r\n   * @returns {string} 加密结果\r\n   */\r\n  aesEncrypt: (content) => {\r\n    let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n    let srcs = CryptoJS.enc.Utf8.parse(content);\r\n    let encrypted = CryptoJS.AES.encrypt(srcs, key, {\r\n      mode: CryptoJS.mode.ECB,\r\n      padding: CryptoJS.pad.Pkcs7,\r\n    });\r\n    return encrypted.toString();\r\n  },\r\n\r\n  /**\r\n   * 解密方法\r\n   * @param encryptStr 密文\r\n   * @returns {string} 明文\r\n   */\r\n  aesDecrypt: (encryptStr) => {\r\n    let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n    let decrypt = CryptoJS.AES.decrypt(encryptStr, key, {\r\n      mode: CryptoJS.mode.ECB,\r\n      padding: CryptoJS.pad.Pkcs7,\r\n    });\r\n    return CryptoJS.enc.Utf8.stringify(decrypt).toString();\r\n  },\r\n};\r\nexport default {\r\n  name: \"DemandTab\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tabs: [],\r\n      tabIndex: 0,\r\n      items: [],\r\n      pageNum: 1,\r\n      pageSize: 6,\r\n      total: 0,\r\n      defaultUrl: require(\"../../../../assets/purchaseSales/demandDefault.png\"),\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  mounted() {\r\n    this.getDemandData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      getDicts(\"demand_type\").then((res) => {\r\n        const { code, data = [] } = res;\r\n        if (code === 200) {\r\n          this.tabs = data;\r\n          this.tabs.unshift({\r\n            dictLabel: \"全部\",\r\n            dictValue: undefined,\r\n            dictSort: 1,\r\n            dictType: \"demand_type\",\r\n          });\r\n          const item = head(data);\r\n          this.getDemandData(item.dictValue);\r\n        }\r\n      });\r\n    },\r\n    getDemandData(type) {\r\n      this.loading = true;\r\n      listDemand({\r\n        demandType: type,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n        displayStatus: 1,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          const { code, rows = [] } = res;\r\n\r\n          if (code === 200) {\r\n            // this.items = rows;\r\n            this.items = map((item) => {\r\n              let url;\r\n              const images = JSON.parse(item.scenePicture) || [];\r\n              if (images.length > 0) {\r\n                url = head(images).url;\r\n              }\r\n              return {\r\n                id: item.id,\r\n                title: item.demandTitle,\r\n                company:\r\n                  item.displayRestrictions === 2\r\n                    ? formatCompanyName(item.companyName)\r\n                    : item.companyName,\r\n                url,\r\n              };\r\n            }, rows);\r\n          }\r\n          this.total = res.total;\r\n          // this.items = res.rows;\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    onTabChange(index) {\r\n      if (index !== this.tabIndex) {\r\n        this.tabIndex = index;\r\n        this.pageNum = 1;\r\n        const item = this.tabs[index] || {};\r\n        this.getDemandData(item.dictValue);\r\n      }\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.pageNum = 1;\r\n      const item = this.tabs[this.tabIndex] || {};\r\n      this.getDemandData(item.dictValue);\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      const item = this.tabs[this.tabIndex] || {};\r\n      this.getDemandData(item.dictValue);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n\r\n.damand-tab-container {\r\n  .tab-main {\r\n    position: relative;\r\n    display: flex;\r\n    flex-shrink: 0;\r\n    width: 100%;\r\n    flex-direction: row;\r\n    ::v-deep .el-scrollbar__wrap {\r\n      overflow-x: hidden;\r\n      overflow-y: auto;\r\n    }\r\n    .left {\r\n      width: 148px;\r\n      height: 580px;\r\n      background: #21c9b8;\r\n      .tab-content {\r\n        padding: 24px 0 24px 18px;\r\n        &-item {\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          flex-shrink: 0;\r\n          height: 40px;\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #ffffff;\r\n          line-height: 14px;\r\n          transition: background, color 0.25ms ease;\r\n          margin-bottom: 12px;\r\n          cursor: pointer;\r\n          &.active {\r\n            color: #21c9b8;\r\n            background: linear-gradient(270deg, #fbfdff 0%, #ffffff 100%);\r\n          }\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .right {\r\n      flex: 1;\r\n      padding-left: 36px;\r\n      .card {\r\n        width: 100%;\r\n        min-height: 318px;\r\n        background: #ffffff;\r\n        box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.1);\r\n        margin-bottom: 24px;\r\n        &-img {\r\n          width: 100%;\r\n          height: 200px;\r\n          background: #ffffff;\r\n        }\r\n        &-footer {\r\n          padding: 16px 24px;\r\n          .title {\r\n            // @include multiEllipsis(2);\r\n            @include ellipsis;\r\n            font-size: 18px;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            line-height: 26px;\r\n            margin-bottom: 12px;\r\n          }\r\n          .subtitle {\r\n            @include ellipsis;\r\n            font-size: 14px;\r\n            font-weight: 400;\r\n            color: #666666;\r\n            line-height: 14px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.damand-tab-container {\r\n  .tab-page-end {\r\n    .company-tab-pagination {\r\n      width: 240px;\r\n      margin: 0 auto;\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #ffffff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        margin: 0 6px;\r\n        color: #333;\r\n      }\r\n      .el-pager {\r\n        .number {\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 1px solid #d9d9d9;\r\n          background: #ffffff;\r\n          border-radius: 4px;\r\n          line-height: 32px;\r\n          margin: 0 6px;\r\n          &.active {\r\n            background: #21c9b8;\r\n            border: 1px solid #21c9b8;\r\n            color: #fff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;AA2DA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAC,sBAAA,CAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAM,SAAA;AACA,IAAAC,OAAA,GAAAC,OAAA,CAAAD,OAAA;EACA;AACA;AACA;AACA;AACA;EACAE,UAAA,WAAAA,WAAAC,OAAA;IACA,IAAAC,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAT,SAAA;IACA,IAAAU,IAAA,GAAAJ,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAL,OAAA;IACA,IAAAO,SAAA,GAAAL,iBAAA,CAAAM,GAAA,CAAAC,OAAA,CAAAH,IAAA,EAAAL,GAAA;MACAS,IAAA,EAAAR,iBAAA,CAAAQ,IAAA,CAAAC,GAAA;MACAC,OAAA,EAAAV,iBAAA,CAAAW,GAAA,CAAAC;IACA;IACA,OAAAP,SAAA,CAAAQ,QAAA;EACA;EAEA;AACA;AACA;AACA;AACA;EACAC,UAAA,WAAAA,WAAAC,UAAA;IACA,IAAAhB,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAT,SAAA;IACA,IAAAsB,OAAA,GAAAhB,iBAAA,CAAAM,GAAA,CAAAU,OAAA,CAAAD,UAAA,EAAAhB,GAAA;MACAS,IAAA,EAAAR,iBAAA,CAAAQ,IAAA,CAAAC,GAAA;MACAC,OAAA,EAAAV,iBAAA,CAAAW,GAAA,CAAAC;IACA;IACA,OAAAZ,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAe,SAAA,CAAAD,OAAA,EAAAH,QAAA;EACA;AACA;AAAA,IAAAK,QAAA,GAAAtB,OAAA,CAAAuB,OAAA,GACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,IAAA;MACAC,QAAA;MACAC,KAAA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,UAAA,EAAAzC,OAAA;IACA;EACA;EACA0C,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAH,QAAA,WAAAA,SAAA;MAAA,IAAAI,KAAA;MACA,IAAAC,cAAA,iBAAAC,IAAA,WAAAC,GAAA;QACA,IAAAC,IAAA,GAAAD,GAAA,CAAAC,IAAA;UAAAC,SAAA,GAAAF,GAAA,CAAAjB,IAAA;UAAAA,IAAA,GAAAmB,SAAA,mBAAAA,SAAA;QACA,IAAAD,IAAA;UACAJ,KAAA,CAAAZ,IAAA,GAAAF,IAAA;UACAc,KAAA,CAAAZ,IAAA,CAAAkB,OAAA;YACAC,SAAA;YACAC,SAAA,EAAAC,SAAA;YACAC,QAAA;YACAC,QAAA;UACA;UACA,IAAAC,IAAA,OAAAC,WAAA,EAAA3B,IAAA;UACAc,KAAA,CAAAF,aAAA,CAAAc,IAAA,CAAAJ,SAAA;QACA;MACA;IACA;IACAV,aAAA,WAAAA,cAAAgB,IAAA;MAAA,IAAAC,MAAA;MACA,KAAA5B,OAAA;MACA,IAAA6B,kBAAA;QACAC,UAAA,EAAAH,IAAA;QACAvB,OAAA,OAAAA,OAAA;QACA;QACA2B,aAAA;MACA,GACAhB,IAAA,WAAAC,GAAA;QACA,IAAAvC,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAT,SAAA;QACA,IAAAsB,OAAA,GAAAhB,iBAAA,CAAAM,GAAA,CAAAU,OAAA,CAAAsB,GAAA,EAAAvC,GAAA;UACAS,IAAA,EAAAR,iBAAA,CAAAQ,IAAA,CAAAC,GAAA;UACAC,OAAA,EAAAV,iBAAA,CAAAW,GAAA,CAAAC;QACA;QACA0B,GAAA,GAAAgB,IAAA,CAAAnD,KAAA,CAAAH,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAe,SAAA,CAAAD,OAAA;QACA,IAAAuC,IAAA,GAAAjB,GAAA;UAAAC,IAAA,GAAAgB,IAAA,CAAAhB,IAAA;UAAAiB,SAAA,GAAAD,IAAA,CAAAE,IAAA;UAAAA,IAAA,GAAAD,SAAA,mBAAAA,SAAA;QAEA,IAAAjB,IAAA;UACA;UACAW,MAAA,CAAAzB,KAAA,OAAAiC,UAAA,YAAAX,IAAA;YACA,IAAAY,GAAA;YACA,IAAAC,MAAA,GAAAN,IAAA,CAAAnD,KAAA,CAAA4C,IAAA,CAAAc,YAAA;YACA,IAAAD,MAAA,CAAAE,MAAA;cACAH,GAAA,OAAAX,WAAA,EAAAY,MAAA,EAAAD,GAAA;YACA;YACA;cACAI,EAAA,EAAAhB,IAAA,CAAAgB,EAAA;cACAC,KAAA,EAAAjB,IAAA,CAAAkB,WAAA;cACAC,OAAA,EACAnB,IAAA,CAAAoB,mBAAA,SACA,IAAAC,wBAAA,EAAArB,IAAA,CAAAsB,WAAA,IACAtB,IAAA,CAAAsB,WAAA;cACAV,GAAA,EAAAA;YACA;UACA,GAAAF,IAAA;QACA;QACAP,MAAA,CAAAtB,KAAA,GAAAU,GAAA,CAAAV,KAAA;QACA;MACA,GACA0C,OAAA;QACApB,MAAA,CAAA5B,OAAA;MACA;IACA;IACAiD,WAAA,WAAAA,YAAAC,KAAA;MACA,IAAAA,KAAA,UAAAhD,QAAA;QACA,KAAAA,QAAA,GAAAgD,KAAA;QACA,KAAA9C,OAAA;QACA,IAAAqB,IAAA,QAAAxB,IAAA,CAAAiD,KAAA;QACA,KAAAvC,aAAA,CAAAc,IAAA,CAAAJ,SAAA;MACA;IACA;IACA8B,gBAAA,WAAAA,iBAAA9C,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAD,OAAA;MACA,IAAAqB,IAAA,QAAAxB,IAAA,MAAAC,QAAA;MACA,KAAAS,aAAA,CAAAc,IAAA,CAAAJ,SAAA;IACA;IACA+B,mBAAA,WAAAA,oBAAAhD,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,IAAAqB,IAAA,QAAAxB,IAAA,MAAAC,QAAA;MACA,KAAAS,aAAA,CAAAc,IAAA,CAAAJ,SAAA;IACA;EACA;AACA", "ignoreList": []}]}