{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\workshopDetail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\manufacturingSharing\\components\\workshopDetail.vue", "mtime": 1750311962970}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["workshopDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "workshopDetail.vue", "sourceRoot": "src/views/manufacturingSharing/components", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"card-container cardStyle\">\r\n      <!-- 左侧 -->\r\n      <div class=\"card_left\">\r\n        <!-- 上半部分 -->\r\n        <div class=\"card_left_top\">\r\n          <div class=\"imgStyle\">\r\n            <img style=\"width: 100%; height: 100%\" :src=\"detailsData.images\r\n              ? detailsData.images.split(',')[0]\r\n              : require('@/assets/device/ceshi.png')\r\n              \" alt=\"\" />\r\n          </div>\r\n          <!-- <div class=\"imgContent\">\r\n            <div style=\"cursor: pointer\">\r\n              <img src=\"../../../assets/device/icon_left.png\" alt=\"\" />\r\n            </div>\r\n            <div style=\"display: flex; align-items: center; margin: 0 10px\">\r\n              <div\r\n                class=\"everyImgStyle\"\r\n                v-for=\"(item, index) in imgList\"\r\n                :key=\"index\"\r\n              >\r\n                <img\r\n                  style=\"width: 100%; height: 100%\"\r\n                  src=\"../../../assets/device/ceshi.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n            </div>\r\n            <div style=\"cursor: pointer\">\r\n              <img src=\"../../../assets/device/icon_right.png\" alt=\"\" />\r\n            </div>\r\n          </div> -->\r\n        </div>\r\n        <!-- 下半部分 -->\r\n        <div class=\"card_left_bottom\">\r\n          <div class=\"title\">{{ detailsData.name }}</div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">所属单位：</div>\r\n            <div class=\"optionValue\">{{ detailsData.company }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">车间地址：</div>\r\n            <div class=\"optionValue\">{{ detailsData.address }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">车间面积：</div>\r\n            <div class=\"optionValue\">{{ detailsData.area || \"--\" }}</div>\r\n          </div>\r\n          <div class=\"everyOption\">\r\n            <div class=\"optionName\">参考价格：</div>\r\n            <div class=\"optionValue\" style=\"color: #cc0a0a\">\r\n              {{ detailsData.price || \"--\" }}\r\n            </div>\r\n          </div>\r\n          <div class=\"buttonStyle\" @click=\"jumpIntention\">申请试用</div>\r\n        </div>\r\n      </div>\r\n      <!-- 中间 -->\r\n      <div class=\"card_center_line\"></div>\r\n      <!-- 右侧 -->\r\n      <div class=\"card_right\">\r\n        <div>\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">车间概况</div>\r\n          </div>\r\n          <div class=\"content_desc\">\r\n            {{ detailsData.description }}\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">设备资源</div>\r\n          </div>\r\n          <div class=\"content_desc\">\r\n            {{ detailsData.resources }}\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">生产能力</div>\r\n          </div>\r\n          <div class=\"content_desc\">\r\n            {{ detailsData.capability }}\r\n          </div>\r\n        </div>\r\n        <div style=\"margin-top: 41px\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">注意事项</div>\r\n          </div>\r\n          <div class=\"content_desc\">\r\n            {{ detailsData.notes }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { workDetailData } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"deviceDetail\",\r\n  data() {\r\n    return {\r\n      detailsData: {},\r\n      imgList: [\r\n        {\r\n          id: 1,\r\n        },\r\n        {\r\n          id: 2,\r\n        },\r\n        {\r\n          id: 3,\r\n        },\r\n        {\r\n          id: 4,\r\n        },\r\n        {\r\n          id: 5,\r\n        },\r\n      ],\r\n      id: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.id = this.$route.query.id;\r\n    this.getWorkData();\r\n  },\r\n  methods: {\r\n    getWorkData() {\r\n      workDetailData(this.id).then((res) => {\r\n        if (res.code === 200) {\r\n          this.detailsData = res.data;\r\n        }\r\n      });\r\n    },\r\n    jumpIntention() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(`/demandInterested?demandName=${this.detailsData.name}&updateTime=${this.detailsData.updateTime}&intentionType=4&fieldName=车间共享&intentionId=${this.detailsData.id}`);\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  background-color: #f2f2f2;\r\n  padding: 30px 0 61px 0;\r\n}\r\n\r\n.cardStyle {\r\n  height: 660px;\r\n  background-color: #ffffff;\r\n  padding: 60px 56px 54px 50px;\r\n  display: flex;\r\n}\r\n\r\n.card_left {\r\n  .card_left_top {\r\n    .imgStyle {\r\n      width: 330px;\r\n      height: 230px;\r\n      border-radius: 2px;\r\n      margin-left: 10px;\r\n    }\r\n\r\n    .imgContent {\r\n      margin-top: 15px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .everyImgStyle {\r\n        width: 54px;\r\n        height: 50px;\r\n        margin-left: 10px;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .everyImgStyle:nth-child(1) {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .card_left_bottom {\r\n    margin-top: 30px;\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      font-size: 20px;\r\n      color: #222222;\r\n      margin-bottom: 13px;\r\n    }\r\n\r\n    .everyOption {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-top: 12px;\r\n\r\n      .optionName {\r\n        min-height: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #999999;\r\n        min-width: 75px;\r\n      }\r\n\r\n      .optionValue {\r\n        min-height: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #333333;\r\n      }\r\n    }\r\n\r\n    .buttonStyle {\r\n      margin-top: 32px;\r\n      margin-left: 55px;\r\n      width: 220px;\r\n      height: 50px;\r\n      background: #21c9b8;\r\n      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);\r\n      border-radius: 2px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #ffffff;\r\n      text-align: center;\r\n      line-height: 50px;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n\r\n.card_center_line {\r\n  width: 1px;\r\n  height: 100%;\r\n  background: #e1e1e1;\r\n  margin-left: 51px;\r\n  margin-right: 61px;\r\n}\r\n\r\n.card_right {\r\n  width: 100%;\r\n  overflow-y: auto;\r\n\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n\r\n  .content_desc {\r\n    // width: 631px;\r\n    // height: 159px;\r\n    margin-top: 20px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #666666;\r\n    line-height: 24px;\r\n  }\r\n}\r\n</style>\r\n"]}]}