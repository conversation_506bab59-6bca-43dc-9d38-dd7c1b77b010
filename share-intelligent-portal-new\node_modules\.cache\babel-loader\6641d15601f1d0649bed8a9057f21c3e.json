{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\oss.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\oss.js", "mtime": 1750311961327}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkT3NzID0gYWRkT3NzOwpleHBvcnRzLmRlbE9zcyA9IGRlbE9zczsKZXhwb3J0cy5nZXRPc3MgPSBnZXRPc3M7CmV4cG9ydHMubGlzdE9zcyA9IGxpc3RPc3M7CmV4cG9ydHMudXBkYXRlT3NzID0gdXBkYXRlT3NzOwpleHBvcnRzLnVwbG9hZCA9IHVwbG9hZDsKZXhwb3J0cy51cGxvYWRGaWxlID0gdXBsb2FkRmlsZTsKZXhwb3J0cy51cGxvYWRVcmwgPSB1cGxvYWRVcmw7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovKg0KICogQEF1dGhvcjogemhjDQogKiBARGF0ZTogMjAyMy0wMi0wNyAxNzo0MzoxNA0KICogQExhc3RFZGl0VGltZTogMjAyMy0wMi0xMiAxMjowNDowMA0KICogQERlc2NyaXB0aW9uOg0KICogQExhc3RFZGl0b3JzOiB6aGMNCiAqLwovKg0KICogQEF1dGhvcjogemhjDQogKiBARGF0ZTogMjAyMy0wMS0zMSAxMToxMzoyMw0KICogQExhc3RFZGl0VGltZTogMjAyMy0wMS0zMSAxMToxMzoyMw0KICogQERlc2NyaXB0aW9uOg0KICogQExhc3RFZGl0b3JzOiB6aGMNCiAqLwoKLy8g5p+l6K+i5LqR5YKo5a2Y5YiX6KGoCmZ1bmN0aW9uIGxpc3RPc3MocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogIi94aXBpbi9vc3MvbGlzdCIsCiAgICBtZXRob2Q6ICJnZXQiLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDmn6Xor6LkupHlgqjlrZjor6bnu4YKZnVuY3Rpb24gZ2V0T3NzKGlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICIveGlwaW4vb3NzLyIgKyBpZCwKICAgIG1ldGhvZDogImdldCIKICB9KTsKfQoKLy8g5paw5aKe5LqR5YKo5a2YCmZ1bmN0aW9uIGFkZE9zcyhkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICIveGlwaW4vb3NzIiwKICAgIG1ldGhvZDogInBvc3QiLAogICAgZGF0YTogZGF0YQogIH0pOwp9Ci8vIOaWsOWinuS6keWCqOWtmApmdW5jdGlvbiB1cGxvYWRGaWxlKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogIi9maWxlL3VwbG9hZCIsCiAgICBtZXRob2Q6ICJwb3N0IiwKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55LqR5YKo5a2YCmZ1bmN0aW9uIHVwZGF0ZU9zcyhkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICIveGlwaW4vb3NzIiwKICAgIG1ldGhvZDogInB1dCIsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOS6keWCqOWtmApmdW5jdGlvbiBkZWxPc3MoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogIi9zeXN0ZW0vb3NzL2RlbGV0ZS8iICsgaWQsCiAgICBtZXRob2Q6ICJkZWxldGUiCiAgfSk7Cn0KCi8vIOS/ruaUueS6keWCqOWtmApmdW5jdGlvbiB1cGxvYWQoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAiL3hpcGluL29zcyIsCiAgICBtZXRob2Q6ICJwb3N0IiwKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS55LqR5YKo5a2YCmZ1bmN0aW9uIHVwbG9hZFVybCgpIHsKICByZXR1cm4gcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvZmlsZS91cGxvYWQiOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listOss", "query", "request", "url", "method", "params", "getOss", "id", "addOss", "data", "uploadFile", "updateOss", "del<PERSON>s", "upload", "uploadUrl", "process", "env", "VUE_APP_BASE_API"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/api/oss.js"], "sourcesContent": ["/*\r\n * @Author: zhc\r\n * @Date: 2023-02-07 17:43:14\r\n * @LastEditTime: 2023-02-12 12:04:00\r\n * @Description:\r\n * @LastEditors: zhc\r\n */\r\n/*\r\n * @Author: zhc\r\n * @Date: 2023-01-31 11:13:23\r\n * @LastEditTime: 2023-01-31 11:13:23\r\n * @Description:\r\n * @LastEditors: zhc\r\n */\r\nimport request from \"@/utils/request\";\r\n\r\n// 查询云储存列表\r\nexport function listOss(query) {\r\n  return request({\r\n    url: \"/xipin/oss/list\",\r\n    method: \"get\",\r\n    params: query,\r\n  });\r\n}\r\n\r\n// 查询云储存详细\r\nexport function getOss(id) {\r\n  return request({\r\n    url: \"/xipin/oss/\" + id,\r\n    method: \"get\",\r\n  });\r\n}\r\n\r\n// 新增云储存\r\nexport function addOss(data) {\r\n  return request({\r\n    url: \"/xipin/oss\",\r\n    method: \"post\",\r\n    data: data,\r\n  });\r\n}\r\n// 新增云储存\r\nexport function uploadFile(data) {\r\n  return request({\r\n    url: \"/file/upload\",\r\n    method: \"post\",\r\n    data: data,\r\n  });\r\n}\r\n\r\n// 修改云储存\r\nexport function updateOss(data) {\r\n  return request({\r\n    url: \"/xipin/oss\",\r\n    method: \"put\",\r\n    data: data,\r\n  });\r\n}\r\n\r\n// 删除云储存\r\nexport function delOss(id) {\r\n  return request({\r\n    url: \"/system/oss/delete/\" + id,\r\n    method: \"delete\",\r\n  });\r\n}\r\n\r\n// 修改云储存\r\nexport function upload(data) {\r\n  return request({\r\n    url: \"/xipin/oss\",\r\n    method: \"post\",\r\n    data: data,\r\n  });\r\n}\r\n\r\n// 修改云储存\r\nexport function uploadUrl() {\r\n  return process.env.VUE_APP_BASE_API + \"/file/upload\";\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;AAcA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACO,SAASC,OAAOA,CAACC,KAAK,EAAE;EAC7B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,MAAMA,CAACC,EAAE,EAAE;EACzB,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,aAAa,GAAGI,EAAE;IACvBH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,MAAMA,CAACC,IAAI,EAAE;EAC3B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AACA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACF,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,MAAMA,CAACL,EAAE,EAAE;EACzB,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGI,EAAE;IAC/BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,MAAMA,CAACJ,IAAI,EAAE;EAC3B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAAA,EAAG;EAC1B,OAAOC,OAAO,CAACC,GAAG,CAACC,gBAAgB,GAAG,cAAc;AACtD", "ignoreList": []}]}