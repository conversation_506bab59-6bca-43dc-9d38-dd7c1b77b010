import request from '@/utils/request'

// 查询用工信息列表
export function listJobInfo(query) {
  return request({
    url: '/system/jobInfo/list',
    method: 'get',
    params: query
  })
}

// 查询用工信息详细
export function getJobInfo(id) {
  return request({
    url: '/system/jobInfo/' + id,
    method: 'get'
  })
}

// 新增用工信息
export function addJobInfo(data) {
  return request({
    url: '/system/jobInfo',
    method: 'post',
    data: data
  })
}

// 修改用工信息
export function updateJobInfo(data) {
  return request({
    url: '/system/jobInfo',
    method: 'put',
    data: data
  })
}

// 删除用工信息
export function delJobInfo(id) {
  return request({
    url: '/system/jobInfo/' + id,
    method: 'delete'
  })
}
