{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyApply\\detail\\index1.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyApply\\detail\\index1.vue", "mtime": 1750311963046}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_demand", "_oss", "_auth", "_store", "_zhm", "_cryptoJs", "id", "secret<PERSON>ey", "_default", "exports", "default", "name", "dicts", "components", "UserMenu", "data", "isDetail", "title", "imageUrl", "actionUrl", "uploadUrl", "headers", "Authorization", "getToken", "process", "env", "VUE_APP_BASE_API", "accept", "isCreate", "imgVisible", "user", "tel", "store", "getters", "companyName", "bussinessNo", "phonenumber", "keywords", "applicationsInput", "info", "displayRestrictions", "undefined", "form", "accountLicenceList", "list", "tableData", "rules", "demandTitle", "required", "message", "trigger", "summary", "contactsName", "contactsMobile", "created", "handleKeywordList", "methods", "initForm", "applicationArea", "scenePicture", "applicationAreaList", "scenePictureList", "auditStatus", "displayStatus", "publisherName", "publisherMobile", "handleDelete", "$router", "push", "getDetail", "_this", "$route", "query", "getDemandDetail", "then", "response", "key", "CryptoJS", "enc", "Utf8", "parse", "decrypt", "AES", "mode", "ECB", "padding", "pad", "Pkcs7", "JSON", "stringify", "total", "goBack", "go", "getUrl", "str", "length", "url", "handleInputConfirm", "val", "handleClose", "tag", "splice", "indexOf", "handleSummaryClose", "handleBeforeUpload", "file", "type", "size", "typeList", "split", "map", "item", "trim", "toLowerCase", "substr", "dotIndex", "lastIndexOf", "$message", "error", "suffix", "substring", "handlePictureCardPreview", "handleRemove", "fileList", "handleSuccess", "code", "changeMode", "goCreate", "businessNo", "handleFilePreview", "window", "open", "displayRestrictionChanged", "res", "_this2", "dict", "display_restrictions", "for<PERSON>ach", "label", "value", "submitForm", "_this3", "$refs", "validate", "valid", "join", "createDemand", "_objectSpread2", "isSubmit", "$modal", "msgSuccess", "edit<PERSON><PERSON><PERSON>", "_this4", "console", "log", "technologyType", "keywordList2", "rows", "handleApplicationRemove", "application", "handleApplicationSuccess", "applicationName", "handleAccountRemove", "accountLicence", "handleAccountSuccess", "accountLicenceName"], "sources": ["src/views/system/user/companyApply/detail/index1.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-11 15:20:15\r\n * @LastEditTime: 2023-02-28 08:48:59\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-11 15:18:41\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"company-demand-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">匹配资源列表</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                资源列表\r\n              </div>\r\n            </div>\r\n            <el-table :data=\"tableData\" style=\"width: 100%\">\r\n              <el-table-column prop=\"companyName\" label=\"公司名称\" width=\"240\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"applicationArea\"\r\n                label=\"应用领域\"\r\n                width=\"180\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  -webkit-line-clamp: 2;\r\n                  line-clamp: 2;\r\n                \"\r\n                label=\"描述\"\r\n              >\r\n                <template slot-scope=\"scoped\">\r\n                  <div v-html=\"scoped.row.summary\"></div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"danger\"\r\n                    @click=\"handleDelete(scope.row)\"\r\n                    >查看详情</el-button\r\n                  >\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport { getDemandDetail, createDemand, editDemand } from \"@/api/system/demand\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport store from \"@/store\";\r\nimport { keywordList2 } from \"@/api/zhm\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\r\n    \"affiliated_unit\",\r\n    \"capital_source\",\r\n    \"affiliated_street\",\r\n    \"display_restrictions\",\r\n  ],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      title: \"需求详情\",\r\n      imageUrl: \"\",\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      isCreate: false,\r\n      imgVisible: false,\r\n      user: {\r\n        tel: store.getters.tel,\r\n        name: store.getters.name,\r\n        companyName: store.getters.companyName,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        phonenumber: store.getters.phonenumber,\r\n      },\r\n      keywords: [],\r\n      applicationsInput: \"\",\r\n      info: {},\r\n      // 展示限制\r\n      displayRestrictions: undefined,\r\n      form: {},\r\n      accountLicenceList: [],\r\n      list: {},\r\n      tableData: [],\r\n      // 表单校验\r\n      rules: {\r\n        demandTitle: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" },\r\n        ],\r\n        displayRestrictions: [\r\n          { required: true, message: \"请选择展示性质\", trigger: \"blur\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"需求描述不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.handleKeywordList();\r\n  },\r\n  methods: {\r\n    initForm() {\r\n      this.form = {\r\n        applicationArea: [],\r\n        scenePicture: [],\r\n        applicationAreaList: [],\r\n        scenePictureList: [],\r\n        keywords: [],\r\n        auditStatus: \"1\",\r\n        displayStatus: \"2\",\r\n        publisherName: this.user.name,\r\n        publisherMobile: this.user.tel,\r\n      };\r\n    },\r\n    handleDelete(data) {\r\n      this.$router.push(\"/resourceHallDetail?id=\" + data.id);\r\n    },\r\n    getDetail() {\r\n      let id = this.$route.query.id;\r\n      getDemandDetail(id).then((response) => {\r\n        let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n        let decrypt = CryptoJS.AES.decrypt(response, key, {\r\n          mode: CryptoJS.mode.ECB,\r\n          padding: CryptoJS.pad.Pkcs7,\r\n        });\r\n        response = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    getUrl(str) {\r\n      if (str && str != null) {\r\n        var list = JSON.parse(str);\r\n        if (list && list.length > 0) {\r\n          return list[0].url;\r\n        }\r\n      }\r\n\r\n      return null;\r\n    },\r\n    // 应用领域新增\r\n    handleInputConfirm() {\r\n      let val = this.applicationsInput;\r\n      if (val) {\r\n        this.form.applicationAreaList.push(val);\r\n      }\r\n      this.applicationsInput = \"\";\r\n    },\r\n    // 应用领域移除\r\n    handleClose(tag) {\r\n      this.form.applicationAreaList.splice(\r\n        this.form.applicationAreaList.indexOf(tag),\r\n        1\r\n      );\r\n    },\r\n    handleSummaryClose(tag) {\r\n      this.form.keywords.splice(this.form.keywords.indexOf(tag), 1);\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePictureCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.form.scenePictureList = fileList;\r\n    },\r\n    handleSuccess(response, file) {\r\n      if (response.code == 200) {\r\n        if (this.form.scenePictureList == null) {\r\n          this.form.scenePictureList = [];\r\n        }\r\n        this.form.scenePictureList.push(response.data);\r\n      }\r\n    },\r\n    changeMode() {\r\n      if (this.isCreate) {\r\n        this.goBack();\r\n        return;\r\n      }\r\n      if (this.isDetail) {\r\n        this.title = \"编辑需求\";\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n        if (this.info.applicationArea) {\r\n          this.form.applicationAreaList = this.info.applicationArea.split(\",\");\r\n        } else {\r\n          this.form.applicationAreaList = [];\r\n        }\r\n        if (this.info.keywords) {\r\n          this.form.keywords = this.info.keywords.split(\",\");\r\n        }\r\n\r\n        if (this.info.scenePicture && this.info.scenePicture != \"null\") {\r\n          this.form.scenePictureList = JSON.parse(this.info.scenePicture);\r\n        } else {\r\n          this.form.scenePictureList = [];\r\n        }\r\n      } else {\r\n        this.isDetail = true;\r\n        this.title = \"需求详情\";\r\n        this.initForm();\r\n        this.getDetail();\r\n      }\r\n    },\r\n    goCreate() {\r\n      this.title = \"新增需求\";\r\n      this.isDetail = false;\r\n      this.initForm();\r\n      this.form.companyName = this.user.companyName;\r\n      this.form.contactsName = this.user.name;\r\n      this.form.contactsMobile = this.user.phonenumber;\r\n      this.form.publisherName = this.user.name;\r\n      this.form.publisherMobile = this.user.phonenumber;\r\n      this.form.businessNo = this.user.bussinessNo;\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    displayRestrictionChanged(res) {\r\n      this.dict.type.display_restrictions.forEach((item) => {\r\n        if (item.label == res) {\r\n          this.form.displayRestrictions = item.value;\r\n        }\r\n      });\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (\r\n            this.form.applicationArea &&\r\n            this.form.applicationArea.length > 0\r\n          ) {\r\n            this.form.applicationArea = this.form.applicationAreaList.join(\",\");\r\n          } else {\r\n            this.form.applicationArea = \"\";\r\n          }\r\n          this.form.scenePicture = JSON.stringify(this.form.scenePictureList);\r\n          this.form.businessNo = this.user.bussinessNo;\r\n          if (this.form.keywords && this.form.keywords.length > 0) {\r\n            this.form.keywords = this.form.keywords.join(\",\");\r\n          } else {\r\n            this.form.keywords = \"\";\r\n          }\r\n          if (this.isCreate) {\r\n            createDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          } else {\r\n            this.form.auditStatus = 1;\r\n            editDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      console.log(this.$route.query.key);\r\n      let info = JSON.parse(this.$route.query.key);\r\n      this.list.technologyType = info.technologyType;\r\n      this.list.summary = info.summary;\r\n      this.list.applicationArea = info.applicationArea;\r\n      keywordList2(this.list).then((res) => {\r\n        this.tableData = res.rows;\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.application = res.data.url;\r\n        this.form.applicationName = res.data.name;\r\n      }\r\n    },\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .company-demand-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 20px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .mt_40 {\r\n          margin-top: 40px;\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 90%;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-textarea__inner {\r\n          width: 90%;\r\n        }\r\n        .add-demand-tag {\r\n          margin-right: 10px;\r\n          height: 32px;\r\n          line-height: 32px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n          color: #333;\r\n        }\r\n        .el-button:hover,\r\n        .el-button:focus {\r\n          border-color: #d9d9d9;\r\n          background-color: #fff;\r\n        }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8 !important;\r\n          border-color: #21c9b8 !important;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA6EA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,IAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,IAAA,GAAAL,OAAA;AACA,IAAAM,SAAA,GAAAP,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARA,IAAAO,EAAA;AASA,IAAAC,SAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,KAAA,GACA,mBACA,kBACA,qBACA,uBACA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,KAAA;MACAC,QAAA;MACAC,SAAA,MAAAC,cAAA;MACAC,OAAA;QAAAC,aAAA,kBAAAC,cAAA;MAAA;MACAH,SAAA,EAAAI,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,MAAA;MACAC,QAAA;MACAC,UAAA;MACAC,IAAA;QACAC,GAAA,EAAAC,cAAA,CAAAC,OAAA,CAAAF,GAAA;QACApB,IAAA,EAAAqB,cAAA,CAAAC,OAAA,CAAAtB,IAAA;QACAuB,WAAA,EAAAF,cAAA,CAAAC,OAAA,CAAAC,WAAA;QACAC,WAAA,EAAAH,cAAA,CAAAC,OAAA,CAAAE,WAAA;QACAC,WAAA,EAAAJ,cAAA,CAAAC,OAAA,CAAAG;MACA;MACAC,QAAA;MACAC,iBAAA;MACAC,IAAA;MACA;MACAC,mBAAA,EAAAC,SAAA;MACAC,IAAA;MACAC,kBAAA;MACAC,IAAA;MACAC,SAAA;MACA;MACAC,KAAA;QACAC,WAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,mBAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,OAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,YAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,WAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,cAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAAf,IAAA;QACAgB,eAAA;QACAC,YAAA;QACAC,mBAAA;QACAC,gBAAA;QACAxB,QAAA;QACAyB,WAAA;QACAC,aAAA;QACAC,aAAA,OAAAlC,IAAA,CAAAnB,IAAA;QACAsD,eAAA,OAAAnC,IAAA,CAAAC;MACA;IACA;IACAmC,YAAA,WAAAA,aAAAnD,IAAA;MACA,KAAAoD,OAAA,CAAAC,IAAA,6BAAArD,IAAA,CAAAT,EAAA;IACA;IACA+D,SAAA,WAAAA,UAAA;MAAA,IAAAC,KAAA;MACA,IAAAhE,EAAA,QAAAiE,MAAA,CAAAC,KAAA,CAAAlE,EAAA;MACA,IAAAmE,uBAAA,EAAAnE,EAAA,EAAAoE,IAAA,WAAAC,QAAA;QACA,IAAAC,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAzE,SAAA;QACA,IAAA0E,OAAA,GAAAJ,iBAAA,CAAAK,GAAA,CAAAD,OAAA,CAAAN,QAAA,EAAAC,GAAA;UACAO,IAAA,EAAAN,iBAAA,CAAAM,IAAA,CAAAC,GAAA;UACAC,OAAA,EAAAR,iBAAA,CAAAS,GAAA,CAAAC;QACA;QACAZ,QAAA,GAAAa,IAAA,CAAAR,KAAA,CAAAH,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAU,SAAA,CAAAR,OAAA;QACAX,KAAA,CAAA/B,IAAA,GAAAoC,QAAA,CAAA5D,IAAA;QACAuD,KAAA,CAAAoB,KAAA,GAAAf,QAAA,CAAAe,KAAA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAxB,OAAA,CAAAyB,EAAA;IACA;IACAC,MAAA,WAAAA,OAAAC,GAAA;MACA,IAAAA,GAAA,IAAAA,GAAA;QACA,IAAAlD,IAAA,GAAA4C,IAAA,CAAAR,KAAA,CAAAc,GAAA;QACA,IAAAlD,IAAA,IAAAA,IAAA,CAAAmD,MAAA;UACA,OAAAnD,IAAA,IAAAoD,GAAA;QACA;MACA;MAEA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,IAAAC,GAAA,QAAA5D,iBAAA;MACA,IAAA4D,GAAA;QACA,KAAAxD,IAAA,CAAAkB,mBAAA,CAAAQ,IAAA,CAAA8B,GAAA;MACA;MACA,KAAA5D,iBAAA;IACA;IACA;IACA6D,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAA1D,IAAA,CAAAkB,mBAAA,CAAAyC,MAAA,CACA,KAAA3D,IAAA,CAAAkB,mBAAA,CAAA0C,OAAA,CAAAF,GAAA,GACA,CACA;IACA;IACAG,kBAAA,WAAAA,mBAAAH,GAAA;MACA,KAAA1D,IAAA,CAAAL,QAAA,CAAAgE,MAAA,MAAA3D,IAAA,CAAAL,QAAA,CAAAiE,OAAA,CAAAF,GAAA;IACA;IACA;IACAI,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAA9F,IAAA,GAAA8F,IAAA,CAAA9F,IAAA;QAAA+F,IAAA,GAAAD,IAAA,CAAAC,IAAA;QAAAC,IAAA,GAAAF,IAAA,CAAAE,IAAA;MACA,IAAAC,QAAA,QAAAjF,MAAA,CACAkF,KAAA,MACAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA,GAAAC,WAAA,GAAAC,MAAA;MAAA;MACA,IAAAC,QAAA,GAAAxG,IAAA,CAAAyG,WAAA;MACA;MACA,IAAAD,QAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;QACA,IAAAC,MAAA,GAAA5G,IAAA,CAAA6G,SAAA,CAAAL,QAAA;QACA,IAAAP,QAAA,CAAAN,OAAA,CAAAiB,MAAA,CAAAN,WAAA;UACA,KAAAI,QAAA,CAAAC,KAAA;UACA;QACA;MACA;MACA;MACA,IAAAX,IAAA;QACA,KAAAU,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACA;IACAG,wBAAA,WAAAA,yBAAAhB,IAAA;MACA,KAAAvF,QAAA,GAAAuF,IAAA,CAAAT,GAAA;MACA,KAAAnE,UAAA;IACA;IACA;IACA6F,YAAA,WAAAA,aAAAjB,IAAA,EAAAkB,QAAA;MACA,KAAAjF,IAAA,CAAAmB,gBAAA,GAAA8D,QAAA;IACA;IACAC,aAAA,WAAAA,cAAAjD,QAAA,EAAA8B,IAAA;MACA,IAAA9B,QAAA,CAAAkD,IAAA;QACA,SAAAnF,IAAA,CAAAmB,gBAAA;UACA,KAAAnB,IAAA,CAAAmB,gBAAA;QACA;QACA,KAAAnB,IAAA,CAAAmB,gBAAA,CAAAO,IAAA,CAAAO,QAAA,CAAA5D,IAAA;MACA;IACA;IACA+G,UAAA,WAAAA,WAAA;MACA,SAAAlG,QAAA;QACA,KAAA+D,MAAA;QACA;MACA;MACA,SAAA3E,QAAA;QACA,KAAAC,KAAA;QACA,KAAAD,QAAA;QACA,KAAA0B,IAAA,QAAAH,IAAA;QACA,SAAAA,IAAA,CAAAmB,eAAA;UACA,KAAAhB,IAAA,CAAAkB,mBAAA,QAAArB,IAAA,CAAAmB,eAAA,CAAAmD,KAAA;QACA;UACA,KAAAnE,IAAA,CAAAkB,mBAAA;QACA;QACA,SAAArB,IAAA,CAAAF,QAAA;UACA,KAAAK,IAAA,CAAAL,QAAA,QAAAE,IAAA,CAAAF,QAAA,CAAAwE,KAAA;QACA;QAEA,SAAAtE,IAAA,CAAAoB,YAAA,SAAApB,IAAA,CAAAoB,YAAA;UACA,KAAAjB,IAAA,CAAAmB,gBAAA,GAAA2B,IAAA,CAAAR,KAAA,MAAAzC,IAAA,CAAAoB,YAAA;QACA;UACA,KAAAjB,IAAA,CAAAmB,gBAAA;QACA;MACA;QACA,KAAA7C,QAAA;QACA,KAAAC,KAAA;QACA,KAAAwC,QAAA;QACA,KAAAY,SAAA;MACA;IACA;IACA0D,QAAA,WAAAA,SAAA;MACA,KAAA9G,KAAA;MACA,KAAAD,QAAA;MACA,KAAAyC,QAAA;MACA,KAAAf,IAAA,CAAAR,WAAA,QAAAJ,IAAA,CAAAI,WAAA;MACA,KAAAQ,IAAA,CAAAU,YAAA,QAAAtB,IAAA,CAAAnB,IAAA;MACA,KAAA+B,IAAA,CAAAW,cAAA,QAAAvB,IAAA,CAAAM,WAAA;MACA,KAAAM,IAAA,CAAAsB,aAAA,QAAAlC,IAAA,CAAAnB,IAAA;MACA,KAAA+B,IAAA,CAAAuB,eAAA,QAAAnC,IAAA,CAAAM,WAAA;MACA,KAAAM,IAAA,CAAAsF,UAAA,QAAAlG,IAAA,CAAAK,WAAA;IACA;IACA8F,iBAAA,WAAAA,kBAAAxB,IAAA;MACAyB,MAAA,CAAAC,IAAA,CAAA1B,IAAA;IACA;IACA2B,yBAAA,WAAAA,0BAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,IAAA,CAAA7B,IAAA,CAAA8B,oBAAA,CAAAC,OAAA,WAAA1B,IAAA;QACA,IAAAA,IAAA,CAAA2B,KAAA,IAAAL,GAAA;UACAC,MAAA,CAAA5F,IAAA,CAAAF,mBAAA,GAAAuE,IAAA,CAAA4B,KAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAlC,IAAA;MAAA,IAAAmC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IACAH,MAAA,CAAAnG,IAAA,CAAAgB,eAAA,IACAmF,MAAA,CAAAnG,IAAA,CAAAgB,eAAA,CAAAqC,MAAA,MACA;YACA8C,MAAA,CAAAnG,IAAA,CAAAgB,eAAA,GAAAmF,MAAA,CAAAnG,IAAA,CAAAkB,mBAAA,CAAAqF,IAAA;UACA;YACAJ,MAAA,CAAAnG,IAAA,CAAAgB,eAAA;UACA;UACAmF,MAAA,CAAAnG,IAAA,CAAAiB,YAAA,GAAA6B,IAAA,CAAAC,SAAA,CAAAoD,MAAA,CAAAnG,IAAA,CAAAmB,gBAAA;UACAgF,MAAA,CAAAnG,IAAA,CAAAsF,UAAA,GAAAa,MAAA,CAAA/G,IAAA,CAAAK,WAAA;UACA,IAAA0G,MAAA,CAAAnG,IAAA,CAAAL,QAAA,IAAAwG,MAAA,CAAAnG,IAAA,CAAAL,QAAA,CAAA0D,MAAA;YACA8C,MAAA,CAAAnG,IAAA,CAAAL,QAAA,GAAAwG,MAAA,CAAAnG,IAAA,CAAAL,QAAA,CAAA4G,IAAA;UACA;YACAJ,MAAA,CAAAnG,IAAA,CAAAL,QAAA;UACA;UACA,IAAAwG,MAAA,CAAAjH,QAAA;YACA,IAAAsH,oBAAA,MAAAC,cAAA,CAAAzI,OAAA,MAAAyI,cAAA,CAAAzI,OAAA,MAAAmI,MAAA,CAAAnG,IAAA;cAAA0G,QAAA,EAAA1C;YAAA,IAAAhC,IAAA,WAAAC,QAAA;cACAkE,MAAA,CAAAQ,MAAA,CAAAC,UAAA;cACAT,MAAA,CAAAf,UAAA;YACA;UACA;YACAe,MAAA,CAAAnG,IAAA,CAAAoB,WAAA;YACA,IAAAyF,kBAAA,MAAAJ,cAAA,CAAAzI,OAAA,MAAAyI,cAAA,CAAAzI,OAAA,MAAAmI,MAAA,CAAAnG,IAAA;cAAA0G,QAAA,EAAA1C;YAAA,IAAAhC,IAAA,WAAAC,QAAA;cACAkE,MAAA,CAAAQ,MAAA,CAAAC,UAAA;cACAT,MAAA,CAAAf,UAAA;YACA;UACA;QACA;MACA;IACA;IACAvE,iBAAA,WAAAA,kBAAA;MAAA,IAAAiG,MAAA;MACAC,OAAA,CAAAC,GAAA,MAAAnF,MAAA,CAAAC,KAAA,CAAAI,GAAA;MACA,IAAArC,IAAA,GAAAiD,IAAA,CAAAR,KAAA,MAAAT,MAAA,CAAAC,KAAA,CAAAI,GAAA;MACA,KAAAhC,IAAA,CAAA+G,cAAA,GAAApH,IAAA,CAAAoH,cAAA;MACA,KAAA/G,IAAA,CAAAO,OAAA,GAAAZ,IAAA,CAAAY,OAAA;MACA,KAAAP,IAAA,CAAAc,eAAA,GAAAnB,IAAA,CAAAmB,eAAA;MACA,IAAAkG,iBAAA,OAAAhH,IAAA,EAAA8B,IAAA,WAAA2D,GAAA;QACAmB,MAAA,CAAA3G,SAAA,GAAAwF,GAAA,CAAAwB,IAAA;MACA;IACA;IACAC,uBAAA,WAAAA,wBAAArD,IAAA,EAAAkB,QAAA;MACA,KAAAjF,IAAA,CAAAqH,WAAA;IACA;IACAC,wBAAA,WAAAA,yBAAA3B,GAAA,EAAA5B,IAAA,EAAAkB,QAAA;MACA;MACA,IAAAU,GAAA,CAAAR,IAAA;QACA,KAAAnF,IAAA,CAAAqH,WAAA,GAAA1B,GAAA,CAAAtH,IAAA,CAAAiF,GAAA;QACA,KAAAtD,IAAA,CAAAuH,eAAA,GAAA5B,GAAA,CAAAtH,IAAA,CAAAJ,IAAA;MACA;IACA;IACAuJ,mBAAA,WAAAA,oBAAAzD,IAAA,EAAAkB,QAAA;MACA,KAAAjF,IAAA,CAAAyH,cAAA;IACA;IACAC,oBAAA,WAAAA,qBAAA/B,GAAA,EAAA5B,IAAA,EAAAkB,QAAA;MACA;MACA,IAAAU,GAAA,CAAAR,IAAA;QACA,KAAAnF,IAAA,CAAAyH,cAAA,GAAA9B,GAAA,CAAAtH,IAAA,CAAAiF,GAAA;QACA,KAAAtD,IAAA,CAAA2H,kBAAA,GAAAhC,GAAA,CAAAtH,IAAA,CAAAJ,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}