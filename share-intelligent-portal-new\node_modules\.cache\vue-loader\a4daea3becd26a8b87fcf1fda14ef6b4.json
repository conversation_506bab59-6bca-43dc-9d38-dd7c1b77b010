{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\equipmentManagement\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\equipmentManagement\\index.vue", "mtime": 1750311963057}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4EA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/equipmentManagement", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"top\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">设备管理</div>\r\n          </div>\r\n          <el-button class=\"btn\" type=\"primary\" plain @click=\"handleAdd\">发布设备信息</el-button>\r\n        </div>\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"100px\"\r\n          style=\"margin-top: 20px\">\r\n          <el-form-item label=\"设备名称\" prop=\"name\">\r\n            <el-input v-model=\"queryParams.name\" placeholder=\"请输入设备名称\" clearable @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"设备分类\" prop=\"category\">\r\n            <el-select v-model=\"queryParams.category\" placeholder=\"请选择设备分类\" clearable style=\"width: 100%\">\r\n              <el-option v-for=\"dict in deviceMenuList\" :key=\"dict.dictLabel\" :label=\"dict.dictLabel\"\r\n                :value=\"dict.dictValue\" />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"所属单位/位置\" prop=\"location\">\r\n            <el-input v-model=\"queryParams.location\" placeholder=\"请输入所属单位/位置\" clearable\r\n              @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"租用模式\" prop=\"rentMode\">\r\n            <el-input v-model=\"queryParams.rentMode\" placeholder=\"请输入租用模式\" clearable\r\n              @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"租用价格\" prop=\"rentPrice\">\r\n            <el-input v-model=\"queryParams.rentPrice\" placeholder=\"请输入租用价格\" clearable\r\n              @keyup.enter.native=\"handleQuery\" />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div class=\"table\">\r\n          <div style=\"width: 100%\">\r\n            <el-table :data=\"tableData\" style=\"width: 100%\" v-loading=\"loading\">\r\n              <el-table-column label=\"设备ID\" align=\"center\" prop=\"id\" />\r\n              <el-table-column label=\"设备名称\" align=\"center\" prop=\"name\" />\r\n              <el-table-column label=\"设备分类\" align=\"center\" prop=\"category\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ getDeviceType(scope.row.category) }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"所属单位/位置\" align=\"center\" prop=\"location\" />\r\n              <el-table-column label=\"发布时间\" align=\"center\" prop=\"createTime\" />\r\n              <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button size=\"mini\" type=\"text\" icon=\"el-icon-edit\" @click=\"handleUpdate(scope.row)\">修改</el-button>\r\n                  <el-button size=\"mini\" type=\"text\" icon=\"el-icon-delete\"\r\n                    @click=\"handleDelete(scope.row)\">删除</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n          <!-- 分页 -->\r\n          <div class=\"pageStyle\">\r\n            <el-pagination v-if=\"tableData && tableData.length > 0\" background layout=\"prev, pager, next\"\r\n              class=\"activity-pagination\" :page-size=\"queryParams.pageSize\" :current-page=\"queryParams.pageNum\"\r\n              :total=\"total\" @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { deviceUserListData, delDeviceInfo } from \"@/api/manufacturingSharing\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      total: 0,\r\n      tableData: [],\r\n      deviceMenuList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        name: null,\r\n        category: null,\r\n        location: null,\r\n        rentMode: null,\r\n        rentPrice: null,\r\n        checkStatus: null,\r\n        createBy: null,\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getDicts();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 查询字典数据列表 */\r\n    getDicts() {\r\n      let params = { dictType: \"device_share_type\" };\r\n      listData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.deviceMenuList = res.rows;\r\n          console.log(this.deviceMenuList, \"----------\");\r\n        }\r\n      });\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      let userinfo = JSON.parse(window.sessionStorage.getItem(\"userinfo\"));\r\n      this.queryParams.createBy = userinfo.memberPhone;\r\n      deviceUserListData(this.queryParams).then((res) => {\r\n        if (res.code == 200) {\r\n          this.tableData = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    handleAdd() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(\"/publishEquipment\");\r\n      }\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.queryParams.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.$router.push(\"/publishEquipment?id=\" + row.id);\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids;\r\n      this.$modal\r\n        .confirm('是否确认删除设备信息编号为\"' + ids + '\"的数据项？')\r\n        .then(function () {\r\n          return delDeviceInfo(ids);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"删除成功\");\r\n        })\r\n        .catch(() => { });\r\n    },\r\n    getDeviceType(type) {\r\n      const deviceType = this.deviceMenuList.find(\r\n        (item) => item.dictValue == type\r\n      );\r\n      return deviceType ? deviceType.dictLabel : \"\";\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.top {\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  // margin-top: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.table {\r\n  // margin-top: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  padding: 20px;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  justify-content: space-around;\r\n}\r\n\r\n.pageStyle {\r\n  width: 100%;\r\n  margin-top: 61px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n</style>\r\n"]}]}