{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\classicCase\\caseDetail.vue?vue&type=style&index=0&id=5275a888&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\classicCase\\caseDetail.vue", "mtime": 1750311962925}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["caseDetail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "caseDetail.vue", "sourceRoot": "src/views/classicCase", "sourcesContent": ["<template>\r\n  <div class=\"resource-hall-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"resource-hall-detail-banner\">\r\n      <img src=\"../../assets/classicCase/classicCaseBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div class=\"resource-hall-detail-title-box\">\r\n      <div class=\"resource-hall-detail-divider\"></div>\r\n      <div class=\"resource-hall-detail-title\">案例详情</div>\r\n      <div class=\"resource-hall-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"resource-hall-detail-content\">\r\n      <div class=\"resource-hall-detail-box\">\r\n        <div class=\"resource-hall-detail-box-title\">\r\n          {{ data.name }}\r\n        </div>\r\n        <!-- <div class=\"resource-hall-detail-headline\">\r\n          <div class=\"headline-content\">\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">案例简介：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.introduction }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div> -->\r\n        <div class=\"resource-hall-detail-description\">\r\n          <div class=\"description-title-box\">\r\n            <div class=\"description-divider\"></div>\r\n            <div class=\"description-title\">客户痛点</div>\r\n          </div>\r\n          <div class=\"description-content\">\r\n            <div class=\"description-text ql-editor\">\r\n              {{ data.introduction }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"resource-hall-detail-description\">\r\n          <div class=\"description-title-box\">\r\n            <div class=\"description-divider\"></div>\r\n            <div class=\"description-title\">部署方案&实施效果</div>\r\n          </div>\r\n          <div class=\"description-content\">\r\n            <div v-html=\"data.detail\" class=\"description-text ql-editor\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// import { getCheckSubmit } from \"@/api/purchaseSales\";\r\nimport { caseDetail } from \"@/api/classicCase\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { getCompanyInfoByLoginInfo } from \"@/api/apathy\";\r\nimport { mapGetters } from \"vuex\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n      showBtn: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      caseDetail(this.$route.query.id)\r\n        .then((res) => {\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          if (!this.token) {\r\n            this.showBtn = true;\r\n          } else {\r\n            this.getInfo();\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 判断此资源是不是自己发布的\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (this.data.createById === res.user.userId) {\r\n          this.showBtn = false;\r\n        } else {\r\n          this.showBtn = true;\r\n        }\r\n      });\r\n    },\r\n    goChat() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      let routeData = this.$router.resolve({\r\n        path: \"/user/im\",\r\n        query: {\r\n          userId: this.data.createImById,\r\n        },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳到我有意向页面\r\n    goIntention() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      // 是否加入企业\r\n      getCompanyInfoByLoginInfo()\r\n        .then((res) => {\r\n          if (res.data) {\r\n            // 是否对此资源提交过意向\r\n            getCheckSubmit({\r\n              id: this.$route.query.id,\r\n              resourceType: \"resource_supply\",\r\n            })\r\n              .then((res) => {\r\n                this.loading = false;\r\n                // true 提交过  false未提交过\r\n                if (res.data) {\r\n                  this.$message({\r\n                    type: \"warning\",\r\n                    message: \"已经提交过了哦！\",\r\n                  });\r\n                } else {\r\n                  let routeData = this.$router.resolve({\r\n                    path: \"/addIntention\",\r\n                    query: {\r\n                      id: this.$route.query.id,\r\n                      type: \"resource_supply\",\r\n                      title: this.data.supplyName,\r\n                    },\r\n                  });\r\n                  window.open(routeData.href, \"_blank\");\r\n                }\r\n              })\r\n              .catch(() => {\r\n                this.loading = false;\r\n              });\r\n          } else {\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"warning\",\r\n              message: \"必须加入企业才可提交我有意向\",\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.resource-hall-detail-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  .resource-hall-detail-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .resource-hall-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .resource-hall-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .resource-hall-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .resource-hall-detail-content {\r\n    background: #f4f5f9;\r\n    padding-bottom: 70px;\r\n    .resource-hall-detail-box {\r\n      width: 1200px;\r\n      background: #fff;\r\n      margin: 0 auto;\r\n      padding: 60px 60px 192px;\r\n      .resource-hall-detail-box-title {\r\n        width: 100%;\r\n        font-size: 32px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        word-wrap: break-word;\r\n      }\r\n      .resource-hall-detail-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-top: 40px;\r\n        padding-bottom: 40px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        .headline-content {\r\n          flex: 1;\r\n          .headline-content-item {\r\n            display: flex;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            line-height: 32px;\r\n            .item-title {\r\n              width: 80px;\r\n              color: #666;\r\n            }\r\n            .item-content {\r\n              flex: 1;\r\n              max-width: 560px;\r\n              color: #333;\r\n              word-wrap: break-word;\r\n            }\r\n          }\r\n          .headline-content-btn {\r\n            padding-top: 112px;\r\n            .headline-btn-style {\r\n              width: 100px;\r\n              height: 32px;\r\n              border-radius: 4px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              padding: 8px 11px;\r\n            }\r\n            .intention-btn {\r\n              background: #21c9b8;\r\n              color: #fff;\r\n            }\r\n            .communication-btn {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        .headline-img {\r\n          width: 400px;\r\n          height: 240px;\r\n          margin-left: 20px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .resource-hall-detail-description {\r\n      padding-top: 39px;\r\n      .description-title-box {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-bottom: 40px;\r\n        .description-divider {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n        .description-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n      .description-content {\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.resource-hall-detail-container {\r\n  .description-content {\r\n    .description-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}