<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="实验室名称" prop="labName">
        <el-input v-model="queryParams.labName" placeholder="请输入实验室名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="联系方式" prop="contactPhone">
        <el-input v-model="queryParams.contactPhone" placeholder="请输入联系方式" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:laboratoryInfo:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:laboratoryInfo:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:laboratoryInfo:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['system:laboratoryInfo:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="laboratoryInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="实验室ID" align="center" prop="id" /> -->
      <el-table-column label="实验室名称" align="center" prop="labName" />
      <el-table-column label="实验室类型" align="center" prop="labType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.lab_type" :value="scope.row.labType" />
        </template>
      </el-table-column>
      <el-table-column label="实验室地址" align="center" prop="labAddress" />
      <el-table-column label="联系方式" align="center" prop="contactPhone" />
      <el-table-column label="实验室介绍" align="center" prop="labIntroduction" />
      <el-table-column label="检测范围" align="center" prop="testingScope" />
      <el-table-column label="实验室图片" align="center" prop="labImages" >
        <template slot-scope="scope">
          <ImagePreview :src="scope.row.labImages" width='200' height='200'></ImagePreview>
        </template>
      </el-table-column>
      </el-table-column>
      <el-table-column label="资质说明" align="center" prop="qualifications" />
      <el-table-column label="CNAS认证" align="center" prop="cnasQualification" />
      <el-table-column label="状态" align="center" prop="status" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:laboratoryInfo:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:laboratoryInfo:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改实验室信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="实验室名称" prop="labName">
          <el-input v-model="form.labName" placeholder="请输入实验室名称" />
        </el-form-item>
        <el-form-item label="实验室地址" prop="labAddress">
          <el-input v-model="form.labAddress" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="联系方式" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="实验室类型" prop="labType">
          <el-select v-model="form.labType" placeholder="请选择实验室类型">
            <el-option v-for="dict in dict.type.lab_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="实验室介绍" prop="labIntroduction">
          <el-input v-model="form.labIntroduction" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="检测范围" prop="testingScope">
          <el-input v-model="form.testingScope" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="实验室图片" prop="labImages">
          <image-upload v-model="form.labImages"/>
        </el-form-item>
        <el-form-item label="资质说明" prop="qualifications">
          <el-input v-model="form.qualifications" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="CNAS认证" prop="cnasQualification">
          <el-input v-model="form.cnasQualification" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLaboratoryInfo, getLaboratoryInfo, delLaboratoryInfo, addLaboratoryInfo, updateLaboratoryInfo } from "@/api/system/laboratoryInfo";

export default {
  name: "LaboratoryInfo",
  dicts: ['lab_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 实验室信息表格数据
      laboratoryInfoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        labName: null,
        labType: null,
        labAddress: null,
        contactPhone: null,
        labIntroduction: null,
        labImages: null,
        qualifications: null,
        cnasQualification: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询实验室信息列表 */
    getList() {
      this.loading = true;
      listLaboratoryInfo(this.queryParams).then(response => {
        this.laboratoryInfoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        labName: null,
        labType: null,
        labAddress: null,
        contactPhone: null,
        labIntroduction: null,
        labImages: null,
        qualifications: null,
        cnasQualification: null,
        status: null,
        createTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加实验室信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getLaboratoryInfo(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改实验室信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLaboratoryInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLaboratoryInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除实验室信息编号为"' + ids + '"的数据项？').then(function () {
        return delLaboratoryInfo(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/laboratoryInfo/export', {
        ...this.queryParams
      }, `laboratoryInfo_${new Date().getTime()}.xlsx`)
    },
    // 图片上传
    uploadFun(params) {
      const file = params.file;
      let form = new FormData();
      form.append("file", file); // 文件对象
      comUpload(form).then((res) => {
        let data = res.data;
        this.form.photo = data.fileFullPath;
        // this.$set(this.form, "images", data.fileFullPath); // 图片全路径
      });
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg" || "image/png" || "image/jpg";
      const isLt2M = file.size / 1024 / 1024 < 50;
      if (!isJPG) {
        this.$message.error("上传图片只能是 jpg、jpeg、png 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 50MB!");
      }
      return isJPG && isLt2M;
    },
  }
};
</script>
