{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\areaTags.vue?vue&type=style&index=0&id=68302124&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\areaTags.vue", "mtime": 1750311962955}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KQGltcG9ydCAifkAvYXNzZXRzL3N0eWxlcy9taXhpbi5zY3NzIjsNCi5hcHBsaWNhdGlvbi1hcmVhIHsNCiAgZGlzcGxheTogZmxleDsNCiAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgZmxleC1zaHJpbms6IDA7DQogIGZsZXgtd3JhcDogd3JhcDsNCiAgLmVsLXRhZyB7DQogICAgbWFyZ2luOiA2cHg7DQogICAgJjpsYXN0LWNoaWxkIHsNCiAgICAgIG1hcmdpbi1yaWdodDogMDsNCiAgICB9DQogIH0NCiAgLmJ1dHRvbi1uZXctdGFnIHsNCiAgICBAaW5jbHVkZSBmbGV4Q2VudGVyOw0KICAgIHdpZHRoOiAyNHB4Ow0KICAgIGhlaWdodDogMjRweDsNCiAgICBib3JkZXI6IDFweCBkb3R0ZWQgIzk5OTk5OTsNCiAgICBib3JkZXItcmFkaXVzOiAxMDAlOw0KICAgIG1hcmdpbi1yaWdodDogNnB4Ow0KICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICBpIHsNCiAgICAgIGNvbG9yOiAjOTk5OTk5Ow0KICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgfQ0KICB9DQogIC5pbnB1dC1uZXctdGFnIHsNCiAgICB3aWR0aDogOTBweDsNCiAgICBtYXJnaW4tcmlnaHQ6IDZweDsNCiAgICB2ZXJ0aWNhbC1hbGlnbjogYm90dG9tOw0KICB9DQp9DQoNCg=="}, {"version": 3, "sources": ["areaTags.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "areaTags.vue", "sourceRoot": "src/views/form/components", "sourcesContent": ["<template>\r\n  <div class=\"application-area\">\r\n    <div class=\"action\">\r\n      <el-input\r\n        class=\"input-new-tag\"\r\n        v-if=\"inputVisible\"\r\n        v-model.trim=\"inputValue\"\r\n        ref=\"saveTagInput\"\r\n        size=\"small\"\r\n        @keyup.enter.native=\"handleInputConfirm\"\r\n        @blur=\"handleInputConfirm\"\r\n      >\r\n      </el-input>\r\n      <div v-else class=\"button-new-tag\" @click=\"showInput\">\r\n        <i class=\"el-icon-plus\"></i>\r\n      </div>\r\n    </div>\r\n    <el-tag\r\n      v-for=\"tag in dynamicTags\"\r\n      :key=\"`tag${tag}`\"\r\n      closable\r\n      :disable-transitions=\"false\"\r\n      @close=\"handleClose(tag)\">\r\n      {{tag}}\r\n    </el-tag>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { includes } from 'ramda'\r\nimport { getDicts } from '@/api/system/dict/data'\r\n\r\nexport default {\r\n  name: 'ApplicationAreaDicts',\r\n  data() {\r\n    return {\r\n      dynamicTags: [],\r\n      inputVisible: false,\r\n      inputValue: ''\r\n    };\r\n  },\r\n  watch: {\r\n    value: {\r\n      handler(val) {\r\n        if (val) {\r\n          this.dynamicTags =  Array.isArray(val) ? val : this.value.split(',');\r\n        } else {\r\n          this.dynamicTags = [];\r\n          return [];\r\n        }\r\n      },\r\n      deep: true,\r\n      immediate: true\r\n    }\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      getDicts('application_area').then(res => {\r\n        const { data = [] } = res;\r\n        if (data && data.length > 0) {\r\n          this.dynamicTags = data.map(item => item.dictLabel)\r\n          this.$emit(\"input\", this.dynamicTags);\r\n        }\r\n      })\r\n    },\r\n    handleClose(tag) {\r\n      this.dynamicTags = this.dynamicTags.filter(item => item !== tag);\r\n      this.$nextTick(() => {\r\n        this.$emit(\"input\", this.formatValue(this.dynamicTags));\r\n      })\r\n    },\r\n\r\n    showInput() {\r\n      this.inputVisible = true;\r\n      this.$nextTick(_ => {\r\n        this.$refs.saveTagInput.$refs.input.focus();\r\n      });\r\n    },\r\n\r\n    handleInputConfirm() {\r\n      let inputValue = this.inputValue;\r\n      if (inputValue && !this.dynamicTags.includes(inputValue)) {\r\n        this.dynamicTags = [inputValue, ...this.dynamicTags];\r\n        this.$nextTick(() => {\r\n          this.$emit(\"input\", this.dynamicTags);\r\n        })\r\n      } else {\r\n        this.$message.warning(\"应用领域已存在\");\r\n      }\r\n      this.inputVisible = false;\r\n      this.inputValue = '';\r\n\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n.application-area {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  flex-shrink: 0;\r\n  flex-wrap: wrap;\r\n  .el-tag {\r\n    margin: 6px;\r\n    &:last-child {\r\n      margin-right: 0;\r\n    }\r\n  }\r\n  .button-new-tag {\r\n    @include flexCenter;\r\n    width: 24px;\r\n    height: 24px;\r\n    border: 1px dotted #999999;\r\n    border-radius: 100%;\r\n    margin-right: 6px;\r\n    cursor: pointer;\r\n    i {\r\n      color: #999999;\r\n      font-size: 16px;\r\n      font-weight: bold;\r\n    }\r\n  }\r\n  .input-new-tag {\r\n    width: 90px;\r\n    margin-right: 6px;\r\n    vertical-align: bottom;\r\n  }\r\n}\r\n\r\n</style>\r\n"]}]}