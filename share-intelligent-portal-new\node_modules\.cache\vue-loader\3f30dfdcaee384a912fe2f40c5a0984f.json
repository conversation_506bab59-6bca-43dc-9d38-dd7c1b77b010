{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\profile\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\profile\\index.vue", "mtime": 1750311963073}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsgZ2V0SW5mbyB9IGZyb20gIkAvYXBpL2xvZ2luIjsNCmltcG9ydCB7IG15U3VwcGx5LCBteURlbWFuZCB9IGZyb20gIkAvYXBpL2hvbWUiOw0KaW1wb3J0IHsgZG9ja2luZ0xpc3QgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlVzZXIiLA0KICBjb21wb25lbnRzOiB7IFVzZXJNZW51IH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGFwcGxpTGlzdDogWw0KICAgICAgICB7DQogICAgICAgICAgdXJsOiByZXF1aXJlKCIuLi8uLi8uLi8uLi9hc3NldHMvdXNlci9hcHBsaTEucG5nIiksDQogICAgICAgICAgbmFtZTogIuWkjeadkOWVhuWfjiIsDQogICAgICAgICAgbGluazogJ2h0dHA6Ly82MS4yNDAuMTQ1LjEwMDoxMDAxLycNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHVybDogcmVxdWlyZSgiLi4vLi4vLi4vLi4vYXNzZXRzL3VzZXIvYXBwbGkyLnBuZyIpLA0KICAgICAgICAgIG5hbWU6ICLku5PlgqjnianmtYEiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdXJsOiByZXF1aXJlKCIuLi8uLi8uLi8uLi9hc3NldHMvdXNlci9hcHBsaTMucG5nIiksDQogICAgICAgICAgbmFtZTogIumHkeiejeacjeWKoSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB1cmw6IHJlcXVpcmUoIi4uLy4uLy4uLy4uL2Fzc2V0cy91c2VyL2FwcGxpNC5wbmciKSwNCiAgICAgICAgICBuYW1lOiAi6K6+5aSH566h55CGIiwNCiAgICAgICAgICByb3V0ZXI6ICcvdXNlci9lcXVpcG1lbnRNYW5hZ2VtZW50Jw0KICAgICAgICB9LA0KICAgICAgXSwNCiAgICAgIGRlZmF1bHRBdmF0YXI6IHJlcXVpcmUoJ0AvYXNzZXRzL2ltYWdlcy9hdmF0YXIucG5nJyksDQogICAgICBkZW1hbmRTdXBwbHk6ICIxIiwNCiAgICAgIHVzZXJpbmZvOiB7fSwNCiAgICAgIHN1cHBseUxpc3Q6IFtdLA0KICAgICAgZGVtYW5kTGlzdDogW10sDQogICAgICBkb2NraW5nTGlzdDogW10sDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldEluZm8oKTsNCiAgICB0aGlzLmdldFN1cHBseUxpc3QoKTsNCiAgICB0aGlzLmdldERlbWFuZExpc3QoKTsNCiAgICB0aGlzLmdldERvY2tpbmdMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDojrflj5bnlKjmiLfkv6Hmga8NCiAgICBnZXRJbmZvKCkgew0KICAgICAgZ2V0SW5mbygpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy51c2VyaW5mbyA9IHJlcy5tZW1iZXI7DQogICAgICAgICAgd2luZG93LnNlc3Npb25TdG9yYWdlLnNldEl0ZW0oInVzZXJpbmZvIiwgSlNPTi5zdHJpbmdpZnkocmVzLm1lbWJlcikpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZUNsaWNrKHZhbCkgew0KICAgICAgdGhpcy5kZW1hbmRTdXBwbHkgPSB2YWw7DQogICAgfSwNCiAgICBoYW5kbGVMaW5rKHZhbCkgew0KICAgICAgaWYgKHZhbC5saW5rKSB7DQogICAgICAgIHdpbmRvdy5vcGVuKHZhbC5saW5rKQ0KICAgICAgfSBlbHNlIGlmICh2YWwucm91dGVyKSB7DQogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogdmFsLnJvdXRlciB9KTsNCiAgICAgIH0NCiAgICAgIGVsc2Ugew0KICAgICAgICB0aGlzLiRub3RpZnkuaW5mbyh7DQogICAgICAgICAgdGl0bGU6ICfmlazor7fmnJ/lvoUnLA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldFN1cHBseUxpc3QoKSB7DQogICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHF1ZXJ5VHlwZTogIm15IiwNCiAgICAgIH07DQogICAgICBteVN1cHBseShwYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuc3VwcGx5TGlzdCA9IHJlcy5yb3dzOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldERlbWFuZExpc3QoKSB7DQogICAgICBsZXQgcGFyYW1zID0gew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHF1ZXJ5VHlwZTogIm15IiwNCiAgICAgIH07DQogICAgICBteURlbWFuZChwYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuZGVtYW5kTGlzdCA9IHJlcy5yb3dzOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldERvY2tpbmdMaXN0KCkgew0KICAgICAgZG9ja2luZ0xpc3Qoew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMTAsDQogICAgICAgIHF1ZXJ5VHlwZTogJ215JywNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmRvY2tpbmdMaXN0ID0gcmVzLnJvd3M7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdvVG9zdXBwbHlEZW1hbmQoKSB7DQogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7IHBhdGg6ICcvdXNlci9zdXBwbHlEZW1hbmQnIH0pDQogICAgfSwNCiAgICBnb1RvTm90aWNlKCkgew0KICAgICAgLy8gdGhpcy4kcm91dGVyLnB1c2goe3BhdGg6Jy91c2VyL25vdGljZSd9KQ0KICAgIH0sDQogICAgZ29Ub2RvY2tpbmdSZWNvcmRzKCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiAnL3VzZXIvZG9ja2luZ1JlY29yZHMnIH0pDQogICAgfQ0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/profile", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <el-row :gutter=\"24\">\r\n          <el-col :span=\"12\">\r\n            <div class=\"user-info-card\">\r\n              <!-- 上部分 -->\r\n              <div class=\"user-info-card-top\">\r\n                <!-- 左侧图片 -->\r\n                <div class=\"imgStyle\">\r\n                  <img style=\"width: 100%; height: 100%\" :src=\"userinfo.avatar ? userinfo.avatar : defaultAvatar\"\r\n                    alt=\"\" />\r\n                </div>\r\n                <!-- 右侧内容 -->\r\n                <div class=\"user-info-card-top-right\">\r\n                  <div class=\"nameStyle\">{{ userinfo.memberRealName || '暂无' }}</div>\r\n                  <div class=\"phoneStyle\" style=\"margin-top: 17px; margin-bottom: 11px\">\r\n                    联系电话：{{ userinfo.memberPhone || '暂无' }}\r\n                  </div>\r\n                  <!-- <div class=\"phoneStyle\">所属企业：立即加入></div> -->\r\n                </div>\r\n              </div>\r\n              <!-- 下部分 -->\r\n              <div class=\"user-info-card-bottom\">\r\n                <!-- 顶部导航 -->\r\n                <div class=\"navStyle\">\r\n                  <div class=\"navStyle-left\">\r\n                    <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/business_icon.png\" alt=\"\" />\r\n                    <div class=\"text\">企业应用</div>\r\n                  </div>\r\n                  <!-- <div class=\"navStyle-right\">查看更多 >></div> -->\r\n                </div>\r\n                <!-- 底部内容 -->\r\n                <div class=\"businessAppli\">\r\n                  <div class=\"appliItem\" v-for=\"(item, index) in appliList\" :key=\"index\" @click=\"handleLink(item)\">\r\n                    <div class=\"appliImg\">\r\n                      <img style=\"width: 100%; height: 100%\" :src=\"item.url\" alt=\"\" />\r\n                    </div>\r\n                    <div class=\"appliName\">{{ item.name }}</div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"demandSupply\">\r\n              <!-- 顶部导航 -->\r\n              <div class=\"navStyle\">\r\n                <div class=\"navStyle-left\" @click=\"handleClick('1')\" :style=\"demandSupply == '1'\r\n                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'\r\n                  : 'border-bottom: none;cursor: pointer;'\r\n                  \">\r\n                  <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/demand_icon.png\" alt=\"\" />\r\n                  <div class=\"text\">我的需求</div>\r\n                </div>\r\n                <div class=\"navStyle-left\" @click=\"handleClick('2')\" style=\"margin-left: 24px\" :style=\"demandSupply == '2'\r\n                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'\r\n                  : 'border-bottom: none;cursor: pointer;'\r\n                  \">\r\n                  <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/supply_icon.png\" alt=\"\" />\r\n                  <div class=\"text\">我的供给</div>\r\n                </div>\r\n                <div class=\"navStyle-right\" @click=\"goTosupplyDemand\">查看更多 >></div>\r\n              </div>\r\n              <el-table :data=\"demandList\" v-if=\"demandSupply == '1'\">\r\n                <el-table-column prop=\"title\" label=\"需求标题\" />\r\n                <el-table-column prop=\"description\" label=\"需求描述\" />\r\n              </el-table>\r\n              <el-table :data=\"supplyList\" v-if=\"demandSupply == '2'\">\r\n                <el-table-column prop=\"title\" label=\"供给标题\" />\r\n                <el-table-column prop=\"description\" label=\"供给描述\" />\r\n              </el-table>\r\n              <!-- 暂无数据 -->\r\n              <!-- <div>\r\n                <div class=\"noDataStyle\">\r\n                  <img style=\"width: 100%; height: 100%\" src=\"../../../../assets/user/noData.png\" alt=\"\" />\r\n                </div>\r\n                <div class=\"noDataText\">暂无内容</div>\r\n              </div> -->\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"meNotification\">\r\n              <!-- 顶部导航 -->\r\n              <div class=\"navStyle\">\r\n                <div class=\"navStyle-left\">\r\n                  <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/message_icon.png\" alt=\"\" />\r\n                  <div class=\"text\">消息通知</div>\r\n                </div>\r\n                <div class=\"navStyle-right\" @click=\"goToNotice\">查看更多 >></div>\r\n              </div>\r\n              <!-- 暂无数据 -->\r\n              <div>\r\n                <div class=\"noDataStyle\">\r\n                  <img style=\"width: 100%; height: 100%\" src=\"../../../../assets/user/noData.png\" alt=\"\" />\r\n                </div>\r\n                <div class=\"noDataText\">暂无内容</div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <div class=\"dockingRecords\">\r\n              <!-- 顶部导航 -->\r\n              <div class=\"navStyle\">\r\n                <div class=\"navStyle-left\">\r\n                  <img style=\"width: 17px; height: 17px\" src=\"../../../../assets/user/record_icon.png\" alt=\"\" />\r\n                  <div class=\"text\">对接记录</div>\r\n                </div>\r\n                <div class=\"navStyle-right\" @click=\"goTodockingRecords\">查看更多 >></div>\r\n              </div>\r\n              <!-- 暂无数据 -->\r\n              <el-table :data=\"dockingList\" v-if=\"dockingList.length > 0\">\r\n                <el-table-column align=\"center\" prop=\"title\" label=\"资源名称\">\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" prop=\"intentionContent\" label=\"申请内容\">\r\n                </el-table-column>\r\n              </el-table>\r\n              <div v-if=\"dockingList.length == 0\">\r\n                <div class=\"noDataStyle\">\r\n                  <img style=\"width: 100%; height: 100%\" src=\"../../../../assets/user/noData.png\" alt=\"\" />\r\n                </div>\r\n                <div class=\"noDataText\">暂无内容</div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { mySupply, myDemand } from \"@/api/home\";\r\nimport { dockingList } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      appliList: [\r\n        {\r\n          url: require(\"../../../../assets/user/appli1.png\"),\r\n          name: \"复材商城\",\r\n          link: 'http://**************:1001/'\r\n        },\r\n        {\r\n          url: require(\"../../../../assets/user/appli2.png\"),\r\n          name: \"仓储物流\",\r\n        },\r\n        {\r\n          url: require(\"../../../../assets/user/appli3.png\"),\r\n          name: \"金融服务\",\r\n        },\r\n        {\r\n          url: require(\"../../../../assets/user/appli4.png\"),\r\n          name: \"设备管理\",\r\n          router: '/user/equipmentManagement'\r\n        },\r\n      ],\r\n      defaultAvatar: require('@/assets/images/avatar.png'),\r\n      demandSupply: \"1\",\r\n      userinfo: {},\r\n      supplyList: [],\r\n      demandList: [],\r\n      dockingList: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getInfo();\r\n    this.getSupplyList();\r\n    this.getDemandList();\r\n    this.getDockingList();\r\n  },\r\n  methods: {\r\n    // 获取用户信息\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (res.code == 200) {\r\n          this.userinfo = res.member;\r\n          window.sessionStorage.setItem(\"userinfo\", JSON.stringify(res.member));\r\n        }\r\n      });\r\n    },\r\n    handleClick(val) {\r\n      this.demandSupply = val;\r\n    },\r\n    handleLink(val) {\r\n      if (val.link) {\r\n        window.open(val.link)\r\n      } else if (val.router) {\r\n        this.$router.push({ path: val.router });\r\n      }\r\n      else {\r\n        this.$notify.info({\r\n          title: '敬请期待',\r\n        });\r\n      }\r\n    },\r\n    getSupplyList() {\r\n      let params = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        queryType: \"my\",\r\n      };\r\n      mySupply(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.supplyList = res.rows;\r\n        }\r\n      });\r\n    },\r\n    getDemandList() {\r\n      let params = {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        queryType: \"my\",\r\n      };\r\n      myDemand(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.demandList = res.rows;\r\n        }\r\n      });\r\n    },\r\n    getDockingList() {\r\n      dockingList({\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        queryType: 'my',\r\n      }).then((res) => {\r\n        this.dockingList = res.rows;\r\n      });\r\n    },\r\n    goTosupplyDemand() {\r\n      this.$router.push({ path: '/user/supplyDemand' })\r\n    },\r\n    goToNotice() {\r\n      // this.$router.push({path:'/user/notice'})\r\n    },\r\n    goTodockingRecords() {\r\n      this.$router.push({ path: '/user/dockingRecords' })\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 1080px;\r\n}\r\n\r\n.user-info-card {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  height: 450px;\r\n  border-radius: 4px;\r\n\r\n  .user-info-card-top {\r\n    width: 100%;\r\n    height: 140px;\r\n    background: url(\"../../../../assets/user/homePageBanner.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n    padding: 32px 30px 36px 30px;\r\n    display: flex;\r\n\r\n    .imgStyle {\r\n      width: 60px;\r\n      height: 60px;\r\n    }\r\n\r\n    .user-info-card-top-right {\r\n      margin-left: 20px;\r\n\r\n      .nameStyle {\r\n        height: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        font-size: 18px;\r\n        color: #ffffff;\r\n      }\r\n\r\n      .phoneStyle {\r\n        height: 15px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #ffffff;\r\n      }\r\n    }\r\n  }\r\n\r\n  .user-info-card-bottom {\r\n    margin-top: 20px;\r\n\r\n    .businessAppli {\r\n      margin-top: 31px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .appliItem {\r\n        width: 70px;\r\n        margin-left: 41px;\r\n        text-align: center;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .appliItem:nth-child(1) {\r\n        margin-left: 0;\r\n      }\r\n\r\n      .appliImg {\r\n        width: 70px;\r\n        height: 75px;\r\n      }\r\n\r\n      .appliName {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #222222;\r\n        margin-top: 11px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.demandSupply {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  height: 450px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.meNotification {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  height: 450px;\r\n  border-radius: 4px;\r\n  margin-top: 24px;\r\n}\r\n\r\n.dockingRecords {\r\n  background-color: #fff;\r\n  padding: 20px;\r\n  height: 450px;\r\n  border-radius: 4px;\r\n  margin-top: 24px;\r\n}\r\n\r\n.navStyle {\r\n  height: 50px;\r\n  border-bottom: 1px solid #ccc;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .navStyle-left {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 50px;\r\n    border-bottom: 2px solid #21c9b8;\r\n  }\r\n\r\n  .text {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #21c9b8;\r\n    margin-left: 10px;\r\n  }\r\n\r\n  .navStyle-right {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #333333;\r\n    margin-left: auto;\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.noDataStyle {\r\n  width: 259px;\r\n  height: 259px;\r\n  margin-top: 63px;\r\n  margin-left: calc((100% - 259px) / 2);\r\n}\r\n\r\n.noDataText {\r\n  width: 100%;\r\n  text-align: center;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 400;\r\n  font-size: 16px;\r\n  color: #999999;\r\n}\r\n</style>\r\n"]}]}