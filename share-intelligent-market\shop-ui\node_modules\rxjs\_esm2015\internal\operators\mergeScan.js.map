{"version": 3, "file": "mergeScan.js", "sources": ["../../../src/internal/operators/mergeScan.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAyCjG,MAAM,UAAU,SAAS,CAAO,WAAoE,EACpE,IAAO,EACP,aAAqB,MAAM,CAAC,iBAAiB;IAC3E,OAAO,CAAC,MAAqB,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;AACtG,CAAC;AAED,MAAM,OAAO,iBAAiB;IAC5B,YAAoB,WAAoE,EACpE,IAAO,EACP,UAAkB;QAFlB,gBAAW,GAAX,WAAW,CAAyD;QACpE,SAAI,GAAJ,IAAI,CAAG;QACP,eAAU,GAAV,UAAU,CAAQ;IACtC,CAAC;IAED,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,mBAAmB,CAC7C,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CACzD,CAAC,CAAC;IACL,CAAC;CACF;AAOD,MAAM,OAAO,mBAA0B,SAAQ,qBAA2B;IAOxE,YAAY,WAA0B,EAClB,WAAoE,EACpE,GAAM,EACN,UAAkB;QACpC,KAAK,CAAC,WAAW,CAAC,CAAC;QAHD,gBAAW,GAAX,WAAW,CAAyD;QACpE,QAAG,GAAH,GAAG,CAAG;QACN,eAAU,GAAV,UAAU,CAAQ;QAT9B,aAAQ,GAAY,KAAK,CAAC;QAC1B,iBAAY,GAAY,KAAK,CAAC;QAC9B,WAAM,GAAsB,EAAE,CAAC;QAC/B,WAAM,GAAW,CAAC,CAAC;QACjB,UAAK,GAAW,CAAC,CAAC;IAO5B,CAAC;IAES,KAAK,CAAC,KAAU;QACxB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;YACjC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC3B,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YACrC,IAAI,GAAG,CAAC;YACR,IAAI;gBACF,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;gBAC7B,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aAC3C;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,WAAW,CAAC,KAAM,CAAC,CAAC,CAAC,CAAC;aAC9B;YACD,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;SACrB;aAAM;YACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACzB;IACH,CAAC;IAEO,SAAS,CAAC,GAAQ;QACxB,MAAM,eAAe,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC;QACxD,MAAM,WAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;QACrD,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACjC,MAAM,iBAAiB,GAAG,cAAc,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QAI/D,IAAI,iBAAiB,KAAK,eAAe,EAAE;YACzC,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;SACpC;IACH,CAAC;IAES,SAAS;QACjB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACjD,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;gBAC3B,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClC;YACD,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,UAAU,CAAC,UAAa;QACtB,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,WAAW,CAAC,IAAK,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC;IAED,cAAc;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;SAC5B;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE;YACjD,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;gBAC3B,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClC;YACD,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;IACH,CAAC;CACF"}