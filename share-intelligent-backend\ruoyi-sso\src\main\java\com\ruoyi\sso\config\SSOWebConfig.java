package com.ruoyi.sso.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * SSO服务Web配置
 * 主要用于配置CORS跨域支持
 *
 * <AUTHOR>
 */
@Configuration
public class SSOWebConfig implements WebMvcConfigurer {

    /**
     * 跨域配置
     * 允许所有来源访问SSO接口，解决前端测试页面的跨域问题
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                // 允许所有来源
                .allowedOriginPatterns("*")
                // 允许所有请求头
                .allowedHeaders("*")
                // 允许所有HTTP方法
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                // 允许携带认证信息
                .allowCredentials(true)
                // 预检请求缓存时间
                .maxAge(3600);
    }
}
