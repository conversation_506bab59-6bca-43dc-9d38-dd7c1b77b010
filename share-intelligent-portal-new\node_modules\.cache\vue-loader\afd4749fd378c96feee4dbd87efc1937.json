{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\application\\index.vue?vue&type=style&index=0&id=95c65a76&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\application\\index.vue", "mtime": 1750311963040}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/application", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\">\r\n          <div class=\"applicationDesc\">\r\n            <div class=\"desc_left\">\r\n              <div>\r\n                商品发布说明:\r\n                SaaS、API商品发布前需先进行接入调试，接入流程请查看商品接入指南。\r\n              </div>\r\n              <div>\r\n                商品修改说明:\r\n                只有在销售商品才能进行修改操作，提交修改申请后，需要等待运营审核后才能生效。\r\n              </div>\r\n              <div>\r\n                商品升级说明:\r\n                SaaS商品上架后，如需支持用户升级已购买的规格，请在操作中设置升级规则。\r\n              </div>\r\n              <div>\r\n                商品下架说明:\r\n                为保障用户正常访问，商品下架需由云商店审核通过后方可下架。下架全部商品规格后，商品进入“已停售”状态，停售后将不会在商店中呈现和售卖，但不影响已购用户的使用和续订。\r\n              </div>\r\n            </div>\r\n            <div class=\"driver\"></div>\r\n            <div class=\"desc_right\">\r\n              <div class=\"statistics\">\r\n                <div>待发货 {{ shipNum }}</div>\r\n                <div class=\"statisticsItem\">待开票 {{ invoiceNum }}</div>\r\n              </div>\r\n              <div class=\"submitStyle\">\r\n                <div class=\"buttonStyle\" @click=\"publishAppli\">发布应用</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"appliTitle\">我的应用</div>\r\n          <div class=\"appliPart\" v-loading=\"loading\">\r\n            <div class=\"everyItem\" v-for=\"item in appliList\" :key=\"item.id\">\r\n              <div class=\"item_img\">\r\n                <img :src=\"item.appLogo\" alt=\"\" />\r\n              </div>\r\n              <div class=\"item_text\">\r\n                <div class=\"title\">{{ item.appName }}</div>\r\n                <div class=\"desc\">{{ item.briefInto }}</div>\r\n              </div>\r\n              <div class=\"timeStyle\">\r\n                <div class=\"tabHeader\">交付方式</div>\r\n                <div style=\"margin-top: 10px\">\r\n                  {{ item.delivery == \"0\" ? \"Saas服务\" : \"本地部署\" }}\r\n                </div>\r\n              </div>\r\n              <div class=\"timeStyle\">\r\n                <div class=\"tabHeader\">创建时间</div>\r\n                <div style=\"margin-top: 10px\">{{ item.createTime }}</div>\r\n              </div>\r\n              <div class=\"timeStyle\">\r\n                <div class=\"tabHeader\">应用状态</div>\r\n                <div style=\"margin-top: 10px\">\r\n                  {{\r\n                    item.appState == 0\r\n                      ? \"待配置\"\r\n                      : item.appState == 1\r\n                      ? \"待上架\"\r\n                      : item.appState == 2\r\n                      ? \"已上架\"\r\n                      : \"已下架\"\r\n                  }}\r\n                </div>\r\n              </div>\r\n              <div class=\"option\">\r\n                <div style=\"display: flex\">\r\n                  <div\r\n                    class=\"buttonStyle\"\r\n                    @click=\"goEdit(item.id, item.appState)\"\r\n                  >\r\n                    编辑\r\n                  </div>\r\n                  <div\r\n                    v-if=\"item.appState == 2\"\r\n                    class=\"buttonStyle\"\r\n                    @click=\"offShelf(item.id, item.appState)\"\r\n                  >\r\n                    下架\r\n                  </div>\r\n                  <div\r\n                    v-if=\"item.appState == 3\"\r\n                    class=\"buttonStyle\"\r\n                    @click=\"appliGround(item.id, item.appState)\"\r\n                  >\r\n                    上架\r\n                  </div>\r\n                </div>\r\n                <div style=\"display: flex; margin-top: 10px\">\r\n                  <div class=\"buttonStyle\" @click=\"goDetail(item.id)\">详情</div>\r\n                  <div class=\"buttonStyle\" @click=\"handleDelete(item.id)\">\r\n                    删除\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div style=\"text-align: center; margin-top: 45px\">\r\n              <el-pagination\r\n                v-show=\"total > 0\"\r\n                background\r\n                layout=\"prev, pager, next\"\r\n                :page-size=\"5\"\r\n                :current-page.sync=\"queryParams.pageNum\"\r\n                @current-change=\"handleCurrentChange\"\r\n                :total=\"total\"\r\n              >\r\n              </el-pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  appliList,\r\n  delAppli,\r\n  orderStatusNum,\r\n  appliGroundOff,\r\n} from \"@/api/appliMarket\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { checkAuthStatus } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      appliList: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      loading: false,\r\n      shipNum: 0,\r\n      invoiceNum: 0,\r\n      companyStatus: \"0\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getUser();\r\n    this.getOrderStatusNum();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getOrderStatusNum() {\r\n      orderStatusNum().then((res) => {\r\n        if (res.code === 200) {\r\n          (this.shipNum = res.data.waitSendNum),\r\n            (this.invoiceNum = res.data.waitMakeNum);\r\n        }\r\n      });\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        createBy: this.$store.state.user.userId,\r\n        pageNum: this.queryParams.pageNum,\r\n        pageSize: 5,\r\n      };\r\n      appliList(params).then((res) => {\r\n        this.appliList = res.rows;\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 操作日志类型字典翻译\r\n    typeFormat(row, column) {\r\n      return this.selectDictLabel(\r\n        this.dict.type.sys_oper_type,\r\n        row.businessType\r\n      );\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(id) {\r\n      this.$modal\r\n        .confirm(\"是否确认删除该数据项？\")\r\n        .then(function () {\r\n          return delAppli(id);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"操作成功!\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    publishAppli() {\r\n      if (this.companyStatus == \"0\") {\r\n        this.$confirm(\"当前用户未完成服务商认证，请认证后再进行操作。\", \"\", {\r\n          confirmButtonText: \"去认证\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push({\r\n              path: \"/user/spCertification\",\r\n            });\r\n          })\r\n          .catch(() => {});\r\n      } else {\r\n        this.$router.push({\r\n          path: \"/user/publishAppli\",\r\n        });\r\n      }\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push({\r\n        path: \"/user/appliDetail\",\r\n        query: {\r\n          id,\r\n        },\r\n      });\r\n    },\r\n    goEdit(id, status) {\r\n      if (status == 2) {\r\n        this.$message.warning(\"当前应用已上架，请下架后再进行编辑！\");\r\n      } else {\r\n        this.$router.push({\r\n          path: \"/user/publishAppli\",\r\n          query: {\r\n            id,\r\n          },\r\n        });\r\n      }\r\n    },\r\n    // 上架\r\n    appliGround(id, appState) {\r\n      let data = {\r\n        id,\r\n        appState,\r\n      };\r\n      appliGroundOff(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message.success(\"操作成功!\");\r\n          this.getList();\r\n        }\r\n      });\r\n    },\r\n    // 下架\r\n    offShelf(id, appState) {\r\n      let data = {\r\n        id,\r\n        appState,\r\n      };\r\n      appliGroundOff(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message.success(\"操作成功!\");\r\n          this.getList();\r\n        }\r\n      });\r\n      // this.$message.warning(\"当前应用存在进行中订单，无法下架！\");\r\n    },\r\n    getUser() {\r\n      checkAuthStatus().then((res) => {\r\n        if (res.code === 200) {\r\n          this.companyStatus = res.data.companyStatus;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  // background: rgb(242, 248, 255);\r\n  .applicationDesc {\r\n    width: 100%;\r\n    height: 150px;\r\n    display: flex;\r\n    padding: 20px;\r\n    .desc_left {\r\n      width: 80%;\r\n    }\r\n    .driver {\r\n      width: 2px;\r\n      height: 100%;\r\n      background: #ccc;\r\n      margin: 0 5%;\r\n    }\r\n    .desc_right {\r\n      width: 30%;\r\n      display: flex;\r\n      align-items: center;\r\n      .statistics {\r\n        margin-left: 10px;\r\n        width: 50%;\r\n        .statisticsItem {\r\n          margin-top: 12px;\r\n        }\r\n      }\r\n      .submitStyle {\r\n        width: 50%;\r\n        display: flex;\r\n        justify-content: right;\r\n        .buttonStyle {\r\n          width: 100px;\r\n          padding: 10px;\r\n          background: #21c9b8;\r\n          color: #ffffff;\r\n          text-align: center;\r\n          cursor: pointer;\r\n          border-radius: 4px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .appliTitle {\r\n    font-size: 22px;\r\n    margin: 30px;\r\n    font-weight: 600;\r\n  }\r\n  .appliPart {\r\n    .everyItem {\r\n      width: 100%;\r\n      height: 150px;\r\n      background: #ffffff;\r\n      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n      display: flex;\r\n      padding: 20px;\r\n      align-items: center;\r\n      margin-top: 20px;\r\n      .item_img {\r\n        width: 17%;\r\n        height: 100%;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .item_text {\r\n        width: 20%;\r\n        margin-left: 2%;\r\n        .title {\r\n          font-size: 16px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #333333;\r\n        }\r\n        .desc {\r\n          margin-top: 10px;\r\n          font-size: 14px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          color: #999999;\r\n        }\r\n      }\r\n      // .delivery {\r\n      //   margin-left: 2%;\r\n      // }\r\n      .timeStyle {\r\n        width: 15%;\r\n        text-align: center;\r\n      }\r\n      .option {\r\n        // display: flex;\r\n        // justify-content: center;\r\n        width: 18%;\r\n        .buttonStyle {\r\n          color: #21c9b8;\r\n          cursor: pointer;\r\n          margin-left: 20%;\r\n        }\r\n      }\r\n      .tabHeader {\r\n        font-family: Microsoft YaHei;\r\n        font-weight: 400;\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}