{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\approveSetting\\index.vue?vue&type=template&id=4d695104", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\approveSetting\\index.vue", "mtime": 1750311963043}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}