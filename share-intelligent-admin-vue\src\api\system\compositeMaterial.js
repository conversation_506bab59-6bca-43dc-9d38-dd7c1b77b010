import request from '@/utils/request'

// 查询复材展厅列表
export function listCompositeMaterial(query) {
  return request({
    url: '/system/compositeMaterial/list',
    method: 'get',
    params: query
  })
}

// 查询复材展厅详细
export function getCompositeMaterial(id) {
  return request({
    url: '/system/compositeMaterial/' + id,
    method: 'get'
  })
}

// 新增复材展厅
export function addCompositeMaterial(data) {
  return request({
    url: '/system/compositeMaterial',
    method: 'post',
    data: data
  })
}

// 修改复材展厅
export function updateCompositeMaterial(data) {
  return request({
    url: '/system/compositeMaterial',
    method: 'put',
    data: data
  })
}

// 删除复材展厅
export function delCompositeMaterial(id) {
  return request({
    url: '/system/compositeMaterial/' + id,
    method: 'delete'
  })
}
