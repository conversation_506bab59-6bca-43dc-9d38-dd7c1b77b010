{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyDemand\\detail\\index1.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyDemand\\detail\\index1.vue", "mtime": 1750311963050}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_demand", "_oss", "_auth", "_store", "_zhm", "_cryptoJs", "id", "secret<PERSON>ey", "_default", "exports", "default", "name", "dicts", "components", "UserMenu", "data", "isDetail", "title", "imageUrl", "actionUrl", "uploadUrl", "headers", "Authorization", "getToken", "process", "env", "VUE_APP_BASE_API", "accept", "isCreate", "imgVisible", "user", "tel", "store", "getters", "companyName", "bussinessNo", "phonenumber", "keywords", "applicationsInput", "info", "displayRestrictions", "undefined", "form", "accountLicenceList", "list", "tableData", "rules", "demandTitle", "required", "message", "trigger", "summary", "contactsName", "contactsMobile", "created", "handleKeywordList", "methods", "handleDelete", "$router", "push", "initForm", "applicationArea", "scenePicture", "applicationAreaList", "scenePictureList", "auditStatus", "displayStatus", "publisherName", "publisherMobile", "getDetail", "_this", "$route", "query", "getDemandDetail", "then", "response", "key", "CryptoJS", "enc", "Utf8", "parse", "decrypt", "AES", "mode", "ECB", "padding", "pad", "Pkcs7", "res", "JSON", "stringify", "total", "goBack", "go", "getUrl", "str", "length", "url", "handleInputConfirm", "val", "handleClose", "tag", "splice", "indexOf", "handleSummaryClose", "handleBeforeUpload", "file", "type", "size", "typeList", "split", "map", "item", "trim", "toLowerCase", "substr", "dotIndex", "lastIndexOf", "$message", "error", "suffix", "substring", "handlePictureCardPreview", "handleRemove", "fileList", "handleSuccess", "code", "changeMode", "goCreate", "businessNo", "handleFilePreview", "window", "open", "displayRestrictionChanged", "_this2", "dict", "display_restrictions", "for<PERSON>ach", "label", "value", "submitForm", "_this3", "$refs", "validate", "valid", "join", "createDemand", "_objectSpread2", "isSubmit", "$modal", "msgSuccess", "edit<PERSON><PERSON><PERSON>", "_this4", "console", "log", "demandTypeName", "keywordList1", "rows", "handleApplicationRemove", "application", "handleApplicationSuccess", "applicationName", "handleAccountRemove", "accountLicence", "handleAccountSuccess", "accountLicenceName"], "sources": ["src/views/system/user/companyDemand/detail/index1.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-11 15:20:15\r\n * @LastEditTime: 2023-02-28 08:48:59\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-11 15:18:41\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"company-demand-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">匹配资源列表</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                资源列表\r\n              </div>\r\n            </div>\r\n            <el-table :data=\"tableData\" style=\"width: 100%\">\r\n              <el-table-column prop=\"companyName\" label=\"公司名称\" width=\"240\">\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"applicationArea\"\r\n                label=\"应用领域\"\r\n                width=\"180\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                prop=\"summary\"\r\n                style=\"\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  overflow: hidden;\r\n                  text-overflow: ellipsis;\r\n                  -webkit-line-clamp: 2;\r\n                  line-clamp: 2;\r\n                \"\r\n                label=\"描述\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" width=\"120\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    type=\"danger\"\r\n                    @click=\"handleDelete(scope.row)\"\r\n                    >查看详情</el-button\r\n                  >\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport { getDemandDetail, createDemand, editDemand } from \"@/api/system/demand\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport store from \"@/store\";\r\nimport { keywordList1 } from \"@/api/zhm\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\r\n    \"affiliated_unit\",\r\n    \"capital_source\",\r\n    \"affiliated_street\",\r\n    \"display_restrictions\",\r\n  ],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      title: \"需求详情\",\r\n      imageUrl: \"\",\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      isCreate: false,\r\n      imgVisible: false,\r\n      user: {\r\n        tel: store.getters.tel,\r\n        name: store.getters.name,\r\n        companyName: store.getters.companyName,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        phonenumber: store.getters.phonenumber,\r\n      },\r\n      keywords: [],\r\n      applicationsInput: \"\",\r\n      info: {},\r\n      // 展示限制\r\n      displayRestrictions: undefined,\r\n      form: {},\r\n      accountLicenceList: [],\r\n      list: {},\r\n      tableData: [],\r\n      // 表单校验\r\n      rules: {\r\n        demandTitle: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" },\r\n        ],\r\n        displayRestrictions: [\r\n          { required: true, message: \"请选择展示性质\", trigger: \"blur\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"需求描述不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.handleKeywordList();\r\n  },\r\n  methods: {\r\n    handleDelete(data) {\r\n      this.$router.push(\"/demandHallDetail?id=\" + data.id);\r\n    },\r\n    initForm() {\r\n      this.form = {\r\n        applicationArea: [],\r\n        scenePicture: [],\r\n        applicationAreaList: [],\r\n        scenePictureList: [],\r\n        keywords: [],\r\n        auditStatus: \"1\",\r\n        displayStatus: \"2\",\r\n        publisherName: this.user.name,\r\n        publisherMobile: this.user.tel,\r\n      };\r\n    },\r\n    getDetail() {\r\n      let id = this.$route.query.id;\r\n      getDemandDetail(id).then((response) => {\r\n        let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n        let decrypt = CryptoJS.AES.decrypt(response, key, {\r\n          mode: CryptoJS.mode.ECB,\r\n          padding: CryptoJS.pad.Pkcs7,\r\n        });\r\n        res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    getUrl(str) {\r\n      if (str && str != null) {\r\n        var list = JSON.parse(str);\r\n        if (list && list.length > 0) {\r\n          return list[0].url;\r\n        }\r\n      }\r\n\r\n      return null;\r\n    },\r\n    // 应用领域新增\r\n    handleInputConfirm() {\r\n      let val = this.applicationsInput;\r\n      if (val) {\r\n        this.form.applicationAreaList.push(val);\r\n      }\r\n      this.applicationsInput = \"\";\r\n    },\r\n    // 应用领域移除\r\n    handleClose(tag) {\r\n      this.form.applicationAreaList.splice(\r\n        this.form.applicationAreaList.indexOf(tag),\r\n        1\r\n      );\r\n    },\r\n    handleSummaryClose(tag) {\r\n      this.form.keywords.splice(this.form.keywords.indexOf(tag), 1);\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePictureCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.form.scenePictureList = fileList;\r\n    },\r\n    handleSuccess(response, file) {\r\n      if (response.code == 200) {\r\n        if (this.form.scenePictureList == null) {\r\n          this.form.scenePictureList = [];\r\n        }\r\n        this.form.scenePictureList.push(response.data);\r\n      }\r\n    },\r\n    changeMode() {\r\n      if (this.isCreate) {\r\n        this.goBack();\r\n        return;\r\n      }\r\n      if (this.isDetail) {\r\n        this.title = \"编辑需求\";\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n        if (this.info.applicationArea) {\r\n          this.form.applicationAreaList = this.info.applicationArea.split(\",\");\r\n        } else {\r\n          this.form.applicationAreaList = [];\r\n        }\r\n        if (this.info.keywords) {\r\n          this.form.keywords = this.info.keywords.split(\",\");\r\n        }\r\n\r\n        if (this.info.scenePicture && this.info.scenePicture != \"null\") {\r\n          this.form.scenePictureList = JSON.parse(this.info.scenePicture);\r\n        } else {\r\n          this.form.scenePictureList = [];\r\n        }\r\n      } else {\r\n        this.isDetail = true;\r\n        this.title = \"需求详情\";\r\n        this.initForm();\r\n        this.getDetail();\r\n      }\r\n    },\r\n    goCreate() {\r\n      this.title = \"新增需求\";\r\n      this.isDetail = false;\r\n      this.initForm();\r\n      this.form.companyName = this.user.companyName;\r\n      this.form.contactsName = this.user.name;\r\n      this.form.contactsMobile = this.user.phonenumber;\r\n      this.form.publisherName = this.user.name;\r\n      this.form.publisherMobile = this.user.phonenumber;\r\n      this.form.businessNo = this.user.bussinessNo;\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    displayRestrictionChanged(res) {\r\n      this.dict.type.display_restrictions.forEach((item) => {\r\n        if (item.label == res) {\r\n          this.form.displayRestrictions = item.value;\r\n        }\r\n      });\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          if (\r\n            this.form.applicationArea &&\r\n            this.form.applicationArea.length > 0\r\n          ) {\r\n            this.form.applicationArea = this.form.applicationAreaList.join(\",\");\r\n          } else {\r\n            this.form.applicationArea = \"\";\r\n          }\r\n          this.form.scenePicture = JSON.stringify(this.form.scenePictureList);\r\n          this.form.businessNo = this.user.bussinessNo;\r\n          if (this.form.keywords && this.form.keywords.length > 0) {\r\n            this.form.keywords = this.form.keywords.join(\",\");\r\n          } else {\r\n            this.form.keywords = \"\";\r\n          }\r\n          if (this.isCreate) {\r\n            createDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          } else {\r\n            this.form.auditStatus = 1;\r\n            editDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      console.log(this.$route.query.key);\r\n      let info = JSON.parse(this.$route.query.key);\r\n      this.list.demandTypeName = info.demandTypeName;\r\n      this.list.summary = info.summary;\r\n      this.list.applicationArea = info.applicationArea;\r\n      keywordList1(this.list).then((res) => {\r\n        this.tableData = res.rows;\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.application = res.data.url;\r\n        this.form.applicationName = res.data.name;\r\n      }\r\n    },\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.el-table .cell {\r\n  display: -webkit-box; /* 将元素设为一个弹性伸缩盒子 */\r\n  -webkit-box-orient: vertical; /* 设定元素为垂直方向 */\r\n  overflow: hidden; /* 隐藏超出部分 */\r\n  text-overflow: ellipsis; /* 超出部分用省略号(...)表示 */\r\n  -webkit-line-clamp: 2; /* 限制文本显示2行 */\r\n  line-clamp: 2;\r\n}\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .company-demand-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 20px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .mt_40 {\r\n          margin-top: 40px;\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 90%;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-textarea__inner {\r\n          width: 90%;\r\n        }\r\n        .add-demand-tag {\r\n          margin-right: 10px;\r\n          height: 32px;\r\n          line-height: 32px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n          color: #333;\r\n        }\r\n        .el-button:hover,\r\n        .el-button:focus {\r\n          border-color: #d9d9d9;\r\n          background-color: #fff;\r\n        }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8 !important;\r\n          border-color: #21c9b8 !important;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AA2EA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,IAAA,GAAAF,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,IAAA,GAAAL,OAAA;AACA,IAAAM,SAAA,GAAAP,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARA,IAAAO,EAAA;AASA,IAAAC,SAAA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,KAAA,GACA,mBACA,kBACA,qBACA,uBACA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,KAAA;MACAC,QAAA;MACAC,SAAA,MAAAC,cAAA;MACAC,OAAA;QAAAC,aAAA,kBAAAC,cAAA;MAAA;MACAH,SAAA,EAAAI,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MAAA;MACAC,MAAA;MACAC,QAAA;MACAC,UAAA;MACAC,IAAA;QACAC,GAAA,EAAAC,cAAA,CAAAC,OAAA,CAAAF,GAAA;QACApB,IAAA,EAAAqB,cAAA,CAAAC,OAAA,CAAAtB,IAAA;QACAuB,WAAA,EAAAF,cAAA,CAAAC,OAAA,CAAAC,WAAA;QACAC,WAAA,EAAAH,cAAA,CAAAC,OAAA,CAAAE,WAAA;QACAC,WAAA,EAAAJ,cAAA,CAAAC,OAAA,CAAAG;MACA;MACAC,QAAA;MACAC,iBAAA;MACAC,IAAA;MACA;MACAC,mBAAA,EAAAC,SAAA;MACAC,IAAA;MACAC,kBAAA;MACAC,IAAA;MACAC,SAAA;MACA;MACAC,KAAA;QACAC,WAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,mBAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,OAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,YAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,WAAA,GACA;UAAAc,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,cAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAI,OAAA,WAAAA,QAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAA1C,IAAA;MACA,KAAA2C,OAAA,CAAAC,IAAA,2BAAA5C,IAAA,CAAAT,EAAA;IACA;IACAsD,QAAA,WAAAA,SAAA;MACA,KAAAlB,IAAA;QACAmB,eAAA;QACAC,YAAA;QACAC,mBAAA;QACAC,gBAAA;QACA3B,QAAA;QACA4B,WAAA;QACAC,aAAA;QACAC,aAAA,OAAArC,IAAA,CAAAnB,IAAA;QACAyD,eAAA,OAAAtC,IAAA,CAAAC;MACA;IACA;IACAsC,SAAA,WAAAA,UAAA;MAAA,IAAAC,KAAA;MACA,IAAAhE,EAAA,QAAAiE,MAAA,CAAAC,KAAA,CAAAlE,EAAA;MACA,IAAAmE,uBAAA,EAAAnE,EAAA,EAAAoE,IAAA,WAAAC,QAAA;QACA,IAAAC,GAAA,GAAAC,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAzE,SAAA;QACA,IAAA0E,OAAA,GAAAJ,iBAAA,CAAAK,GAAA,CAAAD,OAAA,CAAAN,QAAA,EAAAC,GAAA;UACAO,IAAA,EAAAN,iBAAA,CAAAM,IAAA,CAAAC,GAAA;UACAC,OAAA,EAAAR,iBAAA,CAAAS,GAAA,CAAAC;QACA;QACAC,GAAA,GAAAC,IAAA,CAAAT,KAAA,CAAAH,iBAAA,CAAAC,GAAA,CAAAC,IAAA,CAAAW,SAAA,CAAAT,OAAA;QACAX,KAAA,CAAA/B,IAAA,GAAAoC,QAAA,CAAA5D,IAAA;QACAuD,KAAA,CAAAqB,KAAA,GAAAhB,QAAA,CAAAgB,KAAA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAlC,OAAA,CAAAmC,EAAA;IACA;IACAC,MAAA,WAAAA,OAAAC,GAAA;MACA,IAAAA,GAAA,IAAAA,GAAA;QACA,IAAAnD,IAAA,GAAA6C,IAAA,CAAAT,KAAA,CAAAe,GAAA;QACA,IAAAnD,IAAA,IAAAA,IAAA,CAAAoD,MAAA;UACA,OAAApD,IAAA,IAAAqD,GAAA;QACA;MACA;MAEA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,IAAAC,GAAA,QAAA7D,iBAAA;MACA,IAAA6D,GAAA;QACA,KAAAzD,IAAA,CAAAqB,mBAAA,CAAAJ,IAAA,CAAAwC,GAAA;MACA;MACA,KAAA7D,iBAAA;IACA;IACA;IACA8D,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAA3D,IAAA,CAAAqB,mBAAA,CAAAuC,MAAA,CACA,KAAA5D,IAAA,CAAAqB,mBAAA,CAAAwC,OAAA,CAAAF,GAAA,GACA,CACA;IACA;IACAG,kBAAA,WAAAA,mBAAAH,GAAA;MACA,KAAA3D,IAAA,CAAAL,QAAA,CAAAiE,MAAA,MAAA5D,IAAA,CAAAL,QAAA,CAAAkE,OAAA,CAAAF,GAAA;IACA;IACA;IACAI,kBAAA,WAAAA,mBAAAC,IAAA;MACA,IAAA/F,IAAA,GAAA+F,IAAA,CAAA/F,IAAA;QAAAgG,IAAA,GAAAD,IAAA,CAAAC,IAAA;QAAAC,IAAA,GAAAF,IAAA,CAAAE,IAAA;MACA,IAAAC,QAAA,QAAAlF,MAAA,CACAmF,KAAA,MACAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,IAAA,GAAAC,WAAA,GAAAC,MAAA;MAAA;MACA,IAAAC,QAAA,GAAAzG,IAAA,CAAA0G,WAAA;MACA;MACA,IAAAD,QAAA;QACA,KAAAE,QAAA,CAAAC,KAAA;QACA;MACA;QACA,IAAAC,MAAA,GAAA7G,IAAA,CAAA8G,SAAA,CAAAL,QAAA;QACA,IAAAP,QAAA,CAAAN,OAAA,CAAAiB,MAAA,CAAAN,WAAA;UACA,KAAAI,QAAA,CAAAC,KAAA;UACA;QACA;MACA;MACA;MACA,IAAAX,IAAA;QACA,KAAAU,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACA;IACAG,wBAAA,WAAAA,yBAAAhB,IAAA;MACA,KAAAxF,QAAA,GAAAwF,IAAA,CAAAT,GAAA;MACA,KAAApE,UAAA;IACA;IACA;IACA8F,YAAA,WAAAA,aAAAjB,IAAA,EAAAkB,QAAA;MACA,KAAAlF,IAAA,CAAAsB,gBAAA,GAAA4D,QAAA;IACA;IACAC,aAAA,WAAAA,cAAAlD,QAAA,EAAA+B,IAAA;MACA,IAAA/B,QAAA,CAAAmD,IAAA;QACA,SAAApF,IAAA,CAAAsB,gBAAA;UACA,KAAAtB,IAAA,CAAAsB,gBAAA;QACA;QACA,KAAAtB,IAAA,CAAAsB,gBAAA,CAAAL,IAAA,CAAAgB,QAAA,CAAA5D,IAAA;MACA;IACA;IACAgH,UAAA,WAAAA,WAAA;MACA,SAAAnG,QAAA;QACA,KAAAgE,MAAA;QACA;MACA;MACA,SAAA5E,QAAA;QACA,KAAAC,KAAA;QACA,KAAAD,QAAA;QACA,KAAA0B,IAAA,QAAAH,IAAA;QACA,SAAAA,IAAA,CAAAsB,eAAA;UACA,KAAAnB,IAAA,CAAAqB,mBAAA,QAAAxB,IAAA,CAAAsB,eAAA,CAAAiD,KAAA;QACA;UACA,KAAApE,IAAA,CAAAqB,mBAAA;QACA;QACA,SAAAxB,IAAA,CAAAF,QAAA;UACA,KAAAK,IAAA,CAAAL,QAAA,QAAAE,IAAA,CAAAF,QAAA,CAAAyE,KAAA;QACA;QAEA,SAAAvE,IAAA,CAAAuB,YAAA,SAAAvB,IAAA,CAAAuB,YAAA;UACA,KAAApB,IAAA,CAAAsB,gBAAA,GAAAyB,IAAA,CAAAT,KAAA,MAAAzC,IAAA,CAAAuB,YAAA;QACA;UACA,KAAApB,IAAA,CAAAsB,gBAAA;QACA;MACA;QACA,KAAAhD,QAAA;QACA,KAAAC,KAAA;QACA,KAAA2C,QAAA;QACA,KAAAS,SAAA;MACA;IACA;IACA2D,QAAA,WAAAA,SAAA;MACA,KAAA/G,KAAA;MACA,KAAAD,QAAA;MACA,KAAA4C,QAAA;MACA,KAAAlB,IAAA,CAAAR,WAAA,QAAAJ,IAAA,CAAAI,WAAA;MACA,KAAAQ,IAAA,CAAAU,YAAA,QAAAtB,IAAA,CAAAnB,IAAA;MACA,KAAA+B,IAAA,CAAAW,cAAA,QAAAvB,IAAA,CAAAM,WAAA;MACA,KAAAM,IAAA,CAAAyB,aAAA,QAAArC,IAAA,CAAAnB,IAAA;MACA,KAAA+B,IAAA,CAAA0B,eAAA,QAAAtC,IAAA,CAAAM,WAAA;MACA,KAAAM,IAAA,CAAAuF,UAAA,QAAAnG,IAAA,CAAAK,WAAA;IACA;IACA+F,iBAAA,WAAAA,kBAAAxB,IAAA;MACAyB,MAAA,CAAAC,IAAA,CAAA1B,IAAA;IACA;IACA2B,yBAAA,WAAAA,0BAAA7C,GAAA;MAAA,IAAA8C,MAAA;MACA,KAAAC,IAAA,CAAA5B,IAAA,CAAA6B,oBAAA,CAAAC,OAAA,WAAAzB,IAAA;QACA,IAAAA,IAAA,CAAA0B,KAAA,IAAAlD,GAAA;UACA8C,MAAA,CAAA5F,IAAA,CAAAF,mBAAA,GAAAwE,IAAA,CAAA2B,KAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAjC,IAAA;MAAA,IAAAkC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IACAH,MAAA,CAAAnG,IAAA,CAAAmB,eAAA,IACAgF,MAAA,CAAAnG,IAAA,CAAAmB,eAAA,CAAAmC,MAAA,MACA;YACA6C,MAAA,CAAAnG,IAAA,CAAAmB,eAAA,GAAAgF,MAAA,CAAAnG,IAAA,CAAAqB,mBAAA,CAAAkF,IAAA;UACA;YACAJ,MAAA,CAAAnG,IAAA,CAAAmB,eAAA;UACA;UACAgF,MAAA,CAAAnG,IAAA,CAAAoB,YAAA,GAAA2B,IAAA,CAAAC,SAAA,CAAAmD,MAAA,CAAAnG,IAAA,CAAAsB,gBAAA;UACA6E,MAAA,CAAAnG,IAAA,CAAAuF,UAAA,GAAAY,MAAA,CAAA/G,IAAA,CAAAK,WAAA;UACA,IAAA0G,MAAA,CAAAnG,IAAA,CAAAL,QAAA,IAAAwG,MAAA,CAAAnG,IAAA,CAAAL,QAAA,CAAA2D,MAAA;YACA6C,MAAA,CAAAnG,IAAA,CAAAL,QAAA,GAAAwG,MAAA,CAAAnG,IAAA,CAAAL,QAAA,CAAA4G,IAAA;UACA;YACAJ,MAAA,CAAAnG,IAAA,CAAAL,QAAA;UACA;UACA,IAAAwG,MAAA,CAAAjH,QAAA;YACA,IAAAsH,oBAAA,MAAAC,cAAA,CAAAzI,OAAA,MAAAyI,cAAA,CAAAzI,OAAA,MAAAmI,MAAA,CAAAnG,IAAA;cAAA0G,QAAA,EAAAzC;YAAA,IAAAjC,IAAA,WAAAC,QAAA;cACAkE,MAAA,CAAAQ,MAAA,CAAAC,UAAA;cACAT,MAAA,CAAAd,UAAA;YACA;UACA;YACAc,MAAA,CAAAnG,IAAA,CAAAuB,WAAA;YACA,IAAAsF,kBAAA,MAAAJ,cAAA,CAAAzI,OAAA,MAAAyI,cAAA,CAAAzI,OAAA,MAAAmI,MAAA,CAAAnG,IAAA;cAAA0G,QAAA,EAAAzC;YAAA,IAAAjC,IAAA,WAAAC,QAAA;cACAkE,MAAA,CAAAQ,MAAA,CAAAC,UAAA;cACAT,MAAA,CAAAd,UAAA;YACA;UACA;QACA;MACA;IACA;IACAxE,iBAAA,WAAAA,kBAAA;MAAA,IAAAiG,MAAA;MACAC,OAAA,CAAAC,GAAA,MAAAnF,MAAA,CAAAC,KAAA,CAAAI,GAAA;MACA,IAAArC,IAAA,GAAAkD,IAAA,CAAAT,KAAA,MAAAT,MAAA,CAAAC,KAAA,CAAAI,GAAA;MACA,KAAAhC,IAAA,CAAA+G,cAAA,GAAApH,IAAA,CAAAoH,cAAA;MACA,KAAA/G,IAAA,CAAAO,OAAA,GAAAZ,IAAA,CAAAY,OAAA;MACA,KAAAP,IAAA,CAAAiB,eAAA,GAAAtB,IAAA,CAAAsB,eAAA;MACA,IAAA+F,iBAAA,OAAAhH,IAAA,EAAA8B,IAAA,WAAAc,GAAA;QACAgE,MAAA,CAAA3G,SAAA,GAAA2C,GAAA,CAAAqE,IAAA;MACA;IACA;IACAC,uBAAA,WAAAA,wBAAApD,IAAA,EAAAkB,QAAA;MACA,KAAAlF,IAAA,CAAAqH,WAAA;IACA;IACAC,wBAAA,WAAAA,yBAAAxE,GAAA,EAAAkB,IAAA,EAAAkB,QAAA;MACA;MACA,IAAApC,GAAA,CAAAsC,IAAA;QACA,KAAApF,IAAA,CAAAqH,WAAA,GAAAvE,GAAA,CAAAzE,IAAA,CAAAkF,GAAA;QACA,KAAAvD,IAAA,CAAAuH,eAAA,GAAAzE,GAAA,CAAAzE,IAAA,CAAAJ,IAAA;MACA;IACA;IACAuJ,mBAAA,WAAAA,oBAAAxD,IAAA,EAAAkB,QAAA;MACA,KAAAlF,IAAA,CAAAyH,cAAA;IACA;IACAC,oBAAA,WAAAA,qBAAA5E,GAAA,EAAAkB,IAAA,EAAAkB,QAAA;MACA;MACA,IAAApC,GAAA,CAAAsC,IAAA;QACA,KAAApF,IAAA,CAAAyH,cAAA,GAAA3E,GAAA,CAAAzE,IAAA,CAAAkF,GAAA;QACA,KAAAvD,IAAA,CAAA2H,kBAAA,GAAA7E,GAAA,CAAAzE,IAAA,CAAAJ,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}