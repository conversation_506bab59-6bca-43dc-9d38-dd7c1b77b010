{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\index.vue", "mtime": 1750311962991}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBnYXRld2F5RGVtYW5kTGlzdFNob3csDQogIGdhdGV3YXlTdXBwbHlMaXN0U2hvdywNCiAgZ2V0Q29tcGFueUxpc3RMYiwNCiAgZ2V0RXhwZXJ0TGlzdEZvdXIsDQogIGdldEFjdGl2aXR5TGlzdCwNCn0gZnJvbSAiQC9hcGkvcHVyY2hhc2VTYWxlcyI7DQppbXBvcnQgQ3J5cHRvSlMgZnJvbSAiY3J5cHRvLWpzIjsNCmxldCBzZWNyZXRLZXkgPSAiOXpWbjAlYnFtVVlTR3cybiI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgc2hvd0RlbWFuZDogdHJ1ZSwgLy/mib7pnIDmsYLmjInpkq7nrq3lpLTmmK/lkKblsZXnpLoNCiAgICAgIHNob3dSZXNvdXJjZXM6IGZhbHNlLCAvL+aJvui1hOa6kOaMiemSrueureWktOaYr+WQpuWxleekug0KICAgICAgZGVtYW5kQnRuOiBmYWxzZSwNCiAgICAgIHB1cmNoYXNlTG9hZGluZzogZmFsc2UsDQogICAgICBkYXRhOiBbXSwNCiAgICAgIGNvbXBhbnlEYXRhOiBbXSwgLy/mjqjojZDkvIHkuJrliJfooagNCiAgICAgIGV4cGVydERhdGE6IFtdLCAvL+S4k+WutuaZuuW6k+WIl+ihqA0KICAgICAgYWN0aXZpdHlEYXRhOiBbXSwgLy/mtLvliqjlub/lnLrliJfooagNCiAgICAgIGRlbWFuZFR5cGU6ICIxIiwNCiAgICAgIHRlY2hub2xvZ3lUeXBlOiAi5Zu95Lqn5YyW5pu/5LujIiwNCiAgICAgIHBhZ2VOdW06IDEsDQogICAgICBwYWdlU2l6ZTogNiwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgbmFtZTogIiIsDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldERlbWFuZExpc3QoKTsNCiAgICB0aGlzLmdldENvbXBhbnlMaXN0TGIoKTsNCiAgICB0aGlzLmdldEV4cGVydExpc3RGb3VyKCk7DQogICAgdGhpcy5nZXRBY3Rpdml0eUxpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8vIOaJvumcgOaxguOAgeaJvui1hOa6kOaMiemSruaYvuekuuagt+W8jw0KICAgIHNlYXJjaENvbnRlbnQoZmxhZykgew0KICAgICAgaWYgKGZsYWcgPT09ICIxIikgew0KICAgICAgICB0aGlzLnNob3dEZW1hbmQgPSB0cnVlOw0KICAgICAgICB0aGlzLnNob3dSZXNvdXJjZXMgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5kZW1hbmRUeXBlID0gIjEiOw0KICAgICAgICB0aGlzLmdldERlbWFuZExpc3QoKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2hvd0RlbWFuZCA9IGZhbHNlOw0KICAgICAgICB0aGlzLnNob3dSZXNvdXJjZXMgPSB0cnVlOw0KICAgICAgICB0aGlzLnRlY2hub2xvZ3lUeXBlID0gIuWbveS6p+WMluabv+S7oyI7DQogICAgICAgIHRoaXMuZ2V0U3VwcGx5TGlzdCgpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6Lez6L2s5Yiw5ZWG5py66ZyA5rGC5YiX6KGoDQogICAgdmlld01vcmVEZW1hbmQoKSB7DQogICAgICBsZXQgcm91dGVEYXRhID0gdGhpcy4kcm91dGVyLnJlc29sdmUoew0KICAgICAgICBwYXRoOiAiL2RlbWFuZEhhbGwiLA0KICAgICAgfSk7DQogICAgICB3aW5kb3cub3Blbihyb3V0ZURhdGEuaHJlZiwgIl9ibGFuayIpOw0KICAgIH0sDQogICAgLy8g6Lez6L2s5Yiw6LWE5rqQ5aSn5Y6FDQogICAgdmlld01vcmVTdXBwbHkoKSB7DQogICAgICBsZXQgcm91dGVEYXRhID0gdGhpcy4kcm91dGVyLnJlc29sdmUoew0KICAgICAgICBwYXRoOiAiL3Jlc291cmNlSGFsbCIsDQogICAgICB9KTsNCiAgICAgIHdpbmRvdy5vcGVuKHJvdXRlRGF0YS5ocmVmLCAiX2JsYW5rIik7DQogICAgfSwNCiAgICAvLyDot7PovazliLDpnIDmsYLor6bmg4Uv6LWE5rqQ6aG16Z2iDQogICAgZ29Nb3JlRGV0YWlsKGlkKSB7DQogICAgICBsZXQgcGF0aCA9ICIiOw0KICAgICAgdGhpcy5zaG93RGVtYW5kDQogICAgICAgID8gKHBhdGggPSAiL2RlbWFuZEhhbGxEZXRhaWwiKQ0KICAgICAgICA6IChwYXRoID0gIi9yZXNvdXJjZUhhbGxEZXRhaWwiKTsNCiAgICAgIGxldCByb3V0ZURhdGEgPSB0aGlzLiRyb3V0ZXIucmVzb2x2ZSh7DQogICAgICAgIHBhdGg6IHBhdGgsDQogICAgICAgIHF1ZXJ5OiB7IGlkIH0sDQogICAgICB9KTsNCiAgICAgIHdpbmRvdy5vcGVuKHJvdXRlRGF0YS5ocmVmLCAiX2JsYW5rIik7DQogICAgfSwNCiAgICAvLyDmib7pnIDmsYINCiAgICBnZXREZW1hbmRMaXN0KCkgew0KICAgICAgdGhpcy5wdXJjaGFzZUxvYWRpbmcgPSB0cnVlOw0KICAgICAgZ2F0ZXdheURlbWFuZExpc3RTaG93KHsNCiAgICAgICAgZGVtYW5kVHlwZTogdGhpcy5kZW1hbmRUeXBlLA0KICAgICAgICBjaXR5OiAi6Z2S5bKb5biCIiwNCiAgICAgICAgcmVnaW9uOiAi5Z+O6Ziz5Yy6IiwNCiAgICAgICAgZGlzcGxheVN0YXR1czogMSwNCiAgICAgICAgYXVkaXRTdGF0dXM6IDIsDQogICAgICAgIHBhZ2VOdW06IHRoaXMucGFnZU51bSwNCiAgICAgICAgLy8gcGFnZVNpemU6IHRoaXMucGFnZVNpemUsDQogICAgICAgIG5hbWU6IHRoaXMubmFtZSwNCiAgICAgIH0pDQogICAgICAgIC50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICBsZXQga2V5ID0gQ3J5cHRvSlMuZW5jLlV0ZjgucGFyc2Uoc2VjcmV0S2V5KTsNCiAgICAgICAgICBsZXQgZGVjcnlwdCA9IENyeXB0b0pTLkFFUy5kZWNyeXB0KHJlcywga2V5LCB7DQogICAgICAgICAgICBtb2RlOiBDcnlwdG9KUy5tb2RlLkVDQiwNCiAgICAgICAgICAgIHBhZGRpbmc6IENyeXB0b0pTLnBhZC5Qa2NzNywNCiAgICAgICAgICB9KTsNCiAgICAgICAgICByZXMgPSBKU09OLnBhcnNlKENyeXB0b0pTLmVuYy5VdGY4LnN0cmluZ2lmeShkZWNyeXB0KSk7DQogICAgICAgICAgdGhpcy5wdXJjaGFzZUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICBsZXQgeyByb3dzIH0gPSByZXMgfHwgW107DQogICAgICAgICAgdGhpcy5kYXRhID0gcm93czsNCiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzLnRvdGFsOw0KICAgICAgICAgIHRoaXMuZGF0YS5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgICAgICBpZiAoaXRlbS5zY2VuZVBpY3R1cmUpIHsNCiAgICAgICAgICAgICAgaXRlbS5zY2VuZVBpY3R1cmUgPSBKU09OLnBhcnNlKGl0ZW0uc2NlbmVQaWN0dXJlKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHsNCiAgICAgICAgICB0aGlzLnB1cmNoYXNlTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuZGF0YSA9IFtdOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOaJvui1hOa6kA0KICAgIGdldFN1cHBseUxpc3QoKSB7DQogICAgICAvLyB0aGlzLnB1cmNoYXNlTG9hZGluZyA9IHRydWU7DQogICAgICBnYXRld2F5U3VwcGx5TGlzdFNob3coew0KICAgICAgICB0ZWNobm9sb2d5VHlwZTogdGhpcy50ZWNobm9sb2d5VHlwZSwNCiAgICAgICAgY2l0eTogIumdkuWym+W4giIsDQogICAgICAgIHJlZ2lvbjogIuWfjumYs+WMuiIsDQogICAgICAgIGF1ZGl0U3RhdHVzOiAyLA0KICAgICAgICBkaXNwbGF5U3RhdHVzOiAxLA0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICAvLyBwYWdlU2l6ZTogNiwNCiAgICAgICAgbmFtZTogdGhpcy5uYW1lLA0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGxldCBrZXkgPSBDcnlwdG9KUy5lbmMuVXRmOC5wYXJzZShzZWNyZXRLZXkpOw0KICAgICAgICAgIGxldCBkZWNyeXB0ID0gQ3J5cHRvSlMuQUVTLmRlY3J5cHQocmVzLCBrZXksIHsNCiAgICAgICAgICAgIG1vZGU6IENyeXB0b0pTLm1vZGUuRUNCLA0KICAgICAgICAgICAgcGFkZGluZzogQ3J5cHRvSlMucGFkLlBrY3M3LA0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHJlcyA9IEpTT04ucGFyc2UoQ3J5cHRvSlMuZW5jLlV0Zjguc3RyaW5naWZ5KGRlY3J5cHQpKTsNCiAgICAgICAgICB0aGlzLnRvdGFsID0gcmVzLnRvdGFsOw0KICAgICAgICAgIHRoaXMucHVyY2hhc2VMb2FkaW5nID0gZmFsc2U7DQogICAgICAgICAgbGV0IHsgcm93cyB9ID0gcmVzIHx8IFtdOw0KICAgICAgICAgIHRoaXMuZGF0YSA9IHJvd3M7DQogICAgICAgICAgdGhpcy5kYXRhLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICAgIGl0ZW0ucHJvZHVjdFBob3RvID0gSlNPTi5wYXJzZShpdGVtLnByb2R1Y3RQaG90byk7DQogICAgICAgICAgfSk7DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgLy8gdGhpcy5wdXJjaGFzZUxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmRhdGEgPSBbXTsNCiAgICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDmib7pnIDmsYJ0YWLliIfmjaINCiAgICB0YWJEZW1hbmQoKSB7DQogICAgICB0aGlzLmdldERlbWFuZExpc3QoKTsNCiAgICB9LA0KICAgIC8vIOaJvui1hOa6kHRhYuWIh+aNog0KICAgIHRhYlRlY2hub2xvZ3koKSB7DQogICAgICB0aGlzLmdldFN1cHBseUxpc3QoKTsNCiAgICB9LA0KICAgIC8vIOaOqOiNkOS8geS4muWIl+ihqA0KICAgIGdldENvbXBhbnlMaXN0TGIoKSB7DQogICAgICBnZXRDb21wYW55TGlzdExiKHsgcmVjb21tZW5kU3RhdHVzOiAxIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBsZXQga2V5ID0gQ3J5cHRvSlMuZW5jLlV0ZjgucGFyc2Uoc2VjcmV0S2V5KTsNCiAgICAgICAgbGV0IGRlY3J5cHQgPSBDcnlwdG9KUy5BRVMuZGVjcnlwdChyZXMsIGtleSwgew0KICAgICAgICAgIG1vZGU6IENyeXB0b0pTLm1vZGUuRUNCLA0KICAgICAgICAgIHBhZGRpbmc6IENyeXB0b0pTLnBhZC5Qa2NzNywNCiAgICAgICAgfSk7DQogICAgICAgIHJlcyA9IEpTT04ucGFyc2UoQ3J5cHRvSlMuZW5jLlV0Zjguc3RyaW5naWZ5KGRlY3J5cHQpKTsNCiAgICAgICAgbGV0IGRhdGEgPSByZXMucm93cyB8fCBbXTsNCiAgICAgICAgdGhpcy5jb21wYW55RGF0YSA9IFtdOw0KICAgICAgICBmb3IgKHZhciBpID0gMDsgaSA8IGRhdGEubGVuZ3RoOyBpICs9IDMpIHsNCiAgICAgICAgICB0aGlzLmNvbXBhbnlEYXRhLnB1c2goZGF0YS5zbGljZShpLCBpICsgMykpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOi3s+i9rOWIsOS8geS4muWQjeW9lemhtemdog0KICAgIGdvTW9yZUNvbXBhbnkoKSB7DQogICAgICBsZXQgcm91dGVEYXRhID0gdGhpcy4kcm91dGVyLnJlc29sdmUoew0KICAgICAgICBwYXRoOiAiL2VudGVycHJpc2VMaXN0IiwNCiAgICAgIH0pOw0KICAgICAgd2luZG93Lm9wZW4ocm91dGVEYXRhLmhyZWYsICJfYmxhbmsiKTsNCiAgICB9LA0KICAgIC8vIOi3s+i9rOWIsOS8geS4muivpuaDhemhtemdog0KICAgIHZpZXdDb21wYW55RGV0YWlsKHZhbCkgew0KICAgICAgbGV0IHJvdXRlRGF0YSA9IHRoaXMuJHJvdXRlci5yZXNvbHZlKHsNCiAgICAgICAgcGF0aDogIi9lbnRlcnByaXNlRGV0YWlsIiwNCiAgICAgICAgcXVlcnk6IHsgaWQ6IHZhbC5pZCwgYnVzaW5lc3NObzogdmFsLmJ1c2luZXNzTm8gfSwNCiAgICAgIH0pOw0KICAgICAgd2luZG93Lm9wZW4ocm91dGVEYXRhLmhyZWYsICJfYmxhbmsiKTsNCiAgICB9LA0KICAgIC8vIOS4k+WutuaZuuW6k+WIl+ihqA0KICAgIGdldEV4cGVydExpc3RGb3VyKCkgew0KICAgICAgZ2V0RXhwZXJ0TGlzdEZvdXIoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgbGV0IGtleSA9IENyeXB0b0pTLmVuYy5VdGY4LnBhcnNlKHNlY3JldEtleSk7DQogICAgICAgIGxldCBkZWNyeXB0ID0gQ3J5cHRvSlMuQUVTLmRlY3J5cHQocmVzLCBrZXksIHsNCiAgICAgICAgICBtb2RlOiBDcnlwdG9KUy5tb2RlLkVDQiwNCiAgICAgICAgICBwYWRkaW5nOiBDcnlwdG9KUy5wYWQuUGtjczcsDQogICAgICAgIH0pOw0KICAgICAgICByZXMgPSBKU09OLnBhcnNlKENyeXB0b0pTLmVuYy5VdGY4LnN0cmluZ2lmeShkZWNyeXB0KSk7DQogICAgICAgIGxldCB7IHJvd3MgfSA9IHJlcyB8fCBbXTsNCiAgICAgICAgdGhpcy5leHBlcnREYXRhID0gcm93cy5zbGljZSgwLCA0KTsNCiAgICAgICAgdGhpcy5leHBlcnREYXRhLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICBpdGVtLnRlY2huaXF1ZVR5cGVOYW1lID0gaXRlbS50ZWNobmlxdWVUeXBlTmFtZS5zcGxpdCgiLCIpOw0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5rS75Yqo5bm/5Zy6DQogICAgZ2V0QWN0aXZpdHlMaXN0KCkgew0KICAgICAgZ2V0QWN0aXZpdHlMaXN0KCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGxldCB7IHJvd3MgfSA9IHJlcyB8fCBbXTsNCiAgICAgICAgdGhpcy5hY3Rpdml0eURhdGEgPSByb3dzLnNsaWNlKDAsIDYpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDot7PovazliLDkuJPlrrbmmbrlupPpobXpnaINCiAgICBnb0V4cGVydExpYnJhcnkoKSB7DQogICAgICBsZXQgcm91dGVEYXRhID0gdGhpcy4kcm91dGVyLnJlc29sdmUoew0KICAgICAgICBwYXRoOiAiL2V4cGVydExpYnJhcnkiLA0KICAgICAgfSk7DQogICAgICB3aW5kb3cub3Blbihyb3V0ZURhdGEuaHJlZiwgIl9ibGFuayIpOw0KICAgIH0sDQogICAgLy8g6Lez6L2s5Yiw5LiT5a626K+m5oOF6aG16Z2iDQogICAgZ29FeHBlcnRMaWJyYXJ5RGV0YWlsKGlkKSB7DQogICAgICBsZXQgcm91dGVEYXRhID0gdGhpcy4kcm91dGVyLnJlc29sdmUoew0KICAgICAgICBwYXRoOiAiL2V4cGVydERldGFpbCIsDQogICAgICAgIHF1ZXJ5OiB7IGlkIH0sDQogICAgICB9KTsNCiAgICAgIHdpbmRvdy5vcGVuKHJvdXRlRGF0YS5ocmVmLCAiX2JsYW5rIik7DQogICAgfSwNCiAgICAvLyDot7PovazliLDmtLvliqjlub/lnLoNCiAgICBnb0FjdGl2aXR5U3F1YXJlKCkgew0KICAgICAgbGV0IHJvdXRlRGF0YSA9IHRoaXMuJHJvdXRlci5yZXNvbHZlKHsNCiAgICAgICAgcGF0aDogIi9hY3Rpdml0eVNxdWFyZSIsDQogICAgICB9KTsNCiAgICAgIHdpbmRvdy5vcGVuKHJvdXRlRGF0YS5ocmVmLCAiX2JsYW5rIik7DQogICAgfSwNCiAgICAvLyDot7PovazliLDmnIDmlrDmtLvliqjpobXpnaINCiAgICBnb0FjdGl2aXR5RGV0YWlsKGlkKSB7DQogICAgICBsZXQgcm91dGVEYXRhID0gdGhpcy4kcm91dGVyLnJlc29sdmUoew0KICAgICAgICBwYXRoOiAiL2FjdGl2aXR5RGV0YWlsIiwNCiAgICAgICAgcXVlcnk6IHsgaWQgfSwNCiAgICAgIH0pOw0KICAgICAgd2luZG93Lm9wZW4ocm91dGVEYXRhLmhyZWYsICJfYmxhbmsiKTsNCiAgICB9LA0KDQogICAgaGFuZGxlQ3VycmVudENoYW5nZShwYWdlTnVtKSB7DQogICAgICAvLyBjb25zb2xlLmxvZyhg5b2T5YmN6aG1OiAke3ZhbH1gKTsNCiAgICAgIHRoaXMucGFnZU51bSA9IHBhZ2VOdW07DQogICAgICB0aGlzLmdldERlbWFuZExpc3QoKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAohBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/purchaseSales", "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-02-01 08:50:01\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-12-11 14:05:18\r\n-->\r\n<template>\r\n  <div class=\"purchase-sales-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"purchase-banner\">\r\n      <img src=\"../../assets/purchaseSales/purchaseSalesBanner.png\" alt=\"\" />\r\n    </div>\r\n    <!-- 互采互销 -->\r\n    <div class=\"purchase-sales-content\">\r\n      <div class=\"purchase-sales-title-box\">\r\n        <div class=\"purchase-sales-divider\"></div>\r\n        <div class=\"purchase-sales-title\">互采互销</div>\r\n        <div class=\"purchase-sales-divider\"></div>\r\n      </div>\r\n      <div class=\"purchase-sales-box\">\r\n        <div class=\"purchase-sales-search-content\">\r\n          <div class=\"purchase-sales-search-top\">\r\n            <el-button\r\n              :class=\"{\r\n                'purchase-sale-top-btn': !showDemand,\r\n                'purchase-sale-top-active-btn': showDemand,\r\n              }\"\r\n              @click=\"searchContent('1')\"\r\n              >找需求<img\r\n                v-if=\"showDemand\"\r\n                src=\"../../assets/purchaseSales/arrow.png\"\r\n                alt=\"\"\r\n                class=\"search-top-img\"\r\n            /></el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              class=\"purchase-sale-bottom-btn\"\r\n              @click=\"viewMoreDemand\"\r\n              >查看全部需求 >>\r\n            </el-button>\r\n            <el-button\r\n              :class=\"{\r\n                'purchase-sale-top-btn': !showResources,\r\n                'purchase-sale-top-active-btn': showResources,\r\n              }\"\r\n              @click=\"searchContent('2')\"\r\n              >找资源<img\r\n                v-if=\"showResources\"\r\n                src=\"../../assets/purchaseSales/arrow.png\"\r\n                alt=\"\"\r\n                class=\"search-top-img\"\r\n            /></el-button>\r\n            <el-button\r\n              type=\"text\"\r\n              class=\"purchase-sale-bottom-btn\"\r\n              @click=\"viewMoreSupply\"\r\n              >查看全部资源 >>\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n        <div class=\"purchase-sales-info-content\">\r\n          <div\r\n            class=\"scene-search-box\"\r\n            @click=\"searchContent('2')\"\r\n            v-if=\"showResources\"\r\n          >\r\n            <el-form ref=\"form\" class=\"scene-search-form\" :model=\"name\">\r\n              <el-form-item>\r\n                <el-input\r\n                  v-model=\"name\"\r\n                  placeholder=\"请输入搜索内容\"\r\n                  class=\"scene-search-input\"\r\n                >\r\n                  <el-button\r\n                    slot=\"append\"\r\n                    class=\"scene-search-btn\"\r\n                    @click=\"search\"\r\n                    >找资源</el-button\r\n                  >\r\n                </el-input>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n          <div\r\n            class=\"scene-search-box\"\r\n            @click=\"searchContent('1')\"\r\n            v-if=\"showDemand\"\r\n          >\r\n            <el-form ref=\"form\" class=\"scene-search-form\" :model=\"name\">\r\n              <el-form-item>\r\n                <el-input\r\n                  v-model=\"name\"\r\n                  placeholder=\"请输入搜索内容\"\r\n                  class=\"scene-search-input\"\r\n                >\r\n                  <el-button\r\n                    slot=\"append\"\r\n                    class=\"scene-search-btn\"\r\n                    @click=\"search\"\r\n                    >找需求</el-button\r\n                  >\r\n                </el-input>\r\n              </el-form-item>\r\n            </el-form>\r\n          </div>\r\n          <el-tabs\r\n            v-show=\"showDemand\"\r\n            v-model=\"demandType\"\r\n            class=\"purchase-sales-info-tab\"\r\n            @tab-click=\"tabDemand\"\r\n          >\r\n            <el-tab-pane name=\"1\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/innovation.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">创新研发</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"2\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img src=\"../../assets/purchaseSales/material.png\" alt=\"\" />\r\n                  </div>\r\n                  <div class=\"item-title\">物料采购</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"3\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/intelligence.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">智能制造</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"4\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/digitization.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">数字化管理</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"5\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img src=\"../../assets/purchaseSales/software.png\" alt=\"\" />\r\n                  </div>\r\n                  <div class=\"item-title\">软件服务</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"6\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/supplyChain.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">供应链金融</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"7\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/operationPublicity.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">运营宣传</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"8\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img src=\"../../assets/purchaseSales/other.png\" alt=\"\" />\r\n                  </div>\r\n                  <div class=\"item-title\">其他</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n          <el-tabs\r\n            v-show=\"showResources\"\r\n            v-model=\"technologyType\"\r\n            class=\"purchase-sales-info-tab\"\r\n            @tab-click=\"tabTechnology\"\r\n          >\r\n            <el-tab-pane name=\"国产化替代\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/domesticSubstitution.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">国产化替代</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"机器替人\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/robotSubstitutes.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">机器替人</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"管理提升\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/managementPromotion.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">管理提升</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"质量提升\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/qualityImprovement.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">质量提升</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"灭菌消杀\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/sterilization.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">灭菌消杀</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"供应链金融\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/supplyChain.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">供应链金融</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"新材料\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/newMaterial.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">新材料</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane name=\"绿色星碳\">\r\n              <div slot=\"label\">\r\n                <div class=\"info-type-item\">\r\n                  <div class=\"item-img\">\r\n                    <img\r\n                      src=\"../../assets/purchaseSales/greenDoubleCarbon.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-title\">绿色星碳</div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n          <div v-loading=\"purchaseLoading\" class=\"purchase-sales-info-box\">\r\n            <div\r\n              v-for=\"(item, index) in data\"\r\n              :key=\"index\"\r\n              class=\"purchase-sales-item\"\r\n              @click=\"goMoreDetail(item.id)\"\r\n            >\r\n              <div v-if=\"showDemand\" class=\"item-img\">\r\n                <img\r\n                  v-if=\"item.scenePicture && item.scenePicture.length > 0\"\r\n                  :src=\"item.scenePicture[0].url\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../assets/purchaseSales/demandDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div v-else class=\"item-img\">\r\n                <img\r\n                  v-if=\"item.productPhoto && item.productPhoto.length > 0\"\r\n                  :src=\"item.productPhoto[0].url\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../assets/purchaseSales/resourceDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"item-content\">\r\n                <div class=\"item-content-title\">\r\n                  <span v-if=\"showDemand\">{{ item.demandTitle }}</span>\r\n                  <span v-else-if=\"showResources\">{{ item.supplyName }}</span>\r\n                </div>\r\n                <div class=\"item-content-detail\">\r\n                  <span>{{ item.companyName }}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"tab-page-end\">\r\n        <!-- <span class=\"demonstration\">完整功能</span> -->\r\n        <el-pagination\r\n          class=\"company-tab-pagination\"\r\n          @current-change=\"handleCurrentChange\"\r\n          :current-page=\"pageNum\"\r\n          :page-size=\"pageSize\"\r\n          layout=\" prev, pager, next \"\r\n          :total=\"total\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n    <!-- 推荐企业 -->\r\n    <div class=\"recommend-company\">\r\n      <div class=\"purchase-sales-title-box purchase-sales-title-style\">\r\n        <div class=\"purchase-sales-divider\"></div>\r\n        <div class=\"purchase-sales-title\">推荐企业</div>\r\n        <div class=\"purchase-sales-divider\"></div>\r\n      </div>\r\n      <div class=\"recommend-company-content\">\r\n        <div class=\"recommend-company-more\">\r\n          <el-button type=\"text\" @click=\"goMoreCompany\">查看全部>></el-button>\r\n        </div>\r\n        <div class=\"recommend-company-carousel\">\r\n          <el-carousel\r\n            class=\"recommend-company-carousel-content\"\r\n            :interval=\"5000\"\r\n          >\r\n            <el-carousel-item\r\n              v-for=\"(item, index) in companyData\"\r\n              :key=\"index\"\r\n              class=\"item-company-box\"\r\n            >\r\n              <div\r\n                v-for=\"(val, number) in item\"\r\n                :key=\"number\"\r\n                class=\"item-company-content\"\r\n              >\r\n                <div class=\"item-company-detail\">\r\n                  <div class=\"item-company-img\">\r\n                    <img\r\n                      v-if=\"\r\n                        val.companyPictureList &&\r\n                        val.companyPictureList.length > 0\r\n                      \"\r\n                      :src=\"val.companyPictureList[0].url\"\r\n                      alt=\"\"\r\n                    />\r\n                    <img\r\n                      v-else\r\n                      src=\"../../assets/purchaseSales/companyDefault.png\"\r\n                      alt=\"\"\r\n                    />\r\n                  </div>\r\n                  <div class=\"item-company-info\">\r\n                    <div class=\"item-company-title\">{{ val.name }}</div>\r\n                    <div class=\"item-company-intro\">{{ val.introduce }}</div>\r\n                    <div class=\"item-company-btn\">\r\n                      <el-button type=\"text\" @click=\"viewCompanyDetail(val)\"\r\n                        >查看详情\r\n                        <img\r\n                          src=\"../../assets/purchaseSales/arrow.png\"\r\n                          alt=\"\"\r\n                          class=\"item-company-img\"\r\n                        />\r\n                      </el-button>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-carousel-item>\r\n          </el-carousel>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 专家智库 -->\r\n    <div class=\"expert-library-container\">\r\n      <div class=\"expert-library\">\r\n        <div class=\"purchase-sales-title-box purchase-sales-title-style\">\r\n          <div class=\"purchase-sales-divider\"></div>\r\n          <div class=\"purchase-sales-title\">专家智库</div>\r\n          <div class=\"purchase-sales-divider\"></div>\r\n        </div>\r\n        <div class=\"recommend-company-more\">\r\n          <el-button type=\"text\" @click=\"goExpertLibrary\">查看全部>></el-button>\r\n        </div>\r\n        <div class=\"expert-library-content\">\r\n          <div\r\n            v-for=\"(item, index) in expertData\"\r\n            :key=\"index\"\r\n            class=\"expert-library-item\"\r\n            @click=\"goExpertLibraryDetail(item.id)\"\r\n          >\r\n            <div class=\"expert-library-list\">\r\n              <div class=\"expert-library-title\">\r\n                <div class=\"title-name\">{{ item.expertName }}</div>\r\n              </div>\r\n              <div class=\"expert-library-label\">\r\n                <div\r\n                  v-for=\"(val, index1) in item.techniqueTypeName\"\r\n                  :key=\"index1\"\r\n                  class=\"library-label-item\"\r\n                >\r\n                  <span v-if=\"index1 < 2\" class=\"library-label-type\">\r\n                    {{ `#${val}` }}\r\n                  </span>\r\n                  <span v-else>…</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"expert-library-box\">\r\n                {{ item.synopsis }}\r\n              </div>\r\n            </div>\r\n            <div class=\"expert-library-avatar\">\r\n              <img v-if=\"item.headPortrait\" :src=\"item.headPortrait\" alt=\"\" />\r\n              <img\r\n                v-else\r\n                src=\"../../assets/expertLibrary/defaultImg.png\"\r\n                alt=\"\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- 活动广场 -->\r\n    <div class=\"activity-square\">\r\n      <div class=\"purchase-sales-title-box purchase-sales-title-style\">\r\n        <div class=\"purchase-sales-divider\"></div>\r\n        <div class=\"purchase-sales-title\">活动广场</div>\r\n        <div class=\"purchase-sales-divider\"></div>\r\n      </div>\r\n      <div class=\"recommend-company-more activity-more\">\r\n        <el-button type=\"text\" @click=\"goActivitySquare\">查看全部>></el-button>\r\n      </div>\r\n      <div class=\"activity-square-content\">\r\n        <div\r\n          v-for=\"(item, index) in activityData\"\r\n          :key=\"index\"\r\n          class=\"activity-square-item\"\r\n          @click=\"goActivityDetail(item.id)\"\r\n        >\r\n          <div class=\"activity-square-img\">\r\n            <img\r\n              v-if=\"item.activityPicture\"\r\n              :src=\"item.activityPicture\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div class=\"activity-square-box\">\r\n            <div class=\"activity-square-item-title\">\r\n              {{ item.activityName }}\r\n            </div>\r\n            <div class=\"activity-square-item-info\">\r\n              {{ item.activityOverview }}\r\n            </div>\r\n            <div class=\"activity-square-item-time\">\r\n              {{ item.createTimeStr }}\r\n            </div>\r\n          </div>\r\n          <div class=\"activity-square-label\">{{ item.activityTypeName }}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  gatewayDemandListShow,\r\n  gatewaySupplyListShow,\r\n  getCompanyListLb,\r\n  getExpertListFour,\r\n  getActivityList,\r\n} from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      showDemand: true, //找需求按钮箭头是否展示\r\n      showResources: false, //找资源按钮箭头是否展示\r\n      demandBtn: false,\r\n      purchaseLoading: false,\r\n      data: [],\r\n      companyData: [], //推荐企业列表\r\n      expertData: [], //专家智库列表\r\n      activityData: [], //活动广场列表\r\n      demandType: \"1\",\r\n      technologyType: \"国产化替代\",\r\n      pageNum: 1,\r\n      pageSize: 6,\r\n      total: 0,\r\n      name: \"\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getDemandList();\r\n    this.getCompanyListLb();\r\n    this.getExpertListFour();\r\n    this.getActivityList();\r\n  },\r\n  methods: {\r\n    // 找需求、找资源按钮显示样式\r\n    searchContent(flag) {\r\n      if (flag === \"1\") {\r\n        this.showDemand = true;\r\n        this.showResources = false;\r\n        this.demandType = \"1\";\r\n        this.getDemandList();\r\n      } else {\r\n        this.showDemand = false;\r\n        this.showResources = true;\r\n        this.technologyType = \"国产化替代\";\r\n        this.getSupplyList();\r\n      }\r\n    },\r\n    // 跳转到商机需求列表\r\n    viewMoreDemand() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/demandHall\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到资源大厅\r\n    viewMoreSupply() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/resourceHall\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到需求详情/资源页面\r\n    goMoreDetail(id) {\r\n      let path = \"\";\r\n      this.showDemand\r\n        ? (path = \"/demandHallDetail\")\r\n        : (path = \"/resourceHallDetail\");\r\n      let routeData = this.$router.resolve({\r\n        path: path,\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 找需求\r\n    getDemandList() {\r\n      this.purchaseLoading = true;\r\n      gatewayDemandListShow({\r\n        demandType: this.demandType,\r\n        city: \"青岛市\",\r\n        region: \"城阳区\",\r\n        displayStatus: 1,\r\n        auditStatus: 2,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n        name: this.name,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          this.purchaseLoading = false;\r\n          let { rows } = res || [];\r\n          this.data = rows;\r\n          this.total = res.total;\r\n          this.data.forEach((item) => {\r\n            if (item.scenePicture) {\r\n              item.scenePicture = JSON.parse(item.scenePicture);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {\r\n          this.purchaseLoading = false;\r\n          this.data = [];\r\n        });\r\n    },\r\n    // 找资源\r\n    getSupplyList() {\r\n      // this.purchaseLoading = true;\r\n      gatewaySupplyListShow({\r\n        technologyType: this.technologyType,\r\n        city: \"青岛市\",\r\n        region: \"城阳区\",\r\n        auditStatus: 2,\r\n        displayStatus: 1,\r\n        pageNum: 1,\r\n        // pageSize: 6,\r\n        name: this.name,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          this.total = res.total;\r\n          this.purchaseLoading = false;\r\n          let { rows } = res || [];\r\n          this.data = rows;\r\n          this.data.forEach((item) => {\r\n            item.productPhoto = JSON.parse(item.productPhoto);\r\n          });\r\n        })\r\n        .catch(() => {\r\n          // this.purchaseLoading = false;\r\n          this.data = [];\r\n        });\r\n    },\r\n    // 找需求tab切换\r\n    tabDemand() {\r\n      this.getDemandList();\r\n    },\r\n    // 找资源tab切换\r\n    tabTechnology() {\r\n      this.getSupplyList();\r\n    },\r\n    // 推荐企业列表\r\n    getCompanyListLb() {\r\n      getCompanyListLb({ recommendStatus: 1 }).then((res) => {\r\n        let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n        let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n          mode: CryptoJS.mode.ECB,\r\n          padding: CryptoJS.pad.Pkcs7,\r\n        });\r\n        res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n        let data = res.rows || [];\r\n        this.companyData = [];\r\n        for (var i = 0; i < data.length; i += 3) {\r\n          this.companyData.push(data.slice(i, i + 3));\r\n        }\r\n      });\r\n    },\r\n    // 跳转到企业名录页面\r\n    goMoreCompany() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/enterpriseList\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到企业详情页面\r\n    viewCompanyDetail(val) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/enterpriseDetail\",\r\n        query: { id: val.id, businessNo: val.businessNo },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 专家智库列表\r\n    getExpertListFour() {\r\n      getExpertListFour().then((res) => {\r\n        let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n        let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n          mode: CryptoJS.mode.ECB,\r\n          padding: CryptoJS.pad.Pkcs7,\r\n        });\r\n        res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n        let { rows } = res || [];\r\n        this.expertData = rows.slice(0, 4);\r\n        this.expertData.forEach((item) => {\r\n          item.techniqueTypeName = item.techniqueTypeName.split(\",\");\r\n        });\r\n      });\r\n    },\r\n    // 活动广场\r\n    getActivityList() {\r\n      getActivityList().then((res) => {\r\n        let { rows } = res || [];\r\n        this.activityData = rows.slice(0, 6);\r\n      });\r\n    },\r\n    // 跳转到专家智库页面\r\n    goExpertLibrary() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/expertLibrary\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到专家详情页面\r\n    goExpertLibraryDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/expertDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到活动广场\r\n    goActivitySquare() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/activitySquare\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到最新活动页面\r\n    goActivityDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/activityDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n\r\n    handleCurrentChange(pageNum) {\r\n      // console.log(`当前页: ${val}`);\r\n      this.pageNum = pageNum;\r\n      this.getDemandList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 分页\r\n.block {\r\n  width: 600px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.purchase-sales-container {\r\n  width: 100%;\r\n  background: #fff;\r\n  .purchase-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .purchase-sales-content {\r\n    background: #f4f5f9;\r\n    .purchase-sales-box {\r\n      width: 1216px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      padding-bottom: 60px;\r\n      .purchase-sales-search-content {\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n        padding: 40px 24px 40px 0;\r\n        .purchase-sales-search-top {\r\n          display: flex;\r\n          flex-direction: column;\r\n          .purchase-sale-top-btn {\r\n            width: 172px;\r\n            height: 60px;\r\n            border-radius: 30px;\r\n            border: 1px solid #21c9b8;\r\n            font-size: 20px;\r\n            font-family: PingFangSC-Medium, PingFang SC;\r\n            font-weight: 500;\r\n            color: #21c9b8;\r\n            line-height: 20px;\r\n            margin-left: 0;\r\n            background: transparent;\r\n          }\r\n          .purchase-sale-top-active-btn {\r\n            width: 172px;\r\n            height: 60px;\r\n            border-radius: 30px;\r\n            border: 1px solid #21c9b8;\r\n            font-size: 20px;\r\n            font-family: PingFangSC-Medium, PingFang SC;\r\n            font-weight: 500;\r\n            color: #fff;\r\n            line-height: 20px;\r\n            margin-left: 0;\r\n            background: #21c9b8;\r\n          }\r\n          .search-top-img {\r\n            width: 20px;\r\n            height: 20px;\r\n            margin-left: 12px;\r\n            margin-bottom: -3px;\r\n          }\r\n        }\r\n\r\n        .purchase-sale-bottom-btn {\r\n          font-size: 14px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #21c9b8;\r\n          line-height: 18px;\r\n          margin-left: 0;\r\n          margin-bottom: 60px;\r\n\r\n          .search-bottom-img {\r\n            width: 18px;\r\n            height: 18px;\r\n            margin-bottom: -3px;\r\n          }\r\n        }\r\n      }\r\n      .purchase-sales-info-content {\r\n        flex: 1;\r\n        width: 996px;\r\n        background: #fff;\r\n        .purchase-sales-info-tab {\r\n          padding: 24px 53px 1px 61px;\r\n        }\r\n        .info-type-item {\r\n          display: flex;\r\n          flex-direction: column;\r\n          align-items: center;\r\n          width: 80px;\r\n          text-align: center;\r\n          .item-img {\r\n            width: 60px;\r\n            height: 79px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          .item-title {\r\n            font-size: 16px;\r\n            font-family: PingFangSC-Medium, PingFang SC;\r\n            font-weight: 500;\r\n            color: #333;\r\n            line-height: 16px;\r\n            padding-top: 16px;\r\n          }\r\n          &:hover {\r\n            .item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        .purchase-sales-info-box {\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          padding: 0 24px 24px;\r\n          min-height: 628px;\r\n          .purchase-sales-item {\r\n            width: 300px;\r\n            height: 278px;\r\n            background: #f8f9fb;\r\n            border-radius: 6px;\r\n            margin: 24px 24px 0 0;\r\n            cursor: pointer;\r\n            .item-img {\r\n              width: 100%;\r\n              height: 160px;\r\n              img {\r\n                width: 100%;\r\n                height: 100%;\r\n              }\r\n            }\r\n            .item-content {\r\n              padding: 16px 22px 24px 23px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              .item-content-title {\r\n                width: 255px;\r\n                height: 52px;\r\n                font-size: 18px;\r\n                font-weight: 500;\r\n                color: #333;\r\n                line-height: 26px;\r\n                overflow: hidden;\r\n                display: -webkit-box;\r\n                -webkit-box-orient: vertical;\r\n                -webkit-line-clamp: 2;\r\n                text-overflow: ellipsis;\r\n                margin-bottom: 12px;\r\n                word-wrap: break-word;\r\n              }\r\n              .item-content-detail {\r\n                width: 255px;\r\n                font-weight: 400;\r\n                color: #666;\r\n                line-height: 14px;\r\n                text-overflow: ellipsis; /*让截断的文字显示为点点。还有一个值是clip意截断不显示点点*/\r\n                white-space: nowrap; /*让文字不换行*/\r\n                overflow: hidden; /*超出要隐藏*/\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .recommend-company {\r\n    height: 881px;\r\n    background-image: url(\"../../assets/purchaseSales/recommendCompanyBanner.png\");\r\n    background-size: 100% 881px;\r\n    .recommend-company-content {\r\n      padding: 0 4.79vw;\r\n      .recommend-company-carousel {\r\n        margin-top: 32px;\r\n        .item-company-box {\r\n          display: flex;\r\n          .item-company-content {\r\n            margin-right: 2vw;\r\n            .item-company-detail {\r\n              position: relative;\r\n              .item-company-img {\r\n                width: 28.75vw;\r\n                height: 350px;\r\n                img {\r\n                  width: 100%;\r\n                  height: 100%;\r\n                }\r\n              }\r\n              .item-company-info {\r\n                position: absolute;\r\n                top: 160px;\r\n                left: 1.7vw;\r\n                width: 25.3vw;\r\n                height: 280px;\r\n                background: #fff;\r\n                box-shadow: 0px 10px 40px 0px #264a741a;\r\n                font-family: PingFangSC-Regular, PingFang SC;\r\n                .item-company-title {\r\n                  max-width: 25.2vw;\r\n                  height: 24px;\r\n                  margin: 32px 32px 24px;\r\n                  font-size: 24px;\r\n                  font-weight: 500;\r\n                  color: #333;\r\n                  line-height: 24px;\r\n                  text-overflow: ellipsis;\r\n                  white-space: nowrap;\r\n                  overflow: hidden;\r\n                  word-wrap: break-word;\r\n                }\r\n                .item-company-intro {\r\n                  width: 25.2vw;\r\n                  height: 96px;\r\n                  font-size: 16px;\r\n                  color: #666;\r\n                  line-height: 32px;\r\n                  padding: 0 32px 24px;\r\n                  overflow: hidden;\r\n                  display: -webkit-box;\r\n                  -webkit-box-orient: vertical;\r\n                  -webkit-line-clamp: 3;\r\n                  text-overflow: ellipsis;\r\n                  word-wrap: break-word;\r\n                }\r\n                .item-company-btn {\r\n                  margin: 24px 1.66vw 0 0;\r\n                  text-align: right;\r\n                  .el-button {\r\n                    width: 8.95vw;\r\n                    height: 48px;\r\n                    background: #21c9b8;\r\n                    border-radius: 30px;\r\n                    font-size: 16px;\r\n                    font-family: PingFangSC-Medium, PingFang SC;\r\n                    font-weight: 500;\r\n                    color: #fff;\r\n                    line-height: 16px;\r\n                    .item-company-img {\r\n                      width: 20px;\r\n                      height: 20px;\r\n                      margin-left: 8px;\r\n                      margin-bottom: -5px;\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .expert-library-container {\r\n    background: #f4f5f9;\r\n    .expert-library {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      padding-bottom: 100px;\r\n      .expert-library-content {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        flex-wrap: wrap;\r\n        .expert-library-item {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          width: 578px;\r\n          background: #fff;\r\n          margin-top: 34px;\r\n          padding: 28px 32px;\r\n          min-height: 224px;\r\n          .expert-library-list {\r\n            flex: 1;\r\n            .expert-library-title {\r\n              display: flex;\r\n              justify-content: space-between;\r\n              align-items: center;\r\n              .title-name {\r\n                width: 270px;\r\n                font-size: 32px;\r\n                font-family: PingFangSC-Medium, PingFang SC;\r\n                font-weight: 500;\r\n                color: #333;\r\n                line-height: 32px;\r\n                text-overflow: ellipsis;\r\n                white-space: nowrap;\r\n                overflow: hidden;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n          }\r\n          .expert-library-label {\r\n            display: flex;\r\n            flex-wrap: wrap;\r\n            margin-bottom: 16px;\r\n            .library-label-item {\r\n              max-width: 350px;\r\n              padding: 6px 12px;\r\n              background: #f4f5f9;\r\n              border-radius: 4px;\r\n              font-size: 12px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #666;\r\n              line-height: 12px;\r\n              margin: 24px 16px 0 0;\r\n              .library-label-type {\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n          }\r\n          .expert-library-box {\r\n            width: 370px;\r\n            font-size: 16px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            color: #666;\r\n            line-height: 32px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            text-overflow: ellipsis;\r\n            word-wrap: break-word;\r\n          }\r\n          .expert-library-avatar {\r\n            width: 120px;\r\n            height: 168px;\r\n            margin-left: 24px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          &:hover {\r\n            cursor: pointer;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-square {\r\n    width: 1200px;\r\n    margin: 0 auto;\r\n    padding-bottom: 100px;\r\n    .activity-more {\r\n      margin-bottom: 32px;\r\n    }\r\n    .activity-square-content {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      .activity-square-item {\r\n        position: relative;\r\n        width: 382px;\r\n        background: #fff;\r\n        margin: 24px 14px 0 0;\r\n        box-shadow: 0px 10px 40px 0px #264a741a;\r\n        .activity-square-img {\r\n          width: 100%;\r\n          height: 160px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .activity-square-box {\r\n          padding: 16px 32px 24px 24px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          .activity-square-item-title {\r\n            width: 326px;\r\n            height: 52px;\r\n            font-size: 18px;\r\n            font-weight: 500;\r\n            color: #333;\r\n            line-height: 26px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            text-overflow: ellipsis;\r\n            word-wrap: break-word;\r\n          }\r\n          .activity-square-item-info {\r\n            width: 334px;\r\n            height: 44px;\r\n            color: #666;\r\n            line-height: 22px;\r\n            overflow: hidden;\r\n            display: -webkit-box;\r\n            -webkit-box-orient: vertical;\r\n            -webkit-line-clamp: 2;\r\n            text-overflow: ellipsis;\r\n            margin: 10px 0;\r\n            word-wrap: break-word;\r\n          }\r\n          .activity-square-item-time {\r\n            color: #666;\r\n            line-height: 22px;\r\n          }\r\n        }\r\n        .activity-square-label {\r\n          position: absolute;\r\n          top: 0;\r\n          left: 0;\r\n          padding: 0 4px;\r\n          max-width: 382px;\r\n          max-height: 160px;\r\n          background: rgba(0, 0, 0, 0.5);\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #fff;\r\n          line-height: 26px;\r\n          text-align: center;\r\n          word-wrap: break-word;\r\n        }\r\n        &:hover {\r\n          cursor: pointer;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .purchase-sales-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0;\r\n    .purchase-sales-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .purchase-sales-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .purchase-sales-title-style {\r\n    padding-top: 80px;\r\n  }\r\n  .recommend-company-more {\r\n    text-align: right;\r\n    .el-button {\r\n      font-size: 18px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #21c9b8;\r\n      line-height: 18px;\r\n      padding: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.scene-search-box {\r\n  margin-top: 60px;\r\n  .scene-search-form {\r\n    text-align: center;\r\n    .scene-search-input {\r\n      width: 892px;\r\n      height: 54px;\r\n      .scene-search-btn {\r\n        width: 100px;\r\n      }\r\n    }\r\n  }\r\n}\r\n.purchase-sales-container {\r\n  .purchase-sales-info-tab {\r\n    .el-tabs__nav {\r\n      height: 114px;\r\n      width: 100%;\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n    .el-tabs__nav-wrap::after {\r\n      background-color: transparent;\r\n    }\r\n    .el-tabs__active-bar {\r\n      background-color: transparent;\r\n    }\r\n    .el-tabs__item.is-active {\r\n      .item-title {\r\n        color: #21c9b8 !important;\r\n      }\r\n    }\r\n  }\r\n  .recommend-company {\r\n    .recommend-company-carousel-content {\r\n      height: 546px;\r\n      .el-carousel__container {\r\n        height: 100%;\r\n      }\r\n      .el-carousel__indicator {\r\n        cursor: pointer;\r\n        &.is-active {\r\n          .el-carousel__button {\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n      }\r\n      .el-carousel__button {\r\n        width: 3.125vw;\r\n        height: 6px;\r\n        background: #c1c1c180;\r\n        & + .el-carousel__button {\r\n          margin-left: 16px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .tab-page-end {\r\n    .company-tab-pagination {\r\n      width: 200px;\r\n      margin: 0 auto;\r\n      padding-bottom: 20px;\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #ffffff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        margin: 0 6px;\r\n        color: #333;\r\n      }\r\n      .el-pager {\r\n        .number {\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 1px solid #d9d9d9;\r\n          background: #ffffff;\r\n          border-radius: 4px;\r\n          line-height: 32px;\r\n          margin: 0 6px;\r\n          &.active {\r\n            background: #21c9b8;\r\n            border: 1px solid #21c9b8;\r\n            color: #fff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.scene-search-input {\r\n  .el-input__inner {\r\n    height: 54px;\r\n    background: #fff;\r\n    border-radius: 27px 0 0 27px;\r\n    border: 1px solid #d9d9d9;\r\n    font-size: 16px;\r\n    line-height: 24px;\r\n    padding-left: 30px;\r\n  }\r\n  .el-input-group__append {\r\n    border-radius: 0px 100px 100px 0px;\r\n    background: #21c9b8;\r\n    font-size: 16px;\r\n    color: #fff;\r\n    line-height: 24px;\r\n  }\r\n}\r\n.scene-search-line {\r\n  .el-form-item__label {\r\n    width: 88px;\r\n    font-weight: 500;\r\n    color: #999;\r\n    padding-right: 32px;\r\n    text-align: left;\r\n  }\r\n  .scene-search-radio {\r\n    width: 1050px;\r\n    margin-top: 11px;\r\n    .el-radio-button {\r\n      padding-bottom: 20px;\r\n      .el-radio-button__inner {\r\n        border: none;\r\n        padding: 0 32px 0 0;\r\n        background: none;\r\n        &:hover {\r\n          color: #21c9b8;\r\n        }\r\n      }\r\n      &.is-active {\r\n        .el-radio-button__inner {\r\n          color: #21c9b8;\r\n          background: none;\r\n        }\r\n      }\r\n      .el-radio-button__orig-radio:checked {\r\n        & + .el-radio-button__inner {\r\n          box-shadow: unset;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n.scene-page-end {\r\n  .scene-pagination {\r\n    .btn-prev,\r\n    .btn-next,\r\n    .btn-quickprev {\r\n      width: 32px;\r\n      height: 32px;\r\n      background: #fff;\r\n      border: 1px solid #d9d9d9;\r\n      border-radius: 4px;\r\n      color: #333;\r\n    }\r\n    &.is-background {\r\n      .el-pager {\r\n        .number {\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 1px solid #d9d9d9;\r\n          background: #fff;\r\n          border-radius: 4px;\r\n          line-height: 32px;\r\n          &.active {\r\n            background: #21c9b8;\r\n            border: 1px solid #21c9b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}