<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.LaboratoryInfoMapper">
    
    <resultMap type="LaboratoryInfo" id="LaboratoryInfoResult">
        <result property="id"    column="id"    />
        <result property="labName"    column="lab_name"    />
        <result property="labType"    column="lab_type"    />
        <result property="labAddress"    column="lab_address"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="labIntroduction"    column="lab_introduction"    />
        <result property="labImages"    column="lab_images"    />
        <result property="testingScope"    column="testing_scope"    />
        <result property="qualifications"    column="qualifications"    />
        <result property="cnasQualification"    column="cnas_qualification"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectLaboratoryInfoVo">
        select id, lab_name, lab_type, lab_address, contact_phone, lab_introduction, lab_images, testing_scope, qualifications, cnas_qualification, status, create_time, update_time from laboratory_info
    </sql>

    <select id="selectLaboratoryInfoList" parameterType="LaboratoryInfo" resultMap="LaboratoryInfoResult">
        <include refid="selectLaboratoryInfoVo"/>
        <where>  
            <if test="labName != null  and labName != ''"> and lab_name like concat('%', #{labName}, '%')</if>
            <if test="labType != null  and labType != ''"> and lab_type = #{labType}</if>
            <if test="labAddress != null  and labAddress != ''"> and lab_address = #{labAddress}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="labIntroduction != null  and labIntroduction != ''"> and lab_introduction = #{labIntroduction}</if>
            <if test="labImages != null  and labImages != ''"> and lab_images = #{labImages}</if>
            <if test="testingScope != null  and testingScope != ''"> and testing_scope like concat('%', #{testingScope}, '%')</if>
            <if test="qualifications != null  and qualifications != ''"> and qualifications = #{qualifications}</if>
            <if test="cnasQualification != null  and cnasQualification != ''"> and cnas_qualification = #{cnasQualification}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectLaboratoryInfoById" parameterType="Long" resultMap="LaboratoryInfoResult">
        <include refid="selectLaboratoryInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertLaboratoryInfo" parameterType="LaboratoryInfo" useGeneratedKeys="true" keyProperty="id">
        insert into laboratory_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="labName != null">lab_name,</if>
            <if test="labType != null">lab_type,</if>
            <if test="labAddress != null">lab_address,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="labIntroduction != null">lab_introduction,</if>
            <if test="labImages != null">lab_images,</if>
            <if test="testingScope != null">testing_scope,</if>
            <if test="qualifications != null">qualifications,</if>
            <if test="cnasQualification != null">cnas_qualification,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="labName != null">#{labName},</if>
            <if test="labType != null">#{labType},</if>
            <if test="labAddress != null">#{labAddress},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="labIntroduction != null">#{labIntroduction},</if>
            <if test="labImages != null">#{labImages},</if>
            <if test="testingScope != null">#{testingScope},</if>
            <if test="qualifications != null">#{qualifications},</if>
            <if test="cnasQualification != null">#{cnasQualification},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateLaboratoryInfo" parameterType="LaboratoryInfo">
        update laboratory_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="labName != null">lab_name = #{labName},</if>
            <if test="labType != null">lab_type = #{labType},</if>
            <if test="labAddress != null">lab_address = #{labAddress},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="labIntroduction != null">lab_introduction = #{labIntroduction},</if>
            <if test="labImages != null">lab_images = #{labImages},</if>
            <if test="testingScope != null">testing_scope = #{testingScope},</if>
            <if test="qualifications != null">qualifications = #{qualifications},</if>
            <if test="cnasQualification != null">cnas_qualification = #{cnasQualification},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLaboratoryInfoById" parameterType="Long">
        delete from laboratory_info where id = #{id}
    </delete>

    <delete id="deleteLaboratoryInfoByIds" parameterType="String">
        delete from laboratory_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>