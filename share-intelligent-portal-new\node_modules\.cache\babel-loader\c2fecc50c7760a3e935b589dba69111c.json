{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\appliMarket\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\appliMarket\\index.vue", "mtime": 1750311962921}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_appliMarket", "require", "_default", "exports", "default", "data", "fit", "loading", "form", "keywords", "formInfo", "caseType", "caseTypeList", "pageNum", "pageSize", "total", "flag", "appliTypeData", "dict<PERSON><PERSON>ue", "dict<PERSON><PERSON>l", "appliTypeImgList", "url", "appliDataList", "id", "title", "desc", "tag", "created", "initData", "methods", "_this", "appliType", "then", "res", "code", "console", "log", "getListData", "_this2", "params", "appName", "appCategory", "undefined", "appState", "appliList", "rows", "changeRadio", "onSearch", "handleSizeChange", "handleCurrentChange", "goCaseDetail", "routeData", "$router", "resolve", "path", "query", "window", "open", "href", "goHome", "push", "getappliData", "value", "goPur<PERSON>apps"], "sources": ["src/views/appliMarket/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/appliMarket/appliMarketBanner.png\" alt=\"\" />\r\n      <div class=\"bannerTitle\">应用市场</div>\r\n      <div class=\"bannerDesc\">\r\n        助力企业数字化低碳转型升级，提供成熟完善的数字化应用产品\r\n      </div>\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.keywords\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"appliType\">\r\n        <div\r\n          class=\"everyType\"\r\n          v-for=\"(item, index) in appliTypeData\"\r\n          :key=\"item.dictValue\"\r\n          @click=\"getappliData(item.dictLabel)\"\r\n        >\r\n          <div class=\"everyImg\">\r\n            <img :src=\"appliTypeImgList[index].url\" alt=\"\" />\r\n          </div>\r\n          <div class=\"everyTitle\">{{ item.dictLabel }}</div>\r\n          <div class=\"everyIcon\" v-show=\"flag === item.dictLabel\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"appliContent\">\r\n        <div style=\"display: flex; flex-wrap: wrap\">\r\n          <div\r\n            v-loading=\"loading\"\r\n            class=\"everyContent\"\r\n            v-for=\"item in appliDataList\"\r\n            :key=\"item.id\"\r\n            @click=\"goPurchaseapps(item.id)\"\r\n          >\r\n            <div class=\"title\">{{ item.appName }}</div>\r\n            <div class=\"desc\">\r\n              {{ item.briefInto }}\r\n            </div>\r\n            <div\r\n              class=\"tagStyle\"\r\n              v-if=\"item.appLabel && item.appLabel.length > 0\"\r\n            >\r\n              <div\r\n                class=\"everyTag\"\r\n                v-for=\"itemTag in item.appLabel.split(',')\"\r\n                :key=\"itemTag\"\r\n              >\r\n                {{ itemTag }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"activity-page-end\">\r\n          <el-button class=\"activity-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"appliDataList && appliDataList.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { appliType, appliList } from \"@/api/appliMarket\";\r\n// import { getDicts } from \"@/api/system/dict/data\";\r\n// import { caseList } from \"@/api/classicCase\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      loading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        caseType: \"\", // 案例类型\r\n      },\r\n      caseTypeList: [],\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 12,\r\n      total: 0,\r\n      flag: \"全部\",\r\n      appliTypeData: [\r\n        {\r\n          dictValue: \"0\",\r\n          dictLabel: \"全部\",\r\n        },\r\n        {\r\n          dictValue: \"1\",\r\n          dictLabel: \"研发设计\",\r\n        },\r\n        {\r\n          dictValue: \"2\",\r\n          dictLabel: \"生产制造\",\r\n        },\r\n        {\r\n          dictValue: \"3\",\r\n          dictLabel: \"运营管理\",\r\n        },\r\n        {\r\n          dictValue: \"4\",\r\n          dictLabel: \"质量管控\",\r\n        },\r\n        {\r\n          dictValue: \"5\",\r\n          dictLabel: \"仓储物流\",\r\n        },\r\n        {\r\n          dictValue: \"6\",\r\n          dictLabel: \"安全生产\",\r\n        },\r\n        {\r\n          dictValue: \"7\",\r\n          dictLabel: \"节能减排\",\r\n        },\r\n        {\r\n          dictValue: \"8\",\r\n          dictLabel: \"运维服务\",\r\n        },\r\n      ],\r\n      appliTypeImgList: [\r\n        {\r\n          url: require(\"../../assets/appliMarket/type1.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type2.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type3.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type4.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type5.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type6.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type7.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type8.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type9.png\"),\r\n        },\r\n      ],\r\n      appliDataList: [\r\n        {\r\n          id: 1,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 5,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 6,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 7,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 8,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      appliType().then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res.data, \"------------\");\r\n          this.getListData();\r\n        }\r\n      });\r\n    },\r\n    getListData() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        appName: this.form.keywords,\r\n        appCategory: this.flag == \"全部\" ? undefined : this.flag,\r\n        appState: 2,\r\n      };\r\n      appliList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res, \"7777777777777777777\");\r\n          this.appliDataList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getListData();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.getListData();\r\n    },\r\n    goCaseDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/caseDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    getappliData(value) {\r\n      this.flag = value;\r\n      this.getListData();\r\n    },\r\n    goPurchaseapps(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/purchaseapp\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #ffffff;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 500px;\r\n    position: relative;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    .bannerTitle {\r\n      position: absolute;\r\n      top: 161px;\r\n      left: 24%;\r\n      font-size: 50px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n    }\r\n    .bannerDesc {\r\n      position: absolute;\r\n      top: 249px;\r\n      left: 24%;\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    // padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      margin-top: 40px;\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .appliType {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    .everyType {\r\n      width: 102px;\r\n      // height: 160px;\r\n      text-align: center;\r\n      cursor: pointer;\r\n      .everyImg {\r\n        width: 63px;\r\n        height: 78px;\r\n        margin-left: calc((100% - 63px) / 2);\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .everyTitle {\r\n        font-size: 18px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #979797;\r\n        margin-top: 10px;\r\n      }\r\n      .everyIcon {\r\n        width: 63px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n        margin-top: 10px;\r\n        margin-left: calc((100% - 63px) / 2);\r\n      }\r\n    }\r\n  }\r\n  .appliContent {\r\n    width: 1200px;\r\n    // height: 500px;\r\n    margin: 0 auto;\r\n    .everyContent {\r\n      width: 280px;\r\n      height: 220px;\r\n      background: #ffffff;\r\n      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n      border-radius: 4px;\r\n      padding: 30px;\r\n      cursor: pointer;\r\n      margin-left: 24px;\r\n      margin-top: 20px;\r\n      .title {\r\n        font-size: 20px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #222222;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 1;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n      .desc {\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #65676a;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n        margin-top: 26px;\r\n      }\r\n      .tagStyle {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-top: 30px;\r\n        .everyTag {\r\n          width: 90px;\r\n          height: 42px;\r\n          border: 1px solid #21c9b8;\r\n          border-radius: 21px;\r\n          font-size: 16px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #21c9b8;\r\n          text-align: center;\r\n          line-height: 42px;\r\n          overflow: hidden;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n          -webkit-line-clamp: 1;\r\n          text-overflow: ellipsis;\r\n          word-wrap: break-word;\r\n        }\r\n      }\r\n    }\r\n    .everyContent:hover {\r\n      box-shadow: 0 4px 16px 0 rgba(38, 74, 116, 0.2);\r\n      .title {\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    .everyContent:nth-child(4n + 1) {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    padding: 8% 0;\r\n    background: #fff;\r\n    margin-top: 25px;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    padding: 24px 0 60px;\r\n    .activity-page-btn {\r\n      width: 82px;\r\n      height: 32px;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      border: 1px solid #d9d9d9;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #333;\r\n      line-height: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AA2FA,IAAAA,YAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AAAA,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;MACAC,OAAA;MACAC,IAAA;QACAC,QAAA;MACA;MACAC,QAAA;QACAC,QAAA;MACA;MACAC,YAAA;MACAP,IAAA;MACAQ,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,IAAA;MACAC,aAAA,GACA;QACAC,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,GACA;QACAD,SAAA;QACAC,SAAA;MACA,EACA;MACAC,gBAAA,GACA;QACAC,GAAA,EAAApB,OAAA;MACA,GACA;QACAoB,GAAA,EAAApB,OAAA;MACA,GACA;QACAoB,GAAA,EAAApB,OAAA;MACA,GACA;QACAoB,GAAA,EAAApB,OAAA;MACA,GACA;QACAoB,GAAA,EAAApB,OAAA;MACA,GACA;QACAoB,GAAA,EAAApB,OAAA;MACA,GACA;QACAoB,GAAA,EAAApB,OAAA;MACA,GACA;QACAoB,GAAA,EAAApB,OAAA;MACA,GACA;QACAoB,GAAA,EAAApB,OAAA;MACA,EACA;MACAqB,aAAA,GACA;QACAC,EAAA;QACAC,KAAA;QACAC,IAAA;QACAC,GAAA;MACA,GACA;QACAH,EAAA;QACAC,KAAA;QACAC,IAAA;QACAC,GAAA;MACA,GACA;QACAH,EAAA;QACAC,KAAA;QACAC,IAAA;QACAC,GAAA;MACA,GACA;QACAH,EAAA;QACAC,KAAA;QACAC,IAAA;QACAC,GAAA;MACA,GACA;QACAH,EAAA;QACAC,KAAA;QACAC,IAAA;QACAC,GAAA;MACA,GACA;QACAH,EAAA;QACAC,KAAA;QACAC,IAAA;QACAC,GAAA;MACA,GACA;QACAH,EAAA;QACAC,KAAA;QACAC,IAAA;QACAC,GAAA;MACA,GACA;QACAH,EAAA;QACAC,KAAA;QACAC,IAAA;QACAC,GAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;EACA;EACAC,OAAA;IACAD,QAAA,WAAAA,SAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,sBAAA,IAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAC,OAAA,CAAAC,GAAA,CAAAH,GAAA,CAAA5B,IAAA;UACAyB,KAAA,CAAAO,WAAA;QACA;MACA;IACA;IACAA,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAA/B,OAAA;MACA,IAAAgC,MAAA;QACA1B,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA,QAAA;QACA0B,OAAA,OAAAhC,IAAA,CAAAC,QAAA;QACAgC,WAAA,OAAAzB,IAAA,WAAA0B,SAAA,QAAA1B,IAAA;QACA2B,QAAA;MACA;MACA,IAAAC,sBAAA,EAAAL,MAAA,EAAAP,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAC,OAAA,CAAAC,GAAA,CAAAH,GAAA;UACAK,MAAA,CAAAhB,aAAA,GAAAW,GAAA,CAAAY,IAAA;UACAP,MAAA,CAAAvB,KAAA,GAAAkB,GAAA,CAAAlB,KAAA;UACAuB,MAAA,CAAA/B,OAAA;QACA;MACA;IACA;IACAuC,WAAA,WAAAA,YAAA;MACA,KAAAC,QAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAlC,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAiC,QAAA;IACA;IACAE,mBAAA,WAAAA,oBAAApC,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAwB,WAAA;IACA;IACAU,QAAA,WAAAA,SAAA;MACA,KAAAlC,OAAA;MACA,KAAAwB,WAAA;IACA;IACAa,YAAA,WAAAA,aAAA3B,EAAA;MACA,IAAA4B,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACAC,KAAA;UAAAhC,EAAA,EAAAA;QAAA;MACA;MACAiC,MAAA,CAAAC,IAAA,CAAAN,SAAA,CAAAO,IAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAP,OAAA,CAAAQ,IAAA;QAAAN,IAAA;MAAA;IACA;IACAO,YAAA,WAAAA,aAAAC,KAAA;MACA,KAAA9C,IAAA,GAAA8C,KAAA;MACA,KAAAzB,WAAA;IACA;IACA0B,cAAA,WAAAA,eAAAxC,EAAA;MACA,IAAA4B,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACAC,KAAA;UAAAhC,EAAA,EAAAA;QAAA;MACA;MACAiC,MAAA,CAAAC,IAAA,CAAAN,SAAA,CAAAO,IAAA;IACA;EACA;AACA", "ignoreList": []}]}