{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\notice\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\notice\\detail\\index.vue", "mtime": 1750311963066}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_info", "name", "components", "UserMenu", "data", "form", "created", "getDetail", "methods", "_this", "userId", "$route", "query", "id", "getInfoDetail", "then", "response", "total", "goBack", "$router", "go", "deleteInfo", "row", "_this2", "$confirm", "type", "_", "ids", "code", "catch"], "sources": ["src/views/system/user/notice/detail/index.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-21 14:03:04\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"notice-record-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <el-button icon=\"el-icon-arrow-left\" @click=\"goBack\"></el-button>\r\n              <div class=\"header-text\">消息详情</div>\r\n            </div>\r\n            <el-form ref=\"form\" :model=\"form\" label-width=\"120px\">\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"资源类型:\" prop=\"policyTitle\">\r\n                  {{ form.resourceTypeName || \"--\" }}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"资源标题:\" prop=\"publishCompany\">\r\n                  {{ form.resourceTitle || \"--\" }}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"资源描述:\" prop=\"policyType\">\r\n                  {{ form.resourceDescribe || \"--\" }}</el-form-item\r\n                >\r\n              </el-col>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"联系人:\" prop=\"policyType\">\r\n                  {{ form.contactPhone || \"--\" }}</el-form-item\r\n                >\r\n              </el-col>\r\n            </el-form>\r\n            <div class=\"delete-btn\">\r\n              <el-button type=\"danger\" @click=\"deleteInfo\">删除</el-button>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport { deleteInfo, getInfoDetail } from \"@/api/system/info\";\r\nexport default {\r\n  name: \"Notice\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      form: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    getDetail() {\r\n      let userId = this.$route.query.id;\r\n      getInfoDetail(userId).then((response) => {\r\n        this.form = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    deleteInfo(row) {\r\n      this.$confirm(\"是否确认删除该消息？\", { type: \"error\" })\r\n        .then((_) => {\r\n          deleteInfo({ ids: this.$route.query.id }).then((response) => {\r\n            if (response.code == 200) {\r\n              this.$router.go(-1);\r\n            }\r\n          });\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .notice-record-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      height: 500px;\r\n      padding-top: 12px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        display: flex;\r\n        margin-bottom: 30px;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAqDA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAE,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAD,SAAA,WAAAA,UAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,MAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,EAAA;MACA,IAAAC,mBAAA,EAAAJ,MAAA,EAAAK,IAAA,WAAAC,QAAA;QACAP,KAAA,CAAAJ,IAAA,GAAAW,QAAA,CAAAZ,IAAA;QACAK,KAAA,CAAAQ,KAAA,GAAAD,QAAA,CAAAC,KAAA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QAAAC,IAAA;MAAA,GACAV,IAAA,WAAAW,CAAA;QACA,IAAAL,gBAAA;UAAAM,GAAA,EAAAJ,MAAA,CAAAZ,MAAA,CAAAC,KAAA,CAAAC;QAAA,GAAAE,IAAA,WAAAC,QAAA;UACA,IAAAA,QAAA,CAAAY,IAAA;YACAL,MAAA,CAAAJ,OAAA,CAAAC,EAAA;UACA;QACA;MACA,GACAS,KAAA,WAAAH,CAAA;IACA;EACA;AACA", "ignoreList": []}]}