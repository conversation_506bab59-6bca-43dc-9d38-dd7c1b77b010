package com.ruoyi.im.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Author:micah
 **/

@TableName("im_notice")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImNotice extends Model<ImNotice>{

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;//主键

	private String title;//消息

	private String create_by;//创建人

	private Date create_time;//创建时间


}
