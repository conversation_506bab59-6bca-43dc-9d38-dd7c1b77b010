package com.ruoyi.portalweb.api.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 政策辅助申报对象 policy_submit_consult
 * 
 * <AUTHOR>
 * @date 2024-07-08
 */
public class PolicySubmitConsult extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long policySubmitConsultId;

    /** 政策标题 */
    @Excel(name = "政策标题")
    private String policySubmitTitle;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contact;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    public void setPolicySubmitConsultId(Long policySubmitConsultId) 
    {
        this.policySubmitConsultId = policySubmitConsultId;
    }

    public Long getPolicySubmitConsultId() 
    {
        return policySubmitConsultId;
    }
    public void setPolicySubmitTitle(String policySubmitTitle) 
    {
        this.policySubmitTitle = policySubmitTitle;
    }

    public String getPolicySubmitTitle() 
    {
        return policySubmitTitle;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setContact(String contact) 
    {
        this.contact = contact;
    }

    public String getContact() 
    {
        return contact;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("policySubmitConsultId", getPolicySubmitConsultId())
            .append("policySubmitTitle", getPolicySubmitTitle())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("content", getContent())
            .append("contact", getContact())
            .append("phone", getPhone())
            .toString();
    }
}
