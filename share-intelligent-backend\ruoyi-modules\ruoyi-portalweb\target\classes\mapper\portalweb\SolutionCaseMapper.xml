<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.SolutionCaseMapper">

    <resultMap type="SolutionCase" id="SolutionCaseResult">
        <result property="solutionCaseId" column="solution_case_id"/>
        <result property="solutionId" column="solution_id"/>
        <result property="solutionCaseName" column="solution_case_name"/>
        <result property="solutionCaseIntroduction" column="solution_case_introduction"/>
        <result property="solutionCaseContent" column="solution_case_content"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectSolutionCaseVo">
        select solution_case_id, solution_id, solution_case_name, solution_case_introduction, solution_case_content,
        del_flag, create_by, create_time, update_by, update_time, remark from solution_case
    </sql>

    <select id="selectSolutionCaseList" parameterType="SolutionCase" resultMap="SolutionCaseResult">
        <include refid="selectSolutionCaseVo"/>
        <where>
            <if test="solutionId != null ">and solution_id = #{solutionId}</if>
            <if test="solutionCaseName != null  and solutionCaseName != ''">and solution_case_name like concat('%',
                #{solutionCaseName}, '%')
            </if>
            <if test="solutionCaseIntroduction != null  and solutionCaseIntroduction != ''">and
                solution_case_introduction = #{solutionCaseIntroduction}
            </if>
            <if test="solutionCaseContent != null  and solutionCaseContent != ''">and solution_case_content =
                #{solutionCaseContent}
            </if>
        </where>
    </select>

    <select id="selectListBySolutionId" resultMap="SolutionCaseResult">
        SELECT * FROM solution_case
        WHERE solution_id = #{solutionId}
    </select>

    <select id="selectSolutionCaseBySolutionCaseId" parameterType="Long" resultMap="SolutionCaseResult">
        <include refid="selectSolutionCaseVo"/>
        where solution_case_id = #{solutionCaseId}
    </select>
    <select id="selectSolutionCaseListBySolutionIds" resultMap="SolutionCaseResult" parameterType="List">
        <include refid="selectSolutionCaseVo"/>
        WHERE solution_id IN
        <foreach collection="list" open="(" close=")" separator="," item="solutionId" >
            #{solutionId}
        </foreach>
    </select>

    <insert id="insertSolutionCase" parameterType="SolutionCase" useGeneratedKeys="true" keyProperty="solutionCaseId">
        insert into solution_case
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="solutionId != null">solution_id,</if>
            <if test="solutionCaseName != null">solution_case_name,</if>
            <if test="solutionCaseIntroduction != null">solution_case_introduction,</if>
            <if test="solutionCaseContent != null">solution_case_content,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="solutionId != null">#{solutionId},</if>
            <if test="solutionCaseName != null">#{solutionCaseName},</if>
            <if test="solutionCaseIntroduction != null">#{solutionCaseIntroduction},</if>
            <if test="solutionCaseContent != null">#{solutionCaseContent},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>
    <insert id="insertSolutionCaseList"
            parameterType="java.util.List">
        insert into solution_case (solution_id, solution_case_name, solution_case_introduction, solution_case_content,
        create_by, update_by, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.solutionId},#{item.solutionCaseName},#{item.solutionCaseIntroduction},#{item.solutionCaseContent},#{item.createBy},#{item.updateBy},#{item.remark})
        </foreach>

    </insert>

    <update id="updateSolutionCase" parameterType="SolutionCase">
        update solution_case
        <trim prefix="SET" suffixOverrides=",">
            <if test="solutionId != null">solution_id = #{solutionId},</if>
            <if test="solutionCaseName != null">solution_case_name = #{solutionCaseName},</if>
            <if test="solutionCaseIntroduction != null">solution_case_introduction = #{solutionCaseIntroduction},</if>
            <if test="solutionCaseContent != null">solution_case_content = #{solutionCaseContent},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where solution_case_id = #{solutionCaseId}
    </update>

    <delete id="deleteSolutionCaseBySolutionCaseId" parameterType="Long">
        delete from solution_case where solution_case_id = #{solutionCaseId}
    </delete>

    <delete id="deleteSolutionCaseBySolutionCaseIds" parameterType="String">
        delete from solution_case where solution_case_id in
        <foreach item="solutionCaseId" collection="array" open="(" separator="," close=")">
            #{solutionCaseId}
        </foreach>
    </delete>
    <delete id="deleteSolutionCaseBySolutionId" parameterType="java.lang.Long">
        delete from solution_case where solution_id = #{solutionId}
    </delete>
    <delete id="deleteSolutionCaseBySolutionIds">
        delete from solution_case where solution_id in
        <foreach collection="array" item="solutionId" open="(" separator="," close=")">
            #{solutionId}
        </foreach>
    </delete>
</mapper>