<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSO统一登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 400px;
            max-width: 90%;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
        
        .success-message {
            color: #27ae60;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
        
        .client-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .client-info h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .client-info p {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🔐 SSO统一登录</h1>
            <p>请使用您的账号登录</p>
        </div>
        
        <div id="clientInfo" class="client-info" style="display: none;">
            <h3 id="clientName">正在登录到系统</h3>
            <p id="clientDesc">请输入您的用户名和密码</p>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required placeholder="请输入用户名">
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required placeholder="请输入密码">
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                登录
            </button>
            
            <div id="message"></div>
        </form>
    </div>

    <script>
        // 获取URL参数
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                client_id: params.get('client_id'),
                redirect_uri: params.get('redirect_uri'),
                state: params.get('state')
            };
        }

        // 显示客户端信息
        function showClientInfo(clientId) {
            const clientInfo = document.getElementById('clientInfo');
            const clientName = document.getElementById('clientName');
            const clientDesc = document.getElementById('clientDesc');
            
            const clients = {
                'backend': {
                    name: '复合材料共享智造平台',
                    desc: '正在登录到主系统'
                },
                'market': {
                    name: '智能市场系统',
                    desc: '正在登录到从系统'
                }
            };
            
            const client = clients[clientId] || {
                name: '未知系统',
                desc: '正在登录到系统'
            };
            
            clientName.textContent = client.name;
            clientDesc.textContent = client.desc;
            clientInfo.style.display = 'block';
        }

        // 显示消息
        function showMessage(message, type = 'error') {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = message;
            messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
        }

        // 登录处理
        async function handleLogin(event) {
            event.preventDefault();
            
            const loginBtn = document.getElementById('loginBtn');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const params = getUrlParams();
            
            if (!username || !password) {
                showMessage('请输入用户名和密码');
                return;
            }
            
            if (!params.client_id) {
                showMessage('缺少客户端ID参数');
                return;
            }
            
            // 禁用登录按钮
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                const response = await fetch('/sso/authenticate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        clientId: params.client_id,
                        redirectUri: params.redirect_uri,
                        state: params.state
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 200 && result.data && result.data.success) {
                    showMessage('登录成功，正在跳转...', 'success');
                    
                    // 跳转到回调地址
                    if (result.data.redirectUrl) {
                        window.location.href = result.data.redirectUrl;
                    } else {
                        showMessage('登录成功，但缺少回调地址');
                    }
                } else {
                    showMessage(result.msg || '登录失败');
                }
                
            } catch (error) {
                console.error('登录请求失败:', error);
                showMessage('网络错误，请稍后重试');
            } finally {
                // 恢复登录按钮
                loginBtn.disabled = false;
                loginBtn.textContent = '登录';
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            const params = getUrlParams();
            
            // 显示客户端信息
            if (params.client_id) {
                showClientInfo(params.client_id);
            }
            
            // 绑定登录表单
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
            
            // 自动聚焦到用户名输入框
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
