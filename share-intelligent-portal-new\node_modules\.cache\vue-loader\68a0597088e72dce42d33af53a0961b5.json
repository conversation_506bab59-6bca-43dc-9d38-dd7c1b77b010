{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\appStore\\info.vue?vue&type=style&index=0&id=a2ba981a&lang=css", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\appStore\\info.vue", "mtime": 1750311962920}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouaW5mby10b3Agew0KICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgnLi4vLi4vYXNzZXRzL2FwcFN0b3JlL2FwcGJhbm5lci5wbmcnKTsNCiAgICBoZWlnaHQ6IDM2MHB4Ow0KICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJTsNCn0NCg0KLmluZm8tdG9wIHNwYW4gew0KICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgdmVydGljYWwtYWxpZ246IHRvcDsNCn0NCg0KLmluZm8tdG9wIC5yaWdodC1pbmZvIHsNCiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgdmVydGljYWwtYWxpZ246IHRvcDsNCiAgICB3aWR0aDogMzMxcHg7DQogICAgaGVpZ2h0OiAzMjZweDsNCiAgICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoJy4uLy4uL2Fzc2V0cy9hcHBTdG9yZS9hcHBiYW5uZXJzdWIucG5nJyk7DQogICAgYmFja2dyb3VuZC1zaXplOiAxMDAlOw0KICAgIG1hcmdpbi10b3A6IDhweDsNCiAgICBmbG9hdDogcmlnaHQ7DQp9DQoNCi5pbmZvLXRvcCAucmlnaHQtaW5mbz5kaXYgew0KICAgIHdpZHRoOiAzMDRweDsNCiAgICBoZWlnaHQ6IDE4NHB4Ow0KICAgIG1hcmdpbi10b3A6IDgwcHg7DQogICAgbWFyZ2luLWxlZnQ6IDIwcHg7DQogICAgb3BhY2l0eTogMC43NTsNCiAgICBib3JkZXItcmFkaXVzOiAycHg7DQogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAxKTsNCn0NCg0KLmluZm8tdG9wIC5yaWdodC1pbmZvPmRpdiBwIHsNCiAgICBsaW5lLWhlaWdodDogMzZweDsNCiAgICBwYWRkaW5nLWxlZnQ6IDMwcHg7DQogICAgcGFkZGluZy1ib3R0b206IDhweDsNCiAgICBjb2xvcjogcmdiYSgxMDIsIDEwMiwgMTAyLCAxKTsNCiAgICBmb250LXNpemU6IDE0cHg7DQogICAgdGV4dC1hbGlnbjogbGVmdDsNCn0NCg0KLmluZm8tdG9wIHNwYW4uYnRuIHsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgd2lkdGg6IDEwNHB4Ow0KICAgIGhlaWdodDogMzZweDsNCiAgICBsaW5lLWhlaWdodDogMzZweDsNCiAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgY29sb3I6ICM0MjhBRkE7DQogICAgZm9udC1zaXplOiAxNnB4Ow0KICAgIGJvcmRlcjogMXB4IHNvbGlkICMyMWM5Yjg7DQogICAgbWFyZ2luLXJpZ2h0OiAyMHB4Ow0KfQ0K"}, {"version": 3, "sources": ["info.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+HA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "info.vue", "sourceRoot": "src/views/appStore", "sourcesContent": ["<template>\r\n    <div style=\"background-color: #fff;\">\r\n        <div class=\"info-top\">\r\n            <div style=\"width: 1128px;margin: 0px auto;\">\r\n\r\n                <div style=\"width: 797px;display: inline-block;vertical-align: top;\">\r\n                    <div style=\"margin-top: 60px;width: 100%;\">\r\n                        <span style=\"color: rgba(51, 51, 51, 1);font-size: 42px;line-height: 66px;\">{{ info.appStoreName\r\n                        }}</span>\r\n                        <span style=\"line-height: 34px;border-radius: 2px;background-color: #21c9b8;color: #fff;\r\n\t\t\t\t\t\t\t\tfont-size: 14px;margin: 16px 30px;padding: 0px 10px;\">{{ info.appLabel }}</span>\r\n                    </div>\r\n                    <div style=\"color: rgba(102, 102, 102, 1);font-size: 16px;overflow: hidden;\r\n\t\t\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t\t\t\tdisplay: -webkit-box;\r\n\t\t\t\t\t\t-webkit-box-orient: vertical;\r\n\t\t\t\t\t\t-webkit-line-clamp: 2;\">{{ info.appStoreIntroduction }}</div>\r\n                    <div style=\"line-height: 36px;color: #21c9b8;font-size: 20px;margin: 20px 0\">\r\n                        {{ info.appStorePrice }}元起 / 年</div>\r\n                    <div style=\"margin-top: 30px;\">\r\n                        <span class=\"btn\" style=\"background: transparent linear-gradient(105deg, #21c9b8 0%, #7AB2FF 100%) 0% 0% no-repeat padding-box;\r\n\t\t\t\t\t\t\t\tcolor: rgba(255, 255, 255, 1);\" @click=\"\">立即订阅</span>\r\n                        <!--\t\t\t\t\t\t\t<span class=\"btn\" @click=\"dingyue\" v-if=\"detail.issub==0\">立即收藏</span>-->\r\n                        <!--\t\t\t\t\t\t\t<span class=\"btn\" @click=\"quxiaodingyue\" v-else>已收藏</span>-->\r\n                        <!--\t\t\t\t\t\t\t<span class=\"btn\" @click=\"getUrl()\">跳转使用</span>-->\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"right-info\" v-show=\"info.supply !== ''\">\r\n                    <div>\r\n                        <p style=\"padding-top: 10px;\">应用提供：{{ info.supply }}</p>\r\n                        <p>联系人：{{ info.appStoreContactsName }}</p>\r\n                        <p>联系电话：{{ info.appStoreContactsPhone }}</p>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div style=\"width: 1226px; margin: 60px auto;\">\r\n            <h3 style=\"line-height: 30px;color: #333;font-size: 20px;font-weight: 400;padding-bottom: 20px;\">\r\n                <span style=\"display: inline-block;vertical-align: top;height: 20px;width: 3px;\r\n\t\t\t\t\tbackground-color: #428AFA;border-radius: 3px;margin: 5px 18px 5px 0px;\"></span>\r\n                应用介绍\r\n            </h3>\r\n            <div>\r\n                {{ info.appStoreContent }}\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport { getAppStoreDetail } from \"@/api/appStore\";\r\nimport { appliCollect } from \"@/api/appliMarket\";\r\nimport \"@/assets/styles/index.css\";\r\n\r\nexport default {\r\n    name: \"appStoreInfo\",\r\n    data() {\r\n        return {\r\n            showLogin: false,\r\n            userinfo: [],\r\n            token: '',\r\n            id: '',\r\n            info: {},\r\n            detail: [],\r\n            Dataradio: \"\",\r\n            showGm: false,\r\n            team: '',\r\n            phone: window.sessionStorage.getItem('userName') ? window.sessionStorage.getItem('userName') : ''\r\n        };\r\n    },\r\n    created() {\r\n        if (this.$route.query.id) {\r\n            this.id = this.$route.query.id\r\n            this.getInfo()\r\n        }\r\n    },\r\n    methods: {\r\n        updateDataList() {\r\n            console.log(this.checked)\r\n        },\r\n        changeHandler() {\r\n            if (this.Dataradio == 'month') {\r\n                this.detail.price = '1000'\r\n            } else if (this.Dataradio == 'year') {\r\n                this.detail.price = '30000'\r\n            } else if (this.Dataradio == 'permanent') {\r\n                this.detail.price = '68000'\r\n            }\r\n        },\r\n        goto() {\r\n            window.open(this.info.erweima)\r\n        },\r\n        async getInfo() {\r\n            let res = await getAppStoreDetail({ appStoreId: this.id })\r\n            if (res.code == 200) {\r\n                this.info = res.data;\r\n                console.log(this.info,'info')\r\n                this.detail = res.data;\r\n            }\r\n        },\r\n        async dingyue() {\r\n            let res = await appliCollect({ appId: this.id, userId: window.sessionStorage.getItem('userId') })\r\n            if (res.code == 200) {\r\n                this.getInfo();\r\n            } else {\r\n                alert(res.msg)\r\n            }\r\n        },\r\n        quxiaodingyue() {\r\n            YS.postFetch('uuc/store/unsubscribe', {\r\n                id: this.id,\r\n                userId: window.sessionStorage.getItem('userId')\r\n            }).then(res => {\r\n                if (res.code == 200) {\r\n                    this.getInfo();\r\n                } else {\r\n                    alert(res.msg)\r\n                }\r\n\r\n            });\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style type=\"text/css\">\r\n.info-top {\r\n    background-image: url('../../assets/appStore/appbanner.png');\r\n    height: 360px;\r\n    background-size: 100%;\r\n}\r\n\r\n.info-top span {\r\n    display: inline-block;\r\n    text-align: center;\r\n    vertical-align: top;\r\n}\r\n\r\n.info-top .right-info {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n    width: 331px;\r\n    height: 326px;\r\n    background-image: url('../../assets/appStore/appbannersub.png');\r\n    background-size: 100%;\r\n    margin-top: 8px;\r\n    float: right;\r\n}\r\n\r\n.info-top .right-info>div {\r\n    width: 304px;\r\n    height: 184px;\r\n    margin-top: 80px;\r\n    margin-left: 20px;\r\n    opacity: 0.75;\r\n    border-radius: 2px;\r\n    background-color: rgba(255, 255, 255, 1);\r\n}\r\n\r\n.info-top .right-info>div p {\r\n    line-height: 36px;\r\n    padding-left: 30px;\r\n    padding-bottom: 8px;\r\n    color: rgba(102, 102, 102, 1);\r\n    font-size: 14px;\r\n    text-align: left;\r\n}\r\n\r\n.info-top span.btn {\r\n    cursor: pointer;\r\n    width: 104px;\r\n    height: 36px;\r\n    line-height: 36px;\r\n    border-radius: 4px;\r\n    color: #428AFA;\r\n    font-size: 16px;\r\n    border: 1px solid #21c9b8;\r\n    margin-right: 20px;\r\n}\r\n</style>"]}]}