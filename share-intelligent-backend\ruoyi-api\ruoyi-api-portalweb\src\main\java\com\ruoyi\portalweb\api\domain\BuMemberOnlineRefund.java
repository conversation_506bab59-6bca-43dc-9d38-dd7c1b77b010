package com.ruoyi.portalweb.api.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;

/**
 * 商城用户线上退款对象 bu_member_online_refund
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
public class BuMemberOnlineRefund extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 租户id */
    @Excel(name = "租户id")
    private String tenantId;

    /** 客户id */
    @Excel(name = "客户id")
    private Long customerId;

    /** 会员id */
    @Excel(name = "会员id")
    private Long memberId;

    /** 退货单号 */
    @Excel(name = "退货单号")
    private String refundOrderNo;

    /** 退款时填写原商户订单号 */
    @Excel(name = "退款时填写原商户订单号")
    private String payOrderNo;

    /** 退款方式(4701:微信支付 4702：支付宝支付) */
    @Excel(name = "退款方式(4701:微信支付 4702：支付宝支付)")
    private String onlinePayStyle;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal money;

    /** 商户退款单号 */
    @Excel(name = "商户退款单号")
    private String appStoreOrderNo;

    /** 退款状态（SUCCESS-退款成功;CHANGE-退款异常;REFUNDCLOSE—退款关闭） */
    @Excel(name = "退款状态", readConverterExp = "S=UCCESS-退款成功;CHANGE-退款异常;REFUNDCLOSE—退款关闭")
    private String refundStatus;

    /** 退款成功时间 */
    @Excel(name = "退款成功时间")
    private String successTime;

    /** 退款入账账户 */
    @Excel(name = "退款入账账户")
    private String refundRecvAccout;

    /** 创建部门 */
    @Excel(name = "创建部门")
    private Long createDept;

    /** 状态(是否退款成功 1：否 2：是) */
    @Excel(name = "状态(是否退款成功 1：否 2：是)")
    private Long status;

    /** 删除标记 */
    @Excel(name = "删除标记")
    private Long isDeleted;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTenantId(String tenantId) 
    {
        this.tenantId = tenantId;
    }

    public String getTenantId() 
    {
        return tenantId;
    }
    public void setCustomerId(Long customerId) 
    {
        this.customerId = customerId;
    }

    public Long getCustomerId() 
    {
        return customerId;
    }
    public void setMemberId(Long memberId) 
    {
        this.memberId = memberId;
    }

    public Long getMemberId() 
    {
        return memberId;
    }
    public void setRefundOrderNo(String refundOrderNo) 
    {
        this.refundOrderNo = refundOrderNo;
    }

    public String getRefundOrderNo() 
    {
        return refundOrderNo;
    }
    public void setPayOrderNo(String payOrderNo) 
    {
        this.payOrderNo = payOrderNo;
    }

    public String getPayOrderNo() 
    {
        return payOrderNo;
    }
    public void setOnlinePayStyle(String onlinePayStyle) 
    {
        this.onlinePayStyle = onlinePayStyle;
    }

    public String getOnlinePayStyle() 
    {
        return onlinePayStyle;
    }
    public void setMoney(BigDecimal money) 
    {
        this.money = money;
    }

    public BigDecimal getMoney() 
    {
        return money;
    }
    public void setAppStoreOrderNo(String appStoreOrderNo) 
    {
        this.appStoreOrderNo = appStoreOrderNo;
    }

    public String getAppStoreOrderNo() 
    {
        return appStoreOrderNo;
    }
    public void setRefundStatus(String refundStatus) 
    {
        this.refundStatus = refundStatus;
    }

    public String getRefundStatus() 
    {
        return refundStatus;
    }
    public void setSuccessTime(String successTime) 
    {
        this.successTime = successTime;
    }

    public String getSuccessTime() 
    {
        return successTime;
    }
    public void setRefundRecvAccout(String refundRecvAccout) 
    {
        this.refundRecvAccout = refundRecvAccout;
    }

    public String getRefundRecvAccout() 
    {
        return refundRecvAccout;
    }
    public void setCreateDept(Long createDept) 
    {
        this.createDept = createDept;
    }

    public Long getCreateDept() 
    {
        return createDept;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }
    public void setIsDeleted(Long isDeleted) 
    {
        this.isDeleted = isDeleted;
    }

    public Long getIsDeleted() 
    {
        return isDeleted;
    }
    public void setVersion(Long version) 
    {
        this.version = version;
    }

    public Long getVersion() 
    {
        return version;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("tenantId", getTenantId())
            .append("customerId", getCustomerId())
            .append("memberId", getMemberId())
            .append("refundOrderNo", getRefundOrderNo())
            .append("payOrderNo", getPayOrderNo())
            .append("onlinePayStyle", getOnlinePayStyle())
            .append("money", getMoney())
            .append("appStoreOrderNo", getAppStoreOrderNo())
            .append("refundStatus", getRefundStatus())
            .append("successTime", getSuccessTime())
            .append("refundRecvAccout", getRefundRecvAccout())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createDept", getCreateDept())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("status", getStatus())
            .append("isDeleted", getIsDeleted())
            .append("version", getVersion())
            .toString();
    }
}
