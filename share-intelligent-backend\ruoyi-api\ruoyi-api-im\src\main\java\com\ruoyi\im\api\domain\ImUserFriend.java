package com.ruoyi.im.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName(value = "im_user_friend")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImUserFriend extends Model<ImUserFriend> {

        @TableId(value = "id", type = IdType.AUTO)
        private Long id;//自增

        private String userId;//用户ID

        private String friendId;//好友ID

        private String remark;//备注

        private Date create_time;//创建时间
}
