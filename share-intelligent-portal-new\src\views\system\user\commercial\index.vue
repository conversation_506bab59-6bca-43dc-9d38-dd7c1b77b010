<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:38:32
 * @LastEditTime: 2023-02-20 10:22:25
 * @Description: 
 * @LastEditors: zhc
-->
<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-02-03 11:20:59
 * @Description: 
 * @LastEditors: zhc
-->
<template>
  <div class="app-container">
    <div class="company-demand-record-page">
      <el-row :gutter="20">
        <el-col :span="2.5" :xs="24">
          <user-menu />
        </el-col>
        <el-col :span="20" :xs="24">
          <div class="top-row">
            <el-radio-group v-model="type" @change="changeType">
              <el-radio-button label="0">推荐资源</el-radio-button>
              <el-radio-button label="1">推荐需求</el-radio-button>
            </el-radio-group>
          </div>
          <div class="company-demand-pannel" v-if="type == '0'">
            <div class="none-class" v-if="!records || records.length == 0">
              <el-image
                style="width: 160px; height: 160px"
                :src="require('@/assets/user/none.png')"
                :fit="fit"
              ></el-image>
              <div class="text">暂无数据</div>
            </div>
            <div
              class="company-demand-item"
              v-for="item in records"
              v-bind:key="item.id"
            >
              <a class="left" @click="goDetail(item.id)">
                <el-image
                  v-if="item.scenePicture"
                  style="width: 90px; height: 64px"
                  :src="getUrl(item.scenePicture)"
                  :fit="fit"
                ></el-image>
                <div class="company-demand-title">
                  {{ item.demandTitle }}
                </div>
              </a>
              <div class="company-demand-status">
                {{ parseTime(item.createTime) }}
              </div>
            </div>
          </div>
          <div class="company-demand-pannel" v-if="type == '1'">
            <div class="none-class" v-if="!records || records.length == 0">
              <el-image
                style="width: 160px; height: 160px"
                :src="require('@/assets/user/none.png')"
                :fit="fit"
              ></el-image>
              <div class="text">暂无数据</div>
            </div>
            <div
              class="company-demand-item"
              v-for="item in records"
              v-bind:key="item.id"
            >
              <a class="left" @click="goResourceDetail(item.id)">
                <el-image
                  v-if="item.productPhoto"
                  style="width: 90px; height: 64px"
                  :src="getUrl(item.productPhoto)"
                  :fit="fit"
                ></el-image>
                <div class="company-demand-title">
                  {{ item.supplyName }}
                </div>
              </a>
              <div class="company-demand-status">
                {{ parseTime(item.createTime) }}
              </div>
            </div>
          </div>
          <el-pagination
            v-show="total > 0"
            background
            layout="prev, pager, next"
            :page-size="5"
            :current-page.sync="queryParams.pageNum"
            @current-change="handleCurrentChange"
            :total="total"
          >
          </el-pagination>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import UserMenu from "../components/userMenu.vue";
import { getDemandList } from "@/api/system/demand";
import { getApplyList } from "@/api/system/apply";
import store from "@/store";
import CryptoJS from "crypto-js";
let secretKey = "9zVn0%bqmUYSGw2n";

export default {
  name: "CompanyDemand",
  components: { UserMenu },
  data() {
    return {
      userId: store.getters.userId,
      type: "0",
      queryParams: {
        pageNum: 1,
        pageSize: 5,
      },
      total: 1,
      fit: "cover",
      records: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    changeType(type) {
      console.log(type, 1111111111111);
      if (type == "0") {
        this.getList();
      }
      if (type == "1") {
        this.getResourceList();
      }
    },
    getUrl(str) {
      var list = JSON.parse(str);
      if (list && list.length > 0) {
        return list[0].url;
      }
      return null;
    },
    getList() {
      getDemandList({
        ...this.queryParams,
        auditStatus: "2",
        createById: this.userId,
      }).then((response) => {
        let key = CryptoJS.enc.Utf8.parse(secretKey);
        let decrypt = CryptoJS.AES.decrypt(response, key, {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7,
        });
        response = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
        this.records = response.rows;
        this.total = response.total;
      });
    },
    handleCurrentChange(pageNum) {
      this.queryParams.pageNum = pageNum;
      this.getList();
    },
    doRevocation() {
      this.$confirm("是否确认撤回该提报？", { type: "error" })
        .then((_) => {
          revocationPolicy({ ids: row.id }).then((response) => {
            this.$message({
              message: "操作成功",
              type: "success",
            });
          });
        })
        .catch((_) => {});
    },
    goDetail(id) {
      this.$router.push("/user/companyDemandDetail?id=" + id);
    },
    goResourceDetail(id) {
      this.$router.push("/user/companyApplyDetail?id=" + id);
    },
    getResourceList() {
      getApplyList({
        ...this.queryParams,
        auditStatus: "2",
        createById: this.userId,
      }).then((response) => {
        let key = CryptoJS.enc.Utf8.parse(secretKey);
        let decrypt = CryptoJS.AES.decrypt(response, key, {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7,
        });
        response = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));
        this.records = response.rows;
        console.log(this.records, "获取到数据了吗--------------");
        this.total = response.total;
      });
    },
    getStatusName(status) {
      switch (status) {
        case 1:
          return "审核中";
        case 2:
          return "审核通过";
        case 3:
          return "审核驳回";

        default:
          break;
      }
    },
    getStatusClass(status) {
      switch (status) {
        case 1:
          return " orange";
        case 2:
          return "green";
        case 3:
          return "red";
      }
    },
  },
};
</script>

<style lang="scss">
.app-container {
  background: #f4f5f9;
  .company-demand-record-page {
    .top-row {
      display: flex;
      justify-content: space-between;
      .button-add {
        width: 100px;
        height: 32px;
        background: #21c9b8;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 400;
        color: #ffffff;
      }
    }
    .company-demand-pannel {
      margin-top: 24px;
      width: 100%;
      height: 600px;
      background: #fff;
      .none-class {
        text-align: center;
        padding: 10% 0;
        .text {
          font-size: 14px;
          font-weight: 400;
          color: #999999;
          line-height: 14px;
        }
      }
      .company-demand-item {
        display: flex;
        position: relative;
        padding: 20px;
        height: 112px;
        border-bottom: 1px solid #e8e8e8;
        .left {
          width: 73%;
          display: flex;
          .company-demand-title {
            margin-left: 24px;
            font-size: 16px;
            font-weight: 500;
            color: #323233;
            width: 85%;
            line-height: 30px;
            overflow: hidden;
            -webkit-line-clamp: 1;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
          }
        }
        .company-demand-status {
          position: absolute;
          right: 30px;
          top: 40px;
          padding: 2px 8px;
          border-radius: 4px;
          font-size: 14px;
          font-size: 15px;
          line-height: 30px;
          text-align: center;
          font-weight: 500;
          overflow: hidden;
          -webkit-line-clamp: 1;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
        }
        .orange {
          background: rgba(246, 141, 57, 0.15);
          color: #ff8b2e;
        }
        .green {
          background: rgba(21, 188, 132, 0.15);
          color: #15bc84;
        }
        .red {
          background: rgba(255, 77, 77, 0.15);
          color: #ff4d4d;
        }
      }
    }
    .el-radio-button {
      margin-right: 30px;
    }
    .el-radio-button__inner {
      width: 96px;
      height: 32px;
      background: transparent;
      border-radius: 20px;
      text-align: center;
      color: #333333;
      border: none;
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background: #21c9b8 !important;
      color: #fff;
      box-shadow: none;
    }
    .el-radio-button__inner:hover {
      color: #333333;
    }

    .el-pagination {
      width: 100%;
      margin-top: 20px;
      text-align: center;
    }
    .el-pagination.is-background .el-pager li {
      background-color: #fff;
    }
    .el-pagination.is-background .el-pager li:not(.disabled).active {
      background-color: #21c9b8;
      color: #ffffff;
    }
    .el-pagination.is-background .el-pager li:not(.disabled):hover {
      color: #21c9b8;
    }
  }
}
</style>
