<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.FileDetailMapper">

    <resultMap type="com.ruoyi.portalweb.vo.FileDetailVO" id="FileDetailResult">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="parentType" column="parent_type"/>
        <result property="fileId" column="file_id"/>
        <result property="fileType" column="file_type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberId" column="member_id"/>

        <result property="fileName" column="file_name"/>
        <result property="filePath" column="file_path"/>
        <result property="fileFullPath" column="file_full_path"/>
    </resultMap>

    <sql id="Base_Column_List">
		a.*
	</sql>

	<sql id="Base_Table_List">
		FROM file_detail a
	</sql>

    <select id="selectFileDetailList" parameterType="FileDetailVO" resultMap="FileDetailResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        <where>
            a.del_flag = 0
            <if test="parentId != null and parentId != ''">and a.parent_id = #{parentId}</if>
            <if test="parentType != null and parentType != ''">and a.parent_type = #{parentType}</if>
            <if test="fileId != null and fileId != ''">and a.file_id = #{fileId}</if>
            <if test="fileType != null and fileType != ''">and a.file_type = #{fileType}</if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <select id="selectFileDetailById" parameterType="Long" resultMap="FileDetailResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        where id = #{id}
    </select>

    <insert id="insertFileDetail" parameterType="FileDetail" useGeneratedKeys="true" keyProperty="id">
        insert into file_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="parentType != null">parent_type,</if>
            <if test="fileId != null">file_id,</if>
            <if test="fileType != null">file_type,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="parentType != null">#{parentType},</if>
            <if test="fileId != null">#{fileId},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateFileDetail" parameterType="FileDetail">
        update file_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null and parentId != ''">parent_id = #{parentId},</if>
            <if test="parentType != null and parentType != ''">parent_type = #{parentType},</if>
            <if test="fileId != null and fileId != ''">file_id = #{fileId},</if>
            <if test="fileType != null and fileType != ''">file_type = #{fileType},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFileDetailById" parameterType="Long">
        delete from file_detail where id = #{id}
    </delete>

    <delete id="deleteFileDetailByIds" parameterType="String">
        delete from file_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据billID，物理删除 -->
	<delete id="removeBybillId">
		DELETE FROM file_detail
		WHERE parent_id = #{parentId} and parent_type = #{parentType}
		<choose>
			<when test="type != null and type == 'isnull'">
				AND file_type is null
			</when>
			<when test="type != null and type != ''">
				AND file_type = #{type}
			</when>
			<otherwise>
			</otherwise>
		</choose>
  	</delete>

    <select id="selectPictureList" resultMap="FileDetailResult">
        SELECT  a.* ,b.file_name,b.file_path,b.file_full_path
        FROM file_detail a
        LEFT JOIN sys_file_info b on a.file_id = b.file_id
        WHERE a.del_flag = 0
        AND a.parent_id = #{parentId} and parent_type = #{parentType}
        <if test="fileUrl!=null and fileUrl!=''">
         	AND b.file_path = #{fileUrl}
        </if>
    </select>
</mapper>