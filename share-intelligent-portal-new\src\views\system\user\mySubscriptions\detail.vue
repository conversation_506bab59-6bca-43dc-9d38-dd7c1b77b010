<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu />
      </el-col>
      <el-col :span="20" :xs="24">
        <div class="content">
          <div class="content_top">
            <div class="orderStatus">
              <div class="statusName" v-if="info.orderStatus">
                {{
                  orderForm.filter((item) => item.value == info.orderStatus)[0]
                    .statusName
                }}
              </div>
              <div class="desc" v-if="info.orderStatus">
                {{
                  orderForm.filter((item) => item.value == info.orderStatus)[0]
                    .desc
                }}
              </div>
            </div>
            <div class="amountMoney">
              <span style="color: rgb(173, 173, 173)">订单金额:</span>
              <span style="margin-left: 10px">¥ {{ info.price }}</span>
            </div>
            <!-- 待支付 -->
            <div class="button_content" v-if="info.orderStatus == 1">
              <div>
                <div class="buttonStyle">去支付</div>
                <div style="margin-top: 10px">
                  <span
                    style="color: #21c9b8; cursor: pointer"
                    @click="cancelOrder(info.id)"
                    >取消订单</span
                  >
                  <span
                    style="margin-left: 20px; color: #21c9b8; cursor: pointer"
                    @click="tryout(info)"
                    >前往试用</span
                  >
                </div>
              </div>
            </div>
            <!-- 待发货 -->
            <div class="button_content" v-if="info.orderStatus == 2">
              <div>
                <div>
                  <span
                    style="margin-left: 20px; color: #21c9b8; cursor: pointer"
                    @click="tryout(info)"
                    >前往试用</span
                  >
                </div>
              </div>
            </div>
            <!-- 已发货 -->
            <div class="button_content" v-if="info.orderStatus == 4">
              <div>
                <div>
                  <span
                    style="color: #21c9b8; cursor: pointer"
                    @click="applyInvoice(info.id)"
                    >申请开票</span
                  >
                  <span
                    style="margin-left: 20px; color: #21c9b8; cursor: pointer"
                    @click="confirmReceipt(info.id)"
                    >确认收货</span
                  >
                </div>
              </div>
            </div>
            <!-- 已成交 -->
            <div class="button_content" v-if="info.orderStatus == 5">
              <div>
                <div>
                  <span style="color: #21c9b8; cursor: pointer">已开票</span>
                </div>
              </div>
            </div>
            <!-- 待续费 -->
            <div class="button_content" v-if="info.orderStatus == 6">
              <div>
                <div>
                  <span style="color: #21c9b8; cursor: pointer">去支付</span>
                </div>
              </div>
            </div>
          </div>
          <div class="content_bottom">
            <div>
              <el-descriptions title="订单信息" :column="2">
                <el-descriptions-item label="订单编号">{{
                  info.id
                }}</el-descriptions-item>
                <el-descriptions-item label="下单时间">{{
                  info.orderDate
                }}</el-descriptions-item>
                <el-descriptions-item label="应用提供">{{
                  info.supply
                }}</el-descriptions-item>
                <el-descriptions-item label="付款时间">
                  <el-tag size="small">{{
                    info.payTime ? parseTime(info.payTime) : "--"
                  }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="应用编号">{{
                  info.appCode
                }}</el-descriptions-item>
                <el-descriptions-item label="发货时间">{{
                  info.deliverTime
                }}</el-descriptions-item>
                <el-descriptions-item label="付款方式">{{
                  info.payWay
                }}</el-descriptions-item>
                <el-descriptions-item label="成交时间">{{
                  info.makeTime
                }}</el-descriptions-item>
              </el-descriptions>
            </div>
            <div style="margin-top: 30px">
              <el-table :data="tableData" style="width: 100%">
                <el-table-column prop="remark" label="产品标题">
                </el-table-column>
                <el-table-column label="产品图片">
                  <template slot-scope="scope">
                    <img
                      style="width: 100px; height: 100px"
                      :src="scope.row.appLogo"
                      alt=""
                    />
                  </template>
                </el-table-column>
                <el-table-column prop="appCategory" label="产品类型">
                </el-table-column>
                <el-table-column label="规格信息">
                  <template slot-scope="scoped">
                    <!-- <div>规格: {{ scoped.row.spec }}</div> -->
                    <div>
                      可用时长:
                      {{ scoped.row.validTime == "1" ? "一年" : "永久" }}
                    </div>
                    <div>可用人数: 不限</div>
                  </template>
                </el-table-column>
                <el-table-column label="有效时间">
                  <template slot-scope="scope">
                    {{
                      scope.row.expirationTime
                        ? parseTime(scope.row.expirationTime)
                        : "--"
                    }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="btnStyle">
            <el-button @click="goBack">返回</el-button>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-dialog
      title="开票信息"
      :visible.sync="invoiceVisible"
      width="750px"
      append-to-body
    >
      <el-form :model="invoiceData" label-width="80px">
        <el-form-item label="发票类型:" prop="realName">
          {{ invoiceData.invoiceType == 1 ? "专票" : "普票" }}
        </el-form-item>
        <el-form-item label="公司名称:" prop="phonenumber">
          {{ invoiceData.companyName }}
        </el-form-item>
        <el-form-item label="税号:" prop="weixin">
          {{ invoiceData.dutyParagraph }}
        </el-form-item>
        <el-form-item label="公司地址:" prop="email">
          {{ invoiceData.address }}
        </el-form-item>
        <el-form-item label="公司电话:" prop="email">
          {{ invoiceData.phone }}
        </el-form-item>
        <el-form-item label="开户银行:" prop="email">
          {{ invoiceData.openAccount }}
        </el-form-item>
        <el-form-item label="银行账号:" prop="email">
          {{ invoiceData.bankAccount }}
        </el-form-item>
        <el-form-item label="邮箱地址:" prop="email">
          {{ invoiceData.email }}
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDialog">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  orderDetail,
  cancelOrder,
  invoiceList,
  applyInvoice,
  modifyStatus,
} from "@/api/system/user";
import UserMenu from "../components/userMenu.vue";
// import { getToken } from "@/utils/auth";
export default {
  name: "Operlog",
  dicts: ["sys_oper_type", "sys_common_status"],
  components: { UserMenu },
  data() {
    return {
      queryParams: {
        pageNum: 1,
      },
      total: 0,
      flag: "0",
      tableData: [],
      info: {},
      invoiceData: {},
      orderForm: [
        {
          value: 1,
          statusName: "待支付",
          desc: "如对订单有疑问，可联系客服4008-939-365",
        },
        {
          value: 2,
          statusName: "待发货",
          desc: "平台将于2023-08-04日前发货，感谢您的支持!如您对订单有疑问，可联系客服4008-939-365",
        },
        {
          value: 3,
          statusName: "支付失败",
          desc: "订单支付失败，如您对订单有疑问，可联系客服4008-939-365",
        },
        {
          value: 4,
          statusName: "已发货",
          desc: "使用过程中有任何问题，可联系客服4008-939-365",
        },
        {
          value: 5,
          statusName: "已成交",
          desc: "使用过程中有任何问题，可联系客服4008-939-365",
        },
        {
          value: 6,
          statusName: "待续费",
          desc: "请尽快续费，以免影响正常使用",
        },
        {
          value: 7,
          statusName: "已关闭",
          desc: "如对订单有疑问，可联系客服4008-939-365",
        },
        {
          value: 8,
          statusName: "支付中",
          desc: "如对订单有疑问，可联系客服4008-939-365",
        },
        {
          value: 9,
          statusName: "已取消",
          desc: "如对订单有疑问，可联系客服4008-939-365",
        },
      ],
      invoiceVisible: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      let id = this.$route.query.id;
      orderDetail(id).then((res) => {
        this.loading = false;
        this.tableData = [];
        if (res.code === 200) {
          this.info = res.data;
          this.tableData.push(res.data);
        }
      });
    },
    handleCurrentChange(pageNum) {
      this.queryParams.pageNum = pageNum;
      this.getList();
    },
    tryout(item) {
      console.log(item);
      if (item.appName == "云端研发") {
        let url;
        let hostname;
        var result;
        hostname = " https://yunduanyanfa.ningmengdou.com/login ";
        result = encodeURIComponent(hostname);
        url = "https://sso.ningmengdou.com/single/login?returnUrl=" + result;
        window.open(url, "_blank");
      } else if (item.appName == "檬豆云供应链管理系统") {
      } else if (item.appName == "集采平台") {
        window.open("https://mdy.ningmengdou.com");
      } else if (item.appName == "云MES") {
        let userid = "18660283726";
        console.log(userid);
        let jsonData = { U: userid, P: "12a", A: "acb" };
        console.log(jsonData);
        const encodedData = btoa(JSON.stringify(jsonData));
        console.log(encodedData);
        window.open(
          "http://mes.ningmengdou.com/default.html?parm=" + encodedData,
          "_blank"
        );
      } else {
        window.open("//" + item.webexperienceUrl, "_blank");
      }
    },
    cancelOrder(id) {
      this.$confirm("订单取消后无法恢复，请谨慎操作!", "取消订单", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          cancelOrder(id).then((res) => {
            if (res.code === 200) {
              this.$message.success("操作成功!");
              this.$router.go(-1);
            }
          });
        })
        .catch(() => {});
    },
    applyInvoice() {
      this.getInvoiceData();
    },
    getInvoiceData() {
      invoiceList().then((res) => {
        if (res.code === 200) {
          this.invoiceData = res.data;
          this.invoiceVisible = true;
        }
      });
    },
    confirmReceipt(id) {
      this.$confirm("确认后订单状态无法变更，确认收货吗？", "确认收货", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let data = {
            id,
            orderStatus: 5,
          };
          modifyStatus(data).then((res) => {
            if (res.code === 200) {
              this.$message.success("操作成功!");
              this.getList();
            }
          });
        })
        .catch(() => {});
    },
    submitForm() {
      let data = {
        invoiceMedium: "1",
        invoiceType: "1",
        issueType: "1",
        invoiceHeader: this.invoiceData.companyName,
        dutyParagraph: this.invoiceData.dutyParagraph,
        email: this.invoiceData.email,
        orderId: this.info.id,
        sendTo: this.invoiceData.userId,
      };
      applyInvoice(data).then((res) => {
        if (res.code === 200) {
          this.invoiceVisible = false;
          this.$message.success("操作成功!");
        }
      });
    },
    cancelDialog() {
      this.invoiceVisible = false;
    },
    goBack() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #f4f5f9;
}
.content {
  width: 100%;
  padding: 40px;
  background: #ffffff;
  .content_top {
    display: flex;
    width: 100%;
    height: 120px;
    align-items: center;
    .orderStatus {
      width: 30%;
      text-align: center;
      .statusName {
        font-size: 18px;
        font-weight: 600;
      }
      .desc {
        font-size: 14px;
        color: rgb(173, 173, 173);
        margin-top: 30px;
      }
    }
    .amountMoney {
      width: 40%;
      text-align: center;
    }
    .button_content {
      width: 20%;
      text-align: center;
      .buttonStyle {
        height: 50px;
        background: #21c9b8;
        line-height: 50px;
        color: #ffffff;
        cursor: pointer;
      }
    }
  }
  .content_bottom {
    margin-top: 20px;
    padding: 20px;
    width: 100%;
  }
  .btnStyle {
    text-align: center;
  }
}
</style>
