-- 创建复材展厅表
create table composite_material (
  id                bigint(20)      not null auto_increment    comment '主键ID',
  material_name     varchar(100)    not null                   comment '材料名称',
  image_url         varchar(500)    not null                   comment '材料图片URL',
  image_list        text                                       comment '图片列表',
  description       text                                       comment '材料介绍',
  product_type      varchar(50)                                comment '产品类',
  sort_order        int(4)          default 0                  comment '排序号',
  status            char(1)         default '0'                comment '状态（0正常 1停用）',
  del_flag          char(1)         default '0'                comment '删除标志（0代表存在 2代表删除）',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (id)
) engine=innodb auto_increment=100 comment = '复材展厅表';

-- 创建索引
create index idx_composite_material_name on composite_material(material_name);
create index idx_composite_material_status on composite_material(status);
create index idx_composite_material_product_type on composite_material(product_type);
