{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\talentJoinNow\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\talentJoinNow\\index.vue", "mtime": 1750311963088}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_data", "_serviceSharing", "components", "UserMenu", "data", "id", "form", "name", "contactPhone", "birthDate", "location", "graduateSchool", "education", "currentCompany", "workStatus", "position", "jobTitle", "skills", "positionType", "workExperience", "photo", "resumeFile", "settledStatus", "rules", "required", "message", "trigger", "positionTypeList", "educationList", "jobTitleList", "workStatusList", "photoList", "created", "getPositionType", "getEducation", "getJobTitle", "getWorkStatus", "$route", "query", "getTalentInfo", "methods", "_this", "talentDetailData", "then", "res", "_this2", "params", "dictType", "listData", "response", "rows", "_this3", "_this4", "_this5", "onSubmit", "_this6", "$refs", "validate", "valid", "_this6$photoList$", "_this6$photoList$2", "url", "talentAdd", "code", "$message", "success", "onCancel", "$router", "go"], "sources": ["src/views/system/user/talentJoinNow/index.vue"], "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-row :gutter=\"20\">\r\n            <el-col :span=\"2.5\" :xs=\"24\">\r\n                <user-menu activeIndex=\"1\" />\r\n            </el-col>\r\n            <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n                <div class=\"main-content\">\r\n                    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n                        <el-row :gutter=\"50\">\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"姓名\" prop=\"name\">\r\n                                    <el-input v-model=\"form.name\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"联系电话\" prop=\"contactPhone\">\r\n                                    <el-input v-model=\"form.contactPhone\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"出生年月\">\r\n                                    <el-date-picker v-model=\"form.birthDate\" type=\"date\" placeholder=\"选择日期\"\r\n                                        value-format=\"yyyy-MM-dd\" style=\"width: 100%\" />\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"所在地\">\r\n                                    <el-input v-model=\"form.location\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"毕业院校\">\r\n                                    <el-input v-model=\"form.graduateSchool\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"最高学历\" prop=\"education\">\r\n                                    <el-select v-model=\"form.education\" placeholder=\"请选择最高学历\" clearable\r\n                                        style=\"width: 100%\">\r\n                                        <el-option v-for=\"dict in educationList\" :key=\"dict.dictLabel\"\r\n                                            :label=\"dict.dictLabel\" :value=\"dict.dictValue\" />\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"所在单位\">\r\n                                    <el-input v-model=\"form.currentCompany\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"工作状态\" prop=\"workStatus\">\r\n                                    <el-radio-group v-model=\"form.workStatus\">\r\n                                        <el-radio v-for=\"dict in workStatusList\" :key=\"dict.dictValue\"\r\n                                            :label=\"dict.dictValue\" :value=\"dict.dictValue\">{{ dict.dictLabel\r\n                                            }}</el-radio>\r\n                                    </el-radio-group>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"职务\">\r\n                                    <el-input v-model=\"form.position\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"职称\" prop=\"jobTitle\">\r\n                                    <el-select v-model=\"form.jobTitle\" placeholder=\"请选择职称\" clearable\r\n                                        style=\"width: 100%\">\r\n                                        <el-option v-for=\"dict in jobTitleList\" :key=\"dict.dictLabel\"\r\n                                            :label=\"dict.dictLabel\" :value=\"dict.dictValue\" />\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"技术领域\">\r\n                                    <el-input v-model=\"form.skills\" placeholder=\"请输入\"></el-input>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"岗位分类\" prop=\"positionType\">\r\n                                    <el-select v-model=\"form.positionType\" placeholder=\"请选择岗位分类\" clearable\r\n                                        style=\"width: 100%\">\r\n                                        <el-option v-for=\"dict in positionTypeList\" :key=\"dict.dictLabel\"\r\n                                            :label=\"dict.dictLabel\" :value=\"dict.dictValue\" />\r\n                                    </el-select>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"24\">\r\n                                <el-form-item label=\"个人简介\">\r\n                                    <el-input v-model=\"form.workExperience\" type=\"textarea\" resize=\"none\" :rows=\"8\"\r\n                                        maxlength=\"500\" show-word-limit placeholder=\"请输入\" />\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"个人照片\">\r\n                                    <ImageUpload v-model=\"photoList\" :limit=\"1\"/>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"12\">\r\n                                <el-form-item label=\"附件\">\r\n                                    <FileUpload v-model=\"form.resumeFile\" />\r\n                                </el-form-item>\r\n                            </el-col>\r\n                            <el-col :span=\"24\">\r\n                                <el-form-item class=\"footer-submit\">\r\n                                    <el-button type=\"primary\" @click=\"onSubmit\">提交</el-button>\r\n                                    <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\">取消</el-button>\r\n                                </el-form-item>\r\n                            </el-col>\r\n                        </el-row>\r\n                    </el-form>\r\n                </div>\r\n            </el-col>\r\n        </el-row>\r\n    </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { talentAdd, talentDetailData } from \"@/api/serviceSharing\";\r\n\r\nexport default {\r\n    components: { UserMenu },\r\n    data() {\r\n        return {\r\n            id: \"\",\r\n            form: {\r\n                name: \"\",\r\n                contactPhone: \"\",\r\n                birthDate: \"\",\r\n                location: \"\",\r\n                graduateSchool: \"\",\r\n                education: \"\",\r\n                currentCompany: \"\",\r\n                workStatus: \"\",\r\n                position: \"\",\r\n                jobTitle: \"\",\r\n                skills: \"\",\r\n                positionType: \"\", // 岗位分类\r\n                workExperience: \"\", // 个人简介\r\n                photo: \"\", // 照片\r\n                resumeFile: \"\", // 简历附件\r\n                settledStatus: \"0\",\r\n            },\r\n            rules: {\r\n                name: [{ required: true, message: \"姓名不能为空\", trigger: \"blur\" }],\r\n                positionType: [\r\n                    { required: true, message: \"岗位分类不能为空\", trigger: \"change\" },\r\n                ],\r\n                education: [\r\n                    { required: true, message: \"最高学历不能为空\", trigger: \"blur\" },\r\n                ],\r\n                jobTitle: [\r\n                    { required: true, message: \"职称不能为空\", trigger: \"blur\" },\r\n                ],\r\n                workStatus: [\r\n                    { required: true, message: \"工作状态不能为空\", trigger: \"change\" },\r\n                ],\r\n            },\r\n            positionTypeList: [], // 岗位分类\r\n            educationList: [], // 最高学历\r\n            jobTitleList: [], // 职称\r\n            workStatusList: [], // 工作状态\r\n            photoList: [],\r\n        };\r\n    },\r\n    created() {\r\n        this.getPositionType();\r\n        this.getEducation();\r\n        this.getJobTitle();\r\n        this.getWorkStatus();\r\n        if (this.$route.query.id) {\r\n            this.id = this.$route.query.id;\r\n            this.getTalentInfo();\r\n        }\r\n    },\r\n    methods: {\r\n        getTalentInfo() {\r\n            talentDetailData(this.id).then((res) => {\r\n                this.form = res.data;\r\n                this.photoList = res.data.photo ? [res.data.photo] : [];\r\n            })\r\n        },\r\n        // 岗位分类\r\n        getPositionType() {\r\n            let params = { dictType: \"position_type\" };\r\n            listData(params).then((response) => {\r\n                this.positionTypeList = response.rows;\r\n            });\r\n        },\r\n        // 最高学历\r\n        getEducation() {\r\n            let params = { dictType: \"education\" };\r\n            listData(params).then((response) => {\r\n                this.educationList = response.rows;\r\n            });\r\n        },\r\n        // 职称\r\n        getJobTitle() {\r\n            let params = { dictType: \"job_title\" };\r\n            listData(params).then((response) => {\r\n                this.jobTitleList = response.rows;\r\n            });\r\n        },\r\n        // 工作状态\r\n        getWorkStatus() {\r\n            let params = { dictType: \"work_status\" };\r\n            listData(params).then((response) => {\r\n                this.workStatusList = response.rows;\r\n            });\r\n        },\r\n        onSubmit() {\r\n            this.$refs[\"form\"].validate((valid) => {\r\n                this.form.photo = this.photoList[0]?.url ? this.photoList[0]?.url : \"\";\r\n                if (valid) {\r\n                    talentAdd(this.form).then((res) => {\r\n                        if (res.code === 200) {\r\n                            this.$message.success(\"操作成功\");\r\n                            this.onCancel();\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        },\r\n        onCancel() {\r\n            this.$router.go(-1);\r\n        },\r\n    },\r\n};\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n    background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n}\r\n\r\n.main-content {\r\n    background-color: #fff;\r\n    padding: 20px;\r\n    padding-bottom: 100px;\r\n\r\n    .btn-box {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin-top: 40px;\r\n\r\n        .btn {\r\n            width: 200px;\r\n            height: 50px;\r\n        }\r\n    }\r\n\r\n    .card {\r\n        margin-top: 170px;\r\n        padding: 10px;\r\n        box-sizing: border-box;\r\n        width: 280px;\r\n        height: 240px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        align-items: center;\r\n        background: url(\"../../../../assets/userCenter/card_bg.png\") no-repeat;\r\n        background-size: 100% 100%;\r\n\r\n\r\n        .card-title {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            margin-bottom: 10px;\r\n            color: #030A1A;\r\n        }\r\n\r\n        .card-content {\r\n            font-size: 14px;\r\n            color: #666666;\r\n            line-height: 30px;\r\n        }\r\n\r\n        .success {\r\n            color: #21C9B8;\r\n        }\r\n\r\n        .btn-card {\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            margin-top: 0px;\r\n\r\n            .btn {\r\n                width: 200px;\r\n                height: 50px;\r\n            }\r\n        }\r\n    }\r\n\r\n}\r\n\r\n\r\n.content {\r\n    width: 100%;\r\n    padding-bottom: 60px;\r\n    background-color: #f2f2f2;\r\n}\r\n\r\n.content_banner {\r\n    width: 100%;\r\n    height: 300px;\r\n    background-image: url(\"../../../../assets/release/banner.png\");\r\n    background-size: 100% 100%;\r\n    text-align: center;\r\n    margin: 0 auto;\r\n    padding-top: 28px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    font-size: 40px;\r\n    color: #000;\r\n\r\n    .imgContent {\r\n        width: 100%;\r\n        display: flex;\r\n        justify-content: center;\r\n        margin-top: 10px;\r\n\r\n        .imgStyle {\r\n            width: 1256px;\r\n            height: 206px;\r\n            position: relative;\r\n        }\r\n    }\r\n}\r\n\r\n.content_card {\r\n    // height: 1530px;\r\n    background: #ffffff;\r\n    border-radius: 2px;\r\n    margin-top: 30px;\r\n    padding: 59px 60px 57px 60px;\r\n}\r\n\r\n.addStyle {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #21c9b8;\r\n    margin-left: auto;\r\n    cursor: pointer;\r\n}\r\n\r\n.footer-submit {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-top: 60px;\r\n\r\n    .el-button {\r\n        width: 140px;\r\n        height: 50px;\r\n    }\r\n}\r\n</style>"], "mappings": ";;;;;;;AAqHA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAG,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,EAAA;MACAC,IAAA;QACAC,IAAA;QACAC,YAAA;QACAC,SAAA;QACAC,QAAA;QACAC,cAAA;QACAC,SAAA;QACAC,cAAA;QACAC,UAAA;QACAC,QAAA;QACAC,QAAA;QACAC,MAAA;QACAC,YAAA;QAAA;QACAC,cAAA;QAAA;QACAC,KAAA;QAAA;QACAC,UAAA;QAAA;QACAC,aAAA;MACA;MACAC,KAAA;QACAhB,IAAA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAR,YAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAd,SAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAV,QAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,UAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,gBAAA;MAAA;MACAC,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,cAAA;MAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,eAAA;IACA,KAAAC,YAAA;IACA,KAAAC,WAAA;IACA,KAAAC,aAAA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAjC,EAAA;MACA,KAAAA,EAAA,QAAAgC,MAAA,CAAAC,KAAA,CAAAjC,EAAA;MACA,KAAAkC,aAAA;IACA;EACA;EACAC,OAAA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,gCAAA,OAAArC,EAAA,EAAAsC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAnC,IAAA,GAAAsC,GAAA,CAAAxC,IAAA;QACAqC,KAAA,CAAAV,SAAA,GAAAa,GAAA,CAAAxC,IAAA,CAAAgB,KAAA,IAAAwB,GAAA,CAAAxC,IAAA,CAAAgB,KAAA;MACA;IACA;IACA;IACAa,eAAA,WAAAA,gBAAA;MAAA,IAAAY,MAAA;MACA,IAAAC,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAH,IAAA,WAAAM,QAAA;QACAJ,MAAA,CAAAlB,gBAAA,GAAAsB,QAAA,CAAAC,IAAA;MACA;IACA;IACA;IACAhB,YAAA,WAAAA,aAAA;MAAA,IAAAiB,MAAA;MACA,IAAAL,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAH,IAAA,WAAAM,QAAA;QACAE,MAAA,CAAAvB,aAAA,GAAAqB,QAAA,CAAAC,IAAA;MACA;IACA;IACA;IACAf,WAAA,WAAAA,YAAA;MAAA,IAAAiB,MAAA;MACA,IAAAN,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAH,IAAA,WAAAM,QAAA;QACAG,MAAA,CAAAvB,YAAA,GAAAoB,QAAA,CAAAC,IAAA;MACA;IACA;IACA;IACAd,aAAA,WAAAA,cAAA;MAAA,IAAAiB,MAAA;MACA,IAAAP,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAH,IAAA,WAAAM,QAAA;QACAI,MAAA,CAAAvB,cAAA,GAAAmB,QAAA,CAAAC,IAAA;MACA;IACA;IACAI,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QAAA,IAAAC,iBAAA,EAAAC,kBAAA;QACAL,MAAA,CAAAjD,IAAA,CAAAc,KAAA,IAAAuC,iBAAA,GAAAJ,MAAA,CAAAxB,SAAA,iBAAA4B,iBAAA,eAAAA,iBAAA,CAAAE,GAAA,IAAAD,kBAAA,GAAAL,MAAA,CAAAxB,SAAA,iBAAA6B,kBAAA,uBAAAA,kBAAA,CAAAC,GAAA;QACA,IAAAH,KAAA;UACA,IAAAI,yBAAA,EAAAP,MAAA,CAAAjD,IAAA,EAAAqC,IAAA,WAAAC,GAAA;YACA,IAAAA,GAAA,CAAAmB,IAAA;cACAR,MAAA,CAAAS,QAAA,CAAAC,OAAA;cACAV,MAAA,CAAAW,QAAA;YACA;UACA;QACA;MACA;IACA;IACAA,QAAA,WAAAA,SAAA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;EACA;AACA", "ignoreList": []}]}