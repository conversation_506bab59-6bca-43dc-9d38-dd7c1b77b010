{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\mySubscriptions\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\mySubscriptions\\detail.vue", "mtime": 1750311963062}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBvcmRlckRldGFpbCwNCiAgY2FuY2VsT3JkZXIsDQogIGludm9pY2VMaXN0LA0KICBhcHBseUludm9pY2UsDQogIG1vZGlmeVN0YXR1cywNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOw0KaW1wb3J0IFVzZXJNZW51IGZyb20gIi4uL2NvbXBvbmVudHMvdXNlck1lbnUudnVlIjsNCi8vIGltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIk9wZXJsb2ciLA0KICBkaWN0czogWyJzeXNfb3Blcl90eXBlIiwgInN5c19jb21tb25fc3RhdHVzIl0sDQogIGNvbXBvbmVudHM6IHsgVXNlck1lbnUgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgIH0sDQogICAgICB0b3RhbDogMCwNCiAgICAgIGZsYWc6ICIwIiwNCiAgICAgIHRhYmxlRGF0YTogW10sDQogICAgICBpbmZvOiB7fSwNCiAgICAgIGludm9pY2VEYXRhOiB7fSwNCiAgICAgIG9yZGVyRm9ybTogWw0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6IDEsDQogICAgICAgICAgc3RhdHVzTmFtZTogIuW+heaUr+S7mCIsDQogICAgICAgICAgZGVzYzogIuWmguWvueiuouWNleacieeWkemXru+8jOWPr+iBlOezu+WuouacjTQwMDgtOTM5LTM2NSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogMiwNCiAgICAgICAgICBzdGF0dXNOYW1lOiAi5b6F5Y+R6LSnIiwNCiAgICAgICAgICBkZXNjOiAi5bmz5Y+w5bCG5LqOMjAyMy0wOC0wNOaXpeWJjeWPkei0p++8jOaEn+iwouaCqOeahOaUr+aMgSHlpoLmgqjlr7norqLljZXmnInnlpHpl67vvIzlj6/ogZTns7vlrqLmnI00MDA4LTkzOS0zNjUiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6IDMsDQogICAgICAgICAgc3RhdHVzTmFtZTogIuaUr+S7mOWksei0pSIsDQogICAgICAgICAgZGVzYzogIuiuouWNleaUr+S7mOWksei0pe+8jOWmguaCqOWvueiuouWNleacieeWkemXru+8jOWPr+iBlOezu+WuouacjTQwMDgtOTM5LTM2NSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogNCwNCiAgICAgICAgICBzdGF0dXNOYW1lOiAi5bey5Y+R6LSnIiwNCiAgICAgICAgICBkZXNjOiAi5L2/55So6L+H56iL5Lit5pyJ5Lu75L2V6Zeu6aKY77yM5Y+v6IGU57O75a6i5pyNNDAwOC05MzktMzY1IiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiA1LA0KICAgICAgICAgIHN0YXR1c05hbWU6ICLlt7LmiJDkuqQiLA0KICAgICAgICAgIGRlc2M6ICLkvb/nlKjov4fnqIvkuK3mnInku7vkvZXpl67popjvvIzlj6/ogZTns7vlrqLmnI00MDA4LTkzOS0zNjUiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6IDYsDQogICAgICAgICAgc3RhdHVzTmFtZTogIuW+hee7rei0uSIsDQogICAgICAgICAgZGVzYzogIuivt+WwveW/q+e7rei0ue+8jOS7peWFjeW9seWTjeato+W4uOS9v+eUqCIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogNywNCiAgICAgICAgICBzdGF0dXNOYW1lOiAi5bey5YWz6ZetIiwNCiAgICAgICAgICBkZXNjOiAi5aaC5a+56K6i5Y2V5pyJ55aR6Zeu77yM5Y+v6IGU57O75a6i5pyNNDAwOC05MzktMzY1IiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiA4LA0KICAgICAgICAgIHN0YXR1c05hbWU6ICLmlK/ku5jkuK0iLA0KICAgICAgICAgIGRlc2M6ICLlpoLlr7norqLljZXmnInnlpHpl67vvIzlj6/ogZTns7vlrqLmnI00MDA4LTkzOS0zNjUiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6IDksDQogICAgICAgICAgc3RhdHVzTmFtZTogIuW3suWPlua2iCIsDQogICAgICAgICAgZGVzYzogIuWmguWvueiuouWNleacieeWkemXru+8jOWPr+iBlOezu+WuouacjTQwMDgtOTM5LTM2NSIsDQogICAgICAgIH0sDQogICAgICBdLA0KICAgICAgaW52b2ljZVZpc2libGU6IGZhbHNlLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxldCBpZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkOw0KICAgICAgb3JkZXJEZXRhaWwoaWQpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy50YWJsZURhdGEgPSBbXTsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmluZm8gPSByZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLnRhYmxlRGF0YS5wdXNoKHJlcy5kYXRhKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlKHBhZ2VOdW0pIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IHBhZ2VOdW07DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIHRyeW91dChpdGVtKSB7DQogICAgICBjb25zb2xlLmxvZyhpdGVtKTsNCiAgICAgIGlmIChpdGVtLmFwcE5hbWUgPT0gIuS6keerr+eglOWPkSIpIHsNCiAgICAgICAgbGV0IHVybDsNCiAgICAgICAgbGV0IGhvc3RuYW1lOw0KICAgICAgICB2YXIgcmVzdWx0Ow0KICAgICAgICBob3N0bmFtZSA9ICIgaHR0cHM6Ly95dW5kdWFueWFuZmEubmluZ21lbmdkb3UuY29tL2xvZ2luICI7DQogICAgICAgIHJlc3VsdCA9IGVuY29kZVVSSUNvbXBvbmVudChob3N0bmFtZSk7DQogICAgICAgIHVybCA9ICJodHRwczovL3Nzby5uaW5nbWVuZ2RvdS5jb20vc2luZ2xlL2xvZ2luP3JldHVyblVybD0iICsgcmVzdWx0Ow0KICAgICAgICB3aW5kb3cub3Blbih1cmwsICJfYmxhbmsiKTsNCiAgICAgIH0gZWxzZSBpZiAoaXRlbS5hcHBOYW1lID09ICLmqqzosYbkupHkvpvlupTpk77nrqHnkIbns7vnu58iKSB7DQogICAgICB9IGVsc2UgaWYgKGl0ZW0uYXBwTmFtZSA9PSAi6ZuG6YeH5bmz5Y+wIikgew0KICAgICAgICB3aW5kb3cub3BlbigiaHR0cHM6Ly9tZHkubmluZ21lbmdkb3UuY29tIik7DQogICAgICB9IGVsc2UgaWYgKGl0ZW0uYXBwTmFtZSA9PSAi5LqRTUVTIikgew0KICAgICAgICBsZXQgdXNlcmlkID0gIjE4NjYwMjgzNzI2IjsNCiAgICAgICAgY29uc29sZS5sb2codXNlcmlkKTsNCiAgICAgICAgbGV0IGpzb25EYXRhID0geyBVOiB1c2VyaWQsIFA6ICIxMmEiLCBBOiAiYWNiIiB9Ow0KICAgICAgICBjb25zb2xlLmxvZyhqc29uRGF0YSk7DQogICAgICAgIGNvbnN0IGVuY29kZWREYXRhID0gYnRvYShKU09OLnN0cmluZ2lmeShqc29uRGF0YSkpOw0KICAgICAgICBjb25zb2xlLmxvZyhlbmNvZGVkRGF0YSk7DQogICAgICAgIHdpbmRvdy5vcGVuKA0KICAgICAgICAgICJodHRwOi8vbWVzLm5pbmdtZW5nZG91LmNvbS9kZWZhdWx0Lmh0bWw/cGFybT0iICsgZW5jb2RlZERhdGEsDQogICAgICAgICAgIl9ibGFuayINCiAgICAgICAgKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHdpbmRvdy5vcGVuKCIvLyIgKyBpdGVtLndlYmV4cGVyaWVuY2VVcmwsICJfYmxhbmsiKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGNhbmNlbE9yZGVyKGlkKSB7DQogICAgICB0aGlzLiRjb25maXJtKCLorqLljZXlj5bmtojlkI7ml6Dms5XmgaLlpI3vvIzor7fosKjmhY7mk43kvZwhIiwgIuWPlua2iOiuouWNlSIsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIGNhbmNlbE9yZGVyKGlkKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfISIpOw0KICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgYXBwbHlJbnZvaWNlKCkgew0KICAgICAgdGhpcy5nZXRJbnZvaWNlRGF0YSgpOw0KICAgIH0sDQogICAgZ2V0SW52b2ljZURhdGEoKSB7DQogICAgICBpbnZvaWNlTGlzdCgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuaW52b2ljZURhdGEgPSByZXMuZGF0YTsNCiAgICAgICAgICB0aGlzLmludm9pY2VWaXNpYmxlID0gdHJ1ZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBjb25maXJtUmVjZWlwdChpZCkgew0KICAgICAgdGhpcy4kY29uZmlybSgi56Gu6K6k5ZCO6K6i5Y2V54q25oCB5peg5rOV5Y+Y5pu077yM56Gu6K6k5pS26LSn5ZCX77yfIiwgIuehruiupOaUtui0pyIsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIGxldCBkYXRhID0gew0KICAgICAgICAgICAgaWQsDQogICAgICAgICAgICBvcmRlclN0YXR1czogNSwNCiAgICAgICAgICB9Ow0KICAgICAgICAgIG1vZGlmeVN0YXR1cyhkYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfISIpOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICBsZXQgZGF0YSA9IHsNCiAgICAgICAgaW52b2ljZU1lZGl1bTogIjEiLA0KICAgICAgICBpbnZvaWNlVHlwZTogIjEiLA0KICAgICAgICBpc3N1ZVR5cGU6ICIxIiwNCiAgICAgICAgaW52b2ljZUhlYWRlcjogdGhpcy5pbnZvaWNlRGF0YS5jb21wYW55TmFtZSwNCiAgICAgICAgZHV0eVBhcmFncmFwaDogdGhpcy5pbnZvaWNlRGF0YS5kdXR5UGFyYWdyYXBoLA0KICAgICAgICBlbWFpbDogdGhpcy5pbnZvaWNlRGF0YS5lbWFpbCwNCiAgICAgICAgb3JkZXJJZDogdGhpcy5pbmZvLmlkLA0KICAgICAgICBzZW5kVG86IHRoaXMuaW52b2ljZURhdGEudXNlcklkLA0KICAgICAgfTsNCiAgICAgIGFwcGx5SW52b2ljZShkYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmludm9pY2VWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLmk43kvZzmiJDlip8hIik7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgY2FuY2VsRGlhbG9nKCkgew0KICAgICAgdGhpcy5pbnZvaWNlVmlzaWJsZSA9IGZhbHNlOw0KICAgIH0sDQogICAgZ29CYWNrKCkgew0KICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8MA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/system/user/mySubscriptions", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\">\r\n          <div class=\"content_top\">\r\n            <div class=\"orderStatus\">\r\n              <div class=\"statusName\" v-if=\"info.orderStatus\">\r\n                {{\r\n                  orderForm.filter((item) => item.value == info.orderStatus)[0]\r\n                    .statusName\r\n                }}\r\n              </div>\r\n              <div class=\"desc\" v-if=\"info.orderStatus\">\r\n                {{\r\n                  orderForm.filter((item) => item.value == info.orderStatus)[0]\r\n                    .desc\r\n                }}\r\n              </div>\r\n            </div>\r\n            <div class=\"amountMoney\">\r\n              <span style=\"color: rgb(173, 173, 173)\">订单金额:</span>\r\n              <span style=\"margin-left: 10px\">¥ {{ info.price }}</span>\r\n            </div>\r\n            <!-- 待支付 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 1\">\r\n              <div>\r\n                <div class=\"buttonStyle\">去支付</div>\r\n                <div style=\"margin-top: 10px\">\r\n                  <span\r\n                    style=\"color: #21c9b8; cursor: pointer\"\r\n                    @click=\"cancelOrder(info.id)\"\r\n                    >取消订单</span\r\n                  >\r\n                  <span\r\n                    style=\"margin-left: 20px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"tryout(info)\"\r\n                    >前往试用</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 待发货 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 2\">\r\n              <div>\r\n                <div>\r\n                  <span\r\n                    style=\"margin-left: 20px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"tryout(info)\"\r\n                    >前往试用</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 已发货 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 4\">\r\n              <div>\r\n                <div>\r\n                  <span\r\n                    style=\"color: #21c9b8; cursor: pointer\"\r\n                    @click=\"applyInvoice(info.id)\"\r\n                    >申请开票</span\r\n                  >\r\n                  <span\r\n                    style=\"margin-left: 20px; color: #21c9b8; cursor: pointer\"\r\n                    @click=\"confirmReceipt(info.id)\"\r\n                    >确认收货</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 已成交 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 5\">\r\n              <div>\r\n                <div>\r\n                  <span style=\"color: #21c9b8; cursor: pointer\">已开票</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- 待续费 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 6\">\r\n              <div>\r\n                <div>\r\n                  <span style=\"color: #21c9b8; cursor: pointer\">去支付</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"content_bottom\">\r\n            <div>\r\n              <el-descriptions title=\"订单信息\" :column=\"2\">\r\n                <el-descriptions-item label=\"订单编号\">{{\r\n                  info.id\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"下单时间\">{{\r\n                  info.orderDate\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"应用提供\">{{\r\n                  info.supply\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"付款时间\">\r\n                  <el-tag size=\"small\">{{\r\n                    info.payTime ? parseTime(info.payTime) : \"--\"\r\n                  }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"应用编号\">{{\r\n                  info.appCode\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"发货时间\">{{\r\n                  info.deliverTime\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"付款方式\">{{\r\n                  info.payWay\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"成交时间\">{{\r\n                  info.makeTime\r\n                }}</el-descriptions-item>\r\n              </el-descriptions>\r\n            </div>\r\n            <div style=\"margin-top: 30px\">\r\n              <el-table :data=\"tableData\" style=\"width: 100%\">\r\n                <el-table-column prop=\"remark\" label=\"产品标题\">\r\n                </el-table-column>\r\n                <el-table-column label=\"产品图片\">\r\n                  <template slot-scope=\"scope\">\r\n                    <img\r\n                      style=\"width: 100px; height: 100px\"\r\n                      :src=\"scope.row.appLogo\"\r\n                      alt=\"\"\r\n                    />\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column prop=\"appCategory\" label=\"产品类型\">\r\n                </el-table-column>\r\n                <el-table-column label=\"规格信息\">\r\n                  <template slot-scope=\"scoped\">\r\n                    <!-- <div>规格: {{ scoped.row.spec }}</div> -->\r\n                    <div>\r\n                      可用时长:\r\n                      {{ scoped.row.validTime == \"1\" ? \"一年\" : \"永久\" }}\r\n                    </div>\r\n                    <div>可用人数: 不限</div>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"有效时间\">\r\n                  <template slot-scope=\"scope\">\r\n                    {{\r\n                      scope.row.expirationTime\r\n                        ? parseTime(scope.row.expirationTime)\r\n                        : \"--\"\r\n                    }}\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </div>\r\n          <div class=\"btnStyle\">\r\n            <el-button @click=\"goBack\">返回</el-button>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      title=\"开票信息\"\r\n      :visible.sync=\"invoiceVisible\"\r\n      width=\"750px\"\r\n      append-to-body\r\n    >\r\n      <el-form :model=\"invoiceData\" label-width=\"80px\">\r\n        <el-form-item label=\"发票类型:\" prop=\"realName\">\r\n          {{ invoiceData.invoiceType == 1 ? \"专票\" : \"普票\" }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司名称:\" prop=\"phonenumber\">\r\n          {{ invoiceData.companyName }}\r\n        </el-form-item>\r\n        <el-form-item label=\"税号:\" prop=\"weixin\">\r\n          {{ invoiceData.dutyParagraph }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司地址:\" prop=\"email\">\r\n          {{ invoiceData.address }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司电话:\" prop=\"email\">\r\n          {{ invoiceData.phone }}\r\n        </el-form-item>\r\n        <el-form-item label=\"开户银行:\" prop=\"email\">\r\n          {{ invoiceData.openAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"银行账号:\" prop=\"email\">\r\n          {{ invoiceData.bankAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱地址:\" prop=\"email\">\r\n          {{ invoiceData.email }}\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  orderDetail,\r\n  cancelOrder,\r\n  invoiceList,\r\n  applyInvoice,\r\n  modifyStatus,\r\n} from \"@/api/system/user\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\n// import { getToken } from \"@/utils/auth\";\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      flag: \"0\",\r\n      tableData: [],\r\n      info: {},\r\n      invoiceData: {},\r\n      orderForm: [\r\n        {\r\n          value: 1,\r\n          statusName: \"待支付\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 2,\r\n          statusName: \"待发货\",\r\n          desc: \"平台将于2023-08-04日前发货，感谢您的支持!如您对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 3,\r\n          statusName: \"支付失败\",\r\n          desc: \"订单支付失败，如您对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 4,\r\n          statusName: \"已发货\",\r\n          desc: \"使用过程中有任何问题，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 5,\r\n          statusName: \"已成交\",\r\n          desc: \"使用过程中有任何问题，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 6,\r\n          statusName: \"待续费\",\r\n          desc: \"请尽快续费，以免影响正常使用\",\r\n        },\r\n        {\r\n          value: 7,\r\n          statusName: \"已关闭\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 8,\r\n          statusName: \"支付中\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 9,\r\n          statusName: \"已取消\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n      ],\r\n      invoiceVisible: false,\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      let id = this.$route.query.id;\r\n      orderDetail(id).then((res) => {\r\n        this.loading = false;\r\n        this.tableData = [];\r\n        if (res.code === 200) {\r\n          this.info = res.data;\r\n          this.tableData.push(res.data);\r\n        }\r\n      });\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    tryout(item) {\r\n      console.log(item);\r\n      if (item.appName == \"云端研发\") {\r\n        let url;\r\n        let hostname;\r\n        var result;\r\n        hostname = \" https://yunduanyanfa.ningmengdou.com/login \";\r\n        result = encodeURIComponent(hostname);\r\n        url = \"https://sso.ningmengdou.com/single/login?returnUrl=\" + result;\r\n        window.open(url, \"_blank\");\r\n      } else if (item.appName == \"檬豆云供应链管理系统\") {\r\n      } else if (item.appName == \"集采平台\") {\r\n        window.open(\"https://mdy.ningmengdou.com\");\r\n      } else if (item.appName == \"云MES\") {\r\n        let userid = \"18660283726\";\r\n        console.log(userid);\r\n        let jsonData = { U: userid, P: \"12a\", A: \"acb\" };\r\n        console.log(jsonData);\r\n        const encodedData = btoa(JSON.stringify(jsonData));\r\n        console.log(encodedData);\r\n        window.open(\r\n          \"http://mes.ningmengdou.com/default.html?parm=\" + encodedData,\r\n          \"_blank\"\r\n        );\r\n      } else {\r\n        window.open(\"//\" + item.webexperienceUrl, \"_blank\");\r\n      }\r\n    },\r\n    cancelOrder(id) {\r\n      this.$confirm(\"订单取消后无法恢复，请谨慎操作!\", \"取消订单\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          cancelOrder(id).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.$router.go(-1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    applyInvoice() {\r\n      this.getInvoiceData();\r\n    },\r\n    getInvoiceData() {\r\n      invoiceList().then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceData = res.data;\r\n          this.invoiceVisible = true;\r\n        }\r\n      });\r\n    },\r\n    confirmReceipt(id) {\r\n      this.$confirm(\"确认后订单状态无法变更，确认收货吗？\", \"确认收货\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          let data = {\r\n            id,\r\n            orderStatus: 5,\r\n          };\r\n          modifyStatus(data).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    submitForm() {\r\n      let data = {\r\n        invoiceMedium: \"1\",\r\n        invoiceType: \"1\",\r\n        issueType: \"1\",\r\n        invoiceHeader: this.invoiceData.companyName,\r\n        dutyParagraph: this.invoiceData.dutyParagraph,\r\n        email: this.invoiceData.email,\r\n        orderId: this.info.id,\r\n        sendTo: this.invoiceData.userId,\r\n      };\r\n      applyInvoice(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceVisible = false;\r\n          this.$message.success(\"操作成功!\");\r\n        }\r\n      });\r\n    },\r\n    cancelDialog() {\r\n      this.invoiceVisible = false;\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  .content_top {\r\n    display: flex;\r\n    width: 100%;\r\n    height: 120px;\r\n    align-items: center;\r\n    .orderStatus {\r\n      width: 30%;\r\n      text-align: center;\r\n      .statusName {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n      }\r\n      .desc {\r\n        font-size: 14px;\r\n        color: rgb(173, 173, 173);\r\n        margin-top: 30px;\r\n      }\r\n    }\r\n    .amountMoney {\r\n      width: 40%;\r\n      text-align: center;\r\n    }\r\n    .button_content {\r\n      width: 20%;\r\n      text-align: center;\r\n      .buttonStyle {\r\n        height: 50px;\r\n        background: #21c9b8;\r\n        line-height: 50px;\r\n        color: #ffffff;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n  .content_bottom {\r\n    margin-top: 20px;\r\n    padding: 20px;\r\n    width: 100%;\r\n  }\r\n  .btnStyle {\r\n    text-align: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}