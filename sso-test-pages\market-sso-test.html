<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>从系统SSO测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; background: #28a745; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #218838; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 从系统（Market）SSO测试页面</h1>
        
        <div class="test-section info">
            <h3>📋 测试说明</h3>
            <p>本页面用于测试从系统的SSO客户端功能。请按顺序执行以下测试：</p>
            <ol>
                <li>检查SSO状态</li>
                <li>获取主系统登录地址</li>
                <li>执行SSO登录</li>
                <li>检查用户信息</li>
                <li>测试用户同步</li>
                <li>测试SSO登出</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>1. 检查SSO状态</h3>
            <button onclick="checkSSOStatus()">检查SSO状态</button>
            <div id="statusResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>2. 获取主系统登录地址</h3>
            <input type="text" id="redirectUrl" placeholder="登录成功后跳转地址（可选）" style="width: 300px; padding: 8px;">
            <button onclick="getMainSystemLoginUrl()">获取主系统登录地址</button>
            <div id="loginUrlResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>3. 跳转到主系统登录</h3>
            <button onclick="redirectToMainSystem()">跳转到主系统</button>
            <p><small>点击后将跳转到主系统进行SSO登录</small></p>
        </div>

        <div class="test-section">
            <h3>4. SSO登录回调测试</h3>
            <p>模拟SSO登录回调（仅用于测试）：</p>
            <input type="text" id="testCode" placeholder="测试授权码" style="width: 200px; padding: 8px;">
            <input type="text" id="testState" placeholder="测试状态参数" style="width: 200px; padding: 8px;">
            <button onclick="testSSOCallback()">模拟回调</button>
            <div id="callbackResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>5. 用户信息同步测试</h3>
            <button onclick="testUserSync()">测试用户同步</button>
            <div id="syncResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>6. SSO登出</h3>
            <button onclick="ssoLogout()">SSO登出</button>
            <div id="logoutResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 10px; margin-top: 10px;"></div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8081'; // 从系统地址
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> <span style="color: ${type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue'};">${message}</span>`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }

        async function makeRequest(url, options = {}) {
            try {
                log(`发送请求: ${options.method || 'GET'} ${url}`);
                const response = await fetch(url, {
                    credentials: 'include',
                    ...options
                });
                const data = await response.json();
                log(`响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
                return { success: false, error: error.message };
            }
        }

        async function checkSSOStatus() {
            const result = await makeRequest(`${BASE_URL}/sso/status`);
            const resultDiv = document.getElementById('statusResult');
            
            if (result.success) {
                resultDiv.innerHTML = `<pre>${JSON.stringify(result.data, null, 2)}</pre>`;
                resultDiv.className = 'result success';
                log('SSO状态检查成功', 'success');
            } else {
                resultDiv.innerHTML = `<span style="color: red;">检查失败: ${result.error || '未知错误'}</span>`;
                resultDiv.className = 'result error';
                log('SSO状态检查失败', 'error');
            }
        }

        async function getMainSystemLoginUrl() {
            const redirectUrl = document.getElementById('redirectUrl').value;
            const url = `${BASE_URL}/sso/loginUrl${redirectUrl ? '?redirect=' + encodeURIComponent(redirectUrl) : ''}`;
            const result = await makeRequest(url);
            const resultDiv = document.getElementById('loginUrlResult');
            
            if (result.success) {
                const loginUrl = result.data.data.loginUrl;
                resultDiv.innerHTML = `
                    <p><strong>主系统登录地址:</strong></p>
                    <p><a href="${loginUrl}" target="_blank">${loginUrl}</a></p>
                    <button onclick="window.open('${loginUrl}', '_blank')">在新窗口中打开</button>
                `;
                resultDiv.className = 'result success';
                log('获取主系统登录地址成功', 'success');
            } else {
                resultDiv.innerHTML = `<span style="color: red;">获取失败: ${result.error || '未知错误'}</span>`;
                resultDiv.className = 'result error';
                log('获取主系统登录地址失败', 'error');
            }
        }

        async function redirectToMainSystem() {
            const redirectUrl = document.getElementById('redirectUrl').value || window.location.href;
            log('准备跳转到主系统登录页面');
            window.location.href = `${BASE_URL}/sso/redirect-to-main?redirect=${encodeURIComponent(redirectUrl)}`;
        }

        async function testSSOCallback() {
            const code = document.getElementById('testCode').value;
            const state = document.getElementById('testState').value;
            
            if (!code) {
                alert('请输入测试授权码');
                return;
            }
            
            const url = `${BASE_URL}/sso/callback?code=${encodeURIComponent(code)}${state ? '&state=' + encodeURIComponent(state) : ''}`;
            log('模拟SSO回调测试');
            window.location.href = url;
        }

        async function testUserSync() {
            const testUserInfo = {
                userId: "test123",
                username: "testuser",
                nickName: "测试用户",
                email: "<EMAIL>",
                phonenumber: "13800138000",
                sex: "0",
                avatar: ""
            };
            
            const result = await makeRequest(`${BASE_URL}/sso/sync-user`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testUserInfo)
            });
            
            const resultDiv = document.getElementById('syncResult');
            
            if (result.success) {
                resultDiv.innerHTML = `<span style="color: green;">用户同步测试成功</span>`;
                resultDiv.className = 'result success';
                log('用户同步测试成功', 'success');
            } else {
                resultDiv.innerHTML = `<span style="color: red;">同步失败: ${result.error || '未知错误'}</span>`;
                resultDiv.className = 'result error';
                log('用户同步测试失败', 'error');
            }
        }

        async function ssoLogout() {
            const result = await makeRequest(`${BASE_URL}/sso/logout`, { method: 'POST' });
            const resultDiv = document.getElementById('logoutResult');
            
            if (result.success) {
                resultDiv.innerHTML = `<span style="color: green;">登出成功</span>`;
                resultDiv.className = 'result success';
                log('SSO登出成功', 'success');
                // 刷新页面状态
                setTimeout(() => {
                    checkSSOStatus();
                }, 1000);
            } else {
                resultDiv.innerHTML = `<span style="color: red;">登出失败: ${result.error || '未知错误'}</span>`;
                resultDiv.className = 'result error';
                log('SSO登出失败', 'error');
            }
        }

        // 页面加载时自动检查状态
        window.onload = function() {
            log('页面加载完成，开始初始化测试');
            checkSSOStatus();
        };
    </script>
</body>
</html>
