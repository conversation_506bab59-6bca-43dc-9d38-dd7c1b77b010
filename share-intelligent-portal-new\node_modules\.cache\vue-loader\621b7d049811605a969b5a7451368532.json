{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\classicCase\\caseDetail.vue?vue&type=template&id=5275a888&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\classicCase\\caseDetail.vue", "mtime": 1750311962925}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInJlc291cmNlLWhhbGwtZGV0YWlsLWNvbnRhaW5lciIgfSwgWwogICAgX3ZtLl9tKDApLAogICAgX3ZtLl9tKDEpLAogICAgX2MoCiAgICAgICJkaXYiLAogICAgICB7CiAgICAgICAgZGlyZWN0aXZlczogWwogICAgICAgICAgewogICAgICAgICAgICBuYW1lOiAibG9hZGluZyIsCiAgICAgICAgICAgIHJhd05hbWU6ICJ2LWxvYWRpbmciLAogICAgICAgICAgICB2YWx1ZTogX3ZtLmxvYWRpbmcsCiAgICAgICAgICAgIGV4cHJlc3Npb246ICJsb2FkaW5nIiwKICAgICAgICAgIH0sCiAgICAgICAgXSwKICAgICAgICBzdGF0aWNDbGFzczogInJlc291cmNlLWhhbGwtZGV0YWlsLWNvbnRlbnQiLAogICAgICB9LAogICAgICBbCiAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJyZXNvdXJjZS1oYWxsLWRldGFpbC1ib3giIH0sIFsKICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAicmVzb3VyY2UtaGFsbC1kZXRhaWwtYm94LXRpdGxlIiB9LCBbCiAgICAgICAgICAgIF92bS5fdigiICIgKyBfdm0uX3MoX3ZtLmRhdGEubmFtZSkgKyAiICIpLAogICAgICAgICAgXSksCiAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInJlc291cmNlLWhhbGwtZGV0YWlsLWRlc2NyaXB0aW9uIiB9LCBbCiAgICAgICAgICAgIF92bS5fbSgyKSwKICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJkZXNjcmlwdGlvbi1jb250ZW50IiB9LCBbCiAgICAgICAgICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJkZXNjcmlwdGlvbi10ZXh0IHFsLWVkaXRvciIgfSwgWwogICAgICAgICAgICAgICAgX3ZtLl92KCIgIiArIF92bS5fcyhfdm0uZGF0YS5pbnRyb2R1Y3Rpb24pICsgIiAiKSwKICAgICAgICAgICAgICBdKSwKICAgICAgICAgICAgXSksCiAgICAgICAgICBdKSwKICAgICAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAicmVzb3VyY2UtaGFsbC1kZXRhaWwtZGVzY3JpcHRpb24iIH0sIFsKICAgICAgICAgICAgX3ZtLl9tKDMpLAogICAgICAgICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImRlc2NyaXB0aW9uLWNvbnRlbnQiIH0sIFsKICAgICAgICAgICAgICBfYygiZGl2IiwgewogICAgICAgICAgICAgICAgc3RhdGljQ2xhc3M6ICJkZXNjcmlwdGlvbi10ZXh0IHFsLWVkaXRvciIsCiAgICAgICAgICAgICAgICBkb21Qcm9wczogeyBpbm5lckhUTUw6IF92bS5fcyhfdm0uZGF0YS5kZXRhaWwpIH0sCiAgICAgICAgICAgICAgfSksCiAgICAgICAgICAgIF0pLAogICAgICAgICAgXSksCiAgICAgICAgXSksCiAgICAgIF0KICAgICksCiAgXSkKfQp2YXIgc3RhdGljUmVuZGVyRm5zID0gWwogIGZ1bmN0aW9uICgpIHsKICAgIHZhciBfdm0gPSB0aGlzCiAgICB2YXIgX2ggPSBfdm0uJGNyZWF0ZUVsZW1lbnQKICAgIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogICAgcmV0dXJuIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAicmVzb3VyY2UtaGFsbC1kZXRhaWwtYmFubmVyIiB9LCBbCiAgICAgIF9jKCJpbWciLCB7CiAgICAgICAgYXR0cnM6IHsKICAgICAgICAgIHNyYzogcmVxdWlyZSgiLi4vLi4vYXNzZXRzL2NsYXNzaWNDYXNlL2NsYXNzaWNDYXNlQmFubmVyLnBuZyIpLAogICAgICAgICAgYWx0OiAiIiwKICAgICAgICB9LAogICAgICB9KSwKICAgIF0pCiAgfSwKICBmdW5jdGlvbiAoKSB7CiAgICB2YXIgX3ZtID0gdGhpcwogICAgdmFyIF9oID0gX3ZtLiRjcmVhdGVFbGVtZW50CiAgICB2YXIgX2MgPSBfdm0uX3NlbGYuX2MgfHwgX2gKICAgIHJldHVybiBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInJlc291cmNlLWhhbGwtZGV0YWlsLXRpdGxlLWJveCIgfSwgWwogICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInJlc291cmNlLWhhbGwtZGV0YWlsLWRpdmlkZXIiIH0pLAogICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogInJlc291cmNlLWhhbGwtZGV0YWlsLXRpdGxlIiB9LCBbCiAgICAgICAgX3ZtLl92KCLmoYjkvovor6bmg4UiKSwKICAgICAgXSksCiAgICAgIF9jKCJkaXYiLCB7IHN0YXRpY0NsYXNzOiAicmVzb3VyY2UtaGFsbC1kZXRhaWwtZGl2aWRlciIgfSksCiAgICBdKQogIH0sCiAgZnVuY3Rpb24gKCkgewogICAgdmFyIF92bSA9IHRoaXMKICAgIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogICAgdmFyIF9jID0gX3ZtLl9zZWxmLl9jIHx8IF9oCiAgICByZXR1cm4gX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJkZXNjcmlwdGlvbi10aXRsZS1ib3giIH0sIFsKICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJkZXNjcmlwdGlvbi1kaXZpZGVyIiB9KSwKICAgICAgX2MoImRpdiIsIHsgc3RhdGljQ2xhc3M6ICJkZXNjcmlwdGlvbi10aXRsZSIgfSwgW192bS5fdigi5a6i5oi355eb54K5IildKSwKICAgIF0pCiAgfSwKICBmdW5jdGlvbiAoKSB7CiAgICB2YXIgX3ZtID0gdGhpcwogICAgdmFyIF9oID0gX3ZtLiRjcmVhdGVFbGVtZW50CiAgICB2YXIgX2MgPSBfdm0uX3NlbGYuX2MgfHwgX2gKICAgIHJldHVybiBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImRlc2NyaXB0aW9uLXRpdGxlLWJveCIgfSwgWwogICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImRlc2NyaXB0aW9uLWRpdmlkZXIiIH0pLAogICAgICBfYygiZGl2IiwgeyBzdGF0aWNDbGFzczogImRlc2NyaXB0aW9uLXRpdGxlIiB9LCBbCiAgICAgICAgX3ZtLl92KCLpg6jnvbLmlrnmoYgm5a6e5pa95pWI5p6cIiksCiAgICAgIF0pLAogICAgXSkKICB9LApdCnJlbmRlci5fd2l0aFN0cmlwcGVkID0gdHJ1ZQoKZXhwb3J0IHsgcmVuZGVyLCBzdGF0aWNSZW5kZXJGbnMgfQ=="}]}