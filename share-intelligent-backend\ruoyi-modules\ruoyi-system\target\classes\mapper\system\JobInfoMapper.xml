<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.JobInfoMapper">
    
    <resultMap type="JobInfo" id="JobInfoResult">
        <result property="id"    column="id"    />
        <result property="positionName"    column="position_name"    />
        <result property="salaryRange"    column="salary_range"    />
        <result property="salaryMin"    column="salary_min"    />
        <result property="salaryMax"    column="salary_max"    />
        <result property="ageLimit"    column="age_limit"    />
        <result property="company"    column="company"    />
        <result property="location"    column="location"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="requirements"    column="requirements"    />
        <result property="responsibilities"    column="responsibilities"    />
        <result property="otherLimits"    column="other_limits"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createBy"    column="create_by"    />
    </resultMap>

    <sql id="selectJobInfoVo">
        select id, position_name, salary_range, salary_min, salary_max, age_limit, company, location, contact_phone, requirements, responsibilities, other_limits, status, create_time, update_time,  update_by, create_by from job_info
    </sql>

    <select id="selectJobInfoList" parameterType="JobInfo" resultMap="JobInfoResult">
        <include refid="selectJobInfoVo"/>
        <where>  
            <if test="positionName != null  and positionName != ''"> and position_name like concat('%', #{positionName}, '%')</if>
            <if test="salaryRange != null  and salaryRange != ''"> and salary_range = #{salaryRange}</if>
            <if test="salaryMin != null "> and salary_min = #{salaryMin}</if>
            <if test="salaryMax != null "> and salary_max = #{salaryMax}</if>
            <if test="ageLimit != null  and ageLimit != ''"> and age_limit = #{ageLimit}</if>
            <if test="company != null  and company != ''"> and company = #{company}</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="requirements != null  and requirements != ''"> and requirements = #{requirements}</if>
            <if test="responsibilities != null  and responsibilities != ''"> and responsibilities = #{responsibilities}</if>
            <if test="otherLimits != null  and otherLimits != ''"> and other_limits = #{otherLimits}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="updateBy != null and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="createBy != null and createBy != ''"> and create_by = #{createBy}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectJobInfoById" parameterType="Long" resultMap="JobInfoResult">
        <include refid="selectJobInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertJobInfo" parameterType="JobInfo" useGeneratedKeys="true" keyProperty="id">
        insert into job_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="positionName != null and positionName != ''">position_name,</if>
            <if test="salaryRange != null and salaryRange != ''">salary_range,</if>
            <if test="salaryMin != null">salary_min,</if>
            <if test="salaryMax != null">salary_max,</if>
            <if test="ageLimit != null">age_limit,</if>
            <if test="company != null and company != ''">company,</if>
            <if test="location != null and location != ''">location,</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone,</if>
            <if test="requirements != null">requirements,</if>
            <if test="responsibilities != null">responsibilities,</if>
            <if test="otherLimits != null">other_limits,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="createBy != null">create_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="positionName != null and positionName != ''">#{positionName},</if>
            <if test="salaryRange != null and salaryRange != ''">#{salaryRange},</if>
            <if test="salaryMin != null">#{salaryMin},</if>
            <if test="salaryMax != null">#{salaryMax},</if>
            <if test="ageLimit != null">#{ageLimit},</if>
            <if test="company != null and company != ''">#{company},</if>
            <if test="location != null and location != ''">#{location},</if>
            <if test="contactPhone != null and contactPhone != ''">#{contactPhone},</if>
            <if test="requirements != null">#{requirements},</if>
            <if test="responsibilities != null">#{responsibilities},</if>
            <if test="otherLimits != null">#{otherLimits},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="createBy != null">#{createBy},</if>
         </trim>
    </insert>

    <update id="updateJobInfo" parameterType="JobInfo">
        update job_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="positionName != null and positionName != ''">position_name = #{positionName},</if>
            <if test="salaryRange != null and salaryRange != ''">salary_range = #{salaryRange},</if>
            <if test="salaryMin != null">salary_min = #{salaryMin},</if>
            <if test="salaryMax != null">salary_max = #{salaryMax},</if>
            <if test="ageLimit != null">age_limit = #{ageLimit},</if>
            <if test="company != null and company != ''">company = #{company},</if>
            <if test="location != null and location != ''">location = #{location},</if>
            <if test="contactPhone != null and contactPhone != ''">contact_phone = #{contactPhone},</if>
            <if test="requirements != null">requirements = #{requirements},</if>
            <if test="responsibilities != null">responsibilities = #{responsibilities},</if>
            <if test="otherLimits != null">other_limits = #{otherLimits},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteJobInfoById" parameterType="Long">
        delete from job_info where id = #{id}
    </delete>

    <delete id="deleteJobInfoByIds" parameterType="String">
        delete from job_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>