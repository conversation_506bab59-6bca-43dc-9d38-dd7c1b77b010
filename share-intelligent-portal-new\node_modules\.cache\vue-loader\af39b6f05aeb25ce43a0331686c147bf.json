{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\supplyForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\supplyForm.vue", "mtime": 1750311962955}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBrZXl3b3JkTGlzdCwgc3VwcGx5QWRkIH0gZnJvbSAiQC9hcGkvemhtIjsNCmltcG9ydCBjYWNoZSBmcm9tICJAL3BsdWdpbnMvY2FjaGUiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJzdXBwbHlGb3JtIiwNCiAgZGljdHM6IFsNCiAgICAic3VwcGx5X3R5cGUiLA0KICAgICJ0ZWNobm9sb2d5X3R5cGUiLA0KICAgICJjb29wZXJhdGlvbl9tb2RlIiwNCiAgICAicHJvZHVjdF9zdGFnZSIsDQogICAgImFwcGxpY2F0aW9uX2FyZWEiLA0KICBdLA0KICBkYXRhKCkgew0KICAgIGNvbnN0IHsgdXNlciB9ID0gdGhpcy4kc3RvcmUuc3RhdGU7DQogICAgcmV0dXJuIHsNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAga2V5d29yZExvYWRpbmc6IGZhbHNlLA0KICAgICAgZm9ybTogew0KICAgICAgICAvLyDkvpvnu5nnsbvlnosNCiAgICAgICAgc3VwcGx5VHlwZTogW10sDQogICAgICAgIC8vIOmcgOaxguagh+mimA0KICAgICAgICBzdXBwbHlOYW1lOiB1bmRlZmluZWQsDQogICAgICAgIC8vIOaPj+i/sA0KICAgICAgICBzdW1tYXJ5OiB1bmRlZmluZWQsDQogICAgICAgIC8vIOWFs+mUruivjQ0KICAgICAgICBrZXl3b3JkczogW10sDQogICAgICAgIC8vIOaKgOacr+exu+WIqw0KICAgICAgICB0ZWNobm9sb2d5VHlwZTogW10sDQogICAgICAgIC8vIOW6lOeUqOmihuWfnw0KICAgICAgICBhcHBsaWNhdGlvbkFyZWE6IFtdLA0KICAgICAgICAvLyDkuqflk4Hlm77niYcNCiAgICAgICAgcHJvZHVjdFBob3RvOiBbXSwNCiAgICAgICAgLy8g6ZmE5Lu2DQogICAgICAgIGVuY2xvc3VyZTogW10sDQogICAgICAgIC8vIOS6p+WTgemYtuautQ0KICAgICAgICBwcm9kdWN0U3RhZ2U6IHVuZGVmaW5lZCwNCiAgICAgICAgLy8g5ZCI5L2c5pa55byPDQogICAgICAgIGNvb3BlcmF0aW9uTW9kZTogdW5kZWZpbmVkLA0KICAgICAgICAvLyDlhazlj7jlkI3np7ANCiAgICAgICAgY29tcGFueU5hbWU6IHVzZXIuY29tcGFueU5hbWUgfHwgIuafoOaqrOixhiIsDQogICAgICAgIC8vIOiBlOezu+S6ug0KICAgICAgICBjb250YWN0c05hbWU6IHVzZXIubmFtZSwNCiAgICAgICAgLy8g6IGU57O755S16K+dDQogICAgICAgIGNvbnRhY3RzTW9iaWxlOiB1c2VyLnRlbCwNCiAgICAgICAgcHVibGlzaGVyTmFtZTogdXNlci5uYW1lLA0KICAgICAgICBwdWJsaXNoZXJNb2JpbGU6IHVzZXIudGVsLA0KICAgICAgICBidXNpbmVzc05vOiB1c2VyLmJ1c3NpbmVzc05vLA0KICAgICAgICBhdWRpdFN0YXR1czogIjIiLA0KICAgICAgICBkaXNwbGF5U3RhdHVzOiAiMSIsDQogICAgICB9LA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgc3VwcGx5VHlwZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nkvpvnu5nnsbvlnosiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgc3VwcGx5TmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXkvpvnu5nmoIfpopgiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgc3VtbWFyeTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fovpPlhaXkvpvnu5nmj4/ov7AiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgdGVjaG5vbG9neVR5cGU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5oqA5pyv57G75YirIiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGFwcGxpY2F0aW9uQXJlYTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nlupTnlKjpoobln58iLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgcHJvZHVjdFBob3RvOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeS+m+e7meWbvueJhyIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBjb21wYW55TmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fnu7TmiqTlhazlj7jlkI3np7AiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29udGFjdHNOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+e7tOaKpOiBlOezu+S6uiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBjb250YWN0c01vYmlsZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fnu7TmiqTogZTns7vnlLXor50iLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgIH0sDQogICAgfTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGluaXQoKSB7DQogICAgICBjb25zdCBkYXRhID0gY2FjaGUubG9jYWwuZ2V0SlNPTigic3VwcGx5X2RhdGEiKTsNCiAgICAgIGlmIChkYXRhKSB7DQogICAgICAgIHRoaXMuZm9ybSA9IGRhdGE7DQogICAgICB9DQogICAgfSwNCiAgICBvbkNhbmNlbCgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5iYWNrKCk7DQogICAgfSwNCiAgICBvblNhdmUoKSB7DQogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGNhY2hlLmxvY2FsLnNldEpTT04oInN1cHBseV9kYXRhIiwgdGhpcy5mb3JtKTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaaguWtmOaIkOWKnyIpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIG9uU3VibWl0KHN0YXR1cykgew0KICAgICAgdGhpcy4kcmVmcy5mb3JtLnZhbGlkYXRlKCh2YWxpZCkgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgICAgIGNvbnN0IHsNCiAgICAgICAgICAgIHN1cHBseVR5cGUsDQogICAgICAgICAgICBrZXl3b3JkcywNCiAgICAgICAgICAgIGFwcGxpY2F0aW9uQXJlYSwNCiAgICAgICAgICAgIHByb2R1Y3RQaG90bywNCiAgICAgICAgICAgIGVuY2xvc3VyZSwNCiAgICAgICAgICAgIHRlY2hub2xvZ3lUeXBlLA0KICAgICAgICAgICAgLi4ucmVzdA0KICAgICAgICAgIH0gPSB0aGlzLmZvcm07DQogICAgICAgICAgY29uc3QgZGF0YSA9IHsNCiAgICAgICAgICAgIC4uLnJlc3QsDQogICAgICAgICAgICBhdWRpdFN0YXR1czogc3RhdHVzLA0KICAgICAgICAgIH07DQogICAgICAgICAgaWYgKHN1cHBseVR5cGUubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgZGF0YVsic3VwcGx5VHlwZSJdID0gc3VwcGx5VHlwZS5qb2luKCk7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChrZXl3b3Jkcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBkYXRhWyJrZXl3b3JkcyJdID0ga2V5d29yZHMuam9pbigpOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAodGVjaG5vbG9neVR5cGUubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgZGF0YVsidGVjaG5vbG9neVR5cGUiXSA9IHRlY2hub2xvZ3lUeXBlLmpvaW4oKTsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGFwcGxpY2F0aW9uQXJlYS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICBkYXRhWyJhcHBsaWNhdGlvbkFyZWEiXSA9IGFwcGxpY2F0aW9uQXJlYS5qb2luKCk7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChwcm9kdWN0UGhvdG8ubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgZGF0YVsicHJvZHVjdFBob3RvIl0gPSBKU09OLnN0cmluZ2lmeShwcm9kdWN0UGhvdG8pOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoZW5jbG9zdXJlLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIGRhdGFbImVuY2xvc3VyZSJdID0gSlNPTi5zdHJpbmdpZnkoZW5jbG9zdXJlKTsNCiAgICAgICAgICB9DQogICAgICAgICAgc3VwcGx5QWRkKGRhdGEpDQogICAgICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgIGNvbnN0IHsgY29kZSwgbXNnIH0gPSByZXM7DQogICAgICAgICAgICAgIGlmIChjb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgICAgICBjYWNoZS5sb2NhbC5yZW1vdmUoInN1cHBseV9kYXRhIik7DQogICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlj5HluIPmiJDlip8iKTsNCiAgICAgICAgICAgICAgICB0aGlzLiRyb3V0ZXIuYmFjaygpOw0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IobXNnIHx8ICLlj5HluIPlpLHotKUiKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIC5maW5hbGx5KCgpID0+ICh0aGlzLmxvYWRpbmcgPSBmYWxzZSkpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZUtleXdvcmRMaXN0KCkgew0KICAgICAgY29uc3QgeyBzdW1tYXJ5IH0gPSB0aGlzLmZvcm07DQogICAgICBpZiAoc3VtbWFyeSkgew0KICAgICAgICB0aGlzLmtleXdvcmRMb2FkaW5nID0gdHJ1ZTsNCiAgICAgICAga2V5d29yZExpc3Qoc3VtbWFyeSkNCiAgICAgICAgICAudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBjb25zdCB7IGNvZGUsIGRhdGEsIG1zZyB9ID0gcmVzOw0KICAgICAgICAgICAgaWYgKGNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLmZvcm0ua2V5d29yZHMgPSBkYXRhOw0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihtc2cpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgLmZpbmFsbHkoKCkgPT4gKHRoaXMua2V5d29yZExvYWRpbmcgPSBmYWxzZSkpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fovpPlhaXpnIDmsYLmj4/ov7AiKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgaGFuZGxlQ2xvc2UodGFnKSB7DQogICAgICB0aGlzLmZvcm0ua2V5d29yZHMgPSB0aGlzLmZvcm0ua2V5d29yZHMuZmlsdGVyKChpdGVtKSA9PiBpdGVtICE9PSB0YWcpOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["supplyForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqKA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "supplyForm.vue", "sourceRoot": "src/views/form/components", "sourcesContent": ["<template>\r\n  <div class=\"supply-form\">\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item label=\"资源类型\" prop=\"supplyType\">\r\n        <el-checkbox-group\r\n          v-model=\"form.supplyType\"\r\n          placeholder=\"请选择\"\r\n          clearable\r\n        >\r\n          <el-checkbox\r\n            v-for=\"dict in dict.type.supply_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.value\"\r\n            :value=\"dict.value\"\r\n            >{{ dict.label }}</el-checkbox\r\n          >\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <el-form-item prop=\"supplyName\" label=\"资源标题\">\r\n        <el-input\r\n          v-model=\"form.supplyName\"\r\n          maxlength=\"50\"\r\n          show-word-limit\r\n          placeholder=\"请输入\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"summary\" label=\"资源描述\">\r\n        <el-input\r\n          type=\"textarea\"\r\n          v-model=\"form.summary\"\r\n          maxlength=\"500\"\r\n          rows=\"6\"\r\n          show-word-limit\r\n          placeholder=\"请输入资源描述\"\r\n        ></el-input>\r\n        <div class=\"extra-content\">\r\n          <div class=\"extra-content-header\">\r\n            <el-button\r\n              :loading=\"keywordLoading\"\r\n              @click=\"handleKeywordList\"\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              >生成关键词</el-button\r\n            >\r\n            <span class=\"tip\">生成关键词有利于实现精准匹配哦！</span>\r\n          </div>\r\n          <div v-if=\"form.keywords.length > 0\" class=\"extra-content-body\">\r\n            <el-tag\r\n              :key=\"`${tag}_${index}`\"\r\n              v-for=\"(tag, index) in form.keywords\"\r\n              closable\r\n              size=\"small\"\r\n              disable-transitions\r\n              @close=\"handleClose(tag)\"\r\n            >\r\n              {{ tag }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item prop=\"technologyType\" label=\"技术类别\">\r\n        <el-select\r\n          v-model=\"form.technologyType\"\r\n          filterable\r\n          allow-create\r\n          multiple\r\n          style=\"width: 100%\"\r\n          placeholder=\"请选择\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in dict.type.technology_type\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.label\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item prop=\"applicationArea\" label=\"应用领域\">\r\n        <el-select\r\n          v-model=\"form.applicationArea\"\r\n          filterable\r\n          allow-create\r\n          multiple\r\n          style=\"width: 100%\"\r\n          placeholder=\"请选择\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in dict.type.application_area\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.label\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"资源图片\" prop=\"productPhoto\">\r\n        <ImageUpload v-model=\"form.productPhoto\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"合作方式\" prop=\"cooperationMode\">\r\n        <el-select\r\n          v-model=\"form.cooperationMode\"\r\n          placeholder=\"请选择\"\r\n          style=\"width: 100%\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.cooperation_mode\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品阶段\" prop=\"productStage\">\r\n        <el-select\r\n          v-model=\"form.productStage\"\r\n          placeholder=\"请选择\"\r\n          style=\"width: 100%\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.product_stage\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"上传附件\" prop=\"enclosure\">\r\n        <FileUpload v-model=\"form.enclosure\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.companyName\"\r\n          placeholder=\"请先绑定公司\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系人\" prop=\"contactsName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactsName\"\r\n          placeholder=\"请先维护联系人\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系方式\" prop=\"contactsMobile\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactsMobile\"\r\n          placeholder=\"请先维护联系方式\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button @click.once=\"onCancel\">取消</el-button>\r\n        <el-button @click=\"onSubmit('0')\" type=\"primary\" plain\r\n          >暂存草稿</el-button\r\n        >\r\n        <el-button type=\"primary\" @click=\"onSubmit('2')\">发布</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { keywordList, supplyAdd } from \"@/api/zhm\";\r\nimport cache from \"@/plugins/cache\";\r\n\r\nexport default {\r\n  name: \"supplyForm\",\r\n  dicts: [\r\n    \"supply_type\",\r\n    \"technology_type\",\r\n    \"cooperation_mode\",\r\n    \"product_stage\",\r\n    \"application_area\",\r\n  ],\r\n  data() {\r\n    const { user } = this.$store.state;\r\n    return {\r\n      loading: false,\r\n      keywordLoading: false,\r\n      form: {\r\n        // 供给类型\r\n        supplyType: [],\r\n        // 需求标题\r\n        supplyName: undefined,\r\n        // 描述\r\n        summary: undefined,\r\n        // 关键词\r\n        keywords: [],\r\n        // 技术类别\r\n        technologyType: [],\r\n        // 应用领域\r\n        applicationArea: [],\r\n        // 产品图片\r\n        productPhoto: [],\r\n        // 附件\r\n        enclosure: [],\r\n        // 产品阶段\r\n        productStage: undefined,\r\n        // 合作方式\r\n        cooperationMode: undefined,\r\n        // 公司名称\r\n        companyName: user.companyName || \"柠檬豆\",\r\n        // 联系人\r\n        contactsName: user.name,\r\n        // 联系电话\r\n        contactsMobile: user.tel,\r\n        publisherName: user.name,\r\n        publisherMobile: user.tel,\r\n        businessNo: user.bussinessNo,\r\n        auditStatus: \"2\",\r\n        displayStatus: \"1\",\r\n      },\r\n      rules: {\r\n        supplyType: [\r\n          { required: true, message: \"请选择供给类型\", trigger: \"blur\" },\r\n        ],\r\n        supplyName: [\r\n          { required: true, message: \"请输入供给标题\", trigger: \"blur\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"请输入供给描述\", trigger: \"blur\" },\r\n        ],\r\n        technologyType: [\r\n          { required: true, message: \"请选择技术类别\", trigger: \"blur\" },\r\n        ],\r\n        applicationArea: [\r\n          { required: true, message: \"请选择应用领域\", trigger: \"blur\" },\r\n        ],\r\n        productPhoto: [\r\n          { required: true, message: \"请选择供给图片\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"请维护公司名称\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"请维护联系人\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"请维护联系电话\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      const data = cache.local.getJSON(\"supply_data\");\r\n      if (data) {\r\n        this.form = data;\r\n      }\r\n    },\r\n    onCancel() {\r\n      this.$router.back();\r\n    },\r\n    onSave() {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          cache.local.setJSON(\"supply_data\", this.form);\r\n          this.$message.success(\"暂存成功\");\r\n        }\r\n      });\r\n    },\r\n    onSubmit(status) {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          const {\r\n            supplyType,\r\n            keywords,\r\n            applicationArea,\r\n            productPhoto,\r\n            enclosure,\r\n            technologyType,\r\n            ...rest\r\n          } = this.form;\r\n          const data = {\r\n            ...rest,\r\n            auditStatus: status,\r\n          };\r\n          if (supplyType.length > 0) {\r\n            data[\"supplyType\"] = supplyType.join();\r\n          }\r\n          if (keywords.length > 0) {\r\n            data[\"keywords\"] = keywords.join();\r\n          }\r\n          if (technologyType.length > 0) {\r\n            data[\"technologyType\"] = technologyType.join();\r\n          }\r\n          if (applicationArea.length > 0) {\r\n            data[\"applicationArea\"] = applicationArea.join();\r\n          }\r\n          if (productPhoto.length > 0) {\r\n            data[\"productPhoto\"] = JSON.stringify(productPhoto);\r\n          }\r\n          if (enclosure.length > 0) {\r\n            data[\"enclosure\"] = JSON.stringify(enclosure);\r\n          }\r\n          supplyAdd(data)\r\n            .then((res) => {\r\n              const { code, msg } = res;\r\n              if (code === 200) {\r\n                cache.local.remove(\"supply_data\");\r\n                this.$message.success(\"发布成功\");\r\n                this.$router.back();\r\n              } else {\r\n                this.$message.error(msg || \"发布失败\");\r\n              }\r\n            })\r\n            .finally(() => (this.loading = false));\r\n        }\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      const { summary } = this.form;\r\n      if (summary) {\r\n        this.keywordLoading = true;\r\n        keywordList(summary)\r\n          .then((res) => {\r\n            const { code, data, msg } = res;\r\n            if (code === 200) {\r\n              this.form.keywords = data;\r\n            } else {\r\n              this.$message.error(msg);\r\n            }\r\n          })\r\n          .finally(() => (this.keywordLoading = false));\r\n      } else {\r\n        this.$message.warning(\"请输入需求描述\");\r\n      }\r\n    },\r\n\r\n    handleClose(tag) {\r\n      this.form.keywords = this.form.keywords.filter((item) => item !== tag);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.supply-form {\r\n  width: 676px;\r\n  .label-item {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    .title {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #333333;\r\n      line-height: 18px;\r\n    }\r\n    .extra {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .extra-content {\r\n    padding: 12px 0;\r\n    &-header {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      .tip {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n        line-height: 14px;\r\n        margin-left: 12px;\r\n      }\r\n    }\r\n    &-body {\r\n      padding-top: 6px;\r\n      .el-tag {\r\n        margin-right: 12px;\r\n        &:last-child {\r\n          margin-right: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  ::v-deep.el-form-item__label {\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    line-height: 18px;\r\n    margin-bottom: 12px;\r\n    padding: 0;\r\n  }\r\n  .el-checkbox {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #262626;\r\n    line-height: 18px;\r\n    margin-right: 28px;\r\n  }\r\n  .footer-submit {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-top: 40px;\r\n    .el-button {\r\n      width: 160px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}