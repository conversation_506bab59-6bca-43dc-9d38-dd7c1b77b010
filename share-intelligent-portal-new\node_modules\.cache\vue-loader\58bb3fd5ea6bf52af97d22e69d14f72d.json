{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyDemand\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\companyDemand\\detail\\index.vue", "mtime": 1750311963050}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpsZXQgaWQgPSAwOw0KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsgZ2V0RGVtYW5kRGV0YWlsLCBjcmVhdGVEZW1hbmQsIGVkaXREZW1hbmQgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGVtYW5kIjsNCmltcG9ydCB7IHVwbG9hZFVybCB9IGZyb20gIkAvYXBpL29zcyI7DQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7DQppbXBvcnQgc3RvcmUgZnJvbSAiQC9zdG9yZSI7DQppbXBvcnQgeyBkZW1hbmRBZGQsIGtleXdvcmRMaXN0IH0gZnJvbSAiQC9hcGkvemhtIjsNCmltcG9ydCBDcnlwdG9KUyBmcm9tICJjcnlwdG8tanMiOw0KbGV0IHNlY3JldEtleSA9ICI5elZuMCVicW1VWVNHdzJuIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiTm90aWNlIiwNCiAgZGljdHM6IFsNCiAgICAiZGVtYW5kX3R5cGUiLA0KICAgICJhZmZpbGlhdGVkX3VuaXQiLA0KICAgICJjYXBpdGFsX3NvdXJjZSIsDQogICAgImFmZmlsaWF0ZWRfc3RyZWV0IiwNCiAgICAiZGlzcGxheV9yZXN0cmljdGlvbnMiLA0KICAgICJhcHBsaWNhdGlvbl9hcmVhIiwNCiAgXSwNCiAgY29tcG9uZW50czogeyBVc2VyTWVudSB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBpc0RldGFpbDogdHJ1ZSwNCiAgICAgIHRpdGxlOiAi6ZyA5rGC6K+m5oOFIiwNCiAgICAgIGltYWdlVXJsOiAiIiwNCiAgICAgIGFjdGlvblVybDogdXBsb2FkVXJsKCksDQogICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkgfSwNCiAgICAgIHVwbG9hZFVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSArICIvZmlsZS91cGxvYWQiLCAvL+S4iuS8oOWcsOWdgA0KICAgICAgYWNjZXB0OiAiLmpwZywgLmpwZWcsIC5wbmcsIC5ibXAiLA0KICAgICAgaXNDcmVhdGU6IGZhbHNlLA0KICAgICAgaW1nVmlzaWJsZTogZmFsc2UsDQogICAgICB1c2VyOiB7DQogICAgICAgIHRlbDogc3RvcmUuZ2V0dGVycy50ZWwsDQogICAgICAgIG5hbWU6IHN0b3JlLmdldHRlcnMubmFtZSwNCiAgICAgICAgY29tcGFueU5hbWU6IHN0b3JlLmdldHRlcnMuY29tcGFueU5hbWUsDQogICAgICAgIGJ1c3NpbmVzc05vOiBzdG9yZS5nZXR0ZXJzLmJ1c3NpbmVzc05vLA0KICAgICAgICBwaG9uZW51bWJlcjogc3RvcmUuZ2V0dGVycy5waG9uZW51bWJlciwNCiAgICAgIH0sDQogICAgICBrZXl3b3JkczogW10sDQogICAgICBhcHBsaWNhdGlvbnNJbnB1dDogIiIsDQogICAgICBpbmZvOiB7fSwNCg0KICAgICAgZm9ybToge30sDQogICAgICBhY2NvdW50TGljZW5jZUxpc3Q6IFtdLA0KDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGRlbWFuZFRpdGxlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIumcgOaxguagh+mimOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgICBkZW1hbmRUeXBlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqemcgOaxguexu+WeiyIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sDQogICAgICAgIF0sDQogICAgICAgIGFwcGxpY2F0aW9uQXJlYTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nlupTnlKjpoobln58iLCB0cmlnZ2VyOiAiY2hhbmdlIiB9LA0KICAgICAgICBdLA0KICAgICAgICBkaXNwbGF5UmVzdHJpY3Rpb25zOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeWxleekuumZkOWItiIsIHRyaWdnZXI6ICJjaGFuZ2UiIH0sDQogICAgICAgIF0sDQogICAgICAgIHN1bW1hcnk6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6ZyA5rGC5o+P6L+w5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvbnRhY3RzTmFtZTogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLogZTns7vkurrkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfSwNCiAgICAgICAgXSwNCiAgICAgICAgY29tcGFueU5hbWU6IFsNCiAgICAgICAgICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5YWs5Y+45ZCN56ew5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIiIH0sDQogICAgICAgIF0sDQogICAgICAgIGNvbnRhY3RzTW9iaWxlOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuiBlOezu+eUteivneS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuaXNDcmVhdGUgPSB0aGlzLiRyb3V0ZS5xdWVyeS50eXBlID09IDE7DQogICAgaWYgKHRoaXMuaXNDcmVhdGUpIHsNCiAgICAgIHRoaXMuZ29DcmVhdGUoKTsNCiAgICB9IGVsc2Ugew0KICAgICAgdGhpcy5nZXREZXRhaWwoKTsNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBpbml0Rm9ybSgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgZGVtYW5kVHlwZTogW10sDQogICAgICAgIGFwcGxpY2F0aW9uQXJlYTogW10sDQogICAgICAgIHNjZW5lUGljdHVyZTogW10sDQogICAgICAgIC8vIGFwcGxpY2F0aW9uQXJlYUxpc3Q6IFtdLA0KICAgICAgICBzY2VuZVBpY3R1cmVMaXN0OiBbXSwNCiAgICAgICAga2V5d29yZHM6IFtdLA0KICAgICAgICBhdWRpdFN0YXR1czogIjEiLA0KICAgICAgICBkaXNwbGF5U3RhdHVzOiAiMiIsDQogICAgICAgIHB1Ymxpc2hlck5hbWU6IHRoaXMudXNlci5uYW1lLA0KICAgICAgICBwdWJsaXNoZXJNb2JpbGU6IHRoaXMudXNlci50ZWwsDQogICAgICAgIC8vIOWxleekuumZkOWItg0KICAgICAgICBkaXNwbGF5UmVzdHJpY3Rpb25zOiB1bmRlZmluZWQsDQogICAgICB9Ow0KICAgIH0sDQogICAgZ2V0RGV0YWlsKCkgew0KICAgICAgbGV0IGlkID0gdGhpcy4kcm91dGUucXVlcnkuaWQ7DQogICAgICBnZXREZW1hbmREZXRhaWwoaWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIGxldCBrZXkgPSBDcnlwdG9KUy5lbmMuVXRmOC5wYXJzZShzZWNyZXRLZXkpOw0KICAgICAgICBsZXQgZGVjcnlwdCA9IENyeXB0b0pTLkFFUy5kZWNyeXB0KHJlc3BvbnNlLCBrZXksIHsNCiAgICAgICAgICBtb2RlOiBDcnlwdG9KUy5tb2RlLkVDQiwNCiAgICAgICAgICBwYWRkaW5nOiBDcnlwdG9KUy5wYWQuUGtjczcsDQogICAgICAgIH0pOw0KICAgICAgICByZXNwb25zZSA9IEpTT04ucGFyc2UoQ3J5cHRvSlMuZW5jLlV0Zjguc3RyaW5naWZ5KGRlY3J5cHQpKTsNCiAgICAgICAgdGhpcy5pbmZvID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBnb0JhY2soKSB7DQogICAgICB0aGlzLiRyb3V0ZXIuZ28oLTEpOw0KICAgIH0sDQogICAgZ2V0VXJsKHN0cikgew0KICAgICAgaWYgKHN0ciAmJiBzdHIgIT0gbnVsbCkgew0KICAgICAgICB2YXIgbGlzdCA9IEpTT04ucGFyc2Uoc3RyKTsNCiAgICAgICAgaWYgKGxpc3QgJiYgbGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgICAgcmV0dXJuIGxpc3RbMF0udXJsOw0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIHJldHVybiBudWxsOw0KICAgIH0sDQogICAgLy8gLy8g5bqU55So6aKG5Z+f5paw5aKeDQogICAgLy8gaGFuZGxlSW5wdXRDb25maXJtKCkgew0KICAgIC8vICAgbGV0IHZhbCA9IHRoaXMuYXBwbGljYXRpb25zSW5wdXQ7DQogICAgLy8gICBpZiAodmFsKSB7DQogICAgLy8gICAgIHRoaXMuZm9ybS5hcHBsaWNhdGlvbkFyZWFMaXN0LnB1c2godmFsKTsNCiAgICAvLyAgIH0NCiAgICAvLyAgIHRoaXMuYXBwbGljYXRpb25zSW5wdXQgPSAiIjsNCiAgICAvLyB9LA0KICAgIC8vIC8vIOW6lOeUqOmihuWfn+enu+mZpA0KICAgIC8vIGhhbmRsZUNsb3NlKHRhZykgew0KICAgIC8vICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uQXJlYUxpc3Quc3BsaWNlKA0KICAgIC8vICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb25BcmVhTGlzdC5pbmRleE9mKHRhZyksDQogICAgLy8gICAgIDENCiAgICAvLyAgICk7DQogICAgLy8gfSwNCiAgICBoYW5kbGVTdW1tYXJ5Q2xvc2UodGFnKSB7DQogICAgICB0aGlzLmZvcm0ua2V5d29yZHMuc3BsaWNlKHRoaXMuZm9ybS5rZXl3b3Jkcy5pbmRleE9mKHRhZyksIDEpOw0KICAgIH0sDQogICAgLy8g5Lqn5ZOB54Wn54mH5LiK5Lyg5LmL5YmN55qE6ZKp5a2QDQogICAgaGFuZGxlQmVmb3JlVXBsb2FkKGZpbGUpIHsNCiAgICAgIGxldCB7IG5hbWUsIHR5cGUsIHNpemUgfSA9IGZpbGU7DQogICAgICBsZXQgdHlwZUxpc3QgPSB0aGlzLmFjY2VwdA0KICAgICAgICAuc3BsaXQoIiwiKQ0KICAgICAgICAubWFwKChpdGVtKSA9PiBpdGVtLnRyaW0oKS50b0xvd2VyQ2FzZSgpLnN1YnN0cigxKSk7DQogICAgICBsZXQgZG90SW5kZXggPSBuYW1lLmxhc3RJbmRleE9mKCIuIik7DQogICAgICAvLyDmlofku7bnsbvlnovmoKHpqowNCiAgICAgIGlmIChkb3RJbmRleCA9PT0gLTEpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+35LiK5Lyg5q2j56Gu5qC85byP55qE5paH5Lu2Iik7DQogICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldCBzdWZmaXggPSBuYW1lLnN1YnN0cmluZyhkb3RJbmRleCArIDEpOw0KICAgICAgICBpZiAodHlwZUxpc3QuaW5kZXhPZihzdWZmaXgudG9Mb3dlckNhc2UoKSkgPT09IC0xKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+35LiK5Lyg5q2j56Gu5qC85byP55qE5paH5Lu2Iik7DQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9DQogICAgICAvLyDmlofku7bkuIrkvKDlpKflsI/pmZDliLYNCiAgICAgIGlmIChzaXplID4gMTA0ODU3NiAqIDIwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuaWh+S7tuWkp+Wwj+S4jeiDvei2hei/hzIwTe+8gSIpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICAvLyDngrnlh7vkuqflk4HnhafniYcNCiAgICBoYW5kbGVQaWN0dXJlQ2FyZFByZXZpZXcoZmlsZSkgew0KICAgICAgdGhpcy5pbWFnZVVybCA9IGZpbGUudXJsOw0KICAgICAgdGhpcy5pbWdWaXNpYmxlID0gdHJ1ZTsNCiAgICB9LA0KICAgIC8vIOWIoOmZpOS6p+WTgeeFp+eJhw0KICAgIGhhbmRsZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5mb3JtLnNjZW5lUGljdHVyZUxpc3QgPSBmaWxlTGlzdDsNCiAgICB9LA0KICAgIGhhbmRsZVN1Y2Nlc3MocmVzcG9uc2UsIGZpbGUpIHsNCiAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDIwMCkgew0KICAgICAgICBpZiAodGhpcy5mb3JtLnNjZW5lUGljdHVyZUxpc3QgPT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5zY2VuZVBpY3R1cmVMaXN0ID0gW107DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5mb3JtLnNjZW5lUGljdHVyZUxpc3QucHVzaChyZXNwb25zZS5kYXRhKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGNoYW5nZU1vZGUoKSB7DQogICAgICBpZiAodGhpcy5pc0NyZWF0ZSkgew0KICAgICAgICB0aGlzLmdvQmFjaygpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQogICAgICBpZiAodGhpcy5pc0RldGFpbCkgew0KICAgICAgICB0aGlzLnRpdGxlID0gIue8lui+kemcgOaxgiI7DQogICAgICAgIHRoaXMuaXNEZXRhaWwgPSBmYWxzZTsNCiAgICAgICAgdGhpcy5mb3JtID0gdGhpcy5pbmZvOw0KICAgICAgICBpZiAodGhpcy5pbmZvLmFwcGxpY2F0aW9uQXJlYSkgew0KICAgICAgICAgIHRoaXMuZm9ybS5hcHBsaWNhdGlvbkFyZWEgPSB0aGlzLmluZm8uYXBwbGljYXRpb25BcmVhLnNwbGl0KCIsIik7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uQXJlYSA9IFtdOw0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLmluZm8uZGlzcGxheVJlc3RyaWN0aW9ucykgew0KICAgICAgICAgIHRoaXMuZm9ybS5kaXNwbGF5UmVzdHJpY3Rpb25zID0NCiAgICAgICAgICAgIHRoaXMuaW5mby5kaXNwbGF5UmVzdHJpY3Rpb25zLnRvU3RyaW5nKCk7DQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuaW5mby5rZXl3b3Jkcykgew0KICAgICAgICAgIHRoaXMuZm9ybS5rZXl3b3JkcyA9IHRoaXMuaW5mby5rZXl3b3Jkcy5zcGxpdCgiLCIpOw0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLmluZm8uZGVtYW5kVHlwZSkgew0KICAgICAgICAgIHRoaXMuZm9ybS5kZW1hbmRUeXBlID0gdGhpcy5pbmZvLmRlbWFuZFR5cGUuc3BsaXQoIiwiKTsNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5pbmZvLnNjZW5lUGljdHVyZSAmJiB0aGlzLmluZm8uc2NlbmVQaWN0dXJlICE9ICJudWxsIikgew0KICAgICAgICAgIHRoaXMuZm9ybS5zY2VuZVBpY3R1cmVMaXN0ID0gSlNPTi5wYXJzZSh0aGlzLmluZm8uc2NlbmVQaWN0dXJlKTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmZvcm0uc2NlbmVQaWN0dXJlTGlzdCA9IFtdOw0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmlzRGV0YWlsID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLpnIDmsYLor6bmg4UiOw0KICAgICAgICB0aGlzLmluaXRGb3JtKCk7DQogICAgICAgIHRoaXMuZ2V0RGV0YWlsKCk7DQogICAgICB9DQogICAgfSwNCiAgICBnb0NyZWF0ZSgpIHsNCiAgICAgIHRoaXMudGl0bGUgPSAi5paw5aKe6ZyA5rGCIjsNCiAgICAgIHRoaXMuaXNEZXRhaWwgPSBmYWxzZTsNCiAgICAgIHRoaXMuaW5pdEZvcm0oKTsNCiAgICAgIHRoaXMuZm9ybS5jb21wYW55TmFtZSA9IHRoaXMudXNlci5jb21wYW55TmFtZTsNCiAgICAgIHRoaXMuZm9ybS5jb250YWN0c05hbWUgPSB0aGlzLnVzZXIubmFtZTsNCiAgICAgIHRoaXMuZm9ybS5jb250YWN0c01vYmlsZSA9IHRoaXMudXNlci5waG9uZW51bWJlcjsNCiAgICAgIHRoaXMuZm9ybS5wdWJsaXNoZXJOYW1lID0gdGhpcy51c2VyLm5hbWU7DQogICAgICB0aGlzLmZvcm0ucHVibGlzaGVyTW9iaWxlID0gdGhpcy51c2VyLnBob25lbnVtYmVyOw0KICAgICAgdGhpcy5mb3JtLmJ1c2luZXNzTm8gPSB0aGlzLnVzZXIuYnVzc2luZXNzTm87DQogICAgfSwNCiAgICBoYW5kbGVGaWxlUHJldmlldyhmaWxlKSB7DQogICAgICB3aW5kb3cub3BlbihmaWxlKTsNCiAgICB9LA0KICAgIGRpc3BsYXlSZXN0cmljdGlvbkNoYW5nZWQocmVzKSB7DQogICAgICB0aGlzLmRpY3QudHlwZS5kaXNwbGF5X3Jlc3RyaWN0aW9ucy5mb3JFYWNoKChpdGVtKSA9PiB7DQogICAgICAgIGlmIChpdGVtLmxhYmVsID09IHJlcykgew0KICAgICAgICAgIHRoaXMuZm9ybS5kaXNwbGF5UmVzdHJpY3Rpb25zID0gaXRlbS52YWx1ZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzdWJtaXRGb3JtKHR5cGUpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgLy8gaWYgKA0KICAgICAgICAgIC8vICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uQXJlYUxpc3QgJiYNCiAgICAgICAgICAvLyAgIHRoaXMuZm9ybS5hcHBsaWNhdGlvbkFyZWFMaXN0Lmxlbmd0aCA+IDANCiAgICAgICAgICAvLyApIHsNCiAgICAgICAgICAvLyAgIHRoaXMuZm9ybS5hcHBsaWNhdGlvbkFyZWEgPSB0aGlzLmZvcm0uYXBwbGljYXRpb25BcmVhTGlzdC5qb2luKCIsIik7DQogICAgICAgICAgLy8gfSBlbHNlIHsNCiAgICAgICAgICAvLyAgIHRoaXMuZm9ybS5hcHBsaWNhdGlvbkFyZWEgPSAiIjsNCiAgICAgICAgICAvLyB9DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5hcHBsaWNhdGlvbkFyZWEubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uQXJlYSA9IHRoaXMuZm9ybS5hcHBsaWNhdGlvbkFyZWEuam9pbigpOw0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmZvcm0uc2NlbmVQaWN0dXJlID0gSlNPTi5zdHJpbmdpZnkodGhpcy5mb3JtLnNjZW5lUGljdHVyZUxpc3QpOw0KICAgICAgICAgIHRoaXMuZm9ybS5idXNpbmVzc05vID0gdGhpcy51c2VyLmJ1c3NpbmVzc05vOw0KICAgICAgICAgIGlmICh0aGlzLmZvcm0ua2V5d29yZHMgJiYgdGhpcy5mb3JtLmtleXdvcmRzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5rZXl3b3JkcyA9IHRoaXMuZm9ybS5rZXl3b3Jkcy5qb2luKCIsIik7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5rZXl3b3JkcyA9ICIiOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmRlbWFuZFR5cGUubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtLmRlbWFuZFR5cGUgPSB0aGlzLmZvcm0uZGVtYW5kVHlwZS5qb2luKCk7DQogICAgICAgICAgfQ0KICAgICAgICAgIGlmICh0aGlzLmlzQ3JlYXRlKSB7DQogICAgICAgICAgICBjcmVhdGVEZW1hbmQoeyAuLi50aGlzLmZvcm0sIGlzU3VibWl0OiB0eXBlIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLmNoYW5nZU1vZGUoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLmZvcm0uYXVkaXRTdGF0dXMgPSAxOw0KICAgICAgICAgICAgZWRpdERlbWFuZCh7IC4uLnRoaXMuZm9ybSwgaXNTdWJtaXQ6IHR5cGUgfSkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMuY2hhbmdlTW9kZSgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIHRvWml5dWFuKCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICBwYXRoOiAiL3VzZXIvY29tcGFueUFwcGx5RGV0YWlsMSIsDQogICAgICAgIHF1ZXJ5OiB7IGtleTogSlNPTi5zdHJpbmdpZnkodGhpcy5pbmZvKSB9LA0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVLZXl3b3JkTGlzdCgpIHsNCiAgICAgIGNvbnN0IHsgc3VtbWFyeSB9ID0gdGhpcy5mb3JtOw0KICAgICAgaWYgKHN1bW1hcnkpIHsNCiAgICAgICAga2V5d29yZExpc3Qoc3VtbWFyeSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgY29uc3QgeyBjb2RlLCBkYXRhLCBtc2cgfSA9IHJlczsNCiAgICAgICAgICBpZiAoY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICB0aGlzLmZvcm0ua2V5d29yZHMgPSBkYXRhOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKG1zZyk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36L6T5YWl6ZyA5rGC5o+P6L+wIik7DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVBcHBsaWNhdGlvblJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uID0gIiI7DQogICAgfSwNCiAgICBoYW5kbGVBcHBsaWNhdGlvblN1Y2Nlc3MocmVzLCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgLy/mraTlpITlhpnkuIrkvKBvc3PmiJDlip/kuYvlkI7nmoTpgLvovpENCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy5mb3JtLmFwcGxpY2F0aW9uID0gcmVzLmRhdGEudXJsOw0KICAgICAgICB0aGlzLmZvcm0uYXBwbGljYXRpb25OYW1lID0gcmVzLmRhdGEubmFtZTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUFjY291bnRSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMuZm9ybS5hY2NvdW50TGljZW5jZSA9ICIiOw0KICAgIH0sDQogICAgaGFuZGxlQWNjb3VudFN1Y2Nlc3MocmVzLCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgLy/mraTlpITlhpnkuIrkvKBvc3PmiJDlip/kuYvlkI7nmoTpgLvovpENCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy5mb3JtLmFjY291bnRMaWNlbmNlID0gcmVzLmRhdGEudXJsOw0KICAgICAgICB0aGlzLmZvcm0uYWNjb3VudExpY2VuY2VOYW1lID0gcmVzLmRhdGEubmFtZTsNCiAgICAgIH0NCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4RA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/companyDemand/detail", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-11 15:20:15\r\n * @LastEditTime: 2023-02-28 08:48:59\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-11 15:18:41\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"company-demand-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <div class=\"header-text\">{{ this.title }}</div>\r\n            </div>\r\n            <div class=\"detail-page\" v-if=\"isDetail\">\r\n              <div class=\"header-small\">\r\n                <div class=\"red-tag\"></div>\r\n                基本信息\r\n                <el-button\r\n                  plain\r\n                  type=\"primary\"\r\n                  style=\"position: absolute; right: 0\"\r\n                  @click=\"toZiyuan\"\r\n                  >查看平台匹配资源</el-button\r\n                >\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 需求标题 </template>\r\n                  {{ info.demandTitle }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 需求描述 </template>\r\n                  {{ info.summary }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 应用领域</template>\r\n                  {{ info.applicationArea }}\r\n                </el-descriptions-item>\r\n\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 需求封面 </template>\r\n                  <el-image\r\n                    style=\"width: 90px; height: 64px\"\r\n                    :src=\"getUrl(info.scenePicture)\"\r\n                  ></el-image>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <div class=\"header-small mt_40\">\r\n                <div class=\"red-tag\"></div>\r\n                联系信息\r\n              </div>\r\n\r\n              <el-descriptions class=\"margin-top\" :column=\"1\" border>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 公司名称 </template>\r\n                  {{ info.companyName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系人 </template>\r\n                  {{ info.contactsName }}\r\n                </el-descriptions-item>\r\n                <el-descriptions-item>\r\n                  <template slot=\"label\"> 联系电话</template>\r\n                  {{ info.contactsMobile }}\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n              <el-image\r\n                class=\"status_approving\"\r\n                v-if=\"info.status == '1'\"\r\n                style=\"width: 120px; height: 102px\"\r\n                :src=\"require('@/assets/user/status_approving.png')\"\r\n              ></el-image>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"goBack\">返回</el-button>\r\n                <el-button type=\"danger\" @click=\"changeMode\">编辑</el-button>\r\n              </div>\r\n            </div>\r\n            <div class=\"edit-page\" v-else>\r\n              <el-form\r\n                ref=\"form\"\r\n                :model=\"form\"\r\n                :rules=\"rules\"\r\n                label-position=\"top\"\r\n              >\r\n                <el-form-item label=\"需求标题\" prop=\"demandTitle\">\r\n                  <el-input v-model=\"form.demandTitle\" placeholder=\"请输入\" />\r\n                </el-form-item>\r\n                <el-form-item prop=\"demandType\">\r\n                  <div class=\"label-item\" slot=\"label\">\r\n                    <span>需求类型</span>\r\n                    <span class=\"extra\"\r\n                      >（可按需求产品+应用行业+应用领域进行描述）</span\r\n                    >\r\n                  </div>\r\n                  <el-checkbox-group\r\n                    v-model=\"form.demandType\"\r\n                    placeholder=\"请选择\"\r\n                    clearable\r\n                  >\r\n                    <el-checkbox\r\n                      v-for=\"dict in dict.type.demand_type\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.value\"\r\n                      :value=\"dict.value\"\r\n                      >{{ dict.label }}</el-checkbox\r\n                    >\r\n                  </el-checkbox-group>\r\n                </el-form-item>\r\n                <el-form-item label=\"需求描述\" prop=\"summary\">\r\n                  <el-input\r\n                    v-model=\"form.summary\"\r\n                    type=\"textarea\"\r\n                    :rows=\"2\"\r\n                    :maxlength=\"500\"\r\n                    placeholder=\"请输入\"\r\n                  />\r\n                  <div class=\"extra-content\">\r\n                    <div class=\"extra-content-header\">\r\n                      <el-button\r\n                        @click=\"handleKeywordList\"\r\n                        size=\"small\"\r\n                        type=\"primary\"\r\n                        >生成关键词</el-button\r\n                      >\r\n                      <span class=\"tip\">生成关键词有利于实现精准匹配哦！</span>\r\n                    </div>\r\n                    <div\r\n                      v-if=\"form.keywords && form.keywords.length > 0\"\r\n                      class=\"extra-content-body\"\r\n                    >\r\n                      <el-tag\r\n                        :key=\"`${tag}_${index}`\"\r\n                        v-for=\"(tag, index) in form.keywords\"\r\n                        closable\r\n                        size=\"small\"\r\n                        disable-transitions\r\n                        @close=\"handleSummaryClose(tag)\"\r\n                      >\r\n                        {{ tag }}\r\n                      </el-tag>\r\n                    </div>\r\n                  </div>\r\n                </el-form-item>\r\n                <el-form-item label=\"应用领域\" prop=\"applicationArea\">\r\n                  <el-select\r\n                    v-model=\"form.applicationArea\"\r\n                    filterable\r\n                    multiple\r\n                    allow-create\r\n                    style=\"width: 100%\"\r\n                    placeholder=\"请选择\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in dict.type.application_area\"\r\n                      :key=\"item.value\"\r\n                      :label=\"item.label\"\r\n                      :value=\"item.label\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                  <!-- <el-tag\r\n                    v-for=\"tag in form.applicationAreaList\"\r\n                    closable\r\n                    class=\"add-demand-tag\"\r\n                    :key=\"tag\"\r\n                    :disable-transitions=\"false\"\r\n                    @close=\"handleClose(tag)\"\r\n                  >\r\n                    {{ tag }}\r\n                  </el-tag>\r\n                  <el-input v-model=\"applicationsInput\" :maxlength=\"255\">\r\n                  </el-input>\r\n                  <el-button\r\n                    size=\"small\"\r\n                    icon=\"el-icon-plus\"\r\n                    class=\"add-demand-btn-tag\"\r\n                    @click=\"handleInputConfirm\"\r\n                    >新增</el-button\r\n                  > -->\r\n                </el-form-item>\r\n                <el-form-item label=\"产品图片\">\r\n                  <el-upload\r\n                    list-type=\"picture-card\"\r\n                    :headers=\"headers\"\r\n                    :action=\"uploadUrl\"\r\n                    :file-list=\"form.scenePictureList\"\r\n                    :accept=\"accept\"\r\n                    :before-upload=\"handleBeforeUpload\"\r\n                    :on-preview=\"handlePictureCardPreview\"\r\n                    :on-remove=\"handleRemove\"\r\n                    :on-success=\"handleSuccess\"\r\n                  >\r\n                    <i class=\"el-icon-plus\"></i>\r\n                  </el-upload>\r\n                  <el-dialog\r\n                    append-to-body\r\n                    :visible.sync=\"imgVisible\"\r\n                    :close-on-click-modal=\"false\"\r\n                  >\r\n                    <img v-if=\"imageUrl\" width=\"100%\" :src=\"imageUrl\" alt=\"\" />\r\n                  </el-dialog>\r\n                </el-form-item>\r\n                <el-form-item label=\"展示限制\" prop=\"displayRestrictions\">\r\n                  <el-select\r\n                    v-model=\"form.displayRestrictions\"\r\n                    placeholder=\"请选择\"\r\n                    style=\"width: 100%\"\r\n                    clearable\r\n                  >\r\n                    <el-option\r\n                      v-for=\"dict in dict.type.display_restrictions\"\r\n                      :key=\"dict.value\"\r\n                      :label=\"dict.label\"\r\n                      :value=\"dict.value\"\r\n                    />\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n                      <el-input\r\n                        disabled\r\n                        v-model=\"form.companyName\"\r\n                        placeholder=\"请输入公司名称\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"联系人\" prop=\"contactsName\">\r\n                      <el-input\r\n                        disabled\r\n                        v-model=\"form.contactsName\"\r\n                        placeholder=\"请输入联系人\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-row>\r\n                  <el-col :span=\"24\">\r\n                    <el-form-item label=\"联系电话\" prop=\"contactsMobile\">\r\n                      <el-input\r\n                        disabled\r\n                        v-model=\"form.contactsMobile\"\r\n                        placeholder=\"请输入联系电话\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form>\r\n              <div class=\"delete-btn\">\r\n                <el-button @click=\"changeMode\">返回</el-button>\r\n                <!-- <el-button type=\"error\" @click=\"changeMode(0)\"\r\n                  >暂存草稿</el-button\r\n                > -->\r\n                <el-button type=\"danger\" @click=\"submitForm(1)\"\r\n                  >提交审核</el-button\r\n                >\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nlet id = 0;\r\n\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport { getDemandDetail, createDemand, editDemand } from \"@/api/system/demand\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport store from \"@/store\";\r\nimport { demandAdd, keywordList } from \"@/api/zhm\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  name: \"Notice\",\r\n  dicts: [\r\n    \"demand_type\",\r\n    \"affiliated_unit\",\r\n    \"capital_source\",\r\n    \"affiliated_street\",\r\n    \"display_restrictions\",\r\n    \"application_area\",\r\n  ],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      isDetail: true,\r\n      title: \"需求详情\",\r\n      imageUrl: \"\",\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      isCreate: false,\r\n      imgVisible: false,\r\n      user: {\r\n        tel: store.getters.tel,\r\n        name: store.getters.name,\r\n        companyName: store.getters.companyName,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        phonenumber: store.getters.phonenumber,\r\n      },\r\n      keywords: [],\r\n      applicationsInput: \"\",\r\n      info: {},\r\n\r\n      form: {},\r\n      accountLicenceList: [],\r\n\r\n      // 表单校验\r\n      rules: {\r\n        demandTitle: [\r\n          { required: true, message: \"需求标题不能为空\", trigger: \"blur\" },\r\n        ],\r\n        demandType: [\r\n          { required: true, message: \"请选择需求类型\", trigger: \"change\" },\r\n        ],\r\n        applicationArea: [\r\n          { required: true, message: \"请选择应用领域\", trigger: \"change\" },\r\n        ],\r\n        displayRestrictions: [\r\n          { required: true, message: \"请选择展示限制\", trigger: \"change\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"需求描述不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"联系人不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"联系电话不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.isCreate = this.$route.query.type == 1;\r\n    if (this.isCreate) {\r\n      this.goCreate();\r\n    } else {\r\n      this.getDetail();\r\n    }\r\n  },\r\n  methods: {\r\n    initForm() {\r\n      this.form = {\r\n        demandType: [],\r\n        applicationArea: [],\r\n        scenePicture: [],\r\n        // applicationAreaList: [],\r\n        scenePictureList: [],\r\n        keywords: [],\r\n        auditStatus: \"1\",\r\n        displayStatus: \"2\",\r\n        publisherName: this.user.name,\r\n        publisherMobile: this.user.tel,\r\n        // 展示限制\r\n        displayRestrictions: undefined,\r\n      };\r\n    },\r\n    getDetail() {\r\n      let id = this.$route.query.id;\r\n      getDemandDetail(id).then((response) => {\r\n        let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n        let decrypt = CryptoJS.AES.decrypt(response, key, {\r\n          mode: CryptoJS.mode.ECB,\r\n          padding: CryptoJS.pad.Pkcs7,\r\n        });\r\n        response = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n        this.info = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    getUrl(str) {\r\n      if (str && str != null) {\r\n        var list = JSON.parse(str);\r\n        if (list && list.length > 0) {\r\n          return list[0].url;\r\n        }\r\n      }\r\n\r\n      return null;\r\n    },\r\n    // // 应用领域新增\r\n    // handleInputConfirm() {\r\n    //   let val = this.applicationsInput;\r\n    //   if (val) {\r\n    //     this.form.applicationAreaList.push(val);\r\n    //   }\r\n    //   this.applicationsInput = \"\";\r\n    // },\r\n    // // 应用领域移除\r\n    // handleClose(tag) {\r\n    //   this.form.applicationAreaList.splice(\r\n    //     this.form.applicationAreaList.indexOf(tag),\r\n    //     1\r\n    //   );\r\n    // },\r\n    handleSummaryClose(tag) {\r\n      this.form.keywords.splice(this.form.keywords.indexOf(tag), 1);\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePictureCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.form.scenePictureList = fileList;\r\n    },\r\n    handleSuccess(response, file) {\r\n      if (response.code == 200) {\r\n        if (this.form.scenePictureList == null) {\r\n          this.form.scenePictureList = [];\r\n        }\r\n        this.form.scenePictureList.push(response.data);\r\n      }\r\n    },\r\n    changeMode() {\r\n      if (this.isCreate) {\r\n        this.goBack();\r\n        return;\r\n      }\r\n      if (this.isDetail) {\r\n        this.title = \"编辑需求\";\r\n        this.isDetail = false;\r\n        this.form = this.info;\r\n        if (this.info.applicationArea) {\r\n          this.form.applicationArea = this.info.applicationArea.split(\",\");\r\n        } else {\r\n          this.form.applicationArea = [];\r\n        }\r\n        if (this.info.displayRestrictions) {\r\n          this.form.displayRestrictions =\r\n            this.info.displayRestrictions.toString();\r\n        }\r\n        if (this.info.keywords) {\r\n          this.form.keywords = this.info.keywords.split(\",\");\r\n        }\r\n        if (this.info.demandType) {\r\n          this.form.demandType = this.info.demandType.split(\",\");\r\n        }\r\n        if (this.info.scenePicture && this.info.scenePicture != \"null\") {\r\n          this.form.scenePictureList = JSON.parse(this.info.scenePicture);\r\n        } else {\r\n          this.form.scenePictureList = [];\r\n        }\r\n      } else {\r\n        this.isDetail = true;\r\n        this.title = \"需求详情\";\r\n        this.initForm();\r\n        this.getDetail();\r\n      }\r\n    },\r\n    goCreate() {\r\n      this.title = \"新增需求\";\r\n      this.isDetail = false;\r\n      this.initForm();\r\n      this.form.companyName = this.user.companyName;\r\n      this.form.contactsName = this.user.name;\r\n      this.form.contactsMobile = this.user.phonenumber;\r\n      this.form.publisherName = this.user.name;\r\n      this.form.publisherMobile = this.user.phonenumber;\r\n      this.form.businessNo = this.user.bussinessNo;\r\n    },\r\n    handleFilePreview(file) {\r\n      window.open(file);\r\n    },\r\n    displayRestrictionChanged(res) {\r\n      this.dict.type.display_restrictions.forEach((item) => {\r\n        if (item.label == res) {\r\n          this.form.displayRestrictions = item.value;\r\n        }\r\n      });\r\n    },\r\n    submitForm(type) {\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          // if (\r\n          //   this.form.applicationAreaList &&\r\n          //   this.form.applicationAreaList.length > 0\r\n          // ) {\r\n          //   this.form.applicationArea = this.form.applicationAreaList.join(\",\");\r\n          // } else {\r\n          //   this.form.applicationArea = \"\";\r\n          // }\r\n          if (this.form.applicationArea.length > 0) {\r\n            this.form.applicationArea = this.form.applicationArea.join();\r\n          }\r\n          this.form.scenePicture = JSON.stringify(this.form.scenePictureList);\r\n          this.form.businessNo = this.user.bussinessNo;\r\n          if (this.form.keywords && this.form.keywords.length > 0) {\r\n            this.form.keywords = this.form.keywords.join(\",\");\r\n          } else {\r\n            this.form.keywords = \"\";\r\n          }\r\n          if (this.form.demandType.length > 0) {\r\n            this.form.demandType = this.form.demandType.join();\r\n          }\r\n          if (this.isCreate) {\r\n            createDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          } else {\r\n            this.form.auditStatus = 1;\r\n            editDemand({ ...this.form, isSubmit: type }).then((response) => {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.changeMode();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    toZiyuan() {\r\n      this.$router.push({\r\n        path: \"/user/companyApplyDetail1\",\r\n        query: { key: JSON.stringify(this.info) },\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      const { summary } = this.form;\r\n      if (summary) {\r\n        keywordList(summary).then((res) => {\r\n          const { code, data, msg } = res;\r\n          if (code === 200) {\r\n            this.form.keywords = data;\r\n          } else {\r\n            this.$message.error(msg);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning(\"请输入需求描述\");\r\n      }\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.form.application = \"\";\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.application = res.data.url;\r\n        this.form.applicationName = res.data.name;\r\n      }\r\n    },\r\n    handleAccountRemove(file, fileList) {\r\n      this.form.accountLicence = \"\";\r\n    },\r\n    handleAccountSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.accountLicence = res.data.url;\r\n        this.form.accountLicenceName = res.data.name;\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .company-demand-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      padding-top: 12px;\r\n      padding: 10px 30px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        margin-bottom: 30px;\r\n        width: 100%;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          font-size: 24px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 24px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .detail-page {\r\n        position: relative;\r\n\r\n        .header-small {\r\n          text-align: center;\r\n          display: flex;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 16px;\r\n          margin-bottom: 36px;\r\n\r\n          .red-tag {\r\n            margin-right: 12px;\r\n            width: 3px;\r\n            height: 16px;\r\n            background: #21c9b8;\r\n          }\r\n        }\r\n        .mt_40 {\r\n          margin-top: 40px;\r\n        }\r\n        .file-class {\r\n          width: 733px;\r\n          height: 40px;\r\n          background: #f7f8fa;\r\n          border-radius: 4px;\r\n          padding: 0 20px;\r\n          display: flex;\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 40px;\r\n          position: relative;\r\n\r\n          .el-image {\r\n            margin: 12px 8px 0 0;\r\n          }\r\n          .previwe-class {\r\n            right: 20px;\r\n            position: absolute;\r\n            margin: 8px 0 0 0;\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            text-align: center;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #2f76e0;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n        .status_approving {\r\n          top: 0px;\r\n          right: 20px;\r\n          position: absolute;\r\n        }\r\n      }\r\n\r\n      .edit-page {\r\n        .el-input--medium .el-input__inner {\r\n          width: 90%;\r\n          height: 36px;\r\n          line-height: 36px;\r\n        }\r\n        .el-textarea__inner {\r\n          width: 90%;\r\n        }\r\n        .add-demand-tag {\r\n          margin-right: 10px;\r\n          height: 32px;\r\n          line-height: 32px;\r\n        }\r\n        .el-button--primary {\r\n          background: #fff;\r\n          color: #333;\r\n          border-color: #bfbfbf;\r\n        }\r\n        .el-button--danger {\r\n          background: #fff;\r\n          color: #21c9b8;\r\n          border-color: #21c9b8;\r\n        }\r\n        .tip {\r\n          padding-left: 10px;\r\n          font-size: 12px;\r\n          font-weight: 400;\r\n          color: #8c8c8c;\r\n          line-height: 18px;\r\n        }\r\n      }\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {\r\n        padding: 10px;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #333333;\r\n      }\r\n\r\n      .el-descriptions--medium.is-bordered .el-descriptions-item__label {\r\n        padding: 15px;\r\n        text-align: center;\r\n        font-size: 14px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        width: 200px;\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        margin-top: 20px;\r\n        text-align: center;\r\n        .el-button {\r\n          padding: 12px 55px;\r\n          color: #333;\r\n        }\r\n        .el-button:hover,\r\n        .el-button:focus {\r\n          border-color: #d9d9d9;\r\n          background-color: #fff;\r\n        }\r\n        .el-button--danger {\r\n          margin-left: 30px;\r\n          color: #ffffff;\r\n          background-color: #21c9b8 !important;\r\n          border-color: #21c9b8 !important;\r\n        }\r\n        .el-button--error {\r\n          margin-left: 30px;\r\n          color: #21c9b8;\r\n          background-color: #ffffff;\r\n          border-color: #21c9b8;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .el-input__suffix {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 11%;\r\n}\r\n.el-checkbox {\r\n  font-size: 14px;\r\n  font-weight: 400;\r\n  color: #262626;\r\n  line-height: 18px;\r\n  margin-right: 28px;\r\n}\r\n.edit-page {\r\n  padding-left: 50px;\r\n}\r\n</style>\r\n"]}]}