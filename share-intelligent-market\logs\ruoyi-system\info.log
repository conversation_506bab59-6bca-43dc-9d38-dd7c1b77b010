11:45:03.460 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:45:04.668 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fc8dba1f-c75d-407e-b5ce-d09f6520d2ff_config-0
11:45:04.738 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 42 ms to scan 1 urls, producing 3 keys and 6 values 
11:45:04.782 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 4 keys and 9 values 
11:45:04.793 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
11:45:05.016 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 219 ms to scan 274 urls, producing 0 keys and 0 values 
11:45:05.029 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
11:45:05.042 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
11:45:05.056 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
11:45:05.257 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 199 ms to scan 274 urls, producing 0 keys and 0 values 
11:45:05.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc8dba1f-c75d-407e-b5ce-d09f6520d2ff_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:45:05.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc8dba1f-c75d-407e-b5ce-d09f6520d2ff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$334/1431244283
11:45:05.263 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc8dba1f-c75d-407e-b5ce-d09f6520d2ff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$335/527464124
11:45:05.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc8dba1f-c75d-407e-b5ce-d09f6520d2ff_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:45:05.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc8dba1f-c75d-407e-b5ce-d09f6520d2ff_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:45:05.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc8dba1f-c75d-407e-b5ce-d09f6520d2ff_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:45:07.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc8dba1f-c75d-407e-b5ce-d09f6520d2ff_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750218306791_127.0.0.1_58633
11:45:07.062 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc8dba1f-c75d-407e-b5ce-d09f6520d2ff_config-0] Notify connected event to listeners.
11:45:07.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc8dba1f-c75d-407e-b5ce-d09f6520d2ff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:45:07.063 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc8dba1f-c75d-407e-b5ce-d09f6520d2ff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/2120404899
11:45:07.176 [main] INFO  c.r.s.RuoYiSystemApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
11:45:11.174 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9701"]
11:45:11.174 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:45:11.176 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.62]
11:45:11.519 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:45:13.307 [main] INFO  c.a.d.p.DruidDataSource - [init,998] - {dataSource-1,master} inited
11:45:13.310 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,148] - dynamic-datasource - add a datasource named [master] success
11:45:13.311 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,228] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:45:20.319 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:45:21.027 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7f1538af-76ec-41b5-a126-8e1190583823
11:45:21.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f1538af-76ec-41b5-a126-8e1190583823] RpcClient init label, labels = {module=naming, source=sdk}
11:45:21.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f1538af-76ec-41b5-a126-8e1190583823] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:45:21.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f1538af-76ec-41b5-a126-8e1190583823] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:45:21.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f1538af-76ec-41b5-a126-8e1190583823] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:45:21.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f1538af-76ec-41b5-a126-8e1190583823] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:45:21.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f1538af-76ec-41b5-a126-8e1190583823] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750218321041_127.0.0.1_58781
11:45:21.157 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f1538af-76ec-41b5-a126-8e1190583823] Notify connected event to listeners.
11:45:21.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f1538af-76ec-41b5-a126-8e1190583823] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:45:21.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f1538af-76ec-41b5-a126-8e1190583823] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$344/2120404899
11:45:25.632 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9701"]
11:45:25.701 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-system 192.168.0.68:9701 register finished
11:45:26.077 [main] INFO  c.r.s.RuoYiSystemApplication - [logStarted,61] - Started RuoYiSystemApplication in 23.572 seconds (JVM running for 25.307)
11:45:26.107 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system.yaml, group=DEFAULT_GROUP
11:45:26.107 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system, group=DEFAULT_GROUP
11:45:26.108 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-system-dev.yaml, group=DEFAULT_GROUP
11:45:26.166 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f1538af-76ec-41b5-a126-8e1190583823] Receive server push request, request = NotifySubscriberRequest, requestId = 7
11:45:26.172 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f1538af-76ec-41b5-a126-8e1190583823] Ack server push request, request = NotifySubscriberRequest, requestId = 7
11:45:26.572 [RMI TCP Connection(2)-192.168.0.68] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
