{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\application\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\application\\index.vue", "mtime": 1750311963040}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhcHBsaUxpc3QsDQogIGRlbEFwcGxpLA0KICBvcmRlclN0YXR1c051bSwNCiAgYXBwbGlHcm91bmRPZmYsDQp9IGZyb20gIkAvYXBpL2FwcGxpTWFya2V0IjsNCmltcG9ydCBVc2VyTWVudSBmcm9tICIuLi9jb21wb25lbnRzL3VzZXJNZW51LnZ1ZSI7DQppbXBvcnQgeyBjaGVja0F1dGhTdGF0dXMgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIk9wZXJsb2ciLA0KICBkaWN0czogWyJzeXNfb3Blcl90eXBlIiwgInN5c19jb21tb25fc3RhdHVzIl0sDQogIGNvbXBvbmVudHM6IHsgVXNlck1lbnUgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgYXBwbGlMaXN0OiBbXSwNCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICB9LA0KICAgICAgdG90YWw6IDAsDQogICAgICBsb2FkaW5nOiBmYWxzZSwNCiAgICAgIHNoaXBOdW06IDAsDQogICAgICBpbnZvaWNlTnVtOiAwLA0KICAgICAgY29tcGFueVN0YXR1czogIjAiLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRVc2VyKCk7DQogICAgdGhpcy5nZXRPcmRlclN0YXR1c051bSgpOw0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0T3JkZXJTdGF0dXNOdW0oKSB7DQogICAgICBvcmRlclN0YXR1c051bSgpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICh0aGlzLnNoaXBOdW0gPSByZXMuZGF0YS53YWl0U2VuZE51bSksDQogICAgICAgICAgICAodGhpcy5pbnZvaWNlTnVtID0gcmVzLmRhdGEud2FpdE1ha2VOdW0pOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGV0IHBhcmFtcyA9IHsNCiAgICAgICAgY3JlYXRlQnk6IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIudXNlcklkLA0KICAgICAgICBwYWdlTnVtOiB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0sDQogICAgICAgIHBhZ2VTaXplOiA1LA0KICAgICAgfTsNCiAgICAgIGFwcGxpTGlzdChwYXJhbXMpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICB0aGlzLmFwcGxpTGlzdCA9IHJlcy5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5pON5L2c5pel5b+X57G75Z6L5a2X5YW457+76K+RDQogICAgdHlwZUZvcm1hdChyb3csIGNvbHVtbikgew0KICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0RGljdExhYmVsKA0KICAgICAgICB0aGlzLmRpY3QudHlwZS5zeXNfb3Blcl90eXBlLA0KICAgICAgICByb3cuYnVzaW5lc3NUeXBlDQogICAgICApOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShpZCkgew0KICAgICAgdGhpcy4kbW9kYWwNCiAgICAgICAgLmNvbmZpcm0oIuaYr+WQpuehruiupOWIoOmZpOivpeaVsOaNrumhue+8nyIpDQogICAgICAgIC50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICByZXR1cm4gZGVsQXBwbGkoaWQpOw0KICAgICAgICB9KQ0KICAgICAgICAudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5pON5L2c5oiQ5YqfISIpOw0KICAgICAgICB9KQ0KICAgICAgICAuY2F0Y2goKCkgPT4ge30pOw0KICAgIH0sDQogICAgaGFuZGxlQ3VycmVudENoYW5nZShwYWdlTnVtKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSBwYWdlTnVtOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBwdWJsaXNoQXBwbGkoKSB7DQogICAgICBpZiAodGhpcy5jb21wYW55U3RhdHVzID09ICIwIikgew0KICAgICAgICB0aGlzLiRjb25maXJtKCLlvZPliY3nlKjmiLfmnKrlrozmiJDmnI3liqHllYborqTor4HvvIzor7forqTor4HlkI7lho3ov5vooYzmk43kvZzjgIIiLCAiIiwgew0KICAgICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi5Y676K6k6K+BIiwNCiAgICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgICB0eXBlOiAid2FybmluZyIsDQogICAgICAgIH0pDQogICAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICAgICAgICBwYXRoOiAiL3VzZXIvc3BDZXJ0aWZpY2F0aW9uIiwNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0pDQogICAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsNCiAgICAgICAgICBwYXRoOiAiL3VzZXIvcHVibGlzaEFwcGxpIiwNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBnb0RldGFpbChpZCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICBwYXRoOiAiL3VzZXIvYXBwbGlEZXRhaWwiLA0KICAgICAgICBxdWVyeTogew0KICAgICAgICAgIGlkLA0KICAgICAgICB9LA0KICAgICAgfSk7DQogICAgfSwNCiAgICBnb0VkaXQoaWQsIHN0YXR1cykgew0KICAgICAgaWYgKHN0YXR1cyA9PSAyKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi5b2T5YmN5bqU55So5bey5LiK5p6277yM6K+35LiL5p625ZCO5YaN6L+b6KGM57yW6L6R77yBIik7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7DQogICAgICAgICAgcGF0aDogIi91c2VyL3B1Ymxpc2hBcHBsaSIsDQogICAgICAgICAgcXVlcnk6IHsNCiAgICAgICAgICAgIGlkLA0KICAgICAgICAgIH0sDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5LiK5p62DQogICAgYXBwbGlHcm91bmQoaWQsIGFwcFN0YXRlKSB7DQogICAgICBsZXQgZGF0YSA9IHsNCiAgICAgICAgaWQsDQogICAgICAgIGFwcFN0YXRlLA0KICAgICAgfTsNCiAgICAgIGFwcGxpR3JvdW5kT2ZmKGRhdGEpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfISIpOw0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOS4i+aetg0KICAgIG9mZlNoZWxmKGlkLCBhcHBTdGF0ZSkgew0KICAgICAgbGV0IGRhdGEgPSB7DQogICAgICAgIGlkLA0KICAgICAgICBhcHBTdGF0ZSwNCiAgICAgIH07DQogICAgICBhcHBsaUdyb3VuZE9mZihkYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyEiKTsNCiAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgICAvLyB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuW9k+WJjeW6lOeUqOWtmOWcqOi/m+ihjOS4reiuouWNle+8jOaXoOazleS4i+aetu+8gSIpOw0KICAgIH0sDQogICAgZ2V0VXNlcigpIHsNCiAgICAgIGNoZWNrQXV0aFN0YXR1cygpLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuY29tcGFueVN0YXR1cyA9IHJlcy5kYXRhLmNvbXBhbnlTdGF0dXM7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/application", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\">\r\n          <div class=\"applicationDesc\">\r\n            <div class=\"desc_left\">\r\n              <div>\r\n                商品发布说明:\r\n                SaaS、API商品发布前需先进行接入调试，接入流程请查看商品接入指南。\r\n              </div>\r\n              <div>\r\n                商品修改说明:\r\n                只有在销售商品才能进行修改操作，提交修改申请后，需要等待运营审核后才能生效。\r\n              </div>\r\n              <div>\r\n                商品升级说明:\r\n                SaaS商品上架后，如需支持用户升级已购买的规格，请在操作中设置升级规则。\r\n              </div>\r\n              <div>\r\n                商品下架说明:\r\n                为保障用户正常访问，商品下架需由云商店审核通过后方可下架。下架全部商品规格后，商品进入“已停售”状态，停售后将不会在商店中呈现和售卖，但不影响已购用户的使用和续订。\r\n              </div>\r\n            </div>\r\n            <div class=\"driver\"></div>\r\n            <div class=\"desc_right\">\r\n              <div class=\"statistics\">\r\n                <div>待发货 {{ shipNum }}</div>\r\n                <div class=\"statisticsItem\">待开票 {{ invoiceNum }}</div>\r\n              </div>\r\n              <div class=\"submitStyle\">\r\n                <div class=\"buttonStyle\" @click=\"publishAppli\">发布应用</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"appliTitle\">我的应用</div>\r\n          <div class=\"appliPart\" v-loading=\"loading\">\r\n            <div class=\"everyItem\" v-for=\"item in appliList\" :key=\"item.id\">\r\n              <div class=\"item_img\">\r\n                <img :src=\"item.appLogo\" alt=\"\" />\r\n              </div>\r\n              <div class=\"item_text\">\r\n                <div class=\"title\">{{ item.appName }}</div>\r\n                <div class=\"desc\">{{ item.briefInto }}</div>\r\n              </div>\r\n              <div class=\"timeStyle\">\r\n                <div class=\"tabHeader\">交付方式</div>\r\n                <div style=\"margin-top: 10px\">\r\n                  {{ item.delivery == \"0\" ? \"Saas服务\" : \"本地部署\" }}\r\n                </div>\r\n              </div>\r\n              <div class=\"timeStyle\">\r\n                <div class=\"tabHeader\">创建时间</div>\r\n                <div style=\"margin-top: 10px\">{{ item.createTime }}</div>\r\n              </div>\r\n              <div class=\"timeStyle\">\r\n                <div class=\"tabHeader\">应用状态</div>\r\n                <div style=\"margin-top: 10px\">\r\n                  {{\r\n                    item.appState == 0\r\n                      ? \"待配置\"\r\n                      : item.appState == 1\r\n                      ? \"待上架\"\r\n                      : item.appState == 2\r\n                      ? \"已上架\"\r\n                      : \"已下架\"\r\n                  }}\r\n                </div>\r\n              </div>\r\n              <div class=\"option\">\r\n                <div style=\"display: flex\">\r\n                  <div\r\n                    class=\"buttonStyle\"\r\n                    @click=\"goEdit(item.id, item.appState)\"\r\n                  >\r\n                    编辑\r\n                  </div>\r\n                  <div\r\n                    v-if=\"item.appState == 2\"\r\n                    class=\"buttonStyle\"\r\n                    @click=\"offShelf(item.id, item.appState)\"\r\n                  >\r\n                    下架\r\n                  </div>\r\n                  <div\r\n                    v-if=\"item.appState == 3\"\r\n                    class=\"buttonStyle\"\r\n                    @click=\"appliGround(item.id, item.appState)\"\r\n                  >\r\n                    上架\r\n                  </div>\r\n                </div>\r\n                <div style=\"display: flex; margin-top: 10px\">\r\n                  <div class=\"buttonStyle\" @click=\"goDetail(item.id)\">详情</div>\r\n                  <div class=\"buttonStyle\" @click=\"handleDelete(item.id)\">\r\n                    删除\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <div style=\"text-align: center; margin-top: 45px\">\r\n              <el-pagination\r\n                v-show=\"total > 0\"\r\n                background\r\n                layout=\"prev, pager, next\"\r\n                :page-size=\"5\"\r\n                :current-page.sync=\"queryParams.pageNum\"\r\n                @current-change=\"handleCurrentChange\"\r\n                :total=\"total\"\r\n              >\r\n              </el-pagination>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  appliList,\r\n  delAppli,\r\n  orderStatusNum,\r\n  appliGroundOff,\r\n} from \"@/api/appliMarket\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { checkAuthStatus } from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      appliList: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      loading: false,\r\n      shipNum: 0,\r\n      invoiceNum: 0,\r\n      companyStatus: \"0\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getUser();\r\n    this.getOrderStatusNum();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getOrderStatusNum() {\r\n      orderStatusNum().then((res) => {\r\n        if (res.code === 200) {\r\n          (this.shipNum = res.data.waitSendNum),\r\n            (this.invoiceNum = res.data.waitMakeNum);\r\n        }\r\n      });\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        createBy: this.$store.state.user.userId,\r\n        pageNum: this.queryParams.pageNum,\r\n        pageSize: 5,\r\n      };\r\n      appliList(params).then((res) => {\r\n        this.appliList = res.rows;\r\n        this.total = res.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 操作日志类型字典翻译\r\n    typeFormat(row, column) {\r\n      return this.selectDictLabel(\r\n        this.dict.type.sys_oper_type,\r\n        row.businessType\r\n      );\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(id) {\r\n      this.$modal\r\n        .confirm(\"是否确认删除该数据项？\")\r\n        .then(function () {\r\n          return delAppli(id);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(\"操作成功!\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    publishAppli() {\r\n      if (this.companyStatus == \"0\") {\r\n        this.$confirm(\"当前用户未完成服务商认证，请认证后再进行操作。\", \"\", {\r\n          confirmButtonText: \"去认证\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push({\r\n              path: \"/user/spCertification\",\r\n            });\r\n          })\r\n          .catch(() => {});\r\n      } else {\r\n        this.$router.push({\r\n          path: \"/user/publishAppli\",\r\n        });\r\n      }\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push({\r\n        path: \"/user/appliDetail\",\r\n        query: {\r\n          id,\r\n        },\r\n      });\r\n    },\r\n    goEdit(id, status) {\r\n      if (status == 2) {\r\n        this.$message.warning(\"当前应用已上架，请下架后再进行编辑！\");\r\n      } else {\r\n        this.$router.push({\r\n          path: \"/user/publishAppli\",\r\n          query: {\r\n            id,\r\n          },\r\n        });\r\n      }\r\n    },\r\n    // 上架\r\n    appliGround(id, appState) {\r\n      let data = {\r\n        id,\r\n        appState,\r\n      };\r\n      appliGroundOff(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message.success(\"操作成功!\");\r\n          this.getList();\r\n        }\r\n      });\r\n    },\r\n    // 下架\r\n    offShelf(id, appState) {\r\n      let data = {\r\n        id,\r\n        appState,\r\n      };\r\n      appliGroundOff(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message.success(\"操作成功!\");\r\n          this.getList();\r\n        }\r\n      });\r\n      // this.$message.warning(\"当前应用存在进行中订单，无法下架！\");\r\n    },\r\n    getUser() {\r\n      checkAuthStatus().then((res) => {\r\n        if (res.code === 200) {\r\n          this.companyStatus = res.data.companyStatus;\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  // background: rgb(242, 248, 255);\r\n  .applicationDesc {\r\n    width: 100%;\r\n    height: 150px;\r\n    display: flex;\r\n    padding: 20px;\r\n    .desc_left {\r\n      width: 80%;\r\n    }\r\n    .driver {\r\n      width: 2px;\r\n      height: 100%;\r\n      background: #ccc;\r\n      margin: 0 5%;\r\n    }\r\n    .desc_right {\r\n      width: 30%;\r\n      display: flex;\r\n      align-items: center;\r\n      .statistics {\r\n        margin-left: 10px;\r\n        width: 50%;\r\n        .statisticsItem {\r\n          margin-top: 12px;\r\n        }\r\n      }\r\n      .submitStyle {\r\n        width: 50%;\r\n        display: flex;\r\n        justify-content: right;\r\n        .buttonStyle {\r\n          width: 100px;\r\n          padding: 10px;\r\n          background: #21c9b8;\r\n          color: #ffffff;\r\n          text-align: center;\r\n          cursor: pointer;\r\n          border-radius: 4px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .appliTitle {\r\n    font-size: 22px;\r\n    margin: 30px;\r\n    font-weight: 600;\r\n  }\r\n  .appliPart {\r\n    .everyItem {\r\n      width: 100%;\r\n      height: 150px;\r\n      background: #ffffff;\r\n      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n      display: flex;\r\n      padding: 20px;\r\n      align-items: center;\r\n      margin-top: 20px;\r\n      .item_img {\r\n        width: 17%;\r\n        height: 100%;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .item_text {\r\n        width: 20%;\r\n        margin-left: 2%;\r\n        .title {\r\n          font-size: 16px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #333333;\r\n        }\r\n        .desc {\r\n          margin-top: 10px;\r\n          font-size: 14px;\r\n          font-family: Microsoft YaHei;\r\n          font-weight: 400;\r\n          color: #999999;\r\n        }\r\n      }\r\n      // .delivery {\r\n      //   margin-left: 2%;\r\n      // }\r\n      .timeStyle {\r\n        width: 15%;\r\n        text-align: center;\r\n      }\r\n      .option {\r\n        // display: flex;\r\n        // justify-content: center;\r\n        width: 18%;\r\n        .buttonStyle {\r\n          color: #21c9b8;\r\n          cursor: pointer;\r\n          margin-left: 20%;\r\n        }\r\n      }\r\n      .tabHeader {\r\n        font-family: Microsoft YaHei;\r\n        font-weight: 400;\r\n        color: #999999;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}