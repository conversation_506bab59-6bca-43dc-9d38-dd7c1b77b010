{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\supplyDemand\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\supplyDemand\\index.vue", "mtime": 1750311963085}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF91c2VyTWVudSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiKSk7CnZhciBfZGVtYW5kID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL2NvbXBvbmVudHMvZGVtYW5kLnZ1ZSIpKTsKdmFyIF9zdXBwbHkgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vY29tcG9uZW50cy9zdXBwbHkudnVlIikpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogIlVzZXIiLAogIGNvbXBvbmVudHM6IHsKICAgIFVzZXJNZW51OiBfdXNlck1lbnUuZGVmYXVsdCwKICAgIGRlbWFuZDogX2RlbWFuZC5kZWZhdWx0LAogICAgc3VwcGx5OiBfc3VwcGx5LmRlZmF1bHQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBkZW1hbmRTdXBwbHk6ICIxIiwKICAgICAgdHlwZUxpc3Q6IFtdLAogICAgICBzdGF0dXNMaXN0OiBbXSwKICAgICAgZm9ybTogewogICAgICAgIG9yZGVyU3RhdHVzOiAiIgogICAgICB9CiAgICB9OwogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHt9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZUNsaWNrOiBmdW5jdGlvbiBoYW5kbGVDbGljayh2YWwpIHsKICAgICAgdGhpcy5kZW1hbmRTdXBwbHkgPSB2YWw7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_demand", "_supply", "name", "components", "UserMenu", "demand", "supply", "data", "demandSupply", "typeList", "statusList", "form", "orderStatus", "created", "methods", "handleClick", "val"], "sources": ["src/views/system/user/supplyDemand/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div>\r\n          <!-- 顶部导航 -->\r\n          <div class=\"navStyle\">\r\n            <div\r\n              class=\"navStyle-left\"\r\n              @click=\"handleClick('1')\"\r\n              :style=\"\r\n                demandSupply == '1'\r\n                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'\r\n                  : 'border-bottom: none;cursor: pointer;'\r\n              \"\r\n            >\r\n              <div class=\"text\">我的需求</div>\r\n            </div>\r\n            <div\r\n              class=\"navStyle-left\"\r\n              @click=\"handleClick('2')\"\r\n              style=\"margin-left: 24px\"\r\n              :style=\"\r\n                demandSupply == '2'\r\n                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'\r\n                  : 'border-bottom: none;cursor: pointer;'\r\n              \"\r\n            >\r\n              <div class=\"text\">我的供给</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"typeStyle\">\r\n            <div class=\"typeLine\"></div>\r\n            <div class=\"typeText\">\r\n              我的{{ demandSupply == \"1\" ? \"需求\" : \"供给\" }}\r\n            </div>\r\n          </div>\r\n          <div style=\"margin-top: 20px\">\r\n            <demand v-if=\"demandSupply == '1'\"></demand>\r\n            <supply v-if=\"demandSupply == '2'\"></supply>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport demand from \"./components/demand.vue\";\r\nimport supply from \"./components/supply.vue\";\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu, demand, supply },\r\n  data() {\r\n    return {\r\n      demandSupply: \"1\",\r\n      typeList: [],\r\n      statusList: [],\r\n      form: {\r\n        orderStatus: \"\",\r\n      },\r\n    };\r\n  },\r\n  created() {},\r\n  methods: {\r\n    handleClick(val) {\r\n      this.demandSupply = val;\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 1080px;\r\n}\r\n.navStyle {\r\n  height: 50px;\r\n  border-bottom: 1px solid #ccc;\r\n  display: flex;\r\n  align-items: center;\r\n  .navStyle-left {\r\n    display: flex;\r\n    align-items: center;\r\n    height: 50px;\r\n    border-bottom: 2px solid #21c9b8;\r\n  }\r\n  .text {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #21c9b8;\r\n  }\r\n  .navStyle-right {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #333333;\r\n    margin-left: auto;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.typeStyle {\r\n  width: 100%;\r\n  height: 50px;\r\n  display: flex;\r\n  align-items: center;\r\n  background: #ffffff;\r\n  margin-top: 20px;\r\n  .typeLine {\r\n    width: 2px;\r\n    height: 20px;\r\n    background: #10af9f;\r\n  }\r\n  .typeText {\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 400;\r\n    font-size: 14px;\r\n    color: #333333;\r\n    margin-left: 17px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAkDA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAG,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA,iBAAA;IAAAC,MAAA,EAAAA,eAAA;IAAAC,MAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,QAAA;MACAC,UAAA;MACAC,IAAA;QACAC,WAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;EACAC,OAAA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAAR,YAAA,GAAAQ,GAAA;IACA;EACA;AACA", "ignoreList": []}]}