{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\addPolicy.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\addPolicy.vue", "mtime": 1750311962954}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_demand", "require", "name", "data", "user", "$store", "state", "loading", "form", "title", "undefined", "policyId", "fileList", "contractPerson", "contractPhone", "tel", "status", "rules", "required", "message", "trigger", "created", "_this$$route$params", "$route", "params", "id", "methods", "onSubmit", "_this", "$refs", "validate", "valid", "editPolicyApply", "_objectSpread2", "default", "then", "res", "code", "msg", "$message", "success", "$router", "back", "error", "finally"], "sources": ["src/views/form/addPolicy.vue"], "sourcesContent": ["<template>\r\n  <div class=\"intention-page\">\r\n    <div class=\"intention-page-header\">\r\n      <div class=\"banner\">\r\n        <img\r\n          src=\"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230210/1676034162343360.webp\"\r\n          alt=\"在线申报\"\r\n        />\r\n      </div>\r\n    </div>\r\n    <div class=\"intention-page-title\">在线申报</div>\r\n    <div v-loading=\"loading\" class=\"card-container intention-form\">\r\n      <div class=\"form-content\">\r\n        <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-width=\"80px\">\r\n          <el-form-item label=\"政策名称\">\r\n            {{ form.title }}\r\n          </el-form-item>\r\n          <el-form-item label=\"联系人\" prop=\"contractPerson\">\r\n            <el-input v-model=\"form.contractPerson\" placeholder=\"请输入联系人\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"联系方式\" prop=\"contractPhone\">\r\n            <el-input disabled v-model=\"form.contractPhone\"></el-input>\r\n          </el-form-item>\r\n          <el-form-item label=\"上传文件\" prop=\"fileList\">\r\n            <FileUpload v-model=\"form.fileList\" />\r\n          </el-form-item>\r\n          <el-form-item class=\"footer-submit\">\r\n            <el-button @click=\"onSubmit(1)\" type=\"primary\" plain>暂存草稿</el-button>\r\n            <el-button type=\"primary\" @click=\"onSubmit(2)\">提交审核</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nimport {editPolicyApply} from \"@/api/system/demand\";\r\n\r\nexport default {\r\n  name: \"addPolicy\",\r\n  data() {\r\n    const { user } = this.$store.state;\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        title: undefined,\r\n        policyId: undefined,\r\n        fileList: [],\r\n        contractPerson: user.name,\r\n        contractPhone: user.tel,\r\n        status: undefined,\r\n      },\r\n      rules: {\r\n        contractPerson: [\r\n          { required: true, message: \"请输入联系人\", trigger: \"blur\" },\r\n        ],\r\n        contractPhone: [\r\n          { required: true, message: \"请先维护联系方式\", trigger: \"blur\" },\r\n        ],\r\n        fileList: [\r\n          { required: true, message: \"请上传文件\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    }\r\n  },\r\n  created() {\r\n    const { id, title } = this.$route.params;\r\n    if (id) {\r\n      this.form.title = title;\r\n      this.form.policyId = id;\r\n    }\r\n  },\r\n  methods: {\r\n    onSubmit(status) {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          editPolicyApply({\r\n            ...this.form,\r\n            status,\r\n          }).then((res) => {\r\n            const { code, msg } = res;\r\n            if (code === 200) {\r\n              this.$message.success(\"提交成功\");\r\n              this.$router.back();\r\n            } else {\r\n              this.$message.error(msg || \"提交失败\");\r\n            }\r\n          }).finally(() => this.loading = false)\r\n        }\r\n      })\r\n\r\n    },\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n.intention-page {\r\n  background-color: #F4F5F9;\r\n  padding-bottom: 80px;\r\n  &-header {\r\n    background-color: #FFFFFF;\r\n    .banner {\r\n      width: 100%;\r\n      height: 540px;\r\n      background-color: #f5f5f5;\r\n      img {\r\n        width: 100%;\r\n        height: 540px;\r\n        object-fit: fill;\r\n      }\r\n    }\r\n    .body {\r\n      padding: 60px 0;\r\n    }\r\n  }\r\n  &-title {\r\n    font-size: 40px;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    line-height: 40px;\r\n    text-align: center;\r\n    padding: 60px 0;\r\n  }\r\n  .intention-form {\r\n    @include flexCenter;\r\n    height: 664px;\r\n    background-color: #FFFFFF;\r\n    margin-bottom: 80px;\r\n    .form-content {\r\n      width: 750px;\r\n      .footer-submit {\r\n        display: flex;\r\n        flex-direction: row;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-top: 40px;\r\n        .el-button {\r\n          width: 160px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAsCA,IAAAA,OAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA,IAAAC,IAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,IAAA;IACA;MACAG,OAAA;MACAC,IAAA;QACAC,KAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,QAAA;QACAC,cAAA,EAAAT,IAAA,CAAAF,IAAA;QACAY,aAAA,EAAAV,IAAA,CAAAW,GAAA;QACAC,MAAA,EAAAN;MACA;MACAO,KAAA;QACAJ,cAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,aAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,mBAAA,QAAAC,MAAA,CAAAC,MAAA;MAAAC,EAAA,GAAAH,mBAAA,CAAAG,EAAA;MAAAhB,KAAA,GAAAa,mBAAA,CAAAb,KAAA;IACA,IAAAgB,EAAA;MACA,KAAAjB,IAAA,CAAAC,KAAA,GAAAA,KAAA;MACA,KAAAD,IAAA,CAAAG,QAAA,GAAAc,EAAA;IACA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAAX,MAAA;MAAA,IAAAY,KAAA;MACA,KAAAC,KAAA,CAAArB,IAAA,CAAAsB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,KAAA,CAAArB,OAAA;UACA,IAAAyB,uBAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAN,KAAA,CAAApB,IAAA;YACAQ,MAAA,EAAAA;UAAA,EACA,EAAAmB,IAAA,WAAAC,GAAA;YACA,IAAAC,IAAA,GAAAD,GAAA,CAAAC,IAAA;cAAAC,GAAA,GAAAF,GAAA,CAAAE,GAAA;YACA,IAAAD,IAAA;cACAT,KAAA,CAAAW,QAAA,CAAAC,OAAA;cACAZ,KAAA,CAAAa,OAAA,CAAAC,IAAA;YACA;cACAd,KAAA,CAAAW,QAAA,CAAAI,KAAA,CAAAL,GAAA;YACA;UACA,GAAAM,OAAA;YAAA,OAAAhB,KAAA,CAAArB,OAAA;UAAA;QACA;MACA;IAEA;EACA;AACA", "ignoreList": []}]}