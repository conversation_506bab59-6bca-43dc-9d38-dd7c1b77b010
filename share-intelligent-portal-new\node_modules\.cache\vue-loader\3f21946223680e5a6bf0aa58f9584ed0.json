{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\demand\\index.vue?vue&type=template&id=38b6aa28&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\demand\\index.vue", "mtime": 1750311962949}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}