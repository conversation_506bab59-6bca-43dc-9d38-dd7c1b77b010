<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
        <div>
          <!-- 顶部导航 -->
          <div class="navStyle">
            <div
              class="navStyle-left"
              @click="handleClick('1')"
              :style="
                demandSupply == '1'
                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'
                  : 'border-bottom: none;cursor: pointer;'
              "
            >
              <div class="text">我的需求</div>
            </div>
            <div
              class="navStyle-left"
              @click="handleClick('2')"
              style="margin-left: 24px"
              :style="
                demandSupply == '2'
                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'
                  : 'border-bottom: none;cursor: pointer;'
              "
            >
              <div class="text">我的供给</div>
            </div>
          </div>
          <div class="typeStyle">
            <div class="typeLine"></div>
            <div class="typeText">
              我的{{ demandSupply == "1" ? "需求" : "供给" }}
            </div>
          </div>
          <div style="margin-top: 20px">
            <demand v-if="demandSupply == '1'"></demand>
            <supply v-if="demandSupply == '2'"></supply>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import UserMenu from "../components/userMenu.vue";
import demand from "./components/demand.vue";
import supply from "./components/supply.vue";
export default {
  name: "User",
  components: { UserMenu, demand, supply },
  data() {
    return {
      demandSupply: "1",
      typeList: [],
      statusList: [],
      form: {
        orderStatus: "",
      },
    };
  },
  created() {},
  methods: {
    handleClick(val) {
      this.demandSupply = val;
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));
  height: 1080px;
}
.navStyle {
  height: 50px;
  border-bottom: 1px solid #ccc;
  display: flex;
  align-items: center;
  .navStyle-left {
    display: flex;
    align-items: center;
    height: 50px;
    border-bottom: 2px solid #21c9b8;
  }
  .text {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #21c9b8;
  }
  .navStyle-right {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    margin-left: auto;
    cursor: pointer;
  }
}
.typeStyle {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
  background: #ffffff;
  margin-top: 20px;
  .typeLine {
    width: 2px;
    height: 20px;
    background: #10af9f;
  }
  .typeText {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    margin-left: 17px;
  }
}
</style>
