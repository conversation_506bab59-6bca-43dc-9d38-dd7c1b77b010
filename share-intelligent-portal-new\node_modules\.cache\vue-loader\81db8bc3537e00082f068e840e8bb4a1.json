{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\role\\index.vue?vue&type=template&id=5fc99eaf", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\role\\index.vue", "mtime": 1750311963037}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}