import request from '@/utils/request'

// 查询创业孵化信息列表
export function listIncubationInfo(query) {
  return request({
    url: '/system/incubationInfo/list',
    method: 'get',
    params: query
  })
}

// 查询创业孵化信息详细
export function getIncubationInfo(id) {
  return request({
    url: '/system/incubationInfo/' + id,
    method: 'get'
  })
}

// 新增创业孵化信息
export function addIncubationInfo(data) {
  return request({
    url: '/system/incubationInfo',
    method: 'post',
    data: data
  })
}

// 修改创业孵化信息
export function updateIncubationInfo(data) {
  return request({
    url: '/system/incubationInfo',
    method: 'put',
    data: data
  })
}

// 删除创业孵化信息
export function delIncubationInfo(id) {
  return request({
    url: '/system/incubationInfo/' + id,
    method: 'delete'
  })
}
