{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\index.vue", "mtime": 1750311962980}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ramda", "require", "_notice", "_zhm", "_default", "exports", "default", "name", "components", "data", "noticeLoading", "form", "title", "undefined", "notices", "letItems", "rightItems", "checkedArr", "policyItems", "created", "getNoticeData", "getTypePolicy", "getLeftItems", "getRightItems", "watch", "$route", "to", "from", "$router", "go", "mounted", "_this", "specialLoc", "query", "inter", "setInterval", "target", "$refs", "clearInterval", "scrollIntoView", "methods", "_this2", "getListByText", "typeTop", "pageNum", "pageSize", "then", "res", "code", "rows", "finally", "_this3", "listPolicyByType", "_this4", "_this5", "listByTypePolicy", "items", "forEachObjIndexed", "value", "key", "push", "children", "map", "item", "id", "labelCode", "text", "labelText", "search", "concat", "onCancel", "onConfirm", "length", "$message", "info", "params", "console", "log"], "sources": ["src/views/policy/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"policy-page\">\r\n    <div class=\"policy-page-header\">\r\n      <div class=\"banner\">\r\n        <img src=\"../../assets/policy/banner.png\" alt=\"政策大厅\" />\r\n      </div>\r\n      <div class=\"body\">\r\n        <div class=\"enterprise-list-title-box\">\r\n          <div class=\"enterprise-list-divider\"></div>\r\n          <div class=\"enterprise-list-title\">链政策</div>\r\n          <div class=\"enterprise-list-divider\"></div>\r\n        </div>\r\n        <!-- <header-tag title=\"链政策\" /> -->\r\n        <div class=\"search-box\">\r\n          <el-form ref=\"form\" class=\"search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.title\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"search-input\"\r\n              >\r\n                <el-button slot=\"append\" class=\"search-btn\" @click=\"search\"\r\n                  >搜索\r\n                </el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container policy-page-body\">\r\n      <div class=\"card\">\r\n        <router-link to=\"/policymode\" class=\"card-left\">\r\n          <el-image\r\n            fit=\"contain\"\r\n            class=\"notice-icon\"\r\n            :src=\"require('../../assets/policy/zixun.png')\"\r\n          />\r\n          <div class=\"imageBtn\">查看更多</div>\r\n        </router-link>\r\n        <div v-loading=\"noticeLoading\" class=\"card-right notice-content\">\r\n          <template v-if=\"notices.length > 0\">\r\n            <router-link\r\n              class=\"notice-item\"\r\n              v-for=\"item in notices\"\r\n              :key=\"item.id\"\r\n              :to=\"`/policyDetail?id=${item.id}`\"\r\n            >\r\n              <div class=\"notice-item-content\">\r\n                <div class=\"title\">{{ item.title }}</div>\r\n                <div class=\"footer\">\r\n                  <div class=\"company\">{{ item.company }}</div>\r\n                  <div class=\"date\">{{ item.updateTime }}</div>\r\n                  <!-- <div class=\"date\">发文日期：{{ item.updateTime }}</div> -->\r\n                </div>\r\n              </div>\r\n              <div class=\"notice-item-btn\">查看详情</div>\r\n            </router-link>\r\n          </template>\r\n          <template v-else>\r\n            <el-empty />\r\n          </template>\r\n        </div>\r\n      </div>\r\n      <div>\r\n        <div class=\"card-left\">\r\n          <el-image\r\n            fit=\"contain\"\r\n            class=\"draw-icon\"\r\n            :src=\"require('../../assets/policy/huaxiang.png')\"\r\n          />\r\n        </div>\r\n        <div class=\"card-right policy-content\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\" v-for=\"item in policyItems\" :key=\"item.key\">\r\n              <div class=\"policy-card\">\r\n                <el-checkbox-group v-model=\"checkedArr\">\r\n                  <el-checkbox\r\n                    class=\"checkbox-policy\"\r\n                    v-for=\"code in item.children\"\r\n                    :label=\"code.code\"\r\n                    :key=\"code.code\"\r\n                    >{{ code.text }}</el-checkbox\r\n                  >\r\n                </el-checkbox-group>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <div class=\"policy-content-footer\">\r\n            <div @click=\"onCancel\" class=\"btn-cancel\">取消</div>\r\n            <div @click=\"onConfirm\" class=\"btn-confirm\">确定</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"card\">\r\n        <router-link to=\"/policyDeclare\" class=\"card-left\">\r\n          <el-image\r\n            fit=\"fill\"\r\n            class=\"apply-icon\"\r\n            :src=\"require('../../assets/policy/shenbao.png')\"\r\n          />\r\n          <div class=\"imageBtn\">在线查看</div>\r\n        </router-link>\r\n        <div class=\"card-right apply-content\">\r\n          <div class=\"apply-card\">\r\n            <router-link to=\"/policyDeclare\" class=\"apply-card-header\">\r\n              <div class=\"left\">\r\n                <div class=\"tag\" />\r\n                <div class=\"title\">科创平台</div>\r\n              </div>\r\n              <div class=\"right\">更多>></div>\r\n            </router-link>\r\n            <div class=\"apply-card-body\">\r\n              <template v-if=\"letItems.length > 0\">\r\n                <router-link\r\n                  class=\"item\"\r\n                  :to=\"`/policyDeclareDetail?id=${item.id}`\"\r\n                  v-for=\"item in letItems\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div class=\"item-tag\" />\r\n                  <div class=\"item-text\">{{ item.title }}</div>\r\n                </router-link>\r\n              </template>\r\n              <template v-else>\r\n                <el-empty />\r\n              </template>\r\n            </div>\r\n          </div>\r\n          <div class=\"apply-card\">\r\n            <router-link to=\"/policyDeclare\" class=\"apply-card-header\">\r\n              <div class=\"left\">\r\n                <div class=\"tag\" />\r\n                <div class=\"title\">人才政策</div>\r\n              </div>\r\n              <div class=\"right\">更多>></div>\r\n            </router-link>\r\n            <div class=\"apply-card-body\">\r\n              <template v-if=\"letItems.length > 0\">\r\n                <router-link\r\n                  class=\"item\"\r\n                  :to=\"`/policyDeclareDetail?id=${item.id}`\"\r\n                  v-for=\"item in rightItems\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div class=\"item-tag\" />\r\n                  <div class=\"item-text\">{{ item.title }}</div>\r\n                </router-link>\r\n              </template>\r\n              <template v-else>\r\n                <el-empty />\r\n              </template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { forEachObjIndexed, map } from \"ramda\";\r\n// import HeaderTag from \"@/views/components/home/<USER>\";\r\nimport { getListByText } from \"@/api/notice\";\r\nimport { listPolicyByType, listByTypePolicy } from \"@/api/zhm\";\r\n// import { policymode } from \"../components/home/<USER>\";\r\n\r\nexport default {\r\n  name: \"policyPage\",\r\n  components: {\r\n    // HeaderTag,\r\n    // policymode,\r\n  },\r\n  data() {\r\n    return {\r\n      noticeLoading: false,\r\n      form: {\r\n        title: undefined,\r\n      },\r\n      notices: [],\r\n      letItems: [],\r\n      rightItems: [],\r\n      checkedArr: [],\r\n      policyItems: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getNoticeData();\r\n    this.getTypePolicy();\r\n    this.getLeftItems();\r\n    this.getRightItems();\r\n  },\r\n  watch: {\r\n    $route(to, from) {\r\n      // to表示要跳转的路由，from表示从哪儿跳的路由   to.path\r\n      this.$router.go(0);\r\n    },\r\n  },\r\n  mounted() {\r\n    let specialLoc = this.$route.query.specialLoc;\r\n    if (specialLoc) {\r\n      let inter = setInterval(() => {\r\n        let target = this.$refs[specialLoc];\r\n        if (target) {\r\n          clearInterval(inter);\r\n          target.scrollIntoView();\r\n        }\r\n      }, 100);\r\n    }\r\n  },\r\n  methods: {\r\n    getNoticeData() {\r\n      this.noticeLoading = true;\r\n      getListByText({\r\n        typeTop: 2,\r\n        pageNum: 1,\r\n        pageSize: 4,\r\n      })\r\n        .then((res) => {\r\n          const { code, rows } = res;\r\n          if (code === 200) {\r\n            this.notices = rows;\r\n          }\r\n        })\r\n        .finally(() => (this.noticeLoading = false));\r\n    },\r\n    getLeftItems() {\r\n      listPolicyByType(100).then((res) => {\r\n        const { code, rows } = res;\r\n        if (code === 200) {\r\n          this.letItems = rows;\r\n        }\r\n      });\r\n    },\r\n    getRightItems() {\r\n      listPolicyByType(101).then((res) => {\r\n        const { code, rows } = res;\r\n        if (code === 200) {\r\n          this.rightItems = rows;\r\n        }\r\n      });\r\n    },\r\n    getTypePolicy() {\r\n      listByTypePolicy().then((res) => {\r\n        const { code, data } = res;\r\n        if (code === 200) {\r\n          const items = [];\r\n          forEachObjIndexed((value, key) => {\r\n            items.push({\r\n              key,\r\n              children: map(\r\n                (item) => ({\r\n                  id: item.id,\r\n                  code: item.labelCode,\r\n                  text: item.labelText,\r\n                }),\r\n                value\r\n              ),\r\n            });\r\n          }, data);\r\n          this.policyItems = items;\r\n        }\r\n      });\r\n    },\r\n    search() {\r\n      const { title } = this.form;\r\n      if (title) {\r\n        this.$router.push(`/notice?keyword=${title}`);\r\n      }\r\n    },\r\n    onCancel() {\r\n      this.checkedArr = [];\r\n    },\r\n    onConfirm() {\r\n      if (this.checkedArr.length === 0) {\r\n        this.$message.info(\"请选择画像\");\r\n        return;\r\n      }\r\n      this.$router.push({\r\n        name: \"policyDeclare\",\r\n        params: { code: this.checkedArr },\r\n      });\r\n      console.log(this.checkedArr);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n\r\n.policy-page {\r\n  background-color: #f4f5f9;\r\n  &-header {\r\n    background-color: #ffffff;\r\n    .banner {\r\n      width: 100%;\r\n      height: 50vh;\r\n      background-color: #f5f5f5;\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: fill;\r\n      }\r\n    }\r\n\r\n    .body {\r\n      padding: 20px 0;\r\n    }\r\n    .search-box {\r\n      // padding-top: 40px;\r\n      .search-form {\r\n        text-align: center;\r\n\r\n        .search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n\r\n          .search-btn {\r\n            width: 100px;\r\n          }\r\n\r\n          ::v-deep.el-input__inner {\r\n            height: 54px;\r\n            background: #fff;\r\n            border-radius: 27px 0 0 27px;\r\n            border: 1px solid #d9d9d9;\r\n            font-size: 16px;\r\n            line-height: 24px;\r\n            padding-left: 30px;\r\n          }\r\n\r\n          ::v-deep.el-input-group__append {\r\n            border: 1px solid #21c9b8;\r\n            border-radius: 0 100px 100px 0;\r\n            background: #21c9b8;\r\n            font-size: 16px;\r\n            color: #fff;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &-body {\r\n    padding: 40px 0;\r\n    .card {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      flex-shrink: 0;\r\n      width: 100%;\r\n      margin-bottom: 40px;\r\n      &-left {\r\n        // width: 292px;\r\n        position: relative;\r\n        flex-shrink: 0;\r\n        .notice-icon {\r\n          width: 100%;\r\n          // height: 632px;\r\n        }\r\n        .draw-icon {\r\n          width: 100%;\r\n          height: 100%;\r\n          // height: 526px;\r\n        }\r\n        .apply-icon {\r\n          width: 100%;\r\n          height: 304px;\r\n        }\r\n        .imageBtn {\r\n          position: absolute;\r\n          left: calc((100% - 200px) / 2);\r\n          bottom: 20px;\r\n          width: 200px;\r\n          height: 44px;\r\n          line-height: 44px;\r\n          background: #ffffff;\r\n          border-radius: 2px;\r\n          text-align: center;\r\n          cursor: pointer;\r\n          color: #21c9b8;\r\n        }\r\n      }\r\n      &-right {\r\n        flex: 1;\r\n        &.notice-content {\r\n          // height: 632px;\r\n          padding-left: 24px;\r\n          padding-top: 20px;\r\n          overflow-y: auto;\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          justify-content: space-between;\r\n          .notice-item {\r\n            // display: flex;\r\n            // flex-shrink: 0;\r\n            // flex-direction: row;\r\n            // align-items: center;\r\n            width: 49%;\r\n            height: 200px;\r\n            background-color: #ffffff;\r\n            padding: 20px 24px;\r\n            margin-bottom: 24px;\r\n            &-content {\r\n              // display: flex;\r\n              // flex-direction: column;\r\n              // flex: 1;\r\n              // justify-content: space-between;\r\n              // height: 100%;\r\n              padding-right: 24px;\r\n              .title {\r\n                @include multiEllipsis(1);\r\n                font-size: 24px;\r\n                font-weight: 500;\r\n                color: #323233;\r\n                line-height: 32px;\r\n              }\r\n              .footer {\r\n                display: flex;\r\n                flex-direction: row;\r\n                align-items: center;\r\n                margin-top: 50px;\r\n                margin-bottom: 20px;\r\n                .company {\r\n                  font-size: 14px;\r\n                  font-weight: 400;\r\n                  color: #999999;\r\n                  line-height: 14px;\r\n                  margin-right: 40px;\r\n                }\r\n                .date {\r\n                  font-size: 14px;\r\n                  font-weight: 400;\r\n                  color: #999999;\r\n                  line-height: 14px;\r\n                }\r\n              }\r\n            }\r\n            &-btn {\r\n              @include flexCenter;\r\n              width: 128px;\r\n              height: 40px;\r\n              background: #21c9b8;\r\n              border-radius: 4px;\r\n              font-size: 16px;\r\n              font-weight: 500;\r\n              color: #ffffff;\r\n              line-height: 16px;\r\n            }\r\n            &:last-child {\r\n              margin-bottom: 0;\r\n            }\r\n          }\r\n        }\r\n        &.policy-content {\r\n          display: flex;\r\n          flex-direction: column;\r\n          flex-shrink: 0;\r\n          justify-content: space-between;\r\n          height: 526px;\r\n          padding: 24px;\r\n          background-color: #ffffff;\r\n          margin: 24px 0 60px 0;\r\n          .policy-card {\r\n            height: 202px;\r\n            background: #f4f5f9;\r\n            border-radius: 8px;\r\n            overflow-y: auto;\r\n            overflow-x: hidden;\r\n            margin-bottom: 10px;\r\n            padding: 10px 0;\r\n            .checkbox-policy {\r\n              padding: 6px 26px;\r\n              ::v-deep.el-checkbox__label {\r\n                @include ellipsis;\r\n                display: inline-block;\r\n                max-width: 130px;\r\n                font-size: 12px;\r\n                font-weight: 400;\r\n                color: #262626;\r\n                line-height: 12px;\r\n              }\r\n            }\r\n          }\r\n          .policy-content-footer {\r\n            @include flexCenter;\r\n            .btn-cancel {\r\n              @include flexCenter;\r\n              width: 160px;\r\n              height: 40px;\r\n              border-radius: 4px;\r\n              border: 1px solid #d9d9d9;\r\n              font-size: 16px;\r\n              font-weight: 500;\r\n              color: #333333;\r\n              line-height: 16px;\r\n              margin-right: 25px;\r\n              cursor: pointer;\r\n            }\r\n            .btn-confirm {\r\n              @include flexCenter;\r\n              width: 160px;\r\n              height: 40px;\r\n              background: #21c9b8;\r\n              border-radius: 4px;\r\n              font-size: 16px;\r\n              font-weight: 500;\r\n              color: #ffffff;\r\n              line-height: 16px;\r\n              cursor: pointer;\r\n            }\r\n          }\r\n        }\r\n        &.apply-content {\r\n          height: 304px;\r\n          display: flex;\r\n          flex-direction: row;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          padding-left: 24px;\r\n          .apply-card {\r\n            width: 426px;\r\n            height: 304px;\r\n            background: #ffffff;\r\n            &-header {\r\n              display: flex;\r\n              flex-direction: row;\r\n              align-items: center;\r\n              justify-content: space-between;\r\n              height: 66px;\r\n              background-color: #ffffff;\r\n              padding: 0 24px;\r\n              border-bottom: 1px solid #e8e8e8;\r\n              .left {\r\n                display: flex;\r\n                flex-direction: row;\r\n                align-items: center;\r\n                .tag {\r\n                  width: 4px;\r\n                  height: 20px;\r\n                  background: #21c9b8;\r\n                  margin-right: 8px;\r\n                }\r\n                .title {\r\n                  font-size: 18px;\r\n                  font-weight: 500;\r\n                  color: #333333;\r\n                  line-height: 18px;\r\n                }\r\n              }\r\n              .right {\r\n                font-size: 18px;\r\n                font-weight: 400;\r\n                color: #21c9b8;\r\n                line-height: 18px;\r\n              }\r\n            }\r\n            &-body {\r\n              padding: 12px 0;\r\n              .item {\r\n                display: flex;\r\n                flex-direction: row;\r\n                align-items: center;\r\n                padding: 12px 24px;\r\n                &-tag {\r\n                  width: 6px;\r\n                  height: 6px;\r\n                  background: #21c9b8;\r\n                }\r\n                &-text {\r\n                  @include ellipsis;\r\n                  flex: 1;\r\n                  font-size: 16px;\r\n                  font-weight: 400;\r\n                  color: #666666;\r\n                  line-height: 16px;\r\n                  padding-left: 14px;\r\n                }\r\n              }\r\n              .el-empty {\r\n                padding: 0 !important;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n.enterprise-list-title-box {\r\n  width: 336px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30px 0 40px;\r\n  .enterprise-list-title {\r\n    font-size: 40px;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #333;\r\n    line-height: 40px;\r\n    padding: 0 40px;\r\n  }\r\n  .enterprise-list-divider {\r\n    width: 48px;\r\n    height: 4px;\r\n    background: #21c9b8;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;AAiKA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,OAAA,GAAAD,OAAA;AACA,IAAAE,IAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFA;AAGA;AAAA,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,UAAA;IACA;IACA;EAAA,CACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,IAAA;QACAC,KAAA,EAAAC;MACA;MACAC,OAAA;MACAC,QAAA;MACAC,UAAA;MACAC,UAAA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;IACA,KAAAC,aAAA;IACA,KAAAC,YAAA;IACA,KAAAC,aAAA;EACA;EACAC,KAAA;IACAC,MAAA,WAAAA,OAAAC,EAAA,EAAAC,IAAA;MACA;MACA,KAAAC,OAAA,CAAAC,EAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,UAAA,QAAAP,MAAA,CAAAQ,KAAA,CAAAD,UAAA;IACA,IAAAA,UAAA;MACA,IAAAE,KAAA,GAAAC,WAAA;QACA,IAAAC,MAAA,GAAAL,KAAA,CAAAM,KAAA,CAAAL,UAAA;QACA,IAAAI,MAAA;UACAE,aAAA,CAAAJ,KAAA;UACAE,MAAA,CAAAG,cAAA;QACA;MACA;IACA;EACA;EACAC,OAAA;IACApB,aAAA,WAAAA,cAAA;MAAA,IAAAqB,MAAA;MACA,KAAA/B,aAAA;MACA,IAAAgC,qBAAA;QACAC,OAAA;QACAC,OAAA;QACAC,QAAA;MACA,GACAC,IAAA,WAAAC,GAAA;QACA,IAAAC,IAAA,GAAAD,GAAA,CAAAC,IAAA;UAAAC,IAAA,GAAAF,GAAA,CAAAE,IAAA;QACA,IAAAD,IAAA;UACAP,MAAA,CAAA3B,OAAA,GAAAmC,IAAA;QACA;MACA,GACAC,OAAA;QAAA,OAAAT,MAAA,CAAA/B,aAAA;MAAA;IACA;IACAY,YAAA,WAAAA,aAAA;MAAA,IAAA6B,MAAA;MACA,IAAAC,qBAAA,OAAAN,IAAA,WAAAC,GAAA;QACA,IAAAC,IAAA,GAAAD,GAAA,CAAAC,IAAA;UAAAC,IAAA,GAAAF,GAAA,CAAAE,IAAA;QACA,IAAAD,IAAA;UACAG,MAAA,CAAApC,QAAA,GAAAkC,IAAA;QACA;MACA;IACA;IACA1B,aAAA,WAAAA,cAAA;MAAA,IAAA8B,MAAA;MACA,IAAAD,qBAAA,OAAAN,IAAA,WAAAC,GAAA;QACA,IAAAC,IAAA,GAAAD,GAAA,CAAAC,IAAA;UAAAC,IAAA,GAAAF,GAAA,CAAAE,IAAA;QACA,IAAAD,IAAA;UACAK,MAAA,CAAArC,UAAA,GAAAiC,IAAA;QACA;MACA;IACA;IACA5B,aAAA,WAAAA,cAAA;MAAA,IAAAiC,MAAA;MACA,IAAAC,qBAAA,IAAAT,IAAA,WAAAC,GAAA;QACA,IAAAC,IAAA,GAAAD,GAAA,CAAAC,IAAA;UAAAvC,IAAA,GAAAsC,GAAA,CAAAtC,IAAA;QACA,IAAAuC,IAAA;UACA,IAAAQ,KAAA;UACA,IAAAC,wBAAA,YAAAC,KAAA,EAAAC,GAAA;YACAH,KAAA,CAAAI,IAAA;cACAD,GAAA,EAAAA,GAAA;cACAE,QAAA,MAAAC,UAAA,EACA,UAAAC,IAAA;gBAAA;kBACAC,EAAA,EAAAD,IAAA,CAAAC,EAAA;kBACAhB,IAAA,EAAAe,IAAA,CAAAE,SAAA;kBACAC,IAAA,EAAAH,IAAA,CAAAI;gBACA;cAAA,GACAT,KACA;YACA;UACA,GAAAjD,IAAA;UACA6C,MAAA,CAAApC,WAAA,GAAAsC,KAAA;QACA;MACA;IACA;IACAY,MAAA,WAAAA,OAAA;MACA,IAAAxD,KAAA,QAAAD,IAAA,CAAAC,KAAA;MACA,IAAAA,KAAA;QACA,KAAAgB,OAAA,CAAAgC,IAAA,oBAAAS,MAAA,CAAAzD,KAAA;MACA;IACA;IACA0D,QAAA,WAAAA,SAAA;MACA,KAAArD,UAAA;IACA;IACAsD,SAAA,WAAAA,UAAA;MACA,SAAAtD,UAAA,CAAAuD,MAAA;QACA,KAAAC,QAAA,CAAAC,IAAA;QACA;MACA;MACA,KAAA9C,OAAA,CAAAgC,IAAA;QACArD,IAAA;QACAoE,MAAA;UAAA3B,IAAA,OAAA/B;QAAA;MACA;MACA2D,OAAA,CAAAC,GAAA,MAAA5D,UAAA;IACA;EACA;AACA", "ignoreList": []}]}