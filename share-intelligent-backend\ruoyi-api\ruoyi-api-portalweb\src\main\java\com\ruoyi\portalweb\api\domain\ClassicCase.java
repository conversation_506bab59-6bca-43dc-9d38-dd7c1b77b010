package com.ruoyi.portalweb.api.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 典型案例对象 classic_case
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
public class ClassicCase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 典型案例ID */
    @ApiModelProperty(value = "典型案例ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long classicCaseId;

    /** 方案类型ID */
    @Excel(name = "方案类型ID")
    @ApiModelProperty(value = "方案类型ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long solutionTypeId;

    /** 名称 */
    @Excel(name = "名称")
    @ApiModelProperty(value = "名称")
    private String classicCaseName;

    /** 公司名 */
    @Excel(name = "公司名")
    @ApiModelProperty(value = "公司名")
    private String classicCaseCompany;

    /** 简介 */
    @Excel(name = "简介")
    @ApiModelProperty(value = "简介")
    private String classicCaseIntroduction;

    /** 项目背景 */
    @Excel(name = "项目背景")
    @ApiModelProperty(value = "项目背景")
    private String classicCaseBackground;

    /** 实施内容 */
    @Excel(name = "实施内容")
    @ApiModelProperty(value = "实施内容")
    private byte[] classicCaseContent;

    /** 项目效果 */
    @Excel(name = "项目效果")
    @ApiModelProperty(value = "项目效果")
    private String classicCaseEffects;

    /** 删除标志（0代表存在 2代表删除） */
    @ApiModelProperty(value = "删除标志（0代表存在 2代表删除）")
    private String delFlag;

    public void setClassicCaseId(Long classicCaseId) 
    {
        this.classicCaseId = classicCaseId;
    }

    public Long getClassicCaseId() 
    {
        return classicCaseId;
    }
    public void setSolutionTypeId(Long solutionTypeId) 
    {
        this.solutionTypeId = solutionTypeId;
    }

    public Long getSolutionTypeId() 
    {
        return solutionTypeId;
    }
    public void setClassicCaseName(String classicCaseName) 
    {
        this.classicCaseName = classicCaseName;
    }

    public String getClassicCaseName() 
    {
        return classicCaseName;
    }
    public void setClassicCaseCompany(String classicCaseCompany) 
    {
        this.classicCaseCompany = classicCaseCompany;
    }

    public String getClassicCaseCompany() 
    {
        return classicCaseCompany;
    }
    public void setClassicCaseIntroduction(String classicCaseIntroduction) 
    {
        this.classicCaseIntroduction = classicCaseIntroduction;
    }

    public String getClassicCaseIntroduction() 
    {
        return classicCaseIntroduction;
    }
    public void setClassicCaseBackground(String classicCaseBackground) 
    {
        this.classicCaseBackground = classicCaseBackground;
    }

    public String getClassicCaseBackground() 
    {
        return classicCaseBackground;
    }

    public void setClassicCaseContent(byte[] classicCaseContent)
    {
        this.classicCaseContent = classicCaseContent;
    }
    public byte[] getClassicCaseContent()
    {
        return classicCaseContent;
    }

    public void setClassicCaseEffects(String classicCaseEffects)
    {
        this.classicCaseEffects = classicCaseEffects;
    }

    public String getClassicCaseEffects() 
    {
        return classicCaseEffects;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("classicCaseId", getClassicCaseId())
            .append("solutionTypeId", getSolutionTypeId())
            .append("classicCaseName", getClassicCaseName())
            .append("classicCaseCompany", getClassicCaseCompany())
            .append("classicCaseIntroduction", getClassicCaseIntroduction())
            .append("classicCaseBackground", getClassicCaseBackground())
            .append("classicCaseContent", getClassicCaseContent())
            .append("classicCaseEffects", getClassicCaseEffects())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
