<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="检测项目名称" prop="itemName">
        <el-input v-model="queryParams.itemName" placeholder="请输入检测项目名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:testingItem:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:testingItem:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:testingItem:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['system:testingItem:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="testingItemList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="检测项目名称" align="center" prop="itemName" />
      <el-table-column label="检测详情" align="center" prop="testingDetails" />
      <el-table-column label="状态" align="center" prop="status" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:testingItem:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:testingItem:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改检测项目对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="检测项目名称" prop="itemName">
          <el-input v-model="form.itemName" placeholder="请输入检测项目名称" />
        </el-form-item>
        <el-form-item label="检测详情" prop="testingDetails">
          <el-input v-model="form.testingDetails" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="实验室" prop="labId">
          <el-select v-model="labId" placeholder="请选择实验室">
            <el-option v-for="item in labList" :key="item.id" :label="item.labName" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listTestingItem, getTestingItem, delTestingItem, addTestingItem, updateTestingItem, addTestingItemAndLab,
  updateTestingItemAndLab, getTestingItemAndLab
} from "@/api/system/testingItem";
import { listLaboratoryInfo } from "@/api/system/laboratoryInfo";

export default {
  name: "TestingItem",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 检测项目表格数据
      testingItemList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        itemName: null,
        testingDetails: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 关联的实验室id
      labId: null,
      // 表单校验
      rules: {
        itemName: [
          { required: true, message: "检测项目名称不能为空", trigger: "blur" }
        ],
      },
      labList: [],
    };
  },
  created() {
    this.getList();
    this.getLabList();
  },
  methods: {
    /** 查询检测项目列表 */
    getList() {
      this.loading = true;
      listTestingItem(this.queryParams).then(response => {
        this.testingItemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getLabList() {
      let params = {
        pageNum: 1,
        pageSize: 1000
      }
      listLaboratoryInfo(params).then(response => {
        this.labList = response.rows;
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        itemName: null,
        testingDetails: null,
        status: null,
        createTime: null,
        updateTime: null
      };
      this.labId = null;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检测项目";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getTestingItemAndLab(id).then(response => {
        this.form = response.data.testingItem;
        this.labId = response.data.labs.length > 0 ? response.data.labs[0].id : '';
        this.open = true;
        this.title = "修改检测项目";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let params = {
            testingItem: this.form,
            labIds: [this.labId]
          }
          if (this.form.id != null) {
            updateTestingItemAndLab(params).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTestingItemAndLab(params).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除检测项目编号为"' + ids + '"的数据项？').then(function () {
        return delTestingItem(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/testingItem/export', {
        ...this.queryParams
      }, `testingItem_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
