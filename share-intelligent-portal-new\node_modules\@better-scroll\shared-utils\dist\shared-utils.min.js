!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).SharedUtils={})}(this,function(r){"use strict";function n(e){console.error("[BScroll warn]: "+e)}var s=function(){return(s=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};var i="undefined"!=typeof window,e=i&&navigator.userAgent.toLowerCase(),t=!(!e||!/wechatdevtools/.test(e)),o=e&&0<e.indexOf("android"),a="string"==typeof e&&(!!(a=/os (\d\d?_\d(_\d)?)/.exec(e))&&!!(13===(a=a[1].split("_").map(function(e){return parseInt(e,10)}))[0]&&4<=a[1]));if(r.supportsPassive=!1,i)try{var f={};Object.defineProperty(f,"passive",{get:function(){r.supportsPassive=!0}}),window.addEventListener("test-passive",function(){},f)}catch(e){}function c(e,t){for(var n in t)e[n]=t[n];return e}var u=i&&document.createElement("div").style,p=function(){if(i)for(var e=0,t=[{key:"standard",value:"transform"},{key:"webkit",value:"webkitTransform"},{key:"Moz",value:"MozTransform"},{key:"O",value:"OTransform"},{key:"ms",value:"msTransform"}];e<t.length;e++){var n=t[e];if(void 0!==u[n.value])return n.key}return!1}();function l(e){return!1===p?e:"standard"===p?"transitionEnd"===e?"transitionend":e:p+e.charAt(0).toUpperCase()+e.substr(1)}function v(e,t,n,i){i=r.supportsPassive?{passive:!1,capture:!!i}:!!i;e.addEventListener(t,n,i)}function d(e,t,n,i){e.removeEventListener(t,n,{capture:!!i})}var f=p&&"standard"!==p?"-"+p.toLowerCase()+"-":"",h=l("transform"),y=l("transition"),m=i&&l("perspective")in u,g=i&&("ontouchstart"in window||t),w=i&&y in u,h={transform:h,transition:y,transitionTimingFunction:l("transitionTimingFunction"),transitionDuration:l("transitionDuration"),transitionDelay:l("transitionDelay"),transformOrigin:l("transformOrigin"),transitionEnd:l("transitionEnd"),transitionProperty:l("transitionProperty")};function E(e,t){for(var n in t)if(t[n].test(e[n]))return!0;return!1}y=E;function b(e,t){void 0===t&&(t="click"),"mouseup"===e.type?r=e:"touchend"!==e.type&&"touchcancel"!==e.type||(r=e.changedTouches[0]);var n,i={},r=(r&&(i.screenX=r.screenX||0,i.screenY=r.screenY||0,i.clientX=r.clientX||0,i.clientY=r.clientY||0),{ctrlKey:e.ctrlKey,shiftKey:e.shiftKey,altKey:e.altKey,metaKey:e.metaKey});if("undefined"!=typeof MouseEvent)try{n=new MouseEvent(t,c(s({bubbles:!0,cancelable:!0},r),i))}catch(e){o()}else o();function o(){(n=document.createEvent("Event")).initEvent(t,!0,!0),c(n,i)}n.forwardedTouchEvent=!0,n._constructed=!0,e.target.dispatchEvent(n)}function T(e,t){t.parentNode.insertBefore(e,t)}function O(e,t){return new RegExp("(^|\\s)"+t+"(\\s|$)").test(e.className)}var C=i&&window;function k(){}function A(e){}var D=i?C.requestAnimationFrame||C.webkitRequestAnimationFrame||C.mozRequestAnimationFrame||C.oRequestAnimationFrame||function(e){return window.setTimeout(e,e.interval||1e3/60)}:k,C=i?C.cancelAnimationFrame||C.webkitCancelAnimationFrame||C.mozCancelAnimationFrame||C.oCancelAnimationFrame||function(e){window.clearTimeout(e)}:k,M={enumerable:!0,configurable:!0,get:A,set:A};F.prototype.on=function(e,t,n){return void 0===n&&(n=this),this.hasType(e),this.events[e]||(this.events[e]=[]),this.events[e].push([t,n]),this},F.prototype.once=function(i,r,o){var s=this,a=(void 0===o&&(o=this),this.hasType(i),function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];s.off(i,a);var n=r.apply(o,e);if(!0===n)return n});return a.fn=r,this.on(i,a),this},F.prototype.off=function(e,t){if(!e&&!t)return this.events={},this;if(e){if(this.hasType(e),t){var n=this.events[e];if(n)for(var i=n.length;i--;)(n[i][0]===t||n[i][0]&&n[i][0].fn===t)&&n.splice(i,1)}else this.events[e]=[];return this}},F.prototype.trigger=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.hasType(e);e=this.events[e];if(e)for(var i=e.length,r=function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var i=Array(e),r=0,t=0;t<n;t++)for(var o=arguments[t],s=0,a=o.length;s<a;s++,r++)i[r]=o[s];return i}(e),o=0;o<i;o++){var s=r[o],a=s[0],s=s[1];if(a&&!0===(a=a.apply(s,t)))return a}},F.prototype.registerType=function(e){var t=this;e.forEach(function(e){t.eventTypes[e]=e})},F.prototype.destroy=function(){this.events={},this.eventTypes={}},F.prototype.hasType=function(e){var t=this.eventTypes;t[e]!==e&&n('EventEmitter has used unknown event type: "'+e+'", should be oneof ['+Object.keys(t).map(function(e){return JSON.stringify(e)})+"]")};var x=F;function F(e){this.events={},this.eventTypes={},this.registerType(e)}j.prototype.destroy=function(){this.removeDOMEvents(),this.events=[]},j.prototype.addDOMEvents=function(){this.handleDOMEvents(v)},j.prototype.removeDOMEvents=function(){this.handleDOMEvents(d)},j.prototype.handleDOMEvents=function(t){var n=this,i=this.wrapper;this.events.forEach(function(e){t(i,e.name,n,!!e.capture)})},j.prototype.handleEvent=function(t){var n=t.type;this.events.some(function(e){return e.name===n&&(e.handler(t),!0)})};var P=j;function j(e,t){this.wrapper=e,this.events=t,this.addDOMEvents()}r.EventEmitter=x,r.EventRegister=P,r.HTMLCollectionToArray=function(e){return Array.prototype.slice.call(e,0)},r.addClass=function(e,t){var n;O(e,t)||((n=e.className.split(" ")).push(t),e.className=n.join(" "))},r.addEvent=v,r.assert=function(e,t){if(!e)throw new Error("[BScroll] "+t)},r.before=T,r.between=function(e,t,n){return e<t?t:n<e?n:e},r.cancelAnimationFrame=C,r.click=b,r.cssVendor=f,r.dblclick=function(e){b(e,"dblclick")},r.ease={swipe:{style:"cubic-bezier(0.23, 1, 0.32, 1)",fn:function(e){return 1+--e*e*e*e*e}},swipeBounce:{style:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",fn:function(e){return e*(2-e)}},bounce:{style:"cubic-bezier(0.165, 0.84, 0.44, 1)",fn:function(e){return 1- --e*e*e*e}}},r.eventTypeMap={touchstart:1,touchmove:1,touchend:1,touchcancel:1,mousedown:2,mousemove:2,mouseup:2},r.extend=c,r.findIndex=function(e,i){var r;return e.findIndex?e.findIndex(i):(r=-1,e.some(function(e,t,n){e=i(e,t,n);if(e)return r=t,e}),r)},r.getClientSize=function(e){return{width:e.clientWidth,height:e.clientHeight}},r.getDistance=function(e,t){return Math.sqrt(e*e+t*t)},r.getElement=function(e){return"string"==typeof e?document.querySelector(e):e},r.getNow=function(){return window.performance&&window.performance.now&&window.performance.timing?window.performance.now()+window.performance.timing.navigationStart:+new Date},r.getRect=function(e){var t;return e instanceof window.SVGElement?{top:(t=e.getBoundingClientRect()).top,left:t.left,width:t.width,height:t.height}:{top:e.offsetTop,left:e.offsetLeft,width:e.offsetWidth,height:e.offsetHeight}},r.hasClass=O,r.hasPerspective=m,r.hasTouch=g,r.hasTransition=w,r.inBrowser=i,r.isAndroid=o,r.isIOSBadVersion=a,r.isUndef=function(e){return null==e},r.isWeChatDevTools=t,r.maybePrevent=function(e){e.cancelable&&e.preventDefault()},r.offset=function(e){for(var t=0,n=0;e;)t-=e.offsetLeft,n-=e.offsetTop,e=e.offsetParent;return{left:t,top:n}},r.offsetToBody=function(e){return{left:-((e=e.getBoundingClientRect()).left+window.pageXOffset),top:-(e.top+window.pageYOffset)}},r.prepend=function(e,t){var n=t.firstChild;n?T(e,n):t.appendChild(e)},r.preventDefaultExceptionFn=E,r.propertiesProxy=function(e,o,t){M.get=function(){for(var e=this,t=o.split("."),n=0;n<t.length-1;n++)if("object"!=typeof(e=e[t[n]])||!e)return;var i=t.pop();return"function"==typeof e[i]?function(){return e[i].apply(e,arguments)}:e[i]},M.set=function(e){for(var t,n=this,i=o.split("."),r=0;r<i.length-1;r++)n[t=i[r]]||(n[t]={}),n=n[t];n[i.pop()]=e},Object.defineProperty(e,t,M)},r.removeChild=function(e,t){e.removeChild(t)},r.removeClass=function(e,t){O(e,t)&&(t=new RegExp("(^|\\s)"+t+"(\\s|$)","g"),e.className=e.className.replace(t," "))},r.removeEvent=d,r.requestAnimationFrame=D,r.style=h,r.tagExceptionFn=y,r.tap=function(e,t){var n=document.createEvent("Event");n.initEvent(t,!0,!0),n.pageX=e.pageX,n.pageY=e.pageY,e.target.dispatchEvent(n)},r.ua=e,r.warn=n,Object.defineProperty(r,"__esModule",{value:!0})});
