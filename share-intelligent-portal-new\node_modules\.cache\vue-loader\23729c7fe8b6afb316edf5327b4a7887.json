{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\sharedOrders\\index.vue?vue&type=style&index=0&id=5d8e372b&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\sharedOrders\\index.vue", "mtime": 1750311963082}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byByaWdodCwgcmdiKDIyNSwgMjQ3LCAyNDApLCByZ2IoMjQ0LCAyNTIsIDI1MCkpOw0KfQ0KDQoudG9wIHsNCiAgcGFkZGluZzogMjBweDsNCiAgYmFja2dyb3VuZDogI2ZmZjsNCiAgYm9yZGVyLXJhZGl1czogMTBweDsNCiAgLy8gbWFyZ2luLXRvcDogMjBweDsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KDQogIC5jb250ZW50X3RpdGxlIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQoNCiAgICAuaWNvbiB7DQogICAgICB3aWR0aDogNHB4Ow0KICAgICAgaGVpZ2h0OiAyMHB4Ow0KICAgICAgYmFja2dyb3VuZDogIzIxYzliODsNCiAgICB9DQoNCiAgICAudGl0bGUgew0KICAgICAgZm9udC1mYW1pbHk6IFNvdXJjZSBIYW4gU2FucyBDTjsNCiAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICBjb2xvcjogIzAzMGExYTsNCiAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4Ow0KICAgIH0NCiAgfQ0KfQ0KDQoudGFibGUgew0KICBtYXJnaW4tdG9wOiAyMHB4Ow0KICBiYWNrZ3JvdW5kOiAjZmZmOw0KICBib3JkZXItcmFkaXVzOiAxMHB4Ow0KICBwYWRkaW5nOiAyMHB4Ow0KICBib3gtc2l6aW5nOiBib3JkZXItYm94Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBmbGV4LXdyYXA6IHdyYXA7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYXJvdW5kOw0KDQogIC5kZXNjew0KICAgIGRpc3BsYXk6IC13ZWJraXQtYm94Ow0KICAgIC13ZWJraXQtYm94LW9yaWVudDogdmVydGljYWw7DQogICAgLXdlYmtpdC1saW5lLWNsYW1wOiA3Ow0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQogICAgd2hpdGUtc3BhY2U6IG5vcm1hbDsNCiAgICB3b3JkLWJyZWFrOiBicmVhay1hbGw7DQogIH0NCn0NCg0KLnBhZ2VTdHlsZSB7DQogIHdpZHRoOiAxMDAlOw0KICBtYXJnaW4tdG9wOiA2MXB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsHA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/sharedOrders", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"top\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">共享订单</div>\r\n          </div>\r\n          <el-button class=\"btn\" type=\"primary\" plain @click=\"handleAdd\">发布共享订单</el-button>\r\n        </div>\r\n        <div class=\"table\">\r\n          <div style=\"width: 100%\">\r\n            <el-table :data=\"tableData\" style=\"width: 100%\" :v-loading=\"loading\" max-height=\"600\">\r\n              <el-table-column label=\"需求企业\" align=\"center\" prop=\"demandCompany\" width=\"150\" />\r\n              <el-table-column label=\"需求截止时间\" align=\"center\" prop=\"deadline\" width=\"100\" />\r\n              <el-table-column label=\"托单价格\" align=\"center\" prop=\"price\" />\r\n              <el-table-column label=\"联系电话\" align=\"center\" prop=\"contactPhone\" />\r\n              <el-table-column label=\"交货地址\" align=\"center\" prop=\"deliveryAddress\" width=\"150\" />\r\n              <el-table-column label=\"文件要求\" align=\"center\" prop=\"fileRequirement\" width=\"500\">\r\n                <template slot-scope=\"scope\">\r\n                  <div class=\"desc\">{{ scope.row.fileRequirement ? scope.row.fileRequirement : \"无\"}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" size=\"small\" @click=\"viewProductDetail(scope.row)\">查看详情</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n          <!-- 分页 -->\r\n          <div class=\"pageStyle\">\r\n            <el-pagination v-if=\"tableData && tableData.length > 0\" background layout=\"prev, pager, next\"\r\n              class=\"activity-pagination\" :page-size=\"pageSize\" :current-page=\"pageNum\" :total=\"total\"\r\n              @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { manufactureOrderListData } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      tableData: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      };\r\n      manufactureOrderListData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.tableData = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    handleAdd() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(\"/release?index=2\");\r\n      }\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    viewProductDetail(row) {\r\n      if (row) {\r\n        this.$router.push(\"/productOrderDetail?id=\" + row.id);\r\n      } else {\r\n        this.$message.error(\"暂无该订单详情\");\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n}\r\n\r\n.top {\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  // margin-top: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.table {\r\n  margin-top: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  padding: 20px;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  justify-content: space-around;\r\n\r\n  .desc{\r\n    display: -webkit-box;\r\n    -webkit-box-orient: vertical;\r\n    -webkit-line-clamp: 7;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: normal;\r\n    word-break: break-all;\r\n  }\r\n}\r\n\r\n.pageStyle {\r\n  width: 100%;\r\n  margin-top: 61px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n</style>\r\n"]}]}