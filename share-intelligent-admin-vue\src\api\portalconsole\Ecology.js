import request from '@/utils/request'

// 查询生态协作列表
export function listEcology(query) {
  return request({
    url: '/portalconsole/Ecology/list',
    method: 'get',
    params: query
  })
}

// 查询生态协作详细
export function getEcology(ecologyId) {
  return request({
    url: '/portalconsole/Ecology/' + ecologyId,
    method: 'get'
  })
}

// 新增生态协作
export function addEcology(data) {
  return request({
    url: '/portalconsole/Ecology',
    method: 'post',
    data: data
  })
}

// 修改生态协作
export function updateEcology(data) {
  return request({
    url: '/portalconsole/Ecology',
    method: 'put',
    data: data
  })
}

// 删除生态协作
export function delEcology(ecologyId) {
  return request({
    url: '/portalconsole/Ecology/' + ecologyId,
    method: 'delete'
  })
}
