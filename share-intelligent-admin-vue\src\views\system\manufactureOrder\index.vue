<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="需求截止时间" prop="deadline">
        <el-date-picker clearable v-model="queryParams.deadline" type="date" value-format="yyyy-MM-dd"
          placeholder="请选择需求截止时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="托单价格" prop="price">
        <el-input v-model="queryParams.price" placeholder="请输入托单价格" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="需求企业" prop="demandCompany">
        <el-input v-model="queryParams.demandCompany" placeholder="请输入需求企业" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="联系电话" prop="contactPhone">
        <el-input v-model="queryParams.contactPhone" placeholder="请输入联系电话" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="开户行" prop="bankName">
        <el-input v-model="queryParams.bankName" placeholder="请输入开户行" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="付款账号" prop="paymentAccount">
        <el-input v-model="queryParams.paymentAccount" placeholder="请输入付款账号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:manufactureOrder:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:manufactureOrder:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:manufactureOrder:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['system:manufactureOrder:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="manufactureOrderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="需求截止时间" align="center" prop="deadline" width="180" />
      <el-table-column label="订单类型" align="center" prop="orderType" />
      <el-table-column label="拦标价" align="center" prop="price" />
      <el-table-column label="需求企业" align="center" prop="demandCompany" />
      <el-table-column label="联系电话" align="center" prop="contactPhone" />
      <el-table-column label="交货地址" align="center" prop="deliveryAddress" />
      <el-table-column label="交货要求" align="center" prop="fileRequirement" />
      <!-- <el-table-column label="开户行" align="center" prop="bankName" />
      <el-table-column label="付款账号" align="center" prop="paymentAccount" /> -->
      <el-table-column label="审核状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.auditStatus == 1 ? "已审核" : "待审核" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:manufactureOrder:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:manufactureOrder:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改制造订单对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-position="top">
        <el-form-item label="需求企业" prop="demandCompany">
          <el-input v-model="form.demandCompany" placeholder="请输入需求企业" />
        </el-form-item>
        <el-form-item label="联系人" prop="contactPerson">
          <el-input v-model="form.contactPerson" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系电话" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系电话" />
        </el-form-item>
        <el-form-item label="交货地址" prop="deliveryAddress">
          <el-input v-model="form.deliveryAddress" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="交货要求" prop="fileRequirement">
          <el-input v-model="form.fileRequirement" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <!-- <el-form-item label="开户行" prop="bankName">
          <el-input v-model="form.bankName" placeholder="请输入开户行" />
        </el-form-item>
        <el-form-item label="付款账号" prop="paymentAccount">
          <el-input v-model="form.paymentAccount" placeholder="请输入付款账号" />
        </el-form-item> -->
        <el-form-item>
          <div slot="label">
            <div style="display: flex; width: 1080px">
              <div>需求物料</div>
              <div class="addStyle" @click="addMaterial">新增行</div>
            </div>
          </div>
          <el-table :data="jobList">
            <el-table-column label="物料名称" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.name"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="规格型号" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.modelNumber"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="数量" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.quantity" type="number"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="单位" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.unit"></el-input>
              </template>
            </el-table-column>
            <el-table-column label="可承接量" align="center">
              <template slot-scope="scope">
                <el-input v-model="scope.row.capacity"></el-input>
              </template>
            </el-table-column>
             <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="text" icon="el-icon-delete" @click="deleteMaterial(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>

        <el-form-item label="审核状态" prop="settledStatus">
          <el-select v-model="form.auditStatus" placeholder="请选择" clearable style="width: 100%">
            <el-option v-for="dict in auditStatusList" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="拦标价" style="width: 80%">
              <el-input v-model="form.price" placeholder="请输入" type="number">
                <template slot="suffix"> /元 </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否允许拼单">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in isJointOrderList" :key="dict.value" :label="dict.value" :value="dict.value">{{
                  dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="可承接量">
        <el-input v-model="form.title" placeholder="请输入"></el-input>
      </el-form-item> -->
        <el-row>
          <el-col :span="12">
            <el-form-item label="需求截至时间" prop="deadline" style="width: 80%">
              <el-date-picker v-model="form.deadline" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交付时间" style="width: 80%">
              <el-date-picker v-model="form.bankName" type="date" placeholder="选择日期" value-format="yyyy-MM-dd"
                style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listManufactureOrder,
  getManufactureOrder,
  delManufactureOrder,
  addManufactureOrder,
  updateManufactureOrder,
} from "@/api/system/manufactureOrder";

export default {
  name: "ManufactureOrder",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 制造订单表格数据
      manufactureOrderList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deadline: null,
        status: null,
        orderType: null,
        price: null,
        demandCompany: null,
        contactPhone: null,
        deliveryAddress: null,
        fileRequirement: null,
        bankName: null,
        paymentAccount: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        deadline: [
          { required: true, message: "需求截止时间不能为空", trigger: "blur" },
        ],
        status: [
          { required: true, message: "订单状态不能为空", trigger: "change" },
        ],
        orderType: [
          { required: true, message: "订单类型不能为空", trigger: "change" },
        ],
      },
      auditStatusList: [
        {
          dictValue: "0",
          dictLabel: "待审核",
        },
        {
          dictValue: "1",
          dictLabel: "已审核",
        },
      ],
      isJointOrderList: [
        {
          label: "否",
          value: "0",
        },
        {
          label: "是",
          value: "1",
        },
      ],
      jobList: [
        {
          name: "",
          modelNumber: "",
          quantity: "",
          unit: "",
          capacity: "",
        },
      ],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询制造订单列表 */
    getList() {
      this.loading = true;
      listManufactureOrder(this.queryParams).then((response) => {
        this.manufactureOrderList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        deadline: null,
        status: "0",
        orderType: "生产订单",
        price: null,
        demandCompany: null,
        contactPhone: null,
        deliveryAddress: null,
        fileRequirement: null,
        bankName: null,
        paymentAccount: null,
        createTime: null,
        updateTime: null,
        auditStatus: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加制造订单";
      this.jobList = [
        {
          name: "",
          modelNumber: "",
          quantity: "",
          unit: "",
          capacity: "",
        },
      ];
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getManufactureOrder(id).then((response) => {
        this.form = response.data;
        this.jobList = this.form.materials.length > 0 ? this.form.materials : [{ name: "", modelNumber: "", quantity: "", unit: "", capacity: "", }];
        this.open = true;
        this.title = "修改制造订单";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        this.form.materials = this.jobList;
        if (valid) {
          if (this.form.id != null) {
            updateManufactureOrder(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addManufactureOrder(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除制造订单编号为"' + ids + '"的数据项？')
        .then(function () {
          return delManufactureOrder(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/manufactureOrder/export",
        {
          ...this.queryParams,
        },
        `manufactureOrder_${new Date().getTime()}.xlsx`
      );
    },
    addMaterial() {
      this.jobList.push({
        name: "",
        modelNumber: "",
        quantity: "",
        unit: "",
        capacity: "",
      });
    },
    deleteMaterial(index) {
      this.jobList.splice(index, 1);
    }
  },
};
</script>
<style lang="scss">
.title {
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 18px;
  color: #21c9b8;
}

.titleLine {
  width: 100%;
  height: 1px;
  background: #21c9b8;
  margin: 20px 0 30px 0;
}

.addStyle {
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #21c9b8;
  margin-left: auto;
  cursor: pointer;
}

.el-dialog__body {
  padding: 30px 40px;
}
</style>