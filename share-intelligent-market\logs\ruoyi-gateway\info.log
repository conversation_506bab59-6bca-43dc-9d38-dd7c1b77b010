09:14:53.781 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:14:54.929 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 06431772-5467-4aa5-b56d-579944f8b3aa_config-0
09:14:55.008 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 44 ms to scan 1 urls, producing 3 keys and 6 values 
09:14:55.050 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 4 keys and 9 values 
09:14:55.063 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 3 keys and 10 values 
09:14:55.213 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 147 ms to scan 237 urls, producing 0 keys and 0 values 
09:14:55.223 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:14:55.237 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:14:55.248 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:14:55.393 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 143 ms to scan 237 urls, producing 0 keys and 0 values 
09:14:55.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:14:55.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1863557065
09:14:55.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1428527783
09:14:55.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:14:55.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:14:55.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:14:57.070 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295696775_127.0.0.1_50282
09:14:57.071 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Notify connected event to listeners.
09:14:57.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:57.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06431772-5467-4aa5-b56d-579944f8b3aa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/495984416
09:14:57.189 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:15:02.880 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:15:03.293 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 22b1c25a-b643-4ee0-8c0e-cac88279b153
09:15:03.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] RpcClient init label, labels = {module=naming, source=sdk}
09:15:03.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:15:03.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:15:03.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:15:03.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:15:03.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295703306_127.0.0.1_50354
09:15:03.412 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Notify connected event to listeners.
09:15:03.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:03.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/495984416
09:15:03.801 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:15:04.843 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0
09:15:04.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:15:04.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1863557065
09:15:04.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1428527783
09:15:04.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:15:04.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:15:04.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:15:04.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295704851_127.0.0.1_50367
09:15:04.958 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Notify connected event to listeners.
09:15:04.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:04.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ef036a5-97fe-4f15-b9dc-d0b69c2b718e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/495984416
09:15:05.746 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:15:05.748 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:15:05.928 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:15:05.929 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:15:06.104 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
09:15:06.138 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 13.393 seconds (JVM running for 14.942)
09:15:06.146 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
09:15:06.147 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
09:15:06.147 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
09:15:06.701 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:15:06.702 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:15:36.095 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:15:36.095 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1c25a-b643-4ee0-8c0e-cac88279b153] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:17:53.975 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:17:58.171 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0
09:17:58.354 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 109 ms to scan 1 urls, producing 3 keys and 6 values 
09:17:58.453 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 4 keys and 9 values 
09:17:58.475 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
09:17:58.762 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 283 ms to scan 237 urls, producing 0 keys and 0 values 
09:17:58.780 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
09:17:58.807 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 1 keys and 7 values 
09:17:58.828 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
09:17:59.114 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 282 ms to scan 237 urls, producing 0 keys and 0 values 
09:17:59.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:17:59.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/716206126
09:17:59.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1901648626
09:17:59.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:17:59.129 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:17:59.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:03.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:06.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:09.633 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:18:09.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:09.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1567857145
09:18:11.139 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:18:14.647 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:16.894 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:19.284 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:21.745 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:22.051 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:18:24.318 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:25.716 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 453c6e63-a356-4fd1-ba77-b43e14aaed27
09:18:25.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] RpcClient init label, labels = {module=naming, source=sdk}
09:18:25.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:25.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:25.734 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:25.735 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:27.001 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:27.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:29.745 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:30.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:32.139 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:18:32.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:32.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1567857145
09:18:32.793 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:33.404 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:18:35.756 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:36.352 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:36.461 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0
09:18:36.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:36.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/716206126
09:18:36.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/1901648626
09:18:36.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:36.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:36.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:38.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:39.179 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:39.179 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:40.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:18:41.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:42.349 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:42.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:42.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/1567857145
09:18:42.782 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:18:44.229 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:45.861 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:18:45.861 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4d359c69[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:18:45.862 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@28f6a008[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 16]
09:18:45.862 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:46.467 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [453c6e63-a356-4fd1-ba77-b43e14aaed27] Client is shutdown, stop reconnect to server
09:18:46.561 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:47.110 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:47.355 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:47.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:47.891 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f5dc0e4-3301-42b9-9513-71c8b316f487_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:48.147 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:18:48.795 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec16cde5-37b1-4aa9-92ad-1edf44525bf0_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
09:19:49.435 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:19:50.756 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0
09:19:50.895 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 71 ms to scan 1 urls, producing 3 keys and 6 values 
09:19:50.961 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
09:19:50.980 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
09:19:51.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 248 ms to scan 237 urls, producing 0 keys and 0 values 
09:19:51.243 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:19:51.259 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:19:51.274 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:19:51.476 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 199 ms to scan 237 urls, producing 0 keys and 0 values 
09:19:51.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:19:51.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1428527783
09:19:51.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/423109432
09:19:51.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:19:51.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:19:51.497 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:19:53.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295992979_127.0.0.1_54935
09:19:53.254 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Notify connected event to listeners.
09:19:53.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:53.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4b36b0c-0869-40f5-8e6f-bc540ad79ea5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/269853881
09:19:53.358 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:19:59.267 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:19:59.682 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6872d378-fcda-4402-b9e2-aa2bbe2b5119
09:19:59.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] RpcClient init label, labels = {module=naming, source=sdk}
09:19:59.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:59.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:59.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:59.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:19:59.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750295999692_127.0.0.1_55084
09:19:59.801 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Notify connected event to listeners.
09:19:59.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:59.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/269853881
09:20:00.182 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:20:00.955 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4050b938-2720-4f54-85cb-df186df61393_config-0
09:20:00.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:20:00.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1428527783
09:20:00.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/423109432
09:20:00.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:20:00.956 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:20:00.956 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:20:01.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296000959_127.0.0.1_55091
09:20:01.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:20:01.071 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Notify connected event to listeners.
09:20:01.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4050b938-2720-4f54-85cb-df186df61393_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/269853881
09:20:01.818 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:20:01.821 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Ack server push request, request = NotifySubscriberRequest, requestId = 17
09:20:01.984 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Receive server push request, request = NotifySubscriberRequest, requestId = 18
09:20:01.986 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Ack server push request, request = NotifySubscriberRequest, requestId = 18
09:20:01.988 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Receive server push request, request = NotifySubscriberRequest, requestId = 19
09:20:01.988 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6872d378-fcda-4402-b9e2-aa2bbe2b5119] Ack server push request, request = NotifySubscriberRequest, requestId = 19
09:20:02.404 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:20:02.404 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@28c75c93[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:20:02.404 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750295999692_127.0.0.1_55084
09:20:02.407 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750295999692_127.0.0.1_55084]Ignore complete event,isRunning:false,isAbandon=false
09:20:02.412 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3fc7abf6[Running, pool size = 28, active threads = 0, queued tasks = 0, completed tasks = 28]
09:21:37.487 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
09:21:38.999 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0
09:21:39.120 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 68 ms to scan 1 urls, producing 3 keys and 6 values 
09:21:39.181 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:21:39.198 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
09:21:39.425 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 224 ms to scan 237 urls, producing 0 keys and 0 values 
09:21:39.440 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
09:21:39.464 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
09:21:39.484 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
09:21:39.694 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 206 ms to scan 237 urls, producing 0 keys and 0 values 
09:21:39.701 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:39.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1715602761
09:21:39.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/366752671
09:21:39.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:39.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:39.728 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:41.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296101330_127.0.0.1_55916
09:21:41.617 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Notify connected event to listeners.
09:21:41.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:41.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de4c121b-b986-4cc8-bd95-1ff9226efcfa_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/449541293
09:21:41.768 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
09:21:47.698 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:21:48.115 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9cebefb3-73d1-4a5a-9223-ed648bab0093
09:21:48.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] RpcClient init label, labels = {module=naming, source=sdk}
09:21:48.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:21:48.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:21:48.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:21:48.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:48.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296108128_127.0.0.1_55999
09:21:48.241 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Notify connected event to listeners.
09:21:48.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:48.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/449541293
09:21:48.671 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:21:49.580 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2870be57-dc7a-4d0e-a943-006b972122c1_config-0
09:21:49.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:21:49.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1715602761
09:21:49.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/366752671
09:21:49.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:21:49.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:21:49.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:21:49.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750296109587_127.0.0.1_56003
09:21:49.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:21:49.696 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Notify connected event to listeners.
09:21:49.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2870be57-dc7a-4d0e-a943-006b972122c1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/449541293
09:21:50.806 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:21:50.807 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:21:50.810 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:21:50.811 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:21:51.037 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
09:21:51.142 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 14.568 seconds (JVM running for 15.974)
09:21:51.173 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
09:21:51.175 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
09:21:51.176 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
09:21:51.539 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:21:51.546 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:22:20.948 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:22:20.950 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cebefb3-73d1-4a5a-9223-ed648bab0093] Ack server push request, request = NotifySubscriberRequest, requestId = 6
