11:44:54.653 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.3.Final
11:44:56.117 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 902850b4-6b5f-4d85-a877-b4f18d7bd075_config-0
11:44:56.237 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 57 ms to scan 1 urls, producing 3 keys and 6 values 
11:44:56.298 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 4 keys and 9 values 
11:44:56.315 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 3 keys and 10 values 
11:44:56.595 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 277 ms to scan 237 urls, producing 0 keys and 0 values 
11:44:56.611 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 5 values 
11:44:56.633 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
11:44:56.650 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
11:44:56.918 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 265 ms to scan 237 urls, producing 0 keys and 0 values 
11:44:56.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [902850b4-6b5f-4d85-a877-b4f18d7bd075_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:44:56.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [902850b4-6b5f-4d85-a877-b4f18d7bd075_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1715602761
11:44:56.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [902850b4-6b5f-4d85-a877-b4f18d7bd075_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/366752671
11:44:56.926 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [902850b4-6b5f-4d85-a877-b4f18d7bd075_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:44:56.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [902850b4-6b5f-4d85-a877-b4f18d7bd075_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:44:56.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [902850b4-6b5f-4d85-a877-b4f18d7bd075_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:44:59.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [902850b4-6b5f-4d85-a877-b4f18d7bd075_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750218299119_127.0.0.1_58528
11:44:59.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [902850b4-6b5f-4d85-a877-b4f18d7bd075_config-0] Notify connected event to listeners.
11:44:59.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [902850b4-6b5f-4d85-a877-b4f18d7bd075_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:44:59.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [902850b4-6b5f-4d85-a877-b4f18d7bd075_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/449541293
11:45:00.084 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStartupProfileInfo,646] - The following 1 profile is active: "dev"
11:45:08.469 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
11:45:08.955 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f0dd1b62-8fbe-4342-bc80-d680e481ce3c
11:45:08.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] RpcClient init label, labels = {module=naming, source=sdk}
11:45:08.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:45:08.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:45:08.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:45:08.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:45:09.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750218308967_127.0.0.1_58641
11:45:09.074 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Notify connected event to listeners.
11:45:09.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:45:09.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/449541293
11:45:09.497 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
11:45:10.388 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5fe5232b-d1ae-4294-ab18-45aff4bf226f_config-0
11:45:10.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe5232b-d1ae-4294-ab18-45aff4bf226f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:45:10.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe5232b-d1ae-4294-ab18-45aff4bf226f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$336/1715602761
11:45:10.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe5232b-d1ae-4294-ab18-45aff4bf226f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$337/366752671
11:45:10.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe5232b-d1ae-4294-ab18-45aff4bf226f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:45:10.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe5232b-d1ae-4294-ab18-45aff4bf226f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:45:10.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe5232b-d1ae-4294-ab18-45aff4bf226f_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:45:10.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe5232b-d1ae-4294-ab18-45aff4bf226f_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750218310394_127.0.0.1_58651
11:45:10.499 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe5232b-d1ae-4294-ab18-45aff4bf226f_config-0] Notify connected event to listeners.
11:45:10.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe5232b-d1ae-4294-ab18-45aff4bf226f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:45:10.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fe5232b-d1ae-4294-ab18-45aff4bf226f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$346/449541293
11:45:11.272 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Receive server push request, request = NotifySubscriberRequest, requestId = 5
11:45:11.274 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Ack server push request, request = NotifySubscriberRequest, requestId = 5
11:45:11.991 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP ruoyi-gateway 192.168.0.68:8097 register finished
11:45:12.030 [main] INFO  c.r.g.RuoYiGatewayApplication - [logStarted,61] - Started RuoYiGatewayApplication in 18.492 seconds (JVM running for 20.099)
11:45:12.037 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway, group=DEFAULT_GROUP
11:45:12.037 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway.yaml, group=DEFAULT_GROUP
11:45:12.038 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListenersForApplications,105] - listening config: dataId=ruoyi-gateway-dev.yaml, group=DEFAULT_GROUP
11:45:12.502 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Receive server push request, request = NotifySubscriberRequest, requestId = 6
11:45:12.512 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Ack server push request, request = NotifySubscriberRequest, requestId = 6
11:45:41.633 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Receive server push request, request = NotifySubscriberRequest, requestId = 9
11:45:41.634 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Ack server push request, request = NotifySubscriberRequest, requestId = 9
15:05:15.038 [nacos-grpc-client-executor-3151] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Receive server push request, request = NotifySubscriberRequest, requestId = 11
15:05:15.038 [nacos-grpc-client-executor-3151] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Ack server push request, request = NotifySubscriberRequest, requestId = 11
16:38:14.535 [nacos-grpc-client-executor-4704] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Receive server push request, request = NotifySubscriberRequest, requestId = 13
16:38:14.536 [nacos-grpc-client-executor-4704] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Ack server push request, request = NotifySubscriberRequest, requestId = 13
16:38:33.347 [nacos-grpc-client-executor-4712] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Receive server push request, request = NotifySubscriberRequest, requestId = 15
16:38:33.349 [nacos-grpc-client-executor-4712] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0dd1b62-8fbe-4342-bc80-d680e481ce3c] Ack server push request, request = NotifySubscriberRequest, requestId = 15
