{"version": 3, "file": "mergeScan.js", "sources": ["../../../src/internal/operators/mergeScan.ts"], "names": [], "mappings": ";AAKA,OAAO,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAyCjG,MAAM,UAAU,SAAS,CAAO,WAAoE,EACpE,IAAO,EACP,UAA6C;IAA7C,2BAAA,EAAA,aAAqB,MAAM,CAAC,iBAAiB;IAC3E,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC,EAAjE,CAAiE,CAAC;AACtG,CAAC;AAED;IACE,2BAAoB,WAAoE,EACpE,IAAO,EACP,UAAkB;QAFlB,gBAAW,GAAX,WAAW,CAAyD;QACpE,SAAI,GAAJ,IAAI,CAAG;QACP,eAAU,GAAV,UAAU,CAAQ;IACtC,CAAC;IAED,gCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,mBAAmB,CAC7C,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CACzD,CAAC,CAAC;IACL,CAAC;IACH,wBAAC;AAAD,CAAC,AAXD,IAWC;;AAOD;IAA+C,+CAA2B;IAOxE,6BAAY,WAA0B,EAClB,WAAoE,EACpE,GAAM,EACN,UAAkB;QAHtC,YAIE,kBAAM,WAAW,CAAC,SACnB;QAJmB,iBAAW,GAAX,WAAW,CAAyD;QACpE,SAAG,GAAH,GAAG,CAAG;QACN,gBAAU,GAAV,UAAU,CAAQ;QAT9B,cAAQ,GAAY,KAAK,CAAC;QAC1B,kBAAY,GAAY,KAAK,CAAC;QAC9B,YAAM,GAAsB,EAAE,CAAC;QAC/B,YAAM,GAAW,CAAC,CAAC;QACjB,WAAK,GAAW,CAAC,CAAC;;IAO5B,CAAC;IAES,mCAAK,GAAf,UAAgB,KAAU;QACxB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;YACjC,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YACrC,IAAI,GAAG,SAAA,CAAC;YACR,IAAI;gBACM,IAAA,8BAAW,CAAU;gBAC7B,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aAC3C;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,WAAW,CAAC,KAAM,CAAC,CAAC,CAAC,CAAC;aAC9B;YACD,IAAI,CAAC,MAAM,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;SACrB;aAAM;YACL,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACzB;IACH,CAAC;IAEO,uCAAS,GAAjB,UAAkB,GAAQ;QACxB,IAAM,eAAe,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC;QACxD,IAAM,WAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;QACrD,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACjC,IAAM,iBAAiB,GAAG,cAAc,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC;QAI/D,IAAI,iBAAiB,KAAK,eAAe,EAAE;YACzC,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;SACpC;IACH,CAAC;IAES,uCAAS,GAAnB;QACE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;YACjD,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;gBAC3B,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClC;YACD,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,wCAAU,GAAV,UAAW,UAAa;QACd,IAAA,8BAAW,CAAU;QAC7B,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC;QACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,WAAW,CAAC,IAAK,CAAC,UAAU,CAAC,CAAC;IAChC,CAAC;IAED,4CAAc,GAAd;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;SAC5B;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE;YACjD,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,EAAE;gBAC3B,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClC;YACD,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;IACH,CAAC;IACH,0BAAC;AAAD,CAAC,AA3ED,CAA+C,qBAAqB,GA2EnE"}