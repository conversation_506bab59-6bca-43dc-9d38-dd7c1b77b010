{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\myCollect\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\myCollect\\index.vue", "mtime": 1750311963061}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_appliMarket", "require", "_userMenu", "_interopRequireDefault", "_store", "name", "dicts", "components", "UserMenu", "data", "loading", "queryParams", "pageNum", "total", "tableData", "created", "getList", "methods", "_this", "collectList", "then", "res", "code", "viewDetail", "id", "routeData", "$router", "resolve", "path", "query", "window", "open", "href", "cancelCollect", "_this2", "userId", "store", "getters", "appliCancelCollect", "$message", "success", "handleCurrentChange", "num"], "sources": ["src/views/system/user/myCollect/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\" v-loading=\"loading\">\r\n          <el-table :data=\"tableData\" style=\"width: 100%\">\r\n            <el-table-column prop=\"appName\" label=\"收藏应用\"> </el-table-column>\r\n            <el-table-column prop=\"appLabel\" label=\"应用标签\">\r\n            </el-table-column>\r\n            <el-table-column label=\"状态\"> 已收藏 </el-table-column>\r\n            <el-table-column label=\"操作\" width=\"150\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  @click.native.prevent=\"viewDetail(scope.row.id)\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                >\r\n                  详情\r\n                </el-button>\r\n                <el-button\r\n                  @click.native.prevent=\"cancelCollect(scope.row.id)\"\r\n                  type=\"text\"\r\n                  size=\"small\"\r\n                >\r\n                  取消收藏\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <el-pagination\r\n            v-show=\"total > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            :page-size=\"5\"\r\n            :current-page.sync=\"queryParams.pageNum\"\r\n            @current-change=\"handleCurrentChange\"\r\n            :total=\"total\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { collectList, appliCancelCollect } from \"@/api/appliMarket\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport store from \"@/store\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      tableData: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      collectList().then((res) => {\r\n        this.loading = false;\r\n        if (res.code === 200) {\r\n          this.tableData = res.data;\r\n          // this.total = res.total;\r\n        }\r\n      });\r\n    },\r\n    viewDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/purchaseapp\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    cancelCollect(id) {\r\n      let data = {\r\n        id,\r\n        userId: store.getters.userId,\r\n      };\r\n      appliCancelCollect(data).then((res) => {\r\n        if (res.code === 200) {\r\n          this.$message.success(\"操作成功!\");\r\n          this.getList();\r\n        }\r\n      });\r\n    },\r\n    handleCurrentChange(num) {\r\n      this.queryParams.pageNum = num;\r\n      this.getList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  height: calc(100vh - 150px);\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  // background: rgb(242, 248, 255);\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAiDA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAI,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,WAAA;QACAC,OAAA;MACA;MACAC,KAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAR,OAAA;MACA,IAAAS,wBAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,KAAA,CAAAR,OAAA;QACA,IAAAW,GAAA,CAAAC,IAAA;UACAJ,KAAA,CAAAJ,SAAA,GAAAO,GAAA,CAAAZ,IAAA;UACA;QACA;MACA;IACA;IACAc,UAAA,WAAAA,WAAAC,EAAA;MACA,IAAAC,SAAA,QAAAC,OAAA,CAAAC,OAAA;QACAC,IAAA;QACAC,KAAA;UAAAL,EAAA,EAAAA;QAAA;MACA;MACAM,MAAA,CAAAC,IAAA,CAAAN,SAAA,CAAAO,IAAA;IACA;IACAC,aAAA,WAAAA,cAAAT,EAAA;MAAA,IAAAU,MAAA;MACA,IAAAzB,IAAA;QACAe,EAAA,EAAAA,EAAA;QACAW,MAAA,EAAAC,cAAA,CAAAC,OAAA,CAAAF;MACA;MACA,IAAAG,+BAAA,EAAA7B,IAAA,EAAAW,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAY,MAAA,CAAAK,QAAA,CAAAC,OAAA;UACAN,MAAA,CAAAlB,OAAA;QACA;MACA;IACA;IACAyB,mBAAA,WAAAA,oBAAAC,GAAA;MACA,KAAA/B,WAAA,CAAAC,OAAA,GAAA8B,GAAA;MACA,KAAA1B,OAAA;IACA;EACA;AACA", "ignoreList": []}]}