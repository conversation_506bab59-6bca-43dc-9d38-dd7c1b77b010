{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\im.js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\api\\im.js", "mtime": 1750311961311}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1750313272376}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZ2V0Q3VzdG9tZXJTZXJ2aWNlckluZm8gPSBnZXRDdXN0b21lclNlcnZpY2VySW5mbzsKZXhwb3J0cy5nZXRJbmZvRGV0YWlsID0gZ2V0SW5mb0RldGFpbDsKZXhwb3J0cy5nZXRVc2VyTGlzdEJ5SWRzID0gZ2V0VXNlckxpc3RCeUlkczsKZXhwb3J0cy5nZXRVc2VyTGlzdEJ5TmFtZSA9IGdldFVzZXJMaXN0QnlOYW1lOwpleHBvcnRzLmxpc3RJbmZvID0gbGlzdEluZm87CmV4cG9ydHMucmV2b2NhdGlvbkFwcGx5ID0gcmV2b2NhdGlvbkFwcGx5Owp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4uL3V0aWxzL3JlcXVlc3QiKSk7Ci8qDQogKiBAQXV0aG9yOiB6aGMNCiAqIEBEYXRlOiAyMDIzLTA2LTEyIDEzOjU5OjA4DQogKiBATGFzdEVkaXRUaW1lOiAyMDIzLTA2LTEyIDEzOjU5OjA5DQogKiBARGVzY3JpcHRpb246DQogKiBATGFzdEVkaXRvcnM6IHpoYw0KICovCi8qDQogKiBAQXV0aG9yOiB6aGMNCiAqIEBEYXRlOiAyMDIzLTA2LTA4IDE1OjA5OjAzDQogKiBATGFzdEVkaXRUaW1lOiAyMDIzLTA2LTA4IDE1OjA5OjA4DQogKiBARGVzY3JpcHRpb246DQogKiBATGFzdEVkaXRvcnM6IHpoYw0KICovCgovLyDmn6Xor6Lmtojmga/pgJrnn6XliJfooagKZnVuY3Rpb24gbGlzdEluZm8oZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAiL3N5c3RlbS9pbmZvL2xpc3QiLAogICAgbWV0aG9kOiAiZ2V0IiwKICAgIGRhdGE6IGRhdGEKICB9KTsKfQovL+iOt+W<PERSON>luWuouacjeeUqOaIt+S/oeaBrwpmdW5jdGlvbiBnZXRDdXN0b21lclNlcnZpY2VySW5mbygpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogIi9zeXN0ZW0vaW0vZ2V0Q3VzdG9tZXJTZXJ2aWNlckluZm8iLAogICAgbWV0aG9kOiAiZ2V0IgogIH0pOwp9CgovLyDmn6Xor6LlhazlkYror6bnu4YKZnVuY3Rpb24gZ2V0SW5mb0RldGFpbChpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAiL3N5c3RlbS9pbmZvL2dldEluZm8iLAogICAgbWV0aG9kOiAiZ2V0IiwKICAgIGRhdGE6IHsKICAgICAgaWQ6IGlkCiAgICB9CiAgfSk7Cn0KCi8vIOaSpOmUgOWKoOWFpeS8geS4mueahOeUs+ivtwpmdW5jdGlvbiByZXZvY2F0aW9uQXBwbHkoaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogIi9zeXN0ZW0vaW5mby9leGl0LWNvbXBhbnk/aWQ9Ii5jb25jYXQoaWQpLAogICAgbWV0aG9kOiAiZ2V0IgogIH0pOwp9CgovLyDmoLnmja7nlKjmiLdpZOWIl+ihqOiOt+WPlueUqOaIt+S/oeaBrwpmdW5jdGlvbiBnZXRVc2VyTGlzdEJ5SWRzKGlkcykgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAiL3N5c3RlbS9pbS9nZXRVc2VyTGlzdEJ5SWRzIiwKICAgIG1ldGhvZDogInBvc3QiLAogICAgZGF0YTogewogICAgICBpZHM6IGlkcwogICAgfQogIH0pOwp9Ci8vIOagueaNrueUqOaIt+aYteensOaooeeziuafpeivouiBiuWkqeeUqOaItwpmdW5jdGlvbiBnZXRVc2VyTGlzdEJ5TmFtZShyZWFsTmFtZSwgaWRzKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICIvc3lzdGVtL2ltL2dldFVzZXJMaXN0QnlOYW1lIiwKICAgIG1ldGhvZDogInBvc3QiLAogICAgZGF0YTogewogICAgICByZWFsTmFtZTogcmVhbE5hbWUsCiAgICAgIGlkczogaWRzCiAgICB9CiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listInfo", "data", "request", "url", "method", "getCustomerServicerInfo", "getInfoDetail", "id", "revocationApply", "concat", "getUserListByIds", "ids", "getUserListByName", "realName"], "sources": ["E:/company/nmd/nmdnew/share-intelligent/share-intelligent-portal-new/src/api/im.js"], "sourcesContent": ["/*\r\n * @Author: zhc\r\n * @Date: 2023-06-12 13:59:08\r\n * @LastEditTime: 2023-06-12 13:59:09\r\n * @Description:\r\n * @LastEditors: zhc\r\n */\r\n/*\r\n * @Author: zhc\r\n * @Date: 2023-06-08 15:09:03\r\n * @LastEditTime: 2023-06-08 15:09:08\r\n * @Description:\r\n * @LastEditors: zhc\r\n */\r\n\r\nimport request from \"../utils/request\";\r\n\r\n// 查询消息通知列表\r\nexport function listInfo(data) {\r\n  return request({\r\n    url: \"/system/info/list\",\r\n    method: \"get\",\r\n    data,\r\n  });\r\n}\r\n//获取客服用户信息\r\nexport function getCustomerServicerInfo() {\r\n  return request({\r\n    url: \"/system/im/getCustomerServicerInfo\",\r\n    method: \"get\",\r\n  });\r\n}\r\n\r\n// 查询公告详细\r\nexport function getInfoDetail(id) {\r\n  return request({\r\n    url: \"/system/info/getInfo\",\r\n    method: \"get\",\r\n    data: { id },\r\n  });\r\n}\r\n\r\n// 撤销加入企业的申请\r\nexport function revocationApply(id) {\r\n  return request({\r\n    url: `/system/info/exit-company?id=${id}`,\r\n    method: \"get\",\r\n  });\r\n}\r\n\r\n// 根据用户id列表获取用户信息\r\nexport function getUserListByIds(ids) {\r\n  return request({\r\n    url: \"/system/im/getUserListByIds\",\r\n    method: \"post\",\r\n    data: { ids: ids },\r\n  });\r\n}\r\n// 根据用户昵称模糊查询聊天用户\r\nexport function getUserListByName(realName, ids) {\r\n  return request({\r\n    url: \"/system/im/getUserListByName\",\r\n    method: \"post\",\r\n    data: { realName: realName, ids: ids },\r\n  });\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAeA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AACO,SAASC,QAAQA,CAACC,IAAI,EAAE;EAC7B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbH,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;AACA;AACO,SAASI,uBAAuBA,CAAA,EAAG;EACxC,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,aAAaA,CAACC,EAAE,EAAE;EAChC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbH,IAAI,EAAE;MAAEM,EAAE,EAAFA;IAAG;EACb,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACD,EAAE,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,kCAAAM,MAAA,CAAkCF,EAAE,CAAE;IACzCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,gBAAgBA,CAACC,GAAG,EAAE;EACpC,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,MAAM;IACdH,IAAI,EAAE;MAAEU,GAAG,EAAEA;IAAI;EACnB,CAAC,CAAC;AACJ;AACA;AACO,SAASC,iBAAiBA,CAACC,QAAQ,EAAEF,GAAG,EAAE;EAC/C,OAAO,IAAAT,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,MAAM;IACdH,IAAI,EAAE;MAAEY,QAAQ,EAAEA,QAAQ;MAAEF,GAAG,EAAEA;IAAI;EACvC,CAAC,CAAC;AACJ", "ignoreList": []}]}