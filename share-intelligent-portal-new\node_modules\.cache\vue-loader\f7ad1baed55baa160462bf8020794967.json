{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\dockingRecords\\detail.vue?vue&type=style&index=0&id=b6c4ee42&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\dockingRecords\\detail.vue", "mtime": 1750311963054}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KHRvIHJpZ2h0LCByZ2IoMjI1LCAyNDcsIDI0MCksIHJnYigyNDQsIDI1MiwgMjUwKSk7DQogICAgaGVpZ2h0OiAxMDAwcHg7DQp9DQoNCi50b3Agew0KICAgIHBhZGRpbmc6IDIwcHg7DQogICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICBib3JkZXItcmFkaXVzOiAxMHB4Ow0KICAgIG1hcmdpbi10b3A6IDIwcHg7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQoNCiAgICAuY29udGVudF90aXRsZSB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KDQogICAgICAgIC5pY29uIHsNCiAgICAgICAgICAgIHdpZHRoOiA0cHg7DQogICAgICAgICAgICBoZWlnaHQ6IDIwcHg7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjMjFjOWI4Ow0KICAgICAgICB9DQoNCiAgICAgICAgLnRpdGxlIHsNCiAgICAgICAgICAgIGZvbnQtZmFtaWx5OiBTb3VyY2UgSGFuIFNhbnMgQ047DQogICAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICAgICAgY29sb3I6ICMwMzBhMWE7DQogICAgICAgICAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgICAgICAgfQ0KDQogICAgICAgIC5idXR0b25TdHlsZSB7DQogICAgICAgICAgICBwYWRkaW5nOiAxMHB4IDIwcHg7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjMjFjOWI4Ow0KICAgICAgICAgICAgY29sb3I6ICNmZmY7DQogICAgICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAgICAgICBib3JkZXItcmFkaXVzOiAxMHB4Ow0KICAgICAgICAgICAgbWFyZ2luLWxlZnQ6IGF1dG87DQogICAgICAgIH0NCiAgICB9DQp9DQoNCi5xdWVyeUZvcm0gew0KICAgIHBhZGRpbmc6IDIwcHg7DQp9DQoNCi5jb250ZW50IHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOw0KICAgIHBhZGRpbmc6IDIwcHg7DQogICAgbWFyZ2luLXRvcDogMjBweDsNCiAgICBib3JkZXItcmFkaXVzOiAxMHB4Ow0KDQogICAgcCB7DQogICAgICAgIG1hcmdpbjogMTBweCAwOw0KICAgIH0NCn0NCg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6DA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/system/user/dockingRecords", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-row :gutter=\"20\">\r\n            <el-col :span=\"2.5\" :xs=\"24\">\r\n                <user-menu activeIndex=\"1\" />\r\n            </el-col>\r\n            <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n                <div class=\"top\">\r\n                    <div class=\"content_title\">\r\n                        <div class=\"icon\"></div>\r\n                        <div class=\"title\">我的对接记录</div>\r\n                        <div class=\"buttonStyle\" @click=\"cancel\">返回</div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"content\">\r\n                    <p>意向提交时间：{{ this.content.createTime }}</p>\r\n                    <p>资源类型：{{ this.content.intentionTypeName }}</p>\r\n                    <p v-if=\"this.content.title\">标题：{{ this.content.title }}</p>\r\n                    <p>意向描述：{{ this.content.intentionContent }}</p>\r\n                    <p v-if=\"this.content.completionDate\">完成日期：{{ this.content.completionDate }}</p>\r\n                    <p v-if=\"this.content.quantity\">承接量：{{ this.content.quantity }}</p>\r\n                    <p v-if=\"this.content.price\">含税单价：{{ this.content.price }}</p>\r\n                    <p v-if=\"this.content.rate\">税率：{{ this.content.rate }}</p>\r\n                    <p v-if=\"this.content.shippingFee\">运费：{{ this.content.shippingFee }}</p>\r\n                    <p v-if=\"this.content.sum\">含税合计：{{ this.content.sum }}</p>\r\n                    <p>联系人：{{ this.userinfo.memberRealName }}</p>\r\n                    <p>联系电话：{{ this.userinfo.memberPhone }}</p>\r\n                </div>\r\n            </el-col>\r\n        </el-row>\r\n    </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\n\r\nexport default {\r\n    name: \"User\",\r\n    components: { UserMenu },\r\n    data() {\r\n        return {\r\n            id: 1,\r\n            content: {},\r\n            userinfo: {}\r\n        };\r\n    },\r\n    created() {\r\n        if (this.$route.query.id) {\r\n            this.id = this.$route.query.id;\r\n            this.content = this.$route.query;\r\n            console.log(this.content)\r\n        }\r\n        this.userinfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n    },\r\n    methods: {\r\n        cancel() {\r\n            this.$router.go(-1);\r\n        },\r\n    },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n    background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n    height: 1000px;\r\n}\r\n\r\n.top {\r\n    padding: 20px;\r\n    background: #fff;\r\n    border-radius: 10px;\r\n    margin-top: 20px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .content_title {\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n\r\n        .icon {\r\n            width: 4px;\r\n            height: 20px;\r\n            background: #21c9b8;\r\n        }\r\n\r\n        .title {\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 18px;\r\n            color: #030a1a;\r\n            margin-left: 10px;\r\n        }\r\n\r\n        .buttonStyle {\r\n            padding: 10px 20px;\r\n            background: #21c9b8;\r\n            color: #fff;\r\n            text-align: center;\r\n            cursor: pointer;\r\n            border-radius: 10px;\r\n            margin-left: auto;\r\n        }\r\n    }\r\n}\r\n\r\n.queryForm {\r\n    padding: 20px;\r\n}\r\n\r\n.content {\r\n    background-color: #fff;\r\n    padding: 20px;\r\n    margin-top: 20px;\r\n    border-radius: 10px;\r\n\r\n    p {\r\n        margin: 10px 0;\r\n    }\r\n}\r\n</style>"]}]}