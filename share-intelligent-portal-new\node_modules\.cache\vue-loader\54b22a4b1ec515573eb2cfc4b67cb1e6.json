{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\index.vue?vue&type=template&id=6f35d124&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\index.vue", "mtime": 1750311962991}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}