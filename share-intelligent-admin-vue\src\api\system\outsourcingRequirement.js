import request from '@/utils/request'

// 查询工序外协需求列表
export function listOutsourcingRequirement(query) {
  return request({
    url: '/system/outsourcingRequirement/list',
    method: 'get',
    params: query
  })
}

// 查询工序外协需求详细
export function getOutsourcingRequirement(id) {
  return request({
    url: '/system/outsourcingRequirement/' + id,
    method: 'get'
  })
}

// 新增工序外协需求
export function addOutsourcingRequirement(data) {
  return request({
    url: '/system/outsourcingRequirement',
    method: 'post',
    data: data
  })
}

// 修改工序外协需求
export function updateOutsourcingRequirement(data) {
  return request({
    url: '/system/outsourcingRequirement',
    method: 'put',
    data: data
  })
}

// 删除工序外协需求
export function delOutsourcingRequirement(id) {
  return request({
    url: '/system/outsourcingRequirement/' + id,
    method: 'delete'
  })
}
