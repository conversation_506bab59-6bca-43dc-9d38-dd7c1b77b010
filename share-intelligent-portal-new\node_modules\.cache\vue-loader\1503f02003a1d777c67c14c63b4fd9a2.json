{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\orderManage\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\orderManage\\detail.vue", "mtime": 1750311963068}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBvcmRlckRlbCwgaW52b2ljZUxpc3QgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7DQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsgdXBsb2FkVXJsIH0gZnJvbSAiQC9hcGkvb3NzIjsNCmltcG9ydCB7IGdldFRva2VuIH0gZnJvbSAiQC91dGlscy9hdXRoIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiT3BlcmxvZyIsDQogIGRpY3RzOiBbInN5c19vcGVyX3R5cGUiLCAic3lzX2NvbW1vbl9zdGF0dXMiXSwNCiAgY29tcG9uZW50czogeyBVc2VyTWVudSB9LA0KICBkYXRhKCkgew0KICAgIHZhciB2YWxpZElzRW1wdHlBcnIgPSAocywgdmFsdWUsIGNhbGxiYWNrKSA9PiB7DQogICAgICBpZiAoIUFycmF5LmlzQXJyYXkodmFsdWUpIHx8IHZhbHVlLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuivt+S4iuS8oOaWh+S7tiIpKTsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgY2FsbGJhY2soKTsNCiAgICB9Ow0KICAgIHJldHVybiB7DQogICAgICBoZWFkZXJzOiB7IEF1dGhvcml6YXRpb246ICJCZWFyZXIgIiArIGdldFRva2VuKCkgfSwNCiAgICAgIGFjdGlvblVybDogdXBsb2FkVXJsKCksDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgfSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgZmxhZzogIjAiLA0KICAgICAgdGFibGVEYXRhOiBbXSwNCiAgICAgIGluZm86IHt9LA0KICAgICAgaW52b2ljZURhdGE6IHt9LA0KICAgICAgb3JkZXJGb3JtOiBbDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogMSwNCiAgICAgICAgICBzdGF0dXNOYW1lOiAi5b6F5pSv5LuYIiwNCiAgICAgICAgICBkZXNjOiAi5aaC5a+56K6i5Y2V5pyJ55aR6Zeu77yM5Y+v6IGU57O75a6i5pyNNDAwOC05MzktMzY1IiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiAyLA0KICAgICAgICAgIHN0YXR1c05hbWU6ICLlvoXlj5HotKciLA0KICAgICAgICAgIGRlc2M6ICLlubPlj7DlsIbkuo4yMDIzLTA4LTA05pel5YmN5Y+R6LSn77yM5oSf6LCi5oKo55qE5pSv5oyBIeWmguaCqOWvueiuouWNleacieeWkemXru+8jOWPr+iBlOezu+WuouacjTQwMDgtOTM5LTM2NSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogMywNCiAgICAgICAgICBzdGF0dXNOYW1lOiAi5pSv5LuY5aSx6LSlIiwNCiAgICAgICAgICBkZXNjOiAi6K6i5Y2V5pSv5LuY5aSx6LSl77yM5aaC5oKo5a+56K6i5Y2V5pyJ55aR6Zeu77yM5Y+v6IGU57O75a6i5pyNNDAwOC05MzktMzY1IiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiA0LA0KICAgICAgICAgIHN0YXR1c05hbWU6ICLlt7Llj5HotKciLA0KICAgICAgICAgIGRlc2M6ICLkvb/nlKjov4fnqIvkuK3mnInku7vkvZXpl67popjvvIzlj6/ogZTns7vlrqLmnI00MDA4LTkzOS0zNjUiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6IDUsDQogICAgICAgICAgc3RhdHVzTmFtZTogIuW3suaIkOS6pCIsDQogICAgICAgICAgZGVzYzogIuS9v+eUqOi/h+eoi+S4reacieS7u+S9lemXrumimO+8jOWPr+iBlOezu+WuouacjTQwMDgtOTM5LTM2NSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogNiwNCiAgICAgICAgICBzdGF0dXNOYW1lOiAi5b6F57ut6LS5IiwNCiAgICAgICAgICBkZXNjOiAi6K+35bC95b+r57ut6LS577yM5Lul5YWN5b2x5ZON5q2j5bi45L2/55SoIiwNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiA3LA0KICAgICAgICAgIHN0YXR1c05hbWU6ICLlt7LlhbPpl60iLA0KICAgICAgICAgIGRlc2M6ICLlpoLlr7norqLljZXmnInnlpHpl67vvIzlj6/ogZTns7vlrqLmnI00MDA4LTkzOS0zNjUiLA0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdmFsdWU6IDgsDQogICAgICAgICAgc3RhdHVzTmFtZTogIuaUr+S7mOS4rSIsDQogICAgICAgICAgZGVzYzogIuWmguWvueiuouWNleacieeWkemXru+8jOWPr+iBlOezu+WuouacjTQwMDgtOTM5LTM2NSIsDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB2YWx1ZTogOSwNCiAgICAgICAgICBzdGF0dXNOYW1lOiAi5bey5Y+W5raIIiwNCiAgICAgICAgICBkZXNjOiAi5aaC5a+56K6i5Y2V5pyJ55aR6Zeu77yM5Y+v6IGU57O75a6i5pyNNDAwOC05MzktMzY1IiwNCiAgICAgICAgfSwNCiAgICAgIF0sDQogICAgICBpbnZvaWNlVmlzaWJsZTogZmFsc2UsDQogICAgICBydWxlRm9ybTogew0KICAgICAgICBjb21wYW55Q2FyZExpc3Q6IFtdLA0KICAgICAgfSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGNvbXBhbnlDYXJkTGlzdDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIHZhbGlkYXRvcjogdmFsaWRJc0VtcHR5QXJyLCB0cmlnZ2VyOiAiY2hhbmdlIiB9LA0KICAgICAgICBdLA0KICAgICAgfSwNCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsZXQgaWQgPSB0aGlzLiRyb3V0ZS5xdWVyeS5pZDsNCiAgICAgIGxldCBkYXRhID0gew0KICAgICAgICBpZCwNCiAgICAgIH07DQogICAgICBvcmRlckRlbChkYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICAgIHRoaXMudGFibGVEYXRhID0gW107DQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5pbmZvID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhpcy50YWJsZURhdGEucHVzaChyZXMuZGF0YSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlQ3VycmVudENoYW5nZShwYWdlTnVtKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSBwYWdlTnVtOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBjYW5jZWxPcmRlcihpZCkgew0KICAgICAgdGhpcy4kY29uZmlybSgi6K6i5Y2V5Y+W5raI5ZCO5peg5rOV5oGi5aSN77yM6K+36LCo5oWO5pON5L2cISIsICLlj5bmtojorqLljZUiLCB7DQogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAi56Gu5a6aIiwNCiAgICAgICAgY2FuY2VsQnV0dG9uVGV4dDogIuWPlua2iCIsDQogICAgICAgIHR5cGU6ICJ3YXJuaW5nIiwNCiAgICAgIH0pDQogICAgICAgIC50aGVuKCgpID0+IHsNCiAgICAgICAgICBjYW5jZWxPcmRlcihpZCkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuaTjeS9nOaIkOWKnyEiKTsNCiAgICAgICAgICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIGFwcGx5SW52b2ljZSgpIHsNCiAgICAgIHRoaXMuZ2V0SW52b2ljZURhdGEoKTsNCiAgICB9LA0KICAgIGdldEludm9pY2VEYXRhKCkgew0KICAgICAgaW52b2ljZUxpc3QoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmludm9pY2VEYXRhID0gcmVzLmRhdGE7DQogICAgICAgICAgdGhpcy5pbnZvaWNlVmlzaWJsZSA9IHRydWU7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnMucnVsZUZvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGxldCBkYXRhID0gew0KICAgICAgICAgICAgY29tcGFueUNhcmRMaXN0OiB0aGlzLnJ1bGVGb3JtLmNvbXBhbnlDYXJkTGlzdCwNCiAgICAgICAgICAgIG9yZGVySWQ6IHRoaXMuY3VycmVudElkLA0KICAgICAgICAgIH07DQogICAgICAgICAgc2VuZEludm9pY2UoZGF0YSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgICAgICB0aGlzLmludm9pY2VWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfISIpOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBjb25zb2xlLmxvZygiZXJyb3Igc3VibWl0ISEiKTsNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgY2FuY2VsRGlhbG9nKCkgew0KICAgICAgdGhpcy5pbnZvaWNlVmlzaWJsZSA9IGZhbHNlOw0KICAgIH0sDQogICAgZ29CYWNrKCkgew0KICAgICAgdGhpcy4kcm91dGVyLmdvKC0xKTsNCiAgICB9LA0KICAgIGdvU2hpcChpZCkgew0KICAgICAgdGhpcy4kY29uZmlybSgi6LSn5ZOB5Y+R6LSn5ZCO5peg5rOV5pKk6ZSA77yM56Gu6K6k5Y+R6LSn5ZCX77yfIiwgIuWPkei0p+ehruiupCIsIHsNCiAgICAgICAgY29uZmlybUJ1dHRvblRleHQ6ICLnoa7lrpoiLA0KICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAi5Y+W5raIIiwNCiAgICAgICAgdHlwZTogIndhcm5pbmciLA0KICAgICAgfSkNCiAgICAgICAgLnRoZW4oKCkgPT4gew0KICAgICAgICAgIGxldCBkYXRhID0gew0KICAgICAgICAgICAgaWQsDQogICAgICAgICAgICBvcmRlclN0YXR1czogNCwNCiAgICAgICAgICB9Ow0KICAgICAgICAgIG1vZGlmeVN0YXR1cyhkYXRhKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5pON5L2c5oiQ5YqfISIpOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKCgpID0+IHt9KTsNCiAgICB9LA0KICAgIGludm9pY2luZygpIHsNCiAgICAgIGludm9pY2VMaXN0KCkudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5pbnZvaWNlRGF0YSA9IHJlcy5kYXRhOw0KICAgICAgICAgIHRoaXMuaW52b2ljZVZpc2libGUgPSB0cnVlOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGhhbmRsZUFwcGxpY2F0aW9uUmVtb3ZlKGZpbGUsIGZpbGVMaXN0KSB7DQogICAgICB0aGlzLnJ1bGVGb3JtLmNvbXBhbnlDYXJkTGlzdCA9IFtdOw0KICAgIH0sDQogICAgaGFuZGxlQXBwbGljYXRpb25TdWNjZXNzKHJlcywgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy5ydWxlRm9ybS5jb21wYW55Q2FyZExpc3QucHVzaCh7DQogICAgICAgICAgbmFtZTogcmVzLmRhdGEubmFtZSwNCiAgICAgICAgICB1cmw6IHJlcy5kYXRhLnVybCwNCiAgICAgICAgICB0eXBlOiByZXMuZGF0YS50eXBlLA0KICAgICAgICAgIHN1ZmZpeDogcmVzLmRhdGEuc3VmZml4LA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUV4Y2VlZExpY2VuY2UoZmlsZXMsIGZpbGVMaXN0KSB7DQogICAgICBsZXQgbnVtID0gZmlsZXMubGVuZ3RoICsgZmlsZUxpc3QubGVuZ3RoOw0KICAgICAgaWYgKG51bSA+PSAxKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuS4iuS8oOaVsOmHj+i2hei/h+S4iumZkCIpOw0KICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVQcmV2aWV3KGZpbGUpIHsNCiAgICAgIHdpbmRvdy5vcGVuKGZpbGUudXJsKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2NA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/system/user/orderManage", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"content\">\r\n          <div class=\"content_top\">\r\n            <div class=\"orderStatus\">\r\n              <div class=\"statusName\" v-if=\"info.orderStatus\">\r\n                {{\r\n                  orderForm.filter((item) => item.value == info.orderStatus)[0]\r\n                    .statusName\r\n                }}\r\n              </div>\r\n              <div class=\"desc\" v-if=\"info.orderStatus\">\r\n                {{\r\n                  orderForm.filter((item) => item.value == info.orderStatus)[0]\r\n                    .desc\r\n                }}\r\n              </div>\r\n            </div>\r\n            <div class=\"amountMoney\">\r\n              <span style=\"color: rgb(173, 173, 173)\">订单金额:</span>\r\n              <span style=\"margin-left: 10px\">¥ {{ info.price }}</span>\r\n            </div>\r\n            <!-- 待支付 -->\r\n            <!-- <div class=\"button_content\" v-if=\"info.orderStatus == 1\">\r\n              <div>\r\n                <div class=\"buttonStyle\">去支付</div>\r\n                <div style=\"margin-top: 10px\">\r\n                  <span\r\n                    style=\"color: #21C9B8; cursor: pointer\"\r\n                    @click=\"cancelOrder(info.id)\"\r\n                    >取消订单</span\r\n                  >\r\n                  <span\r\n                    style=\"margin-left: 20px; color: #21C9B8; cursor: pointer\"\r\n                    @click=\"tryout(info.erweima)\"\r\n                    >前往试用</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div> -->\r\n            <!-- 待发货 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 2\">\r\n              <div class=\"buttonStyle\" @click=\"goShip(info.id)\">去发货</div>\r\n            </div>\r\n            <!-- 已发货 -->\r\n            <div class=\"button_content\" v-if=\"info.orderStatus == 4\">\r\n              <div class=\"buttonStyle\" @click=\"invoicing\">\r\n                {{ info.makeinvoice == 0 ? \"开具发票\" : \"重新开票\" }}\r\n              </div>\r\n            </div>\r\n            <!-- 已成交 -->\r\n            <!-- <div class=\"button_content\" v-if=\"info.orderStatus == 5\">\r\n              <div>\r\n                <div>\r\n                  <span\r\n                    style=\"color: #21C9B8; cursor: pointer\"\r\n                    @click=\"applyInvoice(info.id)\"\r\n                    >已开票</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div> -->\r\n            <!-- 待续费 -->\r\n            <!-- <div class=\"button_content\" v-if=\"info.orderStatus == 6\">\r\n              <div>\r\n                <div>\r\n                  <span\r\n                    style=\"color: #21C9B8; cursor: pointer\"\r\n                    @click=\"applyInvoice(info.id)\"\r\n                    >去支付</span\r\n                  >\r\n                </div>\r\n              </div>\r\n            </div> -->\r\n          </div>\r\n          <div class=\"content_bottom\">\r\n            <div>\r\n              <el-descriptions title=\"订单信息\" :column=\"2\">\r\n                <el-descriptions-item label=\"订单编号\">{{\r\n                  info.id\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"下单时间\">{{\r\n                  info.orderDate\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"应用提供\">{{\r\n                  info.supply\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"付款时间\">\r\n                  <el-tag size=\"small\">{{\r\n                    info.payTime ? parseTime(info.payTime) : \"--\"\r\n                  }}</el-tag>\r\n                </el-descriptions-item>\r\n                <el-descriptions-item label=\"应用编号\">{{\r\n                  info.appCode\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"发货时间\">{{\r\n                  info.deliverTime\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"付款方式\">{{\r\n                  info.payWay\r\n                }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"成交时间\">{{\r\n                  info.makeTime\r\n                }}</el-descriptions-item>\r\n              </el-descriptions>\r\n            </div>\r\n            <div style=\"margin-top: 30px\">\r\n              <el-table :data=\"tableData\" style=\"width: 100%\">\r\n                <el-table-column prop=\"remark\" label=\"产品标题\">\r\n                </el-table-column>\r\n                <el-table-column label=\"产品图片\">\r\n                  <template slot-scope=\"scope\">\r\n                    <img\r\n                      style=\"width: 100px; height: 100px\"\r\n                      :src=\"scope.row.appLogo\"\r\n                      alt=\"\"\r\n                    />\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column prop=\"appCategory\" label=\"产品类型\">\r\n                </el-table-column>\r\n                <el-table-column label=\"规格信息\">\r\n                  <template slot-scope=\"scoped\">\r\n                    <!-- <div>规格: {{ scoped.spec }}</div> -->\r\n                    <div>\r\n                      可用时长: {{ scoped.validTime == \"1\" ? \"一年\" : \"永久\" }}\r\n                    </div>\r\n                    <div>可用人数: 不限</div>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column label=\"有效时间\">\r\n                  <template slot-scope=\"scope\">\r\n                    {{\r\n                      scope.row.expirationTime\r\n                        ? parseTime(scope.row.expirationTime)\r\n                        : \"--\"\r\n                    }}\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </div>\r\n          </div>\r\n          <div class=\"btnStyle\">\r\n            <el-button @click=\"goBack\">返回</el-button>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <el-dialog\r\n      title=\"开票信息\"\r\n      :visible.sync=\"invoiceVisible\"\r\n      width=\"750px\"\r\n      append-to-body\r\n    >\r\n      <el-form\r\n        ref=\"ruleForm\"\r\n        :model=\"ruleForm\"\r\n        :rules=\"rules\"\r\n        label-width=\"80px\"\r\n      >\r\n        <el-form-item label=\"发票类型:\">\r\n          {{ invoiceData.invoiceType == 1 ? \"专票\" : \"普票\" }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司名称:\">\r\n          {{ invoiceData.companyName }}\r\n        </el-form-item>\r\n        <el-form-item label=\"税号:\">\r\n          {{ invoiceData.dutyParagraph }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司地址:\">\r\n          {{ invoiceData.address }}\r\n        </el-form-item>\r\n        <el-form-item label=\"公司电话:\">\r\n          {{ invoiceData.phone }}\r\n        </el-form-item>\r\n        <el-form-item label=\"开户银行:\">\r\n          {{ invoiceData.openAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"银行账号:\">\r\n          {{ invoiceData.bankAccount }}\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱地址:\">\r\n          {{ invoiceData.email }}\r\n        </el-form-item>\r\n        <el-form-item label=\"上传发票\" prop=\"companyCardList\">\r\n          <el-upload\r\n            :headers=\"headers\"\r\n            :action=\"actionUrl\"\r\n            accept=\".pdf\"\r\n            :file-list=\"ruleForm.companyCardList\"\r\n            :on-remove=\"handleApplicationRemove\"\r\n            :on-success=\"handleApplicationSuccess\"\r\n            :on-exceed=\"handleExceedLicence\"\r\n            :on-preview=\"handlePreview\"\r\n            :limit=\"1\"\r\n          >\r\n            <div>\r\n              <el-button size=\"small\" type=\"primary\" icon=\"el-icon-upload2\"\r\n                >上传文件</el-button\r\n              >\r\n              <span style=\"margin-left: 10px\">仅限pdf格式</span>\r\n            </div>\r\n          </el-upload>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancelDialog\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">发送发票</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { orderDel, invoiceList } from \"@/api/system/user\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  name: \"Operlog\",\r\n  dicts: [\"sys_oper_type\", \"sys_common_status\"],\r\n  components: { UserMenu },\r\n  data() {\r\n    var validIsEmptyArr = (s, value, callback) => {\r\n      if (!Array.isArray(value) || value.length === 0) {\r\n        callback(new Error(\"请上传文件\"));\r\n        return;\r\n      }\r\n      callback();\r\n    };\r\n    return {\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      actionUrl: uploadUrl(),\r\n      queryParams: {\r\n        pageNum: 1,\r\n      },\r\n      total: 0,\r\n      flag: \"0\",\r\n      tableData: [],\r\n      info: {},\r\n      invoiceData: {},\r\n      orderForm: [\r\n        {\r\n          value: 1,\r\n          statusName: \"待支付\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 2,\r\n          statusName: \"待发货\",\r\n          desc: \"平台将于2023-08-04日前发货，感谢您的支持!如您对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 3,\r\n          statusName: \"支付失败\",\r\n          desc: \"订单支付失败，如您对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 4,\r\n          statusName: \"已发货\",\r\n          desc: \"使用过程中有任何问题，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 5,\r\n          statusName: \"已成交\",\r\n          desc: \"使用过程中有任何问题，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 6,\r\n          statusName: \"待续费\",\r\n          desc: \"请尽快续费，以免影响正常使用\",\r\n        },\r\n        {\r\n          value: 7,\r\n          statusName: \"已关闭\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 8,\r\n          statusName: \"支付中\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n        {\r\n          value: 9,\r\n          statusName: \"已取消\",\r\n          desc: \"如对订单有疑问，可联系客服4008-939-365\",\r\n        },\r\n      ],\r\n      invoiceVisible: false,\r\n      ruleForm: {\r\n        companyCardList: [],\r\n      },\r\n      rules: {\r\n        companyCardList: [\r\n          { required: true, validator: validIsEmptyArr, trigger: \"change\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      let id = this.$route.query.id;\r\n      let data = {\r\n        id,\r\n      };\r\n      orderDel(data).then((res) => {\r\n        this.loading = false;\r\n        this.tableData = [];\r\n        if (res.code === 200) {\r\n          this.info = res.data;\r\n          this.tableData.push(res.data);\r\n        }\r\n      });\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.queryParams.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    cancelOrder(id) {\r\n      this.$confirm(\"订单取消后无法恢复，请谨慎操作!\", \"取消订单\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          cancelOrder(id).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.$router.go(-1);\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    applyInvoice() {\r\n      this.getInvoiceData();\r\n    },\r\n    getInvoiceData() {\r\n      invoiceList().then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceData = res.data;\r\n          this.invoiceVisible = true;\r\n        }\r\n      });\r\n    },\r\n    submitForm() {\r\n      this.$refs.ruleForm.validate((valid) => {\r\n        if (valid) {\r\n          let data = {\r\n            companyCardList: this.ruleForm.companyCardList,\r\n            orderId: this.currentId,\r\n          };\r\n          sendInvoice(data).then((res) => {\r\n            if (res.code === 200) {\r\n              this.invoiceVisible = false;\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        } else {\r\n          console.log(\"error submit!!\");\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    cancelDialog() {\r\n      this.invoiceVisible = false;\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    goShip(id) {\r\n      this.$confirm(\"货品发货后无法撤销，确认发货吗？\", \"发货确认\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          let data = {\r\n            id,\r\n            orderStatus: 4,\r\n          };\r\n          modifyStatus(data).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"操作成功!\");\r\n              this.getList();\r\n            }\r\n          });\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    invoicing() {\r\n      invoiceList().then((res) => {\r\n        if (res.code === 200) {\r\n          this.invoiceData = res.data;\r\n          this.invoiceVisible = true;\r\n        }\r\n      });\r\n    },\r\n    handleApplicationRemove(file, fileList) {\r\n      this.ruleForm.companyCardList = [];\r\n    },\r\n    handleApplicationSuccess(res, file, fileList) {\r\n      if (res.code == 200) {\r\n        this.ruleForm.companyCardList.push({\r\n          name: res.data.name,\r\n          url: res.data.url,\r\n          type: res.data.type,\r\n          suffix: res.data.suffix,\r\n        });\r\n      }\r\n    },\r\n    handleExceedLicence(files, fileList) {\r\n      let num = files.length + fileList.length;\r\n      if (num >= 1) {\r\n        this.$message.error(\"上传数量超过上限\");\r\n        return false;\r\n      }\r\n    },\r\n    handlePreview(file) {\r\n      window.open(file.url);\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: #f4f5f9;\r\n}\r\n.content {\r\n  width: 100%;\r\n  padding: 40px;\r\n  background: #ffffff;\r\n  .content_top {\r\n    display: flex;\r\n    width: 100%;\r\n    height: 120px;\r\n    align-items: center;\r\n    .orderStatus {\r\n      width: 30%;\r\n      text-align: center;\r\n      .statusName {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n      }\r\n      .desc {\r\n        font-size: 14px;\r\n        color: rgb(173, 173, 173);\r\n        margin-top: 30px;\r\n      }\r\n    }\r\n    .amountMoney {\r\n      width: 40%;\r\n      text-align: center;\r\n    }\r\n    .button_content {\r\n      width: 20%;\r\n      text-align: center;\r\n      .buttonStyle {\r\n        height: 50px;\r\n        background: #21c9b8;\r\n        line-height: 50px;\r\n        color: #ffffff;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n  .content_bottom {\r\n    margin-top: 20px;\r\n    padding: 20px;\r\n    width: 100%;\r\n  }\r\n  .btnStyle {\r\n    text-align: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}