<template>
  <div class="content">
    <div class="content_banner">
      人才服务
      <div class="imgContent">
        <div class="imgStyle">
          <img
            style="width: 100%; height: 100%"
            src="../../../../assets/order/orderStep.png"
            alt=""
          />
          <div class="joinNow" @click="joinNow">立即入驻</div>
        </div>
      </div>
    </div>
    <div class="card-container card_top">
      <div class="card_top_item">
        <div class="largeCategory">岗位分类：</div>
        <div
          class="smallCategory"
          :class="postCategory === item.dictValue ? 'smallCategoryActive' : ''"
          v-for="(item, index) in positionTypeList"
          :key="index"
          @click="switchPostCategory(item.dictValue)"
        >
          {{ item.dictLabel }}
        </div>
      </div>
      <div class="card_top_itemLine"></div>
      <div class="card_top_item">
        <div class="largeCategory">最高学历：</div>
        <div
          class="smallCategory"
          :class="educational === item.dictValue ? 'smallCategoryActive' : ''"
          v-for="(item, index) in educationList"
          :key="index"
          @click="switchEducational(item.dictValue)"
        >
          {{ item.dictLabel }}
        </div>
      </div>
      <div class="card_top_itemLine"></div>
      <div class="card_top_item">
        <div class="largeCategory">职 称：</div>
        <div
          class="smallCategory"
          :class="
            technicalTitle === item.dictValue ? 'smallCategoryActive' : ''
          "
          v-for="(item, index) in jobTitleList"
          :key="index"
          @click="switchTechnicalTitle(item.dictValue)"
        >
          {{ item.dictLabel }}
        </div>
      </div>
      <div class="card_top_itemLine"></div>
      <div class="card_top_item">
        <div class="largeCategory">工作状态：</div>
        <div
          class="smallCategory"
          :class="
            workingCondition === item.dictValue ? 'smallCategoryActive' : ''
          "
          v-for="(item, index) in workStatusList"
          :key="index"
          @click="switchWorkingCondition(item.dictValue)"
        >
          {{ item.dictLabel }}
        </div>
      </div>
      <div class="buttonStyle">
        <div class="imgStyle" @click="initList">
          <img
            style="width: 100%; height: 100%"
            src="../../../../assets/serviceSharing/reset.png"
            alt=""
          />
        </div>
        <div class="buttonText" @click="resetData">重置筛选</div>
      </div>
    </div>
    <div class="card-container card_bottom">
      <div
        class="content_bottom"
        v-loading="loading"
        v-if="talentList && talentList.length > 0"
      >
        <div
          class="card_bottom_item tr2"
          v-for="(item, index) in talentList"
          :key="index"
          @click="goDetail(item.id)"
        >
          <span class="bottom"></span>
          <span class="right"></span>
          <span class="top"></span>
          <span class="left"></span>
          <!-- 左侧图片 -->
          <div class="imgStyle">
            <img
              style="width: 100%; height: 100%"
              :src="
                item.photo
                  ? item.photo
                  : require('../../../../assets/serviceSharing/ceshi.png')
              "
              alt=""
            />
          </div>
          <!-- 右侧内容 -->
          <div class="content_bottom_right">
            <div class="title ellips1">{{ item.name }}</div>
            <div class="category" v-if="item.education">
              {{
                educationList.filter(
                  (item1) => item1.dictValue == item.education
                )[0].dictLabel
              }}
            </div>
            <div class="statusStyle">
              {{ item.workStatus == "0" ? "在职" : "离职" }}
            </div>
          </div>
        </div>
      </div>
      <div class="none-class" v-else>
        <el-image
          style="width: 160px; height: 160px"
          :src="require('@/assets/user/none.png')"
          :fit="fit"
        ></el-image>
        <div class="text">暂无数据</div>
      </div>
      <!-- 分页 -->
      <div class="pageStyle">
        <el-pagination
          v-if="talentList && talentList.length > 0"
          background
          layout="prev, pager, next"
          class="activity-pagination"
          :page-size="pageSize"
          :current-page="pageNum"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { listData } from "@/api/system/dict/data";
import { talentListData } from "@/api/serviceSharing";
export default {
  data() {
    return {
      loading: false,
      pageNum: 1,
      pageSize: 16,
      total: 0,
      postCategory: "",
      educational: "",
      technicalTitle: "",
      workingCondition: "",
      talentList: [],
      positionTypeList: [], // 岗位分类
      educationList: [], // 最高学历
      jobTitleList: [], // 职称
      workStatusList: [], // 工作状态
      fit: "cover",
    };
  },
  created() {
    this.getPositionType();
    this.getEducation();
    this.getJobTitle();
    this.getWorkStatus();
    this.getList();
  },
  methods: {
    // 岗位分类
    getPositionType() {
      let params = { dictType: "position_type" };
      listData(params).then((response) => {
        this.positionTypeList = response.rows;
        this.positionTypeList.unshift({
          dictValue: "",
          dictLabel: "全部",
        });
      });
    },
    // 最高学历
    getEducation() {
      let params = { dictType: "education" };
      listData(params).then((response) => {
        this.educationList = response.rows;
        this.educationList.unshift({
          dictValue: "",
          dictLabel: "全部",
        });
      });
    },
    // 职称
    getJobTitle() {
      let params = { dictType: "job_title" };
      listData(params).then((response) => {
        this.jobTitleList = response.rows;
        this.jobTitleList.unshift({
          dictValue: "",
          dictLabel: "全部",
        });
      });
    },
    // 工作状态
    getWorkStatus() {
      let params = { dictType: "work_status" };
      listData(params).then((response) => {
        this.workStatusList = response.rows;
        this.workStatusList.unshift({
          dictValue: "",
          dictLabel: "全部",
        });
      });
    },
    getList() {
      this.loading = true;
      let params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        positionType: this.postCategory,
        education: this.educational,
        jobTitle: this.technicalTitle,
        workStatus: this.workingCondition,
        settledStatus: "1",
      };
      talentListData(params).then((res) => {
        if (res.code === 200) {
          this.talentList = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    joinNow() {
      this.$router.push({
        path: "/talentJoinNow",
      });
    },
    switchPostCategory(index) {
      this.postCategory = index;
      this.getList();
    },
    switchEducational(index) {
      this.educational = index;
      this.getList();
    },
    switchTechnicalTitle(index) {
      this.technicalTitle = index;
      this.getList();
    },
    switchWorkingCondition(index) {
      this.workingCondition = index;
      this.getList();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.pageNum = pageNum;
      this.getList();
    },
    goDetail(id) {
      this.$router.push("/talentDetail?id=" + id);
    },
    initList() {
      this.getList();
    },
    resetData() {
      this.postCategory = "";
      this.educational = "";
      this.technicalTitle = "";
      this.workingCondition = "";
      this.getList();
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  padding-bottom: 60px;
  background-color: #f2f2f2;
}
.content_banner {
  width: 100%;
  height: 300px;
  background-image: url("../../../../assets/release/banner.png");
  background-size: 100% 100%;
  text-align: center;
  margin: 0 auto;
  padding-top: 28px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  font-size: 40px;
  color: #000;
  .imgContent {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 10px;
    .imgStyle {
      width: 1256px;
      height: 206px;
      position: relative;
      .joinNow {
        position: absolute;
        right: 90px;
        top: 75px;
        width: 110px;
        height: 50px;
        background: #f79a47;
        border-radius: 2px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #ffffff;
        line-height: 50px;
        text-align: center;
        cursor: pointer;
      }
    }
  }
}
.card_top {
  background: #ffffff;
  border-radius: 2px;
  margin-top: 30px;
  padding: 30px;
  // padding: 58px 60px 32px 62px;
  .card_top_item {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .largeCategory {
      width: 90px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #222222;
      margin-right: 28px;
    }
    .smallCategory {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      padding: 12px 24px;
      cursor: pointer;
    }
    .smallCategoryActive {
      background: #e0f7f5;
      border-radius: 2px;
      color: #21c9b8;
    }
  }
  .card_top_item:nth-child(1) {
    margin-top: 0;
  }
  .card_top_itemLine {
    width: 100%;
    height: 1px;
    background: #eeeeee;
    margin-top: 20px;
  }
  .buttonStyle {
    margin-top: 9px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    .imgStyle {
      width: 19px;
      height: 16px;
      cursor: pointer;
    }
    .buttonText {
      margin-left: 10px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #21c9b8;
      cursor: pointer;
    }
  }
}
.card_bottom {
  height: 850px;
  background: #ffffff;
  border-radius: 2px;
  margin-top: 30px;
  padding: 40px 60px 62px 60px;
  .content_bottom {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .card_bottom_item {
      width: 255px;
      height: 150px;
      background: #ffffff;
      box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.06);
      border-radius: 4px;
      border: 2px solid #ffffff;
      margin-left: 20px;
      padding: 20px 18px;
      display: flex;
      cursor: pointer;

      span {
        position: absolute;
        z-index: 1;
        background-color: #37c9b8;
        transition: transform 0.5s ease;
      }
      .bottom,
      .top {
        height: 2px;
        left: -1px;
        right: -1px;
        transform: scaleX(0);
      }
      .left,
      .right {
        width: 2px;
        top: -1px;
        bottom: -1px;
        transform: scaleY(0);
      }
      .bottom {
        bottom: -1px;
        transform-origin: bottom right;
      }
      .right {
        right: -1px;
        transform-origin: top right;
      }
      .top {
        top: -1px;
        transform-origin: top left;
      }
      .left {
        left: -1px;
        transform-origin: bottom left;
      }
      .imgStyle {
        width: 90px;
        height: 108px;
      }
      .content_bottom_right {
        margin-left: 17px;
      }
      .title {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 18px;
        color: #000000;
        height: 24px;
      }
      .category {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 16px;
        color: #7e7e7e;
        margin-top: 14px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        text-overflow: ellipsis;
        word-wrap: break-word;
      }
      .statusStyle {
        width: 70px;
        height: 30px;
        margin-top: 22px;
        background: #e0f7f5;
        border-radius: 2px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #21c9b8;
        line-height: 450px;
        text-align: center;
        line-height: 30px;
      }
    }
    .card_bottom_item:nth-child(4n + 1) {
      margin-left: 0;
    }
    .card_bottom_item:nth-child(n + 5) {
      margin-top: 20px;
    }
    .card_bottom_item:hover {
      box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);
      scale: 1.01;
      .top {
        transform-origin: top right;
        transform: scaleX(1);
      }
      .left {
        transform-origin: top left;
        transform: scaleY(1);
      }
      .bottom {
        transform-origin: bottom left;
        transform: scaleX(1);
      }
      .right {
        transform-origin: bottom right;
        transform: scaleY(1);
      }
    }
  }
  .pageStyle {
    margin-top: 60px;
    width: 100%;
    text-align: center;
  }
}
.none-class {
  text-align: center;
  padding: 8% 0;
  .text {
    font-size: 14px;
    font-weight: 400;
    color: #999999;
    line-height: 14px;
  }
}
.ellips1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  text-overflow: ellipsis;
  word-wrap: break-word;
}
</style>
