{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\serviceSharing\\components\\talentPool\\index copy.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\serviceSharing\\components\\talentPool\\index copy.vue", "mtime": 1750311963013}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0RGF0YSB9IGZyb20gIkAvYXBpL3N5c3RlbS9kaWN0L2RhdGEiOw0KaW1wb3J0IHsgdGFsZW50TGlzdERhdGEgfSBmcm9tICJAL2FwaS9zZXJ2aWNlU2hhcmluZyI7DQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgcGFnZU51bTogMSwNCiAgICAgIHBhZ2VTaXplOiAxNiwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgcG9zdENhdGVnb3J5OiAiIiwNCiAgICAgIGVkdWNhdGlvbmFsOiAiIiwNCiAgICAgIHRlY2huaWNhbFRpdGxlOiAiIiwNCiAgICAgIHdvcmtpbmdDb25kaXRpb246ICIiLA0KICAgICAgdGFsZW50TGlzdDogW10sDQogICAgICBwb3NpdGlvblR5cGVMaXN0OiBbXSwgLy8g5bKX5L2N5YiG57G7DQogICAgICBlZHVjYXRpb25MaXN0OiBbXSwgLy8g5pyA6auY5a2m5Y6GDQogICAgICBqb2JUaXRsZUxpc3Q6IFtdLCAvLyDogYznp7ANCiAgICAgIHdvcmtTdGF0dXNMaXN0OiBbXSwgLy8g5bel5L2c54q25oCBDQogICAgICBmaXQ6ICJjb3ZlciIsDQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldFBvc2l0aW9uVHlwZSgpOw0KICAgIHRoaXMuZ2V0RWR1Y2F0aW9uKCk7DQogICAgdGhpcy5nZXRKb2JUaXRsZSgpOw0KICAgIHRoaXMuZ2V0V29ya1N0YXR1cygpOw0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLy8g5bKX5L2N5YiG57G7DQogICAgZ2V0UG9zaXRpb25UeXBlKCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsgZGljdFR5cGU6ICJwb3NpdGlvbl90eXBlIiB9Ow0KICAgICAgbGlzdERhdGEocGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnBvc2l0aW9uVHlwZUxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnBvc2l0aW9uVHlwZUxpc3QudW5zaGlmdCh7DQogICAgICAgICAgZGljdFZhbHVlOiAiIiwNCiAgICAgICAgICBkaWN0TGFiZWw6ICLlhajpg6giLA0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5pyA6auY5a2m5Y6GDQogICAgZ2V0RWR1Y2F0aW9uKCkgew0KICAgICAgbGV0IHBhcmFtcyA9IHsgZGljdFR5cGU6ICJlZHVjYXRpb24iIH07DQogICAgICBsaXN0RGF0YShwYXJhbXMpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuZWR1Y2F0aW9uTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMuZWR1Y2F0aW9uTGlzdC51bnNoaWZ0KHsNCiAgICAgICAgICBkaWN0VmFsdWU6ICIiLA0KICAgICAgICAgIGRpY3RMYWJlbDogIuWFqOmDqCIsDQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDogYznp7ANCiAgICBnZXRKb2JUaXRsZSgpIHsNCiAgICAgIGxldCBwYXJhbXMgPSB7IGRpY3RUeXBlOiAiam9iX3RpdGxlIiB9Ow0KICAgICAgbGlzdERhdGEocGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLmpvYlRpdGxlTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMuam9iVGl0bGVMaXN0LnVuc2hpZnQoew0KICAgICAgICAgIGRpY3RWYWx1ZTogIiIsDQogICAgICAgICAgZGljdExhYmVsOiAi5YWo6YOoIiwNCiAgICAgICAgfSk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOW3peS9nOeKtuaAgQ0KICAgIGdldFdvcmtTdGF0dXMoKSB7DQogICAgICBsZXQgcGFyYW1zID0geyBkaWN0VHlwZTogIndvcmtfc3RhdHVzIiB9Ow0KICAgICAgbGlzdERhdGEocGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLndvcmtTdGF0dXNMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy53b3JrU3RhdHVzTGlzdC51bnNoaWZ0KHsNCiAgICAgICAgICBkaWN0VmFsdWU6ICIiLA0KICAgICAgICAgIGRpY3RMYWJlbDogIuWFqOmDqCIsDQogICAgICAgIH0pOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxldCBwYXJhbXMgPSB7DQogICAgICAgIHBhZ2VOdW06IHRoaXMucGFnZU51bSwNCiAgICAgICAgcGFnZVNpemU6IHRoaXMucGFnZVNpemUsDQogICAgICAgIHBvc2l0aW9uVHlwZTogdGhpcy5wb3N0Q2F0ZWdvcnksDQogICAgICAgIGVkdWNhdGlvbjogdGhpcy5lZHVjYXRpb25hbCwNCiAgICAgICAgam9iVGl0bGU6IHRoaXMudGVjaG5pY2FsVGl0bGUsDQogICAgICAgIHdvcmtTdGF0dXM6IHRoaXMud29ya2luZ0NvbmRpdGlvbiwNCiAgICAgICAgc2V0dGxlZFN0YXR1czogIjEiLA0KICAgICAgfTsNCiAgICAgIHRhbGVudExpc3REYXRhKHBhcmFtcykudGhlbigocmVzKSA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy50YWxlbnRMaXN0ID0gcmVzLnJvd3M7DQogICAgICAgICAgdGhpcy50b3RhbCA9IHJlcy50b3RhbDsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBqb2luTm93KCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goew0KICAgICAgICBwYXRoOiAiL3RhbGVudEpvaW5Ob3ciLA0KICAgICAgfSk7DQogICAgfSwNCiAgICBzd2l0Y2hQb3N0Q2F0ZWdvcnkoaW5kZXgpIHsNCiAgICAgIHRoaXMucG9zdENhdGVnb3J5ID0gaW5kZXg7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIHN3aXRjaEVkdWNhdGlvbmFsKGluZGV4KSB7DQogICAgICB0aGlzLmVkdWNhdGlvbmFsID0gaW5kZXg7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIHN3aXRjaFRlY2huaWNhbFRpdGxlKGluZGV4KSB7DQogICAgICB0aGlzLnRlY2huaWNhbFRpdGxlID0gaW5kZXg7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIHN3aXRjaFdvcmtpbmdDb25kaXRpb24oaW5kZXgpIHsNCiAgICAgIHRoaXMud29ya2luZ0NvbmRpdGlvbiA9IGluZGV4Ow0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICBoYW5kbGVTaXplQ2hhbmdlKHBhZ2VTaXplKSB7DQogICAgICB0aGlzLnBhZ2VTaXplID0gcGFnZVNpemU7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2UocGFnZU51bSkgew0KICAgICAgdGhpcy5wYWdlTnVtID0gcGFnZU51bTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgZ29EZXRhaWwoaWQpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCIvdGFsZW50RGV0YWlsP2lkPSIgKyBpZCk7DQogICAgfSwNCiAgICBpbml0TGlzdCgpIHsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgcmVzZXREYXRhKCkgew0KICAgICAgdGhpcy5wb3N0Q2F0ZWdvcnkgPSAiIjsNCiAgICAgIHRoaXMuZWR1Y2F0aW9uYWwgPSAiIjsNCiAgICAgIHRoaXMudGVjaG5pY2FsVGl0bGUgPSAiIjsNCiAgICAgIHRoaXMud29ya2luZ0NvbmRpdGlvbiA9ICIiOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgfSwNCn07DQo="}, {"version": 3, "sources": ["index copy.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index copy.vue", "sourceRoot": "src/views/serviceSharing/components/talentPool", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      人才服务\r\n      <div class=\"imgContent\">\r\n        <div class=\"imgStyle\">\r\n          <img\r\n            style=\"width: 100%; height: 100%\"\r\n            src=\"../../../../assets/order/orderStep.png\"\r\n            alt=\"\"\r\n          />\r\n          <div class=\"joinNow\" @click=\"joinNow\">立即入驻</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container card_top\">\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">岗位分类：</div>\r\n        <div\r\n          class=\"smallCategory\"\r\n          :class=\"postCategory === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in positionTypeList\"\r\n          :key=\"index\"\r\n          @click=\"switchPostCategory(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">最高学历：</div>\r\n        <div\r\n          class=\"smallCategory\"\r\n          :class=\"educational === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in educationList\"\r\n          :key=\"index\"\r\n          @click=\"switchEducational(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">职 称：</div>\r\n        <div\r\n          class=\"smallCategory\"\r\n          :class=\"\r\n            technicalTitle === item.dictValue ? 'smallCategoryActive' : ''\r\n          \"\r\n          v-for=\"(item, index) in jobTitleList\"\r\n          :key=\"index\"\r\n          @click=\"switchTechnicalTitle(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">工作状态：</div>\r\n        <div\r\n          class=\"smallCategory\"\r\n          :class=\"\r\n            workingCondition === item.dictValue ? 'smallCategoryActive' : ''\r\n          \"\r\n          v-for=\"(item, index) in workStatusList\"\r\n          :key=\"index\"\r\n          @click=\"switchWorkingCondition(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"buttonStyle\">\r\n        <div class=\"imgStyle\" @click=\"initList\">\r\n          <img\r\n            style=\"width: 100%; height: 100%\"\r\n            src=\"../../../../assets/serviceSharing/reset.png\"\r\n            alt=\"\"\r\n          />\r\n        </div>\r\n        <div class=\"buttonText\" @click=\"resetData\">重置筛选</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container card_bottom\">\r\n      <div\r\n        class=\"content_bottom\"\r\n        v-loading=\"loading\"\r\n        v-if=\"talentList && talentList.length > 0\"\r\n      >\r\n        <div\r\n          class=\"card_bottom_item tr2\"\r\n          v-for=\"(item, index) in talentList\"\r\n          :key=\"index\"\r\n          @click=\"goDetail(item.id)\"\r\n        >\r\n          <span class=\"bottom\"></span>\r\n          <span class=\"right\"></span>\r\n          <span class=\"top\"></span>\r\n          <span class=\"left\"></span>\r\n          <!-- 左侧图片 -->\r\n          <div class=\"imgStyle\">\r\n            <img\r\n              style=\"width: 100%; height: 100%\"\r\n              :src=\"\r\n                item.photo\r\n                  ? item.photo\r\n                  : require('../../../../assets/serviceSharing/ceshi.png')\r\n              \"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <!-- 右侧内容 -->\r\n          <div class=\"content_bottom_right\">\r\n            <div class=\"title ellips1\">{{ item.name }}</div>\r\n            <div class=\"category\" v-if=\"item.education\">\r\n              {{\r\n                educationList.filter(\r\n                  (item1) => item1.dictValue == item.education\r\n                )[0].dictLabel\r\n              }}\r\n            </div>\r\n            <div class=\"statusStyle\">\r\n              {{ item.workStatus == \"0\" ? \"在职\" : \"离职\" }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"none-class\" v-else>\r\n        <el-image\r\n          style=\"width: 160px; height: 160px\"\r\n          :src=\"require('@/assets/user/none.png')\"\r\n          :fit=\"fit\"\r\n        ></el-image>\r\n        <div class=\"text\">暂无数据</div>\r\n      </div>\r\n      <!-- 分页 -->\r\n      <div class=\"pageStyle\">\r\n        <el-pagination\r\n          v-if=\"talentList && talentList.length > 0\"\r\n          background\r\n          layout=\"prev, pager, next\"\r\n          class=\"activity-pagination\"\r\n          :page-size=\"pageSize\"\r\n          :current-page=\"pageNum\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { talentListData } from \"@/api/serviceSharing\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageNum: 1,\r\n      pageSize: 16,\r\n      total: 0,\r\n      postCategory: \"\",\r\n      educational: \"\",\r\n      technicalTitle: \"\",\r\n      workingCondition: \"\",\r\n      talentList: [],\r\n      positionTypeList: [], // 岗位分类\r\n      educationList: [], // 最高学历\r\n      jobTitleList: [], // 职称\r\n      workStatusList: [], // 工作状态\r\n      fit: \"cover\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getPositionType();\r\n    this.getEducation();\r\n    this.getJobTitle();\r\n    this.getWorkStatus();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    // 岗位分类\r\n    getPositionType() {\r\n      let params = { dictType: \"position_type\" };\r\n      listData(params).then((response) => {\r\n        this.positionTypeList = response.rows;\r\n        this.positionTypeList.unshift({\r\n          dictValue: \"\",\r\n          dictLabel: \"全部\",\r\n        });\r\n      });\r\n    },\r\n    // 最高学历\r\n    getEducation() {\r\n      let params = { dictType: \"education\" };\r\n      listData(params).then((response) => {\r\n        this.educationList = response.rows;\r\n        this.educationList.unshift({\r\n          dictValue: \"\",\r\n          dictLabel: \"全部\",\r\n        });\r\n      });\r\n    },\r\n    // 职称\r\n    getJobTitle() {\r\n      let params = { dictType: \"job_title\" };\r\n      listData(params).then((response) => {\r\n        this.jobTitleList = response.rows;\r\n        this.jobTitleList.unshift({\r\n          dictValue: \"\",\r\n          dictLabel: \"全部\",\r\n        });\r\n      });\r\n    },\r\n    // 工作状态\r\n    getWorkStatus() {\r\n      let params = { dictType: \"work_status\" };\r\n      listData(params).then((response) => {\r\n        this.workStatusList = response.rows;\r\n        this.workStatusList.unshift({\r\n          dictValue: \"\",\r\n          dictLabel: \"全部\",\r\n        });\r\n      });\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        positionType: this.postCategory,\r\n        education: this.educational,\r\n        jobTitle: this.technicalTitle,\r\n        workStatus: this.workingCondition,\r\n        settledStatus: \"1\",\r\n      };\r\n      talentListData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.talentList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    joinNow() {\r\n      this.$router.push({\r\n        path: \"/talentJoinNow\",\r\n      });\r\n    },\r\n    switchPostCategory(index) {\r\n      this.postCategory = index;\r\n      this.getList();\r\n    },\r\n    switchEducational(index) {\r\n      this.educational = index;\r\n      this.getList();\r\n    },\r\n    switchTechnicalTitle(index) {\r\n      this.technicalTitle = index;\r\n      this.getList();\r\n    },\r\n    switchWorkingCondition(index) {\r\n      this.workingCondition = index;\r\n      this.getList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push(\"/talentDetail?id=\" + id);\r\n    },\r\n    initList() {\r\n      this.getList();\r\n    },\r\n    resetData() {\r\n      this.postCategory = \"\";\r\n      this.educational = \"\";\r\n      this.technicalTitle = \"\";\r\n      this.workingCondition = \"\";\r\n      this.getList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n  background-color: #f2f2f2;\r\n}\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 28px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n  .imgContent {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-top: 10px;\r\n    .imgStyle {\r\n      width: 1256px;\r\n      height: 206px;\r\n      position: relative;\r\n      .joinNow {\r\n        position: absolute;\r\n        right: 90px;\r\n        top: 75px;\r\n        width: 110px;\r\n        height: 50px;\r\n        background: #f79a47;\r\n        border-radius: 2px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 18px;\r\n        color: #ffffff;\r\n        line-height: 50px;\r\n        text-align: center;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n}\r\n.card_top {\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: 30px;\r\n  padding: 30px;\r\n  // padding: 58px 60px 32px 62px;\r\n  .card_top_item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    .largeCategory {\r\n      width: 90px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #222222;\r\n      margin-right: 28px;\r\n    }\r\n    .smallCategory {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n      padding: 12px 24px;\r\n      cursor: pointer;\r\n    }\r\n    .smallCategoryActive {\r\n      background: #e0f7f5;\r\n      border-radius: 2px;\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n  .card_top_item:nth-child(1) {\r\n    margin-top: 0;\r\n  }\r\n  .card_top_itemLine {\r\n    width: 100%;\r\n    height: 1px;\r\n    background: #eeeeee;\r\n    margin-top: 20px;\r\n  }\r\n  .buttonStyle {\r\n    margin-top: 9px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    .imgStyle {\r\n      width: 19px;\r\n      height: 16px;\r\n      cursor: pointer;\r\n    }\r\n    .buttonText {\r\n      margin-left: 10px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #21c9b8;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n.card_bottom {\r\n  height: 850px;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: 30px;\r\n  padding: 40px 60px 62px 60px;\r\n  .content_bottom {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    .card_bottom_item {\r\n      width: 255px;\r\n      height: 150px;\r\n      background: #ffffff;\r\n      box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.06);\r\n      border-radius: 4px;\r\n      border: 2px solid #ffffff;\r\n      margin-left: 20px;\r\n      padding: 20px 18px;\r\n      display: flex;\r\n      cursor: pointer;\r\n\r\n      span {\r\n        position: absolute;\r\n        z-index: 1;\r\n        background-color: #37c9b8;\r\n        transition: transform 0.5s ease;\r\n      }\r\n      .bottom,\r\n      .top {\r\n        height: 2px;\r\n        left: -1px;\r\n        right: -1px;\r\n        transform: scaleX(0);\r\n      }\r\n      .left,\r\n      .right {\r\n        width: 2px;\r\n        top: -1px;\r\n        bottom: -1px;\r\n        transform: scaleY(0);\r\n      }\r\n      .bottom {\r\n        bottom: -1px;\r\n        transform-origin: bottom right;\r\n      }\r\n      .right {\r\n        right: -1px;\r\n        transform-origin: top right;\r\n      }\r\n      .top {\r\n        top: -1px;\r\n        transform-origin: top left;\r\n      }\r\n      .left {\r\n        left: -1px;\r\n        transform-origin: bottom left;\r\n      }\r\n      .imgStyle {\r\n        width: 90px;\r\n        height: 108px;\r\n      }\r\n      .content_bottom_right {\r\n        margin-left: 17px;\r\n      }\r\n      .title {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 18px;\r\n        color: #000000;\r\n        height: 24px;\r\n      }\r\n      .category {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 16px;\r\n        color: #7e7e7e;\r\n        margin-top: 14px;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 1;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n      .statusStyle {\r\n        width: 70px;\r\n        height: 30px;\r\n        margin-top: 22px;\r\n        background: #e0f7f5;\r\n        border-radius: 2px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #21c9b8;\r\n        line-height: 450px;\r\n        text-align: center;\r\n        line-height: 30px;\r\n      }\r\n    }\r\n    .card_bottom_item:nth-child(4n + 1) {\r\n      margin-left: 0;\r\n    }\r\n    .card_bottom_item:nth-child(n + 5) {\r\n      margin-top: 20px;\r\n    }\r\n    .card_bottom_item:hover {\r\n      box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);\r\n      scale: 1.01;\r\n      .top {\r\n        transform-origin: top right;\r\n        transform: scaleX(1);\r\n      }\r\n      .left {\r\n        transform-origin: top left;\r\n        transform: scaleY(1);\r\n      }\r\n      .bottom {\r\n        transform-origin: bottom left;\r\n        transform: scaleX(1);\r\n      }\r\n      .right {\r\n        transform-origin: bottom right;\r\n        transform: scaleY(1);\r\n      }\r\n    }\r\n  }\r\n  .pageStyle {\r\n    margin-top: 60px;\r\n    width: 100%;\r\n    text-align: center;\r\n  }\r\n}\r\n.none-class {\r\n  text-align: center;\r\n  padding: 8% 0;\r\n  .text {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #999999;\r\n    line-height: 14px;\r\n  }\r\n}\r\n.ellips1 {\r\n  overflow: hidden;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 1;\r\n  text-overflow: ellipsis;\r\n  word-wrap: break-word;\r\n}\r\n</style>\r\n"]}]}