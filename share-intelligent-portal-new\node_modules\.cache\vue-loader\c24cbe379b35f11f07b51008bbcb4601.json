{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\resourceHall\\index.vue?vue&type=template&id=592ced1a&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\resourceHall\\index.vue", "mtime": 1750311962990}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}