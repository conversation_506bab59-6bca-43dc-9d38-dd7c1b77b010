<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
        <el-row :gutter="24">
          <el-col :span="12">
            <div class="user-info-card">
              <!-- 上部分 -->
              <div class="user-info-card-top">
                <!-- 左侧图片 -->
                <div class="imgStyle">
                  <img style="width: 100%; height: 100%" :src="userinfo.avatar ? userinfo.avatar : defaultAvatar"
                    alt="" />
                </div>
                <!-- 右侧内容 -->
                <div class="user-info-card-top-right">
                  <div class="nameStyle">{{ userinfo.memberRealName || '暂无' }}</div>
                  <div class="phoneStyle" style="margin-top: 17px; margin-bottom: 11px">
                    联系电话：{{ userinfo.memberPhone || '暂无' }}
                  </div>
                  <!-- <div class="phoneStyle">所属企业：立即加入></div> -->
                </div>
              </div>
              <!-- 下部分 -->
              <div class="user-info-card-bottom">
                <!-- 顶部导航 -->
                <div class="navStyle">
                  <div class="navStyle-left">
                    <img style="width: 17px; height: 17px" src="../../../../assets/user/business_icon.png" alt="" />
                    <div class="text">企业应用</div>
                  </div>
                  <!-- <div class="navStyle-right">查看更多 >></div> -->
                </div>
                <!-- 底部内容 -->
                <div class="businessAppli">
                  <div class="appliItem" v-for="(item, index) in appliList" :key="index" @click="handleLink(item)">
                    <div class="appliImg">
                      <img style="width: 100%; height: 100%" :src="item.url" alt="" />
                    </div>
                    <div class="appliName">{{ item.name }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="demandSupply">
              <!-- 顶部导航 -->
              <div class="navStyle">
                <div class="navStyle-left" @click="handleClick('1')" :style="demandSupply == '1'
                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'
                  : 'border-bottom: none;cursor: pointer;'
                  ">
                  <img style="width: 17px; height: 17px" src="../../../../assets/user/demand_icon.png" alt="" />
                  <div class="text">我的需求</div>
                </div>
                <div class="navStyle-left" @click="handleClick('2')" style="margin-left: 24px" :style="demandSupply == '2'
                  ? 'border-bottom: 2px solid #21C9B8;cursor: pointer;'
                  : 'border-bottom: none;cursor: pointer;'
                  ">
                  <img style="width: 17px; height: 17px" src="../../../../assets/user/supply_icon.png" alt="" />
                  <div class="text">我的供给</div>
                </div>
                <div class="navStyle-right" @click="goTosupplyDemand">查看更多 >></div>
              </div>
              <el-table :data="demandList" v-if="demandSupply == '1'">
                <el-table-column prop="title" label="需求标题" />
                <el-table-column prop="description" label="需求描述" />
              </el-table>
              <el-table :data="supplyList" v-if="demandSupply == '2'">
                <el-table-column prop="title" label="供给标题" />
                <el-table-column prop="description" label="供给描述" />
              </el-table>
              <!-- 暂无数据 -->
              <!-- <div>
                <div class="noDataStyle">
                  <img style="width: 100%; height: 100%" src="../../../../assets/user/noData.png" alt="" />
                </div>
                <div class="noDataText">暂无内容</div>
              </div> -->
            </div>
          </el-col>
          <el-col :span="12">
            <div class="meNotification">
              <!-- 顶部导航 -->
              <div class="navStyle">
                <div class="navStyle-left">
                  <img style="width: 17px; height: 17px" src="../../../../assets/user/message_icon.png" alt="" />
                  <div class="text">消息通知</div>
                </div>
                <div class="navStyle-right" @click="goToNotice">查看更多 >></div>
              </div>
              <!-- 暂无数据 -->
              <div>
                <div class="noDataStyle">
                  <img style="width: 100%; height: 100%" src="../../../../assets/user/noData.png" alt="" />
                </div>
                <div class="noDataText">暂无内容</div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="dockingRecords">
              <!-- 顶部导航 -->
              <div class="navStyle">
                <div class="navStyle-left">
                  <img style="width: 17px; height: 17px" src="../../../../assets/user/record_icon.png" alt="" />
                  <div class="text">对接记录</div>
                </div>
                <div class="navStyle-right" @click="goTodockingRecords">查看更多 >></div>
              </div>
              <!-- 暂无数据 -->
              <el-table :data="dockingList" v-if="dockingList.length > 0">
                <el-table-column align="center" prop="title" label="资源名称">
                </el-table-column>
                <el-table-column align="center" prop="intentionContent" label="申请内容">
                </el-table-column>
              </el-table>
              <div v-if="dockingList.length == 0">
                <div class="noDataStyle">
                  <img style="width: 100%; height: 100%" src="../../../../assets/user/noData.png" alt="" />
                </div>
                <div class="noDataText">暂无内容</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import UserMenu from "../components/userMenu.vue";
import { getInfo } from "@/api/login";
import { mySupply, myDemand } from "@/api/home";
import { dockingList } from "@/api/system/user";

export default {
  name: "User",
  components: { UserMenu },
  data() {
    return {
      appliList: [
        {
          url: require("../../../../assets/user/appli1.png"),
          name: "复材商城",
          link: 'http://**************:1001/'
        },
        {
          url: require("../../../../assets/user/appli2.png"),
          name: "仓储物流",
        },
        {
          url: require("../../../../assets/user/appli3.png"),
          name: "金融服务",
        },
        {
          url: require("../../../../assets/user/appli4.png"),
          name: "设备管理",
          router: '/user/equipmentManagement'
        },
      ],
      defaultAvatar: require('@/assets/images/avatar.png'),
      demandSupply: "1",
      userinfo: {},
      supplyList: [],
      demandList: [],
      dockingList: [],
    };
  },
  created() {
    this.getInfo();
    this.getSupplyList();
    this.getDemandList();
    this.getDockingList();
  },
  methods: {
    // 获取用户信息
    getInfo() {
      getInfo().then((res) => {
        if (res.code == 200) {
          this.userinfo = res.member;
          window.sessionStorage.setItem("userinfo", JSON.stringify(res.member));
        }
      });
    },
    handleClick(val) {
      this.demandSupply = val;
    },
    handleLink(val) {
      if (val.link) {
        window.open(val.link)
      } else if (val.router) {
        this.$router.push({ path: val.router });
      }
      else {
        this.$notify.info({
          title: '敬请期待',
        });
      }
    },
    getSupplyList() {
      let params = {
        pageNum: 1,
        pageSize: 10,
        queryType: "my",
      };
      mySupply(params).then((res) => {
        if (res.code === 200) {
          this.supplyList = res.rows;
        }
      });
    },
    getDemandList() {
      let params = {
        pageNum: 1,
        pageSize: 10,
        queryType: "my",
      };
      myDemand(params).then((res) => {
        if (res.code === 200) {
          this.demandList = res.rows;
        }
      });
    },
    getDockingList() {
      dockingList({
        pageNum: 1,
        pageSize: 10,
        queryType: 'my',
      }).then((res) => {
        this.dockingList = res.rows;
      });
    },
    goTosupplyDemand() {
      this.$router.push({ path: '/user/supplyDemand' })
    },
    goToNotice() {
      // this.$router.push({path:'/user/notice'})
    },
    goTodockingRecords() {
      this.$router.push({ path: '/user/dockingRecords' })
    }
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));
  height: 1080px;
}

.user-info-card {
  background-color: #fff;
  padding: 20px;
  height: 450px;
  border-radius: 4px;

  .user-info-card-top {
    width: 100%;
    height: 140px;
    background: url("../../../../assets/user/homePageBanner.png") no-repeat;
    background-size: 100% 100%;
    padding: 32px 30px 36px 30px;
    display: flex;

    .imgStyle {
      width: 60px;
      height: 60px;
    }

    .user-info-card-top-right {
      margin-left: 20px;

      .nameStyle {
        height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 500;
        font-size: 18px;
        color: #ffffff;
      }

      .phoneStyle {
        height: 15px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #ffffff;
      }
    }
  }

  .user-info-card-bottom {
    margin-top: 20px;

    .businessAppli {
      margin-top: 31px;
      display: flex;
      align-items: center;

      .appliItem {
        width: 70px;
        margin-left: 41px;
        text-align: center;
        cursor: pointer;
      }

      .appliItem:nth-child(1) {
        margin-left: 0;
      }

      .appliImg {
        width: 70px;
        height: 75px;
      }

      .appliName {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #222222;
        margin-top: 11px;
      }
    }
  }
}

.demandSupply {
  background-color: #fff;
  padding: 20px;
  height: 450px;
  border-radius: 4px;
}

.meNotification {
  background-color: #fff;
  padding: 20px;
  height: 450px;
  border-radius: 4px;
  margin-top: 24px;
}

.dockingRecords {
  background-color: #fff;
  padding: 20px;
  height: 450px;
  border-radius: 4px;
  margin-top: 24px;
}

.navStyle {
  height: 50px;
  border-bottom: 1px solid #ccc;
  display: flex;
  align-items: center;

  .navStyle-left {
    display: flex;
    align-items: center;
    height: 50px;
    border-bottom: 2px solid #21c9b8;
  }

  .text {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #21c9b8;
    margin-left: 10px;
  }

  .navStyle-right {
    font-family: Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    margin-left: auto;
    cursor: pointer;
  }
}

.noDataStyle {
  width: 259px;
  height: 259px;
  margin-top: 63px;
  margin-left: calc((100% - 259px) / 2);
}

.noDataText {
  width: 100%;
  text-align: center;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 16px;
  color: #999999;
}
</style>
