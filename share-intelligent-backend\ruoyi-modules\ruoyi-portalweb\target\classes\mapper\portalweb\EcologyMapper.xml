<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.portalweb.mapper.EcologyMapper">

    <resultMap type="com.ruoyi.portalweb.vo.EcologyVO" id="EcologyResult">
        <result property="ecologyId" column="ecology_id"/>
        <result property="ecologyCategoryId" column="ecology_category_id"/>
        <result property="companyId" column="company_id"/>
        <result property="ecologyIdeas" column="ecology_ideas"/>
        <result property="ecologyContact" column="ecology_contact"/>
        <result property="ecologyPhone" column="ecology_phone"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="memberId" column="member_id"/>
        <result property="ecologyField" column="ecology_field"/>

        <result property="ecologyCategoryName" column="ecology_category_name"/>
        <result property="ecologyFieldName" column="ecology_field_name"/>
        <result property="memberCompanyName" column="member_company_name"/>
    </resultMap>

    <sql id="selectEcologyVo">
        select ecology_id, ecology_category_id, company_id, ecology_ideas, ecology_contact, ecology_phone, del_flag,
        create_by, create_time, update_by, update_time, remark ,member_id,ecology_field
        from ecology
    </sql>

    <sql id="Base_Column_List">
		a.*,
        b.ecology_category_name,
        c.dict_label AS ecology_field_name,
        d.member_company_name
	</sql>

	<sql id="Base_Table_List">
		FROM ecology a
        LEFT JOIN ecology_category b ON a.ecology_category_id = b.ecology_category_id
        LEFT JOIN sys_dict_data c ON a.ecology_field = c.dict_value AND c.dict_type = 'ecology_field'
        LEFT JOIN member d ON a.member_id = d.member_id
	</sql>

    <select id="selectEcologyList" parameterType="Ecology" resultMap="EcologyResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        <where>
            a.del_flag = 0
            <if test="ecologyCategoryId != null ">and a.ecology_category_id = #{ecologyCategoryId}</if>
            <if test="companyId != null  and companyId != ''">and a.company_id = #{companyId}</if>
            <if test="ecologyIdeas != null  and ecologyIdeas != ''">and a.ecology_ideas = #{ecologyIdeas}</if>
            <if test="ecologyContact != null  and ecologyContact != ''">a.and ecology_contact = #{ecologyContact}</if>
            <if test="ecologyPhone != null  and ecologyPhone != ''">and a.ecology_phone = #{ecologyPhone}</if>
            <if test="memberId != null  and memberId != ''">and a.member_id = #{memberId}</if>
            <if test="ecologyField != null  and ecologyField != ''">and a.ecology_field = #{ecologyField}</if>
        </where>
    </select>

    <select id="selectEcologyByEcologyId" parameterType="Long" resultMap="EcologyResult">
        SELECT
		<include refid="Base_Column_List" />
		<include refid="Base_Table_List" />
        where a.ecology_id = #{ecologyId}
    </select>

    <insert id="insertEcology" parameterType="Ecology" useGeneratedKeys="true" keyProperty="ecologyId">
        insert into ecology
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ecologyCategoryId != null">ecology_category_id,</if>
            <if test="companyId != null">company_id,</if>
            <if test="ecologyIdeas != null">ecology_ideas,</if>
            <if test="ecologyContact != null">ecology_contact,</if>
            <if test="ecologyPhone != null">ecology_phone,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="memberId != null">member_id,</if>
            <if test="ecologyField != null">ecology_field,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ecologyCategoryId != null">#{ecologyCategoryId},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="ecologyIdeas != null">#{ecologyIdeas},</if>
            <if test="ecologyContact != null">#{ecologyContact},</if>
            <if test="ecologyPhone != null">#{ecologyPhone},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="ecologyField != null">#{ecologyField},</if>
        </trim>
    </insert>

    <update id="updateEcology" parameterType="Ecology">
        update ecology
        <trim prefix="SET" suffixOverrides=",">
            <if test="ecologyCategoryId != null">ecology_category_id = #{ecologyCategoryId},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="ecologyIdeas != null">ecology_ideas = #{ecologyIdeas},</if>
            <if test="ecologyContact != null">ecology_contact = #{ecologyContact},</if>
            <if test="ecologyPhone != null">ecology_phone = #{ecologyPhone},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="ecologyField != null">ecology_field = #{ecologyField},</if>
        </trim>
        where ecology_id = #{ecologyId}
    </update>

    <delete id="deleteEcologyByEcologyId" parameterType="Long">
        delete from ecology where ecology_id = #{ecologyId}
    </delete>

    <delete id="deleteEcologyByEcologyIds" parameterType="String">
        delete from ecology where ecology_id in
        <foreach item="ecologyId" collection="array" open="(" separator="," close=")">
            #{ecologyId}
        </foreach>
    </delete>
</mapper>