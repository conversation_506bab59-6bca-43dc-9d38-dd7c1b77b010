{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\appliMarket\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\appliMarket\\index.vue", "mtime": 1750311962921}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2FA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/appliMarket", "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"activity-banner\">\r\n      <img src=\"../../assets/appliMarket/appliMarketBanner.png\" alt=\"\" />\r\n      <div class=\"bannerTitle\">应用市场</div>\r\n      <div class=\"bannerDesc\">\r\n        助力企业数字化低碳转型升级，提供成熟完善的数字化应用产品\r\n      </div>\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"activity-title-content\">\r\n        <div class=\"activity-search-box\">\r\n          <el-form ref=\"form\" class=\"activity-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.keywords\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"activity-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"activity-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"appliType\">\r\n        <div\r\n          class=\"everyType\"\r\n          v-for=\"(item, index) in appliTypeData\"\r\n          :key=\"item.dictValue\"\r\n          @click=\"getappliData(item.dictLabel)\"\r\n        >\r\n          <div class=\"everyImg\">\r\n            <img :src=\"appliTypeImgList[index].url\" alt=\"\" />\r\n          </div>\r\n          <div class=\"everyTitle\">{{ item.dictLabel }}</div>\r\n          <div class=\"everyIcon\" v-show=\"flag === item.dictLabel\"></div>\r\n        </div>\r\n      </div>\r\n      <div class=\"appliContent\">\r\n        <div style=\"display: flex; flex-wrap: wrap\">\r\n          <div\r\n            v-loading=\"loading\"\r\n            class=\"everyContent\"\r\n            v-for=\"item in appliDataList\"\r\n            :key=\"item.id\"\r\n            @click=\"goPurchaseapps(item.id)\"\r\n          >\r\n            <div class=\"title\">{{ item.appName }}</div>\r\n            <div class=\"desc\">\r\n              {{ item.briefInto }}\r\n            </div>\r\n            <div\r\n              class=\"tagStyle\"\r\n              v-if=\"item.appLabel && item.appLabel.length > 0\"\r\n            >\r\n              <div\r\n                class=\"everyTag\"\r\n                v-for=\"itemTag in item.appLabel.split(',')\"\r\n                :key=\"itemTag\"\r\n              >\r\n                {{ itemTag }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"activity-page-end\">\r\n          <el-button class=\"activity-page-btn\" @click=\"goHome\">首页</el-button>\r\n          <el-pagination\r\n            v-if=\"appliDataList && appliDataList.length > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            class=\"activity-pagination\"\r\n            :page-size=\"pageSize\"\r\n            :current-page=\"pageNum\"\r\n            :total=\"total\"\r\n            @current-change=\"handleCurrentChange\"\r\n          >\r\n          </el-pagination>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { appliType, appliList } from \"@/api/appliMarket\";\r\n// import { getDicts } from \"@/api/system/dict/data\";\r\n// import { caseList } from \"@/api/classicCase\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      loading: false,\r\n      form: {\r\n        keywords: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        caseType: \"\", // 案例类型\r\n      },\r\n      caseTypeList: [],\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 12,\r\n      total: 0,\r\n      flag: \"全部\",\r\n      appliTypeData: [\r\n        {\r\n          dictValue: \"0\",\r\n          dictLabel: \"全部\",\r\n        },\r\n        {\r\n          dictValue: \"1\",\r\n          dictLabel: \"研发设计\",\r\n        },\r\n        {\r\n          dictValue: \"2\",\r\n          dictLabel: \"生产制造\",\r\n        },\r\n        {\r\n          dictValue: \"3\",\r\n          dictLabel: \"运营管理\",\r\n        },\r\n        {\r\n          dictValue: \"4\",\r\n          dictLabel: \"质量管控\",\r\n        },\r\n        {\r\n          dictValue: \"5\",\r\n          dictLabel: \"仓储物流\",\r\n        },\r\n        {\r\n          dictValue: \"6\",\r\n          dictLabel: \"安全生产\",\r\n        },\r\n        {\r\n          dictValue: \"7\",\r\n          dictLabel: \"节能减排\",\r\n        },\r\n        {\r\n          dictValue: \"8\",\r\n          dictLabel: \"运维服务\",\r\n        },\r\n      ],\r\n      appliTypeImgList: [\r\n        {\r\n          url: require(\"../../assets/appliMarket/type1.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type2.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type3.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type4.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type5.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type6.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type7.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type8.png\"),\r\n        },\r\n        {\r\n          url: require(\"../../assets/appliMarket/type9.png\"),\r\n        },\r\n      ],\r\n      appliDataList: [\r\n        {\r\n          id: 1,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 2,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 3,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 4,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 5,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 6,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 7,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n        {\r\n          id: 8,\r\n          title: \"云端研发\",\r\n          desc: \"促进产学研合作，解决信息孤岛 在创新环节实现提效降本。\",\r\n          tag: \"案例详情\",\r\n        },\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      appliType().then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res.data, \"------------\");\r\n          this.getListData();\r\n        }\r\n      });\r\n    },\r\n    getListData() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        appName: this.form.keywords,\r\n        appCategory: this.flag == \"全部\" ? undefined : this.flag,\r\n        appState: 2,\r\n      };\r\n      appliList(params).then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res, \"7777777777777777777\");\r\n          this.appliDataList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getListData();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.getListData();\r\n    },\r\n    goCaseDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/caseDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n    getappliData(value) {\r\n      this.flag = value;\r\n      this.getListData();\r\n    },\r\n    goPurchaseapps(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/purchaseapp\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.activity-container {\r\n  width: 100%;\r\n  background: #ffffff;\r\n  .activity-banner {\r\n    width: 100%;\r\n    height: 500px;\r\n    position: relative;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n    .bannerTitle {\r\n      position: absolute;\r\n      top: 161px;\r\n      left: 24%;\r\n      font-size: 50px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: bold;\r\n      color: #ffffff;\r\n    }\r\n    .bannerDesc {\r\n      position: absolute;\r\n      top: 249px;\r\n      left: 24%;\r\n      font-size: 24px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      color: #ffffff;\r\n    }\r\n  }\r\n  .activity-title-content {\r\n    width: 100%;\r\n    background-color: #fff;\r\n    // padding-bottom: 18px;\r\n    .activity-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .activity-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .activity-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .activity-search-box {\r\n      margin-top: 40px;\r\n      .activity-search-form {\r\n        text-align: center;\r\n        .activity-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .activity-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .appliType {\r\n    width: 1200px;\r\n    margin: 40px auto 0;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    .everyType {\r\n      width: 102px;\r\n      // height: 160px;\r\n      text-align: center;\r\n      cursor: pointer;\r\n      .everyImg {\r\n        width: 63px;\r\n        height: 78px;\r\n        margin-left: calc((100% - 63px) / 2);\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n      .everyTitle {\r\n        font-size: 18px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #979797;\r\n        margin-top: 10px;\r\n      }\r\n      .everyIcon {\r\n        width: 63px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n        margin-top: 10px;\r\n        margin-left: calc((100% - 63px) / 2);\r\n      }\r\n    }\r\n  }\r\n  .appliContent {\r\n    width: 1200px;\r\n    // height: 500px;\r\n    margin: 0 auto;\r\n    .everyContent {\r\n      width: 280px;\r\n      height: 220px;\r\n      background: #ffffff;\r\n      box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.06);\r\n      border-radius: 4px;\r\n      padding: 30px;\r\n      cursor: pointer;\r\n      margin-left: 24px;\r\n      margin-top: 20px;\r\n      .title {\r\n        font-size: 20px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #222222;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 1;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n      .desc {\r\n        font-size: 16px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        color: #65676a;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n        margin-top: 26px;\r\n      }\r\n      .tagStyle {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-top: 30px;\r\n        .everyTag {\r\n          width: 90px;\r\n          height: 42px;\r\n          border: 1px solid #21c9b8;\r\n          border-radius: 21px;\r\n          font-size: 16px;\r\n          font-family: Source Han Sans CN;\r\n          font-weight: 500;\r\n          color: #21c9b8;\r\n          text-align: center;\r\n          line-height: 42px;\r\n          overflow: hidden;\r\n          display: -webkit-box;\r\n          -webkit-box-orient: vertical;\r\n          -webkit-line-clamp: 1;\r\n          text-overflow: ellipsis;\r\n          word-wrap: break-word;\r\n        }\r\n      }\r\n    }\r\n    .everyContent:hover {\r\n      box-shadow: 0 4px 16px 0 rgba(38, 74, 116, 0.2);\r\n      .title {\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    .everyContent:nth-child(4n + 1) {\r\n      margin-left: 0;\r\n    }\r\n  }\r\n  .none-class {\r\n    text-align: center;\r\n    padding: 8% 0;\r\n    background: #fff;\r\n    margin-top: 25px;\r\n    .text {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    padding: 24px 0 60px;\r\n    .activity-page-btn {\r\n      width: 82px;\r\n      height: 32px;\r\n      background: #fff;\r\n      border-radius: 4px;\r\n      border: 1px solid #d9d9d9;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #333;\r\n      line-height: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.activity-container {\r\n  .activity-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .activity-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .activity-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .activity-page-end {\r\n    .activity-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}