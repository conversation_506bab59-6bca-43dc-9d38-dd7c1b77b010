{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\companyTab.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\components\\companyTab.vue", "mtime": 1750311962929}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["companyTab.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "companyTab.vue", "sourceRoot": "src/views/components/home/<USER>", "sourcesContent": ["<template>\r\n  <div class=\"company-tab-container\">\r\n    <div v-loading=\"loading\" class=\"tab-main\">\r\n      <el-scrollbar noresize class=\"left\">\r\n        <div class=\"tab-content\">\r\n          <div\r\n            v-for=\"(item, index) in tabs\"\r\n            :key=\"index\"\r\n            :class=\"{ active: tabIndex === index }\"\r\n            class=\"tab-content-item\"\r\n            @click=\"onTabChange(index)\"\r\n          >\r\n            {{ item.dictLabel }}\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n      <el-row class=\"right\" :gutter=\"24\">\r\n        <template v-if=\"items.length > 0\">\r\n          <el-col :span=\"8\" v-for=\"item in items\" :key=\"item.id\">\r\n            <router-link\r\n              target=\"_blank\"\r\n              :to=\"`/enterpriseDetail?id=${item.id}&businessNo=${item.businessNo}`\"\r\n            >\r\n              <div class=\"card\">\r\n                <el-image\r\n                  class=\"card-img\"\r\n                  :src=\"item.url ? item.url : defaultUrl\"\r\n                  fit=\"fill\"\r\n                />\r\n                <div class=\"card-footer\">\r\n                  <div class=\"title\" :title=\"item.company\">\r\n                    {{ item.company }}\r\n                  </div>\r\n                  <div class=\"tag\">{{ item.tag }}</div>\r\n                </div>\r\n              </div>\r\n            </router-link>\r\n          </el-col>\r\n        </template>\r\n        <template v-else>\r\n          <el-empty />\r\n        </template>\r\n      </el-row>\r\n    </div>\r\n    <div class=\"tab-page-end\">\r\n      <!-- <span class=\"demonstration\">完整功能</span> -->\r\n      <el-pagination\r\n        class=\"company-tab-pagination\"\r\n        @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\"\r\n        :current-page=\"pageNum\"\r\n        :page-sizes=\"[100, 200, 300, 400]\"\r\n        :page-size=\"pageSize\"\r\n        layout=\" prev, pager, next \"\r\n        :total=\"total\"\r\n      >\r\n      </el-pagination>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { head, map } from \"ramda\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { listCompany } from \"@/api/zhm\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  name: \"CompanyTab\",\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      tabs: [],\r\n      tabIndex: 0,\r\n      items: [],\r\n      pageNum: 1,\r\n      pageSize: 6,\r\n      total: 0,\r\n      defaultUrl: require(\"../../../../assets/purchaseSales/companyDefault.png\"),\r\n    };\r\n  },\r\n  created() {\r\n    this.initData();\r\n  },\r\n  methods: {\r\n    initData() {\r\n      getDicts(\"industrial_chain\").then((res) => {\r\n        const { code, data = [] } = res;\r\n        if (code === 200) {\r\n          this.tabs = data;\r\n          this.tabs.unshift({\r\n            dictLabel: \"全部\",\r\n            dictValue: undefined,\r\n          });\r\n          const item = head(data);\r\n          this.getCompanyData(item.dictValue);\r\n        }\r\n      });\r\n    },\r\n    getCompanyData(type) {\r\n      this.loading = true;\r\n      listCompany({\r\n        industrialChain: type,\r\n        recommendStatus: 1,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          const { code, rows = [] } = res;\r\n          if (code === 200) {\r\n            this.items = map((item) => {\r\n              let url;\r\n              const images = item.companyPictureList || [];\r\n              if (images.length > 0) {\r\n                url = head(images).url;\r\n              }\r\n              return {\r\n                id: item.id,\r\n                company: item.name,\r\n                businessNo: item.businessNo,\r\n                tag: item.category,\r\n                url,\r\n              };\r\n            }, rows);\r\n          }\r\n          this.total = res.total;\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    onTabChange(index) {\r\n      if (index !== this.tabIndex) {\r\n        this.tabIndex = index;\r\n        this.pageNum = 1;\r\n        const item = this.tabs[index] || {};\r\n        this.getCompanyData(item.dictValue);\r\n      }\r\n    },\r\n    handleSizeChange(newSize) {\r\n      // console.log(`每页 ${val} 条`);\r\n      this.pageSize = newSize;\r\n      const item = this.tabs[this.tabIndex] || {};\r\n\r\n      this.getCompanyData(item.dictValue);\r\n    },\r\n    handleCurrentChange(newPage) {\r\n      // console.log(`当前页: ${val}`);\r\n      this.pageNum = newPage;\r\n      const item = this.tabs[this.tabIndex] || {};\r\n      this.getCompanyData(item.dictValue);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n.company-tab-container {\r\n  .tab-main {\r\n    position: relative;\r\n    display: flex;\r\n    flex-shrink: 0;\r\n    width: 100%;\r\n    flex-direction: row;\r\n    ::v-deep .el-scrollbar__wrap {\r\n      overflow-x: hidden;\r\n      overflow-y: auto;\r\n    }\r\n    .left {\r\n      width: 148px;\r\n      height: 580px;\r\n      background: #21c9b8;\r\n      .el-scrollbar__wrap {\r\n        height: 103%;\r\n      }\r\n      // ::-webkit-scrollbar-track-piece {\r\n      //   background-color: #21C9B8 !important;\r\n      // }\r\n      .tab-content {\r\n        padding: 24px 0 24px 18px;\r\n        &-item {\r\n          @include ellipsis;\r\n          display: flex;\r\n          justify-content: center;\r\n          align-items: center;\r\n          flex-shrink: 0;\r\n          height: 40px;\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #ffffff;\r\n          line-height: 14px;\r\n          transition: background, color 0.25ms ease;\r\n          margin-bottom: 12px;\r\n          cursor: pointer;\r\n          &.active {\r\n            color: #21c9b8;\r\n            background: linear-gradient(270deg, #fbfdff 0%, #ffffff 100%);\r\n          }\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .right {\r\n      flex: 1;\r\n      padding-left: 36px;\r\n      .card {\r\n        width: 100%;\r\n        min-height: 300px;\r\n        background: #ffffff;\r\n        box-shadow: 0 10px 50px 0 rgba(38, 74, 116, 0.1);\r\n        margin-bottom: 24px;\r\n        &-img {\r\n          width: 100%;\r\n          height: 200px;\r\n          background: #ffffff;\r\n        }\r\n        &-footer {\r\n          padding: 16px 20px;\r\n          .title {\r\n            @include ellipsis;\r\n            // @include multiEllipsis(2);\r\n            font-size: 18px;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            line-height: 26px;\r\n            margin-bottom: 10px;\r\n          }\r\n          .tag {\r\n            display: inline-block;\r\n            background: rgba(197, 37, 33, 0.1);\r\n            border-radius: 4px;\r\n            padding: 6px 10px;\r\n            font-size: 12px;\r\n            font-weight: 400;\r\n            color: #21c9b8;\r\n            line-height: 12px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.company-tab-container {\r\n  .tab-page-end {\r\n    .company-tab-pagination {\r\n      width: 220px;\r\n      margin: 0 auto;\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #ffffff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        margin: 0 6px;\r\n        color: #333;\r\n      }\r\n      .el-pager {\r\n        .number {\r\n          width: 32px;\r\n          height: 32px;\r\n          border: 1px solid #d9d9d9;\r\n          background: #ffffff;\r\n          border-radius: 4px;\r\n          line-height: 32px;\r\n          margin: 0 6px;\r\n          &.active {\r\n            background: #21c9b8;\r\n            border: 1px solid #21c9b8;\r\n            color: #fff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}