package com.ruoyi.auth.controller;

import com.ruoyi.auth.service.SSOClientService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.security.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * SSO客户端控制器
 * 从系统作为SSO客户端，接收主系统的单点登录
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/sso")
public class SSOClientController {

    private static final Logger log = LoggerFactory.getLogger(SSOClientController.class);

    @Autowired
    private SSOClientService ssoClientService;

    /**
     * SSO登录入口
     * 主系统跳转到此接口进行单点登录
     * 
     * @param token SSO Token
     * @param system 系统标识
     * @param redirect 登录成功后的跳转地址
     * @param response HTTP响应
     */
    @GetMapping("/login")
    public void ssoLogin(@RequestParam("token") String token,
                        @RequestParam("system") String system,
                        @RequestParam(value = "redirect", required = false) String redirect,
                        HttpServletResponse response) throws IOException {
        
        try {
            log.info("收到SSO登录请求: token={}, system={}, redirect={}", token, system, redirect);
            
            // 验证SSO Token
            Map<String, Object> userInfo = ssoClientService.validateSSOToken(token, system);
            if (userInfo == null) {
                log.warn("SSO Token验证失败，跳转到本地登录页面");
                response.sendRedirect("/login?error=sso_token_invalid");
                return;
            }
            
            // 创建或更新本地用户
            LoginUser loginUser = ssoClientService.getOrCreateLocalUser(userInfo);
            if (loginUser == null) {
                log.error("创建本地用户失败");
                response.sendRedirect("/login?error=create_user_failed");
                return;
            }
            
            // 生成本地访问Token
            String localToken = ssoClientService.generateLocalToken(loginUser);
            
            // 设置登录状态
            ssoClientService.setLoginStatus(localToken, loginUser);
            
            // 构造跳转URL
            String redirectUrl = buildRedirectUrl(redirect, localToken);
            
            log.info("SSO登录成功，用户: {}, 跳转到: {}", loginUser.getUsername(), redirectUrl);
            response.sendRedirect(redirectUrl);
            
        } catch (Exception e) {
            log.error("SSO登录处理异常", e);
            response.sendRedirect("/login?error=sso_login_error");
        }
    }

    /**
     * SSO登出接口
     * 
     * @param request HTTP请求
     * @return 登出结果
     */
    @PostMapping("/logout")
    public AjaxResult ssoLogout(HttpServletRequest request) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (loginUser != null) {
                // 清除本地会话
                ssoClientService.clearLocalUserSession(loginUser.getToken());
                
                // 通知主系统登出
                ssoClientService.notifyMainSystemLogout(loginUser.getToken());
                
                log.info("用户 {} SSO登出成功", loginUser.getUsername());
            }
            
            return AjaxResult.success("登出成功");
        } catch (Exception e) {
            log.error("SSO登出失败", e);
            return AjaxResult.error("登出失败: " + e.getMessage());
        }
    }

    /**
     * 检查SSO状态
     * 
     * @param request HTTP请求
     * @return SSO状态
     */
    @GetMapping("/status")
    public AjaxResult checkSSOStatus(HttpServletRequest request) {
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Map<String, Object> status = new HashMap<>();
            
            if (loginUser != null) {
                status.put("isLogin", true);
                status.put("username", loginUser.getUsername());
                status.put("userId", loginUser.getUserid());
                status.put("system", "market");
                
                // 检查主系统登录状态
                boolean mainSystemStatus = ssoClientService.checkMainSystemStatus();
                status.put("mainSystemLogin", mainSystemStatus);
            } else {
                status.put("isLogin", false);
                status.put("mainSystemLogin", false);
            }
            
            return AjaxResult.success(status);
        } catch (Exception e) {
            log.error("检查SSO状态失败", e);
            return AjaxResult.error("检查SSO状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取主系统登录地址
     * 
     * @param redirect 登录成功后的跳转地址
     * @return 主系统登录地址
     */
    @GetMapping("/loginUrl")
    public AjaxResult getMainSystemLoginUrl(@RequestParam(value = "redirect", required = false) String redirect) {
        try {
            String loginUrl = ssoClientService.getMainSystemLoginUrl(redirect);
            Map<String, Object> result = new HashMap<>();
            result.put("loginUrl", loginUrl);
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取主系统登录地址失败", e);
            return AjaxResult.error("获取登录地址失败: " + e.getMessage());
        }
    }

    /**
     * 同步用户信息接口
     * 主系统调用此接口同步用户信息
     * 
     * @param userInfo 用户信息
     * @return 同步结果
     */
    @PostMapping("/sync-user")
    public AjaxResult syncUser(@RequestBody Map<String, Object> userInfo) {
        try {
            boolean result = ssoClientService.syncUserInfo(userInfo);
            if (result) {
                return AjaxResult.success("用户信息同步成功");
            } else {
                return AjaxResult.error("用户信息同步失败");
            }
        } catch (Exception e) {
            log.error("同步用户信息失败", e);
            return AjaxResult.error("同步用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 强制跳转到主系统登录
     * 
     * @param redirect 登录成功后的跳转地址
     * @param response HTTP响应
     */
    @GetMapping("/redirect-to-main")
    public void redirectToMainSystem(@RequestParam(value = "redirect", required = false) String redirect,
                                   HttpServletResponse response) throws IOException {
        try {
            String loginUrl = ssoClientService.getMainSystemLoginUrl(redirect);
            response.sendRedirect(loginUrl);
        } catch (Exception e) {
            log.error("跳转到主系统失败", e);
            response.sendRedirect("/login?error=redirect_failed");
        }
    }

    /**
     * 构造重定向URL
     * 
     * @param redirect 原始重定向地址
     * @param token 本地Token
     * @return 重定向URL
     */
    private String buildRedirectUrl(String redirect, String token) {
        if (StringUtils.isNotEmpty(redirect)) {
            // 如果有指定重定向地址，使用指定地址
            if (redirect.contains("?")) {
                return redirect + "&token=" + token;
            } else {
                return redirect + "?token=" + token;
            }
        } else {
            // 默认跳转到首页
            return "/?token=" + token;
        }
    }
}
