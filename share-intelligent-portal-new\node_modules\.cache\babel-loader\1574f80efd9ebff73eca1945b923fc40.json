{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\role\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\role\\index.vue", "mtime": 1750311963037}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJFOi9jb21wYW55L25tZC9ubWRuZXcvc2hhcmUtaW50ZWxsaWdlbnQvc2hhcmUtaW50ZWxsaWdlbnQtcG9ydGFsLW5ldy9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lc25leHQuaXRlcmF0b3IuY29uc3RydWN0b3IuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzbmV4dC5pdGVyYXRvci5mb3ItZWFjaC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXNuZXh0Lml0ZXJhdG9yLm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvd2ViLmRvbS1jb2xsZWN0aW9ucy5mb3ItZWFjaC5qcyIpOwp2YXIgX3JvbGUgPSByZXF1aXJlKCJAL2FwaS9zeXN0ZW0vcm9sZSIpOwp2YXIgX21lbnUgPSByZXF1aXJlKCJAL2FwaS9zeXN0ZW0vbWVudSIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSB7CiAgbmFtZTogIlJvbGUiLAogIGRpY3RzOiBbJ3N5c19ub3JtYWxfZGlzYWJsZSddLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g6KeS6Imy6KGo5qC85pWw5o2uCiAgICAgIHJvbGVMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCCiAgICAgIG9wZW46IGZhbHNlLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYLvvIjmlbDmja7mnYPpmZDvvIkKICAgICAgb3BlbkRhdGFTY29wZTogZmFsc2UsCiAgICAgIG1lbnVFeHBhbmQ6IGZhbHNlLAogICAgICBtZW51Tm9kZUFsbDogZmFsc2UsCiAgICAgIGRlcHRFeHBhbmQ6IHRydWUsCiAgICAgIGRlcHROb2RlQWxsOiBmYWxzZSwKICAgICAgLy8g5pel5pyf6IyD5Zu0CiAgICAgIGRhdGVSYW5nZTogW10sCiAgICAgIC8vIOaVsOaNruiMg+WbtOmAiemhuQogICAgICBkYXRhU2NvcGVPcHRpb25zOiBbewogICAgICAgIHZhbHVlOiAiMSIsCiAgICAgICAgbGFiZWw6ICLlhajpg6jmlbDmja7mnYPpmZAiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogIjIiLAogICAgICAgIGxhYmVsOiAi6Ieq5a6a5pWw5o2u5p2D6ZmQIgogICAgICB9LCB7CiAgICAgICAgdmFsdWU6ICIzIiwKICAgICAgICBsYWJlbDogIuacrOmDqOmXqOaVsOaNruadg+mZkCIKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAiNCIsCiAgICAgICAgbGFiZWw6ICLmnKzpg6jpl6jlj4rku6XkuIvmlbDmja7mnYPpmZAiCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogIjUiLAogICAgICAgIGxhYmVsOiAi5LuF5pys5Lq65pWw5o2u5p2D6ZmQIgogICAgICB9XSwKICAgICAgLy8g6I+c5Y2V5YiX6KGoCiAgICAgIG1lbnVPcHRpb25zOiBbXSwKICAgICAgLy8g6YOo6Zeo5YiX6KGoCiAgICAgIGRlcHRPcHRpb25zOiBbXSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMTAsCiAgICAgICAgcm9sZU5hbWU6IHVuZGVmaW5lZCwKICAgICAgICByb2xlS2V5OiB1bmRlZmluZWQsCiAgICAgICAgc3RhdHVzOiB1bmRlZmluZWQKICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICBkZWZhdWx0UHJvcHM6IHsKICAgICAgICBjaGlsZHJlbjogImNoaWxkcmVuIiwKICAgICAgICBsYWJlbDogImxhYmVsIgogICAgICB9LAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHsKICAgICAgICByb2xlTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogIuinkuiJsuWQjeensOS4jeiDveS4uuepuiIsCiAgICAgICAgICB0cmlnZ2VyOiAiYmx1ciIKICAgICAgICB9XSwKICAgICAgICByb2xlS2V5OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi5p2D6ZmQ5a2X56ym5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dLAogICAgICAgIHJvbGVTb3J0OiBbewogICAgICAgICAgcmVxdWlyZWQ6IHRydWUsCiAgICAgICAgICBtZXNzYWdlOiAi6KeS6Imy6aG65bqP5LiN6IO95Li656m6IiwKICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdGhpcy5nZXRMaXN0KCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvKiog5p+l6K+i6KeS6Imy5YiX6KGoICovZ2V0TGlzdDogZnVuY3Rpb24gZ2V0TGlzdCgpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgKDAsIF9yb2xlLmxpc3RSb2xlKSh0aGlzLmFkZERhdGVSYW5nZSh0aGlzLnF1ZXJ5UGFyYW1zLCB0aGlzLmRhdGVSYW5nZSkpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXMucm9sZUxpc3QgPSByZXNwb25zZS5yb3dzOwogICAgICAgIF90aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5p+l6K+i6I+c5Y2V5qCR57uT5p6EICovZ2V0TWVudVRyZWVzZWxlY3Q6IGZ1bmN0aW9uIGdldE1lbnVUcmVlc2VsZWN0KCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgKDAsIF9tZW51LnRyZWVzZWxlY3QpKCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIubWVudU9wdGlvbnMgPSByZXNwb25zZS5kYXRhOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmiYDmnInoj5zljZXoioLngrnmlbDmja4KICAgIGdldE1lbnVBbGxDaGVja2VkS2V5czogZnVuY3Rpb24gZ2V0TWVudUFsbENoZWNrZWRLZXlzKCkgewogICAgICAvLyDnm67liY3ooqvpgInkuK3nmoToj5zljZXoioLngrkKICAgICAgdmFyIGNoZWNrZWRLZXlzID0gdGhpcy4kcmVmcy5tZW51LmdldENoZWNrZWRLZXlzKCk7CiAgICAgIC8vIOWNiumAieS4reeahOiPnOWNleiKgueCuQogICAgICB2YXIgaGFsZkNoZWNrZWRLZXlzID0gdGhpcy4kcmVmcy5tZW51LmdldEhhbGZDaGVja2VkS2V5cygpOwogICAgICBjaGVja2VkS2V5cy51bnNoaWZ0LmFwcGx5KGNoZWNrZWRLZXlzLCBoYWxmQ2hlY2tlZEtleXMpOwogICAgICByZXR1cm4gY2hlY2tlZEtleXM7CiAgICB9LAogICAgLy8g5omA5pyJ6YOo6Zeo6IqC54K55pWw5o2uCiAgICBnZXREZXB0QWxsQ2hlY2tlZEtleXM6IGZ1bmN0aW9uIGdldERlcHRBbGxDaGVja2VkS2V5cygpIHsKICAgICAgLy8g55uu5YmN6KKr6YCJ5Lit55qE6YOo6Zeo6IqC54K5CiAgICAgIHZhciBjaGVja2VkS2V5cyA9IHRoaXMuJHJlZnMuZGVwdC5nZXRDaGVja2VkS2V5cygpOwogICAgICAvLyDljYrpgInkuK3nmoTpg6jpl6joioLngrkKICAgICAgdmFyIGhhbGZDaGVja2VkS2V5cyA9IHRoaXMuJHJlZnMuZGVwdC5nZXRIYWxmQ2hlY2tlZEtleXMoKTsKICAgICAgY2hlY2tlZEtleXMudW5zaGlmdC5hcHBseShjaGVja2VkS2V5cywgaGFsZkNoZWNrZWRLZXlzKTsKICAgICAgcmV0dXJuIGNoZWNrZWRLZXlzOwogICAgfSwKICAgIC8qKiDmoLnmja7op5LoibJJROafpeivouiPnOWNleagkee7k+aehCAqL2dldFJvbGVNZW51VHJlZXNlbGVjdDogZnVuY3Rpb24gZ2V0Um9sZU1lbnVUcmVlc2VsZWN0KHJvbGVJZCkgewogICAgICB2YXIgX3RoaXMzID0gdGhpczsKICAgICAgcmV0dXJuICgwLCBfbWVudS5yb2xlTWVudVRyZWVzZWxlY3QpKHJvbGVJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczMubWVudU9wdGlvbnMgPSByZXNwb25zZS5tZW51czsKICAgICAgICByZXR1cm4gcmVzcG9uc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8qKiDmoLnmja7op5LoibJJROafpeivoumDqOmXqOagkee7k+aehCAqL2dldERlcHRUcmVlOiBmdW5jdGlvbiBnZXREZXB0VHJlZShyb2xlSWQpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHJldHVybiAoMCwgX3JvbGUuZGVwdFRyZWVTZWxlY3QpKHJvbGVJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczQuZGVwdE9wdGlvbnMgPSByZXNwb25zZS5kZXB0czsKICAgICAgICByZXR1cm4gcmVzcG9uc2U7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOinkuiJsueKtuaAgeS/ruaUuQogICAgaGFuZGxlU3RhdHVzQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB2YXIgdGV4dCA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICLlkK/nlKgiIDogIuWBnOeUqCI7CiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICciIicgKyByb3cucm9sZU5hbWUgKyAnIuinkuiJsuWQl++8nycpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX3JvbGUuY2hhbmdlUm9sZVN0YXR1cykocm93LnJvbGVJZCwgcm93LnN0YXR1cyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNS4kbW9kYWwubXNnU3VjY2Vzcyh0ZXh0ICsgIuaIkOWKnyIpOwogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICIxIiA6ICIwIjsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDlj5bmtojmjInpkq7vvIjmlbDmja7mnYPpmZDvvIkKICAgIGNhbmNlbERhdGFTY29wZTogZnVuY3Rpb24gY2FuY2VsRGF0YVNjb3BlKCkgewogICAgICB0aGlzLm9wZW5EYXRhU2NvcGUgPSBmYWxzZTsKICAgICAgdGhpcy5yZXNldCgpOwogICAgfSwKICAgIC8vIOihqOWNlemHjee9rgogICAgcmVzZXQ6IGZ1bmN0aW9uIHJlc2V0KCkgewogICAgICBpZiAodGhpcy4kcmVmcy5tZW51ICE9IHVuZGVmaW5lZCkgewogICAgICAgIHRoaXMuJHJlZnMubWVudS5zZXRDaGVja2VkS2V5cyhbXSk7CiAgICAgIH0KICAgICAgdGhpcy5tZW51RXhwYW5kID0gZmFsc2UsIHRoaXMubWVudU5vZGVBbGwgPSBmYWxzZSwgdGhpcy5kZXB0RXhwYW5kID0gdHJ1ZSwgdGhpcy5kZXB0Tm9kZUFsbCA9IGZhbHNlLCB0aGlzLmZvcm0gPSB7CiAgICAgICAgcm9sZUlkOiB1bmRlZmluZWQsCiAgICAgICAgcm9sZU5hbWU6IHVuZGVmaW5lZCwKICAgICAgICByb2xlS2V5OiB1bmRlZmluZWQsCiAgICAgICAgcm9sZVNvcnQ6IDAsCiAgICAgICAgc3RhdHVzOiAiMCIsCiAgICAgICAgbWVudUlkczogW10sCiAgICAgICAgZGVwdElkczogW10sCiAgICAgICAgbWVudUNoZWNrU3RyaWN0bHk6IHRydWUsCiAgICAgICAgZGVwdENoZWNrU3RyaWN0bHk6IHRydWUsCiAgICAgICAgcmVtYXJrOiB1bmRlZmluZWQKICAgICAgfTsKICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsKICAgIH0sCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovaGFuZGxlUXVlcnk6IGZ1bmN0aW9uIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOwogICAgICB0aGlzLmdldExpc3QoKTsKICAgIH0sCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovcmVzZXRRdWVyeTogZnVuY3Rpb24gcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXTsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOwogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7CiAgICB9LAogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgcmV0dXJuIGl0ZW0ucm9sZUlkOwogICAgICB9KTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDE7CiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aDsKICAgIH0sCiAgICAvLyDmm7TlpJrmk43kvZzop6blj5EKICAgIGhhbmRsZUNvbW1hbmQ6IGZ1bmN0aW9uIGhhbmRsZUNvbW1hbmQoY29tbWFuZCwgcm93KSB7CiAgICAgIHN3aXRjaCAoY29tbWFuZCkgewogICAgICAgIGNhc2UgImhhbmRsZURhdGFTY29wZSI6CiAgICAgICAgICB0aGlzLmhhbmRsZURhdGFTY29wZShyb3cpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgY2FzZSAiaGFuZGxlQXV0aFVzZXIiOgogICAgICAgICAgdGhpcy5oYW5kbGVBdXRoVXNlcihyb3cpOwogICAgICAgICAgYnJlYWs7CiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIGJyZWFrOwogICAgICB9CiAgICB9LAogICAgLy8g5qCR5p2D6ZmQ77yI5bGV5byAL+aKmOWPoO+8iQogICAgaGFuZGxlQ2hlY2tlZFRyZWVFeHBhbmQ6IGZ1bmN0aW9uIGhhbmRsZUNoZWNrZWRUcmVlRXhwYW5kKHZhbHVlLCB0eXBlKSB7CiAgICAgIGlmICh0eXBlID09ICdtZW51JykgewogICAgICAgIHZhciB0cmVlTGlzdCA9IHRoaXMubWVudU9wdGlvbnM7CiAgICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCB0cmVlTGlzdC5sZW5ndGg7IGkrKykgewogICAgICAgICAgdGhpcy4kcmVmcy5tZW51LnN0b3JlLm5vZGVzTWFwW3RyZWVMaXN0W2ldLmlkXS5leHBhbmRlZCA9IHZhbHVlOwogICAgICAgIH0KICAgICAgfSBlbHNlIGlmICh0eXBlID09ICdkZXB0JykgewogICAgICAgIHZhciBfdHJlZUxpc3QgPSB0aGlzLmRlcHRPcHRpb25zOwogICAgICAgIGZvciAodmFyIF9pID0gMDsgX2kgPCBfdHJlZUxpc3QubGVuZ3RoOyBfaSsrKSB7CiAgICAgICAgICB0aGlzLiRyZWZzLmRlcHQuc3RvcmUubm9kZXNNYXBbX3RyZWVMaXN0W19pXS5pZF0uZXhwYW5kZWQgPSB2YWx1ZTsKICAgICAgICB9CiAgICAgIH0KICAgIH0sCiAgICAvLyDmoJHmnYPpmZDvvIjlhajpgIkv5YWo5LiN6YCJ77yJCiAgICBoYW5kbGVDaGVja2VkVHJlZU5vZGVBbGw6IGZ1bmN0aW9uIGhhbmRsZUNoZWNrZWRUcmVlTm9kZUFsbCh2YWx1ZSwgdHlwZSkgewogICAgICBpZiAodHlwZSA9PSAnbWVudScpIHsKICAgICAgICB0aGlzLiRyZWZzLm1lbnUuc2V0Q2hlY2tlZE5vZGVzKHZhbHVlID8gdGhpcy5tZW51T3B0aW9ucyA6IFtdKTsKICAgICAgfSBlbHNlIGlmICh0eXBlID09ICdkZXB0JykgewogICAgICAgIHRoaXMuJHJlZnMuZGVwdC5zZXRDaGVja2VkTm9kZXModmFsdWUgPyB0aGlzLmRlcHRPcHRpb25zIDogW10pOwogICAgICB9CiAgICB9LAogICAgLy8g5qCR5p2D6ZmQ77yI54i25a2Q6IGU5Yqo77yJCiAgICBoYW5kbGVDaGVja2VkVHJlZUNvbm5lY3Q6IGZ1bmN0aW9uIGhhbmRsZUNoZWNrZWRUcmVlQ29ubmVjdCh2YWx1ZSwgdHlwZSkgewogICAgICBpZiAodHlwZSA9PSAnbWVudScpIHsKICAgICAgICB0aGlzLmZvcm0ubWVudUNoZWNrU3RyaWN0bHkgPSB2YWx1ZSA/IHRydWUgOiBmYWxzZTsKICAgICAgfSBlbHNlIGlmICh0eXBlID09ICdkZXB0JykgewogICAgICAgIHRoaXMuZm9ybS5kZXB0Q2hlY2tTdHJpY3RseSA9IHZhbHVlID8gdHJ1ZSA6IGZhbHNlOwogICAgICB9CiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqL2hhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMuZ2V0TWVudVRyZWVzZWxlY3QoKTsKICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsKICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDop5LoibIiOwogICAgfSwKICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi9oYW5kbGVVcGRhdGU6IGZ1bmN0aW9uIGhhbmRsZVVwZGF0ZShyb3cpIHsKICAgICAgdmFyIF90aGlzNiA9IHRoaXM7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgICAgdmFyIHJvbGVJZCA9IHJvdy5yb2xlSWQgfHwgdGhpcy5pZHM7CiAgICAgIHZhciByb2xlTWVudSA9IHRoaXMuZ2V0Um9sZU1lbnVUcmVlc2VsZWN0KHJvbGVJZCk7CiAgICAgICgwLCBfcm9sZS5nZXRSb2xlKShyb2xlSWQpLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgX3RoaXM2LmZvcm0gPSByZXNwb25zZS5kYXRhOwogICAgICAgIF90aGlzNi5vcGVuID0gdHJ1ZTsKICAgICAgICBfdGhpczYuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgIHJvbGVNZW51LnRoZW4oZnVuY3Rpb24gKHJlcykgewogICAgICAgICAgICB2YXIgY2hlY2tlZEtleXMgPSByZXMuY2hlY2tlZEtleXM7CiAgICAgICAgICAgIGNoZWNrZWRLZXlzLmZvckVhY2goZnVuY3Rpb24gKHYpIHsKICAgICAgICAgICAgICBfdGhpczYuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICAgICAgICAgIF90aGlzNi4kcmVmcy5tZW51LnNldENoZWNrZWQodiwgdHJ1ZSwgZmFsc2UpOwogICAgICAgICAgICAgIH0pOwogICAgICAgICAgICB9KTsKICAgICAgICAgIH0pOwogICAgICAgIH0pOwogICAgICAgIF90aGlzNi50aXRsZSA9ICLkv67mlLnop5LoibIiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog6YCJ5oup6KeS6Imy5p2D6ZmQ6IyD5Zu06Kem5Y+RICovZGF0YVNjb3BlU2VsZWN0Q2hhbmdlOiBmdW5jdGlvbiBkYXRhU2NvcGVTZWxlY3RDaGFuZ2UodmFsdWUpIHsKICAgICAgaWYgKHZhbHVlICE9PSAnMicpIHsKICAgICAgICB0aGlzLiRyZWZzLmRlcHQuc2V0Q2hlY2tlZEtleXMoW10pOwogICAgICB9CiAgICB9LAogICAgLyoqIOWIhumFjeaVsOaNruadg+mZkOaTjeS9nCAqL2hhbmRsZURhdGFTY29wZTogZnVuY3Rpb24gaGFuZGxlRGF0YVNjb3BlKHJvdykgewogICAgICB2YXIgX3RoaXM3ID0gdGhpczsKICAgICAgdGhpcy5yZXNldCgpOwogICAgICB2YXIgZGVwdFRyZWVTZWxlY3QgPSB0aGlzLmdldERlcHRUcmVlKHJvdy5yb2xlSWQpOwogICAgICAoMCwgX3JvbGUuZ2V0Um9sZSkocm93LnJvbGVJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczcuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXM3Lm9wZW5EYXRhU2NvcGUgPSB0cnVlOwogICAgICAgIF90aGlzNy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgICAgZGVwdFRyZWVTZWxlY3QudGhlbihmdW5jdGlvbiAocmVzKSB7CiAgICAgICAgICAgIF90aGlzNy4kcmVmcy5kZXB0LnNldENoZWNrZWRLZXlzKHJlcy5jaGVja2VkS2V5cyk7CiAgICAgICAgICB9KTsKICAgICAgICB9KTsKICAgICAgICBfdGhpczcudGl0bGUgPSAi5YiG6YWN5pWw5o2u5p2D6ZmQIjsKICAgICAgfSk7CiAgICB9LAogICAgLyoqIOWIhumFjeeUqOaIt+aTjeS9nCAqLwogICAgaGFuZGxlQXV0aFVzZXI6IGZ1bmN0aW9uIGhhbmRsZUF1dGhVc2VyKHJvdykgewogICAgICB2YXIgcm9sZUlkID0gcm93LnJvbGVJZDsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi9zeXN0ZW0vcm9sZS1hdXRoL3VzZXIvIiArIHJvbGVJZCk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzOCA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmIChfdGhpczguZm9ybS5yb2xlSWQgIT0gdW5kZWZpbmVkKSB7CiAgICAgICAgICAgIF90aGlzOC5mb3JtLm1lbnVJZHMgPSBfdGhpczguZ2V0TWVudUFsbENoZWNrZWRLZXlzKCk7CiAgICAgICAgICAgICgwLCBfcm9sZS51cGRhdGVSb2xlKShfdGhpczguZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczguJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzOC5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXM4LmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBfdGhpczguZm9ybS5tZW51SWRzID0gX3RoaXM4LmdldE1lbnVBbGxDaGVja2VkS2V5cygpOwogICAgICAgICAgICAoMCwgX3JvbGUuYWRkUm9sZSkoX3RoaXM4LmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXM4LiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczgub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzOC5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSk7CiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSru+8iOaVsOaNruadg+mZkO+8iSAqLwogICAgc3VibWl0RGF0YVNjb3BlOiBmdW5jdGlvbiBzdWJtaXREYXRhU2NvcGUoKSB7CiAgICAgIHZhciBfdGhpczkgPSB0aGlzOwogICAgICBpZiAodGhpcy5mb3JtLnJvbGVJZCAhPSB1bmRlZmluZWQpIHsKICAgICAgICB0aGlzLmZvcm0uZGVwdElkcyA9IHRoaXMuZ2V0RGVwdEFsbENoZWNrZWRLZXlzKCk7CiAgICAgICAgKDAsIF9yb2xlLmRhdGFTY29wZSkodGhpcy5mb3JtKS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICAgICAgX3RoaXM5LiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgIF90aGlzOS5vcGVuRGF0YVNjb3BlID0gZmFsc2U7CiAgICAgICAgICBfdGhpczkuZ2V0TGlzdCgpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqL2hhbmRsZURlbGV0ZTogZnVuY3Rpb24gaGFuZGxlRGVsZXRlKHJvdykgewogICAgICB2YXIgX3RoaXMwID0gdGhpczsKICAgICAgdmFyIHJvbGVJZHMgPSByb3cucm9sZUlkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTop5LoibLnvJblj7fkuLoiJyArIHJvbGVJZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX3JvbGUuZGVsUm9sZSkocm9sZUlkcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzMC5nZXRMaXN0KCk7CiAgICAgICAgX3RoaXMwLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi9oYW5kbGVFeHBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnc3lzdGVtL3JvbGUvZXhwb3J0JywgKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKHt9LCB0aGlzLnF1ZXJ5UGFyYW1zKSwgInJvbGVfIi5jb25jYXQobmV3IERhdGUoKS5nZXRUaW1lKCksICIueGxzeCIpKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["_role", "require", "_menu", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "roleList", "title", "open", "openDataScope", "menuExpand", "menuNodeAll", "deptExpand", "deptNodeAll", "date<PERSON><PERSON><PERSON>", "dataScopeOptions", "value", "label", "menuOptions", "deptOptions", "queryParams", "pageNum", "pageSize", "<PERSON><PERSON><PERSON>", "undefined", "<PERSON><PERSON><PERSON>", "status", "form", "defaultProps", "children", "rules", "required", "message", "trigger", "roleSort", "created", "getList", "methods", "_this", "listRole", "addDateRange", "then", "response", "rows", "getMenuTreeselect", "_this2", "menuTreeselect", "getMenuAllCheckedKeys", "checked<PERSON>eys", "$refs", "menu", "getChe<PERSON><PERSON>eys", "halfC<PERSON>cked<PERSON>eys", "getHalfCheckedKeys", "unshift", "apply", "getDeptAllCheckedKeys", "dept", "getRoleMenuTreeselect", "roleId", "_this3", "roleMenuTreeselect", "menus", "getDeptTree", "_this4", "deptTreeSelect", "depts", "handleStatusChange", "row", "_this5", "text", "$modal", "confirm", "changeRoleStatus", "msgSuccess", "catch", "cancel", "reset", "cancelDataScope", "set<PERSON><PERSON><PERSON><PERSON>eys", "menuIds", "deptIds", "menu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleCommand", "command", "handleDataScope", "handleAuthUser", "handleCheckedTreeExpand", "type", "treeList", "i", "store", "nodesMap", "id", "expanded", "handleCheckedTreeNodeAll", "setCheckedNodes", "handleCheckedTreeConnect", "handleAdd", "handleUpdate", "_this6", "roleMenu", "getRole", "$nextTick", "res", "for<PERSON>ach", "v", "setChecked", "dataScopeSelectChange", "_this7", "$router", "push", "submitForm", "_this8", "validate", "valid", "updateRole", "addRole", "submitDataScope", "_this9", "dataScope", "handleDelete", "_this0", "roleIds", "delRole", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime"], "sources": ["src/views/system/role/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\">\r\n      <el-form-item label=\"角色名称\" prop=\"roleName\">\r\n        <el-input\r\n          v-model=\"queryParams.roleName\"\r\n          placeholder=\"请输入角色名称\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"权限字符\" prop=\"roleKey\">\r\n        <el-input\r\n          v-model=\"queryParams.roleKey\"\r\n          placeholder=\"请输入权限字符\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          placeholder=\"角色状态\"\r\n          clearable\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['system:role:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['system:role:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['system:role:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['system:role:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"roleList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"角色编号\" prop=\"roleId\" width=\"120\" />\r\n      <el-table-column label=\"角色名称\" prop=\"roleName\" :show-overflow-tooltip=\"true\" width=\"150\" />\r\n      <el-table-column label=\"权限字符\" prop=\"roleKey\" :show-overflow-tooltip=\"true\" width=\"150\" />\r\n      <el-table-column label=\"显示顺序\" prop=\"roleSort\" width=\"100\" />\r\n      <el-table-column label=\"状态\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.status\"\r\n            active-value=\"0\"\r\n            inactive-value=\"1\"\r\n            @change=\"handleStatusChange(scope.row)\"\r\n          ></el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"创建时间\" align=\"center\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\" v-if=\"scope.row.roleId !== 1\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['system:role:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['system:role:remove']\"\r\n          >删除</el-button>\r\n          <el-dropdown size=\"mini\" @command=\"(command) => handleCommand(command, scope.row)\" v-hasPermi=\"['system:role:edit']\">\r\n            <el-button size=\"mini\" type=\"text\" icon=\"el-icon-d-arrow-right\">更多</el-button>\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <el-dropdown-item command=\"handleDataScope\" icon=\"el-icon-circle-check\"\r\n                v-hasPermi=\"['system:role:edit']\">数据权限</el-dropdown-item>\r\n              <el-dropdown-item command=\"handleAuthUser\" icon=\"el-icon-user\"\r\n                v-hasPermi=\"['system:role:edit']\">分配用户</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改角色配置对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-form-item label=\"角色名称\" prop=\"roleName\">\r\n          <el-input v-model=\"form.roleName\" placeholder=\"请输入角色名称\" />\r\n        </el-form-item>\r\n        <el-form-item prop=\"roleKey\">\r\n          <span slot=\"label\">\r\n            <el-tooltip content=\"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)\" placement=\"top\">\r\n              <i class=\"el-icon-question\"></i>\r\n            </el-tooltip>\r\n            权限字符\r\n          </span>\r\n          <el-input v-model=\"form.roleKey\" placeholder=\"请输入权限字符\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"角色顺序\" prop=\"roleSort\">\r\n          <el-input-number v-model=\"form.roleSort\" controls-position=\"right\" :min=\"0\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\">\r\n          <el-radio-group v-model=\"form.status\">\r\n            <el-radio\r\n              v-for=\"dict in dict.type.sys_normal_disable\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.value\"\r\n            >{{dict.label}}</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"菜单权限\">\r\n          <el-checkbox v-model=\"menuExpand\" @change=\"handleCheckedTreeExpand($event, 'menu')\">展开/折叠</el-checkbox>\r\n          <el-checkbox v-model=\"menuNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'menu')\">全选/全不选</el-checkbox>\r\n          <el-checkbox v-model=\"form.menuCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'menu')\">父子联动</el-checkbox>\r\n          <el-tree\r\n            class=\"tree-border\"\r\n            :data=\"menuOptions\"\r\n            show-checkbox\r\n            ref=\"menu\"\r\n            node-key=\"id\"\r\n            :check-strictly=\"!form.menuCheckStrictly\"\r\n            empty-text=\"加载中，请稍候\"\r\n            :props=\"defaultProps\"\r\n          ></el-tree>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\">\r\n          <el-input v-model=\"form.remark\" type=\"textarea\" placeholder=\"请输入内容\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 分配角色数据权限对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"openDataScope\" width=\"500px\" append-to-body>\r\n      <el-form :model=\"form\" label-width=\"80px\">\r\n        <el-form-item label=\"角色名称\">\r\n          <el-input v-model=\"form.roleName\" :disabled=\"true\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"权限字符\">\r\n          <el-input v-model=\"form.roleKey\" :disabled=\"true\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"权限范围\">\r\n          <el-select v-model=\"form.dataScope\" @change=\"dataScopeSelectChange\">\r\n            <el-option\r\n              v-for=\"item in dataScopeOptions\"\r\n              :key=\"item.value\"\r\n              :label=\"item.label\"\r\n              :value=\"item.value\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"数据权限\" v-show=\"form.dataScope == 2\">\r\n          <el-checkbox v-model=\"deptExpand\" @change=\"handleCheckedTreeExpand($event, 'dept')\">展开/折叠</el-checkbox>\r\n          <el-checkbox v-model=\"deptNodeAll\" @change=\"handleCheckedTreeNodeAll($event, 'dept')\">全选/全不选</el-checkbox>\r\n          <el-checkbox v-model=\"form.deptCheckStrictly\" @change=\"handleCheckedTreeConnect($event, 'dept')\">父子联动</el-checkbox>\r\n          <el-tree\r\n            class=\"tree-border\"\r\n            :data=\"deptOptions\"\r\n            show-checkbox\r\n            default-expand-all\r\n            ref=\"dept\"\r\n            node-key=\"id\"\r\n            :check-strictly=\"!form.deptCheckStrictly\"\r\n            empty-text=\"加载中，请稍候\"\r\n            :props=\"defaultProps\"\r\n          ></el-tree>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitDataScope\">确 定</el-button>\r\n        <el-button @click=\"cancelDataScope\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listRole, getRole, delRole, addRole, updateRole, dataScope, changeRoleStatus, deptTreeSelect } from \"@/api/system/role\";\r\nimport { treeselect as menuTreeselect, roleMenuTreeselect } from \"@/api/system/menu\";\r\n\r\nexport default {\r\n  name: \"Role\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 角色表格数据\r\n      roleList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否显示弹出层（数据权限）\r\n      openDataScope: false,\r\n      menuExpand: false,\r\n      menuNodeAll: false,\r\n      deptExpand: true,\r\n      deptNodeAll: false,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 数据范围选项\r\n      dataScopeOptions: [\r\n        {\r\n          value: \"1\",\r\n          label: \"全部数据权限\"\r\n        },\r\n        {\r\n          value: \"2\",\r\n          label: \"自定数据权限\"\r\n        },\r\n        {\r\n          value: \"3\",\r\n          label: \"本部门数据权限\"\r\n        },\r\n        {\r\n          value: \"4\",\r\n          label: \"本部门及以下数据权限\"\r\n        },\r\n        {\r\n          value: \"5\",\r\n          label: \"仅本人数据权限\"\r\n        }\r\n      ],\r\n      // 菜单列表\r\n      menuOptions: [],\r\n      // 部门列表\r\n      deptOptions: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        roleName: undefined,\r\n        roleKey: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\"\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        roleName: [\r\n          { required: true, message: \"角色名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        roleKey: [\r\n          { required: true, message: \"权限字符不能为空\", trigger: \"blur\" }\r\n        ],\r\n        roleSort: [\r\n          { required: true, message: \"角色顺序不能为空\", trigger: \"blur\" }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询角色列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listRole(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n          this.roleList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    /** 查询菜单树结构 */\r\n    getMenuTreeselect() {\r\n      menuTreeselect().then(response => {\r\n        this.menuOptions = response.data;\r\n      });\r\n    },\r\n    // 所有菜单节点数据\r\n    getMenuAllCheckedKeys() {\r\n      // 目前被选中的菜单节点\r\n      let checkedKeys = this.$refs.menu.getCheckedKeys();\r\n      // 半选中的菜单节点\r\n      let halfCheckedKeys = this.$refs.menu.getHalfCheckedKeys();\r\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\r\n      return checkedKeys;\r\n    },\r\n    // 所有部门节点数据\r\n    getDeptAllCheckedKeys() {\r\n      // 目前被选中的部门节点\r\n      let checkedKeys = this.$refs.dept.getCheckedKeys();\r\n      // 半选中的部门节点\r\n      let halfCheckedKeys = this.$refs.dept.getHalfCheckedKeys();\r\n      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);\r\n      return checkedKeys;\r\n    },\r\n    /** 根据角色ID查询菜单树结构 */\r\n    getRoleMenuTreeselect(roleId) {\r\n      return roleMenuTreeselect(roleId).then(response => {\r\n        this.menuOptions = response.menus;\r\n        return response;\r\n      });\r\n    },\r\n    /** 根据角色ID查询部门树结构 */\r\n    getDeptTree(roleId) {\r\n      return deptTreeSelect(roleId).then(response => {\r\n        this.deptOptions = response.depts;\r\n        return response;\r\n      });\r\n    },\r\n    // 角色状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.roleName + '\"角色吗？').then(function() {\r\n        return changeRoleStatus(row.roleId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function() {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 取消按钮（数据权限）\r\n    cancelDataScope() {\r\n      this.openDataScope = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      if (this.$refs.menu != undefined) {\r\n        this.$refs.menu.setCheckedKeys([]);\r\n      }\r\n      this.menuExpand = false,\r\n      this.menuNodeAll = false,\r\n      this.deptExpand = true,\r\n      this.deptNodeAll = false,\r\n      this.form = {\r\n        roleId: undefined,\r\n        roleName: undefined,\r\n        roleKey: undefined,\r\n        roleSort: 0,\r\n        status: \"0\",\r\n        menuIds: [],\r\n        deptIds: [],\r\n        menuCheckStrictly: true,\r\n        deptCheckStrictly: true,\r\n        remark: undefined\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.roleId)\r\n      this.single = selection.length!=1\r\n      this.multiple = !selection.length\r\n    },\r\n    // 更多操作触发\r\n    handleCommand(command, row) {\r\n      switch (command) {\r\n        case \"handleDataScope\":\r\n          this.handleDataScope(row);\r\n          break;\r\n        case \"handleAuthUser\":\r\n          this.handleAuthUser(row);\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    },\r\n    // 树权限（展开/折叠）\r\n    handleCheckedTreeExpand(value, type) {\r\n      if (type == 'menu') {\r\n        let treeList = this.menuOptions;\r\n        for (let i = 0; i < treeList.length; i++) {\r\n          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value;\r\n        }\r\n      } else if (type == 'dept') {\r\n        let treeList = this.deptOptions;\r\n        for (let i = 0; i < treeList.length; i++) {\r\n          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value;\r\n        }\r\n      }\r\n    },\r\n    // 树权限（全选/全不选）\r\n    handleCheckedTreeNodeAll(value, type) {\r\n      if (type == 'menu') {\r\n        this.$refs.menu.setCheckedNodes(value ? this.menuOptions: []);\r\n      } else if (type == 'dept') {\r\n        this.$refs.dept.setCheckedNodes(value ? this.deptOptions: []);\r\n      }\r\n    },\r\n    // 树权限（父子联动）\r\n    handleCheckedTreeConnect(value, type) {\r\n      if (type == 'menu') {\r\n        this.form.menuCheckStrictly = value ? true: false;\r\n      } else if (type == 'dept') {\r\n        this.form.deptCheckStrictly = value ? true: false;\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.getMenuTreeselect();\r\n      this.open = true;\r\n      this.title = \"添加角色\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const roleId = row.roleId || this.ids\r\n      const roleMenu = this.getRoleMenuTreeselect(roleId);\r\n      getRole(roleId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.$nextTick(() => {\r\n          roleMenu.then(res => {\r\n            let checkedKeys = res.checkedKeys\r\n            checkedKeys.forEach((v) => {\r\n                this.$nextTick(()=>{\r\n                    this.$refs.menu.setChecked(v, true ,false);\r\n                })\r\n            })\r\n          });\r\n        });\r\n        this.title = \"修改角色\";\r\n      });\r\n    },\r\n    /** 选择角色权限范围触发 */\r\n    dataScopeSelectChange(value) {\r\n      if(value !== '2') {\r\n        this.$refs.dept.setCheckedKeys([]);\r\n      }\r\n    },\r\n    /** 分配数据权限操作 */\r\n    handleDataScope(row) {\r\n      this.reset();\r\n      const deptTreeSelect = this.getDeptTree(row.roleId);\r\n      getRole(row.roleId).then(response => {\r\n        this.form = response.data;\r\n        this.openDataScope = true;\r\n        this.$nextTick(() => {\r\n          deptTreeSelect.then(res => {\r\n            this.$refs.dept.setCheckedKeys(res.checkedKeys);\r\n          });\r\n        });\r\n        this.title = \"分配数据权限\";\r\n      });\r\n    },\r\n    /** 分配用户操作 */\r\n    handleAuthUser: function(row) {\r\n      const roleId = row.roleId;\r\n      this.$router.push(\"/system/role-auth/user/\" + roleId);\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.roleId != undefined) {\r\n            this.form.menuIds = this.getMenuAllCheckedKeys();\r\n            updateRole(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            this.form.menuIds = this.getMenuAllCheckedKeys();\r\n            addRole(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 提交按钮（数据权限） */\r\n    submitDataScope: function() {\r\n      if (this.form.roleId != undefined) {\r\n        this.form.deptIds = this.getDeptAllCheckedKeys();\r\n        dataScope(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\");\r\n          this.openDataScope = false;\r\n          this.getList();\r\n        });\r\n      }\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const roleIds = row.roleId || this.ids;\r\n      this.$modal.confirm('是否确认删除角色编号为\"' + roleIds + '\"的数据项？').then(function() {\r\n        return delRole(roleIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {});\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/role/export', {\r\n        ...this.queryParams\r\n      }, `role_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>"], "mappings": ";;;;;;;;;;;;;;;AA8PA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,UAAA;MACAC,WAAA;MACA;MACAC,SAAA;MACA;MACAC,gBAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,IAAA;MACAC,YAAA;QACAC,QAAA;QACAZ,KAAA;MACA;MACA;MACAa,KAAA;QACAP,QAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,OAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAtC,OAAA;MACA,IAAAuC,cAAA,OAAAC,YAAA,MAAApB,WAAA,OAAAN,SAAA,GAAA2B,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAhC,QAAA,GAAAoC,QAAA,CAAAC,IAAA;QACAL,KAAA,CAAAjC,KAAA,GAAAqC,QAAA,CAAArC,KAAA;QACAiC,KAAA,CAAAtC,OAAA;MACA,CACA;IACA;IACA,cACA4C,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,gBAAA,IAAAL,IAAA,WAAAC,QAAA;QACAG,MAAA,CAAA3B,WAAA,GAAAwB,QAAA,CAAA3C,IAAA;MACA;IACA;IACA;IACAgD,qBAAA,WAAAA,sBAAA;MACA;MACA,IAAAC,WAAA,QAAAC,KAAA,CAAAC,IAAA,CAAAC,cAAA;MACA;MACA,IAAAC,eAAA,QAAAH,KAAA,CAAAC,IAAA,CAAAG,kBAAA;MACAL,WAAA,CAAAM,OAAA,CAAAC,KAAA,CAAAP,WAAA,EAAAI,eAAA;MACA,OAAAJ,WAAA;IACA;IACA;IACAQ,qBAAA,WAAAA,sBAAA;MACA;MACA,IAAAR,WAAA,QAAAC,KAAA,CAAAQ,IAAA,CAAAN,cAAA;MACA;MACA,IAAAC,eAAA,QAAAH,KAAA,CAAAQ,IAAA,CAAAJ,kBAAA;MACAL,WAAA,CAAAM,OAAA,CAAAC,KAAA,CAAAP,WAAA,EAAAI,eAAA;MACA,OAAAJ,WAAA;IACA;IACA,oBACAU,qBAAA,WAAAA,sBAAAC,MAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,wBAAA,EAAAF,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAkB,MAAA,CAAA1C,WAAA,GAAAwB,QAAA,CAAAoB,KAAA;QACA,OAAApB,QAAA;MACA;IACA;IACA,oBACAqB,WAAA,WAAAA,YAAAJ,MAAA;MAAA,IAAAK,MAAA;MACA,WAAAC,oBAAA,EAAAN,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAsB,MAAA,CAAA7C,WAAA,GAAAuB,QAAA,CAAAwB,KAAA;QACA,OAAAxB,QAAA;MACA;IACA;IACA;IACAyB,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAA1C,MAAA;MACA,KAAA6C,MAAA,CAAAC,OAAA,UAAAF,IAAA,UAAAF,GAAA,CAAA7C,QAAA,YAAAkB,IAAA;QACA,WAAAgC,sBAAA,EAAAL,GAAA,CAAAT,MAAA,EAAAS,GAAA,CAAA1C,MAAA;MACA,GAAAe,IAAA;QACA4B,MAAA,CAAAE,MAAA,CAAAG,UAAA,CAAAJ,IAAA;MACA,GAAAK,KAAA;QACAP,GAAA,CAAA1C,MAAA,GAAA0C,GAAA,CAAA1C,MAAA;MACA;IACA;IACA;IACAkD,MAAA,WAAAA,OAAA;MACA,KAAApE,IAAA;MACA,KAAAqE,KAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAArE,aAAA;MACA,KAAAoE,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,SAAA5B,KAAA,CAAAC,IAAA,IAAA1B,SAAA;QACA,KAAAyB,KAAA,CAAAC,IAAA,CAAA6B,cAAA;MACA;MACA,KAAArE,UAAA,UACA,KAAAC,WAAA,UACA,KAAAC,UAAA,SACA,KAAAC,WAAA,UACA,KAAAc,IAAA;QACAgC,MAAA,EAAAnC,SAAA;QACAD,QAAA,EAAAC,SAAA;QACAC,OAAA,EAAAD,SAAA;QACAU,QAAA;QACAR,MAAA;QACAsD,OAAA;QACAC,OAAA;QACAC,iBAAA;QACAC,iBAAA;QACAC,MAAA,EAAA5D;MACA;MACA,KAAA6D,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlE,WAAA,CAAAC,OAAA;MACA,KAAAe,OAAA;IACA;IACA,aACAmD,UAAA,WAAAA,WAAA;MACA,KAAAzE,SAAA;MACA,KAAAuE,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAxF,GAAA,GAAAwF,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhC,MAAA;MAAA;MACA,KAAAzD,MAAA,GAAAuF,SAAA,CAAAG,MAAA;MACA,KAAAzF,QAAA,IAAAsF,SAAA,CAAAG,MAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAAC,OAAA,EAAA1B,GAAA;MACA,QAAA0B,OAAA;QACA;UACA,KAAAC,eAAA,CAAA3B,GAAA;UACA;QACA;UACA,KAAA4B,cAAA,CAAA5B,GAAA;UACA;QACA;UACA;MACA;IACA;IACA;IACA6B,uBAAA,WAAAA,wBAAAjF,KAAA,EAAAkF,IAAA;MACA,IAAAA,IAAA;QACA,IAAAC,QAAA,QAAAjF,WAAA;QACA,SAAAkF,CAAA,MAAAA,CAAA,GAAAD,QAAA,CAAAP,MAAA,EAAAQ,CAAA;UACA,KAAAnD,KAAA,CAAAC,IAAA,CAAAmD,KAAA,CAAAC,QAAA,CAAAH,QAAA,CAAAC,CAAA,EAAAG,EAAA,EAAAC,QAAA,GAAAxF,KAAA;QACA;MACA,WAAAkF,IAAA;QACA,IAAAC,SAAA,QAAAhF,WAAA;QACA,SAAAiF,EAAA,MAAAA,EAAA,GAAAD,SAAA,CAAAP,MAAA,EAAAQ,EAAA;UACA,KAAAnD,KAAA,CAAAQ,IAAA,CAAA4C,KAAA,CAAAC,QAAA,CAAAH,SAAA,CAAAC,EAAA,EAAAG,EAAA,EAAAC,QAAA,GAAAxF,KAAA;QACA;MACA;IACA;IACA;IACAyF,wBAAA,WAAAA,yBAAAzF,KAAA,EAAAkF,IAAA;MACA,IAAAA,IAAA;QACA,KAAAjD,KAAA,CAAAC,IAAA,CAAAwD,eAAA,CAAA1F,KAAA,QAAAE,WAAA;MACA,WAAAgF,IAAA;QACA,KAAAjD,KAAA,CAAAQ,IAAA,CAAAiD,eAAA,CAAA1F,KAAA,QAAAG,WAAA;MACA;IACA;IACA;IACAwF,wBAAA,WAAAA,yBAAA3F,KAAA,EAAAkF,IAAA;MACA,IAAAA,IAAA;QACA,KAAAvE,IAAA,CAAAuD,iBAAA,GAAAlE,KAAA;MACA,WAAAkF,IAAA;QACA,KAAAvE,IAAA,CAAAwD,iBAAA,GAAAnE,KAAA;MACA;IACA;IACA,aACA4F,SAAA,WAAAA,UAAA;MACA,KAAA/B,KAAA;MACA,KAAAjC,iBAAA;MACA,KAAApC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAsG,YAAA,WAAAA,aAAAzC,GAAA;MAAA,IAAA0C,MAAA;MACA,KAAAjC,KAAA;MACA,IAAAlB,MAAA,GAAAS,GAAA,CAAAT,MAAA,SAAA1D,GAAA;MACA,IAAA8G,QAAA,QAAArD,qBAAA,CAAAC,MAAA;MACA,IAAAqD,aAAA,EAAArD,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACAoE,MAAA,CAAAnF,IAAA,GAAAe,QAAA,CAAA3C,IAAA;QACA+G,MAAA,CAAAtG,IAAA;QACAsG,MAAA,CAAAG,SAAA;UACAF,QAAA,CAAAtE,IAAA,WAAAyE,GAAA;YACA,IAAAlE,WAAA,GAAAkE,GAAA,CAAAlE,WAAA;YACAA,WAAA,CAAAmE,OAAA,WAAAC,CAAA;cACAN,MAAA,CAAAG,SAAA;gBACAH,MAAA,CAAA7D,KAAA,CAAAC,IAAA,CAAAmE,UAAA,CAAAD,CAAA;cACA;YACA;UACA;QACA;QACAN,MAAA,CAAAvG,KAAA;MACA;IACA;IACA,iBACA+G,qBAAA,WAAAA,sBAAAtG,KAAA;MACA,IAAAA,KAAA;QACA,KAAAiC,KAAA,CAAAQ,IAAA,CAAAsB,cAAA;MACA;IACA;IACA,eACAgB,eAAA,WAAAA,gBAAA3B,GAAA;MAAA,IAAAmD,MAAA;MACA,KAAA1C,KAAA;MACA,IAAAZ,cAAA,QAAAF,WAAA,CAAAK,GAAA,CAAAT,MAAA;MACA,IAAAqD,aAAA,EAAA5C,GAAA,CAAAT,MAAA,EAAAlB,IAAA,WAAAC,QAAA;QACA6E,MAAA,CAAA5F,IAAA,GAAAe,QAAA,CAAA3C,IAAA;QACAwH,MAAA,CAAA9G,aAAA;QACA8G,MAAA,CAAAN,SAAA;UACAhD,cAAA,CAAAxB,IAAA,WAAAyE,GAAA;YACAK,MAAA,CAAAtE,KAAA,CAAAQ,IAAA,CAAAsB,cAAA,CAAAmC,GAAA,CAAAlE,WAAA;UACA;QACA;QACAuE,MAAA,CAAAhH,KAAA;MACA;IACA;IACA;IACAyF,cAAA,WAAAA,eAAA5B,GAAA;MACA,IAAAT,MAAA,GAAAS,GAAA,CAAAT,MAAA;MACA,KAAA6D,OAAA,CAAAC,IAAA,6BAAA9D,MAAA;IACA;IACA;IACA+D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA1E,KAAA,SAAA2E,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAAhG,IAAA,CAAAgC,MAAA,IAAAnC,SAAA;YACAmG,MAAA,CAAAhG,IAAA,CAAAqD,OAAA,GAAA2C,MAAA,CAAA5E,qBAAA;YACA,IAAA+E,gBAAA,EAAAH,MAAA,CAAAhG,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAiF,MAAA,CAAApD,MAAA,CAAAG,UAAA;cACAiD,MAAA,CAAAnH,IAAA;cACAmH,MAAA,CAAAvF,OAAA;YACA;UACA;YACAuF,MAAA,CAAAhG,IAAA,CAAAqD,OAAA,GAAA2C,MAAA,CAAA5E,qBAAA;YACA,IAAAgF,aAAA,EAAAJ,MAAA,CAAAhG,IAAA,EAAAc,IAAA,WAAAC,QAAA;cACAiF,MAAA,CAAApD,MAAA,CAAAG,UAAA;cACAiD,MAAA,CAAAnH,IAAA;cACAmH,MAAA,CAAAvF,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACA4F,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,SAAAtG,IAAA,CAAAgC,MAAA,IAAAnC,SAAA;QACA,KAAAG,IAAA,CAAAsD,OAAA,QAAAzB,qBAAA;QACA,IAAA0E,eAAA,OAAAvG,IAAA,EAAAc,IAAA,WAAAC,QAAA;UACAuF,MAAA,CAAA1D,MAAA,CAAAG,UAAA;UACAuD,MAAA,CAAAxH,aAAA;UACAwH,MAAA,CAAA7F,OAAA;QACA;MACA;IACA;IACA,aACA+F,YAAA,WAAAA,aAAA/D,GAAA;MAAA,IAAAgE,MAAA;MACA,IAAAC,OAAA,GAAAjE,GAAA,CAAAT,MAAA,SAAA1D,GAAA;MACA,KAAAsE,MAAA,CAAAC,OAAA,kBAAA6D,OAAA,aAAA5F,IAAA;QACA,WAAA6F,aAAA,EAAAD,OAAA;MACA,GAAA5F,IAAA;QACA2F,MAAA,CAAAhG,OAAA;QACAgG,MAAA,CAAA7D,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA;IACA;IACA,aACA4D,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,2BAAAC,cAAA,CAAAC,OAAA,MACA,KAAAtH,WAAA,WAAAuH,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}