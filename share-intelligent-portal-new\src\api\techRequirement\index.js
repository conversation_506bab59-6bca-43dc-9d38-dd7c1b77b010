import request from '@/utils/request'

// 查询技术需求列表
export function listSysTechRequirement(query) {
  return request({
    url: '/system/SysTechRequirement/list',
    method: 'get',
    params: query
  })
}

// 查询技术需求详细
export function getSysTechRequirement(requirementId) {
  return request({
    url: '/system/SysTechRequirement/' + requirementId,
    method: 'get'
  })
}

// 新增技术需求
export function addSysTechRequirement(data) {
  return request({
    url: '/system/SysTechRequirement',
    method: 'post',
    data: data
  })
}

// 修改技术需求
export function updateSysTechRequirement(data) {
  return request({
    url: '/system/SysTechRequirement',
    method: 'put',
    data: data
  })
}

// 删除技术需求
export function delSysTechRequirement(requirementId) {
  return request({
    url: '/system/SysTechRequirement/' + requirementId,
    method: 'delete'
  })
}

// 新增供方信息
export function addSysSupplierInfo(data) {
  return request({
    url: '/system/SysSupplierInfo',
    method: 'post',
    data: data
  })
}
