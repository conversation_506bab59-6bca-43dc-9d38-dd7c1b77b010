<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-input v-model="queryParams.gender" placeholder="请输入性别" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="照片" prop="photo">
        <el-input v-model="queryParams.photo" placeholder="请输入照片" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="最高学历" prop="education">
        <el-input v-model="queryParams.education" placeholder="请输入最高学历" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="职称" prop="jobTitle">
        <el-input v-model="queryParams.jobTitle" placeholder="请输入职称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="联系方式" prop="contactPhone">
        <el-input v-model="queryParams.contactPhone" placeholder="请输入联系方式" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:info:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:info:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:info:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['system:info:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="infoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="id" />
      <el-table-column label="姓名" align="center" prop="name" />
      <el-table-column label="性别" align="center" prop="gender" />
      <!-- <el-table-column label="照片" align="center" prop="photo" /> -->
      <el-table-column label="岗位分类" align="center">
        <template slot-scope="scope">
          {{
            positionTypeList.filter(
              (item) => item.dictValue === scope.row.positionType
            )[0].dictLabel
          }}
        </template>
      </el-table-column>
      <el-table-column label="最高学历" align="center">
        <template slot-scope="scope">
          {{
            educationList.filter(
              (item) => item.dictValue === scope.row.education
            )[0].dictLabel
          }}
        </template>
      </el-table-column>
      <el-table-column label="职称" align="center">
        <template slot-scope="scope">
          {{
            jobTitleList.filter(
              (item) => item.dictValue === scope.row.jobTitle
            )[0].dictLabel
          }}
        </template>
      </el-table-column>
      <el-table-column label="工作状态" align="center">
        <template slot-scope="scope">
          {{
            workStatusList.filter(
              (item) => item.dictValue === scope.row.workStatus
            )[0].dictLabel
          }}
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.settledStatus == 1 ? "已审核" : "待审核" }}
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="在职状态"
        align="center"
        prop="employmentStatus"
      />
      <el-table-column label="联系方式" align="center" prop="contactPhone" />
      <el-table-column label="工作经验" align="center" prop="workExperience" />
      <el-table-column label="技能特长" align="center" prop="skills" />
      <el-table-column label="简历附件" align="center" prop="resumeFile" /> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:info:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:info:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改人才信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-input v-model="form.gender" placeholder="请输入性别" />
        </el-form-item>
        <el-form-item label="照片" prop="photo">
          <!-- <el-upload class="avatar-uploader" list-type="picture-card" action="" :show-file-list="false"
            :http-request="uploadFun" :before-upload="beforeAvatarUpload">
            <el-image v-if="form.photo" :src="form.photo" class="avatar">
              <div slot="error" class="image-slot">
                <span>暂无图片</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload> -->
          <ImageUpload v-model="form.photo" :limit="1" :multiple="false" />

        </el-form-item>
        <el-form-item label="岗位分类" prop="positionType">
          <el-select v-model="form.positionType" placeholder="请选择岗位分类" clearable style="width: 100%">
            <el-option v-for="dict in positionTypeList" :key="dict.dictLabel" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="最高学历" prop="education">
          <el-select v-model="form.education" placeholder="请选择最高学历" clearable style="width: 100%">
            <el-option v-for="dict in educationList" :key="dict.dictLabel" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="职称" prop="jobTitle">
          <el-select v-model="form.jobTitle" placeholder="请选择职称" clearable style="width: 100%">
            <el-option v-for="dict in jobTitleList" :key="dict.dictLabel" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="工作状态" prop="workStatus">
          <el-select v-model="form.workStatus" placeholder="请选择职称" clearable style="width: 100%">
            <el-option v-for="dict in workStatusList" :key="dict.dictLabel" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
        <el-form-item label="联系方式" prop="contactPhone">
          <el-input v-model="form.contactPhone" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="工作经验" prop="workExperience">
          <el-input v-model="form.workExperience" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="技能特长" prop="skills">
          <el-input v-model="form.skills" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="简历附件" prop="resumeFile">
          <file-upload v-model="form.resumeFile" />
        </el-form-item>
        <el-form-item label="审核状态" prop="settledStatus">
          <el-select v-model="form.settledStatus" placeholder="请选择" clearable style="width: 100%">
            <el-option v-for="dict in auditStatusList" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInfo,
  getInfo,
  delInfo,
  addInfo,
  updateInfo,
} from "@/api/system/info";
import { listData } from "@/api/system/dict/data";

export default {
  name: "Info",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 人才信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        gender: null,
        photo: null,
        positionType: null,
        education: null,
        jobTitle: null,
        workStatus: null,
        contactPhone: null,
        workExperience: null,
        skills: null,
        resumeFile: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        positionType: [
          { required: true, message: "岗位分类不能为空", trigger: "change" },
        ],
        education: [
          { required: true, message: "最高学历不能为空", trigger: "blur" },
        ],
        jobTitle: [
          { required: true, message: "职称不能为空", trigger: "blur" },
        ],
        workStatus: [
          { required: true, message: "工作状态不能为空", trigger: "change" },
        ],
      },
      positionTypeList: [], // 岗位分类
      educationList: [], // 最高学历
      jobTitleList: [], // 职称
      workStatusList: [], // 工作状态
      auditStatusList: [
        {
          dictValue: "0",
          dictLabel: "待审核",
        },
        {
          dictValue: "1",
          dictLabel: "已审核",
        },
      ],
    };
  },
  created() {
    this.getPositionType();
    this.getEducation();
    this.getJobTitle();
    this.getWorkStatus();
    this.getList();
  },
  methods: {
    // 岗位分类
    getPositionType() {
      let params = { dictType: "position_type" };
      listData(params).then((response) => {
        this.positionTypeList = response.rows;
      });
    },
    // 最高学历
    getEducation() {
      let params = { dictType: "education" };
      listData(params).then((response) => {
        this.educationList = response.rows;
      });
    },
    // 职称
    getJobTitle() {
      let params = { dictType: "job_title" };
      listData(params).then((response) => {
        this.jobTitleList = response.rows;
      });
    },
    // 工作状态
    getWorkStatus() {
      let params = { dictType: "work_status" };
      listData(params).then((response) => {
        this.workStatusList = response.rows;
      });
    },
    /** 查询人才信息列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then((response) => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        gender: null,
        photo: null,
        positionType: null,
        education: null,
        jobTitle: null,
        workStatus: null,
        contactPhone: null,
        workExperience: null,
        skills: null,
        resumeFile: null,
        createTime: null,
        updateTime: null,
        settledStatus: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加人才信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getInfo(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改人才信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除人才信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/info/export",
        {
          ...this.queryParams,
        },
        `info_${new Date().getTime()}.xlsx`
      );
    },
    // 图片上传
    uploadFun(params) {
      const file = params.file;
      let form = new FormData();
      form.append("file", file); // 文件对象
      comUpload(form).then((res) => {
        let data = res.data;
        this.form.photo = data.fileFullPath;
        // this.$set(this.form, "images", data.fileFullPath); // 图片全路径
      });
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg" || "image/png" || "image/jpg";
      const isLt2M = file.size / 1024 / 1024 < 50;
      if (!isJPG) {
        this.$message.error("上传图片只能是 jpg、jpeg、png 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 50MB!");
      }
      return isJPG && isLt2M;
    },
  },
};
</script>
