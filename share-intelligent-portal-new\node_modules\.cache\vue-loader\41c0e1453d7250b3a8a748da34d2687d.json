{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\index.vue?vue&type=style&index=1&id=61c36e2c&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\index.vue", "mtime": 1750311962979}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQoucG9saWN5LWRlY2xhcmUtY29udGFpbmVyIHsNCiAgLnBvbGljeS1kZWNsYXJlbC1zZWFyY2gtaW5wdXQgew0KICAgIC5lbC1pbnB1dF9faW5uZXIgew0KICAgICAgaGVpZ2h0OiA1NHB4Ow0KICAgICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDI3cHggMCAwIDI3cHg7DQogICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtUmVndWxhciwgUGluZ0ZhbmcgU0M7DQogICAgICBsaW5lLWhlaWdodDogMjRweDsNCiAgICAgIHBhZGRpbmctbGVmdDogMzBweDsNCiAgICB9DQogICAgLmVsLWlucHV0LWdyb3VwX19hcHBlbmQgew0KICAgICAgYm9yZGVyLXJhZGl1czogMHB4IDEwMHB4IDEwMHB4IDBweDsNCiAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1SZWd1bGFyLCBQaW5nRmFuZyBTQzsNCiAgICAgIGNvbG9yOiAjZmZmOw0KICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgfQ0KICB9DQogIC5lbC1mb3JtLWl0ZW1fX2xhYmVsIHsNCiAgICB3aWR0aDogODhweDsNCiAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1NZWRpdW0sIFBpbmdGYW5nIFNDOw0KICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgY29sb3I6ICM5OTk7DQogICAgcGFkZGluZy1yaWdodDogMzJweDsNCiAgICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICB9DQogIC5wb2xpY3ktZGVjbGFyZWwtc2VhcmNoLWxpbmUgew0KICAgIC5lbC1mb3JtLWl0ZW1fX2NvbnRlbnQgew0KICAgICAgd2lkdGg6IDk3MHB4Ow0KICAgIH0NCiAgfQ0KICAuZWwtcmFkaW8tYnV0dG9uIHsNCiAgICBwYWRkaW5nLWJvdHRvbTogMjBweDsNCiAgICAuZWwtcmFkaW8tYnV0dG9uX19pbm5lciB7DQogICAgICBib3JkZXI6IG5vbmU7DQogICAgICBwYWRkaW5nOiAwIDMycHggMCAwOw0KICAgICAgYmFja2dyb3VuZDogbm9uZTsNCiAgICAgICY6aG92ZXIgew0KICAgICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgIH0NCiAgICB9DQogICAgJi5pcy1hY3RpdmUgew0KICAgICAgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgew0KICAgICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgICAgYmFja2dyb3VuZDogbm9uZTsNCiAgICAgIH0NCiAgICB9DQogICAgLmVsLXJhZGlvLWJ1dHRvbl9fb3JpZy1yYWRpbzpjaGVja2VkIHsNCiAgICAgICYgKyAuZWwtcmFkaW8tYnV0dG9uX19pbm5lciB7DQogICAgICAgIGJveC1zaGFkb3c6IHVuc2V0Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICAucG9saWN5LWRlY2xhcmVsLXBhZ2UtZW5kIHsNCiAgICAucG9saWN5LWRlY2xhcmVsLXBhZ2luYXRpb24gew0KICAgICAgLmJ0bi1wcmV2LA0KICAgICAgLmJ0bi1uZXh0LA0KICAgICAgLmJ0bi1xdWlja3ByZXYgew0KICAgICAgICB3aWR0aDogMzJweDsNCiAgICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgfQ0KICAgICAgJi5pcy1iYWNrZ3JvdW5kIHsNCiAgICAgICAgLmVsLXBhZ2VyIHsNCiAgICAgICAgICAubnVtYmVyIHsNCiAgICAgICAgICAgIHdpZHRoOiAzMnB4Ow0KICAgICAgICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMzJweDsNCiAgICAgICAgICAgICYuYWN0aXZlIHsNCiAgICAgICAgICAgICAgYmFja2dyb3VuZDogIzIxYzliODsNCiAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzIxYzliODsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/policy/declare", "sourcesContent": ["<template>\r\n  <div class=\"policy-declare-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"policy-declarel-banner\">\r\n      <img src=\"../../../assets/policyDeclare/policyDeclareBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"policy-declarel-title-content\">\r\n        <div class=\"policy-declarel-title-box\">\r\n          <div class=\"policy-declarel-divider\"></div>\r\n          <div class=\"policy-declarel-title\">政策申报</div>\r\n          <div class=\"policy-declarel-divider\"></div>\r\n        </div>\r\n        <div class=\"policy-declarel-search-box\">\r\n          <el-form ref=\"form\" class=\"policy-declarel-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.text\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"policy-declarel-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"policy-declarel-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"policy-declarel-card\">\r\n        <div class=\"policy-declarel-info-content\">\r\n          <div class=\"policy-declarel-search-type-box\">\r\n            <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n              <div class=\"policy-declarel-search-line\">\r\n                <el-form-item\r\n                  label=\"发布单位\"\r\n                  class=\"policy-declarel-search-line-item\"\r\n                  :class=\"{ advanced: !advancedReleaseId }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.releaseId\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in releaseIdList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.id\"\r\n                      >{{ item.name }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"policy-declarel-search-line-btn\"\r\n                  @click=\"toggleReleaseId\"\r\n                  >{{ advancedReleaseId ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n              <div class=\"policy-declarel-search-line\">\r\n                <el-form-item\r\n                  label=\"政策类型\"\r\n                  class=\"policy-declarel-search-line-item\"\r\n                  :class=\"{ advanced: !advancedType }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.type\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in typeList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"policy-declarel-search-line-btn\"\r\n                  @click=\"toggleType\"\r\n                  >{{ advancedType ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n              <div class=\"policy-declarel-search-line\">\r\n                <el-form-item\r\n                  label=\"政策状态\"\r\n                  class=\"policy-declarel-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.policyStatus\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in policyStatusList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.value\"\r\n                      >{{ item.label }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"policy-declarel-list-item\"\r\n            @click=\"goPolicyDeclarelDetail(item)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-headline\">\r\n                <div class=\"item-title\">{{ item.releaseUnitName }}</div>\r\n                <div v-if=\"item.releaseDistrict\" class=\"item-address-tag\">\r\n                  <img\r\n                    src=\"../../../assets/policyDeclare/policyAddressIcon.png\"\r\n                    alt=\"\"\r\n                    class=\"item-address-img\"\r\n                  />\r\n                  <div class=\"item-address-text\">\r\n                    {{ item.releaseDistrict }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              <div class=\"list-item-title\">\r\n                {{ item.title }}\r\n              </div>\r\n              <div class=\"list-item-box\">\r\n                <div class=\"list-item-time\">\r\n                  <div\r\n                    v-if=\"item.policyStatus === 2\"\r\n                    class=\"list-item-time-end\"\r\n                  >\r\n                    申报结束\r\n                  </div>\r\n                  <div v-else class=\"list-item-time-red\">\r\n                    距申报截止还有\r\n                    <div class=\"red-num\">{{ item.dayCount }}</div>\r\n                    天\r\n                  </div>\r\n                </div>\r\n                <div class=\"list-item-money\">\r\n                  <div class=\"list-item-money-title\">最高奖励</div>\r\n                  <span class=\"list-item-money-num\">{{ item.maxReward }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"list-item-status\">\r\n                <!-- 1进行中  2已截止 -->\r\n                <img\r\n                  v-if=\"item.policyStatus === 1\"\r\n                  src=\"../../../assets/policyDeclare/carryOnIcon.png\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../assets/policyDeclare/cutoffIcon.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"policy-declarel-page-end\">\r\n            <el-button class=\"policy-declarel-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            >\r\n            <el-pagination\r\n              v-if=\"data && data.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"policy-declarel-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @size-change=\"handleSizeChange\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getPolicyDeclareList, getListByName } from \"@/api/policyDeclare\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport { POLICY_STATUS } from \"@/const/status\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        text: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        releaseId: \"\", //发布单位\r\n        type: \"\", //政策类型\r\n        policyStatus: \"\", //政策状态\r\n        labelCodeList: [], //政策画像code集合\r\n      },\r\n      releaseIdList: [], //发布单位下拉列表\r\n      typeList: [], //政策类型下拉列表\r\n      policyStatusList: POLICY_STATUS,\r\n      advancedReleaseId: false,\r\n      advancedType: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    const { code } = this.$route.params || {};\r\n    console.log(this.$route);\r\n    if (code) {\r\n      this.formInfo.labelCodeList = code;\r\n    }\r\n    this.getDictsList(\"policy_type\", \"typeList\");\r\n    this.getListByName();\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getPolicyDeclareList({\r\n        text: this.form.text,\r\n        type: this.formInfo.type,\r\n        releaseId: this.formInfo.releaseId,\r\n        policyStatus: this.formInfo.policyStatus,\r\n        labelCodeList: this.formInfo.labelCodeList,\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 查询发布单位\r\n    getListByName() {\r\n      getListByName().then((res) => {\r\n        this.releaseIdList = res.data || [];\r\n      });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    // 显示更多发布单位\r\n    toggleReleaseId() {\r\n      this.advancedReleaseId = !this.advancedReleaseId;\r\n    },\r\n    // 显示更多政策类型\r\n    toggleType() {\r\n      this.advancedType = !this.advancedType;\r\n    },\r\n    changeRadio() {\r\n      console.log(typeof this.formInfo.releaseId, \"0000\");\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n    // 跳转到政策详情页面\r\n    goPolicyDeclarelDetail(item) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/policyDeclareDetail\",\r\n        query: { id: item.id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n  watch: {},\r\n  computed: {},\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.policy-declare-container {\r\n  width: 100%;\r\n  .policy-declarel-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .policy-declarel-title-content {\r\n    width: 100%;\r\n    padding-bottom: 18px;\r\n    .policy-declarel-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .policy-declarel-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .policy-declarel-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .policy-declarel-search-box {\r\n      .policy-declarel-search-form {\r\n        text-align: center;\r\n        .policy-declarel-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .policy-declarel-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .policy-declarel-card {\r\n    background: #f4f5f9;\r\n    padding-top: 40px;\r\n    .policy-declarel-info-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      .policy-declarel-search-type-box {\r\n        background: #fff;\r\n        margin-bottom: 17px;\r\n        .policy-declarel-search-line {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 14px 24px 4px;\r\n          .policy-declarel-search-line-item {\r\n            flex: 1;\r\n            margin-bottom: 0;\r\n            display: flex;\r\n            &.advanced {\r\n              overflow: hidden;\r\n              height: 45px;\r\n            }\r\n            .more-radio {\r\n              margin-top: 11px;\r\n              flex: 1;\r\n            }\r\n          }\r\n          .policy-declarel-search-line-btn {\r\n            display: inline-block;\r\n            width: 64px;\r\n            height: 24px;\r\n            background: #fff;\r\n            border-radius: 2px;\r\n            border: 1px solid #d9d9d9;\r\n            font-size: 12px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            color: #333;\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 0 16px;\r\n            margin-top: 5px;\r\n            &:hover {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n          & + .policy-declarel-search-line {\r\n            border-top: 1px solid #f5f5f5;\r\n          }\r\n        }\r\n      }\r\n      .policy-declarel-list-item {\r\n        position: relative;\r\n        width: 100%;\r\n        background: #fff;\r\n        border-radius: 12px;\r\n        .list-item-content {\r\n          padding: 27px 24px 24px 24px;\r\n          cursor: pointer;\r\n          .list-item-headline {\r\n            display: flex;\r\n            align-items: center;\r\n            .item-title {\r\n              max-width: 570px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #999;\r\n              line-height: 18px;\r\n              word-break: break-all;\r\n            }\r\n            .item-address-tag {\r\n              display: flex;\r\n              align-items: center;\r\n              border-radius: 6px;\r\n              border: 2px solid #ff8516;\r\n              font-size: 15px;\r\n              line-height: 15px;\r\n              text-align: center;\r\n              margin-left: 12px;\r\n              color: #ff8516;\r\n              .item-address-img {\r\n                width: 19px;\r\n                height: 18px;\r\n                margin-right: 1px;\r\n              }\r\n              .item-address-text {\r\n                max-width: 570px;\r\n                word-break: break-all;\r\n                padding: 3px 5px 2px 0;\r\n              }\r\n            }\r\n          }\r\n          .list-item-title {\r\n            font-size: 24px;\r\n            font-family: PingFangSC-Medium, PingFang SC;\r\n            font-weight: 500;\r\n            color: #323233;\r\n            line-height: 36px;\r\n            word-break: break-all;\r\n            padding-top: 18px;\r\n          }\r\n          .list-item-box {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            margin-top: 20px;\r\n            .list-item-time {\r\n              background: #f5f5f5;\r\n              border-radius: 6px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #999;\r\n              line-height: 18px;\r\n              .list-item-time-end {\r\n                padding: 6px 12px;\r\n              }\r\n              .list-item-time-red {\r\n                display: flex;\r\n                align-items: center;\r\n                padding: 6px 15px 6px 12px;\r\n                .red-num {\r\n                  max-width: 270px;\r\n                  font-size: 24px;\r\n                  font-family: PingFangSC-Medium, PingFang SC;\r\n                  font-weight: 500;\r\n                  color: #cf4140;\r\n                  line-height: 24px;\r\n                  word-wrap: break-word;\r\n                }\r\n              }\r\n            }\r\n            .list-item-money {\r\n              display: flex;\r\n              align-items: flex-end;\r\n              max-width: 570px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              .list-item-money-title {\r\n                font-size: 18px;\r\n                color: #999;\r\n                line-height: 18px;\r\n                margin-right: 6px;\r\n              }\r\n              .list-item-money-num {\r\n                max-width: 270px;\r\n                font-size: 36px;\r\n                font-weight: 500;\r\n                color: #cf4140;\r\n                line-height: 36px;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n          }\r\n        }\r\n        & + .policy-declarel-list-item {\r\n          margin-top: 24px;\r\n        }\r\n        &:hover {\r\n          .list-item-title {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        .list-item-status {\r\n          position: absolute;\r\n          top: 0;\r\n          right: 0;\r\n          img {\r\n            width: 92px;\r\n            height: 71px;\r\n          }\r\n        }\r\n      }\r\n      .policy-declarel-page-end {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        padding: 24px 0 60px;\r\n        .policy-declarel-page-btn {\r\n          width: 82px;\r\n          height: 32px;\r\n          background: #fff;\r\n          border-radius: 4px;\r\n          border: 1px solid #d9d9d9;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.policy-declare-container {\r\n  .policy-declarel-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .el-form-item__label {\r\n    width: 88px;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #999;\r\n    padding-right: 32px;\r\n    text-align: left;\r\n  }\r\n  .policy-declarel-search-line {\r\n    .el-form-item__content {\r\n      width: 970px;\r\n    }\r\n  }\r\n  .el-radio-button {\r\n    padding-bottom: 20px;\r\n    .el-radio-button__inner {\r\n      border: none;\r\n      padding: 0 32px 0 0;\r\n      background: none;\r\n      &:hover {\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        color: #21c9b8;\r\n        background: none;\r\n      }\r\n    }\r\n    .el-radio-button__orig-radio:checked {\r\n      & + .el-radio-button__inner {\r\n        box-shadow: unset;\r\n      }\r\n    }\r\n  }\r\n  .policy-declarel-page-end {\r\n    .policy-declarel-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}