{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\appliMarket\\payment.vue?vue&type=style&index=0&id=c6a648f2&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\appliMarket\\payment.vue", "mtime": 1750311962923}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["payment.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0MA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "payment.vue", "sourceRoot": "src/views/appliMarket", "sourcesContent": ["<template>\r\n  <div class=\"activity-container\">\r\n    <div class=\"order\">\r\n      <div class=\"step\"><img src=\"../../assets/images/step2.jpg\" /></div>\r\n      <div class=\"order_t\">\r\n        <p><strong>请确认订单信息</strong></p>\r\n        <p>您可在个人中心查看订单信息</p>\r\n      </div>\r\n      <div class=\"order_t2\">\r\n        <p><strong>订单信息</strong></p>\r\n      </div>\r\n      <div class=\"order_detail\">\r\n        <table border=\"0\" class=\"tb_order\">\r\n          <tr>\r\n            <td bgcolor=\"#de791b\">\r\n              <strong style=\"color: #fff\">应用名称</strong>\r\n            </td>\r\n            <td bgcolor=\"#de791b\">\r\n              <strong style=\"color: #fff\">应用编号</strong>\r\n            </td>\r\n            <td bgcolor=\"#de791b\">\r\n              <strong style=\"color: #fff\">应用提供</strong>\r\n            </td>\r\n            <!-- <td bgcolor=\"#de791b\">\r\n              <strong style=\"color: #fff\">规格</strong>\r\n            </td> -->\r\n            <td bgcolor=\"#de791b\">\r\n              <strong style=\"color: #fff\">可用时长</strong>\r\n            </td>\r\n            <td bgcolor=\"#de791b\">\r\n              <strong style=\"color: #fff\">可用人数</strong>\r\n            </td>\r\n            <td bgcolor=\"#de791b\">\r\n              <strong style=\"color: #fff\">价格</strong>\r\n            </td>\r\n          </tr>\r\n          <tr>\r\n            <td>{{ orderData.remark }}</td>\r\n            <td>{{ orderData.appCode }}</td>\r\n            <td>{{ orderData.supply }}</td>\r\n            <!-- <td>{{ orderData.spec == \"1\" ? \"基础版\" : \"高级版\" }}</td> -->\r\n            <td>{{ orderData.validTime == \"1\" ? \"一年\" : \"永久\" }}</td>\r\n            <td>不限</td>\r\n            <td>￥{{ orderData.price }}</td>\r\n          </tr>\r\n        </table>\r\n      </div>\r\n      <div class=\"order_t2\">\r\n        <p><strong>支付选择</strong></p>\r\n      </div>\r\n      <div>\r\n        <el-radio v-model=\"payRadio\" label=\"1\">线上支付</el-radio>\r\n        <el-radio v-model=\"payRadio\" label=\"2\">线下支付</el-radio>\r\n      </div>\r\n      <div v-show=\"payRadio == '1'\">\r\n        <div class=\"order_t2\">\r\n          <p><strong>选择支付平台</strong></p>\r\n        </div>\r\n        <div class=\"zhifu_list\">\r\n          <a href=\"javascript:void(0)\" class=\"selected11\"\r\n            ><img src=\"../../assets/images/wechart.jpg\"\r\n          /></a>\r\n        </div>\r\n      </div>\r\n      <div v-show=\"payRadio == '2'\">\r\n        <div class=\"order_t2\">\r\n          <p style=\"font-size: 18px\">\r\n            转账信息(请按照以下信息进行转账汇款操作)\r\n          </p>\r\n          <p>税号: 91370212MA3ER1RQXE</p>\r\n          <p>注册地址: 山东省青岛市市北区敦化路119号1802室</p>\r\n          <p>电话: 0532-88897900</p>\r\n          <p>银行账号: 532907425710601</p>\r\n          <p>开户行: 招商银行青岛崂山支行</p>\r\n        </div>\r\n      </div>\r\n      <div class=\"lijizhifu\">\r\n        <a href=\"javascript:void(0)\" @click=\"cancel\">取消</a\r\n        ><a href=\"javascript:void(0)\" class=\"selected12\" @click=\"zhifu\"\r\n          >立即支付</a\r\n        >\r\n      </div>\r\n    </div>\r\n\r\n    <!--购买弹窗-->\r\n    <div class=\"tishi_bg\" v-if=\"showzf\">\r\n      <div class=\"zhifu_bg\">\r\n        <div class=\"goumai_t\">\r\n          <span\r\n            ><a href=\"javascript:void(0)\" @click=\"showzf = false\"\r\n              ><img\r\n                style=\"margin-top: 12px\"\r\n                src=\"../../assets/images/close2.png\" /></a></span\r\n          >微信扫码支付\r\n        </div>\r\n        <div class=\"goumai_c\" style=\"padding-top: 0\">\r\n          <div class=\"goumai_total\">\r\n            <div class=\"goumai_total_l\">\r\n              <p>\r\n                支付:<strong>￥{{ orderData.price }}</strong>\r\n              </p>\r\n              <p>请于2小时内支付</p>\r\n            </div>\r\n          </div>\r\n          <div class=\"qrcode\">\r\n            <div id=\"myqrcode\"></div>\r\n          </div>\r\n          <!-- <div class=\"zhifuewm\">\r\n            <img src=\"../../assets/images/erweima3.png\" />\r\n          </div> -->\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { orderDetail } from \"@/api/system/user\";\r\nimport { orderPayment } from \"@/api/appliMarket\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      showLogin: false,\r\n      userinfo: [],\r\n      token: \"\",\r\n      detail: [],\r\n      cmsList: [],\r\n      id: \"\",\r\n      orderData: {},\r\n      showzf: false,\r\n      payRadio: \"1\",\r\n      timer: null,\r\n    };\r\n  },\r\n  created() {\r\n    this.getOrderData();\r\n  },\r\n  methods: {\r\n    getOrderData() {\r\n      let id = this.$route.query.id;\r\n      orderDetail(id).then((res) => {\r\n        if (res.code === 200) {\r\n          this.orderData = res.data;\r\n        }\r\n      });\r\n    },\r\n    zhifu() {\r\n      if (this.payRadio == \"1\") {\r\n        let data = {\r\n          id: this.orderData.id,\r\n          appId: this.orderData.appId,\r\n          price: this.orderData.price,\r\n          totalAmount: this.orderData.totalAmount,\r\n          spec: this.orderData.spec,\r\n          validTime: this.orderData.validTime,\r\n          phone: this.orderData.phone,\r\n          remark: this.orderData.remark,\r\n        };\r\n        orderPayment(data).then((res) => {\r\n          if (res.code === 200) {\r\n            console.log(res, \"下单返回----------------\");\r\n            this.showzf = true;\r\n            setTimeout(() => {\r\n              let qr = new QRCode(document.getElementById(\"myqrcode\"), {\r\n                text: res.data.codeUrl,\r\n              });\r\n              qr._el.title = \"\";\r\n            }, 500);\r\n            //3秒轮循环判断是否支付成功\r\n            this.timer = setInterval(() => {\r\n              this.getOrder();\r\n            }, 3000);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    getOrder() {\r\n      let id = this.orderData.id;\r\n      orderDetail(id).then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res.data.orderStatus, \"--------\");\r\n          if (res.data.orderStatus == \"2\") {\r\n            clearInterval(this.timer);\r\n            this.$router.push({\r\n              path: \"/paySuccess\",\r\n              query: {\r\n                price: res.data.price,\r\n              },\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    cancel() {\r\n      this.$router.go(-1);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.order {\r\n  width: 1000px;\r\n  margin-top: 0;\r\n  margin-right: auto;\r\n  margin-bottom: 0;\r\n  margin-left: auto;\r\n  padding-bottom: 50px;\r\n  min-height: 500px;\r\n  .step {\r\n    text-align: center;\r\n    padding-top: 20px;\r\n    width: 100%;\r\n    padding-bottom: 20px;\r\n  }\r\n  .order_t {\r\n    font-size: 14px;\r\n    background-image: url(\"../../assets/images/step.png\");\r\n    background-repeat: no-repeat;\r\n    background-position: left center;\r\n    padding-left: 75px;\r\n  }\r\n\r\n  .order_t strong {\r\n    font-size: 18px;\r\n  }\r\n  .order_t2 {\r\n    padding-top: 30px;\r\n  }\r\n  table.tb_order {\r\n    padding: 0px;\r\n    border-collapse: collapse;\r\n    width: 100%;\r\n    border-top-width: 1px;\r\n    border-right-width: 1px;\r\n    border-bottom-width: 0px;\r\n    border-left-width: 1px;\r\n    border-top-style: solid;\r\n    border-right-style: solid;\r\n    border-bottom-style: solid;\r\n    border-left-style: solid;\r\n    border-top-color: #eee;\r\n    border-right-color: #eee;\r\n    border-bottom-color: #eee;\r\n    border-left-color: #eee;\r\n    margin-top: 15px;\r\n    margin-right: 0px;\r\n    margin-bottom: 0px;\r\n    margin-left: 0px;\r\n    color: #fff;\r\n  }\r\n\r\n  table.tb_order tr {\r\n    margin: 0px;\r\n    padding: 0px;\r\n  }\r\n\r\n  table.tb_order tr td {\r\n    margin: 0px;\r\n    padding: 0px;\r\n    text-align: center;\r\n    border-top-width: 0px;\r\n    border-right-width: 0px;\r\n    border-bottom-width: 1px;\r\n    border-left-width: 0px;\r\n    border-top-style: solid;\r\n    border-right-style: solid;\r\n    border-bottom-style: solid;\r\n    border-left-style: solid;\r\n    border-top-color: #eee;\r\n    border-right-color: #eee;\r\n    border-bottom-color: #eee;\r\n    border-left-color: #eee;\r\n    line-height: 50px;\r\n    color: #333;\r\n  }\r\n\r\n  .zhifu_list {\r\n    margin: 0px;\r\n    padding-top: 15px;\r\n    padding-right: 0px;\r\n    padding-bottom: 0px;\r\n    padding-left: 0px;\r\n  }\r\n\r\n  .zhifu_list a {\r\n    border: 1px solid #eee;\r\n    // width: 150px;\r\n    display: inline-block;\r\n    padding-top: 0px;\r\n    padding-right: 15px;\r\n    padding-bottom: 0px;\r\n    padding-left: 15px;\r\n    margin-top: 0px;\r\n    margin-right: 15px;\r\n    margin-bottom: 0px;\r\n    margin-left: 0px;\r\n  }\r\n\r\n  .selected11 {\r\n    border: 1px solid #de791b !important;\r\n  }\r\n\r\n  .lijizhifu {\r\n    padding-top: 15px;\r\n    text-align: right;\r\n  }\r\n\r\n  .lijizhifu a {\r\n    display: inline-block;\r\n    border: 1px solid #eee;\r\n    line-height: 40px;\r\n    padding-top: 0px;\r\n    padding-right: 25px;\r\n    padding-bottom: 0px;\r\n    padding-left: 25px;\r\n    margin-left: 10px;\r\n  }\r\n\r\n  .lijizhifu a:hover,\r\n  .selected12 {\r\n    color: #fff;\r\n    background-color: #de791b;\r\n  }\r\n}\r\n\r\n/*提示*/\r\n\r\n.tishi_bg {\r\n  position: fixed;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(255, 255, 255, 0.6);\r\n  z-index: 100;\r\n  top: 0;\r\n  left: 0;\r\n  .zhifu_bg {\r\n    width: 400px;\r\n    position: relative;\r\n    margin-top: 8%;\r\n    margin-right: auto;\r\n    margin-bottom: 0;\r\n    margin-left: auto;\r\n    background-color: #fff;\r\n    border-radius: 10px;\r\n    box-shadow: 0px 0px 4px 0px rgb(51 51 51 / 20%);\r\n    padding: 0px;\r\n    overflow: hidden;\r\n  }\r\n  .goumai_t {\r\n    background-color: #eee;\r\n    color: #333;\r\n    font-size: 18px;\r\n    line-height: 50px;\r\n    padding-right: 30px;\r\n    padding-left: 30px;\r\n  }\r\n\r\n  .goumai_t span {\r\n    float: right;\r\n  }\r\n\r\n  .goumai_t span img {\r\n    width: 25px;\r\n  }\r\n  .zhifuewm {\r\n    width: 250px;\r\n    padding: 10px;\r\n    margin-top: 15px;\r\n    margin-right: auto;\r\n    margin-bottom: 0;\r\n    margin-left: auto;\r\n    border: 1px solid #ccc;\r\n  }\r\n\r\n  .zhifuewm img {\r\n    width: 100%;\r\n  }\r\n  .goumai_c {\r\n    padding: 30px;\r\n  }\r\n  .goumai_total {\r\n    padding-top: 15px;\r\n    display: flex;\r\n    flex-flow: row wrap;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .goumai_total_l {\r\n    width: 60%;\r\n  }\r\n\r\n  .goumai_total_l strong {\r\n    color: #f00;\r\n    font-size: 24px;\r\n  }\r\n  .qrcode {\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n}\r\n</style>\r\n"]}]}