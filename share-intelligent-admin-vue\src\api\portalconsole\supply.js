import request from '@/utils/request'

// 查询服务供给列表
export function listSupply(query) {
  return request({
    url: '/portalconsole/supply/list',
    method: 'get',
    params: query
  })
}

// 查询服务供给详细
export function getSupply(id) {
  return request({
    url: '/portalconsole/supply/' + id,
    method: 'get'
  })
}

// 新增服务供给
export function addSupply(data) {
  return request({
    url: '/portalconsole/supply',
    method: 'post',
    data: data
  })
}

// 修改服务供给
export function updateSupply(data) {
  return request({
    url: '/portalconsole/supply',
    method: 'put',
    data: data
  })
}

// 删除服务供给
export function delSupply(id) {
  return request({
    url: '/portalconsole/supply/' + id,
    method: 'delete'
  })
}
