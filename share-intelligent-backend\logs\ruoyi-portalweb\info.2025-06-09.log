09:23:51.324 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:23:51.400 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:23:51.757 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:23:51.757 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:24:02.288 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:24:04.528 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9212"]
09:24:04.529 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:24:04.529 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
09:24:04.704 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:24:05.551 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
09:24:05.592 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:25:42.350 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:25:42.429 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:25:42.855 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:25:42.855 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:25:45.089 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:25:47.853 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9212"]
09:25:47.855 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:25:47.855 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
09:25:48.020 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:25:50.399 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
09:25:50.430 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:37:54.642 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
09:37:54.727 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:37:55.197 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:37:55.198 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:37:58.892 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
09:38:05.225 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9212"]
09:38:05.228 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:38:05.228 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
09:38:05.570 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:38:07.030 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
09:38:07.032 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
09:38:07.032 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:38:12.314 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getVerifier,69] - 获取签名验证器
09:38:13.366 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-09T01:38:13.366Z
09:38:13.376 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayClient,91] - 获取httpClient
09:38:13.612 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-09T01:38:13.612Z
09:38:14.183 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:38:15.764 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayNoSignClient,114] - == getWxPayNoSignClient END ==
09:38:17.588 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9212"]
09:38:17.618 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
09:38:17.620 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
09:38:17.837 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-portalweb ************:9212 register finished
09:38:19.865 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStarted,61] - Started RuoYiPortalwebApplication in 25.998 seconds (JVM running for 26.931)
09:38:19.903 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb.yml, group=DEFAULT_GROUP
09:38:19.904 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb-prod.yml, group=DEFAULT_GROUP
09:38:19.905 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb, group=DEFAULT_GROUP
09:38:20.119 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:38:13.371 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-09T02:38:13.371Z
10:38:13.580 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-09T02:38:13.580Z
11:10:39.147 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,95] - De-registering from Nacos Server now...
11:10:39.149 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,115] - De-registration finished.
11:10:39.302 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,217] - dynamic-datasource start closing ....
11:10:39.305 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2174] - {dataSource-1} closing ...
11:10:39.311 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2247] - {dataSource-1} closed
11:10:39.311 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - [destroy,98] - dynamic-datasource close the datasource named [master] success,
11:10:39.311 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,221] - dynamic-datasource all closed success,bye
11:10:48.045 [main] INFO  c.a.n.c.e.SearchableProperties - [sortPropertySourceDefaultOrder,197] - properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
11:10:48.148 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:10:48.754 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:10:48.754 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:10:53.391 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStartupProfileInfo,638] - The following 1 profile is active: "prod"
11:10:59.381 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9212"]
11:10:59.390 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:10:59.390 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.83]
11:10:59.860 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:11:01.834 [main] INFO  c.a.d.p.DruidDataSource - [init,1009] - {dataSource-1,master} inited
11:11:01.839 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,160] - dynamic-datasource - add a datasource named [master] success
11:11:01.840 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,243] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:11:09.083 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getVerifier,69] - 获取签名验证器
11:11:11.507 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-09T03:11:11.507Z
11:11:11.527 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayClient,91] - 获取httpClient
11:11:11.760 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-09T03:11:11.760Z
11:11:12.937 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:11:15.077 [main] INFO  c.r.p.s.c.WxPayConfigV3 - [getWxPayNoSignClient,114] - == getWxPayNoSignClient END ==
11:11:18.134 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9212"]
11:11:18.300 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
11:11:18.302 [main] INFO  c.a.n.p.a.s.c.ClientAuthPluginManager - [init,56] - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
11:11:18.550 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,76] - nacos registry, DEFAULT_GROUP ruoyi-portalweb ************:9212 register finished
11:11:20.832 [main] INFO  c.r.p.RuoYiPortalwebApplication - [logStarted,61] - Started RuoYiPortalwebApplication in 33.595 seconds (JVM running for 35.407)
11:11:20.907 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb.yml, group=DEFAULT_GROUP
11:11:20.908 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb-prod.yml, group=DEFAULT_GROUP
11:11:20.910 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=ruoyi-portalweb, group=DEFAULT_GROUP
11:11:21.318 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:11:11.518 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-09T04:11:11.518Z
12:11:11.779 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-09T04:11:11.779Z
13:11:11.510 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-09T05:11:11.510Z
13:11:11.756 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-09T05:11:11.756Z
14:11:11.524 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-09T06:11:11.524Z
14:11:11.774 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-09T06:11:11.774Z
15:11:11.509 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-09T07:11:11.509Z
15:11:11.766 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-09T07:11:11.766Z
16:11:11.516 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-09T08:11:11.516Z
16:11:11.746 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-09T08:11:11.746Z
17:11:11.509 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-09T09:11:11.509Z
17:11:11.735 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-09T09:11:11.735Z
18:11:11.521 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,94] - Begin update Certificate.Date:2025-06-09T10:11:11.521Z
18:11:11.765 [scheduled_update_cert_thread] INFO  c.w.p.c.a.h.c.CertManagerSingleton - [lambda$init$0,96] - Finish update Certificate.Date:2025-06-09T10:11:11.765Z
18:38:24.656 [lettuce-nioEventLoop-4-2] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:254)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
18:38:24.666 [lettuce-nioEventLoop-4-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:254)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:748)
18:38:24.838 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /127.0.0.1:6379
18:38:24.838 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /127.0.0.1:6379
18:38:31.183 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
18:38:31.183 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 127.0.0.1:6379
