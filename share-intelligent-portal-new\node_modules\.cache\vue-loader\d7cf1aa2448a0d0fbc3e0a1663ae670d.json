{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\resourceHall\\index.vue?vue&type=style&index=1&id=592ced1a&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\resourceHall\\index.vue", "mtime": 1750311962990}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnJlc291cmNlLWhhbGwtY29udGFpbmVyIHsNCiAgLnJlc291cmNlLWhhbGwtc2VhcmNoLWlucHV0IHsNCiAgICAuZWwtaW5wdXRfX2lubmVyIHsNCiAgICAgIGhlaWdodDogNTRweDsNCiAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICBib3JkZXItcmFkaXVzOiAyN3B4IDAgMCAyN3B4Ow0KICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLVJlZ3VsYXIsIFBpbmdGYW5nIFNDOw0KICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7DQogICAgICBwYWRkaW5nLWxlZnQ6IDMwcHg7DQogICAgfQ0KICAgIC5lbC1pbnB1dC1ncm91cF9fYXBwZW5kIHsNCiAgICAgIGJvcmRlci1yYWRpdXM6IDBweCAxMDBweCAxMDBweCAwcHg7DQogICAgICBiYWNrZ3JvdW5kOiAjMjFjOWI4Ow0KICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtUmVndWxhciwgUGluZ0ZhbmcgU0M7DQogICAgICBjb2xvcjogI2ZmZjsNCiAgICAgIGxpbmUtaGVpZ2h0OiAyNHB4Ow0KICAgIH0NCiAgfQ0KICAuZWwtZm9ybS1pdGVtX19sYWJlbCB7DQogICAgd2lkdGg6IDg4cHg7DQogICAgZm9udC1mYW1pbHk6IFBpbmdGYW5nU0MtTWVkaXVtLCBQaW5nRmFuZyBTQzsNCiAgICBmb250LXdlaWdodDogNTAwOw0KICAgIGNvbG9yOiAjOTk5Ow0KICAgIHBhZGRpbmctcmlnaHQ6IDMycHg7DQogICAgdGV4dC1hbGlnbjogbGVmdDsNCiAgfQ0KICAucmVzb3VyY2UtaGFsbC1zZWFyY2gtbW9yZS1saW5lIHsNCiAgICAuZWwtZm9ybS1pdGVtX19jb250ZW50IHsNCiAgICAgIHdpZHRoOiA5NzBweDsNCiAgICB9DQogIH0NCiAgLmVsLXJhZGlvLWJ1dHRvbiB7DQogICAgcGFkZGluZy1ib3R0b206IDIwcHg7DQogICAgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgew0KICAgICAgYm9yZGVyOiBub25lOw0KICAgICAgcGFkZGluZzogMCAzMnB4IDAgMDsNCiAgICAgIGJhY2tncm91bmQ6IG5vbmU7DQogICAgICAmOmhvdmVyIHsNCiAgICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgICB9DQogICAgfQ0KICAgICYuaXMtYWN0aXZlIHsNCiAgICAgIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsNCiAgICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgICAgIGJhY2tncm91bmQ6IG5vbmU7DQogICAgICB9DQogICAgfQ0KICAgIC5lbC1yYWRpby1idXR0b25fX29yaWctcmFkaW86Y2hlY2tlZCB7DQogICAgICAmICsgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgew0KICAgICAgICBib3gtc2hhZG93OiB1bnNldDsNCiAgICAgIH0NCiAgICB9DQogIH0NCiAgLnJlc291cmNlLWhhbGwtcGFnZS1lbmQgew0KICAgIC5yZXNvdXJjZS1oYWxsLXBhZ2luYXRpb24gew0KICAgICAgLmJ0bi1wcmV2LA0KICAgICAgLmJ0bi1uZXh0LA0KICAgICAgLmJ0bi1xdWlja3ByZXYgew0KICAgICAgICB3aWR0aDogMzJweDsNCiAgICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgfQ0KICAgICAgJi5pcy1iYWNrZ3JvdW5kIHsNCiAgICAgICAgLmVsLXBhZ2VyIHsNCiAgICAgICAgICAubnVtYmVyIHsNCiAgICAgICAgICAgIHdpZHRoOiAzMnB4Ow0KICAgICAgICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMzJweDsNCiAgICAgICAgICAgICYuYWN0aXZlIHsNCiAgICAgICAgICAgICAgYmFja2dyb3VuZDogIzIxYzliODsNCiAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzIxYzliODsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkhBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/purchaseSales/component/resourceHall", "sourcesContent": ["<template>\r\n  <div class=\"resource-hall-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"resource-hall-banner\">\r\n      <img\r\n        src=\"../../../../assets/resourceHall/resourceHallBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"resource-hall-title-content\">\r\n        <div class=\"resource-hall-title-box\">\r\n          <div class=\"resource-hall-divider\"></div>\r\n          <div class=\"resource-hall-title\">资源大厅</div>\r\n          <div class=\"resource-hall-divider\"></div>\r\n        </div>\r\n        <div class=\"resource-hall-search-box\">\r\n          <el-form ref=\"form\" class=\"resource-hall-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.name\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"resource-hall-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"resource-hall-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"resource-hall-card\">\r\n        <div class=\"resource-hall-info-content\">\r\n          <div class=\"resource-hall-search-type-box\">\r\n            <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n              <div class=\"resource-hall-search-line\">\r\n                <el-form-item\r\n                  label=\"供给类型\"\r\n                  class=\"resource-hall-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.supplyType\"\r\n                    class=\"resource-hall-search-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in supplyTypeList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"resource-hall-search-more-line\">\r\n                <el-form-item\r\n                  label=\"技术类别\"\r\n                  class=\"resource-hall-search-more-line-item\"\r\n                  :class=\"{ advanced: !advanced }\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.technologyType\"\r\n                    class=\"more-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in technologyTypeList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictLabel\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n                <el-button\r\n                  class=\"resource-hall-search-more-line-btn\"\r\n                  @click=\"toggleAdvanced\"\r\n                  >{{ advanced ? \"收起\" : \"更多\"\r\n                  }}<i class=\"el-icon-arrow-down\"></i>\r\n                </el-button>\r\n              </div>\r\n              <div class=\"resource-hall-search-line\">\r\n                <el-form-item\r\n                  label=\"成果阶段\"\r\n                  class=\"resource-hall-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.productStage\"\r\n                    class=\"resource-hall-search-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in productStageList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n              <div class=\"resource-hall-search-line\">\r\n                <el-form-item\r\n                  label=\"合作方式\"\r\n                  class=\"resource-hall-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.cooperationMode\"\r\n                    class=\"resource-hall-search-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in cooperationModeList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"resource-hall-list-item\"\r\n            @click=\"goResourceDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img\r\n                  v-if=\"item.productPhoto && item.productPhoto.length > 0\"\r\n                  :src=\"item.productPhoto[0].url\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/resourceDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"list-item-info\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.supplyName }}\r\n                </div>\r\n                <div class=\"list-item-text\">\r\n                  <div class=\"list-item-label\">应用领域：</div>\r\n                  <div class=\"list-item-tag-box\">\r\n                    <div\r\n                      v-for=\"(val, num) in item.applicationArea\"\r\n                      :key=\"num\"\r\n                      class=\"lilst-item-tag red-tag\"\r\n                    >\r\n                      {{ val }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <div class=\"list-item-text\">\r\n                  <div class=\"list-item-label\">技术类别：</div>\r\n                  <div class=\"list-item-tag-box\">\r\n                    <div\r\n                      v-for=\"(val, num) in item.technologyType\"\r\n                      :key=\"num\"\r\n                      class=\"lilst-item-tag blue-tag\"\r\n                    >\r\n                      {{ val }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"resource-hall-page-end\">\r\n            <el-button class=\"resource-hall-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            >\r\n            <el-pagination\r\n              v-if=\"data && data.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"resource-hall-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @size-change=\"handleSizeChange\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getResourceHallList } from \"@/api/purchaseSales\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        name: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        supplyType: \"\", //供给类型\r\n        technologyType: \"\", //技术类别\r\n        productStage: \"\", //成果阶段\r\n        cooperationMode: \"\", //合作方式\r\n      },\r\n      supplyTypeList: [], //供给类型列表\r\n      technologyTypeList: [], //技术类别列表\r\n      productStageList: [], //成果阶段列表\r\n      cooperationModeList: [], //合作方式列表\r\n      advanced: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getDictsList(\"supply_type\", \"supplyTypeList\");\r\n    this.getDictsList(\"technology_type\", \"technologyTypeList\");\r\n    this.getDictsList(\"product_stage\", \"productStageList\");\r\n    this.getDictsList(\"cooperation_mode\", \"cooperationModeList\");\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      getResourceHallList({\r\n        ...this.form,\r\n        ...this.formInfo,\r\n        auditStatus: 2,\r\n        displayStatus: 1,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          console.log(res);\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          console.log(res);\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.data.forEach((item) => {\r\n            item.productPhoto = item.productPhoto\r\n              ? JSON.parse(item.productPhoto)\r\n              : [];\r\n            item.applicationArea = item.applicationArea\r\n              ? item.applicationArea.split(\",\")\r\n              : \"\";\r\n            item.technologyType = item.technologyType\r\n              ? item.technologyType.split(\",\")\r\n              : \"\";\r\n          });\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n    // 技术类别显示更多\r\n    toggleAdvanced() {\r\n      this.advanced = !this.advanced;\r\n    },\r\n    // 跳转到资源详情页面\r\n    goResourceDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/resourceHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.resource-hall-container {\r\n  width: 100%;\r\n  .resource-hall-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .resource-hall-title-content {\r\n    width: 100%;\r\n    padding-bottom: 18px;\r\n    .resource-hall-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .resource-hall-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .resource-hall-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .resource-hall-search-box {\r\n      .resource-hall-search-form {\r\n        text-align: center;\r\n        .resource-hall-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .resource-hall-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .resource-hall-card {\r\n    background: #f4f5f9;\r\n    padding-top: 40px;\r\n    .resource-hall-info-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      .resource-hall-search-type-box {\r\n        background: #fff;\r\n        margin-bottom: 17px;\r\n        .resource-hall-search-line {\r\n          padding: 14px 24px 4px;\r\n          .resource-hall-search-line-item {\r\n            margin-bottom: 0;\r\n            .resource-hall-search-radio {\r\n              width: 1050px;\r\n              margin-top: 11px;\r\n            }\r\n          }\r\n          & + .resource-hall-search-line {\r\n            border-top: 1px solid #f5f5f5;\r\n          }\r\n        }\r\n        .resource-hall-search-more-line {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          padding: 14px 24px 4px;\r\n          border-top: 1px solid #f5f5f5;\r\n          border-bottom: 1px solid #f5f5f5;\r\n          .resource-hall-search-more-line-item {\r\n            flex: 1;\r\n            margin-bottom: 0;\r\n            display: flex;\r\n            &.advanced {\r\n              overflow: hidden;\r\n              height: 45px;\r\n            }\r\n            .more-radio {\r\n              margin-top: 11px;\r\n              flex: 1;\r\n            }\r\n          }\r\n          .resource-hall-search-more-line-btn {\r\n            display: inline-block;\r\n            width: 64px;\r\n            height: 24px;\r\n            background: #fff;\r\n            border-radius: 2px;\r\n            border: 1px solid #d9d9d9;\r\n            font-size: 12px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            color: #333;\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 0 16px;\r\n            margin-top: 5px;\r\n            &:hover {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .resource-hall-list-item {\r\n        width: 100%;\r\n        background: #fff;\r\n        border-radius: 12px;\r\n        .list-item-content {\r\n          display: flex;\r\n          padding: 24px;\r\n          cursor: pointer;\r\n          .list-item-img {\r\n            width: 180px;\r\n            height: 128px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n              border-radius: 4px;\r\n            }\r\n          }\r\n          .list-item-info {\r\n            padding-left: 24px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            .list-item-title {\r\n              width: 922px;\r\n              height: 20px;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              font-size: 20px;\r\n              font-weight: 500;\r\n              color: #323233;\r\n              line-height: 20px;\r\n              margin-bottom: 24px;\r\n              word-wrap: break-word;\r\n            }\r\n            .list-item-text {\r\n              display: flex;\r\n              align-items: top;\r\n              .list-item-label {\r\n                color: #323233;\r\n                line-height: 14px;\r\n                margin-top: 16px;\r\n              }\r\n              .list-item-tag-box {\r\n                display: flex;\r\n                width: 852px;\r\n                flex-wrap: wrap;\r\n                .lilst-item-tag {\r\n                  max-width: 840px;\r\n                  padding: 0px 12px;\r\n                  border-radius: 4px;\r\n                  font-size: 12px;\r\n                  line-height: 24px;\r\n                  text-align: center;\r\n                  margin-right: 16px;\r\n                  margin-top: 12px;\r\n                  word-wrap: break-word;\r\n                  text-align: left;\r\n                }\r\n                .red-tag {\r\n                  color: #21c9b8;\r\n                  background: #21c9b8 1a;\r\n                }\r\n                .blue-tag {\r\n                  background: #214dc51a;\r\n                  color: #214dc5;\r\n                }\r\n              }\r\n            }\r\n          }\r\n          &:hover {\r\n            .list-item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        & + .resource-hall-list-item {\r\n          margin-top: 24px;\r\n        }\r\n      }\r\n      .resource-hall-page-end {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        padding: 24px 0 60px;\r\n        .resource-hall-page-btn {\r\n          width: 82px;\r\n          height: 32px;\r\n          background: #fff;\r\n          border-radius: 4px;\r\n          border: 1px solid #d9d9d9;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.resource-hall-container {\r\n  .resource-hall-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .el-form-item__label {\r\n    width: 88px;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #999;\r\n    padding-right: 32px;\r\n    text-align: left;\r\n  }\r\n  .resource-hall-search-more-line {\r\n    .el-form-item__content {\r\n      width: 970px;\r\n    }\r\n  }\r\n  .el-radio-button {\r\n    padding-bottom: 20px;\r\n    .el-radio-button__inner {\r\n      border: none;\r\n      padding: 0 32px 0 0;\r\n      background: none;\r\n      &:hover {\r\n        color: #21c9b8;\r\n      }\r\n    }\r\n    &.is-active {\r\n      .el-radio-button__inner {\r\n        color: #21c9b8;\r\n        background: none;\r\n      }\r\n    }\r\n    .el-radio-button__orig-radio:checked {\r\n      & + .el-radio-button__inner {\r\n        box-shadow: unset;\r\n      }\r\n    }\r\n  }\r\n  .resource-hall-page-end {\r\n    .resource-hall-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}