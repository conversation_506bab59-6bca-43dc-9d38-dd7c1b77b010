<template>
  <div class="content">
    <div class="card-container cardStyle">
      <!-- 左侧 -->
      <div class="card_left">
        <div class="card_left_bottom">
          <div class="title">{{ detailsData.demandCompany }}</div>
          <div class="everyOption">
            <div class="optionName">联系人：</div>
            <div class="optionValue">{{ detailsData.contactPerson }}</div>
          </div>
          <div class="everyOption">
            <div class="optionName">联系电话：</div>
            <div class="optionValue">{{ detailsData.contactPhone }}</div>
          </div>
          <div class="everyOption">
            <div class="optionName">交货地址：</div>
            <div class="optionValue">{{ detailsData.deliveryAddress }}</div>
          </div>
          <div class="everyOption">
            <div class="optionName">需求截至时间：</div>
            <div class="optionValue">{{ detailsData.deadline }}</div>
          </div>
          <div class="everyOption">
            <div class="optionName">订单状态：</div>
            <div class="optionValue">
              {{
                detailsData.status == "0"
                  ? "未接单"
                  : detailsData.status == "1"
                    ? "进行中"
                    : "已完成"
              }}
            </div>
          </div>
          <div class="buttonStyle" @click="jumpIntention">我要接单</div>
        </div>
      </div>
      <!-- 中间 -->
      <div class="card_center_line"></div>
      <!-- 右侧 -->
      <div class="card_right">
        <div>
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">需求物料</div>
          </div>
          <div style="margin-top: 22px">
            <el-table :data="detailsData.materials" style="margin-top: 20px">
              <el-table-column label="物料名称" align="center" prop="name">
              </el-table-column>
              <el-table-column label="规格型号" align="center" prop="modelNumber">
              </el-table-column>
              <el-table-column label="数量" align="center" prop="quantity">
              </el-table-column>
              <el-table-column label="单位" align="center" prop="unit">
              </el-table-column>
              <el-table-column label="可承接量" align="center" prop="capacity">
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div style="margin-top: 41px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">交付要求</div>
          </div>
          <div style="margin-top: 22px">
            <el-descriptions class="margin-top" title="" :column="2" border>
              <!-- <el-descriptions-item>
                <template slot="label"> 压力 </template>
60Mpa
</el-descriptions-item>
<el-descriptions-item>
  <template slot="label"> 温度 </template>
  18100000000
</el-descriptions-item>
<el-descriptions-item>
  <template slot="label"> 尺寸 </template>
  3.5m
</el-descriptions-item> -->
              <!-- <el-descriptions-item>
                <template slot="label"> 规格型号 </template>
                T-565487
              </el-descriptions-item> -->
              <el-descriptions-item>
                <template slot="label"> 拦标价 </template>
                <!-- {{ detailsData.deliveryAddress }} -->
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 是否允许接单 </template>
                是
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 交付时间 </template>
                <!-- 2025-12-05 -->
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 需求截至时间： </template>
                {{ detailsData.deadline }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </div>
        <div style="margin-top: 41px">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">基本信息</div>
          </div>
          <div style="margin-top: 22px">
            <el-descriptions class="margin-top" title="" :column="2" border>
              <el-descriptions-item>
                <template slot="label"> 需求企业 </template>
                {{ detailsData.demandCompany }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 联系人 </template>
                {{ detailsData.contactPerson }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 联系电话 </template>
                {{ detailsData.contactPhone }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 交货地址 </template>
                {{ detailsData.deliveryAddress }}
              </el-descriptions-item>
              <el-descriptions-item :span="2">
                <template slot="label"> 交货要求 </template>
                {{ detailsData.fileRequirement }}
              </el-descriptions-item>
              <!-- <el-descriptions-item>
                <template slot="label"> 开户行 </template>
                {{ detailsData.bankName }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label"> 付款账号 </template>
                {{ detailsData.paymentAccount }}
              </el-descriptions-item> -->
            </el-descriptions>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { orderDetailData } from "@/api/manufacturingSharing";

export default {
  name: "deviceDetail",
  data() {
    return {
      detailsData: {},
    };
  },
  created() {
    this.id = this.$route.query.id;
    this.getDetailData();
  },
  methods: {
    getDetailData() {
      orderDetailData(this.id).then((res) => {
        if (res.code === 200) {
          this.detailsData = res.data;
        }
      });
    },
    takeOrder(id) {
      this.$router.push("/receiveOrder"); // 传id
    },
    jumpIntention() {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => { });
        return;
      } else {
        this.$router.push(`/receiveOrder?demandName=${this.detailsData.demandCompany}&updateTime=${this.detailsData.updateTime}&intentionType=12&fieldName=生成订单&intentionId=${this.detailsData.id}`);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  background-color: #f2f2f2;
  padding: 30px 0 61px 0;
}

.cardStyle {
  height: 100%;
  // height: 660px;
  background-color: #ffffff;
  padding: 60px 56px 54px 50px;
  display: flex;
}

.card_left {
  .card_left_bottom {
    .title {
      font-family: Source Han Sans CN;
      font-weight: 500;
      font-size: 20px;
      color: #222222;
      margin-bottom: 25px;
    }

    .everyOption {
      display: flex;
      align-items: center;
      margin-top: 12px;

      .optionName {
        // height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #999999;
      }

      .optionValue {
        // height: 14px;
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
      }
    }

    .buttonStyle {
      margin-top: 32px;
      // margin-left: 55px;
      width: 220px;
      height: 50px;
      background: #21c9b8;
      box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);
      border-radius: 2px;
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
    }
  }
}

.card_center_line {
  width: 1px;
  height: 660px;
  background: #e1e1e1;
  margin-left: 60px;
  margin-right: 61px;
}

.card_right {
  width: 100%;

  // overflow-y: auto;
  .content_title {
    display: flex;
    align-items: center;

    .icon {
      width: 4px;
      height: 20px;
      background: #21c9b8;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #030a1a;
      margin-left: 10px;
    }
  }
}
</style>
