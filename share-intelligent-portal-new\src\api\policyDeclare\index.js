import request from "@/utils/request";

// 政策申报列表
export function getPolicyDeclareList({
  text,
  type,
  releaseId,
  policyStatus,
  labelCodeList,
  pageNum,
  pageSize,
}) {
  return request({
    url: `/system/policy/listByText?pageNum=${pageNum}&pageSize=${pageSize}`,
    method: "post",
    data: {
      text,
      type,
      releaseId,
      policyStatus,
      labelCodeList,
    },
  });
}

// 政策申报--查询发布单位列表
export function getListByName() {
  return request({
    url: "/system/orgInfo/listByName",
    method: "get",
  });
}

// 政策申报--政策详情
export function getPolicyDeclareDetail(params) {
  return request({
    url: "/system/policy/getInfo",
    method: "get",
    params,
  });
}
