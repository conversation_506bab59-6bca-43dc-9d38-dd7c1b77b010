<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSO服务端口检测工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; }
        .port-item { padding: 10px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
        .port-success { background-color: #d4edda; border-color: #c3e6cb; }
        .port-error { background-color: #f8d7da; border-color: #f5c6cb; }
        .loading { color: #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 SSO服务端口检测工具</h1>
        
        <div class="test-section info">
            <h3>📋 使用说明</h3>
            <p>此工具将自动检测常用端口上的SSO服务，帮助您找到正确的服务地址。</p>
        </div>

        <div class="test-section">
            <h3>🚀 自动检测</h3>
            <button onclick="detectAllPorts()">开始检测所有端口</button>
            <button onclick="clearResults()">清空结果</button>
            <div id="detectionResults" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🎯 手动测试</h3>
            <input type="text" id="customPort" placeholder="输入端口号" style="width: 150px; padding: 8px;">
            <button onclick="testCustomPort()">测试端口</button>
            <div id="customResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 检测结果汇总</h3>
            <div id="summary" class="result"></div>
        </div>
    </div>

    <script>
        const commonPorts = [
            { port: 9100, name: 'SSO认证服务', path: '/sso/status' },
            { port: 9200, name: '主系统认证服务', path: '/sso/status' },
            { port: 9700, name: '从系统认证服务', path: '/sso/status' }
        ];

        let detectionResults = [];

        async function testPort(port, name, path) {
            const url = `http://localhost:${port}${path}`;
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    timeout: 3000,
                    credentials: 'include'
                });
                
                const result = {
                    port,
                    name,
                    url,
                    status: response.status,
                    success: response.ok,
                    timestamp: new Date().toLocaleTimeString()
                };

                if (response.ok) {
                    try {
                        const data = await response.json();
                        result.data = data;
                    } catch (e) {
                        result.data = 'Non-JSON response';
                    }
                }

                return result;
            } catch (error) {
                return {
                    port,
                    name,
                    url,
                    success: false,
                    error: error.message,
                    timestamp: new Date().toLocaleTimeString()
                };
            }
        }

        async function detectAllPorts() {
            const resultsDiv = document.getElementById('detectionResults');
            resultsDiv.innerHTML = '<div class="loading">🔍 正在检测服务端口...</div>';
            
            detectionResults = [];
            
            for (const portInfo of commonPorts) {
                const resultDiv = document.createElement('div');
                resultDiv.className = 'port-item loading';
                resultDiv.innerHTML = `🔍 检测 ${portInfo.name} (${portInfo.port}) ...`;
                resultsDiv.appendChild(resultDiv);
                
                const result = await testPort(portInfo.port, portInfo.name, portInfo.path);
                detectionResults.push(result);
                
                if (result.success) {
                    resultDiv.className = 'port-item port-success';
                    resultDiv.innerHTML = `
                        ✅ <strong>${result.name}</strong> - 端口 ${result.port}<br>
                        <small>URL: <a href="${result.url}" target="_blank">${result.url}</a></small><br>
                        <small>状态: ${result.status} | 时间: ${result.timestamp}</small>
                    `;
                } else {
                    resultDiv.className = 'port-item port-error';
                    resultDiv.innerHTML = `
                        ❌ <strong>${result.name}</strong> - 端口 ${result.port}<br>
                        <small>URL: ${result.url}</small><br>
                        <small>错误: ${result.error || '服务不可用'} | 时间: ${result.timestamp}</small>
                    `;
                }
            }
            
            updateSummary();
        }

        async function testCustomPort() {
            const port = document.getElementById('customPort').value;
            if (!port) {
                alert('请输入端口号');
                return;
            }
            
            const resultDiv = document.getElementById('customResult');
            resultDiv.innerHTML = '<div class="loading">🔍 正在测试端口...</div>';
            
            const result = await testPort(port, '自定义端口', '/sso/status');
            
            if (result.success) {
                resultDiv.innerHTML = `
                    <div class="port-success">
                        ✅ 端口 ${port} 可访问<br>
                        <small>URL: <a href="${result.url}" target="_blank">${result.url}</a></small><br>
                        <small>状态: ${result.status}</small>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="port-error">
                        ❌ 端口 ${port} 不可访问<br>
                        <small>错误: ${result.error || '服务不可用'}</small>
                    </div>
                `;
            }
        }

        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            const successfulPorts = detectionResults.filter(r => r.success);
            const failedPorts = detectionResults.filter(r => !r.success);
            
            let summaryHTML = `
                <h4>📈 检测汇总</h4>
                <p>✅ 可用服务: ${successfulPorts.length} 个</p>
                <p>❌ 不可用服务: ${failedPorts.length} 个</p>
            `;
            
            if (successfulPorts.length > 0) {
                summaryHTML += '<h5>🎯 可用的SSO服务:</h5><ul>';
                successfulPorts.forEach(result => {
                    if (result.url.includes('/sso/status')) {
                        summaryHTML += `<li><strong>${result.name}</strong>: <a href="${result.url}" target="_blank">${result.url}</a></li>`;
                    }
                });
                summaryHTML += '</ul>';
            }
            
            summaryDiv.innerHTML = summaryHTML;
        }

        function clearResults() {
            document.getElementById('detectionResults').innerHTML = '';
            document.getElementById('customResult').innerHTML = '';
            document.getElementById('summary').innerHTML = '';
            detectionResults = [];
        }

        // 页面加载时自动开始检测
        window.onload = function() {
            setTimeout(() => {
                detectAllPorts();
            }, 1000);
        };
    </script>
</body>
</html>
