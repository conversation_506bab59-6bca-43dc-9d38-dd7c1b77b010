<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DeviceInfoMapper">

    <resultMap type="DeviceInfo" id="DeviceInfoResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="category"    column="category"    />
        <result property="specifications"    column="specifications"    />
        <result property="location"    column="location"    />
        <result property="description"    column="description"    />
        <result property="images"    column="images"    />
        <result property="rentMode"    column="rent_mode"    />
        <result property="rentPrice"    column="rent_price"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="pressure"    column="pressure"    />
        <result property="temperature"    column="temperature"    />
        <result property="dimension"    column="dimension"    />
        <result property="modelNumber"    column="model_number"    />
        <result property="belongingCompany"    column="belonging_company"    />
        <result property="checkStatus"    column="check_status"    />
    </resultMap>

    <sql id="selectDeviceInfoVo">
        select id, name, category, specifications, location, description, images, rent_mode, rent_price, create_time, update_time, pressure, temperature, dimension, model_number,  belonging_company, check_status,create_by, update_by from device_info
    </sql>

    <select id="selectDeviceInfoList" parameterType="DeviceInfo" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo"/>
        <where>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="category != null  and category != ''">and category = #{category}</if>
            <if test="specifications != null  and specifications != ''">and specifications = #{specifications}</if>
            <if test="location != null  and location != ''">and location = #{location}</if>
            <if test="description != null  and description != ''">and description = #{description}</if>
            <if test="images != null  and images != ''">and images = #{images}</if>
            <if test="rentMode != null  and rentMode != ''">and rent_mode = #{rentMode}</if>
            <if test="rentPrice != null ">and rent_price = #{rentPrice}</if>
            <if test="pressure != null  and pressure != ''">and pressure = #{pressure}</if>
            <if test="temperature != null  and temperature != ''">and temperature = #{temperature}</if>
            <if test="dimension != null  and dimension != ''">and dimension = #{dimension}</if>
            <if test="modelNumber != null  and modelNumber != ''">and model_number = #{modelNumber}</if>
            <if test="belongingCompany != null  and belongingCompany != ''">and belonging_company =
                #{belongingCompany}
            </if>
            <if test="createBy != null  and createBy != ''">and create_by = #{createBy}</if>
            <if test="updateBy != null  and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="checkStatus != null  and checkStatus != ''">and check_status = #{checkStatus}</if>
        </where>
        order by id desc
    </select>

    <select id="selectDeviceInfoById" parameterType="Long" resultMap="DeviceInfoResult">
        <include refid="selectDeviceInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertDeviceInfo" parameterType="DeviceInfo" useGeneratedKeys="true" keyProperty="id">
        insert into device_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="category != null and category != ''">category,</if>
            <if test="specifications != null">specifications,</if>
            <if test="location != null and location != ''">location,</if>
            <if test="description != null">description,</if>
            <if test="images != null">images,</if>
            <if test="rentMode != null">rent_mode,</if>
            <if test="rentPrice != null">rent_price,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="pressure != null">pressure,</if>
            <if test="temperature != null">temperature,</if>
            <if test="dimension != null">dimension,</if>
            <if test="modelNumber != null">model_number,</if>
            <if test="belongingCompany != null">belonging_company,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="checkStatus != null">check_status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="category != null and category != ''">#{category},</if>
            <if test="specifications != null">#{specifications},</if>
            <if test="location != null and location != ''">#{location},</if>
            <if test="description != null">#{description},</if>
            <if test="images != null">#{images},</if>
            <if test="rentMode != null">#{rentMode},</if>
            <if test="rentPrice != null">#{rentPrice},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="pressure != null">#{pressure},</if>
            <if test="temperature != null">#{temperature},</if>
            <if test="dimension != null">#{dimension},</if>
            <if test="modelNumber != null">#{modelNumber},</if>
            <if test="belongingCompany != null">#{belongingCompany},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
        </trim>
    </insert>

    <update id="updateDeviceInfo" parameterType="DeviceInfo">
        update device_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="category != null and category != ''">category = #{category},</if>
            <if test="specifications != null">specifications = #{specifications},</if>
            <if test="location != null and location != ''">location = #{location},</if>
            <if test="description != null">description = #{description},</if>
            <if test="images != null">images = #{images},</if>
            <if test="rentMode != null">rent_mode = #{rentMode},</if>
            <if test="rentPrice != null">rent_price = #{rentPrice},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="pressure != null">pressure = #{pressure},</if>
            <if test="temperature != null">temperature = #{temperature},</if>
            <if test="dimension != null">dimension = #{dimension},</if>
            <if test="modelNumber != null">model_number = #{modelNumber},</if>
            <if test="belongingCompany != null">belonging_company = #{belongingCompany},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceInfoById" parameterType="Long">
        delete from device_info where id = #{id}
    </delete>

    <delete id="deleteDeviceInfoByIds" parameterType="String">
        delete from device_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
