{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\notice\\detail\\index.vue?vue&type=style&index=0&id=f87f3f46&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\notice\\detail\\index.vue", "mtime": 1750311963066}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5hcHAtY29udGFpbmVyIHsNCiAgYmFja2dyb3VuZDogI2Y0ZjVmOTsNCiAgLm5vdGljZS1yZWNvcmQtZGV0YWlsIHsNCiAgICAuaW5mby1jb250YWluZXIgew0KICAgICAgd2lkdGg6IDEwMCU7DQogICAgICBoZWlnaHQ6IDUwMHB4Ow0KICAgICAgcGFkZGluZy10b3A6IDEycHg7DQoNCiAgICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlOw0KICAgICAgLmhlYWRlciB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDMwcHg7DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICAgICAgLmVsLWJ1dHRvbiB7DQogICAgICAgICAgaGVpZ2h0OiA0MHB4Ow0KICAgICAgICAgIGJvcmRlci1jb2xvcjogdHJhbnNwYXJlbnQ7DQogICAgICAgICAgcGFkZGluZzogMTBweCAxMHB4IDEwcHggMjBweDsNCiAgICAgICAgICBmb250LXNpemU6IDIwcHg7DQogICAgICAgICAgY29sb3I6ICMwMDA7DQogICAgICAgIH0NCiAgICAgICAgLmVsLWJ1dHRvbjpob3ZlciB7DQogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7DQogICAgICAgIH0NCiAgICAgICAgLmhlYWRlci10ZXh0IHsNCiAgICAgICAgICBsaW5lLWhlaWdodDogNDBweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLmRlbGV0ZS1idG4gew0KICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6FA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/notice/detail", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-21 14:03:04\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"notice-record-detail\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"info-container\">\r\n            <div class=\"header\">\r\n              <el-button icon=\"el-icon-arrow-left\" @click=\"goBack\"></el-button>\r\n              <div class=\"header-text\">消息详情</div>\r\n            </div>\r\n            <el-form ref=\"form\" :model=\"form\" label-width=\"120px\">\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"资源类型:\" prop=\"policyTitle\">\r\n                  {{ form.resourceTypeName || \"--\" }}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"资源标题:\" prop=\"publishCompany\">\r\n                  {{ form.resourceTitle || \"--\" }}\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"资源描述:\" prop=\"policyType\">\r\n                  {{ form.resourceDescribe || \"--\" }}</el-form-item\r\n                >\r\n              </el-col>\r\n              <el-col :span=\"24\">\r\n                <el-form-item label=\"联系人:\" prop=\"policyType\">\r\n                  {{ form.contactPhone || \"--\" }}</el-form-item\r\n                >\r\n              </el-col>\r\n            </el-form>\r\n            <div class=\"delete-btn\">\r\n              <el-button type=\"danger\" @click=\"deleteInfo\">删除</el-button>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../../components/userMenu.vue\";\r\nimport { deleteInfo, getInfoDetail } from \"@/api/system/info\";\r\nexport default {\r\n  name: \"Notice\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      form: {},\r\n    };\r\n  },\r\n  created() {\r\n    this.getDetail();\r\n  },\r\n  methods: {\r\n    getDetail() {\r\n      let userId = this.$route.query.id;\r\n      getInfoDetail(userId).then((response) => {\r\n        this.form = response.data;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    goBack() {\r\n      this.$router.go(-1);\r\n    },\r\n    deleteInfo(row) {\r\n      this.$confirm(\"是否确认删除该消息？\", { type: \"error\" })\r\n        .then((_) => {\r\n          deleteInfo({ ids: this.$route.query.id }).then((response) => {\r\n            if (response.code == 200) {\r\n              this.$router.go(-1);\r\n            }\r\n          });\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .notice-record-detail {\r\n    .info-container {\r\n      width: 100%;\r\n      height: 500px;\r\n      padding-top: 12px;\r\n\r\n      background-color: white;\r\n      .header {\r\n        display: flex;\r\n        margin-bottom: 30px;\r\n        text-align: center;\r\n        .el-button {\r\n          height: 40px;\r\n          border-color: transparent;\r\n          padding: 10px 10px 10px 20px;\r\n          font-size: 20px;\r\n          color: #000;\r\n        }\r\n        .el-button:hover {\r\n          background-color: white;\r\n        }\r\n        .header-text {\r\n          line-height: 40px;\r\n        }\r\n      }\r\n      .delete-btn {\r\n        width: 100%;\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}