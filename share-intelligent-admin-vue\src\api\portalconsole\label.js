import request from '@/utils/request'

// 查询政策标签列表
export function listLabel(query) {
  return request({
    url: '/portalconsole/policyLabel/list',
    method: 'get',
    params: query
  })
}

// 查询政策标签详细
export function getLabel(policyLabelId) {
  return request({
    url: '/portalconsole/policyLabel/' + policyLabelId,
    method: 'get'
  })
}

// 新增政策标签
export function addLabel(data) {
  return request({
    url: '/portalconsole/policyLabel',
    method: 'post',
    data: data
  })
}

// 修改政策标签
export function updateLabel(data) {
  return request({
    url: '/portalconsole/policyLabel',
    method: 'put',
    data: data
  })
}

// 删除政策标签
export function delLabel(policyLabelId) {
  return request({
    url: '/portalconsole/policyLabel/' + policyLabelId,
    method: 'delete'
  })
}
