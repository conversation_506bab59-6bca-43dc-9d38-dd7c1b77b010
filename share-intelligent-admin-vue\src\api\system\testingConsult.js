import request from '@/utils/request'

// 查询检测咨询列表
export function listTestingConsult(query) {
  return request({
    url: '/system/testingConsult/list',
    method: 'get',
    params: query
  })
}

// 查询检测咨询详细
export function getTestingConsult(id) {
  return request({
    url: '/system/testingConsult/' + id,
    method: 'get'
  })
}

// 新增检测咨询
export function addTestingConsult(data) {
  return request({
    url: '/system/testingConsult',
    method: 'post',
    data: data
  })
}

// 修改检测咨询
export function updateTestingConsult(data) {
  return request({
    url: '/system/testingConsult',
    method: 'put',
    data: data
  })
}

// 删除检测咨询
export function delTestingConsult(id) {
  return request({
    url: '/system/testingConsult/' + id,
    method: 'delete'
  })
}
