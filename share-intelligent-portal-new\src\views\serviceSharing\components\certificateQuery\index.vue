<template>
  <div class="main">
    <div class="card-container content">
      <div class="left">
        <div class="content_title">
          <div class="icon"></div>
          <div class="title">技能鉴定证书查询</div>
        </div>
        <el-form ref="form" :rules="rules" :model="form" label-position="top">
          <el-form-item label="" prop="name">
            <el-input v-model="form.name" placeholder="请输入您的姓名" prefix-icon="el-icon-user-solid"></el-input>
          </el-form-item>
          <el-form-item label="" prop="idCard">
            <el-input v-model="form.idCard" placeholder="请输入您的身份证号码 " prefix-icon="el-icon-lock"
              show-password></el-input>
          </el-form-item>
          <el-form-item class="footer-submit">
            <el-button style="width: 100%; height: 50px" type="primary" @click="onSubmit">提交</el-button>
          </el-form-item>
          <el-form-item>
            <span class="tips">可查询内容：复合材料技能鉴定证书</span>
          </el-form-item>
        </el-form>
      </div>
      <div class="right">
        <div class="describe">
          1、输入左侧姓名+身份证号即可查询
        </div>
        <div class="describe">
          2、本平台可查询由易复材共享智造平台颁发的技能鉴定证书
        </div>
        <div class="describe">
          3、因考核信息可能存在延迟情况，如未查询到结果或对查询结果若有异议，
          可联系官方客服：<span class="phone">0318-8234488</span>
        </div>
      </div>
    </div>
  </div>

</template>

<script>

export default {
  data() {
    return {
      form: {
        name: '',
        idCard: '',
      },
      rules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
        ],
        idCard: [
          { required: true, message: '请输入11位身份证号码', trigger: 'blur' },
        ],
      },
    };
  },
  methods: {
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$router.push(`/certificateQueryResult?name=${this.form.name}&idCard=${this.form.idCard}`)
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100vh;
  background-color: #F2F2F2;
  padding: 20px;
  box-sizing: border-box;
}

.content {
  background-color: #fff;
  margin-top: 40px;
  padding: 40px;
  box-sizing: border-box;
  height: 530px;
  display: flex;
  width: 100%;
}

.left {
  width: 50%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;

  .content_title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .icon {
      width: 4px;
      height: 20px;
      background: #21c9b8;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #030a1a;
      margin-left: 10px;
    }
  }

  .footer-submit {
    margin-top: 40px;
  }

  .tips {
    font-weight: 400;
    font-size: 16px;
    color: #7E7E7E;
  }
}

.right {
  width: 50%;
  height: 100%;
  background-color: #fff;
  padding: 20px;
  box-sizing: border-box;
  padding-top: 54px;

  .describe {
    font-weight: 400;
    font-size: 16px;
    color: #666666;
    // margin-bottom: 20px;
    line-height: 30px;

    .phone {
      color: #21C9B8;
    }
  }
}
</style>