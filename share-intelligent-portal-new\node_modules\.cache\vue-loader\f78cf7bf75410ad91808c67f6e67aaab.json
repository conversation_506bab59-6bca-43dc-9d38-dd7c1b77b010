{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\invoiceInfo\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\invoiceInfo\\index.vue", "mtime": 1750311963060}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBpbnZvaWNlTGlzdCwgaW52b2ljZUFkZCwgaW52b2ljZUVkaXQgfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7DQppbXBvcnQgc3RvcmUgZnJvbSAiQC9zdG9yZSI7DQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHVzZXJBdmF0YXIgZnJvbSAiLi4vcHJvZmlsZS91c2VyQXZhdGFyIjsNCmltcG9ydCB1c2VySW5mbyBmcm9tICIuLi9wcm9maWxlL3VzZXJJbmZvIjsNCmltcG9ydCB7IHVwbG9hZFVybCB9IGZyb20gIkAvYXBpL29zcyI7DQppbXBvcnQgeyBnZXRUb2tlbiB9IGZyb20gIkAvdXRpbHMvYXV0aCI7DQppbXBvcnQgew0KICBnZXRVc2VySW5mbywNCiAgdXBkYXRlVXNlckluZm8sDQogIGNoZWNrQXV0aFN0YXR1cywNCiAgZ2V0Q29tcGFueUxpc3RCeU5hbWUsDQogIHRyYW5zZmVyQ29tcGFueSwNCiAgZ2V0UG9zaXRpb25EYXRhLA0KfSBmcm9tICJAL2FwaS9zeXN0ZW0vdXNlciI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlVzZXJJbmZvIiwNCiAgY29tcG9uZW50czogeyB1c2VyQXZhdGFyLCB1c2VySW5mbywgVXNlck1lbnUgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgZml0OiAiY292ZXIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBlZGl0RGlhbG9nOiBmYWxzZSwNCiAgICAgIGNvbXBhbnlEaWFsb2c6IGZhbHNlLA0KICAgICAgcG9zaXRpb25EaWFsb2c6IGZhbHNlLA0KICAgICAgYWN0aW9uVXJsOiB1cGxvYWRVcmwoKSwNCiAgICAgIGhlYWRlcnM6IHsgQXV0aG9yaXphdGlvbjogIkJlYXJlciAiICsgZ2V0VG9rZW4oKSB9LA0KICAgICAgdXNlcklkOiBzdG9yZS5nZXR0ZXJzLnVzZXJJZCwNCiAgICAgIHVzZXI6IHsNCiAgICAgICAgLy8gbmFtZTogc3RvcmUuZ2V0dGVycy5uYW1lLA0KICAgICAgICAvLyBhdmF0YXI6IHN0b3JlLmdldHRlcnMuYXZhdGFyLA0KICAgICAgICAvLyBpZDogc3RvcmUuZ2V0dGVycy51c2VySWQsDQogICAgICB9LA0KICAgICAgZm9ybTogew0KICAgICAgICBoZWFkVHlwZTogIjEiLA0KICAgICAgICBpbnZvaWNlVHlwZTogMSwNCiAgICAgICAgY29tcGFueU5hbWU6ICIiLA0KICAgICAgICBkdXR5UGFyYWdyYXBoOiAiIiwNCiAgICAgICAgYWRkcmVzczogIiIsDQogICAgICAgIHBob25lOiAiIiwNCiAgICAgICAgb3BlbkFjY291bnQ6ICIiLA0KICAgICAgICBiYW5rQWNjb3VudDogIiIsDQogICAgICAgIGVtYWlsOiAiIiwNCiAgICAgIH0sDQogICAgICBydWxlczoge30sDQogICAgICBjb21wYW55T3B0aW9uczogW10sDQogICAgICBwb3NpdGlvbk9wdGlvbnM6IFtdLA0KICAgICAgY29tcGFueUxvYWRpbmc6IGZhbHNlLA0KICAgICAgY29tcGFueUlkOiAiIiwNCiAgICAgIGNvbXBhbnlOYW1lOiAiIiwNCiAgICAgIGNvbXBhbnlDb2RlOiAiIiwNCiAgICAgIHBlcnNvbmFsU3RhdHVzOiAiIiwNCiAgICAgIGNvbXBhbnlTdGF0dXM6ICIiLA0KICAgICAgcG9zaXRpb25GaXJzdERhdGE6IFtdLA0KICAgICAgYWN0aXZlRmlyc3RJbmRleDogMCwNCiAgICAgIHBvc2l0aW9uQ2hpbGREYXRhOiBbXSwNCiAgICAgIHNlbGVjdFBvc2l0aW9uRGF0YToge30sDQogICAgICBpZGVudGl0eVR5cGU6ICIiLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgLy8gdGhpcy5nZXRVc2VyKCk7DQogICAgLy8gdGhpcy5nZXRQb3NpdGlvbkRhdGEoKTsNCiAgICAvLyB0aGlzLmlzUmVsZXZhbmNlQ29tcGFueSgpOw0KICAgIHRoaXMuZ2V0SW52b2ljZURhdGEoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGdldEludm9pY2VEYXRhKCkgew0KICAgICAgaW52b2ljZUxpc3QoKS50aGVuKChyZXMpID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICBjb25zb2xlLmxvZyhyZXMsICItLS0tLS0tLS0tLS0iKTsNCiAgICAgICAgICB0aGlzLnVzZXIgPSByZXMuZGF0YSA/IHJlcy5kYXRhIDoge307DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgaXNSZWxldmFuY2VDb21wYW55KCkgew0KICAgICAgbGV0IGZsYWcgPSB0aGlzLiRyb3V0ZS5xdWVyeS5yZWxldmFuY2VDb21wYW55Ow0KICAgICAgaWYgKGZsYWcgPT0gIjEiKSB7DQogICAgICAgIHRoaXMub3BlbkNvbWFwbnlEaWFsb2coKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGNhbmNlbEVkaXREaWFsb2coKSB7DQogICAgICB0aGlzLmVkaXREaWFsb2cgPSBmYWxzZTsNCiAgICAgIHRoaXMuZm9ybSA9IHt9Ow0KICAgIH0sDQogICAgb3BlbkVkaXREaWFsb2coKSB7DQogICAgICBpZiAodGhpcy51c2VyLmlkKSB7DQogICAgICAgIHRoaXMuZm9ybSA9IHsgLi4udGhpcy51c2VyIH07DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgICAgaGVhZFR5cGU6ICIxIiwNCiAgICAgICAgICBpbnZvaWNlVHlwZTogMSwNCiAgICAgICAgICBjb21wYW55TmFtZTogIiIsDQogICAgICAgICAgZHV0eVBhcmFncmFwaDogIiIsDQogICAgICAgICAgYWRkcmVzczogIiIsDQogICAgICAgICAgcGhvbmU6ICIiLA0KICAgICAgICAgIG9wZW5BY2NvdW50OiAiIiwNCiAgICAgICAgICBiYW5rQWNjb3VudDogIiIsDQogICAgICAgICAgZW1haWw6ICIiLA0KICAgICAgICB9Ow0KICAgICAgfQ0KICAgICAgdGhpcy5lZGl0RGlhbG9nID0gdHJ1ZTsNCiAgICB9LA0KICAgIGNhbmNlbENvbWFwbnlEaWFsb2coKSB7DQogICAgICB0aGlzLmNvbXBhbnlEaWFsb2cgPSBmYWxzZTsNCiAgICAgIHRoaXMuY29tcGFueUNvZGUgPSAiIjsNCiAgICAgIHRoaXMuY29tcGFueUlkID0gIiI7DQogICAgfSwNCiAgICBvcGVuQ29tYXBueURpYWxvZygpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsgLi4udGhpcy51c2VyIH07DQogICAgICB0aGlzLmNvbXBhbnlEaWFsb2cgPSB0cnVlOw0KICAgIH0sDQogICAgY2FuY2VsUG9zaXRpb25EaWFsb2coKSB7DQogICAgICB0aGlzLnNlbGVjdFBvc2l0aW9uRGF0YSA9IHt9Ow0KICAgICAgdGhpcy5pZGVudGl0eVR5cGUgPSAiIjsNCiAgICAgIHRoaXMucG9zaXRpb25EaWFsb2cgPSBmYWxzZTsNCiAgICB9LA0KICAgIG9wZW5Qb3NpdGlvbkRpYWxvZygpIHsNCiAgICAgIHRoaXMuc2VsZWN0UG9zaXRpb25EYXRhID0ge307DQogICAgICB0aGlzLmlkZW50aXR5VHlwZSA9ICIiOw0KICAgICAgdGhpcy5wb3NpdGlvbkRpYWxvZyA9IHRydWU7DQogICAgfSwNCiAgICBzdWJtaXRDb21wYW55Rm9ybSgpIHt9LA0KICAgIGdldFVzZXIoKSB7DQogICAgICBnZXRVc2VySW5mbyh0aGlzLnVzZXJJZCkudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgdGhpcy51c2VyID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5yb2xlR3JvdXAgPSByZXNwb25zZS5yb2xlR3JvdXA7DQogICAgICAgIHRoaXMucG9zdEdyb3VwID0gcmVzcG9uc2UucG9zdEdyb3VwOw0KICAgICAgfSk7DQogICAgICBjaGVja0F1dGhTdGF0dXMoKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnBlcnNvbmFsU3RhdHVzID0gcmVzcG9uc2UuZGF0YS5wZXJzb25hbFN0YXR1czsNCiAgICAgICAgdGhpcy5jb21wYW55U3RhdHVzID0gcmVzcG9uc2UuZGF0YS5jb21wYW55U3RhdHVzOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBnZXRQb3NpdGlvbkRhdGEoKSB7DQogICAgICBnZXRQb3NpdGlvbkRhdGEoKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICByZXNwb25zZS5kYXRhLmZvckVhY2goKGl0ZW0pID0+IHsNCiAgICAgICAgICB0aGlzLnBvc2l0aW9uRmlyc3REYXRhLnB1c2goaXRlbSk7DQogICAgICAgIH0pOw0KICAgICAgICB0aGlzLnBvc2l0aW9uQ2hpbGREYXRhID0gcmVzcG9uc2UuZGF0YVswXS5jaGlsZHJlbjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgc2VsZWN0RmlzcnQoaW5kZXgsIGRhdGEpIHsNCiAgICAgIHRoaXMuYWN0aXZlRmlyc3RJbmRleCA9IGluZGV4Ow0KICAgICAgdGhpcy5wb3NpdGlvbkNoaWxkRGF0YSA9IGRhdGEuY2hpbGRyZW47DQogICAgfSwNCiAgICBzZWxlY3RQb3NpdGlvbihwb3NpdGlvbikgew0KICAgICAgdGhpcy5zZWxlY3RQb3NpdGlvbkRhdGEgPSBwb3NpdGlvbjsNCiAgICAgIHRoaXMuaWRlbnRpdHlUeXBlID0gcG9zaXRpb24ucG9zdE5hbWU7DQogICAgfSwNCiAgICBjb21wYW55Q2hhbmdlZChyZXMpIHsNCiAgICAgIHRoaXMuY29tcGFueU9wdGlvbnMuZm9yRWFjaCgoaXRlbSkgPT4gew0KICAgICAgICBpZiAoaXRlbS5pZCA9PSByZXMpIHsNCiAgICAgICAgICB0aGlzLmNvbXBhbnlDb2RlID0gaXRlbS5jcmVkaXRDb2RlOw0KICAgICAgICAgIHRoaXMuY29tcGFueUlkID0gaXRlbS5pZDsNCiAgICAgICAgICB0aGlzLmNvbXBhbnlOYW1lID0gaXRlbS5uYW1lOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIGNoYW5nZVBvc2l0aW9uKCkgew0KICAgICAgaWYgKHRoaXMuaWRlbnRpdHlUeXBlKSB7DQogICAgICAgIHRoaXMuZm9ybS5pZGVudGl0eVR5cGUgPSB0aGlzLmlkZW50aXR5VHlwZTsNCiAgICAgICAgdGhpcy5jYW5jZWxQb3NpdGlvbkRpYWxvZygpOw0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0Q29tcGFueUxpc3QocXVlcnkpIHsNCiAgICAgIGlmIChxdWVyeSAhPT0gIiIpIHsNCiAgICAgICAgZ2V0Q29tcGFueUxpc3RCeU5hbWUocXVlcnkpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgdGhpcy5jb21wYW55T3B0aW9ucyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQogICAgdHJhbnNmZXJDb21wYW55KCkgew0KICAgICAgdHJhbnNmZXJDb21wYW55KHsNCiAgICAgICAgdGlhbnlhbklkOiB0aGlzLmNvbXBhbnlJZCwNCiAgICAgICAgYnVzaW5lc3NObzogdGhpcy5jb21wYW55Q29kZSwNCiAgICAgIH0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5jYW5jZWxDb21hcG55RGlhbG9nKCk7DQogICAgICAgICAgdGhpcy5mb3JtLmNvbXBhbnlOYW1lID0gdGhpcy5jb21wYW55TmFtZTsNCiAgICAgICAgICB0aGlzLmZvcm0uYnVzc2luZXNzTm8gPSB0aGlzLmNvbXBhbnlDb2RlOw0KICAgICAgICAgIGxldCBmbGFnID0gdGhpcy4kcm91dGUucXVlcnkucmVsZXZhbmNlQ29tcGFueTsNCiAgICAgICAgICBpZiAoZmxhZyA9PSAiMSIpIHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOw0KICAgICAgICAgICAgc3RvcmUuZGlzcGF0Y2goIkdldEluZm8iKTsNCiAgICAgICAgICAgIHRoaXMuZ2V0VXNlcigpOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgaWYgKHRoaXMudXNlci5pZCkgew0KICAgICAgICBsZXQgZGF0YSA9IHsNCiAgICAgICAgICBpZDogdGhpcy51c2VyLmlkLA0KICAgICAgICAgIC4uLnRoaXMuZm9ybSwNCiAgICAgICAgfTsNCiAgICAgICAgaW52b2ljZUVkaXQoZGF0YSkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgaWYgKHJlcy5jb2RlID09IDIwMCkgew0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5pON5L2c5oiQ5YqfIik7DQogICAgICAgICAgICB0aGlzLmNhbmNlbEVkaXREaWFsb2coKTsNCiAgICAgICAgICAgIHRoaXMuZ2V0SW52b2ljZURhdGEoKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaW52b2ljZUFkZCh0aGlzLmZvcm0pLnRoZW4oKHJlcykgPT4gew0KICAgICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaTjeS9nOaIkOWKnyIpOw0KICAgICAgICAgICAgdGhpcy5jYW5jZWxFZGl0RGlhbG9nKCk7DQogICAgICAgICAgICB0aGlzLmdldEludm9pY2VEYXRhKCk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVVwbG9hZFN1Y2Nlc3MocmVzLCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgLy/mraTlpITlhpnkuIrkvKBvc3PmiJDlip/kuYvlkI7nmoTpgLvovpENCiAgICAgIGlmIChyZXMuY29kZSA9PSAyMDApIHsNCiAgICAgICAgdGhpcy5mb3JtLmF2YXRhciA9IHJlcy5kYXRhLnVybDsNCiAgICAgIH0NCiAgICB9LA0KICAgIHJlc2V0KCkge30sDQogICAganVtcFRvQXBwcm92ZSgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKCIvdXNlci9hcHByb3ZlU2V0dGluZyIpOw0KICAgIH0sDQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/invoiceInfo", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-03-24 09:06:27\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"user-info-page\">\r\n    <div class=\"app-container\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu activeIndex=\"1\" />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"user-info-container\">\r\n            <div class=\"header-small\">\r\n              <div class=\"red-tag\"></div>\r\n              发票信息\r\n            </div>\r\n            <div class=\"user-info-card\">\r\n              <el-button\r\n                class=\"edit-button\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"openEditDialog\"\r\n                >点击修改</el-button\r\n              >\r\n              <!-- <el-image\r\n                class=\"user-info-avatar\"\r\n                :src=\"user.avatar\"\r\n                :fit=\"fit\"\r\n              ></el-image> -->\r\n\r\n              <!-- <div class=\"user-name\">{{ user.realName || \"--\" }}</div>\r\n              <div class=\"phone-class\">\r\n                <el-image :src=\"require('@/assets/user/phone.png')\"></el-image>\r\n                <span class=\"phone-number\">{{ user.phonenumber }}</span>\r\n              </div> -->\r\n              <div class=\"info-box\" style=\"width: 350px\">\r\n                <el-form ref=\"form\" :model=\"user\" label-width=\"80px\">\r\n                  <!-- <el-form-item label=\"抬头类型:\">\r\n                    {{ user.headType }}\r\n                  </el-form-item> -->\r\n                  <el-form-item label=\"发票类型:\">\r\n                    <span>{{\r\n                      user.invoiceType == \"1\"\r\n                        ? \"专票\"\r\n                        : user.invoiceType == \"2\"\r\n                        ? \"普票\"\r\n                        : \"\"\r\n                    }}</span>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"公司名称:\">\r\n                    {{ user.companyName }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"税号:\">\r\n                    {{ user.dutyParagraph }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"公司地址:\">\r\n                    {{ user.address }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"公司电话:\">\r\n                    {{ user.phone }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"开户银行:\">\r\n                    {{ user.openAccount }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"银行账号:\">\r\n                    {{ user.bankAccount }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"邮箱地址:\">\r\n                    {{ user.email }}\r\n                  </el-form-item>\r\n                  <!-- <el-form-item label=\"身份认证:\">\r\n                    <div class=\"tag-group\">\r\n                      <a\r\n                        class=\"label-container orange\"\r\n                        v-if=\"this.companyStatus == '1'\"\r\n                        @click=\"jumpToApprove\"\r\n                      >\r\n                        <el-image\r\n                          style=\"width: 12px; height: 12px\"\r\n                          :src=\"\r\n                            require('@/assets/user/authentication_orange.png')\r\n                          \"\r\n                        ></el-image>\r\n                        <span>企业认证</span>\r\n                      </a>\r\n                      <a\r\n                        class=\"label-container red\"\r\n                        v-if=\"personalStatus == '1'\"\r\n                        @click=\"jumpToApprove\"\r\n                      >\r\n                        <el-image\r\n                          class=\"red\"\r\n                          style=\"width: 12px; height: 12px\"\r\n                          :src=\"require('@/assets/user/authentication_red.png')\"\r\n                        ></el-image>\r\n                        <span>名片认证</span>\r\n                      </a>\r\n                      <a\r\n                        v-if=\"\r\n                          this.personalStatus == '0' &&\r\n                          this.companyStatus == '0'\r\n                        \"\r\n                        class=\"label-container\"\r\n                        @click=\"jumpToApprove\"\r\n                      >\r\n                        <el-image\r\n                          style=\"width: 12px; height: 12px\"\r\n                          :src=\"require('@/assets/user/authentication.png')\"\r\n                        ></el-image>\r\n                        <span>未认证</span>\r\n                      </a>\r\n                    </div>\r\n                  </el-form-item> -->\r\n                </el-form>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 添加或修改部门对话框 -->\r\n      <el-dialog\r\n        title=\"发票信息修改\"\r\n        :visible.sync=\"editDialog\"\r\n        width=\"750px\"\r\n        append-to-body\r\n      >\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n          <!-- <el-form-item label=\"抬头类型\" prop=\"realName\">\r\n            <el-radio v-model=\"form.headType\" label=\"1\">公司</el-radio>\r\n            <el-radio v-model=\"form.headType\" label=\"2\">个人</el-radio>\r\n          </el-form-item> -->\r\n          <el-form-item label=\"发票类型\" prop=\"realName\">\r\n            <el-radio v-model=\"form.invoiceType\" :label=\"1\">专票</el-radio>\r\n            <el-radio v-model=\"form.invoiceType\" :label=\"2\">普票</el-radio>\r\n          </el-form-item>\r\n          <el-form-item label=\"公司名称\" prop=\"phonenumber\">\r\n            <el-input v-model=\"form.companyName\" placeholder=\"请输入公司名称\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"税号\" prop=\"weixin\">\r\n            <el-input v-model=\"form.dutyParagraph\" placeholder=\"请输入税号\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"公司地址\" prop=\"email\">\r\n            <el-input v-model=\"form.address\" placeholder=\"请输入公司地址\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"公司电话\" prop=\"email\">\r\n            <el-input v-model=\"form.phone\" placeholder=\"请输入公司电话\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"开户银行\" prop=\"email\">\r\n            <el-input v-model=\"form.openAccount\" placeholder=\"请输入开户银行\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行账号\" prop=\"email\">\r\n            <el-input v-model=\"form.bankAccount\" placeholder=\"请输入银行账号\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"邮箱地址\" prop=\"email\">\r\n            <el-input v-model=\"form.email\" placeholder=\"请输入邮箱地址\" />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n          <el-button @click=\"cancelEditDialog\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n      <el-dialog\r\n        title=\"关联企业\"\r\n        :visible.sync=\"companyDialog\"\r\n        width=\"550px\"\r\n        append-to-body\r\n      >\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n          <el-form-item label=\"企业名称\">\r\n            <el-select\r\n              v-model=\"companyId\"\r\n              filterable\r\n              remote\r\n              reserve-keyword\r\n              placeholder=\"请输入关键词\"\r\n              :remote-method=\"getCompanyList\"\r\n              :loading=\"companyLoading\"\r\n              @change=\"companyChanged\"\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in companyOptions\"\r\n                :key=\"item.id\"\r\n                :label=\"item.name\"\r\n                :value=\"item.id\"\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"transferCompany\">确 定</el-button>\r\n          <el-button @click=\"cancelComapnyDialog\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n      <el-dialog\r\n        title=\"修改职务\"\r\n        :visible.sync=\"positionDialog\"\r\n        width=\"800px\"\r\n        append-to-body\r\n      >\r\n        <el-row>\r\n          <el-col :span=\"5\">\r\n            <div class=\"left-container\">\r\n              <div\r\n                v-for=\"(firstData, index) in positionFirstData\"\r\n                v-bind:key=\"firstData.postName\"\r\n                :class=\"\r\n                  index === activeFirstIndex\r\n                    ? 'position-header selected-style'\r\n                    : 'position-header'\r\n                \"\r\n              >\r\n                <a @click=\"selectFisrt(index, firstData)\">\r\n                  {{ firstData.postName || \"\" }}\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"19\">\r\n            <div class=\"right-container\">\r\n              <div\r\n                v-for=\"childData in positionChildData\"\r\n                v-bind:key=\"childData.postId\"\r\n              >\r\n                <div>\r\n                  <div class=\"second-header\">\r\n                    {{ childData.postName || \"--\" }}\r\n                  </div>\r\n                  <div class=\"position-container\">\r\n                    <a\r\n                      v-for=\"pos in childData.children\"\r\n                      v-bind:key=\"pos.postId\"\r\n                      :class=\"\r\n                        selectPositionData.postId == pos.postId\r\n                          ? 'position-tag selected'\r\n                          : 'position-tag'\r\n                      \"\r\n                      @click=\"selectPosition(pos)\"\r\n                    >\r\n                      {{ pos.postName || \"--\" }}\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"changePosition\">确 定</el-button>\r\n          <el-button @click=\"cancelPositionDialog\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { invoiceList, invoiceAdd, invoiceEdit } from \"@/api/system/user\";\r\nimport store from \"@/store\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport userAvatar from \"../profile/userAvatar\";\r\nimport userInfo from \"../profile/userInfo\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport {\r\n  getUserInfo,\r\n  updateUserInfo,\r\n  checkAuthStatus,\r\n  getCompanyListByName,\r\n  transferCompany,\r\n  getPositionData,\r\n} from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"UserInfo\",\r\n  components: { userAvatar, userInfo, UserMenu },\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      // 是否显示弹出层\r\n      editDialog: false,\r\n      companyDialog: false,\r\n      positionDialog: false,\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      userId: store.getters.userId,\r\n      user: {\r\n        // name: store.getters.name,\r\n        // avatar: store.getters.avatar,\r\n        // id: store.getters.userId,\r\n      },\r\n      form: {\r\n        headType: \"1\",\r\n        invoiceType: 1,\r\n        companyName: \"\",\r\n        dutyParagraph: \"\",\r\n        address: \"\",\r\n        phone: \"\",\r\n        openAccount: \"\",\r\n        bankAccount: \"\",\r\n        email: \"\",\r\n      },\r\n      rules: {},\r\n      companyOptions: [],\r\n      positionOptions: [],\r\n      companyLoading: false,\r\n      companyId: \"\",\r\n      companyName: \"\",\r\n      companyCode: \"\",\r\n      personalStatus: \"\",\r\n      companyStatus: \"\",\r\n      positionFirstData: [],\r\n      activeFirstIndex: 0,\r\n      positionChildData: [],\r\n      selectPositionData: {},\r\n      identityType: \"\",\r\n    };\r\n  },\r\n  created() {\r\n    // this.getUser();\r\n    // this.getPositionData();\r\n    // this.isRelevanceCompany();\r\n    this.getInvoiceData();\r\n  },\r\n  methods: {\r\n    getInvoiceData() {\r\n      invoiceList().then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res, \"------------\");\r\n          this.user = res.data ? res.data : {};\r\n        }\r\n      });\r\n    },\r\n    isRelevanceCompany() {\r\n      let flag = this.$route.query.relevanceCompany;\r\n      if (flag == \"1\") {\r\n        this.openComapnyDialog();\r\n      }\r\n    },\r\n    cancelEditDialog() {\r\n      this.editDialog = false;\r\n      this.form = {};\r\n    },\r\n    openEditDialog() {\r\n      if (this.user.id) {\r\n        this.form = { ...this.user };\r\n      } else {\r\n        this.form = {\r\n          headType: \"1\",\r\n          invoiceType: 1,\r\n          companyName: \"\",\r\n          dutyParagraph: \"\",\r\n          address: \"\",\r\n          phone: \"\",\r\n          openAccount: \"\",\r\n          bankAccount: \"\",\r\n          email: \"\",\r\n        };\r\n      }\r\n      this.editDialog = true;\r\n    },\r\n    cancelComapnyDialog() {\r\n      this.companyDialog = false;\r\n      this.companyCode = \"\";\r\n      this.companyId = \"\";\r\n    },\r\n    openComapnyDialog() {\r\n      this.form = { ...this.user };\r\n      this.companyDialog = true;\r\n    },\r\n    cancelPositionDialog() {\r\n      this.selectPositionData = {};\r\n      this.identityType = \"\";\r\n      this.positionDialog = false;\r\n    },\r\n    openPositionDialog() {\r\n      this.selectPositionData = {};\r\n      this.identityType = \"\";\r\n      this.positionDialog = true;\r\n    },\r\n    submitCompanyForm() {},\r\n    getUser() {\r\n      getUserInfo(this.userId).then((response) => {\r\n        this.user = response.data;\r\n        this.roleGroup = response.roleGroup;\r\n        this.postGroup = response.postGroup;\r\n      });\r\n      checkAuthStatus().then((response) => {\r\n        this.personalStatus = response.data.personalStatus;\r\n        this.companyStatus = response.data.companyStatus;\r\n      });\r\n    },\r\n    getPositionData() {\r\n      getPositionData().then((response) => {\r\n        response.data.forEach((item) => {\r\n          this.positionFirstData.push(item);\r\n        });\r\n        this.positionChildData = response.data[0].children;\r\n      });\r\n    },\r\n    selectFisrt(index, data) {\r\n      this.activeFirstIndex = index;\r\n      this.positionChildData = data.children;\r\n    },\r\n    selectPosition(position) {\r\n      this.selectPositionData = position;\r\n      this.identityType = position.postName;\r\n    },\r\n    companyChanged(res) {\r\n      this.companyOptions.forEach((item) => {\r\n        if (item.id == res) {\r\n          this.companyCode = item.creditCode;\r\n          this.companyId = item.id;\r\n          this.companyName = item.name;\r\n        }\r\n      });\r\n    },\r\n    changePosition() {\r\n      if (this.identityType) {\r\n        this.form.identityType = this.identityType;\r\n        this.cancelPositionDialog();\r\n      }\r\n    },\r\n    getCompanyList(query) {\r\n      if (query !== \"\") {\r\n        getCompanyListByName(query).then((response) => {\r\n          this.companyOptions = response.data;\r\n        });\r\n      }\r\n    },\r\n    transferCompany() {\r\n      transferCompany({\r\n        tianyanId: this.companyId,\r\n        businessNo: this.companyCode,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.cancelComapnyDialog();\r\n          this.form.companyName = this.companyName;\r\n          this.form.bussinessNo = this.companyCode;\r\n          let flag = this.$route.query.relevanceCompany;\r\n          if (flag == \"1\") {\r\n            this.$modal.msgSuccess(\"操作成功\");\r\n            store.dispatch(\"GetInfo\");\r\n            this.getUser();\r\n          }\r\n        }\r\n      });\r\n    },\r\n    submitForm() {\r\n      if (this.user.id) {\r\n        let data = {\r\n          id: this.user.id,\r\n          ...this.form,\r\n        };\r\n        invoiceEdit(data).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$modal.msgSuccess(\"操作成功\");\r\n            this.cancelEditDialog();\r\n            this.getInvoiceData();\r\n          }\r\n        });\r\n      } else {\r\n        invoiceAdd(this.form).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$modal.msgSuccess(\"操作成功\");\r\n            this.cancelEditDialog();\r\n            this.getInvoiceData();\r\n          }\r\n        });\r\n      }\r\n    },\r\n    handleUploadSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.avatar = res.data.url;\r\n      }\r\n    },\r\n    reset() {},\r\n    jumpToApprove() {\r\n      this.$router.push(\"/user/approveSetting\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-input {\r\n  height: 36px;\r\n  width: 380px;\r\n  line-height: 36px;\r\n}\r\n.action-class {\r\n  padding-left: 16px;\r\n  color: #21c9b8;\r\n}\r\n.el-button--primary {\r\n  color: #21c9b8;\r\n  background-color: #ffffff;\r\n  border-color: #21c9b8;\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .user-info-container {\r\n    background: #fff;\r\n    height: calc(100vh - 150px);\r\n    padding: 20px;\r\n    position: relative;\r\n\r\n    .edit-button {\r\n      position: absolute;\r\n      right: 10px;\r\n      top: 90px;\r\n      margin-left: 30px;\r\n      color: #21c9b8;\r\n      background-color: #ffffff;\r\n      border-color: transparent;\r\n    }\r\n    .user-info-avatar {\r\n      width: 120px;\r\n      height: 120px;\r\n      border-radius: 50%;\r\n    }\r\n    .header-small {\r\n      text-align: center;\r\n      display: flex;\r\n      font-size: 20px;\r\n      font-weight: 500;\r\n      color: #333333;\r\n      line-height: 20px;\r\n\r\n      .red-tag {\r\n        margin-right: 12px;\r\n        width: 3px;\r\n        height: 22px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .user-info-card {\r\n      margin-top: 80px;\r\n      margin-right: 80px;\r\n      text-align: center;\r\n      .user-name {\r\n        margin-top: 10px;\r\n        font-size: 24px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        line-height: 24px;\r\n      }\r\n      .phone-class {\r\n        width: 100%;\r\n        height: 30px;\r\n        margin-top: 12px;\r\n\r\n        .el-image {\r\n          width: 16px;\r\n          height: 16px;\r\n        }\r\n        .phone-number {\r\n          margin-left: 12px;\r\n          font-size: 16px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 16px;\r\n        }\r\n      }\r\n      .info-box {\r\n        margin: 24px auto 0 auto;\r\n        .el-form-item {\r\n          margin-bottom: 4px;\r\n          text-align: start;\r\n        }\r\n        .tag-group {\r\n          display: flex;\r\n          .label-container {\r\n            padding: 4px 12px;\r\n            margin-top: 6px;\r\n            margin-right: 6px;\r\n            height: 24px;\r\n            background: #f0f1f4;\r\n            border-radius: 4px;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            color: #8a8c94;\r\n            line-height: 12px;\r\n            .el-image {\r\n              margin: 2px 4px 0 0;\r\n            }\r\n          }\r\n          .orange {\r\n            background: rgba(255, 191, 69, 0.2);\r\n            color: #ff8a27;\r\n          }\r\n          .red {\r\n            background: rgba(197, 37, 33, 0.1);\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .position-header {\r\n      color: red;\r\n    }\r\n  }\r\n}\r\n\r\n.left-container {\r\n  height: 500px;\r\n  margin-right: 10px;\r\n  overflow-y: auto;\r\n  text-align: center;\r\n  .position-header {\r\n    height: 50px;\r\n    line-height: 50px;\r\n    font-weight: 500;\r\n\r\n    overflow-y: auto;\r\n  }\r\n  .selected-style {\r\n    background: #21c9b8;\r\n    color: #fff;\r\n  }\r\n}\r\n\r\n.right-container {\r\n  height: 500px;\r\n  overflow-y: auto;\r\n\r\n  .second-header {\r\n    height: 65px;\r\n    line-height: 65px;\r\n\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n  }\r\n  .position-container {\r\n    width: 70%;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    .position-tag {\r\n      padding: 0 20px;\r\n      font-size: 15px;\r\n      line-height: 36px;\r\n      color: #000;\r\n    }\r\n    .selected {\r\n      background: #21c9b8 !important;\r\n      color: #fff !important;\r\n    }\r\n    a:hover {\r\n      cursor: pointer;\r\n      color: #21c9b8;\r\n      text-decoration: none;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}