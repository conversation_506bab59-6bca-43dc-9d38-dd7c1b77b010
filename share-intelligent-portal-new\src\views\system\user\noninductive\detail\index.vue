<!--
 * @Author: zhc
 * @Date: 2023-02-03 11:06:49
 * @LastEditTime: 2023-03-24 08:55:04
 * @Description:
 * @LastEditors: zhc
-->
<template>
  <div class="app-container">
    <div class="notice-record-detail">
      <el-row :gutter="20">
        <el-col :span="2.5" :xs="24">
          <user-menu />
        </el-col>
        <el-col :span="18" :xs="24">
          <div class="info-container">
            <div class="header">
              <div class="header-text">兑现详情</div>
            </div>
            <div class="detail-page" v-if="isDetail">
              <div class="header-small">
                <div class="red-tag"></div>
                基本信息
              </div>

              <el-descriptions class="margin-top" :column="1" border>
                <el-descriptions-item>
                  <template slot="label"> 所属单位 </template>
                  {{ info.affiliatedUnitName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 资金来源 </template>
                  {{ info.capitalSourceName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 项目名称</template>
                  {{
                    info.itemTypeFirstName || "" + info.itemTypeSecondName || ""
                  }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 公司名称</template>
                  {{ info.companyName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 所属街道</template>
                  {{ info.affiliatedStreetName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 企业注册地址</template>
                  {{ info.companyAddress }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 企业统一代码 </template>
                  {{ info.companyCreditCode }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 开户银行</template>
                  {{ info.bankName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 银行账号</template>
                  {{ info.bankCode }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 联系人</template>
                  {{ info.contactsName }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 联系电话 </template>
                  {{ info.contactsPhone }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 申请书 </template>
                  <div v-if="!info.application">--</div>
                  <a
                    v-else
                    class="file-class"
                    @click="handleFilePreview(info.application)"
                  >
                    <el-image
                      style="width: 14px; height: 17px"
                      :src="require('@/assets/user/file_pdf.png')"
                    ></el-image>
                    {{ info.applicationName }}
                    <div class="previwe-class">立即查看</div>
                  </a>
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 开户许可证 </template>
                  <div v-if="!info.accountLicence">--</div>

                  <a
                    class="file-class"
                    v-else
                    @click="handleFilePreview(info.accountLicence)"
                  >
                    <el-image
                      style="width: 14px; height: 17px"
                      :src="require('@/assets/user/file_pdf.png')"
                    ></el-image>
                    {{ info.accountLicenceName }}
                    <div class="previwe-class">立即查看</div>
                  </a>
                  <!-- <el-upload
                    v-if="info.accountLicence"
                    class="upload-demo"
                    :on-preview="handleFilePreview(info.accountLicence)"
                  >
                  </el-upload> -->
                </el-descriptions-item>
              </el-descriptions>
              <el-image
                class="status_approving"
                v-if="info.status === '1'"
                style="width: 120px; height: 102px"
                :src="require('@/assets/user/status_approving.png')"
              ></el-image>
              <div class="delete-btn">
                <el-button @click="goBack">返回</el-button>
                <el-button
                  v-if="info.status == '0'"
                  type="danger"
                  @click="changeMode"
                  >编辑</el-button
                >
              </div>
            </div>
            <div class="edit-page" v-else>
              <el-form
                ref="form"
                :model="form"
                :rules="rules"
                label-width="120px"
              >
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="所属单位" prop="affiliatedUnitCode">
                      <el-select
                        v-model="form.affiliatedUnitCode"
                        placeholder="请选择所属单位"
                      >
                        <el-option
                          v-for="dict in dict.type.affiliated_unit"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="公司名称" prop="companyName">
                      <el-input
                        v-model="form.companyName"
                        placeholder="公司名称"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="企业统一代码" prop="companyCreditCode">
                      <el-input
                        v-model="form.companyCreditCode"
                        placeholder="请输入企业统一代码"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系人" prop="contactsName">
                      <el-input
                        v-model="form.contactsName"
                        placeholder="请输入联系人"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="资金来源" prop="capitalSourceCode">
                      <el-select
                        v-model="form.capitalSourceCode"
                        placeholder="请选择资金来源"
                      >
                        <el-option
                          v-for="dict in dict.type.capital_source"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="所属街道" prop="affiliatedStreetCode">
                      <el-select
                        v-model="form.affiliatedStreetCode"
                        placeholder="请选择所属街道"
                      >
                        <el-option
                          v-for="dict in dict.type.affiliated_street"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="开户银行" prop="bankName">
                      <el-input
                        v-model="form.bankName"
                        placeholder="请选择开户银行"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="联系电话" prop="contactsPhone">
                      <el-input
                        v-model="form.contactsPhone"
                        placeholder="请选择联系电话"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="选择项目" prop="itemTypeCode">
                      <el-cascader
                        ref="test"
                        :props="projectProps"
                        v-model="form.itemTypeCode"
                        placeholder="请选择项目"
                        @change="itemTypeChanged"
                      ></el-cascader>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="企业注册地址" prop="companyAddress">
                      <el-input
                        v-model="form.companyAddress"
                        placeholder="请输入企业注册地址"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="银行账号" prop="bankCode">
                      <el-input
                        v-model="form.bankCode"
                        placeholder="输入请银行账号"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="申请书上传" prop="application">
                      <el-upload
                        :headers="headers"
                        :action="actionUrl"
                        accept=".pdf"
                        :file-list="applicationList"
                        :on-remove="handleApplicationRemove"
                        :on-success="handleApplicationSuccess"
                        :on-exceed="handleExceedLicence"
                        :on-preview="handlePreview"
                        :limit="1"
                      >
                        <div>
                          <el-button
                            size="small"
                            type="primary"
                            icon="el-icon-upload2"
                            >上传文件</el-button
                          >
                          <span class="tip">仅限pdf格式</span>
                        </div>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="开户许可证上传" prop="accountLicence">
                      <el-upload
                        :headers="headers"
                        :action="actionUrl"
                        accept=".pdf"
                        :file-list="accountLicenceList"
                        :on-remove="handleAccountRemove"
                        :on-exceed="handleExceedLicence"
                        :on-success="handleAccountSuccess"
                        :on-preview="handlePreview"
                        :limit="1"
                      >
                        <div>
                          <el-button
                            size="small"
                            type="danger"
                            icon="el-icon-upload2"
                            >上传文件</el-button
                          >
                          <span class="tip">仅限pdf格式</span>
                        </div>
                      </el-upload>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
              <div class="delete-btn">
                <el-button @click="changeMode">返回</el-button>
                <el-button type="error" @click="changeMode(0)"
                  >暂存草稿</el-button
                >
                <el-button type="danger" @click="submitForm(1)"
                  >提交审核</el-button
                >
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
let id = 0;

import UserMenu from "../../components/userMenu.vue";
import {
  getNoninductiveDetail,
  editNoninductive,
} from "@/api/system/noninductive";
import { getDicts, getSecondDicts } from "@/api/system/dict/data.js";
import { uploadUrl } from "@/api/oss";
import { getToken } from "@/utils/auth";

export default {
  name: "Notice",
  dicts: ["affiliated_unit", "capital_source", "affiliated_street"],
  components: { UserMenu },
  data() {
    return {
      isDetail: true,
      actionUrl: uploadUrl(),
      headers: { Authorization: "Bearer " + getToken() },
      info: {},
      form: {},
      accountLicenceList: [],
      // 表单校验
      rules: {
        affiliatedUnitCode: [
          { required: true, message: "所属单位不能为空", trigger: "blur" },
        ],
        companyName: [
          { required: true, message: "公司名称不能为空", trigger: "blur" },
        ],
        companyCreditCode: [
          { required: true, message: "企业统一代码不能为空", trigger: "blur" },
        ],
        contactsName: [
          { required: true, message: "联系人不能为空", trigger: "blur" },
        ],
        capitalSourceCode: [
          { required: true, message: "资金来源不能为空", trigger: "blur" },
        ],
        affiliatedStreetCode: [
          { required: true, message: "所属街道不能为空", trigger: "blur" },
        ],
        bankName: [
          { required: true, message: "开户银行不能为空", trigger: "blur" },
        ],
        contactsPhone: [
          { required: true, message: "联系电话不能为空", trigger: "blur" },
        ],
        itemTypeCode: [
          { required: true, message: "选择项目不能为空", trigger: "blur" },
        ],
        companyAddress: [
          { required: true, message: "企业注册地址不能为空", trigger: "blur" },
        ],
        bankCode: [
          { required: true, message: "银行账号不能为空", trigger: "blur" },
        ],
        application: [
          { required: true, message: "申请书不能为空", trigger: "blur" },
        ],
        accountLicence: [
          { required: true, message: "申请书不能为空", trigger: "blur" },
        ],
      },
      projectProps: {
        lazy: true,
        lazyLoad(node, resolve) {
          let res = [];
          if (node.level == 0) {
            getDicts("item_type").then((response) => {
              response.data.forEach((element) => {
                res.push({
                  label: element.dictLabel,
                  value: element.dictValue,
                  dictCode: element.dictCode,
                });
              });
              const nodes = res;
              resolve(nodes);
            });
          } else {
            getSecondDicts({ parentCode: node.data.dictCode }).then(
              (response) => {
                response.data.forEach((element) => {
                  res.push({
                    label: element.dictLabel,
                    value: element.dictValue,
                    leaf: true,
                  });
                });
                const nodes = res;
                resolve(nodes);
              }
            );
          }
          // const nodes = Array.from({ length: level + 1 }).map((item) => ({
          //   value: ++id,
          //   label: `选项${id}`,
          //   leaf: level >= 2,
          // }));
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
        },
      },
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    itemTypeChanged(val) {
      this.form.itemTypeFirstCode = val[0];
      this.form.itemTypeSecondCode = val[1];
    },
    getDetail() {
      let userId = this.$route.query.id;
      getNoninductiveDetail(userId).then((response) => {
        this.info = response.data;
        this.total = response.total;
      });
    },
    // 申请书、开户许可证预览
    handlePreview(file) {
      window.open(file.url);
    },
    // 开户许可证上传数量限制
    handleExceedLicence(files, fileList) {
      let num = files.length + fileList.length;
      if (num >= 1) {
        this.$message.error("上传数量超过上限");
        return false;
      }
    },
    goBack() {
      this.$router.go(-1);
    },

    changeMode() {
      if (this.isDetail) {
        this.isDetail = false;
        this.form = this.info;
        this.form.itemTypeCode = [
          this.info.itemTypeFirstCode,
          this.info.itemTypeSecondCode,
        ];
        this.accountLicenceList = this.info.accountLicence
          ? [
              {
                name: this.info.accountLicenceName,
                url: this.info.accountLicence,
              },
            ]
          : [];
        this.applicationList = this.info.application
          ? [
              {
                name: this.info.applicationName,
                url: this.info.application,
              },
            ]
          : [];
      } else {
        this.isDetail = true;
        this.form = {};
        this.getDetail();
      }
    },
    handleFilePreview(file) {
      window.open(file);
    },
    submitForm(type) {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          editNoninductive({ ...this.form, isSubmit: type }).then(
            (response) => {
              this.$modal.msgSuccess("操作成功");
              this.changeMode();
            }
          );
        }
      });
    },
    handleApplicationRemove(file, fileList) {
      this.form.application = "";
    },
    handleApplicationSuccess(res, file, fileList) {
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        this.form.application = res.data.url;
        this.form.applicationName = res.data.name;
      }
    },
    handleAccountRemove(file, fileList) {
      this.form.accountLicence = "";
    },
    handleAccountSuccess(res, file, fileList) {
      //此处写上传oss成功之后的逻辑
      if (res.code == 200) {
        this.form.accountLicence = res.data.url;
        this.form.accountLicenceName = res.data.name;
      }
    },
  },
};
</script>

<style lang="scss">
.app-container {
  background: #f4f5f9;
  .notice-record-detail {
    .info-container {
      width: 100%;
      padding-top: 12px;
      padding: 10px 30px;

      background-color: white;
      .header {
        margin-bottom: 30px;
        width: 100%;
        text-align: center;
        .el-button {
          height: 40px;
          border-color: transparent;
          padding: 10px 10px 10px 20px;
          font-size: 20px;
          color: #000;
        }
        .el-button:hover {
          background-color: white;
        }
        .header-text {
          font-size: 24px;
          font-weight: 500;
          color: #333333;
          line-height: 24px;
          line-height: 40px;
        }
      }
      .detail-page {
        position: relative;

        .header-small {
          text-align: center;
          display: flex;
          font-size: 16px;
          font-weight: 500;
          color: #333333;
          line-height: 16px;
          margin-bottom: 20px;

          .red-tag {
            margin-right: 12px;
            width: 3px;
            height: 16px;
            background: #21c9b8;
          }
        }
        .file-class {
          width: 733px;
          height: 40px;
          background: #f7f8fa;
          border-radius: 4px;
          padding: 0 20px;
          display: flex;
          font-size: 14px;
          font-weight: 400;
          color: #333333;
          line-height: 40px;
          position: relative;

          .el-image {
            margin: 12px 8px 0 0;
          }
          .previwe-class {
            right: 20px;
            position: absolute;
            margin: 8px 0 0 0;
            width: 72px;
            height: 24px;
            border-radius: 16px;
            text-align: center;
            border: 1px solid #2f76e0;
            font-size: 12px;
            font-weight: 400;
            color: #2f76e0;
            line-height: 24px;
          }
        }
        .status_approving {
          top: 0px;
          right: 20px;
          position: absolute;
        }
      }

      .edit-page {
        .el-input--medium .el-input__inner {
          width: 300px;
          height: 36px;
          line-height: 36px;
        }
        .el-button--primary {
          background: #fff;
          color: #333;
          border-color: #bfbfbf;
        }
        .el-button--danger {
          background: #fff;
          color: #21c9b8;
          border-color: #21c9b8;
        }
        .tip {
          padding-left: 10px;
          font-size: 12px;
          font-weight: 400;
          color: #8c8c8c;
          line-height: 18px;
        }
      }
      .el-descriptions--medium.is-bordered .el-descriptions-item__cell {
        padding: 10px;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
      }

      .el-descriptions--medium.is-bordered .el-descriptions-item__label {
        padding: 15px;
        text-align: center;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        width: 200px;
      }
      .delete-btn {
        width: 100%;
        margin-top: 20px;
        text-align: center;
        .el-button {
          padding: 12px 55px;
        }
        .el-button--danger {
          margin-left: 30px;
          color: #ffffff;
          background-color: #21c9b8;
          border-color: #21c9b8;
        }
        .el-button--error {
          margin-left: 30px;
          color: #21c9b8;
          background-color: #ffffff;
          border-color: #21c9b8;
        }
      }
    }
  }
}
</style>
