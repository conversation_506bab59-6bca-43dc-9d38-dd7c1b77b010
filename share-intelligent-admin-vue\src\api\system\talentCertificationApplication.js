import request from '@/utils/request'

// 查询衡水市职业技能鉴定中心申请列表
export function listTalentCertificationApplication(query) {
  return request({
    url: '/system/talentCertificationApplication/list',
    method: 'get',
    params: query
  })
}

// 查询衡水市职业技能鉴定中心申请详细
export function getTalentCertificationApplication(id) {
  return request({
    url: '/system/talentCertificationApplication/' + id,
    method: 'get'
  })
}

// 新增衡水市职业技能鉴定中心申请
export function addTalentCertificationApplication(data) {
  return request({
    url: '/system/talentCertificationApplication',
    method: 'post',
    data: data
  })
}

// 修改衡水市职业技能鉴定中心申请
export function updateTalentCertificationApplication(data) {
  return request({
    url: '/system/talentCertificationApplication',
    method: 'put',
    data: data
  })
}

// 删除衡水市职业技能鉴定中心申请
export function delTalentCertificationApplication(id) {
  return request({
    url: '/system/talentCertificationApplication/' + id,
    method: 'delete'
  })
}
