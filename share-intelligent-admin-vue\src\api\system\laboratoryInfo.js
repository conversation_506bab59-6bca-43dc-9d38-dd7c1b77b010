import request from '@/utils/request'

// 查询实验室信息列表
export function listLaboratoryInfo(query) {
  return request({
    url: '/system/laboratoryInfo/list',
    method: 'get',
    params: query
  })
}

// 查询实验室信息详细
export function getLaboratoryInfo(id) {
  return request({
    url: '/system/laboratoryInfo/' + id,
    method: 'get'
  })
}

// 新增实验室信息
export function addLaboratoryInfo(data) {
  return request({
    url: '/system/laboratoryInfo',
    method: 'post',
    data: data
  })
}

// 修改实验室信息
export function updateLaboratoryInfo(data) {
  return request({
    url: '/system/laboratoryInfo',
    method: 'put',
    data: data
  })
}

// 删除实验室信息
export function delLaboratoryInfo(id) {
  return request({
    url: '/system/laboratoryInfo/' + id,
    method: 'delete'
  })
}
