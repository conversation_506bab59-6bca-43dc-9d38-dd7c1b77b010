<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 23.0.2, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 1228.65 1188.97" style="enable-background:new 0 0 1228.65 1188.97;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFFFFF;}
	.st1{fill:#B5B5B6;}
	.st2{fill:url(#SVGID_1_);}
	.st3{fill:none;stroke:#B5B5B6;stroke-width:0.5;stroke-linecap:round;stroke-miterlimit:10;}
	.st4{fill:url(#SVGID_2_);}
	.st5{fill:url(#SVGID_3_);}
	.st6{fill:url(#SVGID_4_);}
	.st7{fill:url(#SVGID_5_);}
	.st8{fill:url(#SVGID_6_);}
	.st9{fill:url(#SVGID_7_);}
	.st10{fill:#211131;}
	.st11{fill:url(#SVGID_8_);}
	.st12{fill:url(#SVGID_9_);}
	.st13{fill:url(#SVGID_10_);}
	.st14{fill:url(#SVGID_11_);}
	.st15{fill:url(#SVGID_12_);}
	.st16{fill:url(#SVGID_13_);}
	.st17{fill:#727271;}
	.st18{fill:#FFFFFF;stroke:#DCDDDD;stroke-width:0.25;stroke-miterlimit:10;stroke-dasharray:1;}
	.st19{fill:none;stroke:#DCDDDD;stroke-width:0.25;stroke-miterlimit:10;stroke-dasharray:1;}
	.st20{opacity:0.3;}
	.st21{fill:url(#SVGID_16_);}
	.st22{fill:url(#SVGID_17_);}
	.st23{fill:url(#SVGID_18_);}
	.st24{opacity:0.3;fill:url(#SVGID_19_);}
	.st25{fill:url(#SVGID_20_);}
	.st26{fill:url(#SVGID_21_);}
	.st27{fill:#9FA0A0;}
	.st28{fill:none;stroke:#000000;stroke-width:0.25;stroke-miterlimit:10;stroke-dasharray:1;}
	.st29{fill:none;stroke:#9FA0A0;stroke-width:0.25;stroke-miterlimit:10;}
	.st30{fill:#7D7D7D;}
	.st31{fill:#C9CACA;}
	.st32{fill:none;stroke:#9FA0A0;stroke-width:0.5;stroke-miterlimit:10;}
	.st33{fill:url(#SVGID_22_);}
	.st34{fill:url(#SVGID_23_);}
	.st35{fill:url(#SVGID_24_);}
	.st36{fill:url(#SVGID_25_);}
	.st37{fill:url(#SVGID_26_);}
	.st38{fill:url(#SVGID_27_);}
	.st39{fill:none;stroke:#DCDDDD;stroke-width:0.25;stroke-miterlimit:10;}
	.st40{fill:none;stroke:#727171;stroke-width:0.25;stroke-miterlimit:10;}
	.st41{fill:url(#SVGID_28_);}
	.st42{fill:url(#SVGID_29_);}
	.st43{fill:url(#SVGID_30_);}
	.st44{fill:url(#SVGID_31_);}
	.st45{fill:url(#SVGID_32_);}
	.st46{fill:url(#SVGID_33_);}
	.st47{fill:url(#SVGID_34_);}
	.st48{fill:url(#SVGID_35_);}
	.st49{fill:url(#SVGID_36_);}
	.st50{fill:url(#SVGID_37_);}
	.st51{fill:url(#SVGID_38_);}
	.st52{fill:url(#SVGID_39_);}
	.st53{fill:url(#SVGID_40_);}
	.st54{fill:url(#SVGID_41_);}
	.st55{fill:url(#SVGID_42_);}
	.st56{fill:url(#SVGID_43_);}
	.st57{fill:url(#SVGID_44_);}
	.st58{fill:url(#SVGID_45_);}
	.st59{fill:url(#SVGID_46_);}
	.st60{fill:url(#SVGID_47_);}
	.st61{fill:url(#SVGID_48_);}
	.st62{fill:#848484;}
	.st63{fill-rule:evenodd;clip-rule:evenodd;fill:url(#SVGID_49_);}
	.st64{fill:#858683;}
	.st65{fill:#EAEEF1;}
	.st66{fill-rule:evenodd;clip-rule:evenodd;fill:url(#SVGID_50_);}
	.st67{fill:#1D2975;}
	.st68{fill:#1C96D4;}
	.st69{fill-rule:evenodd;clip-rule:evenodd;fill:#211131;}
	.st70{fill-rule:evenodd;clip-rule:evenodd;fill:#B5B5B6;}
	.st71{fill:none;stroke:#B5B5B6;stroke-width:0.5;stroke-miterlimit:10;}
	.st72{clip-path:url(#SVGID_52_);}
	.st73{clip-path:url(#SVGID_56_);}
	.st74{fill:#898989;}
	.st75{fill:url(#SVGID_61_);}
	.st76{fill:url(#SVGID_62_);}
	.st77{fill:url(#SVGID_63_);}
	.st78{fill:url(#SVGID_64_);}
	.st79{fill:url(#SVGID_65_);}
	.st80{fill:url(#SVGID_66_);}
	.st81{fill:url(#SVGID_67_);}
	.st82{fill:url(#SVGID_68_);}
	.st83{fill:url(#SVGID_69_);}
	.st84{fill:url(#SVGID_70_);}
	.st85{fill:url(#SVGID_71_);}
	.st86{fill:url(#SVGID_72_);}
	.st87{fill:none;stroke:#898989;stroke-width:0.25;stroke-miterlimit:10;}
	.st88{opacity:0.2;clip-path:url(#SVGID_74_);}
	.st89{fill:url(#SVGID_75_);}
	.st90{fill:url(#SVGID_76_);}
	.st91{fill:url(#SVGID_77_);}
	.st92{fill:url(#SVGID_78_);}
	.st93{fill:url(#SVGID_79_);}
	.st94{fill:url(#SVGID_80_);}
	.st95{fill:url(#SVGID_85_);}
	.st96{fill:url(#SVGID_86_);}
	.st97{fill:url(#SVGID_87_);}
	.st98{fill:url(#SVGID_88_);}
	.st99{fill:url(#SVGID_89_);}
	.st100{fill:url(#SVGID_90_);}
	.st101{fill:url(#SVGID_91_);}
	.st102{fill:url(#SVGID_92_);}
	.st103{fill:url(#SVGID_93_);}
	.st104{fill:url(#SVGID_94_);}
	.st105{fill:url(#SVGID_95_);}
	.st106{fill:url(#SVGID_96_);}
	.st107{fill:none;stroke:#898989;stroke-width:0.5;stroke-miterlimit:10;}
	.st108{fill:none;stroke:#898989;stroke-width:0.5;stroke-miterlimit:10;stroke-dasharray:2;}
	.st109{fill:#1B1332;}
	.st110{fill:url(#SVGID_101_);}
	.st111{fill:url(#SVGID_102_);}
	.st112{fill:url(#SVGID_103_);}
	.st113{fill:url(#SVGID_104_);}
	.st114{fill:url(#SVGID_105_);}
	.st115{fill:url(#SVGID_106_);}
	.st116{fill:url(#SVGID_107_);}
	.st117{fill:url(#SVGID_108_);}
	.st118{fill:url(#SVGID_109_);}
	.st119{fill:url(#SVGID_110_);}
	.st120{fill:url(#SVGID_111_);}
	.st121{fill:url(#SVGID_112_);}
	.st122{fill:url(#SVGID_113_);}
	.st123{fill:url(#SVGID_114_);}
	.st124{fill:url(#SVGID_115_);}
	.st125{fill:url(#SVGID_116_);}
	.st126{fill:url(#SVGID_117_);}
	.st127{fill:url(#SVGID_118_);}
	.st128{fill:url(#SVGID_119_);}
	.st129{fill:url(#SVGID_120_);}
	.st130{fill:url(#SVGID_121_);}
	.st131{fill:none;stroke:#EAEAEA;stroke-miterlimit:10;}
	.st132{fill:url(#SVGID_122_);}
	.st133{fill:url(#SVGID_123_);}
	.st134{fill:url(#SVGID_124_);}
	.st135{fill:url(#SVGID_125_);}
	.st136{fill:url(#SVGID_126_);}
	.st137{fill:url(#SVGID_127_);}
	.st138{fill:none;stroke:#3E3A39;stroke-width:0.25;stroke-miterlimit:10;stroke-dasharray:0.5;}
	.st139{fill:url(#SVGID_130_);}
	.st140{fill:url(#SVGID_131_);}
	.st141{fill:url(#SVGID_132_);}
	.st142{fill:url(#SVGID_133_);}
	.st143{fill:url(#SVGID_134_);}
	.st144{fill:url(#SVGID_135_);}
	.st145{fill:url(#SVGID_136_);}
	.st146{clip-path:url(#SVGID_138_);}
	.st147{fill:none;stroke:#B5B5B6;stroke-miterlimit:10;}
	.st148{fill:url(#SVGID_141_);}
	.st149{fill:url(#SVGID_142_);}
	.st150{fill:url(#SVGID_143_);}
	.st151{fill:url(#SVGID_144_);}
	.st152{fill:url(#SVGID_145_);}
	.st153{fill:url(#SVGID_146_);}
	.st154{fill:url(#SVGID_147_);}
	.st155{clip-path:url(#SVGID_149_);}
	.st156{opacity:0.2;}
	.st157{fill:url(#SVGID_154_);}
	.st158{fill:url(#SVGID_155_);}
	.st159{fill:url(#SVGID_156_);}
	.st160{fill:url(#SVGID_157_);}
	.st161{fill:url(#SVGID_158_);}
	.st162{fill:url(#SVGID_159_);}
	.st163{fill:#FFFFFF;stroke:#B5B5B6;stroke-miterlimit:10;}
	.st164{fill:url(#SVGID_168_);}
	.st165{fill:url(#SVGID_169_);}
	.st166{fill:url(#SVGID_170_);}
	.st167{fill:url(#SVGID_171_);}
	.st168{fill:url(#SVGID_172_);}
	.st169{fill:url(#SVGID_173_);}
	.st170{fill:url(#SVGID_174_);}
	.st171{clip-path:url(#SVGID_176_);}
	.st172{fill:url(#SVGID_177_);}
	.st173{fill:url(#SVGID_178_);}
	.st174{fill:url(#SVGID_179_);}
	.st175{fill:url(#SVGID_180_);}
	.st176{fill:url(#SVGID_181_);}
	.st177{fill:url(#SVGID_182_);}
	.st178{fill:url(#SVGID_187_);}
	.st179{fill:url(#SVGID_188_);}
	.st180{fill:url(#SVGID_189_);}
	.st181{fill:url(#SVGID_190_);}
	.st182{fill:url(#SVGID_191_);}
	.st183{fill:url(#SVGID_192_);}
	.st184{fill:url(#SVGID_203_);}
	.st185{fill:url(#SVGID_204_);}
	.st186{fill:url(#SVGID_205_);}
	.st187{fill:url(#SVGID_206_);}
	.st188{fill:url(#SVGID_207_);}
	.st189{fill:url(#SVGID_208_);}
	.st190{fill:#F7F7F7;}
	.st191{fill:#707070;}
	.st192{fill:none;stroke:#707070;stroke-width:0.25;stroke-miterlimit:10;}
	.st193{fill:none;stroke:#DCDDDD;stroke-width:0.5;stroke-miterlimit:10;}
	.st194{fill:url(#SVGID_209_);}
	.st195{fill:url(#SVGID_210_);}
	.st196{fill:url(#SVGID_211_);}
	.st197{fill:url(#SVGID_212_);}
	.st198{fill:url(#SVGID_213_);}
	.st199{fill:url(#SVGID_214_);}
	.st200{fill:url(#SVGID_215_);}
	.st201{fill:url(#SVGID_216_);}
	.st202{fill:url(#SVGID_217_);}
	.st203{fill:url(#SVGID_218_);}
	.st204{fill:url(#SVGID_219_);}
	.st205{fill:url(#SVGID_220_);}
	.st206{fill:url(#SVGID_221_);}
	.st207{fill:none;stroke:#707070;stroke-width:0.25;stroke-miterlimit:10;stroke-dasharray:2;}
	.st208{fill:url(#SVGID_222_);}
	.st209{fill:url(#SVGID_223_);}
	.st210{fill:url(#SVGID_224_);}
	.st211{fill:url(#SVGID_225_);}
	.st212{fill:url(#SVGID_226_);}
	.st213{fill:url(#SVGID_227_);}
	.st214{fill:url(#SVGID_228_);}
	.st215{fill:url(#SVGID_229_);}
	.st216{fill:url(#SVGID_230_);}
	.st217{fill:url(#SVGID_231_);}
	.st218{fill:url(#SVGID_232_);}
	.st219{fill:url(#SVGID_233_);}
	.st220{fill:url(#SVGID_234_);}
	.st221{fill:url(#SVGID_235_);}
	.st222{fill:url(#SVGID_236_);}
	.st223{clip-path:url(#SVGID_238_);fill:#1B1332;}
	.st224{clip-path:url(#SVGID_238_);}
	.st225{fill:url(#SVGID_239_);}
	.st226{fill:url(#SVGID_240_);}
	.st227{fill:url(#SVGID_241_);}
	.st228{fill:url(#SVGID_242_);}
	.st229{fill:url(#SVGID_243_);}
	.st230{fill:url(#SVGID_244_);}
	.st231{fill:url(#SVGID_245_);}
	.st232{fill:url(#SVGID_246_);}
	.st233{fill:url(#SVGID_247_);}
	.st234{fill:url(#SVGID_248_);}
	.st235{fill:url(#SVGID_249_);}
	.st236{fill:url(#SVGID_250_);}
	.st237{fill:url(#SVGID_251_);}
	.st238{fill:url(#SVGID_252_);}
	.st239{fill:url(#SVGID_253_);}
	.st240{fill:url(#SVGID_254_);}
	.st241{fill:url(#SVGID_255_);}
	.st242{fill:url(#SVGID_256_);}
	.st243{fill:none;}
	.st244{fill:url(#SVGID_257_);}
	.st245{fill:url(#SVGID_258_);}
	.st246{fill:url(#SVGID_259_);}
	.st247{fill:url(#SVGID_260_);}
	.st248{fill:url(#SVGID_261_);}
	.st249{fill:url(#SVGID_262_);}
	.st250{fill:url(#SVGID_263_);}
	.st251{fill:url(#SVGID_264_);}
	.st252{fill:url(#SVGID_265_);}
	.st253{fill:url(#SVGID_266_);}
	.st254{fill:url(#SVGID_267_);}
	.st255{fill:url(#SVGID_268_);}
	.st256{fill:url(#SVGID_269_);}
	.st257{fill:url(#SVGID_270_);}
	.st258{fill:url(#SVGID_271_);}
	.st259{fill:url(#SVGID_272_);}
	.st260{fill:url(#SVGID_273_);}
	.st261{fill:url(#SVGID_274_);}
	.st262{fill:url(#SVGID_275_);}
	.st263{fill:url(#SVGID_276_);}
	.st264{fill:url(#SVGID_277_);}
	.st265{fill:url(#SVGID_278_);}
	.st266{fill:url(#SVGID_279_);}
	.st267{fill:url(#SVGID_280_);}
	.st268{fill:url(#SVGID_281_);}
	.st269{fill:url(#SVGID_282_);}
	.st270{clip-path:url(#SVGID_284_);fill:#1B1332;}
	.st271{clip-path:url(#SVGID_284_);}
	.st272{fill:url(#SVGID_285_);}
	.st273{fill:url(#SVGID_286_);}
	.st274{fill:url(#SVGID_287_);}
	.st275{fill:url(#SVGID_288_);}
	.st276{fill:url(#SVGID_289_);}
	.st277{fill:url(#SVGID_290_);}
	.st278{fill:url(#SVGID_291_);}
	.st279{fill:url(#SVGID_292_);}
	.st280{fill:url(#SVGID_293_);}
	.st281{fill:url(#SVGID_294_);}
	.st282{fill:url(#SVGID_295_);}
	.st283{fill:url(#SVGID_296_);}
	.st284{fill:url(#SVGID_297_);}
	.st285{fill:url(#SVGID_298_);}
	.st286{fill:url(#SVGID_299_);}
	.st287{fill:url(#SVGID_300_);}
	.st288{fill:url(#SVGID_301_);}
	.st289{fill:url(#SVGID_302_);}
	.st290{fill:url(#SVGID_303_);}
	.st291{fill:url(#SVGID_304_);}
	.st292{fill:url(#SVGID_305_);}
	.st293{fill:url(#SVGID_306_);}
	.st294{fill:url(#SVGID_307_);}
	.st295{fill:url(#SVGID_308_);}
	.st296{fill:url(#SVGID_309_);}
	.st297{fill:url(#SVGID_310_);}
	.st298{fill:url(#SVGID_311_);}
	.st299{fill:url(#SVGID_312_);}
	.st300{fill:url(#SVGID_313_);}
	.st301{fill:url(#SVGID_314_);}
	.st302{fill:url(#SVGID_315_);}
	.st303{fill:url(#SVGID_316_);}
	.st304{fill:url(#SVGID_317_);}
	.st305{fill:url(#SVGID_318_);}
	.st306{fill:url(#SVGID_319_);}
	.st307{fill:url(#SVGID_320_);}
	.st308{fill:url(#SVGID_321_);}
	.st309{fill:url(#SVGID_322_);}
	.st310{fill:url(#SVGID_323_);}
	.st311{fill:url(#SVGID_324_);}
	.st312{fill:url(#SVGID_325_);}
	.st313{fill:url(#SVGID_326_);}
	.st314{fill:url(#SVGID_327_);}
	.st315{fill:url(#SVGID_328_);}
	.st316{fill:url(#SVGID_329_);}
	.st317{fill:url(#SVGID_330_);}
	.st318{fill:url(#SVGID_331_);}
	.st319{fill:url(#SVGID_332_);}
	.st320{fill:url(#SVGID_333_);}
	.st321{fill:url(#SVGID_334_);}
	.st322{fill:url(#SVGID_335_);}
	.st323{clip-path:url(#SVGID_337_);}
	.st324{clip-path:url(#SVGID_341_);}
	.st325{clip-path:url(#SVGID_345_);}
	.st326{fill:#FFFFFF;stroke:#D8D8D8;stroke-miterlimit:10;}
	.st327{fill:url(#SVGID_350_);}
	.st328{fill:url(#SVGID_351_);}
	.st329{fill:url(#SVGID_352_);}
	.st330{fill:url(#SVGID_353_);}
	.st331{fill:url(#SVGID_354_);}
	.st332{fill:url(#SVGID_355_);}
	.st333{fill:#595757;}
	.st334{fill:url(#SVGID_356_);}
	.st335{fill:url(#SVGID_357_);}
	.st336{fill:url(#SVGID_358_);}
	.st337{fill:url(#SVGID_359_);}
	.st338{fill:url(#SVGID_360_);}
	.st339{fill:url(#SVGID_361_);}
	.st340{fill:url(#SVGID_362_);}
	.st341{fill:url(#SVGID_363_);}
	.st342{fill:url(#SVGID_364_);}
	.st343{clip-path:url(#SVGID_366_);}
	.st344{fill:url(#SVGID_367_);}
	.st345{fill:url(#SVGID_368_);}
	.st346{fill:url(#SVGID_369_);}
	.st347{fill:url(#SVGID_370_);}
	.st348{fill:url(#SVGID_371_);}
	.st349{fill:url(#SVGID_372_);}
	.st350{fill:url(#SVGID_373_);}
	.st351{fill:url(#SVGID_374_);}
	.st352{clip-path:url(#SVGID_376_);}
	.st353{clip-path:url(#SVGID_380_);}
	.st354{clip-path:url(#SVGID_382_);}
	.st355{clip-path:url(#SVGID_388_);}
	.st356{clip-path:url(#SVGID_390_);}
	.st357{fill:url(#SVGID_395_);}
	.st358{fill:url(#SVGID_396_);}
	.st359{fill:url(#SVGID_397_);}
	.st360{fill:url(#SVGID_398_);}
	.st361{fill:url(#SVGID_399_);}
	.st362{fill:url(#SVGID_400_);}
	.st363{fill:none;stroke:#898989;stroke-miterlimit:10;}
	.st364{clip-path:url(#SVGID_402_);}
	.st365{clip-path:url(#SVGID_404_);}
	.st366{clip-path:url(#SVGID_410_);}
	.st367{clip-path:url(#SVGID_412_);}
	.st368{clip-path:url(#SVGID_422_);}
	.st369{clip-path:url(#SVGID_424_);}
	.st370{clip-path:url(#SVGID_430_);}
	.st371{fill:#12132D;}
	.st372{clip-path:url(#SVGID_432_);}
	.st373{fill:url(#SVGID_433_);}
	.st374{fill:url(#SVGID_434_);}
	.st375{fill:url(#SVGID_435_);}
	.st376{fill:url(#SVGID_436_);}
	.st377{fill:url(#SVGID_437_);}
	.st378{fill:url(#SVGID_438_);}
	.st379{clip-path:url(#SVGID_440_);}
	.st380{clip-path:url(#SVGID_442_);}
	.st381{clip-path:url(#SVGID_452_);}
	.st382{fill:url(#SVGID_457_);}
	.st383{fill:url(#SVGID_458_);}
	.st384{fill:url(#SVGID_459_);}
	.st385{fill:url(#SVGID_460_);}
	.st386{fill:url(#SVGID_461_);}
	.st387{fill:url(#SVGID_462_);}
	.st388{fill:url(#SVGID_463_);}
	.st389{fill:#999999;}
	.st390{fill:none;stroke:url(#SVGID_464_);stroke-miterlimit:10;}
	.st391{fill:#B5B4B5;}
	.st392{fill:url(#SVGID_465_);}
	.st393{fill:url(#SVGID_466_);}
	.st394{fill:url(#SVGID_467_);}
	.st395{fill:url(#SVGID_468_);}
	.st396{fill:url(#SVGID_469_);}
	.st397{fill:url(#SVGID_470_);}
	.st398{fill:url(#SVGID_471_);}
	.st399{clip-path:url(#SVGID_473_);}
	.st400{fill:url(#SVGID_476_);}
	.st401{fill:none;stroke:url(#SVGID_477_);stroke-miterlimit:10;}
	.st402{fill:url(#SVGID_478_);}
	.st403{fill:url(#SVGID_479_);}
	.st404{fill:url(#SVGID_480_);}
	.st405{fill:url(#SVGID_481_);}
	.st406{fill:url(#SVGID_482_);}
	.st407{fill:url(#SVGID_483_);}
	.st408{fill:url(#SVGID_484_);}
	.st409{fill:url(#SVGID_485_);}
	.st410{clip-path:url(#SVGID_487_);}
	.st411{fill:url(#SVGID_490_);}
	.st412{clip-path:url(#SVGID_492_);}
	.st413{fill:#CCCCCC;}
	.st414{fill:none;stroke:#FFFFFF;stroke-miterlimit:10;}
	.st415{fill:#DCDCDC;}
	.st416{fill:url(#SVGID_493_);}
	.st417{opacity:0.88;}
	.st418{clip-path:url(#SVGID_495_);}
	.st419{opacity:0.1;}
	.st420{fill:#FFFFFF;stroke:#FFFFFF;stroke-width:0.25;stroke-miterlimit:10;}
	.st421{fill:none;stroke:#FFFFFF;stroke-width:0.25;stroke-miterlimit:10;}
	.st422{fill:url(#SVGID_498_);}
	.st423{fill:url(#SVGID_499_);}
	.st424{fill:url(#SVGID_500_);}
	.st425{fill:url(#SVGID_501_);}
	.st426{fill:url(#SVGID_502_);}
	.st427{fill:url(#SVGID_503_);}
	.st428{fill:url(#SVGID_504_);}
	.st429{clip-path:url(#SVGID_506_);}
	.st430{clip-path:url(#SVGID_510_);}
	.st431{fill:url(#SVGID_511_);}
	.st432{fill:url(#SVGID_514_);}
	.st433{opacity:0.1;fill:#FFFFFF;}
	.st434{fill:none;stroke:#FFFFFF;stroke-width:5;stroke-miterlimit:10;}
	.st435{fill:url(#SVGID_515_);}
	.st436{fill:none;stroke:url(#SVGID_516_);stroke-miterlimit:10;}
	.st437{fill:none;stroke:url(#SVGID_517_);stroke-miterlimit:10;}
	.st438{fill:url(#SVGID_518_);}
	.st439{fill:url(#SVGID_519_);}
	.st440{fill:url(#SVGID_520_);}
	.st441{fill:url(#SVGID_521_);}
	.st442{fill:url(#SVGID_522_);}
	.st443{fill:url(#SVGID_523_);}
	.st444{fill:url(#SVGID_524_);}
	.st445{fill:url(#SVGID_525_);}
	.st446{fill:url(#SVGID_526_);}
	.st447{fill:url(#SVGID_527_);}
	.st448{fill:url(#SVGID_528_);}
	.st449{fill:url(#SVGID_529_);}
	.st450{fill:url(#SVGID_530_);}
	.st451{clip-path:url(#SVGID_532_);}
	.st452{clip-path:url(#SVGID_534_);}
	.st453{fill:url(#SVGID_537_);}
	.st454{fill:url(#SVGID_540_);}
	.st455{fill:none;stroke:url(#SVGID_541_);stroke-miterlimit:10;}
	.st456{fill:none;stroke:url(#SVGID_542_);stroke-miterlimit:10;}
	.st457{fill:url(#SVGID_543_);}
	.st458{fill:url(#SVGID_544_);}
	.st459{fill:url(#SVGID_545_);}
	.st460{fill:url(#SVGID_546_);}
	.st461{fill:url(#SVGID_547_);}
	.st462{fill:url(#SVGID_548_);}
	.st463{fill:url(#SVGID_549_);}
	.st464{fill:url(#SVGID_550_);}
	.st465{fill:url(#SVGID_551_);}
	.st466{fill:url(#SVGID_552_);}
	.st467{fill:url(#SVGID_553_);}
	.st468{fill:url(#SVGID_554_);}
	.st469{fill:url(#SVGID_555_);}
	.st470{fill:url(#SVGID_556_);}
	.st471{clip-path:url(#SVGID_558_);}
	.st472{fill:url(#SVGID_561_);}
	.st473{fill:url(#SVGID_564_);}
	.st474{fill:#72C8D5;}
	.st475{fill:none;stroke:#1564B0;stroke-miterlimit:10;}
	.st476{fill:#1564B0;}
	.st477{fill:url(#SVGID_565_);}
	.st478{fill:url(#SVGID_566_);}
	.st479{fill:url(#SVGID_567_);}
	.st480{fill:url(#SVGID_568_);}
	.st481{fill:url(#SVGID_569_);}
	.st482{fill:url(#SVGID_570_);}
	.st483{fill:none;stroke:#1564B0;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st484{fill:none;stroke:#1564B0;stroke-linecap:round;stroke-miterlimit:10;}
	.st485{fill:none;stroke:url(#SVGID_571_);stroke-miterlimit:10;}
	.st486{fill:url(#SVGID_572_);}
	.st487{fill:url(#SVGID_573_);}
	.st488{fill:url(#SVGID_574_);}
	.st489{fill:url(#SVGID_575_);}
	.st490{fill:url(#SVGID_576_);}
	.st491{fill:url(#SVGID_577_);}
	.st492{fill:url(#SVGID_578_);}
	.st493{fill:url(#SVGID_579_);}
	.st494{clip-path:url(#SVGID_581_);}
	.st495{fill:url(#SVGID_584_);}
	.st496{fill:url(#SVGID_587_);}
	.st497{fill:url(#SVGID_588_);}
	.st498{fill:url(#SVGID_589_);}
	.st499{fill:url(#SVGID_590_);}
	.st500{fill:url(#SVGID_591_);}
	.st501{fill:url(#SVGID_592_);}
	.st502{fill:url(#SVGID_593_);}
	.st503{fill:none;stroke:url(#SVGID_594_);stroke-miterlimit:10;}
	.st504{fill:url(#SVGID_595_);}
	.st505{fill:url(#SVGID_596_);}
	.st506{fill:url(#SVGID_597_);}
	.st507{fill:url(#SVGID_598_);}
	.st508{fill:url(#SVGID_599_);}
	.st509{fill:url(#SVGID_600_);}
	.st510{fill:url(#SVGID_601_);}
	.st511{fill:url(#SVGID_602_);}
	.st512{fill:#DCDDDD;}
	.st513{fill:none;stroke:#1B1332;stroke-width:2;stroke-miterlimit:10;}
	.st514{fill:none;stroke:#1B1332;stroke-miterlimit:10;}
	.st515{clip-path:url(#SVGID_604_);}
	.st516{clip-path:url(#SVGID_608_);}
	.st517{clip-path:url(#SVGID_612_);fill:#FFFFFF;}
	.st518{clip-path:url(#SVGID_612_);}
	.st519{clip-path:url(#SVGID_618_);}
	.st520{fill:none;stroke:url(#SVGID_621_);stroke-width:0.5;stroke-miterlimit:10;}
	.st521{fill:url(#SVGID_622_);}
	.st522{fill:url(#SVGID_623_);}
	.st523{fill:url(#SVGID_624_);}
	.st524{fill:url(#SVGID_625_);}
	.st525{fill:url(#SVGID_626_);}
	.st526{fill:url(#SVGID_627_);}
	.st527{fill:url(#SVGID_628_);}
	.st528{fill:url(#SVGID_629_);}
	.st529{fill:url(#SVGID_630_);}
	.st530{fill:url(#SVGID_631_);}
	.st531{fill:url(#SVGID_632_);}
	.st532{fill:url(#SVGID_633_);}
	.st533{clip-path:url(#SVGID_637_);}
	.st534{fill:url(#SVGID_638_);}
	.st535{fill:url(#SVGID_639_);}
	.st536{fill:url(#SVGID_640_);}
	.st537{fill:url(#SVGID_641_);}
	.st538{fill:url(#SVGID_642_);}
	.st539{fill:url(#SVGID_643_);}
	.st540{fill:url(#SVGID_644_);}
	.st541{clip-path:url(#SVGID_650_);}
	.st542{fill:url(#SVGID_651_);}
	.st543{fill:url(#SVGID_652_);}
	.st544{fill:url(#SVGID_653_);}
	.st545{fill:url(#SVGID_654_);}
	.st546{fill:url(#SVGID_655_);}
	.st547{fill:url(#SVGID_656_);}
	.st548{fill:none;stroke:#FFFFFF;stroke-width:2;stroke-miterlimit:10;}
	.st549{fill:url(#SVGID_659_);}
	.st550{fill:url(#SVGID_660_);}
	.st551{fill:url(#SVGID_661_);}
	.st552{fill:url(#SVGID_662_);}
	.st553{fill:url(#SVGID_663_);}
	.st554{fill:url(#SVGID_664_);}
	.st555{fill:url(#SVGID_665_);}
	.st556{fill:url(#SVGID_666_);}
	.st557{fill:url(#SVGID_667_);}
	.st558{fill:url(#SVGID_668_);}
	.st559{fill:url(#SVGID_669_);}
	.st560{fill:url(#SVGID_670_);}
	.st561{fill:url(#SVGID_671_);}
	.st562{fill:url(#SVGID_672_);}
	.st563{fill:url(#SVGID_673_);}
	.st564{fill:url(#SVGID_674_);}
	.st565{fill:url(#SVGID_675_);}
	.st566{fill:url(#SVGID_676_);}
	.st567{fill:url(#SVGID_677_);}
	.st568{fill:url(#SVGID_678_);}
	.st569{fill:url(#SVGID_679_);}
	.st570{fill:url(#SVGID_680_);}
	.st571{fill:url(#SVGID_681_);}
	.st572{fill:url(#SVGID_682_);}
	.st573{fill:url(#SVGID_683_);}
	.st574{fill:url(#SVGID_684_);}
	.st575{fill:#E60012;}
	.st576{fill:url(#SVGID_685_);}
	.st577{fill:url(#SVGID_686_);}
	.st578{fill:url(#SVGID_687_);}
	.st579{fill:url(#SVGID_688_);}
	.st580{fill:url(#SVGID_689_);}
	.st581{fill:url(#SVGID_690_);}
	.st582{fill:url(#SVGID_691_);}
	.st583{fill:url(#SVGID_692_);}
	.st584{fill:url(#SVGID_693_);}
	.st585{fill:url(#SVGID_694_);}
	.st586{fill:url(#SVGID_695_);}
	.st587{fill:url(#SVGID_696_);}
	.st588{fill:url(#SVGID_697_);}
	.st589{fill:url(#SVGID_698_);}
	.st590{fill:url(#SVGID_699_);}
	.st591{fill:#F7F8F8;stroke:#000000;stroke-width:0.25;stroke-miterlimit:10;}
	.st592{fill:#727171;}
	.st593{fill:none;stroke:#211131;stroke-width:1.4734;stroke-miterlimit:10;}
	.st594{fill-rule:evenodd;clip-rule:evenodd;fill:#EFEFEF;}
	.st595{fill-rule:evenodd;clip-rule:evenodd;fill:#898989;}
	.st596{fill-rule:evenodd;clip-rule:evenodd;}
	.st597{fill-rule:evenodd;clip-rule:evenodd;fill:none;stroke:#9FA0A0;stroke-width:0.9727;stroke-miterlimit:22.9256;}
	.st598{fill:#F7F8F8;}
	.st599{fill:none;stroke:#898989;stroke-width:1.1313;stroke-miterlimit:10;}
	.st600{fill:none;stroke:#898989;stroke-width:1.1313;stroke-miterlimit:10;stroke-dasharray:4.5253;}
	.st601{fill:url(#SVGID_700_);}
	.st602{fill:url(#SVGID_701_);}
	.st603{fill:url(#SVGID_702_);}
	.st604{fill:url(#SVGID_703_);}
	.st605{fill:url(#SVGID_704_);}
	.st606{fill:url(#SVGID_705_);}
	.st607{fill:url(#SVGID_706_);}
	.st608{fill:url(#SVGID_707_);}
	.st609{fill:url(#SVGID_708_);}
	.st610{fill:url(#SVGID_709_);}
	.st611{fill:url(#SVGID_710_);}
	.st612{fill:url(#SVGID_711_);}
	.st613{fill:url(#SVGID_712_);}
	.st614{fill:url(#SVGID_713_);}
	.st615{fill:url(#SVGID_714_);}
	.st616{fill:url(#SVGID_717_);}
	.st617{fill:url(#SVGID_718_);}
	.st618{fill:url(#SVGID_719_);}
	.st619{fill:url(#SVGID_720_);}
	.st620{fill:url(#SVGID_721_);}
	.st621{fill:url(#SVGID_722_);}
	.st622{fill:url(#SVGID_723_);}
	.st623{fill:url(#SVGID_724_);}
	.st624{fill:url(#SVGID_725_);}
	.st625{fill:url(#SVGID_726_);}
	.st626{fill:url(#SVGID_727_);}
	.st627{fill:url(#SVGID_728_);}
	.st628{opacity:0.2;clip-path:url(#SVGID_730_);}
	.st629{fill:url(#SVGID_731_);}
	.st630{fill:url(#SVGID_732_);}
	.st631{fill:url(#SVGID_733_);}
	.st632{fill:url(#SVGID_734_);}
	.st633{fill:url(#SVGID_735_);}
	.st634{fill:url(#SVGID_736_);}
	.st635{fill:url(#SVGID_741_);}
	.st636{fill:url(#SVGID_742_);}
	.st637{fill:url(#SVGID_743_);}
	.st638{fill:url(#SVGID_744_);}
	.st639{fill:url(#SVGID_745_);}
	.st640{fill:url(#SVGID_746_);}
	.st641{fill:url(#SVGID_747_);}
	.st642{fill:url(#SVGID_748_);}
	.st643{fill:url(#SVGID_749_);}
	.st644{fill:url(#SVGID_750_);}
	.st645{fill:url(#SVGID_751_);}
	.st646{fill:url(#SVGID_752_);}
	.st647{fill:none;stroke:#EAEAEA;stroke-width:2.4542;stroke-miterlimit:10;}
	.st648{fill:url(#SVGID_757_);}
	.st649{fill:url(#SVGID_758_);}
	.st650{fill:url(#SVGID_759_);}
	.st651{fill:url(#SVGID_760_);}
	.st652{fill:url(#SVGID_761_);}
	.st653{fill:url(#SVGID_762_);}
	.st654{fill:none;stroke:#3E3A39;stroke-width:0.6135;stroke-miterlimit:10;stroke-dasharray:1.2271;}
	.st655{fill:url(#SVGID_763_);}
	.st656{fill:url(#SVGID_764_);}
	.st657{fill:url(#SVGID_765_);}
	.st658{fill:url(#SVGID_766_);}
	.st659{fill:url(#SVGID_767_);}
	.st660{fill:url(#SVGID_768_);}
	.st661{fill:url(#SVGID_769_);}
	.st662{fill:url(#SVGID_770_);}
	.st663{fill:url(#SVGID_771_);}
	.st664{fill:url(#SVGID_772_);}
	.st665{fill:url(#SVGID_773_);}
	.st666{fill:url(#SVGID_774_);}
	.st667{fill:url(#SVGID_775_);}
	.st668{fill:url(#SVGID_776_);}
</style>
<g>
	<g>
		
			<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="524.5449" y1="1076.3907" x2="-385.4535" y2="190.1127" gradientTransform="matrix(0.9659 -0.2588 0.2588 0.9659 -110.5412 -226.5402)">
			<stop  offset="0" style="stop-color:#FFFFFF"/>
			<stop  offset="0.0609" style="stop-color:#F6F6F6"/>
			<stop  offset="0.1612" style="stop-color:#DDDDDD"/>
			<stop  offset="0.2885" style="stop-color:#B4B4B4"/>
			<stop  offset="0.4377" style="stop-color:#7C7C7C"/>
			<stop  offset="0.6034" style="stop-color:#353535"/>
			<stop  offset="0.7178" style="stop-color:#000000"/>
		</linearGradient>
		<path class="st2" d="M571.98,447.23l-73.97-276.07c44.9,218.32-157.62,282.9-195.98,293.09c-3.97,1.05-6.18,1.53-6.18,1.53
			L62.37,528.43c-3.46,25.11-5.25,50.76-5.25,76.82c0,45.65,5.49,90.02,15.84,132.48l3.18,11.87l380.07-101.84
			C543.55,624.36,595.39,534.58,571.98,447.23z"/>
	</g>
	<g>
		
			<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="266.4358" y1="112.2218" x2="450.8269" y2="1208.9899" gradientTransform="matrix(0.9659 -0.2588 0.2588 0.9659 -110.5412 -226.5402)">
			<stop  offset="0.2225" style="stop-color:#FFFFFF"/>
			<stop  offset="0.377" style="stop-color:#AEAEAE"/>
			<stop  offset="0.5315" style="stop-color:#646464"/>
			<stop  offset="0.6587" style="stop-color:#2E2E2E"/>
			<stop  offset="0.7535" style="stop-color:#0D0D0D"/>
			<stop  offset="0.8052" style="stop-color:#000000"/>
		</linearGradient>
		<path class="st4" d="M295.85,465.77c0,0,2.21-0.47,6.18-1.53C340.4,454.05,542.94,389.46,498,171.1l0.02,0.06
			c0-0.02-0.01-0.04-0.01-0.06L470.11,67.17l-17.14,4.59c-66.4,20.06-127.57,52.17-180.96,93.79l62.55,232.87
			C342.47,427.72,325.14,457.87,295.85,465.77z"/>
	</g>
	<g>
		
			<linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="538.6896" y1="-1517.5931" x2="-371.3047" y2="-2403.8672" gradientTransform="matrix(-0.2588 0.9659 -0.9659 -0.2588 -804.6427 -291.4569)">
			<stop  offset="0" style="stop-color:#FFFFFF"/>
			<stop  offset="0.0609" style="stop-color:#F6F6F6"/>
			<stop  offset="0.1612" style="stop-color:#DDDDDD"/>
			<stop  offset="0.2885" style="stop-color:#B4B4B4"/>
			<stop  offset="0.4377" style="stop-color:#7C7C7C"/>
			<stop  offset="0.6034" style="stop-color:#353535"/>
			<stop  offset="0.7178" style="stop-color:#000000"/>
		</linearGradient>
		<path class="st5" d="M772.52,647.76l276.13,73.99c-98.49-32.71-141.31-95.2-157.9-156.52c-22.74-84.04,3.78-165.88,3.78-165.88
			c0,0.01,0,0.01,0,0.02c0-0.01,0-0.01,0-0.01l62.47-233.5c-53.65-41.9-115.18-74.19-181.98-94.27l-16.43-4.4L656.74,447.23
			C633.34,534.58,685.17,624.36,772.52,647.76z"/>
	</g>
	
		<linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="280.5687" y1="-2481.7471" x2="464.9599" y2="-1384.979" gradientTransform="matrix(-0.2588 0.9659 -0.9659 -0.2588 -804.6427 -291.4569)">
		<stop  offset="0.2225" style="stop-color:#FFFFFF"/>
		<stop  offset="0.377" style="stop-color:#AEAEAE"/>
		<stop  offset="0.5315" style="stop-color:#646464"/>
		<stop  offset="0.6587" style="stop-color:#2E2E2E"/>
		<stop  offset="0.7535" style="stop-color:#0D0D0D"/>
		<stop  offset="0.8052" style="stop-color:#000000"/>
	</linearGradient>
	<path class="st6" d="M894.52,399.36c-0.38,1.17-26.35,82.44-3.78,165.87c16.59,61.32,59.41,123.81,157.9,156.52l103.95,27.81
		l2.12-7.92c10.98-43.65,16.81-89.34,16.81-136.39c0-25.94-1.78-51.47-5.21-76.47l-232.83-62.23
		C904.18,458.75,886.73,428.67,894.52,399.36z"/>
	<g>
		
			<linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="43.9833" y1="205.0724" x2="-865.9913" y2="-681.1824" gradientTransform="matrix(-0.7071 -0.7071 0.7071 -0.7071 532.5557 693.2115)">
			<stop  offset="0" style="stop-color:#FFFFFF"/>
			<stop  offset="0.0609" style="stop-color:#F6F6F6"/>
			<stop  offset="0.1612" style="stop-color:#DDDDDD"/>
			<stop  offset="0.2885" style="stop-color:#B4B4B4"/>
			<stop  offset="0.4377" style="stop-color:#7C7C7C"/>
			<stop  offset="0.6034" style="stop-color:#353535"/>
			<stop  offset="0.7178" style="stop-color:#000000"/>
		</linearGradient>
		<path class="st7" d="M498.55,721.16L296.41,923.3c77.57-68.94,153.1-74.78,214.5-58.49c0.41,0.11,0.82,0.22,1.23,0.33
			c0.16,0.04,0.31,0.08,0.47,0.13c83.22,22.73,140.06,85.76,140.06,85.76c0,0-0.01-0.01-0.01-0.01c0.01,0.01,0.01,0.01,0.01,0.01
			l170.91,170.78c69.68-28.26,132.34-70.21,184.65-122.51L730.1,721.16C666.16,657.22,562.49,657.22,498.55,721.16z"/>
		
			<linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="-214.1704" y1="-759.0476" x2="-29.7792" y2="337.7205" gradientTransform="matrix(-0.7071 -0.7071 0.7071 -0.7071 532.5557 693.2115)">
			<stop  offset="0.2225" style="stop-color:#FFFFFF"/>
			<stop  offset="0.377" style="stop-color:#AEAEAE"/>
			<stop  offset="0.5315" style="stop-color:#646464"/>
			<stop  offset="0.6587" style="stop-color:#2E2E2E"/>
			<stop  offset="0.7535" style="stop-color:#0D0D0D"/>
			<stop  offset="0.8052" style="stop-color:#000000"/>
		</linearGradient>
		<path class="st8" d="M652.67,951.02c-0.82-0.9-57.44-63.18-140.05-85.75c-0.16-0.04-0.31-0.08-0.47-0.13
			c-0.41-0.11-0.82-0.22-1.23-0.33c-61.4-16.29-136.93-10.46-214.5,58.49l-76.01,76.07c52.23,52.2,114.76,94.07,184.29,122.32
			L575,951.17C596.41,929.69,631.18,929.62,652.67,951.02z"/>
	</g>
</g>
</svg>
