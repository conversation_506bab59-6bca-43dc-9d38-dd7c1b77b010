{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\index.vue?vue&type=style&index=0&id=7b0b9f62&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\index.vue", "mtime": 1750311962980}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICJ+QC9hc3NldHMvc3R5bGVzL21peGluLnNjc3MiOw0KDQoucG9saWN5LXBhZ2Ugew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjRmNWY5Ow0KICAmLWhlYWRlciB7DQogICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCiAgICAuYmFubmVyIHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgaGVpZ2h0OiA1MHZoOw0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTsNCiAgICAgIGltZyB7DQogICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICAgIG9iamVjdC1maXQ6IGZpbGw7DQogICAgICB9DQogICAgfQ0KDQogICAgLmJvZHkgew0KICAgICAgcGFkZGluZzogMjBweCAwOw0KICAgIH0NCiAgICAuc2VhcmNoLWJveCB7DQogICAgICAvLyBwYWRkaW5nLXRvcDogNDBweDsNCiAgICAgIC5zZWFyY2gtZm9ybSB7DQogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCg0KICAgICAgICAuc2VhcmNoLWlucHV0IHsNCiAgICAgICAgICB3aWR0aDogNzkycHg7DQogICAgICAgICAgaGVpZ2h0OiA1NHB4Ow0KDQogICAgICAgICAgLnNlYXJjaC1idG4gew0KICAgICAgICAgICAgd2lkdGg6IDEwMHB4Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgIDo6di1kZWVwLmVsLWlucHV0X19pbm5lciB7DQogICAgICAgICAgICBoZWlnaHQ6IDU0cHg7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogMjdweCAwIDAgMjdweDsNCiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7DQogICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMjRweDsNCiAgICAgICAgICAgIHBhZGRpbmctbGVmdDogMzBweDsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICA6OnYtZGVlcC5lbC1pbnB1dC1ncm91cF9fYXBwZW5kIHsNCiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMyMWM5Yjg7DQogICAgICAgICAgICBib3JkZXItcmFkaXVzOiAwIDEwMHB4IDEwMHB4IDA7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjMjFjOWI4Ow0KICAgICAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICAgICAgY29sb3I6ICNmZmY7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMjRweDsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCiAgJi1ib2R5IHsNCiAgICBwYWRkaW5nOiA0MHB4IDA7DQogICAgLmNhcmQgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgZmxleC1zaHJpbms6IDA7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICAgIG1hcmdpbi1ib3R0b206IDQwcHg7DQogICAgICAmLWxlZnQgew0KICAgICAgICAvLyB3aWR0aDogMjkycHg7DQogICAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICAgICAgZmxleC1zaHJpbms6IDA7DQogICAgICAgIC5ub3RpY2UtaWNvbiB7DQogICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgLy8gaGVpZ2h0OiA2MzJweDsNCiAgICAgICAgfQ0KICAgICAgICAuZHJhdy1pY29uIHsNCiAgICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICAgICAgLy8gaGVpZ2h0OiA1MjZweDsNCiAgICAgICAgfQ0KICAgICAgICAuYXBwbHktaWNvbiB7DQogICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgaGVpZ2h0OiAzMDRweDsNCiAgICAgICAgfQ0KICAgICAgICAuaW1hZ2VCdG4gew0KICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCiAgICAgICAgICBsZWZ0OiBjYWxjKCgxMDAlIC0gMjAwcHgpIC8gMik7DQogICAgICAgICAgYm90dG9tOiAyMHB4Ow0KICAgICAgICAgIHdpZHRoOiAyMDBweDsNCiAgICAgICAgICBoZWlnaHQ6IDQ0cHg7DQogICAgICAgICAgbGluZS1oZWlnaHQ6IDQ0cHg7DQogICAgICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiAycHg7DQogICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOw0KICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgJi1yaWdodCB7DQogICAgICAgIGZsZXg6IDE7DQogICAgICAgICYubm90aWNlLWNvbnRlbnQgew0KICAgICAgICAgIC8vIGhlaWdodDogNjMycHg7DQogICAgICAgICAgcGFkZGluZy1sZWZ0OiAyNHB4Ow0KICAgICAgICAgIHBhZGRpbmctdG9wOiAyMHB4Ow0KICAgICAgICAgIG92ZXJmbG93LXk6IGF1dG87DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBmbGV4LXdyYXA6IHdyYXA7DQogICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICAgIC5ub3RpY2UtaXRlbSB7DQogICAgICAgICAgICAvLyBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgLy8gZmxleC1zaHJpbms6IDA7DQogICAgICAgICAgICAvLyBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICAgICAgICAgICAgLy8gYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgICAgIHdpZHRoOiA0OSU7DQogICAgICAgICAgICBoZWlnaHQ6IDIwMHB4Ow0KICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCiAgICAgICAgICAgIHBhZGRpbmc6IDIwcHggMjRweDsNCiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDI0cHg7DQogICAgICAgICAgICAmLWNvbnRlbnQgew0KICAgICAgICAgICAgICAvLyBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgICAvLyBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgICAgICAgICAvLyBmbGV4OiAxOw0KICAgICAgICAgICAgICAvLyBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICAgICAgICAgIC8vIGhlaWdodDogMTAwJTsNCiAgICAgICAgICAgICAgcGFkZGluZy1yaWdodDogMjRweDsNCiAgICAgICAgICAgICAgLnRpdGxlIHsNCiAgICAgICAgICAgICAgICBAaW5jbHVkZSBtdWx0aUVsbGlwc2lzKDEpOw0KICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMjRweDsNCiAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgICAgICAgIGNvbG9yOiAjMzIzMjMzOw0KICAgICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAzMnB4Ow0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIC5mb290ZXIgew0KICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgICAgICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgICAgICAgIG1hcmdpbi10b3A6IDUwcHg7DQogICAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICAgICAgICAgICAgICAuY29tcGFueSB7DQogICAgICAgICAgICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgICAgICAgICAgY29sb3I6ICM5OTk5OTk7DQogICAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMTRweDsNCiAgICAgICAgICAgICAgICAgIG1hcmdpbi1yaWdodDogNDBweDsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgLmRhdGUgew0KICAgICAgICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgICAgICAgICAgIGNvbG9yOiAjOTk5OTk5Ow0KICAgICAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDE0cHg7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgICAmLWJ0biB7DQogICAgICAgICAgICAgIEBpbmNsdWRlIGZsZXhDZW50ZXI7DQogICAgICAgICAgICAgIHdpZHRoOiAxMjhweDsNCiAgICAgICAgICAgICAgaGVpZ2h0OiA0MHB4Ow0KICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjMjFjOWI4Ow0KICAgICAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgICAgICAgICAgY29sb3I6ICNmZmZmZmY7DQogICAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAxNnB4Ow0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgJjpsYXN0LWNoaWxkIHsNCiAgICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgJi5wb2xpY3ktY29udGVudCB7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOw0KICAgICAgICAgIGZsZXgtc2hyaW5rOiAwOw0KICAgICAgICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICAgICAgICBoZWlnaHQ6IDUyNnB4Ow0KICAgICAgICAgIHBhZGRpbmc6IDI0cHg7DQogICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmZmZmZjsNCiAgICAgICAgICBtYXJnaW46IDI0cHggMCA2MHB4IDA7DQogICAgICAgICAgLnBvbGljeS1jYXJkIHsNCiAgICAgICAgICAgIGhlaWdodDogMjAycHg7DQogICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZjRmNWY5Ow0KICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICAgICAgICAgICAgb3ZlcmZsb3cteTogYXV0bzsNCiAgICAgICAgICAgIG92ZXJmbG93LXg6IGhpZGRlbjsNCiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7DQogICAgICAgICAgICBwYWRkaW5nOiAxMHB4IDA7DQogICAgICAgICAgICAuY2hlY2tib3gtcG9saWN5IHsNCiAgICAgICAgICAgICAgcGFkZGluZzogNnB4IDI2cHg7DQogICAgICAgICAgICAgIDo6di1kZWVwLmVsLWNoZWNrYm94X19sYWJlbCB7DQogICAgICAgICAgICAgICAgQGluY2x1ZGUgZWxsaXBzaXM7DQogICAgICAgICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgICAgICAgICAgICAgIG1heC13aWR0aDogMTMwcHg7DQogICAgICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7DQogICAgICAgICAgICAgICAgY29sb3I6ICMyNjI2MjY7DQogICAgICAgICAgICAgICAgbGluZS1oZWlnaHQ6IDEycHg7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgLnBvbGljeS1jb250ZW50LWZvb3RlciB7DQogICAgICAgICAgICBAaW5jbHVkZSBmbGV4Q2VudGVyOw0KICAgICAgICAgICAgLmJ0bi1jYW5jZWwgew0KICAgICAgICAgICAgICBAaW5jbHVkZSBmbGV4Q2VudGVyOw0KICAgICAgICAgICAgICB3aWR0aDogMTYwcHg7DQogICAgICAgICAgICAgIGhlaWdodDogNDBweDsNCiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICAgICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgICAgICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMTZweDsNCiAgICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiAyNXB4Ow0KICAgICAgICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICAgICAgICB9DQogICAgICAgICAgICAuYnRuLWNvbmZpcm0gew0KICAgICAgICAgICAgICBAaW5jbHVkZSBmbGV4Q2VudGVyOw0KICAgICAgICAgICAgICB3aWR0aDogMTYwcHg7DQogICAgICAgICAgICAgIGhlaWdodDogNDBweDsNCiAgICAgICAgICAgICAgYmFja2dyb3VuZDogIzIxYzliODsNCiAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4Ow0KICAgICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgICAgICAgIGNvbG9yOiAjZmZmZmZmOw0KICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMTZweDsNCiAgICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICAmLmFwcGx5LWNvbnRlbnQgew0KICAgICAgICAgIGhlaWdodDogMzA0cHg7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICAgIHBhZGRpbmctbGVmdDogMjRweDsNCiAgICAgICAgICAuYXBwbHktY2FyZCB7DQogICAgICAgICAgICB3aWR0aDogNDI2cHg7DQogICAgICAgICAgICBoZWlnaHQ6IDMwNHB4Ow0KICAgICAgICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgICAgICAgICAgICYtaGVhZGVyIHsNCiAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgICAgZmxleC1kaXJlY3Rpb246IHJvdzsNCiAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgICAgICAgICBoZWlnaHQ6IDY2cHg7DQogICAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmZmZmY7DQogICAgICAgICAgICAgIHBhZGRpbmc6IDAgMjRweDsNCiAgICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlOGU4ZTg7DQogICAgICAgICAgICAgIC5sZWZ0IHsNCiAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiByb3c7DQogICAgICAgICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICAgICAgICAgICAgICAudGFnIHsNCiAgICAgICAgICAgICAgICAgIHdpZHRoOiA0cHg7DQogICAgICAgICAgICAgICAgICBoZWlnaHQ6IDIwcHg7DQogICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAjMjFjOWI4Ow0KICAgICAgICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA4cHg7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIC50aXRsZSB7DQogICAgICAgICAgICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgICAgICAgICAgICAgY29sb3I6ICMzMzMzMzM7DQogICAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMThweDsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgLnJpZ2h0IHsNCiAgICAgICAgICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgICAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDsNCiAgICAgICAgICAgICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMThweDsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgJi1ib2R5IHsNCiAgICAgICAgICAgICAgcGFkZGluZzogMTJweCAwOw0KICAgICAgICAgICAgICAuaXRlbSB7DQogICAgICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgICAgICAgcGFkZGluZzogMTJweCAyNHB4Ow0KICAgICAgICAgICAgICAgICYtdGFnIHsNCiAgICAgICAgICAgICAgICAgIHdpZHRoOiA2cHg7DQogICAgICAgICAgICAgICAgICBoZWlnaHQ6IDZweDsNCiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICYtdGV4dCB7DQogICAgICAgICAgICAgICAgICBAaW5jbHVkZSBlbGxpcHNpczsNCiAgICAgICAgICAgICAgICAgIGZsZXg6IDE7DQogICAgICAgICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICAgICAgICBmb250LXdlaWdodDogNDAwOw0KICAgICAgICAgICAgICAgICAgY29sb3I6ICM2NjY2NjY7DQogICAgICAgICAgICAgICAgICBsaW5lLWhlaWdodDogMTZweDsNCiAgICAgICAgICAgICAgICAgIHBhZGRpbmctbGVmdDogMTRweDsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgLmVsLWVtcHR5IHsNCiAgICAgICAgICAgICAgICBwYWRkaW5nOiAwICFpbXBvcnRhbnQ7DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgICY6bGFzdC1jaGlsZCB7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDA7DQogICAgICB9DQogICAgfQ0KICB9DQp9DQouZW50ZXJwcmlzZS1saXN0LXRpdGxlLWJveCB7DQogIHdpZHRoOiAzMzZweDsNCiAgbWFyZ2luOiAwIGF1dG87DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogMzBweCAwIDQwcHg7DQogIC5lbnRlcnByaXNlLWxpc3QtdGl0bGUgew0KICAgIGZvbnQtc2l6ZTogNDBweDsNCiAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1NZWRpdW0sIFBpbmdGYW5nIFNDOw0KICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgY29sb3I6ICMzMzM7DQogICAgbGluZS1oZWlnaHQ6IDQwcHg7DQogICAgcGFkZGluZzogMCA0MHB4Ow0KICB9DQogIC5lbnRlcnByaXNlLWxpc3QtZGl2aWRlciB7DQogICAgd2lkdGg6IDQ4cHg7DQogICAgaGVpZ2h0OiA0cHg7DQogICAgYmFja2dyb3VuZDogIzIxYzliODsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiSA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/policy", "sourcesContent": ["<template>\r\n  <div class=\"policy-page\">\r\n    <div class=\"policy-page-header\">\r\n      <div class=\"banner\">\r\n        <img src=\"../../assets/policy/banner.png\" alt=\"政策大厅\" />\r\n      </div>\r\n      <div class=\"body\">\r\n        <div class=\"enterprise-list-title-box\">\r\n          <div class=\"enterprise-list-divider\"></div>\r\n          <div class=\"enterprise-list-title\">链政策</div>\r\n          <div class=\"enterprise-list-divider\"></div>\r\n        </div>\r\n        <!-- <header-tag title=\"链政策\" /> -->\r\n        <div class=\"search-box\">\r\n          <el-form ref=\"form\" class=\"search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.title\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"search-input\"\r\n              >\r\n                <el-button slot=\"append\" class=\"search-btn\" @click=\"search\"\r\n                  >搜索\r\n                </el-button>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container policy-page-body\">\r\n      <div class=\"card\">\r\n        <router-link to=\"/policymode\" class=\"card-left\">\r\n          <el-image\r\n            fit=\"contain\"\r\n            class=\"notice-icon\"\r\n            :src=\"require('../../assets/policy/zixun.png')\"\r\n          />\r\n          <div class=\"imageBtn\">查看更多</div>\r\n        </router-link>\r\n        <div v-loading=\"noticeLoading\" class=\"card-right notice-content\">\r\n          <template v-if=\"notices.length > 0\">\r\n            <router-link\r\n              class=\"notice-item\"\r\n              v-for=\"item in notices\"\r\n              :key=\"item.id\"\r\n              :to=\"`/policyDetail?id=${item.id}`\"\r\n            >\r\n              <div class=\"notice-item-content\">\r\n                <div class=\"title\">{{ item.title }}</div>\r\n                <div class=\"footer\">\r\n                  <div class=\"company\">{{ item.company }}</div>\r\n                  <div class=\"date\">{{ item.updateTime }}</div>\r\n                  <!-- <div class=\"date\">发文日期：{{ item.updateTime }}</div> -->\r\n                </div>\r\n              </div>\r\n              <div class=\"notice-item-btn\">查看详情</div>\r\n            </router-link>\r\n          </template>\r\n          <template v-else>\r\n            <el-empty />\r\n          </template>\r\n        </div>\r\n      </div>\r\n      <div>\r\n        <div class=\"card-left\">\r\n          <el-image\r\n            fit=\"contain\"\r\n            class=\"draw-icon\"\r\n            :src=\"require('../../assets/policy/huaxiang.png')\"\r\n          />\r\n        </div>\r\n        <div class=\"card-right policy-content\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\" v-for=\"item in policyItems\" :key=\"item.key\">\r\n              <div class=\"policy-card\">\r\n                <el-checkbox-group v-model=\"checkedArr\">\r\n                  <el-checkbox\r\n                    class=\"checkbox-policy\"\r\n                    v-for=\"code in item.children\"\r\n                    :label=\"code.code\"\r\n                    :key=\"code.code\"\r\n                    >{{ code.text }}</el-checkbox\r\n                  >\r\n                </el-checkbox-group>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n          <div class=\"policy-content-footer\">\r\n            <div @click=\"onCancel\" class=\"btn-cancel\">取消</div>\r\n            <div @click=\"onConfirm\" class=\"btn-confirm\">确定</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"card\">\r\n        <router-link to=\"/policyDeclare\" class=\"card-left\">\r\n          <el-image\r\n            fit=\"fill\"\r\n            class=\"apply-icon\"\r\n            :src=\"require('../../assets/policy/shenbao.png')\"\r\n          />\r\n          <div class=\"imageBtn\">在线查看</div>\r\n        </router-link>\r\n        <div class=\"card-right apply-content\">\r\n          <div class=\"apply-card\">\r\n            <router-link to=\"/policyDeclare\" class=\"apply-card-header\">\r\n              <div class=\"left\">\r\n                <div class=\"tag\" />\r\n                <div class=\"title\">科创平台</div>\r\n              </div>\r\n              <div class=\"right\">更多>></div>\r\n            </router-link>\r\n            <div class=\"apply-card-body\">\r\n              <template v-if=\"letItems.length > 0\">\r\n                <router-link\r\n                  class=\"item\"\r\n                  :to=\"`/policyDeclareDetail?id=${item.id}`\"\r\n                  v-for=\"item in letItems\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div class=\"item-tag\" />\r\n                  <div class=\"item-text\">{{ item.title }}</div>\r\n                </router-link>\r\n              </template>\r\n              <template v-else>\r\n                <el-empty />\r\n              </template>\r\n            </div>\r\n          </div>\r\n          <div class=\"apply-card\">\r\n            <router-link to=\"/policyDeclare\" class=\"apply-card-header\">\r\n              <div class=\"left\">\r\n                <div class=\"tag\" />\r\n                <div class=\"title\">人才政策</div>\r\n              </div>\r\n              <div class=\"right\">更多>></div>\r\n            </router-link>\r\n            <div class=\"apply-card-body\">\r\n              <template v-if=\"letItems.length > 0\">\r\n                <router-link\r\n                  class=\"item\"\r\n                  :to=\"`/policyDeclareDetail?id=${item.id}`\"\r\n                  v-for=\"item in rightItems\"\r\n                  :key=\"item.id\"\r\n                >\r\n                  <div class=\"item-tag\" />\r\n                  <div class=\"item-text\">{{ item.title }}</div>\r\n                </router-link>\r\n              </template>\r\n              <template v-else>\r\n                <el-empty />\r\n              </template>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { forEachObjIndexed, map } from \"ramda\";\r\n// import HeaderTag from \"@/views/components/home/<USER>\";\r\nimport { getListByText } from \"@/api/notice\";\r\nimport { listPolicyByType, listByTypePolicy } from \"@/api/zhm\";\r\n// import { policymode } from \"../components/home/<USER>\";\r\n\r\nexport default {\r\n  name: \"policyPage\",\r\n  components: {\r\n    // HeaderTag,\r\n    // policymode,\r\n  },\r\n  data() {\r\n    return {\r\n      noticeLoading: false,\r\n      form: {\r\n        title: undefined,\r\n      },\r\n      notices: [],\r\n      letItems: [],\r\n      rightItems: [],\r\n      checkedArr: [],\r\n      policyItems: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getNoticeData();\r\n    this.getTypePolicy();\r\n    this.getLeftItems();\r\n    this.getRightItems();\r\n  },\r\n  watch: {\r\n    $route(to, from) {\r\n      // to表示要跳转的路由，from表示从哪儿跳的路由   to.path\r\n      this.$router.go(0);\r\n    },\r\n  },\r\n  mounted() {\r\n    let specialLoc = this.$route.query.specialLoc;\r\n    if (specialLoc) {\r\n      let inter = setInterval(() => {\r\n        let target = this.$refs[specialLoc];\r\n        if (target) {\r\n          clearInterval(inter);\r\n          target.scrollIntoView();\r\n        }\r\n      }, 100);\r\n    }\r\n  },\r\n  methods: {\r\n    getNoticeData() {\r\n      this.noticeLoading = true;\r\n      getListByText({\r\n        typeTop: 2,\r\n        pageNum: 1,\r\n        pageSize: 4,\r\n      })\r\n        .then((res) => {\r\n          const { code, rows } = res;\r\n          if (code === 200) {\r\n            this.notices = rows;\r\n          }\r\n        })\r\n        .finally(() => (this.noticeLoading = false));\r\n    },\r\n    getLeftItems() {\r\n      listPolicyByType(100).then((res) => {\r\n        const { code, rows } = res;\r\n        if (code === 200) {\r\n          this.letItems = rows;\r\n        }\r\n      });\r\n    },\r\n    getRightItems() {\r\n      listPolicyByType(101).then((res) => {\r\n        const { code, rows } = res;\r\n        if (code === 200) {\r\n          this.rightItems = rows;\r\n        }\r\n      });\r\n    },\r\n    getTypePolicy() {\r\n      listByTypePolicy().then((res) => {\r\n        const { code, data } = res;\r\n        if (code === 200) {\r\n          const items = [];\r\n          forEachObjIndexed((value, key) => {\r\n            items.push({\r\n              key,\r\n              children: map(\r\n                (item) => ({\r\n                  id: item.id,\r\n                  code: item.labelCode,\r\n                  text: item.labelText,\r\n                }),\r\n                value\r\n              ),\r\n            });\r\n          }, data);\r\n          this.policyItems = items;\r\n        }\r\n      });\r\n    },\r\n    search() {\r\n      const { title } = this.form;\r\n      if (title) {\r\n        this.$router.push(`/notice?keyword=${title}`);\r\n      }\r\n    },\r\n    onCancel() {\r\n      this.checkedArr = [];\r\n    },\r\n    onConfirm() {\r\n      if (this.checkedArr.length === 0) {\r\n        this.$message.info(\"请选择画像\");\r\n        return;\r\n      }\r\n      this.$router.push({\r\n        name: \"policyDeclare\",\r\n        params: { code: this.checkedArr },\r\n      });\r\n      console.log(this.checkedArr);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"~@/assets/styles/mixin.scss\";\r\n\r\n.policy-page {\r\n  background-color: #f4f5f9;\r\n  &-header {\r\n    background-color: #ffffff;\r\n    .banner {\r\n      width: 100%;\r\n      height: 50vh;\r\n      background-color: #f5f5f5;\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: fill;\r\n      }\r\n    }\r\n\r\n    .body {\r\n      padding: 20px 0;\r\n    }\r\n    .search-box {\r\n      // padding-top: 40px;\r\n      .search-form {\r\n        text-align: center;\r\n\r\n        .search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n\r\n          .search-btn {\r\n            width: 100px;\r\n          }\r\n\r\n          ::v-deep.el-input__inner {\r\n            height: 54px;\r\n            background: #fff;\r\n            border-radius: 27px 0 0 27px;\r\n            border: 1px solid #d9d9d9;\r\n            font-size: 16px;\r\n            line-height: 24px;\r\n            padding-left: 30px;\r\n          }\r\n\r\n          ::v-deep.el-input-group__append {\r\n            border: 1px solid #21c9b8;\r\n            border-radius: 0 100px 100px 0;\r\n            background: #21c9b8;\r\n            font-size: 16px;\r\n            color: #fff;\r\n            line-height: 24px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &-body {\r\n    padding: 40px 0;\r\n    .card {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      flex-shrink: 0;\r\n      width: 100%;\r\n      margin-bottom: 40px;\r\n      &-left {\r\n        // width: 292px;\r\n        position: relative;\r\n        flex-shrink: 0;\r\n        .notice-icon {\r\n          width: 100%;\r\n          // height: 632px;\r\n        }\r\n        .draw-icon {\r\n          width: 100%;\r\n          height: 100%;\r\n          // height: 526px;\r\n        }\r\n        .apply-icon {\r\n          width: 100%;\r\n          height: 304px;\r\n        }\r\n        .imageBtn {\r\n          position: absolute;\r\n          left: calc((100% - 200px) / 2);\r\n          bottom: 20px;\r\n          width: 200px;\r\n          height: 44px;\r\n          line-height: 44px;\r\n          background: #ffffff;\r\n          border-radius: 2px;\r\n          text-align: center;\r\n          cursor: pointer;\r\n          color: #21c9b8;\r\n        }\r\n      }\r\n      &-right {\r\n        flex: 1;\r\n        &.notice-content {\r\n          // height: 632px;\r\n          padding-left: 24px;\r\n          padding-top: 20px;\r\n          overflow-y: auto;\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          justify-content: space-between;\r\n          .notice-item {\r\n            // display: flex;\r\n            // flex-shrink: 0;\r\n            // flex-direction: row;\r\n            // align-items: center;\r\n            width: 49%;\r\n            height: 200px;\r\n            background-color: #ffffff;\r\n            padding: 20px 24px;\r\n            margin-bottom: 24px;\r\n            &-content {\r\n              // display: flex;\r\n              // flex-direction: column;\r\n              // flex: 1;\r\n              // justify-content: space-between;\r\n              // height: 100%;\r\n              padding-right: 24px;\r\n              .title {\r\n                @include multiEllipsis(1);\r\n                font-size: 24px;\r\n                font-weight: 500;\r\n                color: #323233;\r\n                line-height: 32px;\r\n              }\r\n              .footer {\r\n                display: flex;\r\n                flex-direction: row;\r\n                align-items: center;\r\n                margin-top: 50px;\r\n                margin-bottom: 20px;\r\n                .company {\r\n                  font-size: 14px;\r\n                  font-weight: 400;\r\n                  color: #999999;\r\n                  line-height: 14px;\r\n                  margin-right: 40px;\r\n                }\r\n                .date {\r\n                  font-size: 14px;\r\n                  font-weight: 400;\r\n                  color: #999999;\r\n                  line-height: 14px;\r\n                }\r\n              }\r\n            }\r\n            &-btn {\r\n              @include flexCenter;\r\n              width: 128px;\r\n              height: 40px;\r\n              background: #21c9b8;\r\n              border-radius: 4px;\r\n              font-size: 16px;\r\n              font-weight: 500;\r\n              color: #ffffff;\r\n              line-height: 16px;\r\n            }\r\n            &:last-child {\r\n              margin-bottom: 0;\r\n            }\r\n          }\r\n        }\r\n        &.policy-content {\r\n          display: flex;\r\n          flex-direction: column;\r\n          flex-shrink: 0;\r\n          justify-content: space-between;\r\n          height: 526px;\r\n          padding: 24px;\r\n          background-color: #ffffff;\r\n          margin: 24px 0 60px 0;\r\n          .policy-card {\r\n            height: 202px;\r\n            background: #f4f5f9;\r\n            border-radius: 8px;\r\n            overflow-y: auto;\r\n            overflow-x: hidden;\r\n            margin-bottom: 10px;\r\n            padding: 10px 0;\r\n            .checkbox-policy {\r\n              padding: 6px 26px;\r\n              ::v-deep.el-checkbox__label {\r\n                @include ellipsis;\r\n                display: inline-block;\r\n                max-width: 130px;\r\n                font-size: 12px;\r\n                font-weight: 400;\r\n                color: #262626;\r\n                line-height: 12px;\r\n              }\r\n            }\r\n          }\r\n          .policy-content-footer {\r\n            @include flexCenter;\r\n            .btn-cancel {\r\n              @include flexCenter;\r\n              width: 160px;\r\n              height: 40px;\r\n              border-radius: 4px;\r\n              border: 1px solid #d9d9d9;\r\n              font-size: 16px;\r\n              font-weight: 500;\r\n              color: #333333;\r\n              line-height: 16px;\r\n              margin-right: 25px;\r\n              cursor: pointer;\r\n            }\r\n            .btn-confirm {\r\n              @include flexCenter;\r\n              width: 160px;\r\n              height: 40px;\r\n              background: #21c9b8;\r\n              border-radius: 4px;\r\n              font-size: 16px;\r\n              font-weight: 500;\r\n              color: #ffffff;\r\n              line-height: 16px;\r\n              cursor: pointer;\r\n            }\r\n          }\r\n        }\r\n        &.apply-content {\r\n          height: 304px;\r\n          display: flex;\r\n          flex-direction: row;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          padding-left: 24px;\r\n          .apply-card {\r\n            width: 426px;\r\n            height: 304px;\r\n            background: #ffffff;\r\n            &-header {\r\n              display: flex;\r\n              flex-direction: row;\r\n              align-items: center;\r\n              justify-content: space-between;\r\n              height: 66px;\r\n              background-color: #ffffff;\r\n              padding: 0 24px;\r\n              border-bottom: 1px solid #e8e8e8;\r\n              .left {\r\n                display: flex;\r\n                flex-direction: row;\r\n                align-items: center;\r\n                .tag {\r\n                  width: 4px;\r\n                  height: 20px;\r\n                  background: #21c9b8;\r\n                  margin-right: 8px;\r\n                }\r\n                .title {\r\n                  font-size: 18px;\r\n                  font-weight: 500;\r\n                  color: #333333;\r\n                  line-height: 18px;\r\n                }\r\n              }\r\n              .right {\r\n                font-size: 18px;\r\n                font-weight: 400;\r\n                color: #21c9b8;\r\n                line-height: 18px;\r\n              }\r\n            }\r\n            &-body {\r\n              padding: 12px 0;\r\n              .item {\r\n                display: flex;\r\n                flex-direction: row;\r\n                align-items: center;\r\n                padding: 12px 24px;\r\n                &-tag {\r\n                  width: 6px;\r\n                  height: 6px;\r\n                  background: #21c9b8;\r\n                }\r\n                &-text {\r\n                  @include ellipsis;\r\n                  flex: 1;\r\n                  font-size: 16px;\r\n                  font-weight: 400;\r\n                  color: #666666;\r\n                  line-height: 16px;\r\n                  padding-left: 14px;\r\n                }\r\n              }\r\n              .el-empty {\r\n                padding: 0 !important;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n.enterprise-list-title-box {\r\n  width: 336px;\r\n  margin: 0 auto;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 30px 0 40px;\r\n  .enterprise-list-title {\r\n    font-size: 40px;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #333;\r\n    line-height: 40px;\r\n    padding: 0 40px;\r\n  }\r\n  .enterprise-list-divider {\r\n    width: 48px;\r\n    height: 4px;\r\n    background: #21c9b8;\r\n  }\r\n}\r\n</style>\r\n"]}]}