{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\im\\components\\video.vue?vue&type=style&index=0&id=158075bf&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\im\\components\\video.vue", "mtime": 1750311963058}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnZpZGVvIC8q5Y+v5LiN6K6+572uKi8gew0KICAudmlkZW8tcGxheWVyIHsNCiAgICBoZWlnaHQ6IDUwMHB4Ow0KICB9DQogIC52anMtY3VzdG9tLXNraW4gPiAudmlkZW8tanMgew0KICAgIHdpZHRoOiAxMDAlOw0KICAgIGhlaWdodDogMTAwJTsNCiAgfQ0KICAvLyBwb3NpdGlvbjpyZWxhdGl2ZTsNCiAgLy8gcGFkZGluZzo1cHg7DQogIC8vIHBhZGRpbmctdG9wOiA3MHB4Ow0KICAvLyAgLnZqcy1wb3N0ZXIgLyrlr7nnlLvpnaLpnZnmraLnmoTmoLflvI/ov5vooYzorr7nva7mlL7lpKfkuIDlgI0qLw0KICAvLyAgew0KICAvLyAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgyKQ0KICAvLyAgfQ0KICAvLyAgLnZqcy10ZWNoIC8q5a+555S76Z2i5pKt5pS+55qE5qC35byP6L+b6KGM6K6+572u5pS+5aSn5LiA5YCNKi8NCiAgLy8gIHsNCiAgLy8gICAgICB0cmFuc2Zvcm06IHNjYWxlKDIpDQogIC8vICB9DQogIC8vICAgLnZpZGVvLWpzIC52anMtYmlnLXBsYXktYnV0dG9uIC8q5a+55pKt5pS+5oyJ6ZKu55qE5qC35byP6L+b6KGM6K6+572uKi8NCiAgLy8gICB7DQogIC8vICAgICAgIHdpZHRoOiA4MHB4Ow0KICAvLyAgICAgICAgIGhlaWdodDogMTAwJTsNCiAgLy8gICAgICAgICBib3JkZXItcmFkaXVzOiAxZW07DQogIC8vICAgfQ0KfQ0K"}, {"version": 3, "sources": ["video.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "video.vue", "sourceRoot": "src/views/system/user/im/components", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-05-31 11:30:14\r\n * @LastEditTime: 2023-05-31 11:51:13\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"video\">\r\n    <!-- 使用组件 -->\r\n    <video-player\r\n      class=\"video-player vjs-custom-skin\"\r\n      ref=\"videoPlayer\"\r\n      :playsinline=\"true\"\r\n      :options=\"playerOptions\"\r\n    ></video-player>\r\n  </div>\r\n</template>\r\n\r\n<script type=\"text/ecmascript-6\">\r\n// 以下三行一定要引入\r\nimport { videoPlayer } from 'vue-video-player'\r\nimport 'video.js/dist/video-js.css'\r\nimport 'vue-video-player/src/custom-theme.css'\r\n// import 'video.js/dist/lang/zh-CN'\r\n\r\nexport default {\r\n// name: 'videoplayer',\r\ncomponents: { // 必需引入\r\n  videoPlayer\r\n},\r\nprops: [ // 接收父组件的数据\r\n  'mp4Pic',\r\n  'mp4Url'\r\n],\r\ndata () {\r\n  return {\r\n    fileAreaHeight: 100,\r\n    fileType: 'mp4', // 资源的类型\r\n  }\r\n},\r\ncomputed: { // 使用计算属性\r\n    playerOptions () {\r\n      const playerOptionsObj = {\r\n        playbackRates: [0.7, 1.0, 1.5, 2.0], //视频播放速度\r\n        autoplay: true, // 如果true，浏览器准备好时开始回放。\r\n        muted: false, // 默认情况下将会消除任何音频。\r\n        loop: false, // 导致视频一结束就重新开始。\r\n        // preload: 'auto', // 建议浏览器在<video>加载元素后是否应该开始下载视频数据。auto浏览器选择最佳行为,立即开始加载视频（如果浏览器支持）。\r\n        language: 'zh-CN',\r\n        // aspectRatio: '16:9', // 将播放器置于流畅模式，并在计算播放器的动态大小时使用该值。值应该代表一个比例 - 用冒号分隔的两个数字（例如\"16:9\"或\"4:3\"）。\r\n        fluid: false, // 当true时，Video.js player将拥有流体大小。换句话说，它将按比例缩放以适应其容器。\r\n        sources: [{\r\n          type: 'video/' + this.fileType,\t// 资源格式写法：'video/mp4'，否则控制台会出现notSupportedMessage设置的错误。\r\n          src: this.mp4Url // 视频url地址\r\n        }],\r\n        poster: this.mp4Pic, // 视频封面地址\r\n        // width: document.documentElement.clientWidth,\r\n        height: this.fileAreaHeight,\t// 设置高度，fluid需要设置成flase\r\n        notSupportedMessage: '此视频暂无法播放...', // 允许覆盖Video.js无法播放媒体源时显示的默认信息。\r\n        controlBar: {\r\n          timeDivider: true,\r\n          durationDisplay: true,\r\n          remainingTimeDisplay: false,\r\n          fullscreenToggle: true  //全屏按钮\r\n        }\r\n      }\r\n      return playerOptionsObj\r\n    }\r\n  },\r\n  watch: {\r\n\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.video /*可不设置*/ {\r\n  .video-player {\r\n    height: 500px;\r\n  }\r\n  .vjs-custom-skin > .video-js {\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  // position:relative;\r\n  // padding:5px;\r\n  // padding-top: 70px;\r\n  //  .vjs-poster /*对画面静止的样式进行设置放大一倍*/\r\n  //  {\r\n  //        transform: scale(2)\r\n  //  }\r\n  //  .vjs-tech /*对画面播放的样式进行设置放大一倍*/\r\n  //  {\r\n  //      transform: scale(2)\r\n  //  }\r\n  //   .video-js .vjs-big-play-button /*对播放按钮的样式进行设置*/\r\n  //   {\r\n  //       width: 80px;\r\n  //         height: 100%;\r\n  //         border-radius: 1em;\r\n  //   }\r\n}\r\n</style>\r\n"]}]}