# SSO单点登录部署指南

## 📋 概述

本文档描述了主系统（share-intelligent-backend）与从系统（share-intelligent-market）之间的SSO单点登录实现方案。

### 🏗️ 架构说明

- **主系统**: share-intelligent-backend (数据库: industry)
- **从系统**: share-intelligent-market (数据库: market)
- **认证流程**: 主系统作为认证中心，从系统作为客户端

## 🔧 系统配置

### 1. 主系统配置 (share-intelligent-backend)

#### 1.1 修改配置文件
在 `ruoyi-auth/src/main/resources/bootstrap.yml` 中添加：

```yaml
# SSO单点登录配置
sso:
  # 主系统配置
  main-system:
    system-id: backend
    name: 复合材料共享智造平台
    url: http://localhost:9200
  # 从系统配置
  systems:
    market:
      name: 智能市场系统
      url: http://localhost:8081
      callback: /sso/login
      database: market
      sync-user-url: /sso/sync-user
  # Token配置
  token:
    expire-minutes: 30
    prefix: sso_token:
  # 缓存配置
  cache:
    redis-prefix: sso:
```

#### 1.2 添加的文件
- `SSOController.java` - SSO认证中心控制器
- `SSOService.java` - SSO认证服务实现
- `SSOConfig.java` - SSO配置类

### 2. 从系统配置 (share-intelligent-market)

#### 2.1 修改配置文件
在 `ruoyi-auth/src/main/resources/bootstrap.yml` 中添加：

```yaml
# SSO单点登录配置
sso:
  # 主系统配置
  main-system:
    url: http://localhost:9200
    verify-token-url: /sso/verify
    login-url: /sso/login
    logout-url: /sso/logout
    status-url: /sso/status
  # 当前系统配置
  current-system:
    system-id: market
    name: 智能市场系统
    callback-url: http://localhost:8081/sso/login
    database: market
  # Token配置
  token:
    expire-minutes: 480
    prefix: local_token:
  # 缓存配置
  cache:
    redis-prefix: sso_client:
```

#### 2.2 添加的文件
- `SSOClientController.java` - SSO客户端控制器
- `SSOClientService.java` - SSO客户端服务实现
- `SSOClientConfig.java` - SSO客户端配置类

### 3. 前端配置 (从系统前端)

#### 3.1 添加前端文件
- `src/utils/sso.js` - SSO前端工具类
- `src/router/sso-guard.js` - SSO路由守卫

#### 3.2 环境变量配置
在 `.env.development` 中添加：

```env
# 主系统地址
VUE_APP_MAIN_SYSTEM_URL=http://localhost:9200
```

## 🚀 部署步骤

### 1. 数据库准备

#### 1.1 主系统数据库 (industry)
确保主系统数据库已正确配置用户表和权限表。

#### 1.2 从系统数据库 (market)
确保从系统数据库已配置，可以创建或同步用户信息。

### 2. 服务启动顺序

1. **启动Redis服务** (用于SSO Token缓存)
2. **启动Nacos服务** (服务注册与配置中心)
3. **启动主系统后端服务**
   ```bash
   cd share-intelligent-backend
   # 启动认证中心
   java -jar ruoyi-auth/target/ruoyi-auth.jar
   # 启动其他微服务...
   ```
4. **启动从系统后端服务**
   ```bash
   cd share-intelligent-market
   # 启动认证服务
   java -jar ruoyi-auth/target/ruoyi-auth.jar
   # 启动其他微服务...
   ```
5. **启动前端服务**
   ```bash
   cd share-intelligent-market/ruoyi-ui
   npm run dev
   ```

### 3. 网络配置

确保以下端口可以正常访问：
- 主系统认证中心: 9200
- 从系统认证中心: 9700
- 从系统前端: 8081
- Redis: 6379
- Nacos: 8848

## 🔄 SSO登录流程

### 1. 用户访问从系统
1. 用户访问从系统 `http://localhost:8081`
2. 前端检查本地是否有有效token
3. 如无token，跳转到主系统登录

### 2. 主系统认证
1. 用户在主系统登录 `http://localhost:9200/sso/login?target=market`
2. 主系统验证用户凭据
3. 生成SSO Token并跳转回从系统

### 3. 从系统处理
1. 从系统接收SSO Token
2. 调用主系统验证Token
3. 创建或更新本地用户
4. 生成本地session
5. 跳转到目标页面

## 🔧 接口说明

### 主系统SSO接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/sso/login` | GET | SSO登录入口 |
| `/sso/verify` | POST | 验证SSO Token |
| `/sso/userinfo/{userId}` | GET | 获取用户信息 |
| `/sso/logout` | POST | SSO登出 |
| `/sso/status` | GET | 检查SSO状态 |
| `/sso/sync-user` | POST | 同步用户信息 |

### 从系统SSO接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/sso/login` | GET | SSO登录回调 |
| `/sso/logout` | POST | SSO登出 |
| `/sso/status` | GET | 检查登录状态 |
| `/sso/loginUrl` | GET | 获取主系统登录地址 |
| `/sso/sync-user` | POST | 接收用户同步 |

## 🛠️ 故障排除

### 1. Token验证失败
- 检查Redis连接
- 确认Token未过期
- 验证系统标识匹配

### 2. 跨域问题
- 配置CORS允许跨域访问
- 检查前端代理配置

### 3. 用户同步失败
- 检查数据库连接
- 确认用户表结构
- 验证用户服务接口

### 4. 网络连接问题
- 检查服务间网络连通性
- 确认防火墙配置
- 验证端口开放状态

## 📝 注意事项

1. **安全性**
   - SSO Token有效期较短（30分钟）
   - Token一次性使用
   - 使用HTTPS传输（生产环境）

2. **性能**
   - Redis缓存Token信息
   - 避免频繁的跨系统调用
   - 合理设置连接超时

3. **监控**
   - 记录SSO登录日志
   - 监控Token验证成功率
   - 跟踪用户同步状态

## 🔄 扩展说明

### 添加新的从系统
1. 在主系统配置中添加新系统信息
2. 新系统实现SSO客户端接口
3. 配置相应的回调地址和同步接口

### 自定义用户同步
1. 实现自定义的用户映射逻辑
2. 扩展用户信息同步字段
3. 处理用户权限同步

## 🧪 测试验证

### 1. 功能测试脚本

创建测试脚本验证SSO功能：

```bash
#!/bin/bash
# SSO功能测试脚本

echo "=== SSO功能测试 ==="

# 1. 测试主系统SSO状态接口
echo "1. 测试主系统SSO状态..."
curl -X GET "http://localhost:9200/sso/status"

# 2. 测试从系统SSO状态接口
echo "2. 测试从系统SSO状态..."
curl -X GET "http://localhost:8081/sso/status"

# 3. 测试获取主系统登录地址
echo "3. 测试获取主系统登录地址..."
curl -X GET "http://localhost:8081/sso/loginUrl?redirect=http://localhost:8081/dashboard"

echo "=== 测试完成 ==="
```

### 2. 手动测试步骤

1. **清除浏览器缓存和Cookie**
2. **访问从系统**: `http://localhost:8081`
3. **验证自动跳转**: 应跳转到主系统登录页面
4. **主系统登录**: 输入用户名密码登录
5. **验证回调**: 应自动跳转回从系统并完成登录
6. **测试登出**: 在从系统登出，验证主系统也已登出

### 3. 日志检查

检查以下日志确认SSO流程：
- 主系统认证中心日志
- 从系统认证服务日志
- Redis缓存操作日志
- 前端浏览器控制台日志

这个SSO方案提供了完整的单点登录功能，支持多系统间的用户认证和信息同步。
