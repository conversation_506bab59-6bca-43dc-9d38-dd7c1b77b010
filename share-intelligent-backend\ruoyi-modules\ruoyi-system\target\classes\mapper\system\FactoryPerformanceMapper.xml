<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FactoryPerformanceMapper">
    
    <resultMap type="FactoryPerformance" id="FactoryPerformanceResult">
        <result property="id"    column="id"    />
        <result property="factoryId"    column="factory_id"    />
        <result property="projectName"    column="project_name"    />
        <result property="contactPerson"    column="contact_person"    />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="attachment"    column="attachment"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectFactoryPerformanceVo">
        select id, factory_id, project_name, contact_person, contact_phone, attachment, create_time, update_time from factory_performance
    </sql>

    <select id="selectFactoryPerformanceList" parameterType="FactoryPerformance" resultMap="FactoryPerformanceResult">
        <include refid="selectFactoryPerformanceVo"/>
        <where>  
            <if test="factoryId != null "> and factory_id = #{factoryId}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="contactPerson != null  and contactPerson != ''"> and contact_person = #{contactPerson}</if>
            <if test="contactPhone != null  and contactPhone != ''"> and contact_phone = #{contactPhone}</if>
            <if test="attachment != null  and attachment != ''"> and attachment = #{attachment}</if>
        </where>
    </select>
    
    <select id="selectFactoryPerformanceById" parameterType="Long" resultMap="FactoryPerformanceResult">
        <include refid="selectFactoryPerformanceVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFactoryPerformance" parameterType="FactoryPerformance" useGeneratedKeys="true" keyProperty="id">
        insert into factory_performance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="factoryId != null">factory_id,</if>
            <if test="projectName != null and projectName != ''">project_name,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="attachment != null">attachment,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="factoryId != null">#{factoryId},</if>
            <if test="projectName != null and projectName != ''">#{projectName},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="attachment != null">#{attachment},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateFactoryPerformance" parameterType="FactoryPerformance">
        update factory_performance
        <trim prefix="SET" suffixOverrides=",">
            <if test="factoryId != null">factory_id = #{factoryId},</if>
            <if test="projectName != null and projectName != ''">project_name = #{projectName},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="attachment != null">attachment = #{attachment},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFactoryPerformanceById" parameterType="Long">
        delete from factory_performance where id = #{id}
    </delete>

    <delete id="deleteFactoryPerformanceByIds" parameterType="String">
        delete from factory_performance where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>