{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\AppMain.vue?vue&type=style&index=1&id=078753dd&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\AppMain.vue", "mtime": 1750311962841}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8vIGZpeCBjc3Mgc3R5bGUgYnVnIGluIG9wZW4gZWwtZGlhbG9nDQouZWwtcG9wdXAtcGFyZW50LS1oaWRkZW4gew0KICAuZml4ZWQtaGVhZGVyIHsNCiAgICBwYWRkaW5nLXJpZ2h0OiAxN3B4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["AppMain.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;AACA;AACA;AACA;AACA;AACA", "file": "AppMain.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\r\n  <section class=\"app-main el-scrollbar\">\r\n    <transition name=\"fade-transform\" mode=\"out-in\">\r\n      <keep-alive :include=\"cachedViews\">\r\n        <router-view v-if=\"!$route.meta.link\" :key=\"key\" />\r\n      </keep-alive>\r\n    </transition>\r\n    <iframe-toggle />\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport iframeToggle from \"./IframeToggle/index\"\r\n\r\nexport default {\r\n  name: 'AppMain',\r\n  components: { iframeToggle },\r\n  computed: {\r\n    cachedViews() {\r\n      return this.$store.state.tagsView.cachedViews\r\n    },\r\n    key() {\r\n      return this.$route.path\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-main {\r\n  /* 50= navbar  50  */\r\n  min-height: calc(100vh - 80px);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow-x: hidden;\r\n}\r\n\r\n.fixed-header + .app-main {\r\n  padding-top: 80px;\r\n}\r\n\r\n.hasTagsView {\r\n  .app-main {\r\n    /* 84 = navbar + tags-view = 50 + 34 */\r\n    min-height: calc(100vh - 84px);\r\n  }\r\n\r\n  .fixed-header + .app-main {\r\n    padding-top: 84px;\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n// fix css style bug in open el-dialog\r\n.el-popup-parent--hidden {\r\n  .fixed-header {\r\n    padding-right: 17px;\r\n  }\r\n}\r\n</style>\r\n"]}]}