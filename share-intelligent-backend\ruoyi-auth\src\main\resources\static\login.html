<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>复合材料共享智造平台 - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 400px;
            max-width: 90%;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 26px;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #666;
            font-size: 14px;
        }
        
        .system-info {
            background: #f0f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .system-info h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .system-info p {
            color: #666;
            font-size: 14px;
        }
        
        .sso-notice {
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .sso-notice h4 {
            color: #155724;
            margin-bottom: 10px;
        }
        
        .sso-notice p {
            color: #155724;
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .sso-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        
        .sso-btn:hover {
            background: #218838;
        }
        
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #ddd;
        }
        
        .divider span {
            background: white;
            padding: 0 15px;
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
        
        .success-message {
            color: #27ae60;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🏭 复合材料共享智造平台</h1>
            <p>欢迎登录主系统</p>
        </div>
        
        <div class="system-info">
            <h3>🔗 主系统登录</h3>
            <p>您正在登录复合材料共享智造平台（主系统）</p>
        </div>
        
        <div class="sso-notice">
            <h4>🔐 推荐使用SSO统一登录</h4>
            <p>使用SSO登录可以实现多系统间的无缝切换</p>
            <button class="sso-btn" onclick="redirectToSSO()">
                前往SSO统一登录
            </button>
        </div>
        
        <div class="divider">
            <span>或使用本地账号登录</span>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" required placeholder="请输入用户名">
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required placeholder="请输入密码">
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                本地登录
            </button>
            
            <div id="message"></div>
        </form>
    </div>

    <script>
        // 获取URL参数
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                redirect: params.get('redirect'),
                error: params.get('error')
            };
        }

        // 跳转到SSO登录
        function redirectToSSO() {
            const params = getUrlParams();
            let ssoUrl = '/sso/login';
            
            if (params.redirect) {
                ssoUrl += '?redirect=' + encodeURIComponent(params.redirect);
            }
            
            window.location.href = ssoUrl;
        }

        // 显示消息
        function showMessage(message, type = 'error') {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = message;
            messageDiv.className = type === 'error' ? 'error-message' : 'success-message';
        }

        // 本地登录处理
        async function handleLocalLogin(event) {
            event.preventDefault();
            
            const loginBtn = document.getElementById('loginBtn');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const params = getUrlParams();
            
            if (!username || !password) {
                showMessage('请输入用户名和密码');
                return;
            }
            
            // 禁用登录按钮
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                const response = await fetch('/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage('登录成功，正在跳转...', 'success');
                    
                    // 跳转到指定页面或首页
                    const redirectUrl = params.redirect || '/index';
                    setTimeout(() => {
                        window.location.href = redirectUrl;
                    }, 1000);
                } else {
                    showMessage(result.msg || '登录失败');
                }
                
            } catch (error) {
                console.error('登录请求失败:', error);
                showMessage('网络错误，请稍后重试');
            } finally {
                // 恢复登录按钮
                loginBtn.disabled = false;
                loginBtn.textContent = '本地登录';
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            const params = getUrlParams();
            
            // 显示错误信息
            if (params.error) {
                const errorMessages = {
                    'invalid_token': 'Token无效，请重新登录',
                    'token_expired': 'Token已过期，请重新登录',
                    'access_denied': '访问被拒绝',
                    'callback_error': 'SSO回调处理失败',
                    'user_sync_failed': '用户信息同步失败'
                };
                
                const errorMessage = errorMessages[params.error] || '登录失败';
                showMessage(errorMessage);
            }
            
            // 绑定登录表单
            document.getElementById('loginForm').addEventListener('submit', handleLocalLogin);
            
            // 自动聚焦到用户名输入框
            document.getElementById('username').focus();
        });
    </script>
</body>
</html>
