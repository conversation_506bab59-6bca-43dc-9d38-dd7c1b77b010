{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\supplyForm.vue?vue&type=style&index=0&id=35ebacfb&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\form\\components\\supplyForm.vue", "mtime": 1750311962955}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["supplyForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "supplyForm.vue", "sourceRoot": "src/views/form/components", "sourcesContent": ["<template>\r\n  <div class=\"supply-form\">\r\n    <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n      <el-form-item label=\"资源类型\" prop=\"supplyType\">\r\n        <el-checkbox-group\r\n          v-model=\"form.supplyType\"\r\n          placeholder=\"请选择\"\r\n          clearable\r\n        >\r\n          <el-checkbox\r\n            v-for=\"dict in dict.type.supply_type\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.value\"\r\n            :value=\"dict.value\"\r\n            >{{ dict.label }}</el-checkbox\r\n          >\r\n        </el-checkbox-group>\r\n      </el-form-item>\r\n      <el-form-item prop=\"supplyName\" label=\"资源标题\">\r\n        <el-input\r\n          v-model=\"form.supplyName\"\r\n          maxlength=\"50\"\r\n          show-word-limit\r\n          placeholder=\"请输入\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"summary\" label=\"资源描述\">\r\n        <el-input\r\n          type=\"textarea\"\r\n          v-model=\"form.summary\"\r\n          maxlength=\"500\"\r\n          rows=\"6\"\r\n          show-word-limit\r\n          placeholder=\"请输入资源描述\"\r\n        ></el-input>\r\n        <div class=\"extra-content\">\r\n          <div class=\"extra-content-header\">\r\n            <el-button\r\n              :loading=\"keywordLoading\"\r\n              @click=\"handleKeywordList\"\r\n              size=\"small\"\r\n              type=\"primary\"\r\n              >生成关键词</el-button\r\n            >\r\n            <span class=\"tip\">生成关键词有利于实现精准匹配哦！</span>\r\n          </div>\r\n          <div v-if=\"form.keywords.length > 0\" class=\"extra-content-body\">\r\n            <el-tag\r\n              :key=\"`${tag}_${index}`\"\r\n              v-for=\"(tag, index) in form.keywords\"\r\n              closable\r\n              size=\"small\"\r\n              disable-transitions\r\n              @close=\"handleClose(tag)\"\r\n            >\r\n              {{ tag }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item prop=\"technologyType\" label=\"技术类别\">\r\n        <el-select\r\n          v-model=\"form.technologyType\"\r\n          filterable\r\n          allow-create\r\n          multiple\r\n          style=\"width: 100%\"\r\n          placeholder=\"请选择\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in dict.type.technology_type\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.label\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item prop=\"applicationArea\" label=\"应用领域\">\r\n        <el-select\r\n          v-model=\"form.applicationArea\"\r\n          filterable\r\n          allow-create\r\n          multiple\r\n          style=\"width: 100%\"\r\n          placeholder=\"请选择\"\r\n        >\r\n          <el-option\r\n            v-for=\"item in dict.type.application_area\"\r\n            :key=\"item.value\"\r\n            :label=\"item.label\"\r\n            :value=\"item.label\"\r\n          >\r\n          </el-option>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"资源图片\" prop=\"productPhoto\">\r\n        <ImageUpload v-model=\"form.productPhoto\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"合作方式\" prop=\"cooperationMode\">\r\n        <el-select\r\n          v-model=\"form.cooperationMode\"\r\n          placeholder=\"请选择\"\r\n          style=\"width: 100%\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.cooperation_mode\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"产品阶段\" prop=\"productStage\">\r\n        <el-select\r\n          v-model=\"form.productStage\"\r\n          placeholder=\"请选择\"\r\n          style=\"width: 100%\"\r\n          clearable\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.product_stage\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"上传附件\" prop=\"enclosure\">\r\n        <FileUpload v-model=\"form.enclosure\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"公司名称\" prop=\"companyName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.companyName\"\r\n          placeholder=\"请先绑定公司\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系人\" prop=\"contactsName\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactsName\"\r\n          placeholder=\"请先维护联系人\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"联系方式\" prop=\"contactsMobile\">\r\n        <el-input\r\n          disabled\r\n          v-model=\"form.contactsMobile\"\r\n          placeholder=\"请先维护联系方式\"\r\n        ></el-input>\r\n      </el-form-item>\r\n      <el-form-item class=\"footer-submit\">\r\n        <el-button @click.once=\"onCancel\">取消</el-button>\r\n        <el-button @click=\"onSubmit('0')\" type=\"primary\" plain\r\n          >暂存草稿</el-button\r\n        >\r\n        <el-button type=\"primary\" @click=\"onSubmit('2')\">发布</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { keywordList, supplyAdd } from \"@/api/zhm\";\r\nimport cache from \"@/plugins/cache\";\r\n\r\nexport default {\r\n  name: \"supplyForm\",\r\n  dicts: [\r\n    \"supply_type\",\r\n    \"technology_type\",\r\n    \"cooperation_mode\",\r\n    \"product_stage\",\r\n    \"application_area\",\r\n  ],\r\n  data() {\r\n    const { user } = this.$store.state;\r\n    return {\r\n      loading: false,\r\n      keywordLoading: false,\r\n      form: {\r\n        // 供给类型\r\n        supplyType: [],\r\n        // 需求标题\r\n        supplyName: undefined,\r\n        // 描述\r\n        summary: undefined,\r\n        // 关键词\r\n        keywords: [],\r\n        // 技术类别\r\n        technologyType: [],\r\n        // 应用领域\r\n        applicationArea: [],\r\n        // 产品图片\r\n        productPhoto: [],\r\n        // 附件\r\n        enclosure: [],\r\n        // 产品阶段\r\n        productStage: undefined,\r\n        // 合作方式\r\n        cooperationMode: undefined,\r\n        // 公司名称\r\n        companyName: user.companyName || \"柠檬豆\",\r\n        // 联系人\r\n        contactsName: user.name,\r\n        // 联系电话\r\n        contactsMobile: user.tel,\r\n        publisherName: user.name,\r\n        publisherMobile: user.tel,\r\n        businessNo: user.bussinessNo,\r\n        auditStatus: \"2\",\r\n        displayStatus: \"1\",\r\n      },\r\n      rules: {\r\n        supplyType: [\r\n          { required: true, message: \"请选择供给类型\", trigger: \"blur\" },\r\n        ],\r\n        supplyName: [\r\n          { required: true, message: \"请输入供给标题\", trigger: \"blur\" },\r\n        ],\r\n        summary: [\r\n          { required: true, message: \"请输入供给描述\", trigger: \"blur\" },\r\n        ],\r\n        technologyType: [\r\n          { required: true, message: \"请选择技术类别\", trigger: \"blur\" },\r\n        ],\r\n        applicationArea: [\r\n          { required: true, message: \"请选择应用领域\", trigger: \"blur\" },\r\n        ],\r\n        productPhoto: [\r\n          { required: true, message: \"请选择供给图片\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"请维护公司名称\", trigger: \"blur\" },\r\n        ],\r\n        contactsName: [\r\n          { required: true, message: \"请维护联系人\", trigger: \"blur\" },\r\n        ],\r\n        contactsMobile: [\r\n          { required: true, message: \"请维护联系电话\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  methods: {\r\n    init() {\r\n      const data = cache.local.getJSON(\"supply_data\");\r\n      if (data) {\r\n        this.form = data;\r\n      }\r\n    },\r\n    onCancel() {\r\n      this.$router.back();\r\n    },\r\n    onSave() {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          cache.local.setJSON(\"supply_data\", this.form);\r\n          this.$message.success(\"暂存成功\");\r\n        }\r\n      });\r\n    },\r\n    onSubmit(status) {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          const {\r\n            supplyType,\r\n            keywords,\r\n            applicationArea,\r\n            productPhoto,\r\n            enclosure,\r\n            technologyType,\r\n            ...rest\r\n          } = this.form;\r\n          const data = {\r\n            ...rest,\r\n            auditStatus: status,\r\n          };\r\n          if (supplyType.length > 0) {\r\n            data[\"supplyType\"] = supplyType.join();\r\n          }\r\n          if (keywords.length > 0) {\r\n            data[\"keywords\"] = keywords.join();\r\n          }\r\n          if (technologyType.length > 0) {\r\n            data[\"technologyType\"] = technologyType.join();\r\n          }\r\n          if (applicationArea.length > 0) {\r\n            data[\"applicationArea\"] = applicationArea.join();\r\n          }\r\n          if (productPhoto.length > 0) {\r\n            data[\"productPhoto\"] = JSON.stringify(productPhoto);\r\n          }\r\n          if (enclosure.length > 0) {\r\n            data[\"enclosure\"] = JSON.stringify(enclosure);\r\n          }\r\n          supplyAdd(data)\r\n            .then((res) => {\r\n              const { code, msg } = res;\r\n              if (code === 200) {\r\n                cache.local.remove(\"supply_data\");\r\n                this.$message.success(\"发布成功\");\r\n                this.$router.back();\r\n              } else {\r\n                this.$message.error(msg || \"发布失败\");\r\n              }\r\n            })\r\n            .finally(() => (this.loading = false));\r\n        }\r\n      });\r\n    },\r\n    handleKeywordList() {\r\n      const { summary } = this.form;\r\n      if (summary) {\r\n        this.keywordLoading = true;\r\n        keywordList(summary)\r\n          .then((res) => {\r\n            const { code, data, msg } = res;\r\n            if (code === 200) {\r\n              this.form.keywords = data;\r\n            } else {\r\n              this.$message.error(msg);\r\n            }\r\n          })\r\n          .finally(() => (this.keywordLoading = false));\r\n      } else {\r\n        this.$message.warning(\"请输入需求描述\");\r\n      }\r\n    },\r\n\r\n    handleClose(tag) {\r\n      this.form.keywords = this.form.keywords.filter((item) => item !== tag);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.supply-form {\r\n  width: 676px;\r\n  .label-item {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    .title {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #333333;\r\n      line-height: 18px;\r\n    }\r\n    .extra {\r\n      font-size: 14px;\r\n      font-weight: 400;\r\n      color: #999999;\r\n      line-height: 14px;\r\n    }\r\n  }\r\n  .extra-content {\r\n    padding: 12px 0;\r\n    &-header {\r\n      display: flex;\r\n      flex-direction: row;\r\n      align-items: center;\r\n      .tip {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        color: #999999;\r\n        line-height: 14px;\r\n        margin-left: 12px;\r\n      }\r\n    }\r\n    &-body {\r\n      padding-top: 6px;\r\n      .el-tag {\r\n        margin-right: 12px;\r\n        &:last-child {\r\n          margin-right: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  ::v-deep.el-form-item__label {\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    line-height: 18px;\r\n    margin-bottom: 12px;\r\n    padding: 0;\r\n  }\r\n  .el-checkbox {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #262626;\r\n    line-height: 18px;\r\n    margin-right: 28px;\r\n  }\r\n  .footer-submit {\r\n    display: flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-top: 40px;\r\n    .el-button {\r\n      width: 160px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}