{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\demandHall\\index.vue?vue&type=style&index=1&id=9fc74ad2&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\demandHall\\index.vue", "mtime": 1750311962985}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZGVtYW5kLWhhbGwtY29udGFpbmVyIHsNCiAgLmRlbWFuZC1oYWxsLXNlYXJjaC1pbnB1dCB7DQogICAgLmVsLWlucHV0X19pbm5lciB7DQogICAgICBoZWlnaHQ6IDU0cHg7DQogICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgYm9yZGVyLXJhZGl1czogMjdweCAwIDAgMjdweDsNCiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7DQogICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICBmb250LWZhbWlseTogUGluZ0ZhbmdTQy1SZWd1bGFyLCBQaW5nRmFuZyBTQzsNCiAgICAgIGxpbmUtaGVpZ2h0OiAyNHB4Ow0KICAgICAgcGFkZGluZy1sZWZ0OiAzMHB4Ow0KICAgIH0NCiAgICAuZWwtaW5wdXQtZ3JvdXBfX2FwcGVuZCB7DQogICAgICBib3JkZXItcmFkaXVzOiAwcHggMTAwcHggMTAwcHggMHB4Ow0KICAgICAgYmFja2dyb3VuZDogIzIxYzliODsNCiAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLVJlZ3VsYXIsIFBpbmdGYW5nIFNDOw0KICAgICAgY29sb3I6ICNmZmY7DQogICAgICBsaW5lLWhlaWdodDogMjRweDsNCiAgICB9DQogIH0NCiAgLmRlbWFuZC1oYWxsLXNlYXJjaC1saW5lIHsNCiAgICAuZWwtZm9ybS1pdGVtX19sYWJlbCB7DQogICAgICB3aWR0aDogODhweDsNCiAgICAgIGZvbnQtZmFtaWx5OiBQaW5nRmFuZ1NDLU1lZGl1bSwgUGluZ0ZhbmcgU0M7DQogICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgY29sb3I6ICM5OTk7DQogICAgICBwYWRkaW5nLXJpZ2h0OiAzMnB4Ow0KICAgICAgdGV4dC1hbGlnbjogbGVmdDsNCiAgICB9DQogICAgLmRlbWFuZC1oYWxsLXNlYXJjaC1yYWRpbyB7DQogICAgICB3aWR0aDogMTA1MHB4Ow0KICAgICAgbWFyZ2luLXRvcDogMTFweDsNCiAgICAgIC5lbC1yYWRpby1idXR0b24gew0KICAgICAgICBwYWRkaW5nLWJvdHRvbTogMjBweDsNCiAgICAgICAgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgew0KICAgICAgICAgIGJvcmRlcjogbm9uZTsNCiAgICAgICAgICBwYWRkaW5nOiAwIDMycHggMCAwOw0KICAgICAgICAgIGJhY2tncm91bmQ6IG5vbmU7DQogICAgICAgICAgJjpob3ZlciB7DQogICAgICAgICAgICBjb2xvcjogIzIxYzliODsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgJi5pcy1hY3RpdmUgew0KICAgICAgICAgIC5lbC1yYWRpby1idXR0b25fX2lubmVyIHsNCiAgICAgICAgICAgIGNvbG9yOiAjMjFjOWI4Ow0KICAgICAgICAgICAgYmFja2dyb3VuZDogbm9uZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLmVsLXJhZGlvLWJ1dHRvbl9fb3JpZy1yYWRpbzpjaGVja2VkIHsNCiAgICAgICAgICAmICsgLmVsLXJhZGlvLWJ1dHRvbl9faW5uZXIgew0KICAgICAgICAgICAgYm94LXNoYWRvdzogdW5zZXQ7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQogIC5kZW1hbmQtaGFsbC1wYWdlLWVuZCB7DQogICAgLmRlbWFuZC1oYWxsLXBhZ2luYXRpb24gew0KICAgICAgLmJ0bi1wcmV2LA0KICAgICAgLmJ0bi1uZXh0LA0KICAgICAgLmJ0bi1xdWlja3ByZXYgew0KICAgICAgICB3aWR0aDogMzJweDsNCiAgICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5Ow0KICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgIGNvbG9yOiAjMzMzOw0KICAgICAgfQ0KICAgICAgJi5pcy1iYWNrZ3JvdW5kIHsNCiAgICAgICAgLmVsLXBhZ2VyIHsNCiAgICAgICAgICAubnVtYmVyIHsNCiAgICAgICAgICAgIHdpZHRoOiAzMnB4Ow0KICAgICAgICAgICAgaGVpZ2h0OiAzMnB4Ow0KICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmY7DQogICAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgICBsaW5lLWhlaWdodDogMzJweDsNCiAgICAgICAgICAgICYuYWN0aXZlIHsNCiAgICAgICAgICAgICAgYmFja2dyb3VuZDogIzIxYzliODsNCiAgICAgICAgICAgICAgYm9yZGVyOiAxcHggc29saWQgIzIxYzliODsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/purchaseSales/component/demandHall", "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-02-01 17:22:11\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-12-09 18:52:38\r\n-->\r\n<template>\r\n  <div class=\"demand-hall-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"purchase-banner\">\r\n      <img src=\"../../../../assets/demandHall/demandHallBanner.png\" alt=\"\" />\r\n    </div>\r\n    <div v-loading=\"loading\">\r\n      <div class=\"demand-hall-title-content\">\r\n        <div class=\"demand-hall-title-box\">\r\n          <div class=\"demand-hall-divider\"></div>\r\n          <div class=\"demand-hall-title\">商机需求</div>\r\n          <div class=\"demand-hall-divider\"></div>\r\n        </div>\r\n        <div class=\"demand-hall-search-box\">\r\n          <el-form ref=\"form\" class=\"demand-hall-search-form\" :model=\"form\">\r\n            <el-form-item>\r\n              <el-input\r\n                v-model=\"form.name\"\r\n                placeholder=\"请输入搜索内容\"\r\n                class=\"demand-hall-search-input\"\r\n              >\r\n                <el-button\r\n                  slot=\"append\"\r\n                  class=\"demand-hall-search-btn\"\r\n                  @click=\"onSearch\"\r\n                  >搜索</el-button\r\n                >\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </div>\r\n      <div class=\"demand-hall-card\">\r\n        <div class=\"demand-hall-info-content\">\r\n          <div class=\"demand-hall-search-type-box\">\r\n            <el-form ref=\"formInfo\" :model=\"formInfo\">\r\n              <div class=\"demand-hall-search-line\">\r\n                <el-form-item\r\n                  label=\"需求类型\"\r\n                  class=\"demand-hall-search-line-item\"\r\n                >\r\n                  <el-radio-group\r\n                    v-model=\"formInfo.demandType\"\r\n                    class=\"demand-hall-search-radio\"\r\n                    @input=\"changeRadio\"\r\n                  >\r\n                    <el-radio-button label=\"\">全部</el-radio-button>\r\n                    <el-radio-button\r\n                      v-for=\"(item, index) in demandList\"\r\n                      :key=\"index\"\r\n                      :label=\"item.dictValue\"\r\n                      >{{ item.dictLabel }}</el-radio-button\r\n                    >\r\n                  </el-radio-group>\r\n                </el-form-item>\r\n              </div>\r\n            </el-form>\r\n          </div>\r\n          <div\r\n            v-for=\"(item, index) in data\"\r\n            :key=\"index\"\r\n            class=\"demand-hall-list-item\"\r\n            @click=\"goDemandDetail(item.id)\"\r\n          >\r\n            <div class=\"list-item-content\">\r\n              <div class=\"list-item-img\">\r\n                <img\r\n                  v-if=\"item.scenePicture && item.scenePicture.length > 0\"\r\n                  alt=\"\"\r\n                  :src=\"item.scenePicture[0].url\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/demandDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"list-item-info\">\r\n                <div class=\"list-item-title\">\r\n                  {{ item.demandTitle }}\r\n                </div>\r\n                <div class=\"list-item-text\">\r\n                  <div class=\"list-item-label\">应用领域：</div>\r\n                  <div class=\"list-item-tag-box\">\r\n                    <div\r\n                      v-for=\"(val, num) in item.applicationArea\"\r\n                      :key=\"num\"\r\n                      class=\"lilst-item-tag\"\r\n                    >\r\n                      {{ val }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"demand-hall-page-end\">\r\n            <el-button class=\"demand-hall-page-btn\" @click=\"goHome\"\r\n              >首页</el-button\r\n            >\r\n            <el-pagination\r\n              v-if=\"data && data.length > 0\"\r\n              background\r\n              layout=\"prev, pager, next\"\r\n              class=\"demand-hall-pagination\"\r\n              :page-size=\"pageSize\"\r\n              :current-page=\"pageNum\"\r\n              :total=\"total\"\r\n              @size-change=\"handleSizeChange\"\r\n              @current-change=\"handleCurrentChange\"\r\n            >\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { gatewayDemendListTen } from \"@/api/purchaseSales\";\r\nimport { getDicts } from \"@/api/system/dict/data\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      form: {\r\n        name: \"\", //搜索内容\r\n      },\r\n      formInfo: {\r\n        demandType: \"\", //需求类型\r\n      },\r\n      demandList: [], //资讯类型列表\r\n      data: [],\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    this.getDictsList(\"demand_type\", \"demandList\");\r\n    this.search();\r\n  },\r\n  methods: {\r\n    search() {\r\n      this.loading = true;\r\n      gatewayDemendListTen({\r\n        ...this.form,\r\n        ...this.formInfo,\r\n        displayStatus: 1,\r\n        auditStatus: 2,\r\n        pageNum: this.pageNum,\r\n        // pageSize: this.pageSize,\r\n      })\r\n        .then((res) => {\r\n          console.log(res);\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          console.log(res);\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows;\r\n          this.data.forEach((item) => {\r\n            item.scenePicture = item.scenePicture\r\n              ? JSON.parse(item.scenePicture)\r\n              : [];\r\n            item.applicationArea = item.applicationArea\r\n              ? item.applicationArea.split(\",\")\r\n              : \"\";\r\n          });\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 字典\r\n    getDictsList(code, propertyName) {\r\n      getDicts(code).then((res) => {\r\n        this[propertyName] = res.data || [];\r\n      });\r\n    },\r\n    changeRadio() {\r\n      this.onSearch();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.onSearch();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.search();\r\n    },\r\n    onSearch() {\r\n      this.pageNum = 1;\r\n      this.search();\r\n    },\r\n    // 跳转到详情页面\r\n    goDemandDetail(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/demandHallDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳转到首页\r\n    goHome() {\r\n      this.$router.push({ path: \"/index\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.demand-hall-container {\r\n  width: 100%;\r\n  .purchase-banner {\r\n    width: 100%;\r\n    height: 50vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .demand-hall-title-content {\r\n    width: 100%;\r\n    padding-bottom: 18px;\r\n    .demand-hall-title-box {\r\n      width: 336px;\r\n      margin: 0 auto;\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      padding: 60px 0 40px;\r\n      .demand-hall-title {\r\n        font-size: 40px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 40px;\r\n        padding: 0 40px;\r\n      }\r\n      .demand-hall-divider {\r\n        width: 48px;\r\n        height: 4px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .demand-hall-search-box {\r\n      .demand-hall-search-form {\r\n        text-align: center;\r\n        .demand-hall-search-input {\r\n          width: 792px;\r\n          height: 54px;\r\n          .demand-hall-search-btn {\r\n            width: 100px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .demand-hall-card {\r\n    background: #f4f5f9;\r\n    padding-top: 40px;\r\n    .demand-hall-info-content {\r\n      width: 1200px;\r\n      margin: 0 auto;\r\n      .demand-hall-search-type-box {\r\n        background: #fff;\r\n        margin-bottom: 17px;\r\n        .demand-hall-search-line {\r\n          padding: 14px 24px 4px;\r\n          .demand-hall-search-line-item {\r\n            margin-bottom: 0;\r\n          }\r\n        }\r\n      }\r\n      .demand-hall-list-item {\r\n        width: 100%;\r\n        background: #fff;\r\n        border-radius: 12px;\r\n        .list-item-content {\r\n          display: flex;\r\n          padding: 24px;\r\n          cursor: pointer;\r\n          .list-item-img {\r\n            width: 180px;\r\n            height: 128px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n              border-radius: 7px;\r\n            }\r\n          }\r\n          .list-item-info {\r\n            padding-left: 24px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            .list-item-title {\r\n              width: 922px;\r\n              height: 24px;\r\n              text-overflow: ellipsis;\r\n              white-space: nowrap;\r\n              overflow: hidden;\r\n              font-size: 20px;\r\n              font-weight: 500;\r\n              color: #323233;\r\n              line-height: 20px;\r\n              margin-bottom: 26px;\r\n              word-wrap: break-word;\r\n            }\r\n            .list-item-text {\r\n              display: flex;\r\n              align-items: top;\r\n              .list-item-label {\r\n                color: #323233;\r\n                line-height: 14px;\r\n                margin-top: 14px;\r\n              }\r\n              .list-item-tag-box {\r\n                display: flex;\r\n                width: 852px;\r\n                flex-wrap: wrap;\r\n                .lilst-item-tag {\r\n                  padding: 0 12px;\r\n                  max-width: 840px;\r\n                  background: #21c9b8 1a;\r\n                  border-radius: 4px;\r\n                  font-size: 12px;\r\n                  color: #21c9b8;\r\n                  line-height: 24px;\r\n                  text-align: center;\r\n                  margin-right: 16px;\r\n                  margin-top: 10px;\r\n                  word-wrap: break-word;\r\n                  text-align: left;\r\n                }\r\n              }\r\n            }\r\n          }\r\n          &:hover {\r\n            .list-item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n        & + .demand-hall-list-item {\r\n          margin-top: 24px;\r\n        }\r\n      }\r\n      .demand-hall-page-end {\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin: 0 auto;\r\n        padding: 24px 0 60px;\r\n        .demand-hall-page-btn {\r\n          width: 82px;\r\n          height: 32px;\r\n          background: #fff;\r\n          border-radius: 4px;\r\n          border: 1px solid #d9d9d9;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.demand-hall-container {\r\n  .demand-hall-search-input {\r\n    .el-input__inner {\r\n      height: 54px;\r\n      background: #fff;\r\n      border-radius: 27px 0 0 27px;\r\n      border: 1px solid #d9d9d9;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      line-height: 24px;\r\n      padding-left: 30px;\r\n    }\r\n    .el-input-group__append {\r\n      border-radius: 0px 100px 100px 0px;\r\n      background: #21c9b8;\r\n      font-size: 16px;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n      color: #fff;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n  .demand-hall-search-line {\r\n    .el-form-item__label {\r\n      width: 88px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #999;\r\n      padding-right: 32px;\r\n      text-align: left;\r\n    }\r\n    .demand-hall-search-radio {\r\n      width: 1050px;\r\n      margin-top: 11px;\r\n      .el-radio-button {\r\n        padding-bottom: 20px;\r\n        .el-radio-button__inner {\r\n          border: none;\r\n          padding: 0 32px 0 0;\r\n          background: none;\r\n          &:hover {\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n        &.is-active {\r\n          .el-radio-button__inner {\r\n            color: #21c9b8;\r\n            background: none;\r\n          }\r\n        }\r\n        .el-radio-button__orig-radio:checked {\r\n          & + .el-radio-button__inner {\r\n            box-shadow: unset;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .demand-hall-page-end {\r\n    .demand-hall-pagination {\r\n      .btn-prev,\r\n      .btn-next,\r\n      .btn-quickprev {\r\n        width: 32px;\r\n        height: 32px;\r\n        background: #fff;\r\n        border: 1px solid #d9d9d9;\r\n        border-radius: 4px;\r\n        color: #333;\r\n      }\r\n      &.is-background {\r\n        .el-pager {\r\n          .number {\r\n            width: 32px;\r\n            height: 32px;\r\n            border: 1px solid #d9d9d9;\r\n            background: #fff;\r\n            border-radius: 4px;\r\n            line-height: 32px;\r\n            &.active {\r\n              background: #21c9b8;\r\n              border: 1px solid #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}