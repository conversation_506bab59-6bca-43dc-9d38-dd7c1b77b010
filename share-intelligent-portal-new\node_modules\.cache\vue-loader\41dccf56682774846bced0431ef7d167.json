{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Footer\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Footer\\index.vue", "mtime": 1750311962842}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Footer", "sourcesContent": ["<template>\r\n  <div class=\"footer\">\r\n    <div class=\"footer-content\">\r\n      <div class=\"footer_top\">\r\n        <div class=\"imgStyle\">\r\n          <img src=\"@/assets/images/home/<USER>\" alt=\"\" />\r\n        </div>\r\n        <div class=\"title\">易复材共享智造工业互联网平台</div>\r\n      </div>\r\n      <div class=\"footer_middle\">\r\n        <div class=\"itemStyle\" @click=\"goPage(1)\">供需对接</div>\r\n        <div class=\"itemStyle\" @click=\"goPage(2)\">复材商城</div>\r\n        <div class=\"itemStyle\" @click=\"goPage(3)\">制造共享</div>\r\n        <div class=\"itemStyle\" @click=\"goPage(4)\">服务共享</div>\r\n        <div class=\"itemStyle\" @click=\"goPage(5)\">创新共享</div>\r\n        <div class=\"itemStyle\" @click=\"goPage(6)\">解决方案</div>\r\n        <div class=\"itemStyle\" @click=\"goPage(7)\">应用商店</div>\r\n        <div class=\"itemStyle\" @click=\"goPage(8)\">关于我们</div>\r\n      </div>\r\n      <!-- <div class=\"footer_bottom\">\r\n        <div class=\"item_style\">\r\n          Copyright@ 青岛檬豆网络科技有限公司版权所有\r\n        </div>\r\n        <div class=\"item_style\">\r\n          <div class=\"email_style\">\r\n            <img :src=\"require('@/assets/images/email.png')\" alt=\"\" />\r\n          </div>\r\n          <div style=\"margin-left: 11px\"><EMAIL></div>\r\n        </div>\r\n        <div class=\"item_style\">\r\n          <div class=\"phone_style\">\r\n            <img :src=\"require('@/assets/images/phone.png')\" alt=\"\" />\r\n          </div>\r\n          <div style=\"margin-left: 11px\">4008—939—365</div>\r\n        </div>\r\n        <div class=\"item_style\">\r\n          <div class=\"document_style\">\r\n            <img :src=\"require('@/assets/images/document.png')\" alt=\"\" />\r\n          </div>\r\n          <div style=\"margin-left: 11px\">《法律声明及隐私保护》</div>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      mobile: \"\",\r\n      key: \"QmRlODJTVGhkNg==\",\r\n      type: \"cG9saWN5Y2FzaA==\",\r\n      base64EncodeChars:\r\n        \"ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",\r\n    };\r\n  },\r\n  name: \"Footer\",\r\n  methods: {\r\n    goPage(flag) {\r\n      if (flag == 1) {\r\n        this.$router.push({\r\n          path: \"/supplyDemandDocking\",\r\n        });\r\n      }\r\n      if (flag == 2) {\r\n        // this.$router.push({\r\n        //   path: \"/classicCase\",\r\n        // });\r\n        window.open(\"http://**************:1001/\");\r\n      }\r\n      if (flag == 3) {\r\n        this.$router.push({\r\n          path: \"/manufacturingSharing\",\r\n        });\r\n      }\r\n      if (flag == 4) {\r\n        this.$router.push({\r\n          path: \"/serviceSharing\",\r\n        });\r\n      }\r\n      if (flag == 5) {\r\n        this.$router.push({\r\n          path: \"/innovationSharing\",\r\n        });\r\n      }\r\n      if (flag == 6) {\r\n        this.$router.push({\r\n          path: \"/solution\",\r\n        });\r\n      }\r\n      if (flag == 7) {\r\n        this.$router.push({\r\n          path: \"/appStore\",\r\n        });\r\n      }\r\n      if (flag == 8) {\r\n        this.$router.push({\r\n          path: \"/aboutUs\",\r\n        });\r\n      }\r\n    },\r\n    // toDigital() {\r\n    //   window.open(\"https://zhenduan.ningmengdou.com/digital-diagosis-web/\");\r\n    // },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.footer {\r\n  width: 100%;\r\n  background: #232323;\r\n  padding: 80px 0;\r\n\r\n  &-content {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    .footer_top {\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      .imgStyle {\r\n        width: 47px;\r\n        img {\r\n          width: 100%;\r\n        }\r\n      }\r\n      .title {\r\n        margin-left: 20px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 500;\r\n        font-size: 20px;\r\n        color: #ffffff;\r\n        line-height: 20px;\r\n      }\r\n    }\r\n    .footer_middle {\r\n      display: flex;\r\n      justify-content: center;\r\n      margin-top: 58px;\r\n      .itemStyle {\r\n        margin-left: 40px;\r\n        font-size: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #e4dcdc;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n    .footer_bottom {\r\n      display: flex;\r\n      justify-content: center;\r\n      margin-top: 70px;\r\n      .item_style {\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 14px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        color: #e4dcdc;\r\n        margin-left: 20px;\r\n        .email_style {\r\n          width: 24px;\r\n          height: 18px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .phone_style {\r\n          width: 24px;\r\n          height: 24px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n        .document_style {\r\n          width: 18px;\r\n          height: 24px;\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n      .item_style:nth-child(1) {\r\n        margin-left: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}