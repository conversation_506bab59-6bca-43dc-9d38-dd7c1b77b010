<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysProductMapper">
    
    <resultMap type="SysProduct" id="SysProductResult">
        <result property="productId"    column="product_id"    />
        <result property="productName"    column="product_name"    />
        <result property="productImageUrl"    column="product_image_url"    />
        <result property="exhibitionHallId"    column="exhibition_hall_id"    />
        <result property="exhibitionHallName"    column="exhibition_hall_name"    />
        <result property="factoryId"    column="factory_id"    />
        <result property="productDesc"    column="product_desc"    />
        <result property="productPrice"    column="product_price"    />
        <result property="productCategory"    column="product_category"    />
        <result property="productStatus"    column="product_status"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSysProductVo">
        select p.product_id, p.product_name, p.product_image_url, p.exhibition_hall_id,
               c.material_name as exhibition_hall_name, p.factory_id, p.product_desc,
               p.product_price, p.product_category, p.product_status, p.sort_order,
               p.create_by, p.create_time, p.update_by, p.update_time, p.remark
        from sys_product p
        left join composite_material c on p.exhibition_hall_id = c.id
    </sql>

    <select id="selectSysProductList" parameterType="SysProduct" resultMap="SysProductResult">
        <include refid="selectSysProductVo"/>
        <where>
            <if test="productName != null  and productName != ''"> and p.product_name like concat('%', #{productName}, '%')</if>
            <if test="productImageUrl != null  and productImageUrl != ''"> and p.product_image_url = #{productImageUrl}</if>
            <if test="exhibitionHallId != null "> and p.exhibition_hall_id = #{exhibitionHallId}</if>
            <if test="exhibitionHallName != null and exhibitionHallName != ''"> and c.material_name like concat('%', #{exhibitionHallName}, '%')</if>
            <if test="factoryId != null and factoryId != ''"> and FIND_IN_SET(#{factoryId}, p.factory_id)</if>
            <if test="productDesc != null  and productDesc != ''"> and p.product_desc = #{productDesc}</if>
            <if test="productPrice != null "> and p.product_price = #{productPrice}</if>
            <if test="productCategory != null  and productCategory != ''"> and p.product_category = #{productCategory}</if>
            <if test="productStatus != null  and productStatus != ''"> and p.product_status = #{productStatus}</if>
            <if test="sortOrder != null "> and p.sort_order = #{sortOrder}</if>
        </where>
    </select>
    
    <select id="selectSysProductByProductId" parameterType="Long" resultMap="SysProductResult">
        <include refid="selectSysProductVo"/>
        where p.product_id = #{productId}
    </select>
        
    <insert id="insertSysProduct" parameterType="SysProduct" useGeneratedKeys="true" keyProperty="productId">
        insert into sys_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="productImageUrl != null">product_image_url,</if>
            <if test="exhibitionHallId != null">exhibition_hall_id,</if>
            <if test="factoryId != null">factory_id,</if>
            <if test="productDesc != null">product_desc,</if>
            <if test="productPrice != null">product_price,</if>
            <if test="productCategory != null">product_category,</if>
            <if test="productStatus != null">product_status,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="productImageUrl != null">#{productImageUrl},</if>
            <if test="exhibitionHallId != null">#{exhibitionHallId},</if>
            <if test="factoryId != null">#{factoryId},</if>
            <if test="productDesc != null">#{productDesc},</if>
            <if test="productPrice != null">#{productPrice},</if>
            <if test="productCategory != null">#{productCategory},</if>
            <if test="productStatus != null">#{productStatus},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSysProduct" parameterType="SysProduct">
        update sys_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="productImageUrl != null">product_image_url = #{productImageUrl},</if>
            <if test="exhibitionHallId != null">exhibition_hall_id = #{exhibitionHallId},</if>
            <if test="factoryId != null">factory_id = #{factoryId},</if>
            <if test="productDesc != null">product_desc = #{productDesc},</if>
            <if test="productPrice != null">product_price = #{productPrice},</if>
            <if test="productCategory != null">product_category = #{productCategory},</if>
            <if test="productStatus != null">product_status = #{productStatus},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where product_id = #{productId}
    </update>

    <delete id="deleteSysProductByProductId" parameterType="Long">
        delete from sys_product where product_id = #{productId}
    </delete>

    <delete id="deleteSysProductByProductIds" parameterType="String">
        delete from sys_product where product_id in 
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>
</mapper>