import request from '@/utils/request'

// 查询专家库列表
export function listExpertDatabase(query) {
  return request({
    url: '/portalconsole/ExpertDatabase/list',
    method: 'get',
    params: query
  })
}

// 查询专家库详细
export function getExpertDatabase(expertDatabaseId) {
  return request({
    url: '/portalconsole/ExpertDatabase/' + expertDatabaseId,
    method: 'get'
  })
}

// 新增专家库
export function addExpertDatabase(data) {
  return request({
    url: '/portalconsole/ExpertDatabase',
    method: 'post',
    data: data
  })
}

// 修改专家库
export function updateExpertDatabase(data) {
  return request({
    url: '/portalconsole/ExpertDatabase',
    method: 'put',
    data: data
  })
}

// 删除专家库
export function delExpertDatabase(expertDatabaseId) {
  return request({
    url: '/portalconsole/ExpertDatabase/' + expertDatabaseId,
    method: 'delete'
  })
}
