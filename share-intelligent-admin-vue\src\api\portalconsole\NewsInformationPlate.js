import request from '@/utils/request'

// 查询咨询板块列表
export function listNewsInformationPlate(query) {
  return request({
    url: '/portalconsole/NewsInformationPlate/list',
    method: 'get',
    params: query
  })
}

// 查询咨询板块详细
export function getNewsInformationPlate(newsInformationPlateId) {
  return request({
    url: '/portalconsole/NewsInformationPlate/' + newsInformationPlateId,
    method: 'get'
  })
}

// 新增咨询板块
export function addNewsInformationPlate(data) {
  return request({
    url: '/portalconsole/NewsInformationPlate',
    method: 'post',
    data: data
  })
}

// 修改咨询板块
export function updateNewsInformationPlate(data) {
  return request({
    url: '/portalconsole/NewsInformationPlate',
    method: 'put',
    data: data
  })
}

// 删除咨询板块
export function delNewsInformationPlate(newsInformationPlateId) {
  return request({
    url: '/portalconsole/NewsInformationPlate/' + newsInformationPlateId,
    method: 'delete'
  })
}
