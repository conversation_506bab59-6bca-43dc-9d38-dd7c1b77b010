{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\detail.vue?vue&type=template&id=a604382e&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\policy\\declare\\detail.vue", "mtime": 1750311962979}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1750313277641}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}