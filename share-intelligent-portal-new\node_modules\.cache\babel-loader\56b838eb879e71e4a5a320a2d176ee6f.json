{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\sharedOrders\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\sharedOrders\\index.vue", "mtime": 1750311963082}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_manufacturingSharing", "name", "components", "UserMenu", "data", "loading", "pageNum", "pageSize", "total", "tableData", "created", "getList", "methods", "_this", "params", "manufactureOrderListData", "then", "res", "code", "rows", "handleAdd", "_this2", "userInfo", "JSON", "parse", "sessionStorage", "getItem", "memberCompanyName", "$confirm", "confirmButtonText", "cancelButtonText", "type", "cancelButtonClass", "confirmButtonClass", "$router", "push", "catch", "handleSizeChange", "handleCurrentChange", "viewProductDetail", "row", "id", "$message", "error"], "sources": ["src/views/system/user/sharedOrders/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"top\">\r\n          <div class=\"content_title\">\r\n            <div class=\"icon\"></div>\r\n            <div class=\"title\">共享订单</div>\r\n          </div>\r\n          <el-button class=\"btn\" type=\"primary\" plain @click=\"handleAdd\">发布共享订单</el-button>\r\n        </div>\r\n        <div class=\"table\">\r\n          <div style=\"width: 100%\">\r\n            <el-table :data=\"tableData\" style=\"width: 100%\" :v-loading=\"loading\" max-height=\"600\">\r\n              <el-table-column label=\"需求企业\" align=\"center\" prop=\"demandCompany\" width=\"150\" />\r\n              <el-table-column label=\"需求截止时间\" align=\"center\" prop=\"deadline\" width=\"100\" />\r\n              <el-table-column label=\"托单价格\" align=\"center\" prop=\"price\" />\r\n              <el-table-column label=\"联系电话\" align=\"center\" prop=\"contactPhone\" />\r\n              <el-table-column label=\"交货地址\" align=\"center\" prop=\"deliveryAddress\" width=\"150\" />\r\n              <el-table-column label=\"文件要求\" align=\"center\" prop=\"fileRequirement\" width=\"500\">\r\n                <template slot-scope=\"scope\">\r\n                  <div class=\"desc\">{{ scope.row.fileRequirement ? scope.row.fileRequirement : \"无\"}}</div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" size=\"small\" @click=\"viewProductDetail(scope.row)\">查看详情</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n          </div>\r\n          <!-- 分页 -->\r\n          <div class=\"pageStyle\">\r\n            <el-pagination v-if=\"tableData && tableData.length > 0\" background layout=\"prev, pager, next\"\r\n              class=\"activity-pagination\" :page-size=\"pageSize\" :current-page=\"pageNum\" :total=\"total\"\r\n              @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\">\r\n            </el-pagination>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { manufactureOrderListData } from \"@/api/manufacturingSharing\";\r\n\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageNum: 1,\r\n      pageSize: 10,\r\n      total: 0,\r\n      tableData: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n      };\r\n      manufactureOrderListData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.tableData = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    handleAdd() {\r\n      let userInfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n      if (!userInfo?.memberCompanyName) {\r\n        this.$confirm(\"您当前尚未关联企业，是否前往操作?\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n          cancelButtonClass: \"cancelButtonClass\",\r\n          confirmButtonClass: \"customClass\",\r\n        })\r\n          .then(() => {\r\n            this.$router.push(\"/user/userCenter\");\r\n          })\r\n          .catch(() => { });\r\n        return;\r\n      } else {\r\n        this.$router.push(\"/release?index=2\");\r\n      }\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    viewProductDetail(row) {\r\n      if (row) {\r\n        this.$router.push(\"/productOrderDetail?id=\" + row.id);\r\n      } else {\r\n        this.$message.error(\"暂无该订单详情\");\r\n      }\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n}\r\n\r\n.top {\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  // margin-top: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .content_title {\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 4px;\r\n      height: 20px;\r\n      background: #21c9b8;\r\n    }\r\n\r\n    .title {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #030a1a;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.table {\r\n  margin-top: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  padding: 20px;\r\n  box-sizing: border-box;\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  justify-content: space-around;\r\n\r\n  .desc{\r\n    display: -webkit-box;\r\n    -webkit-box-orient: vertical;\r\n    -webkit-line-clamp: 7;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: normal;\r\n    word-break: break-all;\r\n  }\r\n}\r\n\r\n.pageStyle {\r\n  width: 100%;\r\n  margin-top: 61px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AA+CA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAE,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MACAC,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAR,OAAA;MACA,IAAAS,MAAA;QACAR,OAAA,OAAAA,OAAA;QACAC,QAAA,OAAAA;MACA;MACA,IAAAQ,8CAAA,EAAAD,MAAA,EAAAE,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAL,KAAA,CAAAJ,SAAA,GAAAQ,GAAA,CAAAE,IAAA;UACAN,KAAA,CAAAL,KAAA,GAAAS,GAAA,CAAAT,KAAA;UACAK,KAAA,CAAAR,OAAA;QACA;MACA;IACA;IACAe,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,cAAA,CAAAC,OAAA;MACA,MAAAJ,QAAA,aAAAA,QAAA,eAAAA,QAAA,CAAAK,iBAAA;QACA,KAAAC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;UACAC,iBAAA;UACAC,kBAAA;QACA,GACAjB,IAAA;UACAK,MAAA,CAAAa,OAAA,CAAAC,IAAA;QACA,GACAC,KAAA;QACA;MACA;QACA,KAAAF,OAAA,CAAAC,IAAA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAA9B,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAI,OAAA;IACA;IACA2B,mBAAA,WAAAA,oBAAAhC,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAK,OAAA;IACA;IACA4B,iBAAA,WAAAA,kBAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAAN,OAAA,CAAAC,IAAA,6BAAAK,GAAA,CAAAC,EAAA;MACA;QACA,KAAAC,QAAA,CAAAC,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}