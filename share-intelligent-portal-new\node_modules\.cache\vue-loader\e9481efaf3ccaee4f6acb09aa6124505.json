{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\thinkTank.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\components\\home\\thinkTank.vue", "mtime": 1750311962937}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["thinkTank.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "thinkTank.vue", "sourceRoot": "src/views/components/home", "sourcesContent": ["<template>\r\n  <div\r\n    class=\"cardBg wow animate__animated animate__fadeInUp\"\r\n    data-wow-duration=\"1s\"\r\n  >\r\n    <div class=\"card-container\">\r\n      <div class=\"enterpriseTitle\">\r\n        <div>专家智库</div>\r\n        <div class=\"allEnterprise\" @click=\"gothinkTank\">查看全部>></div>\r\n      </div>\r\n      <div class=\"expert-library-list\">\r\n        <div\r\n          v-for=\"(item, index) in data\"\r\n          :key=\"index\"\r\n          class=\"list-item-content\"\r\n          @click=\"goExpertLibrary(item.id)\"\r\n        >\r\n          <div class=\"list-item-box\">\r\n            <div class=\"item-headline\">\r\n              <div class=\"item-title\">\r\n                {{ item.expertName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"expert-library-label\">\r\n              <div\r\n                v-for=\"(val, index1) in item.techniqueTypeName\"\r\n                :key=\"index1\"\r\n                class=\"library-label-item\"\r\n              >\r\n                <span v-if=\"index1 < 2\" class=\"expert-library-type\">{{\r\n                  `#${val}`\r\n                }}</span>\r\n                <span v-else>…</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"expert-library-box\">\r\n              {{ item.synopsis }}\r\n            </div>\r\n          </div>\r\n          <div class=\"list-item-img\">\r\n            <img v-if=\"item.headPortrait\" :src=\"item.headPortrait\" alt=\"\" />\r\n            <img\r\n              v-else\r\n              src=\"../../../assets/expertLibrary/defaultImg.png\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getExpertList } from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: [],\r\n      pageNum: 1,\r\n      total: 0,\r\n    };\r\n  },\r\n  created() {\r\n    // this.searchExpert();\r\n  },\r\n  methods: {\r\n    gothinkTank() {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/thinkTank\",\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    searchExpert() {\r\n      this.loading = true;\r\n      getExpertList({\r\n        pageSize: 4,\r\n      })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n\r\n          this.loading = false;\r\n          let { rows, total } = res || [];\r\n          this.data = rows.slice(0, 4);\r\n          this.data.forEach((item) => {\r\n            item.techniqueTypeName = item.techniqueTypeName\r\n              ? item.techniqueTypeName.split(\",\")\r\n              : [];\r\n          });\r\n          this.total = total;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 跳转到专家详情页面\r\n    goExpertLibrary(id) {\r\n      let routeData = this.$router.resolve({\r\n        path: \"/expertDetail\",\r\n        query: { id },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cardBg {\r\n  width: 100%;\r\n  height: 740px;\r\n  background-image: url(\"../../../assets/images/home/<USER>\");\r\n  background-size: 100% 100%;\r\n}\r\n.enterpriseTitle {\r\n  width: 100%;\r\n  font-size: 36px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  color: #000000;\r\n  text-align: center;\r\n  margin: 60px 0 20px 0;\r\n  padding-top: 70px;\r\n  position: relative;\r\n  .allEnterprise {\r\n    position: absolute;\r\n    top: 8 px;\r\n    right: 0;\r\n    font-size: 16px;\r\n    font-family: Source Han Sans CN;\r\n    font-weight: 500;\r\n    color: #21c9b8;\r\n    line-height: 26px;\r\n    cursor: pointer;\r\n  }\r\n}\r\n.content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  width: 100%;\r\n  height: 280px;\r\n  .contentItem {\r\n    width: 23%;\r\n    height: 100%;\r\n    text-align: center;\r\n    cursor: pointer;\r\n    background: #ffffff;\r\n    box-shadow: 0px 2px 20px 0px rgba(32, 84, 252, 0.14);\r\n    border-radius: 4px;\r\n    img {\r\n      width: 100%;\r\n      height: 230px;\r\n    }\r\n  }\r\n}\r\n.expert-library-list {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  width: 100%;\r\n  .list-item-content {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    width: 578px;\r\n    background: #fff;\r\n    margin-top: 31px;\r\n    padding: 28px 32px;\r\n    min-height: 240px;\r\n    .list-item-box {\r\n      flex: 1;\r\n      .item-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        .item-title {\r\n          width: 280px;\r\n          font-size: 32px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 32px;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          word-wrap: break-word;\r\n        }\r\n      }\r\n      .expert-library-label {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        margin: 0 0 16px;\r\n        .library-label-item {\r\n          max-width: 350px;\r\n          padding: 6px 12px;\r\n          background: #f4f5f9;\r\n          border-radius: 4px;\r\n          font-size: 12px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #666;\r\n          line-height: 12px;\r\n          margin: 24px 16px 0 0;\r\n          .expert-library-type {\r\n            word-wrap: break-word;\r\n          }\r\n        }\r\n      }\r\n      .expert-library-box {\r\n        width: 370px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #666;\r\n        line-height: 32px;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 2;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n    }\r\n    .list-item-img {\r\n      width: 120px;\r\n      height: 168px;\r\n      margin-left: 24px;\r\n      img {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n    &:hover {\r\n      cursor: pointer;\r\n    }\r\n  }\r\n  .list-item-content:hover {\r\n    box-shadow: 0px 2px 20px 0px rgba(13, 230, 96, 0.3);\r\n  }\r\n}\r\n</style>\r\n"]}]}