<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="2.5" :xs="24">
        <user-menu activeIndex="1" />
      </el-col>
      <el-col :span="20" :xs="24" style="width: calc(100% - 180px)">
        <div class="top">
          <div class="content_title">
            <div class="icon"></div>
            <div class="title">车间管理</div>
          </div>
          <el-button class="btn" type="primary" plain @click="handleAdd">发布车间信息</el-button>
        </div>
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px"
          style="margin-top: 20px">
          <el-form-item label="车间名称" prop="name">
            <el-input v-model="queryParams.name" placeholder="请输入车间名称" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="所属单位" prop="company">
            <el-input v-model="queryParams.company" placeholder="请输入所属单位" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="车间面积" prop="area">
            <el-input v-model="queryParams.area" placeholder="请输入车间面积" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="参考价格" prop="price">
            <el-input v-model="queryParams.price" placeholder="请输入参考价格" clearable @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="table">
          <div style="width: 100%">
            <el-table :data="tableData" style="width: 100%" v-loading="loading">
              <el-table-column label="车间ID" align="center" prop="id" />
              <el-table-column label="车间名称" align="center" prop="name" />
              <el-table-column label="车间类型" align="center">
                <template slot-scope="scope">
                  {{ getWorkShopType(scope.row.type) }}
                </template>
              </el-table-column>
              <el-table-column label="所属单位" align="center" prop="company" />
              <el-table-column label="车间地址" align="center" prop="address" />
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
                  <el-button size="mini" type="text" icon="el-icon-delete"
                    @click="handleDelete(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <!-- 分页 -->
          <div class="pageStyle">
            <el-pagination v-if="tableData && tableData.length > 0" background layout="prev, pager, next"
              class="activity-pagination" :page-size="queryParams.pageSize" :current-page="queryParams.pageNum"
              :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange">
            </el-pagination>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import UserMenu from "../components/userMenu.vue";
import { listData } from "@/api/system/dict/data";
import { workUserListData, delWorkInfo } from "@/api/manufacturingSharing";

export default {
  name: "User",
  components: { UserMenu },
  data() {
    return {
      loading: false,
      total: 0,
      tableData: [],
      workShopTypeList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        company: null,
        area: null,
        price: null,
        checkStatus: null,
        createBy: null,
      },
    };
  },
  created() {
    this.getDicts();
    this.getList();
  },
  methods: {
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 查询字典数据列表 */
    getDicts() {
      let params = { dictType: "workshop_type" };
      listData(params).then((res) => {
        if (res.code === 200) {
          this.workShopTypeList = res.rows;
        }
      });
    },
    getList() {
      this.loading = true;
      let userinfo = JSON.parse(window.sessionStorage.getItem("userinfo"));
      this.queryParams.createBy = userinfo.memberPhone;
      workUserListData(this.queryParams).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.total = res.total;
          this.loading = false;
        }
      });
    },
    handleAdd() {
      let userInfo = JSON.parse(sessionStorage.getItem("userinfo"));
      if (!userInfo?.memberCompanyName) {
        this.$confirm("您当前尚未关联企业，是否前往操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
          cancelButtonClass: "cancelButtonClass",
          confirmButtonClass: "customClass",
        })
          .then(() => {
            this.$router.push("/user/userCenter");
          })
          .catch(() => { });
        return;
      } else {
        this.$router.push("/publishWorkshop");
      }
    },
    handleSizeChange(pageSize) {
      this.queryParams.pageSize = pageSize;
      this.getList();
    },
    handleCurrentChange(pageNum) {
      this.queryParams.pageNum = pageNum;
      this.getList();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.$router.push("/publishWorkshop?id=" + row.id);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除车间信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delWorkInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    getWorkShopType(type) {
      const wrokShopType = this.workShopTypeList.find(
        (item) => item.dictValue == type
      );
      return wrokShopType ? wrokShopType.dictLabel : "";
    },
  },
};
</script>
<style lang="scss" scoped>
.app-container {
  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));
  height: 100vh;
}

.top {
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  // margin-top: 20px;
  display: flex;
  justify-content: space-between;

  .content_title {
    display: flex;
    align-items: center;

    .icon {
      width: 4px;
      height: 20px;
      background: #21c9b8;
    }

    .title {
      font-family: Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #030a1a;
      margin-left: 10px;
    }
  }
}

.table {
  background: #fff;
  border-radius: 10px;
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-around;
}

.pageStyle {
  width: 100%;
  margin-top: 61px;
  display: flex;
  justify-content: center;
}
</style>
