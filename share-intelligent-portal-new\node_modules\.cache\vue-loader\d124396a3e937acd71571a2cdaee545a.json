{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\notice\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\notice\\index.vue", "mtime": 1750311963067}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsgbGlzdEluZm8sIGRlbGV0ZUluZm8sIGdldEluZm9EZXRhaWwgfSBmcm9tICJAL2FwaS9zeXN0ZW0vaW5mbyI7DQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJOb3RpY2UiLA0KICBjb21wb25lbnRzOiB7IFVzZXJNZW51IH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGFjdGl2ZU5hbWU6ICJmaXJzdCIsDQogICAgICByZWNvcmRzOiBbXSwNCiAgICAgIHN5c3RlbVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogNSwNCiAgICAgIH0sDQogICAgICB0b3RhbDogMCwNCiAgICAgIGZpdDogImNvdmVyIiwNCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgIH07DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRTeXN0ZW1MaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnb0RldGFpbChpZCkgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goIi91c2VyL25vdGljZURldGFpbD9pZD0iICsgaWQpOw0KICAgIH0sDQogICAgaGFuZGxlQ2xpY2sodGFiLCBldmVudCkgew0KICAgICAgaWYgKHRhYi4kb3B0aW9ucy5wcm9wc0RhdGEubmFtZSA9PSAiZmlyc3QiKSB7DQogICAgICAgIHRoaXMuZ2V0UmVzcG9uc2VMaXN0KCk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmdldFN5c3RlbUxpc3QoKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgZGVsZXRlSW5mbyhyb3csIHR5cGUpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oIuaYr+WQpuehruiupOWIoOmZpOivpea2iOaBr++8nyIsIHsgdHlwZTogImVycm9yIiB9KQ0KICAgICAgICAudGhlbigoXykgPT4gew0KICAgICAgICAgIGRlbGV0ZUluZm8oeyBpZHM6IHJvdy5pZC50b1N0cmluZygpIH0pLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICAgICAgbWVzc2FnZTogIuaTjeS9nOaIkOWKnyIsDQogICAgICAgICAgICAgIHR5cGU6ICJzdWNjZXNzIiwNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0pOw0KICAgICAgICAgIGlmICh0eXBlID09IDEpIHsNCiAgICAgICAgICAgIHRoaXMuZ2V0UmVzcG9uc2VMaXN0KCk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHRoaXMuZ2V0U3lzdGVtTGlzdCgpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKChfKSA9PiB7fSk7DQogICAgfSwNCiAgICBnZXRTeXN0ZW1MaXN0KCkgew0KICAgICAgbGlzdEluZm8oeyAuLi50aGlzLnN5c3RlbVBhcmFtcywgdHlwZTogMiB9KS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLnJlY29yZHMgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldEluZm9EZXRhaWwoaWQpIHsNCiAgICAgIGdldEluZm9EZXRhaWwoaWQpLnRoZW4oKHJlc3BvbnNlKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0U3lzdGVtTGlzdCgpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICByZXZvY2F0aW9uQXBwbHkoaWQpIHsNCiAgICAgIHJldm9jYXRpb25BcHBseSh7IGlkOiBpZCB9KS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlKHsNCiAgICAgICAgICBtZXNzYWdlOiAi5pON5L2c5oiQ5YqfIiwNCiAgICAgICAgICB0eXBlOiAic3VjY2VzcyIsDQogICAgICAgIH0pOw0KICAgICAgICB0aGlzLmdldFN5c3RlbUxpc3QoKTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgc3lzdGVtUGFnZUNoYW5nZShyZXMpIHsNCiAgICAgIHRoaXMuc3lzdGVtUGFyYW1zLnBhZ2VOdW0gPSByZXM7DQogICAgICB0aGlzLmdldFN5c3RlbUxpc3QoKTsNCiAgICB9LA0KICB9LA0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/notice", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-21 16:37:16\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <div class=\"notice-record-page\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"response-message\">\r\n            <div\r\n              class=\"response-message-item\"\r\n              v-for=\"item in records\"\r\n              :key=\"item.id\"\r\n            >\r\n              <el-image\r\n                class=\"iamge\"\r\n                style=\"width: 36px; height: 36px\"\r\n                :src=\"\r\n                  require(item.readStatus === 0\r\n                    ? '@/assets/user/notice_2.png'\r\n                    : '@/assets/user/notice_1.png')\r\n                \"\r\n              ></el-image>\r\n              <a class=\"item-content\" @click=\"getInfoDetail(item.id)\">\r\n                <div class=\"title\">{{ item.title || \"系统消息\" }}</div>\r\n                <div class=\"item-content-bottom\">\r\n                  <div class=\"content\">\r\n                    {{ item.remark || item.describeInfo || \"--\" }}\r\n                  </div>\r\n\r\n                  <div class=\"date\">{{ item.createTime || \"--\" }}</div>\r\n                </div>\r\n              </a>\r\n              <a @click=\"revocationApply(item.id)\" v-if=\"item.backStatus == 1\">\r\n                <el-image\r\n                  class=\"re-icon\"\r\n                  style=\"width: 18px; height: 20px\"\r\n                  :src=\"require('@/assets/user/revocation.png')\"\r\n                ></el-image>\r\n              </a>\r\n\r\n              <a @click=\"deleteInfo(item, 2)\">\r\n                <el-image\r\n                  class=\"delete-icon\"\r\n                  style=\"width: 18px; height: 20px\"\r\n                  :src=\"require('@/assets/user/delete.png')\"\r\n                ></el-image>\r\n              </a>\r\n            </div>\r\n          </div>\r\n          <el-pagination\r\n            v-show=\"total > 0\"\r\n            background\r\n            layout=\"prev, pager, next\"\r\n            :page-size=\"5\"\r\n            :current-page.sync=\"systemParams.pageNum\"\r\n            @current-change=\"systemPageChange\"\r\n            :total=\"total\"\r\n          >\r\n          </el-pagination>\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listInfo, deleteInfo, getInfoDetail } from \"@/api/system/info\";\r\nexport default {\r\n  name: \"Notice\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      activeName: \"first\",\r\n      records: [],\r\n      systemParams: {\r\n        pageNum: 1,\r\n        pageSize: 5,\r\n      },\r\n      total: 0,\r\n      fit: \"cover\",\r\n      dialogVisible: false,\r\n    };\r\n  },\r\n  created() {\r\n    this.getSystemList();\r\n  },\r\n  methods: {\r\n    goDetail(id) {\r\n      this.$router.push(\"/user/noticeDetail?id=\" + id);\r\n    },\r\n    handleClick(tab, event) {\r\n      if (tab.$options.propsData.name == \"first\") {\r\n        this.getResponseList();\r\n      } else {\r\n        this.getSystemList();\r\n      }\r\n    },\r\n\r\n    deleteInfo(row, type) {\r\n      this.$confirm(\"是否确认删除该消息？\", { type: \"error\" })\r\n        .then((_) => {\r\n          deleteInfo({ ids: row.id.toString() }).then((response) => {\r\n            this.$message({\r\n              message: \"操作成功\",\r\n              type: \"success\",\r\n            });\r\n          });\r\n          if (type == 1) {\r\n            this.getResponseList();\r\n          } else {\r\n            this.getSystemList();\r\n          }\r\n        })\r\n        .catch((_) => {});\r\n    },\r\n    getSystemList() {\r\n      listInfo({ ...this.systemParams, type: 2 }).then((response) => {\r\n        this.records = response.rows;\r\n        this.total = response.total;\r\n      });\r\n    },\r\n    getInfoDetail(id) {\r\n      getInfoDetail(id).then((response) => {\r\n        this.getSystemList();\r\n      });\r\n    },\r\n    revocationApply(id) {\r\n      revocationApply({ id: id }).then((response) => {\r\n        this.$message({\r\n          message: \"操作成功\",\r\n          type: \"success\",\r\n        });\r\n        this.getSystemList();\r\n      });\r\n    },\r\n    systemPageChange(res) {\r\n      this.systemParams.pageNum = res;\r\n      this.getSystemList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .notice-record-page {\r\n    .response-message {\r\n      width: 100%;\r\n      height: 600px;\r\n      background-color: #fff;\r\n      .none-class {\r\n        text-align: center;\r\n        padding: 10% 0;\r\n        .text {\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n          color: #999999;\r\n          line-height: 14px;\r\n        }\r\n      }\r\n      .response-message-item {\r\n        width: 100%;\r\n        height: 100px;\r\n        vertical-align: middle;\r\n        padding: 22px 22px;\r\n        display: flex;\r\n        border-bottom: 1px solid #e8e8e8;\r\n        position: relative;\r\n\r\n        .iamge {\r\n          margin: auto 0;\r\n        }\r\n        .item-content {\r\n          vertical-align: middle;\r\n          margin-left: 14px;\r\n          .title {\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            color: #333333;\r\n            line-height: 20px;\r\n          }\r\n          .item-content-bottom {\r\n            width: 100%;\r\n            display: flex;\r\n            line-height: 45px;\r\n            font-size: 14px;\r\n            font-weight: 400;\r\n\r\n            .content {\r\n              width: 680px;\r\n              height: 30px;\r\n              color: #666666;\r\n              overflow: hidden;\r\n              -webkit-line-clamp: 1;\r\n              text-overflow: ellipsis;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n            }\r\n            .to-detail {\r\n              width: 150px;\r\n              text-align: center;\r\n              color: #21c9b8;\r\n            }\r\n            .date {\r\n              width: 180px;\r\n              text-align: center;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              color: #666666;\r\n            }\r\n          }\r\n        }\r\n        .delete-icon {\r\n          right: 30px;\r\n          top: 40px;\r\n          margin: 0 auto;\r\n          position: absolute;\r\n        }\r\n        .re-icon {\r\n          right: 80px;\r\n          top: 40px;\r\n          margin: 0 auto;\r\n          position: absolute;\r\n        }\r\n      }\r\n    }\r\n\r\n    .el-pagination {\r\n      width: 100%;\r\n      margin-top: 20px;\r\n      text-align: center;\r\n    }\r\n    .el-pagination.is-background .el-pager li {\r\n      background-color: #fff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled).active {\r\n      background-color: #21c9b8;\r\n      color: #ffffff;\r\n    }\r\n    .el-pagination.is-background .el-pager li:not(.disabled):hover {\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}