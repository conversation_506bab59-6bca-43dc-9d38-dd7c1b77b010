/** PURE_IMPORTS_START tslib,_Subscription PURE_IMPORTS_END */
import * as tslib_1 from "tslib";
import { Subscription } from '../Subscription';
var Action = /*@__PURE__*/ (function (_super) {
    tslib_1.__extends(Action, _super);
    function Action(scheduler, work) {
        return _super.call(this) || this;
    }
    Action.prototype.schedule = function (state, delay) {
        if (delay === void 0) {
            delay = 0;
        }
        return this;
    };
    return Action;
}(Subscription));
export { Action };
//# sourceMappingURL=Action.js.map
