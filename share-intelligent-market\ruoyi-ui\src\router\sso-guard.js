/**
 * SSO路由守卫
 * 
 * <AUTHOR>
 */

import { getToken } from '@/utils/auth'
import { autoSSOLoginCheck, redirectToMainSystemLogin } from '@/utils/sso'

/**
 * SSO路由守卫
 * 在路由跳转前检查SSO登录状态
 * 
 * @param {Object} to 目标路由
 * @param {Object} from 来源路由
 * @param {Function} next 路由跳转函数
 */
export function ssoRouterGuard(to, from, next) {
  // 白名单路径，不需要登录验证
  const whiteList = ['/login', '/sso/login', '/404', '/401']
  
  // 如果是白名单路径，直接通过
  if (whiteList.includes(to.path)) {
    next()
    return
  }
  
  // 检查本地token
  const token = getToken()
  
  if (token) {
    // 已有token，直接通过
    next()
  } else {
    // 无token，检查是否需要SSO登录
    autoSSOLoginCheck()
      .then(isSSO => {
        if (!isSSO) {
          // 不是SSO登录，跳转到主系统登录
          const redirectUrl = window.location.href
          redirectToMainSystemLogin(redirectUrl)
        }
        // 如果是SSO登录，autoSSOLoginCheck会自动处理跳转
      })
      .catch(error => {
        console.error('SSO检查失败:', error)
        // 降级处理：跳转到主系统登录
        const redirectUrl = window.location.href
        redirectToMainSystemLogin(redirectUrl)
      })
  }
}

/**
 * 安装SSO路由守卫
 * 
 * @param {Object} router Vue Router实例
 */
export function installSSOGuard(router) {
  router.beforeEach((to, from, next) => {
    ssoRouterGuard(to, from, next)
  })
}
