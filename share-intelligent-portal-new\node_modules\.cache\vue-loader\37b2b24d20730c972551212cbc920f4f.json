{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\serviceSharing\\components\\talentPool\\index copy.vue?vue&type=style&index=0&id=61e2820f&lang=scss&scoped=true", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\serviceSharing\\components\\talentPool\\index copy.vue", "mtime": 1750311963013}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index copy.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmSA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index copy.vue", "sourceRoot": "src/views/serviceSharing/components/talentPool", "sourcesContent": ["<template>\r\n  <div class=\"content\">\r\n    <div class=\"content_banner\">\r\n      人才服务\r\n      <div class=\"imgContent\">\r\n        <div class=\"imgStyle\">\r\n          <img\r\n            style=\"width: 100%; height: 100%\"\r\n            src=\"../../../../assets/order/orderStep.png\"\r\n            alt=\"\"\r\n          />\r\n          <div class=\"joinNow\" @click=\"joinNow\">立即入驻</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container card_top\">\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">岗位分类：</div>\r\n        <div\r\n          class=\"smallCategory\"\r\n          :class=\"postCategory === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in positionTypeList\"\r\n          :key=\"index\"\r\n          @click=\"switchPostCategory(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">最高学历：</div>\r\n        <div\r\n          class=\"smallCategory\"\r\n          :class=\"educational === item.dictValue ? 'smallCategoryActive' : ''\"\r\n          v-for=\"(item, index) in educationList\"\r\n          :key=\"index\"\r\n          @click=\"switchEducational(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">职 称：</div>\r\n        <div\r\n          class=\"smallCategory\"\r\n          :class=\"\r\n            technicalTitle === item.dictValue ? 'smallCategoryActive' : ''\r\n          \"\r\n          v-for=\"(item, index) in jobTitleList\"\r\n          :key=\"index\"\r\n          @click=\"switchTechnicalTitle(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"card_top_itemLine\"></div>\r\n      <div class=\"card_top_item\">\r\n        <div class=\"largeCategory\">工作状态：</div>\r\n        <div\r\n          class=\"smallCategory\"\r\n          :class=\"\r\n            workingCondition === item.dictValue ? 'smallCategoryActive' : ''\r\n          \"\r\n          v-for=\"(item, index) in workStatusList\"\r\n          :key=\"index\"\r\n          @click=\"switchWorkingCondition(item.dictValue)\"\r\n        >\r\n          {{ item.dictLabel }}\r\n        </div>\r\n      </div>\r\n      <div class=\"buttonStyle\">\r\n        <div class=\"imgStyle\" @click=\"initList\">\r\n          <img\r\n            style=\"width: 100%; height: 100%\"\r\n            src=\"../../../../assets/serviceSharing/reset.png\"\r\n            alt=\"\"\r\n          />\r\n        </div>\r\n        <div class=\"buttonText\" @click=\"resetData\">重置筛选</div>\r\n      </div>\r\n    </div>\r\n    <div class=\"card-container card_bottom\">\r\n      <div\r\n        class=\"content_bottom\"\r\n        v-loading=\"loading\"\r\n        v-if=\"talentList && talentList.length > 0\"\r\n      >\r\n        <div\r\n          class=\"card_bottom_item tr2\"\r\n          v-for=\"(item, index) in talentList\"\r\n          :key=\"index\"\r\n          @click=\"goDetail(item.id)\"\r\n        >\r\n          <span class=\"bottom\"></span>\r\n          <span class=\"right\"></span>\r\n          <span class=\"top\"></span>\r\n          <span class=\"left\"></span>\r\n          <!-- 左侧图片 -->\r\n          <div class=\"imgStyle\">\r\n            <img\r\n              style=\"width: 100%; height: 100%\"\r\n              :src=\"\r\n                item.photo\r\n                  ? item.photo\r\n                  : require('../../../../assets/serviceSharing/ceshi.png')\r\n              \"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <!-- 右侧内容 -->\r\n          <div class=\"content_bottom_right\">\r\n            <div class=\"title ellips1\">{{ item.name }}</div>\r\n            <div class=\"category\" v-if=\"item.education\">\r\n              {{\r\n                educationList.filter(\r\n                  (item1) => item1.dictValue == item.education\r\n                )[0].dictLabel\r\n              }}\r\n            </div>\r\n            <div class=\"statusStyle\">\r\n              {{ item.workStatus == \"0\" ? \"在职\" : \"离职\" }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"none-class\" v-else>\r\n        <el-image\r\n          style=\"width: 160px; height: 160px\"\r\n          :src=\"require('@/assets/user/none.png')\"\r\n          :fit=\"fit\"\r\n        ></el-image>\r\n        <div class=\"text\">暂无数据</div>\r\n      </div>\r\n      <!-- 分页 -->\r\n      <div class=\"pageStyle\">\r\n        <el-pagination\r\n          v-if=\"talentList && talentList.length > 0\"\r\n          background\r\n          layout=\"prev, pager, next\"\r\n          class=\"activity-pagination\"\r\n          :page-size=\"pageSize\"\r\n          :current-page=\"pageNum\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        >\r\n        </el-pagination>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { talentListData } from \"@/api/serviceSharing\";\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      pageNum: 1,\r\n      pageSize: 16,\r\n      total: 0,\r\n      postCategory: \"\",\r\n      educational: \"\",\r\n      technicalTitle: \"\",\r\n      workingCondition: \"\",\r\n      talentList: [],\r\n      positionTypeList: [], // 岗位分类\r\n      educationList: [], // 最高学历\r\n      jobTitleList: [], // 职称\r\n      workStatusList: [], // 工作状态\r\n      fit: \"cover\",\r\n    };\r\n  },\r\n  created() {\r\n    this.getPositionType();\r\n    this.getEducation();\r\n    this.getJobTitle();\r\n    this.getWorkStatus();\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    // 岗位分类\r\n    getPositionType() {\r\n      let params = { dictType: \"position_type\" };\r\n      listData(params).then((response) => {\r\n        this.positionTypeList = response.rows;\r\n        this.positionTypeList.unshift({\r\n          dictValue: \"\",\r\n          dictLabel: \"全部\",\r\n        });\r\n      });\r\n    },\r\n    // 最高学历\r\n    getEducation() {\r\n      let params = { dictType: \"education\" };\r\n      listData(params).then((response) => {\r\n        this.educationList = response.rows;\r\n        this.educationList.unshift({\r\n          dictValue: \"\",\r\n          dictLabel: \"全部\",\r\n        });\r\n      });\r\n    },\r\n    // 职称\r\n    getJobTitle() {\r\n      let params = { dictType: \"job_title\" };\r\n      listData(params).then((response) => {\r\n        this.jobTitleList = response.rows;\r\n        this.jobTitleList.unshift({\r\n          dictValue: \"\",\r\n          dictLabel: \"全部\",\r\n        });\r\n      });\r\n    },\r\n    // 工作状态\r\n    getWorkStatus() {\r\n      let params = { dictType: \"work_status\" };\r\n      listData(params).then((response) => {\r\n        this.workStatusList = response.rows;\r\n        this.workStatusList.unshift({\r\n          dictValue: \"\",\r\n          dictLabel: \"全部\",\r\n        });\r\n      });\r\n    },\r\n    getList() {\r\n      this.loading = true;\r\n      let params = {\r\n        pageNum: this.pageNum,\r\n        pageSize: this.pageSize,\r\n        positionType: this.postCategory,\r\n        education: this.educational,\r\n        jobTitle: this.technicalTitle,\r\n        workStatus: this.workingCondition,\r\n        settledStatus: \"1\",\r\n      };\r\n      talentListData(params).then((res) => {\r\n        if (res.code === 200) {\r\n          this.talentList = res.rows;\r\n          this.total = res.total;\r\n          this.loading = false;\r\n        }\r\n      });\r\n    },\r\n    joinNow() {\r\n      this.$router.push({\r\n        path: \"/talentJoinNow\",\r\n      });\r\n    },\r\n    switchPostCategory(index) {\r\n      this.postCategory = index;\r\n      this.getList();\r\n    },\r\n    switchEducational(index) {\r\n      this.educational = index;\r\n      this.getList();\r\n    },\r\n    switchTechnicalTitle(index) {\r\n      this.technicalTitle = index;\r\n      this.getList();\r\n    },\r\n    switchWorkingCondition(index) {\r\n      this.workingCondition = index;\r\n      this.getList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    goDetail(id) {\r\n      this.$router.push(\"/talentDetail?id=\" + id);\r\n    },\r\n    initList() {\r\n      this.getList();\r\n    },\r\n    resetData() {\r\n      this.postCategory = \"\";\r\n      this.educational = \"\";\r\n      this.technicalTitle = \"\";\r\n      this.workingCondition = \"\";\r\n      this.getList();\r\n    },\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.content {\r\n  width: 100%;\r\n  padding-bottom: 60px;\r\n  background-color: #f2f2f2;\r\n}\r\n.content_banner {\r\n  width: 100%;\r\n  height: 300px;\r\n  background-image: url(\"../../../../assets/release/banner.png\");\r\n  background-size: 100% 100%;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  padding-top: 28px;\r\n  font-family: Source Han Sans CN;\r\n  font-weight: 500;\r\n  font-size: 40px;\r\n  color: #000;\r\n  .imgContent {\r\n    width: 100%;\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-top: 10px;\r\n    .imgStyle {\r\n      width: 1256px;\r\n      height: 206px;\r\n      position: relative;\r\n      .joinNow {\r\n        position: absolute;\r\n        right: 90px;\r\n        top: 75px;\r\n        width: 110px;\r\n        height: 50px;\r\n        background: #f79a47;\r\n        border-radius: 2px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 18px;\r\n        color: #ffffff;\r\n        line-height: 50px;\r\n        text-align: center;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n}\r\n.card_top {\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: 30px;\r\n  padding: 30px;\r\n  // padding: 58px 60px 32px 62px;\r\n  .card_top_item {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 20px;\r\n    .largeCategory {\r\n      width: 90px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #222222;\r\n      margin-right: 28px;\r\n    }\r\n    .smallCategory {\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n      padding: 12px 24px;\r\n      cursor: pointer;\r\n    }\r\n    .smallCategoryActive {\r\n      background: #e0f7f5;\r\n      border-radius: 2px;\r\n      color: #21c9b8;\r\n    }\r\n  }\r\n  .card_top_item:nth-child(1) {\r\n    margin-top: 0;\r\n  }\r\n  .card_top_itemLine {\r\n    width: 100%;\r\n    height: 1px;\r\n    background: #eeeeee;\r\n    margin-top: 20px;\r\n  }\r\n  .buttonStyle {\r\n    margin-top: 9px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    .imgStyle {\r\n      width: 19px;\r\n      height: 16px;\r\n      cursor: pointer;\r\n    }\r\n    .buttonText {\r\n      margin-left: 10px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 400;\r\n      font-size: 16px;\r\n      color: #21c9b8;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n}\r\n.card_bottom {\r\n  height: 850px;\r\n  background: #ffffff;\r\n  border-radius: 2px;\r\n  margin-top: 30px;\r\n  padding: 40px 60px 62px 60px;\r\n  .content_bottom {\r\n    display: flex;\r\n    align-items: center;\r\n    flex-wrap: wrap;\r\n    .card_bottom_item {\r\n      width: 255px;\r\n      height: 150px;\r\n      background: #ffffff;\r\n      box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.06);\r\n      border-radius: 4px;\r\n      border: 2px solid #ffffff;\r\n      margin-left: 20px;\r\n      padding: 20px 18px;\r\n      display: flex;\r\n      cursor: pointer;\r\n\r\n      span {\r\n        position: absolute;\r\n        z-index: 1;\r\n        background-color: #37c9b8;\r\n        transition: transform 0.5s ease;\r\n      }\r\n      .bottom,\r\n      .top {\r\n        height: 2px;\r\n        left: -1px;\r\n        right: -1px;\r\n        transform: scaleX(0);\r\n      }\r\n      .left,\r\n      .right {\r\n        width: 2px;\r\n        top: -1px;\r\n        bottom: -1px;\r\n        transform: scaleY(0);\r\n      }\r\n      .bottom {\r\n        bottom: -1px;\r\n        transform-origin: bottom right;\r\n      }\r\n      .right {\r\n        right: -1px;\r\n        transform-origin: top right;\r\n      }\r\n      .top {\r\n        top: -1px;\r\n        transform-origin: top left;\r\n      }\r\n      .left {\r\n        left: -1px;\r\n        transform-origin: bottom left;\r\n      }\r\n      .imgStyle {\r\n        width: 90px;\r\n        height: 108px;\r\n      }\r\n      .content_bottom_right {\r\n        margin-left: 17px;\r\n      }\r\n      .title {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 18px;\r\n        color: #000000;\r\n        height: 24px;\r\n      }\r\n      .category {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 16px;\r\n        color: #7e7e7e;\r\n        margin-top: 14px;\r\n        overflow: hidden;\r\n        display: -webkit-box;\r\n        -webkit-box-orient: vertical;\r\n        -webkit-line-clamp: 1;\r\n        text-overflow: ellipsis;\r\n        word-wrap: break-word;\r\n      }\r\n      .statusStyle {\r\n        width: 70px;\r\n        height: 30px;\r\n        margin-top: 22px;\r\n        background: #e0f7f5;\r\n        border-radius: 2px;\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #21c9b8;\r\n        line-height: 450px;\r\n        text-align: center;\r\n        line-height: 30px;\r\n      }\r\n    }\r\n    .card_bottom_item:nth-child(4n + 1) {\r\n      margin-left: 0;\r\n    }\r\n    .card_bottom_item:nth-child(n + 5) {\r\n      margin-top: 20px;\r\n    }\r\n    .card_bottom_item:hover {\r\n      box-shadow: 0px 3px 20px 0px rgba(132, 212, 178, 0.6);\r\n      scale: 1.01;\r\n      .top {\r\n        transform-origin: top right;\r\n        transform: scaleX(1);\r\n      }\r\n      .left {\r\n        transform-origin: top left;\r\n        transform: scaleY(1);\r\n      }\r\n      .bottom {\r\n        transform-origin: bottom left;\r\n        transform: scaleX(1);\r\n      }\r\n      .right {\r\n        transform-origin: bottom right;\r\n        transform: scaleY(1);\r\n      }\r\n    }\r\n  }\r\n  .pageStyle {\r\n    margin-top: 60px;\r\n    width: 100%;\r\n    text-align: center;\r\n  }\r\n}\r\n.none-class {\r\n  text-align: center;\r\n  padding: 8% 0;\r\n  .text {\r\n    font-size: 14px;\r\n    font-weight: 400;\r\n    color: #999999;\r\n    line-height: 14px;\r\n  }\r\n}\r\n.ellips1 {\r\n  overflow: hidden;\r\n  display: -webkit-box;\r\n  -webkit-box-orient: vertical;\r\n  -webkit-line-clamp: 1;\r\n  text-overflow: ellipsis;\r\n  word-wrap: break-word;\r\n}\r\n</style>\r\n"]}]}