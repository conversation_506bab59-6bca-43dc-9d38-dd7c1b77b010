{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\invoiceInfo\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\invoiceInfo\\index.vue", "mtime": 1750311963060}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_user", "require", "_store", "_interopRequireDefault", "_userMenu", "_userAvatar", "_userInfo", "_oss", "_auth", "name", "components", "userAvatar", "userInfo", "UserMenu", "data", "fit", "editDialog", "companyDialog", "positionDialog", "actionUrl", "uploadUrl", "headers", "Authorization", "getToken", "userId", "store", "getters", "user", "form", "headType", "invoiceType", "companyName", "dutyParagraph", "address", "phone", "openAccount", "bankAccount", "email", "rules", "companyOptions", "positionOptions", "companyLoading", "companyId", "companyCode", "personalStatus", "companyStatus", "positionFirstData", "activeFirstIndex", "positionChildData", "selectPositionData", "identityType", "created", "getInvoiceData", "methods", "_this", "invoiceList", "then", "res", "code", "console", "log", "isRelevanceCompany", "flag", "$route", "query", "relevanceCompany", "openComapnyDialog", "cancelEditDialog", "openEditDialog", "id", "_objectSpread2", "default", "cancelComapnyDialog", "cancelPositionDialog", "openPositionDialog", "submitCompanyForm", "getUser", "_this2", "getUserInfo", "response", "roleGroup", "postGroup", "checkAuthStatus", "getPositionData", "_this3", "for<PERSON>ach", "item", "push", "children", "selectFisrt", "index", "selectPosition", "position", "postName", "companyChanged", "_this4", "creditCode", "changePosition", "getCompanyList", "_this5", "getCompanyListByName", "transferCompany", "_this6", "tianyanId", "businessNo", "bussinessNo", "$modal", "msgSuccess", "dispatch", "submitForm", "_this7", "invoiceEdit", "invoiceAdd", "handleUploadSuccess", "file", "fileList", "avatar", "url", "reset", "jumpToApprove", "$router"], "sources": ["src/views/system/user/invoiceInfo/index.vue"], "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-03-24 09:06:27\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"user-info-page\">\r\n    <div class=\"app-container\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"2.5\" :xs=\"24\">\r\n          <user-menu activeIndex=\"1\" />\r\n        </el-col>\r\n        <el-col :span=\"20\" :xs=\"24\">\r\n          <div class=\"user-info-container\">\r\n            <div class=\"header-small\">\r\n              <div class=\"red-tag\"></div>\r\n              发票信息\r\n            </div>\r\n            <div class=\"user-info-card\">\r\n              <el-button\r\n                class=\"edit-button\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"openEditDialog\"\r\n                >点击修改</el-button\r\n              >\r\n              <!-- <el-image\r\n                class=\"user-info-avatar\"\r\n                :src=\"user.avatar\"\r\n                :fit=\"fit\"\r\n              ></el-image> -->\r\n\r\n              <!-- <div class=\"user-name\">{{ user.realName || \"--\" }}</div>\r\n              <div class=\"phone-class\">\r\n                <el-image :src=\"require('@/assets/user/phone.png')\"></el-image>\r\n                <span class=\"phone-number\">{{ user.phonenumber }}</span>\r\n              </div> -->\r\n              <div class=\"info-box\" style=\"width: 350px\">\r\n                <el-form ref=\"form\" :model=\"user\" label-width=\"80px\">\r\n                  <!-- <el-form-item label=\"抬头类型:\">\r\n                    {{ user.headType }}\r\n                  </el-form-item> -->\r\n                  <el-form-item label=\"发票类型:\">\r\n                    <span>{{\r\n                      user.invoiceType == \"1\"\r\n                        ? \"专票\"\r\n                        : user.invoiceType == \"2\"\r\n                        ? \"普票\"\r\n                        : \"\"\r\n                    }}</span>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"公司名称:\">\r\n                    {{ user.companyName }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"税号:\">\r\n                    {{ user.dutyParagraph }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"公司地址:\">\r\n                    {{ user.address }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"公司电话:\">\r\n                    {{ user.phone }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"开户银行:\">\r\n                    {{ user.openAccount }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"银行账号:\">\r\n                    {{ user.bankAccount }}\r\n                  </el-form-item>\r\n                  <el-form-item label=\"邮箱地址:\">\r\n                    {{ user.email }}\r\n                  </el-form-item>\r\n                  <!-- <el-form-item label=\"身份认证:\">\r\n                    <div class=\"tag-group\">\r\n                      <a\r\n                        class=\"label-container orange\"\r\n                        v-if=\"this.companyStatus == '1'\"\r\n                        @click=\"jumpToApprove\"\r\n                      >\r\n                        <el-image\r\n                          style=\"width: 12px; height: 12px\"\r\n                          :src=\"\r\n                            require('@/assets/user/authentication_orange.png')\r\n                          \"\r\n                        ></el-image>\r\n                        <span>企业认证</span>\r\n                      </a>\r\n                      <a\r\n                        class=\"label-container red\"\r\n                        v-if=\"personalStatus == '1'\"\r\n                        @click=\"jumpToApprove\"\r\n                      >\r\n                        <el-image\r\n                          class=\"red\"\r\n                          style=\"width: 12px; height: 12px\"\r\n                          :src=\"require('@/assets/user/authentication_red.png')\"\r\n                        ></el-image>\r\n                        <span>名片认证</span>\r\n                      </a>\r\n                      <a\r\n                        v-if=\"\r\n                          this.personalStatus == '0' &&\r\n                          this.companyStatus == '0'\r\n                        \"\r\n                        class=\"label-container\"\r\n                        @click=\"jumpToApprove\"\r\n                      >\r\n                        <el-image\r\n                          style=\"width: 12px; height: 12px\"\r\n                          :src=\"require('@/assets/user/authentication.png')\"\r\n                        ></el-image>\r\n                        <span>未认证</span>\r\n                      </a>\r\n                    </div>\r\n                  </el-form-item> -->\r\n                </el-form>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 添加或修改部门对话框 -->\r\n      <el-dialog\r\n        title=\"发票信息修改\"\r\n        :visible.sync=\"editDialog\"\r\n        width=\"750px\"\r\n        append-to-body\r\n      >\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n          <!-- <el-form-item label=\"抬头类型\" prop=\"realName\">\r\n            <el-radio v-model=\"form.headType\" label=\"1\">公司</el-radio>\r\n            <el-radio v-model=\"form.headType\" label=\"2\">个人</el-radio>\r\n          </el-form-item> -->\r\n          <el-form-item label=\"发票类型\" prop=\"realName\">\r\n            <el-radio v-model=\"form.invoiceType\" :label=\"1\">专票</el-radio>\r\n            <el-radio v-model=\"form.invoiceType\" :label=\"2\">普票</el-radio>\r\n          </el-form-item>\r\n          <el-form-item label=\"公司名称\" prop=\"phonenumber\">\r\n            <el-input v-model=\"form.companyName\" placeholder=\"请输入公司名称\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"税号\" prop=\"weixin\">\r\n            <el-input v-model=\"form.dutyParagraph\" placeholder=\"请输入税号\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"公司地址\" prop=\"email\">\r\n            <el-input v-model=\"form.address\" placeholder=\"请输入公司地址\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"公司电话\" prop=\"email\">\r\n            <el-input v-model=\"form.phone\" placeholder=\"请输入公司电话\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"开户银行\" prop=\"email\">\r\n            <el-input v-model=\"form.openAccount\" placeholder=\"请输入开户银行\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行账号\" prop=\"email\">\r\n            <el-input v-model=\"form.bankAccount\" placeholder=\"请输入银行账号\" />\r\n          </el-form-item>\r\n          <el-form-item label=\"邮箱地址\" prop=\"email\">\r\n            <el-input v-model=\"form.email\" placeholder=\"请输入邮箱地址\" />\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n          <el-button @click=\"cancelEditDialog\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n      <el-dialog\r\n        title=\"关联企业\"\r\n        :visible.sync=\"companyDialog\"\r\n        width=\"550px\"\r\n        append-to-body\r\n      >\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n          <el-form-item label=\"企业名称\">\r\n            <el-select\r\n              v-model=\"companyId\"\r\n              filterable\r\n              remote\r\n              reserve-keyword\r\n              placeholder=\"请输入关键词\"\r\n              :remote-method=\"getCompanyList\"\r\n              :loading=\"companyLoading\"\r\n              @change=\"companyChanged\"\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in companyOptions\"\r\n                :key=\"item.id\"\r\n                :label=\"item.name\"\r\n                :value=\"item.id\"\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"transferCompany\">确 定</el-button>\r\n          <el-button @click=\"cancelComapnyDialog\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n      <el-dialog\r\n        title=\"修改职务\"\r\n        :visible.sync=\"positionDialog\"\r\n        width=\"800px\"\r\n        append-to-body\r\n      >\r\n        <el-row>\r\n          <el-col :span=\"5\">\r\n            <div class=\"left-container\">\r\n              <div\r\n                v-for=\"(firstData, index) in positionFirstData\"\r\n                v-bind:key=\"firstData.postName\"\r\n                :class=\"\r\n                  index === activeFirstIndex\r\n                    ? 'position-header selected-style'\r\n                    : 'position-header'\r\n                \"\r\n              >\r\n                <a @click=\"selectFisrt(index, firstData)\">\r\n                  {{ firstData.postName || \"\" }}\r\n                </a>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"19\">\r\n            <div class=\"right-container\">\r\n              <div\r\n                v-for=\"childData in positionChildData\"\r\n                v-bind:key=\"childData.postId\"\r\n              >\r\n                <div>\r\n                  <div class=\"second-header\">\r\n                    {{ childData.postName || \"--\" }}\r\n                  </div>\r\n                  <div class=\"position-container\">\r\n                    <a\r\n                      v-for=\"pos in childData.children\"\r\n                      v-bind:key=\"pos.postId\"\r\n                      :class=\"\r\n                        selectPositionData.postId == pos.postId\r\n                          ? 'position-tag selected'\r\n                          : 'position-tag'\r\n                      \"\r\n                      @click=\"selectPosition(pos)\"\r\n                    >\r\n                      {{ pos.postName || \"--\" }}\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"changePosition\">确 定</el-button>\r\n          <el-button @click=\"cancelPositionDialog\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { invoiceList, invoiceAdd, invoiceEdit } from \"@/api/system/user\";\r\nimport store from \"@/store\";\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport userAvatar from \"../profile/userAvatar\";\r\nimport userInfo from \"../profile/userInfo\";\r\nimport { uploadUrl } from \"@/api/oss\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport {\r\n  getUserInfo,\r\n  updateUserInfo,\r\n  checkAuthStatus,\r\n  getCompanyListByName,\r\n  transferCompany,\r\n  getPositionData,\r\n} from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"UserInfo\",\r\n  components: { userAvatar, userInfo, UserMenu },\r\n  data() {\r\n    return {\r\n      fit: \"cover\",\r\n      // 是否显示弹出层\r\n      editDialog: false,\r\n      companyDialog: false,\r\n      positionDialog: false,\r\n      actionUrl: uploadUrl(),\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      userId: store.getters.userId,\r\n      user: {\r\n        // name: store.getters.name,\r\n        // avatar: store.getters.avatar,\r\n        // id: store.getters.userId,\r\n      },\r\n      form: {\r\n        headType: \"1\",\r\n        invoiceType: 1,\r\n        companyName: \"\",\r\n        dutyParagraph: \"\",\r\n        address: \"\",\r\n        phone: \"\",\r\n        openAccount: \"\",\r\n        bankAccount: \"\",\r\n        email: \"\",\r\n      },\r\n      rules: {},\r\n      companyOptions: [],\r\n      positionOptions: [],\r\n      companyLoading: false,\r\n      companyId: \"\",\r\n      companyName: \"\",\r\n      companyCode: \"\",\r\n      personalStatus: \"\",\r\n      companyStatus: \"\",\r\n      positionFirstData: [],\r\n      activeFirstIndex: 0,\r\n      positionChildData: [],\r\n      selectPositionData: {},\r\n      identityType: \"\",\r\n    };\r\n  },\r\n  created() {\r\n    // this.getUser();\r\n    // this.getPositionData();\r\n    // this.isRelevanceCompany();\r\n    this.getInvoiceData();\r\n  },\r\n  methods: {\r\n    getInvoiceData() {\r\n      invoiceList().then((res) => {\r\n        if (res.code === 200) {\r\n          console.log(res, \"------------\");\r\n          this.user = res.data ? res.data : {};\r\n        }\r\n      });\r\n    },\r\n    isRelevanceCompany() {\r\n      let flag = this.$route.query.relevanceCompany;\r\n      if (flag == \"1\") {\r\n        this.openComapnyDialog();\r\n      }\r\n    },\r\n    cancelEditDialog() {\r\n      this.editDialog = false;\r\n      this.form = {};\r\n    },\r\n    openEditDialog() {\r\n      if (this.user.id) {\r\n        this.form = { ...this.user };\r\n      } else {\r\n        this.form = {\r\n          headType: \"1\",\r\n          invoiceType: 1,\r\n          companyName: \"\",\r\n          dutyParagraph: \"\",\r\n          address: \"\",\r\n          phone: \"\",\r\n          openAccount: \"\",\r\n          bankAccount: \"\",\r\n          email: \"\",\r\n        };\r\n      }\r\n      this.editDialog = true;\r\n    },\r\n    cancelComapnyDialog() {\r\n      this.companyDialog = false;\r\n      this.companyCode = \"\";\r\n      this.companyId = \"\";\r\n    },\r\n    openComapnyDialog() {\r\n      this.form = { ...this.user };\r\n      this.companyDialog = true;\r\n    },\r\n    cancelPositionDialog() {\r\n      this.selectPositionData = {};\r\n      this.identityType = \"\";\r\n      this.positionDialog = false;\r\n    },\r\n    openPositionDialog() {\r\n      this.selectPositionData = {};\r\n      this.identityType = \"\";\r\n      this.positionDialog = true;\r\n    },\r\n    submitCompanyForm() {},\r\n    getUser() {\r\n      getUserInfo(this.userId).then((response) => {\r\n        this.user = response.data;\r\n        this.roleGroup = response.roleGroup;\r\n        this.postGroup = response.postGroup;\r\n      });\r\n      checkAuthStatus().then((response) => {\r\n        this.personalStatus = response.data.personalStatus;\r\n        this.companyStatus = response.data.companyStatus;\r\n      });\r\n    },\r\n    getPositionData() {\r\n      getPositionData().then((response) => {\r\n        response.data.forEach((item) => {\r\n          this.positionFirstData.push(item);\r\n        });\r\n        this.positionChildData = response.data[0].children;\r\n      });\r\n    },\r\n    selectFisrt(index, data) {\r\n      this.activeFirstIndex = index;\r\n      this.positionChildData = data.children;\r\n    },\r\n    selectPosition(position) {\r\n      this.selectPositionData = position;\r\n      this.identityType = position.postName;\r\n    },\r\n    companyChanged(res) {\r\n      this.companyOptions.forEach((item) => {\r\n        if (item.id == res) {\r\n          this.companyCode = item.creditCode;\r\n          this.companyId = item.id;\r\n          this.companyName = item.name;\r\n        }\r\n      });\r\n    },\r\n    changePosition() {\r\n      if (this.identityType) {\r\n        this.form.identityType = this.identityType;\r\n        this.cancelPositionDialog();\r\n      }\r\n    },\r\n    getCompanyList(query) {\r\n      if (query !== \"\") {\r\n        getCompanyListByName(query).then((response) => {\r\n          this.companyOptions = response.data;\r\n        });\r\n      }\r\n    },\r\n    transferCompany() {\r\n      transferCompany({\r\n        tianyanId: this.companyId,\r\n        businessNo: this.companyCode,\r\n      }).then((res) => {\r\n        if (res.code == 200) {\r\n          this.cancelComapnyDialog();\r\n          this.form.companyName = this.companyName;\r\n          this.form.bussinessNo = this.companyCode;\r\n          let flag = this.$route.query.relevanceCompany;\r\n          if (flag == \"1\") {\r\n            this.$modal.msgSuccess(\"操作成功\");\r\n            store.dispatch(\"GetInfo\");\r\n            this.getUser();\r\n          }\r\n        }\r\n      });\r\n    },\r\n    submitForm() {\r\n      if (this.user.id) {\r\n        let data = {\r\n          id: this.user.id,\r\n          ...this.form,\r\n        };\r\n        invoiceEdit(data).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$modal.msgSuccess(\"操作成功\");\r\n            this.cancelEditDialog();\r\n            this.getInvoiceData();\r\n          }\r\n        });\r\n      } else {\r\n        invoiceAdd(this.form).then((res) => {\r\n          if (res.code == 200) {\r\n            this.$modal.msgSuccess(\"操作成功\");\r\n            this.cancelEditDialog();\r\n            this.getInvoiceData();\r\n          }\r\n        });\r\n      }\r\n    },\r\n    handleUploadSuccess(res, file, fileList) {\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.form.avatar = res.data.url;\r\n      }\r\n    },\r\n    reset() {},\r\n    jumpToApprove() {\r\n      this.$router.push(\"/user/approveSetting\");\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-input {\r\n  height: 36px;\r\n  width: 380px;\r\n  line-height: 36px;\r\n}\r\n.action-class {\r\n  padding-left: 16px;\r\n  color: #21c9b8;\r\n}\r\n.el-button--primary {\r\n  color: #21c9b8;\r\n  background-color: #ffffff;\r\n  border-color: #21c9b8;\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .user-info-container {\r\n    background: #fff;\r\n    height: calc(100vh - 150px);\r\n    padding: 20px;\r\n    position: relative;\r\n\r\n    .edit-button {\r\n      position: absolute;\r\n      right: 10px;\r\n      top: 90px;\r\n      margin-left: 30px;\r\n      color: #21c9b8;\r\n      background-color: #ffffff;\r\n      border-color: transparent;\r\n    }\r\n    .user-info-avatar {\r\n      width: 120px;\r\n      height: 120px;\r\n      border-radius: 50%;\r\n    }\r\n    .header-small {\r\n      text-align: center;\r\n      display: flex;\r\n      font-size: 20px;\r\n      font-weight: 500;\r\n      color: #333333;\r\n      line-height: 20px;\r\n\r\n      .red-tag {\r\n        margin-right: 12px;\r\n        width: 3px;\r\n        height: 22px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .user-info-card {\r\n      margin-top: 80px;\r\n      margin-right: 80px;\r\n      text-align: center;\r\n      .user-name {\r\n        margin-top: 10px;\r\n        font-size: 24px;\r\n        font-weight: 500;\r\n        color: #333333;\r\n        line-height: 24px;\r\n      }\r\n      .phone-class {\r\n        width: 100%;\r\n        height: 30px;\r\n        margin-top: 12px;\r\n\r\n        .el-image {\r\n          width: 16px;\r\n          height: 16px;\r\n        }\r\n        .phone-number {\r\n          margin-left: 12px;\r\n          font-size: 16px;\r\n          font-weight: 400;\r\n          color: #333333;\r\n          line-height: 16px;\r\n        }\r\n      }\r\n      .info-box {\r\n        margin: 24px auto 0 auto;\r\n        .el-form-item {\r\n          margin-bottom: 4px;\r\n          text-align: start;\r\n        }\r\n        .tag-group {\r\n          display: flex;\r\n          .label-container {\r\n            padding: 4px 12px;\r\n            margin-top: 6px;\r\n            margin-right: 6px;\r\n            height: 24px;\r\n            background: #f0f1f4;\r\n            border-radius: 4px;\r\n            font-size: 12px;\r\n            font-weight: 500;\r\n            color: #8a8c94;\r\n            line-height: 12px;\r\n            .el-image {\r\n              margin: 2px 4px 0 0;\r\n            }\r\n          }\r\n          .orange {\r\n            background: rgba(255, 191, 69, 0.2);\r\n            color: #ff8a27;\r\n          }\r\n          .red {\r\n            background: rgba(197, 37, 33, 0.1);\r\n            color: #21c9b8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .position-header {\r\n      color: red;\r\n    }\r\n  }\r\n}\r\n\r\n.left-container {\r\n  height: 500px;\r\n  margin-right: 10px;\r\n  overflow-y: auto;\r\n  text-align: center;\r\n  .position-header {\r\n    height: 50px;\r\n    line-height: 50px;\r\n    font-weight: 500;\r\n\r\n    overflow-y: auto;\r\n  }\r\n  .selected-style {\r\n    background: #21c9b8;\r\n    color: #fff;\r\n  }\r\n}\r\n\r\n.right-container {\r\n  height: 500px;\r\n  overflow-y: auto;\r\n\r\n  .second-header {\r\n    height: 65px;\r\n    line-height: 65px;\r\n\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n  }\r\n  .position-container {\r\n    width: 70%;\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    .position-tag {\r\n      padding: 0 20px;\r\n      font-size: 15px;\r\n      line-height: 36px;\r\n      color: #000;\r\n    }\r\n    .selected {\r\n      background: #21c9b8 !important;\r\n      color: #fff !important;\r\n    }\r\n    a:hover {\r\n      cursor: pointer;\r\n      color: #21c9b8;\r\n      text-decoration: none;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;AAuQA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,SAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,SAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,IAAA,GAAAN,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAUA;EACAQ,IAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,QAAA,EAAAA,iBAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,GAAA;MACA;MACAC,UAAA;MACAC,aAAA;MACAC,cAAA;MACAC,SAAA,MAAAC,cAAA;MACAC,OAAA;QAAAC,aAAA,kBAAAC,cAAA;MAAA;MACAC,MAAA,EAAAC,cAAA,CAAAC,OAAA,CAAAF,MAAA;MACAG,IAAA;QACA;QACA;QACA;MAAA,CACA;MACAC,IAAA;QACAC,QAAA;QACAC,WAAA;QACAC,WAAA;QACAC,aAAA;QACAC,OAAA;QACAC,KAAA;QACAC,WAAA;QACAC,WAAA;QACAC,KAAA;MACA;MACAC,KAAA;MACAC,cAAA;MACAC,eAAA;MACAC,cAAA;MACAC,SAAA;MACAX,WAAA;MACAY,WAAA;MACAC,cAAA;MACAC,aAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,iBAAA;MACAC,kBAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA;IACA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAD,cAAA,WAAAA,eAAA;MAAA,IAAAE,KAAA;MACA,IAAAC,iBAAA,IAAAC,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAC,OAAA,CAAAC,GAAA,CAAAH,GAAA;UACAH,KAAA,CAAA3B,IAAA,GAAA8B,GAAA,CAAA3C,IAAA,GAAA2C,GAAA,CAAA3C,IAAA;QACA;MACA;IACA;IACA+C,kBAAA,WAAAA,mBAAA;MACA,IAAAC,IAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,gBAAA;MACA,IAAAH,IAAA;QACA,KAAAI,iBAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAnD,UAAA;MACA,KAAAY,IAAA;IACA;IACAwC,cAAA,WAAAA,eAAA;MACA,SAAAzC,IAAA,CAAA0C,EAAA;QACA,KAAAzC,IAAA,OAAA0C,cAAA,CAAAC,OAAA,WAAA5C,IAAA;MACA;QACA,KAAAC,IAAA;UACAC,QAAA;UACAC,WAAA;UACAC,WAAA;UACAC,aAAA;UACAC,OAAA;UACAC,KAAA;UACAC,WAAA;UACAC,WAAA;UACAC,KAAA;QACA;MACA;MACA,KAAArB,UAAA;IACA;IACAwD,mBAAA,WAAAA,oBAAA;MACA,KAAAvD,aAAA;MACA,KAAA0B,WAAA;MACA,KAAAD,SAAA;IACA;IACAwB,iBAAA,WAAAA,kBAAA;MACA,KAAAtC,IAAA,OAAA0C,cAAA,CAAAC,OAAA,WAAA5C,IAAA;MACA,KAAAV,aAAA;IACA;IACAwD,oBAAA,WAAAA,qBAAA;MACA,KAAAxB,kBAAA;MACA,KAAAC,YAAA;MACA,KAAAhC,cAAA;IACA;IACAwD,kBAAA,WAAAA,mBAAA;MACA,KAAAzB,kBAAA;MACA,KAAAC,YAAA;MACA,KAAAhC,cAAA;IACA;IACAyD,iBAAA,WAAAA,kBAAA;IACAC,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,iBAAA,OAAAtD,MAAA,EAAAgC,IAAA,WAAAuB,QAAA;QACAF,MAAA,CAAAlD,IAAA,GAAAoD,QAAA,CAAAjE,IAAA;QACA+D,MAAA,CAAAG,SAAA,GAAAD,QAAA,CAAAC,SAAA;QACAH,MAAA,CAAAI,SAAA,GAAAF,QAAA,CAAAE,SAAA;MACA;MACA,IAAAC,qBAAA,IAAA1B,IAAA,WAAAuB,QAAA;QACAF,MAAA,CAAAjC,cAAA,GAAAmC,QAAA,CAAAjE,IAAA,CAAA8B,cAAA;QACAiC,MAAA,CAAAhC,aAAA,GAAAkC,QAAA,CAAAjE,IAAA,CAAA+B,aAAA;MACA;IACA;IACAsC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,qBAAA,IAAA3B,IAAA,WAAAuB,QAAA;QACAA,QAAA,CAAAjE,IAAA,CAAAuE,OAAA,WAAAC,IAAA;UACAF,MAAA,CAAAtC,iBAAA,CAAAyC,IAAA,CAAAD,IAAA;QACA;QACAF,MAAA,CAAApC,iBAAA,GAAA+B,QAAA,CAAAjE,IAAA,IAAA0E,QAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAAC,KAAA,EAAA5E,IAAA;MACA,KAAAiC,gBAAA,GAAA2C,KAAA;MACA,KAAA1C,iBAAA,GAAAlC,IAAA,CAAA0E,QAAA;IACA;IACAG,cAAA,WAAAA,eAAAC,QAAA;MACA,KAAA3C,kBAAA,GAAA2C,QAAA;MACA,KAAA1C,YAAA,GAAA0C,QAAA,CAAAC,QAAA;IACA;IACAC,cAAA,WAAAA,eAAArC,GAAA;MAAA,IAAAsC,MAAA;MACA,KAAAxD,cAAA,CAAA8C,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAjB,EAAA,IAAAZ,GAAA;UACAsC,MAAA,CAAApD,WAAA,GAAA2C,IAAA,CAAAU,UAAA;UACAD,MAAA,CAAArD,SAAA,GAAA4C,IAAA,CAAAjB,EAAA;UACA0B,MAAA,CAAAhE,WAAA,GAAAuD,IAAA,CAAA7E,IAAA;QACA;MACA;IACA;IACAwF,cAAA,WAAAA,eAAA;MACA,SAAA/C,YAAA;QACA,KAAAtB,IAAA,CAAAsB,YAAA,QAAAA,YAAA;QACA,KAAAuB,oBAAA;MACA;IACA;IACAyB,cAAA,WAAAA,eAAAlC,KAAA;MAAA,IAAAmC,MAAA;MACA,IAAAnC,KAAA;QACA,IAAAoC,0BAAA,EAAApC,KAAA,EAAAR,IAAA,WAAAuB,QAAA;UACAoB,MAAA,CAAA5D,cAAA,GAAAwC,QAAA,CAAAjE,IAAA;QACA;MACA;IACA;IACAuF,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,qBAAA;QACAE,SAAA,OAAA7D,SAAA;QACA8D,UAAA,OAAA7D;MACA,GAAAa,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA4C,MAAA,CAAA9B,mBAAA;UACA8B,MAAA,CAAA1E,IAAA,CAAAG,WAAA,GAAAuE,MAAA,CAAAvE,WAAA;UACAuE,MAAA,CAAA1E,IAAA,CAAA6E,WAAA,GAAAH,MAAA,CAAA3D,WAAA;UACA,IAAAmB,IAAA,GAAAwC,MAAA,CAAAvC,MAAA,CAAAC,KAAA,CAAAC,gBAAA;UACA,IAAAH,IAAA;YACAwC,MAAA,CAAAI,MAAA,CAAAC,UAAA;YACAlF,cAAA,CAAAmF,QAAA;YACAN,MAAA,CAAA1B,OAAA;UACA;QACA;MACA;IACA;IACAiC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,SAAAnF,IAAA,CAAA0C,EAAA;QACA,IAAAvD,IAAA,OAAAwD,cAAA,CAAAC,OAAA;UACAF,EAAA,OAAA1C,IAAA,CAAA0C;QAAA,GACA,KAAAzC,IAAA,CACA;QACA,IAAAmF,iBAAA,EAAAjG,IAAA,EAAA0C,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACAoD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;YACAG,MAAA,CAAA3C,gBAAA;YACA2C,MAAA,CAAA1D,cAAA;UACA;QACA;MACA;QACA,IAAA4D,gBAAA,OAAApF,IAAA,EAAA4B,IAAA,WAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACAoD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;YACAG,MAAA,CAAA3C,gBAAA;YACA2C,MAAA,CAAA1D,cAAA;UACA;QACA;MACA;IACA;IACA6D,mBAAA,WAAAA,oBAAAxD,GAAA,EAAAyD,IAAA,EAAAC,QAAA;MACA;MACA,IAAA1D,GAAA,CAAAC,IAAA;QACA,KAAA9B,IAAA,CAAAwF,MAAA,GAAA3D,GAAA,CAAA3C,IAAA,CAAAuG,GAAA;MACA;IACA;IACAC,KAAA,WAAAA,MAAA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAC,OAAA,CAAAjC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}