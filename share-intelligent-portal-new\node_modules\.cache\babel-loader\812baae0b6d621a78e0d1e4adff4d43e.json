{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\publishEmInformation\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\publishEmInformation\\index.vue", "mtime": 1750311963077}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_userMenu", "_interopRequireDefault", "require", "_data", "_serviceSharing", "name", "components", "UserMenu", "data", "form", "positionName", "requirements", "salaryRange", "responsibilities", "desc", "supplyName", "technologyType", "applicationArea", "productPhoto", "cooperationMode", "productStage", "enclosure", "companyName", "contactsName", "contactsMobile", "rules", "required", "message", "trigger", "salaryMin", "salaryMax", "company", "location", "contactPhone", "queryParams", "categoryId", "undefined", "status", "pageNum", "pageSize", "total", "labelStyle", "fontWeight", "fontSize", "color", "width", "justifyContent", "contentStyle", "salaryRangeList", "locationList", "created", "getSalaryRange", "getLocation", "methods", "_this", "params", "dictType", "listData", "then", "response", "rows", "_this2", "handleQuery", "getList", "reset<PERSON><PERSON>y", "handleSizeChange", "handleCurrentChange", "onSubmit", "_this3", "userinfo", "JSON", "parse", "window", "sessionStorage", "getItem", "createBy", "memberPhone", "console", "log", "$refs", "validate", "valid", "employAddData", "res", "code", "$message", "success", "onCancel", "$router", "push"], "sources": ["src/views/system/user/publishEmInformation/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row\r\n      :gutter=\"20\"\r\n      style=\"background: linear-gradient(to right, #e1f7f0, #f4fcfa)\"\r\n    >\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu activeIndex=\"1\" />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n        <div class=\"formStyle\">\r\n          <el-form ref=\"form\" :rules=\"rules\" :model=\"form\" label-position=\"top\">\r\n            <el-form-item label=\"岗位名称\" prop=\"positionName\">\r\n              <el-input\r\n                v-model=\"form.positionName\"\r\n                placeholder=\"请输入岗位名称\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"薪资范围\" prop=\"salaryRange\">\r\n              <el-select\r\n                v-model=\"form.salaryRange\"\r\n                placeholder=\"请选择薪资范围\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in salaryRangeList\"\r\n                  :key=\"dict.dictLabel\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"最低薪资\" prop=\"salaryMin\">\r\n              <el-input v-model=\"form.salaryMin\" placeholder=\"请输入最低薪资\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"最高薪资\" prop=\"salaryMax\">\r\n              <el-input v-model=\"form.salaryMax\" placeholder=\"请输入最高薪资\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"年龄限制\" prop=\"ageLimit\">\r\n              <el-input v-model=\"form.ageLimit\" placeholder=\"请输入年龄限制\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"用工单位\" prop=\"company\">\r\n              <el-input v-model=\"form.company\" placeholder=\"请输入用工单位\" />\r\n            </el-form-item>\r\n            <el-form-item label=\"用工地点\" prop=\"location\">\r\n              <el-select\r\n                v-model=\"form.location\"\r\n                placeholder=\"请选择薪资范围\"\r\n                clearable\r\n                style=\"width: 100%\"\r\n              >\r\n                <el-option\r\n                  v-for=\"dict in locationList\"\r\n                  :key=\"dict.dictLabel\"\r\n                  :label=\"dict.dictLabel\"\r\n                  :value=\"dict.dictValue\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n            <el-form-item label=\"联系方式\" prop=\"contactPhone\">\r\n              <el-input\r\n                v-model=\"form.contactPhone\"\r\n                placeholder=\"请输入联系方式\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"岗位要求\" prop=\"requirements\">\r\n              <el-input\r\n                v-model=\"form.requirements\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"岗位职责\" prop=\"responsibilities\">\r\n              <el-input\r\n                v-model=\"form.responsibilities\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"其他限制\" prop=\"otherLimits\">\r\n              <el-input\r\n                v-model=\"form.otherLimits\"\r\n                type=\"textarea\"\r\n                placeholder=\"请输入内容\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item class=\"footer-submit\">\r\n              <el-button type=\"primary\" @click=\"onSubmit\">发布</el-button>\r\n              <el-button style=\"margin-left: 140px\" @click.once=\"onCancel\"\r\n                >取消</el-button\r\n              >\r\n            </el-form-item>\r\n          </el-form>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { employAddData } from \"@/api/serviceSharing\";\r\nexport default {\r\n  name: \"User\",\r\n  components: { UserMenu },\r\n  data() {\r\n    return {\r\n      form: {\r\n        positionName: \"\",\r\n        requirements: \"\",\r\n        salaryRange: \"\",\r\n        responsibilities: \"\",\r\n        desc: \"\",\r\n        supplyName: \"\",\r\n        technologyType: [],\r\n        applicationArea: [],\r\n        productPhoto: [],\r\n        cooperationMode: \"\",\r\n        productStage: \"\",\r\n        enclosure: [],\r\n        companyName: \"\",\r\n        contactsName: \"\",\r\n        contactsMobile: \"\",\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        positionName: [\r\n          { required: true, message: \"岗位名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        salaryRange: [\r\n          {\r\n            required: true,\r\n            message: \"薪资范围不能为空\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        salaryMin: [\r\n          { required: true, message: \"最低薪资不能为空\", trigger: \"blur\" },\r\n        ],\r\n        salaryMax: [\r\n          { required: true, message: \"最高薪资不能为空\", trigger: \"blur\" },\r\n        ],\r\n        company: [\r\n          { required: true, message: \"用工单位不能为空\", trigger: \"blur\" },\r\n        ],\r\n        location: [\r\n          { required: true, message: \"用工地点不能为空\", trigger: \"blur\" },\r\n        ],\r\n        contactPhone: [\r\n          { required: true, message: \"联系方式不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      queryParams: {\r\n        categoryId: undefined,\r\n        status: undefined,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        total: 0,\r\n      },\r\n      labelStyle: {\r\n        fontWeight: 400,\r\n        fontSize: \"14px\",\r\n        color: \"#999999\",\r\n        width: \"60px\",\r\n        justifyContent: \"flex-end\",\r\n      },\r\n      contentStyle: {\r\n        fontWeight: 400,\r\n        fontSize: \"14px\",\r\n        color: \"#333333\",\r\n      },\r\n      salaryRangeList: [], // 薪资范围\r\n      locationList: [], // 用工地点\r\n    };\r\n  },\r\n  created() {\r\n    this.getSalaryRange();\r\n    this.getLocation();\r\n  },\r\n  methods: {\r\n    // 薪资范围字典\r\n    getSalaryRange() {\r\n      let params = { dictType: \"salary_range\" };\r\n      listData(params).then((response) => {\r\n        this.salaryRangeList = response.rows;\r\n      });\r\n    },\r\n    // 用工地点字典\r\n    getLocation() {\r\n      let params = { dictType: \"location\" };\r\n      listData(params).then((response) => {\r\n        this.locationList = response.rows;\r\n      });\r\n    },\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    resetQuery() {\r\n      this.queryParams = {\r\n        categoryId: undefined,\r\n        status: undefined,\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n      };\r\n      this.getList();\r\n    },\r\n    handleSizeChange(pageSize) {\r\n      this.pageSize = pageSize;\r\n      this.getList();\r\n    },\r\n    handleCurrentChange(pageNum) {\r\n      this.pageNum = pageNum;\r\n      this.getList();\r\n    },\r\n    onSubmit() {\r\n      let userinfo = JSON.parse(window.sessionStorage.getItem(\"userinfo\"));\r\n      this.form.createBy = userinfo.memberPhone;\r\n      console.log(this.form)\r\n      this.$refs[\"form\"].validate((valid) => {\r\n        if (valid) {\r\n          // this.form.materials = this.jobList;\r\n          employAddData(this.form).then((res) => {\r\n            if (res.code === 200) {\r\n              this.$message.success(\"发布成功\");\r\n              this.onCancel();\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    onCancel() {\r\n      this.$router.push(\"/user/emInformation\");\r\n    }\r\n  },\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n  background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n  height: 100vh;\r\n}\r\n\r\n.formStyle {\r\n  padding: 20px;\r\n  background: #fff;\r\n  border-radius: 10px;\r\n  .footer-submit {\r\n    text-align: center;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AAoGA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCACA;EACAG,IAAA;EACAC,UAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;QACAC,YAAA;QACAC,YAAA;QACAC,WAAA;QACAC,gBAAA;QACAC,IAAA;QACAC,UAAA;QACAC,cAAA;QACAC,eAAA;QACAC,YAAA;QACAC,eAAA;QACAC,YAAA;QACAC,SAAA;QACAC,WAAA;QACAC,YAAA;QACAC,cAAA;MACA;MACA;MACAC,KAAA;QACAf,YAAA,GACA;UAAAgB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,WAAA,GACA;UACAc,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAC,SAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAE,SAAA,GACA;UAAAJ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAG,OAAA,GACA;UAAAL,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAI,QAAA,GACA;UAAAN,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAK,YAAA,GACA;UAAAP,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAM,WAAA;QACAC,UAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD,SAAA;QACAE,OAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,KAAA;QACAC,KAAA;QACAC,cAAA;MACA;MACAC,YAAA;QACAL,UAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACAI,eAAA;MAAA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA;IACAF,cAAA,WAAAA,eAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAAN,eAAA,GAAAW,QAAA,CAAAC,IAAA;MACA;IACA;IACA;IACAR,WAAA,WAAAA,YAAA;MAAA,IAAAS,MAAA;MACA,IAAAN,MAAA;QAAAC,QAAA;MAAA;MACA,IAAAC,cAAA,EAAAF,MAAA,EAAAG,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAAZ,YAAA,GAAAU,QAAA,CAAAC,IAAA;MACA;IACA;IACAE,WAAA,WAAAA,YAAA;MACA,KAAA5B,WAAA,CAAAI,OAAA;MACA,KAAAyB,OAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAA9B,WAAA;QACAC,UAAA,EAAAC,SAAA;QACAC,MAAA,EAAAD,SAAA;QACAE,OAAA;QACAC,QAAA;MACA;MACA,KAAAwB,OAAA;IACA;IACAE,gBAAA,WAAAA,iBAAA1B,QAAA;MACA,KAAAA,QAAA,GAAAA,QAAA;MACA,KAAAwB,OAAA;IACA;IACAG,mBAAA,WAAAA,oBAAA5B,OAAA;MACA,KAAAA,OAAA,GAAAA,OAAA;MACA,KAAAyB,OAAA;IACA;IACAI,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,MAAA,CAAAC,cAAA,CAAAC,OAAA;MACA,KAAAjE,IAAA,CAAAkE,QAAA,GAAAN,QAAA,CAAAO,WAAA;MACAC,OAAA,CAAAC,GAAA,MAAArE,IAAA;MACA,KAAAsE,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAC,6BAAA,EAAAd,MAAA,CAAA3D,IAAA,EAAAiD,IAAA,WAAAyB,GAAA;YACA,IAAAA,GAAA,CAAAC,IAAA;cACAhB,MAAA,CAAAiB,QAAA,CAAAC,OAAA;cACAlB,MAAA,CAAAmB,QAAA;YACA;UACA;QACA;MACA;IACA;IACAA,QAAA,WAAAA,SAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;EACA;AACA", "ignoreList": []}]}