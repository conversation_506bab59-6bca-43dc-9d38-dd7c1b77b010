package com.ruoyi.auth.service;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.service.TokenService;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * SSO服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class SSOService {

    private static final Logger log = LoggerFactory.getLogger(SSOService.class);

    @Autowired
    private RedisService redisService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private RestTemplate restTemplate;

    // SSO服务器配置
    @Value("${sso.server.url:http://localhost:9300}")
    private String ssoServerUrl;

    @Value("${sso.server.token-url:/sso/token}")
    private String tokenUrl;

    @Value("${sso.server.validate-url:/sso/validate}")
    private String validateUrl;

    @Value("${sso.server.userinfo-url:/sso/userinfo}")
    private String userinfoUrl;

    @Value("${sso.server.logout-url:/sso/logout}")
    private String logoutUrl;

    // 客户端配置
    @Value("${sso.client.id:market}")
    private String clientId;

    @Value("${sso.client.secret:market_2024#RuoYi@Share$Key!9999}")
    private String clientSecret;

    @Value("${sso.client.callback-url:http://localhost:8081/sso/callback}")
    private String callbackUrl;

    // SSO缓存配置
    private static final String SSO_TOKEN_PREFIX = "sso:market:token:";
    private static final String SSO_USER_PREFIX = "sso:market:user:";
    private static final long SSO_CACHE_EXPIRE = 28800; // 8小时

    /**
     * 使用授权码换取访问令牌
     *
     * @param authCode 授权码
     * @return Token信息，失败返回null
     */
    public Map<String, Object> exchangeToken(String authCode) {
        if (StringUtils.isEmpty(authCode)) {
            return null;
        }

        try {
            String url = ssoServerUrl + tokenUrl;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
            params.add("grant_type", "authorization_code");
            params.add("code", authCode);
            params.add("client_id", clientId);
            params.add("client_secret", clientSecret);
            params.add("redirect_uri", callbackUrl);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(params, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                log.info("授权码换取令牌成功");
                return response.getBody();
            } else {
                log.warn("授权码换取令牌失败: {}", response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            log.error("授权码换取令牌异常", e);
            return null;
        }
    }

    /**
     * 验证访问令牌
     *
     * @param accessToken 访问令牌
     * @return 验证结果，失败返回null
     */
    public Map<String, Object> validateToken(String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            return null;
        }

        try {
            // 1. 先从缓存中查找
            String cacheKey = SSO_TOKEN_PREFIX + accessToken;
            Map<String, Object> cachedResult = redisService.getCacheObject(cacheKey);
            if (cachedResult != null) {
                log.debug("从缓存中获取Token验证结果");
                return cachedResult;
            }

            // 2. 调用SSO服务验证接口
            String url = ssoServerUrl + validateUrl;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(accessToken);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("access_token", accessToken);
            requestBody.put("client_id", clientId);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> result = response.getBody();
                Boolean valid = (Boolean) result.get("valid");
                if (Boolean.TRUE.equals(valid)) {
                    // 3. 缓存验证结果
                    redisService.setCacheObject(cacheKey, result, SSO_CACHE_EXPIRE, TimeUnit.SECONDS);

                    log.debug("访问令牌验证成功");
                    return result;
                }
            }

            log.warn("访问令牌验证失败");
            return null;

        } catch (Exception e) {
            log.error("验证访问令牌异常", e);
            return null;
        }
    }

    /**
     * 获取用户信息
     *
     * @param accessToken 访问令牌
     * @return 用户信息，失败返回null
     */
    public Map<String, Object> getUserInfo(String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            return null;
        }

        try {
            // 1. 先从缓存中查找
            String cacheKey = SSO_USER_PREFIX + accessToken;
            Map<String, Object> cachedUserInfo = redisService.getCacheObject(cacheKey);
            if (cachedUserInfo != null) {
                log.debug("从缓存中获取用户信息");
                return cachedUserInfo;
            }

            // 2. 调用SSO服务获取用户信息
            String url = ssoServerUrl + userinfoUrl;

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);

            HttpEntity<String> request = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> userInfo = response.getBody();

                // 3. 缓存用户信息
                redisService.setCacheObject(cacheKey, userInfo, SSO_CACHE_EXPIRE, TimeUnit.SECONDS);

                log.debug("获取用户信息成功: {}", userInfo.get("username"));
                return userInfo;
            } else {
                log.warn("获取用户信息失败: {}", response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            log.error("获取用户信息异常", e);
            return null;
        }
    }

    /**
     * 根据SSO用户信息获取或创建本地用户
     *
     * @param ssoUserInfo SSO用户信息
     * @return 本地用户信息
     */
    public LoginUser getOrCreateLocalUser(Map<String, Object> ssoUserInfo) {
        try {
            String ssoUserId = (String) ssoUserInfo.get("userId");
            String username = (String) ssoUserInfo.get("username");
            String[] roles = (String[]) ssoUserInfo.get("roles");
            String[] permissions = (String[]) ssoUserInfo.get("permissions");
            Boolean hasPermission = (Boolean) ssoUserInfo.get("hasPermission");

            // 1. 尝试通过用户名查找本地用户
            SysUser localUser = remoteUserService.getUserInfo(username, "inner").getData();

            if (localUser == null) {
                // 2. 创建新用户（基于SSO权限映射）
                localUser = createLocalUserFromSSO(ssoUserInfo);
            } else {
                // 3. 更新用户权限信息（基于SSO权限映射）
                updateLocalUserPermissions(localUser, ssoUserInfo);
            }

            // 4. 构造LoginUser对象
            LoginUser loginUser = new LoginUser();
            loginUser.setUserId(localUser.getUserId());
            loginUser.setUsername(localUser.getUserName());
            loginUser.setUser(localUser);

            // 5. 设置权限信息（从SSO获取的权限映射）
            if (Boolean.TRUE.equals(hasPermission) && roles != null && roles.length > 0) {
                // 这里可以根据SSO返回的角色和权限设置本地权限
                // 例如：loginUser.setPermissions(Arrays.asList(permissions));
                log.info("用户 {} 拥有角色: {} 和权限: {}", username,
                        String.join(",", roles),
                        permissions != null ? String.join(",", permissions) : "无");
            }

            // 6. 缓存SSO用户映射关系
            String userCacheKey = SSO_USER_PREFIX + "mapping:" + ssoUserId;
            Map<String, Object> mappingInfo = new HashMap<>();
            mappingInfo.put("localUserId", localUser.getUserId());
            mappingInfo.put("username", username);
            mappingInfo.put("roles", roles);
            mappingInfo.put("permissions", permissions);
            mappingInfo.put("hasPermission", hasPermission);
            redisService.setCacheObject(userCacheKey, mappingInfo, SSO_CACHE_EXPIRE, TimeUnit.SECONDS);

            return loginUser;

        } catch (Exception e) {
            log.error("获取或创建本地用户失败", e);
            return null;
        }
    }

    /**
     * 基于SSO信息创建本地用户
     */
    private SysUser createLocalUserFromSSO(Map<String, Object> ssoUserInfo) {
        // 实现用户创建逻辑
        SysUser user = new SysUser();
        user.setUserName((String) ssoUserInfo.get("username"));
        user.setNickName((String) ssoUserInfo.get("username")); // 默认使用用户名作为昵称
        user.setStatus("0"); // 正常状态
        user.setRemark("SSO自动创建用户 - 来自智能市场系统");

        // 根据SSO权限设置默认角色
        String[] roles = (String[]) ssoUserInfo.get("roles");
        if (roles != null && roles.length > 0) {
            // 这里可以根据SSO角色映射到本地角色
            if (java.util.Arrays.asList(roles).contains("admin")) {
                user.setRemark(user.getRemark() + " - 管理员用户");
            } else if (java.util.Arrays.asList(roles).contains("market_manager")) {
                user.setRemark(user.getRemark() + " - 市场管理员");
            } else {
                user.setRemark(user.getRemark() + " - 普通用户");
            }
        }

        // 调用远程服务创建用户
        // remoteUserService.insertUser(user, "inner");

        log.info("基于SSO信息创建本地用户: {}", user.getUserName());
        return user;
    }

    /**
     * 更新本地用户权限信息
     */
    private void updateLocalUserPermissions(SysUser localUser, Map<String, Object> ssoUserInfo) {
        // 实现用户权限更新逻辑
        String[] roles = (String[]) ssoUserInfo.get("roles");
        String[] permissions = (String[]) ssoUserInfo.get("permissions");

        // 这里可以根据SSO返回的权限信息更新本地用户的角色和权限
        // 例如：同步角色信息、更新权限缓存等

        log.info("更新用户 {} 的权限信息，角色: {}, 权限: {}",
                localUser.getUserName(),
                roles != null ? String.join(",", roles) : "无",
                permissions != null ? String.join(",", permissions) : "无");

        // 可以在这里调用远程服务更新用户信息
        // remoteUserService.updateUser(localUser, "inner");
    }

    /**
     * SSO登出
     *
     * @param accessToken 访问令牌
     * @return 是否登出成功
     */
    public boolean logout(String accessToken) {
        if (StringUtils.isEmpty(accessToken)) {
            return false;
        }

        try {
            String url = ssoServerUrl + logoutUrl;

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);

            HttpEntity<String> request = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                // 清除本地缓存
                clearLocalCache(accessToken);

                log.info("SSO登出成功");
                return true;
            } else {
                log.warn("SSO登出失败: {}", response.getStatusCode());
                return false;
            }

        } catch (Exception e) {
            log.error("SSO登出异常", e);
            return false;
        }
    }

    /**
     * 清除本地缓存
     */
    private void clearLocalCache(String accessToken) {
        try {
            String tokenCacheKey = SSO_TOKEN_PREFIX + accessToken;
            String userCacheKey = SSO_USER_PREFIX + accessToken;

            redisService.deleteObject(tokenCacheKey);
            redisService.deleteObject(userCacheKey);

            log.debug("清除本地SSO缓存成功");
        } catch (Exception e) {
            log.error("清除本地SSO缓存失败", e);
        }
    }

    /**
     * 获取SSO登录URL
     */
    public String getSSOLoginUrl(String redirectUrl) {
        StringBuilder loginUrl = new StringBuilder();
        loginUrl.append(ssoServerUrl).append("/sso/login");
        loginUrl.append("?client_id=").append(clientId);
        loginUrl.append("&redirect_uri=").append(callbackUrl);

        if (StringUtils.isNotEmpty(redirectUrl)) {
            loginUrl.append("&state=").append(redirectUrl);
        }

        return loginUrl.toString();
    }

    /**
     * 检查SSO服务状态
     */
    public boolean checkSSOServerStatus() {
        try {
            String url = ssoServerUrl + "/sso/status";

            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);

            return response.getStatusCode() == HttpStatus.OK;

        } catch (Exception e) {
            log.error("检查SSO服务状态异常", e);
            return false;
        }
    }

    /**
     * 记录SSO登录日志
     */
    public void recordSSOLogin(LoginUser loginUser, String accessToken) {
        try {
            log.info("用户 {} 通过SSO登录成功，客户端: {}", loginUser.getUsername(), clientId);
            // 可以在这里记录登录日志到数据库
        } catch (Exception e) {
            log.error("记录SSO登录日志失败", e);
        }
    }

    /**
     * 检查用户权限
     */
    public boolean checkUserPermission(String username, String permission) {
        try {
            // 从缓存中获取用户权限映射信息
            String userCacheKey = SSO_USER_PREFIX + "mapping:" + username;
            Map<String, Object> mappingInfo = redisService.getCacheObject(userCacheKey);

            if (mappingInfo != null) {
                String[] permissions = (String[]) mappingInfo.get("permissions");
                if (permissions != null) {
                    return java.util.Arrays.asList(permissions).contains(permission);
                }
            }

            return false;
        } catch (Exception e) {
            log.error("检查用户权限失败", e);
            return false;
        }
    }

    /**
     * 检查用户角色
     */
    public boolean checkUserRole(String username, String role) {
        try {
            // 从缓存中获取用户权限映射信息
            String userCacheKey = SSO_USER_PREFIX + "mapping:" + username;
            Map<String, Object> mappingInfo = redisService.getCacheObject(userCacheKey);

            if (mappingInfo != null) {
                String[] roles = (String[]) mappingInfo.get("roles");
                if (roles != null) {
                    return java.util.Arrays.asList(roles).contains(role);
                }
            }

            return false;
        } catch (Exception e) {
            log.error("检查用户角色失败", e);
            return false;
        }
    }
}
