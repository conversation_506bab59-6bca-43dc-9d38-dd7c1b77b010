{"name": "slice-ansi", "version": "3.0.0", "description": "Slice a string with ANSI escape codes", "license": "MIT", "repository": "chalk/slice-ansi", "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["slice", "string", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "devDependencies": {"ava": "^2.1.0", "chalk": "^2.4.2", "random-item": "^3.0.0", "strip-ansi": "^5.0.0", "xo": "^0.24.0"}}