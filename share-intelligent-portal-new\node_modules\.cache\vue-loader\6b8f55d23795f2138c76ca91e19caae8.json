{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\talentDetail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\talentDetail\\index.vue", "mtime": 1750311963087}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgVXNlck1lbnUgZnJvbSAiLi4vY29tcG9uZW50cy91c2VyTWVudS52dWUiOw0KaW1wb3J0IHsgbGlzdERhdGEgfSBmcm9tICJAL2FwaS9zeXN0ZW0vZGljdC9kYXRhIjsNCmltcG9ydCB7IHRhbGVudEFkZCwgdGFsZW50RGV0YWlsRGF0YSB9IGZyb20gIkAvYXBpL3NlcnZpY2VTaGFyaW5nIjsNCmV4cG9ydCBkZWZhdWx0IHsNCiAgICBjb21wb25lbnRzOiB7IFVzZXJNZW51IH0sDQogICAgZGF0YSgpIHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAgIGRldGFpbHNEYXRhOiB7fSwNCiAgICAgICAgICAgIGZpbGVMaXN0OiBbDQogICAgICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICAgICBmaWxlTmFtZTogIuaKpeihqOaVsOaNri5jc3YiLA0KICAgICAgICAgICAgICAgICAgICBmaWxlVXJsOiAiIiwNCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAgICAgZmlsZU5hbWU6ICLmiqXooajmlbDmja4uY3N2IiwNCiAgICAgICAgICAgICAgICAgICAgZmlsZVVybDogIiIsDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIF0sDQogICAgICAgICAgICBpZDogbnVsbCwNCiAgICAgICAgICAgIHNpemU6ICIiLA0KICAgICAgICAgICAgcG9zaXRpb25UeXBlTGlzdDogW10sIC8vIOWyl+S9jeWIhuexuw0KICAgICAgICAgICAgZWR1Y2F0aW9uTGlzdDogW10sIC8vIOacgOmrmOWtpuWOhg0KICAgICAgICAgICAgam9iVGl0bGVMaXN0OiBbXSwgLy8g6IGM56ewDQogICAgICAgICAgICB3b3JrU3RhdHVzTGlzdDogW10sIC8vIOW3peS9nOeKtuaAgQ0KICAgICAgICB9Ow0KICAgIH0sDQogICAgY3JlYXRlZCgpIHsNCiAgICAgICAgdGhpcy5pZCA9IHRoaXMuJHJvdXRlLnF1ZXJ5LmlkOw0KICAgICAgICB0aGlzLmdldFBvc2l0aW9uVHlwZSgpOw0KICAgICAgICB0aGlzLmdldEVkdWNhdGlvbigpOw0KICAgICAgICB0aGlzLmdldEpvYlRpdGxlKCk7DQogICAgICAgIHRoaXMuZ2V0V29ya1N0YXR1cygpOw0KICAgICAgICB0aGlzLmdldERldGFpbERhdGEoKTsNCiAgICB9LA0KICAgIG1ldGhvZHM6IHsNCiAgICAgICAgZ2V0RGV0YWlsRGF0YSgpIHsNCiAgICAgICAgICAgIHRhbGVudERldGFpbERhdGEodGhpcy5pZCkudGhlbigocmVzKSA9PiB7DQogICAgICAgICAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5kZXRhaWxzRGF0YSA9IHJlcy5kYXRhOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvLyDlspfkvY3liIbnsbsNCiAgICAgICAgZ2V0UG9zaXRpb25UeXBlKCkgew0KICAgICAgICAgICAgbGV0IHBhcmFtcyA9IHsgZGljdFR5cGU6ICJwb3NpdGlvbl90eXBlIiB9Ow0KICAgICAgICAgICAgbGlzdERhdGEocGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMucG9zaXRpb25UeXBlTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgICAgICAgdGhpcy5wb3NpdGlvblR5cGVMaXN0LnVuc2hpZnQoew0KICAgICAgICAgICAgICAgICAgICBkaWN0VmFsdWU6ICIiLA0KICAgICAgICAgICAgICAgICAgICBkaWN0TGFiZWw6ICLlhajpg6giLA0KICAgICAgICAgICAgICAgIH0pOw0KICAgICAgICAgICAgfSk7DQogICAgICAgIH0sDQogICAgICAgIC8vIOacgOmrmOWtpuWOhg0KICAgICAgICBnZXRFZHVjYXRpb24oKSB7DQogICAgICAgICAgICBsZXQgcGFyYW1zID0geyBkaWN0VHlwZTogImVkdWNhdGlvbiIgfTsNCiAgICAgICAgICAgIGxpc3REYXRhKHBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLmVkdWNhdGlvbkxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICAgICAgICAgIHRoaXMuZWR1Y2F0aW9uTGlzdC51bnNoaWZ0KHsNCiAgICAgICAgICAgICAgICAgICAgZGljdFZhbHVlOiAiIiwNCiAgICAgICAgICAgICAgICAgICAgZGljdExhYmVsOiAi5YWo6YOoIiwNCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICB9LA0KICAgICAgICAvLyDogYznp7ANCiAgICAgICAgZ2V0Sm9iVGl0bGUoKSB7DQogICAgICAgICAgICBsZXQgcGFyYW1zID0geyBkaWN0VHlwZTogImpvYl90aXRsZSIgfTsNCiAgICAgICAgICAgIGxpc3REYXRhKHBhcmFtcykudGhlbigocmVzcG9uc2UpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLmpvYlRpdGxlTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgICAgICAgICAgdGhpcy5qb2JUaXRsZUxpc3QudW5zaGlmdCh7DQogICAgICAgICAgICAgICAgICAgIGRpY3RWYWx1ZTogIiIsDQogICAgICAgICAgICAgICAgICAgIGRpY3RMYWJlbDogIuWFqOmDqCIsDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgLy8g5bel5L2c54q25oCBDQogICAgICAgIGdldFdvcmtTdGF0dXMoKSB7DQogICAgICAgICAgICBsZXQgcGFyYW1zID0geyBkaWN0VHlwZTogIndvcmtfc3RhdHVzIiB9Ow0KICAgICAgICAgICAgbGlzdERhdGEocGFyYW1zKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMud29ya1N0YXR1c0xpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICAgICAgICAgIHRoaXMud29ya1N0YXR1c0xpc3QudW5zaGlmdCh7DQogICAgICAgICAgICAgICAgICAgIGRpY3RWYWx1ZTogIiIsDQogICAgICAgICAgICAgICAgICAgIGRpY3RMYWJlbDogIuWFqOmDqCIsDQogICAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgfSwNCiAgICAgICAgaW50ZW50aW9uKGlkKSB7DQogICAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCgiL3JlY2VpdmVPcmRlciIpOyAvLyDkvKBpZA0KICAgICAgICB9LA0KICAgICAgICBkb3duTG9hZEZpbGUodXJsKSB7DQogICAgICAgICAgICBpZiAodXJsKSB7DQogICAgICAgICAgICAgICAgd2luZG93Lm9wZW4odXJsKTsNCiAgICAgICAgICAgIH0NCiAgICAgICAgfSwNCiAgICB9LA0KfTsNCg0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/talentDetail", "sourcesContent": ["<template>\r\n    <div class=\"app-container\">\r\n        <el-row :gutter=\"20\">\r\n            <el-col :span=\"2.5\" :xs=\"24\">\r\n                <user-menu activeIndex=\"1\" />\r\n            </el-col>\r\n            <el-col :span=\"20\" :xs=\"24\" style=\"width: calc(100% - 180px)\">\r\n                <div class=\"cardStyle\">\r\n                    <!-- 左侧 -->\r\n                    <div class=\"card_left\">\r\n                        <div class=\"card_left_bottom\">\r\n                            <div class=\"imgStyle\">\r\n                                <img style=\"width: 100%; height: 100%\" :src=\"detailsData.photo\r\n                                    ? detailsData.photo\r\n                                    : require('../../../../assets/serviceSharing/ceshi2.png')\r\n                                    \" alt=\"\" />\r\n                            </div>\r\n                            <div class=\"title\">{{ detailsData.name }}</div>\r\n                            <div class=\"everyOption\">\r\n                                <div class=\"optionName\">岗位分类：</div>\r\n                                <div class=\"optionValue\" v-if=\"detailsData.positionType\">\r\n                                    {{\r\n                                        positionTypeList.filter(\r\n                                            (item) => item.dictValue == detailsData.positionType\r\n                                        )[0].dictLabel\r\n                                    }}\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"everyOption\">\r\n                                <div class=\"optionName\">最高学历：</div>\r\n                                <div class=\"optionValue\" v-if=\"detailsData.education\">\r\n                                    {{\r\n                                        educationList.filter(\r\n                                            (item) => item.dictValue == detailsData.education\r\n                                        )[0].dictLabel\r\n                                    }}\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"everyOption\">\r\n                                <div class=\"optionName\">工作状态：</div>\r\n                                <div class=\"optionValue\" v-if=\"detailsData.workStatus\">\r\n                                    {{\r\n                                        workStatusList.filter(\r\n                                            (item) => item.dictValue == detailsData.workStatus\r\n                                        )[0].dictLabel\r\n                                    }}\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"everyOption\">\r\n                                <div class=\"optionName\">联系方式：</div>\r\n                                <div class=\"optionValue\">{{ detailsData.contactPhone }}</div>\r\n                            </div>\r\n                            <!-- <div class=\"buttonStyle\" @click=\"intention\">我有意向</div> -->\r\n                        </div>\r\n                    </div>\r\n                    <!-- 中间 -->\r\n                    <div class=\"card_center_line\"></div>\r\n                    <!-- 右侧 -->\r\n                    <div class=\"card_right\">\r\n                        <div>\r\n                            <div class=\"content_title\">\r\n                                <div class=\"icon\"></div>\r\n                                <div class=\"title\">基本信息</div>\r\n                            </div>\r\n                            <div style=\"margin-top: 22px\">\r\n                                <el-descriptions class=\"margin-top\" title=\"\" :column=\"2\" :size=\"size\" border>\r\n                                    <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 毕业院校 </template>\r\n北京大学\r\n</el-descriptions-item> -->\r\n                                    <el-descriptions-item>\r\n                                        <template slot=\"label\"> 最高学历 </template>\r\n                                        {{\r\n                                            educationList.filter(\r\n                                                (item) => item.dictValue == detailsData.education\r\n                                            )[0].dictLabel\r\n                                        }}\r\n                                    </el-descriptions-item>\r\n                                    <!-- <el-descriptions-item>\r\n                <template slot=\"label\"> 出生年月 </template>\r\n                1999年5月25日\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 所在地 </template>\r\n                北京市\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 所在单位 </template>\r\n                北京爱德华科技有限公司\r\n              </el-descriptions-item>\r\n              <el-descriptions-item>\r\n                <template slot=\"label\"> 职务 </template>\r\n                经理\r\n              </el-descriptions-item> -->\r\n                                    <el-descriptions-item>\r\n                                        <template slot=\"label\"> 职称 </template>\r\n                                        {{\r\n                                            jobTitleList.filter(\r\n                                                (item) => item.dictValue == detailsData.jobTitle\r\n                                            )[0].dictLabel\r\n                                        }}\r\n                                    </el-descriptions-item>\r\n                                    <el-descriptions-item>\r\n                                        <template slot=\"label\"> 工作状态 </template>\r\n                                        {{\r\n                                            workStatusList.filter(\r\n                                                (item) => item.dictValue == detailsData.workStatus\r\n                                            )[0].dictLabel\r\n                                        }}\r\n                                    </el-descriptions-item>\r\n                                </el-descriptions>\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"margin-top: 31px\">\r\n                            <div class=\"content_title\">\r\n                                <div class=\"icon\"></div>\r\n                                <div class=\"title\">个人简历</div>\r\n                            </div>\r\n                            <div style=\"margin-top: 22px\" class=\"desc\">\r\n                                {{ detailsData.workExperience }}\r\n                            </div>\r\n                        </div>\r\n                        <div style=\"margin-top: 31px\">\r\n                            <div class=\"content_title\">\r\n                                <div class=\"icon\"></div>\r\n                                <div class=\"title\">技术领域</div>\r\n                            </div>\r\n                            <div style=\"margin-top: 22px\" class=\"desc\">\r\n                                {{ detailsData.skills }}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </el-col>\r\n        </el-row>\r\n    </div>\r\n</template>\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport { listData } from \"@/api/system/dict/data\";\r\nimport { talentAdd, talentDetailData } from \"@/api/serviceSharing\";\r\nexport default {\r\n    components: { UserMenu },\r\n    data() {\r\n        return {\r\n            detailsData: {},\r\n            fileList: [\r\n                {\r\n                    fileName: \"报表数据.csv\",\r\n                    fileUrl: \"\",\r\n                },\r\n                {\r\n                    fileName: \"报表数据.csv\",\r\n                    fileUrl: \"\",\r\n                },\r\n            ],\r\n            id: null,\r\n            size: \"\",\r\n            positionTypeList: [], // 岗位分类\r\n            educationList: [], // 最高学历\r\n            jobTitleList: [], // 职称\r\n            workStatusList: [], // 工作状态\r\n        };\r\n    },\r\n    created() {\r\n        this.id = this.$route.query.id;\r\n        this.getPositionType();\r\n        this.getEducation();\r\n        this.getJobTitle();\r\n        this.getWorkStatus();\r\n        this.getDetailData();\r\n    },\r\n    methods: {\r\n        getDetailData() {\r\n            talentDetailData(this.id).then((res) => {\r\n                if (res.code === 200) {\r\n                    this.detailsData = res.data;\r\n                }\r\n            });\r\n        },\r\n        // 岗位分类\r\n        getPositionType() {\r\n            let params = { dictType: \"position_type\" };\r\n            listData(params).then((response) => {\r\n                this.positionTypeList = response.rows;\r\n                this.positionTypeList.unshift({\r\n                    dictValue: \"\",\r\n                    dictLabel: \"全部\",\r\n                });\r\n            });\r\n        },\r\n        // 最高学历\r\n        getEducation() {\r\n            let params = { dictType: \"education\" };\r\n            listData(params).then((response) => {\r\n                this.educationList = response.rows;\r\n                this.educationList.unshift({\r\n                    dictValue: \"\",\r\n                    dictLabel: \"全部\",\r\n                });\r\n            });\r\n        },\r\n        // 职称\r\n        getJobTitle() {\r\n            let params = { dictType: \"job_title\" };\r\n            listData(params).then((response) => {\r\n                this.jobTitleList = response.rows;\r\n                this.jobTitleList.unshift({\r\n                    dictValue: \"\",\r\n                    dictLabel: \"全部\",\r\n                });\r\n            });\r\n        },\r\n        // 工作状态\r\n        getWorkStatus() {\r\n            let params = { dictType: \"work_status\" };\r\n            listData(params).then((response) => {\r\n                this.workStatusList = response.rows;\r\n                this.workStatusList.unshift({\r\n                    dictValue: \"\",\r\n                    dictLabel: \"全部\",\r\n                });\r\n            });\r\n        },\r\n        intention(id) {\r\n            this.$router.push(\"/receiveOrder\"); // 传id\r\n        },\r\n        downLoadFile(url) {\r\n            if (url) {\r\n                window.open(url);\r\n            }\r\n        },\r\n    },\r\n};\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.app-container {\r\n    background: linear-gradient(to right, rgb(225, 247, 240), rgb(244, 252, 250));\r\n    min-height: 85vh;\r\n}\r\n\r\n.content {\r\n    width: 100%;\r\n    background-color: #f2f2f2;\r\n    padding: 30px 0 61px 0;\r\n}\r\n\r\n.cardStyle {\r\n    // height: 630px;\r\n    background-color: #ffffff;\r\n    padding: 60px 56px 54px 50px;\r\n    display: flex;\r\n}\r\n\r\n.card_left {\r\n    .card_left_bottom {\r\n        .imgStyle {\r\n            width: 150px;\r\n            height: 180px;\r\n            margin-left: 35px;\r\n        }\r\n\r\n        .title {\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 500;\r\n            font-size: 20px;\r\n            color: #222222;\r\n            margin-top: 19px;\r\n            margin-bottom: 18px;\r\n            text-align: center;\r\n        }\r\n\r\n        .everyOption {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-top: 12px;\r\n\r\n            .optionName {\r\n                // height: 14px;\r\n                font-family: Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 14px;\r\n                color: #999999;\r\n            }\r\n\r\n            .optionValue {\r\n                // height: 14px;\r\n                font-family: Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 14px;\r\n                color: #333333;\r\n            }\r\n        }\r\n\r\n        .buttonStyle {\r\n            margin-top: 32px;\r\n            // margin-left: 55px;\r\n            width: 220px;\r\n            height: 50px;\r\n            background: #21c9b8;\r\n            box-shadow: 0px 3px 10px 0px rgba(33, 201, 184, 0.6);\r\n            border-radius: 2px;\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #ffffff;\r\n            text-align: center;\r\n            line-height: 50px;\r\n            cursor: pointer;\r\n        }\r\n    }\r\n}\r\n\r\n.card_center_line {\r\n    width: 1px;\r\n    height: 100%;\r\n    background: #e1e1e1;\r\n    margin-left: 60px;\r\n    margin-right: 61px;\r\n}\r\n\r\n.card_right {\r\n    width: 100%;\r\n\r\n    // overflow-y: auto;\r\n    .content_title {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .icon {\r\n            width: 4px;\r\n            height: 20px;\r\n            background: #21c9b8;\r\n        }\r\n\r\n        .title {\r\n            font-family: Source Han Sans CN;\r\n            font-weight: 400;\r\n            font-size: 18px;\r\n            color: #030a1a;\r\n            margin-left: 10px;\r\n        }\r\n    }\r\n\r\n    .desc {\r\n        font-family: Source Han Sans CN;\r\n        font-weight: 400;\r\n        font-size: 14px;\r\n        color: #666666;\r\n        line-height: 24px;\r\n    }\r\n\r\n    .fileContent {\r\n        margin-top: 22px;\r\n        display: flex;\r\n        align-items: center;\r\n        flex-wrap: wrap;\r\n\r\n        .fileItem {\r\n            width: 280px;\r\n            height: 50px;\r\n            background: #e8f9f8;\r\n            border-radius: 2px;\r\n            display: flex;\r\n            align-items: center;\r\n            padding: 10px;\r\n            margin-left: 20px;\r\n            cursor: pointer;\r\n\r\n            .fileImg {\r\n                width: 24px;\r\n                height: 28px;\r\n            }\r\n\r\n            .fileName {\r\n                font-family: Source Han Sans CN;\r\n                font-weight: 400;\r\n                font-size: 14px;\r\n                color: #666666;\r\n                margin-left: 15px;\r\n            }\r\n        }\r\n\r\n        .fileItem:nth-child(2n + 1) {\r\n            margin-left: 0;\r\n        }\r\n    }\r\n}\r\n</style>"]}]}