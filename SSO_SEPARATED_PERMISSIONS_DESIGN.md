# SSO分离权限模式设计文档

## 概述

本文档描述了基于分离权限模式的SSO（单点登录）系统设计，该设计允许各个系统独立管理权限，同时通过SSO服务实现统一身份认证。

## 系统架构

### 核心组件

1. **SSO认证服务** (`ruoyi-sso`) - 端口: 9300
   - 负责用户身份认证
   - 颁发和验证访问令牌
   - 管理用户权限映射关系

2. **主系统** (`share-intelligent-backend`) - 端口: 9200
   - 复合材料共享智造平台
   - 使用 `industry` 数据库
   - 客户端ID: `backend`

3. **子系统** (`share-intelligent-market`) - 端口: 8081
   - 智能市场系统
   - 使用 `market` 数据库
   - 客户端ID: `market`

## 分离权限模式特点

### 优势
- **系统独立性强**: 各系统维护自己的权限体系
- **灵活性高**: 可以根据业务需求定制权限规则
- **扩展性好**: 新增系统时不影响现有系统
- **维护简单**: 权限变更只影响对应系统

### 实现方式
- **SSO只负责身份认证**: 验证用户身份，不管理具体权限
- **权限映射机制**: 通过用户映射服务关联SSO用户与各系统权限
- **独立权限管理**: 每个系统根据映射关系独立管理权限

## 技术实现

### SSO认证流程

1. **用户访问系统** → 重定向到SSO登录页面
2. **用户登录** → SSO验证用户身份
3. **颁发授权码** → 重定向回系统回调地址
4. **换取访问令牌** → 系统使用授权码换取访问令牌
5. **获取用户信息** → 包含权限映射信息
6. **本地权限处理** → 根据映射信息设置本地权限

### 权限映射机制

#### 用户映射服务 (`UserMappingService`)
```java
// 获取用户在指定系统中的权限
Map<String, Object> getUserPermissions(String ssoUsername, String clientId);

// 创建用户映射关系
boolean createUserMapping(String ssoUsername, String clientId, 
                         String systemUserId, Map<String, Object> permissions);

// 检查用户权限
boolean hasPermission(String ssoUsername, String clientId);
```

#### 默认权限配置
- **管理员用户 (admin)**:
  - Backend系统: `admin`, `system_manager` 角色
  - Market系统: `admin`, `market_manager` 角色
- **普通用户**:
  - Backend系统: `user` 角色
  - Market系统: `customer` 角色

### 配置说明

#### SSO服务配置 (`bootstrap.yml`)
```yaml
sso:
  clients:
    backend:
      name: 复合材料共享智造平台
      url: http://localhost:9200
      callback-url: http://localhost:9200/sso/callback
      database: industry
      secret: "backend_2024#RuoYi@Share$Key!8888"
    market:
      name: 智能市场系统
      url: http://localhost:8081
      callback-url: http://localhost:8081/sso/callback
      database: market
      secret: "market_2024#RuoYi@Share$Key!9999"
```

#### 客户端配置 (`application-sso.yml`)
```yaml
sso:
  server:
    url: http://localhost:9300
  client:
    id: market  # 或 backend
    secret: "对应的客户端密钥"
    callback-url: "对应的回调地址"
```

## API接口

### SSO认证服务接口

- `GET /sso/login` - SSO登录页面
- `POST /sso/token` - 授权码换取令牌
- `POST /sso/validate` - 验证访问令牌
- `GET /sso/userinfo` - 获取用户信息（含权限映射）
- `POST /sso/logout` - SSO登出
- `GET /sso/status` - 服务状态检查

### 用户映射管理接口

- `GET /sso/user-mapping/permissions` - 获取用户权限
- `POST /sso/user-mapping/create` - 创建用户映射
- `PUT /sso/user-mapping/update` - 更新用户映射
- `DELETE /sso/user-mapping/delete` - 删除用户映射
- `GET /sso/user-mapping/permission-codes/{clientId}` - 获取支持的权限代码

### 客户端集成接口

- `GET /sso/callback` - SSO登录回调
- `GET /sso/login-url` - 获取SSO登录地址
- `POST /sso/logout` - 客户端登出
- `GET /sso/userinfo` - 获取当前用户信息
- `GET /sso/check-permission` - 检查用户权限
- `GET /sso/check-role` - 检查用户角色

## 权限映射示例

### Backend系统权限
```json
{
  "roles": ["admin", "system_manager"],
  "permissionCodes": [
    "system:user:list", "system:user:add", "system:user:edit",
    "system:role:list", "system:role:add", "system:role:edit",
    "system:menu:list", "system:menu:add", "system:menu:edit"
  ]
}
```

### Market系统权限
```json
{
  "roles": ["admin", "market_manager"],
  "permissionCodes": [
    "market:product:list", "market:product:add", "market:product:edit",
    "market:order:list", "market:order:process", "market:order:cancel",
    "market:user:list", "market:user:manage"
  ]
}
```

## 部署说明

### 启动顺序
1. 启动Redis服务
2. 启动Nacos配置中心
3. 启动SSO认证服务 (端口: 9300)
4. 启动Backend系统 (端口: 9200)
5. 启动Market系统 (端口: 8081)

### 配置检查
- 确保各系统的客户端密钥配置正确
- 确保回调地址配置正确
- 确保Redis连接配置正确
- 确保Nacos配置同步正常

## 安全考虑

1. **客户端密钥**: 使用强密钥，定期更换
2. **令牌过期**: 设置合理的令牌过期时间
3. **HTTPS**: 生产环境使用HTTPS协议
4. **权限验证**: 各系统独立验证用户权限
5. **日志审计**: 记录关键操作日志

## 扩展性

### 新增系统集成
1. 在SSO服务中添加新的客户端配置
2. 在新系统中集成SSO客户端代码
3. 配置权限映射关系
4. 测试SSO登录流程

### 权限体系扩展
1. 在用户映射服务中添加新的权限代码
2. 更新默认权限配置
3. 提供权限管理界面
4. 支持动态权限分配

## 故障处理

### 常见问题
1. **SSO服务不可用**: 各系统降级为本地登录
2. **权限映射失败**: 使用默认权限配置
3. **令牌验证失败**: 重新引导用户登录
4. **回调地址错误**: 检查配置文件中的回调地址

### 监控指标
- SSO服务可用性
- 登录成功率
- 令牌验证成功率
- 权限映射成功率
- 系统响应时间

## 总结

分离权限模式的SSO设计实现了身份认证的统一管理和权限管理的独立控制，既保证了用户体验的一致性，又保持了各系统的独立性和灵活性。通过权限映射机制，可以灵活地控制用户在不同系统中的访问权限，满足复杂业务场景的需求。
