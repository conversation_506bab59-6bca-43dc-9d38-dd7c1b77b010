{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\demandHall\\detail.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\demandHall\\detail.vue", "mtime": 1750311962984}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8FA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/purchaseSales/component/demandHall", "sourcesContent": ["<template>\r\n  <div class=\"demand-hall-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"demand-hall-detail-banner\">\r\n      <img\r\n        src=\"../../../../assets/demandHall/demandHallDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"demand-hall-detail-title-box\">\r\n      <div class=\"demand-hall-detail-divider\"></div>\r\n      <div class=\"demand-hall-detail-title\">需求详情</div>\r\n      <div class=\"demand-hall-detail-divider\"></div>\r\n    </div>\r\n    <div v-loading=\"loading\" class=\"demand-hall-detail-content\">\r\n      <div class=\"demand-hall-detail-box\">\r\n        <div class=\"demand-hall-detail-box-title\">\r\n          {{ data.demandTitle }}\r\n        </div>\r\n        <div class=\"demand-hall-detail-headline\">\r\n          <div class=\"headline-content\">\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">应用领域：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.applicationArea }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">需求方：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.companyName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">联系人：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.contactsName }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">联系方式：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.contactsMobile }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-item\">\r\n              <div class=\"item-title\">发布时间：</div>\r\n              <div class=\"item-content\">\r\n                {{ data.createTimeString }}\r\n              </div>\r\n            </div>\r\n            <div class=\"headline-content-btn\">\r\n              <el-button\r\n                v-if=\"showBtn\"\r\n                class=\"headline-btn-style intention-btn\"\r\n                @click=\"goIntention\"\r\n                >我有意向\r\n              </el-button>\r\n              <el-button\r\n                class=\"headline-btn-style communication-btn\"\r\n                @click=\"goChat\"\r\n                icon=\"el-icon-chat-dot-round\"\r\n                >在线沟通</el-button\r\n              >\r\n            </div>\r\n          </div>\r\n          <div class=\"headline-img\">\r\n            <img\r\n              v-if=\"data.scenePicture && data.scenePicture.length > 0\"\r\n              :src=\"data.scenePicture[0].url\"\r\n              alt=\"\"\r\n            />\r\n            <img\r\n              v-else\r\n              src=\"../../../../assets/purchaseSales/demandDefault.png\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div class=\"demand-hall-detail-description\">\r\n          <div class=\"description-title-box\">\r\n            <div class=\"description-divider\"></div>\r\n            <div class=\"description-title\">需求描述</div>\r\n          </div>\r\n          <div class=\"description-content\">\r\n            <div v-html=\"data.summary\" class=\"description-text ql-editor\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getDemandDetail, getCheckSubmit } from \"@/api/purchaseSales\";\r\nimport { getInfo } from \"@/api/login\";\r\nimport { getCompanyInfoByLoginInfo } from \"@/api/apathy\";\r\nimport { mapGetters } from \"vuex\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      data: {},\r\n      showBtn: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      this.loading = true;\r\n      getDemandDetail(this.$route.query.id)\r\n        .then((res) => {\r\n          console.log(res);\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          console.log(res);\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n          this.data.scenePicture = this.data.scenePicture\r\n            ? JSON.parse(this.data.scenePicture)\r\n            : [];\r\n          if (this.data.companyName && this.data.displayRestrictions === 2) {\r\n            if (this.data.companyName.length > 2) {\r\n              this.data.companyName =\r\n                this.data.companyName.substring(0, 2) +\r\n                \"****\" +\r\n                this.data.companyName.slice(6);\r\n            }\r\n          }\r\n          if (!this.token) {\r\n            this.showBtn = true;\r\n          } else {\r\n            this.getInfo();\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 判断此资源是不是自己发布的\r\n    getInfo() {\r\n      getInfo().then((res) => {\r\n        if (this.data.createById === res.user.userId) {\r\n          this.showBtn = false;\r\n        } else {\r\n          this.showBtn = true;\r\n        }\r\n      });\r\n    },\r\n    goChat() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      let routeData = this.$router.resolve({\r\n        path: \"/user/im\",\r\n        query: {\r\n          userId: this.data.createImById,\r\n        },\r\n      });\r\n      window.open(routeData.href, \"_blank\");\r\n    },\r\n    // 跳到我有意向页面\r\n    goIntention() {\r\n      // 判断是否登录\r\n      if (!this.token) {\r\n        this.$confirm(\"请先登录\", \"提示\", {\r\n          confirmButtonText: \"去登录\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\",\r\n        }).then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            location.href = \"/login\";\r\n          });\r\n        });\r\n        return;\r\n      }\r\n      this.loading = true;\r\n      // 是否加入企业\r\n      getCompanyInfoByLoginInfo()\r\n        .then((res) => {\r\n          if (res.data) {\r\n            // 是否对此资源提交过意向\r\n            getCheckSubmit({\r\n              id: this.$route.query.id,\r\n              resourceType: \"resource_demand\",\r\n            })\r\n              .then((res) => {\r\n                this.loading = false;\r\n                // true 提交过  false未提交过\r\n                if (res.data) {\r\n                  this.$message({\r\n                    type: \"warning\",\r\n                    message: \"已经提交过了哦！\",\r\n                  });\r\n                } else {\r\n                  let routeData = this.$router.resolve({\r\n                    path: \"/addIntention\",\r\n                    query: {\r\n                      id: this.$route.query.id,\r\n                      type: \"resource_demand\",\r\n                      title: this.data.demandTitle,\r\n                    },\r\n                  });\r\n                  window.open(routeData.href, \"_blank\");\r\n                }\r\n              })\r\n              .catch(() => {\r\n                this.loading = false;\r\n              });\r\n          } else {\r\n            this.loading = false;\r\n            this.$message({\r\n              type: \"warning\",\r\n              message: \"必须加入企业才可提交我有意向\",\r\n            });\r\n          }\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\"]),\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.demand-hall-detail-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n\r\n  .demand-hall-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .demand-hall-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n\r\n    .demand-hall-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n\r\n    .demand-hall-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n\r\n  .demand-hall-detail-content {\r\n    background: #f4f5f9;\r\n    padding-bottom: 70px;\r\n\r\n    .demand-hall-detail-box {\r\n      width: 1200px;\r\n      background: #fff;\r\n      margin: 0 auto;\r\n      padding: 60px 60px 192px;\r\n\r\n      .demand-hall-detail-box-title {\r\n        width: 100%;\r\n        font-size: 32px;\r\n        font-family: PingFangSC-Semibold, PingFang SC;\r\n        font-weight: 600;\r\n        color: #333;\r\n        line-height: 32px;\r\n        word-wrap: break-word;\r\n      }\r\n\r\n      .demand-hall-detail-headline {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        margin-top: 40px;\r\n        padding-bottom: 40px;\r\n        border-bottom: 1px solid #e8e8e8;\r\n\r\n        .headline-content {\r\n          flex: 1;\r\n\r\n          .headline-content-item {\r\n            display: flex;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            line-height: 32px;\r\n\r\n            .item-title {\r\n              width: 80px;\r\n              color: #666;\r\n            }\r\n\r\n            .item-content {\r\n              flex: 1;\r\n              max-width: 590px;\r\n              color: #333;\r\n              word-wrap: break-word;\r\n            }\r\n          }\r\n\r\n          .headline-content-btn {\r\n            padding-top: 48px;\r\n\r\n            .headline-btn-style {\r\n              width: 100px;\r\n              height: 32px;\r\n              border-radius: 4px;\r\n              font-family: PingFangSC-Regular, PingFang SC;\r\n              padding: 8px 11px;\r\n            }\r\n\r\n            .intention-btn {\r\n              background: #21c9b8;\r\n              color: #fff;\r\n            }\r\n\r\n            .communication-btn {\r\n              border: 1px solid #21c9b8;\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n\r\n        .headline-img {\r\n          width: 400px;\r\n          height: 240px;\r\n          margin-left: 20px;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .demand-hall-detail-description {\r\n      padding-top: 39px;\r\n\r\n      .description-title-box {\r\n        display: flex;\r\n        align-items: center;\r\n        padding-bottom: 40px;\r\n\r\n        .description-divider {\r\n          width: 4px;\r\n          height: 20px;\r\n          background: #21c9b8;\r\n        }\r\n\r\n        .description-title {\r\n          font-size: 24px;\r\n          font-family: PingFangSC-Medium, PingFang SC;\r\n          font-weight: 500;\r\n          color: #333;\r\n          line-height: 24px;\r\n          padding-left: 8px;\r\n        }\r\n      }\r\n\r\n      .description-content {\r\n        width: 1072px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n        word-wrap: break-word;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n<style lang=\"scss\">\r\n.demand-hall-detail-container {\r\n  .description-content {\r\n    .description-text {\r\n      word-break: break-all;\r\n      font-size: 16px;\r\n      line-height: 28px;\r\n      color: #333;\r\n      font-family: PingFangSC-Regular, PingFang SC;\r\n\r\n      img {\r\n        max-width: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}