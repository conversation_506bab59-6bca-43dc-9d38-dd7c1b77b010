{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\enterpriseList\\detail.vue?vue&type=style&index=1&id=47302afe&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\purchaseSales\\component\\enterpriseList\\detail.vue", "mtime": 1750311962986}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5lbnRlcnByaXNlLWRldGFpbC1jb250YWluZXIgew0KICAuZW50ZXJwcmlzZS10aXRsZS1jYXJvdXNlbCB7DQogICAgLmVsLWNhcm91c2VsX19jb250YWluZXIgew0KICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgLmVsLWNhcm91c2VsX19hcnJvdyB7DQogICAgICAgIGhlaWdodDogNDhweDsNCiAgICAgICAgd2lkdGg6IDQ4cHg7DQogICAgICAgIGJhY2tncm91bmQ6ICNmNGY1Zjk7DQogICAgICAgICY6aG92ZXIgew0KICAgICAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIC5lbC1pY29uLWFycm93LWxlZnQsDQogICAgICAuZWwtaWNvbi1hcnJvdy1yaWdodCB7DQogICAgICAgIGZvbnQtc2l6ZTogMjBweDsNCiAgICAgIH0NCiAgICAgIC5lbC1jYXJvdXNlbF9fYXJyb3ctLWxlZnQgew0KICAgICAgICBsZWZ0OiAwOw0KICAgICAgfQ0KICAgICAgLmVsLWNhcm91c2VsX19hcnJvdy0tcmlnaHQgew0KICAgICAgICByaWdodDogMDsNCiAgICAgIH0NCiAgICB9DQogICAgLmVsLWNhcm91c2VsX19pbmRpY2F0b3Igew0KICAgICAgLmVsLWNhcm91c2VsX19idXR0b24gew0KICAgICAgICB3aWR0aDogNHB4Ow0KICAgICAgICBoZWlnaHQ6IDRweDsNCiAgICAgICAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjUpOw0KICAgICAgICBib3JkZXItcmFkaXVzOiAzcHg7DQogICAgICAgICY6aG92ZXIgew0KICAgICAgICAgIHdpZHRoOiAyNHB4Ow0KICAgICAgICAgIGhlaWdodDogNHB4Ow0KICAgICAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogM3B4Ow0KICAgICAgICB9DQogICAgICB9DQogICAgICAuZWwtY2Fyb3VzZWxfX2luZGljYXRvci0taG9yaXpvbnRhbCB7DQogICAgICAgIHBhZGRpbmc6IDEycHggOHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAklBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/purchaseSales/component/enterpriseList", "sourcesContent": ["<!--\r\n * @Author: jhy\r\n * @Date: 2023-02-03 09:26:43\r\n * @LastEditors: JHY\r\n * @LastEditTime: 2023-12-09 21:08:18\r\n-->\r\n<template>\r\n  <div class=\"enterprise-detail-container\">\r\n    <!-- banner图 -->\r\n    <div class=\"enterprise-detail-banner\">\r\n      <img\r\n        src=\"../../../../assets/enterprise/enterpriseDetailBanner.png\"\r\n        alt=\"\"\r\n      />\r\n    </div>\r\n    <div class=\"enterprise-detail-title-box\">\r\n      <div class=\"enterprise-detail-divider\"></div>\r\n      <div class=\"enterprise-detail-title\">机构详情</div>\r\n      <div class=\"enterprise-detail-divider\"></div>\r\n    </div>\r\n    <div class=\"enterprise-detail-content\">\r\n      <div class=\"enterprise-title\">{{ data.name }}</div>\r\n      <div class=\"enterprise-title-carousel\">\r\n        <el-carousel\r\n          v-if=\"data.companyPictureList && data.companyPictureList.length > 0\"\r\n          class=\"carousel-content\"\r\n          arrow=\"always\"\r\n          :interval=\"5000\"\r\n        >\r\n          <el-carousel-item\r\n            v-for=\"(item, index) in data.companyPictureList\"\r\n            :key=\"index\"\r\n            class=\"carousel-item-content\"\r\n          >\r\n            <div class=\"carousel-item-box\">\r\n              <img :src=\"item.url\" alt=\"\" />\r\n            </div>\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n        <img\r\n          v-else\r\n          src=\"../../../../assets/purchaseSales/companyDefault.png\"\r\n          alt=\"\"\r\n          class=\"carousel-default-img\"\r\n        />\r\n      </div>\r\n      <div class=\"enterprise-title-tag\">\r\n        <div\r\n          v-for=\"(item, index) in data.industrialChainValueList\"\r\n          :key=\"index\"\r\n          class=\"title-tag-item\"\r\n        >\r\n          {{ item }}\r\n        </div>\r\n      </div>\r\n      <div class=\"enterprise-title-address\">\r\n        <div class=\"address-content\">\r\n          <img\r\n            src=\"../../../../assets/enterprise/addressIcon.png\"\r\n            alt=\"\"\r\n            class=\"address-content-img\"\r\n          />\r\n          <div class=\"address-content-text\">\r\n            {{ data.address }}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- 企业简介 -->\r\n      <div class=\"enterprise-introduction-box\" v-if=\"this.data.introduce != 0\">\r\n        <div class=\"enterprise-introduction-title\">\r\n          <div class=\"introduction-line\"></div>\r\n          <div class=\"introduction-title\">企业简介</div>\r\n        </div>\r\n        <div class=\"enterprise-introduction-info\">\r\n          {{ data.introduce }}\r\n        </div>\r\n      </div>\r\n      <!-- 企业需求 -->\r\n      <div class=\"enterprise-demand-content\" v-if=\"this.demandTotal != 0\">\r\n        <div class=\"enterprise-introduction-title\">\r\n          <div class=\"introduction-line\"></div>\r\n          <div class=\"introduction-title\">企业需求（{{ demandTotal }}）</div>\r\n        </div>\r\n        <div class=\"enterprise-demand-info\">\r\n          <div\r\n            v-for=\"(item, index) in demandData\"\r\n            :key=\"index\"\r\n            class=\"enterprise-demand-item\"\r\n          >\r\n            <router-link\r\n              :to=\"{ name: 'demandHallDetail', query: { id: item.id } }\"\r\n            >\r\n              <div class=\"item-img\">\r\n                <img\r\n                  v-if=\"item.scenePicture && item.scenePicture.length > 0\"\r\n                  :src=\"item.scenePicture[0].url\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/companyDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <!-- </router-link> -->\r\n              <div class=\"item-content\">\r\n                <div class=\"item-title\">\r\n                  {{ item.demandTitle }}\r\n                </div>\r\n                <div class=\"item-content-tag\">\r\n                  <div\r\n                    v-for=\"(val, num) in item.applicationArea\"\r\n                    :key=\"num\"\r\n                    class=\"item-tag\"\r\n                  >\r\n                    {{ val }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </router-link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- 企业供给 -->\r\n      <div class=\"enterprise-demand-content\" v-if=\"this.supplyTotal != 0\">\r\n        <div class=\"enterprise-introduction-title\">\r\n          <div class=\"introduction-line\"></div>\r\n          <div class=\"introduction-title\">企业供给（{{ supplyTotal }}）</div>\r\n        </div>\r\n        <div class=\"enterprise-demand-info\">\r\n          <div\r\n            v-for=\"(item, index) in supplyData\"\r\n            :key=\"index\"\r\n            class=\"enterprise-demand-item\"\r\n          >\r\n            <router-link\r\n              :to=\"{ name: 'resourceHallDetail', query: { id: item.id } }\"\r\n            >\r\n              <div class=\"item-img\">\r\n                <img\r\n                  v-if=\"item.productPhoto && item.productPhoto.length > 0\"\r\n                  :src=\"item.productPhoto[0].url\"\r\n                  alt=\"\"\r\n                />\r\n                <img\r\n                  v-else\r\n                  src=\"../../../../assets/purchaseSales/resourceDefault.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <!-- </router-link> -->\r\n              <div class=\"item-content\">\r\n                <div class=\"item-title\">\r\n                  {{ item.supplyName }}\r\n                </div>\r\n                <div class=\"item-content-tag\">\r\n                  <div\r\n                    v-for=\"(val, num) in item.applicationArea\"\r\n                    :key=\"num\"\r\n                    class=\"item-tag\"\r\n                  >\r\n                    {{ val }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </router-link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <!-- 企业资料 -->\r\n      <div\r\n        class=\"enterprise-data-content\"\r\n        v-if=\"this.data.companyMaterialList != 0\"\r\n      >\r\n        <div class=\"enterprise-introduction-title introduction-title-data\">\r\n          <div class=\"introduction-line\"></div>\r\n          <div class=\"introduction-title\">企业资料</div>\r\n        </div>\r\n        <div\r\n          v-for=\"(item, index) in data.companyMaterialList\"\r\n          :key=\"index\"\r\n          class=\"introduction-data-info\"\r\n        >\r\n          <div class=\"item-introduction\">\r\n            <div class=\"item-introduction-name\">\r\n              <div class=\"item-introduction-img\">\r\n                <img\r\n                  src=\"../../../../assets/purchaseSales/linkIcon.png\"\r\n                  alt=\"\"\r\n                />\r\n              </div>\r\n              <div class=\"item-introduction-file\">\r\n                {{ item.name }}\r\n              </div>\r\n            </div>\r\n            <div class=\"item-introduction-btn\">\r\n              <el-button @click=\"viewData(item.url)\">立即查看</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"enterprise-detail-btn\">\r\n        <!-- <el-button icon=\"el-icon-chat-dot-round\">在线沟通</el-button> -->\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  getCompanyDetail,\r\n  getDemandList,\r\n  getSupplyList,\r\n} from \"@/api/purchaseSales\";\r\nimport CryptoJS from \"crypto-js\";\r\nlet secretKey = \"9zVn0%bqmUYSGw2n\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      data: {},\r\n      status: true,\r\n      demandData: [],\r\n      demandTotal: 0,\r\n      supplyData: [],\r\n      supplyTotal: 0,\r\n      arr: [],\r\n    };\r\n  },\r\n  created() {\r\n    this.getCompanyDetail();\r\n    this.getDemandList();\r\n    this.getSupplyList();\r\n  },\r\n  methods: {\r\n    // 企业详情\r\n    getCompanyDetail() {\r\n      this.loading = true;\r\n      getCompanyDetail({ id: this.$route.query.id })\r\n        .then((res) => {\r\n          let key = CryptoJS.enc.Utf8.parse(secretKey);\r\n          let decrypt = CryptoJS.AES.decrypt(res, key, {\r\n            mode: CryptoJS.mode.ECB,\r\n            padding: CryptoJS.pad.Pkcs7,\r\n          });\r\n          res = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypt));\r\n          this.loading = false;\r\n          this.data = res.data || {};\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 企业需求\r\n    getDemandList() {\r\n      getDemandList({\r\n        businessNo: this.$route.query.businessNo,\r\n        displayStatus: 1,\r\n        auditStatus: 2,\r\n      }).then((res) => {\r\n        let { rows, total } = res || [];\r\n        this.demandData = rows;\r\n        this.demandData.forEach((item) => {\r\n          item.scenePicture = item.scenePicture\r\n            ? JSON.parse(item.scenePicture)\r\n            : [];\r\n          item.applicationArea = item.applicationArea\r\n            ? item.applicationArea.split(\",\")\r\n            : [];\r\n        });\r\n        this.demandTotal = total;\r\n      });\r\n    },\r\n    // btn11(){\r\n    //   this.$router.push({path:'/demandHallDetail',query:{id:this.demandData.id}})\r\n    // },\r\n    // 企业供给\r\n    getSupplyList() {\r\n      getSupplyList({\r\n        businessNo: this.$route.query.businessNo,\r\n        auditStatus: 2,\r\n        displayStatus: 1,\r\n      }).then((res) => {\r\n        let { rows, total } = res || [];\r\n        this.supplyData = rows;\r\n        this.supplyData.forEach((item) => {\r\n          item.productPhoto = item.productPhoto\r\n            ? JSON.parse(item.productPhoto)\r\n            : [];\r\n          item.applicationArea = item.applicationArea\r\n            ? item.applicationArea.split(\",\")\r\n            : [];\r\n        });\r\n        this.supplyTotal = total;\r\n      });\r\n    },\r\n    // 查看企业资料\r\n    viewData(url) {\r\n      window.open(url);\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.enterprise-detail-container {\r\n  width: 100%;\r\n  background: #f4f5f9;\r\n  padding-bottom: 60px;\r\n  .enterprise-detail-banner {\r\n    width: 100%;\r\n    height: 25.93vh;\r\n    img {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .enterprise-detail-title-box {\r\n    width: 336px;\r\n    margin: 0 auto;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 60px 0 40px;\r\n    .enterprise-detail-title {\r\n      font-size: 40px;\r\n      font-family: PingFangSC-Medium, PingFang SC;\r\n      font-weight: 500;\r\n      color: #333;\r\n      line-height: 40px;\r\n      padding: 0 40px;\r\n    }\r\n    .enterprise-detail-divider {\r\n      width: 48px;\r\n      height: 4px;\r\n      background: #21c9b8;\r\n    }\r\n  }\r\n  .enterprise-detail-content {\r\n    width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 60px 116px 58px;\r\n    background: #fff;\r\n    .enterprise-title {\r\n      max-width: 960px;\r\n      font-size: 32px;\r\n      font-family: PingFangSC-Semibold, PingFang SC;\r\n      font-weight: 600;\r\n      color: #333;\r\n      line-height: 32px;\r\n      text-align: center;\r\n      padding-bottom: 44px;\r\n      word-break: break-all;\r\n    }\r\n    .enterprise-title-carousel {\r\n      width: 720px;\r\n      height: 360px;\r\n      margin: 0 auto;\r\n      .carousel-content {\r\n        width: 100%;\r\n        height: 360px;\r\n        .carousel-item-content {\r\n          width: 100%;\r\n          height: 100%;\r\n          .carousel-item-box {\r\n            margin: 0 auto;\r\n            width: 600px;\r\n            height: 100%;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      .carousel-default-img {\r\n        width: 600px;\r\n        height: 100%;\r\n        margin: 0 auto;\r\n        display: block;\r\n      }\r\n    }\r\n    .enterprise-title-tag {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      width: 600px;\r\n      margin: 0 auto 21px;\r\n      .title-tag-item {\r\n        max-width: 660px;\r\n        background: rgba(197, 37, 33, 0.1);\r\n        border-radius: 4px;\r\n        padding: 6px 12px;\r\n        font-size: 12px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #21c9b8;\r\n        line-height: 12px;\r\n        margin: 16px 16px 0 0;\r\n        word-wrap: break-word;\r\n        text-align: left;\r\n      }\r\n    }\r\n    .enterprise-title-address {\r\n      width: 600px;\r\n      margin: 21px auto 0;\r\n      .address-content {\r\n        display: flex;\r\n        align-items: center;\r\n        .address-content-img {\r\n          width: 12px;\r\n          height: 14px;\r\n        }\r\n        .address-content-text {\r\n          max-width: 600px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #666;\r\n          line-height: 14px;\r\n          padding-left: 6px;\r\n          word-wrap: break-word;\r\n          text-align: left;\r\n        }\r\n      }\r\n    }\r\n    .enterprise-introduction-box {\r\n      width: 960px;\r\n      padding-top: 61px;\r\n      .enterprise-introduction-info {\r\n        width: 960px;\r\n        padding-top: 40px;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        color: #333;\r\n        line-height: 28px;\r\n        word-wrap: break-word;\r\n        text-align: left;\r\n      }\r\n    }\r\n    .enterprise-introduction-title {\r\n      display: flex;\r\n      align-items: center;\r\n      .introduction-line {\r\n        width: 4px;\r\n        height: 20px;\r\n        background: #21c9b8;\r\n      }\r\n      .introduction-title {\r\n        font-size: 24px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #333;\r\n        line-height: 24px;\r\n        padding-left: 8px;\r\n      }\r\n    }\r\n    .introduction-title-data {\r\n      margin-bottom: 40px;\r\n    }\r\n    .enterprise-demand-content {\r\n      width: 960px;\r\n      padding-top: 60px;\r\n      .enterprise-demand-info {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        .enterprise-demand-item {\r\n          width: 222px;\r\n          margin: 40px 18px 0 0;\r\n          background: #f8f9fb;\r\n          .item-img {\r\n            width: 100%;\r\n            height: 160px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          .item-content {\r\n            padding: 16px 16px 14px;\r\n            .item-title {\r\n              width: 190px;\r\n              height: 52px;\r\n              font-size: 18px;\r\n              font-family: PingFangSC-Medium, PingFang SC;\r\n              font-weight: 500;\r\n              color: #333;\r\n              line-height: 26px;\r\n              overflow: hidden;\r\n              display: -webkit-box;\r\n              -webkit-box-orient: vertical;\r\n              -webkit-line-clamp: 2;\r\n              text-overflow: ellipsis;\r\n              word-wrap: break-word;\r\n            }\r\n            .item-content-tag {\r\n              display: flex;\r\n              flex-wrap: wrap;\r\n              .item-tag {\r\n                max-width: 190px;\r\n                padding: 12px;\r\n                font-size: 12px;\r\n                font-family: PingFangSC-Regular, PingFang SC;\r\n                color: #214dc5;\r\n                line-height: 12px;\r\n                background: rgba(33, 77, 197, 0.1);\r\n                border-radius: 4px;\r\n                margin: 12px 12px 0 0;\r\n                word-wrap: break-word;\r\n              }\r\n            }\r\n          }\r\n          &:hover {\r\n            cursor: pointer;\r\n            .item-title {\r\n              color: #21c9b8;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .enterprise-data-content {\r\n      width: 960px;\r\n      margin-top: 60px;\r\n    }\r\n    .introduction-data-info {\r\n      margin-top: 10px;\r\n      .item-introduction {\r\n        display: flex;\r\n        justify-content: space-between;\r\n        align-items: center;\r\n        padding: 13px 13px 16px;\r\n        background: #f7f8fa;\r\n        border-radius: 4px;\r\n        .item-introduction-name {\r\n          display: flex;\r\n          align-items: center;\r\n          width: 850px;\r\n          font-size: 14px;\r\n          font-family: PingFangSC-Regular, PingFang SC;\r\n          color: #333;\r\n          line-height: 14px;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n          overflow: hidden;\r\n          .item-introduction-img {\r\n            width: 26px;\r\n            height: 32px;\r\n            margin-right: 12px;\r\n            img {\r\n              width: 100%;\r\n              height: 100%;\r\n            }\r\n          }\r\n          .item-introduction-file {\r\n            width: 800px;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n          }\r\n        }\r\n        .item-introduction-btn {\r\n          .el-button {\r\n            width: 72px;\r\n            height: 24px;\r\n            border-radius: 16px;\r\n            border: 1px solid #2f76e0;\r\n            font-size: 12px;\r\n            font-family: PingFangSC-Regular, PingFang SC;\r\n            color: #2f76e0;\r\n            padding: 0px 8px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    .enterprise-detail-btn {\r\n      margin-top: 60px;\r\n      text-align: center;\r\n      .el-button {\r\n        width: 163px;\r\n        height: 40px;\r\n        border-radius: 4px;\r\n        border: 1px solid #21c9b8;\r\n        font-size: 16px;\r\n        font-family: PingFangSC-Medium, PingFang SC;\r\n        font-weight: 500;\r\n        color: #21c9b8;\r\n        line-height: 16px;\r\n        &:hover {\r\n          background-color: transparent;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n.enterprise-detail-container {\r\n  .enterprise-title-carousel {\r\n    .el-carousel__container {\r\n      height: 100%;\r\n      .el-carousel__arrow {\r\n        height: 48px;\r\n        width: 48px;\r\n        background: #f4f5f9;\r\n        &:hover {\r\n          background: #21c9b8;\r\n        }\r\n      }\r\n      .el-icon-arrow-left,\r\n      .el-icon-arrow-right {\r\n        font-size: 20px;\r\n      }\r\n      .el-carousel__arrow--left {\r\n        left: 0;\r\n      }\r\n      .el-carousel__arrow--right {\r\n        right: 0;\r\n      }\r\n    }\r\n    .el-carousel__indicator {\r\n      .el-carousel__button {\r\n        width: 4px;\r\n        height: 4px;\r\n        background: rgba(0, 0, 0, 0.5);\r\n        border-radius: 3px;\r\n        &:hover {\r\n          width: 24px;\r\n          height: 4px;\r\n          background: #21c9b8;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n      .el-carousel__indicator--horizontal {\r\n        padding: 12px 8px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}