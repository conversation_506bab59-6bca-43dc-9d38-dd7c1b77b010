<template>
  <!-- 服务供给 -->
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="供给标题" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入供给标题" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="供给类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择供给类型" clearable>
          <el-option v-for="dict in dict.type.supply_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="应用领域" prop="applicationArea">
        <el-select v-model="queryParams.applicationArea" placeholder="请选择应用领域">
          <el-option v-for="dict in applicationFieldList" :key="dict.applicationFieldId"
            :label="dict.applicationFieldName" :value="dict.applicationFieldId"></el-option>
        </el-select>
        <!-- <el-input v-model="queryParams.applicationArea" placeholder="请输入应用领域" clearable 
          @keyup.enter.native="handleQuery" />-->
      </el-form-item>
      <!-- <el-form-item label="匹配需求" prop="matchDemand">
        <el-input v-model="queryParams.matchDemand" placeholder="请输入匹配需求" clearable @keyup.enter.native="handleQuery" />
      </el-form-item> -->
      <el-form-item label="产品阶段" prop="process">
        <el-select v-model="queryParams.process" placeholder="请选择产品阶段" clearable>
          <el-option v-for="dict in dict.type.supply_process" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="合作方式" prop="cooperationType">
        <el-select v-model="queryParams.cooperationType" placeholder="请选择合作方式" clearable>
          <el-option v-for="dict in dict.type.supply_cooperation" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="发布机构" prop="organization">
        <el-input v-model="queryParams.organization" placeholder="请输入发布机构" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发布人" prop="contact">
        <el-input v-model="queryParams.contact" placeholder="请输入发布人" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发布人电话" prop="publisherPhone">
        <el-input v-model="queryParams.publisherPhone" placeholder="请输入发布人电话" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="审核状态" prop="auditStatus">
        <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" clearable>
          <el-option v-for="dict in dict.type.demand_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="显示状态" prop="onShow">
        <el-select v-model="queryParams.onShow" placeholder="请选择显示状态" clearable>
          <el-option v-for="dict in dict.type.demand_show" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['portalconsole:supply:add']">新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['portalconsole:supply:edit']">修改</el-button>
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['portalconsole:supply:remove']">删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['portalconsole:supply:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="supplyList" @selection-change="handleSelectionChange">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="编号" align="center" prop="id" />
      <el-table-column label="供给标题" align="center" prop="title" />
      <el-table-column label="供给类型" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.supply_type" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="应用领域" align="center" prop="applicationAreaName">
      </el-table-column>
      <el-table-column label="服务类别" align="center" prop="technologyCategory">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.technology_category" :value="scope.row.technologyCategory
            ? scope.row.technologyCategory.split(',')
            : []
            " />
        </template>
      </el-table-column>
      <el-table-column label="供给描述" align="center" prop="description" />
      <el-table-column label="匹配需求" align="center" prop="matchDemand" />
      <el-table-column label="产品阶段" align="center" prop="process">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.supply_process" :value="scope.row.process" />
        </template>
      </el-table-column>
      <el-table-column label="合作方式" align="center" prop="cooperationType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.supply_cooperation" :value="scope.row.cooperationType" />
        </template>
      </el-table-column>
      <el-table-column label="发布机构" align="center" prop="organization" />
      <!-- <el-table-column label="产品图片" align="center" prop="imageUrl" />
      <el-table-column label="附件" align="center" prop="attachment" /> -->
      <el-table-column label="发布人" align="center" prop="contact" />
      <el-table-column label="发布人电话" align="center" prop="phone" width="180" />
      <el-table-column label="对接人" align="center" prop="platformContact" />
      <el-table-column label="审核状态" align="center" prop="auditStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.demand_status" :value="scope.row.auditStatus" />
        </template>
      </el-table-column>
      <el-table-column label="推荐状态" align="center" prop="recommend">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.recommend_status" :value="scope.row.recommend" />
        </template>
      </el-table-column>
      <el-table-column label="显示状态" align="center" prop="onShow">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.demand_show" :value="scope.row.onShow" />
        </template>
      </el-table-column>
      <!-- <el-table-column label="创建人" align="center" prop="createdBy" /> -->
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <!--<el-table-column label="更新人" align="center" prop="updatedBy" />-->
      <el-table-column label="更新时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="备注" align="center" prop="remark" />  -->
      <el-table-column label="操作" align="center" class-name="small-padding" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['portalconsole:supply:edit']">修改</el-button>
          <!-- <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)">
              <span class="el-dropdown-link">
                <i class="el-icon-d-arrow-right el-icon--right"></i>更多
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="handleRec">{{scope.row.rec==1?'取消推荐':'设为推荐'}}</el-dropdown-item>
                <el-dropdown-item command="handleCharge">
                  设置供给方
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown> -->
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['portalconsole:supply:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改服务供给对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="70%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="供给标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入供给标题" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="供给类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择供给类型" @change="handleTypeChange">
                <el-option v-for="dict in dict.type.supply_type" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品阶段" prop="process">
              <el-select v-model="form.process" placeholder="请选择产品阶段">
                <el-option v-for="dict in dict.type.supply_process" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="合作方式" prop="cooperationType">
              <el-select v-model="form.cooperationType" placeholder="请选择合作方式">
                <el-option v-for="dict in dict.type.supply_cooperation" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="应用领域" prop="applicationAreas">
              <el-select v-model="applicationAreas" placeholder="请选择应用领域" multiple>
                <el-option v-for="dict in applicationFieldList" :key="dict.applicationFieldId"
                  :label="dict.applicationFieldName" :value="dict.applicationFieldId"></el-option>
              </el-select>
              <!-- <el-input v-model="form.applicationArea" placeholder="请输入应用领域" /> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="服务类别" prop="technologyCategory" v-if="form.type == '1'">
          <el-checkbox-group v-model="form.technologyCategory">
            <el-checkbox v-for="dict in dict.type.technology_category" :key="dict.value" :label="dict.value">
              {{ dict.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="产品类别" prop="productType" v-if="form.type == '2'">
          <el-checkbox-group v-model="form.productType">
            <el-checkbox v-for="dict in dict.type.product_category" :key="dict.value" :label="dict.value">
              {{ dict.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="供给描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入供给描述" />
        </el-form-item>
        <!-- <el-form-item label="匹配需求" prop="matchDemand">
          <el-input v-model="form.matchDemand" placeholder="请输入匹配需求" />
        </el-form-item> -->

        <el-form-item label="发布机构" prop="organization">
          <el-input v-model="form.organization" placeholder="请输入发布机构" />
        </el-form-item>
        <el-divider content-position="left"></el-divider>
        <el-form-item label="产品图片" prop="imageUrl">
          <!-- <el-upload class="avatar-uploader" action="" :show-file-list="false" :http-request="uploadFun"
            :before-upload="beforeAvatarUpload">
            <el-image v-if="form.imageUrl" :src="form.imageUrl" class="avatar">
              <div slot="error" class="image-slot">
                <span>暂无图片</span>
              </div>
            </el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload> -->
          <ImageUpload v-model="form.imageUrl" :limit="1" :multiple="false" />
        </el-form-item>
        <el-form-item label="附件" prop="attachment">
          <!-- <el-input v-model="form.attachment" type="textarea" placeholder="请输入内容" /> -->
          <el-upload class="upload-demo" drag :file-list="fileAddList" :limit="10" action="" :on-preview="handlePreview"
            :on-remove="handleRemove" :before-remove="beforeRemove" :http-request="uploadFile">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
          </el-upload>
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="发布人" prop="contact">
              <el-input v-model="form.contact" placeholder="请输入发布人" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发布人电话" prop="phone">
              <el-input v-model="form.phone" placeholder="请输入发布人电话" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="平台对接人" prop="platformContact">
              <el-input v-model="form.platformContact" placeholder="请输入平台对接人" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="6">
            <el-form-item label="创建人" prop="createdBy">
              <el-input v-model="form.createdBy" placeholder="请输入创建人" />
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row :span="24">
          <el-col :span="8">
            <el-form-item label="审核状态" prop="auditStatus">
              <el-select v-model="form.auditStatus" placeholder="请选择审核状态">
                <el-option v-for="dict in dict.type.demand_status" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="显示状态" prop="onShow">
              <el-select v-model="form.onShow" placeholder="请选择显示状态" :disabled="true">
                <el-option v-for="dict in dict.type.demand_show" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="推荐状态" prop="recommend">
              <el-select v-model="form.recommend" placeholder="请选择推荐状态">
                <el-option v-for="dict in dict.type.recommend_status" :key="dict.value" :label="dict.label"
                  :value="parseInt(dict.value)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="创建时间" prop="createdTime">
          <el-date-picker clearable v-model="form.createdTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择创建时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="更新人" prop="updatedBy">
          <el-input v-model="form.updatedBy" placeholder="请输入更新人" />
        </el-form-item>
        <el-form-item label="更新时间" prop="updatedTime">
          <el-date-picker clearable v-model="form.updatedTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择更新时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listSupply,
  getSupply,
  delSupply,
  addSupply,
  updateSupply,
} from "@/api/portalconsole/supply";
import { comUpload } from "@/api/portalconsole/uploadApi";
import { listApplicationField } from "@/api/portalconsole/applicationField";

export default {
  name: "Supply",
  dicts: [
    "technology_category",
    "product_category",
    "supply_cooperation",
    "supply_type",
    "recommend_status",
    "demand_show",
    "demand_status",
    "supply_process",
    "application_area",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 服务供给表格数据
      supplyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        type: null,
        applicationArea: null,
        technologyCategory: null,
        productType: null,
        matchDemand: null,
        process: null,
        cooperationType: null,
        organization: null,
        contact: null,
        phone: null,
        auditStatus: null,
        onShow: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "供给标题不能为空", trigger: "blur" },
        ],
        process: [
          { required: true, message: "产品阶段不能为空", trigger: "change" },
        ],
        cooperationType: [
          { required: true, message: "合作方式不能为空", trigger: "change" },
        ],
        description: [
          { required: true, message: "供给描述不能为空", trigger: "blur" },
        ],
      },
      fileAddList: [],
      applicationAreas: [],
      applicationFieldList: [],
    };
  },
  created() {
    this.getList();
    this.getapp();
  },
  methods: {
    /** 查询服务供给列表 */
    getList() {
      this.loading = true;
      listSupply(this.queryParams).then((response) => {
        this.supplyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.fileAddList = [];
      this.form = {
        id: null,
        title: null,
        type: null,
        applicationArea: null,
        technologyCategory: [],
        productType: [],
        description: null,
        matchDemand: null,
        process: null,
        cooperationType: null,
        organization: null,
        imageUrl: null,
        attachment: null,
        contact: null,
        publisherPhone: null,
        platformContact: null,
        auditStatus: null,
        recommend: null,
        onShow: 0,
        remark: null,
      };
      this.applicationAreas = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加服务供给";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      this.getapp();
      getSupply(id).then((response) => {
        this.form = response.data;
        // this.applicationAreas = response.data.applicationArea;
        // this.applicationAreas = this.applicationAreas.split(",");
        // this.applicationAreas = this.applicationAreas.map((item) =>
        //   Number(item)
        // );
        // console.log("this.applicationAreas1", this.applicationAreas);
        if (this.form.technologyCategory) {
          this.form.technologyCategory = this.form.technologyCategory.split(",");
        } else {
          this.form.technologyCategory = [];
        }
        if (this.form.productType) {
          this.form.productType = this.form.productType.split(",");
        } else {
          this.form.productType = [];
        }
        if (this.form.attachment) {
          this.fileAddList = JSON.parse(this.form.attachment);
        }
        this.open = true;
        this.title = "修改服务供给";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          console.log("this.form", this.form);
          this.form.technologyCategory = this.form.technologyCategory.join(",");
          this.form.productType = this.form.productType.join(",");
          if (this.applicationAreas.length > 0) {
            this.form.applicationArea = this.applicationAreas.join(",");
          }
          if (this.form.id != null) {
            updateSupply(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addSupply(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除服务供给编号为"' + ids + '"的数据项？')
        .then(function () {
          return delSupply(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "portalconsole/supply/export",
        {
          ...this.queryParams,
        },
        `supply_${new Date().getTime()}.xlsx`
      );
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleRec":
          this.handleRec(row);
          break;
        case "handleCharge":
          this.handleCharge(row);
          break;
        default:
          break;
      }
    },
    //设为推荐
    handleRec(row) {
      // recommend({
      //   productId: row.id,
      //   recommend: row.rec == 1 ? 0 : 1,
      //   module: "fruit"
      // }).then((response) => {
      //   recRes({
      //      goodsId: row.id,
      //      rec: row.rec == 1 ? 0 : 1,
      //      module: "fruit"
      //   }).then((resp) => {})
      //   row.rec = row.rec==1 ? 0 : 1
      //   this.msgSuccess("设置成功");
      // }).catch((err) => {
      //   this.msgError("设置失败");
      // });
    },
    //设置供给方
    handleCharge(row) {
      this.chargerTel = row.charger;
      this.requireId = row.id;
      this.chargemodal = true;
      this.chargeloading = true;
      //this.getCustomer();
    },
    // 图片上传
    uploadFun(params) {
      const file = params.file;
      let form = new FormData();
      form.append("file", file); // 文件对象
      comUpload(form).then((res) => {
        let data = res.data;
        //this.$set(this.form,'imageUrlPath',data.fileId)  // 图片全路径
        this.$set(this.form, "imageUrl", data.fileFullPath); // 图片Id
      });
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg" || "image/png" || "image/jpg";
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isJPG) {
        this.$message.error("上传图片只能是 jpg、jpeg、png 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 2MB!");
      }
      return isJPG && isLt2M;
    },
    //附件上传
    handleRemove(file, fileList) {
      this.fileAddList.splice(
        this.fileAddList.findIndex((item) => item.id == file.id),
        1
      );
      this.$set(this.form, "attachment", JSON.stringify(this.fileAddList)); // 图片全路径
    },
    handlePreview(file) {
      console.log(file);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    uploadFile(params) {
      const file = params.file;
      let form = new FormData();
      form.append("file", file); // 文件对象
      comUpload(form).then((res) => {
        let data = res.data;
        console.log("data", data);
        this.fileAddList.push({
          name: data.fileName,
          id: data.fileId,
          fileUrl: data.fileFullPath,
        });
        this.$set(this.form, "attachment", JSON.stringify(this.fileAddList)); // 图片全路径
      });
    },
    //应用领域
    getapp() {
      listApplicationField().then((response) => {
        this.applicationFieldList = response.rows;
      });
    },
    handleTypeChange(value) {
      if (value == '1') {
        this.form.productType = []
      } else if (value == '2') {
        this.form.technologyCategory = []
      }
    }
  },
};
</script>
<style scoped>
>>>.el-table .el-dropdown-link {
  cursor: pointer;
  color: #409eff;
  margin-left: 5px;
}

.avatar-uploader>>>.el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

/deep/.el-table .cell {
  height: 26px;
}
</style>
