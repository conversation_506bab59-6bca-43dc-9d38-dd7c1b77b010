{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Navbar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\layout\\components\\Navbar.vue", "mtime": 1750311962846}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vuex", "require", "_logo", "_interopRequireDefault", "_Breadcrumb", "_TopNav", "_<PERSON>er", "_Screenfull", "_SizeSelect", "_HeaderSearch", "_Git", "_Doc", "_auth", "components", "Breadcrumb", "TopNav", "<PERSON><PERSON>", "Screenfull", "SizeSelect", "Search", "RuoYiGit", "RuoYiDoc", "computed", "_objectSpread2", "default", "mapGetters", "setting", "get", "$store", "state", "settings", "showSettings", "set", "val", "dispatch", "key", "value", "data", "logoImg", "mobile", "type", "url", "text", "wwk", "ifale", "base64EncodeChars", "created", "userinfo", "JSON", "parse", "sessionStorage", "getItem", "commit", "memberRealName", "avatar", "methods", "login", "$router", "push", "add", "localStorage", "username", "$Base64", "encode", "window", "open", "concat", "toggleSideBar", "logout", "_this", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "w", "_context", "n", "$confirm", "confirmButtonText", "cancelButtonText", "then", "getTicket", "location", "href", "removeTicket", "removeItem", "catch", "a", "goIndex", "path"], "sources": ["src/layout/components/Navbar.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- <div>您好，欢迎来到复合材料共享智造工业互联网平台!</div> -->\r\n    <div class=\"navbar\">\r\n      <div class=\"left-menu\" @click=\"goIndex\">\r\n        <div>\r\n          <img :src=\"logoImg\" class=\"navbar-logo\" />\r\n        </div>\r\n        <div class=\"platTitle\">易复材共享智造<br />工业互联网平台</div>\r\n      </div>\r\n      <div\r\n        class=\"navbar-body\"\r\n        style=\"display: flex; justify-content: space-around\"\r\n      >\r\n        <top-nav id=\"topmenu-container\" class=\"topmenu-container\" />\r\n        <!-- <div style=\"display: flex; text-align: right\">\r\n        <div class=\"chengyang\">\r\n          <div style=\"margin-top: 10px\">\r\n            <img\r\n              style=\"width: 50px; height: 50px\"\r\n              src=\"../../assets/images/chengyang.jpg\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div style=\"font-size: 12px; color: #ccc\">扫码进入小程序</div>\r\n          <div class=\"chengyangBlock\">\r\n            <img src=\"../../assets/images/chengyang.jpg\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"yunduanyanfa\">\r\n          <div style=\"margin-top: 10px\">\r\n            <img\r\n              style=\"width: 50px; height: 50px\"\r\n              src=\"../../assets/images/yunduanyanfa.jpg\"\r\n              alt=\"\"\r\n            />\r\n          </div>\r\n          <div style=\"font-size: 12px; color: #ccc\">扫码进入企业端</div>\r\n          <div class=\"yunduanyanfaBlock\">\r\n            <img src=\"../../assets/images/yunduanyanfa.jpg\" alt=\"\" />\r\n          </div>\r\n        </div>\r\n      </div> -->\r\n      </div>\r\n      <div class=\"right-menu\">\r\n        <!-- <div class=\"suppot\" @click=\"ifale = !ifale\" v-if=\"token\">技术支持</div> -->\r\n        <!-- v-if=\"token\" -->\r\n        <template v-if=\"token\">\r\n          <el-dropdown\r\n            class=\"avatar-container right-menu-item hover-effect\"\r\n            trigger=\"click\"\r\n          >\r\n            <div class=\"avatar-wrapper\">\r\n              <span class=\"name\">{{ name }}</span>\r\n              <el-image\r\n                class=\"userAvatar\"\r\n                icon=\"el-icon-user-solid\"\r\n                :size=\"36\"\r\n                :src=\"avatar ? avatar : require('@/assets/images/avatar.png')\"\r\n              ></el-image>\r\n              <i class=\"el-icon-arrow-down\" />\r\n            </div>\r\n\r\n            <el-dropdown-menu slot=\"dropdown\">\r\n              <router-link to=\"/user/profile\">\r\n                <el-dropdown-item>个人中心</el-dropdown-item>\r\n              </router-link>\r\n              <el-dropdown-item divided @click.native=\"logout\">\r\n                <span>退出登录</span>\r\n              </el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </el-dropdown>\r\n          <!-- <div class=\"cont\" v-show=\"ifale\">\r\n          <p class=\"sum\">13843272866</p>\r\n          <p class=\"sum\">17685863516</p>\r\n          <p class=\"question\">技术问题请拨打</p>\r\n        </div> -->\r\n        </template>\r\n\r\n        <template v-else>\r\n          <div class=\"login-container\">\r\n            <el-button size=\"small\" type=\"primary\" @click=\"login()\" class=\"dl\"\r\n              >登录</el-button\r\n            >\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { mapGetters } from \"vuex\";\r\nimport logoImg from \"@/assets/images/home/<USER>\";\r\nimport Breadcrumb from \"@/components/Breadcrumb\";\r\nimport TopNav from \"@/components/TopNav\";\r\nimport Hamburger from \"@/components/Hamburger\";\r\nimport Screenfull from \"@/components/Screenfull\";\r\nimport SizeSelect from \"@/components/SizeSelect\";\r\nimport Search from \"@/components/HeaderSearch\";\r\nimport RuoYiGit from \"@/components/RuoYi/Git\";\r\nimport RuoYiDoc from \"@/components/RuoYi/Doc\";\r\nimport { getTicket, removeTicket } from \"@/utils/auth\";\r\n\r\nexport default {\r\n  components: {\r\n    Breadcrumb,\r\n    TopNav,\r\n    Hamburger,\r\n    Screenfull,\r\n    SizeSelect,\r\n    Search,\r\n    RuoYiGit,\r\n    RuoYiDoc,\r\n  },\r\n  computed: {\r\n    ...mapGetters([\"token\", \"sidebar\", \"avatar\", \"name\", \"device\"]),\r\n    setting: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings;\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch(\"settings/changeSetting\", {\r\n          key: \"showSettings\",\r\n          value: val,\r\n        });\r\n      },\r\n    },\r\n  },\r\n  data() {\r\n    return {\r\n      logoImg,\r\n      mobile: \"\",\r\n      key: \"QmRlODJTVGhkNg==\",\r\n      type: \"cG9saWN5Y2FzaA==\",\r\n      url: \"\",\r\n      text: {},\r\n      wwk: {},\r\n      // visible: false,\r\n      ifale: false,\r\n      base64EncodeChars:\r\n        \"ABCDEFGHIJKLMNOPORSTUWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\",\r\n    };\r\n  },\r\n  created() {\r\n    let userinfo = JSON.parse(sessionStorage.getItem(\"userinfo\"));\r\n    if (userinfo) {\r\n      this.$store.commit(\"SET_NAME\", userinfo.memberRealName);\r\n      this.$store.commit(\"SET_AVATAR\", userinfo.avatar);\r\n    }\r\n  },\r\n  methods: {\r\n    login() {\r\n      this.$router.push(\"/login\");\r\n    },\r\n    add() {\r\n      if (JSON.parse(localStorage.getItem(\"sessionObj\"))) {\r\n        this.text = JSON.parse(localStorage.getItem(\"sessionObj\"));\r\n        this.wwk = JSON.parse(this.text.data);\r\n        this.mobile = this.wwk.username;\r\n        this.mobile = this.$Base64.encode(this.mobile);\r\n        window.open(\r\n          `https://cyqyfw.com/index/user/login?key=${this.key}&type=${this.type}&mobile=${this.mobile}`\r\n        );\r\n      } else {\r\n        // window.open(\"https://120.221.94.235\");\r\n        window.open(\"https://cyqyfw.com \");\r\n      }\r\n    },\r\n    toggleSideBar() {\r\n      this.$store.dispatch(\"app/toggleSideBar\");\r\n    },\r\n    async logout() {\r\n      this.$confirm(\"确定注销并退出系统吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\",\r\n      })\r\n        .then(() => {\r\n          this.$store.dispatch(\"LogOut\").then(() => {\r\n            if (getTicket()) {\r\n              window.location.href =\r\n                \"https://qyzhfw.chengyang.gov.cn/sso/logout?redirectUrl=https://qyfw.chengyang.gov.cn/index\";\r\n              removeTicket();\r\n            } else {\r\n              location.href = \"/index\";\r\n              localStorage.removeItem(\"sessionObj\");\r\n            }\r\n          });\r\n          sessionStorage.removeItem(\"userinfo\");\r\n        })\r\n        .catch(() => {});\r\n    },\r\n    goIndex() {\r\n      this.$router.push({\r\n        path: \"/index\",\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.cont {\r\n  width: 180px;\r\n  height: 140px;\r\n  text-align: center;\r\n  background-color: #fff;\r\n  border: 1px solid #bbb;\r\n  position: fixed;\r\n  top: 60px;\r\n  right: 80px;\r\n\r\n  .sum {\r\n    font-size: 20px;\r\n  }\r\n\r\n  .question {\r\n    color: rgba(0, 21, 41, 0.678);\r\n  }\r\n}\r\n\r\n.navbar {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  height: 80px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: #fff;\r\n  box-shadow: 0 1px 4px 0 rgba(0, 21, 41, 0.12);\r\n  background: rgb(197, 241, 236);\r\n  background: linear-gradient(\r\n    180deg,\r\n    rgba(197, 241, 236, 1) 34%,\r\n    rgba(245, 255, 254, 1) 99%\r\n  );\r\n  &-body {\r\n    min-width: 1030px;\r\n    // min-width: 1200px;\r\n    // min-width: 66.6%;\r\n    margin-left: auto;\r\n    margin-right: auto;\r\n\r\n    .wgdx {\r\n      position: fixed;\r\n      right: 46.5%;\r\n      top: 27.5px;\r\n    }\r\n\r\n    .wgdx:hover {\r\n      color: #c52622;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .errLog-container {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .left-menu {\r\n    display: flex;\r\n    justify-content: center;\r\n    // width: 200px;\r\n    align-items: center;\r\n    margin-left: 20px;\r\n    cursor: pointer;\r\n\r\n    .platTitle {\r\n      width: 128px;\r\n      height: 36px;\r\n      font-family: Source Han Sans CN;\r\n      font-weight: 500;\r\n      font-size: 16px;\r\n      color: #111111;\r\n      line-height: 20px;\r\n      margin-left: 10px;\r\n    }\r\n\r\n    .navbar-logo {\r\n      width: 63px;\r\n      height: 40px;\r\n      object-fit: contain;\r\n    }\r\n  }\r\n\r\n  .right-menu {\r\n    width: 220px;\r\n    height: 100%;\r\n    padding-right: 32px;\r\n    display: flex;\r\n    justify-content: right;\r\n\r\n    .suppot {\r\n      width: 80px;\r\n      height: 40px;\r\n      margin-top: 28px;\r\n      color: #21c9b8;\r\n      border: none;\r\n      background-color: #fff;\r\n    }\r\n\r\n    .suppot:hover {\r\n      cursor: pointer;\r\n    }\r\n\r\n    // .wgdx {\r\n    //   position: fixed;\r\n    //   right: 46.5%;\r\n    //   top: 27.5px;\r\n    // }\r\n    // .wgdx:hover {\r\n    //   color: #c52622;\r\n    //   cursor: pointer;\r\n    // }\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n\r\n    .right-menu-item {\r\n      display: inline-block;\r\n      padding: 0 8px;\r\n      height: 100%;\r\n      font-size: 18px;\r\n      color: #5a5e66;\r\n      vertical-align: text-bottom;\r\n\r\n      &.hover-effect {\r\n        cursor: pointer;\r\n        transition: background 0.3s;\r\n\r\n        &:hover {\r\n          background: rgba(0, 0, 0, 0.025);\r\n        }\r\n      }\r\n    }\r\n\r\n    .avatar-container {\r\n      display: flex;\r\n      align-items: center;\r\n      flex-direction: row;\r\n      position: relative;\r\n\r\n      .avatar-wrapper {\r\n        display: flex;\r\n        align-items: center;\r\n        flex-direction: row;\r\n\r\n        .userAvatar {\r\n          width: 36px;\r\n          height: 36px;\r\n          border-radius: 50%;\r\n        }\r\n\r\n        .name {\r\n          font-size: 14px;\r\n          font-weight: 500;\r\n          color: #333333;\r\n          line-height: 14px;\r\n          margin-right: 12px;\r\n        }\r\n\r\n        .el-icon-arrow-down {\r\n          cursor: pointer;\r\n          font-size: 14px;\r\n          color: #999999;\r\n          font-weight: bold;\r\n          margin-left: 8px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .login-container {\r\n      .technology {\r\n        color: #21c9b8;\r\n        border: none;\r\n        background-color: #fff;\r\n        width: 120px;\r\n      }\r\n\r\n      .technology:hover {\r\n        cursor: pointer;\r\n      }\r\n\r\n      display: flex;\r\n      // width: 240px;\r\n      height: 80px;\r\n      line-height: 80px;\r\n      align-items: center;\r\n      position: relative;\r\n    }\r\n  }\r\n}\r\n\r\n.chengyang {\r\n  width: 100px;\r\n  height: 83px;\r\n  text-align: center;\r\n}\r\n\r\n.yunduanyanfa {\r\n  width: 100px;\r\n  height: 83px;\r\n  text-align: center;\r\n  margin-left: 20px;\r\n}\r\n\r\n.chengyangBlock {\r\n  position: fixed;\r\n  top: 80px;\r\n  right: calc((100% - 1200px) / 2);\r\n  margin-right: 5%;\r\n  display: none;\r\n}\r\n\r\n.chengyang:hover {\r\n  .chengyangBlock {\r\n    display: block;\r\n  }\r\n}\r\n\r\n.yunduanyanfaBlock {\r\n  position: fixed;\r\n  top: 80px;\r\n  right: calc((100% - 1200px) / 2);\r\n  margin-right: 1%;\r\n  display: none;\r\n}\r\n\r\n.yunduanyanfa:hover {\r\n  .yunduanyanfaBlock {\r\n    display: block;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;AA4FA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,WAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,OAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,UAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,WAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,WAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,aAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,IAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,IAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,KAAA,GAAAX,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAY,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,MAAA,EAAAA,qBAAA;IACAC,QAAA,EAAAA,YAAA;IACAC,QAAA,EAAAA;EACA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,gBAAA;IACAC,OAAA;MACAC,GAAA,WAAAA,IAAA;QACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,YAAA;MACA;MACAC,GAAA,WAAAA,IAAAC,GAAA;QACA,KAAAL,MAAA,CAAAM,QAAA;UACAC,GAAA;UACAC,KAAA,EAAAH;QACA;MACA;IACA;EAAA,EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA,EAAAA,aAAA;MACAC,MAAA;MACAJ,GAAA;MACAK,IAAA;MACAC,GAAA;MACAC,IAAA;MACAC,GAAA;MACA;MACAC,KAAA;MACAC,iBAAA,EACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,QAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAC,cAAA,CAAAC,OAAA;IACA,IAAAJ,QAAA;MACA,KAAAnB,MAAA,CAAAwB,MAAA,aAAAL,QAAA,CAAAM,cAAA;MACA,KAAAzB,MAAA,CAAAwB,MAAA,eAAAL,QAAA,CAAAO,MAAA;IACA;EACA;EACAC,OAAA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;IACA;IACAC,GAAA,WAAAA,IAAA;MACA,IAAAX,IAAA,CAAAC,KAAA,CAAAW,YAAA,CAAAT,OAAA;QACA,KAAAT,IAAA,GAAAM,IAAA,CAAAC,KAAA,CAAAW,YAAA,CAAAT,OAAA;QACA,KAAAR,GAAA,GAAAK,IAAA,CAAAC,KAAA,MAAAP,IAAA,CAAAL,IAAA;QACA,KAAAE,MAAA,QAAAI,GAAA,CAAAkB,QAAA;QACA,KAAAtB,MAAA,QAAAuB,OAAA,CAAAC,MAAA,MAAAxB,MAAA;QACAyB,MAAA,CAAAC,IAAA,4CAAAC,MAAA,CACA,KAAA/B,GAAA,YAAA+B,MAAA,MAAA1B,IAAA,cAAA0B,MAAA,MAAA3B,MAAA,CACA;MACA;QACA;QACAyB,MAAA,CAAAC,IAAA;MACA;IACA;IACAE,aAAA,WAAAA,cAAA;MACA,KAAAvC,MAAA,CAAAM,QAAA;IACA;IACAkC,MAAA,WAAAA,OAAA;MAAA,IAAAC,KAAA;MAAA,WAAAC,kBAAA,CAAA9C,OAAA,mBAAA+C,aAAA,CAAA/C,OAAA,IAAAgD,CAAA,UAAAC,QAAA;QAAA,WAAAF,aAAA,CAAA/C,OAAA,IAAAkD,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cACAP,KAAA,CAAAQ,QAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACAvC,IAAA;cACA,GACAwC,IAAA;gBACAX,KAAA,CAAAzC,MAAA,CAAAM,QAAA,WAAA8C,IAAA;kBACA,QAAAC,eAAA;oBACAjB,MAAA,CAAAkB,QAAA,CAAAC,IAAA,GACA;oBACA,IAAAC,kBAAA;kBACA;oBACAF,QAAA,CAAAC,IAAA;oBACAvB,YAAA,CAAAyB,UAAA;kBACA;gBACA;gBACAnC,cAAA,CAAAmC,UAAA;cACA,GACAC,KAAA;YAAA;cAAA,OAAAX,QAAA,CAAAY,CAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IACA;IACAe,OAAA,WAAAA,QAAA;MACA,KAAA/B,OAAA,CAAAC,IAAA;QACA+B,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}