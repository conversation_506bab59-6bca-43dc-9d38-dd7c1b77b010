{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\approveSetting\\index.vue?vue&type=style&index=0&id=4d695104&lang=scss", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\system\\user\\approveSetting\\index.vue", "mtime": 1750311963043}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1750313272774}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1750313277511}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1750313274296}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1750313271696}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouYXBwLWNvbnRhaW5lciB7DQogIGJhY2tncm91bmQ6ICNmNGY1Zjk7DQogIC5hcHByb3ZlLXNldHRpbmctY29udGFpbmVyIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgICBiYWNrZ3JvdW5kOiAjZmZmOw0KICAgIG1pbi1oZWlnaHQ6IDcwMHB4Ow0KICAgIHBhZGRpbmc6IDIwcHg7DQogICAgLmVsLXNlbGVjdCB7DQogICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICB9DQogICAgLmVsLWJ1dHRvbi0tZGFuZ2VyIHsNCiAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICBjb2xvcjogI2ZmZjsNCiAgICAgIGJvcmRlci1jb2xvcjogIzIxYzliODsNCiAgICB9DQogICAgLmhlYWRlci1zbWFsbCB7DQogICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgZm9udC1zaXplOiAyMHB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IDUwMDsNCiAgICAgIGNvbG9yOiAjMzMzMzMzOw0KICAgICAgbGluZS1oZWlnaHQ6IDIwcHg7DQoNCiAgICAgIC5yZWQtdGFnIHsNCiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMnB4Ow0KICAgICAgICB3aWR0aDogM3B4Ow0KICAgICAgICBoZWlnaHQ6IDIycHg7DQogICAgICAgIGJhY2tncm91bmQ6ICMyMWM5Yjg7DQogICAgICB9DQogICAgfQ0KICAgIC5hcGF0aHktdXBsb2FkLWJ0biB7DQogICAgICBtYXJnaW4tcmlnaHQ6IDIwcHg7DQogICAgfQ0KICAgIC5idXR0b24tY29udGFpbmVyIHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgbWFyZ2luLXRvcDogNTBweDsNCiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgICB9DQoNCiAgICAuZWwtdGFic19fbmF2IHsNCiAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgaGVpZ2h0OiA0MHB4Ow0KICAgICAgcGFkZGluZzogMCA0MyU7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgLy8ganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIH0NCg0KICAgIC5lbC10YWJzX19uYXYtd3JhcDo6YWZ0ZXIgew0KICAgICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7DQogICAgfQ0KICAgIC5lbC10YWJzX19hY3RpdmUtYmFyIHsNCiAgICAgIG1hcmdpbi1sZWZ0OiA0MyU7DQogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjFjOWI4Ow0KICAgIH0NCiAgICAuZWwtdGFic19faXRlbS5pcy1hY3RpdmUgew0KICAgICAgY29sb3I6ICMyMWM5Yjg7DQogICAgfQ0KICAgIC5lbC10YWJzX19pdGVtOmhvdmVyIHsNCiAgICAgIGNvbG9yOiAjMjFjOWI4Ow0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIH0NCiAgfQ0KICAucGljLWNsYXNzIC5oaWRlIC5lbC11cGxvYWQtLXBpY3R1cmUtY2FyZCB7DQogICAgZGlzcGxheTogbm9uZTsNCiAgfQ0KICAuZWwtcGFnaW5hdGlvbiB7DQogICAgd2lkdGg6IDEwMCU7DQogICAgbWFyZ2luLXRvcDogMjBweDsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIH0NCiAgLmVsLXBhZ2luYXRpb24uaXMtYmFja2dyb3VuZCAuZWwtcGFnZXIgbGkgew0KICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7DQogIH0NCiAgLmVsLXBhZ2luYXRpb24uaXMtYmFja2dyb3VuZCAuZWwtcGFnZXIgbGk6bm90KC5kaXNhYmxlZCkuYWN0aXZlIHsNCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjFjOWI4Ow0KICAgIGNvbG9yOiAjZmZmZmZmOw0KICB9DQogIC5lbC1wYWdpbmF0aW9uLmlzLWJhY2tncm91bmQgLmVsLXBhZ2VyIGxpOm5vdCguZGlzYWJsZWQpOmhvdmVyIHsNCiAgICBjb2xvcjogIzIxYzliODsNCiAgfQ0KICAucmVkLXRleHQgew0KICAgIGNvbG9yOiAjMjFjOWI4Ow0KICB9DQogIC50cmFucy1mb3JtIHsNCiAgICBib3JkZXItcmFkaXVzOiA2cHg7DQogICAgYmFja2dyb3VuZDogI2ZmZmZmZjsNCiAgICB3aWR0aDogNDAwcHg7DQogICAgcGFkZGluZzogMjVweCA1cHggNXB4IDI1cHg7DQogICAgLmhlYWRlciB7DQogICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICBmb250LXdlaWdodDogNTAwOw0KICAgICAgY29sb3I6ICMxMjE2MjA7DQogICAgICBsaW5lLWhlaWdodDogMThweDsNCiAgICAgIG1hcmdpbi1ib3R0b206IDEycHg7DQogICAgfQ0KICAgIC5lbC1pbnB1dCB7DQogICAgICBoZWlnaHQ6IDM4cHg7DQogICAgICBpbnB1dCB7DQogICAgICAgIGhlaWdodDogMzhweDsNCiAgICAgIH0NCiAgICB9DQogICAgLmVsLXNlbGVjdCB7DQogICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgICBwb3NpdGlvbjogcmVsYXRpdmU7DQogICAgICB3aWR0aDogMTAwJTsNCiAgICB9DQogICAgLmlucHV0LWljb24gew0KICAgICAgaGVpZ2h0OiAzOXB4Ow0KICAgICAgd2lkdGg6IDE0cHg7DQogICAgICBtYXJnaW4tbGVmdDogMnB4Ow0KICAgIH0NCiAgICAuZWwtZm9ybS1pdGVtLS1tZWRpdW0gLmVsLWZvcm0taXRlbV9fY29udGVudCB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgbGluZS1oZWlnaHQ6IDM2cHg7DQogICAgfQ0KICAgIC5lbC1mb3JtLWl0ZW0gew0KICAgICAgbWFyZ2luLWJvdHRvbTogMTJweDsNCiAgICB9DQogICAgLmVsLWlucHV0X19zdWZmaXgtaW5uZXIgew0KICAgICAgLmFjdGl2ZS1zdHlsZSB7DQogICAgICAgIGNvbG9yOiAjMjFjOWI4Ow0KICAgICAgICBmb250LXNpemU6IDE0cHg7DQogICAgICB9DQogICAgICAuZGlzYWJsZWQtc3R5bGUgew0KICAgICAgICBjb2xvcjogIzk5OTsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgfQ0KICAgIH0NCiAgfQ0KICAuZWwtYnV0dG9uLS1wcmltYXJ5IHsNCiAgICAvKiBjb2xvcjogI0ZGRkZGRjsgKi8NCiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjFjOWI4ICFpbXBvcnRhbnQ7DQogICAgYm9yZGVyLWNvbG9yOiAjMjFjOWI4Ow0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8gBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/user/approveSetting", "sourcesContent": ["<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-12 10:25:16\r\n * @LastEditTime: 2023-02-27 14:43:13\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<!--\r\n * @Author: zhc\r\n * @Date: 2023-02-03 11:06:49\r\n * @LastEditTime: 2023-02-11 11:21:19\r\n * @Description: \r\n * @LastEditors: zhc\r\n-->\r\n<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"2.5\" :xs=\"24\">\r\n        <user-menu />\r\n      </el-col>\r\n      <el-col :span=\"20\" :xs=\"24\">\r\n        <div class=\"approve-setting-container\">\r\n          <div class=\"header-small\">\r\n            <div class=\"red-tag\"></div>\r\n            申请认证\r\n          </div>\r\n          <el-tabs v-model=\"activeName\" @tab-click=\"handleClick\">\r\n            <el-tab-pane label=\"名片认证\" name=\"1\">\r\n              <el-form\r\n                ref=\"personalForm\"\r\n                :model=\"personalForm\"\r\n                :rules=\"personaRules\"\r\n                label-width=\"120px\"\r\n              >\r\n                <el-form-item label=\"真实姓名：\" prop=\"name\">\r\n                  <div v-if=\"!personalEdit\">{{ personalForm.name }}</div>\r\n                  <el-input\r\n                    v-model=\"personalForm.name\"\r\n                    v-else\r\n                    placeholder=\"请输入真实姓名\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系方式：\" prop=\"phone\">\r\n                  <div v-if=\"!personalEdit\">{{ personalForm.phone }}</div>\r\n                  <el-input\r\n                    v-model=\"personalForm.phone\"\r\n                    v-else\r\n                    placeholder=\"请选择联系方式\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"公司名称：\" prop=\"companyName\">\r\n                  <div v-if=\"!personalEdit\">{{ personalForm.companyName }}</div>\r\n                  <el-select\r\n                    v-model=\"personalForm.companyName\"\r\n                    v-else\r\n                    filterable\r\n                    remote\r\n                    reserve-keyword\r\n                    placeholder=\"请输入关键词\"\r\n                    :remote-method=\"getCompanyList\"\r\n                    :loading=\"personalLoading\"\r\n                    @change=\"personChanged\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"item in companyOptions\"\r\n                      :key=\"item.id\"\r\n                      :label=\"item.name\"\r\n                      :value=\"item.id\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"身份证明：\" prop=\"personalCardList\">\r\n                  <div class=\"pic-class\">\r\n                    <el-upload\r\n                      list-type=\"picture-card\"\r\n                      :headers=\"headers\"\r\n                      :action=\"uploadUrl\"\r\n                      :class=\"[personalEdit ? '' : 'hide']\"\r\n                      :disabled=\"!personalEdit\"\r\n                      :file-list=\"personalForm.personalCardList\"\r\n                      :accept=\"accept\"\r\n                      :before-upload=\"handleBeforeUpload\"\r\n                      :on-preview=\"handlePersonalCardPreview\"\r\n                      :on-remove=\"handleRemove\"\r\n                      :on-success=\"handlePersonalCardSuccess\"\r\n                    >\r\n                      <i class=\"el-icon-plus\"></i>\r\n                      <div\r\n                        v-if=\"personalEdit\"\r\n                        slot=\"tip\"\r\n                        class=\"el-upload__tip\"\r\n                      >\r\n                        点击上传\r\n                        <span class=\"red-text\">名片、工作证、工牌</span>\r\n                        等可以认证身份的图片\r\n                      </div>\r\n                    </el-upload>\r\n                  </div>\r\n\r\n                  <el-dialog\r\n                    append-to-body\r\n                    :visible.sync=\"imgVisible\"\r\n                    :close-on-click-modal=\"false\"\r\n                  >\r\n                    <img width=\"100%\" :src=\"imageUrl\" alt=\"\" />\r\n                  </el-dialog>\r\n                </el-form-item>\r\n              </el-form>\r\n              <div class=\"button-container\">\r\n                <el-button\r\n                  v-if=\"!personalEdit\"\r\n                  type=\"danger\"\r\n                  @click=\"changePersonEdit\"\r\n                  >编辑</el-button\r\n                >\r\n                <el-button v-else type=\"danger\" @click=\"submitPersonal()\"\r\n                  >保存</el-button\r\n                >\r\n              </div>\r\n            </el-tab-pane>\r\n            <el-tab-pane label=\"企业认证\" name=\"2\">\r\n              <div class=\"staff-list\">\r\n                <el-form\r\n                  ref=\"companyForm\"\r\n                  :model=\"companyForm\"\r\n                  :rules=\"companyRules\"\r\n                  label-width=\"120px\"\r\n                >\r\n                  <el-row>\r\n                    <el-col :span=\"24\">\r\n                      <el-form-item label=\"真实姓名：\" prop=\"name\">\r\n                        <div v-if=\"!companyEdit\">\r\n                          {{ companyForm.name }}\r\n                        </div>\r\n                        <el-input\r\n                          v-model=\"companyForm.name\"\r\n                          v-else\r\n                          placeholder=\"请输入真实姓名\"\r\n                        />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n\r\n                  <el-row>\r\n                    <el-col :span=\"24\">\r\n                      <el-form-item label=\"联系方式：\" prop=\"phone\">\r\n                        <div v-if=\"!companyEdit\">\r\n                          {{ companyForm.phone }}\r\n                        </div>\r\n                        <el-input\r\n                          v-model=\"companyForm.phone\"\r\n                          v-else\r\n                          placeholder=\"请选择联系方式\"\r\n                        />\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-row>\r\n                    <el-col :span=\"24\">\r\n                      <el-form-item label=\"公司名称：\" prop=\"companyName\">\r\n                        <div v-if=\"!companyEdit\">\r\n                          {{ companyForm.companyName }}\r\n                        </div>\r\n                        <el-select\r\n                          v-model=\"companyForm.companyName\"\r\n                          v-else\r\n                          filterable\r\n                          remote\r\n                          reserve-keyword\r\n                          placeholder=\"请输入关键词\"\r\n                          :remote-method=\"getCompanyList\"\r\n                          :loading=\"companyLoading\"\r\n                          @change=\"companyChanged\"\r\n                        >\r\n                          <el-option\r\n                            v-for=\"item in companyOptions\"\r\n                            :key=\"item.id\"\r\n                            :label=\"item.name\"\r\n                            :value=\"item.id\"\r\n                          >\r\n                          </el-option>\r\n                        </el-select>\r\n                      </el-form-item>\r\n                    </el-col>\r\n                  </el-row>\r\n                  <el-form-item label=\"企业授权书：\">\r\n                    <div class=\"pic-class\">\r\n                      <el-upload\r\n                        list-type=\"picture-card\"\r\n                        :class=\"[companyEdit ? '' : 'hide']\"\r\n                        :headers=\"headers\"\r\n                        :action=\"uploadUrl\"\r\n                        :file-list=\"companyForm.companyCardList\"\r\n                        :accept=\"accept\"\r\n                        :disabled=\"!companyEdit\"\r\n                        :before-upload=\"handleBeforeUpload\"\r\n                        :on-preview=\"handleCompanyCardPreview\"\r\n                        :on-remove=\"handleCompanyCardRemove\"\r\n                        :on-success=\"handlecompanyCardSuccess\"\r\n                      >\r\n                        <i class=\"el-icon-plus\"></i>\r\n                        <div\r\n                          v-if=\"companyEdit\"\r\n                          slot=\"tip\"\r\n                          class=\"el-upload__tip\"\r\n                        >\r\n                          支持jpg、png格式\r\n                          <a class=\"red-text\" @click=\"download\">查看模板>></a>\r\n                        </div>\r\n                      </el-upload>\r\n                    </div>\r\n                    <el-dialog\r\n                      append-to-body\r\n                      :visible.sync=\"imgVisible\"\r\n                      :close-on-click-modal=\"false\"\r\n                    >\r\n                      <img width=\"100%\" :src=\"imageUrl\" alt=\"\" />\r\n                    </el-dialog>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"\" prop=\"companyCardList\">\r\n                    <el-upload\r\n                      :file-list=\"companyForm.authMatTerialList\"\r\n                      :headers=\"headers\"\r\n                      :action=\"uploadUrl\"\r\n                      :disabled=\"!companyEdit\"\r\n                      accept=\".pdf, .docx, .xls\"\r\n                      :on-remove=\"handleompanyCardRemove\"\r\n                      :on-success=\"handleAuthMatTerialSuccess\"\r\n                      :limit=\"10\"\r\n                    >\r\n                      <el-button\r\n                        v-if=\"companyEdit\"\r\n                        class=\"apathy-upload-btn\"\r\n                        size=\"small\"\r\n                        icon=\"el-icon-upload2\"\r\n                        >上传文件\r\n                      </el-button>\r\n                      <span\r\n                        v-if=\"companyEdit\"\r\n                        slot=\"tip\"\r\n                        class=\"el-upload__tip\"\r\n                      >\r\n                        仅限doc、pdf、xls格式\r\n                      </span>\r\n                    </el-upload>\r\n                  </el-form-item>\r\n                </el-form>\r\n                <div class=\"button-container\">\r\n                  <el-button\r\n                    v-if=\"!companyEdit\"\r\n                    type=\"danger\"\r\n                    @click=\"changeCompanyEdit\"\r\n                    >编辑</el-button\r\n                  >\r\n                  <el-button v-else type=\"danger\" @click=\"submitCompany()\"\r\n                    >保存</el-button\r\n                  >\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport UserMenu from \"../components/userMenu.vue\";\r\nimport FileUpload from \"@/components/FileUpload\";\r\nimport store from \"@/store\";\r\nimport { getCompanyListByName } from \"@/api/system/user\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport {\r\n  personalApprove,\r\n  companyApprove,\r\n  getApproveDetail,\r\n} from \"@/api/system/approve.js\";\r\nexport default {\r\n  name: \"ApproveSetting\",\r\n  components: { UserMenu, FileUpload },\r\n  data() {\r\n    return {\r\n      activeName: \"1\",\r\n      uploadUrl: process.env.VUE_APP_BASE_API + \"/file/upload\", //上传地址\r\n      accept: \".jpg, .jpeg, .png, .bmp\",\r\n      headers: { Authorization: \"Bearer \" + getToken() },\r\n      companyOptions: [],\r\n      isAdmin: false,\r\n      loading: false,\r\n      imgVisible: false,\r\n      personalEdit: false,\r\n      companyEdit: false,\r\n      companyLoading: false,\r\n      personalLoading: false,\r\n      imageUrl: \"\",\r\n      user: {\r\n        userId: store.getters.userId,\r\n        bussinessNo: store.getters.bussinessNo,\r\n        phonenumber: store.getters.phonenumber,\r\n      },\r\n      personalForm: {\r\n        personalCardList: [],\r\n      },\r\n      companyForm: {\r\n        authMatTerialList: [],\r\n        companyCardList: [],\r\n      },\r\n      personaRules: {\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n        name: [\r\n          { required: true, message: \"真实姓名不能为空\", trigger: \"blur\" },\r\n        ],\r\n        phone: [\r\n          { required: true, message: \"联系方式不能为空\", trigger: \"blur\" },\r\n        ],\r\n        personalCardList: [\r\n          { required: false, message: \"身份证明不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n      companyRules: {\r\n        name: [\r\n          { required: true, message: \"真实姓名不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyName: [\r\n          { required: true, message: \"公司名称不能为空\", trigger: \"blur\" },\r\n        ],\r\n\r\n        phone: [\r\n          { required: true, message: \"联系方式不能为空\", trigger: \"blur\" },\r\n        ],\r\n        authMatTerialList: [\r\n          { required: false, message: \"授权书不能为空\", trigger: \"blur\" },\r\n        ],\r\n        companyCardList: [\r\n          { required: false, message: \"附件不能为空\", trigger: \"blur\" },\r\n        ],\r\n      },\r\n    };\r\n  },\r\n  created() {\r\n    this.getApproveDetail();\r\n  },\r\n  methods: {\r\n    handleClick(tab, event) {\r\n      if (this.activeName == \"1\") {\r\n      } else {\r\n      }\r\n      this.getApproveDetail();\r\n      this.queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 5,\r\n      };\r\n    },\r\n    changePersonEdit() {\r\n      this.personalEdit = true;\r\n    },\r\n    changeCompanyEdit() {\r\n      this.companyEdit = true;\r\n    },\r\n    // 产品照片上传之前的钩子\r\n    handleBeforeUpload(file) {\r\n      let { name, type, size } = file;\r\n      let typeList = this.accept\r\n        .split(\",\")\r\n        .map((item) => item.trim().toLowerCase().substr(1));\r\n      let dotIndex = name.lastIndexOf(\".\");\r\n      // 文件类型校验\r\n      if (dotIndex === -1) {\r\n        this.$message.error(\"请上传正确格式的文件\");\r\n        return false;\r\n      } else {\r\n        let suffix = name.substring(dotIndex + 1);\r\n        if (typeList.indexOf(suffix.toLowerCase()) === -1) {\r\n          this.$message.error(\"请上传正确格式的文件\");\r\n          return false;\r\n        }\r\n      }\r\n      // 文件上传大小限制\r\n      if (size > 1048576 * 20) {\r\n        this.$message.error(\"文件大小不能超过20M！\");\r\n        return false;\r\n      }\r\n    },\r\n    // 点击产品照片\r\n    handlePersonalCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    handleCompanyCardPreview(file) {\r\n      this.imageUrl = file.url;\r\n      this.imgVisible = true;\r\n    },\r\n    // 删除产品照片\r\n    handleRemove(file, fileList) {\r\n      this.personalForm.personalCardList = fileList;\r\n    },\r\n    // 删除产品照片\r\n    handleCompanyCardRemove(file, fileList) {\r\n      this.companyForm.companyCardList = fileList;\r\n    },\r\n    handlecompanyCardSuccess(res, file, fileList) {\r\n      if (!this.companyForm.companyForm) {\r\n        this.companyForm.companyCardList = [];\r\n      }\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.companyForm.companyCardList.push(res.data);\r\n      }\r\n    },\r\n    handlePersonalCardSuccess(res, file, fileList) {\r\n      if (!this.personalForm.personalCardList) {\r\n        this.personalForm.personalCardList = [];\r\n      }\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.personalForm.personalCardList.push(res.data);\r\n      }\r\n    },\r\n    handleAuthMatTerialSuccess(res, file, fileList) {\r\n      if (!this.companyForm.authMatTerialList) {\r\n        this.companyForm.authMatTerialList = [];\r\n      }\r\n      //此处写上传oss成功之后的逻辑\r\n      if (res.code == 200) {\r\n        this.companyForm.authMatTerialList.push(res.data);\r\n      }\r\n    },\r\n    getCompanyList(query) {\r\n      if (query !== \"\") {\r\n        getCompanyListByName(query).then((response) => {\r\n          this.companyOptions = response.data;\r\n        });\r\n      }\r\n    },\r\n    companyChanged(res) {\r\n      this.companyOptions.forEach((item) => {\r\n        if (item.id == res) {\r\n          this.companyForm.bussinessNo = item.creditCode;\r\n          this.companyForm.tianyanId = item.id;\r\n          this.companyForm.companyName = item.name;\r\n        }\r\n      });\r\n    },\r\n    personChanged(res) {\r\n      this.companyOptions.forEach((item) => {\r\n        if (item.id == res) {\r\n          this.personalForm.bussinessNo = item.creditCode;\r\n          this.personalForm.tianyanId = item.id;\r\n          this.personalForm.companyName = item.name;\r\n        }\r\n      });\r\n    },\r\n    handleompanyCardRemove(file, fileList) {\r\n      this.companyForm.companyCardList = res.data;\r\n    },\r\n    submitPersonal() {\r\n      this.$refs.personalForm.validate((valid) => {\r\n        if (valid) {\r\n          personalApprove({\r\n            ...this.personalForm,\r\n          }).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.getApproveDetail();\r\n              this.personalEdit = false;\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    submitCompany() {\r\n      // if (!this.companyForm.authMatTerialList) {\r\n      //   this.$modal.msgError(\"请上传授权书\");\r\n      // }\r\n      this.$refs.companyForm.validate((valid) => {\r\n        if (valid) {\r\n          companyApprove({\r\n            ...this.companyForm,\r\n          }).then((res) => {\r\n            if (res.code == 200) {\r\n              this.$modal.msgSuccess(\"操作成功\");\r\n              this.getApproveDetail();\r\n              this.companyEdit = false;\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    download() {\r\n      // let url =\r\n      //   \"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230213/1676277801454790.docx\";\r\n      // window.open(url, \"_blank\");\r\n      var link = document.createElement(\"a\");\r\n      link.href =\r\n        \"https://xp-tech.oss-cn-beijing.aliyuncs.com/cykc/20230213/1676277801454790.docx\";\r\n      link.download = \"授权书\";\r\n      link.target = \"_blank\";\r\n      link.click();\r\n    },\r\n\r\n    getApproveDetail() {\r\n      getApproveDetail().then((res) => {\r\n        if (res.code == 200) {\r\n          this.personalForm = res.data ?? {};\r\n          this.companyForm = res.data ?? {};\r\n          if (!this.personalForm.personalCardList) {\r\n            this.personalForm.personalCardList = [];\r\n          }\r\n          if (!this.companyForm.authMatTerialList) {\r\n            this.companyForm.authMatTerialList = [];\r\n          }\r\n          if (!this.companyForm.companyCardList) {\r\n            this.companyForm.companyCardList = [];\r\n          }\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.app-container {\r\n  background: #f4f5f9;\r\n  .approve-setting-container {\r\n    width: 100%;\r\n    background: #fff;\r\n    min-height: 700px;\r\n    padding: 20px;\r\n    .el-select {\r\n      display: inline-block;\r\n      position: relative;\r\n      width: 100%;\r\n    }\r\n    .el-button--danger {\r\n      background: #21c9b8;\r\n      color: #fff;\r\n      border-color: #21c9b8;\r\n    }\r\n    .header-small {\r\n      text-align: center;\r\n      display: flex;\r\n      font-size: 20px;\r\n      font-weight: 500;\r\n      color: #333333;\r\n      line-height: 20px;\r\n\r\n      .red-tag {\r\n        margin-right: 12px;\r\n        width: 3px;\r\n        height: 22px;\r\n        background: #21c9b8;\r\n      }\r\n    }\r\n    .apathy-upload-btn {\r\n      margin-right: 20px;\r\n    }\r\n    .button-container {\r\n      width: 100%;\r\n      margin-top: 50px;\r\n      text-align: center;\r\n    }\r\n\r\n    .el-tabs__nav {\r\n      width: 100%;\r\n      height: 40px;\r\n      padding: 0 43%;\r\n      display: flex;\r\n      // justify-content: space-between;\r\n    }\r\n\r\n    .el-tabs__nav-wrap::after {\r\n      background-color: transparent;\r\n    }\r\n    .el-tabs__active-bar {\r\n      margin-left: 43%;\r\n      background-color: #21c9b8;\r\n    }\r\n    .el-tabs__item.is-active {\r\n      color: #21c9b8;\r\n    }\r\n    .el-tabs__item:hover {\r\n      color: #21c9b8;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n  .pic-class .hide .el-upload--picture-card {\r\n    display: none;\r\n  }\r\n  .el-pagination {\r\n    width: 100%;\r\n    margin-top: 20px;\r\n    text-align: center;\r\n  }\r\n  .el-pagination.is-background .el-pager li {\r\n    background-color: #fff;\r\n  }\r\n  .el-pagination.is-background .el-pager li:not(.disabled).active {\r\n    background-color: #21c9b8;\r\n    color: #ffffff;\r\n  }\r\n  .el-pagination.is-background .el-pager li:not(.disabled):hover {\r\n    color: #21c9b8;\r\n  }\r\n  .red-text {\r\n    color: #21c9b8;\r\n  }\r\n  .trans-form {\r\n    border-radius: 6px;\r\n    background: #ffffff;\r\n    width: 400px;\r\n    padding: 25px 5px 5px 25px;\r\n    .header {\r\n      font-size: 18px;\r\n      font-weight: 500;\r\n      color: #121620;\r\n      line-height: 18px;\r\n      margin-bottom: 12px;\r\n    }\r\n    .el-input {\r\n      height: 38px;\r\n      input {\r\n        height: 38px;\r\n      }\r\n    }\r\n    .el-select {\r\n      display: inline-block;\r\n      position: relative;\r\n      width: 100%;\r\n    }\r\n    .input-icon {\r\n      height: 39px;\r\n      width: 14px;\r\n      margin-left: 2px;\r\n    }\r\n    .el-form-item--medium .el-form-item__content {\r\n      display: flex;\r\n      line-height: 36px;\r\n    }\r\n    .el-form-item {\r\n      margin-bottom: 12px;\r\n    }\r\n    .el-input__suffix-inner {\r\n      .active-style {\r\n        color: #21c9b8;\r\n        font-size: 14px;\r\n      }\r\n      .disabled-style {\r\n        color: #999;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n  .el-button--primary {\r\n    /* color: #FFFFFF; */\r\n    background-color: #21c9b8 !important;\r\n    border-color: #21c9b8;\r\n  }\r\n}\r\n</style>\r\n"]}]}