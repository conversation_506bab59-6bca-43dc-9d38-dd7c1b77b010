package com.ruoyi.sso.service.impl;

import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.sso.service.UserMappingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 用户映射服务实现
 * 使用Redis存储用户映射关系
 * 
 * <AUTHOR>
 */
@Service
public class UserMappingServiceImpl implements UserMappingService {
    
    private static final Logger log = LoggerFactory.getLogger(UserMappingServiceImpl.class);
    
    @Autowired
    private RedisService redisService;
    
    // Redis键前缀
    private static final String USER_MAPPING_PREFIX = "user_mapping:";
    private static final String USER_PERMISSIONS_PREFIX = "user_permissions:";
    
    // 缓存过期时间（24小时）
    private static final long CACHE_EXPIRE_HOURS = 24L;
    
    @Override
    public Map<String, Object> getUserPermissions(String ssoUsername, String clientId) {
        if (StringUtils.isEmpty(ssoUsername) || StringUtils.isEmpty(clientId)) {
            return new HashMap<>();
        }
        
        String key = USER_PERMISSIONS_PREFIX + clientId + ":" + ssoUsername;
        Map<String, Object> permissions = redisService.getCacheObject(key);
        
        if (permissions == null) {
            // 如果缓存中没有，返回默认权限或从配置中获取
            permissions = getDefaultPermissions(ssoUsername, clientId);
            if (!permissions.isEmpty()) {
                // 缓存默认权限
                redisService.setCacheObject(key, permissions, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            }
        }
        
        return permissions != null ? permissions : new HashMap<>();
    }
    
    @Override
    public boolean createUserMapping(String ssoUsername, String clientId, String systemUserId, Map<String, Object> permissions) {
        if (StringUtils.isEmpty(ssoUsername) || StringUtils.isEmpty(clientId)) {
            return false;
        }
        
        try {
            // 存储用户映射关系
            String mappingKey = USER_MAPPING_PREFIX + clientId + ":" + ssoUsername;
            Map<String, Object> mapping = new HashMap<>();
            mapping.put("ssoUsername", ssoUsername);
            mapping.put("clientId", clientId);
            mapping.put("systemUserId", systemUserId);
            mapping.put("createTime", System.currentTimeMillis());
            mapping.put("updateTime", System.currentTimeMillis());
            
            redisService.setCacheObject(mappingKey, mapping, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            
            // 存储权限信息
            if (permissions != null && !permissions.isEmpty()) {
                String permissionsKey = USER_PERMISSIONS_PREFIX + clientId + ":" + ssoUsername;
                redisService.setCacheObject(permissionsKey, permissions, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            }
            
            log.info("创建用户映射成功: ssoUsername={}, clientId={}, systemUserId={}", 
                    ssoUsername, clientId, systemUserId);
            return true;
            
        } catch (Exception e) {
            log.error("创建用户映射失败", e);
            return false;
        }
    }
    
    @Override
    public boolean updateUserMapping(String ssoUsername, String clientId, Map<String, Object> permissions) {
        if (StringUtils.isEmpty(ssoUsername) || StringUtils.isEmpty(clientId)) {
            return false;
        }
        
        try {
            // 更新权限信息
            String permissionsKey = USER_PERMISSIONS_PREFIX + clientId + ":" + ssoUsername;
            if (permissions != null && !permissions.isEmpty()) {
                permissions.put("updateTime", System.currentTimeMillis());
                redisService.setCacheObject(permissionsKey, permissions, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            }
            
            // 更新映射关系的更新时间
            String mappingKey = USER_MAPPING_PREFIX + clientId + ":" + ssoUsername;
            Map<String, Object> mapping = redisService.getCacheObject(mappingKey);
            if (mapping != null) {
                mapping.put("updateTime", System.currentTimeMillis());
                redisService.setCacheObject(mappingKey, mapping, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            }
            
            log.info("更新用户映射成功: ssoUsername={}, clientId={}", ssoUsername, clientId);
            return true;
            
        } catch (Exception e) {
            log.error("更新用户映射失败", e);
            return false;
        }
    }
    
    @Override
    public boolean deleteUserMapping(String ssoUsername, String clientId) {
        if (StringUtils.isEmpty(ssoUsername) || StringUtils.isEmpty(clientId)) {
            return false;
        }
        
        try {
            String mappingKey = USER_MAPPING_PREFIX + clientId + ":" + ssoUsername;
            String permissionsKey = USER_PERMISSIONS_PREFIX + clientId + ":" + ssoUsername;
            
            redisService.deleteObject(mappingKey);
            redisService.deleteObject(permissionsKey);
            
            log.info("删除用户映射成功: ssoUsername={}, clientId={}", ssoUsername, clientId);
            return true;
            
        } catch (Exception e) {
            log.error("删除用户映射失败", e);
            return false;
        }
    }
    
    @Override
    public boolean hasPermission(String ssoUsername, String clientId) {
        Map<String, Object> permissions = getUserPermissions(ssoUsername, clientId);
        return !permissions.isEmpty();
    }
    
    @Override
    public String[] getUserRoles(String ssoUsername, String clientId) {
        Map<String, Object> permissions = getUserPermissions(ssoUsername, clientId);
        Object roles = permissions.get("roles");
        
        if (roles instanceof String[]) {
            return (String[]) roles;
        } else if (roles instanceof List) {
            List<?> roleList = (List<?>) roles;
            return roleList.stream().map(Object::toString).toArray(String[]::new);
        } else if (roles instanceof String) {
            return ((String) roles).split(",");
        }
        
        return new String[0];
    }
    
    @Override
    public String[] getUserPermissionCodes(String ssoUsername, String clientId) {
        Map<String, Object> permissions = getUserPermissions(ssoUsername, clientId);
        Object permissionCodes = permissions.get("permissionCodes");
        
        if (permissionCodes instanceof String[]) {
            return (String[]) permissionCodes;
        } else if (permissionCodes instanceof List) {
            List<?> permissionList = (List<?>) permissionCodes;
            return permissionList.stream().map(Object::toString).toArray(String[]::new);
        } else if (permissionCodes instanceof String) {
            return ((String) permissionCodes).split(",");
        }
        
        return new String[0];
    }
    
    /**
     * 获取默认权限配置
     * 这里可以根据业务需求配置不同用户在不同系统中的默认权限
     */
    private Map<String, Object> getDefaultPermissions(String ssoUsername, String clientId) {
        Map<String, Object> defaultPermissions = new HashMap<>();
        
        // 根据用户名和客户端ID配置默认权限
        if ("admin".equals(ssoUsername)) {
            // 管理员用户的默认权限
            if ("backend".equals(clientId)) {
                defaultPermissions.put("roles", Arrays.asList("admin", "system_manager"));
                defaultPermissions.put("permissionCodes", Arrays.asList(
                    "system:user:list", "system:user:add", "system:user:edit", "system:user:remove",
                    "system:role:list", "system:role:add", "system:role:edit", "system:role:remove",
                    "system:menu:list", "system:menu:add", "system:menu:edit", "system:menu:remove"
                ));
            } else if ("market".equals(clientId)) {
                defaultPermissions.put("roles", Arrays.asList("admin", "market_manager"));
                defaultPermissions.put("permissionCodes", Arrays.asList(
                    "market:product:list", "market:product:add", "market:product:edit", "market:product:remove",
                    "market:order:list", "market:order:process", "market:order:cancel",
                    "market:user:list", "market:user:manage"
                ));
            }
        } else {
            // 普通用户的默认权限
            if ("backend".equals(clientId)) {
                defaultPermissions.put("roles", Arrays.asList("user"));
                defaultPermissions.put("permissionCodes", Arrays.asList(
                    "system:user:list", "system:user:view"
                ));
            } else if ("market".equals(clientId)) {
                defaultPermissions.put("roles", Arrays.asList("customer"));
                defaultPermissions.put("permissionCodes", Arrays.asList(
                    "market:product:list", "market:product:view",
                    "market:order:list", "market:order:create"
                ));
            }
        }
        
        if (!defaultPermissions.isEmpty()) {
            defaultPermissions.put("createTime", System.currentTimeMillis());
            defaultPermissions.put("source", "default");
        }
        
        return defaultPermissions;
    }
}
