{"remainingRequest": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\src\\views\\register.vue", "mtime": 1750311962994}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\babel.config.js", "mtime": 1750311961269}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750313274302}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750313271694}, {"path": "E:\\company\\nmd\\nmdnew\\share-intelligent\\share-intelligent-portal-new\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750313275789}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_login", "require", "name", "data", "_this", "equalToPassword", "rule", "value", "callback", "registerForm", "password", "Error", "codeUrl", "username", "confirmPassword", "code", "uuid", "registerRules", "required", "trigger", "message", "min", "max", "validator", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "created", "getCode", "methods", "_this2", "getCodeImg", "then", "res", "undefined", "img", "handleRegister", "_this3", "$refs", "validate", "valid", "register", "$alert", "dangerouslyUseHTMLString", "type", "$router", "push", "catch"], "sources": ["src/views/register.vue"], "sourcesContent": ["<template>\r\n  <div class=\"register\">\r\n    <el-form\r\n      ref=\"registerForm\"\r\n      :model=\"registerForm\"\r\n      :rules=\"registerRules\"\r\n      class=\"register-form\"\r\n    >\r\n      <h3 class=\"title\">若依后台管理系统</h3>\r\n      <el-form-item prop=\"username\">\r\n        <el-input\r\n          v-model=\"registerForm.username\"\r\n          type=\"text\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"账号\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"user\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"password\">\r\n        <el-input\r\n          v-model=\"registerForm.password\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"密码\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"password\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"confirmPassword\">\r\n        <el-input\r\n          v-model=\"registerForm.confirmPassword\"\r\n          type=\"password\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"确认密码\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"password\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n      </el-form-item>\r\n      <el-form-item prop=\"code\" v-if=\"captchaEnabled\">\r\n        <el-input\r\n          v-model=\"registerForm.code\"\r\n          auto-complete=\"off\"\r\n          placeholder=\"验证码\"\r\n          style=\"width: 63%\"\r\n          @keyup.enter.native=\"handleRegister\"\r\n        >\r\n          <svg-icon\r\n            slot=\"prefix\"\r\n            icon-class=\"validCode\"\r\n            class=\"el-input__icon input-icon\"\r\n          />\r\n        </el-input>\r\n        <div class=\"register-code\">\r\n          <img :src=\"codeUrl\" @click=\"getCode\" class=\"register-code-img\" />\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item style=\"width: 100%\">\r\n        <el-button\r\n          :loading=\"loading\"\r\n          size=\"medium\"\r\n          type=\"primary\"\r\n          style=\"width: 100%\"\r\n          @click.native.prevent=\"handleRegister\"\r\n        >\r\n          <span v-if=\"!loading\">注 册</span>\r\n          <span v-else>注 册 中...</span>\r\n        </el-button>\r\n        <div style=\"float: right\">\r\n          <router-link class=\"link-type\" :to=\"'/login'\"\r\n            >使用已有账户登录</router-link\r\n          >\r\n        </div>\r\n      </el-form-item>\r\n    </el-form>\r\n    <!--  底部  -->\r\n    <div class=\"el-register-footer\">\r\n      <span>Copyright © 2018-2022 ruoyi.vip All Rights Reserved.</span>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getCodeImg, register } from \"@/api/login\";\r\n\r\nexport default {\r\n  name: \"Register\",\r\n  data() {\r\n    const equalToPassword = (rule, value, callback) => {\r\n      if (this.registerForm.password !== value) {\r\n        callback(new Error(\"两次输入的密码不一致\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n      codeUrl: \"\",\r\n      registerForm: {\r\n        username: \"\",\r\n        password: \"\",\r\n        confirmPassword: \"\",\r\n        code: \"\",\r\n        uuid: \"\",\r\n      },\r\n      registerRules: {\r\n        username: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的账号\" },\r\n          {\r\n            min: 2,\r\n            max: 20,\r\n            message: \"用户账号长度必须介于 2 和 20 之间\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        password: [\r\n          { required: true, trigger: \"blur\", message: \"请输入您的密码\" },\r\n          {\r\n            min: 5,\r\n            max: 20,\r\n            message: \"用户密码长度必须介于 5 和 20 之间\",\r\n            trigger: \"blur\",\r\n          },\r\n        ],\r\n        confirmPassword: [\r\n          { required: true, trigger: \"blur\", message: \"请再次输入您的密码\" },\r\n          { required: true, validator: equalToPassword, trigger: \"blur\" },\r\n        ],\r\n        code: [{ required: true, trigger: \"change\", message: \"请输入验证码\" }],\r\n      },\r\n      loading: false,\r\n      captchaEnabled: true,\r\n    };\r\n  },\r\n  created() {\r\n    this.getCode();\r\n  },\r\n  methods: {\r\n    getCode() {\r\n      getCodeImg().then((res) => {\r\n        this.captchaEnabled =\r\n          res.captchaEnabled === undefined ? true : res.captchaEnabled;\r\n        if (this.captchaEnabled) {\r\n          this.codeUrl = \"data:image/gif;base64,\" + res.img;\r\n          this.registerForm.uuid = res.uuid;\r\n        }\r\n      });\r\n    },\r\n    handleRegister() {\r\n      this.$refs.registerForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          register(this.registerForm)\r\n            .then((res) => {\r\n              const username = this.registerForm.username;\r\n              this.$alert(\r\n                \"<font color='red'>恭喜你，您的账号 \" +\r\n                  username +\r\n                  \" 注册成功！</font>\",\r\n                \"系统提示\",\r\n                {\r\n                  dangerouslyUseHTMLString: true,\r\n                  type: \"success\",\r\n                }\r\n              )\r\n                .then(() => {\r\n                  this.$router.push(\"/login\");\r\n                })\r\n                .catch(() => {});\r\n            })\r\n            .catch(() => {\r\n              this.loading = false;\r\n              if (this.captchaEnabled) {\r\n                this.getCode();\r\n              }\r\n            });\r\n        }\r\n      });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\">\r\n.register {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100%;\r\n  background-image: url(\"../assets/images/login-background.jpg\");\r\n  background-size: cover;\r\n}\r\n.title {\r\n  margin: 0px auto 30px auto;\r\n  text-align: center;\r\n  color: #707070;\r\n}\r\n\r\n.register-form {\r\n  border-radius: 6px;\r\n  background: #ffffff;\r\n  width: 400px;\r\n  padding: 25px 25px 5px 25px;\r\n  .el-input {\r\n    height: 38px;\r\n    input {\r\n      height: 38px;\r\n    }\r\n  }\r\n  .input-icon {\r\n    height: 39px;\r\n    width: 14px;\r\n    margin-left: 2px;\r\n  }\r\n}\r\n.register-tip {\r\n  font-size: 13px;\r\n  text-align: center;\r\n  color: #bfbfbf;\r\n}\r\n.register-code {\r\n  width: 33%;\r\n  height: 38px;\r\n  float: right;\r\n  img {\r\n    cursor: pointer;\r\n    vertical-align: middle;\r\n  }\r\n}\r\n.el-register-footer {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  position: fixed;\r\n  bottom: 0;\r\n  width: 100%;\r\n  text-align: center;\r\n  color: #fff;\r\n  font-family: Arial;\r\n  font-size: 12px;\r\n  letter-spacing: 1px;\r\n}\r\n.register-code-img {\r\n  height: 38px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;AAiGA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,eAAA,YAAAA,gBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,IAAAJ,KAAA,CAAAK,YAAA,CAAAC,QAAA,KAAAH,KAAA;QACAC,QAAA,KAAAG,KAAA;MACA;QACAH,QAAA;MACA;IACA;IACA;MACAI,OAAA;MACAH,YAAA;QACAI,QAAA;QACAH,QAAA;QACAI,eAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,aAAA;QACAJ,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAC,GAAA;UACAC,GAAA;UACAF,OAAA;UACAD,OAAA;QACA,EACA;QACAT,QAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UACAC,GAAA;UACAC,GAAA;UACAF,OAAA;UACAD,OAAA;QACA,EACA;QACAL,eAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAF,QAAA;UAAAK,SAAA,EAAAlB,eAAA;UAAAc,OAAA;QAAA,EACA;QACAJ,IAAA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAI,OAAA;MACAC,cAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,MAAA;MACA,IAAAC,iBAAA,IAAAC,IAAA,WAAAC,GAAA;QACAH,MAAA,CAAAJ,cAAA,GACAO,GAAA,CAAAP,cAAA,KAAAQ,SAAA,UAAAD,GAAA,CAAAP,cAAA;QACA,IAAAI,MAAA,CAAAJ,cAAA;UACAI,MAAA,CAAAjB,OAAA,8BAAAoB,GAAA,CAAAE,GAAA;UACAL,MAAA,CAAApB,YAAA,CAAAO,IAAA,GAAAgB,GAAA,CAAAhB,IAAA;QACA;MACA;IACA;IACAmB,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA5B,YAAA,CAAA6B,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAZ,OAAA;UACA,IAAAgB,eAAA,EAAAJ,MAAA,CAAA3B,YAAA,EACAsB,IAAA,WAAAC,GAAA;YACA,IAAAnB,QAAA,GAAAuB,MAAA,CAAA3B,YAAA,CAAAI,QAAA;YACAuB,MAAA,CAAAK,MAAA,CACA,gCACA5B,QAAA,GACA,iBACA,QACA;cACA6B,wBAAA;cACAC,IAAA;YACA,CACA,EACAZ,IAAA;cACAK,MAAA,CAAAQ,OAAA,CAAAC,IAAA;YACA,GACAC,KAAA;UACA,GACAA,KAAA;YACAV,MAAA,CAAAZ,OAAA;YACA,IAAAY,MAAA,CAAAX,cAAA;cACAW,MAAA,CAAAT,OAAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}