!function(t,o){"object"==typeof exports&&"undefined"!=typeof module?module.exports=o():"function"==typeof define&&define.amd?define(o):(t="undefined"!=typeof globalThis?globalThis:t||self).PullDown=o()}(this,function(){"use strict";var s="undefined"!=typeof window,t=s&&navigator.userAgent.toLowerCase();if(t&&/wechatdevtools/.test(t),t&&t.indexOf("android"),"string"==typeof t)(t=/os (\d\d?_\d(_\d)?)/.exec(t))&&(13===(t=t[1].split("_").map(function(t){return parseInt(t,10)}))[0]&&t[1]);if(s)try{var o={};Object.defineProperty(o,"passive",{get:function(){}}),window.addEventListener("test-passive",function(){},o)}catch(t){}var i=s&&document.createElement("div").style,e=function(){if(s)for(var t=0,o=[{key:"standard",value:"transform"},{key:"webkit",value:"webkitTransform"},{key:"Moz",value:"MozTransform"},{key:"O",value:"OTransform"},{key:"ms",value:"msTransform"}];t<o.length;t++){var e=o[t];if(void 0!==i[e.value])return e.key}return!1}();function r(t){return!1===e?t:"standard"===e?"transitionEnd"===t?"transitionend":t:e+t.charAt(0).toUpperCase()+t.substr(1)}e&&"standard"!==e&&e.toLowerCase();r("transform"),r("transition");s&&r("perspective")in i,r("transitionTimingFunction"),r("transitionDuration"),r("transitionDelay"),r("transformOrigin"),r("transitionEnd"),r("transitionProperty");var n={style:"cubic-bezier(0.165, 0.84, 0.44, 1)",fn:function(t){return 1- --t*t*t*t}},l=[{key:"finishPullDown",name:"finishPullDown"},{key:"openPullDown",name:"openPullDown"},{key:"closePullDown",name:"closePullDown"},{key:"autoPullDownRefresh",name:"autoPullDownRefresh"}].map(function(t){return{key:t.key,sourceKey:"plugins.pullDownRefresh."+t.name}}),h="pullingDown",c="enterThreshold",a="leaveThreshold";function u(t){this.scroll=t,this.pulling=0,this.thresholdBoundary=0,this.init()}return u.prototype.setPulling=function(t){this.pulling=t},u.prototype.setThresholdBoundary=function(t){this.thresholdBoundary=t},u.prototype.init=function(){this.handleBScroll(),this.handleOptions(this.scroll.options.pullDownRefresh),this.handleHooks(),this.watch()},u.prototype.handleBScroll=function(){this.scroll.registerType([h,c,a]),this.scroll.proxy(l)},u.prototype.handleOptions=function(t){this.options=function(t,o){for(var e in o)t[e]=o[e];return t}({threshold:90,stop:40},t=!0===(t=void 0===t?{}:t)?{}:t),this.scroll.options.probeType=3},u.prototype.handleHooks=function(){var o=this,t=(this.hooksFn=[],this.scroll.scroller),e=t.scrollBehaviorY;this.currentMinScrollY=this.cachedOriginanMinScrollY=e.minScrollPos,this.registerHooks(this.scroll.hooks,this.scroll.hooks.eventTypes.contentChanged,function(){o.finishPullDown()}),this.registerHooks(e.hooks,e.hooks.eventTypes.computeBoundary,function(t){0<t.maxScrollPos&&(t.maxScrollPos=-1),t.minScrollPos=o.currentMinScrollY}),this.hasMouseWheelPlugin()&&(this.registerHooks(this.scroll,this.scroll.eventTypes.alterOptions,function(t){t.discreteTime=300,t.easeTime=350}),this.registerHooks(this.scroll,this.scroll.eventTypes.mousewheelEnd,function(){t.hooks.trigger(t.hooks.eventTypes.end)}))},u.prototype.registerHooks=function(t,o,e){t.on(o,e,this),this.hooksFn.push([t,o,e])},u.prototype.hasMouseWheelPlugin=function(){return!!this.scroll.eventTypes.alterOptions},u.prototype.watch=function(){var t=this.scroll.scroller;this.watching=!0,this.registerHooks(t.hooks,t.hooks.eventTypes.end,this.checkPullDown),this.registerHooks(this.scroll,this.scroll.eventTypes.scrollStart,this.resetStateBeforeScrollStart),this.registerHooks(this.scroll,this.scroll.eventTypes.scroll,this.checkLocationOfThresholdBoundary),this.hasMouseWheelPlugin()&&this.registerHooks(this.scroll,this.scroll.eventTypes.mousewheelStart,this.resetStateBeforeScrollStart)},u.prototype.resetStateBeforeScrollStart=function(){this.isFetchingStatus()||(this.setPulling(1),this.setThresholdBoundary(0))},u.prototype.checkLocationOfThresholdBoundary=function(){var t,o,e;1===this.pulling&&(t=this.scroll,o=1!==this.thresholdBoundary&&this.locateInsideThresholdBoundary(),e=2!==this.thresholdBoundary&&!this.locateInsideThresholdBoundary(),o&&(this.setThresholdBoundary(1),t.trigger(c)),e&&(this.setThresholdBoundary(2),t.trigger(a)))},u.prototype.locateInsideThresholdBoundary=function(){return this.scroll.y<=this.options.threshold},u.prototype.unwatch=function(){var t=this.scroll,o=t.scroller;this.watching=!1,o.hooks.off(o.hooks.eventTypes.end,this.checkPullDown),t.off(t.eventTypes.scrollStart,this.resetStateBeforeScrollStart),t.off(t.eventTypes.scroll,this.checkLocationOfThresholdBoundary),this.hasMouseWheelPlugin()&&t.off(t.eventTypes.mousewheelStart,this.resetStateBeforeScrollStart)},u.prototype.checkPullDown=function(){var t=this.options,o=t.threshold,t=t.stop;return!(this.scroll.y<o)&&(1===this.pulling&&(this.modifyBehaviorYBoundary(t),this.setPulling(2),this.scroll.trigger(h)),this.scroll.scrollTo(this.scroll.x,t,this.scroll.options.bounceTime,n),this.isFetchingStatus())},u.prototype.isFetchingStatus=function(){return 2===this.pulling},u.prototype.modifyBehaviorYBoundary=function(t){var o=this.scroll.scroller.scrollBehaviorY;this.cachedOriginanMinScrollY=o.minScrollPos,this.currentMinScrollY=t,o.computeBoundary()},u.prototype.finishPullDown=function(){var t;this.isFetchingStatus()&&(t=this.scroll.scroller.scrollBehaviorY,this.currentMinScrollY=this.cachedOriginanMinScrollY,t.computeBoundary(),this.setPulling(0),this.scroll.resetPosition(this.scroll.options.bounceTime,n))},u.prototype.openPullDown=function(t){this.handleOptions(t=void 0===t?{}:t),this.watching||this.watch()},u.prototype.closePullDown=function(){this.unwatch()},u.prototype.autoPullDownRefresh=function(){var t=this.options,o=t.threshold,t=t.stop;!this.isFetchingStatus()&&this.watching&&(this.modifyBehaviorYBoundary(t),this.scroll.trigger(this.scroll.eventTypes.scrollStart),this.scroll.scrollTo(this.scroll.x,o),this.setPulling(2),this.scroll.trigger(h),this.scroll.scrollTo(this.scroll.x,t,this.scroll.options.bounceTime,n))},u.pluginName="pullDownRefresh",u});
